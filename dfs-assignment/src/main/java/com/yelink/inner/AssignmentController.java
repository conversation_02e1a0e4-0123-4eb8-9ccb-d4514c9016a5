package com.yelink.inner;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.constant.PriorityTypeEnum;
import com.yelink.assignment.constant.ReportType;
import com.yelink.assignment.entity.operation.OperationDayCountEntity;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderTeamEntity;
import com.yelink.assignment.entity.operation.dto.OperationOrderSelectDTO;
import com.yelink.assignment.entity.report.ReportCountEntity;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.entity.report.dto.FinishAndReportCountDTO;
import com.yelink.assignment.mapper.OperationOrderMapper;
import com.yelink.assignment.service.MemoryCalApplyService;
import com.yelink.assignment.service.OperationDayCountService;
import com.yelink.assignment.service.OperationOrderTeamService;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.ReportCountService;
import com.yelink.assignment.service.ReportLineService;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.api.dfs.MaterialInterface;
import com.yelink.dfscommon.api.dfs.WorkOrderInterface;
import com.yelink.dfscommon.constant.assignment.OperationStateEnum;
import com.yelink.dfscommon.dto.DateRangeReq;
import com.yelink.dfscommon.dto.DeviceOrderDTO;
import com.yelink.dfscommon.dto.OperationDayCountDTO;
import com.yelink.dfscommon.dto.OperationOrderDTO;
import com.yelink.dfscommon.dto.screen.OperationOrderStateDTO;
import com.yelink.dfscommon.entity.MaterialEntityDTO;
import com.yelink.dfscommon.entity.dfs.FacCalEuiEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 作业工单对外接口
 * @time 2021/7/9 12:01
 */
@Slf4j
@AllArgsConstructor
@RestController
public class AssignmentController implements AssignmentInterface {
    private OperationService operationService;
    private OperationDayCountService operationDayCountService;
    private MemoryCalApplyService memoryCalApplyService;
    private WorkOrderInterface workOrderInterface;
    private ReportLineService reportLineService;
    private OperationOrderMapper operationOrderMapper;
    private ReportCountService reportCountService;
    private MaterialInterface materialInterface;
    private OperationOrderTeamService operationOrderTeamService;


    @Override
    public ResponseData getOperationOrder(String workOrderNumber) {
        List<OperationOrderEntity> operationOrderEntityList = operationService.getByWorkOrder(workOrderNumber);
        return ResponseData.success(operationOrderEntityList);
    }

    @Override
    public ResponseData getOperationOrderList(List<String> workOrderNumbers) {
        List<OperationOrderEntity> operationOrderEntities = operationService.lambdaQuery().eq(OperationOrderEntity::getWorkOrderNum, workOrderNumbers).list();
        return ResponseData.success(operationOrderEntities);
    }

    @Override
    public ResponseData listOperationOrder(DateRangeReq req) {
        // 不传分页参数,查全部
        OperationOrderSelectDTO build = OperationOrderSelectDTO.builder()
                .actualStartDate(Objects.isNull(req.getStart1()) ? null : DateUtil.format(req.getStart1(), DateUtil.DATETIME_FORMAT))
                .actualEndDate(Objects.isNull(req.getEnd1()) ? null : DateUtil.format(req.getEnd1(), DateUtil.DATETIME_FORMAT))
                .actualFinishStartDate(Objects.isNull(req.getStart2()) ? null : DateUtil.format(req.getStart2(), DateUtil.DATETIME_FORMAT))
                .actualFinishEndDate(Objects.isNull(req.getEnd2()) ? null : DateUtil.format(req.getEnd2(), DateUtil.DATETIME_FORMAT))
                .build();
        Page<OperationOrderEntity> page = operationService.getOperationOrderList(build);
        List<OperationOrderEntity> records = page.getRecords();
        return ResponseData.success(records);
    }

    @Override
    public ResponseData listOperationOrderReport(DateRangeReq req) {
        List<ReportLineEntity> list = reportLineService.lambdaQuery()
                // 上报时间
                .between(!Objects.isNull(req.getStart1()) && !Objects.isNull(req.getEnd1()), ReportLineEntity::getReportDate, req.getStart1(), req.getEnd1())
                // 更新时间
                .between(!Objects.isNull(req.getStart2()) && !Objects.isNull(req.getEnd2()), ReportLineEntity::getUpdateTime, req.getStart2(), req.getEnd2())
                .list();
        return ResponseData.success(list);
    }

    /**
     * 获取作业工单当日计划量
     *
     * @param workOrderNumber
     * @return
     */
    @Override
    public ResponseData getDayPlanCount(String workOrderNumber) {
        List<OperationOrderEntity> list = operationService.listInWork(workOrderNumber);
        Double planCount = list.stream().filter(o -> o.getProductCount() != null).mapToDouble(OperationOrderEntity::getProductCount).sum();
        return ResponseData.success(planCount);
    }

    /**
     * 获取作业工单当日完成量
     *
     * @param workOrderNumber
     * @return
     */
    @Override
    public ResponseData getDayFinishCount(String workOrderNumber) {
        List<OperationOrderEntity> list = operationService.listInWork(workOrderNumber);
        Double finishCount = 0.0;
        for (OperationOrderEntity operationOrderEntity : list) {
            finishCount += operationDayCountService.getDayFinishCount(operationOrderEntity.getOperationNumber());
        }
        return ResponseData.success(finishCount);
    }

    @Override
    public ResponseData getReportList(String workOrderNumber) {
        List<OperationDayCountEntity> reportList = new ArrayList<>();
        List<OperationOrderEntity> list = operationService.getByWorkOrder(workOrderNumber);
        if (CollectionUtils.isEmpty(list)) {
            return ResponseData.success(reportList);
        }
        for (OperationOrderEntity operationOrderEntity : list) {
            reportList.addAll(operationDayCountService.getReportList(operationOrderEntity.getOperationNumber()));
        }
        return ResponseData.success(reportList);
    }

    /**
     * 产量内存计算
     *
     * @param eui
     * @param countId
     * @param count
     * @param entity
     */
    @Override
    public void reportLineMemoryCal(String eui, Integer countId, Double count, FacCalEuiEntity entity) {
        memoryCalApplyService.reportLineMemoryCal(eui, countId, count, entity);
    }

    @Override
    public void inputRecordMemoryCal(String eui, Integer countId, Double count, Integer lineId) {
        memoryCalApplyService.inputRecordMemoryCal(eui, countId, count, lineId);
    }

    @Override
    public ResponseData selectByLineIdAndCollect(DeviceOrderDTO deviceOrderDTO) {
        LambdaQueryWrapper<OperationOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(deviceOrderDTO.getLineId() != null, OperationOrderEntity::getLineId, deviceOrderDTO.getLineId())
                .in(!CollectionUtils.isEmpty(deviceOrderDTO.getCollect()), OperationOrderEntity::getOperationNumber, deviceOrderDTO.getCollect());
        return ResponseData.success(operationService.list(wrapper));
    }

    @Override
    public ResponseData selectListByLineId(Integer lineId, String state) {
        List<OperationOrderEntity> operationOrderEntities = operationOrderMapper.selectListByLineId(lineId, state);
        List<OperationOrderEntity> collect = operationOrderEntities.stream()
                .sorted(Comparator.comparing(OperationOrderEntity::getPlanStartDate, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(OperationOrderEntity::getOperationNumber)).collect(Collectors.toList());
        for (OperationOrderEntity operationOrderEntity : collect) {
            ResponseData materialData = materialInterface.detailByCodeAndSkuId(operationOrderEntity.getMaterialCode(), operationOrderEntity.getSkuId());
            MaterialEntityDTO materialEntity = JacksonUtil.getResponseObject(materialData, MaterialEntityDTO.class);
            if (materialEntity != null) {
                operationOrderEntity.setMaterialFields(materialEntity);
            }
            operationOrderEntity.setPriorityName(PriorityTypeEnum.getNameByCode(operationOrderEntity.getPriority()));
        }
        return ResponseData.success(collect);
    }

    /*@Override
    public void dealOperationOrderConstantState() {
        List<Integer> list = new ArrayList<>();
        list.add(OperationStateEnum.INVESTMENT.getCode());
        list.add(OperationStateEnum.HANG_UP.getCode());
        List<OperationOrderEntity> operationOrders = operationService.getByStates(list);
        for (OperationOrderEntity operationOrder : operationOrders) {
            HashMap<String, Object> map = new HashMap<>();
            map.put(ColumnUtil.getField(OperationOrderEntity::getOperationNumber), operationOrder.getOperationNumber());
            map.put(ColumnUtil.getField(OperationOrderEntity::getState), operationOrder.getState());
            workOrderInterface.dealConstantState(map);
        }
    }*/

    @Override
    public ResponseData getOperationOrderDayCount(@RequestBody OperationDayCountDTO entity) {
        LambdaQueryWrapper<OperationDayCountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationDayCountEntity::getLineId, entity.getLineId())
                .eq(OperationDayCountEntity::getTime, entity.getTime());
        List<OperationDayCountEntity> list = operationDayCountService.list(wrapper);
        //转成dto
        List<OperationDayCountDTO> result = JSON.parseArray(JSON.toJSONString(list), OperationDayCountDTO.class);
        List<String> operationNums = result.stream().map(OperationDayCountDTO::getOperationNumber).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(operationNums)) {
            //查询对应的工单
            LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(OperationOrderEntity::getOperationNumber, OperationOrderEntity::getWorkOrderNum)
                    .in(OperationOrderEntity::getOperationNumber, operationNums);
            List<OperationOrderEntity> operationOrderEntities = operationService.list(queryWrapper);
            Map<String, String> collect = operationOrderEntities.stream().collect(Collectors.toMap(OperationOrderEntity::getOperationNumber, OperationOrderEntity::getWorkOrderNum));
            //设置工单号
            for (OperationDayCountDTO dto : result) {
                dto.setWorkOrderNumber(collect.get(dto.getOperationNumber()));
            }
        }

        return ResponseData.success(result);
    }

    @Override
    public ResponseData batchUpdateOperationOrder(List<OperationOrderDTO> dtos) {
        for (OperationOrderDTO dto : dtos) {
            LambdaUpdateWrapper<OperationOrderEntity> uw = new LambdaUpdateWrapper<>();
            uw.in(OperationOrderEntity::getOperationNumber, dto.getOperationNumbers())
                    .set(!StringUtils.isEmpty(dto.getTooling()), OperationOrderEntity::getTooling, dto.getTooling())
                    .set(dto.getCoefficient() != null, OperationOrderEntity::getCoefficient, dto.getCoefficient())
                    .set(OperationOrderEntity::getIsAutoCompleted, dto.getIsAutoCompleted());
            operationService.update(uw);
        }
        return ResponseData.success();
    }

    @Override
    public ResponseData dealReportCountRecord(String operationNumber, Integer action) {
        reportCountService.dealReportCountRecord(operationNumber, action, Constant.MANUAL);
        return ResponseData.success();
    }

    @Override
    public ResponseData getFinishAndReportCount(List<String> operationNumbers) {
        List<FinishAndReportCountDTO> list = new ArrayList<>();
        // 查询作业工单列表
        LambdaQueryWrapper<OperationOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.in(OperationOrderEntity::getOperationNumber, operationNumbers);
        List<OperationOrderEntity> operationOrderEntities = operationService.list(qw);
        Map<String, List<OperationOrderEntity>> operationOrder = operationOrderEntities.stream()
                .collect(Collectors.groupingBy(OperationOrderEntity::getOperationNumber));
        // 获取已报工数 = 手动报工数
        LambdaQueryWrapper<ReportLineEntity> reportWrapper = new LambdaQueryWrapper<>();
        reportWrapper.in(ReportLineEntity::getOperationNumber, operationNumbers)
                .in(ReportLineEntity::getType, ReportType.REPORT.getType(), ReportType.AUTO.getType());
        List<ReportLineEntity> allReportList = reportLineService.list(reportWrapper);

        Map<String, List<ReportLineEntity>> reportCollect = allReportList.stream()
                .filter(o -> o.getType().equals(ReportType.REPORT.getType()))
                .collect(Collectors.groupingBy(ReportLineEntity::getOperationNumber));
        Map<String, List<ReportLineEntity>> autoCollect = allReportList.stream()
                .filter(o -> o.getType().equals(ReportType.AUTO.getType()))
                .collect(Collectors.groupingBy(ReportLineEntity::getOperationNumber));
        for (Map.Entry<String, List<OperationOrderEntity>> entry : operationOrder.entrySet()) {
            String operationNumber = entry.getKey();
            List<ReportLineEntity> autoEntities = autoCollect.get(operationNumber);
            List<ReportLineEntity> reportEntities = reportCollect.get(operationNumber);
            // 获取完工数量 = 自动报工数 + 手动报工数
            double reportCount = CollectionUtils.isEmpty(reportEntities) ? 0.0 : reportEntities.stream().mapToDouble(ReportLineEntity::getFinishCount).sum();
            double finishCount = CollectionUtils.isEmpty(autoEntities) ? 0.0 : autoEntities.stream().mapToDouble(ReportLineEntity::getFinishCount).sum() + reportCount;
            double productCount = operationOrder.get(operationNumber).get(0).getProductCount();
            int state = operationOrder.get(operationNumber).get(0).getState();
            list.add(
                    FinishAndReportCountDTO.builder()
                            .state(state)
                            .operationNumber(operationNumber)
                            .productCount(productCount)
                            .finishCount(finishCount)
                            .reportCount(reportCount)
                            .build()
            );
        }
        return ResponseData.success(list);
    }

    /**
     * 获取当天报工数，并以工单汇总返回
     *
     * @param recordDate
     */
    @Override
    public ResponseData getDayFinishCountOfWorkOrder(Long recordDate) {
        Date date = new Date(recordDate);
        List<OperationOrderEntity> orderEntities = operationService.getFinishCountGroupByWorkOrder(date);
        ArrayList<OperationDayCountDTO> dtos = new ArrayList<>();
        for (OperationOrderEntity entity : orderEntities) {
            dtos.add(OperationDayCountDTO.builder().workOrderNumber(entity.getWorkOrderNum()).count(entity.getFinishCount()).build());
        }
        return ResponseData.success(dtos);
    }

    /**
     * 获取当天完成单据
     *
     * @param recordDate
     * @return
     */
    @Override
    public ResponseData getDayFinishOrderCount(Long recordDate) {
        Date date = new Date(recordDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        calendar.add(Calendar.SECOND, -1);
        Date time = calendar.getTime();
        List<OperationOrderEntity> orderEntities = operationService.lambdaQuery()
                .select(OperationOrderEntity::getWorkOrderNum, OperationOrderEntity::getOperationNumber)
                .between(OperationOrderEntity::getActualEndDate, date, time).list();
        List<OperationDayCountDTO> dtos = orderEntities.stream()
                .map(o -> OperationDayCountDTO.builder()
                        .workOrderNumber(o.getWorkOrderNum())
                        .operationNumber(o.getOperationNumber())
                        .build()).collect(Collectors.toList());
        return ResponseData.success(dtos);
    }

    /**
     * 获取作业工单
     * 待生产单数：该产线类型下，状态为生效，工单的排产状态为已排产，并且有作业工单(或有产线名称)，这些工单(作业工单)的个数
     * （工单状态：生效，投产，挂起；已排产的工单个数；工单/作业工单分开计算）
     *
     * @return
     */
    @Override
    public ResponseData getPlanOrderCount(Integer modelId) {
        List<OperationOrderEntity> orderEntities = operationService.lambdaQuery()
                .select(OperationOrderEntity::getWorkOrderNum, OperationOrderEntity::getOperationNumber)
                .eq(OperationOrderEntity::getWorkCenterId, modelId)
                .in(OperationOrderEntity::getState, OperationStateEnum.RELEASED.getCode(),
                        OperationStateEnum.INVESTMENT.getCode(),
                        OperationStateEnum.HANG_UP.getCode()).list();
        List<OperationDayCountDTO> dtos = orderEntities.stream()
                .map(o -> OperationDayCountDTO.builder()
                        .workOrderNumber(o.getWorkOrderNum())
                        .operationNumber(o.getOperationNumber())
                        .build()).collect(Collectors.toList());
        return ResponseData.success(dtos);
    }

    /**
     * 获取投产状态的作业工单
     *
     * @param modelId
     * @return
     */
    @Override
    public ResponseData getStatesOrder(Integer modelId) {
        List<OperationOrderEntity> orderEntities = operationService.lambdaQuery()
                .select(OperationOrderEntity::getWorkOrderNum,
                        OperationOrderEntity::getOperationNumber,
                        OperationOrderEntity::getLineId,
                        OperationOrderEntity::getState)
                .eq(OperationOrderEntity::getWorkCenterId, modelId)
                .in(OperationOrderEntity::getState, OperationStateEnum.RELEASED.getCode(),
                        OperationStateEnum.INVESTMENT.getCode(),
                        OperationStateEnum.HANG_UP.getCode())
                .list();
        List<OperationOrderStateDTO> dtos = orderEntities.stream()
                .map(entity -> OperationOrderStateDTO.builder()
                        .workOrderNumber(entity.getWorkOrderNum())
                        .operationNumber(entity.getOperationNumber())
                        .lineId(entity.getLineId())
                        .state(entity.getState())
                        .build()).collect(Collectors.toList());
        return ResponseData.success(dtos);
    }

    /**
     * 按人员统计产出
     *
     * @param modelId
     * @param recordDate
     * @return
     */
    @Override
    public ResponseData getFinishCountGroupByUser(Integer modelId, Long recordDate) {
        Date date = new Date(recordDate);
        List<ReportLineEntity> entities = reportLineService.getFinishCountGroupByUser(modelId, date);
        List<OperationDayCountDTO> dtos = entities.stream()
                .map(o -> OperationDayCountDTO.builder()
                        .operationNumber(o.getOperator())
                        .count(o.getFinishCount())
                        .build()).collect(Collectors.toList());
        return ResponseData.success(dtos);
    }

    /**
     * 作业工单完成数,按产线统计
     *
     * @param modelId
     * @param recordDate
     */
    @Override
    public ResponseData getDayFinishCountOfLine(Integer modelId, Long recordDate) {
        Date date = new Date(recordDate);
        List<OperationDayCountDTO> dtos = reportLineService.getFinishCountGroupByLine(modelId, date);
        return ResponseData.success(dtos);
    }

    /**
     * 作业工单完成个数
     *
     * @param modelId
     * @param recordDate
     * @return
     */
    @Override
    public ResponseData getDayFinishOrderCountOfLine(Integer modelId, Long recordDate) {
        Date date = new Date(recordDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        calendar.add(Calendar.SECOND, -1);
        Date endTime = calendar.getTime();
        List<OperationOrderEntity> orderEntities = operationService.lambdaQuery()
                .eq(OperationOrderEntity::getWorkCenterId, modelId)
                .between(OperationOrderEntity::getActualEndDate, date, endTime)
                .list();
        Map<Integer, Long> map = orderEntities.stream().collect(Collectors.groupingBy(OperationOrderEntity::getLineId, Collectors.counting()));
        Set<Map.Entry<Integer, Long>> entries = map.entrySet();
        List<OperationDayCountDTO> dtos = new ArrayList<>();
        for (Map.Entry<Integer, Long> entry : entries) {
            dtos.add(OperationDayCountDTO.builder()
                    .lineId(entry.getKey())
                    .count(entry.getValue().doubleValue())
                    .build());
        }
        return ResponseData.success(dtos);
    }

    @Override
    public ResponseData getOperationOrderTeamMembers(String operationNumber) {
        return ResponseData.success(operationOrderTeamService.lambdaQuery()
                .eq(OperationOrderTeamEntity::getOperationOrderNumber, operationNumber).list());
    }

    @Override
    public ResponseData getOccupyOrderByLine(Integer lineId) {
        //查询最后一条，如果是占用计数器的状态，则取其工单执行新方法
        ReportCountEntity reportCountEntity = reportCountService.lambdaQuery()
                .eq(ReportCountEntity::getLineId, lineId)
                .orderByDesc(ReportCountEntity::getCreateTime).orderByDesc(ReportCountEntity::getId)
                .last("limit").one();
        if (reportCountEntity == null) {
            return ResponseData.success();
        }

        if (ReportType.activeTypeList.contains(reportCountEntity.getType())) {
            return ResponseData.success(operationService.getByOperationNumberSimple(reportCountEntity.getOperationNumber()));
        }

        return ResponseData.success();
    }

}
