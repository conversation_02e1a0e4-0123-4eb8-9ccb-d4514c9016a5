package com.yelink.assignment.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.assignment.entity.common.ConfigEntity;
import com.yelink.assignment.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @description: 作业工单定时任务
 * @time 2021/8/25 16:58
 */
@Lazy(false)
//@Component
@EnableScheduling
@Slf4j
public class ScheduleTask /*implements SchedulingConfigurer*/ {
    /**
     * 默认每分钟执行一次
     */
    private final static int DEFAULT_INTERVAL = 60;
    private static final String PROGRESS_INTERVAL = "progressInterval";
    @Autowired
    private RedisTemplate redisTemplate;
    /*@Autowired
    private MethodService methodService;*/
    @Autowired
    private ConfigService configService;


    /*@Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        int interval = getInterval();
        taskRegistrar.addTriggerTask(() -> {
            //分布式锁
            Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.OPERATION_PROCESS_LOCK, new Date(), interval, TimeUnit.SECONDS);
            if (lockStatus == null || !lockStatus) {
                return;
            }
            log.info("作业工单定时任务{}", new Date());
            methodService.deal();
        }, triggerContext -> {
            //根据工厂最早开始时间拿到cron表达式
            PeriodicTrigger trigger;
            try {
                trigger = new PeriodicTrigger(interval, TimeUnit.SECONDS);
                return trigger.nextExecutionTime(triggerContext);
            } catch (Exception e) {
                //如果格式有问题就按默认的
                trigger = new PeriodicTrigger(DEFAULT_INTERVAL, TimeUnit.SECONDS);
                return trigger.nextExecutionTime(triggerContext);
            }
        });
    }*/

    private int getInterval() {
        LambdaQueryWrapper<ConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConfigEntity::getCode, PROGRESS_INTERVAL);
        ConfigEntity entity = configService.getOne(wrapper);
        if (entity != null) {
            return Integer.valueOf(entity.getValue());
        }
        return DEFAULT_INTERVAL;
    }

}
