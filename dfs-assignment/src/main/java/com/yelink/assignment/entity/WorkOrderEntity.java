package com.yelink.assignment.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-03-17 11:59
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 工单id
     */
    private Integer workOrderId;

    /**
     * 工单号
     */
    private String workOrderNumber;

    /**
     * 工单名称
     */
    private String workOrderName;

    /**
     * 指标任务ID,用于对接kafka设备
     */
    private Integer targetJobId;

    /**
     * 告警任务ID,用于对接kafka设备
     */
    private Integer alarmJobId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 状态
     * 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消
     */
    private Integer state;

    /**
     * 产线编号
     */
    private String lineCode;

    /**
     * 产线名称
     */
    private String lineName;

    /**
     * 产线id
     */
    private Integer lineId;
    /**
     * 产线id
     */
    private String lineIds;
    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 计划数量
     */
    private Double planQuantity;

    /**
     * 工单完成定义（实际计划量）
     */
    private Double actualQuantity;

    /**
     * 工艺编号
     */
    private String craftCode;
    /**
     * 工艺名称
     */
    private String craftName;
    /**
     * 工艺版本号
     */
    private String craftVersion;

    /**
     * 工艺中的最后一道工序
     */
    private Boolean isEndCraft;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品编号
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品版本号
     */
    private String productVersion;

    /**
     * 物料编号
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料状态
     * 物料状态（1-创建 2-启用 3-停用 4-废弃）
     */
    private Integer materialState;
    /**
     * 物料状态
     * 物料状态（1-创建 2-启用 3-停用 4-废弃）
     */
    private String materialStateName;

    /**
     * 物料规格
     */
    private String materialStandard;
    /**
     * 不合格量
     */
    private Double unqualified;

    /**
     * 关联的父工单
     */
    private String pnumber;
    /**
     * 关联的父工单
     */
    private List<WorkOrderEntity> subWorkOrder;

    /**
     * 是否有父工单 1是 0否
     */
    private Boolean isPid;

    /**
     * 子工单顺序
     */
    private String idSequence;

    /**
     * 进度
     */
    private Double progress;

    /**
     * 规格单位
     */
    private String unit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 物料当前库存
     */
    private Double currentInventory;
    /**
     * 物料是否齐套
     */
    private Boolean materialIsComplete;
    /**
     * 物料齐套数量
     */
    private Double materialCompleteNum;
    /**
     * 物料欠套数量
     */
    private Double materialOweNum;
    /**
     * 理论工作时长
     */
    private Double theoreticalWorkingHours;

    /**
     * 类型（该类型用于指标）
     */
    private String type;

    /**
     * 工单类型 1-正常工单 2-返回工单
     */
    private Integer orderType;

    private String orderTypeName;

    /**
     * CreateBy
     */
    private String createBy;
    /**
     * 创建人名称
     */
    private String createName;

    /**
     * CreateDate
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * UpdateBy
     */
    private String updateBy;

    /**
     * UpdateDate
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;
    /**
     * 有效工时
     */
    private Double effectiveHours;

    /**
     * 计划员真实姓名
     */
    private String magNickname;
    /**
     * 计划员手机号
     */
    private String magPhone;
    /**
     * 计划员账号
     */
    private String magName;

    /**
     * 已完成数量
     */
    private Double finishCount;

    /**
     * 投入数量
     */
    private Double inputTotal;

    /**
     * 工单接收人（工单完成时通知人员账号，逗号隔开）
     */
    private String noticeUsername;

    /**
     * 优先级
     */
    private String priority;
    /**
     * 工单状态名称 0-创建 1-发放 2-完成 3-关闭 4-取消
     */
    private String stateName;

    /**
     * 客户名称
     */
    private String customer;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 计划交付时间/计划开始生产时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handOverDate;

    /**
     * 订单号，用逗号隔开
     */
    private String productOrderNumber;

    /**
     * 销售订单号，用逗号隔开
     */
    private String saleOrderNumber;

    /**
     * 包装单位
     */
    private String packageUnit;

    /**
     * 包装数
     */
    private Double packageQuantity;
    /**
     * 是否备料
     */
    private Boolean prepared;
    /**
     * 是否发料
     */
    private Boolean assigned;
    /**
     * erp关联单据编号
     */
    private String erpDocumentCode;

    /**
     * 工位是否正在执行该工单 0-否 1-是
     */
    private Integer working;

    private String orderName;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 是否有子工单
     */
    private Boolean isSubWorkOrder;

    /**
     * 计量重量(工单物料计划数量记录计算用)
     */
    private Double measurementQuantity;

    /**
     * 计量单位(工单物料计划数量记录计算用)
     */
    private String measurementUnit;

    /**
     * 审核人
     */
    private String approver;

    /**
     * 审核人名字
     */
    private String approverName;

    /**
     * 实际审批人
     */
    private String actualApprover;

    /**
     * 实际审批人名字
     */
    private String actualApproverName;

    /**
     * 审批状态
     */
    private Integer approvalStatus;

    /**
     * 审批状态名称
     */
    private String approvalStatusName;

    /**
     * 审批建议
     */
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;
    /**
     * 产线类型名称（大屏用）
     */
    private String lineModelName;

    /**
     * 规则id（批量创建工单）
     */
    private Integer numberRuleId;

    /**
     * 生产时间段
     * <p>
     * [
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * ]
     */
    private List<Map> produceRange;

    /**
     * 物料iD
     */
    private String materialId;
    /**
     * 产前状态
     */
    private Boolean prenatalStatus;
    /**
     * 计划工时
     */
    private Double plannedWorkingHours;
    /**
     * 实际工时
     */
    private Double actualWorkingHours;
    /**
     * 工单执行
     */
    private String executionStatus;
    /**
     * 工单执行
     */
    private String executionStatusName;
    /**
     * 时差
     */
    private Double timeDifference;
    /**
     * 调机时长
     */
    private String workOrderRecordTime;

    /**
     * 工单事件(大屏的工单备注信息要用)
     */
    private String workOrderEvent;

    /**
     * 工序id
     */
    private Integer procedureId;

    /**
     * 工序名称
     */
    private String procedureName;

    /**
     * 客户名称（关联销售订单）
     */
    private String customerName;

    /**
     * 排产数量
     */
    private Double productCount;

    /**
     * 待排数量
     */
    private Double pendentQuantity;

    /**
     * 流转数量
     */
    private Double inStockCount;

    /**
     * 制造单元模型ID
     */
    private Integer lineModelId;

    /**
     * 工艺工序名称
     */
    private String craftProcedureName;

    /**
     * 工艺工序ID
     */
    private Integer craftProcedureId;

    /**
     * 流转时长
     */
    private Double circulationDuration;


}
