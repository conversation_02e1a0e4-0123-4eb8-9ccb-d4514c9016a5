package com.yelink.assignment.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/8/4 14:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OperationOrderExportDTO {
    /**
     * 作业工单编号
     */
    @ExcelProperty("作业工单号")
    private String operationNumber;
    /**
     * 状态名称 创建、生效、投产、挂起、完成
     */
    @ExcelProperty("状态")
    private String stateName;
    /**
     * 优先级名称
     */
    @ExcelProperty("优先级")
    private String priorityName;
    /**
     * 物料编码
     */
    @ExcelProperty("物料编码")
    private String materialCode;
    /**
     * 物料名称
     */
    @ExcelProperty("物料名称")
    private String materialName;
    /**
     * 待产数量
     */
    @ExcelProperty("排产数量")
    private Double productCount;
    /**
     * 完成数量
     */
    @ExcelProperty("完成数量")
    private Double finishCount;
    /**
     * 不良数量
     */
    @ExcelProperty("不良数量")
    private Double unqualified;
    /**
     * 制造单元名称
     */
    @ExcelProperty("制造单元")
    private String lineName;
    /**
     * 制造单元类型名称
     */
    @ExcelProperty("制造单元类型")
    private String modelName;
    /**
     * 工序名称
     */
    @ExcelProperty("工序名称")
    private String procedureName;

    /**
     * 计划开始时间
     */
    @ExcelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planStartDate;

    /**
     * 计划结束时间
     */
    @ExcelProperty("计划完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planEndDate;
    /**
     * 实际开始时间
     */
    @ExcelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;
    /**
     * 实际完成时间
     */
    @ExcelProperty("实际完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;
    /**
     * 实际工时
     */
    @ExcelProperty("实际工时")
    private Double actualWorkingHours;
    /**
     * 工装（工装模具、模具编号）
     */
    @ExcelProperty("模具编号")
    private String tooling;
    /**
     * 班次类型
     */
    @ExcelProperty("班次")
    private String shiftType;
    /**
     * 生产工单编号
     */
    @ExcelProperty("生产工单号")
    private String workOrderNum;
    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    // 物料配置字段(物料规格、图号、材质)
    /**
     * 物料规格
     */
    @ExcelProperty("物料规格")
    private String standard;
    /**
     * 图号
     */
    @ExcelProperty("图号")
    private String drawingNumber;
    /**
     * 材质
     */
    @ExcelProperty("材质")
    private String rawMaterial;

    public static OperationOrderExportDTO convertOperationOrderExportDTO(OperationOrderEntity record){
         return OperationOrderExportDTO.builder()
                .operationNumber(record.getOperationNumber())
                .stateName(record.getStateName())
                .priorityName(record.getPriorityName())
                .procedureName(record.getProcedureName())
                .planStartDate(record.getPlanStartDate())
                .planEndDate(record.getPlanEndDate())
                .actualStartDate(record.getActualStartDate())
                .actualEndDate(record.getActualEndDate())
                .actualWorkingHours(record.getActualWorkingHours())
                .tooling(record.getTooling())
                .shiftType(record.getShiftType())
                .workOrderNum(record.getWorkOrderNum())
                .productCount(record.getProductCount())
                .finishCount(record.getFinishCount())
                .unqualified(record.getUnqualified())
                .lineName(record.getLineName())
                .modelName(record.getWorkCenterName())
                .remark(record.getRemark())
                .materialCode(record.getMaterialCode())
                .materialName(record.getMaterialFields().getName())
                .standard(record.getMaterialFields().getStandard())
                .drawingNumber(record.getMaterialFields().getDrawingNumber())
                .rawMaterial(record.getMaterialFields().getRawMaterial())
                .build();
    }

}
