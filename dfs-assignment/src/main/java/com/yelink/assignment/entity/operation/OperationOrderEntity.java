package com.yelink.assignment.entity.operation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.entity.MaterialEntityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-03-03
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("dfs_operation_order")
public class OperationOrderEntity extends Model<OperationOrderEntity>  {

    private static final long serialVersionUID = 8582433437601788991L;

    /**
     * 作业工单id
     */
    @ApiModelProperty("作业工单id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 作业工单编号
     */
    @ApiModelProperty("作业工单编号")
    @LogTag(name = "作业工单编号")
    @TableField(value = "operation_number")
    @NotNull(groups = {Update.class}, message = "作业工单编号不能为空")
    private String operationNumber;

    /**
     * 生产工单编号
     */
    @ApiModelProperty("生产工单编号")
    @LogTag(name = "生产工单编号")
    @TableField(value = "work_order_num")
    @NotNull(groups = {Update.class}, message = "生产工单编号不能为空")
    private String workOrderNum;

    /**
     * 班次类型
     */
    @ApiModelProperty("班次类型")
    @LogTag(name = "班次类型")
    @TableField(value = "shift_type")
    private String shiftType;

    /**
     * 班次id
     */
    @ApiModelProperty("班次id")
    @TableField(value = "shift_id")
    private Integer shiftId;


    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @LogTag(name = "物料编码")
    @TableField(value = "material_code")
    @NotNull(groups = {Update.class}, message = "物料编码不能为空")
    private String materialCode;

    /**
     * 状态id 1-创建、2-生效、3-投产、4-挂起、5-完成
     */
    @ApiModelProperty("状态id 1-创建、2-生效、3-投产、4-挂起、5-完成")
    @TableField(value = "state")
    private Integer state;

    /**
     * 状态名称 创建、生效、投产、挂起、完成
     */
    @ApiModelProperty("状态名称 创建、生效、投产、挂起、完成")
    @LogTag(name = "状态名称")
    @TableField(value = "state_name")
    private String stateName;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    @TableField(value = "product_count")
    @NotNull(groups = {Update.class}, message = "排产数量不能为空")
    private Double productCount;

    /**
     * 计划开始时间
     */
    @ApiModelProperty("计划开始时间")
    @LogTag(name = "计划开始时间")
    @TableField(value = "plan_start_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planStartDate;

    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划结束时间")
    @LogTag(name = "计划结束时间")
    @TableField(value = "plan_end_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planEndDate;

    /**
     * 优先级1-正常（默认） 2-优先 3-加急 4-特急
     */
    @ApiModelProperty("优先级1-正常（默认） 2-优先 3-加急 4-特急")
    @TableField(value = "priority")
    private Integer priority;

    /**
     * 优先级名称
     */
    @ApiModelProperty("优先级名称")
    @TableField(exist = false)
    private String priorityName;

    /**
     * 不良数量
     */
    @ApiModelProperty("不良数量")
    @TableField(value = "unqualified")
    private Double unqualified;

    /**
     * 完成数量
     */
    @ApiModelProperty("完成数量")
    @TableField(value = "finish_count")
    private Double finishCount;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    @TableField(value = "input_total")
    private Double inputTotal;

    /**
     * 待产数量
     */
    @ApiModelProperty("待产数量")
    @TableField(value = "wait_product_count")
    private Double waitProductCount;

    /**
     * 制造单元名称
     */
    @ApiModelProperty("制造单元名称")
    @LogTag(name = "制造单元名称")
    @TableField(value = "line_name")
    private String lineName;

    /**
     * 制造单元id
     */
    @ApiModelProperty("制造单元id")
    @TableField(value = "line_id")
    private Integer lineId;

    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @LogTag(name = "制造单元类型名称")
    @TableField(value = "work_center_name")
    private String workCenterName;
    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心Id")
    @TableField(value = "work_center_id")
    private Integer workCenterId;

    /**
     * 工序名称
     * 数据库有该字段，但是不用
     */
    @ApiModelProperty("工序名称 数据库有该字段，但是不用")
    @TableField(exist = false)
    private String procedureName;

    /**
     * 实际工时
     */
    @ApiModelProperty("实际工时")
    @TableField(value = "actual_working_hours")
    private Double actualWorkingHours;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @TableField(value = "actual_start_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际完成时间
     */
    @ApiModelProperty("实际完成时间")
    @TableField(value = "actual_end_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 创建人账号
     */
    @ApiModelProperty("创建人账号")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    @TableField(value = "create_name")
    private String createName;


    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    @LogTag(name = "修改人姓名")
    @TableField(value = "update_name")
    private String updateName;

    /**
     * 修改人账号
     */
    @ApiModelProperty("修改人账号")
    @TableField(value = "update_by")
    private String updateBy;


    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @LogTag(name = "修改时间")
    @TableField(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    /**
     * 批次
     */
    @ApiModelProperty("批次")
    @LogTag(name = "批次")
    @TableField(value = "batch")
    private String batch;

    /**
     * 载具号
     */
    @ApiModelProperty("载具号")
    @LogTag(name = "载具号")
    @TableField(value = "vehicle_code")
    private String vehicleCode;

    /**
     * 工装（工装模具、模具编号）
     */
    @ApiModelProperty("工装（工装模具、模具编号）")
    @LogTag(name = "工装")
    @TableField(value = "tooling")
    private String tooling;

    /**
     * 报工系数
     */
    @ApiModelProperty("报工系数")
    @TableField(value = "coefficient")
    private Integer coefficient;

    /**
     * 操作员
     */
    @ApiModelProperty("操作员")
    @LogTag(name = "操作员")
    @TableField(value = "producer")
    private String producer;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * 物料Id
     */
    @ApiModelProperty("物料Id")
    @TableField(exist = false)
    private Integer materialId;

    /**
     * 工艺Id
     */
    @ApiModelProperty("工艺Id")
    @TableField(exist = false)
    private Integer craftId;

    /**
     * 是否自动完工
     */
    @ApiModelProperty("是否自动完工")
    @TableField(value = "is_auto_completed")
    private Boolean isAutoCompleted;

    /**
     * 班组ID
     */
    @ApiModelProperty("班组ID")
    @TableField(value = "team_id")
    private Integer teamId;

    /**
     * 班组名称
     */
    @ApiModelProperty("班组名称")
    @TableField(exist = false)
    private String teamName;

    /**
     * 设备ID
     */
    @ApiModelProperty("设备ID")
    @TableField(value = "device_id")
    private Integer deviceId;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @TableField(exist = false)
    private String deviceName;


    /**
     * 工作中心类型
     */
    @ApiModelProperty("工作中心类型")
    @TableField(exist = false)
    private String workCenterType;

    /**
     * 工作中心关联资源类型
     */
    @ApiModelProperty("工作中心关联资源类型")
    @TableField(exist = false)
    private String workCenterRelevanceType;

    /**
     * 作业工单 工艺工序关联关系
     */
    @ApiModelProperty("作业工单 工艺工序关联关系")
    @TableField(exist = false)
    private List<OperationOrderProcedureRelationEntity> procedureRelationEntities;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    @TableField(exist = false)
    private MaterialEntityDTO materialFields;

    /**
     * 工单投产班组成员列表
     */
    @ApiModelProperty("工单投产班组成员列表")
    @TableField(exist = false)
    private List<OperationOrderTeamEntity> operationOrderTeamEntities;

    /**
     * 工单关联班组id
     */
    @ApiModelProperty("工单关联班组id")
    @TableField(exist = false)
    private List<Integer> relevanceTeamIds;

    /**
     * 工单关联班组名称
     */
    @ApiModelProperty("工单关联班组名称")
    @TableField(exist = false)
    private List<String> relevanceTeamNames;

    /**
     * 工单关联设备id
     */
    @ApiModelProperty("工单关联设备id")
    @TableField(exist = false)
    private List<Integer> relevanceDeviceIds;
    /**
     * 工单关联设备名称
     */
    @ApiModelProperty("工单关联设备名称")
    @TableField(exist = false)
    private List<String> relevanceDeviceNames;

    /**
     * 工单关联制造单元id
     */
    @ApiModelProperty("工单关联制造单元id")
    @TableField(exist = false)
    private List<Integer> relevanceLineIds;
    /**
     * 工单关联制造单元名称
     */
    @ApiModelProperty("工单关联制造单元名称")
    @TableField(exist = false)
    private List<String> relevanceLineNames;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    @TableField(value = "sku_id")
    private Integer skuId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public interface Insert {
    }

    public interface Update {
    }
}
