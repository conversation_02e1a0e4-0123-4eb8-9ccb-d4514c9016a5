package com.yelink.assignment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_unqualified_img")
public class UnqualifiedImgEntity extends Model<UnqualifiedImgEntity> {
    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报工记录id
     */
    @TableField(value = "report_id")
    private Integer reportId;

    /**
     * 图片URL
     */
    @TableField(value = "url")
    private String url;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
