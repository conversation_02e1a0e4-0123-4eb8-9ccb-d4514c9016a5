package com.yelink.assignment.entity.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 各个单据存在关联关系时，相同进行物料比较，返回相同物料的差值
 * @Date 2022/5/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BillMaterialVo {
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料数量
     */
    private Double quantity;
}
