package com.yelink.assignment.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-03-26 18:33
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_report_count")
public class ReportCountEntity extends Model<ReportCountEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * investment-投入
     * hangup-挂起
     * report-报工
     * finish-完工
     */
    @TableField(value = "type")
    private String type;

    /**
     * 产线id
     */
    @TableField(value = "line_id")
    private Integer lineId;

    /**
     * 产线名称
     */
    @TableField(value = "line_name")
    private String lineName;

    /**
     * 作业工单号
     */
    @TableField(value = "operation_number")
    private String operationNumber;

    /**
     * 完成数量
     */
    @TableField(value = "finish_count")
    private Double finishCount;

    /**
     * 不合格数量
     */
    @TableField(value = "unqualified")
    private Integer unqualified;

    /**
     * 账号
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 上报人姓名
     */
    @TableField(value = "user_nickname")
    private String userNickname;

    /**
     * 报工时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "report_date")
    private Date reportDate;

    /**
     * 报工时间time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "report_time")
    private Date reportTime;

    /**
     * 记录显示采集器采集数量的时间点
     */
/*    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "auto_count_record_time")
    private Date autoCountRecordTime;*/

    /**
     * 采集器采集数量(上个采集时间点到当前时间)
     */
/*    @TableField(value = "auto_count")
    private Integer autoCount;*/

    /**
     * 批次
     */
    @TableField(value = "batch")
    private String batch;

    /**
     * 班次id
     */
    @TableField(value = "shift_id")
    private Integer shiftId;

    /**
     * 班次
     */
    @TableField(value = "shift_type")
    private String shiftType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;
    /**
     * 有效工时
     */
    @TableField(value = "effective_hours")
    private Double effectiveHours;

    /**
     * 计数类型 0-普通次数 1-小计 2-汇总
     */
    @TableField(exist = false)
    private Integer countType = 0;

    /**
     * 图片地址
     */
    @TableField(exist = false)
    private String[] picUrls;

    /**
     * 不良描述
     */
    @TableField(exist = false)
    private String defectDesc;

    /**
     * 操作员账号
     */
    @TableField(exist = false)
    private String operator;

    /**
     * 操作员名称
     */
    @TableField(exist = false)
    private String operatorName;

    /**
     * 载具号
     */
    @TableField(exist = false)
    private String vehicleCode;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
