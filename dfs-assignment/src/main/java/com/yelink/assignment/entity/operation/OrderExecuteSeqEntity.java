package com.yelink.assignment.entity.operation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 工单投产顺序表
 * @Author: zengzhengfu
 * @Date: 2022/11/4
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_order_execute_seq")
public class OrderExecuteSeqEntity extends Model<OrderExecuteSeqEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 工单号/作业工单号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 设备id
     */
    @TableField(value = "device_id")
    private Integer deviceId;

    /**
     * 产线id
     */
    @TableField(value = "line_id")
    private Integer lineId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
