package com.yelink.assignment.entity.shift;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022-03-21 14:14
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ShiftEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 班次类型
     */
    private String shiftType;

    /**
     * 上班时间
     */
    private String workTime;

    /**
     * 休息时间
     */
    private String breakTime;

    /**
     * 上班时长
     */
    private Double workDuration;

    /**
     * 休息时长
     */
    private Double breakDuration;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

}
