package com.yelink.assignment.entity.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


/**
 * @Description: 输入工单产量前显示采集器数量
 * @Author: z<PERSON>zheng<PERSON>
 * @Date: 2021/9/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoCountDTO {

    /**
     * 完成数量
     */
    private Double finishCount;
    /**
     * 计数器上报数量
     */
    private Double autoCount;
    /**
     * 上次点击报工按钮的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date autoCountRecordTime;

    /**
     * 有效工时
     */
    private Double effectiveHours;
    /**
     * 参考工时
     */
    private Double referenceHours;

    /**
     * 工单已完成数量
     */
    private Double workOrderFinishCount;

    /**
     * 工单不良数量
     */
    private Double workOrderUnqualified;

    /**
     * 工单已报有效工时
     */
    private Double workOrderEffectiveHours;

    /**
     * 今日产线已完成数量
     */
    private Double lineFinishCount;

    /**
     * 今日产线不良数量
     */
    private Double lineUnqualified;

    /**
     * 今日产线已报有效工时
     */
    private Double lineEffectiveHours;

    /**
     * 不良图片url
     */
    private List<String> picUrls;
}
