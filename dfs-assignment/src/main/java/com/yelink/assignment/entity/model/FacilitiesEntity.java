package com.yelink.assignment.entity.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-12-03 11:13
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class FacilitiesEntity {

    /**
     * 工位id
     */
    private Integer fid;

    /**
     * 工位名称
     */
    private String fname;

    /**
     * 单位id
     */
    private Integer cid;

    /**
     * 厂区id
     */

    private Integer aid;

    /**
     * 车间id
     */
    private Integer gid;

    /**
     * 生产线id
     */
    private Integer productionLineId;

    /**
     * 生产线分支id
     */
    private Integer sublineId;

    /**
     * 产线上顺序
     */
    private Integer lineOrder;

    /**
     * 类型1编码
     */
    private Integer typeOneCode;

    /**
     * 类型1名称
     */
    private String typeOneName;

    /**
     * 类型2编码
     */
    private Integer typeTwoCode;

    /**
     * 类型2名称
     */
    private String typeTwoName;

    /**
     * 根据类型确定，生产工位状态 0-停机 1-运行中 2-暂停 3-故障
     */
    private Integer state;

    /**
     * 是否带传感器  true false
     */
    private Boolean withSensor;

    /**
     * 工位编号
     */
    private String fcode;


    /**
     * 工位地址
     */
    private String place;

    /**
     * 工位图片
     */
    private String img;

    /**
     * 管理人真实姓名
     */
    private String magNickname;

    /**
     * 管理人账号
     */
    private String magName;

    /**
     * 管理人手机号码
     */
    private String magPhone;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 经纬度
     */
    private Object location;

    /**
     * 工位类型id
     */
    private Integer modelId;

    /**
     * 工位顺序(model表)
     */
    private Integer seq;


    /**
     * 是否有告警
     */
    private boolean alarm;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建人
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 0-不参与产线计数
     * 1-采集器 * 1
     * 2-采集器 * 包装系数
     */
    private Integer isCheck;

    /**
     * 是否作为主计数工位
     */
    private Boolean isMain;

    /**
     * 是否作为不合格检测设备
     */
    private Boolean isDefective;

    /**
     * 是否计算产线投入量
     */
    private Boolean isInput;

    /**
     * 设备列表
     */
    private String eui;
    /**
     * 厂区名称
     */
    private String aname;
    /**
     * 车间名称
     */
    private String gname;
    /**
     * 产线名称
     */
    private String linename;

    /**
     * 产线分支模型id
     */
    private String sublineModelId;

    /**
     * 产线分支模型code
     */
    private String sublineModelCode;

    /**
     * 模型code
     */
    private String modelCode;

    /**
     * 并发，即同时创建同类型工位的数量
     */
    private Integer count;

    /**
     * 绑定的员工ID
     */
    private List<String> employeeName;

    /**
     * 绑定的员工中文名
     */
    private List<String> employeeNickname;

    /**
     * 运行时长
     */
    private Long runningDuration;
    /**
     * 暂停时长
     */
    private Long pauseDuration;

    /**
     * 多个code逗号隔开
     */
    private String fcodes;

    /**
     * 质检工位选择的质检方案
     */
    private Integer defectSchemeId;

    /**
     * 是否为维修工位
     */
    private Boolean isMaintain;

    /**
     * 维修工位选择的维修方案
     */
    private Integer maintainSchemeId;


}
