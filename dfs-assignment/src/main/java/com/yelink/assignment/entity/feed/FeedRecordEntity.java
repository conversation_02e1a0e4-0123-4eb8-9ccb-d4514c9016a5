package com.yelink.assignment.entity.feed;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 上料记录表
 * @Date 2022/3/29 17:56
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_feed_record")
public class FeedRecordEntity {

    private static final long serialVersionUID = 1L;

    /**
     * feedRecordId
     */
    @TableId(value = "feed_record_id", type = IdType.AUTO)
    private Integer feedRecordId;

    /**
     * 产线id
     */
    @TableField(value = "line_id")
    private Integer lineId;

    /**
     * 产线名称
     */
    @TableField(exist = false)
    private String lineName;

    /**
     * 工位id
     */
    @TableField(value = "fac_id")
    private Integer facId;

    /**
     * 工位名称
     */
    @TableField(exist = false)
    private String facName;

    /**
     * 作业工单号
     */
    @TableField(value = "operation_order_num")
    private String operationOrderNum;

    /**
     * 工单号
     */
    @TableField(value = "work_order_num")
    private String workOrderNum;

    /**
     * 物料名称
     */
    @TableField(value = "material_name")
    private String materialName;

    /**
     * 物料编码
     */
    @TableField(value = "material_code")
    private String materialCode;

    /**
     * 物料数量
     */
    @TableField(value = "material_num")
    private Double materialNum;


    /**
     * 批次号
     */
    @TableField(value = "batch_number")
    private String batchNumber;

    /**
     * 供应商名称
     */
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 上料时间
     */
    @TableField(value = "feed_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date feedTime;

    /**
     * 上料人
     */
    @TableField(value = "feed_by")
    private String feedBy;

    /**
     * 上料人
     */
    @TableField(exist = false)
    private String feedByName;

    /**
     * 扫码记录id
     */
    @TableField(value = "code_record_id")
    private Integer codeRecordId;

    /**
     * 操作
     */
    @TableField(exist = false)
    private String operation;
}
