package com.yelink.assignment.entity.report.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Description: 完工数量回显
 * @Author: Chensn
 * @Date: 2022/6/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinishAndReportCountDTO {

    /**
     * 作业工单号
     */
    private String operationNumber;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 人工报工数量
     */
    private Double reportCount;

    /**
     * 完成数量
     */
    private Double finishCount;

    /**
     * 计划数量
     */
    private Double productCount;

}
