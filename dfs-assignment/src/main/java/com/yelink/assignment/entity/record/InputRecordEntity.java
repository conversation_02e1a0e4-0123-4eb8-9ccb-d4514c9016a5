package com.yelink.assignment.entity.record;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/10/22 18:54
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_input_record")
public class InputRecordEntity extends Model<InputRecordEntity> {
    /**
     * Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 工单号
     */
    @TableField(value = "operation_number")
    private String operationNumber;

    /**
     * 产线id
     */
    @TableField(value = "line_id")
    private Integer lineId;

    /**
     * 记录显示采集器采集数量的时间点
     */
    @TableField(value = "input_record_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inputRecordTime;

    /**
     * 状态 investment-投入,report-报工,hangUp-挂起,finished-完工
     */
    @TableField(value = "type")
    private String type;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;


}
