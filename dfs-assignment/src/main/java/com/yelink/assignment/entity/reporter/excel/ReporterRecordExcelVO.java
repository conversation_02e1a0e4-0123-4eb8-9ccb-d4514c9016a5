package com.yelink.assignment.entity.reporter.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.assignment.entity.dto.MaterialTypeConfigFieldExportDTO;
import com.yelink.assignment.entity.reporter.vo.ReporterRecordVO;
import com.yelink.dfscommon.entity.dfs.MaterialEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 报工表导出excelVO
 *
 * <AUTHOR>
 * @date 2022-08-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReporterRecordExcelVO extends MaterialTypeConfigFieldExportDTO{
    // 工单表

    @ExcelProperty("生产工单号")
    private String workOrderNumber;

    @ExcelProperty("状态")
    private String state;

    @ExcelProperty("产品编码")
    private String materialCode;

    @ExcelProperty("计划数量")
    private Double planQuantity;

    @ExcelProperty("工序名称")
    private String craftName;

    @ExcelProperty("制造单元类型")
    private String lineModelName;

    @ExcelProperty("制造单元名称")
    private String lineName;

    @ExcelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ExcelProperty("计划完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @ExcelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualStartDate;

    @ExcelProperty("实际完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualEndDate;

    @ExcelProperty("投入数量")
    private Double inputTotal;

    @ExcelProperty("完成数量")
    private Double finishCount;

    @ExcelProperty("不良数量")
    private Double unqualified;

    @ExcelProperty("计划工时")
    private Double plannedWorkingHours;

    @ExcelProperty("生产时长")
    private Double actualWorkingHours;

    @ExcelProperty("流转时长")
    private Double circulationDuration;

    @ExcelProperty("生产订单号")
    private String orderNumber;

    @ExcelProperty("销售订单号")
    private String saleOrderNumber;

    @ExcelProperty("作业工单号")
    private String operationNumber;

    // 报工表

    @ExcelProperty("上报方式")
    private String type;

    @ExcelProperty("批次")
    private String batch;

    @ExcelProperty("载具号")
    private String vehicleCode;

    @ExcelProperty("班次")
    private String shiftType;

    @ExcelProperty("上报完成数")
    private Double finishCountTwo;

    @ExcelProperty("上报不良数")
    private Double unqualifiedTwo;

    @ExcelProperty("不良描述")
    private String defectDesc;

    /**
     * 后端是有效工时
     */
    @ExcelProperty("上报工时")
    private double effectiveHours;

    @ExcelProperty("生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportDate;

    @ExcelProperty("操作员")
    private String operator;

    @ExcelProperty("上报人")
    private String userNickname;

    @ExcelProperty("上报时间")
    private Date createTime;



    /**
     * 对应物料表的name
     */
    @ExcelProperty("产品名称")
    private String name;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String customer;

    @ExcelProperty(value = "物料编码")
    private String code;

    @ExcelProperty(value = "单位")
    private String unit;

    @ExcelProperty(value = "图号")
    private String drawingNumber;

    @ExcelProperty(value = "物料规格")
    private String standard;

    @ExcelProperty(value = "材质")
    private String rawMaterial;

    public static ReporterRecordExcelVO convertToReporterRecordExportDTO(ReporterRecordVO recordVO, MaterialEntity materialFields) {
        ReporterRecordExcelVO build = ReporterRecordExcelVO.builder()
                .workOrderNumber(recordVO.getWorkOrderNumber())
                .state(recordVO.getState())
                .materialCode(recordVO.getMaterialCode())
                .planQuantity(recordVO.getPlanQuantity())
                .craftName(recordVO.getCraftName())
                .lineModelName(recordVO.getLineModelName())
                .lineName(recordVO.getLineName())
                .startDate(recordVO.getStartDate())
                .endDate(recordVO.getEndDate())
                .actualStartDate(recordVO.getActualStartDate())
                .actualEndDate(recordVO.getActualEndDate())
                .inputTotal(recordVO.getInputTotal())
                .finishCount(recordVO.getFinishCount())
                .unqualified(recordVO.getUnqualified())
                .plannedWorkingHours(recordVO.getPlannedWorkingHours())
                .actualWorkingHours(recordVO.getActualWorkingHours())
                .circulationDuration(recordVO.getCirculationDuration())
                .orderNumber(recordVO.getOrderNumber())
                .saleOrderNumber(recordVO.getSaleOrderNumber())
                .type(recordVO.getType())
                .batch(recordVO.getBatch())
                .vehicleCode(recordVO.getVehicleCode())
                .shiftType(recordVO.getShiftType())
                .finishCountTwo(recordVO.getFinishCountTwo())
                .unqualifiedTwo(recordVO.getUnqualifiedTwo())
                .defectDesc(recordVO.getDefectDesc())
                .effectiveHours(recordVO.getEffectiveHours())
                .reportDate(recordVO.getReportDate())
                .operator(recordVO.getOperator())
                .userNickname(recordVO.getUserNickname())
                .createTime(recordVO.getCreateTime())
                .customer(recordVO.getCustomer())
                .unit(materialFields.getComp())
                .code(materialFields.getCode())
                .name(materialFields.getName())
                .drawingNumber(materialFields.getDrawingNumber())
                .standard(materialFields.getStandard())
                .rawMaterial(materialFields.getRawMaterial())
                .build();
        MaterialTypeConfigFieldExportDTO.setMaterialFieldVale(build, materialFields);
        return build;
    }
}

