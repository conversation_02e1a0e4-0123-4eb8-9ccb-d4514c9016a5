package com.yelink.assignment.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 删除单据时记录相关单据
 * @author: zengfu
 * @create: 2020-06-13 10:41
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName("assignment_delete_record")
public class DeleteRecordEntity extends Model<DeleteRecordEntity> {


    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 单据号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 单据类型
     */
    @TableField(value = "order_type")
    private String orderType;

    /**
     * 物料编码
     */
    @TableField(value = "material_code")
    private String materialCode;

    /**
     * 表名
     */
    @TableField(value = "table_name")
    private String tableName;

    /**
     * 数据库来源
     */
    @TableField(value = "table_source")
    private String tableSource;

    /**
     * json数据
     */
    @TableField(value = "json_data")
    private String jsonData;

    /**
     * dfs版本号
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
