package com.yelink.assignment.entity.operation.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022-07-14
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class OperationOrderTaskListDTO {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 作业工单编号
     */
    private String operationNumber;

    /**
     * 生产工单编号
     */
    private String workOrderNum;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 状态id 1-创建、2-生效、3-投产、4-挂起、5-完成
     */
    private Integer state;

    /**
     * 状态名称 创建、生效、投产、挂起、完成
     */
    private String stateName;

    /**
     * 排产数量
     */
    private Double productCount;

    /**
     * 完成数量
     */
    private Double finishCount;

    /**
     * 进度
     */
    private Double progress;

    /**
     * 任务顺序
     */
    private Integer sequence;

    /**
     * 制造单元类型Id
     */
    private Integer modelId;

    /**
     * 产线实例Id
     */
    private Integer lineId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改人姓名
     */
    private String updateName;

    /**
     * 创建人账号
     */
    private String createBy;

    /**
     * 修改人账号
     */
    private String updateBy;

    /**
     * 工装（工装模具、模具编号）
     */
    private String tooling;

    /**
     * 报工系数
     */
    private Integer coefficient;

}
