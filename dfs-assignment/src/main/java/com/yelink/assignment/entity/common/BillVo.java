package com.yelink.assignment.entity.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 各个单据存在关联关系时，相同进行物料比较，返回相同物料的差值
 * @Date 2022/5/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BillVo {
    /**
     * 单据类型
     */
    private String billTypeCode;
    /**
     * 关联的单据号
     */
    private String relatedBillNumber;
    /**
     * 关联的单据类型
     */
    private String relatedBillTypeCode;
    /**
     * 物料对象
     */
    private List<BillMaterialVo> materialVos;
}
