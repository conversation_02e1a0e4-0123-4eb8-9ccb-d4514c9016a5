package com.yelink.assignment.entity.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-03-03 14:52
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DeviceEntity extends Model<DeviceEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    private Integer deviceId;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    @LogTag(name = "设备名称")
    private String deviceName;

    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private Integer cid;

    /**
     * 厂区id
     */
    @ApiModelProperty(value = "厂区id")
    private Integer aid;

    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Integer gid;

    /**
     * 工位id
     */
    @ApiModelProperty(value = "工位id")
    @LogTag(name = "工位", bean = "facilitiesServiceImpl", method = "getNameById")
    private Integer fid;

    /**
     * 生产线id
     */
    @ApiModelProperty(value = "生产线id")
    private Integer productionLineId;

    /**
     * 模型id
     */
    @ApiModelProperty(value = "模型id")
    @LogTag(name = "负责人", bean = "modelServiceImpl", method = "getModelNameById")
    private Integer modelId;

    /**
     * 类型1编码
     */
    @ApiModelProperty(value = "类型1编码")
    private Integer typeCode;

    /**
     * 类型1名称
     */
    @ApiModelProperty(value = "类型1名称")
    @LogTag(name = "设备类型")
    private String typeName;

    /**
     * 设备状态 0-停机 1-运行中 2-待机 3-故障
     */
    @ApiModelProperty(value = "设备状态 0-停机 1-运行中 2-待机 3-故障")
    private Integer state;

    /**
     * 纬度y
     */
    /*@TableField(value = "lat")
    private String lat;*/

    /**
     * 经度x
     */
    /*@TableField(value = "lng")
    private String lng;*/

    /**
     * 海拔z
     */
    /*@TableField(value = "alt")
    private String alt;*/

    /**
     * 设备地址
     */
    @ApiModelProperty(value = "设备地址")
    @LogTag(name = "设备地址")
    private String place;

    /**
     * 设备图片
     */
    @ApiModelProperty(value = "设备图片")
    private String img;

    /**
     * 模型图片
     */
    @ApiModelProperty(value = "模型图片")
    private String modelImg;

    /**
     * 仿真图
     */
    @ApiModelProperty(value = "仿真图")
    private String simulationImg;

    /**
     * 负责人员工id
     */
    @ApiModelProperty(value = "负责人员工id")
    private String magEmployeeId;

    /**
     * 管理人真实姓名
     */
    @ApiModelProperty(value = "管理人真实姓名")
    @LogTag(name = "管理人姓名")
    private String magNickname;

    /**
     * 管理人账号
     */
    @ApiModelProperty(value = "管理人账号")
    private String magName;

    /**
     * 管理人手机号码
     */
    @ApiModelProperty(value = "管理人手机号码")
    private String magPhone;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    private String remark;

    /**
     * 经纬度
     */
    @ApiModelProperty(value = "经纬度")
    private Object location;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 设备类型（暂时没用）
     */
    @ApiModelProperty(value = "排序")
    private String sort;

    /**
     * 生产数据指标id
     */
    @ApiModelProperty(value = "生产数据指标id")
    private String proTargetId;
    /**
     * 设备管理指标id
     */
    @ApiModelProperty(value = "设备管理指标id")
    private String admTargetId;

    /**
     * 状态修改时间
     */
    @ApiModelProperty(value = "状态修改时间")
    private Date stateUpdateTime;


    /**
     * 设备状态 0-停机 1-运行中 2-待机 3-故障
     */
    @ApiModelProperty(value = "设备状态名称")
    private String stateName;

    /**
     * 模型名称
     */
    @ApiModelProperty(value = "模型名称")
    private String modelName;

    /**
     * 告警描述
     */
    @ApiModelProperty(value = "告警描述")
    private String alarmDesc;

    /**
     * 自动告警发生时间
     */
    @ApiModelProperty(value = "自动告警发生时间")
    private Date alarmTime;

    /**
     * 状态持续时间/分钟
     */
    @ApiModelProperty(value = "状态持续时间/分钟")
    private String inStateTime;

    /**
     * 设备型号
     */
    @ApiModelProperty(value = "设备型号")
    @TableField(value = "sp_model")
    private String spModel;

    /**
     * 设备排序号
     *
     * @param
     * @return
     */
    @ApiModelProperty(value = "设备排序号")
    private Integer seq;

    @Override
    public Serializable pkVal() {
        return this.deviceId;
    }

    /**
     * 附件地址
     */
    @ApiModelProperty(value = "附件地址")
    private String fileUrl;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 关联的tagId
     */
    @ApiModelProperty(value = "关联的tagId")
    private Integer tagId;

    /**
     * 维修态
     */
    @ApiModelProperty(value = "维修态")
    private Boolean maintenanceState;

    /**
     * 设备传感器，逗号隔开
     */
    @ApiModelProperty(value = "设备传感器，逗号隔开")
    @TableField(exist = false)
    private String euis;

    /**
     * 计数指标名
     */
    @ApiModelProperty(value = "计数指标名")
    private String targetName;


    public class Update {
    }

}
