package com.yelink.assignment.entity.product;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @String 2021-04-14 14:20
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class MaterialEntity extends Model<MaterialEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 物料ID
     */
    private Integer id;

    /**
     * 状态（1-创建 2-启用 3-停用 4-废弃）
     */
    private Integer state;

    /**
     * 物料编码（根据生成规则生成或输入）
     */
    private String code;

    /**
     * 物料类型
     */
    private Integer type;

    /**
     * 物料类型名称
     */
    private String typeName;

    /**
     * 物料名称
     */
    private String name;

    /**
     * 物料英文名称
     */
    private String nameEnglish;

    /**
     * 物料规格
     */
    private String standard;

    /**
     * 是否为双单位 0-false 1-true
     */
    private Boolean isDoubleUnit;

    /**
     * 单位2
     */
    private String unit;
    /**
     * 换算系数
     */
    private Double scaleFactor;
    /**
     * 单位
     */
    private String comp;


    /**
     * 版本
     */
    private String version;

    /**
     * 分类（采购品-purchase  生产品-product）
     */
    private String sort;

    /**
     * 物料等级
     */
    private String level;

    /**
     * 分类名称
     */
    private String sortName;

    /**
     * 更新人
     */
    private String upStringBy;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private String upStringTime;

    /**
     * 创建时间
     */
    private String createTime;


    /**
     * 创建人姓名
     */
    private String createByNickname;

    /**
     * 修改人姓名
     */
    private String upStringByName;

    /**
     * 状态名称
     */
    private String stateName;

    /**
     * 附件URL
     */
    private String fileUrl;

    /**
     * 是否按批次管理(1-是  0-否)
     */
    private Boolean isBatchMag;

    /**
     * 出库规则(1-顺序出库-入库日期（默认） 0-无)
     */
    private Integer deliveryRules;
    /**
     * 计划开始生产时间
     */
    private String plannedDeliveryString;

    /**
     * 实际交货日期
     */
    private String deliveryString;

    /**
     * 审核人
     */
    private String approver;

    /**
     * 审核人名字
     */
    private String approverName;

    /**
     * 实际审批人
     */
    private String actualApprover;

    /**
     * 实际审批人名字
     */
    private String actualApproverName;

    /**
     * 审批状态
     */
    private Integer approvalStatus;

    /**
     * 审批状态名称
     */
    private String approvalStatusName;

    /**
     * 审批建议
     */
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    private String approvalTime;
    /**
     * 是否为辅料
     */
    private Boolean isAuxiliaryMaterial;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 图号
     */
    private String drawingNumber;
    /**
     * 该物料单价
     */
    private Double materialPrice;
    /**
     * 备注
     */
    private String remark;
    /**
     * 材质
     */
    private String rawMaterial;
    /**
     * 有无bom
     */
    private Boolean haveBom;
    /**
     * 有无工艺
     */
    private Boolean haveCraft;


    /**
     * 是否生产流水码管理
     */
    private Boolean isSupportCodeManage;

    /**
     * 工序Id
     */
    private Integer craftId;

    /**
     * 物料 当前库存数量
     */
    private Double stockQuantity;

    /**
     * 损耗率
     */
    private Double loseRate;


    /**
     * 最小生产批量
     */
    private Double minimumProductionLot;

    /**
     * 工厂型号
     */
    private String factoryModel;


    /**
     * 编制人
     */
    private String editor;

    /**
     * 编制人中文名
     */
    private String editorName;

    /**
     * bom编码
     */
    private String bomNum;

    /**
     * bom版本
     */
    private String bomVersion;

    /**
     * 采购收货小程序
     * 供应商物料编码
     */
    private String supplierMaterialCode;

    /**
     * 扩展字段1
     */
    private String customFieldOne;
    /**
     * 扩展字段2
     */
    private String customFieldTwo;
    /**
     * 扩展字段3
     */
    private String customFieldThree;
    /**
     * 扩展字段4
     */
    private String customFieldFour;
    /**
     * 扩展字段5
     */
    private String customFieldFive;
    /**
     * 扩展字段6
     */
    private String customFieldSix;
    /**
     * 扩展字段7
     */
    private String customFieldSeven;
    /**
     * 扩展字段8
     */
    private String customFieldEight;
    /**
     * 扩展字段9
     */
    private String customFieldNine;
    /**
     * 扩展字段10
     */
    private String customFieldTen;
    /**
     * 扩展字段11
     */
    private String customFieldEleven;

    /**
     * 包装方案编码
     */
    private String packageSchemeCode;

    /**
     * 是否已打印
     */
    private Boolean isPrint;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
