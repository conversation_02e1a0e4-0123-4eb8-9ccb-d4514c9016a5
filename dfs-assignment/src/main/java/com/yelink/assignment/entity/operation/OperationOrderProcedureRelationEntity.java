package com.yelink.assignment.entity.operation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022-04-27 12:04
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_operation_order_procedure_relation")
public class OperationOrderProcedureRelationEntity extends Model<OperationOrderProcedureRelationEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @ApiModelProperty("Id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 作业工单号
     */
    @ApiModelProperty("作业工单号")
    @TableField(value = "operation_order_number")
    private String operationOrderNumber;

    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    @TableField(value = "work_order_number")
    private String workOrderNumber;

    /**
     * 工序类型ID
     */
    @ApiModelProperty("工序类型ID")
    @TableField(value = "procedure_id")
    private Integer procedureId;

    /**
     * 工序类型名称
     */
    @ApiModelProperty("工序类型名称")
    @TableField(value = "procedure_name")
    private String procedureName;

    /**
     * 产线模型ID
     */
    @ApiModelProperty("产线模型ID")
    @TableField(value = "line_model_id")
    private Integer lineModelId;


    /**
     * 工艺工序ID
     */
    @ApiModelProperty("工艺工序ID")
    @TableField(value = "craft_procedure_id")
    private Integer craftProcedureId;

    /**
     * 工艺工序名称
     */
    @ApiModelProperty("工艺工序名称")
    @TableField(value = "craft_procedure_name")
    private String craftProcedureName;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
