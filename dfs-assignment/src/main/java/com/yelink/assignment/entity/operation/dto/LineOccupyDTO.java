package com.yelink.assignment.entity.operation.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25 19:40
 */

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class LineOccupyDTO {
    private String lineName;
    private List<String> operationOrderNumbers;
}
