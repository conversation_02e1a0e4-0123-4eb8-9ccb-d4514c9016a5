package com.yelink.assignment.entity.operation.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * <AUTHOR> @Date 2022/3/2 14:49
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class OperationOrderPageDTO {


    /**
     * 排产数量
     */
    private Double productCount;

    /**
     * 不良数量
     */
    private Double unqualified;

    /**
     * 完成数量
     */
    private Double finishCount;

    /**
     * 投入数量
     */
    private Double inputTotal;

    /**
     * 待产数量
     */
    private Double waitProductCount;

}
