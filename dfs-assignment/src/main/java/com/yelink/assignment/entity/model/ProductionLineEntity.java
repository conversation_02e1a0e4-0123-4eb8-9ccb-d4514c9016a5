package com.yelink.assignment.entity.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;


/**
 * <AUTHOR>
 * @Date 2020-12-03 11:15
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ProductionLineEntity {

    /**
     * 生产线id
     */
    private Integer productionLineId;

    /**
     * 生产线编号
     */
    private String productionLineCode;

    /**
     * 厂区id
     */
    private Integer aid;

    /**
     * 车间id
     */
    private Integer gid;

    /**
     * 默认设施id
     */
    private Integer fid;

    /**
     * 产线名称
     */
    private String name;

    /**
     * 位置
     */
    private String place;

    /**
     * 计划id，表示当前执行的计划
     */
    private Integer scheduleId;

    /**
     * 状态 1-生产中 2-暂停生产 3-闲置
     */
    private Integer state;

    /**
     * 负责人账户
     */
    private String magName;

    /**
     * 负责人真实姓名
     */
    private String magNickname;

    /**
     * 负责人电话
     */
    private String magPhone;

    /**
     * 是否有效
     */
    private Boolean active;

    /**
     * 产线类型ID
     */
    private Integer modelId;

    /**
     * 绑定的员工id
     */
    private String employeeId;
    /**
     * 出勤人数
     */
    private Integer attendance;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 厂区名称
     */
    public String gname;
}
