package com.yelink.assignment.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-03-26 18:33
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_report_line")
public class ReportLineEntity extends Model<ReportLineEntity> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @ApiModelProperty(value = "Id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * report-报工 auto-自动记录
     */
    @ApiModelProperty(value = "report-报工  auto-自动记录")
    @TableField(value = "type")
    private String type;

    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    @TableField(value = "line_id")
    private Integer lineId;

    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    @TableField(value = "line_name")
    private String lineName;

    /**
     * 作业工单号
     */
    @ApiModelProperty(value = "作业工单号")
    @TableField(value = "operation_number")
    private String operationNumber;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    @TableField(value = "finish_count")
    private Double finishCount;

    /**
     * 不合格数量
     */
    @ApiModelProperty(value = "不合格数量")
    @TableField(value = "unqualified")
    private Double unqualified;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 上报人姓名
     */
    @ApiModelProperty(value = "上报人姓名")
    @TableField(value = "user_nickname")
    private String userNickname;

    /**
     * 报工日期
     */
    @ApiModelProperty(value = "报工日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(value = "report_date")
    private Date reportDate;

    /**
     * 报工时间 + 时分秒
     */
    @ApiModelProperty(value = "报工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "report_time")
    private Date reportTime;

    /**
     * 记录显示采集器采集数量的时间点
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @TableField(value = "auto_count_record_time")
//    private Date autoCountRecordTime;

    /**
     * 采集器采集数量(上个采集时间点到当前时间)
     */
//    @TableField(value = "auto_count")
//    private Integer autoCount;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @TableField(value = "batch")
    private String batch;

    /**
     * 班次id
     */
    @ApiModelProperty(value = "班次id")
    @TableField(value = "shift_id")
    private Integer shiftId;

    /**
     * 班次
     */
    @ApiModelProperty(value = "班次")
    @TableField(value = "shift_type")
    private String shiftType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 有效工时/生产工时
     */
    @ApiModelProperty(value = "有效工时")
    @TableField(value = "effective_hours")
    private Double effectiveHours;

    /**
     * dfs_report_count表主键
     */
    @TableField(value = "report_count_id")
    private Integer reportCountId;

    /**
     * 计数类型 0-普通次数 1-小计 2-汇总
     */
    @ApiModelProperty(value = "计数类型 0-普通次数 1-小计 2-汇总")
    @TableField(exist = false)
    private Integer countType = 0;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    @TableField(exist = false)
    private String[] picUrls;

    /**
     * 不良描述
     */
    @ApiModelProperty(value = "不良描述")
    @TableField(value = "defect_desc")
    private String defectDesc;

    /**
     * （作业工单）操作员账号 多个逗号隔开
     */
    @ApiModelProperty(value = "操作员")
    @TableField(value = "operator")
    private String operator;

    /**
     * （作业工单）操作员姓名 多个逗号隔开
     */
    @ApiModelProperty(value = "操作员姓名")
    @TableField(value = "operator_name")
    private String operatorName;

    /**
     * 载具号
     */
    @ApiModelProperty(value = "载具号")
    @TableField(value = "vehicle_code")
    private String vehicleCode;

    /**
     * 班组id
     */
    @ApiModelProperty(value = "班组id")
    @TableField(value = "team_id")
    private Integer teamId;

    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    @TableField(value = "device_id")
    private Integer deviceId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @TableField(value = "customer_name")
    private String customerName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
