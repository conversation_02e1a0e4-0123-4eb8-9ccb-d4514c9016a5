package com.yelink.assignment.entity.operation.dto;

import com.asyncexcel.core.exporter.DataExportParam;
import com.yelink.dfscommon.dto.CommMaterialEntitySelectDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuSelectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-03-05
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class OperationOrderSelectDTO extends DataExportParam<OperationOrderSelectDTO> {

    /**
     * 作业工单id
     */
    @ApiModelProperty("作业工单id")
    private Integer id;

    /**
     * 作业工单编号
     */
    @ApiModelProperty("作业工单编号")
    private String operationNumber;

    /**
     * 生产工单编号
     */
    @ApiModelProperty("生产工单编号")
    private String workOrderNum;

    /**
     * 班次id
     */
    @ApiModelProperty("班次id")
    private Integer shiftId;

    /**
     * 班次类型
     */
    @ApiModelProperty("班次类型")
    private Integer shiftType;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 状态(多个逗号分割)
     */
    @ApiModelProperty("状态(多个逗号分割)")
    private String state;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    private Double planQuantity;

    /**
     * 不良数量
     */
    @ApiModelProperty("不良数量")
    private Double unqualified;

    /**
     * 完成数量
     */
    @ApiModelProperty("完成数量")
    private Double finishCount;

    /**
     * 待产数量
     */
    @ApiModelProperty("待产数量")
    private Double waitProductCount;

    /**
     * 制造单元名称
     */
    @ApiModelProperty("制造单元名称")
    private String lineName;

    /**
     * 制造单元id
     */
    @ApiModelProperty("制造单元id")
    private String lineId;

    /**
     * 制造单元类型名称
     */
    @ApiModelProperty("制造单元类型名称")
    private String modelName;
    /**
     * 制造单元类型Id
     */
    @ApiModelProperty("制造单元类型Id")
    private Integer modelId;

    /**
     * 工序Id
     */
    @ApiModelProperty("工序Id")
    private Integer procedureId;


    /**
     * 工艺工序id
     */
    @ApiModelProperty("工艺工序id")
    private Integer craftProcedureId;

    /**
     * 工序Id(多个逗号分割)
     */
    @ApiModelProperty("工序Id(多个逗号分割)")
    private String procedureIds;

    /**
     * 实际工时
     */
    @ApiModelProperty("实际工时")
    private Double actualWorkingHours;

    /**
     * 实际开始时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty("实际开始时间 yyyy-MM-dd HH:mm:ss")
    private String actualStartDate;
    /**
     * 实际开始时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty("实际开始时间 yyyy-MM-dd HH:mm:ss")
    private String actualEndDate;

    /**
     * 实际完成时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty("实际完成时间 yyyy-MM-dd HH:mm:ss")
    private String actualFinishStartDate;
    /**
     * 实际完成时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty("实际完成时间 yyyy-MM-dd HH:mm:ss")
    private String actualFinishEndDate;

    /**
     * 创建开始时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty("创建开始时间 yyyy-MM-dd HH:mm:ss")
    private String createStartTime;

    /**
     * 创建结束时间 yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty("创建结束时间 yyyy-MM-dd HH:mm:ss")
    private String createEndTime;

    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    private String workCenterId;

    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer current;
    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer size;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    private CommMaterialEntitySelectDTO materialFields;

    /**
     * 物料特征参数
     */
    @ApiModelProperty("物料特征参数")
    protected List<MaterialSkuSelectDTO> materialSkus;

    /**
     * 当前页最小的id
     */
    private Integer currentMinId;
}
