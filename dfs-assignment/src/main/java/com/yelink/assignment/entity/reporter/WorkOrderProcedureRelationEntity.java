package com.yelink.assignment.entity.reporter;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 工单工序关联表
 * <AUTHOR>
 * @date 2022-09-09
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderProcedureRelationEntity {
    /**
     * Id
     */
    private Integer id;

    /**
     * 工单号
     */
    private String workOrderNumber;

    /**
     * 工单Id
     */
    private Integer workOrderId;

    /**
     * 工序类型ID
     */
    private Integer procedureId;

    /**
     * 工序类型名称
     */
    private String procedureName;

    /**
     * 产线模型ID
     */
    private Integer lineModelId;


    /**
     * 工艺工序ID
     */
    private Integer craftProcedureId;

    /**
     * 工艺工序名称
     */
    private String craftProcedureName;

}

