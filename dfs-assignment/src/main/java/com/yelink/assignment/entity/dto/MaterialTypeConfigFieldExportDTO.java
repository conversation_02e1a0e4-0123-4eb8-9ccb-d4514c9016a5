package com.yelink.assignment.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.string.StringStringConverter;
import com.yelink.dfscommon.entity.dfs.MaterialEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/3/31 10:40
 */
@Data
public class MaterialTypeConfigFieldExportDTO {
    /**
     * 扩展字段1
     */
    @ExcelProperty(value = "扩展字段1", converter = StringStringConverter.class)
    private Object customFieldOne;
    /**
     * 扩展字段2
     */
    @ExcelProperty(value = "扩展字段2", converter = StringStringConverter.class)
    private Object customFieldTwo;
    /**
     * 扩展字段3
     */
    @ExcelProperty(value = "扩展字段3", converter = StringStringConverter.class)
    private Object customFieldThree;
    /**
     * 扩展字段4
     */
    @ExcelProperty(value = "扩展字段4", converter = StringStringConverter.class)
    private Object customFieldFour;
    /**
     * 扩展字段5
     */
    @ExcelProperty(value = "扩展字段5", converter = StringStringConverter.class)
    private Object customFieldFive;
    /**
     * 扩展字段6
     */
    @ExcelProperty(value = "扩展字段6", converter = StringStringConverter.class)
    private Object customFieldSix;
    /**
     * 扩展字段7
     */
    @ExcelProperty(value = "扩展字段7", converter = StringStringConverter.class)
    private Object customFieldSeven;
    /**
     * 扩展字段8
     */
    @ExcelProperty(value = "扩展字段8", converter = StringStringConverter.class)
    private Object customFieldEight;
    /**
     * 扩展字段9
     */
    @ExcelProperty(value = "扩展字段9", converter = StringStringConverter.class)
    private Object customFieldNine;
    /**
     * 扩展字段10
     */
    @ExcelProperty(value = "扩展字段10", converter = StringStringConverter.class)
    private Object customFieldTen;
    /**
     * 扩展字段11
     */
    @ExcelProperty(value = "扩展字段11", converter = StringStringConverter.class)
    private Object customFieldEleven;

    public static <T extends MaterialTypeConfigFieldExportDTO> void setMaterialFieldVale(T build, MaterialEntity material) {
        build.setCustomFieldOne(material.getCustomFieldOne());
        build.setCustomFieldTwo(material.getCustomFieldTwo());
        build.setCustomFieldThree(material.getCustomFieldThree());
        build.setCustomFieldFour(material.getCustomFieldFour());
        build.setCustomFieldFive(material.getCustomFieldFive());
        build.setCustomFieldSix(material.getCustomFieldSix());
        build.setCustomFieldSeven(material.getCustomFieldSeven());
        build.setCustomFieldEight(material.getCustomFieldEight());
        build.setCustomFieldNine(material.getCustomFieldNine());
        build.setCustomFieldTen(material.getCustomFieldTen());
        build.setCustomFieldEleven(material.getCustomFieldEleven());
    }
}
