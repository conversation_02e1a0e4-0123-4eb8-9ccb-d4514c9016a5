package com.yelink.assignment.entity.operation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yelink.dfscommon.annotation.LogTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 工单投产班组成员表
 *
 * <AUTHOR>
 * @Date 2021-07-20 19:08
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@LogTag(bean = "workOrderTeamServiceImpl", method = "getById", param = "id")
@TableName("dfs_operation_order_team")
public class OperationOrderTeamEntity extends Model<OperationOrderTeamEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @ApiModelProperty("Id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 作业工单号
     */
    @ApiModelProperty("作业工单号")
    @TableField(value = "operation_order_number")
    private String operationOrderNumber;

    /**
     * 成员真实姓名
     */
    @ApiModelProperty("成员真实姓名")
    @TableField(exist = false)
    private String memberNickName;

    /**
     * 成员用户ID
     */
    @ApiModelProperty("成员用户ID")
    @TableField(exist = false)
    private Integer memberId;

    /**
     * 成员账号
     */
    @ApiModelProperty("成员账号")
    @TableField(value = "member_name")
    @LogTag(name = "成员", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String memberName;

    /**
     * 班组角色
     */
    @ApiModelProperty("班组角色")
    @TableField(value = "team_role")
    private Integer teamRole;

    /**
     * 班组角色名称
     */
    @ApiModelProperty("班组角色名称")
    @TableField(exist = false)
    private String teamRoleName;

    /**
     * CreateBy
     */
    @ApiModelProperty("CreateBy")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * CreateDate
     */
    @ApiModelProperty("CreateDate")
    @TableField(value = "create_date")
    private Date createDate;

    /**
     * UpdateBy
     */
    @ApiModelProperty("UpdateBy")
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * UpdateDate
     */
    @ApiModelProperty("UpdateDate")
    @TableField(value = "update_date")
    private Date updateDate;

    /**
     * 岗位
     */
    @ApiModelProperty("岗位")
    @TableField(exist = false)
    private String postIds;

    /**
     * 岗位名称
     */
    @ApiModelProperty("岗位名称")
    @TableField(exist = false)
    private String postNames;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
