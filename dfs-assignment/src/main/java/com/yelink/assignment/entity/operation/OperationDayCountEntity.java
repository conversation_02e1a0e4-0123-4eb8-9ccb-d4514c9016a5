package com.yelink.assignment.entity.operation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022-03-17 11:59
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_operation_day_count")
public class OperationDayCountEntity extends Model<OperationDayCountEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 作业工单号
     */
    @TableField(value = "operation_number")
    private String operationNumber;

    /**
     * 完成数量
     */
    @TableField(value = "count")
    private Double count;

    /**
     * 不合格数量
     */
    @TableField(value = "unqualified")
    private Double unqualified;
    /**
     * 返修良品数
     */
    @TableField(value = "repair_qualified_number")
    private Double repairQualifiedNumber;
    /**
     * 产线id
     */
    @TableField(value = "line_id")
    private Integer lineId;
    /**
     * 物料编号
     */
    @TableField(value = "material_code")
    private String materialCode;

    /**
     * 时间
     */
    @TableField(value = "time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;


}
