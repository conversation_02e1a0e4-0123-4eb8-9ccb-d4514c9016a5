package com.yelink.assignment.entity.reporter.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.entity.dfs.MaterialEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 报工表VO
 *
 * <AUTHOR>
 * @date 2022-08-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReporterRecordVO {

    // 工单表
    /**
     * 生产工单编号
     */
    @ApiModelProperty("生产工单编号")
    private String workOrderNumber;
    /**
     * 工单状态
     */
    @ApiModelProperty("工单状态")
    private String state;

    /**
     * 作业工单状态
     */
    @ApiModelProperty("作业工单状态")
    private String assignmentState;
    /**
     * 物料编号
     */
    @ApiModelProperty("物料编号")
    private String materialCode;
    /**
     * 计划数量
     */
    @ApiModelProperty("计划数量")
    private Double planQuantity;
    /**
     * 工序名称
     */
    @ApiModelProperty("工序名称")
    private String craftName;

    /**
     * 产线名称
     */
    @ApiModelProperty("产线名称")
    private String lineName;
    /**
     * 计划开始时间
     */
    @ApiModelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;
    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    private Double inputTotal;
    /**
     * 已完成数量
     */
    @ApiModelProperty("已完成数量")
    private Double finishCount;
    /**
     * 不合格量
     */
    @ApiModelProperty("不合格量")
    private Double unqualified;
    /**
     * 计划工时
     */
    @ApiModelProperty("计划工时")
    private Double plannedWorkingHours;
    /**
     * 生产时长
     */
    @ApiModelProperty("生产时长")
    private Double actualWorkingHours;
    /**
     * 流转时长
     */
    @ApiModelProperty("流转时长")
    private Double circulationDuration;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNumber;
    /**
     * 销售订单号
     */
    @ApiModelProperty("销售订单号")
    private String saleOrderNumber;
    /**
     * 作业工单号 - 作业工单表
     */
    @ApiModelProperty("作业工单号 - 作业工单表")
    private String operationNumber;

    // 报工表
    /**
     * 制造单元模型名称
     */
    @ApiModelProperty("制造单元模型名称")
    private String lineModelName;
    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;
    /**
     * 上报方式
     */
    @ApiModelProperty("上报方式")
    private String type;
    /**
     * 批次
     */
    @ApiModelProperty("批次")
    private String batch;
    /**
     * 载具号
     */
    @ApiModelProperty("载具号")
    private String vehicleCode;
    /**
     * 班次名称
     */
    @ApiModelProperty("班次名称")
    private String shiftType;
    /**
     * 完成数量 - 对应报工表的finish_count字段
     */
    @ApiModelProperty("完成数量 - 对应报工表的finish_count字段")
    private Double finishCountTwo;
    /**
     * 不合格数量 - 对应报工表的unqualified字段
     */
    @ApiModelProperty("不合格数量 - 对应报工表的unqualified字段")
    private Double unqualifiedTwo;
    /**
     * 不良描述
     */
    @ApiModelProperty("不良描述")
    private String defectDesc;
    /**
     * 报工时间
     */
    @ApiModelProperty("报工时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportDate;
    /**
     * 操作员
     */
    @ApiModelProperty("操作员")
    private String operator;
    /**
     * 上报人
     */
    @ApiModelProperty("上报人")
    private String userNickname;
    /**
     * 上报时间
     */
    @ApiModelProperty("上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 物料表
    /**
     * 物料对象
     */
    @ApiModelProperty("物料对象")
    private MaterialEntity materialFields;


    /**
     * 上报工时
     */
    @ApiModelProperty("上报工时")
    private double effectiveHours;

    @ApiModelProperty("工作中心id")
    private Integer workCenterId;

    @ApiModelProperty("工作中心")
    private String workCenterName;

    @ApiModelProperty("工序 (多个','分隔)")
    private String procedureName;

    @ApiModelProperty("客户名称")
    private String customer;

    @ApiModelProperty("报工图片")
    private String[] picUrls;

    @ExcelProperty(value = "物料编码")
    private String code;

    @ExcelProperty(value = "单位")
    private String unit;

    @ExcelProperty(value = "图号")
    private String drawingNumber;

    @ExcelProperty(value = "物料规格")
    private String standard;

    @ExcelProperty(value = "材质")
    private String rawMaterial;
}

