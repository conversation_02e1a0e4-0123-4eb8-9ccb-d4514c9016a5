package com.yelink.assignment.common;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * @Description: 业务相关配置
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/23
 */
@Slf4j
@Component
@Getter
public class WorkProperties {

    @Value("${assignment.tableSchema}")
    private String tableSchema;


}
