package com.yelink.assignment.common;

/**
 * @Description: 工单状态枚举
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum WorkOrderStateEnum {

    /**
     * 状态编码及描述
     * 1-创建、8-审核、9-批准、2-生效、3-投产、4-挂起、5-完成、6-关闭、7-取消
     */
    CREATED(1, "创建"),
    //TO_EXAMINE(8, "已审核"),
    //APPROVAL(9, "已批准"),
    RELEASED(2, "生效"),
    INVESTMENT(3, "投产"),
    HANG_UP(4, "挂起"),
    FINISHED(5, "完成"),
    CLOSED(6, "关闭"),
    CANCELED(7, "取消");


    private Integer code;
    private String name;

    WorkOrderStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WorkOrderStateEnum workOrderStateEnum : WorkOrderStateEnum.values()) {
            if (workOrderStateEnum.code.equals(code)) {
                return workOrderStateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        if(name == null){
            return null;
        }
        for (WorkOrderStateEnum workOrderStateEnum : WorkOrderStateEnum.values()) {
            if (name.equals(workOrderStateEnum.name)) {
                return workOrderStateEnum.code;
            }
        }
        return null;
    }
}

