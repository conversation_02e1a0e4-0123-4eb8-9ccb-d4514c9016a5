package com.yelink.assignment.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 类型
 * @time 2021/5/24 10:46
 */
public enum ReportType {
    /**
     * 类型
     */
    INVESTMENT("investment", "投入"),
    REPORT("report", "报工"),
    AUTO("auto", "自动记录"),
    HANG_UP("hangUp", "挂起"),
    FINISHED("finished", "完工");

    private String type;
    private String name;
    public static List<String> activeTypeList;

    static {
        activeTypeList = new ArrayList<>();
        activeTypeList.add(INVESTMENT.getType());
        activeTypeList.add(REPORT.getType());
    }

    ReportType(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getNameByCode(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (ReportType reportType : ReportType.values()) {
            if (type.equals(reportType.type)) {
                return reportType.getName();
            }
        }
        return null;
    }
}

