package com.yelink.assignment.constant;

/**
 * 优先级类型枚举
 *
 * <AUTHOR>
 * @Date 2022/07/13 11:46
 */
public enum PriorityTypeEnum {
    /**
     * 优先等级
     */
    NORMAL(1, "正常"),
    FIRST(2, "优先"),
    URGENT(3, "加急"),
    EXTRA_URGENT(4, "特急");

    private Integer code;
    private String name;


    PriorityTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {

        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PriorityTypeEnum priorityTypeEnum : PriorityTypeEnum.values()) {
            if (code.intValue() == priorityTypeEnum.code.intValue()) {
                return priorityTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (PriorityTypeEnum priorityTypeEnum : PriorityTypeEnum.values()) {
            if (name.equals(priorityTypeEnum.name)) {
                return priorityTypeEnum.code;
            }
        }
        return null;
    }


}
