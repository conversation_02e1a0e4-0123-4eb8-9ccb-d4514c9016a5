package com.yelink.assignment.constant;


/**
 * @Description: 条码规则状态枚举
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum BarCodeStateEnum {

    /**
     * 状态编码及描述
     * 0-创建 1-发放 2-完成 3-关闭 4-取消
     */
    AVAILABLE(0, "可以使用"),
    OCCUPIED(1, "已被使用"),
    FINISH(2,"已出库完"),
    STOCKED(3,"已上架"),
    NOT_AVAILABLE(4,"不可使用"),
    ;

    private int code;
    private String name;

    BarCodeStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BarCodeStateEnum stateEnum : BarCodeStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (BarCodeStateEnum stateEnum : BarCodeStateEnum.values()) {
            if (name.equals(stateEnum.name)) {
                return stateEnum.code;
            }
        }
        return null;
    }
}
