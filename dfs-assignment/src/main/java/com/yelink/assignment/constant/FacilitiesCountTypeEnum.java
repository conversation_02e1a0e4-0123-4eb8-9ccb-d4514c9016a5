package com.yelink.assignment.constant;


/**
 * @Description: 工位计数类型
 * @Author: zhen<PERSON><PERSON>
 * @Date: 2020/12/5
 */
public enum FacilitiesCountTypeEnum {

    /**
     * 设施编码及描述
     * 生产设施类型
     */
    NONE(0, "否"),
//    QUALIFIED(1, "采集合格品"),
//    CHECKED(2, "采集已检查数"),
    COLLECTOR_MULTIPLIED_ONE(1, "采集器 * 1"),
    COLLECTOR_MULTIPLIED_PACKAGE_RATIO(2, "采集器 * 包装系数"),
    ;

    /**
     * 接口名称定义
     */
    private int code;
    private String name;

    FacilitiesCountTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static FacilitiesCountTypeEnum getByCode(int code) {
        for (FacilitiesCountTypeEnum facilitiesCountTypeEnum : FacilitiesCountTypeEnum.values()) {
            if (code == facilitiesCountTypeEnum.code) {
                return facilitiesCountTypeEnum;
            }
        }
        return null;
    }


}
