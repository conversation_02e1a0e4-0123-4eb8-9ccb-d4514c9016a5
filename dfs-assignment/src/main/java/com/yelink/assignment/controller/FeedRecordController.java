package com.yelink.assignment.controller;

import com.yelink.assignment.entity.feed.FeedRecordEntity;
import com.yelink.assignment.service.FeedRecordService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.yelink.dfscommon.pojo.ResponseData.success;

/**
 * <AUTHOR>
 * @Description 上料记录
 * @Date 2022/3/29 18:02
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/feed/record")
public class FeedRecordController {

    private FeedRecordService feedRecordService;

    /**
     * 保存移动端上料记录提交的数据
     *
     * @return
     */
    @PostMapping("/save")
    public ResponseData saveRecord(@RequestBody FeedRecordEntity feedRecordEntity) {
        feedRecordService.saveFeedRecord(feedRecordEntity);
        return success();
    }

    /**
     * 根据作业工单号，查询对应的上料记录
     *
     * @param
     * @return
     */
    @GetMapping("/list")
    public ResponseData getRecordList(@RequestParam(value = "operationOrderNum") String operationOrderNum) {
        return success(feedRecordService.getRecordList(operationOrderNum));
    }

    /**
     * 删除作业工单的上料记录
     *
     * @param
     * @return
     */
    @DeleteMapping("/remove")
    public ResponseData removeRecord(@RequestParam(value = "feedRecordId") Integer feedRecordId) {
        feedRecordService.removeRecord(feedRecordId);
        return success();
    }

}
