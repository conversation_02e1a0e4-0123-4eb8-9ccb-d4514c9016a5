package com.yelink.assignment.controller.reporter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.assignment.entity.reporter.excel.ReporterRecordExcelVO;
import com.yelink.assignment.entity.reporter.vo.ReporterRecordVO;
import com.yelink.assignment.service.ReportLineService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.dto.ReporterRecordDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static com.yelink.dfscommon.pojo.ResponseData.success;
/**
 * 作业工单报工记录的查询与导出
 * <AUTHOR>
 * @date 2022-08-29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/reporter")
public class ReporterRecordController {


    private ReportLineService reportLineService;


    /**
     * 作业工单的报工记录查询
     * @param dto 报工记录-请求DTO
     * @return
     */
    @PostMapping("/get/list")
    public ResponseData getList(@RequestBody ReporterRecordDTO dto) {
        Page<ReporterRecordVO> result = reportLineService.getReporterRecord(dto);
        return success(result);
    }

    /**
     * 获取作业工单的产线数量报工表的所有上报方式
     *
     * @return
     */
    @GetMapping("/all/type")
    public ResponseData getAllType() {
        return success(reportLineService.getAllReportType());
    }

    /**
     * 下载导出默认模板
     * @param response
     * @throws IOException
     */
    @RequestMapping("/down/export/default/template")
    public void downloadDefaultExportTemplate(HttpServletResponse response) throws IOException {
        byte[] bytes = reportLineService.downloadDefaultExportTemplate("classpath:template/reportRecordTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response,bytes,"作业工单报工记录默认导出模板" + Constant.XLSX);
    }

    /**
     * 上传自定义导出模板
     */
    @PostMapping("/upload/export/template")
    public ResponseData uploadReportRecordTemplate(MultipartFile file) {
        reportLineService.uploadCustomExportTemplate(file,null);
        return success();
    }

    /**
     * 作业工单报工记录自定义导出模板
     * @param response
     * @throws IOException
     */
    @RequestMapping("/down/export/custom/template")
    public void downloadExportTemplate(HttpServletResponse response) throws IOException {
        byte[] bytes = reportLineService.downloadCustomExportTemplate(null);
        ExcelTemplateImportUtil.responseToClient(response,bytes,"作业工单报工记录自定义导出模板" + Constant.XLSX);
    }

    /**
     * 作业工单的报工记录导出
     * @param dto 导出条件
     * @param response
     */
    @PostMapping("/export")
    public void getDailyExport(@RequestBody ReporterRecordDTO dto,HttpServletResponse response) throws IOException {
        dto.setCurrent(1);
        dto.setSize(Integer.MAX_VALUE);
        List<ReporterRecordExcelVO> data = reportLineService.listBy(dto);
        byte[] bytes = reportLineService.getExportTemplate();
        EasyExcelUtil.writeToExcelTemplate(response,data,"报工记录-原始数据","作业工单报工记录.xlsx",bytes,ReporterRecordExcelVO.class);
    }


}

