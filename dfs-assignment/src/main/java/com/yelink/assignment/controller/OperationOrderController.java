package com.yelink.assignment.controller;

import com.alibaba.excel.annotation.ExcelProperty;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.constant.PriorityTypeEnum;
import com.yelink.assignment.entity.common.CommonState;
import com.yelink.assignment.entity.dto.OperationOrderExportDTO;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderProcedureRelationEntity;
import com.yelink.assignment.entity.operation.dto.OperationOrderSelectDTO;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.entity.report.dto.OperationOrderReportDTO;
import com.yelink.assignment.mapper.UnqualifiedImgMapper;
import com.yelink.assignment.request.RequestService;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.ReportCountService;
import com.yelink.assignment.service.ReportLineService;
import com.yelink.assignment.service.impl.AssignmentOrderExportHandler;
import com.yelink.dfscommon.api.dfs.MaterialTypeConfigFieldInterface;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.assignment.OperationStateEnum;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yelink.dfscommon.pojo.ResponseData.fail;
import static com.yelink.dfscommon.pojo.ResponseData.success;


/**
 * 作业工单
 *
 * <AUTHOR>
 * @Date 2022/03/03
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/operation/order")
public class OperationOrderController {
    private OperationService operationService;
    private ReportCountService reportCountService;
    private ReportLineService reportLineService;
    private UnqualifiedImgMapper unqualifiedImgMapper;
    private MaterialTypeConfigFieldInterface materialTypeConfigFieldInterface;
    private RequestService requestService;
    private final ExcelService excelService;

    /**
     * 查询作业工单列表
     *
     * @param operationOrderDTO
     * @return
     */
    @PostMapping("/list")
    public ResponseData getOperationOrderList(@RequestBody OperationOrderSelectDTO operationOrderDTO) {
        Page<OperationOrderEntity> page = operationService.getOperationOrderList(operationOrderDTO);
        return success(page);
    }

    /**
     * 根据条件导出作业工单
     */
    @Deprecated
    @PostMapping("/export")
    public void exportOperationOrders(@RequestBody OperationOrderSelectDTO operationOrderDTO, HttpServletResponse response) throws IOException {
        operationOrderDTO.setCurrent(1);
        operationOrderDTO.setSize(Integer.MAX_VALUE);
        Page<OperationOrderEntity> page = operationService.getOperationOrderList(operationOrderDTO);
        List<OperationOrderEntity> operationOrders = page.getRecords();

        // 根据物料类型配置字段 , 获取没有启用的物料字段
        List<String> notEnableFields = new ArrayList<>();
        ResponseData responseData = materialTypeConfigFieldInterface.getNoEnableMaterialFields();
        if (responseData != null && responseData.getCode().equals(ResponseData.SUCCESS_CODE)) {
            notEnableFields = JacksonUtil.getResponseArray(responseData, String.class);
        }
        // 反射获取该类需要打印的字段
        List<Field> fields = Arrays.stream(OperationOrderExportDTO.class.getDeclaredFields())
                .filter(field -> !Objects.isNull(field.getAnnotation(ExcelProperty.class)))
                .collect(Collectors.toList());
        // 过滤没有启用的物料字段
        List<String> finalNotEnableFields = notEnableFields;
        Set<String> usingFields = fields.stream().map(Field::getName).filter(name -> !finalNotEnableFields.contains(name)).collect(Collectors.toSet());

        // 封装成 OperationOrderExportDTO
        List<OperationOrderExportDTO> operationOrderExports = new ArrayList<>();
        operationOrders.forEach(record -> operationOrderExports.add(OperationOrderExportDTO.convertOperationOrderExportDTO(record)));

        EasyExcelUtil.export(response, usingFields, "作业工单", "作业工单", operationOrderExports, OperationOrderExportDTO.class);
    }

    /**
     * 作业工单异步导出
     *
     * @param operationOrderDTO
     * @return
     */
    @PostMapping("/syc/export")
    public ResponseData exportExcel(@RequestBody OperationOrderSelectDTO operationOrderDTO) {
        DataExportParam<OperationOrderSelectDTO> dataExportParam = operationOrderDTO
                .setExportFileName(BusinessCodeEnum.ASSIGNMENT_ORDER_RECORD.getCodeName())
                .setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.ASSIGNMENT_ORDER_RECORD.name());
        dataExportParam.setCreateUserCode(requestService.getUsername());
        Long result = excelService.doExport(dataExportParam, AssignmentOrderExportHandler.class);
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData exportExcel(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                    @RequestParam(required = false, defaultValue = "1") Integer currentPage) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.ASSIGNMENT_ORDER_RECORD.name());
        return success(excelService.listPage(excelTask, currentPage, pageSize));
    }


    /**
     * 查询 导入进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.ASSIGNMENT_ORDER_RECORD.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.info("任务id错误");
            return fail();
        }
        return success(records.get(0));
    }

    /**
     * 根据生产工单 查询作业工单列表
     *
     * @param workOrderNumber 工单号
     * @return
     */
    @GetMapping("/detail")
    public ResponseData getOperationOrder(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        List<OperationOrderEntity> list = operationService.getByWorkOrder(workOrderNumber);
        return success(list);
    }

    /**
     * 查询作业工单详情
     *
     * @param operationNumber 作业工单号
     * @return
     */
    @GetMapping("/detail/by/number")
    public ResponseData getOperationOrderDetail(@RequestParam(value = "operationNumber") String operationNumber) {
        return success(operationService.getByOperationNumber(operationNumber));
    }

    /**
     * 新增作业工单
     *
     * @param operationOrderEntity
     * @param bindingResult
     * @return
     */
    @PostMapping("/add")
    public ResponseData insertOperationOrder(@RequestBody @Validated({OperationOrderEntity.Insert.class}) OperationOrderEntity operationOrderEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        return success(operationService.insertOperationOrder(operationOrderEntity));
    }

    /**
     * 编辑作业工单
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/update")
    public ResponseData edit(@RequestBody @Validated({OperationOrderEntity.Update.class}) OperationOrderEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        OperationOrderEntity edit = operationService.edit(entity);
        return success(edit);
    }

    /**
     * 删除作业工单
     *
     * @param operationNumber 作业工单编号
     * @return
     */
    @DeleteMapping("/delete")
    public ResponseData delete(@RequestParam(value = "operationNumber") String operationNumber) {
        operationService.delete(operationNumber);
        return success();
    }


    /**
     * 报工前显示采集器数量
     *
     * @param operationNumber
     * @return
     */
    @GetMapping("/input/auto/count")
    public ResponseData getAutoCount(@RequestParam(value = "operationNumber") String operationNumber) {
        return success(reportCountService.getAutoCountAndDate(operationNumber));
    }

    /**
     * 投产（开工）、挂起、完成接口
     *
     * @param workOrder
     * @param action    0-投产  1-完成 2-挂起
     * @return
     */
    @PostMapping("/input/report")
    public ResponseData reportAction(@RequestParam(value = "workOrder") String workOrder,
                                     @RequestParam(value = "action") Integer action) {
        reportCountService.dealReportCountRecord(workOrder, action, Constant.MANUAL);
        return success();
    }


    /**
     * 批量更改作业工单状态
     *
     * @param operationOrderReportDTO
     * @return
     */
    @PostMapping("/batch/update/state")
    public ResponseData hangOccupiedWorkOrder(@RequestBody OperationOrderReportDTO operationOrderReportDTO) {
        for (String operationOrderNumber : operationOrderReportDTO.getOperationOrderNumbers()) {
            reportCountService.dealReportCountRecord(operationOrderNumber, operationOrderReportDTO.getAction(), Constant.MANUAL);
        }
        return success();
    }


    /**
     * 作业工单立即开工 （派工 + 投产）
     */
    @PostMapping("/create/and/report")
    public ResponseData creatAndReport(@RequestBody @Validated({OperationOrderEntity.Insert.class}) OperationOrderEntity operationOrderEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        operationService.insertOperationOrder(operationOrderEntity);
        if (StringUtils.isBlank(operationOrderEntity.getOperationNumber())) {
            throw new ResponseException("作业工单新增失败");
        }
        reportCountService.dealReportCountRecord(operationOrderEntity.getOperationNumber(), 0, Constant.MANUAL);
        return success();
    }


    /**
     * 工单完成前状态校验
     * 工单下所有作业工单都为完成状态才可以完成
     *
     * @param workOrder 工单号
     * @return
     */
    @GetMapping("/check/finish/state")
    public ResponseData checkFinishState(@RequestParam(value = "workOrder") String workOrder) {
        return success(reportCountService.checkFinishState(workOrder));
    }

    /**
     * 作业工单完工（报工 + 完成）
     */
    @PostMapping("/report/finish")
    public ResponseData reportFinish(@RequestBody ReportLineEntity entity) {
        // 根据最后完工数 判断是否需要新增一条报工记录
        Double historyCount = reportCountService.getHistoryFinishCount(entity.getOperationNumber());
        double thisCount = entity.getFinishCount() - historyCount;
        if (thisCount < 0) {
            throw new ResponseException("完工数小于已完成数");
        }
        if (thisCount > 0) {
            // 新增一条报工记录
            entity.setFinishCount(thisCount);
            entity.setUserName(requestService.getUsername());
            reportCountService.countOutput(entity);
        }
        // 报工完成
        reportCountService.dealReportCountRecord(entity.getOperationNumber(), 1, Constant.MANUAL);
        return success();
    }


    /**
     * 获取完工数量和已报工数
     *
     * @param operationNumber 作业工单号
     * @return
     */
    @GetMapping("/finish/report/count")
    public ResponseData getFinishAndReportCount(@RequestParam(value = "operationNumber") String operationNumber) {
        return success(reportCountService.getFinishAndReportCount(operationNumber));
    }

    /**
     * 投产前确认接口（判断制造单元是否占用 true-占用 false-未占用）
     *
     * @param lineId 作业工单号
     * @return
     */
    @GetMapping("/confirm/input/report")
    public ResponseData confirmReportAction(@RequestParam(value = "lineId") Integer lineId) {
        return success(reportCountService.confirmReportAction(lineId));
    }

    /**
     * 报工--输入工单产量
     *
     * @param entity 工单产量
     * @return
     */
    @PostMapping("/input/count")
    public ResponseData countOutput(@RequestBody ReportLineEntity entity) {
        String username = requestService.getUsername();
        if (StringUtils.isNotBlank(username)) {
            entity.setUserName(username);
        }
        reportCountService.countOutput(entity);
        return success(entity);
    }

    /**
     * 工位看板--修改报工数量
     *
     * @param entity
     * @return
     */
    @PutMapping("/input/update/count")
    public ResponseData updateCount(@RequestBody ReportLineEntity entity) {
        reportCountService.updateCount(entity);
        return success(reportCountService.getById(entity.getReportCountId()));
    }

    /**
     * 获取作业工单状态列表
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getState() {
        ArrayList<CommonState> list = new ArrayList<>();
        OperationStateEnum[] values = OperationStateEnum.values();
        for (OperationStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }


    /**
     * 查询报工记录
     *
     * @param operationNumber
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/history/count")
    public ResponseData getInputHistory(@RequestParam(value = "operationNumber") String operationNumber,
                                        @RequestParam(value = "type", required = false) String type,
                                        @RequestParam(value = "current", required = false) Integer current,
                                        @RequestParam(value = "size", required = false) Integer size) {
        return success(operationService.getInputHistory(operationNumber, type, current, size));
    }

    /**
     * 查询报工记录详情
     *
     * @param id
     * @return
     */
    @GetMapping("/history/count/detail")
    public ResponseData getInputHistoryDetail(@RequestParam(value = "id") Integer id) {
        ReportLineEntity entity = reportLineService.getById(id);
        if (entity != null) {
            List<String> urlList = unqualifiedImgMapper.selectUrlList(entity.getId());
            entity.setPicUrls(urlList.toArray(new String[urlList.size()]));
        }
        return success(entity);
    }

    /**
     * 更新报工记录
     * entity
     *
     * @return
     */
    @PutMapping("/update/history/count")
    public ResponseData updateInputHistory(@RequestBody ReportLineEntity entity) {
        return success(operationService.updateInputHistory(entity));
    }

    /**
     * 新增报工记录
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/add/history/count")
    public ResponseData addHistoryCount(@RequestBody @Validated({OperationOrderEntity.Insert.class}) ReportLineEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        return success(operationService.addHistoryCount(entity));
    }


    /**
     * 插入作业工单工序关联关系
     *
     * @param entities
     * @return
     */
    @PostMapping("/add/procedure/relation")
    public ResponseData addOperationProcedureRelation(@RequestBody List<OperationOrderProcedureRelationEntity> entities) {
        operationService.addOperationProcedureRelation(entities);
        return success();
    }

    /**
     * 获取作业工单排产数总和
     *
     * @param workOrderNumber
     * @return
     */
    @GetMapping("/plan/quantity/sum")
    public ResponseData getPlanQuantitySum(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        return success(operationService.getPlanQuantitySum(workOrderNumber));
    }

    /**
     * 作业工单新增前的判断：
     * 1、生产工单关联的作业工单的数量之和(包括当前的作业工单) > 生产工单的计划数量，给前端提示
     *
     * @param entity 作业工单
     * @return
     */
    @PostMapping("/insert/judge")
    public ResponseData judgeBeforeInsert(@RequestBody OperationOrderEntity entity) {
        return success(operationService.judgeBeforeInsert(entity));
    }

    /**
     * 获取优先级枚举列表
     *
     * @return
     */
    @GetMapping("/enum/list")
    public ResponseData getEnumList() {
        List<CommonState> list = new ArrayList<>();
        PriorityTypeEnum[] values = PriorityTypeEnum.values();
        for (PriorityTypeEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 获取状态枚举列表
     * 只要 投产、生效、挂起、完成
     *
     * @return
     */
    @GetMapping("/filter/state/list")
    public ResponseData getStateEnumList() {
        List<CommonState> list = new ArrayList<>();
        OperationStateEnum[] values = OperationStateEnum.values();
        for (OperationStateEnum value : values) {
            CommonState type = new CommonState();
            int code = value.getCode();
            type.setCode(code);
            type.setName(value.getName());
            if (code == 2 || code == 3 || code == 4 || code == 5) {
                list.add(type);
            }
        }
        return success(list);
    }

    /**
     * 工单状态批量编辑
     */
    @PostMapping("/batch/update/states")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        if (batchApprovalDTO.getIds() == null) {
            return fail("编辑的单据id为空");
        }
        String username = requestService.getUsername();
        Boolean ret = operationService.batchUpdateState(batchApprovalDTO, username);
        return ResponseData.success(ret);
    }

}
