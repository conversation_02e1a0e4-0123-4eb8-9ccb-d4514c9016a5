package com.yelink.assignment.provider.constant;

import com.yelink.dfscommon.provider.constant.KafkaMessageTypeInterface;

/**
 * @description: kafka推送消息类型
 * @author: shuang
 * @time: 2022/2/16
 */
public enum KafkaMessageTypeEnum implements KafkaMessageTypeInterface {


    /**
     * 作业工单
     */
    OPERATION_ORDER_AUTO_COMPLETE_MESSAGE("operationOrder", "autoComplete", "作业工单自动完工消息"),
    OPERATION_ORDER_STATUS_CHANGE_MESSAGE("operationOrder", "statusChange", "作业工单状态改变消息"),
    OPERATION_ORDER_UPDATE_MESSAGE("operationOrder", "update", "作业工单编辑消息"),
    OPERATION_ORDER_ADD_MESSAGE("operationOrder", "add", "作业工单新增消息"),
    OPERATION_ORDER_DELETE_MESSAGE("operationOrder", "delete", "作业工单删除消息"),
    /**
     * 作业工单报工事件
     */
    OPERATION_ORDER_REPORT("operationOrder", "report", "新增报工事件"),

    ;
    private String modelCode;

    private String typeCode;

    private String des;

    KafkaMessageTypeEnum(String modelCode, String typeCode, String des) {
        this.modelCode = modelCode;
        this.typeCode = typeCode;
        this.des = des;
    }


    @Override
    public String getModelCode() {
        return modelCode;
    }

    @Override
    public String getTypeCode() {
        return typeCode;
    }

    @Override
    public String getDes() {
        return des;
    }
}
