package com.yelink.assignment.config;

import com.asyncexcel.springboot.SpringExcelContext;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Lazy
@Configuration
public class ExcelMybatisPlusConfig {


    @Resource
    private SpringExcelContext context;


    /**
     * 提前加载子容器对象并设置分页拦截
     */
    @PostConstruct
    public void initExcelMybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        SqlSessionFactory sqlSessionFactory = context.getInstance(SqlSessionFactory.class);
        sqlSessionFactory.getConfiguration().addInterceptor(interceptor);
    }


}
