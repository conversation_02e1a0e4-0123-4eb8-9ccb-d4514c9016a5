package com.yelink.assignment.config;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.ResponseData;
import feign.FeignException;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Response;
import feign.Util;
import feign.codec.Decoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @description: feign配置
 * @time 2021/7/8 21:22
 */

@Slf4j
@RequiredArgsConstructor
public class FeignConfig implements RequestInterceptor, Decoder {
    private final ObjectMapper objectMapper;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            //添加token
            String authorization = request.getHeader("Authorization");
            if(StringUtils.isNotBlank(authorization)){
                requestTemplate.header("Authorization", authorization);
            }
        }
    }


    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        if (response.status() == 404 || response.status() == 204) {
            log.error("服务请求错误",response.status());
            return ResponseData.fail();
        }
        String result = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
        ResponseData baseResponse = objectMapper.readValue(result, ResponseData.class);
        String code = baseResponse.getCode();
        log.debug("feign请求返回数据:{}", JSONObject.toJSONString(baseResponse));
        if (ResponseData.IOT_SUCCESS_CODE.equals(code) || ResponseData.SUCCESS_CODE.equals(code)) {
            return baseResponse;
        } else {

            throw new ResponseException(baseResponse);
        }
    }
}
