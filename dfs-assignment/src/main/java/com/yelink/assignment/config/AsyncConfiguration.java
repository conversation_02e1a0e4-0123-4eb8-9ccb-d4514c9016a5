package com.yelink.assignment.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncExecutionAspectSupport;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Date 2021/11/23 15:59
 */
@Slf4j
@Configuration
public class AsyncConfiguration implements AsyncConfigurer {

    @Bean(name = AsyncExecutionAspectSupport.DEFAULT_TASK_EXECUTOR_BEAN_NAME)
    public ThreadPoolTaskExecutor executor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int corePoolSize = 10;
        //核心线程数
        executor.setCorePoolSize(corePoolSize);
        int maxPoolSize = 50;
        //最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        int queueCapacity = 10;
        //队列长度
        executor.setQueueCapacity(queueCapacity);
        //线程拒绝策略:将任务返还给调用者线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        String threadNamePrefix = "dfsTaskExecutor-";
        //线程名称
        executor.setThreadNamePrefix(threadNamePrefix);
        // 1、告诉线程池，在销毁之前执行shutdown方法
        // 2、设置线程池中任务的等待时间，如果超过这个时候还没有销毁就强制销毁，以确保应用最后能够被关闭，而不是阻塞住
        executor.setWaitForTasksToCompleteOnShutdown(true);
        int awaitTerminationSeconds = 60;
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.initialize();
        return executor;
    }
    @Override
    public Executor getAsyncExecutor() {
        return executor();
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> log.error("执行异步任务{}", method,ex);
    }





}
