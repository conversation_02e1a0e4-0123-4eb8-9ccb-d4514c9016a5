package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.record.InputRecordEntity;

/**
 * <AUTHOR>
 * @Date 2021/10/22 19:02
 */
public interface InputRecordService extends IService<InputRecordEntity> {

    /**
     * 获取产线最后一条记录
     *
     * @param lineId
     * @return
     */
    InputRecordEntity getLastRecordByLineId(Integer lineId);

    /**
     * 更新投入数表，添加投入量记录
     *
     * @param entity
     */
    void updateInputRecord(OperationOrderEntity entity);
}
