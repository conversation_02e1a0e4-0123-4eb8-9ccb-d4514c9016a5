package com.yelink.assignment.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yelink.assignment.entity.dto.OperationOrderExportDTO;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.dto.OperationOrderSelectDTO;
import com.yelink.assignment.service.OperationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 流水码记录导出:导出数据最大不能超过100万行
 * @author: shuang
 * @time: 2023/7/3
 */
@Slf4j
@ExcelHandle
@RequiredArgsConstructor
public class AssignmentOrderExportHandler implements ExportHandler<OperationOrderExportDTO> {


    private final OperationService operationService;


    /**
     * 导出数据最大不能超过100万行
     */
    public static final Long EXPORT_MAX_ROWS = 1000000L;


    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
        WriteSheet sheet = EasyExcelFactory.writerSheet(0, "数据列表").head(OperationOrderExportDTO.class).build();
        context.setWriteSheet(sheet);
    }

    @Override
    public ExportPage<OperationOrderExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        IPage<OperationOrderEntity> page = operationService.getOperationOrderList((OperationOrderSelectDTO) param);
        List<OperationOrderExportDTO> list = page.getRecords().stream().map((OperationOrderEntity operationOrderEntity) -> {
            OperationOrderExportDTO operationOrderExportDTO = OperationOrderExportDTO.builder().build();
            BeanUtils.copyProperties(operationOrderEntity, operationOrderExportDTO);
            return operationOrderExportDTO;
        }).collect(Collectors.toList());
        ExportPage<OperationOrderExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(list);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<OperationOrderExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
