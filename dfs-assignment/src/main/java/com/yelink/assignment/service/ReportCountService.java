package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.dto.LineOccupyDTO;
import com.yelink.assignment.entity.report.ReportCountEntity;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.entity.report.dto.AutoCountDTO;
import com.yelink.assignment.entity.report.dto.FinishAndReportCountDTO;

import java.util.Date;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-26 18:33
 */
public interface ReportCountService extends IService<ReportCountEntity> {

    /**
     * 工位看板--输入工单产量前显示采集器数量
     *
     * @param workOrderNumber
     * @return
     */
    AutoCountDTO getAutoCountAndDate(String workOrderNumber);

    /**
     * 看板输入产线工单产量
     *
     * @param entity
     */
    void countOutput(ReportLineEntity entity);

    /**
     * 修改产线看板数量
     *
     * @param entity
     * @return
     */
    void updateCount(ReportLineEntity entity);


    /**
     * 投产、挂起、完成接口
     *
     * @param operationNumber
     * @param action
     * @param type
     */
    void dealReportCountRecord(String operationNumber, Integer action, String type);



    void insertAutoRecord(OperationOrderEntity entity, Date now);

    /**
     * 是否占有产线计数器
     *
     * @param lineId
     * @param workOrderNumber
     * @return
     */
    Boolean getAllowReport(Integer lineId, String workOrderNumber);


    /**
     * 投产前确认接口（判断制造单元是否占用）
     *
     * @param lineId 产线Id
     * @return
     */
    LineOccupyDTO confirmReportAction(Integer lineId);

    /**
     * 获取完工数量和已报工数
     *
     * @param operationNumber 作业工单号
     * @return
     */
    FinishAndReportCountDTO getFinishAndReportCount(String operationNumber);

    /**
     * 工单完成前状态校验
     * 工单下所有作业工单都为完成状态才可以完成
     *
     * @param workOrder 工单号
     * @return
     */
    boolean checkFinishState(String workOrder);

    /**
     * 获取历史报工完成数
     *
     * @param operationNumber 作业工单号
     * @return
     */
    Double getHistoryFinishCount(String operationNumber);

}
