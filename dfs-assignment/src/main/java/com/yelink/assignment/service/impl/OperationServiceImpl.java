package com.yelink.assignment.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.config.ResponseException;
import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.constant.PriorityTypeEnum;
import com.yelink.assignment.entity.WorkOrderEntity;
import com.yelink.assignment.entity.common.BillMaterialVo;
import com.yelink.assignment.entity.common.BillVo;
import com.yelink.assignment.entity.model.DeviceEntity;
import com.yelink.assignment.entity.operation.OperationOrderDeviceRelevanceEntity;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderLineRelevanceEntity;
import com.yelink.assignment.entity.operation.OperationOrderProcedureRelationEntity;
import com.yelink.assignment.entity.operation.OperationOrderTeamEntity;
import com.yelink.assignment.entity.operation.OperationOrderTeamRelevanceEntity;
import com.yelink.assignment.entity.operation.dto.OperationOrderPageDTO;
import com.yelink.assignment.entity.operation.dto.OperationOrderSelectDTO;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.mapper.OperationOrderMapper;
import com.yelink.assignment.mapper.ReportLineMapper;
import com.yelink.assignment.mapper.UnqualifiedImgMapper;
import com.yelink.assignment.provider.constant.KafkaMessageTypeEnum;
import com.yelink.assignment.request.RequestService;
import com.yelink.assignment.service.InputRecordService;
import com.yelink.assignment.service.OperationOrderDeviceRelevanceService;
import com.yelink.assignment.service.OperationOrderLineRelevanceService;
import com.yelink.assignment.service.OperationOrderProcedureRelationService;
import com.yelink.assignment.service.OperationOrderTeamRelevanceService;
import com.yelink.assignment.service.OperationOrderTeamService;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.OrderExecuteSeqService;
import com.yelink.dfscommon.api.dfs.DeviceInterface;
import com.yelink.dfscommon.api.dfs.MaterialInterface;
import com.yelink.dfscommon.api.dfs.WorkOrderInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.assignment.OperationStateEnum;
import com.yelink.dfscommon.dto.CommMaterialSkuEntitySelectDTO;
import com.yelink.dfscommon.dto.MyPage;
import com.yelink.dfscommon.dto.dfs.MaterialSkuDTO;
import com.yelink.dfscommon.entity.MaterialEntityDTO;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2022-03-03
 */
@Service
@RequiredArgsConstructor
public class OperationServiceImpl extends ServiceImpl<OperationOrderMapper, OperationOrderEntity> implements OperationService {

    private static final String EDIT = "OPERATION_ORDER_EDIT:";
    private final WorkOrderInterface workOrderInterface;
    private final ReportLineMapper reportLineMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final InputRecordService inputRecordService;
    private final MaterialInterface materialInterface;
    private final OperationOrderProcedureRelationService operationOrderProcedureRelationService;
    private final UnqualifiedImgMapper unqualifiedImgMapper;
    private final MessagePushToKafkaService messagePushToKafkaService;
    private final RequestService requestService;
    private final OperationOrderDeviceRelevanceService operationOrderDeviceRelevanceService;
    private final OperationOrderTeamRelevanceService operationOrderTeamRelevanceService;
    private final OperationOrderTeamService operationOrderTeamService;
    private final OperationOrderLineRelevanceService operationOrderLineRelevanceService;
    private final DeviceInterface deviceInterface;
    private final OrderExecuteSeqService orderExecuteSeqService;


    @Override
    public Page<OperationOrderEntity> getOperationOrderList(OperationOrderSelectDTO operationOrderDTO) {
        //处理分页参数
        int current = operationOrderDTO.getCurrent() == null ? 1 : operationOrderDTO.getCurrent();
        int size = operationOrderDTO.getSize() == null ? Integer.MAX_VALUE : operationOrderDTO.getSize();
        QueryWrapper<OperationOrderEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<OperationOrderEntity> wrapper = queryWrapper.lambda();
        //作业工单过滤
        WrapperUtil.like(wrapper, OperationOrderEntity::getOperationNumber, operationOrderDTO.getOperationNumber());
        //生产工单过滤
        WrapperUtil.like(wrapper, OperationOrderEntity::getWorkOrderNum, operationOrderDTO.getWorkOrderNum());
        //物料编码过滤
        WrapperUtil.like(wrapper, OperationOrderEntity::getMaterialCode, operationOrderDTO.getMaterialCode());
        //制造单元过滤
        WrapperUtil.eq(wrapper, OperationOrderEntity::getLineId, operationOrderDTO.getLineId());
        //时间过滤
        WrapperUtil.between(wrapper, OperationOrderEntity::getActualStartDate, operationOrderDTO.getActualStartDate(), operationOrderDTO.getActualEndDate());
        WrapperUtil.between(wrapper, OperationOrderEntity::getActualEndDate, operationOrderDTO.getActualFinishStartDate(), operationOrderDTO.getActualFinishEndDate());
        WrapperUtil.between(wrapper, OperationOrderEntity::getCreateTime, operationOrderDTO.getCreateStartTime(), operationOrderDTO.getCreateEndTime());
        //状态过滤过滤
        if (StringUtils.isNotEmpty(operationOrderDTO.getState())) {
            List<Integer> states = Arrays.stream(operationOrderDTO.getState().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(OperationOrderEntity::getState, states);
        }
        //制造单元类型过滤
        if (StringUtils.isNotEmpty(operationOrderDTO.getModelName())) {
            List<String> modelNames = Arrays.stream(operationOrderDTO.getModelName().split(Constant.SEP)).collect(Collectors.toList());
            wrapper.in(OperationOrderEntity::getWorkCenterName, modelNames);
        }
        //支持多个工作中心id查询
        //支持多个工序id查询
        buildWrapper(wrapper, operationOrderDTO.getWorkCenterId(), operationOrderDTO.getProcedureIds());

        wrapper.orderByDesc(OperationOrderEntity::getCreateTime)
                .orderByDesc(OperationOrderEntity::getId);

        //使用物料条件查询
        CommMaterialSkuEntitySelectDTO skuSelectDTO = CommMaterialSkuEntitySelectDTO.builder().materialFields(operationOrderDTO.getMaterialFields())
                .materialSkus(operationOrderDTO.getMaterialSkus()).build();
        MaterialSkuDTO skuDTO = JacksonUtil.getResponseObject(materialInterface.listCodesAndSkuIdsByCondition(skuSelectDTO), MaterialSkuDTO.class);
        if (skuDTO == null) {
            return new Page<>(current, size);
        }
        if (skuDTO.getUseConditions()) {
            List<String> materialCodes = skuDTO.getMaterialCodes();
            List<Integer> skuIds = skuDTO.getSkuIds();
            if (CollectionUtils.isEmpty(materialCodes) && CollectionUtils.isEmpty(skuIds)) {
                return new Page<>(current, size);
            }
            wrapper.in(!CollectionUtils.isEmpty(materialCodes), OperationOrderEntity::getMaterialCode, materialCodes);
            wrapper.in(!CollectionUtils.isEmpty(skuIds), OperationOrderEntity::getSkuId, skuIds);
        }

        Page<OperationOrderEntity> page = this.page(new Page<>(operationOrderDTO.getCurrent(), operationOrderDTO.getSize()), wrapper);
        List<OperationOrderEntity> records = page.getRecords();
        // 填充物料信息
        setOperationOrderMaterialInfo(records);
        //查询班组名
        setTeamName(records);
        setDeviceName(records);
        // 填充作业工单 工艺工序关联关系
        for (OperationOrderEntity record : page.getRecords()) {
            setOperationOrderProcedureName(record);
            record.setPriorityName(PriorityTypeEnum.getNameByCode(record.getPriority()));
        }
        MyPage<OperationOrderEntity> myPage = new MyPage<OperationOrderEntity>(page.getCurrent(), page.getSize());
        myPage.setRecords(page.getRecords()).setTotal(page.getTotal());
        //统计汇总
        OperationOrderPageDTO pageDTO = getSumForList(queryWrapper);
        myPage.setDto(pageDTO);
        return myPage;
    }
    @Override
    public void buildWrapper(LambdaQueryWrapper<OperationOrderEntity> wrapper, String workCenterIdStr, String procedureIdsStr) {
        if (StringUtils.isNotBlank(workCenterIdStr)) {
            List<Integer> workCenterIds = Arrays.stream(workCenterIdStr.split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(OperationOrderEntity::getWorkCenterId, workCenterIds);
        }
        if (StringUtils.isNotBlank(procedureIdsStr)) {
            List<Integer> procedureIds = Arrays.stream(procedureIdsStr.split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            List<OperationOrderProcedureRelationEntity> operationOrderProcedureRelations = operationOrderProcedureRelationService.lambdaQuery()
                    .in(OperationOrderProcedureRelationEntity::getProcedureId, procedureIds)
                    .list();
            if (CollectionUtils.isEmpty(operationOrderProcedureRelations)) {
                wrapper.isNull(OperationOrderEntity::getOperationNumber);
            } else {
                List<String> operationOrderNumbers = operationOrderProcedureRelations.stream().map(OperationOrderProcedureRelationEntity::getOperationOrderNumber).collect(Collectors.toList());
                wrapper.in(OperationOrderEntity::getOperationNumber, operationOrderNumbers);
            }
        }
    }

    private void setTeamName(List<OperationOrderEntity> records) {
        List<Integer> teamIds = records.stream().map(OperationOrderEntity::getTeamId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(teamIds)) {
            return;
        }
        List<SysTeamEntity> teamListByIds = requestService.getTeamListByIds(teamIds);
        Map<Integer, String> teamIdNameMap = teamListByIds.stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamName));
        for (OperationOrderEntity record : records) {
            record.setTeamName(teamIdNameMap.get(record.getTeamId()));
        }
    }

    private void setDeviceName(List<OperationOrderEntity> records) {
        List<Integer> deviceIds = records.stream().map(OperationOrderEntity::getDeviceId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceIds)) {
            return;
        }
        List<DeviceEntity> deviceListByIds = JacksonUtil.getResponseArray(deviceInterface.listByIds(deviceIds), DeviceEntity.class);
        Map<Integer, String> deviceIdNameMap = deviceListByIds.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
        for (OperationOrderEntity record : records) {
            record.setDeviceName(deviceIdNameMap.get(record.getDeviceId()));
        }
    }

    /**
     * 统计汇总
     *
     * @return
     */
    private OperationOrderPageDTO getSumForList(QueryWrapper<OperationOrderEntity> queryWrapper) {
        queryWrapper.select("IFNULL(sum(product_count),0) AS product_count",
                "IFNULL(sum(finish_count),0) AS finish_count");
        OperationOrderEntity entity = this.getOne(queryWrapper);
        OperationOrderPageDTO build = OperationOrderPageDTO.builder().build();
        if (entity == null) {
            return build;
        }
        //排产数量
        build.setProductCount(entity.getProductCount());
        //完成数量
        build.setFinishCount(entity.getFinishCount());
        return build;

    }

    @Override
    public List<OperationOrderEntity> getByWorkOrder(String workOrder) {
        ResponseData data = workOrderInterface.getWorkOrderByNumber(workOrder);
        if (data.getCode().equals(ResponseData.SUCCESS_CODE) && data.getData() == null) {
            throw new ResponseException("无此工单信息");
        }
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderEntity::getWorkOrderNum, workOrder);
        queryWrapper.in(OperationOrderEntity::getState, OperationStateEnum.RELEASED.getCode(), OperationStateEnum.INVESTMENT.getCode(),
                OperationStateEnum.HANG_UP.getCode(), OperationStateEnum.FINISHED.getCode());
        queryWrapper.orderByDesc(OperationOrderEntity::getCreateTime);
        List<OperationOrderEntity> resultList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        // 填充物料信息
        setOperationOrderMaterialInfo(resultList);
        for (OperationOrderEntity operationOrderEntity : resultList) {
            setOperationOrderProcedureName(operationOrderEntity);
            operationOrderEntity.setPriorityName(PriorityTypeEnum.getNameByCode(operationOrderEntity.getPriority()));
            WorkCenterEntity workCenterEntity = requestService.getWorkCenterById(operationOrderEntity.getWorkCenterId());
            //拿到工作中心type
            operationOrderEntity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
            operationOrderEntity.setWorkCenterRelevanceType(workCenterEntity == null ? null : workCenterEntity.getRelevanceType());
        }
        //查询班组名
        setTeamName(resultList);
        setDeviceName(resultList);
        return resultList;
    }

    /**
     * 作业工单表中只有物料code, 填充作业工单物料信息
     */
    @Override
    public void setOperationOrderMaterialInfo(List<OperationOrderEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 查询物料信息
        String codes = records.stream().map(OperationOrderEntity::getMaterialCode).collect(Collectors.joining(","));
        ResponseData materialsResponseData = materialInterface.getMaterialsByCodes(codes);
        if (materialsResponseData != null && materialsResponseData.getCode().equals(ResponseData.SUCCESS_CODE)) {
            List<MaterialEntityDTO> materials = JacksonUtil.getResponseArray(materialsResponseData, MaterialEntityDTO.class);
            Map<String, MaterialEntityDTO> codeMaterialMap = materials.stream().collect(Collectors.toMap(MaterialEntityDTO::getCode, v -> v));
            // 查询sku信息
            Set<Integer> skuIds = records.stream().map(OperationOrderEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, SkuEntity> skuMap = JacksonUtil.getResponseArray(materialInterface.getSkusByIds(skuIds), SkuEntity.class)
                    .stream().collect(Collectors.toMap(SkuEntity::getSkuId, v -> v));
            for (OperationOrderEntity record : records) {
                MaterialEntityDTO tempMaterial = codeMaterialMap.getOrDefault(record.getMaterialCode(), new MaterialEntityDTO());
                MaterialEntityDTO materialEntityDTO = JacksonUtil.convertObject(tempMaterial, MaterialEntityDTO.class);
                materialEntityDTO.setSkuEntity(skuMap.get(record.getSkuId()));
                record.setMaterialId(materialEntityDTO.getId());
                record.setMaterialCode(materialEntityDTO.getCode());
                record.setMaterialFields(materialEntityDTO);
            }

        }
    }

    /**
     * 设置作业工单工序
     * 作业工单 工艺工序关联关系
     *
     * @param operationOrderEntity
     */
    private void setOperationOrderProcedureName(OperationOrderEntity operationOrderEntity) {
        LambdaQueryWrapper<OperationOrderProcedureRelationEntity> relationQW = new LambdaQueryWrapper<>();
        relationQW.eq(OperationOrderProcedureRelationEntity::getOperationOrderNumber, operationOrderEntity.getOperationNumber());
        List<OperationOrderProcedureRelationEntity> list = operationOrderProcedureRelationService.list(relationQW);
        if (!CollectionUtils.isEmpty(list)) {
            operationOrderEntity.setProcedureRelationEntities(list);
            operationOrderEntity.setProcedureName(list.stream().map(OperationOrderProcedureRelationEntity::getCraftProcedureName).collect(Collectors.joining()));
        }
    }

    @Override
    public OperationOrderEntity getByOperationNumber(String operationOrder) {
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderEntity::getOperationNumber, operationOrder);
        OperationOrderEntity operationOrderEntity = getOne(queryWrapper);
        if (operationOrderEntity == null) {
            return null;
        }
        // 排产数量
        double productCount = operationOrderEntity.getProductCount() == null ? 0.0 : operationOrderEntity.getProductCount();
        // 完成数量
        double finishCount = operationOrderEntity.getFinishCount() == null ? 0.0 : operationOrderEntity.getFinishCount();
        if (productCount - finishCount <= 0) {
            operationOrderEntity.setWaitProductCount(0.0);
        }
        // V1.12.1 查看作业工单的附件，包括工艺附件和物料附件
        ResponseData materialData = materialInterface.detailByCodeAndSkuId(operationOrderEntity.getMaterialCode(), operationOrderEntity.getSkuId());
        if (materialData != null && materialData.getCode().equals(ResponseData.SUCCESS_CODE)) {
            MaterialEntityDTO materialEntity = JacksonUtil.getResponseObject(materialData, MaterialEntityDTO.class);
            if (materialEntity != null) {
                operationOrderEntity.setMaterialId(materialEntity.getId());
                operationOrderEntity.setMaterialCode(materialEntity.getCode());
                operationOrderEntity.setMaterialFields(materialEntity);
//                operationOrderEntity.setMaterialStandard(materialEntity.getStandard());
                operationOrderEntity.setCraftId(materialEntity.getCraftId());
            }
        }
        // 填充物料信息
        List<OperationOrderEntity> list = Stream.of(operationOrderEntity).collect(Collectors.toList());
        setOperationOrderMaterialInfo(list);
        // 作业工单 工艺工序关联
        setOperationOrderProcedureName(operationOrderEntity);
        operationOrderEntity.setPriorityName(PriorityTypeEnum.getNameByCode(operationOrderEntity.getPriority()));

        WorkCenterEntity workCenterEntity = requestService.getWorkCenterById(operationOrderEntity.getWorkCenterId());
        //拿到工作中心type
        operationOrderEntity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
        operationOrderEntity.setWorkCenterRelevanceType(workCenterEntity == null ? null : workCenterEntity.getRelevanceType());
        //获取工单关联设备资源
        getRelevanceDeviceById(operationOrderEntity);
        //获取工单关联班组资源
        getRelevanceTeamById(operationOrderEntity);
        //获取工单关联制造单元资源
        getRelevanceLineById(operationOrderEntity);
        //查询班组信息
        if (operationOrderEntity.getTeamId() != null) {
            SysTeamEntity team = requestService.getTeamById(operationOrderEntity.getTeamId());
            operationOrderEntity.setTeamName(team == null ? null : team.getTeamName());
            // 投产班组成员
            List<OperationOrderTeamEntity> operationOrderTeams = operationOrderTeamService.getOperationOrderTeams(operationOrder);
            operationOrderEntity.setOperationOrderTeamEntities(operationOrderTeams);
        }
        //查询信息
        if (operationOrderEntity.getDeviceId() != null) {
            ResponseData responseData = deviceInterface.getDeviceSimpleEntityById(operationOrderEntity.getDeviceId());
            DeviceEntity deviceEntity = JacksonUtil.getResponseObject(responseData, DeviceEntity.class);
            operationOrderEntity.setDeviceName(deviceEntity == null ? null : deviceEntity.getDeviceName());
        }

        return operationOrderEntity;
    }

    /**
     * 获取工单关联设备资源
     *
     * @param operationOrderEntity
     * @return
     */
    private void getRelevanceDeviceById(OperationOrderEntity operationOrderEntity) {
        //查询关联设备资源
        LambdaQueryWrapper<OperationOrderDeviceRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationOrderDeviceRelevanceEntity::getOperationOrderId, operationOrderEntity.getId());
        List<OperationOrderDeviceRelevanceEntity> deviceRelevanceList = operationOrderDeviceRelevanceService.list(lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(deviceRelevanceList)) {
            operationOrderEntity.setRelevanceDeviceIds(deviceRelevanceList.stream().map(OperationOrderDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList()));
        }
    }

    /**
     * 获取工单关联制造单元资源
     *
     * @param operationOrderEntity
     * @return
     */
    private void getRelevanceLineById(OperationOrderEntity operationOrderEntity) {
        //查询关联设备资源
        LambdaQueryWrapper<OperationOrderLineRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationOrderLineRelevanceEntity::getOperationOrderId, operationOrderEntity.getId());
        List<OperationOrderLineRelevanceEntity> lineRelevanceList = operationOrderLineRelevanceService.list(lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(lineRelevanceList)) {
            operationOrderEntity.setRelevanceLineIds(lineRelevanceList.stream().map(OperationOrderLineRelevanceEntity::getLineId).collect(Collectors.toList()));
        }
    }

    /**
     * 获取工单关联班组资源
     *
     * @param operationEntity
     * @return
     */
    private void getRelevanceTeamById(OperationOrderEntity operationEntity) {
        //查询关联班组资源
        LambdaQueryWrapper<OperationOrderTeamRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationOrderTeamRelevanceEntity::getOperationOrderId, operationEntity.getId());
        List<OperationOrderTeamRelevanceEntity> teamRelevanceList = operationOrderTeamRelevanceService.list(lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(teamRelevanceList)) {
            List<Integer> teamIds = teamRelevanceList.stream().map(OperationOrderTeamRelevanceEntity::getTeamId).collect(Collectors.toList());
            operationEntity.setRelevanceTeamIds(teamIds);
            List<SysTeamEntity> sysTeamEntityList = requestService.getTeamListByIds(teamIds);
            operationEntity.setRelevanceTeamNames(sysTeamEntityList.stream().map(SysTeamEntity::getTeamName).collect(Collectors.toList()));
        }
    }

    @Override
    public OperationOrderEntity getByOperationNumberSimple(String operationOrder) {
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderEntity::getOperationNumber, operationOrder);
        OperationOrderEntity operationOrderEntity = getOne(queryWrapper);
        operationOrderEntity.setPriorityName(PriorityTypeEnum.getNameByCode(operationOrderEntity.getPriority()));
        return operationOrderEntity;
    }

    @Override
    public List<OperationOrderEntity> getRunningOperationByLineIds(String lineIds) {
        if (StringUtils.isBlank(lineIds)) {
            return new ArrayList<>();
        }
        List<String> lineIdList = Arrays.stream(lineIds.split(Constant.SEP)).collect(Collectors.toList());

        //正在生产的订单：投入状态的工单
        LambdaQueryWrapper<OperationOrderEntity> runningQw = new LambdaQueryWrapper<>();
        runningQw.in(OperationOrderEntity::getLineId, lineIdList)
                .eq(OperationOrderEntity::getState, OperationStateEnum.INVESTMENT.getCode())
                .orderByAsc(OperationOrderEntity::getActualStartDate);
        return list(runningQw);
    }

    @Override
    public List<OperationOrderEntity> getRunningOperations() {
        //正在投产作业工单
        LambdaQueryWrapper<OperationOrderEntity> runningQw = new LambdaQueryWrapper<>();
        runningQw.eq(OperationOrderEntity::getState, OperationStateEnum.INVESTMENT.getCode());
        return list(runningQw);
    }

    @Override
    public List<OperationOrderEntity> getByStates(List<Integer> states) {
        //通过状态查询作业工单
        LambdaQueryWrapper<OperationOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OperationOrderEntity::getState, states);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OperationOrderEntity insertOperationOrder(OperationOrderEntity entity) {
        addOperationOrder(entity);
        workOrderInterface.refreshProcedureCount(entity.getWorkOrderNum());
        return entity;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOperationOrder(OperationOrderEntity entity) {
        if (StringUtils.isBlank(entity.getOperationNumber())) {
            throw new ResponseException("新增作业工单失败，请检查作业工单单据配置");
        }
        if (entity.getProductCount() == null) {
            entity.setProductCount(0.0);
        }
        entity.setId(null);
        entity.setStateName(OperationStateEnum.getNameByCode(entity.getState()));
        entity.setCreateTime(new Date());
        String username = requestService.getUsername();
        entity.setCreateBy(username);
        entity.setCreateName(requestService.getNickName(username));
        // 报工系数默认为1
        if (entity.getCoefficient() == null) {
            entity.setCoefficient(1);
        }
        // 获取待排数量
        getWaitProductCount(entity);
        if (entity.getWorkCenterId() != null) {
            // 获取工作中心名称
            WorkCenterEntity workCenterEntity = requestService.getWorkCenterById(entity.getWorkCenterId());
            entity.setWorkCenterName(workCenterEntity.getName());
        }
        this.save(entity);
        //需要更新当前生产工单与产线的关联关系
        if (entity.getLineId() != null) {
            workOrderInterface.updateProductLineRelation(entity.getId(), entity.getLineId(), entity.getWorkOrderNum());
        }

        //保存工单投产班组成员信息
        operationOrderTeamService.add(entity);
        //保存工单关联资源
        saveRelevanceResource(entity);
    }

    /**
     * 获取待排数量
     *
     * @param entity
     */
    private void getWaitProductCount(OperationOrderEntity entity) {
        Double finishCount = entity.getFinishCount();
        Double productCount = entity.getProductCount();
        if (finishCount == null) {
            finishCount = 0.0;
        }
        if (productCount == null) {
            productCount = 0.0;
        }
        entity.setWaitProductCount(productCount - finishCount < 0 ? 0.0 : productCount - finishCount);
    }

    /**
     * 更新补全字段
     *
     * @param entity
     * @return
     */
    private OperationOrderEntity completedNewOperationOrder(OperationOrderEntity entity) {
        OperationOrderEntity old = getById(entity.getId());
        JSONObject oldJsonObject = JSON.parseObject(JSON.toJSONString(old));
        JSONObject newJsonObject = JSON.parseObject(JSON.toJSONString(entity));
        for (Map.Entry<String, Object> entry : newJsonObject.entrySet()) {
            //新值覆盖旧值
            oldJsonObject.put(entry.getKey(), entry.getValue());
        }
        return JSONObject.parseObject(oldJsonObject.toJSONString(), OperationOrderEntity.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OperationOrderEntity edit(OperationOrderEntity entity) {
        OperationOrderEntity updateEntity = editOperationOrder(entity);
        workOrderInterface.refreshProcedureCount(updateEntity.getWorkOrderNum());
        return updateEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    public OperationOrderEntity editOperationOrder(OperationOrderEntity entity) {
        OperationOrderEntity updateEntity = completedNewOperationOrder(entity);
        // 允许修改为空值
        if (entity.getCoefficient() == null) {
            updateEntity.setCoefficient(null);
        }
        if (StringUtils.isEmpty(entity.getTooling())) {
            updateEntity.setTooling(null);
        }
        if (entity.getPlanStartDate() == null) {
            updateEntity.setPlanStartDate(null);
        }
        if (entity.getPlanEndDate() == null) {
            updateEntity.setPlanEndDate(null);
        }
        if (entity.getRemark() == null) {
            updateEntity.setRemark(null);
        }
        if (entity.getLineId() == null) {
            updateEntity.setLineId(null);
        }
        if (entity.getLineName() == null) {
            updateEntity.setLineName(null);
        }
        if (entity.getTeamId() == null) {
            updateEntity.setTeamId(null);
        }
        if (entity.getDeviceId() == null) {
            updateEntity.setDeviceId(null);
        }
        Date now = new Date();
        //如果返回false，则说明该key值存在，已经有程序在使用这个key值，从而实现了分布式加锁的功能
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(EDIT + updateEntity.getOperationNumber(), now, 10, TimeUnit.MINUTES))) {
            return null;
        }
        try {
            OperationOrderEntity old = getById(entity.getId());
            entity.setOperationNumber(old.getOperationNumber());
            entity.setWorkOrderNum(old.getWorkOrderNum());
            setStartEndDate(updateEntity);
            //如果状态不同，且新状态为投产、挂起、完成
            String username = requestService.getUsername();
            updateEntity.setUpdateBy(username);
            updateEntity.setUpdateName(requestService.getNickName(username));
            updateEntity.setUpdateTime(now);
            updateEntity.setStateName(OperationStateEnum.getNameByCode(updateEntity.getState()));
            getWaitProductCount(updateEntity);
            this.updateById(updateEntity);
            //插入状态
            if (!old.getState().equals(updateEntity.getState())) {
                workOrderInterface.insertState(updateEntity.getOperationNumber(), updateEntity.getState(), now.getTime());
                //如果工单状态变更的时候，则发布状态变更消息消息
                messagePushToKafkaService.pushNewMessage(updateEntity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.OPERATION_ORDER_STATUS_CHANGE_MESSAGE);
            }
            //更新报工表
//            updateReportRecord(updateEntity);
            //投入数锁表
            inputRecordService.updateInputRecord(updateEntity);

            // 生产工单自动变更状态（生效->完成）
            autoChangeWorkOrderState(updateEntity);

            //需要更新当前生产工单与产线的关联关系
            if (updateEntity.getLineId() != null) {
                workOrderInterface.updateProductLineRelation(updateEntity.getId(), updateEntity.getLineId(), updateEntity.getWorkOrderNum());
            }
            //保存工单投产班组成员信息
            operationOrderTeamService.add(entity);
            //保存工单关联资源
            saveRelevanceResource(entity);
            //更新工单投产排序表
            orderExecuteSeqService.updateOrderExecuteSeq(entity);

        } catch (Exception e) {
            log.error("编辑保存失败:", e);
        } finally {
            redisTemplate.delete(EDIT + updateEntity.getOperationNumber());
        }
        return this.getById(updateEntity.getId());
    }

    /**
     * 生产工单关联的所有作业工单状态为：完成/关闭/取消，并且生产工单的完成数量 >= 生产工单的计划数量，生产工单自动变更状态（完成）
     */
    private void autoChangeWorkOrderState(OperationOrderEntity entity) {
        Integer workOrderState = null;
        if (entity.getState() == OperationStateEnum.INVESTMENT.getCode()) {
            workOrderState = OperationStateEnum.INVESTMENT.getCode();
        }
        //有一个投产则为投产
        if (workOrderState != null) {
            workOrderInterface.innerUpdateWorkOrderState(entity.getWorkOrderNum(), workOrderState);
            return;
        }

        // 获取生产工单所有关联的作业工单
        LambdaQueryWrapper<OperationOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(OperationOrderEntity::getWorkOrderNum, entity.getWorkOrderNum());
        List<OperationOrderEntity> operationOrderEntities = this.list(qw);
        long count = operationOrderEntities.stream().filter(o -> o.getState().equals(OperationStateEnum.CREATED.getCode()) ||
                o.getState().equals(OperationStateEnum.RELEASED.getCode()) ||
                o.getState().equals(OperationStateEnum.INVESTMENT.getCode()) ||
                o.getState().equals(OperationStateEnum.HANG_UP.getCode())).count();
        if (count > 0) {
            return;
        }
        ResponseData data = workOrderInterface.getWorkOrderByNumber(entity.getWorkOrderNum());
        if (data.getCode().equals(ResponseData.SUCCESS_CODE) && data.getData() != null) {
            WorkOrderEntity workOrderEntity = JacksonUtil.getResponseObject(data, WorkOrderEntity.class);
            if (workOrderEntity.getFinishCount() >= workOrderEntity.getPlanQuantity()) {
                workOrderInterface.innerUpdateWorkOrderState(entity.getWorkOrderNum(), OperationStateEnum.FINISHED.getCode());
            }
        }
    }

    @Override
    public void setStartEndDate(OperationOrderEntity entity) {
        if (entity.getState() == OperationStateEnum.INVESTMENT.getCode()) {
            if (entity.getActualStartDate() == null) {
                entity.setActualStartDate(new Date());
            }
        }
        if (entity.getState() == OperationStateEnum.FINISHED.getCode()) {
            entity.setActualEndDate(new Date());
            double hours = ((double) entity.getActualEndDate().getTime() - entity.getActualStartDate().getTime()) / 1000 / 3600;
            entity.setActualWorkingHours(MathUtil.round(hours, 2));
        }
    }

    /*private void updateReportRecord(OperationOrderEntity entity) {
        if (entity.getLineId() == null) {
            return;
        }
        //更新计数器记录时间点
        ReportCountService reportCountService = SpringUtil.getBean(ReportCountService.class);
        try {
            reportCountService.dealReportRecord(entity);
        } catch (Exception e) {
            log.error("更新计数器记录时间点失败", e);
        }
    }*/


    @Override
    public Page getInputHistory(String operationNumber, String type, Integer current, Integer size) {
        LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
        WrapperUtil.eq(wrapper, ReportLineEntity::getOperationNumber, operationNumber);
        WrapperUtil.eq(wrapper, ReportLineEntity::getType, type);
        wrapper.orderByDesc(ReportLineEntity::getCreateTime);
        List<ReportLineEntity> list = reportLineMapper.selectList(wrapper);

        Page<ReportLineEntity> page;
        if (current == null || size == null) {
            page = new Page<>(1, list.size(), list.size());
            page.setRecords(list);
        } else {
            page = reportLineMapper.selectPage(new Page<>(current, size), wrapper);
        }
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            for (ReportLineEntity entity : page.getRecords()) {
                List<String> urlList = unqualifiedImgMapper.selectUrlList(entity.getId());
                entity.setPicUrls(urlList.toArray(new String[urlList.size()]));
            }
        }
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String operationNumber) {
        OperationOrderEntity operationOrderEntity = delOperationOrder(operationNumber);
        //需要更新当前生产工单与产线的关联关系
        if (operationOrderEntity.getLineId() != null) {
            workOrderInterface.updateProductLineRelation(operationOrderEntity.getId(), operationOrderEntity.getLineId(), operationOrderEntity.getWorkOrderNum());
        }
        workOrderInterface.refreshProcedureCount(operationOrderEntity.getWorkOrderNum());
    }

    @Transactional(rollbackFor = Exception.class)
    public OperationOrderEntity delOperationOrder(String operationNumber) {
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderEntity::getOperationNumber, operationNumber);
        OperationOrderEntity operationOrderEntity = this.getByOperationNumber(operationNumber);
        this.remove(queryWrapper);
        return operationOrderEntity;
    }


    /**
     * 获取工单今日正在投产的作业工单
     *
     * @param workOrderNumber
     * @return
     */
    @Override
    public List<OperationOrderEntity> listInWork(String workOrderNumber) {
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderEntity::getWorkOrderNum, workOrderNumber);
        queryWrapper.in(OperationOrderEntity::getState, OperationStateEnum.HANG_UP.getCode(), OperationStateEnum.INVESTMENT.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public ReportLineEntity updateInputHistory(ReportLineEntity reportLineEntity) {
        LambdaQueryWrapper<ReportLineEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReportLineEntity::getId, reportLineEntity.getId());
        reportLineEntity.setUpdateTime(new Date());
        String username = requestService.getUsername();
        reportLineEntity.setUserName(username);
        reportLineEntity.setUserNickname(requestService.getNickName(username));
        reportLineMapper.update(reportLineEntity, queryWrapper);
        return reportLineEntity;
    }

    @Override
    /**
     * 废弃 **
     */
    public ReportLineEntity addHistoryCount(ReportLineEntity entity) {
        Date date = new Date();
        entity.setReportDate(date);
        entity.setCreateTime(date);
        String username = requestService.getUsername();
        entity.setUserName(username);
        entity.setUserNickname(requestService.getNickName(username));
        reportLineMapper.insert(entity);
        return entity;
    }

    @Override
    public void addOperationProcedureRelation(List<OperationOrderProcedureRelationEntity> entities) {
        for (OperationOrderProcedureRelationEntity entity : entities) {
            entity.setId(null);
        }
        operationOrderProcedureRelationService.saveBatch(entities);
    }

    @Override
    public Double getPlanQuantitySum(String workOrderNumber) {
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderEntity::getWorkOrderNum, workOrderNumber);
        List<OperationOrderEntity> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(o -> o.getProductCount() != null).mapToDouble(OperationOrderEntity::getProductCount).sum();
    }

    @Override
    public List<BillVo> judgeBeforeInsert(OperationOrderEntity entity) {
        Map<String, Double> map = new HashMap<>();
        Map<String, Double> thisMap = new HashMap<>();
        Map<String, Double> operationOrderMaterialMap = new HashMap<>();
        List<BillVo> billVos = new ArrayList<>();
        List<BillMaterialVo> billMaterialVos = new ArrayList<>();
        // 获取生产工单绑定的物料
        ResponseData responseData = workOrderInterface.getWorkOrderByNumber(entity.getWorkOrderNum());
        WorkOrderEntity workOrderEntity = JSON.parseObject(JSON.toJSONString(responseData.getData()), WorkOrderEntity.class);
        map.put(workOrderEntity.getMaterialCode(), workOrderEntity.getPlanQuantity());
        // 获取此次绑定的物料信息
        thisMap.put(entity.getMaterialCode(), entity.getProductCount());
        // 获取生产工单关联的作业工单的物料数量之和(不包含取消状态下的单据)
        LambdaQueryWrapper<OperationOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(OperationOrderEntity::getWorkOrderNum, entity.getWorkOrderNum());
        List<OperationOrderEntity> operationOrderEntities = this.list(qw);
        if (!CollectionUtils.isEmpty(operationOrderEntities)) {
            operationOrderMaterialMap = operationOrderEntities.stream()
                    .filter(o -> !o.getState().equals(OperationStateEnum.CANCELED.getCode()))
                    .filter(o -> thisMap.containsKey(o.getMaterialCode()))
                    .collect(Collectors.toMap(OperationOrderEntity::getMaterialCode, OperationOrderEntity::getProductCount, (o1, o2) -> {
                        o1 += o2;
                        return o1;
                    }));
        }
        // 循环遍历本次提交的物料数量，如果生产工单关联的作业工单的数量之和(包括当前的作业工单) > 作业工单的计划数量，给前端提示
        for (Map.Entry<String, Double> entry : thisMap.entrySet()) {
            String materialCode = entry.getKey();
            double amount = entry.getValue();
            double saleOrderAmount = map.get(materialCode) == null ? 0.0 : map.get(materialCode);
            double beforeAddAmount = operationOrderMaterialMap.get(materialCode) == null ? 0.0 : operationOrderMaterialMap.get(materialCode);
            if (beforeAddAmount + amount > saleOrderAmount) {
                double diff = beforeAddAmount + amount - saleOrderAmount;
                billMaterialVos.add(BillMaterialVo.builder()
                        .materialCode(materialCode)
                        .quantity(diff)
                        .build());
            }
        }
        billVos.add(BillVo.builder()
                .billTypeCode(Constant.OPERATION_ORDER)
                .relatedBillNumber(entity.getWorkOrderNum())
                .relatedBillTypeCode(Constant.WORK_ORDER)
                .materialVos(billMaterialVos).build());
        return billVos;
    }

    @Override
    public void refreshProcedureCountCallBack(String workOrderNumber) {
        String key = RedisKeyPrefix.REFRESH_WORK_ORDER_CALL_BACK_LOCK + workOrderNumber;
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, 0, 1, TimeUnit.MINUTES);
        if (lock == null || !lock) {
            return;
        }
        workOrderInterface.refreshProcedureCount(workOrderNumber);
        redisTemplate.delete(key);
    }

    /**
     * 按工单统计指定日期完成数
     *
     * @param date
     * @return
     */
    @Override
    public List<OperationOrderEntity> getFinishCountGroupByWorkOrder(Date date) {
        return this.baseMapper.getFinishCountGroupByWorkOrder(date);
    }

    @Override
    public Boolean batchUpdateState(BatchChangeStateDTO batchApprovalDTO, String username) {
        List<OperationOrderEntity> list = this.lambdaQuery().in(OperationOrderEntity::getCraftId, batchApprovalDTO.getIds()).list();
        // 改状态时，要求状态都是一样的才能改
        long stateCount = list.stream().map(OperationOrderEntity::getState).distinct().count();
        if (stateCount > 1) {
            throw new com.yelink.dfscommon.constant.ResponseException(RespCodeEnum.MUST_BE_IN_THE_SAME_STATE_TO_MODIFY);
        }
        Integer integer = list.stream().map(OperationOrderEntity::getState).distinct().collect(Collectors.toList()).get(0);
        if (integer > batchApprovalDTO.getState()) {
            throw new com.yelink.dfscommon.constant.ResponseException(RespCodeEnum.UPDATE_STATE_NOT_CROSS_LEVEL);
        }
        boolean update = this.lambdaUpdate().in(OperationOrderEntity::getCraftId, batchApprovalDTO.getIds())
                .set(OperationOrderEntity::getState, batchApprovalDTO.getState())
                .set(OperationOrderEntity::getUpdateTime, new Date())
                .set(OperationOrderEntity::getUpdateBy, username)
                .update();
        return update;
    }

    /**
     * 保存工单关联资源
     *
     * @param entity
     */
    private void saveRelevanceResource(OperationOrderEntity entity) {
        //关联设备
        LambdaUpdateWrapper<OperationOrderDeviceRelevanceEntity> deviceUpdateWrapper = new LambdaUpdateWrapper<>();
        deviceUpdateWrapper.eq(OperationOrderDeviceRelevanceEntity::getOperationOrderId, entity.getId());
        operationOrderDeviceRelevanceService.remove(deviceUpdateWrapper);
        if (!CollectionUtils.isEmpty(entity.getRelevanceDeviceIds())) {
            for (Integer deviceId : entity.getRelevanceDeviceIds()) {
                OperationOrderDeviceRelevanceEntity operationOrderDeviceRelevanceEntity = OperationOrderDeviceRelevanceEntity.builder()
                        .operationOrderId(entity.getId()).deviceId(deviceId).build();
                operationOrderDeviceRelevanceService.save(operationOrderDeviceRelevanceEntity);
            }
        }

        //关联班组
        LambdaUpdateWrapper<OperationOrderTeamRelevanceEntity> teamUpdateWrapper = new LambdaUpdateWrapper<>();
        teamUpdateWrapper.eq(OperationOrderTeamRelevanceEntity::getOperationOrderId, entity.getId());
        operationOrderTeamRelevanceService.remove(teamUpdateWrapper);
        if (!CollectionUtils.isEmpty(entity.getRelevanceTeamIds())) {
            for (Integer teamId : entity.getRelevanceTeamIds()) {
                OperationOrderTeamRelevanceEntity operationOrderTeamRelevanceEntity = OperationOrderTeamRelevanceEntity.builder()
                        .operationOrderId(entity.getId()).teamId(teamId).build();
                operationOrderTeamRelevanceService.save(operationOrderTeamRelevanceEntity);
            }
        }

        //关联制造单元
        LambdaUpdateWrapper<OperationOrderLineRelevanceEntity> lineUpdateWrapper = new LambdaUpdateWrapper<>();
        lineUpdateWrapper.eq(OperationOrderLineRelevanceEntity::getOperationOrderId, entity.getId());
        operationOrderLineRelevanceService.remove(lineUpdateWrapper);
        if (!CollectionUtils.isEmpty(entity.getRelevanceLineIds())) {
            for (Integer lineId : entity.getRelevanceLineIds()) {
                OperationOrderLineRelevanceEntity operationOrderLineRelevanceEntity = OperationOrderLineRelevanceEntity.builder()
                        .operationOrderId(entity.getId()).lineId(lineId).build();
                operationOrderLineRelevanceService.save(operationOrderLineRelevanceEntity);
            }
        }
    }


}
