package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.constant.ReportType;
import com.yelink.assignment.entity.operation.OperationOrderDeviceRelevanceEntity;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OrderExecuteSeqEntity;
import com.yelink.assignment.entity.report.ReportCountEntity;
import com.yelink.assignment.mapper.OrderExecuteSeqMapper;
import com.yelink.assignment.service.OperationOrderDeviceRelevanceService;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.OrderExecuteSeqService;
import com.yelink.assignment.service.ReportCountService;
import com.yelink.dfscommon.api.dfs.DictInterface;
import com.yelink.dfscommon.api.dfs.LineInterface;
import com.yelink.dfscommon.constant.assignment.OperationStateEnum;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.ProductionLineEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2022/11/4
 */
@Service
public class OrderExecuteSeqServiceImpl extends ServiceImpl<OrderExecuteSeqMapper, OrderExecuteSeqEntity> implements OrderExecuteSeqService, CommandLineRunner {

    @Autowired
    private DictInterface dictInterface;
    @Lazy
    @Autowired
    private OperationService operationService;
    @Autowired
    private OperationOrderDeviceRelevanceService operationOrderDeviceRelevanceService;
    @Autowired
    private LineInterface lineInterface;
    @Lazy
    @Autowired
    private ReportCountService reportCountService;

    private final static String INIT_FLAG = "OPERATION_ORDER_EXECUTE_SEQ_INIT";
    private final static String INIT_FLAG_NAME = "作业工单顺序表数据初始化标识";

    @Override
    public void updateOrderExecuteSeq(OperationOrderEntity entity) {
        Date now = new Date();

        if (entity.getState().equals(OperationStateEnum.INVESTMENT.getCode())) {
            //生产基本单元为设备的情况,插入排序表
            if (entity.getDeviceId() != null || entity.getLineId() != null) {
                this.save(
                        OrderExecuteSeqEntity.builder()
                                .orderNumber(entity.getOperationNumber())
                                .deviceId(entity.getDeviceId())
                                .lineId(entity.getLineId())
                                .createTime(now)
                                .build()
                );
            }
            //查询关联资源为设备的情况,插入排序表
            List<OperationOrderDeviceRelevanceEntity> list = operationOrderDeviceRelevanceService
                    .lambdaQuery().eq(OperationOrderDeviceRelevanceEntity::getOperationOrderId, entity.getId()).list();
            if (!CollectionUtils.isEmpty(list)) {
                List<OrderExecuteSeqEntity> collect = list.stream()
                        .map(o -> OrderExecuteSeqEntity.builder()
                                .orderNumber(entity.getOperationNumber())
                                .deviceId(o.getDeviceId())
                                .createTime(now)
                                .build()
                        ).collect(Collectors.toList());

                this.saveBatch(collect);
            }

            //插入自动记录
            reportCountService.insertAutoRecord(entity, now);

        }

        if (entity.getState().equals(OperationStateEnum.HANG_UP.getCode())
                || entity.getState().equals(OperationStateEnum.FINISHED.getCode())
                || entity.getState().equals(OperationStateEnum.CLOSED.getCode())
                || entity.getState().equals(OperationStateEnum.CANCELED.getCode())
        ) {
            this.lambdaUpdate().eq(OrderExecuteSeqEntity::getOrderNumber, entity.getOperationNumber()).remove();
        }
    }

    @Override
    public String getOccupyOrderByLineId(Integer lineId) {
        OrderExecuteSeqEntity orderExecuteSeqEntity = this.lambdaQuery()
                .eq(OrderExecuteSeqEntity::getLineId, lineId)
                .orderByAsc(OrderExecuteSeqEntity::getId)
                .last("limit 1").one();
        return orderExecuteSeqEntity == null ? null : orderExecuteSeqEntity.getOrderNumber();
    }


    /**
     * 数据迁移方法
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(String... args) {
        //try、catch避免影响启动
        try {
            initOrderExecuteSeq();
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private void initOrderExecuteSeq() {
        DictEntity one = JacksonUtil.getResponseObject(dictInterface.getByType(INIT_FLAG), DictEntity.class);
        if (one != null) {
            return;
        }
        dictInterface.save(DictEntity.builder().type(INIT_FLAG).name(INIT_FLAG_NAME).createTime(new Date()).build());

        //获取已经处理的作业工单
        List<String> dealWorkOrders = this.lambdaQuery().select(OrderExecuteSeqEntity::getOrderNumber).list()
                .stream().map(OrderExecuteSeqEntity::getOrderNumber).collect(Collectors.toList());

        //查询所有产线
        List<ProductionLineEntity> list = JacksonUtil.getResponseArray(lineInterface.getLineList(), ProductionLineEntity.class);
        for (ProductionLineEntity lineEntity : list) {
            //查询最后一条，如果是占用计数器的状态，则取其工单执行新方法
            ReportCountEntity reportCountEntity = reportCountService.lambdaQuery()
                    .eq(ReportCountEntity::getLineId, lineEntity.getProductionLineId())
                    .orderByDesc(ReportCountEntity::getCreateTime).orderByDesc(ReportCountEntity::getId)
                    .last("limit 1").one();
            if (reportCountEntity == null) {
                continue;
            }
            if (dealWorkOrders.contains(reportCountEntity.getOperationNumber())) {
                continue;
            }
            if (ReportType.activeTypeList.contains(reportCountEntity.getType())) {
                OperationOrderEntity entity = operationService.getByOperationNumberSimple(reportCountEntity.getOperationNumber());
                updateOrderExecuteSeq(entity);
                dealWorkOrders.add(entity.getOperationNumber());
            }
        }

        //查询所有投产工单，排除已经处理了的工单
        List<OperationOrderEntity> operationOrderEntities = operationService.lambdaQuery()
                .eq(OperationOrderEntity::getState, OperationStateEnum.INVESTMENT.getCode())
                .notIn(!CollectionUtils.isEmpty(dealWorkOrders), OperationOrderEntity::getOperationNumber, dealWorkOrders)
                .orderByDesc(OperationOrderEntity::getPlanStartDate)
                .list();
        for (OperationOrderEntity operationOrderEntity : operationOrderEntities) {
            updateOrderExecuteSeq(operationOrderEntity);
        }
    }
}
