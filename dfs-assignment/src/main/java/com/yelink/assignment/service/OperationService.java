package com.yelink.assignment.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.common.BillVo;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderProcedureRelationEntity;
import com.yelink.assignment.entity.operation.dto.OperationOrderSelectDTO;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;

import java.util.Date;
import java.util.List;


/**
 * Service
 *
 * <AUTHOR>
 * @Date
 */
public interface OperationService extends IService<OperationOrderEntity> {


    /**
     * 查询作业工单列表
     *
     * @param operationOrderDTO
     * @return
     */
    Page<OperationOrderEntity> getOperationOrderList(OperationOrderSelectDTO operationOrderDTO);

    /**
     * 通过生产工单号查询作业工单
     *
     * @param workOrder 工单号
     * @return
     */
    List<OperationOrderEntity> getByWorkOrder(String workOrder);

    /**
     * 作业工单号查询作业工单
     *
     * @param operationOrder
     * @return
     */
    OperationOrderEntity getByOperationNumber(String operationOrder);

    /**
     * 作业工单号查询作业工单（只查询作业工单表）
     *
     * @param operationOrder
     * @return
     */
    OperationOrderEntity getByOperationNumberSimple(String operationOrder);

    /**
     * 作业工单表中只有物料code, 填充作业工单物料信息
     *
     * @param records 要填充的作业工单
     */
    void setOperationOrderMaterialInfo(List<OperationOrderEntity> records);

    /**
     * 通过产线查询正在运行的工单
     *
     * @param lineIds
     * @return
     */
    List<OperationOrderEntity> getRunningOperationByLineIds(String lineIds);

    /**
     * 正在投产作业工单
     *
     * @return
     */
    List<OperationOrderEntity> getRunningOperations();

    /**
     * 通过状态查询作业工单
     *
     * @param states
     * @return
     */
    List<OperationOrderEntity> getByStates(List<Integer> states);

    /**
     * 新增作业工单
     *
     * @param entity
     * @return
     */
    OperationOrderEntity insertOperationOrder(OperationOrderEntity entity);

    /**
     * 更新作业工单
     *
     * @param entity
     * @return
     */
    OperationOrderEntity edit(OperationOrderEntity entity);

    /**
     * 设置实际开始和时间结束时间
     *
     * @param entity
     */
    void setStartEndDate(OperationOrderEntity entity);

    /**
     * 查询报工记录
     *
     * @param operationNumber
     * @param type
     * @param current
     * @param size
     * @return
     */
    Page getInputHistory(String operationNumber, String type, Integer current, Integer size);

    /**
     * 删除作业工单
     *
     * @param operationNumber 作业工单编号
     * @return
     */
    void delete(String operationNumber);

    /**
     * 获取工单今日正在投产的作业工单
     *
     * @param workOrderNumber
     * @return
     */
    List<OperationOrderEntity> listInWork(String workOrderNumber);

    /**
     * 更新报工记录
     * reportLineEntity
     *
     * @return
     */
    ReportLineEntity updateInputHistory(ReportLineEntity reportLineEntity);

    /**
     * 新增报工记录
     *
     * @param entity
     * @return
     */
    ReportLineEntity addHistoryCount(ReportLineEntity entity);

    void addOperationProcedureRelation(List<OperationOrderProcedureRelationEntity> entities);

    Double getPlanQuantitySum(String workOrderNumber);

    /**
     * 作业工单新增前的判断：
     * 1、生产工单关联的作业工单的数量之和(包括当前的作业工单) > 生产工单的计划数量，给前端提示
     *
     * @param entity 作业工单
     * @return
     */
    List<BillVo> judgeBeforeInsert(OperationOrderEntity entity);

    /**
     * 刷新工单回调接口
     *
     * @param workOrderNumber
     */
    void refreshProcedureCountCallBack(String workOrderNumber);

    /**
     * 按工单统计指定日期完成数
     *
     * @param date
     * @return
     */
    List<OperationOrderEntity> getFinishCountGroupByWorkOrder(Date date);
    /**
     * 工单状态批量编辑
     */
    Boolean batchUpdateState(BatchChangeStateDTO batchApprovalDTO, String username);

    /**
     * 构建作业工单 wrapper
     * @param wrapper 查询参数
     * @param workCenterId 工作中心id(多个逗号分割)
     * @param procedureIds 工序id(多个逗号分割)
     */
    void buildWrapper(LambdaQueryWrapper<OperationOrderEntity> wrapper, String workCenterId, String procedureIds);
}
