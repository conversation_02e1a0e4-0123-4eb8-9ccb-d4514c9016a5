package com.yelink.assignment.service.impl;

import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.constant.ReportType;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OrderExecuteSeqEntity;
import com.yelink.assignment.entity.operation.dto.OperationOrderTaskListDTO;
import com.yelink.assignment.entity.record.InputRecordEntity;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.provider.constant.KafkaMessageTypeEnum;
import com.yelink.assignment.request.RequestService;
import com.yelink.assignment.service.InputRecordService;
import com.yelink.assignment.service.MemoryCalApplyService;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.OrderExecuteSeqService;
import com.yelink.assignment.service.ReportCountService;
import com.yelink.assignment.service.ReportLineService;
import com.yelink.dfscommon.api.control.ControlInterface;
import com.yelink.dfscommon.api.dfs.WorkOrderInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.entity.dfs.FacCalEuiEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toList;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2022/3/24
 */
@Slf4j
@Service
@AllArgsConstructor
public class MemoryCalApplyServiceImpl implements MemoryCalApplyService {

    private RedissonClient redissonClient;
    private ReportCountService reportCountService;
    private RequestService requestService;
    private OperationService operationService;
    private ReportLineService reportLineService;
    private WorkOrderInterface workOrderInterface;
    private InputRecordService inputRecordService;
    private MessagePushToKafkaService messagePushToKafkaService;
    private ControlInterface controlInterface;
    private RedisTemplate redisTemplate;
    private OrderExecuteSeqService orderExecuteSeqService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportLineMemoryCal(String eui, Integer countId, Double count, FacCalEuiEntity entity) {
        //查询正在报工的工单
        List<OrderExecuteSeqEntity> orderExecuteSeqEntities;

        Boolean isCalConcurrently = requestService.getIsCalConcurrently(entity.getFid());
        if (entity.getDeviceId() != null) {
            if (isCalConcurrently) {
                orderExecuteSeqEntities = orderExecuteSeqService
                        .lambdaQuery().eq(OrderExecuteSeqEntity::getDeviceId, entity.getDeviceId())
                        .list();
            } else {
                orderExecuteSeqEntities = orderExecuteSeqService
                        .lambdaQuery().eq(OrderExecuteSeqEntity::getDeviceId, entity.getDeviceId())
                        .orderByAsc(OrderExecuteSeqEntity::getId)
                        .last("limit 1").list();
            }
        } else {
            if (isCalConcurrently) {
                orderExecuteSeqEntities = orderExecuteSeqService
                        .lambdaQuery().eq(OrderExecuteSeqEntity::getLineId, entity.getLineId())
                        .list();
            } else {
                orderExecuteSeqEntities = orderExecuteSeqService
                        .lambdaQuery().eq(OrderExecuteSeqEntity::getLineId, entity.getLineId())
                        .orderByAsc(OrderExecuteSeqEntity::getId)
                        .last("limit 1").list();
            }
        }

        //处理多个作业工单
        for (OrderExecuteSeqEntity executeSeqEntity : orderExecuteSeqEntities) {
            dealMultiWorkOrder(count, executeSeqEntity.getOrderNumber());
        }

    }

    private void dealMultiWorkOrder(double count, String operationOrder) {
        RLock lock = redissonClient.getLock(RedisKeyPrefix.MEMORY_CAL_WORK_ORDER + operationOrder);
        try {
            int waitTime = 30;
            int leaseTime = 30;
            if (lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS)) {
                //只有一个线程能该操作该工单（绑定多个计数器的情况）
                dealWorkOrder(count, operationOrder);
                //解锁
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.error("", e);
        }
    }


    private void dealWorkOrder(double count, String operationNumber) {
        Date now = new Date();
        Date recordDate = requestService.getRecordDate(now);
        OperationOrderEntity operation = operationService.getByOperationNumberSimple(operationNumber);

        List<ReportLineEntity> reportLineList = reportLineService.getByOperationNumber(operationNumber);
        List<ReportLineEntity> autoCollect = reportLineList.stream().filter(o -> o.getType().equals(ReportType.AUTO.getType())).collect(toList());
        ReportLineEntity autoRecord = autoCollect.get(0);
        // 自动报工 产量*报工系数
        if (operation.getCoefficient() != null) {
            count = count * operation.getCoefficient();
        }
        double sum = reportLineList.stream().filter(o -> o.getType().equals(ReportType.REPORT.getType()) && o.getFinishCount() != null)
                .mapToDouble(ReportLineEntity::getFinishCount).sum();

        boolean success;
        double totalAutoCount;
        if (autoRecord != null) {
            //更新自动报工
            autoRecord.setReportDate(recordDate);
            autoRecord.setReportTime(now);
            totalAutoCount = autoRecord.getFinishCount() + count;
            success = reportLineService.updateReportLineByProgress(autoRecord, totalAutoCount, now) > 0;
            /*success = reportLineService.lambdaUpdate()
                    .eq(ReportLineEntity::getId, autoRecord.getId())
                    .set(ReportLineEntity::getReportDate, recordDate)
                    .set(ReportLineEntity::getAutoCountRecordTime, now)
                    .set(ReportLineEntity::getReportTime, now)
                    .set(ReportLineEntity::getFinishCount, totalAutoCount)
                    .update();*/
        } else {
            totalAutoCount = count;
            autoRecord = reportLineService.addAutoReportLine(operation, count, now);
            success = true;
        }
        //更新计数器记录id，暂时不存
        //requestService.saveOrUpdateEuiMark(eui, countId, new Date(), autoRecord.getId(), Constant.REPORT_LINE);

        if (!success) {
            return;
        }

        //更新自动报工记录成功，更新工单
        OperationOrderEntity build = OperationOrderEntity.builder().id(operation.getId()).finishCount(totalAutoCount + sum).build();
        operationService.updateById(build);

        operation.setFinishCount(build.getFinishCount());

        //使用redis回调更新工单每日完成量
        redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.UPDATE_OPERATION_DAY_COUNT_BY_COUNTER + operation.getOperationNumber(), 1, 30, TimeUnit.SECONDS);
        //更新工单每日完成量
//        operationDayCountService.updateWorkOrderDayCount(operation);

        //使用redis回调通知工单刷新产量
        redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.REFRESH_WORK_ORDER + operation.getWorkOrderNum(), 1, 30, TimeUnit.SECONDS);
//        workOrderInterface.refreshProcedureCount(operation.getWorkOrderNum());

        //判断是否自动完工
        if (operation.getIsAutoCompleted()) {
            //判断当前产量是否超过计划数量
            if (build.getFinishCount() >= operation.getProductCount()) {
                // 新增一条报工记录
                Date time = new Date();
                Date reportDate = requestService.getRecordDate(now);
                Double historyCount = reportCountService.getHistoryFinishCount(operationNumber);
                // 获取创建该连续生产任务的人
                OperationOrderTaskListDTO taskListDTO = JacksonUtil.getResponseObject(controlInterface.getTaskEntity(operationNumber), OperationOrderTaskListDTO.class);
                String username = null;
                if (!Objects.isNull(taskListDTO)) {
                    username = taskListDTO.getCreateBy();
                }
                ReportLineEntity entity = ReportLineEntity.builder()
                        .finishCount(historyCount)
                        .operationNumber(operationNumber)
                        .reportTime(time)
                        .userName(username)
                        .reportDate(reportDate)
                        .build();
                reportCountService.countOutput(entity);

                //超过排产数量后自动完成
                reportCountService.dealReportCountRecord(operationNumber, Constant.END, Constant.AUTO);

                //如果工单状态变更的时候，则发布状态变更消息消息
                OperationOrderEntity simple = operationService.getByOperationNumberSimple(operationNumber);
                messagePushToKafkaService.pushNewMessage(simple, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.OPERATION_ORDER_AUTO_COMPLETE_MESSAGE);
            }
        }
    }


    @Override
    public void inputRecordMemoryCal(String eui, Integer countId, Double count, Integer lineId) {
        //查询正在报工的工单
        InputRecordEntity inputRecordEntity = inputRecordService.getLastRecordByLineId(lineId);

        // 如果无记录或者最新一条不是投产状态，则直接返回
        if (inputRecordEntity == null || !inputRecordEntity.getType().equals(ReportType.INVESTMENT.getType())) {
            return;
        }
        String operationNumber = inputRecordEntity.getOperationNumber();

        RLock lock = redissonClient.getLock(RedisKeyPrefix.MEMORY_CAL_INPUT_WORK_ORDER + operationNumber);
        try {
            int waitTime = 3;
            int leaseTime = 3;
            if (lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS)) {
                //只有一个线程能该操作该工单（绑定多个计数器的情况）
                dealInput(eui, countId, count, operationNumber, inputRecordEntity.getId());
                //解锁
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            //解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            log.error("", e);
        }
    }

    private void dealInput(String eui, Integer countId, double count, String workOrderNumber, Integer inputRecordId) {
        OperationOrderEntity operation = operationService.getByOperationNumberSimple(workOrderNumber);
        Double inputTotal = operation.getInputTotal();
        inputTotal = inputTotal == null ? 0 : inputTotal;

        OperationOrderEntity build = OperationOrderEntity.builder().id(operation.getId()).inputTotal(inputTotal + count).build();
        operationService.updateById(build);

        //通知工单刷新投入量
        workOrderInterface.refreshInputCount(operation.getWorkOrderNum());

        requestService.saveOrUpdateEuiMark(eui, countId, new Date(), inputRecordId, Constant.FAC_INPUT_RECORD);
    }

}
