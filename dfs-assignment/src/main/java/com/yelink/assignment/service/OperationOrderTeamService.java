package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderTeamEntity;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-07-20 19:08
 */
@Repository
public interface OperationOrderTeamService extends IService<OperationOrderTeamEntity> {

    /**
     * 保存工单投产班组成员信息
     *
     * @param operationOrderEntity
     */
    void add(OperationOrderEntity operationOrderEntity);

    /**
     * 根据工单号查询班组成员信息
     *
     * @param operationOrderNumber
     * @return
     */
    List<OperationOrderTeamEntity> getOperationOrderTeams(String operationOrderNumber);
}
