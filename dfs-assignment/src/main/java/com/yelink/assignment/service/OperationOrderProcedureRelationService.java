package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.operation.OperationOrderProcedureRelationEntity;

import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2022/4/27 21:25
 */
public interface OperationOrderProcedureRelationService extends IService<OperationOrderProcedureRelationEntity> {

    /**
     * 根据工艺工序名称 模糊过滤获取作业工单号
     *
     * @param craftName
     * @return
     */
    List<String> getOrdersByProcedureName(String craftName);

    String getCraftNameByNumber(String operationNumber);
}
