package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.entity.operation.OperationDayCountEntity;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.mapper.OperationDayCountMapper;
import com.yelink.assignment.mapper.OperationOrderMapper;
import com.yelink.assignment.request.RequestService;
import com.yelink.assignment.service.OperationDayCountService;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationDayCountServiceImpl extends ServiceImpl<OperationDayCountMapper, OperationDayCountEntity> implements OperationDayCountService {

    private OperationDayCountMapper operationDayCountMapper;
    private OperationOrderMapper operationOrderMapper;
    private RequestService requestService;
    private RedisTemplate redisTemplate;

    /**
     * 更新工单每日完成量
     *
     * @param
     */
    @Override
    public void updateWorkOrderDayCount(OperationOrderEntity operationOrder) {
        Double finishCount = operationOrder.getFinishCount();
        Date recordDate = requestService.getRecordDate(new Date());
        String workOrder = operationOrder.getOperationNumber();
        //首先获取今天之前的记录，将总数求和
        QueryWrapper<OperationDayCountEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OperationDayCountEntity::getOperationNumber, workOrder)
                .lt(OperationDayCountEntity::getTime, recordDate);
        List<OperationDayCountEntity> countEntities = operationDayCountMapper.selectList(wrapper);
        double sum = 0.0;
        double unqualifiedSum = 0.0;
        if (!CollectionUtils.isEmpty(countEntities)) {
            DoubleSummaryStatistics doubleSummaryStatistics = countEntities.stream().mapToDouble(OperationDayCountEntity::getCount).summaryStatistics();
            sum = doubleSummaryStatistics.getSum();
            DoubleSummaryStatistics unqualifiedDoubleSummaryStatistics = countEntities.stream().mapToDouble(OperationDayCountEntity::getUnqualified).summaryStatistics();
            unqualifiedSum = unqualifiedDoubleSummaryStatistics.getSum();
        }
        //用完成数量减去今天之前的完成数，求出今天的数量
        double todayCount = finishCount - sum;
        Double unqualified = operationOrder.getUnqualified();
        double todayUnqualified = unqualified == null ? 0.0 : unqualified - unqualifiedSum;

        saveOrUpdateOperationDayCount(operationOrder, todayCount, todayUnqualified, recordDate);
    }

    /**
     * 指定日期上报工单每日完成量
     *
     * @param
     */
    @Override
    public void saveOrUpdateOperationDayCount(OperationOrderEntity operationOrderEntity, Double count, Double unqualified, Date date) {
        UpdateWrapper<OperationDayCountEntity> qw = new UpdateWrapper<>();
        qw.lambda().eq(OperationDayCountEntity::getOperationNumber, operationOrderEntity.getOperationNumber())
                .eq(OperationDayCountEntity::getTime, date);

        this.saveOrUpdate(
                OperationDayCountEntity.builder()
                        .operationNumber(operationOrderEntity.getOperationNumber()).count(count)
                        .unqualified(unqualified).lineId(operationOrderEntity.getLineId())
                        .materialCode(operationOrderEntity.getMaterialCode()).time(date).build(),
                qw
        );

    }

    /**
     * 获取作业工单当日完成量
     *
     * @param operationNumber
     * @return
     */
    @Override
    public Double getDayFinishCount(String operationNumber) {
        Date recordDate = requestService.getRecordDate(new Date());
        LambdaQueryWrapper<OperationDayCountEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationDayCountEntity::getTime, recordDate);
        lambdaQueryWrapper.eq(OperationDayCountEntity::getOperationNumber, operationNumber);
        List<OperationDayCountEntity> list = this.list(lambdaQueryWrapper);
        return list.stream().filter(o -> o.getCount() != null).mapToDouble(OperationDayCountEntity::getCount).sum();
    }

    @Override
    public List<OperationDayCountEntity> getReportList(String operationNumber) {
        LambdaQueryWrapper<OperationDayCountEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationDayCountEntity::getOperationNumber, operationNumber);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 计数器回调计算工单每日完成数
     *
     * @param
     */
    @Override
    public void updateByOperationNumber(String operationNumber) {
        String key = RedisKeyPrefix.OPERATION_DAY_COUNT_CALL_BACK_LOCK + operationNumber;
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, 0, 1, TimeUnit.MINUTES);
        if (lock == null || !lock) {
            return;
        }
        LambdaQueryWrapper<OperationOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationOrderEntity::getOperationNumber, operationNumber);
        OperationOrderEntity entity = operationOrderMapper.selectOne(wrapper);
        updateWorkOrderDayCount(entity);
        redisTemplate.delete(key);
    }
}



