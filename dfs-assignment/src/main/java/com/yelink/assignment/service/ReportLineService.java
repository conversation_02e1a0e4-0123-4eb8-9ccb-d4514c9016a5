package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.common.CommonType;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.entity.reporter.excel.ReporterRecordExcelVO;
import com.yelink.assignment.entity.reporter.vo.ReporterRecordVO;
import com.yelink.dfscommon.common.service.ExportDataService;
import com.yelink.dfscommon.dto.OperationDayCountDTO;
import com.yelink.dfscommon.dto.ReporterRecordDTO;

import java.io.IOException;
import java.util.Date;
import java.util.List;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2022/1/16
 */
public interface ReportLineService extends IService<ReportLineEntity> , ExportDataService {
    /**
     * 通过工单号获取
     *
     * @param operationNumber
     * @return
     */
    List<ReportLineEntity> getByOperationNumber(String operationNumber);

    /**
     * 插入自动报工记录
     *
     * @param entity
     * @param count
     * @param now
     * @return
     */
    ReportLineEntity addAutoReportLine(OperationOrderEntity entity, double count, Date now);

    /**
     * 更新记录
     *
     * @param entity
     * @param finishCount
     * @param now
     * @return
     */
    int updateReportLineByProgress(ReportLineEntity entity, Double finishCount, Date now);

    /**
     * 按人员统计产出
     *
     * @param modelId
     * @param date
     * @return
     */
    List<ReportLineEntity> getFinishCountGroupByUser(Integer modelId, Date date);

    /**
     * 按产线统计完成数
     *  @param modelId
     * @param date
     * @return
     */
    List<OperationDayCountDTO> getFinishCountGroupByLine(Integer modelId, Date date);

    /**
     * 查询作业工单报工记录
     * @param dto
     * @return
     */
    Page<ReporterRecordVO> getReporterRecord(ReporterRecordDTO dto);

    /**
     * 获取作业工单的产线数量报工表的所有上报方式
     * <AUTHOR>
     */
    List<CommonType> getAllReportType();


    /**
     * 条件查询所有数据
     * @param dto
     * @return
     */
    List<ReporterRecordExcelVO> listBy(ReporterRecordDTO dto);

    /**
     *
     * @return
     */
    byte[] getExportTemplate() throws IOException;
}
