package com.yelink.assignment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.common.WorkOrderStateEnum;
import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.constant.ReportLineTypeEnum;
import com.yelink.assignment.constant.ReportType;
import com.yelink.assignment.entity.WorkOrderEntity;
import com.yelink.assignment.entity.common.CommonType;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderProcedureRelationEntity;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.entity.reporter.excel.ReporterRecordExcelVO;
import com.yelink.assignment.entity.reporter.vo.ReporterRecordVO;
import com.yelink.assignment.mapper.ReportLineMapper;
import com.yelink.assignment.mapper.UnqualifiedImgMapper;
import com.yelink.assignment.request.RequestService;
import com.yelink.assignment.service.OperationOrderProcedureRelationService;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.ReportLineService;
import com.yelink.dfscommon.api.dfs.InnerUploadInterface;
import com.yelink.dfscommon.api.dfs.MaterialInterface;
import com.yelink.dfscommon.api.dfs.ModelInterface;
import com.yelink.dfscommon.api.dfs.ProductionLineInterface;
import com.yelink.dfscommon.api.dfs.WorkOrderRecordInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.assignment.OperationStateEnum;
import com.yelink.dfscommon.constant.dfs.UploadFileCodeEnum;
import com.yelink.dfscommon.dto.OperationDayCountDTO;
import com.yelink.dfscommon.dto.ReporterRecordDTO;
import com.yelink.dfscommon.entity.dfs.MaterialEntity;
import com.yelink.dfscommon.entity.dfs.ModelEntity;
import com.yelink.dfscommon.entity.dfs.ProductionLineEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-26 18:33
 */
@Service
public class ReportLineServiceImpl extends ServiceImpl<ReportLineMapper, ReportLineEntity> implements ReportLineService {

    @Autowired
    private ReportLineMapper reportLineMapper;
    @Autowired
    private RequestService requestService;
    @Autowired
    private OperationService operationService;
    @Resource
    private WorkOrderRecordInterface workOrderRecordInterface;
    @Resource
    private OperationOrderProcedureRelationService operationOrderProcedureRelationService;
    @Resource
    private MaterialInterface materialInterface;
    @Resource
    private UnqualifiedImgMapper unqualifiedImgMapper;
    @Autowired
    private InnerUploadInterface innerUploadInterface;
    @Autowired
    private ModelInterface modelInterface;
    @Autowired
    private ProductionLineInterface lineInterface;

    @Override
    public List<ReportLineEntity> getByOperationNumber(String operationNumber) {
        LambdaQueryWrapper<ReportLineEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReportLineEntity::getOperationNumber, operationNumber);
        return list(queryWrapper);
    }

    @Override
    public ReportLineEntity addAutoReportLine(OperationOrderEntity entity, double count, Date now) {
        ReportLineEntity build = ReportLineEntity.builder()
                .type(ReportType.AUTO.getType())
                .lineId(entity.getLineId())
                .operationNumber(entity.getOperationNumber())
                .finishCount(count)
                .reportDate(requestService.getRecordDate(now))
                .reportTime(now)
                .createTime(now)
                .updateTime(now)
                .build();
        reportLineMapper.insert(build);
        return build;
    }

    @Override
    public int updateReportLineByProgress(ReportLineEntity entity, Double finishCount, Date now) {
        return reportLineMapper.updateReportLineByProgress(entity, finishCount, now);
    }

    /**
     * 按人员统计产出
     *
     * @param modelId
     * @param date
     * @return
     */
    @Override
    public List<ReportLineEntity> getFinishCountGroupByUser(Integer modelId, Date date) {
        return this.baseMapper.getFinishCountGroupByUser(modelId, date);
    }

    @Override
    public List<OperationDayCountDTO> getFinishCountGroupByLine(Integer modelId, Date date) {
        return this.baseMapper.getFinishCountGroupByLine(modelId, date);
    }

    @Override
    public Page<ReporterRecordVO> getReporterRecord(ReporterRecordDTO dto) {
        if (dto == null) {
            dto = new ReporterRecordDTO();
        }
        // 1、返回结果
        List<ReporterRecordVO> resultList = new LinkedList<>();
        // 3、获取生产工单列表
        List<WorkOrderEntity> orderEntityList = JacksonUtil.getResponseArray(workOrderRecordInterface.getWorkOrderListByDto(dto), WorkOrderEntity.class);
        if (CollectionUtils.isEmpty(orderEntityList)) {
            return new Page<>();
        }
        Map<String, WorkOrderEntity> workOrderMap = orderEntityList.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, o -> o));

        // 4、作业工单表
        LambdaQueryWrapper<OperationOrderEntity> ooeWrapper = new LambdaQueryWrapper<>();
        operationService.buildWrapper(ooeWrapper, dto.getWorkCenterId(), dto.getProcedureIds());
        WrapperUtil.like(ooeWrapper, OperationOrderEntity::getOperationNumber, dto.getOperationNumber());
        // 1、物料表 - 模糊查询物料名称
        if (StringUtils.isNotBlank(dto.getMaterialName())) {
            dto.getMaterialFields().setName(dto.getMaterialName());
        }
        // 物料字段过滤
        if (!FieldUtil.objectIsNull(dto.getMaterialFields())) {
            ResponseData responseData = materialInterface.materialCodesByMaterialFields(dto.getMaterialFields());
            List<String> materialCodes = JSON.parseArray(JSON.toJSONString(responseData.getData()), String.class);
            if (CollectionUtils.isEmpty(materialCodes)) {
                return new Page<>();
            }
            ooeWrapper.in(OperationOrderEntity::getMaterialCode, materialCodes);
        }

        if (StringUtils.isNotBlank(dto.getCraftName())) {
            List<String> operationOrders = operationOrderProcedureRelationService.getOrdersByProcedureName(dto.getCraftName());
            if (CollectionUtils.isEmpty(operationOrders)) {
                return new Page<>();
            }
            ooeWrapper.in(OperationOrderEntity::getOperationNumber, operationOrders);
        }
        if (StringUtils.isNotBlank(dto.getStartDateStart()) || StringUtils.isNotBlank(dto.getStartDateEnd())) {
            ooeWrapper.between(OperationOrderEntity::getPlanStartDate, dto.getStartDateStart(), dto.getStartDateEnd());
        }
        if (StringUtils.isNotBlank(dto.getEndDateStart()) || StringUtils.isNotBlank(dto.getEndDateEnd())) {
            ooeWrapper.between(OperationOrderEntity::getPlanEndDate, dto.getEndDateStart(), dto.getEndDateEnd());
        }
        if (StringUtils.isNotBlank(dto.getActualStartDateStart()) || StringUtils.isNotBlank(dto.getActualStartDateEnd())) {
            ooeWrapper.between(OperationOrderEntity::getActualStartDate, dto.getActualStartDateStart(), dto.getActualStartDateEnd());
        }
        if (StringUtils.isNotBlank(dto.getActualEndDateStart()) || StringUtils.isNotBlank(dto.getActualEndDateEnd())) {
            ooeWrapper.between(OperationOrderEntity::getActualEndDate, dto.getActualEndDateStart(), dto.getActualEndDateEnd());
        }
        ooeWrapper.like(StringUtils.isNotBlank(dto.getLineName()), OperationOrderEntity::getLineName, dto.getLineName());
//        ooeWrapper.like(StringUtils.isNotBlank(dto.getLineModelName()), OperationOrderEntity::getWorkCenterName, dto.getLineModelName());
        // 4.1 作业工单表根据生产工单编号筛选出作业工单列表
        ooeWrapper.in(OperationOrderEntity::getWorkOrderNum, workOrderMap.keySet());
        // 制造单元名称
        if (StringUtils.isNotBlank(dto.getLineNames())) {
            List<String> names = Arrays.asList(dto.getLineNames().split(Constant.SEP));
            ooeWrapper.in(OperationOrderEntity::getLineName, names);
        }
        // 制造单元类型
        if (StringUtils.isNotBlank(dto.getLineModelNames())) {
            List<String> lineModelNames = Arrays.asList(dto.getLineModelNames().split(Constants.SEP));
            List<ModelEntity> modelEntities = JacksonUtil.getResponseArray(modelInterface.getByLineModelNames(lineModelNames), ModelEntity.class);
            List<Integer> lineModelIds = modelEntities.stream().map(ModelEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lineModelIds)) {
                ooeWrapper.isNull(OperationOrderEntity::getOperationNumber);
            } else {
                // 根据制造单元类型查询制造单元
                List<ProductionLineEntity> lineEntities = JacksonUtil.getResponseArray(lineInterface.getProductLine(), ProductionLineEntity.class)
                        .stream().filter(o -> lineModelIds.contains(o.getModelId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(lineEntities)) {
                    ooeWrapper.isNull(OperationOrderEntity::getOperationNumber);
                }
                List<Integer> lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
                ooeWrapper.in(OperationOrderEntity::getLineId, lineIds);
            }
        }
        // ooeWrapper.select(OperationOrderEntity::getId,OperationOrderEntity::getOperationNumber,OperationOrderEntity::getWorkOrderNum);
        List<OperationOrderEntity> operationOrderEntityList = operationService.list(ooeWrapper);
        if (CollectionUtils.isEmpty(operationOrderEntityList)) {
            return new Page<>();
        }
        // 作业工单map<作业工单编号，生产工单编号>
        Map<String, String> operationOrderMap = operationOrderEntityList.stream().collect(Collectors.toMap(OperationOrderEntity::getOperationNumber, OperationOrderEntity::getWorkOrderNum));
        // 作业工单map<作业工单编号，作业工单实体>
        Map<String, OperationOrderEntity> operationOrderListMap = operationOrderEntityList.stream().collect(Collectors.toMap(OperationOrderEntity::getOperationNumber, r -> r));
        List<OperationOrderProcedureRelationEntity> allOrderProcedureRelations = operationOrderProcedureRelationService.lambdaQuery().in(OperationOrderProcedureRelationEntity::getOperationOrderNumber, operationOrderMap.keySet()).list();
        Map<String, List<OperationOrderProcedureRelationEntity>> operationNumberMap = allOrderProcedureRelations.stream().collect(Collectors.groupingBy(OperationOrderProcedureRelationEntity::getOperationOrderNumber));
        // 5、作业工单的报工表
        LambdaQueryWrapper<ReportLineEntity> rleWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getType())) {
            List<String> type = Arrays.stream(dto.getType().split(Constant.SEP)).collect(Collectors.toList());
            rleWrapper.in(ReportLineEntity::getType, type);
        }
        WrapperUtil.like(rleWrapper, ReportLineEntity::getBatch, dto.getBatch());
        WrapperUtil.like(rleWrapper, ReportLineEntity::getVehicleCode, dto.getVehicleCode());
        WrapperUtil.like(rleWrapper, ReportLineEntity::getShiftType, dto.getShiftType());
        WrapperUtil.like(rleWrapper, ReportLineEntity::getDefectDesc, dto.getDefectDesc());
        WrapperUtil.like(rleWrapper, ReportLineEntity::getOperator, dto.getOperator());
        WrapperUtil.like(rleWrapper, ReportLineEntity::getUserNickname, dto.getUserNickname());
        WrapperUtil.between(rleWrapper, ReportLineEntity::getReportDate, dto.getReportDateStartDate(), dto.getReportDateEndDate());
        WrapperUtil.between(rleWrapper, ReportLineEntity::getCreateTime, dto.getCreateTimeStartDate(), dto.getCreateTimeEndDate());
        // 作业工单报工记录隐藏自动报工记录（完成数量为0，不合格数量为0或空）
        rleWrapper.apply("id not in (SELECT id FROM `dfs_report_line` WHERE `type` = 'auto' \n" +
                "AND `finish_count` = 0 and id in (SELECT id FROM `dfs_report_line` \n" +
                "WHERE `unqualified` IS NULL or `unqualified` = 0))");
        rleWrapper.orderByDesc(ReportLineEntity::getCreateTime)
                .orderByDesc(ReportLineEntity::getId);
        // 5.1 根据作业工单筛选
        rleWrapper.in(ReportLineEntity::getOperationNumber, operationOrderMap.keySet());
        // 6、分页
        Page<ReportLineEntity> page = new Page<>();
        if (null == dto.getCurrent() || null == dto.getSize()) {
            List<ReportLineEntity> list = this.list(rleWrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = this.page(new Page<>(dto.getCurrent(), dto.getSize()), rleWrapper);
        }
        // 7、组装数据
        List<ReportLineEntity> records = page.getRecords();
        for (ReportLineEntity record : records) {
            List<String> urlList = unqualifiedImgMapper.selectUrlList(record.getId());
            record.setPicUrls(urlList.toArray(new String[urlList.size()]));
            String operationNumber = record.getOperationNumber();
            List<OperationOrderProcedureRelationEntity> orderProcedureRelations = operationNumberMap.getOrDefault(operationNumber, Collections.emptyList());
            String workOrderNumber = operationOrderMap.get(operationNumber);
            WorkOrderEntity workOrderEntity = workOrderMap.get(workOrderNumber);
            String materialCode = workOrderEntity.getMaterialCode();
            ResponseData responseData = materialInterface.getMaterialEntityByCode(materialCode);
            MaterialEntity materialEntity = JacksonUtil.getResponseObject(responseData, MaterialEntity.class);
            OperationOrderEntity operationOrderEntity = operationOrderListMap.get(operationNumber);
            ReporterRecordVO vo = ReporterRecordVO.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                    .state(workOrderEntity.getState() == null ? null : WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()))
                    .assignmentState(workOrderEntity.getState() == null ? null : OperationStateEnum.getNameByCode(operationOrderEntity.getState()))
                    .materialCode(materialCode)
                    .materialFields(materialEntity)
                    .planQuantity(workOrderEntity.getPlanQuantity())
                    .craftName(operationOrderProcedureRelationService.getCraftNameByNumber(operationNumber))
                    .lineModelName(getLineModelName(workOrderEntity.getLineName()))
                    .lineName(operationOrderEntity.getLineName())
                    // 计划开始时间
                    .startDate(operationOrderEntity.getPlanStartDate())
                    // 计划完成时间
                    .endDate(operationOrderEntity.getPlanEndDate())
                    .actualStartDate(operationOrderEntity.getActualStartDate())
                    .actualEndDate(operationOrderEntity.getActualEndDate())
                    .workCenterId(operationOrderEntity.getWorkCenterId())
                    .workCenterName(operationOrderEntity.getWorkCenterName())
                    .procedureName(orderProcedureRelations.stream().map(OperationOrderProcedureRelationEntity::getProcedureName).filter(Objects::nonNull).collect(Collectors.joining(Constant.SEP)))
                    .inputTotal(workOrderEntity.getInputTotal())
                    .finishCount(workOrderEntity.getFinishCount())
                    .unqualified(workOrderEntity.getUnqualified())
                    .plannedWorkingHours(workOrderEntity.getPlannedWorkingHours())
                    .actualWorkingHours(workOrderEntity.getActualWorkingHours())
                    .circulationDuration(workOrderEntity.getCirculationDuration())
                    .orderNumber(workOrderEntity.getProductOrderNumber())
                    .saleOrderNumber(workOrderEntity.getSaleOrderNumber())
                    .type(record.getType() == null ? null : ReportLineTypeEnum.getNameByType(record.getType()))
                    .batch(record.getBatch())
                    .vehicleCode(record.getVehicleCode())
                    .shiftType(record.getShiftType())
                    .finishCountTwo(record.getFinishCount())
                    .unqualifiedTwo(record.getUnqualified())
                    .defectDesc(record.getDefectDesc())
                    .reportDate(record.getReportDate())
                    .operator(record.getOperatorName())
                    .userNickname(record.getUserNickname())
                    .createTime(record.getCreateTime())
                    .operationNumber(operationNumber)
                    .customer(record.getCustomerName())
                    .picUrls(record.getPicUrls())
                    .effectiveHours(record.getEffectiveHours() == null ? 0.0 : record.getEffectiveHours())
                    // 物料相关字段
                    .code(materialEntity == null ? null : materialEntity.getCode())
                    .unit(materialEntity == null ? null : materialEntity.getComp())
                    .drawingNumber(materialEntity == null ? null : materialEntity.getDrawingNumber())
                    .standard(materialEntity == null ? null : materialEntity.getStandard())
                    .rawMaterial(materialEntity == null ? null : materialEntity.getRawMaterial())
                    .build();
            resultList.add(vo);
        }
        // 8、返回结果
        Page<ReporterRecordVO> result = new Page<>();
        result.setRecords(resultList);
        result.setCurrent(page.getCurrent());
        result.setTotal(page.getTotal());
        result.setPages(page.getPages());
        result.setSize(page.getSize());
        return result;
    }

    private String getLineModelName(String lineName) {
        if (StringUtils.isBlank(lineName)) {
            return null;
        }
        ProductionLineEntity lineEntity = JacksonUtil.getResponseObject(lineInterface.getProductLineByName(lineName), ProductionLineEntity.class);
        ModelEntity lineModelEntity = Objects.isNull(lineEntity) ? null : JacksonUtil.getResponseObject(modelInterface.getById(lineEntity.getModelId()), ModelEntity.class);
        return Objects.nonNull(lineModelEntity) ? lineModelEntity.getName() : null;
    }

    @Override
    public List<CommonType> getAllReportType() {
        ReportLineTypeEnum[] values = ReportLineTypeEnum.values();
        List<CommonType> types = new ArrayList<>();
        for (ReportLineTypeEnum en : values) {
            types.add(CommonType.builder().type(en.getType()).name(en.getName()).build());
        }
        return types;
    }


    @Override
    public List<ReporterRecordExcelVO> listBy(ReporterRecordDTO dto) {
        List<ReporterRecordVO> voList = getReporterRecord(dto).getRecords();
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }
        // 需要导出的数据
        List<ReporterRecordExcelVO> dataList = new ArrayList<>();
        for (ReporterRecordVO vo : voList) {
            MaterialEntity materialEntity = BeanUtil.copyProperties(vo.getMaterialFields(), MaterialEntity.class);
            dataList.add(ReporterRecordExcelVO.convertToReporterRecordExportDTO(vo, materialEntity));
        }
        return dataList;
    }

    @Override
    public void uploadCustomExportTemplate(MultipartFile file, String username) {
        ExcelUtil.checkImportExcelFile(file);
        // 保存文件到fast-dfs并应用
        innerUploadInterface.uploadReferencedFile(file, UploadFileCodeEnum.REPORT_LINE_TEMPLATE.getCode(), username);
    }

    @Override
    public byte[] downloadCustomExportTemplate(Integer id) {
        ResponseData responseData = innerUploadInterface.downloadFile(UploadFileCodeEnum.REPORT_LINE_TEMPLATE.getCode());
        if (responseData.getData() == null) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_IS_NOT_EXIST);
        }
        return JacksonUtil.getResponseObject(responseData, byte[].class);
    }

    @Override
    public byte[] getExportTemplate() throws IOException {
//        byte[] bytes = downloadCustomExportTemplate(null);
//        if (bytes != null) {
//            return bytes;
//        }
        return downloadDefaultExportTemplate("classpath:template/reportRecordTemplate.xlsx");
    }
}
