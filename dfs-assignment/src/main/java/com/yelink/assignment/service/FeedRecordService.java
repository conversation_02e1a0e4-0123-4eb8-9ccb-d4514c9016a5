package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.feed.FeedRecordEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/29 18:03
 */
public interface FeedRecordService extends IService<FeedRecordEntity> {
    /**
     * 保存移动端上料记录提交的数据
     *
     * @param feedRecordEntity
     */
    void saveFeedRecord(FeedRecordEntity feedRecordEntity);

    /**
     * 根据作业工单号，查询对应的上料记录
     *
     * @param operationOrder
     * @return
     */
    List<FeedRecordEntity> getRecordList(String operationOrder);

    /**
     * 删除作业工单的上料记录
     *
     * @param feedRecordId
     * @return
     */
    void removeRecord(Integer feedRecordId);


}
