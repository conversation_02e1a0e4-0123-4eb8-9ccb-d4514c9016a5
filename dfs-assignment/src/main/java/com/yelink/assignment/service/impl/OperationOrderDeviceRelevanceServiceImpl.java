package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.entity.operation.OperationOrderDeviceRelevanceEntity;
import com.yelink.assignment.mapper.OperationOrderDeviceRelevanceMapper;
import com.yelink.assignment.service.OperationOrderDeviceRelevanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2020-12-03 11:14
 */
@Slf4j
@Service
public class OperationOrderDeviceRelevanceServiceImpl extends ServiceImpl<OperationOrderDeviceRelevanceMapper, OperationOrderDeviceRelevanceEntity> implements OperationOrderDeviceRelevanceService {

}
