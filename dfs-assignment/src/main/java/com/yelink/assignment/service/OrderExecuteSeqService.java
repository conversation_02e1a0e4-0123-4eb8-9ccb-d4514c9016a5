package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OrderExecuteSeqEntity;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2022/11/4
 */
public interface OrderExecuteSeqService extends IService<OrderExecuteSeqEntity> {

    /**
     * 更新工单排序
     *
     * @param workOrderEntity
     */
    void updateOrderExecuteSeq(OperationOrderEntity workOrderEntity);

    /**
     * 通过产线获取占有计数器的工单
     *
     * @param lineId
     * @return
     */
    String getOccupyOrderByLineId(Integer lineId);
}
