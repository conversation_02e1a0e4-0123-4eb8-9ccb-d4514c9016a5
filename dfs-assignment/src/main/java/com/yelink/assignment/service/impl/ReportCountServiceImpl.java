package com.yelink.assignment.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.constant.BarCodeStateEnum;
import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.constant.ReportType;
import com.yelink.assignment.entity.UnqualifiedImgEntity;
import com.yelink.assignment.entity.WorkOrderEntity;
import com.yelink.assignment.entity.model.ProductionLineEntity;
import com.yelink.assignment.entity.operation.OperationDayCountEntity;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.dto.LineOccupyDTO;
import com.yelink.assignment.entity.product.MaterialEntity;
import com.yelink.assignment.entity.report.ReportCountEntity;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.entity.report.dto.AutoCountDTO;
import com.yelink.assignment.entity.report.dto.FinishAndReportCountDTO;
import com.yelink.assignment.entity.shift.ShiftEntity;
import com.yelink.assignment.mapper.ReportCountMapper;
import com.yelink.assignment.mapper.ReportLineMapper;
import com.yelink.assignment.mapper.UnqualifiedImgMapper;
import com.yelink.assignment.provider.constant.KafkaMessageTypeEnum;
import com.yelink.assignment.request.RequestService;
import com.yelink.assignment.service.OperationDayCountService;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.OrderExecuteSeqService;
import com.yelink.assignment.service.ReportCountService;
import com.yelink.dfscommon.api.control.ControlInterface;
import com.yelink.dfscommon.api.dfs.BarCodeInterface;
import com.yelink.dfscommon.api.dfs.InnerUploadInterface;
import com.yelink.dfscommon.api.dfs.LineInterface;
import com.yelink.dfscommon.api.dfs.MaterialInterface;
import com.yelink.dfscommon.api.dfs.StaffPieceWorkTimeInterface;
import com.yelink.dfscommon.api.dfs.WorkOrderInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.assignment.OperationStateEnum;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.dto.ReportCountDTO;
import com.yelink.dfscommon.dto.dfs.BarCodeDTO;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-26 18:33
 */
@Slf4j
@Service
public class ReportCountServiceImpl extends ServiceImpl<ReportCountMapper, ReportCountEntity> implements ReportCountService {

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private OperationDayCountService operationDayCountService;
    @Autowired
    @Lazy
    private ReportLineMapper reportLineMapper;
    @Autowired
    private OperationService operationService;
    @Autowired
    private RequestService requestService;
    @Autowired
    private UnqualifiedImgMapper unqualifiedImgMapper;
    @Autowired
    private InnerUploadInterface uploadInterface;
    @Autowired
    private ControlInterface controlInterface;
    @Autowired
    private StaffPieceWorkTimeInterface staffPieceWorkTimeInterface;
    @Autowired
    private BarCodeInterface barCodeInterface;
    @Autowired
    private MaterialInterface materialInterface;
    @Autowired
    private LineInterface lineInterface;
    @Autowired
    private OrderExecuteSeqService orderExecuteSeqService;
    @Autowired
    private MessagePushToKafkaService messagePushToKafkaService;
    @Autowired
    private WorkOrderInterface workOrderInterface;


    @Override
    public AutoCountDTO getAutoCountAndDate(String operationNumber) {

        OperationOrderEntity operationOrder = operationService.getByOperationNumber(operationNumber);
        if (operationOrder == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_NOT_FOUND);
        }

        //1.10.1版本需求，放开报工限制
        //检查采集设备是否被占用
        /*if (isOccupied(line.getProductionLineId())) {
            throw new ResponseException(RespCodeEnum.COUNTER_OCCUPIED);
        }*/

        //查询该工单对应产线最新的一条数据
        //ReportCountEntity bean = getLatestByLine(line.getProductionLineId());

        //检查该工单所属产线能否报工
        /*if (bean != null) {
            String reportingWorkOrderNumber = bean.getOperationNumber();
            if (!reportingWorkOrderNumber.equals(workOrderNumber)) {
                if (bean.getType().equals(ReportType.INVESTMENT.getType()) || bean.getType().equals(ReportType.REPORT.getType())) {
                    throw new ResponseException(RespCodeEnum.REPORT_NOT_ALLOW);
                }
            }
        }*/

        Date now = new Date();
        //计数器记录时间点至当前时间的值
        //Double total = commonService.getAutoCountByLineAndRecord(line);
        Double total = getAutoCountByWorkOrder(operationNumber);
        //计算工单的有效工时的参考值
        Double referenceHours = calculateEffectiveWork(operationOrder, now);

        //计算报工工单相关数据总数
        LambdaQueryWrapper<ReportLineEntity> orderQW = new LambdaQueryWrapper<>();
        orderQW.eq(ReportLineEntity::getOperationNumber, operationNumber);
        List<ReportLineEntity> orderList = reportLineMapper.selectList(orderQW);
        double workOrderEffectiveHours = orderList.stream().filter(reportLineEntity -> reportLineEntity.getEffectiveHours() != null)
                .mapToDouble(ReportLineEntity::getEffectiveHours).sum();
        double workOrderFinishCount = orderList.stream().filter(reportLineEntity -> reportLineEntity.getFinishCount() != null)
                .mapToDouble(ReportLineEntity::getFinishCount).sum();
        double workOrderUnqualified = orderList.stream().filter(reportLineEntity -> reportLineEntity.getUnqualified() != null)
                .mapToDouble(ReportLineEntity::getUnqualified).sum();

        String dateStr = DateUtil.dateStr(new Date());
        //计算报工产线相关数据总数
        LambdaQueryWrapper<OperationDayCountEntity> lineQW = new LambdaQueryWrapper<>();
        lineQW.eq(OperationDayCountEntity::getLineId, operationOrder.getLineId());
        lineQW.like(OperationDayCountEntity::getTime, dateStr);
        List<OperationDayCountEntity> lineList = operationDayCountService.list(lineQW);
        double lineUnqualified = lineList.stream().filter(o -> o.getUnqualified() != null)
                .mapToDouble(OperationDayCountEntity::getUnqualified).sum();
        double lineFinishCount = lineList.stream().filter(o -> o.getCount() != null)
                .mapToDouble(OperationDayCountEntity::getCount).sum();

        //计算今日产线已报有效工时
        LambdaQueryWrapper<ReportLineEntity> orderLineQW = new LambdaQueryWrapper<>();
        orderLineQW.eq(ReportLineEntity::getLineId, operationOrder.getLineId());
        orderLineQW.like(ReportLineEntity::getReportDate, dateStr);
        List<ReportLineEntity> list = reportLineMapper.selectList(orderLineQW);
        double lineEffectiveHours = list.stream().filter(o -> o.getEffectiveHours() != null)
                .mapToDouble(ReportLineEntity::getEffectiveHours).sum();


        return AutoCountDTO.builder()
                .workOrderFinishCount(workOrderFinishCount)
                .workOrderUnqualified(workOrderUnqualified)
                .workOrderEffectiveHours(workOrderEffectiveHours)
                .lineFinishCount(lineFinishCount)
                .lineUnqualified(lineUnqualified)
                .lineEffectiveHours(lineEffectiveHours)
                .autoCount(total)
                .referenceHours(referenceHours)
                .autoCountRecordTime(now)
                .build();
    }

    private Double getAutoCountByWorkOrder(String operationNumber) {
        LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLineEntity::getOperationNumber, operationNumber).eq(ReportLineEntity::getType, ReportType.AUTO.getType());
        List<ReportLineEntity> reportLineEntities = reportLineMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(reportLineEntities)) {
            return 0.0;
        }
        return reportLineEntities.get(0).getFinishCount();
    }

    private Double calculateEffectiveWork(OperationOrderEntity operationOrder, Date now) {
        //查询该工单对应产线最新的一条上报数据
        QueryWrapper<ReportLineEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(ReportLineEntity::getOperationNumber, operationOrder.getOperationNumber())
                .orderByDesc(ReportLineEntity::getCreateTime)
                .orderByDesc(ReportLineEntity::getId)
                .last("limit 1");
        ReportLineEntity entity = reportLineMapper.selectOne(qw);
        if (entity == null) {
            //情况1：如果是第一次报工，则取投产的时间到现在的时间（取小时数）。
            return DateUtil.getDifferenceHour(operationOrder.getActualStartDate(), now);
        } else {
            //情况2：前次报工到此次点击报工的时间段的时间（取小时数）。
            return DateUtil.getDifferenceHour(entity.getCreateTime(), now);
        }
    }

    /**
     * 报工---输入产线工单产量，输入一条记录一条，每条都有效
     * 工单、产线、日期
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void countOutput(ReportLineEntity entity) {
        log.info("报工接口：{}", JSON.toJSONString(entity));
        String operationNumber = entity.getOperationNumber();
        OperationOrderEntity operationOrder = operationService.getByOperationNumber(operationNumber);
        // 客户名称
        SaleOrderEntity saleOrderEntity = JacksonUtil.getResponseObject(workOrderInterface.getSaleOrderByWorkOrderId(operationOrder.getWorkOrderNum()), SaleOrderEntity.class);
        if (saleOrderEntity != null) {
            entity.setCustomerName(saleOrderEntity.getCustomerName());
        }
        //检查是否已经挂起或停止
        /*if (operationOrder.getState() == OperationStateEnum.HANG_UP.getCode()) {
            throw new ResponseException(RespCodeEnum.ORDER_HANG_UP);
        }
        if (operationOrder.getState() == OperationStateEnum.FINISHED.getCode()) {
            throw new ResponseException(RespCodeEnum.ORDER_FINISHED);
        }*/

        Date now = new Date();
        Date recordDate = requestService.getRecordDate(now);
        Integer lineId = operationOrder.getLineId();

        String reporter = entity.getUserName() == null ? null : requestService.getNickName(entity.getUserName());
        String operator = entity.getOperator() == null ? null : requestService.getNickName(entity.getOperator());
        ShiftEntity shiftEntity = requestService.getShiftById(entity.getShiftId());
        entity.setUserNickname(reporter);
        entity.setOperatorName(operator);
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setReportTime(now);
        entity.setReportDate(recordDate);
        entity.setShiftType(shiftEntity == null ? null : shiftEntity.getShiftType());
        entity.setType(ReportType.REPORT.getType());
        entity.setLineId(lineId);
        entity.setLineName(operationOrder.getLineName());
        if(entity.getTeamId() == null) {
            entity.setTeamId(operationOrder.getTeamId());
        }
        entity.setDeviceId(operationOrder.getDeviceId());

        //占用计数器的情况下
        /*if (getAllowReport(lineId, operationNumber)) {
            //插入上报记录
            this.save(entity);
        }*/

        //插入reportCountLine
        ReportLineEntity reportLineEntity = dealReportLineReport(entity);

        //更新作业工单的有效工时
        updateWorkOrderEffectiveHours(entity.getEffectiveHours(), operationOrder);
        //查询该作业工单所有上报记录(包括自动报工记录)
        List<ReportLineEntity> list = getByOperation(entity.getOperationNumber());

        double totalFinish = 0.0;
        double totalUnqualified = 0.0;
        if (!CollectionUtils.isEmpty(list)) {
            totalFinish = list.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
            totalUnqualified = list.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
        }

        OperationOrderEntity build = OperationOrderEntity.builder().id(operationOrder.getId()).finishCount(totalFinish).unqualified(totalUnqualified).build();

        //更新作业工单
        operationService.updateById(build);
        operationOrder = operationService.getByOperationNumber(operationNumber);

        //更新workOrderDayCount
        //手工上报的情况,会把之前的工单每日完成数删除，根据手工上报的数量重新设置工单每日完成数
        updateAllOperationDayCount(operationOrder, list);

        //更新产量表
        updateOutPut(entity.getReportDate());

        //更新工单和工单当天产量
        updateWorkOrderAndDayCount(operationOrder, entity.getReportDate());
        // 报工后推送消息
        messagePushToKafkaService.pushNewMessage(reportLineEntity, Constants.KAFKA_RECORD_TOPIC, KafkaMessageTypeEnum.OPERATION_ORDER_REPORT);

        // 新增人员计时计件记录
        ReportCountDTO reportCountDTO = ReportCountDTO.builder()
                .id(reportLineEntity.getId())
                .operator(reportLineEntity.getOperator())
                .operatorName(reportLineEntity.getOperatorName())
                .reportDate(reportLineEntity.getReportDate())
                .shiftType(reportLineEntity.getShiftType())
                .finishCount(reportLineEntity.getFinishCount())
                .reportTime(reportLineEntity.getReportTime())
                .effectiveHours(reportLineEntity.getEffectiveHours())
                .build();
        staffPieceWorkTimeInterface.setStaffTimePiece(reportCountDTO, operationOrder.getWorkOrderNum(), operationOrder.getOperationNumber());
        if (StringUtils.isNotBlank(entity.getBatch())) {
            // 校验批次的物料和作业工单的批次是否同一批次
            BarCodeDTO barCodeDTO = JacksonUtil.getResponseObject(barCodeInterface.getByBarCode(entity.getBatch()), BarCodeDTO.class);
            if (barCodeDTO != null && !barCodeDTO.getMaterialCode().equals(operationOrder.getMaterialCode())) {
                throw new ResponseException("系统绑定的批次和作业工单的物料不一致");
            }
            if (barCodeDTO == null) {
                // 如果关联的批次在系统中不存在，则新建一条批次信息
                MaterialEntity materialEntity = JacksonUtil.getResponseObject(materialInterface.detail(operationOrder.getMaterialCode()), MaterialEntity.class);
                // 获取批次绑定的供应商（采购收货单情况才能取到）
                BarCodeDTO dto = BarCodeDTO.builder()
                        .barCode(entity.getBatch())
                        .materialCode(operationOrder.getMaterialCode())
                        .materialName(materialEntity.getName())
                        .ruleType(BarCodeTypeEnum.FINISHED.getCode())
                        .relateNumber(operationNumber)
                        .unit(materialEntity.getComp())
                        .createTime(new Date())
                        .createBy(entity.getUserName())
                        .state(BarCodeStateEnum.AVAILABLE.getCode())
                        .isStack(false)
                        .build();
                barCodeInterface.saveBarCode(dto);
            }
        }
    }

    private void updateWorkOrderAndDayCount(OperationOrderEntity operationOrder, Date reportDate) {
        //查询该工单下所有产量
        List<ReportLineEntity> reportLineEntities = updateWorkOrder(operationOrder);

        //更新当天的产量
        updateWorkOrderDayCount(operationOrder.getWorkOrderNum(), reportDate, reportLineEntities);
    }


    private void updateWorkOrderAndDayCountWhenUpdate(OperationOrderEntity operationOrder, Date reportDate, Date oldDate) {
        List<ReportLineEntity> reportLineEntities = updateWorkOrder(operationOrder);
        updateWorkOrderDayCount(operationOrder.getWorkOrderNum(), reportDate, reportLineEntities);
        if (oldDate != null) {
            updateWorkOrderDayCount(operationOrder.getWorkOrderNum(), oldDate, reportLineEntities);
        }
    }

    private void updateWorkOrderDayCount(String workOrderNum, Date reportDate, List<ReportLineEntity> reportLineEntities) {
        //更新当天的产量
        List<ReportLineEntity> collect = reportLineEntities.stream().filter(o -> o.getReportDate().getTime() == reportDate.getTime()).collect(Collectors.toList());
        double dayCount = collect.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
        double dayUnqualified = collect.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();

        requestService.updateWorkOrderDayCount(workOrderNum, dayCount, dayUnqualified, reportDate);
    }

    private List<ReportLineEntity> updateWorkOrder(OperationOrderEntity operationOrder) {
        List<OperationOrderEntity> list = operationService.getByWorkOrder(operationOrder.getWorkOrderNum());
        List<String> operationNumCollect = list.stream().map(OperationOrderEntity::getOperationNumber).collect(Collectors.toList());

        LambdaQueryWrapper<ReportLineEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ReportLineEntity::getOperationNumber, operationNumCollect);
        List<ReportLineEntity> reportLineEntities = reportLineMapper.selectList(queryWrapper);

        double finishCount = reportLineEntities.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
        double unqualified = reportLineEntities.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
        double effectiveHours = reportLineEntities.stream().filter(o -> o.getEffectiveHours() != null).mapToDouble(ReportLineEntity::getEffectiveHours).sum();

        WorkOrderEntity build = WorkOrderEntity.builder().workOrderNumber(operationOrder.getWorkOrderNum())
                .finishCount(finishCount).unqualified(unqualified).effectiveHours(effectiveHours).build();

        requestService.updateWorkOrder(build);
        return reportLineEntities;
    }


    /**
     * 更新工单的有效工时
     *
     * @param effectiveHours
     * @param operationOrder
     */
    private void updateWorkOrderEffectiveHours(Double effectiveHours, OperationOrderEntity operationOrder) {
        //如果没有传报工的有效工时的参数时 则不处理（考虑到非奥维斯的情况）
        if (effectiveHours == null) {
            return;
        }

        //该产线下该工单所有报工录入的有效工时总和(包含档次报工记录的有效工时)
        LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLineEntity::getOperationNumber, operationOrder.getOperationNumber())
                .eq(ReportLineEntity::getLineId, operationOrder.getLineId());
        List<ReportLineEntity> list = reportLineMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            double oldTotalHours = list.stream().filter(o -> o.getEffectiveHours() != null)
                    .mapToDouble(ReportLineEntity::getEffectiveHours).sum();
            operationOrder.setActualWorkingHours(oldTotalHours);
            OperationOrderEntity build = OperationOrderEntity.builder().id(operationOrder.getId()).actualWorkingHours(oldTotalHours).build();
            operationService.updateById(build);
        }
    }


    private void updateAllOperationDayCount(OperationOrderEntity operationOrder, List<ReportLineEntity> list) {

        Map<Long, List<ReportLineEntity>> dayCollect = list.stream().collect(Collectors.groupingBy(o -> o.getReportDate().getTime()));

        Set<Long> reportCountAllDateTime = dayCollect.keySet();

        //更新每天的workOrderDayCount
        for (Map.Entry<Long, List<ReportLineEntity>> longListEntry : dayCollect.entrySet()) {
            Long recordDateTime = longListEntry.getKey();
            List<ReportLineEntity> value = longListEntry.getValue();
            double totalFinish = value.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
            double totalUnqualified = value.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
            operationDayCountService.saveOrUpdateOperationDayCount(operationOrder, totalFinish, totalUnqualified, new Date(recordDateTime));
        }

        LambdaQueryWrapper<OperationDayCountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationDayCountEntity::getOperationNumber, operationOrder.getOperationNumber());
        List<OperationDayCountEntity> entityList = operationDayCountService.list(wrapper);
        List<Long> workOrderDates = entityList.stream().map(o -> o.getTime().getTime()).collect(Collectors.toList());
        //去除不存在的日期工单产量
        workOrderDates.removeAll(reportCountAllDateTime);
        if (!CollectionUtils.isEmpty(workOrderDates)) {
            for (Long dateTime : workOrderDates) {
                LambdaQueryWrapper<OperationDayCountEntity> qw = new LambdaQueryWrapper<>();
                qw.eq(OperationDayCountEntity::getOperationNumber, operationOrder.getOperationNumber());
                qw.eq(OperationDayCountEntity::getTime, new Date(dateTime));
                operationDayCountService.remove(qw);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCount(ReportLineEntity entity) {
        log.info("修改报工接口：{}", JSON.toJSONString(entity));
        ReportLineEntity old = reportLineMapper.selectById(entity.getId());
        Date now = new Date();
        //如果更新的是最后一条，则需要更新计数器的记录时间
        LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLineEntity::getOperationNumber, entity.getOperationNumber())
                .eq(ReportLineEntity::getType, ReportType.REPORT.getType())
                .orderByDesc(ReportLineEntity::getCreateTime)
                .orderByDesc(ReportLineEntity::getId)
                .last("limit 1");
        ReportLineEntity lastReport = reportLineMapper.selectOne(wrapper);
        //判断是否为在该工单最后一条报工记录
        boolean isUpdateLastReport = lastReport != null && lastReport.getId().equals(entity.getReportCountId());


        //更新report_line表
        updateReportLine(entity, now, isUpdateLastReport);

        //查询该工单所有上报记录
        List<ReportLineEntity> list = getByOperation(entity.getOperationNumber());
        OperationOrderEntity operationOrder = operationService.getByOperationNumber(entity.getOperationNumber());

        updateAllOperationDayCount(operationOrder, list);

        //修改时间
        updateOutPut(entity.getReportDate());
        if (old != null && entity.getReportDate().getTime() != old.getReportDate().getTime()) {
            updateOutPut(old.getReportDate());
        }

        //更新作业工单的相关记录
        updateRelative(list);

        //更新工单和工单当天产量
        updateWorkOrderAndDayCountWhenUpdate(operationOrder, entity.getReportDate(), old == null ? null : old.getReportDate());

        //更新工单有效工时
        updateWorkOrderEffectiveHours(entity.getEffectiveHours(), operationOrder);
        // 如果关联的批次在系统中不存在，则新建一条批次信息
        if (StringUtils.isNotBlank(entity.getBatch())) {
            // 校验批次的物料和作业工单的批次是否同一批次
            BarCodeDTO barCodeDTO = JacksonUtil.getResponseObject(barCodeInterface.getByBarCode(entity.getBatch()), BarCodeDTO.class);
            if (barCodeDTO != null && !barCodeDTO.getMaterialCode().equals(operationOrder.getMaterialCode())) {
                throw new ResponseException("系统绑定的批次和作业工单的物料不一致");
            }
            if (barCodeDTO == null) {
                MaterialEntity materialEntity = JacksonUtil.getResponseObject(materialInterface.detail(operationOrder.getMaterialCode()), MaterialEntity.class);
                // 获取批次绑定的供应商（采购收货单情况才能取到）
                String username = requestService.getUsername();
                BarCodeDTO dto = BarCodeDTO.builder()
                        .barCode(entity.getBatch())
                        .materialCode(operationOrder.getMaterialCode())
                        .materialName(materialEntity.getName())
                        .ruleType(BarCodeTypeEnum.FINISHED.getCode())
                        .relateNumber(operationOrder.getOperationNumber())
                        .unit(materialEntity.getComp())
                        .createTime(new Date())
                        .createBy(username)
                        .state(BarCodeStateEnum.AVAILABLE.getCode())
                        .isStack(false)
                        .build();
                barCodeInterface.saveBarCode(dto);
            }
        }
        // 更新人员计时计件
        ReportLineEntity reportLineEntity = reportLineMapper.selectById(entity.getId());
        ReportCountDTO reportCountDTO = ReportCountDTO.builder()
                .id(reportLineEntity.getId())
                .operator(entity.getOperator())
                .operatorName(entity.getOperatorName())
                .reportDate(entity.getReportDate())
                .shiftType(entity.getShiftType())
                .finishCount(entity.getFinishCount())
                .reportTime(entity.getReportTime())
                .effectiveHours(entity.getEffectiveHours())
                .build();
        staffPieceWorkTimeInterface.updateStaffTimePiece(reportCountDTO, operationOrder.getWorkOrderNum(), operationOrder.getOperationNumber());
    }


    private List<ReportLineEntity> getByOperation(String operationNumber) {
        LambdaQueryWrapper<ReportLineEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReportLineEntity::getOperationNumber, operationNumber);
        return reportLineMapper.selectList(queryWrapper);
    }


    private void updateReportLine(ReportLineEntity entity, Date now, boolean isUpdateLastReport) {
        ShiftEntity shiftEntity = requestService.getShiftById(entity.getShiftId());
        entity.setShiftType(shiftEntity == null ? null : shiftEntity.getShiftType());
        String reporter = entity.getUserName() == null ? null : requestService.getNickName(entity.getUserName());
        String operator = entity.getOperator() == null ? null : requestService.getNickName(entity.getOperator());
        entity.setUserNickname(reporter);
        entity.setOperatorName(operator);
        LambdaUpdateWrapper<ReportLineEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ReportLineEntity::getId, entity.getId());
        updateWrapper.set(ReportLineEntity::getFinishCount, entity.getFinishCount());
        updateWrapper.set(ReportLineEntity::getUnqualified, entity.getUnqualified());
        updateWrapper.set(ReportLineEntity::getEffectiveHours, entity.getEffectiveHours());
        updateWrapper.set(ReportLineEntity::getShiftType, entity.getShiftType());
        updateWrapper.set(ReportLineEntity::getShiftId, entity.getShiftId());
        updateWrapper.set(ReportLineEntity::getUserName, entity.getUserName());
        updateWrapper.set(ReportLineEntity::getUserNickname, entity.getUserNickname());
        updateWrapper.set(ReportLineEntity::getOperator, entity.getOperator());
        updateWrapper.set(ReportLineEntity::getOperatorName, entity.getOperatorName());
        updateWrapper.set(ReportLineEntity::getType, ReportType.REPORT.getType());
        updateWrapper.set(ReportLineEntity::getBatch, entity.getBatch());
        updateWrapper.set(ReportLineEntity::getReportDate, entity.getReportDate());
        updateWrapper.set(ReportLineEntity::getUpdateTime, now);
        updateWrapper.set(ReportLineEntity::getDefectDesc, entity.getDefectDesc());
        reportLineMapper.update(entity, updateWrapper);
        if (isUpdateLastReport) {
            resetAutoCount(entity, now);
        }
        //查询原来的不良图片
        LambdaQueryWrapper<UnqualifiedImgEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnqualifiedImgEntity::getReportId, entity.getId());
        List<UnqualifiedImgEntity> unqualifiedImgEntities = unqualifiedImgMapper.selectList(queryWrapper);
        for (UnqualifiedImgEntity unqualifiedImgEntity : unqualifiedImgEntities) {
            //调用uploadInterface.markUploadFile
            uploadInterface.markUploadFile(unqualifiedImgEntity.getUrl(), null, null);
            //删除不良图片表对应的记录
            unqualifiedImgMapper.deleteById(unqualifiedImgEntity.getId());
        }

        //插入新不良图片到新表
        String[] picUrls = entity.getPicUrls();
        if (picUrls != null) {
            for (String picUrl : picUrls) {
                UnqualifiedImgEntity imgEntity = UnqualifiedImgEntity.builder().reportId(entity.getId()).url(picUrl).build();
                unqualifiedImgMapper.insert(imgEntity);
                //调用uploadInterface.markUploadFile
                uploadInterface.markUploadFile(null, picUrl, null);
            }
        }
    }

    private void resetAutoCount(ReportLineEntity entity, Date now) {
        //更新最新的一条报工记录则自动计数清零
        LambdaUpdateWrapper<ReportLineEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ReportLineEntity::getOperationNumber, entity.getOperationNumber()).eq(ReportLineEntity::getType, ReportType.AUTO.getType());
        ReportLineEntity build = ReportLineEntity.builder().finishCount(0.0).updateTime(now).build();
        reportLineMapper.update(build, wrapper);
    }

    /**
     * 通知工单更新output表
     *
     * @param recordDate
     */
    private void updateOutPut(Date recordDate) {
        //更新dfs_output
        List<Double> result = getReportCountByDate(recordDate);
        requestService.saveOrUpdateRecordOutPut(result.get(0), recordDate);
    }

    /**
     * 更新作业工单的相关记录
     *
     * @param list
     */
    private void updateRelative(List<ReportLineEntity> list) {
        Double count = 0.0;
        Double unqualified = 0.0;
        if (!CollectionUtils.isEmpty(list)) {
            count = list.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
            unqualified = list.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
        }

        OperationOrderEntity workOrder = operationService.getByOperationNumber(list.get(0).getOperationNumber());
        OperationOrderEntity build = OperationOrderEntity.builder().id(workOrder.getId()).finishCount(count).unqualified(unqualified).build();
        //更新工单进度
        operationService.updateById(build);
    }

    /**
     * 0-投产  1-完成 2-挂起
     *
     * @param operationNumber
     * @param action
     * @param type
     */
    @Override
    public void dealReportCountRecord(String operationNumber, Integer action, String type) {
        if (action == null) {
            return;
        }
        OperationOrderEntity entity = operationService.getByOperationNumber(operationNumber);
        if (entity == null) {
            return;
        }
        entity.setState(getWorkOrderStateByAction(action));
        entity.setStateName(OperationStateEnum.getNameByCode(entity.getState()));
        //更新作业工单状态
        operationService.setStartEndDate(entity);

        //更新作业工单状态
        operationService.edit(entity);

        // 如果该作业工单存在于连续生产任务列表中，则进行挂起/ 完工操作时，清空任务列表
        try {
            // 计数器自动触发的完工/挂起，不清空任务列表
            if (type.equals(Constant.AUTO) || action.equals(Constant.START)) {
                return;
            }
            String operationNumbers = JacksonUtil.getResponseObject(controlInterface.emptyTaskList(entity.getLineId()), String.class);
            if (StringUtils.isNotBlank(operationNumbers)) {
                // 将自动完工字段置为false
                LambdaUpdateWrapper<OperationOrderEntity> uw = new LambdaUpdateWrapper<>();
                uw.in(OperationOrderEntity::getOperationNumber, operationNumbers)
                        .set(OperationOrderEntity::getIsAutoCompleted, false);
                operationService.update(uw);
            }
        } catch (Exception e) {
            log.error("dfs-control服务调用失败", e);
        }
    }

    /*@Override
    public void dealReportRecord(OperationOrderEntity entity) {
        Date now = new Date();

        //工作中心不是产线的情况
        WorkCenterEntity workCenter = JacksonUtil.getResponseObject(workCenterInterface.getWorkCenterById(entity.getWorkCenterId()), WorkCenterEntity.class);
        if (!workCenter.getType().equals(WorkCenterTypeEnum.LINE.getCode())) {
            insertAutoRecord(entity, now);
            return;
        }

        String type;
        Integer state = entity.getState();
        //检查采集设备是否被占用
        if (isOccupied(entity.getLineId())) {
            insertAutoRecord(entity, now);
            return;
        }

        //插入前检查该产线是否有正在报工的工单,有正在投入的订单则不插入
        ReportCountEntity reportCount = getLatestByLine(entity.getLineId());
        OperationOrderEntity lastOrderEntity = null;
        if (reportCount != null) {
            //通过查询工单，查看是否存在，避免脏数据
            lastOrderEntity = operationService.getByOperationNumber(reportCount.getOperationNumber());
        }

        if (state == OperationStateEnum.INVESTMENT.getCode()) {
            type = ReportType.INVESTMENT.getType();
            //投入前产线有正在报工的工单则不插入
            if (lastOrderEntity != null) {
                String reportType = reportCount.getType();
                if (ReportType.INVESTMENT.getType().equals(reportType) ||
                        ReportType.REPORT.getType().equals(reportType)) {
                    insertAutoRecord(entity, now);
                    return;
                }
            }
        } else if (state == OperationStateEnum.HANG_UP.getCode()) {
            type = ReportType.HANG_UP.getType();
            if (lastOrderEntity != null) {
                //该产线最后一条记录不是当前工单，则不能插入挂起记录
                String lastReportNumber = reportCount.getOperationNumber();
                if (!lastReportNumber.equals(entity.getOperationNumber())) {
                    return;
                }
            }
        } else if (state == OperationStateEnum.FINISHED.getCode()
                || state == OperationStateEnum.CLOSED.getCode()
                || state == OperationStateEnum.CANCELED.getCode()) {
            type = ReportType.FINISHED.getType();
            if (lastOrderEntity != null) {
                //该产线最后一条记录不是当前工单，则不能插入完成记录
                String lastReportNumber = reportCount.getOperationNumber();
                if (!lastReportNumber.equals(entity.getOperationNumber())) {
                    insertAutoRecord(entity, now);
                    return;
                }
            }
        } else {
            //不是投入、挂起、结束生产则不操作
            return;
        }

        //插入报工表记录记录计数器的时间
        ReportCountEntity countEntity = ReportCountEntity.builder()
                .operationNumber(entity.getOperationNumber())
                .lineId(entity.getLineId())
                .reportDate(requestService.getRecordDate(now))
                .reportTime(now)
                .type(type)
                .createTime(now)
                .build();
        this.save(countEntity);
        dealCountLineOperateSuccess(entity, now);

        if (state == OperationStateEnum.FINISHED.getCode() || state == OperationStateEnum.HANG_UP.getCode()) {
            //启动下一张工单
            List<Integer> lineIdCollect = requestService.getOtherLineId(entity.getLineId());
            lineIdCollect.add(entity.getLineId());
            StringBuilder sb = new StringBuilder();
            for (Integer lineId : lineIdCollect) {
                sb.append(lineId.toString()).append(Constant.SEP);
            }
            sb.deleteCharAt(sb.length() - 1);
            //循环遍历启用，确保被释放的采集器对应的产线插入记录
            List<OperationOrderEntity> runningWorkOrder = operationService.getRunningOperationByLineIds(sb.toString());
            //过滤掉没有产线的工单
            runningWorkOrder = runningWorkOrder.stream().filter(o -> o.getLineId() != null).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(runningWorkOrder)) {
                for (OperationOrderEntity operationOrder : runningWorkOrder) {
                    dealReportRecord(operationOrder);
                }
            }
        }
    }*/

    @Override
    public void insertAutoRecord(OperationOrderEntity entity, Date now) {
        //投产时插入自动报工记录
        if (entity.getState() == OperationStateEnum.INVESTMENT.getCode()) {
            LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ReportLineEntity::getOperationNumber, entity.getOperationNumber())
                    .eq(ReportLineEntity::getType, ReportType.AUTO.getType());
            //如果该产线没有自动报工记录，则插入一条
            if (reportLineMapper.selectCount(wrapper) == 0) {
                reportLineMapper.insert(
                        ReportLineEntity.builder()
                                .type(ReportType.AUTO.getType())
                                .lineId(entity.getLineId())
                                .operationNumber(entity.getOperationNumber())
                                .finishCount(0.0)
                                .reportDate(requestService.getRecordDate(now))
                                .reportTime(now)
                                .createTime(now)
                                .updateTime(now)
                                .build()
                );
            }
        }
    }

    private void dealCountLineOperateSuccess(OperationOrderEntity entity, Date now) {
        LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLineEntity::getOperationNumber, entity.getOperationNumber())
                .eq(ReportLineEntity::getType, ReportType.AUTO.getType());
        ReportLineEntity reportLineEntity = reportLineMapper.selectOne(wrapper);
        //投产时插入自动报工记录
        if (entity.getState() == OperationStateEnum.INVESTMENT.getCode()) {
            //如果该产线没有自动报工记录，则插入一条
            if (reportLineEntity == null) {
                reportLineMapper.insert(
                        ReportLineEntity.builder()
                                .type(ReportType.AUTO.getType())
                                .lineId(entity.getLineId())
                                .operationNumber(entity.getOperationNumber())
                                .finishCount(0.0)
                                .reportDate(requestService.getRecordDate(now))
                                .reportTime(now)
                                .createTime(now)
                                .updateTime(now)
                                .build()
                );
            }
            redisTemplate.opsForValue().set(RedisKeyPrefix.getAllowReport(entity.getOperationNumber()), true, 1, TimeUnit.MINUTES);

        } else if (entity.getState() == OperationStateEnum.HANG_UP.getCode()) {
            redisTemplate.opsForValue().set(RedisKeyPrefix.getAllowReport(entity.getOperationNumber()), false, 1, TimeUnit.MINUTES);
        } else if (entity.getState() == OperationStateEnum.FINISHED.getCode()) {
            redisTemplate.opsForValue().set(RedisKeyPrefix.getAllowReport(entity.getOperationNumber()), false, 1, TimeUnit.MINUTES);
        }
    }

    private ReportLineEntity dealReportLineReport(ReportLineEntity reportLineEntity) {
        //插入记录
        reportLineEntity.setId(null);
        reportLineMapper.insert(reportLineEntity);
        //插入不良图片表
        String[] picUrls = reportLineEntity.getPicUrls();
        if (picUrls != null) {
            for (String picUrl : picUrls) {
                UnqualifiedImgEntity imgEntity = UnqualifiedImgEntity.builder().reportId(reportLineEntity.getId()).url(picUrl).build();
                unqualifiedImgMapper.insert(imgEntity);
                uploadInterface.markUploadFile(null, picUrl, null);
            }
        }
        //自动报工记录清零
        reportLineMapper.updateReportLine(reportLineEntity.getOperationNumber(), 0, reportLineEntity.getCreateTime(), ReportType.AUTO.getType());
        return reportLineEntity;
    }

    /**
     * 是否占用计数器
     *
     * @param operationNumber
     * @return
     */
    @Override
    public Boolean getAllowReport(Integer lineId, String operationNumber) {
        Boolean allowReport = (Boolean) redisTemplate.opsForValue().get(RedisKeyPrefix.getAllowReport(operationNumber));
        if (allowReport != null) {
            return allowReport;
        }

        //检查采集设备是否被占用
        if (isOccupied(lineId)) {
            redisTemplate.opsForValue().set(RedisKeyPrefix.getAllowReport(operationNumber), false, 1, TimeUnit.MINUTES);
            return false;
        }

        String reportingWorkOrderNumber = orderExecuteSeqService.getOccupyOrderByLineId(lineId);

        //检查该工单所属产线能否报工
        if (StringUtils.isNotBlank(reportingWorkOrderNumber)) {
            if (!reportingWorkOrderNumber.equals(operationNumber)) {
                redisTemplate.opsForValue().set(RedisKeyPrefix.getAllowReport(operationNumber), false, 1, TimeUnit.MINUTES);
                return false;
            }
        }
        redisTemplate.opsForValue().set(RedisKeyPrefix.getAllowReport(operationNumber), true, 1, TimeUnit.MINUTES);
        return true;
    }


    /*@Override
    public ReportCountEntity getWorkOrderNumberByLineId(Integer productionLineId) {
        LambdaQueryWrapper<ReportCountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportCountEntity::getLineId, productionLineId)
                .orderByDesc(ReportCountEntity::getCreateTime)
                .orderByDesc(ReportCountEntity::getId)
                .last("limit 1");
        return this.getOne(wrapper);
    }*/

    @Override
    public LineOccupyDTO confirmReportAction(Integer lineId) {
        ProductionLineEntity lineEntity = JacksonUtil.getResponseObject(lineInterface.getSimpleEntity(lineId), ProductionLineEntity.class);
        if (lineEntity == null) {
            throw new ResponseException(RespCodeEnum.LINE_NOT_FOUND);
        }
        List<Integer> lineIdCollect = requestService.getOtherLineId(lineId);
        lineIdCollect.add(lineId);
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OperationOrderEntity::getLineId, lineIdCollect)
                .eq(OperationOrderEntity::getState, OperationStateEnum.INVESTMENT.getCode());
        List<OperationOrderEntity> list = operationService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            List<String> collect = list.stream().map(OperationOrderEntity::getOperationNumber).collect(Collectors.toList());
            return LineOccupyDTO.builder().lineName(lineEntity.getName()).operationOrderNumbers(collect).build();
        }
    }

    @Override
    public FinishAndReportCountDTO getFinishAndReportCount(String operationNumber) {
        OperationOrderEntity operationOrderEntity = operationService.getByOperationNumber(operationNumber);
        if (operationOrderEntity == null) {
            return null;
        }
        // 获取已报工数 = 手动报工数
        LambdaQueryWrapper<ReportLineEntity> reportQW = new LambdaQueryWrapper<>();
        reportQW.eq(ReportLineEntity::getOperationNumber, operationNumber)
                .eq(ReportLineEntity::getType, ReportType.REPORT.getType());
        List<ReportLineEntity> reportList = reportLineMapper.selectList(reportQW);
        double reportCount = reportList.stream().mapToDouble(ReportLineEntity::getFinishCount).sum();

        // 获取完工数量 = 自动报工数 + 手动报工数
        LambdaQueryWrapper<ReportLineEntity> finishQW = new LambdaQueryWrapper<>();
        finishQW.eq(ReportLineEntity::getOperationNumber, operationNumber)
                .eq(ReportLineEntity::getType, ReportType.AUTO.getType());

        List<ReportLineEntity> finishList = reportLineMapper.selectList(finishQW);
        double finishCount = finishList.stream().mapToDouble(ReportLineEntity::getFinishCount).sum() + reportCount;
        return FinishAndReportCountDTO.builder().finishCount(finishCount).reportCount(reportCount).build();
    }

    @Override
    public boolean checkFinishState(String workOrder) {
        //工单下所有作业工单都为完成状态才可以完成
        LambdaQueryWrapper<OperationOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderEntity::getWorkOrderNum, workOrder);
        List<OperationOrderEntity> list = operationService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        return list.stream().allMatch(operationOrderEntity -> operationOrderEntity.getState().equals(OperationStateEnum.FINISHED.getCode()));
    }

    @Override
    public Double getHistoryFinishCount(String operationNumber) {
        LambdaQueryWrapper<ReportLineEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReportLineEntity::getOperationNumber, operationNumber);
        List<ReportLineEntity> reportLineEntityList = reportLineMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(reportLineEntityList)) {
            return 0.0;
        }
        return reportLineEntityList.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
    }

    /**
     * 检查该产线上的采集设备是否被其他产线占用
     *
     * @param productionLineId
     * @return
     */
    private Boolean isOccupied(Integer productionLineId) {
        //检查该工单产线上的采集设备是否被占用
        //查询用于计数的采集器
        List<Integer> lineIdCollect = requestService.getOtherLineId(productionLineId);
        if (CollectionUtils.isEmpty(lineIdCollect)) {
            return false;
        }
        boolean occupied = false;
        for (Integer lineId : lineIdCollect) {
            String occupyOperationOrderNumber = orderExecuteSeqService.getOccupyOrderByLineId(lineId);
            if (StringUtils.isNotBlank(occupyOperationOrderNumber)) {
                occupied = true;
                break;
            }
        }
        return occupied;
    }

    private static Integer getWorkOrderStateByAction(Integer action) {
        Integer state = null;
        if (action.equals(Constant.START)) {
            state = OperationStateEnum.INVESTMENT.getCode();
        }
        if (action.equals(Constant.END)) {
            state = OperationStateEnum.FINISHED.getCode();
        }
        if (action.equals(Constant.PAUSE)) {
            state = OperationStateEnum.HANG_UP.getCode();
        }
        return state;
    }

    public static Integer getActionByWorkOrderState(Integer state) {
        Integer action = null;
        if (state == OperationStateEnum.INVESTMENT.getCode()) {
            action = Constant.START;
        }
        if (state == OperationStateEnum.FINISHED.getCode()) {
            action = Constant.END;
        }
        if (state == OperationStateEnum.HANG_UP.getCode()) {
            action = Constant.PAUSE;
        }
        return action;
    }


    private void updateOperationDayCount(String operationNumber, Date recordDate) {
        OperationOrderEntity order = operationService.getByOperationNumber(operationNumber);
        if (order == null) {
            return;
        }
        LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLineEntity::getOperationNumber, operationNumber)
                .eq(ReportLineEntity::getReportDate, recordDate);
        List<ReportLineEntity> list = reportLineMapper.selectList(wrapper);
        double sumFinishCount = 0;
        double sumUnqualifiedCount = 0;
        if (!CollectionUtils.isEmpty(list)) {
            sumFinishCount = list.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
            sumUnqualifiedCount = list.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
        }

        operationDayCountService.saveOrUpdateOperationDayCount(order, sumFinishCount, sumUnqualifiedCount, recordDate);
    }

    private List<Double> getReportCountByDate(Date recordDate) {
        double sumFinishCount = 0;
        double sumUnqualifiedCount = 0;
        LambdaQueryWrapper<ReportLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportLineEntity::getReportDate, recordDate);
        List<ReportLineEntity> list = reportLineMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            sumFinishCount = list.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
            sumUnqualifiedCount = list.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
        }
        List<Double> result = new ArrayList<>();
        result.add(sumFinishCount);
        result.add(sumUnqualifiedCount);
        return result;
    }
}
