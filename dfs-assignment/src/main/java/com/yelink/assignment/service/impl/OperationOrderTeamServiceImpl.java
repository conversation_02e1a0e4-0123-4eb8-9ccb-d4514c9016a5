package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderTeamEntity;
import com.yelink.assignment.mapper.OperationOrderTeamMapper;
import com.yelink.assignment.service.OperationOrderTeamService;
import com.yelink.dfscommon.api.dfs.PostInterface;
import com.yelink.dfscommon.api.dfs.UserInterface;
import com.yelink.dfscommon.constant.TeamRoleEnum;
import com.yelink.dfscommon.entity.dfs.SysPostUserEntity;
import com.yelink.dfscommon.entity.dfs.SysUserEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-07-20 19:08
 */
@AllArgsConstructor
@Service
public class OperationOrderTeamServiceImpl extends ServiceImpl<OperationOrderTeamMapper, OperationOrderTeamEntity> implements OperationOrderTeamService {

    private UserInterface userInterface;
    private PostInterface postInterface;

    @Override
    public void add(OperationOrderEntity operationOrderEntity) {
        if (operationOrderEntity == null || CollectionUtils.isEmpty(operationOrderEntity.getOperationOrderTeamEntities())) {
            return;
        }
        String operationOrderNumber = operationOrderEntity.getOperationNumber();
        List<OperationOrderTeamEntity> list = operationOrderEntity.getOperationOrderTeamEntities();
        list.forEach(o -> {
            o.setOperationOrderNumber(operationOrderNumber);
            o.setCreateBy(operationOrderEntity.getCreateBy());
            o.setCreateDate(operationOrderEntity.getCreateTime());
            o.setUpdateBy(operationOrderEntity.getUpdateBy());
            o.setUpdateDate(operationOrderEntity.getUpdateTime());
        });
        //删除旧组员
        this.lambdaUpdate().eq(OperationOrderTeamEntity::getOperationOrderNumber, operationOrderNumber).remove();
        //插入最新组员信息
        this.saveBatch(list);
    }

    @Override
    public List<OperationOrderTeamEntity> getOperationOrderTeams(String operationOrderNumber) {
        List<OperationOrderTeamEntity> list = this.lambdaQuery().eq(OperationOrderTeamEntity::getOperationOrderNumber, operationOrderNumber).list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        //查询用户昵称
        List<String> userNames = list.stream().map(OperationOrderTeamEntity::getMemberName).collect(Collectors.toList());
        String[] array = userNames.toArray(new String[userNames.size()]);
        Map<String, SysUserEntity> userMap = JacksonUtil.getResponseArray(userInterface.selectByUsernames(array), SysUserEntity.class)
                .stream().collect(Collectors.toMap(SysUserEntity::getUsername, entity -> entity));
        //查询岗位
        Map<String, Object> postMap = JacksonUtil.getResponseObject(postInterface.getPostNameMap(userNames), new TypeReference<Map<String, Object>>() {});
        list.forEach(o -> {
            o.setMemberNickName(userMap.get(o.getMemberName()) == null ? o.getMemberName() : userMap.get(o.getMemberName()).getNickname());
            o.setMemberId(userMap.get(o.getMemberName()) == null ? null : userMap.get(o.getMemberName()).getId());
            o.setTeamRoleName(TeamRoleEnum.getNameByCode(o.getTeamRole()));
            SysPostUserEntity sysPostUserEntity = postMap == null ? null : JacksonUtil.convertObject(postMap.get(o.getMemberName()), SysPostUserEntity.class);
            o.setPostIds(sysPostUserEntity == null ? null : sysPostUserEntity.getPostIds());
            o.setPostNames(sysPostUserEntity == null ? null : sysPostUserEntity.getPostNames());
        });
        return list;
    }
}
