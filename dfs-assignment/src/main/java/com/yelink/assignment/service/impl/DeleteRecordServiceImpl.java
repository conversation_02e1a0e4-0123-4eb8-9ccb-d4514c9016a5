package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.entity.common.DeleteRecordEntity;
import com.yelink.assignment.mapper.DeleteRecordMapper;
import com.yelink.assignment.service.DeleteRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-05-22 11:46
 */
@Slf4j
@Service
@AllArgsConstructor
public class DeleteRecordServiceImpl extends ServiceImpl<DeleteRecordMapper, DeleteRecordEntity> implements DeleteRecordService {


}
