package com.yelink.assignment.service;

import com.yelink.dfscommon.entity.dfs.FacCalEuiEntity;

/**
 * Service
 *
 * <AUTHOR>
 * @Date
 */
public interface MemoryCalApplyService {

    /**
     * 工单产量内存计算
     *
     * @param eui
     * @param countId
     * @param count
     * @param lineId
     */
    void reportLineMemoryCal(String eui, Integer countId, Double count, FacCalEuiEntity lineId);

    /**
     * 工单投入数内存计算
     *
     * @param eui
     * @param countId
     * @param count
     * @param lineId
     */
    void inputRecordMemoryCal(String eui, Integer countId, Double count, Integer lineId);
}
