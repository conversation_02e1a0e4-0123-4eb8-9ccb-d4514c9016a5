package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.constant.BarCodeStateEnum;
import com.yelink.assignment.entity.feed.FeedRecordEntity;
import com.yelink.assignment.entity.model.FacilitiesEntity;
import com.yelink.assignment.entity.model.ProductionLineEntity;
import com.yelink.assignment.entity.product.MaterialEntity;
import com.yelink.assignment.mapper.FeedRecordMapper;
import com.yelink.assignment.request.RequestService;
import com.yelink.assignment.service.FeedRecordService;
import com.yelink.dfscommon.api.dfs.BarCodeInterface;
import com.yelink.dfscommon.api.dfs.FacilitiesInterface;
import com.yelink.dfscommon.api.dfs.LineInterface;
import com.yelink.dfscommon.api.dfs.MaterialInterface;
import com.yelink.dfscommon.dto.dfs.BarCodeDTO;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/3/29 18:03
 */
@Slf4j
@Service
public class FeedRecordServiceImpl extends ServiceImpl<FeedRecordMapper, FeedRecordEntity> implements FeedRecordService {

    @Autowired
    private LineInterface lineInterface;
    @Autowired
    private BarCodeInterface barCodeInterface;
    @Autowired
    private FacilitiesInterface facilitiesInterface;
    @Autowired
    private MaterialInterface materialInterface;
    @Autowired
    private RequestService requestService;

    @Override
    public void saveFeedRecord(FeedRecordEntity feedRecordEntity) {
        feedRecordEntity.setFeedTime(new Date());
        // 获取工位ID
        if (feedRecordEntity.getLineId() != null) {
            ProductionLineEntity line = JacksonUtil.getResponseObject(lineInterface.getSimpleEntity(feedRecordEntity.getLineId()), ProductionLineEntity.class);
            feedRecordEntity.setFacId(line.getFid());
        }
        feedRecordEntity.setFeedBy(requestService.getUsername());
        this.save(feedRecordEntity);
        // 如果系统没有该批次，则需添加一条批次信息
        MaterialEntity materialEntity = JacksonUtil.getResponseObject(materialInterface.detail(feedRecordEntity.getMaterialCode()), MaterialEntity.class);
        String username = requestService.getUsername();
        BarCodeDTO dto = BarCodeDTO.builder()
                .barCode(feedRecordEntity.getBatchNumber())
                .materialCode(feedRecordEntity.getMaterialCode())
                .materialName(feedRecordEntity.getMaterialName())
                .supplier(feedRecordEntity.getSupplierName())
                .state(BarCodeStateEnum.AVAILABLE.getCode())
                .unit(materialEntity.getComp())
                .createTime(new Date())
                .createBy(username)
                .isStack(false)
                .build();
        barCodeInterface.saveBarCode(dto);
    }

    @Override
    public List<FeedRecordEntity> getRecordList(String operationOrderNum) {
        LambdaQueryWrapper<FeedRecordEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(FeedRecordEntity::getOperationOrderNum, operationOrderNum);
        List<FeedRecordEntity> feedRecordEntities = this.list(qw);
        // 获取工位名称
        List<Integer> fids = feedRecordEntities.stream().map(FeedRecordEntity::getFacId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fids)) {
            return new ArrayList<>();
        }
        List<FacilitiesEntity> facilitiesEntities = JacksonUtil.getResponseArray(facilitiesInterface.getListByFids(fids), FacilitiesEntity.class);
        Map<Integer, String> map = facilitiesEntities.stream().collect(Collectors.toMap(FacilitiesEntity::getFid, FacilitiesEntity::getFname));
        for (FeedRecordEntity feedRecordEntity : feedRecordEntities) {
            feedRecordEntity.setFacName(map.get(feedRecordEntity.getFacId()));
            if (!StringUtils.isEmpty(feedRecordEntity.getFeedBy())) {
                feedRecordEntity.setFeedByName(requestService.getNickName(feedRecordEntity.getFeedBy()));
            }
        }
        return feedRecordEntities;
    }

    @Override
    public void removeRecord(Integer feedRecordId) {
        LambdaQueryWrapper<FeedRecordEntity> rm = new LambdaQueryWrapper<>();
        rm.eq(FeedRecordEntity::getFeedRecordId, feedRecordId);
        this.remove(rm);
    }


}
