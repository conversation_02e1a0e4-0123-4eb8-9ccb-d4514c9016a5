package com.yelink.assignment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.assignment.entity.operation.OperationDayCountEntity;
import com.yelink.assignment.entity.operation.OperationOrderEntity;

import java.util.Date;
import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021/4/6 21:25
 */
public interface OperationDayCountService extends IService<OperationDayCountEntity> {
    /**
     * 更新
     *
     * @param operationOrderEntity
     */
    void updateWorkOrderDayCount(OperationOrderEntity operationOrderEntity);

    /**
     * 指定日期上报工单每日完成量
     *
     * @param operationOrderEntity
     * @param count
     * @param unqualified
     * @param date
     */
    void saveOrUpdateOperationDayCount(OperationOrderEntity operationOrderEntity, Double count, Double unqualified, Date date);


    /**
     * 获取作业工单当日完成量
     *
     * @param operationNumber
     * @return
     */
    Double getDayFinishCount(String operationNumber);

    List<OperationDayCountEntity> getReportList(String operationNumber);

    /**
     * 计数器回调计算工单每日完成数
     *
     * @param operationNumber
     */
    void updateByOperationNumber(String operationNumber);
}
