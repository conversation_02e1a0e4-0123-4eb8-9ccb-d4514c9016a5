package com.yelink.assignment.service.impl;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.entity.operation.OperationOrderProcedureRelationEntity;
import com.yelink.assignment.mapper.OperationOrderProcedureRelationMapper;
import com.yelink.assignment.service.OperationOrderProcedureRelationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationOrderProcedureRelationServiceImpl extends ServiceImpl<OperationOrderProcedureRelationMapper,
        OperationOrderProcedureRelationEntity> implements OperationOrderProcedureRelationService {

    @Override
    public List<String> getOrdersByProcedureName(String craftName) {
        LambdaQueryWrapper<OperationOrderProcedureRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(OperationOrderProcedureRelationEntity::getCraftProcedureName, craftName);
        List<OperationOrderProcedureRelationEntity> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(OperationOrderProcedureRelationEntity::getOperationOrderNumber).collect(Collectors.toList());
    }

    @Override
    public String getCraftNameByNumber(String operationNumber) {
        LambdaQueryWrapper<OperationOrderProcedureRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationOrderProcedureRelationEntity::getOperationOrderNumber, operationNumber);
        List<OperationOrderProcedureRelationEntity> list = this.list(queryWrapper);
        return list.stream().map(OperationOrderProcedureRelationEntity::getCraftProcedureName).distinct().collect(Collectors.joining(Constant.SEP));
    }
}



