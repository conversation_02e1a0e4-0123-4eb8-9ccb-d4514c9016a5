package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.constant.ReportType;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.record.InputRecordEntity;
import com.yelink.assignment.mapper.InputRecordMapper;
import com.yelink.assignment.service.InputRecordService;
import com.yelink.dfscommon.constant.assignment.OperationStateEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/10/22 19:04
 */
@Slf4j
@Service
@AllArgsConstructor
public class InputRecordServiceImpl extends ServiceImpl<InputRecordMapper, InputRecordEntity> implements InputRecordService {

    InputRecordMapper inputRecordMapper;

    @Override
    public InputRecordEntity getLastRecordByLineId(Integer lineId) {
        LambdaQueryWrapper<InputRecordEntity> inputRecordWrapper = new LambdaQueryWrapper<>();
        inputRecordWrapper.eq(InputRecordEntity::getLineId, lineId)
                .orderByDesc(InputRecordEntity::getInputRecordTime)
                .orderByDesc(InputRecordEntity::getId)
                .last("limit 1");
        return getOne(inputRecordWrapper);
    }

    @Override
    public void updateInputRecord(OperationOrderEntity entity) {
        if (entity.getLineId() == null) {
            return;
        }
        if (!entity.getState().equals(OperationStateEnum.INVESTMENT.getCode())
                && !entity.getState().equals(OperationStateEnum.HANG_UP.getCode())
                && !entity.getState().equals(OperationStateEnum.FINISHED.getCode())) {
            return;
        }
        String type;
        if (entity.getState().equals(OperationStateEnum.INVESTMENT.getCode())) {
            type = ReportType.INVESTMENT.getType();
        } else if (entity.getState().equals(OperationStateEnum.HANG_UP.getCode())) {
            type = ReportType.HANG_UP.getType();
        } else {
            type = ReportType.FINISHED.getType();
        }

        //查询最后一条记录
        LambdaQueryWrapper<InputRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InputRecordEntity::getLineId, entity.getLineId())
                .orderByDesc(InputRecordEntity::getCreateTime)
                .orderByDesc(InputRecordEntity::getId)
                .last("limit 1");
        InputRecordEntity lastInputRecord = inputRecordMapper.selectOne(wrapper);

        if (entity.getState().equals(OperationStateEnum.INVESTMENT.getCode())) {
            //如果当前状态为投入，插入一条挂起状态的记录
            if (lastInputRecord != null) {
                if (!entity.getOperationNumber().equals(lastInputRecord.getOperationNumber())) {
                    inputRecordMapper.insert(
                            InputRecordEntity.builder()
                                    .operationNumber(lastInputRecord.getOperationNumber())
                                    .lineId(lastInputRecord.getLineId())
                                    .inputRecordTime(new Date())
                                    .createTime(new Date())
                                    .type(ReportType.HANG_UP.getType()).build()
                    );
                }
            }

            inputRecordMapper.insert(InputRecordEntity.builder()
                    .operationNumber(entity.getOperationNumber())
                    .lineId(entity.getLineId())
                    .inputRecordTime(new Date())
                    .createTime(new Date())
                    .type(type).build());

            //删除工位投入数记录点
            //deleteFacInputEuiMark(bean.getWorkOrderNumber());
        }
        if (entity.getState().equals(OperationStateEnum.HANG_UP.getCode())
                || entity.getState().equals(OperationStateEnum.FINISHED.getCode())
        ) {

            if (lastInputRecord != null) {
                //最后一条记录是当前工单，且最后一条记录的状态不是完成（避免重复插入）
                if (entity.getOperationNumber().equals(lastInputRecord.getOperationNumber())
                        && !lastInputRecord.getType().equals(ReportType.FINISHED.getType())) {
                    inputRecordMapper.insert(InputRecordEntity.builder()
                            .operationNumber(entity.getOperationNumber())
                            .lineId(entity.getLineId())
                            .inputRecordTime(new Date())
                            .createTime(new Date())
                            .type(type).build());
                }
            }
        }
    }
}
