package com.yelink.assignment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.assignment.entity.common.ConfigEntity;
import com.yelink.assignment.mapper.ConfigMapper;
import com.yelink.assignment.service.ConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Slf4j
@Service
@AllArgsConstructor
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, ConfigEntity> implements ConfigService {

}



