package com.yelink.assignment.listener;

import com.yelink.assignment.service.OperationDayCountService;
import com.yelink.assignment.service.OperationService;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;



/**
 * 监听所有db的过期事件__keyevent@*__:expired"
 *
 * <AUTHOR>
@Component
@Slf4j
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {
    @Autowired
    private OperationDayCountService operationDayCountService;
    @Autowired
    private OperationService operationService;


    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    /**
     * 针对redis数据失效事件，进行数据处理
     *
     * @param message
     * @param pattern
     */
    @SneakyThrows
    @Override
    public void onMessage(Message message, byte[] pattern) {
        //获取失效的key
        String expiredKey = message.toString();
        //计数设备计算当日完成量回调
        dealWorkOrderDayCountByCounter(expiredKey);
        //作业工单模式下刷新工单产量
        refreshProcedureCount(expiredKey);
    }


    /**
     * 计数设备计算当日完成量回调
     *
     * @param expiredKey
     */
    private void dealWorkOrderDayCountByCounter(String expiredKey) {
        if (expiredKey.startsWith(RedisKeyPrefix.UPDATE_OPERATION_DAY_COUNT_BY_COUNTER)) {
            operationDayCountService.updateByOperationNumber(expiredKey.replace(RedisKeyPrefix.UPDATE_OPERATION_DAY_COUNT_BY_COUNTER, ""));
        }
    }


    /**
     * 作业工单模式下刷新工单产量
     *
     * @param expiredKey
     */
    private void refreshProcedureCount(String expiredKey) {
        if (expiredKey.startsWith(com.yelink.dfscommon.constant.RedisKeyPrefix.REFRESH_WORK_ORDER)) {
            //使用redis回调更新工单每日完成量
            operationService.refreshProcedureCountCallBack(expiredKey.replace(RedisKeyPrefix.REFRESH_WORK_ORDER, ""));
        }
    }


}
