package com.yelink.assignment.listener;

import com.alibaba.fastjson.JSON;
import com.yelink.assignment.listener.manager.ForwardMessageService;
import com.yelink.dfscommon.entity.DfsStandardMessageEntity;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * @Description: kafka监听
 * @Author: zhengfu
 * @Date: 2020/10/14
 */
@Slf4j
@Component
@AllArgsConstructor
public class KafkaConsumer {



    private final Map<String, ForwardMessageService> strategyMap = new ConcurrentHashMap<>();

    /**
     * 注入所有实现了ForwardMessageService接口的Bean
     *
     * @param strategyMap
     */
    @Autowired
    public KafkaConsumer(Map<String, ForwardMessageService> strategyMap) {
        this.strategyMap.putAll(strategyMap);
    }

    /**
     * 监听 dfs_value_chain 价值链主题,有消息就读取
     *
     * @param consumerRecords
     * @see com.yelink.dfscommon.config.KafkaTopic
     */
    @KafkaListener(topics = {"#{kafkaTopic.getEventValueChain()}", "#{kafkaTopic.getEventMainData()}"})
    public void forwardMessage(List<ConsumerRecord<?, ?>> consumerRecords) {
        log.info("本次批量处理kafka消息数：{}", consumerRecords.size());
        for (ConsumerRecord<?, ?> consumerRecord : consumerRecords) {
            // 消费的哪个topic、partition的消息,打印出消息内容
            log.info("kafka forward remind message消息：" + consumerRecord.topic() + "-" + consumerRecord.partition() + "-" + consumerRecord.value());
            //接收的数据格式：evomid, 产品id，告警id、操作类型：save\delete、时间间隔（前多少秒、后多少秒）
            //会送的ack ：成功或者失败
            try {
                DfsStandardMessageEntity kafkaCommandEntity = JSON.parseObject((String) consumerRecord.value(), DfsStandardMessageEntity.class);
                //处理kafka消息
                ForwardMessageService forwardMessageService = strategyMap.get(kafkaCommandEntity.getMessageModel() + "MsgService");
                if (forwardMessageService != null) {
                    forwardMessageService.dealMessage(kafkaCommandEntity);
                } else {
                    log.info("未找到该消息类型的具体处理实现类，消息内容：{}", consumerRecord.value());
                }
            } catch (Exception e) {
                log.error("消息处理错误，消息：{}", consumerRecord.value(), e);
            }

        }
    }


}
