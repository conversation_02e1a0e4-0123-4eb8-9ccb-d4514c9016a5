package com.yelink.assignment.listener.manager.impl;

import com.alibaba.fastjson.JSON;
import com.yelink.assignment.constant.ValueChainKafkaMessageConstants;
import com.yelink.assignment.entity.common.DeleteRecordEntity;
import com.yelink.assignment.entity.operation.OperationDayCountEntity;
import com.yelink.assignment.entity.operation.OperationOrderDeviceRelevanceEntity;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.OperationOrderLineRelevanceEntity;
import com.yelink.assignment.entity.operation.OperationOrderProcedureRelationEntity;
import com.yelink.assignment.entity.operation.OperationOrderTeamEntity;
import com.yelink.assignment.entity.operation.OperationOrderTeamRelevanceEntity;
import com.yelink.assignment.listener.manager.ForwardMessageService;
import com.yelink.assignment.service.DeleteRecordService;
import com.yelink.assignment.service.OperationDayCountService;
import com.yelink.assignment.service.OperationOrderDeviceRelevanceService;
import com.yelink.assignment.service.OperationOrderLineRelevanceService;
import com.yelink.assignment.service.OperationOrderProcedureRelationService;
import com.yelink.assignment.service.OperationOrderTeamRelevanceService;
import com.yelink.assignment.service.OperationOrderTeamService;
import com.yelink.assignment.service.OperationService;
import com.yelink.dfscommon.api.dfs.CommonInterface;
import com.yelink.dfscommon.constant.dfs.DfsEventTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.OperationObjectConstant;
import com.yelink.dfscommon.dto.dfs.DeleteRecordDetailDto;
import com.yelink.dfscommon.entity.DfsStandardMessageEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * dfs消息处理
 * @Date 2022/7/14 20:19
 */
@Slf4j
@AllArgsConstructor
@Service(ValueChainKafkaMessageConstants.WORK_ORDER_MSG_SERVICE)
public class WorkOrderMsgServiceImpl implements ForwardMessageService {

    @Resource
    private OperationService operationService;
    @Resource
    private OperationDayCountService operationDayCountService;
    @Resource
    private OperationOrderDeviceRelevanceService operationOrderDeviceRelevanceService;
    @Resource
    private OperationOrderLineRelevanceService operationOrderLineRelevanceService;
    @Resource
    private OperationOrderProcedureRelationService operationOrderProcedureRelationService;
    @Resource
    private OperationOrderTeamService operationOrderTeamService;
    @Resource
    private OperationOrderTeamRelevanceService operationOrderTeamRelevanceService;
    @Resource
    private DeleteRecordService deleteRecordService;
    @Resource
    private CommonInterface commonInterface;

    /**
     * 用于回退的map
     */
    private Map<String, Function<DeleteRecordEntity, Void>> returnMap;

    @PostConstruct
    public void returnMap() {
        returnMap.put("dfs_operation_day_count", this::returnOperationDayCount);
        returnMap.put("dfs_operation_order_procedure_relation", this::returnOperationOrderProcedureRelation);
        returnMap.put("dfs_operation_order_team", this::returnOperationOrderTeam);
        returnMap.put("dfs_operation_order_device_relevance", this::returnOperationOrderDeviceRelevance);
        returnMap.put("dfs_operation_order_line_relevance", this::returnOperationOrderLineRelevance);
        returnMap.put("dfs_operation_order_team_relevance", this::returnOperationOrderTeamRelevance);
        returnMap.put("dfs_operation_order", this::returnOperationOrder);
    }

    @Override
    public void dealMessage(DfsStandardMessageEntity dfsKafkaMessageEntity) {
        // 删除工单相关的表
        if (DfsEventTypeEnum.WORK_ORDER_DELETE_MESSAGE.getTypeCode().equals(dfsKafkaMessageEntity.getMessageType())) {
            WorkOrderEntity workOrderEntity = JSON.parseObject(JSON.toJSONString(dfsKafkaMessageEntity.getChangeAfterContent()), WorkOrderEntity.class);
            // 获取工单关联的作业工单列表
            List<OperationOrderEntity> operationOrderEntities = operationService.lambdaQuery().eq(OperationOrderEntity::getWorkOrderNum, workOrderEntity.getWorkOrderNumber()).list();
            if (CollectionUtils.isEmpty(operationOrderEntities)) {
                return;
            }
            List<String> operationNumbers = operationOrderEntities.stream().map(OperationOrderEntity::getOperationNumber).collect(Collectors.toList());
            List<Integer> operationIds = operationOrderEntities.stream().map(OperationOrderEntity::getId).collect(Collectors.toList());

            // 删除的数据需要储存
            recordDeleteRecordByWorkOrder(workOrderEntity, operationNumbers, operationIds);
            // 删除数据
            operationDayCountService.lambdaUpdate().in(OperationDayCountEntity::getOperationNumber, operationNumbers).remove();
            operationOrderProcedureRelationService.lambdaUpdate().in(OperationOrderProcedureRelationEntity::getOperationOrderNumber, operationNumbers).remove();
            operationOrderTeamService.lambdaUpdate().in(OperationOrderTeamEntity::getOperationOrderNumber, operationNumbers).remove();
            operationOrderDeviceRelevanceService.lambdaUpdate().in(OperationOrderDeviceRelevanceEntity::getOperationOrderId, operationIds).remove();
            operationOrderLineRelevanceService.lambdaUpdate().in(OperationOrderLineRelevanceEntity::getOperationOrderId, operationIds).remove();
            operationOrderTeamRelevanceService.lambdaUpdate().in(OperationOrderTeamRelevanceEntity::getOperationOrderId, operationIds).remove();
            operationService.lambdaUpdate().in(OperationOrderEntity::getId, operationIds).remove();
        }
        // 恢复工单相关的ams表
        if (DfsEventTypeEnum.WORK_ORDER_RETURN_MESSAGE.getTypeCode().equals(dfsKafkaMessageEntity.getMessageType())) {
            DeleteRecordDetailDto detailDto = JSON.parseObject(JSON.toJSONString(dfsKafkaMessageEntity.getChangeAfterContent()), DeleteRecordDetailDto.class);
            // 查询需要回退的单据
            List<DeleteRecordEntity> deleteRecordEntities = deleteRecordService.lambdaQuery().eq(DeleteRecordEntity::getOrderNumber, detailDto.getOrderNumber())
                    .eq(DeleteRecordEntity::getOrderType, detailDto.getOrderType())
                    .eq(DeleteRecordEntity::getCreateTime, detailDto.getCreateTime())
                    .list();
            for (DeleteRecordEntity deleteRecordEntity : deleteRecordEntities) {
                returnMap.get(deleteRecordEntity.getTableName()).apply(deleteRecordEntity);
            }
            // 将记录删除
            deleteRecordService.lambdaUpdate().eq(DeleteRecordEntity::getOrderNumber, detailDto.getOrderNumber())
                    .eq(DeleteRecordEntity::getOrderType, detailDto.getOrderType())
                    .eq(DeleteRecordEntity::getCreateTime, detailDto.getCreateTime())
                    .remove();
        }
    }

    /**
     * 删除的数据需要储存
     */
    private void recordDeleteRecordByWorkOrder(WorkOrderEntity workOrderEntity, List<String> operationNumbers, List<Integer> operationIds) {
        List<DeleteRecordEntity> deleteRecordEntities = new ArrayList<>();
        // 获取dfs版本号
        String dfsVersion;
        try {
            dfsVersion = JacksonUtil.getResponseObject(commonInterface.getDfsVersion(), String.class);
        } catch (Exception e) {
            dfsVersion = "dfs";
        }
        Date deleteDate = workOrderEntity.getDeleteTime();
        List<OperationDayCountEntity> operationDayCountEntities = operationDayCountService.lambdaQuery().in(OperationDayCountEntity::getOperationNumber, operationNumbers).list();
        if (!CollectionUtils.isEmpty(operationDayCountEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderEntity.getWorkOrderNumber()).orderType(OperationObjectConstant.WORK_ORDER).tableName("dfs_operation_day_count").jsonData(JSON.toJSONString(operationDayCountEntities)).tableSource("dfs-assignment").version(dfsVersion).createBy(workOrderEntity.getDeleteName()).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<OperationOrderProcedureRelationEntity> orderProcedureRelationEntities = operationOrderProcedureRelationService.lambdaQuery().in(OperationOrderProcedureRelationEntity::getOperationOrderNumber, operationNumbers).list();
        if (!CollectionUtils.isEmpty(orderProcedureRelationEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderEntity.getWorkOrderNumber()).orderType(OperationObjectConstant.WORK_ORDER).tableName("dfs_operation_order_procedure_relation").jsonData(JSON.toJSONString(orderProcedureRelationEntities)).tableSource("dfs-assignment").version(dfsVersion).createBy(workOrderEntity.getDeleteName()).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<OperationOrderTeamEntity> operationOrderTeamEntities = operationOrderTeamService.lambdaQuery().in(OperationOrderTeamEntity::getOperationOrderNumber, operationNumbers).list();
        if (!CollectionUtils.isEmpty(operationOrderTeamEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderEntity.getWorkOrderNumber()).orderType(OperationObjectConstant.WORK_ORDER).tableName("dfs_operation_order_team").jsonData(JSON.toJSONString(operationOrderTeamEntities)).tableSource("dfs-assignment").version(dfsVersion).createBy(workOrderEntity.getDeleteName()).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<OperationOrderDeviceRelevanceEntity> orderDeviceRelevanceEntities = operationOrderDeviceRelevanceService.lambdaQuery().in(OperationOrderDeviceRelevanceEntity::getOperationOrderId, operationIds).list();
        if (!CollectionUtils.isEmpty(orderDeviceRelevanceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderEntity.getWorkOrderNumber()).orderType(OperationObjectConstant.WORK_ORDER).tableName("dfs_operation_order_device_relevance").jsonData(JSON.toJSONString(orderDeviceRelevanceEntities)).tableSource("dfs-assignment").version(dfsVersion).createBy(workOrderEntity.getDeleteName()).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<OperationOrderLineRelevanceEntity> orderLineRelevanceEntities = operationOrderLineRelevanceService.lambdaQuery().in(OperationOrderLineRelevanceEntity::getOperationOrderId, operationIds).list();
        if (!CollectionUtils.isEmpty(orderLineRelevanceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderEntity.getWorkOrderNumber()).orderType(OperationObjectConstant.WORK_ORDER).tableName("dfs_operation_order_line_relevance").jsonData(JSON.toJSONString(orderLineRelevanceEntities)).tableSource("dfs-assignment").version(dfsVersion).createBy(workOrderEntity.getDeleteName()).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<OperationOrderTeamRelevanceEntity> orderTeamRelevanceEntities = operationOrderTeamRelevanceService.lambdaQuery().in(OperationOrderTeamRelevanceEntity::getOperationOrderId, operationIds).list();
        if (!CollectionUtils.isEmpty(orderTeamRelevanceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderEntity.getWorkOrderNumber()).orderType(OperationObjectConstant.WORK_ORDER).tableName("dfs_operation_order_team_relevance").jsonData(JSON.toJSONString(orderTeamRelevanceEntities)).tableSource("dfs-assignment").version(dfsVersion).createBy(workOrderEntity.getDeleteName()).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<OperationOrderEntity> orderEntities = operationService.lambdaQuery().in(OperationOrderEntity::getId, operationIds).list();
        if (!CollectionUtils.isEmpty(orderEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderEntity.getWorkOrderNumber()).orderType(OperationObjectConstant.WORK_ORDER).tableName("dfs_operation_order").jsonData(JSON.toJSONString(orderEntities)).tableSource("dfs-assignment").version(dfsVersion).createBy(workOrderEntity.getDeleteName()).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        deleteRecordService.saveBatch(deleteRecordEntities);
        // 推送给dfs记录删除的数据，用于记录
//        List<com.yelink.dfscommon.entity.DeleteRecordEntity> recordEntities = JacksonUtil.convertObject(deleteRecordEntities, com.yelink.dfscommon.entity.DeleteRecordEntity.class);
//        workOrderInterface.recordDeleteRecord(recordEntities);
    }

    private Void returnOperationOrder(DeleteRecordEntity deleteRecordEntity) {
        List<OperationOrderEntity> operationOrderEntities = JSON.parseArray(deleteRecordEntity.getJsonData(), OperationOrderEntity.class);
        operationService.saveBatch(operationOrderEntities);
        return null;
    }

    private Void returnOperationOrderTeamRelevance(DeleteRecordEntity deleteRecordEntity) {
        List<OperationOrderTeamRelevanceEntity> teamRelevanceEntities = JSON.parseArray(deleteRecordEntity.getJsonData(), OperationOrderTeamRelevanceEntity.class);
        operationOrderTeamRelevanceService.saveBatch(teamRelevanceEntities);
        return null;
    }

    private Void returnOperationOrderLineRelevance(DeleteRecordEntity deleteRecordEntity) {
        List<OperationOrderLineRelevanceEntity> lineRelevanceEntities = JSON.parseArray(deleteRecordEntity.getJsonData(), OperationOrderLineRelevanceEntity.class);
        operationOrderLineRelevanceService.saveBatch(lineRelevanceEntities);
        return null;
    }

    private Void returnOperationOrderDeviceRelevance(DeleteRecordEntity deleteRecordEntity) {
        List<OperationOrderDeviceRelevanceEntity> deviceRelevanceEntities = JSON.parseArray(deleteRecordEntity.getJsonData(), OperationOrderDeviceRelevanceEntity.class);
        operationOrderDeviceRelevanceService.saveBatch(deviceRelevanceEntities);
        return null;
    }

    private Void returnOperationOrderTeam(DeleteRecordEntity deleteRecordEntity) {
        List<OperationOrderTeamEntity> teamEntities = JSON.parseArray(deleteRecordEntity.getJsonData(), OperationOrderTeamEntity.class);
        operationOrderTeamService.saveBatch(teamEntities);
        return null;
    }

    private Void returnOperationOrderProcedureRelation(DeleteRecordEntity deleteRecordEntity) {
        List<OperationOrderProcedureRelationEntity> procedureRelationEntities = JSON.parseArray(deleteRecordEntity.getJsonData(), OperationOrderProcedureRelationEntity.class);
        operationOrderProcedureRelationService.saveBatch(procedureRelationEntities);
        return null;
    }

    private Void returnOperationDayCount(DeleteRecordEntity deleteRecordEntity) {
        List<OperationDayCountEntity> operationDayCountEntities = JSON.parseArray(deleteRecordEntity.getJsonData(), OperationDayCountEntity.class);
        operationDayCountService.saveBatch(operationDayCountEntities);
        return null;
    }

}
