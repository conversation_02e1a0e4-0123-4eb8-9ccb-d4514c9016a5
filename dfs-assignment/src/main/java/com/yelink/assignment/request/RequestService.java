package com.yelink.assignment.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yelink.assignment.entity.WorkOrderEntity;
import com.yelink.assignment.entity.model.FacilitiesEntity;
import com.yelink.assignment.entity.model.ProductionLineEntity;
import com.yelink.assignment.entity.shift.ShiftEntity;
import com.yelink.dfscommon.api.dfs.CommonInterface;
import com.yelink.dfscommon.api.dfs.DictInterface;
import com.yelink.dfscommon.api.dfs.FacilitiesInterface;
import com.yelink.dfscommon.api.dfs.LineInterface;
import com.yelink.dfscommon.api.dfs.ProductionInterface;
import com.yelink.dfscommon.api.dfs.ShiftInterface;
import com.yelink.dfscommon.api.dfs.SysTeamInterface;
import com.yelink.dfscommon.api.dfs.UserInterface;
import com.yelink.dfscommon.api.dfs.WorkCenterInterface;
import com.yelink.dfscommon.api.dfs.WorkOrderInterface;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @description: 对dfs发送请求
 * @author: zengfu
 * @create: 2022-03-06 19:47
 **/
@Service
@AllArgsConstructor
@Slf4j
public class RequestService {

    private LineInterface lineInterface;
    private DictInterface dictInterface;
    private UserInterface userInterface;
    private ShiftInterface shiftInterface;
    private CommonInterface commonInterface;
    private WorkOrderInterface workOrderInterface;
    private ProductionInterface productionInterface;
    private WorkCenterInterface workCenterInterface;
    private SysTeamInterface sysTeamInterface;
    private FacilitiesInterface facilitiesInterface;


    public ProductionLineEntity getLineByLineId(Integer lineId) {
        ResponseData data = lineInterface.getLineDetail(lineId);
        if (data.getCode().equals(ResponseData.SUCCESS_CODE) && data.getData() != null) {
            JSONObject json = (JSONObject) JSON.toJSON(data.getData());
            ProductionLineEntity entity = JSONObject.parseObject(json.toJSONString(), ProductionLineEntity.class);
            return entity;
        }
        return null;
    }

    public ShiftEntity getShiftById(Integer shiftId) {
        if (shiftId == null) {
            return null;
        }
        ResponseData responseData = shiftInterface.selectById(shiftId);
        if (responseData.getData() != null) {
            return JSONObject.parseObject(JSON.toJSONString(responseData.getData()), ShiftEntity.class);
        }
        return null;
    }

    public String getUsername() {
        try {
            ResponseData userData = userInterface.getUserName();
            if (userData != null && userData.getCode().equals(ResponseData.SUCCESS_CODE)) {
                return (String) userData.getData();
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
        }
        return null;
    }

    public String getNickName(String userName) {
        try {
            if (StringUtils.isBlank(userName)) {
                return null;
            }
            ResponseData responseData = userInterface.getUserNickName(userName);
            if (responseData.getData() != null) {
                return (String) responseData.getData();
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
        }
        return null;
    }

    public Date getRecordDate(Date now) {
        ResponseData responseData = dictInterface.getRecordDate(now.getTime());
        return new Date((long) responseData.getData());
    }

    public Double getAutoCountByLine(ProductionLineEntity line, Date start, Date end) {
        ResponseData responseData = commonInterface.getAutoCountByLine(line.getProductionLineId(), start.getTime(), end.getTime());
        if (responseData.getData() != null) {
            return (Double) responseData.getData();
        }
        return null;
    }

    public List<FacilitiesEntity> getLastFacilities(ProductionLineEntity lineEntity) {
        ResponseData responseData = commonInterface.getLastFacilities(lineEntity.getProductionLineId());
        if (responseData.getData() != null) {
            return JSONArray.parseArray(JSON.toJSONString(responseData.getData()), FacilitiesEntity.class);
        }
        return null;
    }

    public WorkOrderEntity getWorkOrderByNumber(String workOrderNumber) {
        ResponseData responseData = workOrderInterface.getWorkOrderByNumber(workOrderNumber);
        if (responseData.getData() != null) {
            return JSON.parseObject(JSON.toJSONString(responseData.getData()), WorkOrderEntity.class);
        }
        return null;
    }

    public List<Integer> getOtherLineId(Integer lineId) {
        ResponseData responseData = commonInterface.getOtherLineId(lineId);
        if (responseData.getData() != null) {
            return (List) responseData.getData();
        }
        return null;

    }

    public void saveOrUpdateRecordOutPut(Double count, Date recordDate) {
        productionInterface.saveOrUpdateRecordOutPut(count, recordDate.getTime());
    }

    public void updateWorkOrder(WorkOrderEntity workOrderEntity) {
        workOrderInterface.innerUpdateWorkOrderReport(JSON.toJSONString(workOrderEntity));
    }

    public void updateWorkOrderDayCount(String workOrderNum, double dayCount, double dayUnqualified, Date reportDate) {
        workOrderInterface.innerUpdateWorkOrderDayCount(workOrderNum, dayCount, dayUnqualified, reportDate.getTime());
    }

    public void innerUpdateWorkOrderState(String workOrderNumber, Integer state) {
        workOrderInterface.innerUpdateWorkOrderState(workOrderNumber, state);
    }

    public Double getAutoCountByLineAccumulation(ProductionLineEntity line, Date start, Date end, Integer id, String reportLine) {
        ResponseData responseData = commonInterface.getAutoCountByLineAccumulation(
                line.getProductionLineId(), start.getTime(), end.getTime(), id, reportLine);
        if (responseData.getData() != null) {
            return (Double) responseData.getData();
        }
        return null;
    }

    public void removeEuiMark(Integer recordId, String type) {
        commonInterface.removeEuiMark(recordId, type);
    }

    public void saveOrUpdateEuiMark(String eui, Integer countId, Date date, Integer recordId, String type) {
        commonInterface.saveOrUpdateEuiMark(eui, countId, date.getTime(), recordId, type);
    }

    public WorkCenterEntity getWorkCenterById(Integer workCenterId) {
        return JacksonUtil.getResponseObject(workCenterInterface.getWorkCenterById(workCenterId), WorkCenterEntity.class);
    }

    public List<SysTeamEntity> getTeamListByIds(List<Integer> teamIds) {
        return JacksonUtil.getResponseArray(sysTeamInterface.getTeamListByIds(teamIds), SysTeamEntity.class);
    }

    public SysTeamEntity getTeamById(Integer teamId) {
        return JacksonUtil.getResponseObject(sysTeamInterface.getTeamById(teamId), SysTeamEntity.class);
    }

    public Boolean getIsCalConcurrently(Integer fid) {
        return JacksonUtil.getResponseObject(facilitiesInterface.getIsCalConcurrently(fid), Boolean.class);
    }

}
