package com.yelink.assignment.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.MediaType;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @description: excel 导入导出统一工具
 * @author: shuang
 * @time: 2022/5/9
 */
@Slf4j
public class ExcelTemplateImportUtil {


    /**
     * 导出或下载模板文件使用
     * @param response
     * @param bytes
     * @param templateName  需要包含文件后缀
     */
    public static void responseToClient(HttpServletResponse response, byte[] bytes, String templateName) {
        responseToClient(response, new ByteArrayInputStream(bytes),templateName);
    }

    /**
     * 导出或下载模板文件使用 :该方法会关闭输入的流参数
     * @param response
     * @param inputStream 下载的文件流
     * @param templateName  需要包含文件后缀
     */
    public static void responseToClient(HttpServletResponse response, InputStream inputStream, String templateName) {
        try {
            response.setHeader("Content-disposition", "attachment;filename=\"" + templateName +"\"; filename*=utf-8'zh_cn'"+templateName );
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            OutputStream outputStream = response.getOutputStream();
            //设置缓冲区
            int len;
            byte[] by = new byte[1024];
            //将input流写到缓冲区，然后经过outputStream返回客户端
            while ((len = inputStream.read(by)) != -1) {
                outputStream.write(by, 0, len);
            }
            outputStream.flush();
            outputStream.close();
            inputStream.close();
            return;
        }catch (IOException e){
            log.error("客户端取消下载或连接断开",e);
        }catch (Exception e){
            log.error("导出/下载文件错误",e);
        }


    }

    /**
     *根据resource导出excel模板
     * @param response
     * @param resource
     * @param fileName
     */
    public static void downLoadExcelTemplate(HttpServletResponse response, InputStream resource, String fileName) {
        InputStream bis = null;
        try {
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName+ "\"; filename*=utf-8'zh_cn'" + fileName);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            bis = new BufferedInputStream(resource);
            XSSFWorkbook wb = new XSSFWorkbook(bis);
            ServletOutputStream servletOutputStream = response.getOutputStream();
            OutputStream os = new BufferedOutputStream(servletOutputStream);
            wb.write(os);
            os.flush();
            servletOutputStream.close();
            os.close();
        } catch (IOException ex) {
            log.error("文件获取失败", ex);
        } finally {
            IOUtils.closeQuietly(bis);
        }
    }
}
