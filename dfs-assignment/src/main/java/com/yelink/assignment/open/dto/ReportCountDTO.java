package com.yelink.assignment.open.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2021-03-26 18:33
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ReportCountDTO {

    /**
     * 作业工单号
     */
    @ApiModelProperty(value = "作业工单号")
    private String operationNumber;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    private Double finishCount;

    /**
     * 不合格数量
     */
    @ApiModelProperty(value = "不合格数量")
    private Double unqualified;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    private String userName;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private String batch;

    /**
     * 班次id
     */
    @ApiModelProperty(value = "班次id")
    private Integer shiftId;

    /**
     * 有效工时
     */
    @ApiModelProperty(value = "有效工时")
    private Double effectiveHours;

    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 载具号
     */
    @ApiModelProperty(value = "载具号")
    private String vehicleCode;

    /**
     * 操作员
     */
    @ApiModelProperty(value = "操作员")
    private String operator;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    private String[] picUrls;

    /**
     * 不良描述
     */
    @ApiModelProperty(value = "不良描述")
    private String defectDesc;

    @ApiModelProperty(value = "班组id")
    private Integer teamId;

}




