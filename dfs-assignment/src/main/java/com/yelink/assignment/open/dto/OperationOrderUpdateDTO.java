package com.yelink.assignment.open.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.assignment.entity.operation.OperationOrderTeamEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-03-03
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OperationOrderUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 作业工单id
     */
    @ApiModelProperty(value = "作业工单id", required = true)
    private Integer id;

    /**
     * 作业工单编号
     */
    @ApiModelProperty("作业工单编号")
    private String operationNumber;

    /**
     * 生产工单编号
     */
    @ApiModelProperty("生产工单编号")
    private String workOrderNum;

    /**
     * 班次类型
     */
    @ApiModelProperty("班次类型")
    private String shiftType;

    /**
     * 班次id
     */
    @ApiModelProperty("班次id")
    private Integer shiftId;


    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 状态id 1-创建、2-生效、3-投产、4-挂起、5-完成
     */
    @ApiModelProperty("状态id 1-创建、2-生效、3-投产、4-挂起、5-完成")
    private Integer state;

    /**
     * 状态名称 创建、生效、投产、挂起、完成
     */
    @ApiModelProperty("状态名称 创建、生效、投产、挂起、完成")
    private String stateName;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    private Double productCount;

    /**
     * 计划开始时间
     */
    @ApiModelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planStartDate;

    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planEndDate;

    /**
     * 优先级1-正常（默认） 2-优先 3-加急 4-特急
     */
    @ApiModelProperty("优先级1-正常（默认） 2-优先 3-加急 4-特急")
    private Integer priority;

    /**
     * 不良数量
     */
    @ApiModelProperty("不良数量")
    private Double unqualified;

    /**
     * 完成数量
     */
    @ApiModelProperty("完成数量")
    private Double finishCount;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    private Double inputTotal;

    /**
     * 待产数量
     */
    @ApiModelProperty("待产数量")
    private Double waitProductCount;

    /**
     * 制造单元名称
     */
    @ApiModelProperty("制造单元名称")
    private String lineName;

    /**
     * 制造单元id
     */
    @ApiModelProperty("制造单元id")
    private Integer lineId;

    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    private String workCenterName;
    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心Id")
    private Integer workCenterId;

    /**
     * 实际工时
     */
    @ApiModelProperty("实际工时")
    private Double actualWorkingHours;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际完成时间
     */
    @ApiModelProperty("实际完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 创建人账号
     */
    @ApiModelProperty("创建人账号")
    private String createBy;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String createName;


    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    private String updateName;

    /**
     * 修改人账号
     */
    @ApiModelProperty("修改人账号")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 批次
     */
    @ApiModelProperty("批次")
    private String batch;

    /**
     * 载具号
     */
    @ApiModelProperty("载具号")
    private String vehicleCode;

    /**
     * 工装（工装模具、模具编号）
     */
    @ApiModelProperty("工装（工装模具、模具编号）")
    private String tooling;

    /**
     * 报工系数
     */
    @ApiModelProperty("报工系数")
    private Integer coefficient;

    /**
     * 操作员
     */
    @ApiModelProperty("操作员")
    private String producer;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 是否自动完工
     */
    @ApiModelProperty("是否自动完工")
    private Boolean isAutoCompleted;

    /**
     * 班组ID
     */
    @ApiModelProperty("班组ID")
    private Integer teamId;

    /**
     * 工单投产班组成员列表
     */
    @ApiModelProperty("工单投产班组成员列表")
    private List<OperationOrderTeamEntity> operationOrderTeamEntities;

    /**
     * 工单关联班组id
     */
    @ApiModelProperty("工单关联班组id")
    private List<Integer> relevanceTeamIds;

    /**
     * 工单关联设备id
     */
    @ApiModelProperty("工单关联设备id")
    private List<Integer> relevanceDeviceIds;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    private Integer skuId;
}
