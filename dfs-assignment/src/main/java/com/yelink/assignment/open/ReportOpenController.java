package com.yelink.assignment.open;

import com.alibaba.fastjson.JSON;
import com.yelink.assignment.constant.Constant;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.assignment.mapper.UnqualifiedImgMapper;
import com.yelink.assignment.open.dto.ReportActionDTO;
import com.yelink.assignment.open.dto.ReportCountDTO;
import com.yelink.assignment.service.OperationService;
import com.yelink.assignment.service.ReportCountService;
import com.yelink.assignment.service.ReportLineService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Description: 作业工单报工对外接口
 * @Author: zengzhengfu
 * @Date: 2022/9/22
 */
@RestController
@AllArgsConstructor
@Api(tags = "V1/价值流/作业工单报工接口")
@RequestMapping("/v1/open/report")
public class ReportOpenController {

    private ReportCountService reportCountService;
    private ReportLineService reportLineService;
    private OperationService operationService;
    private UnqualifiedImgMapper unqualifiedImgMapper;

    /**
     * 产线报工--选择工单开始停止
     * <p>
     * workOrder
     * action    0-投产  1-完成 2-挂起
     *
     * @return
     */
    @PostMapping("/update/state")
    @ApiOperation(value = "修改工单状态")
    @OperLog(module = "工单状态变更", type = OperationType.ADD, desc = "更新了单号为#{workOrderNumber}的状态")
    public Result reportAction(@RequestBody ReportActionDTO reportActionDTO) {
        reportCountService.dealReportCountRecord(reportActionDTO.getOperationNumber(), reportActionDTO.getAction(), Constant.MANUAL);
        return Result.success();
    }

    /**
     * 产线报工--输入工单产量
     *
     * @param entity 工单产量
     * @return
     */
    @PostMapping("/count")
    @ApiOperation(value = "工单报工--输入工单产量")
    @OperLog(module = "产线报工", type = OperationType.ADD, desc = "新增了单号为#{workOrder}的报工记录")
    public Result countOutput(@RequestBody ReportCountDTO entity) {
        ReportLineEntity reportLineEntity = JSON.parseObject(JSON.toJSONString(entity), ReportLineEntity.class);
        reportCountService.countOutput(reportLineEntity);
        return Result.success();
    }

    /**
     * 修改报工数量
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @ApiOperation(value = "工单报工--修改报工数量")
    @OperLog(module = "产线报工", type = OperationType.UPDATE, desc = "更新了单号为#{workOrder}为的报工记录")
    public Result updateCount(@RequestBody ReportLineEntity entity) {
        reportCountService.updateCount(entity);
        return Result.success();
    }

    /**
     * 查询报工记录
     *
     * @param operationNumber
     * @param current
     * @param size
     * @return
     */
    @ApiOperation(value = "工单报工--获取报工记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "operationNumber", value = "作业工单号", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "type", value = "report-报工 auto-自动记录", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "current", value = "当前页", dataType = "Integer", defaultValue = ""),
            @ApiImplicitParam(name = "size", value = "每页显示条数", dataType = "Integer", defaultValue = "")
    })
    @GetMapping("/history/count")
    public Result<PageResult<ReportLineEntity>> getInputHistory(@RequestParam(value = "operationNumber") String operationNumber,
                                                                @RequestParam(value = "type", required = false) String type,
                                                                @RequestParam(value = "current", required = false) Integer current,
                                                                @RequestParam(value = "size", required = false) Integer size) {
        return Result.success(operationService.getInputHistory(operationNumber, type, current, size));
    }

    /**
     * 查询报工记录详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询报工记录详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "报工记录ID", dataType = "Integer", defaultValue = "")
    })
    @GetMapping("/history/count/detail")
    public Result<ReportLineEntity> getInputHistoryDetail(@RequestParam(value = "id") Integer id) {
        ReportLineEntity entity = reportLineService.getById(id);
        if (entity != null) {
            List<String> urlList = unqualifiedImgMapper.selectUrlList(entity.getId());
            entity.setPicUrls(urlList.toArray(new String[urlList.size()]));
        }
        return Result.success(entity);
    }

}
