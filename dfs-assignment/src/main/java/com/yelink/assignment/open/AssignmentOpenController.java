package com.yelink.assignment.open;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.assignment.entity.operation.OperationOrderEntity;
import com.yelink.assignment.entity.operation.dto.OperationOrderSelectDTO;
import com.yelink.assignment.open.dto.OperationOrderInsertDTO;
import com.yelink.assignment.open.dto.OperationOrderUpdateDTO;
import com.yelink.assignment.service.OperationService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.pojo.message.KafkaMessageResult;
import com.yelink.dfscommon.pojo.message.NewMessageEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import lombok.AllArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 作业工单对外接口
 *
 * <AUTHOR>
 * @Date 2022/08/18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/v1/open/operation_order")
@Api(tags = "V1/价值流/作业工单接口")
public class AssignmentOpenController {

    @Resource
    private OperationService operationService;

    /**
     * 查询作业工单列表
     *
     * @param operationOrderDTO
     * @return
     */
    @ApiOperation(value = "查询作业工单列表")
    @PostMapping("/list")
    public Result<PageResult<OperationOrderEntity>> getOperationOrderList(@RequestBody OperationOrderSelectDTO operationOrderDTO) {
        Page<OperationOrderEntity> page = operationService.getOperationOrderList(operationOrderDTO);
        return Result.success(page);
    }


    /**
     * 通过ID查询作业工单详情
     *
     * @param id 作业工单id
     * @return
     */
    @ApiOperation(value = "通过ID查询作业工单详情")
    @ApiImplicitParam(paramType = "path", dataType = "int", name = "id", required = true, value = "作业工单id")
    @GetMapping("/detail/{id}")
    public Result<OperationOrderEntity> getOperationOrderDetail(@PathVariable(value = "id") Integer id) {
        return Result.success(operationService.getById(id));
    }

    /**
     * 通过作业工单单号查询作业工单详情
     *
     * @param operationOrderNum
     * @return
     */
    @ApiImplicitParam(paramType = "query", dataType = "String", name = "operationOrderNum", required = true, value = "作业工单单号")
    @ApiOperation(value = "通过作业工单单号查询作业工单详情")
    @GetMapping("/detail/by/number")
    public Result getByOperationNumber(@RequestParam(value = "operationOrderNum") String operationOrderNum) {
        OperationOrderEntity entity = operationService.getByOperationNumber(operationOrderNum);
        return Result.success(entity);
    }

    /**
     * 新增作业工单
     *
     * @param dto
     * @param bindingResult
     * @return
     */
    @ApiOperation(value = "新增作业工单")
    @PostMapping("/add")
    @OperLog(module = "作业工单", type = OperationType.ADD, desc = "新增了作业工单号为#{operationNumber}的作业工单")
    public Result insertOperationOrder(@RequestBody OperationOrderInsertDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        OperationOrderEntity operationOrderEntity = operationService.insertOperationOrder(JacksonUtil.convertObject(dto, OperationOrderEntity.class));
        return Result.success(operationOrderEntity.getId());
    }

    /**
     * 编辑作业工单
     *
     * @param dto
     * @param bindingResult
     * @return
     */
    @ApiOperation(value = "编辑作业工单")
    @OperLog(module = "作业工单", type = OperationType.UPDATE, desc = "修改了作业工单号为#{operationNumber}的作业工单")
    @PutMapping("/update")
    public Result edit(@RequestBody OperationOrderUpdateDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        OperationOrderEntity edit = operationService.edit(JacksonUtil.convertObject(dto, OperationOrderEntity.class));
        return Result.success();
    }

    /**
     * 删除作业工单
     *
     * @param id 作业工单id
     * @return
     */
    @ApiOperation(value = "通过ID删除作业工单")
    @ApiImplicitParam(dataType = "int", name = "id", required = true, value = "作业工单id")
    @OperLog(module = "作业工单", type = OperationType.DELETE, desc = "删除了作业工单号为#{operationNumber}的作业工单")
    @DeleteMapping("/delete/{id}")
    public Result delete(@PathVariable(value = "id") Integer id) {
        operationService.removeById(id);
        return Result.success();
    }

    /**
     * kafka消息说明（返回参数说明，非接口）
     *
     * @return
     */
    @ApiOperation(value = "kafka消息说明（返回参数说明，非接口）", notes = "topic:event_value_chain\n" + "kafka消息method参数说明：\n" +
            "operationOrder.autoComplete：作业工单自动完工\n" +
            "operationOrder.statusChange：作业工单状态改变\n" +
            "operationOrder.update：作业工单编辑\n" +
            "operationOrder.add：作业工单新增\n" +
            "operationOrder.report：作业工单新增报工\n" +
            "operationOrder.delete：作业工单删除",
            extensions = {
                    @Extension(properties = {
                            @ExtensionProperty(name = "topic", value = "event_value_chain"),
                    }),
                    @Extension(name = "messageModelId", properties = {
                            @ExtensionProperty(name = "作业工单自动完工", value = "operationOrder.autoComplete"),
                            @ExtensionProperty(name = "作业工单状态改变", value = "operationOrder.statusChange"),
                            @ExtensionProperty(name = "作业工单编辑", value = "operationOrder.update"),
                            @ExtensionProperty(name = "作业工单新增", value = "operationOrder.add"),
                            @ExtensionProperty(name = "作业工单新增报工", value = "operationOrder.report"),
                            @ExtensionProperty(name = "作业工单删除", value = "operationOrder.delete"),
                    })
            })
    @GetMapping("/kafka/message")
    public KafkaMessageResult<NewMessageEntity<OperationOrderEntity>> message() {
        return KafkaMessageResult.success();
    }

}
