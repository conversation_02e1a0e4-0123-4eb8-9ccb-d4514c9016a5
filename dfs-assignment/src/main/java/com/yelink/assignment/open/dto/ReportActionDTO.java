package com.yelink.assignment.open.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2021-03-26 18:33
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ReportActionDTO {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "作业工单号")
    private String operationNumber;

    /**
     * 0-投产  1-完成 2-挂起
     */
    @ApiModelProperty(value = "0-投产  1-完成 2-挂起")
    private Integer action;



}




