package com.yelink.assignment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yelink.assignment.entity.report.ReportLineEntity;
import com.yelink.dfscommon.dto.OperationDayCountDTO;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2022/1/16
 */
@Repository
public interface ReportLineMapper extends BaseMapper<ReportLineEntity> {

    /**
     * 更新自动记录
     *
     * @param operationNumber
     * @param finishCount
     * @param time
     * @param type
     */
    void updateReportLine(String operationNumber, double finishCount, Date time, String type);

    /**
     * 工单进度更新自动记录，乐观锁，如果时间和数量和原来的不一致则不更新
     *
     * @param entity
     * @param finishCount
     * @param now
     * @return
     */
    int updateReportLineByProgress(ReportLineEntity entity, Double finishCount, Date now);

    /**
     * 新增 主键回显
     *
     * @param reportLineEntity
     * @return
     */
    int insertWriteBackId(ReportLineEntity reportLineEntity);

    /**
     * 按人员统计产出
     *
     * @param modelId
     * @param recordDate
     */
    List<ReportLineEntity> getFinishCountGroupByUser(Integer modelId, Date recordDate);

    /**
     * 按产线统计完成数
     *
     * @param modelId
     * @param recordDate
     * @return
     */
    List<OperationDayCountDTO> getFinishCountGroupByLine(Integer modelId, Date recordDate);
}
