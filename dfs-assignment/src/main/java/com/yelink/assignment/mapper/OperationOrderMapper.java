package com.yelink.assignment.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yelink.assignment.entity.operation.OperationOrderEntity;

import java.util.Date;
import java.util.List;

/**
 * OperationOrder
 *
 * <AUTHOR>
 * @Date 2022-01-21 10:26
 */
public interface OperationOrderMapper extends BaseMapper<OperationOrderEntity> {

    /**
     * 通过产线id查询作业工单(生效、投产、挂起、今日完成)
     *
     * @param lineId 产线ID
     * @param state 状态
     * @return
     */
    List<OperationOrderEntity> selectListByLineId(Integer lineId, String state);

    /**
     * 按工单统计指定日期完成数
     *
     * @param date
     */
    List<OperationOrderEntity> getFinishCountGroupByWorkOrder(Date date);

}
