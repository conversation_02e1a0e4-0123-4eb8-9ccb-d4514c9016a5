package com.yelink;

import com.asyncexcel.springboot.EnableAsyncExcel;
import com.yelink.assignment.config.FeignConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */

@SpringBootApplication
@EnableDiscoveryClient
@EnableAsyncExcel
@MapperScan("com.yelink.assignment.mapper")
@EnableFeignClients(basePackages = {"com.yelink.dfscommon.api"}, defaultConfiguration = FeignConfig.class)
public class DfsAssignmentApplication {

    public static void main(String[] args) {
        SpringApplication.run(DfsAssignmentApplication.class, args);
    }

}
