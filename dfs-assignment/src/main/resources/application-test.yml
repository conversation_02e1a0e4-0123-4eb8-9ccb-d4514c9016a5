server:
  port: 9303
  servlet:
    encoding:
      charset: UTF-8
      force: true
    context-path: /assignment
spring:
  datasource:
    username: root
    password: yelink123
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${mysql.ip}:3306/${assignment.tableSchema}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&useSSL=false&serverTimezone=Asia/Shanghai&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true
    type: com.zaxxer.hikari.HikariDataSource
    dbcp2:
      initial-size: 5
      min-idle: 5
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
  redis:
    database: 14
    host: ${redis.ip}
    port: 6379
    password: null
    jedis:
      pool:
        max-wait: 8
        max-active: 1000
        max-idle: 100
        min-idle: 1
  application:
    name: dfs-assignment
  servlet:
    multipart:
      max-request-size: 5MB
      max-file-size: 5MB
  kafka:
    bootstrap-servers: ${kafka.ip}
    consumer:
      group-id: consumer-group-assignment
      auto-offset-reset: latest
      enable-auto-commit: true
      auto-commit-interval: 100
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 100
      properties:
        session:
          timeout.ms: 120000
    listener:
      type: batch
      concurrency: 3
  cloud:
    nacos:
      discovery:
        server-addr: ${local.vip}:8001
        ip: ${location.ip}
        port: ${server.port}
        metadata:
          version: ${server.version}
  flyway:
    # 是否启用flyway
    enabled: true
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration/exploit
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # flyway 的 clean 命令会删除指定 schema 下的所有 table, 生产务必禁掉。这个默认值是 false 理论上作为默认配置是不科学的。
    clean-disabled: true
    # 是否允许不按顺序迁移 由于有分支版本，需要置为true
    out-of-order: true
  main: #解决@FeignClient值重复问题,解決重复bean
    allow-bean-definition-overriding: true

mybatis-plus:
  mapper-locations: classpath:mapper/*Mapper.xml
  typeAliasesPackage: com.yelink.assignment.entity
  typeEnumsPackage: com.yelink.dfs
  global-config:
    db-config:
      #主键类型  0:数据库ID自增   1:用户输入id  2:全局唯一id(IdWorker)  3:全局唯一ID(uuid)
      table-underline: true
      id-type: auto
  configuration:
    #这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


#关闭hystrix默认熔断
feign:
  hystrix:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000

#日志级别
logging:
  level:
    com.yelink: INFO
    com.yelink.dfs.mapper: INFO
    com.yelink.dfs.service.target: INFO

assignment:
  tableSchema: dfs_assignment_test


kafka:
  topic-suffix: _test
