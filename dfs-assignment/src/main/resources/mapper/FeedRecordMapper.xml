<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.feed.FeedRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.assignment.entity.feed.FeedRecordEntity">
        <result column="feed_record_id" property="feedRecordId" />
        <result column="line_id" property="lineId" />
        <result column="fac_id" property="facId" />
        <result column="operation_order_num" property="operationOrderNum" />
        <result column="work_order_num" property="workOrderNum" />
        <result column="material_name" property="materialName" />
        <result column="material_code" property="materialCode" />
        <result column="batch_number" property="batchNumber" />
        <result column="supplier_name" property="supplierName" />
        <result column="feed_time" property="feedTime" />
        <result column="material_num" property="materialNum" />
    </resultMap>


    <sql id="select_content">
        <if test="e.feedRecordId != null and e.feedRecordId != '' ">
            AND t.feed_record_id = #{e.feedRecordId}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.facId != null and e.facId != '' ">
            AND t.fac_id = #{e.facId}
        </if>
        <if test="e.workOrderNum != null and e.workOrderNum != '' ">
            AND t.work_order_num = #{e.workOrderNum}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialNum != null and e.materialNum != '' ">
            AND t.material_num = #{e.materialNum}
        </if>
        <if test="e.batchNumber != null and e.batchNumber != '' ">
            AND t.batch_number = #{e.batchNumber}
        </if>
        <if test="e.supplierName != null and e.supplierName != '' ">
            AND t.supplier_name = #{e.supplierName}
        </if>
        <if test="e.feedTime != null and e.feedTime != '' ">
            AND t.feed_time = #{e.feedTime}
        </if>
    </sql>

</mapper>