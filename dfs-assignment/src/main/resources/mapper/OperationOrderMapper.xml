<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.assignment.mapper.OperationOrderMapper">

    <resultMap id="BaseResultMap" type="com.yelink.assignment.entity.operation.OperationOrderEntity">
        <result column="id" property="id"/>

    </resultMap>

    <select id="selectListByLineId" resultType="com.yelink.assignment.entity.operation.OperationOrderEntity">
        SELECT *
        FROM `dfs_operation_order`
        WHERE
        <if test="state != null and state != ''">
            state IN
            <foreach item="item" collection="state.split(',')" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="state == null or state == ''">
            (state in (2,3,4) or (state = 5 and to_days(`actual_end_date`) = to_days(NOW())))
        </if>
        AND line_id = #{lineId}
    </select>

    <select id="getFinishCountGroupByWorkOrder" resultType="com.yelink.assignment.entity.operation.OperationOrderEntity">
        SELECT
        oo.work_order_num,
        IFNULL( SUM( odc.count ), 0 ) AS finish_count
        FROM
        dfs_operation_day_count odc
        LEFT JOIN dfs_operation_order oo ON oo.operation_number = odc.operation_number
        <where>
            <if test="date == null ">
                odc.time = #{date}
            </if>
            AND oo.work_order_num IS NOT NULL
        </where>
        GROUP BY oo.work_order_num
    </select>


</mapper>