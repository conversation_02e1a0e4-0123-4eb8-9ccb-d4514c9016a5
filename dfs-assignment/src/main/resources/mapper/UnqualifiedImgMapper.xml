<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.assignment.mapper.UnqualifiedImgMapper">

    <resultMap id="BaseResultMap" type="com.yelink.assignment.entity.UnqualifiedImgEntity">
        <result column="id" property="id"/>
        <result column="report_id" property="reportId"/>
        <result column="url" property="url"/>
    </resultMap>

    <select id="selectUrlList" resultType="java.lang.String">
		select a.url from dfs_unqualified_img a
		join dfs_report_line b on a.report_id = b.id
		where report_id = #{id}
    </select>

</mapper>