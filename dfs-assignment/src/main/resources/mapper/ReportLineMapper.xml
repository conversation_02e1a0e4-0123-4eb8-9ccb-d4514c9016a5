<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.assignment.mapper.ReportLineMapper">

    <resultMap id="BaseResultMap" type="com.yelink.assignment.entity.report.ReportLineEntity">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="line_id" property="lineId"/>
        <result column="line_name" property="lineName"/>
        <result column="operation_number" property="operationNumber"/>
        <result column="finish_count" property="finishCount"/>
        <result column="unqualified" property="unqualified"/>
        <result column="user_name" property="userName"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="create_time" property="createTime"/>
        <result column="effective_hours" property="effectiveHours"/>
        <result column="defect_desc" property="defectDesc"/>
        <result column="operator" property="operator"/>
        <result column="report_time" property="reportTime"/>
        <result column="report_date" property="reportDate"/>
        <result column="operator_name" property="operatorName"/>
        <result column="update_time" property="updateTime"/>
        <result column="auto_count_record_time" property="autoCountRecordTime"/>
        <result column="auto_count" property="autoCount"/>
        <result column="batch" property="batch"/>
        <result column="shift_id" property="shiftId"/>
        <result column="shift_type" property="shiftType"/>
        <result column="report_count_id" property="reportCountId"/>
        <result column="vehicle_code" property="vehicleCode"/>
    </resultMap>

    <insert id="insertWriteBackId" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dfs_report_line (type, line_id, line_name, operation_number,finish_count,unqualified,user_name,
        user_nickname,create_time,effective_hours,defect_desc,operator,report_time,report_date,operator_name,
        update_time,auto_count_record_time,auto_count,batch,shift_id,shift_type,report_count_id,vehicle_code)
        values (#{type}, #{lineId}, #{lineName}, #{operationNumber},#{finishCount},#{unqualified},#{userName},
        #{userNickname},#{createTime},#{effectiveHours},#{defectDesc},#{operator},#{reportTime},#{reportDate},#{operatorName},
        #{updateTime},#{autoCountRecordTime},#{autoCount},#{batch},#{shiftId},#{shiftType},#{reportCountId},#{vehicleCode})
    </insert>

    <update id="updateReportLine">
		update dfs_report_line set finish_count = #{finishCount},
		auto_count_record_time=#{time},
		update_time=#{time}
		where operation_number = #{operationNumber} and `type`=#{type};
    </update>

    <!--<update id="updateReportLineByProgress">
        update dfs_report_line set finish_count = #{finishCount},auto_count_record_time=#{now},
        create_time=#{now},report_date=#{entity.reportDate}
        where operation_number = #{entity.operationNumber}
        and `type`=#{entity.type}
        and finish_count=#{entity.finishCount}
        and auto_count_record_time=#{entity.autoCountRecordTime};
    </update>-->

    <update id="updateReportLineByProgress">
        update dfs_report_line set finish_count = #{finishCount},auto_count_record_time=#{now},
        report_date=#{entity.reportDate},report_time=#{now}
        where id = #{entity.id}
        and update_time=#{entity.updateTime};
    </update>

    <select id="getFinishCountGroupByUser" resultType="com.yelink.assignment.entity.report.ReportLineEntity">
        SELECT
        report.operator,
        IFNULL( SUM( report.finish_count ), 0 ) AS finish_count
        FROM
        dfs_operation_order oo
        LEFT JOIN dfs_report_line report ON report.operation_number = oo.operation_number
        <where>
            <if test="modelId != null and modelId != ''">
                AND oo.model_id = #{modelId}
            </if>
            <if test="recordDate != null ">
                AND report.report_date = #{recordDate}
            </if>
            AND oo.operation_number IS NOT NULL
        </where>
        GROUP BY
        report.operator
    </select>

    <select id="getFinishCountGroupByLine" resultType="com.yelink.dfscommon.dto.OperationDayCountDTO">
        SELECT
            report.line_id AS lineId,
            oo.work_order_num AS workOrderNumber,
            report.finish_count AS count
        FROM
            dfs_operation_order oo
                LEFT JOIN dfs_report_line report ON report.operation_number = oo.operation_number
        <where>
            <if test="modelId != null and modelId != ''">
                AND oo.model_id = #{modelId}
            </if>
            <if test="recordDate != null ">
                AND report.report_date = #{recordDate}
            </if>
            AND report.line_id IS NOT NULL
        </where>
    </select>

</mapper>
