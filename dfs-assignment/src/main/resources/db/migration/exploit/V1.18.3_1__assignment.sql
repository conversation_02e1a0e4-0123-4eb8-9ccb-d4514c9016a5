#DDL

-- 作业工单新增载具编号字段
call proc_add_column(
'dfs_operation_order',
'vehicle_code',
'ALTER TABLE `dfs_operation_order` ADD COLUMN `vehicle_code` varchar(255) DEFAULT NULL COMMENT ''载具编号''');

-- 作业工单报工 新增上报人姓名字段
call proc_add_column(
'dfs_report_line',
'operator_name',
'ALTER TABLE `dfs_report_line` ADD COLUMN `operator_name` varchar(255) DEFAULT NULL COMMENT ''上报人姓名''');

-- 作业工单报工 新增载具编号字段
call proc_add_column(
'dfs_report_line',
'vehicle_code',
'ALTER TABLE `dfs_report_line` ADD COLUMN `vehicle_code` varchar(255) DEFAULT NULL COMMENT ''载具编号''');

CREATE TABLE IF NOT EXISTS `dfs_feed_record` (
   `feed_record_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '上料记录id',
   `line_id` int(11) DEFAULT NULL COMMENT '产线id',
   `fac_id` int(11) DEFAULT NULL COMMENT '工位id',
   `operation_order_num` varchar(255) DEFAULT NULL COMMENT '作业工单号',
   `work_order_num` varchar(255) DEFAULT NULL COMMENT '生产工单号',
   `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
   `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
   `batch_number` varchar(255) DEFAULT NULL COMMENT '批次号',
   `supplier_name` varchar(255) DEFAULT NULL COMMENT '供应商名称',
   `feed_time` datetime DEFAULT NULL COMMENT '上料时间',
   `feed_by` varchar(255) DEFAULT NULL COMMENT '上料人',
   `material_num` double(11,2) DEFAULT NULL COMMENT '物料数量',
   `code_record_id` int(11) DEFAULT NULL COMMENT '扫码记录id',
   PRIMARY KEY (`feed_record_id`) USING BTREE,
   KEY `material_code` (`material_code`),
   KEY `code_record_id` (`code_record_id`),
   KEY `operation_order_num` (`operation_order_num`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='上料记录表';


#DML

