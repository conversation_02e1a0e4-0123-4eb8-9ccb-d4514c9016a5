set autocommit = 0;
#DDL
CREATE TABLE IF NOT EXISTS `dfs_operation_order`
(
    `id`                           int NOT NULL AUTO_INCREMENT COMMENT '作业工单id',
    `operation_number`             varchar(100) DEFAULT NULL COMMENT '作业工单编号',
    `work_order_num`               varchar(100) DEFAULT NULL COMMENT '工单号',
    `shift_id`                     int(11)  DEFAULT NULL COMMENT '班次id',
    `shift_type`                   varchar(100) DEFAULT NULL COMMENT '班次类型',
    `batch`                        varchar(100) DEFAULT NULL COMMENT '批次（载具编号）',
    `material_code`                varchar(100) DEFAULT NULL COMMENT '物料编码',
    `material_name`                varchar(100) DEFAULT NULL COMMENT '物料名称',
    `state`                        int DEFAULT '1' COMMENT '状态 1-创建、2-生效、3-投产、4-挂起、5-完成',
    `state_name`                   varchar(100) DEFAULT NULL COMMENT '状态 创建、生效、投产、挂起、完成',
    `product_count`                double(11,2) DEFAULT NULL COMMENT '排产数量',
    `unqualified`                  int DEFAULT '0' COMMENT '不良数量',
    `finish_count`                 double(11,2) DEFAULT '0.00' COMMENT '完成数量',
    `wait_product_count`           double(11,2) DEFAULT NULL COMMENT '待产数量',
    `line_id`                      int DEFAULT NULL COMMENT '制造单元id',
    `line_name`                    varchar(100) DEFAULT NULL COMMENT '制造单元名称',
    `model_id`                     int DEFAULT NULL COMMENT '制造单元类型id',
    `model_name`                   varchar(100) DEFAULT NULL COMMENT '制造单元类型名称',
    `procedure_id`                 int DEFAULT NULL COMMENT '工序id',
    `craft_procedure_id`           int DEFAULT NULL COMMENT '工艺工序id',
    `procedure_name`               varchar(100) DEFAULT NULL COMMENT '工序名称',
    `actual_start_date`            datetime DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_date`              datetime DEFAULT NULL COMMENT '实际完成时间',
    `actual_working_hours`         double(11,2) DEFAULT NULL COMMENT '实际工时',
    `create_by`                    varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_name`                    varchar(100) DEFAULT NULL COMMENT '创建人姓名',
    `create_time`                  datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`                    varchar(100) DEFAULT NULL COMMENT '修改人',
    `update_name`                    varchar(100) DEFAULT NULL COMMENT '修改人姓名',
    `update_time`                  datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `operation_number` (`operation_number`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8 COMMENT ='作业工单表';

CREATE TABLE IF NOT EXISTS `dfs_report_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'investment-投入,report-报工,hangUp-挂起,finished-完工',
  `aid` int(11) NULL DEFAULT NULL COMMENT '工厂id',
  `aname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工厂名称',
  `gid` int(11) NULL DEFAULT NULL COMMENT '车间id',
  `gname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车间名称',
  `line_id` int(11) NULL DEFAULT NULL COMMENT '产线id',
  `line_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产线名称',
  `fid` int(11) NULL DEFAULT NULL COMMENT '工位id',
  `fname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工位名称',
  `operation_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工单号',
  `finish_count` int(11) NULL DEFAULT NULL COMMENT '完成数量',
  `unqualified` int(11) NULL DEFAULT NULL COMMENT '不合格数量',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号',
  `user_nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上报人姓名',
  `batch` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次',
  `shift_id` int(11) NULL DEFAULT NULL COMMENT '班次id',
  `shift_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '班次名称',
  `auto_count_record_time` datetime NULL DEFAULT NULL COMMENT '记录显示采集器采集数量的时间点',
  `auto_count` int(11) NULL DEFAULT NULL COMMENT '采集器采集数量(上个采集时间点到当前时间)',
  `effective_hours` double(20, 1) NULL DEFAULT NULL COMMENT '有效工时',
  `report_date` date NULL DEFAULT NULL COMMENT '上报时间 ',
  `create_time` datetime NULL DEFAULT NULL COMMENT '上报时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `report_date`(`report_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产线、工位数量报工表' ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `dfs_report_line` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) DEFAULT NULL COMMENT 'investment-投入 hangup-挂起 report-报工 finish-完工',
  `line_id` int(11) DEFAULT NULL COMMENT '产线id',
  `line_name` varchar(50) DEFAULT NULL COMMENT '产线名称',
  `operation_number` varchar(50) DEFAULT NULL COMMENT '工单号',
  `finish_count` int(11) DEFAULT NULL COMMENT '完成数量',
  `unqualified` int(11) DEFAULT NULL COMMENT '不合格数量',
  `user_name` varchar(50) DEFAULT NULL COMMENT '账号',
  `user_nickname` varchar(50) DEFAULT NULL COMMENT '上报人姓名',
  `batch` varchar(50) DEFAULT NULL COMMENT '批次',
  `shift_id` int(11) DEFAULT NULL COMMENT '班次id',
  `shift_type` varchar(50) DEFAULT NULL COMMENT '班次名称',
  `auto_count_record_time` datetime DEFAULT NULL COMMENT '记录显示采集器采集数量的时间点',
  `auto_count` int(11) DEFAULT NULL COMMENT '采集器采集数量(上个采集时间点到当前时间)',
  `effective_hours` double(20,1) DEFAULT NULL COMMENT '有效工时',
  `report_count_id` int(11) DEFAULT NULL COMMENT 'dfs_report_count表主键',
  `report_date` date DEFAULT NULL COMMENT '上报时间 ',
  `create_time` datetime DEFAULT NULL COMMENT '上报时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `report_date` (`report_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='产线数量报工表';

CREATE TABLE IF NOT EXISTS `dfs_operation_day_count`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `operation_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工单号',
  `count` double(11, 3) NULL DEFAULT 0.000 COMMENT '完成数量',
  `unqualified` double(11, 3) NULL DEFAULT 0.000 COMMENT '不合格数量',
  `time` datetime NULL DEFAULT NULL COMMENT '时间',
  `repair_qualified_number` double(11, 3) NULL DEFAULT 0.000 COMMENT '返修良品数',
  `line_id` int(11) NULL DEFAULT NULL COMMENT '产线id',
  `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工单每日产量记录表' ROW_FORMAT = DYNAMIC;

CREATE TABLE IF NOT EXISTS `dfs_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) DEFAULT NULL COMMENT '编码',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `value` varchar(50) DEFAULT NULL COMMENT '值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='配置表';

# 新增报工图片信息表
CREATE TABLE IF NOT EXISTS `dfs_unqualified_img` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `report_id` int(11) DEFAULT NULL COMMENT '报工记录id',
  `url` varchar(255) DEFAULT NULL COMMENT '图片url',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='报工图片信息表';


#DML
INSERT INTO `dfs_config`(`id`, `code`, `name`, `value`) VALUES (1, 'progressInterval', '作业工单产量计算时间间隔（s）', '5');
commit
