#DDL
-- 判断字段不存在则增加该字段
DROP PROCEDURE if EXISTS proc_add_column;
delimiter $$
CREATE PROCEDURE `proc_add_column`(in var_table_name varchar(64),in var_column_name varchar(64),in var_sqlstr varchar(1024))
top:begin
	-- 表不存在则直接返回
	set @p_tablenum='';
	set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
	prepare stmt1 from @sqlstr1;
	execute stmt1;
	deallocate prepare stmt1;
	if(@p_tablenum<1)then
		leave top;
	end if;

	-- 字段已存在则直接返回
	set @p_columnnum='';
	set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
	prepare stmt2 from @sqlstr;
	execute stmt2;
	deallocate prepare stmt2;
	if(@p_columnnum>0)then
		leave top;
	end if;

	-- 表存在且字段不存在则创建新字段
	set @sqlcmd=var_sqlstr;
	prepare stmt3 from @sqlcmd;
	execute stmt3;
	deallocate prepare stmt3;
end $$
delimiter;



-- 修改表字段的函数
DROP PROCEDURE if EXISTS proc_modify_column;
delimiter $$
CREATE PROCEDURE `proc_modify_column`(in var_table_name varchar(64),in var_column_name varchar(64),in var_sqlstr varchar(1024))
top:begin

	-- 表不存在则直接返回
	set @p_tablenum='';
	set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
	prepare stmt1 from @sqlstr1;
	execute stmt1;
	deallocate prepare stmt1;
	if(@p_tablenum<1)then
		leave top;
	end if;

	-- 字段不存在则直接返回
	set @p_columnnum='';
	set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
	prepare stmt2 from @sqlstr;
	execute stmt2;
	deallocate prepare stmt2;
	if(@p_columnnum<=0)then
		leave top;
	end if;


	-- 表存在且字段存在则修改字段
 	set @sqlcmd=var_sqlstr;
 	prepare stmt3 from @sqlcmd;
 	execute stmt3;
 	deallocate prepare stmt3;

	end $$
delimiter;


-- 新增索引
DROP PROCEDURE if EXISTS proc_add_column_index;
delimiter $$
CREATE PROCEDURE `proc_add_column_index`(in var_table_name varchar(64),in var_column_name varchar(64),in var_index_name varchar(64))
top:begin

    -- 表不存在则直接返回
    set @p_tablenum='';
    set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
    prepare stmt1 from @sqlstr1;
    execute stmt1;
    deallocate prepare stmt1;
    if(@p_tablenum<1)then
        leave top;
    end if;

    -- 字段不存在则直接返回
    set @p_columnnum='';
    set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
    prepare stmt2 from @sqlstr;
    execute stmt2;
    deallocate prepare stmt2;
    if(@p_columnnum<=0)then
        leave top;
    end if;

    -- 字段且索引存在才创建索引
    set @str=concat(' alter table `',var_table_name,'` add index `',var_index_name,'` (`',var_column_name,'`);');
    set @cnt = '';
    select count(*) into @cnt from information_schema.statistics where table_schema=database() and table_name=var_table_name and index_name=var_index_name;
    if (@cnt = 0) then
        PREPARE stmt FROM @str;
        EXECUTE stmt;
    end if;
--
end $$
delimiter;


-- 删除索引
DROP PROCEDURE if EXISTS proc_drop_column_index;
delimiter $$
CREATE PROCEDURE `proc_drop_column_index`(in var_table_name varchar(64),in var_column_name varchar(64),in var_index_name varchar(64))
top:begin

    -- 表不存在则直接返回
    set @p_tablenum='';
    set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
    prepare stmt1 from @sqlstr1;
    execute stmt1;
    deallocate prepare stmt1;
    if(@p_tablenum<1)then
        leave top;
    end if;

    -- 字段不存在则直接返回
    set @p_columnnum='';
    set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
    prepare stmt2 from @sqlstr;
    execute stmt2;
    deallocate prepare stmt2;
    if(@p_columnnum<=0)then
        leave top;
    end if;

    -- 字段且索引存在才删除索引
    set @str=concat(' drop index `',var_index_name,'` on `',var_table_name,'`;');
    set @cnt = '';
    select count(*) into @cnt from information_schema.statistics where table_schema=database() and table_name=var_table_name and index_name=var_index_name;
    if (@cnt > 0) then
        PREPARE stmt FROM @str;
        EXECUTE stmt;
    end if;

end $$
delimiter;


CREATE TABLE IF NOT EXISTS `dfs_input_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `operation_number` varchar(50) DEFAULT NULL COMMENT '工单号',
  `line_id` int(11) DEFAULT NULL COMMENT '产线id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `input_record_time` datetime NOT NULL COMMENT '采集器采集时间',
  `type` varchar(20) DEFAULT NULL COMMENT 'investment-投入,report-报工,hangUp-挂起,finish-完成',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='投入量记录表';


call proc_add_column(
'dfs_operation_order',
'input_total',
'ALTER TABLE `dfs_operation_order` ADD COLUMN `input_total` double(11, 2) NULL COMMENT ''投入数'' AFTER `finish_count`');



#DML

