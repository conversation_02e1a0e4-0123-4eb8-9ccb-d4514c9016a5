set autocommit = 0;

-- DDL
-- 物料名称数据类型更改为text
ALTER TABLE `dfs_feed_record` MODIFY COLUMN `material_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '物料名称';

-- 作业工单制造单元关联表
CREATE TABLE if not exists `dfs_operation_order_line_relevance`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `operation_order_id` int(11)      DEFAULT NULL COMMENT '作业工单id',
    `line_id`            int(11)      DEFAULT NULL COMMENT '制造单元id',
    `create_by`          varchar(255) DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4 COMMENT ='作业工单制造单元关联表';

call proc_add_column(
        'dfs_operation_order',
        'device_id',
        'ALTER TABLE `dfs_operation_order` ADD COLUMN `device_id` int(11) NULL COMMENT ''设备id''');
-- 加索引
call proc_add_column_index('dfs_operation_order_line_relevance','operation_order_id','operation_order_id');
call proc_add_column_index('dfs_operation_order_device_relevance','operation_order_id','operation_order_id');
call proc_add_column_index('dfs_operation_order_team_relevance','operation_order_id','operation_order_id');
-- DML

commit;
