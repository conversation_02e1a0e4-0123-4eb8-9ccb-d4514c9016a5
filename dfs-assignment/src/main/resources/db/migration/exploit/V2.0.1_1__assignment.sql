set autocommit = 0;

--  作业工单表 update_time字段按当前时间戳更新
call proc_modify_column(
        'dfs_operation_order',
        'update_time',
        'ALTER TABLE `dfs_operation_order` modify update_time timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP');

-- 作业工单加索引
call proc_add_column_index(
        'dfs_operation_order',
        'work_order_num',
        'work_order_num');

call proc_add_column_index(
        'dfs_report_line',
        'update_time',
        'update_time');

call proc_modify_column(
        'dfs_report_line',
        'update_time',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `update_time` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT ''更新时间'' AFTER `create_time`');

-- 制造单元模型改成操作中心
call proc_modify_column(
        'dfs_operation_order',
        'model_id',
        'ALTER TABLE `dfs_operation_order` CHANGE COLUMN `model_id` `work_center_id` int(11) DEFAULT NULL COMMENT ''工作中心id''');

-- 制造单元模型改成操作中心
call proc_modify_column(
        'dfs_operation_order',
        'model_name',
        'ALTER TABLE `dfs_operation_order` CHANGE COLUMN `model_name` `work_center_name` varchar(100) DEFAULT NULL COMMENT ''工作中心名称''');

call proc_add_column(
        'dfs_operation_order',
        'team_id',
        'ALTER TABLE `dfs_operation_order` ADD COLUMN `team_id` int(11) NULL COMMENT ''班组id''');

-- 新增工单投产班组成员表
CREATE TABLE IF NOT EXISTS `dfs_operation_order_team` (
  `id` int NOT NULL AUTO_INCREMENT,
  `operation_order_number` varchar(255) NOT NULL COMMENT '工单号',
  `member_name` varchar(100) NULL COMMENT '成员账号',
  `team_role` int NULL COMMENT '班组角色',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `operation_order_number` (`operation_order_number`) USING BTREE,
  KEY `member_name` (`member_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单投产班组成员表';


-- 工单设备关联表
CREATE TABLE if not exists `dfs_operation_order_device_relevance`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `operation_order_id` int(11)      DEFAULT NULL COMMENT '作业工单id',
    `device_id`     int(11)      DEFAULT NULL COMMENT '设备id',
    `create_by`     varchar(255) DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工作中心设备关联表';


-- 工单班组关联表
CREATE TABLE if not exists `dfs_operation_order_team_relevance`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `operation_order_id` int(11)      DEFAULT NULL COMMENT '作业工单id',
    `team_id`       int(11)      DEFAULT NULL COMMENT '班组id',
    `create_by`     varchar(255) DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工作中心班组关联表';



#DML
UPDATE `dfs_report_line` SET `update_time` = now() WHERE `update_time` IS NULL AND `type`='auto';

commit;
