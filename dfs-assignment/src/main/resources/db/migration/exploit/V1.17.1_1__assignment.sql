#DDL

-- 作业工单新增报工系数字段
call proc_add_column(
'dfs_operation_order',
'coefficient',
'ALTER TABLE `dfs_operation_order` ADD COLUMN `coefficient` int NULL COMMENT ''报工系数''');

call proc_add_column(
'dfs_operation_order',
'producer',
'ALTER TABLE `dfs_operation_order` ADD COLUMN `producer` varchar(255) NULL COMMENT ''生产人员''');

-- 作业工单报工记录新增报工时间report_time字段
call proc_add_column(
        'dfs_report_line',
        'report_time',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `report_time` datetime NULL COMMENT ''报工时间time''');

call proc_add_column(
        'dfs_report_count',
        'report_time',
        'ALTER TABLE `dfs_report_count` ADD COLUMN `report_time` datetime NULL COMMENT ''报工时间time''');

#DML

