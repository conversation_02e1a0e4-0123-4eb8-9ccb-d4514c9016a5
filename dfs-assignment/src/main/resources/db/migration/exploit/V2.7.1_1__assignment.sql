CREATE TABLE IF NOT EXISTS `dfs_order_execute_seq` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_number` varchar(255) DEFAULT NULL COMMENT '工单号/作业工单号',
  `device_id` int(11) DEFAULT NULL COMMENT '设备id',
  `line_id` int(11) DEFAULT NULL COMMENT '产线id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `union` (`order_number`,`device_id`,`line_id`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4;
