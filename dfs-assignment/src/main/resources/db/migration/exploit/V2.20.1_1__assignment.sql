-- DDL
CREATE TABLE IF NOT EXISTS `assignment_delete_record`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `order_number`  varchar(255) DEFAULT NULL COMMENT '单据号',
    `order_type`    varchar(100) DEFAULT NULL COMMENT '单据类型',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
    `table_name`    varchar(255) DEFAULT NULL COMMENT '表明',
    `table_source`  varchar(100) DEFAULT NULL COMMENT '数据库来源',
    `json_data`     longtext COMMENT 'json数据',
    `version`       varchar(100) DEFAULT NULL COMMENT 'dfs版本号',
    `create_by`     varchar(100) DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `type_number` (`order_number`, `order_type`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='删除单据时记录相关单据';