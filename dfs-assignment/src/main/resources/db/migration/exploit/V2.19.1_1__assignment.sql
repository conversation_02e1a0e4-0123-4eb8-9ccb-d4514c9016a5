-- DDL
CREATE TABLE IF NOT EXISTS `dfs_delete_record`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT,
    `order_number` varchar(255) DEFAULT NULL COMMENT '单据号',
    `order_type`   varchar(100) DEFAULT NULL COMMENT '单据类型',
    `table_name`   varchar(255) DEFAULT NULL COMMENT '表名',
    `json_data`    text COMMENT 'json数据',
    `version`      varchar(100) DEFAULT NULL COMMENT 'dfs版本号',
    PRIMARY KEY (`id`),
    KEY `type_number` (`order_number`, `order_type`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT='删除单据时的相关单据记录表';

-- DML
