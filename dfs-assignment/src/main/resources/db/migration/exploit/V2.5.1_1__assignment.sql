set autocommit = 0;

-- <PERSON><PERSON>
call proc_add_column(
        'dfs_report_line',
        'team_id',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `team_id` int(0) NULL COMMENT ''班组id''');

call proc_add_column(
        'dfs_report_line',
        'device_id',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `device_id` int(0) NULL COMMENT ''设备id''');

-- DML
update dfs_report_line l,dfs_operation_order o set l.team_id=o.team_id where l.operation_number=o.operation_number;

update dfs_report_line l,dfs_operation_order o set l.device_id=o.device_id where l.operation_number=o.operation_number;



commit;
