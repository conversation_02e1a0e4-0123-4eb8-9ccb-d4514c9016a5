<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yelink.dfs.mapper.statistics.StatisticsMapper">


    <select id="alarmTrendSummary" resultType="com.yelink.dfs.entity.statistics.SummaryDTO">
        SELECT
        datelist.date,
        IFNULL(alarmCount,0) alarmCount,
        IFNULL(faultCount,0) faultCount,
        IFNULL(offlineCount,0) offlineCount
        from(
        select date_format(date,'%Y-%m') date
        from(
        select date_format(date_add(CONCAT(YEAR(#{currentDate}),'-01-01'),interval +t.help_topic_id month),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 12
        )a where date &lt;= YEAR(#{currentDate})+1
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(create_time, '%Y-%m') as create_time,
        sum(CASE WHEN alarm_type_number = #{alarmCode} THEN 1 ELSE 0 END ) alarmCount,
        sum(CASE WHEN alarm_type_number = #{faultCode} THEN 1 ELSE 0 END ) faultCount,
        sum(CASE WHEN alarm_type_number = #{offlineCode} THEN 1 ELSE 0 END ) offlineCount
        FROM
        dfs_alarm t
        group by DATE_FORMAT(create_time, '%Y-%m')
        ) alarm ON datelist.date = alarm.create_time
        ORDER BY date ASC
    </select>

    <select id="getFinishRate" resultType="java.util.HashMap">
        SELECT
        datelist.date,
        round(IFNULL(finish_rate,0),4) finish_rate
        from(
        select date_format(date,'%Y-%m') date
        from(
        select date_format(date_add(CONCAT(YEAR(#{currentDate}),'-01-01'),interval +t.help_topic_id month),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 12
        )a where date &lt;= YEAR(#{currentDate})+1
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(`time`, '%Y-%m') as `time`,
        avg(finish_rate) as finish_rate
        FROM
        dfs_record_line_day_union t
        group by DATE_FORMAT(`time`, '%Y-%m')
        ) t ON datelist.date = t.time
        ORDER BY date ASC
    </select>

    <select id="getOee" resultType="java.util.HashMap">
        SELECT
        datelist.date,
        round(IFNULL(oee,0),4) oee
        from(
        select date_format(date,'%Y-%m') date
        from(
        select date_format(date_add(CONCAT(YEAR(#{currentDate}),'-01-01'),interval +t.help_topic_id month),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 12
        )a where date &lt;= YEAR(#{currentDate})+1
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(`time`, '%Y-%m') as `time`,
        avg(oee) as oee
        FROM
        dfs_record_line_day_union t
        group by DATE_FORMAT(`time`, '%Y-%m')
        ) t ON datelist.date = t.time
        ORDER BY date ASC
    </select>

    <select id="getOutput" resultType="java.util.HashMap">
        SELECT
        datelist.date,
        round(IFNULL(`count`,0),0) `count`
        from(
        select date_format(date,'%Y-%m') date
        from(
        select date_format(date_add(CONCAT(YEAR(#{currentDate}),'-01-01'),interval +t.help_topic_id month),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 12
        )a where date &lt;= YEAR(#{currentDate})+1
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(`time`, '%Y-%m') as `time`,
        sum(`count`) as `count`
        FROM
        dfs_record_line_day_union t
        group by DATE_FORMAT(`time`, '%Y-%m')
        ) t ON datelist.date = t.time
        ORDER BY date ASC
    </select>

    <select id="getFinishRateWeekly" resultType="java.util.HashMap">
        SELECT
        datelist.date,
        round(IFNULL(finish_rate,0),4) finish_rate
        from(
        select date_format(date_add(#{currentDate},interval -t.help_topic_id day),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 7
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(`time`, '%Y-%m-%d') as `time`,
        avg(finish_rate) as finish_rate
        FROM
        dfs_record_line_day_union t
        group by DATE_FORMAT(`time`, '%Y-%m-%d')
        ) t ON datelist.date = t.time
        ORDER BY date ASC
    </select>

    <select id="getOeeWeekly" resultType="java.util.HashMap">
        SELECT
        datelist.date,
        round(IFNULL(oee,0),4) oee
        from(
        select date_format(date_add(#{currentDate},interval -t.help_topic_id day),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 7
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(`time`, '%Y-%m-%d') as `time`,
        avg(oee) as oee
        FROM
        dfs_record_line_day_union t
        group by DATE_FORMAT(`time`, '%Y-%m-%d')
        ) t ON datelist.date = t.time
        ORDER BY date ASC
    </select>

    <select id="getOutputWeekly" resultType="java.util.HashMap">
        SELECT
        datelist.date,
        round(IFNULL(`count`,0),0) `count`
        from(
        select date_format(date_add(#{currentDate},interval -t.help_topic_id day),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 7
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(`time`, '%Y-%m-%d') as `time`,
        sum(`count`) as `count`
        FROM
        dfs_record_line_day_union t
        group by DATE_FORMAT(`time`, '%Y-%m-%d')
        ) t ON datelist.date = t.time
        ORDER BY date ASC
    </select>
    <select id="getUnqualifiedRank" resultType="com.yelink.dfs.entity.manufacture.dto.UnqualifiedRankDTO">
        SELECT
        count( 1 ) as count,
        abnormal_name as abnormalName
        FROM
        dfs_record_work_order_unqualified
        where
        DATE_FORMAT(create_date,'%Y-%m-%d') = current_date
        AND fid = #{fid}
        GROUP BY
        abnormal_name
        ORDER BY
        count( 1 ) DESC
        LIMIT 5

    </select>


</mapper>



