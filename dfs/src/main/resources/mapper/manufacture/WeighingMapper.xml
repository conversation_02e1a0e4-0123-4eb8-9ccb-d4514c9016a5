<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.manufacture.WeighingMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.manufacture.WeighingEntity">
        <result column="weighing_id" property="weighingId"/>
        <result column="tracking_number" property="trackingNumber"/>
        <result column="car_number" property="carNumber"/>
        <result column="driver_name" property="driverName"/>
        <result column="address" property="address"/>
        <result column="materiel_name" property="materielName"/>
        <result column="source" property="source"/>
        <result column="batch_number" property="batchNumber"/>
        <result column="first_weighing" property="firstWeighing"/>
        <result column="first_weighing_img" property="firstWeighingImg"/>
        <result column="second_weighing" property="secondWeighing"/>
        <result column="second_weighing_img" property="secondWeighingImg"/>
        <result column="unit" property="unit"/>
        <result column="recorder" property="recorder"/>
        <result column="update_by_id" property="updateById"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.weighingId != null and e.weighingId != '' ">
            AND t.weighing_id = #{e.weighingId}
        </if>
        <if test="e.trackingNumber != null and e.trackingNumber != '' ">
            AND t.tracking_number = #{e.trackingNumber}
        </if>
        <if test="e.carNumber != null and e.carNumber != '' ">
            AND t.car_number = #{e.carNumber}
        </if>
        <if test="e.driverName != null and e.driverName != '' ">
            AND t.driver_name = #{e.driverName}
        </if>
        <if test="e.address != null and e.address != '' ">
            AND t.address = #{e.address}
        </if>
        <if test="e.lng != null and e.lng != '' ">
            AND t.lng = #{e.lng}
        </if>
        <if test="e.lat != null and e.lat != '' ">
            AND t.lat = #{e.lat}
        </if>
        <if test="e.materielName != null and e.materielName != '' ">
            AND t.materiel_name = #{e.materielName}
        </if>
        <if test="e.source != null and e.source != '' ">
            AND t.source = #{e.source}
        </if>
        <if test="e.batchNumber != null and e.batchNumber != '' ">
            AND t.batch_number = #{e.batchNumber}
        </if>
        <if test="e.firstWeighing != null and e.firstWeighing != '' ">
            AND t.first_weighing = #{e.firstWeighing}
        </if>
        <if test="e.firstWeighingImg != null and e.firstWeighingImg != '' ">
            AND t.first_weighing_img = #{e.firstWeighingImg}
        </if>
        <if test="e.secondWeighing != null and e.secondWeighing != '' ">
            AND t.second_weighing = #{e.secondWeighing}
        </if>
        <if test="e.secondWeighingImg != null and e.secondWeighingImg != '' ">
            AND t.second_weighing_img = #{e.secondWeighingImg}
        </if>
        <if test="e.sealImg != null and e.sealImg != '' ">
            AND t.seal_img = #{e.sealImg}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
        <if test="e.recorder != null and e.recorder != '' ">
            AND t.recorder = #{e.recorder}
        </if>
        <if test="e.updateById != null and e.updateById != '' ">
            AND t.update_by_id = #{e.updateById}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createById != null and e.createById != '' ">
            AND t.create_by_id = #{e.createById}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>
