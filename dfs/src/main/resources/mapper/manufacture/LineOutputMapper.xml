<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.manufacture.LineOutputMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.manufacture.LineOutputEntity">
        <result column="output_id" property="outputId"/>
        <result column="line_id" property="lineId"/>
        <result column="time" property="time"/>
        <result column="output" property="output"/>
        <result column="week_average_output" property="weekAverageOutput"/>
        <result column="month_average_output" property="monthAverageOutput"/>
        <result column="week_max_output" property="weekMaxOutput"/>
        <result column="month_max_output" property="monthMaxOutput"/>
        <result column="max_record_output" property="maxRecordOutput"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.outputId != null and e.outputId != '' ">
            AND t.output_id = #{e.outputId}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
        <if test="e.weekOfYear != null and e.weekOfYear != '' ">
            AND t.week_of_year = #{e.weekOfYear}
        </if>
        <if test="e.output != null and e.output != '' ">
            AND t.output = #{e.output}
        </if>
        <if test="e.weekAverageOutput != null and e.weekAverageOutput != '' ">
            AND t.week_average_output = #{e.weekAverageOutput}
        </if>
        <if test="e.monthAverageOutput != null and e.monthAverageOutput != '' ">
            AND t.month_average_output = #{e.monthAverageOutput}
        </if>
        <if test="e.weekMaxOutput != null and e.weekMaxOutput != '' ">
            AND t.week_max_output = #{e.weekMaxOutput}
        </if>
        <if test="e.monthMaxOutput != null and e.monthMaxOutput != '' ">
            AND t.month_max_output = #{e.monthMaxOutput}
        </if>
        <if test="e.maxRecordOutput != null and e.maxRecordOutput != '' ">
            AND t.max_record_output = #{e.maxRecordOutput}
        </if>
    </sql>

</mapper>