<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.manufacture.FacWorkOrderMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.manufacture.FacWorkOrderEntity">
        <result column="id" property="id"/>
        <result column="fid" property="fid"/>
        <result column="work_order" property="workOrder"/>
        <result column="procedures" property="procedures"/>
        <result column="start_time" property="startTime"/>
        <result column="last_procedure" property="lastProcedure"/>
        <result column="finish" property="finish"/>
        <result column="working" property="working"/>
        <result column="finish_time" property="finishTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.procedures != null and e.procedures != '' ">
            AND t.procedures = #{e.procedures}
        </if>
        <if test="e.working != null and e.working != '' ">
            AND t.working = #{e.working}
        </if>
        <if test="e.startTime != null and e.startTime != '' ">
            AND t.start_time = #{e.startTime}
        </if>
        <if test="e.finishTime != null and e.finishTime != '' ">
            AND t.finish_time = #{e.finishTime}
        </if>
        <if test="e.finish != null and e.finish != '' ">
            AND t.finish = #{e.finish}
        </if>
        <if test="e.lastProcedure != null and e.lastProcedure != '' ">
            AND t.last_procedure = #{e.lastProcedure}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>
