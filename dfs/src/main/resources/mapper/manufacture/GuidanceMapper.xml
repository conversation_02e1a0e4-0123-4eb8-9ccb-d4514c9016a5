<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.manufacture.GuidanceMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.manufacture.GuidanceEntity">
        <result column="guidance_id" property="guidanceId"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="product_code" property="productCode"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.guidanceId != null and e.guidanceId != '' ">
            AND t.guidance_id = #{e.guidanceId}
        </if>
        <if test="e.title != null and e.title != '' ">
            AND t.title = #{e.title}
        </if>
        <if test="e.content != null and e.content != '' ">
            AND t.content = #{e.content}
        </if>
        <if test="e.productCode != null and e.productCode != '' ">
            AND t.product_code = #{e.productCode}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>

</mapper>
