<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.manufacture.FacUserReportMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.manufacture.FacUserReportEntity">
		    <result column="id" property="id" />
		    <result column="fid" property="fid" />
		    <result column="work_order" property="workOrder" />
		    <result column="username" property="username" />
		    <result column="nickname" property="nickname" />
		    <result column="create_time" property="createTime" />
	</resultMap>


	<sql id="select_content">
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.fid != null and e.fid != '' ">
					AND t.fid = #{e.fid}
				</if>
				<if test="e.workOrder != null and e.workOrder != '' ">
					AND t.work_order = #{e.workOrder}
				</if>
				<if test="e.username != null and e.username != '' ">
					AND t.username = #{e.username}
				</if>
				<if test="e.nickname != null and e.nickname != '' ">
					AND t.nickname = #{e.nickname}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>
