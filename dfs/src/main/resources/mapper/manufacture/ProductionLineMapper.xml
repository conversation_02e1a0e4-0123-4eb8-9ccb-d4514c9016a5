<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.manufacture.ProductionLineMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.manufacture.ProductionLineEntity">
        <result column="production_line_id" property="productionLineId"/>
        <result column="production_line_code" property="productionLineCode"/>
        <result column="name" property="name"/>
        <result column="place" property="place"/>
        <result column="aid" property="aid"/>
        <result column="gid" property="gid"/>
        <result column="fid" property="fid"/>
        <result column="schedule_id" property="scheduleId"/>
        <result column="state" property="state"/>
        <result column="mag_name" property="magName"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="mag_phone" property="magPhone"/>
        <result column="model_id" property="modelId"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.productionLineCode != null and e.productionLineCode != '' ">
            AND t.production_line_code = #{e.productionLineCode}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.gid != null and e.gid != '' ">
            AND t.gid = #{e.gid}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.place != null and e.place != '' ">
            AND t.place = #{e.place}
        </if>
        <if test="e.scheduleId != null and e.scheduleId != '' ">
            AND t.schedule_id = #{e.scheduleId}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.magName != null and e.magName != '' ">
            AND t.mag_name = #{e.magName}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.magPhone != null and e.magPhone != '' ">
            AND t.mag_phone = #{e.magPhone}
        </if>
        <if test="e.modelId != null and e.modelId != '' ">
            AND t.model_id = #{e.modelId}
        </if>
        <if test="e.employeeId != null and e.employeeId != '' ">
            AND t.employee_id = #{e.employeeId}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>

    <select id="getList" resultType="com.yelink.dfs.entity.manufacture.ProductionLineEntity">
        select line.* from dfs_production_line line
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
