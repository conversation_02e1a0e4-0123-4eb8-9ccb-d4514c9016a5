<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.GridMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.GridEntity">
        <result column="gid" property="gid"/>
        <result column="gcode" property="gcode"/>
        <result column="cid" property="cid"/>
        <result column="aid" property="aid"/>
        <result column="img" property="img"/>
        <result column="gname" property="gname"/>
        <result column="addr" property="addr"/>
        <result column="jus_range" property="jusRange"/>
        <result column="coordinate" property="coordinate"/>
        <result column="remark" property="remark"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="mag_name" property="magName"/>
        <result column="mag_phone" property="magPhone"/>
        <result column="model_id" property="modelId"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="employee_id" property="employeeId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.gid != null and e.gid != '' ">
            AND t.gid = #{e.gid}
        </if>
        <if test="e.employeeId != null and e.employeeId != '' ">
            AND t.employee_id = #{e.employeeId}
        </if>
        <if test="e.gcode != null and e.gcode != '' ">
            AND t.gcode = #{e.gcode}
        </if>
        <if test="e.cid != null and e.cid != '' ">
            AND t.cid = #{e.cid}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.img != null and e.img != '' ">
            AND t.img = #{e.img}
        </if>
        <if test="e.gname != null and e.gname != '' ">
            AND t.gname = #{e.gname}
        </if>
        <if test="e.addr != null and e.addr != '' ">
            AND t.addr = #{e.addr}
        </if>
        <if test="e.jusRange != null and e.jusRange != '' ">
            AND t.jus_range = #{e.jusRange}
        </if>
        <if test="e.coordinate != null and e.coordinate != '' ">
            AND t.coordinate = #{e.coordinate}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.magName != null and e.magName != '' ">
            AND t.mag_name = #{e.magName}
        </if>
        <if test="e.magPhone != null and e.magPhone != '' ">
            AND t.mag_phone = #{e.magPhone}
        </if>
        <if test="e.modelId != null and e.modelId != '' ">
            AND t.model_id = #{e.modelId}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>
