<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.WorkCenterMapper">

    <select id="getList" resultType="com.yelink.dfscommon.entity.dfs.WorkCenterEntity">
        select workCenter.* from dfs_work_center workCenter
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
