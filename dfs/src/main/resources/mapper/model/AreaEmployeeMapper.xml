<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.AreaEmployeeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.AreaEmployeeEntity">
        <result column="id" property="id"/>
        <result column="area_id" property="areaId"/>
        <result column="employee_id" property="employeeId"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.areaId != null and e.areaId != '' ">
            AND t.area_id = #{e.areaId}
        </if>
        <if test="e.employeeId != null and e.employeeId != '' ">
            AND t.employee_id = #{e.employeeId}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>