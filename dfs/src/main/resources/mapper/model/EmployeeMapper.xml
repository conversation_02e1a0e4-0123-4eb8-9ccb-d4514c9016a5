<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.EmployeeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.EmployeeEntity">
        <result column="id" property="id"/>
        <result column="age" property="age"/>
        <result column="name" property="name"/>
        <result column="sex" property="sex"/>
        <result column="job_number" property="jobNumber"/>
        <result column="mobile" property="mobile"/>
        <result column="email" property="email"/>
        <result column="id_card" property="idCard"/>
        <result column="skill_level" property="skillLevel"/>
        <result column="wechat_id" property="wechatId"/>
        <result column="department_id" property="departmentId"/>
        <result column="fid" property="fid"/>
        <result column="entry_date" property="entryDate"/>
        <result column="post" property="post"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.age != null and e.age != '' ">
            AND t.age = #{e.age}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.sex != null and e.sex != '' ">
            AND t.sex = #{e.sex}
        </if>
        <if test="e.jobNumber != null and e.jobNumber != '' ">
            AND t.job_number = #{e.jobNumber}
        </if>
        <if test="e.mobile != null and e.mobile != '' ">
            AND t.mobile = #{e.mobile}
        </if>
        <if test="e.email != null and e.email != '' ">
            AND t.email = #{e.email}
        </if>
        <if test="e.idCard != null and e.idCard != '' ">
            AND t.id_card = #{e.idCard}
        </if>
        <if test="e.skillLevel != null and e.skillLevel != '' ">
            AND t.skill_level = #{e.skillLevel}
        </if>
        <if test="e.wechatId != null and e.wechatId != '' ">
            AND t.wechat_id = #{e.wechatId}
        </if>
        <if test="e.departmentId != null and e.departmentId != '' ">
            AND t.department_id = #{e.departmentId}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.entryDate != null and e.entryDate != '' ">
            AND t.entry_date = #{e.entryDate}
        </if>
        <if test="e.post != null and e.post != '' ">
            AND t.post = #{e.post}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>