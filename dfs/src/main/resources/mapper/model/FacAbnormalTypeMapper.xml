<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.FacAbnormalTypeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.FacAbnormalTypeEntity">
        <result column="id" property="id"/>
        <result column="model_id" property="modelId"/>
        <result column="fac_type_name" property="facTypeName"/>
        <result column="craft_code" property="craftCode"/>
        <result column="procedure_code" property="procedureCode"/>
        <result column="abnormal_name" property="abnormalName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.modelId != null and e.modelId != '' ">
            AND t.model_id = #{e.modelId}
        </if>
        <if test="e.facTypeName != null and e.facTypeName != '' ">
            AND t.fac_type_name = #{e.facTypeName}
        </if>
        <if test="e.craftCode != null and e.craftCode != '' ">
            AND t.craft_code = #{e.craftCode}
        </if>
        <if test="e.procedureCode != null and e.procedureCode != '' ">
            AND t.procedure_code = #{e.procedureCode}
        </if>
        <if test="e.abnormalName != null and e.abnormalName != '' ">
            AND t.abnormal_name = #{e.abnormalName}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>

</mapper>