<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.ModelMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.ModelEntity">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="seq" property="seq"/>
        <result column="pid" property="pid"/>
        <result column="batch_number" property="batchNumber"/>
        <result column="theory_efficiency" property="theoryEfficiency"/>
        <result column="working_hours" property="workingHours"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="model_img" property="modelImg"/>
        <result column="simulation_img" property="simulationImg"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.seq != null and e.seq != '' ">
            AND t.seq = #{e.seq}
        </if>
        <if test="e.pid != null and e.pid != '' ">
            AND t.pid = #{e.pid}
        </if>
        <if test="e.batchNumber != null and e.batchNumber != '' ">
            AND t.batch_number = #{e.batchNumber}
        </if>
        <if test="e.workingHours != null and e.workingHours != '' ">
            AND t.working_hours = #{e.workingHours}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.modelImg != null and e.modelImg != '' ">
            AND t.model_img = #{e.modelImg}
        </if>
        <if test="e.simulationImg != null and e.simulationImg != '' ">
            AND t.simulation_img = #{e.simulationImg}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>
