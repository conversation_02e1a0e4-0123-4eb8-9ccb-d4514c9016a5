<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.FacilitiesMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.FacilitiesEntity">
        <result column="fid" property="fid"/>
        <result column="fname" property="fname"/>
        <result column="cid" property="cid"/>
        <result column="aid" property="aid"/>
        <result column="gid" property="gid"/>
        <result column="production_line_id" property="productionLineId"/>
        <result column="line_order" property="lineOrder"/>
        <result column="type_one_code" property="typeOneCode"/>
        <result column="type_one_name" property="typeOneName"/>
        <result column="type_two_code" property="typeTwoCode"/>
        <result column="type_two_name" property="typeTwoName"/>
        <result column="state" property="state"/>
        <result column="fcode" property="fcode"/>
        <result column="place" property="place"/>
        <result column="img" property="img"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="mag_name" property="magName"/>
        <result column="mag_phone" property="magPhone"/>
        <result column="remark" property="remark"/>
        <result column="location" property="location"/>
        <result column="alarm" property="alarm"/>
        <result column="model_id" property="modelId"/>
        <result column="seq" property="seq"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_check" property="isCheck"/>
        <result column="is_defective" property="isDefective"/>
        <result column="is_input" property="isInput"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.fname != null and e.fname != '' ">
            AND t.fname = #{e.fname}
        </if>
        <if test="e.cid != null and e.cid != '' ">
            AND t.cid = #{e.cid}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.workCenterId != null and e.workCenterId != '' ">
            AND t.work_center_id = #{e.workCenterId}
        </if>
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.lineOrder != null and e.lineOrder != '' ">
            AND t.line_order = #{e.lineOrder}
        </if>
        <if test="e.typeOneCode != null and e.typeOneCode != '' ">
            AND t.type_one_code = #{e.typeOneCode}
        </if>
        <if test="e.typeOneName != null and e.typeOneName != '' ">
            AND t.type_one_name = #{e.typeOneName}
        </if>
        <if test="e.typeTwoCode != null and e.typeTwoCode != '' ">
            AND t.type_two_code = #{e.typeTwoCode}
        </if>
        <if test="e.typeTwoName != null and e.typeTwoName != '' ">
            AND t.type_two_name = #{e.typeTwoName}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.withSensor != null and e.withSensor != '' ">
            AND t.with_sensor = #{e.withSensor}
        </if>
        <if test="e.fcode != null and e.fcode != '' ">
            AND t.fcode = #{e.fcode}
        </if>
        <if test="e.lat != null and e.lat != '' ">
            AND t.lat = #{e.lat}
        </if>
        <if test="e.lng != null and e.lng != '' ">
            AND t.lng = #{e.lng}
        </if>
        <if test="e.alt != null and e.alt != '' ">
            AND t.alt = #{e.alt}
        </if>
        <if test="e.place != null and e.place != '' ">
            AND t.place = #{e.place}
        </if>
        <if test="e.img != null and e.img != '' ">
            AND t.img = #{e.img}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.magName != null and e.magName != '' ">
            AND t.mag_name = #{e.magName}
        </if>
        <if test="e.magPhone != null and e.magPhone != '' ">
            AND t.mag_phone = #{e.magPhone}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.location != null and e.location != '' ">
            AND t.location = #{e.location}
        </if>
        <if test="e.alarm != null and e.alarm != '' ">
            AND t.alarm = #{e.alarm}
        </if>
        <if test="e.modelId != null and e.modelId != '' ">
            AND t.model_id = #{e.modelId}
        </if>
        <if test="e.seq != null and e.seq != '' ">
            AND t.seq = #{e.seq}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.isCheck != null and e.isCheck != '' ">
            AND t.is_check = #{e.isCheck}
        </if>
        <if test="e.isDefective != null and e.isDefective != '' ">
            AND t.is_defective = #{e.isDefective}
        </if>
        <if test="e.isInput != null and e.isInput != '' ">
            AND t.is_input = #{e.isInput}
        </if>
    </sql>

    <select id="getByBoardEui" resultType="com.yelink.dfs.entity.model.FacilitiesEntity">
        select f.*
        from dfs_fac_sensor fs
        left
        join dfs_facilities f on fs.fid=f.fid
        where f.type_one_code=#{facType} and fs.eui=#{eui}
    </select>

    <select id="getFidsByLineId" resultType="java.lang.Integer">
        select fid from dfs_facilities where production_line_id=#{lineId} order by line_order desc
    </select>

    <select id="getFidsByGid" resultType="java.lang.Integer">
        select fid from dfs_facilities where gid=#{gid}
    </select>

    <select id="filterByLineId" resultType="java.lang.Integer">
        select fid from dfs_facilities where production_line_id=#{lineId} and  is_check &lt;&gt; #{iScheck}
    </select>
    <select id="getPageV2" resultType="com.yelink.dfs.entity.model.FacilitiesEntity">
        select facilities.* from dfs_facilities facilities
        left join dfs_model model on model.id = facilities.model_id
        <where>
            <if test="sqlDTO != null and sqlDTO.facilitiesSql != null and sqlDTO.facilitiesSql != ''">
                AND ${sqlDTO.facilitiesSql}
            </if>
            <if test="sqlDTO != null and sqlDTO.modelSql != null and sqlDTO.modelSql != ''">
                AND ${sqlDTO.modelSql}
            </if>
            <if test="sqlDTO != null and sqlDTO.facUserSql != null and sqlDTO.facUserSql != ''">
                AND facilities.fid IN (
                select facUser.fid FROM dfs_fac_user facUser
                <where>
                    ${sqlDTO.facUserSql}
                </where>
                )
            </if>
        </where>
        <if test="sqlDTO != null and sqlDTO.orderBySql != null and sqlDTO.orderBySql != ''">
            ORDER BY
            ${sqlDTO.orderBySql}
        </if>

    </select>
</mapper>
