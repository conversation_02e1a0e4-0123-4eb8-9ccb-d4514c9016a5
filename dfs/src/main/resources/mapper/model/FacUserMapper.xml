<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.FacUserMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.FacUserEntity">
        <result column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="fid" property="fid"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.userName != null and e.userName != '' ">
            AND t.user_name = #{e.userName}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>

    <select id="getFacByUsername" resultType="com.yelink.dfs.entity.model.FacilitiesEntity">
        select f.* from dfs_facilities f
        left join dfs_fac_user fu on fu.fid = f.fid
        where type_one_code=#{typeOneCode} and (fu.user_name=#{username} or fu.user_name is null)
    </select>

</mapper>
