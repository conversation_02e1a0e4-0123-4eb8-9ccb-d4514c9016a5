<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.LineFileMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.LineFileEntity">
        <result column="id" property="id" />
        <result column="production_line_id" property="productionLineId" />
        <result column="file" property="file" />
        <result column="upload_file_time" property="uploadFileTime" />
        <result column="file_name" property="fileName" />
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.file != null and e.file != '' ">
            AND t.file = #{e.file}
        </if>
        <if test="e.uploadFileTime != null and e.uploadFileTime != '' ">
            AND t.upload_file_time = #{e.uploadFileTime}
        </if>
        <if test="e.fileName != null and e.fileName != '' ">
            AND t.file_name = #{e.fileName}
        </if>
    </sql>

</mapper>