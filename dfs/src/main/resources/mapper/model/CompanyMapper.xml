<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.CompanyMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.CompanyEntity">
        <result column="cid" property="cid"/>
        <result column="clogo" property="clogo"/>
        <result column="cname" property="cname"/>
        <result column="ctype_number" property="ctypeNumber"/>
        <result column="ctype" property="ctype"/>
        <result column="caddr" property="caddr"/>
        <result column="caddr_detail" property="caddrDetail"/>
        <result column="cimage" property="cimage"/>
        <result column="lat" property="lat"/>
        <result column="lng" property="lng"/>
        <result column="mag_name" property="magName"/>
        <result column="mag_phone" property="magPhone"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="sec_phone" property="secPhone"/>
        <result column="reg_level" property="regLevel"/>
        <result column="jus_range" property="jusRange"/>
        <result column="coordinate" property="coordinate"/>
        <result column="remark" property="remark"/>
        <result column="cshow" property="cshow"/>
        <result column="ctitle" property="ctitle"/>
        <result column="workers_count" property="workersCount"/>
        <result column="assets" property="assets"/>
        <result column="industry_id" property="industryId"/>
        <result column="industry" property="industry"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="layout_url" property="layoutUrl"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.cid != null and e.cid != '' ">
            AND t.cid = #{e.cid}
        </if>
        <if test="e.clogo != null and e.clogo != '' ">
            AND t.clogo = #{e.clogo}
        </if>
        <if test="e.cname != null and e.cname != '' ">
            AND t.cname = #{e.cname}
        </if>
        <if test="e.ctypeNumber != null and e.ctypeNumber != '' ">
            AND t.ctype_number = #{e.ctypeNumber}
        </if>
        <if test="e.ctype != null and e.ctype != '' ">
            AND t.ctype = #{e.ctype}
        </if>
        <if test="e.caddr != null and e.caddr != '' ">
            AND t.caddr = #{e.caddr}
        </if>
        <if test="e.caddrDetail != null and e.caddrDetail != '' ">
            AND t.caddr_detail = #{e.caddrDetail}
        </if>
        <if test="e.lat != null and e.lat != '' ">
            AND t.lat = #{e.lat}
        </if>
        <if test="e.lng != null and e.lng != '' ">
            AND t.lng = #{e.lng}
        </if>
        <if test="e.magName != null and e.magName != '' ">
            AND t.mag_name = #{e.magName}
        </if>
        <if test="e.magPhone != null and e.magPhone != '' ">
            AND t.mag_phone = #{e.magPhone}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.secPhone != null and e.secPhone != '' ">
            AND t.sec_phone = #{e.secPhone}
        </if>
        <if test="e.regLevel != null and e.regLevel != '' ">
            AND t.reg_level = #{e.regLevel}
        </if>
        <if test="e.jusRange != null and e.jusRange != '' ">
            AND t.jus_range = #{e.jusRange}
        </if>
        <if test="e.coordinate != null and e.coordinate != '' ">
            AND t.coordinate = #{e.coordinate}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.cshow != null and e.cshow != '' ">
            AND t.cshow = #{e.cshow}
        </if>
        <if test="e.ctitle != null and e.ctitle != '' ">
            AND t.ctitle = #{e.ctitle}
        </if>
        <if test="e.workersCount != null and e.workersCount != '' ">
            AND t.workers_count = #{e.workersCount}
        </if>
        <if test="e.assets != null and e.assets != '' ">
            AND t.assets = #{e.assets}
        </if>
        <if test="e.industryId != null and e.industryId != '' ">
            AND t.industry_id = #{e.industryId}
        </if>
        <if test="e.industry != null and e.industry != '' ">
            AND t.industry = #{e.industry}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>
