<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.AreaMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.AreaEntity">
        <result column="aid" property="aid"/>
        <result column="acode" property="acode"/>
        <result column="cid" property="cid"/>
        <!--        <result column="cname" property="cname"/>-->
        <result column="img" property="img"/>
        <result column="aname" property="aname"/>
        <result column="employee_id" property="employeeId"/>
        <result column="jus_range" property="jusRange"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="mag_phone" property="magPhone"/>
        <result column="mag_name" property="magName"/>
        <result column="area" property="area"/>
        <result column="area_number" property="areaNumber"/>
        <result column="area_type" property="areaType"/>
        <result column="coordinate" property="coordinate"/>
        <result column="addr" property="addr"/>
        <result column="lat" property="lat"/>
        <result column="lng" property="lng"/>
        <result column="remark" property="remark"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.employeeId != null and e.employeeId != '' ">
            AND t.employee_id = #{e.employeeId}
        </if>
        <if test="e.acode != null and e.acode != '' ">
            AND t.acode = #{e.acode}
        </if>
        <if test="e.cid != null and e.cid != '' ">
            AND t.cid = #{e.cid}
        </if>
        <!--        <if test="e.cname != null and e.cname != '' ">-->
        <!--            AND t.cname = #{e.cname}-->
        <!--        </if>-->
        <if test="e.img != null and e.img != '' ">
            AND t.img = #{e.img}
        </if>
        <if test="e.aname != null and e.aname != '' ">
            AND t.aname = #{e.aname}
        </if>
        <if test="e.jusRange != null and e.jusRange != '' ">
            AND t.jus_range = #{e.jusRange}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.magPhone != null and e.magPhone != '' ">
            AND t.mag_phone = #{e.magPhone}
        </if>
        <if test="e.magName != null and e.magName != '' ">
            AND t.mag_name = #{e.magName}
        </if>
        <if test="e.area != null and e.area != '' ">
            AND t.area = #{e.area}
        </if>
        <if test="e.areaNumber != null and e.areaNumber != '' ">
            AND t.area_number = #{e.areaNumber}
        </if>
        <if test="e.areaType != null and e.areaType != '' ">
            AND t.area_type = #{e.areaType}
        </if>
        <if test="e.coordinate != null and e.coordinate != '' ">
            AND t.coordinate = #{e.coordinate}
        </if>
        <if test="e.addr != null and e.addr != '' ">
            AND t.addr = #{e.addr}
        </if>
        <if test="e.lat != null and e.lat != '' ">
            AND t.lat = #{e.lat}
        </if>
        <if test="e.lng != null and e.lng != '' ">
            AND t.lng = #{e.lng}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>
