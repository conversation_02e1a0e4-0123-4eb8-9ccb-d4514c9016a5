<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.model.FacSensorMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.model.FacSensorEntity">
        <result column="id" property="id"/>
        <result column="eui" property="eui"/>
        <result column="fid" property="fid"/>
        <result column="sensor_type" property="sensorType"/>
<!--        <result column="position" property="position"/>-->
        <result column="create_date" property="createDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.sensorType != null and e.sensorType != '' ">
            AND t.sensor_type = #{e.sensorType}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
<!--        <if test="e.position != null and e.position != '' ">-->
<!--            AND t.position = #{e.position}-->
<!--        </if>-->
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>

    <select id="getCounterEui" resultMap="BaseResultMap">
        select * from dfs_fac_sensor where fid=#{fid} and sensor_type=#{sensorType}
        limit 1
    </select>

    <select id="getOneFidByEui" resultType="java.lang.Integer">
        select fid from dfs_fac_sensor where eui=#{eui} limit 1
    </select>

    <select id="getProductionLineByBoardEui" resultType="java.lang.Integer">
        select production_line_id from dfs_facilities f where f.fid=(
        select fid from dfs_fac_sensor where eui=#{eui}
        )
    </select>

    <select id="getReportFacsByEui" resultType="com.yelink.dfs.entity.model.FacilitiesEntity">
        select f.* from dfs_facilities f
        left join dfs_fac_sensor fs on f.fid=f.fid
        where f.is_check in
        <foreach item="item" collection="reportCode" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and fs.eui=#{eui}
    </select>

</mapper>
