<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.datax.DataxSaleOrderMaterialMapper">



    <select id="saleOrderCount" resultType="java.lang.Double">
        SELECT
            count(1)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
    </select>

    <select id="saleOrderShouldDeliveredCount" resultType="java.lang.Double">
        SELECT
            count(1)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.require_goods_date  <![CDATA[ <= ]]> #{endDate}
        </where>
    </select>

    <select id="saleOrderDeliveredCount" resultType="java.lang.Double">
        SELECT
            count(1)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status = 1 -- 已发货
        </where>
    </select>

    <select id="saleOrderDeliveredTimelyCount" resultType="java.lang.Double">
        SELECT
            count(1)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status = 1 -- 已发货
            AND m.delivery_date  <![CDATA[ <= ]]> m.require_goods_date
        </where>
    </select>

    <select id="saleOrderDeliveredDelayCount" resultType="java.lang.Double">
        SELECT
            count(1)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status = 1 -- 已发货
            AND m.delivery_date <![CDATA[ > ]]> m.require_goods_date -- 发货日期 大于 要货日期
        </where>
    </select>

    <select id="saleOrderNoDeliveredDelayCount" resultType="java.lang.Double">
        SELECT
            count(1)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status in (0, 2) -- (未/部分)发货
            AND now() <![CDATA[ > ]]> m.require_goods_date -- 当前日期 大于 要货日期
        </where>
    </select>

    <select id="saleMaterialCount" resultType="java.lang.Double">
        SELECT
            IFNULL(sum(m.sales_quantity), 0)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
        </where>
    </select>

    <select id="saleMaterialShouldDeliveredCount" resultType="java.lang.Double">
        SELECT
            IFNULL(sum(m.sales_quantity), 0)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.require_goods_date  <![CDATA[ <= ]]> #{endDate}
        </where>
    </select>

    <select id="saleMaterialDeliveredCount" resultType="java.lang.Double">
        SELECT
            IFNULL(sum(m.applied_quantity), 0)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status in(1, 2) -- (已/部分)发货
        </where>
    </select>

    <select id="saleMaterialDeliveredTimelyCount" resultType="java.lang.Double">
        SELECT
            IFNULL(sum(m.applied_quantity), 0)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status in(1, 2) -- (已/部分)发货
            AND m.delivery_date <![CDATA[ <= ]]> m.require_goods_date	-- 发货时间小于等于要货时间
        </where>
    </select>

    <select id="saleMaterialNoDeliveredCount" resultType="java.lang.Double">
        SELECT
            IFNULL(sum(m.sales_quantity - m.applied_quantity), 0)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status in(0, 2) -- (未/部分)发货
        </where>
    </select>

    <select id="saleMaterialNoDeliveredDelayCount" resultType="java.lang.Double">
        SELECT
            IFNULL(sum(m.sales_quantity - m.applied_quantity), 0)
        FROM
            v_ams_sale_order_material m
                INNER JOIN v_ams_sale_order o ON o.sale_order_id = m.sale_order_id
                AND o.state IN ( 2, 3 )
        <where>
            <if test="startDate != null">
                AND m.${cycleTimeField} <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="endDate != null">
                AND m.${cycleTimeField} <![CDATA[ <= ]]> #{endDate}
            </if>
            AND m.shipment_status in(0, 2) -- (未/部分)发货
            AND now() <![CDATA[ > ]]> m.require_goods_date -- 当前时间大于要货时间
        </where>
    </select>
</mapper>
