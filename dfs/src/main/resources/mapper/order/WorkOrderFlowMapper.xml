<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderFlowMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderFlowEntity">
		    <result column="id" property="id" />
		    <result column="work_order_number" property="workOrderNumber" />
		<result column="quantity" property="quantity" />
		<result column="create_date" property="createDate" />
	</resultMap>


	<sql id="select_content">
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
					AND t.work_order_number = #{e.workOrderNumber}
				</if>
				<if test="e.quantity != null and e.quantity != '' ">
					AND t.quantity = #{e.quantity}
				</if>
				<if test="e.createDate != null and e.createDate != '' ">
					AND t.create_date = #{e.createDate}
				</if>
	</sql>

</mapper>