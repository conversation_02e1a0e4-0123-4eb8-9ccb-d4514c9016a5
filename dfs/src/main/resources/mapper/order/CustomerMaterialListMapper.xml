<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.CustomerMaterialListMapper">

    <select id="getList" resultType="com.yelink.dfs.entity.order.CustomerMaterialListEntity">
        select customerMaterial.* from dfs_customer_material_list customerMaterial
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>