<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.TmpWorkOrderScheduleMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.tmp.TmpWorkOrderScheduleEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_date" property="orderDate"/>
        <result column="delivery_deadline" property="deliveryDeadline"/>
        <result column="planned_delivery_date" property="plannedDeliveryDate"/>
        <result column="planned_ship_date" property="plannedShipDate"/>
        <result column="model_type" property="modelType"/>
        <result column="customer_name" property="customerName"/>
        <result column="need_produce_quantity" property="needProduceQuantity"/>
        <result column="drawing_code" property="drawingCode"/>
        <result column="sale_order_code" property="saleOrderCode"/>
        <result column="product_code" property="productCode"/>
        <result column="product_reserve_num" property="productReserveNum"/>
        <result column="packing_method" property="packingMethod"/>
        <result column="work_order_id" property="workOrderId"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="line_name" property="lineName"/>
        <result column="plan_quantity" property="planQuantity"/>
        <result column="finish_count" property="finishCount"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="material_is_complete" property="materialIsComplete"/>
        <result column="material_owe_num" property="materialOweNum"/>
        <result column="schedule_status_name" property="scheduleStatusName"/>
        <result column="grid_code" property="gridCode"/>
        <result column="material_code" property="materialCode"/>
        <result column="state_name" property="stateName"/>
        <result column="state" property="state"/>
        <result column="unqualified" property="unqualified"/>
        <result column="progress" property="progress"/>
        <result column="actual_start_date" property="actualStartDate"/>
        <result column="actual_end_date" property="actualEndDate"/>
        <result column="line_id" property="lineId"/>
        <result column="gname" property="gname"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.orderId != null and e.orderId != '' ">
            AND t.order_id = #{e.orderId}
        </if>
        <if test="e.orderNumber != null and e.orderNumber != '' ">
            AND t.order_number = #{e.orderNumber}
        </if>
        <if test="e.orderDate != null and e.orderDate != '' ">
            AND t.order_date = #{e.orderDate}
        </if>
        <if test="e.deliveryDeadline != null and e.deliveryDeadline != '' ">
            AND t.delivery_deadline = #{e.deliveryDeadline}
        </if>
        <if test="e.plannedDeliveryDate != null and e.plannedDeliveryDate != '' ">
            AND t.planned_delivery_date = #{e.plannedDeliveryDate}
        </if>
        <if test="e.plannedShipDate != null and e.plannedShipDate != '' ">
            AND t.planned_ship_date = #{e.plannedShipDate}
        </if>
        <if test="e.modelType != null and e.modelType != '' ">
            AND t.model_type = #{e.modelType}
        </if>
        <if test="e.customerName != null and e.customerName != '' ">
            AND t.customer_name = #{e.customerName}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.needProduceQuantity != null and e.needProduceQuantity != '' ">
            AND t.need_produce_quantity = #{e.needProduceQuantity}
        </if>
        <if test="e.drawingCode != null and e.drawingCode != '' ">
            AND t.drawing_code = #{e.drawingCode}
        </if>
        <if test="e.saleOrderCode != null and e.saleOrderCode != '' ">
            AND t.sale_order_code = #{e.saleOrderCode}
        </if>
        <if test="e.productCode != null and e.productCode != '' ">
            AND t.product_code = #{e.productCode}
        </if>
        <if test="e.productReserveNum != null and e.productReserveNum != '' ">
            AND t.product_reserve_num = #{e.productReserveNum}
        </if>
        <if test="e.packingMethod != null and e.packingMethod != '' ">
            AND t.packing_method = #{e.packingMethod}
        </if>
        <if test="e.workOrderId != null and e.workOrderId != '' ">
            AND t.work_order_id = #{e.workOrderId}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.lineName != null and e.lineName != '' ">
            AND t.line_name = #{e.lineName}
        </if>
        <if test="e.planQuantity != null and e.planQuantity != '' ">
            AND t.plan_quantity = #{e.planQuantity}
        </if>
        <if test="e.finishCount != null and e.finishCount != '' ">
            AND t.finish_count = #{e.finishCount}
        </if>
        <if test="e.startDate != null and e.startDate != '' ">
            AND t.start_date = #{e.startDate}
        </if>
        <if test="e.endDate != null and e.endDate != '' ">
            AND t.end_date = #{e.endDate}
        </if>
        <if test="e.materialIsComplete != null and e.materialIsComplete != '' ">
            AND t.material_is_complete = #{e.materialIsComplete}
        </if>
        <if test="e.materialOweNum != null and e.materialOweNum != '' ">
            AND t.material_owe_num = #{e.materialOweNum}
        </if>
        <if test="e.scheduleStatusName != null and e.scheduleStatusName != '' ">
            AND t.schedule_status_name = #{e.scheduleStatusName}
        </if>
    </sql>

</mapper>