<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.RecordWorkOrderMaterialPlanMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.RecordWorkOrderMaterialPlanEntity">
		    <result column="id" property="id" />
		    <result column="work_order_number" property="workOrderNumber" />
		    <result column="material_code" property="materialCode" />
		    <result column="plan_quantity" property="planQuantity" />
		    <result column="unit" property="unit" />
		    <result column="measurement_quantity" property="measurementQuantity" />
		    <result column="measurement_unit" property="measurementUnit" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
					AND t.work_order_number = #{e.workOrderNumber}
				</if>
				<if test="e.materialCode != null and e.materialCode != '' ">
					AND t.material_code = #{e.materialCode}
				</if>
				<if test="e.planQuantity != null and e.planQuantity != '' ">
					AND t.plan_quantity = #{e.planQuantity}
				</if>
				<if test="e.unit != null and e.unit != '' ">
					AND t.unit = #{e.unit}
				</if>
				<if test="e.measurementQuantity != null and e.measurementQuantity != '' ">
					AND t.measurement_quantity = #{e.measurementQuantity}
				</if>
				<if test="e.measurementUnit != null and e.measurementUnit != '' ">
					AND t.measurement_unit = #{e.measurementUnit}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>