<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.DeliveryOrderMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.DeliveryOrderEntity">
        <result column="delivery_order_id" property="deliveryOrderId"/>
        <result column="delivery_id" property="deliveryId"/>
        <result column="order_id" property="orderId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.deliveryOrderId != null and e.deliveryOrderId != '' ">
            AND t.delivery_order_id = #{e.deliveryOrderId}
        </if>
        <if test="e.deliveryId != null and e.deliveryId != '' ">
            AND t.delivery_id = #{e.deliveryId}
        </if>
        <if test="e.orderId != null and e.orderId != '' ">
            AND t.order_id = #{e.orderId}
        </if>
    </sql>

</mapper>