<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.ConfigReverseMaterialDetailMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.tmp.ConfigReverseMaterialDetailEntity">
		    <result column="id" property="id" />
		    <result column="column_name" property="columnName" />
		    <result column="material_type_name" property="materialTypeName" />
		    <result column="material_name_match_rule" property="materialNameMatchRule" />
		    <result column="material_name_match_val" property="materialNameMatchVal" />
		    <result column="material_standard_match_rule" property="materialStandardMatchRule" />
		    <result column="material_standard_match_val" property="materialStandardMatchVal" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.columnName != null and e.columnName != '' ">
					AND t.column_name = #{e.columnName}
				</if>
				<if test="e.materialTypeName != null and e.materialTypeName != '' ">
					AND t.material_type_name = #{e.materialTypeName}
				</if>
				<if test="e.materialNameMatchRule != null and e.materialNameMatchRule != '' ">
					AND t.material_name_match_rule = #{e.materialNameMatchRule}
				</if>
				<if test="e.materialNameMatchVal != null and e.materialNameMatchVal != '' ">
					AND t.material_name_match_val = #{e.materialNameMatchVal}
				</if>
				<if test="e.materialStandardMatchRule != null and e.materialStandardMatchRule != '' ">
					AND t.material_standard_match_rule = #{e.materialStandardMatchRule}
				</if>
				<if test="e.materialStandardMatchVal != null and e.materialStandardMatchVal != '' ">
					AND t.material_standard_match_val = #{e.materialStandardMatchVal}
				</if>
	</sql>

</mapper>