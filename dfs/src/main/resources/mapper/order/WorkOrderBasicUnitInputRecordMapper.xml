<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderBasicUnitInputRecordMapper">

    <select id="getList" resultType="com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity">
        select workOrderBasicUnitInputRecord.* from dfs_work_order_basic_unit_input_record workOrderBasicUnitInputRecord
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>