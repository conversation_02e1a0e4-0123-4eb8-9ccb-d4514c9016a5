<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.CustomerMapper">

    <sql id="select_content">
        <if test="e.customerId != null and e.customerId != '' ">
            AND t.customer_id = #{e.customerId}
        </if>
        <if test="e.salesman != null and e.salesman != '' ">
            AND t.salesman = #{e.salesman}
        </if>
        <!-- 兼容旧字段名cusSuccessor -->
        <if test="e.cusSuccessor != null and e.cusSuccessor != '' ">
            AND t.salesman = #{e.cusSuccessor}
        </if>
        <if test="e.cusState != null and e.cusState != '' ">
            AND t.cus_state = #{e.cusState}
        </if>
        <if test="e.customerCode != null and e.customerCode != '' ">
            AND t.customer_code = #{e.customerCode}
        </if>
        <if test="e.customerName != null and e.customerName != '' ">
            AND t.customer_name = #{e.customerName}
        </if>
        <if test="e.customerMobile != null and e.customerMobile != '' ">
            AND t.customer_mobile = #{e.customerMobile}
        </if>
        <if test="e.customerAddr != null and e.customerAddr != '' ">
            AND t.customer_addr = #{e.customerAddr}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.reviewer != null and e.reviewer != '' ">
            AND t.reviewer = #{e.reviewer}
        </if>
    </sql>

    <insert id="insertCounterparts">
        INSERT INTO counterpart  (type,user_id) VALUES (#{type},#{userId})
    </insert>

    <delete id="deleteCounterpartByType">
        DELETE FROM counterpart  WHERE  type = #{type}
    </delete>
    <select id="getCounterparts" resultType="com.yelink.dfs.entity.order.CounterpartEntity">
        SELECT * FROM counterpart  where  type = #{type}
    </select>

    <select id="pageCustomerAddress" resultType="com.yelink.dfs.open.v1.production.dto.CustomerAddressDTO" >
        SELECT customer.customer_code as customerCode,
               customer.customer_name as customerName,
               customer.customer_mobile as customerMobile,
               customer.salesman as salesman,
               customer.cus_state as cusState,
               customer.company_number as companyNumber,
               -- customer.company_contact_person as companyContactPerson,
               customer.customer_contacts as customerContacts,
               customer.customer_priority as customerPriority,
               -- customer.customer_category as customerCategory,
               -- customer.company_website as companyWebsite,
               customer.company_type as companyType,
               -- customer.company_property as companyProperty,
               customer.company_scale as companyScale,
               customer.company_full_name as companyFullName,
               customerAddr.province_id as provinceId,
               customerAddr.province_name as provinceName,
               customerAddr.city_id as cityId,
               customerAddr.city_name as cityName,
               customerAddr.area_id as areaId,
               customerAddr.area_name as areaName,
               customerAddr.detail_address as detailAddress,
               customerAddr.contact_person as contactPerson,
               customerAddr.phone as phone,
               customerAddr.email as email,
               customerAddr.is_default as isDefault
        FROM dfs_customer customer
                 LEFT JOIN dfs_customer_address customerAddr ON customer.customer_id=customerAddr.customer_id
        where customer.cus_state = 2
        <if test="selectDTO.customerCode != null and selectDTO.customerCode != ''" >
            AND customer.customer_code LIKE CONCAT('%', #{selectDTO.customerCode}, '%')
        </if>
        <if test="selectDTO.customerName != null and selectDTO.customerName != ''" >
            AND customer.customer_name LIKE CONCAT('%', #{selectDTO.customerName}, '%')
        </if>
        <if test="selectDTO.salesman != null and selectDTO.salesman != ''" >
            AND customer.salesman LIKE CONCAT('%', #{selectDTO.salesman}, '%')
        </if>
        <if test="selectDTO.salesmanNickname != null and selectDTO.salesmanNickname != ''" >
            AND EXISTS (
                SELECT 1 FROM sys_users u
                WHERE u.user_name = customer.salesman
                AND u.nick_name LIKE CONCAT('%', #{selectDTO.salesmanNickname}, '%')
            )
        </if>
        <if test="selectDTO.salesmanMobile != null and selectDTO.salesmanMobile != ''" >
            AND EXISTS (
                SELECT 1 FROM sys_users u
                WHERE u.user_name = customer.salesman
                AND u.mobile LIKE CONCAT('%', #{selectDTO.salesmanMobile}, '%')
            )
        </if>
        <if test="selectDTO.companyScale != null and selectDTO.companyScale != ''" >
            AND customer.company_scale LIKE CONCAT('%', #{selectDTO.companyScale}, '%')
        </if>
        <if test="selectDTO.createByName != null and selectDTO.createByName != ''" >
            AND EXISTS (
                SELECT 1 FROM sys_users u
                WHERE u.user_name = customer.create_by
                AND u.nick_name LIKE CONCAT('%', #{selectDTO.createByName}, '%')
            )
        </if>
    </select>

    <select id="getList" resultType="com.yelink.dfs.entity.order.CustomerEntity">
        select customer.* from dfs_customer customer
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
