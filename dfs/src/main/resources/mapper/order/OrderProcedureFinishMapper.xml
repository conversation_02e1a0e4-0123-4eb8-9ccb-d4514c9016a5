<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.OrderProcedureFinishMapper">

    <insert id="saveOrUpdateRecord">
        INSERT INTO dfs_order_procedure_finish(order_number, craft_procedure_id, finish_sum)
        VALUES (#{productOrderNumber}, #{craftProcedureId}, #{finishSum})
            ON DUPLICATE KEY UPDATE finish_sum = #{finishSum};
    </insert>

</mapper>
