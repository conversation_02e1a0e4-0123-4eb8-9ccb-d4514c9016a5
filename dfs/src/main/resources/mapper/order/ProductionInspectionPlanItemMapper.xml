<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.ProductionInspectionPlanItemMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.ProductionInspectionPlanItemEntity">
		    <result column="id" property="id" />
		    <result column="production_inspection_item_id" property="productionInspectionItemId" />
		    <result column="production_inspection_plan_id" property="productionInspectionPlanId" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.productionInspectionItemId != null and e.productionInspectionItemId != '' ">
					AND t.production_inspection_item_id = #{e.productionInspectionItemId}
				</if>
				<if test="e.productionInspectionPlanId != null and e.productionInspectionPlanId != '' ">
					AND t.production_inspection_plan_id = #{e.productionInspectionPlanId}
				</if>
	</sql>

</mapper>