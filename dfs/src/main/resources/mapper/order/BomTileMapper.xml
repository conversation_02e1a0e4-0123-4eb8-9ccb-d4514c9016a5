<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.BomTileMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.BomTileEntity">
		    <result column="id" property="id" />
		    <result column="bom_code" property="bomCode" />
		    <result column="material_code" property="materialCode" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.bomCode != null and e.bomCode != '' ">
					AND t.bom_code = #{e.bomCode}
				</if>
				<if test="e.materialCode != null and e.materialCode != '' ">
					AND t.material_code = #{e.materialCode}
				</if>
	</sql>

</mapper>