<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.FacInputRecordMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.FacInputRecordEntity">
		    <result column="id" property="id" />
		    <result column="work_order" property="workOrder" />
		    <result column="fid" property="fid" />
		    <result column="input_count" property="inputCount" />
		    <result column="loss_count" property="lossCount" />
		    <result column="last_record_time" property="lastRecordTime" />
		    <result column="input_record_time" property="inputRecordTime" />
	</resultMap>


	<sql id="select_content">
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.workOrder != null and e.workOrder != '' ">
					AND t.work_order = #{e.workOrder}
				</if>
				<if test="e.fid != null and e.fid != '' ">
					AND t.fid = #{e.fid}
				</if>
				<if test="e.inputCount != null and e.inputCount != '' ">
					AND t.input_count = #{e.inputCount}
				</if>
				<if test="e.lastRecordTime != null and e.lastRecordTime != '' ">
					AND t.last_record_time = #{e.lastRecordTime}
				</if>
				<if test="e.inputRecordTime != null and e.inputRecordTime != '' ">
					AND t.input_record_time = #{e.inputRecordTime}
				</if>
	</sql>

</mapper>