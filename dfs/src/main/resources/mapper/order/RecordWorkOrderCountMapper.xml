<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.RecordWorkOrderCountMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.RecordWorkOrderCountEntity">
        <result column="id" property="id"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="work_order_name" property="workOrderName"/>
        <result column="fid" property="fid"/>
        <result column="record_date" property="recordDate"/>
        <result column="actual_quantity" property="actualQuantity"/>
        <result column="checked_quantity" property="checkedQuantity"/>
        <result column="defective_quantity" property="defectiveQuantity"/>
        <result column="pass_rate" property="passRate"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="line_id" property="lineId"/>
        <result column="material_code" property="materialCode"/>
        <result column="qualified_number" property="qualifiedNumber"/>
        <result column="direct_rate" property="directRate"/>
        <result column="defective_rate" property="defectiveRate"/>
        <result column="repair_qualified_number" property="repairQualifiedNumber"/>
        <result column="final_qualified_number" property="finalQualifiedNumber"/>
        <result column="final_qualified_rate" property="finalQualifiedRate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.shift != null and e.shift != '' ">
            AND t.shift = #{e.shift}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.qualifiedNumber != null and e.qualifiedNumber != '' ">
            AND t.qualified_number = #{e.qualifiedNumber}
        </if>
        <if test="e.directRate != null and e.directRate != '' ">
            AND t.direct_rate = #{e.directRate}
        </if>
        <if test="e.defectiveRate != null and e.defectiveRate != '' ">
            AND t.defective_rate = #{e.defectiveRate}
        </if>
        <if test="e.repairQualifiedNumber != null and e.repairQualifiedNumber != '' ">
            AND t.repair_qualified_number = #{e.repairQualifiedNumber}
        </if>
        <if test="e.finalQualifiedNumber != null and e.finalQualifiedNumber != '' ">
            AND t.final_qualified_number = #{e.finalQualifiedNumber}
        </if>
        <if test="e.finalQualifiedRate != null and e.finalQualifiedRate != '' ">
            AND t.id = #{e.finalQualifiedRate}
        </if>
        <if test="e.workOrderId != null and e.workOrderId != '' ">
            AND t.work_order_id = #{e.workOrderId}
        </if>
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>

        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.workOrderName != null and e.workOrderName != '' ">
            AND t.work_order_name = #{e.workOrderName}
        </if>
        <if test="e.recordDate != null and e.recordDate != '' ">
            AND t.record_date = #{e.recordDate}
        </if>
        <if test="e.actualQuantity != null and e.actualQuantity != '' ">
            AND t.actual_quantity = #{e.actualQuantity}
        </if>
        <if test="e.checkedQuantity != null and e.checkedQuantity != '' ">
            AND t.checked_quantity = #{e.checkedQuantity}
        </if>
        <if test="e.defectiveQuantity != null and e.defectiveQuantity != '' ">
            AND t.defective_quantity = #{e.defectiveQuantity}
        </if>
        <if test="e.passRate != null and e.passRate != '' ">
            AND t.pass_rate = #{e.passRate}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>
    <update id="updateQualified">
        update dfs_record_work_order_count
        <set>
            checked_quantity=checked_quantity+1,
            pass_rate= ((checked_quantity-defective_quantity)/checked_quantity)*100
        </set>
        where work_order_number=#{workOrderNumber} and fid=#{fid}
    </update>
    <update id="updateUnQualified">
        update dfs_record_work_order_count
        <set>
            checked_quantity=checked_quantity+1,
            defective_quantity=defective_quantity+1,
            pass_rate= ((checked_quantity-defective_quantity)/checked_quantity)*100
        </set>
        where work_order_number=#{workOrderNumber} and fid=#{fid}
    </update>
</mapper>
