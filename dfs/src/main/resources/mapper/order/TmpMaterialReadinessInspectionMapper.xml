<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.TmpMaterialReadinessInspectionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.tmp.TmpMaterialReadinessInspectionEntity">
        <result column="id" property="id"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_quantity" property="materialQuantity"/>
        <result column="raw_material_code" property="rawMaterialCode"/>
        <result column="raw_material_name" property="rawMaterialName"/>
        <result column="raw_material_type" property="rawMaterialType"/>
        <result column="current_inventory" property="currentInventory"/>
        <result column="deduct_inventory" property="deductInventory"/>
        <result column="raw_material_quantity" property="rawMaterialQuantity"/>
        <result column="material_complete_num" property="materialCompleteNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialQuantity != null and e.materialQuantity != '' ">
            AND t.material_quantity = #{e.materialQuantity}
        </if>
        <if test="e.rawMaterialCode != null and e.rawMaterialCode != '' ">
            AND t.raw_material_code = #{e.rawMaterialCode}
        </if>
        <if test="e.rawMaterialName != null and e.rawMaterialName != '' ">
            AND t.raw_material_name = #{e.rawMaterialName}
        </if>
        <if test="e.rawMaterialType != null and e.rawMaterialType != '' ">
            AND t.raw_material_type = #{e.rawMaterialType}
        </if>
        <if test="e.currentInventory != null and e.currentInventory != '' ">
            AND t.current_inventory = #{e.currentInventory}
        </if>
        <if test="e.deductInventory != null and e.deductInventory != '' ">
            AND t.deduct_inventory = #{e.deductInventory}
        </if>
        <if test="e.rawMaterialQuantity != null and e.rawMaterialQuantity != '' ">
            AND t.raw_material_quantity = #{e.rawMaterialQuantity}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.materialCompleteNum != null and e.materialCompleteNum != '' ">
            AND t.material_complete_num = #{e.materialCompleteNum}
        </if>
    </sql>

    <delete id="truncateTmpStockTable">
        truncate table dfs_tmp_material_readiness_inspection
    </delete>

</mapper>