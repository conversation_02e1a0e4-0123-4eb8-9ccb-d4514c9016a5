<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrder10minSumMapper">

	<update id="updateDelta">
        UPDATE dfs_work_order_10min_sum
        SET
            produce_quantity = produce_quantity + #{deltaProduceQuantity},
            unqualified_quantity = unqualified_quantity + #{deltaUnqualifiedQuantity},
            `time` = #{now}
        WHERE
            work_order_number = #{workOrderNumber}
        AND record_time <![CDATA[ > ]]> #{startTime}
        -- 只更新满足条件的最后两条（这部分还要用于计算），减少性能损耗
        ORDER BY id DESC limit 2
	</update>

</mapper>
