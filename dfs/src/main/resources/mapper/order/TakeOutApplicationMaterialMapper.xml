<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.TakeOutApplicationMaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.TakeOutApplicationMaterialEntity">
        <result column="id" property="id"/>
        <result column="take_out_application_id" property="takeOutApplicationId"/>
        <result column="take_out_application_num" property="takeOutApplicationNum"/>
        <result column="code" property="code"/>
        <result column="reference_num" property="referenceNum"/>
        <result column="actual_num" property="actualNum"/>
        <result column="plan_num" property="planNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.takeOutApplicationId != null and e.takeOutApplicationId != '' ">
            AND t.take_out_application_id = #{e.takeOutApplicationId}
        </if>
        <if test="e.takeOutApplicationNum != null and e.takeOutApplicationNum != '' ">
            AND t.take_out_application_num = #{e.takeOutApplicationNum}
        </if>
        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.referenceNum != null and e.referenceNum != '' ">
            AND t.reference_num = #{e.referenceNum}
        </if>
        <if test="e.actualNum != null and e.actualNum != '' ">
            AND t.actual_num = #{e.actualNum}
        </if>
        <if test="e.planNum != null and e.planNum != '' ">
            AND t.plan_num = #{e.planNum}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>

</mapper>
