<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.RuleSeqMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.RuleSeqEntity">
		    <result column="id" property="id" />
		    <result column="rule_type" property="ruleType" />
		    <result column="seq" property="seq" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>


	<sql id="select_content">
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.ruleType != null and e.ruleType != '' ">
					AND t.rule_type = #{e.ruleType}
				</if>
				<if test="e.seq != null and e.seq != '' ">
					AND t.seq = #{e.seq}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

	<update id="seqAddOne">
		update dfs_rule_seq
		SET seq = #{seq}
		where rule_type = #{typeCode}
		AND uuid = #{uuid}
		<if test="relationNumber != null and relationNumber != '' ">
			AND relation_number = #{relationNumber}
			AND relation_type = #{relationType}
		</if>
		<if test="relationNumber == null or relationNumber == '' ">
			AND (relation_number is null or relation_number ='')
		</if>
	</update>

	<insert id="insertOrUpdate" parameterType="com.yelink.dfs.entity.order.RuleSeqEntity">
		INSERT INTO dfs_rule_seq (
			rule_type,
			seq,
			create_time,
			update_time,
			uuid,
			relation_number,
			relation_type
		) VALUES (
			#{ruleType},
			#{seq},
			#{createTime},
			#{updateTime},
			#{uuid},
			#{relationNumber},
			#{relationType}
		)
		ON DUPLICATE KEY UPDATE
			seq = CASE
				WHEN VALUES(seq) > seq THEN VALUES(seq)
				ELSE seq
			END,
			update_time = VALUES(update_time)
	</insert>


</mapper>
