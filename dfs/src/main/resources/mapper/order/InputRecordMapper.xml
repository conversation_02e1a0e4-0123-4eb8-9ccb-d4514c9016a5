<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.InputRecordMapper">
    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.InputRecordEntity">
        <result column="id" property="id"/>
        <result column="work_order" property="workOrder"/>
        <result column="line_id" property="lineId"/>
        <result column="input_record_time" property="inputRecordTime"/>
        <result column="create_time" property="createTime"/>
        <result column="type" property="type"/>
    </resultMap>
    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.inputRecordTime != null and e.inputRecordTime != '' ">
            AND t.input_record_time = #{e.inputRecordTime}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>
</mapper>