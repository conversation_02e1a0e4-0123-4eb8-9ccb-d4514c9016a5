<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.RecordWorkOrderResumeMapper">

    <select id="batchQueryLast" resultType="com.yelink.dfs.entity.order.RecordWorkOrderResumeEntity">
        SELECT  * FROM  dfs_record_work_order_resume
        WHERE id IN(

            SELECT MAX(id) FROM dfs_record_work_order_resume
            WHERE work_order_number IN
            <foreach item="item" collection="workOrderNumbers" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            GROUP BY work_order_number
        )
    </select>

    <select id="groupLastByCondition" resultType="com.yelink.dfs.entity.order.RecordWorkOrderResumeEntity">
        SELECT t1.*
        FROM dfs_record_work_order_resume t1
        JOIN (
            SELECT work_order_number, MAX(id) AS max_id
            FROM dfs_record_work_order_resume
            <where>
                <if test="dto.startTimeDown != null">
                    AND start_time <![CDATA[ >= ]]> #{dto.startTimeDown}
                </if>
                <if test="dto.startTimeUp != null ">
                    AND start_time <![CDATA[ <= ]]> #{dto.startTimeUp}
                </if>
                <if test="dto.orderState != null ">
                    AND work_order_state = #{dto.orderState}
                </if>
            </where>
            GROUP BY work_order_number
        ) t2 ON t1.id = t2.max_id;
    </select>
</mapper>