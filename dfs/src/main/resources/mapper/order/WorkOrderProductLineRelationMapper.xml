<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderProductLineRelationMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderProductLineRelationEntity">
		    <result column="id" property="id" />
		    <result column="work_order_id" property="workOrderId" />
		    <result column="product_line_id" property="productLineId" />
		<result column="operation_order_id" property="operationOrderId" />
	</resultMap>


	<sql id="select_content">
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.workOrderId != null and e.workOrderId != '' ">
					AND t.work_order_id = #{e.workOrderId}
				</if>
				<if test="e.productLineId != null and e.productLineId != '' ">
					AND t.product_line_id = #{e.productLineId}
				</if>
				<if test="e.operationOrderId != null and e.operationOrderId != '' ">
					AND t.operation_order_id = #{e.operationOrderId}
				</if>
	</sql>


</mapper>