<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper">


    <select id="totalCountBeforeToday" resultType="java.lang.Double">
        select sum(`count`)
        from dfs_record_work_order_day_count
        where work_order_number = #{workOrderNumber}
          and `time` &lt; #{date}
    </select>

    <select id="selectSevenDayData" resultType="com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity">
        select b.id,
               b.work_order_number,
               ifnull(b.count, 0) as count,
               b.unqualified,
               a.tmp_date         as time,
               b.repair_qualified_number,
               b.line_id,
               b.material_code
        from (
                 SELECT curdate() as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 1 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 2 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 3 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 4 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 5 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 6 day) as tmp_date
             ) a
                 left join (
            SELECT * FROM `dfs_record_work_order_day_count` WHERE line_id IN (#{lineIds})
        ) b on a.tmp_date = b.time;
    </select>
    <select id="selectThirtyDayData" resultType="com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity">
        select b.id,
               b.work_order_number,
               ifnull(b.count, 0)   as count,
               b.unqualified,
               a.tmp_date           as time,
               b.repair_qualified_number,
               ifnull(b.line_id, 0) as line_id,
               b.material_code
        from (
                 SELECT curdate() as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 1 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 2 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 3 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 4 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 5 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 6 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 7 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 8 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 9 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 10 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 11 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 12 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 13 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 14 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 15 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 16 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 17 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 18 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 19 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 20 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 21 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 22 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 23 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 24 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 25 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 26 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 27 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 28 day) as tmp_date
                 union all
                 SELECT date_sub(curdate(), interval 29 day) as tmp_date
             ) a
                 left join (
            SELECT * FROM `dfs_record_work_order_day_count` WHERE line_id IN (#{lineIds})
        ) b on a.tmp_date = b.time;
    </select>

    <select id="selectProductionLineOutputInfo"
            resultType="com.yelink.dfs.entity.statement.dto.ProductionDailyResultDTO$ProductionLineOutputRecord">
        SELECT
        drwodc.id,
        drwodc.time,
        dg.gname,
        dpl.name,
        sum(drwodc.count) as 'count',                                                <!-- 需要统计某一天所有的count的总和 -->
        sum(drwodc.unqualified) as 'unqualified'
        FROM dfs_record_work_order_day_count AS drwodc
        left JOIN dfs_production_line AS dpl ON drwodc.line_id =
        dpl.production_line_id <!-- 产线产量表的line_id对应产线表的production_line_id -->
        left JOIN dfs_grid AS dg ON dpl.gid = dg.gid                                    <!-- 产线表的gid 对应 车间表的gid -->
        where 1=1                                                                        <!-- 不传时间的处理方式 -->
        <if test="productionLineOutputStartTime != null and productionLineOutputEndTime != null">
            AND drwodc.time >= #{productionLineOutputStartTime}
            AND drwodc.time <![CDATA[<=]]>  #{productionLineOutputEndTime}
        </if>
        group by dpl.production_line_id,drwodc.time <!-- 必须根据时间去分组 -->
        ORDER BY drwodc.time DESC;
    </select>


    <select id="getWorkCountPerDay" resultType="com.yelink.dfs.entity.order.dto.WorkCountPerDayQueryDTO">
        SELECT
        DATE_FORMAT(r.time,'%Y-%m-%d') AS `date`,
        m.name AS `name`,
        SUM(r.`count`) AS `count`
        FROM
        `dfs_record_work_order_day_count` r
        INNER JOIN dfs_material m ON r.material_code = m.code
        INNER JOIN dfs_production_line l ON r.line_id = l.production_line_id
        INNER JOIN dfs_grid g ON l.gid = g.gid
        <where>
            <if test=" gidList != null and gidList.size > 0">
                AND g.gid in
                <foreach item="item" collection="gidList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test=" materialTypeList != null and materialTypeList.size > 0">
                AND m.type in
                <foreach item="item" collection="materialTypeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test=" startDate != null ">
                <![CDATA[
                    AND r.time >= #{startQuery}
                ]]>
            </if>
            <if test=" endDate != null ">
                <![CDATA[
                    AND r.time <= #{endQuery}
                ]]>
            </if>
        </where>
        GROUP BY DATE_FORMAT(r.time,'%Y-%m-%d'), m.name
        ORDER BY r.time
    </select>

    <select id="getWorkCountPerDaySum" resultType="com.yelink.dfs.entity.order.dto.WorkCountPerDayQuerySumDTO">
        SELECT
        m.name AS `name`,
        sum(r.count) AS `count`
        FROM
        `dfs_record_work_order_day_count` r
        INNER JOIN dfs_material m ON r.material_code = m.code
        INNER JOIN dfs_production_line l ON r.line_id = l.production_line_id
        INNER JOIN dfs_grid g ON l.gid = g.gid
        <where>
            <if test=" gidList != null and gidList.size > 0">
                AND g.gid in
                <foreach item="item" collection="gidList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test=" materialTypeList != null and materialTypeList.size > 0">
                AND m.type in
                <foreach item="item" collection="materialTypeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test=" startDate != null ">
                <![CDATA[
                AND r.time >= #{startDate}
            ]]>
            </if>
            <if test=" endDate != null ">
                <![CDATA[
                AND r.time <= #{endDate}
            ]]>
            </if>
        </where>
        GROUP BY m.name
    </select>


<!--    <select id="selectReportCount" resultType="java.lang.Double">-->
<!--        SELECT COUNT(*)-->
<!--        FROM (-->
<!--        SELECT product_flow_code-->
<!--        FROM dfs_product_flow_code_record-->
<!--        WHERE relation_number=#{workOrderNumber} and is_report=1 and state= 1-->
<!--        GROUP BY product_flow_code-->
<!--        <if test=" recordDate != null ">-->
<!--            HAVING DATE_FORMAT(MIN(report_time),'%Y-%m-%d 00:00:00') =#{recordDate}-->
<!--        </if>-->
<!--        ) AS T;-->
<!--    </select>-->

    <select id="selectReportCount" resultType="java.lang.Double">
        SELECT count(id)
        FROM dfs_code_report
        WHERE relation_number=#{workOrderNumber} and is_report=1
        <if test=" recordDate != null ">
            and DATE_FORMAT(report_time,'%Y-%m-%d 00:00:00') =#{recordDate}
        </if>
        <if test="productFlowCodes != null and productFlowCodes.size != 0 ">
            AND product_flow_code in
            <foreach collection="productFlowCodes" item="productFlowCode" open="(" close=")" separator=",">
                #{productFlowCode}
            </foreach>
        </if>
    </select>

    <select id="selectLineReportCount" resultType="com.yelink.dfs.entity.order.dto.WorkOrderLineDayCountDTO">
        SELECT line_id as lineId, count(id) as count
        FROM dfs_code_report
        WHERE relation_number=#{workOrderNumber} and is_report=1
        <if test=" recordDate != null ">
            and DATE_FORMAT(report_time,'%Y-%m-%d 00:00:00') =#{recordDate}
        </if>
        GROUP BY line_id
    </select>

    <select id="selectInputCount" resultType="java.lang.Double">
        SELECT count(id)
        FROM dfs_code_report
        WHERE relation_number=#{workOrderNumber} and is_input=1
        <if test=" recordDate != null ">
            and DATE_FORMAT(input_time,'%Y-%m-%d 00:00:00') =#{recordDate}
        </if>
    </select>

    <select id="selectLineInputCount" resultType="com.yelink.dfs.entity.order.dto.WorkOrderLineDayCountDTO">
        SELECT line_id as lineId, count(id) as input
        FROM dfs_code_report
        WHERE relation_number=#{workOrderNumber} and is_input=1
        <if test=" recordDate != null ">
            and DATE_FORMAT(input_time,'%Y-%m-%d 00:00:00') =#{recordDate}
        </if>
        GROUP BY line_id
    </select>

    <select id="selectUnqualifiedCount" resultType="java.lang.Double">
        SELECT count(id)
        FROM dfs_code_report
        WHERE relation_number=#{workOrderNumber} and is_unqualified=1
        <if test=" recordDate != null ">
            and DATE_FORMAT(unqualified_time,'%Y-%m-%d 00:00:00') =#{recordDate}
        </if>
        <if test=" productFlowCodes != null and productFlowCodes.size != 0 ">
            AND product_flow_code in
            <foreach collection="productFlowCodes" item="productFlowCode" open="(" close=")" separator=",">
                #{productFlowCode}
            </foreach>
        </if>
    </select>

    <select id="selectLineUnqualifiedCount" resultType="com.yelink.dfs.entity.order.dto.WorkOrderLineDayCountDTO">
        SELECT line_id as lineId, count(id) as unqualified
        FROM dfs_code_report
        WHERE relation_number=#{workOrderNumber} and is_unqualified=1
        <if test=" recordDate != null ">
            and DATE_FORMAT(unqualified_time,'%Y-%m-%d 00:00:00') =#{recordDate}
        </if>
        GROUP BY line_id
    </select>

    <select id="selectFirstUnqualifiedCount" resultType="java.lang.Double">
        SELECT count(id)
        FROM dfs_code_report
        WHERE relation_number=#{workOrderNumber} and is_first_unqualified=1
        <if test=" recordDate != null ">
            and DATE_FORMAT(first_unqualified_time,'%Y-%m-%d 00:00:00') =#{recordDate}
        </if>
    </select>

    <select id="selectRecordDateList" resultType="com.yelink.dfs.entity.order.dto.RecordDateDTO">
        SELECT DATE_FORMAT(create_time, '%Y-%m-%d 00:00:00') AS recordDate
        FROM dfs_code_report
        where relation_number = #{workOrderNumber}
          and (is_report = 1 or is_unqualified = 1 or is_input = 1)
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d 00:00:00')
    </select>

    <select id="listProductionStatement" resultType="com.yelink.dfs.entity.statement.dto.StatementProductionDTO$StatementProductionVO">
        SELECT dayCount.time AS dateTime,
               dayCount.work_order_number AS workOrderNumber,
               workOrder.material_code AS materialCode,
               workOrder.line_id AS lineId,
               workOrder.device_id AS deviceId,
               workOrder.team_id AS teamId,
               workOrder.work_center_id AS workCenterId,
               workOrder.plan_quantity AS workOrderPlanQuantity,
               workOrder.finish_count AS workOrderFinishQuantity,
               workOrder.unqualified AS workOrderUnqualified,
               workOrder.product_order_number AS productOrderNumber,
               workOrder.sale_order_number AS saleOrderNumber,
               IFNULL(workPlan.plan_quantity,0) AS planQuantity,
               dayCount.count AS productQuantity,
               dayCount.unqualified AS unqualified
        FROM dfs_record_work_order_day_count dayCount
            LEFT JOIN dfs_work_order_plan workPlan ON workPlan.work_order_number=dayCount.work_order_number AND workPlan.time=dayCount.time
            INNER JOIN dfs_work_order workOrder ON dayCount.work_order_number=workOrder.work_order_number
        WHERE 1=1 <include refid="productionStatementCondition" />
        ORDER BY dayCount.time desc
    </select>


    <select id="listWeekProductionStatement" resultType="com.yelink.dfs.entity.statement.dto.StatementProductionDTO$StatementProductionVO">
        SELECT dayCount.time AS dateTime,
               dayCount.work_order_number AS workOrderNumber,
               workOrder.material_code AS materialCode,
               workOrder.line_id AS lineId,
               workOrder.device_id AS deviceId,
               workOrder.team_id AS teamId,
               workOrder.work_center_id AS workCenterId,
               workOrder.plan_quantity AS workOrderPlanQuantity,
               workOrder.finish_count AS workOrderFinishQuantity,
               workOrder.unqualified AS workOrderUnqualified,
               workOrder.product_order_number AS productOrderNumber,
               workOrder.sale_order_number AS saleOrderNumber,
               (SELECT SUM(workPlan.plan_quantity) FROM dfs_work_order_plan workPlan
                WHERE workPlan.work_order_number=dayCount.work_order_number AND WEEK(workPlan.time, 1)=WEEK(dayCount.time, 1)
               ) AS planQuantity,
               SUM(dayCount.count) AS productQuantity,
               SUM(dayCount.unqualified) AS unqualified
        FROM dfs_record_work_order_day_count dayCount
                 INNER JOIN dfs_work_order workOrder ON dayCount.work_order_number=workOrder.work_order_number
        WHERE 1=1 <include refid="productionStatementCondition" />

        GROUP BY WEEK(dayCount.time, 1),dayCount.work_order_number
        ORDER BY dayCount.time desc
    </select>

    <select id="listMonthProductionStatement" resultType="com.yelink.dfs.entity.statement.dto.StatementProductionDTO$StatementProductionVO">
        SELECT dayCount.time AS dateTime,
               dayCount.work_order_number AS workOrderNumber,
               workOrder.material_code AS materialCode,
               workOrder.line_id AS lineId,
               workOrder.device_id AS deviceId,
               workOrder.team_id AS teamId,
               workOrder.work_center_id AS workCenterId,
               workOrder.plan_quantity AS workOrderPlanQuantity,
               workOrder.finish_count AS workOrderFinishQuantity,
               workOrder.unqualified AS workOrderUnqualified,
               workOrder.product_order_number AS productOrderNumber,
               workOrder.sale_order_number AS saleOrderNumber,
               (SELECT SUM(workPlan.plan_quantity) FROM dfs_work_order_plan workPlan
               WHERE workPlan.work_order_number=dayCount.work_order_number AND MONTH(workPlan.time)=MONTH(dayCount.time)
                ) AS planQuantity,
               SUM(dayCount.count) AS productQuantity,
               SUM(dayCount.unqualified) AS unqualified
        FROM dfs_record_work_order_day_count dayCount
            INNER JOIN dfs_work_order workOrder ON dayCount.work_order_number=workOrder.work_order_number
        where 1=1 <include refid="productionStatementCondition" />
        GROUP BY MONTH(dayCount.time),dayCount.work_order_number
        ORDER BY dayCount.time desc
    </select>

    <sql id="productionStatementCondition" >
        <if test="selectDTO.timeRange != null and selectDTO.timeRange.startTime != null and selectDTO.timeRange.endTime != null " >
            AND dayCount.time BETWEEN #{selectDTO.timeRange.startTime} AND #{selectDTO.timeRange.endTime}
        </if>
        <if test="selectDTO.workOrderNumber != null and selectDTO.workOrderNumber != ''" >
            AND dayCount.work_order_number LIKE CONCAT('%', #{selectDTO.workOrderNumber}, '%')
        </if>
        <if test="selectDTO.productOrderNumber != null and selectDTO.productOrderNumber != ''" >
            AND workOrder.product_order_number LIKE CONCAT('%', #{selectDTO.productOrderNumber}, '%')
        </if>
        <if test="selectDTO.saleOrderNumber != null and selectDTO.saleOrderNumber != ''" >
            AND workOrder.sale_order_number LIKE CONCAT('%', #{selectDTO.saleOrderNumber}, '%')
        </if>
        <if test="selectDTO.saleOrderNumbers != null and selectDTO.saleOrderNumbers.size != 0">
            AND workOrder.sale_order_number in
            <foreach collection="selectDTO.saleOrderNumbers" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.lineIds != null and selectDTO.lineIds.size != 0">
            AND workOrder.line_id in
            <foreach collection="selectDTO.lineIds" item="lineId" open="(" close=")" separator=",">
                #{lineId}
            </foreach>
        </if>
        <if test="selectDTO.workOrderIds != null and selectDTO.workOrderIds.size != 0">
            AND workOrder.work_order_id in
            <foreach collection="selectDTO.workOrderIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.workCenterIds != null and selectDTO.workCenterIds.size != 0">
            AND workOrder.work_center_id in
            <foreach collection="selectDTO.workCenterIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.materialCodes != null and selectDTO.materialCodes.size != 0">
            AND BINARY workOrder.material_code in
            <foreach collection="selectDTO.materialCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="pageWorkOrderEfficiency" resultType="com.yelink.dfs.entity.statement.dto.StatementProductionDTO$WorkOrderEfficiencyVO">
        SELECT dayCount.time AS dateTime,
               dayCount.work_order_number AS workOrderNumber,
               workOrder.work_order_name AS workOrderName,
               workOrder.state AS state,
               workOrder.material_code AS materialCode,
               workOrder.work_center_id AS workCenterId,
               workOrder.work_center_name AS workCenterName,
               workOrder.line_id AS lineId,
               workOrder.device_id AS deviceId,
               workOrder.team_id AS teamId,
               workOrder.plan_quantity AS planQuantity,
               workOrder.finish_count AS finishCount,
               workOrder.unqualified AS unqualified,
               IFNULL(workPlan.plan_quantity,0) AS todayPlanQuantity,
               dayCount.count AS todayFinishCount
        FROM dfs_record_work_order_day_count dayCount
                 INNER JOIN dfs_work_order workOrder ON dayCount.work_order_number=workOrder.work_order_number
                 LEFT JOIN dfs_work_order_plan workPlan ON workPlan.work_order_number=dayCount.work_order_number AND workPlan.time=dayCount.time
        WHERE workOrder.state IN
        <foreach item="item" collection="selectDTO.states.split(',')" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="selectDTO.timeRange != null and selectDTO.timeRange.startTime != null and selectDTO.timeRange.endTime != null " >
            AND dayCount.time BETWEEN #{selectDTO.timeRange.startTime} AND #{selectDTO.timeRange.endTime}
        </if>
        <if test="selectDTO.workOrderNumber != null and selectDTO.workOrderNumber != ''" >
            AND dayCount.work_order_number LIKE CONCAT('%', #{selectDTO.workOrderNumber}, '%')
        </if>
        <if test="selectDTO.workOrderName != null and selectDTO.workOrderName != ''" >
            AND workOrder.work_order_name LIKE CONCAT('%', #{selectDTO.workOrderName}, '%')
        </if>
        <if test="selectDTO.workCenterIds != null and selectDTO.workCenterIds != ''" >
            AND workOrder.work_center_id IN
            <foreach item="item" collection="selectDTO.workCenterIds.split(',')" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.workCenterName != null and selectDTO.workCenterName != ''" >
            AND workOrder.work_center_name LIKE CONCAT('%', #{selectDTO.workCenterName}, '%')
        </if>
        <if test="selectDTO.lineIds != null and selectDTO.lineIds.size != 0">
            AND workOrder.line_id in
            <foreach collection="selectDTO.lineIds" item="lineId" open="(" close=")" separator=",">
                #{lineId}
            </foreach>
        </if>
        <if test="selectDTO.deviceIds != null and selectDTO.deviceIds.size != 0">
            AND workOrder.device_id in
            <foreach collection="selectDTO.deviceIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.teamIds != null and selectDTO.teamIds.size != 0">
            AND workOrder.team_id in
            <foreach collection="selectDTO.teamIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.materialCodes != null and selectDTO.materialCodes.size != 0">
            AND BINARY workOrder.material_code in
            <foreach collection="selectDTO.materialCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY dayCount.time desc
    </select>
</mapper>
