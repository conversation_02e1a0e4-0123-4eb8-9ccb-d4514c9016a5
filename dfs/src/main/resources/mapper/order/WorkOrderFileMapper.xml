<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderFileMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderFileEntity">
		    <result column="id" property="id" />
		    <result column="work_order_id" property="workOrderId" />
		    <result column="file" property="file" />
		    <result column="file_name" property="fileName" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.workOrderId != null and e.workOrderId != '' ">
					AND t.work_order_id = #{e.workOrderId}
				</if>
				<if test="e.fileName != null and e.fileName != '' ">
					AND t.file_name = #{e.fileName}
				</if>
				<if test="e.file != null and e.file != '' ">
					AND t.file = #{e.file}
				</if>
	</sql>

</mapper>