<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.OrderWorkOrderMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.OrderWorkOrderEntity">
        <result column="id" property="id"/>
        <result column="work_order_id" property="workOrderId"/>
        <result column="order_id" property="orderId"/>
        <result column="type" property="type"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrderId != null and e.workOrderId != '' ">
            AND t.work_order_id = #{e.workOrderId}
        </if>
        <if test="e.orderId != null and e.orderId != '' ">
            AND t.order_id = #{e.orderId}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
    </sql>

</mapper>
