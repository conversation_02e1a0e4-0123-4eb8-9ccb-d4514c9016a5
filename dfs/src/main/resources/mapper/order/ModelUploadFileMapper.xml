<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.ModelUploadFileMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.common.ModelUploadFileEntity">
        <result column="id" property="id" />
        <result column="model_type" property="modelType" />
        <result column="file_name" property="fileName" />
        <result column="file_address" property="fileAddress" />
        <result column="upload_time" property="uploadTime" />
        <result column="upload_person" property="uploadPerson" />
    </resultMap>

</mapper>
