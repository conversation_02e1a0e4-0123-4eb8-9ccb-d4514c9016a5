<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderInputMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderInputEntity">
        <result column="id" property="id"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="input" property="input"/>
        <result column="create_date" property="createDate"/>
        <result column="report_date" property="reportDate"/>
        <result column="operation" property="operation"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.quantity != null and e.quantity != '' ">
            AND t.input = #{input}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.reportDate != null and e.reportDate != '' ">
            AND t.report_date = #{e.reportDate}
        </if>
        <if test="e.operation != null and e.operation != '' ">
            AND t.operation = #{e.operation}
        </if>
    </sql>

    <select id="getList" resultType="com.yelink.dfs.entity.order.WorkOrderInputEntity">
        select input.* from dfs_work_order_input input
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>