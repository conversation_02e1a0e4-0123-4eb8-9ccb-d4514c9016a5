<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderRemarkMapper">

    <select id="getList" resultType="com.yelink.dfs.entity.order.WorkOrderRemarkEntity">
        select workOrderRemark.* from dfs_work_order_remark workOrderRemark
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
</mapper>
