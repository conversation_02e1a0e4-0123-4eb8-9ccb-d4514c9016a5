<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderProcedureRelationMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity">
		    <result column="id" property="id" />
		    <result column="work_order_number" property="workOrderNumber" />
		    <result column="procedure_id" property="procedureId" />
		    <result column="procedure_name" property="procedureName" />
		    <result column="craft_procedure_id" property="craftProcedureId" />
		    <result column="craft_procedure_name" property="craftProcedureName" />
	</resultMap>


	<sql id="select_content">
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
					AND t.work_order_number = #{e.workOrderNumber}
				</if>
				<if test="e.procedureId != null and e.procedureId != '' ">
					AND t.procedure_id = #{e.procedureId}
				</if>
				<if test="e.procedureName != null and e.procedureName != '' ">
					AND t.procedure_name = #{e.procedureName}
				</if>
				<if test="e.craftProcedureId != null and e.craftProcedureId != '' ">
					AND t.craft_procedure_id = #{e.craftProcedureId}
				</if>
				<if test="e.craftProcedureName != null and e.craftProcedureName != '' ">
					AND t.craft_procedure_name = #{e.craftProcedureName}
				</if>
	</sql>

	<select id="getWorkOrderCountByCirculationState" resultType="java.lang.Integer" >
		SELECT
		COUNT(wo.work_order_number)
		FROM
		dfs_work_order_procedure_relation wopr
		-- 	工单表
		LEFT JOIN dfs_work_order wo ON wo.work_order_number = wopr.work_order_number
		<where>
			<if test="nextCpIds != null and nextCpIds.size > 0 ">
				AND wopr.craft_procedure_id IN
				<foreach item="item" index="index" collection="nextCpIds" open="(" separator="," close=")" >
					#{item}
				</foreach>
			</if>
			<if test="state != null and state !='' ">
				AND wo.circulation_state = #{state}
			</if>
			AND wo.work_order_number IS NOT NULL
		</where>
	</select>

	<select id="getMinCirculationDuration" resultType="com.yelink.dfs.entity.order.WorkOrderEntity" >
		SELECT
		wopr.craft_procedure_name AS procedureName,
		MIN(wo.circulation_duration) AS circulation_duration
		FROM
		dfs_work_order_procedure_relation wopr
		-- 	工单表
		LEFT JOIN dfs_work_order wo ON wo.work_order_number = wopr.work_order_number
		--	工单订单关联表
		LEFT JOIN dfs_order_work_order_relation owor ON owor.work_order_id = wopr.work_order_id
		<where>
			<if test="nextCpId != null and nextCpId !='' ">
				AND wopr.craft_procedure_id = #{nextCpId}
			</if>
			<if test="orderId != null and orderId !='' ">
				AND owor.order_id =  #{orderId}
			</if>
			AND wo.circulation_duration IS NOT NULL
		</where>
	</select>

</mapper>
