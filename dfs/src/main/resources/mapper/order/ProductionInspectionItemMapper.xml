<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.ProductionInspectionItemMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.ProductionInspectionItemEntity">
		    <result column="id" property="id" />
		    <result column="production_inspection_item_code" property="productionInspectionItemCode" />
		    <result column="production_inspection_item_name" property="productionInspectionItemName" />
		    <result column="production_inspection_project_team_id" property="productionInspectionProjectTeamId" />
		    <result column="production_inspection_project_team_name" property="productionInspectionProjectTeamName" />
		    <result column="inspection_standard_name" property="inspectionStandardName" />
		    <result column="inspection_contents" property="inspectionContents" />
		    <result column="reference_value" property="referenceValue" />
		    <result column="knowledge_base" property="knowledgeBase" />
		    <result column="state" property="state" />
		    <result column="remark" property="remark" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.productionInspectionItemCode != null and e.productionInspectionItemCode != '' ">
					AND t.production_inspection_item_code = #{e.productionInspectionItemCode}
				</if>
				<if test="e.productionInspectionItemName != null and e.productionInspectionItemName != '' ">
					AND t.production_inspection_item_name = #{e.productionInspectionItemName}
				</if>
				<if test="e.productionInspectionProjectTeamId != null and e.productionInspectionProjectTeamId != '' ">
					AND t.production_inspection_project_team_id = #{e.productionInspectionProjectTeamId}
				</if>
				<if test="e.productionInspectionProjectTeamName != null and e.productionInspectionProjectTeamName != '' ">
					AND t.production_inspection_project_team_name = #{e.productionInspectionProjectTeamName}
				</if>
				<if test="e.inspectionStandardName != null and e.inspectionStandardName != '' ">
					AND t.inspection_standard_name = #{e.inspectionStandardName}
				</if>
				<if test="e.inspectionContents != null and e.inspectionContents != '' ">
					AND t.inspection_contents = #{e.inspectionContents}
				</if>
				<if test="e.referenceValue != null and e.referenceValue != '' ">
					AND t.reference_value = #{e.referenceValue}
				</if>
				<if test="e.knowledgeBase != null and e.knowledgeBase != '' ">
					AND t.knowledge_base = #{e.knowledgeBase}
				</if>
				<if test="e.state != null and e.state != '' ">
					AND t.state = #{e.state}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
	</sql>

</mapper>