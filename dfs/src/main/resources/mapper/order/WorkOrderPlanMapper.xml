<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderPlanMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderPlanEntity">
        <result column="id" property="id"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="time" property="time"/>
        <result column="plan_quantity" property="planQuantity"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
        <if test="e.planQuantity != null and e.planQuantity != '' ">
            AND t.plan_quantity = #{e.planQuantity}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>

    <select id="getTotalQuantityByWorkOrder" resultType="java.lang.Double">
        select sum(plan_quantity) from dfs_work_order_plan where work_order_number=#{workOrderNumber}
    </select>

    <insert id="saveOrUpdateRecord" >
        INSERT INTO dfs_work_order_plan(work_order_number, time, plan_quantity, create_by, create_date)
        VALUES (#{workOrderNumber}, #{planDate}, #{planQuantity}, #{username}, #{createDate})
            ON DUPLICATE KEY UPDATE plan_quantity=#{planQuantity},update_by=#{username},update_date=CURRENT_TIMESTAMP();
    </insert>

</mapper>
