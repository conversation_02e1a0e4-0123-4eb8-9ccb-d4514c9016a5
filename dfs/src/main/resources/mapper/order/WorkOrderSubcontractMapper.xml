<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderSubcontractMapper">


    <select id="orderCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT s.work_order_number)
        FROM `dfs_work_order_subcontract` s
        INNER JOIN dfs_work_order o on o.work_order_number = s.work_order_number
        <where>
<!--            <if test="dto.lineId != null">-->
<!--                and o.line_id = #{dto.lineId}-->
<!--            </if>-->
<!--            <if test="dto.teamId != null">-->
<!--                and o.team_id = #{dto.teamId}-->
<!--            </if>-->
<!--            <if test="dto.deviceId != null">-->
<!--                and o.device_id = #{dto.deviceId}-->
<!--            </if>-->
            <if test="dto.lineId != null">
                AND (o.work_order_number in (select work_order_number from dfs_work_order_basic_unit_relation WHERE production_basic_unit_id = #{dto.lineId} and work_center_type = 'line')
                )
            </if>
            <if test="dto.teamId != null">
                AND (o.work_order_number in (select work_order_number from dfs_work_order_basic_unit_relation WHERE production_basic_unit_id = #{dto.teamId} and work_center_type = 'team')
                )
            </if>
            <if test="dto.deviceId != null">
                AND (o.work_order_number in (select work_order_number from dfs_work_order_basic_unit_relation WHERE production_basic_unit_id = #{dto.deviceId} and work_center_type = 'device')
                )
            </if>
            and o.actual_end_date is null
        </where>
    </select>


</mapper>
