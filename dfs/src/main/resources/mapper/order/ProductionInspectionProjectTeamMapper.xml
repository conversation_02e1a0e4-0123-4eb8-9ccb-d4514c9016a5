<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.ProductionInspectionProjectTeamMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.ProductionInspectionProjectTeamEntity">
		    <result column="id" property="id" />
		    <result column="production_inspection_project_team_name" property="productionInspectionProjectTeamName" />
		    <result column="production_inspection_project_team_code" property="productionInspectionProjectTeamCode" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_by" property="createBy" />
		    <result column="update_time" property="updateTime" />
		    <result column="create_time" property="createTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.productionInspectionProjectTeamName != null and e.productionInspectionProjectTeamName != '' ">
					AND t.production_inspection_project_team_name = #{e.productionInspectionProjectTeamName}
				</if>
				<if test="e.productionInspectionProjectTeamCode != null and e.productionInspectionProjectTeamCode != '' ">
					AND t.production_inspection_project_team_code = #{e.productionInspectionProjectTeamCode}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>