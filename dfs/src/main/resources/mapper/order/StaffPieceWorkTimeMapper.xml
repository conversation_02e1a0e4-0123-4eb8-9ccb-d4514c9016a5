<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.StaffPieceWorkTimeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.StaffPieceWorkTimeEntity">
        <result column="staff_piece_work_time_id" property="staffPieceWorkTimeId" />
        <result column="staff_code" property="staffCode" />
        <result column="staff_name" property="staffName" />
        <result column="shift" property="shift" />
        <result column="work_order_number" property="workOrderNumber" />
        <result column="assignment_number" property="assignmentNumber" />
        <result column="product_num" property="productNum" />
        <result column="hours" property="hours" />
        <result column="report_time" property="reportTime" />
    </resultMap>


    <sql id="select_content">
        <if test="e.staffPieceWorkTimeId != null and e.staffPieceWorkTimeId != '' ">
            AND t.staff_piece_work_time_id = #{e.staffPieceWorkTimeId}
        </if>
        <if test="e.staffCode != null and e.staffCode != '' ">
            AND t.staff_code = #{e.staffCode}
        </if>
        <if test="e.staffName != null and e.staffName != '' ">
            AND t.staff_name = #{e.staffName}
        </if>
        <if test="e.shift != null and e.shift != '' ">
            AND t.shift = #{e.shift}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.assignmentNumber != null and e.assignmentNumber != '' ">
            AND t.assignment_number = #{e.assignmentNumber}
        </if>
        <if test="e.productNum != null and e.productNum != '' ">
            AND t.product_num = #{e.productNum}
        </if>
        <if test="e.hours != null and e.hours != '' ">
            AND t.hours = #{e.hours}
        </if>
        <if test="e.reportTime != null and e.reportTime != '' ">
            AND t.report_time = #{e.reportTime}
        </if>
    </sql>

</mapper>
