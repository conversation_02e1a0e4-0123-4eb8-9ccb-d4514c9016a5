<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.RecordWorkOrderUnqualifiedMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity">
        <result column="id" property="id"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="work_order_name" property="workOrderName"/>
        <result column="sequence_id" property="sequenceId"/>
        <result column="abnormal_name" property="abnormalName"/>
        <result column="create_by" property="createBy"/>
        <result column="material_code" property="materialCode"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrderId != null and e.workOrderId != '' ">
            AND t.work_order_id = #{e.workOrderId}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.workOrderName != null and e.workOrderName != '' ">
            AND t.work_order_name = #{e.workOrderName}
        </if>
        <if test="e.abnormalId != null and e.abnormalId != '' ">
            AND t.abnormal_id = #{e.abnormalId}
        </if>
        <if test="e.abnormalName != null and e.abnormalName != '' ">
            AND t.abnormal_name = #{e.abnormalName}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
    </sql>
    <select id="getCount" resultType="java.lang.Integer">
    select count(1) from(
        select DISTINCT(a.sequence_id) from dfs_record_work_order_unqualified a where a.work_order_number = #{workOrderNumber}
    ) b
    </select>
    <select id="getList" resultType="com.yelink.dfs.entity.quality.dto.QualityRecordsDTO">
        SELECT
        b.create_date createDate,
        f.fid,
        f.fname fname,
        l.`name` lineName,
        max( b.material_code ) materialCode,
        b.work_order_number workOrderNumber,
        count( *) abnormalTotal
        FROM
        (
        SELECT
        max( work_order_number ) work_order_number,
        max( work_order_name ) work_order_name,
        max( fid ) fid,
        max( material_code ) material_code,
        date_format( max( create_date ), '%Y-%m-%d' ) create_date
        FROM
        dfs_record_work_order_unqualified
        GROUP BY
        sequence_id
        ) b
        LEFT JOIN dfs_work_order a ON a.work_order_number = b.work_order_number
        LEFT JOIN dfs_facilities f ON b.fid = f.fid
        LEFT JOIN dfs_production_line l ON a.line_id = l.production_line_id
        <where>
            <if test="startTime!=null and startTime!=''">
                AND b.create_date between #{startTime} and #{endTime}
            </if>
            <if test="workOrderNumber!=null and workOrderNumber!=''">
                AND b.work_order_number = #{workOrderNumber}
            </if>
            <if test="materialCode!=null and materialCode!='' ">
                AND b.material_code = #{materialCode}
            </if>
            <if test="lineId!=null and lineId!=''">
                AND a.line_id = #{lineId}
            </if>
        </where>
        GROUP BY
        b.work_order_number,
        b.fid,
        b.create_date


    </select>
    <select id="getPage" resultType="com.yelink.dfs.entity.quality.dto.QualityRecordsDTO">
        SELECT
        b.create_date createDate,
        f.fname fname,
        f.fid,
        l.`name` lineName,
        max( b.material_code ) materialCode,
        b.work_order_number workOrderNumber,
        count( * ) abnormalTotal
        FROM
        (
        SELECT
        max( work_order_number ) work_order_number,
        max( work_order_name ) work_order_name,
        max( fid ) fid,
        max( material_code ) material_code,
        date_format( max( create_date ), '%Y-%m-%d' ) create_date
        FROM
        dfs_record_work_order_unqualified
        GROUP BY
        sequence_id
        ) b
        LEFT JOIN dfs_work_order a ON a.work_order_number = b.work_order_number
        LEFT JOIN dfs_facilities f ON b.fid = f.fid
        LEFT JOIN dfs_production_line l ON a.line_id = l.production_line_id
        <where>
            <if test="startTime!=null and startTime!=''">
                AND b.create_date between #{startTime} and #{endTime}
            </if>
            <if test="workOrderNumber!=null and workOrderNumber!=''">
                AND b.work_order_number = #{workOrderNumber}
            </if>
            <if test="materialCode!=null and materialCode!='' ">
                AND b.material_code = #{materialCode}
            </if>
            <if test="lineId!=null and lineId!=''">
                AND a.line_id = #{lineId}
            </if>
        </where>
        GROUP BY
        b.work_order_number,
        b.fid,
        b.create_date
    </select>
    <select id="getUnqualified" resultType="java.lang.Integer">
        SELECT count(1) FROM (
        SELECT
        max( work_order_number ) work_order_number,
        max( work_order_name ) work_order_name,
        max( fid ) fid,
        max( material_code ) material_code
        FROM
        dfs_record_work_order_unqualified
        GROUP BY
        sequence_id
        )a WHERE a.work_order_number=#{workOrderNumber}
        <if test="fid!=null">
            and a.fid=#{fid}
        </if>
    </select>

    <select id="getUnqualifiedByDate" resultType="java.lang.Integer">
        SELECT count(1)
        FROM dfs_record_work_order_unqualified
        WHERE id in
        (
            SELECT
                max(id)
            FROM
                dfs_record_work_order_unqualified
            <where>
                <if test="recordDate!=null">
                    and record_date=DATE_FORMAT(#{recordDate},'%Y-%m-%d')
                </if>
                <if test="workOrderNumber!=null and workOrderNumber!=''">
                    and work_order_number=#{workOrderNumber}
                </if>
                <if test="fid!=null">
                    and fid=#{fid}
                </if>
                <if test="fid==null">
                    and fid is null
                </if>
            </where>
            GROUP BY sequence_id
        )
        AND defect_id !=0
    </select>
    <select id="getUnqualifiedByLineId" resultType="com.yelink.dfs.entity.screen.vo.UnqualifiedVO">
        select re.abnormal_name as unqualifiedName, count(re.abnormal_name) as num from dfs_record_work_order_unqualified re
        inner join  dfs_work_order wo on re.work_order_number = wo.work_order_number
        WHERE re.record_date=#{date} and defect_type != 'qualified'
          <if test="lineIds !=null and lineIds.size != 0">
              and wo.line_id in
              <foreach collection="lineIds" item="lineId" open="(" close=")" separator=",">
                  #{lineId}
              </foreach>
          </if>
        GROUP BY re.abnormal_name order by num DESC  limit #{topNum}
    </select>

    <select id="getPageV2" resultType="com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity">
        select recordWorkOrderUnqualified.* from dfs_record_work_order_unqualified recordWorkOrderUnqualified
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
</mapper>
