<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderTeamMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderTeamEntity">
        <result column="id" property="id"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="member_name" property="memberName"/>
        <result column="team_role" property="teamRole"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
    </resultMap>

</mapper>
