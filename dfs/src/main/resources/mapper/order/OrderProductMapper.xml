<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.OrderProductMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.OrderProductEntity">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="product_id" property="productId"/>
        <result column="product_code" property="productCode"/>
        <result column="standard" property="standard"/>
        <result column="quantity" property="quantity"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.orderId != null and e.orderId != '' ">
            AND t.order_id = #{e.orderId}
        </if>
        <if test="e.orderNumber != null and e.orderNumber != '' ">
            AND t.order_number = #{e.orderNumber}
        </if>
        <if test="e.productId != null and e.productId != '' ">
            AND t.product_id = #{e.productId}
        </if>
        <if test="e.productCode != null and e.productCode != '' ">
            AND t.product_code = #{e.productCode}
        </if>
        <if test="e.standard != null and e.standard != '' ">
            AND t.standard = #{e.standard}
        </if>
        <if test="e.quantity != null and e.quantity != '' ">
            AND t.quantity = #{e.quantity}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>
