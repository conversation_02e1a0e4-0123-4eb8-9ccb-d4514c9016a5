<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.TakeOutApplicationStockMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.TakeOutApplicationStockEntity">
        <result column="id" property="id"/>
        <result column="take_out_application_id" property="takeOutApplicationId"/>
        <result column="take_out_application_num" property="takeOutApplicationNum"/>
        <result column="stock_id" property="stockId"/>
        <result column="stock_num" property="stockNum"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.takeOutApplicationId != null and e.takeOutApplicationId != '' ">
            AND t.take_out_application_id = #{e.takeOutApplicationId}
        </if>
        <if test="e.takeOutApplicationNum != null and e.takeOutApplicationNum != '' ">
            AND t.take_out_application_num = #{e.takeOutApplicationNum}
        </if>
        <if test="e.stockId != null and e.stockId != '' ">
            AND t.stock_id = #{e.stockId}
        </if>
        <if test="e.stockNum != null and e.stockNum != '' ">
            AND t.stock_num = #{e.stockNum}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>
