<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.ProductionInspectionPlanMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.ProductionInspectionPlanEntity">
		    <result column="id" property="id" />
		    <result column="production_inspection_plan_code" property="productionInspectionPlanCode" />
		    <result column="production_inspection_plan_name" property="productionInspectionPlanName" />
		    <result column="state" property="state" />
		    <result column="remark" property="remark" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.productionInspectionPlanCode != null and e.productionInspectionPlanCode != '' ">
					AND t.production_inspection_plan_code = #{e.productionInspectionPlanCode}
				</if>
				<if test="e.productionInspectionPlanName != null and e.productionInspectionPlanName != '' ">
					AND t.production_inspection_plan_name = #{e.productionInspectionPlanName}
				</if>
				<if test="e.state != null and e.state != '' ">
					AND t.state = #{e.state}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
	</sql>

</mapper>