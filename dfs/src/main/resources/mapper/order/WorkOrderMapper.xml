<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.order.WorkOrderMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.order.WorkOrderEntity">
        <result column="work_order_id" property="workOrderId"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="work_order_name" property="workOrderName"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="actual_start_date" property="actualStartDate"/>
        <result column="actual_end_date" property="actualEndDate"/>
        <result column="product_order_number" property="productOrderNumber"/>
        <result column="state" property="state"/>
        <result column="line_code" property="lineCode"/>
        <result column="plan_quantity" property="planQuantity"/>
        <result column="pnumber" property="pnumber"/>
        <result column="is_pid" property="isPid"/>
        <result column="id_sequence" property="idSequence"/>
        <result column="remark" property="remark"/>
        <result column="type" property="type"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="mag_phone" property="magPhone"/>
        <result column="mag_name" property="magName"/>
        <result column="material_code" property="materialCode"/>
        <result column="unqualified" property="unqualified"/>
        <result column="current_inventory" property="currentInventory"/>
        <result column="material_is_complete" property="materialIsComplete"/>
        <result column="material_complete_num" property="materialCompleteNum"/>
        <result column="material_owe_num" property="materialOweNum"/>
        <result column="theoretical_working_hours" property="theoreticalWorkingHours"/>
        <result column="approver" property="approver"/>
        <result column="erp_document_code" property="erpDocumentCode"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
        <result column="effective_hours" property="effectiveHours"/>
        <result column="planned_working_hours" property="plannedWorkingHours"/>
        <result column="actual_working_hours" property="actualWorkingHours"/>
        <result column="execution_status" property="executionStatus"/>
        <result column="time_difference" property="timeDifference"/>
        <result column="issue" property="issue"/>
        <result column="relate_order_material_id" property="relateOrderMaterialId"/>
        <result column="circulation_duration" property="circulationDuration"/>
        <result column="in_stock_count" property="inStockCount"/>
    </resultMap>

    <resultMap id="JudgeOrderMap" type="com.yelink.dfs.entity.order.vo.JudgeOrderVO">
        <result column="work_order_number" property="orderNumber"/>
        <result column="work_order_id" property="orderId"/>
    </resultMap>

    <sql id="select_content">
        <if test="e.plannedWorkingHours != null and e.plannedWorkingHours != '' ">
            AND t.planned_working_hours = #{e.plannedWorkingHours}
        </if>
        <if test="e.actualWorkingHours != null and e.actualWorkingHours != '' ">
            AND t.actual_working_hours = #{e.actualWorkingHours}
        </if>
        <if test="e.executionStatus != null and e.executionStatus != '' ">
            AND t.execution_status = #{e.executionStatus}
        </if>
        <if test="e.timeDifference != null and e.timeDifference != '' ">
            AND t.time_difference = #{e.timeDifference}
        </if>
        <if test="e.unqualified != null and e.unqualified != '' ">
            AND t.unqualified = #{e.unqualified}
        </if>
        <if test="e.theoreticalWorkingHours != null and e.theoreticalWorkingHours != '' ">
            AND t.theoretical_working_hours = #{e.theoreticalWorkingHours}
        </if>
        <if test="e.reviewer != null and e.reviewer != '' ">
            AND t.reviewer = #{e.reviewer}
        </if>
        <if test="e.targetJobId != null and e.targetJobId != '' ">
            AND t.target_job_id = #{e.targetJobId}
        </if>
        <if test="e.alarmJobId != null and e.alarmJobId != '' ">
            AND t.alarm_job_id = #{e.alarmJobId}
        </if>
        <if test="e.noticeUserId != null and e.noticeUserId != '' ">
            AND t.notice_user_id = #{e.noticeUserId}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.materialState != null and e.materialState != '' ">
            AND t.material_state = #{e.materialState}
        </if>
        <!--        <if test="e.materialTandard != null and e.materialTandard != '' ">-->
        <!--            AND t.material_tandard = #{e.materialTandard}-->
        <!--        </if>-->
        <if test="e.completedQuantity != null and e.completedQuantity != '' ">
            AND t.completed_quantity = #{e.completedQuantity}
        </if>
        <if test="e.workOrderId != null and e.workOrderId != '' ">
            AND t.work_order_id = #{e.workOrderId}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.workOrderName != null and e.workOrderName != '' ">
            AND t.work_order_name = #{e.workOrderName}
        </if>
        <if test="e.startDate != null and e.startDate != '' ">
            AND t.start_date = #{e.startDate}
        </if>
        <if test="e.endDate != null and e.endDate != '' ">
            AND t.end_date = #{e.endDate}
        </if>
        <if test="e.actualStartDate != null and e.actualStartDate != '' ">
            AND t.actual_start_date = #{e.actualStartDate}
        </if>
        <if test="e.actualEndDate != null and e.actualEndDate != '' ">
            AND t.actual_end_date = #{e.actualEndDate}
        </if>
        <if test="e.productOrderNumber != null and e.productOrderNumber != '' ">
            AND t.product_order_number = #{e.productOrderNumber}
        </if>
        <if test="e.orderName != null and e.orderName != '' ">
            AND t.order_name = #{e.orderName}
        </if>
        <if test="e.productCode != null and e.productCode != '' ">
            AND t.product_code = #{e.productCode}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.lineCode != null and e.lineCode != '' ">
            AND t.line_code = #{e.lineCode}
        </if>
        <if test="e.planQuantity != null and e.planQuantity != '' ">
            AND t.plan_quantity = #{e.planQuantity}
        </if>
        <if test="e.craftCode != null and e.craftCode != '' ">
            AND t.craft_code = #{e.craftCode}
        </if>
        <if test="e.craftName != null and e.craftName != '' ">
            AND t.craft_name = #{e.craftName}
        </if>
        <if test="e.craftVersion != null and e.craftVersion != '' ">
            AND t.craft_version = #{e.craftVersion}
        </if>
        <if test="e.productVersion != null and e.productVersion != '' ">
            AND t.product_version = #{e.productVersion}
        </if>
        <if test="e.productName != null and e.productName != '' ">
            AND t.product_name = #{e.productName}
        </if>
        <if test="e.productTotal != null and e.productTotal != '' ">
            AND t.product_total = #{e.productTotal}
        </if>
        <if test="e.pnumber != null and e.pnumber != '' ">
            AND t.pnumber = #{e.pnumber}
        </if>
        <if test="e.isPid != null and e.isPid != '' ">
            AND t.is_pid = #{e.isPid}
        </if>
        <if test="e.idSequence != null and e.idSequence != '' ">
            AND t.id_sequence = #{e.idSequence}
        </if>
        <if test="e.isEndCraft != null and e.isEndCraft != '' ">
            AND t.is_end_craft = #{e.isEndCraft}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.currentInventory != null and e.currentInventory != '' ">
            AND t.current_inventory = #{e.currentInventory}
        </if>
        <if test="e.materialIsComplete != null and e.materialIsComplete != '' ">
            AND t.material_is_complete = #{e.materialIsComplete}
        </if>
        <if test="e.materialOweNum != null and e.materialOweNum != '' ">
            AND t.material_owe_num = #{e.materialOweNum}
        </if>
        <if test="e.materialCompleteNum != null and e.materialCompleteNum != '' ">
            AND t.material_complete_num = #{e.materialCompleteNum}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.magPhone != null and e.magPhone != '' ">
            AND t.mag_phone = #{e.magPhone}
        </if>
        <if test="e.magName != null and e.magName != '' ">
            AND t.mag_name = #{e.magName}
        </if>
        <if test="e.erpDocumentCode != null and e.erpDocumentCode != '' ">
            AND t.erp_document_code = #{e.erpDocumentCode}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
        <if test="e.effectiveHours != null and e.effectiveHours != '' ">
            AND t.effective_hours = #{e.effectiveHours}
        </if>
        <if test="e.circulationDuration != null and e.circulationDuration != '' ">
            AND t.circulation_duration = #{e.circulationDuration}
        </if>
        <if test="e.inStockCount != null and e.inStockCount != '' ">
            AND t.in_stock_count = #{e.inStockCount}
        </if>
    </sql>

    <select id="getAppointWorkOrders" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT * FROM dfs_work_order
        WHERE is_pid = FALSE
        <if test="lineIds != null and lineIds.length > 0">
            and line_id in
            <foreach item="item" collection="lineIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        or line_id is null
        ORDER BY
        -- FIELD( state, '3', '2', '8', '9', '5', '6', '1', '7' ),
        -- start_date
        create_date
        DESC
    </select>
    <select id="getFinishWorkOrder" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT
        *
        FROM
        dfs_work_order
        WHERE
        create_date BETWEEN date_sub( now(), INTERVAL 6 MONTH )
        AND now()
        AND state = '5'
        AND line_id = #{productionLineId}
        ORDER BY create_date DESC
    </select>
    <select id="getWorkOrderByName" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT * FROM dfs_work_order WHERE work_order_name = #{workOrderName}
    </select>
    <select id="getByWorkOrderNumber" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT * FROM dfs_work_order WHERE work_order_number = #{workOrderNumber}
    </select>

    <select id="getNumberListByWorkOrderNumber" resultType="com.yelink.dfs.entity.order.vo.PrintSourceOrderVO">
        SELECT work_order_number as orderNumber, plan_quantity FROM dfs_work_order
        <if test="workOrderNumber != null">
            WHERE work_order_number LIKE "%${workOrderNumber}%"
        </if>
        ORDER BY create_date DESC
    </select>

    <select id="getMonthNewRecord" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT  * FROM dfs_work_order WHERE
        DATE_FORMAT( create_date, '%Y%m' ) = DATE_FORMAT( CURDATE( ) , '%Y%m' )
        ORDER BY create_date DESC,work_order_id DESC limit 1
    </select>
    <select id="getToDayRecords" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT * FROM dfs_work_order
        WHERE DATEDIFF(start_date,NOW())=0
        <if test="lineId != null">
            and line_id=#{lineId}
        </if>
        <if test="state != null">
            and state=#{state}
        </if>
        <if test="workOrderNumber != null and workOrderNumber != ''">
            and work_order_number=#{workOrderNumber}
        </if>
        <if test="workOrderName != null and workOrderName != ''">
            and work_order_name=#{workOrderName}
        </if>
        <if test="materialCode != null and materialCode != ''">
            and material_code=#{materialCode}
        </if>
        <if test="materialName != null and materialName != ''">
            and material_name=#{materialName}
        </if>
    </select>
    <select id="getFutureSevenDaysList" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT
        *
        FROM
        dfs_work_order
        WHERE
        date( start_date ) &gt;= CURDATE()
        AND date( start_date ) &lt;= DATE_ADD( CURDATE(), INTERVAL 7 DAY )
        ORDER BY
        start_date ASC

    </select>

    <delete id="dropScheduleReverseMaterialTable">
        DROP TABLE IF EXISTS `dfs_config_schedule_reverse_material`
    </delete>

    <update id="createScheduleInitReverseMaterialTable">
        CREATE TABLE IF NOT EXISTS `dfs_config_schedule_reverse_material`
        (
            `material_code` varchar(100) DEFAULT NULL COMMENT '物料名称'
        ) ENGINE = InnoDB
          DEFAULT CHARSET = utf8mb4
          COLLATE = utf8mb4_general_ci COMMENT ='工单排程--倒排物料配置表'
    </update>

    <insert id="initScheduleMaterialCodeData">
        INSERT INTO `dfs_config_schedule_reverse_material` (material_code)
        VALUES
        <foreach item="materialCode" collection="materialCodes" separator=",">
            (#{materialCode})
        </foreach>
    </insert>

    <insert id="addColumnToScheduleMaterialTable">
        ALTER TABLE `dfs_config_schedule_reverse_material`
            ADD COLUMN `${fieldName}` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT #{columnName};
    </insert>

    <select id="getScheduleFieldData" resultType="java.lang.String">
        SELECT `${fieldName}`
        FROM `dfs_config_schedule_reverse_material`
        WHERE `material_code` = #{bomCode}
    </select>

    <update id="updateScheduleFieldData">
        UPDATE `dfs_config_schedule_reverse_material`
        SET `${fieldName}` = #{materialCode}
        WHERE `material_code` = #{bomCode}
    </update>

    <select id="getScheduleReverseExtendFieldData" resultType="com.yelink.dfscommon.dto.CommonTableDTO">
        SELECT COLUMN_NAME, COLUMN_COMMENT
        FROM information_schema.COLUMNS
        WHERE TABLE_NAME = 'dfs_config_schedule_reverse_material'
        AND TABLE_SCHEMA = #{tableSchema}
    </select>

    <select id="getRelatedDataTableByWorkOrder" resultType="com.yelink.dfscommon.dto.CommonTableDTO">
        SELECT c.TABLE_SCHEMA, c.TABLE_NAME, c.COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS c
                 JOIN INFORMATION_SCHEMA.TABLES t
                      ON c.TABLE_SCHEMA = t.TABLE_SCHEMA
                          AND c.TABLE_NAME = t.TABLE_NAME
        WHERE
            c.COLUMN_NAME IN ('work_order_id', 'work_order_number', 'work_order', 'work_order_num')
          AND c.TABLE_SCHEMA = #{tableSchema}
          AND t.TABLE_TYPE = 'BASE TABLE'
        GROUP BY c.TABLE_SCHEMA, c.TABLE_NAME
    </select>

    <delete id="deleteRelatedDataByWorkOrder">
        DELETE FROM ${tableName} WHERE `${columnName}` = '${value}';
    </delete>

    <select id="getScheduleReverseMaterialConf" resultType="java.util.Map">
        SELECT * FROM `dfs_config_schedule_reverse_material`
        WHERE `material_code` IN
        <foreach item="materialCode" collection="materialCodes" separator="," open="(" close=")" index="">
            #{materialCode}
        </foreach>
    </select>

    <select id="getScheduleReverseMaterialCode" resultType="string">
        SELECT `material_code`
        FROM `dfs_config_schedule_reverse_material`
        WHERE
        <foreach item="extendMaterial" collection="extendMaterialList" separator="and" index="">
            <foreach item="item" collection="extendMaterial.value.split(',')" separator="or" index="">
                <choose>
                    <when test="item.equals('null')">
                        `${extendMaterial.fieldName}` IS NULL
                    </when>
                    <otherwise>
                        `${extendMaterial.fieldName}` LIKE "%${item}%"
                    </otherwise>
                </choose>
            </foreach>
        </foreach>
    </select>

    <select id="workOrderByPage" resultType="com.yelink.dfs.entity.screen.vo.WorkOrderEntityVO">
        select * from (select a.work_order_id,a.work_order_number,a.work_order_name,a.state,a.end_date,a.material_code,m.`name` AS material_name,a.plan_quantity,a.finish_count,a.in_stock_count,a.actual_working_hours,a.progress,a.circulation_duration,a.planned_working_hours
                            ,b.procedure_names procedure_name,c.device_code device_code,c.device_id device_id,d.order_id order_id from
                           (select * from dfs_work_order where state = 3  UNION ALL
                            select * from dfs_work_order where state = 4 UNION ALL
                            select * from dfs_work_order where state = 2 UNION ALL
                            select * from dfs_work_order where state = 5 and to_days(actual_end_date) = to_days(#{nowTime})) a
                               left JOIN
                           ( select work_order_number,GROUP_CONCAT(procedure_name) procedure_names from dfs_work_order_procedure_relation GROUP BY work_order_number ) b on b.work_order_number = a.work_order_number

                               LEFT JOIN dfs_work_order_product_line_relation f on a.work_order_id = f.work_order_id

                               LEFT JOIN dfs_device c on c.production_line_id = f.product_line_id
                               left JOIN dfs_order_work_order_relation d on a.work_order_id = d.work_order_id and d.order_type = 'productOrder'

                               LEFT JOIN dfs_material m on m.`code`= a.material_code
                       where product_line_id in (select production_line_id from dfs_production_line where gid = #{gridId}) ) a ORDER BY device_id
    </select>
    <select id="producedTotal" resultType="java.lang.Integer">
        select count(DISTINCT(a.work_order_number)) from
            (
                select * from dfs_work_order where state = 4  UNION ALL
                select * from dfs_work_order where state = 2 ) a
                left JOIN
            ( select work_order_number,GROUP_CONCAT(procedure_name) procedure_names from dfs_work_order_procedure_relation GROUP BY work_order_number ) b on b.work_order_number = a.work_order_number

                LEFT JOIN dfs_work_order_product_line_relation f on a.work_order_id = f.work_order_id
                LEFT JOIN dfs_device c on c.production_line_id = f.product_line_id
                left JOIN dfs_order_work_order_relation d on a.work_order_id = d.work_order_id and d.order_type = 'productOrder'
        where product_line_id in (select production_line_id from dfs_production_line where gid = #{gridId})
    </select>
    <select id="producingTotal" resultType="java.lang.Integer">
        select count(DISTINCT(a.work_order_number)) from
            (select * from dfs_work_order where state = 3  ) a
                left JOIN
            ( select work_order_number,GROUP_CONCAT(procedure_name) procedure_names from dfs_work_order_procedure_relation GROUP BY work_order_number ) b on b.work_order_number = a.work_order_number

                LEFT JOIN dfs_work_order_product_line_relation f on a.work_order_id = f.work_order_id
                LEFT JOIN dfs_device c on c.production_line_id = f.product_line_id
                left JOIN dfs_order_work_order_relation d on a.work_order_id = d.work_order_id and d.order_type = 'productOrder'
        where product_line_id in (select production_line_id from dfs_production_line where gid =  #{gridId})
    </select>
    <select id="completedTotal" resultType="java.lang.Integer">
        select count(DISTINCT(a.work_order_number)) from
            (
                select * from dfs_work_order where state = 5 and to_days(actual_end_date) = to_days(#{nowTime})) a
                left JOIN
            ( select work_order_number,GROUP_CONCAT(procedure_name) procedure_names from dfs_work_order_procedure_relation GROUP BY work_order_number ) b on b.work_order_number = a.work_order_number

                LEFT JOIN dfs_work_order_product_line_relation f on a.work_order_id = f.work_order_id
                LEFT JOIN dfs_device c on c.production_line_id = f.product_line_id
                left JOIN dfs_order_work_order_relation d on a.work_order_id = d.work_order_id and d.order_type = 'productOrder'
        where product_line_id in (select production_line_id from dfs_production_line where gid = #{gridId})
    </select>

    <select id="selectListWorkOrderByPage" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT * FROM dfs_work_order
        WHERE state IN (2,3,4,5)
        <if test="lineId != null">
            AND (work_order_number in (select work_order_number from dfs_work_order_basic_unit_relation WHERE production_basic_unit_id = #{lineId} and work_center_type = 'line')
            <if test="workCenterId != null and workCenterId != ''">
                OR (work_center_id=#{workCenterId} AND work_order_number not in (SELECT work_order_number FROM dfs_work_order_basic_unit_relation WHERE work_center_id = #{workCenterId}))
            </if>
            )
          </if>
        <if test="teamId != null">
            AND (work_order_number in (select work_order_number from dfs_work_order_basic_unit_relation WHERE production_basic_unit_id = #{teamId} and work_center_type = 'team')
            <if test="workCenterId != null and workCenterId != ''">
                OR (work_center_id=#{workCenterId} AND work_order_number not in (SELECT work_order_number FROM dfs_work_order_basic_unit_relation WHERE work_center_id = #{workCenterId}))
            </if>
            )
        </if>
        <if test="deviceId != null">
            AND (work_order_number in (select work_order_number from dfs_work_order_basic_unit_relation WHERE production_basic_unit_id = #{deviceId} and work_center_type = 'device')
            <if test="workCenterId != null and workCenterId != ''">
                OR (work_center_id=#{workCenterId} AND work_order_number not in (SELECT work_order_number FROM dfs_work_order_basic_unit_relation WHERE work_center_id = #{workCenterId}))
            </if>
            )
        </if>
        <if test="workCenterId != null and workCenterId != ''">
            and work_center_id=#{workCenterId}
        </if>
        <if test="state != null and state != ''">
            and state=#{state}
        </if>
        <if test="materialCode != null and materialCode != ''">
            and material_code LIKE CONCAT('%',#{materialCode},'%')
        </if>
        <if test="assignmentState != null and assignmentState != ''">
            and assignment_state = #{assignmentState}
        </if>
        <if test="materialName != null and materialName != ''">
            and material_code IN (SELECT code FROM dfs_material WHERE name like CONCAT('%',#{materialName},'%'))
        </if>
        <if test="workOrderNumber != null and workOrderNumber != ''">
            and work_order_number LIKE CONCAT('%',#{workOrderNumber},'%')
        </if>
        <if test="workOrderName != null and workOrderName != ''">
            and work_order_name LIKE CONCAT('%',#{workOrderName},'%')
        </if>
        <if test="isSubcontract != null and isSubcontract">
            and work_order_number in (SELECT DISTINCT work_order_number from dfs_work_order_subcontract)
        </if>
        <if test="workOrderNumbers != null and workOrderNumbers.size > 0">
            and work_order_number IN
            <foreach item="item" index="index" collection="workOrderNumbers" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY create_date desc
    </select>
    <select id="selectPageByProcedureId" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT * from (
        SELECT *, case
        when state = 3 THEN 1
        when state = 4 THEN 2
        when state = 2 THEN 3
<!--        when state = 5 and actual_end_date BETWEEN date_sub(now(), INTERVAL 0 DAY) AND DATE_SUB(now(),INTERVAL -1 DAY) THEN 4-->
        ELSE 5
        END as sort
        FROM dfs_work_order
        WHERE state IN (2,3,4)
        and product_order_number is not null
        <if test="procedureId != null ">
            and work_order_id IN (SELECT work_order_id FROM dfs_work_order_procedure_relation WHERE procedure_id =
            #{procedureId})
        </if>
        <if test="(isolationIds != null and isolationIds.size > 0) or (workCenterIds != null and workCenterIds.size > 0)">
            AND work_order_number IN (
                SELECT work_order_number
                FROM dfs_work_order_basic_unit_relation
                <where>
                    <trim prefixOverrides="OR">
                        <if test="isolationIds != null and isolationIds.size > 0">
                            OR isolation_id IN
                            <foreach item="isolationId" index="index" collection="isolationIds" open="(" separator="," close=")">
                                #{isolationId}
                            </foreach>
                        </if>
                        <if test="workCenterIds != null and workCenterIds.size > 0">
                            OR isolation_id IN
                            <foreach item="workCenterId" index="index" collection="workCenterIds" open="(" separator="," close=")">
                                #{workCenterId}
                            </foreach>
                        </if>
                    </trim>
                </where>
            )
        </if>
        )a
        where a.sort &lt; 5
        ORDER BY a.sort asc,
        case a.sort when 1 then a.state_change_time end desc,
        case a.sort when 2 then a.state_change_time end desc,
        case a.sort when 3 then a.start_date end asc
<!--        ,case a.sort when 4 then a.actual_end_date end desc-->
    </select>


    <select id="getTheRestOfProductionTime" resultType="java.lang.Double">
        SELECT
        SUM(( wo.plan_quantity - wo.finish_count )* prwh.processing_hours * prwh.degree_of_difficulty ) AS result
        FROM
        -- 工单表
        dfs_work_order wo
        -- 	工单工序关联表
        LEFT JOIN dfs_work_order_procedure_relation wopr ON wopr.work_order_number = wo.work_order_number
        -- 	工艺工序的加工时长
        LEFT JOIN dfs_procedure_relation_work_hours prwh ON prwh.procedure_id = wopr.craft_procedure_id
        <where>
            AND prwh.processing_hours IS NOT NULL
            <if test="state != null and state != ''">
                AND wo.state = #{state}
            </if>
            <if test="lineId != null and lineId != ''">
                AND wo.line_id = #{lineId}
            </if>
            <if test="excludeOrders != null and excludeOrders.size > 0">
                AND wopr.work_order_number NOT IN
                <foreach item="item" index="index" collection="excludeOrders" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getProcessingHoursOfWorkOrder" resultType="java.lang.Double">
        SELECT
        SUM(( wo.plan_quantity - wo.finish_count )* prwh.processing_hours * prwh.degree_of_difficulty ) AS result
        FROM
        -- 工单表
        dfs_work_order wo
        -- 	工单工序关联表
        LEFT JOIN dfs_work_order_procedure_relation wopr ON wopr.work_order_number = wo.work_order_number
        -- 	工艺工序的加工时长
        LEFT JOIN dfs_procedure_relation_work_hours prwh ON prwh.procedure_id = wopr.craft_procedure_id
        <where>
            AND prwh.processing_hours IS NOT NULL
            <if test="workOrderNumbers != null and workOrderNumbers.size > 0 ">
                AND wopr.work_order_number IN
                <foreach item="item" index="index" collection="workOrderNumbers" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="workOrderDayPlan" resultType="com.yelink.dfs.entity.statement.vo.WorkOrderDayPlanVO">
        select * from (
            SELECT
                plan.work_order_number,
                plan.production_basic_unit_type,
                plan.production_basic_unit_id,
                plan.production_basic_unit_name,
                plan.is_main,
                MIN( plan.time ) AS planStartTime,
                MAX( plan.time ) AS planEndTime,
                workOrder.state AS state,
                workOrder.work_center_id AS workCenterId,
                workOrder.work_center_name AS workCenterName,
                workOrder.material_code AS materialCode,
                workOrder.plan_quantity AS planQuantity,
                workOrder.finish_count AS finishQuantity,
                workOrder.end_date AS requireTime,
                material.`name` AS materialName
            FROM
                dfs_work_order_plan plan
                INNER JOIN dfs_work_order workOrder on plan.work_order_number = workOrder.work_order_number
                INNER JOIN dfs_material material ON workOrder.material_code = material.`code`
            <where>
                plan.TIME BETWEEN #{selectDTO.timeRange.startTime} AND #{selectDTO.timeRange.endTime}
                <if test="selectDTO.workCenterIds != null and selectDTO.workCenterIds.size > 0">
                    AND workOrder.work_center_id IN
                    <foreach item="item" collection="selectDTO.workCenterIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="(selectDTO.lineIds != null and selectDTO.lineIds.size > 0) or (selectDTO.teamIds != null and selectDTO.teamIds.size > 0) or (selectDTO.deviceIds != null and selectDTO.deviceIds.size > 0)">
                    AND (
                    <trim prefixOverrides="OR">
                        <if test="selectDTO.lineIds != null and selectDTO.lineIds.size > 0">
                            OR(
                                plan.production_basic_unit_id IN
                                <foreach item="item" collection="selectDTO.lineIds" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                                AND plan.production_basic_unit_type = 'line'
                            )
                        </if>
                        <if test="selectDTO.teamIds != null and selectDTO.teamIds.size > 0">
                            OR(
                            plan.production_basic_unit_id IN
                            <foreach item="item" collection="selectDTO.teamIds" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            AND plan.production_basic_unit_type = 'team'
                            )
                        </if>
                        <if test="selectDTO.deviceIds != null and selectDTO.deviceIds.size > 0">
                            OR(
                            plan.production_basic_unit_id IN
                            <foreach item="item" collection="selectDTO.deviceIds" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            AND plan.production_basic_unit_type = 'device'
                            )
                        </if>

                    </trim>
                    )

                </if>
            </where>
            GROUP BY plan.work_order_number, plan.production_basic_unit_type, plan.production_basic_unit_id, plan.is_main
        )t
        ORDER BY t.planStartTime ASC

    </select>

    <select id="pageRelateDeviceWorkOrder" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT workOrder.work_order_id as workOrderId,
               workOrder.work_order_number as workOrderNumber,
               workOrder.work_order_name as workOrderName,
               workOrder.start_date as startDate,
               workOrder.end_date as endDate,
               workOrder.actual_start_date as actualStartDate,
               workOrder.actual_end_date as actualEndDate,
               workOrder.state as state,
               workOrder.assignment_state as assignmentState,
               workOrder.line_code as lineCode,
               workOrder.line_name as lineName,
               workOrder.line_id as lineId,
               workOrder.plan_quantity as planQuantity,
               workOrder.material_code as materialCode,
               workOrder.unqualified as unqualified,
               workOrder.finish_count as finishCount,
               workOrder.input_total as inputTotal,
               workOrder.product_order_number as productOrderNumber,
               workOrder.sale_order_number as saleOrderNumber,
               workOrder.craft_id as craftId,
               workOrder.craft_code as craftCode,
               workOrder.work_center_id as workCenterId,
               workOrder.work_center_name as workCenterName,
               workOrder.team_id as teamId,
               workOrder.state_change_time as stateChangeTime,
               IFNULL(workOrder.device_id,relateDevice.device_id) as deviceId,
               workOrder.production_basic_unit_id as productionBasicUnitId
        FROM dfs_work_order workOrder
            LEFT JOIN dfs_work_order_device_relevance relateDevice ON workOrder.work_order_id = relateDevice.work_order_id
        WHERE (
            workOrder.device_id in
                <foreach item="item" collection="deviceIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            OR relateDevice.device_id IN
                <foreach item="item" collection="deviceIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
            <if test="states != null and states.size > 0">
                AND workOrder.state IN
                <foreach item="item" collection="states" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        ORDER BY workOrder.create_date DESC
    </select>

    <select id="listMetrics" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        SELECT
            a.work_order_number,
            a.material_code,
            b. NAME material_name,
            a.work_center_id,
            a.work_center_name,
            a.production_basic_unit_id,
            CASE
        WHEN a.line_id IS NOT NULL THEN
            a.line_name
        WHEN a.team_id IS NOT NULL THEN
            (
                SELECT
                    team_name
                FROM
                    sys_team
                WHERE
                    team_id = a.team_id
            )
        WHEN a.device_id IS NOT NULL THEN
            (
                SELECT
                    device_name
                FROM
                    dfs_device
                WHERE
                    device_id = a.device_id
            )
        END production_basic_unit_name
        FROM
            dfs_work_order a
        LEFT JOIN dfs_material b ON a.material_code = b. CODE
    </select>

    <select id="selectByIdNoCache" resultType="com.yelink.dfs.entity.order.WorkOrderEntity" flushCache="true">
        SELECT * FROM dfs_work_order WHERE work_order_id = #{workOrderId}
    </select>

    <select id="getList" resultType="com.yelink.dfs.entity.order.WorkOrderEntity">
        select * from dfs_work_order where work_order_id in
        (
        SELECT
        workOrder.work_order_id
        FROM
        dfs_work_order workOrder
        <if test="sql != null and sql != ''">
            <if test="sql.contains('workOrderBasicUnitRelation')">
                LEFT JOIN dfs_work_order_basic_unit_relation workOrderBasicUnitRelation ON workOrder.work_order_number = workOrderBasicUnitRelation.work_order_number
            </if>
            <if test="sql.contains('workOrderProcedureRelation')">
                LEFT JOIN dfs_work_order_procedure_relation workOrderProcedureRelation ON workOrder.work_order_number = workOrderProcedureRelation.work_order_number
            </if>
            <if test="sql.contains('material')">
                LEFT JOIN dfs_material material ON workOrder.material_code = material.code
            </if>
            ${sql}
        </if>
        )
        <!--        select workOrder.* from dfs_work_order workOrder-->
        <!--        <where>-->
        <!--            <if test="sqlDTO != null and sqlDTO.workOrderSql != null and sqlDTO.workOrderSql != ''">-->
        <!--                AND ${sqlDTO.workOrderSql}-->
        <!--            </if>-->
        <!--            <if test="sqlDTO != null and sqlDTO.workOrderBasicUnitRelationSql != null and sqlDTO.workOrderBasicUnitRelationSql != ''">-->
        <!--                AND workOrder.work_order_number IN (select workOrderBasicUnitRelation.work_order_number-->
        <!--                FROM dfs_work_order_basic_unit_relation workOrderBasicUnitRelation-->
        <!--                <where>-->
        <!--                    ${sqlDTO.workOrderBasicUnitRelationSql})-->
        <!--                </where>-->
        <!--            </if>-->
        <!--            <if test="sqlDTO != null and sqlDTO.workOrderProcedureRelationSql != null and sqlDTO.workOrderProcedureRelationSql != ''">-->
        <!--                AND workOrder.work_order_number IN (select workOrderProcedureRelation.work_order_number-->
        <!--                FROM dfs_work_order_procedure_relation workOrderProcedureRelation-->
        <!--                <where>-->
        <!--                    ${sqlDTO.workOrderProcedureRelationSql})-->
        <!--                </where>-->
        <!--            </if>-->
        <!--            <if test="sqlDTO != null and sqlDTO.materialSql != null and sqlDTO.materialSql != ''">-->
        <!--                AND workOrder.material_code IN (select material.code-->
        <!--                FROM dfs_material material-->
        <!--                <where>-->
        <!--                    ${sqlDTO.materialSql})-->
        <!--                </where>-->
        <!--            </if>-->
        <!--        </where>-->
        <!--        <if test="sqlDTO != null and sqlDTO.orderBySql != null and sqlDTO.orderBySql != ''">-->
        <!--            ORDER BY-->
        <!--            ${sqlDTO.orderBySql}-->
        <!--        </if>-->
    </select>

</mapper>
