<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.iot.WorkOrderBarcodeMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.iot.WorkOrderBarcodeEntity">
		    <result column="id" property="id" />
		    <result column="barcode_set" property="barcodeSet" />
		    <result column="work_order_num" property="workOrderNum" />
		    <result column="barcode" property="barcode" />
		    <result column="input_fid" property="inputFid" />
		    <result column="input_time" property="inputTime" />
		    <result column="produce_fid" property="produceFid" />
		    <result column="produce_time" property="produceTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.barcodeSet != null and e.barcodeSet != '' ">
					AND t.barcode_set = #{e.barcodeSet}
				</if>
				<if test="e.workOrderNum != null and e.workOrderNum != '' ">
					AND t.work_order_num = #{e.workOrderNum}
				</if>
				<if test="e.barcode != null and e.barcode != '' ">
					AND t.barcode = #{e.barcode}
				</if>
				<if test="e.inputFid != null and e.inputFid != '' ">
					AND t.input_fid = #{e.inputFid}
				</if>
				<if test="e.inputTime != null and e.inputTime != '' ">
					AND t.input_time = #{e.inputTime}
				</if>
				<if test="e.produceFid != null and e.produceFid != '' ">
					AND t.produce_fid = #{e.produceFid}
				</if>
				<if test="e.produceTime != null and e.produceTime != '' ">
					AND t.produce_time = #{e.produceTime}
				</if>
	</sql>

</mapper>