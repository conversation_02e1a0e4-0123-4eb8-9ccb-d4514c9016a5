<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.iot.IotJobMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.iot.IotJobEntity">
        <result column="id" property="id"/>
        <result column="model_code" property="modelCode"/>
        <result column="job_code" property="jobCode"/>
        <result column="job_id" property="jobId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.modelCode != null and e.modelCode != '' ">
            AND t.model_code = #{e.modelCode}
        </if>
        <if test="e.jobCode != null and e.jobCode != '' ">
            AND t.job_code = #{e.jobCode}
        </if>
        <if test="e.jobId != null and e.jobId != '' ">
            AND t.job_id = #{e.jobId}
        </if>
    </sql>

</mapper>