<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sys.TableMapper">

    <select id="getAllDfsTable" resultType="com.yelink.dfscommon.dto.CommonTableDTO">
        select `table_name`
        from dfs_config_cleanable_table
    </select>

    <select id="getDfsTableList" resultType="com.yelink.dfscommon.dto.CommonTableDTO">
        select a.`table_schema` ,a.`table_name` ,a.`table_comment`,
        truncate(a.`data_length`/1024/1024, 2) as `data_length`,
        truncate(a.`index_length`/1024/1024, 2) as `index_length`,
        a.`table_rows` tableRows
        from information_schema.tables a
        right join dfs_config_cleanable_table b
        on a.`table_name` = b.`table_name`
        where table_schema= #{tableSchema}
        <if test="tableName!=null and tableName!=''">
            and a.`table_name` like concat('%',#{tableName},'%')
        </if>
        <if test="tableComment!=null and tableComment!=''">
            and a.`table_comment` like concat('%',#{tableComment},'%')
        </if>
        order by a.`data_length` desc, a.`index_length` desc
    </select>

    <delete id="cleanDfsData">
        delete
        from ${tableName}
        where create_time &lt; #{time}
    </delete>

    <delete id="cleanDfsRecordOrQualityData">
        delete
        from ${tableName}
        where `time` &lt; #{time}
    </delete>

    <delete id="cleanDfsAlarmRecordData">
        delete
        from ${tableName}
        where `alarm_time` &lt; #{time}
    </delete>

    <delete id="cleanDfsNoticeRecordData">
        delete
        from ${tableName}
        where `send_time` &lt; #{time}
    </delete>

    <delete id="cleanDfsDataSpace">
        optimize TABLE ${tableName}
    </delete>

    <select id="getCleanableTableList" resultType="com.yelink.dfs.entity.sys.CleanableTableVo">
        select `id`, `table_name`
        from dfs_config_cleanable_table
    </select>

    <select id="getEarliestCreatTime" resultType="java.lang.String">
        select create_time
        from ${tableName}
        limit 1
    </select>

    <select id="getEarliestAlarmTime" resultType="java.lang.String">
        select alarm_time
        from ${tableName}
        limit 1
    </select>

    <select id="getEarliestSendTime" resultType="java.lang.String">
        select send_time
        from ${tableName}
        limit 1
    </select>

    <select id="getEarliestTime" resultType="java.lang.String">
        select `time`
        from ${tableName}
        limit 1
    </select>

    <select id="getRows" resultType="java.lang.Integer">
        select count(1)
        from ${tableName}
    </select>

    <select id="getOnlyBackupStructureTable" resultType="java.lang.String">
        select `table_name` from information_schema.tables a where a.table_schema= #{tableSchema}
        and (a.table_name like 'dfs_record%'
        <if test="backupTableList != null and backupTableList.size()>0">
            or a.table_name in
            <foreach item="item" index="index" collection="backupTableList" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        );
    </select>

    <select id="getBackupDataStructureTable" resultType="java.lang.String">
        select `table_name` from information_schema.tables a where a.table_schema= #{tableSchema}
        <if test="backupTableList != null and backupTableList.size()>0">
            AND a.table_name not in
            <foreach item="item" index="index" collection="backupTableList" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and a.table_name not like 'dfs_record%';
    </select>

    <select id="checkTableIsExist" resultType="java.lang.Boolean">
        SELECT EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = #{tableSchema} AND TABLE_NAME = #{tableName}
        );
    </select>

    <insert id="createTable">
        CREATE TABLE `${dto.tableSchema}`.`${dto.tableName}` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            <foreach item="field" collection="dto.fields">
                ${field.fieldSql},
            </foreach>
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=#{dto.tableRemark};
    </insert>

    <insert id="addColumn">
        ALTER TABLE `${tableSchema}`.`${tableName}` ADD COLUMN ${fieldSql};
    </insert>

    <update id="updateColumn">
        ALTER TABLE `${tableSchema}`.`${tableName}` CHANGE `${oldFieldCode}` ${fieldSql};
    </update>

    <delete id="dropColumn">
        ALTER TABLE `${tableSchema}`.`${tableName}` DROP COLUMN ${deleteFieldCode};
    </delete>

    <delete id="dropTable">
        DROP TABLE IF EXISTS `${tableSchema}`.`${tableName}`;
    </delete>

    <select id="executeSql" resultType="java.util.LinkedHashMap">
        ${sql}
    </select>

    <select id="selectTable" resultType="java.util.Map">
        select * from `${tableSchema}`.`${tableName}`
        <where>
            <foreach item="fieldSelect" collection="fieldSelects">
                <if test="fieldSelect.fieldLikeValue != null and fieldSelect.fieldLikeValue != '' ">
                    AND ${fieldSelect.fieldCode} LIKE CONCAT('%', #{fieldSelect.fieldLikeValue}, '%')
                </if>
                <if test="fieldSelect.fieldEqualValue != null and fieldSelect.fieldEqualValue != '' ">
                    AND ${fieldSelect.fieldCode} = #{fieldSelect.fieldEqualValue}
                </if>
                <if test="fieldSelect.fieldStartTime != null and fieldSelect.fieldEndTime != null ">
                    AND ${fieldSelect.fieldCode} between ${fieldSelect.fieldStartTime} and ${fieldSelect.fieldEndTime}
                </if>
            </foreach>
        </where>
    </select>

    <insert id="createTableView">
        CREATE OR REPLACE VIEW  ${tableSchema}.${tableName} AS ${script};
    </insert>

    <delete id="deleteTableView">
        DROP VIEW IF EXISTS `${tableSchema}`.`${tableName}`;
    </delete>


    <select id="listTableColumn" resultType="com.yelink.dfs.entity.sys.vo.TableColumnVO">
        SELECT * FROM `information_schema`.`COLUMNS`
        WHERE `table_name` IN
            <foreach item="item" collection="tableNames" open="(" close=")" separator=",">
                #{item}
            </foreach>
        AND `column_name` IN
            <foreach item="item" collection="tableColumns" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>
</mapper>

