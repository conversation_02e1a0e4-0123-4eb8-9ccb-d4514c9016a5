<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sys.RunSqlMapper">
    <update id="runSqlBySqlStr" parameterType="String">
        -- <![CDATA[${sqlStr}]]>
        ${sqlStr}
    </update>

    <update id="runSqlBySqlList" parameterType="java.util.List">
        SET FOREIGN_KEY_CHECKS = 0;
        <foreach item="item" collection="sqlList">
            ${item}
        </foreach>
        SET FOREIGN_KEY_CHECKS = 1;
    </update>
    <select id="truncateAllTable" resultType="java.lang.String">
        SELECT `TABLE_NAME` FROM INFORMATION_SCHEMA.TABLES WHERE table_schema = #{database};
    </select>

</mapper>

