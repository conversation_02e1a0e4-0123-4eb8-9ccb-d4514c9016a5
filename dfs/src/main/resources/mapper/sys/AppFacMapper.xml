<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sys.AppFacMapper">
    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.sys.AppFacEntity">
        <result column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="fac_ids" property="facIds" />
        <result column="fac_model_ids" property="facModelIds" />
        <result column="line_model_ids" property="lineModelIds" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.appId != null and e.appId != '' ">
            AND t.app_id = #{e.appId}
        </if>
        <if test="e.facIds != null and e.facIds != '' ">
            AND t.fac_ids = #{e.facIds}
        </if>
        <if test="e.facModelIds != null and e.facModelIds != '' ">
            AND t.fac_model_ids = #{e.facModelIds}
        </if>
        <if test="e.lineModelIds != null and e.lineModelIds != '' ">
            AND t.line_model_ids = #{e.lineModelIds}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>
</mapper>