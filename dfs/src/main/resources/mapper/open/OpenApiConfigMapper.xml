<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.open.OpenApiConfigMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.common.ExtApiConfig">
		    <result column="service" property="service" />
		    <result column="model" property="model" />
		    <result column="interface_code" property="interfaceCode" />
		    <result column="interface_name" property="interfaceName" />
		    <result column="http_method" property="httpMethod" />
		    <result column="path" property="path" />
	</resultMap>


</mapper>