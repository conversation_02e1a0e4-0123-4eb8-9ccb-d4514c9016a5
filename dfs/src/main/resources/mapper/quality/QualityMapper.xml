<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.QualityMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.quality.QualityEntity">
        <result column="quality_id" property="qualityId"/>
        <result column="area_id" property="areaId"/>
        <result column="grid_id" property="gridId"/>
        <result column="line_id" property="lineId"/>
        <result column="fid" property="fid"/>
        <result column="work_order" property="workOrder"/>
        <result column="unqualified" property="unqualified"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.qualityId != null and e.qualityId != '' ">
            AND t.quality_id = #{e.qualityId}
        </if>
        <if test="e.areaId != null and e.areaId != '' ">
            AND t.area_id = #{e.areaId}
        </if>
        <if test="e.gridId != null and e.gridId != '' ">
            AND t.grid_id = #{e.gridId}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.unqualified != null and e.unqualified != '' ">
            AND t.unqualified = #{e.unqualified}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>