<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.furnace.LaboratoryReportMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.furnace.LaboratoryReportEntity">
        <result column="id" property="id"/>
        <result column="furnace_code" property="furnaceCode"/>
        <result column="fid" property="fid"/>
        <result column="sequence" property="sequence"/>
        <result column="affirm_number" property="affirmNumber"/>
        <result column="state" property="state"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.furnaceCode != null and e.furnaceCode != '' ">
            AND t.furnace_code = #{e.furnaceCode}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.sequence != null and e.sequence != '' ">
            AND t.sequence = #{e.sequence}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.affirmNumber != null and e.affirmNumber != '' ">
            AND t.affirm_number = #{e.affirmNumber}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>

</mapper>