<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.furnace.SecondaryMaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.furnace.SecondaryMaterialEntity">
        <result column="id" property="id"/>
        <result column="furnace_num" property="furnaceCode"/>
        <result column="test_id" property="testId"/>
        <result column="fid" property="fid"/>
        <result column="seq" property="seq"/>
        <result column="material_code" property="materialCode"/>
        <result column="reference_value" property="referenceValue"/>
        <result column="deviation_value" property="deviationValue"/>
        <result column="correction_value" property="correctionValue"/>
        <result column="is_confirm" property="isConfirm"/>
        <result column="is_add" property="isAdd"/>
        <result column="weigh" property="weigh"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="tapping_capacity_val" property="tappingCapacityVal"/>
        <result column="proposal_val" property="proposalVal"/>
        <result column="refining_furnace_val" property="refiningFurnaceVal"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.furnaceCode != null and e.furnaceCode != '' ">
            AND t.furnace_code = #{e.furnaceCode}
        </if>
        <if test="e.testId != null and e.testId != '' ">
            AND t.test_id = #{e.testId}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.seq != null and e.seq != '' ">
            AND t.seq = #{e.seq}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.referenceValue != null and e.referenceValue != '' ">
            AND t.reference_value = #{e.referenceValue}
        </if>
        <if test="e.deviationValue != null and e.deviationValue != '' ">
            AND t.deviation_value = #{e.deviationValue}
        </if>
        <if test="e.correctionValue != null and e.correctionValue != '' ">
            AND t.correction_value = #{e.correctionValue}
        </if>
        <if test="e.weigh != null and e.weigh != '' ">
            AND t.weigh = #{e.weigh}
        </if>
        <if test="e.isConfirm != null and e.isConfirm != '' ">
            AND t.is_confirm = #{e.isConfirm}
        </if>
        <if test="e.isAdd != null and e.isAdd != '' ">
            AND t.is_add = #{e.isAdd}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.tappingCapacityVal != null and e.tappingCapacityVal != '' ">
            AND t.tapping_capacity_val = #{e.tappingCapacityVal}
        </if>
        <if test="e.proposalVal != null and e.proposalVal != '' ">
            AND t.proposal_val = #{e.proposalVal}
        </if>
        <if test="e.refiningFurnaceVal != null and e.refiningFurnaceVal != '' ">
            AND t.refining_furnace_val = #{e.refiningFurnaceVal}
        </if>
    </sql>

</mapper>
