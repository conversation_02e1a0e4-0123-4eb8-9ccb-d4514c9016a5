<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.furnace.FurnaceMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.furnace.FurnaceEntity">
        <result column="id" property="id"/>
        <result column="furnace_code" property="furnaceCode"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="fid" property="fid"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="shift_type" property="shiftType"/>
        <result column="furnace_user_id" property="furnaceUserId"/>
        <result column="dispatcher" property="dispatcher"/>
        <result column="state" property="state"/>
        <result column="shift_furnace_seq" property="shiftFurnaceSeq"/>
        <result column="day_furnace_seq" property="dayFurnaceSeq"/>
        <result column="shift_count" property="shiftCount"/>
        <result column="day_count" property="dayCount"/>
        <result column="cumulative_time" property="cumulativeTime"/>
        <result column="is_confirm" property="isConfirm"/>
        <result column="load_quantity" property="loadQuantity"/>
        <result column="steel_quantity" property="steelQuantity"/>
        <result column="electric_quantity" property="electricQuantity"/>
        <result column="oxygen_quantity" property="oxygenQuantity"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.furnaceCode != null and e.furnaceCode != '' ">
            AND t.furnace_code = #{e.furnaceCode}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.deviceCode != null and e.deviceCode != '' ">
            AND t.device_code = #{e.deviceCode}
        </if>
        <if test="e.startTime != null and e.startTime != '' ">
            AND t.start_time = #{e.startTime}
        </if>
        <if test="e.endTime != null and e.endTime != '' ">
            AND t.end_time = #{e.endTime}
        </if>
        <if test="e.shiftType != null and e.shiftType != '' ">
            AND t.shift_type = #{e.shiftType}
        </if>
        <if test="e.furnaceUserId != null and e.furnaceUserId != '' ">
            AND t.furnace_user_id = #{e.furnaceUserId}
        </if>
        <if test="e.dispatcher != null and e.dispatcher != '' ">
            AND t.dispatcher = #{e.dispatcher}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.shiftFurnaceSeq != null and e.shiftFurnaceSeq != '' ">
            AND t.shift_furnace_seq = #{e.shiftFurnaceSeq}
        </if>
        <if test="e.dayFurnaceSeq != null and e.dayFurnaceSeq != '' ">
            AND t.day_furnace_seq = #{e.dayFurnaceSeq}
        </if>
        <if test="e.shiftCount != null and e.shiftCount != '' ">
            AND t.shift_count = #{e.shiftCount}
        </if>
        <if test="e.cumulativeTime != null and e.cumulativeTime != '' ">
            AND t.cumulative_time = #{e.cumulativeTime}
        </if>
        <if test="e.isConfirm != null and e.isConfirm != '' ">
            AND t.is_confirm = #{e.isConfirm}
        </if>
        <if test="e.loadQuantity != null and e.loadQuantity != '' ">
            AND t.load_quantity = #{e.loadQuantity}
        </if>
        <if test="e.steelQuantity != null and e.steelQuantity != '' ">
            AND t.steel_quantity = #{e.steelQuantity}
        </if>
        <if test="e.oxygenQuantity != null and e.oxygenQuantity != '' ">
            AND t.electric_quantity = #{e.oxygenQuantity}
        </if>
        <if test="e.steelQuantity != null and e.steelQuantity != '' ">
            AND t.oxygen_quantity = #{e.steelQuantity}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

    <select id="getNewestFurnaceNotIncludeToday" resultType="com.yelink.dfs.entity.furnace.FurnaceEntity">
        select * from dfs_furnace where create_time &lt; CURDATE() ORDER BY create_time desc limit 1;
    </select>

</mapper>
