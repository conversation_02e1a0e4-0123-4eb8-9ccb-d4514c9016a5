<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.furnace.ConfigOffsetMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.furnace.ConfigOffsetEntity">
        <result column="id" property="id"/>
        <result column="element_name" property="elementName"/>
        <result column="value" property="value"/>
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_date" property="updateDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.elementName != null and e.elementName != '' ">
            AND t.element_name = #{e.elementName}
        </if>
        <if test="e.value != null and e.value != '' ">
            AND t.vlaue = #{e.vlaue}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>

</mapper>