<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.furnace.ConfigDefValMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.furnace.ConfigDefValEntity">
        <result column="id" property="id" />
        <result column="fid" property="fid" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="def_val" property="defVal" />
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.defVal != null and e.defVal != '' ">
            AND t.def_val = #{e.defVal}
        </if>
    </sql>

</mapper>
