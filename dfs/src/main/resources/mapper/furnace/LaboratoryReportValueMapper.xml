<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.furnace.LaboratoryReportValueMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.furnace.LaboratoryReportValueEntity">
        <result column="id" property="id"/>
        <result column="number" property="number"/>
        <result column="element_name" property="elementName"/>
        <result column="value" property="value"/>
        <result column="revise_value" property="reviseValue"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.number != null and e.number != '' ">
            AND t.laboratory_report_id = #{e.laboratoryReportId}
        </if>
        <if test="e.elementName != null and e.elementName != '' ">
            AND t.element_name = #{e.elementName}
        </if>
        <if test="e.value != null and e.value != '' ">
            AND t.vlaue = #{e.vlaue}
        </if>
        <if test="e.reviseValue != null and e.reviseValue != '' ">
            AND t.vlaue = #{e.vlaue}
        </if>
    </sql>

</mapper>