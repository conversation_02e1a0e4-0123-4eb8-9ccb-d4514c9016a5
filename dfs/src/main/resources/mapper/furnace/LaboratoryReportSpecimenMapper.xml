<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.furnace.LaboratoryReportSpecimenMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.furnace.LaboratoryReportSpecimenEntity">
		    <result column="id" property="id" />
		    <result column="number" property="number" />
		    <result column="state" property="state" />
		    <result column="time" property="time" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.number != null and e.number != '' ">
					AND t.number = #{e.number}
				</if>
				<if test="e.state != null and e.state != '' ">
					AND t.state = #{e.state}
				</if>
				<if test="e.time != null and e.time != '' ">
					AND t.time = #{e.time}
				</if>
	</sql>

</mapper>