<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.notice.InfoNoticeConfigMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.notice.InfoNoticeConfigEntity">
		    <result column="id" property="id" />
		    <result column="notice_name" property="configName" />
		    <result column="notice_type" property="noticeType" />
		    <result column="notice_content" property="noticeContent" />
		    <result column="frequency" property="frequency" />
		    <result column="notice_method" property="noticeMethod" />
		    <result column="remark" property="remark" />
			<result column="create_by" property="createBy" />
			<result column="update_by" property="updateBy" />
			<result column="create_time" property="createTime" />
			<result column="update_time" property="updateTime" />
	</resultMap>




</mapper>