<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.notice.NoticeRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.notice.NoticeRecordEntity">
        <result column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="access_account" property="accessAccount"/>
        <result column="content" property="content"/>
        <result column="send_time" property="sendTime"/>
        <result column="result" property="result"/>
        <result column="type" property="type"/>
        <result column="success" property="success"/>
    </resultMap>

    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.username != null and e.username != '' ">
            AND t.username = #{e.username}
        </if>
        <if test="e.accessAccount != null and e.accessAccount != '' ">
            AND t.access_account = #{e.accessAccount}
        </if>
        <if test="e.content != null and e.content != '' ">
            AND t.content = #{e.content}
        </if>
        <if test="e.sendTime != null and e.sendTime != '' ">
            AND t.send_time = #{e.sendTime}
        </if>
        <if test="e.result != null and e.result != '' ">
            AND t.result = #{e.result}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.success != null and e.success != '' ">
            AND t.success = #{e.success}
        </if>
    </sql>

    <insert id="insertBatchRecord">
        INSERT INTO `dfs_notice_record`
        (`username`, `access_account`, `content`, `send_time`, `result`, `type`, `success`)
        VALUES
        <foreach collection="clientIds" item="item" index="index" separator=",">
            (#{entity.username},#{item},#{entity.content},NOW(),#{entity.result},#{entity.type},#{entity.success})
        </foreach>
    </insert>

</mapper>
