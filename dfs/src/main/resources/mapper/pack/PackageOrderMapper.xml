<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.pack.PackageOrderMapper">

    <select id="selectByIdNoCache" resultType="com.yelink.dfs.entity.pack.PackageOrderEntity" flushCache="true">
        SELECT * FROM dfs_package_order WHERE id = #{id}
    </select>

</mapper>
