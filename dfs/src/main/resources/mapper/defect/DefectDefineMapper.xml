<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.defect.DefectDefineMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.defect.DefectDefineEntity">
		    <result column="defect_id" property="defectId" />
		    <result column="defect_code" property="defectCode" />
		    <result column="defect_name" property="defectName" />
		    <result column="defect_type" property="defectType" />
		    <result column="description" property="description" />
		    <result column="remark" property="remark" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.defectId != null and e.defectId != '' ">
					AND t.defect_id = #{e.defectId}
				</if>
				<if test="e.defectCode != null and e.defectCode != '' ">
					AND t.defect_code = #{e.defectCode}
				</if>
				<if test="e.defectName != null and e.defectName != '' ">
					AND t.defect_name = #{e.defectName}
				</if>
				<if test="e.defectType != null and e.defectType != '' ">
					AND t.defect_type = #{e.defectType}
				</if>
				<if test="e.description != null and e.description != '' ">
					AND t.description = #{e.description}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>