<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.defect.DefectSchemeMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.defect.DefectSchemeEntity">
		    <result column="scheme_id" property="schemeId" />
<!--		    <result column="scheme_code" property="schemeCode" />-->
		    <result column="scheme_name" property="schemeName" />
		    <result column="scheme_type" property="schemeType" />
		    <result column="type" property="type" />
		    <result column="status" property="status" />
		    <result column="remark" property="remark" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
		    <result column="qualified_check" property="qualifiedCheck" />
	</resultMap>


	<sql id="select_content">
				<if test="e.schemeId != null and e.schemeId != '' ">
					AND t.scheme_id = #{e.schemeId}
				</if>
				<if test="e.schemeCode != null and e.schemeCode != '' ">
					AND t.scheme_code = #{e.schemeCode}
				</if>
				<if test="e.schemeName != null and e.schemeName != '' ">
					AND t.scheme_name = #{e.schemeName}
				</if>
				<if test="e.schemeType != null and e.schemeType != '' ">
					AND t.scheme_type = #{e.schemeType}
				</if>
				<if test="e.type != null and e.type != '' ">
					AND t.type = #{e.type}
				</if>
				<if test="e.status != null and e.status != '' ">
					AND t.status = #{e.status}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.qualifiedCheck != null and e.qualifiedCheck != '' ">
					AND t.qualified_check = #{e.qualifiedCheck}
				</if>
	</sql>

</mapper>