<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.defect.DefectSchemeDetailMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.defect.DefectSchemeDetailEntity">
		    <result column="detail_id" property="detailId" />
		    <result column="scheme_id" property="schemeId" />
		    <result column="defect_id" property="defectId" />
			<result column="remark" property="remark" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.detailId != null and e.detailId != '' ">
					AND t.detail_id = #{e.detailId}
				</if>
				<if test="e.schemeId != null and e.schemeId != '' ">
					AND t.scheme_id = #{e.schemeId}
				</if>
				<if test="e.defectId != null and e.defectId != '' ">
					AND t.defect_id = #{e.defectId}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
	</sql>

</mapper>