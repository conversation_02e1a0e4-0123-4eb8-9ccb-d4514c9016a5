<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.defect.DefectRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.defect.DefectRecordEntity">
        <result column="record_id" property="recordId"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="bar_code" property="barCode"/>
        <result column="scheme_id" property="schemeId"/>
        <result column="defect_id" property="defectId"/>
        <result column="defect_name" property="defectName"/>
        <result column="defect_type" property="defectType"/>
        <result column="work_order" property="workOrder"/>
        <result column="fid" property="fid"/>
        <result column="fname" property="fname"/>
        <result column="remark" property="remark"/>
        <result column="pic_url" property="picUrl"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.recordId != null and e.recordId != '' ">
            AND t.record_id = #{e.recordId}
        </if>
        <if test="e.serialNumber != null and e.serialNumber != '' ">
            AND t.serial_number = #{e.serialNumber}
        </if>
        <if test="e.barCode != null and e.barCode != '' ">
            AND t.bar_code = #{e.barCode}
        </if>
        <if test="e.schemeId != null and e.schemeId != '' ">
            AND t.scheme_id = #{e.schemeId}
        </if>
        <if test="e.defectId != null and e.defectId != '' ">
            AND t.defect_id = #{e.defectId}
        </if>
        <if test="e.defectName != null and e.defectName != '' ">
            AND t.defect_name = #{e.defectName}
        </if>
        <if test="e.defectType != null and e.defectType != '' ">
            AND t.defect_type = #{e.defectType}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.fname != null and e.fname != '' ">
            AND t.fname = #{e.fname}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.picUrl != null and e.picUrl != '' ">
            AND t.pic_url = #{e.picUrl}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>
    <select id="getWeekDefectRecordForFac" resultType="com.yelink.dfs.entity.defect.DefectRecordEntity">
        SELECT
        *
        FROM
        dfs_defect_record
        WHERE
        fid in
        <foreach item="item" collection="fids" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and
        DATE_SUB( CURDATE(), INTERVAL 7 DAY ) &lt;= date( create_time )
        order by create_time
    </select>


</mapper>