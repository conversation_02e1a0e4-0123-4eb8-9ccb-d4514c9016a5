<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.screen.ScreenDeviceTagMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.screen.ScreenDeviceTagEntity">
		    <result column="id" property="id" />
		    <result column="tag_name" property="tagName" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
		    <result column="state" property="state" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.tagName != null and e.tagName != '' ">
					AND t.tag_name = #{e.tagName}
				</if>
				<if test="e.gridId != null and e.gridId != '' ">
					AND t.grid_id = #{e.gridId}
				</if>
				<if test="e.lineId != null and e.lineId != '' ">
					AND t.line_id = #{e.lineId}
				</if>
				<if test="e.deviceId != null and e.deviceId != '' ">
					AND t.device_id = #{e.deviceId}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.state != null and e.state != '' ">
					AND t.state = #{e.state}
				</if>
	</sql>

</mapper>