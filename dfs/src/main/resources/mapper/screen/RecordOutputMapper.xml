<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.screen.RecordOutputMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.RecordOutputEntity">
        <result column="id" property="id"/>
        <result column="planQuantity" property="planQuantity"/>
        <result column="completed" property="completed"/>
        <result column="orderPlanQuantity" property="orderPlanQuantity"/>
        <result column="orderCompleted" property="orderCompleted"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.planQuantity != null and e.planQuantity != '' ">
            AND t.planQuantity = #{e.planQuantity}
        </if>
        <if test="e.completed != null and e.completed != '' ">
            AND t.completed = #{e.completed}
        </if>
        <if test="e.orderPlanQuantity != null and e.orderPlanQuantity != '' ">
            AND t.order_plan_quantity = #{e.planQuantity}
        </if>
        <if test="e.orderCompleted != null and e.orderCompleted != '' ">
            AND t.order_completed = #{e.orderCompleted}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>
