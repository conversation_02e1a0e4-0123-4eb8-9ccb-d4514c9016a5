<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.screen.ScreenSensorMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.screen.ScreenSensorEntity">
		    <result column="id" property="id" />
		    <result column="sensor_id" property="sensorId" />
		    <result column="screen_id" property="screenId" />
		    <result column="create_time" property="createTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.sensorId != null and e.sensorId != '' ">
					AND t.sensor_id = #{e.sensorId}
				</if>
				<if test="e.screenId != null and e.screenId != '' ">
					AND t.screen_id = #{e.screenId}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>