<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.screen.ComponentMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.screen.ComponentEntity">
		    <result column="id" property="id" />
		    <result column="position" property="position" />
		    <result column="name" property="name" />
		    <result column="size" property="size" />
		    <result column="scale_size" property="scaleSize" />
		    <result column="type" property="type" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
		    <result column="status" property="status" />
		    <result column="screen_id" property="screenId" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.position != null and e.position != '' ">
					AND t.position = #{e.position}
				</if>
				<if test="e.name != null and e.name != '' ">
					AND t.name = #{e.name}
				</if>
				<if test="e.size != null and e.size != '' ">
					AND t.size = #{e.size}
				</if>
				<if test="e.scaleSize != null and e.scaleSize != '' ">
					AND t.scale_size = #{e.scaleSize}
				</if>
				<if test="e.type != null and e.type != '' ">
					AND t.type = #{e.type}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.status != null and e.status != '' ">
					AND t.status = #{e.status}
				</if>
	</sql>

</mapper>