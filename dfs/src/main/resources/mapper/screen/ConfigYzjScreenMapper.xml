<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.screen.ConfigYzjScreenMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.screen.ConfigYzjScreenEntity">
        <result column="id" property="id"/>
        <result column="eui" property="eui"/>
        <result column="screen_name" property="screenName"/>
        <result column="model_id" property="modelId"/>
        <result column="device_id" property="deviceId"/>
        <result column="target_name" property="targetName"/>
        <result column="type" property="type"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="grid_name" property="gridName"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.modelId != null and e.modelId != '' ">
            AND t.model_id = #{e.modelId}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.targetName != null and e.targetName != '' ">
            AND t.target_name = #{e.targetName}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
    </sql>

    <select id="getFinishCountInThisYear" resultType="com.yelink.dfs.entity.target.record.RecordManualCollectionEntity">
        SELECT *
        FROM `dfs_record_manual_collection`
        WHERE YEAR(report_time) = YEAR(#{currentDate})
        <if test="instanceIdList != null and instanceIdList.size > 0">
            AND `device_id` IN
            <foreach item="item" collection="instanceIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getManualCollectionOnDay" resultType="com.yelink.dfs.entity.target.record.RecordManualCollectionEntity">
        SELECT *
        FROM `dfs_record_manual_collection`
        WHERE TO_DAYS(report_time) = TO_DAYS(#{date})
        <if test="instanceIdList != null and instanceIdList.size > 0">
            AND `device_id` IN
            <foreach item="item" collection="instanceIdList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getParentWorkOrderOnDay" resultType="com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity">
        SELECT *
        FROM `dfs_record_work_order_day_count`
        WHERE TO_DAYS(time) = TO_DAYS(#{date})
        AND `line_id` is null
        <if test="instanceNumList != null and instanceNumList.size > 0">
            AND `work_order_number` IN
            <foreach item="item" collection="instanceNumList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </select>


</mapper>