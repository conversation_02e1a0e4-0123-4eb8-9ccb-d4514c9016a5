<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.screen.ScreenConfigMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.screen.ScreenConfigEntity">
		    <result column="id" property="id" />
		    <result column="screen_name" property="screenName" />
		    <result column="is_show_home_page" property="isShowHomePage" />
		    <result column="function_json" property="functionJson" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.screenName != null and e.screenName != '' ">
					AND t.screen_name = #{e.screenName}
				</if>
				<if test="e.isShowHomePage != null and e.isShowHomePage != '' ">
					AND t.is_show_home_page = #{e.isShowHomePage}
				</if>
				<if test="e.functionJson != null and e.functionJson != '' ">
					AND t.function_json = #{e.functionJson}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
	</sql>

</mapper>