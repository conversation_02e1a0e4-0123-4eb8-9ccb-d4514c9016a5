<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordWorkOrderStateMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordWorkOrderStateEntity">
        <result column="id" property="id"/>
        <result column="work_order" property="workOrder"/>
        <result column="state" property="state"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

    <!--<select id="selectWorkingOrderList" resultType="java.lang.String">
        select work_order from dfs_record_constant_work_order_state where state = #{code} and `time` between #{startDate} and #{endDate} group by work_order
    </select>-->

    <!--<insert id="insertConstantRecord">
        INSERT INTO `dfs_record_constant_work_order_state`(`work_order`, `state`, `time`)
        VALUES (#{number}, #{state}, #{time})
    </insert>-->


</mapper>
