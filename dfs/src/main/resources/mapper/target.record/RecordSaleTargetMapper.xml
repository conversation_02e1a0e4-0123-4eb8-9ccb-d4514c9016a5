<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordSaleTargetMapper">

    <insert id="insertRecord">
        INSERT INTO
            <if test="dto.timeUnit != null" >
                dfs_record_sale_target_${dto.timeUnit}
            </if>
            <if test="dto.timeUnit == null">
                dfs_record_sale_target
            </if>
            ( target_value, target_method, time_range, record_time)
        VALUES
            (
                #{dto.targetValue},
                #{dto.targetMethod},
                #{dto.timeRange},
                #{dto.now}
            )
            ON DUPLICATE KEY UPDATE target_value = #{dto.targetValue} , record_time = #{dto.now}
    </insert>


    <select id="selectRecord" resultType="java.lang.Double">
        SELECT target_value
            FROM
            <if test="dto.timeUnit != null" >
                dfs_record_sale_target_${dto.timeUnit}
            </if>
            <if test="dto.timeUnit == null">
                dfs_record_sale_target
            </if>
        <where>
            target_method = #{dto.targetMethod}
            <if test="dto.timeRange != null">
                AND time_range = #{dto.timeRange}
            </if>
            LIMIT 1;
        </where>
    </select>
</mapper>