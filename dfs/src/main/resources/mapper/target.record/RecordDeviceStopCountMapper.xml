<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceStopCountMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceStopCountEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="type_code" property="typeCode"/>
        <result column="count" property="count"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.typeCode != null and e.typeCode != '' ">
            AND t.type_code = #{e.typeCode}
        </if>
        <if test="e.count != null and e.count != '' ">
            AND t.count = #{e.count}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>