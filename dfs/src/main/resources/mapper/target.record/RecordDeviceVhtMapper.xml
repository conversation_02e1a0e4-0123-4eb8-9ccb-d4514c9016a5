<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceVhtMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceVhtEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="vhat" property="vhat"/>
        <result column="vhbt" property="vhbt"/>
        <result column="vhct" property="vhct"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.vhat != null and e.vhat != '' ">
            AND t.vhat = #{e.vhat}
        </if>
        <if test="e.vhbt != null and e.vhbt != '' ">
            AND t.vhbt = #{e.vhbt}
        </if>
        <if test="e.vhct != null and e.vhct != '' ">
            AND t.vhct = #{e.vhct}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>
