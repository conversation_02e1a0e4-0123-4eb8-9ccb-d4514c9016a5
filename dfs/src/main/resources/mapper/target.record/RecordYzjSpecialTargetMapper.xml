<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordYzjSpecialTargetMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordYzjSpecialTargetEntity">
        <result column="id" property="id"/>
        <result column="target_name" property="targetName"/>
        <result column="batch" property="batch"/>
        <result column="batch_name" property="batchName"/>
        <result column="value" property="value"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.targetName != null and e.targetName != '' ">
            AND t.target_name = #{e.targetName}
        </if>
        <if test="e.batch != null and e.batch != '' ">
            AND t.batch = #{e.batch}
        </if>
        <if test="e.batchName != null and e.batch_name != '' ">
            AND t.batch_name = #{e.batchName}
        </if>
        <if test="e.value != null and e.value != '' ">
            AND t.value = #{e.value}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>


</mapper>