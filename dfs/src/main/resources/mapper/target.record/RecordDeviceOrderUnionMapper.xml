<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceOrderUnionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceOrderUnionEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="theoretical_speed" property="theoreticalSpeed"/>
        <result column="amount" property="amount"/>
        <result column="unqualified" property="unqualified"/>
        <result column="run_time" property="runTime"/>
        <result column="load_time" property="loadTime"/>
        <result column="yield" property="yield"/>
        <result column="performance" property="performance"/>
        <result column="time_efficiency" property="timeEfficiency"/>
        <result column="oee" property="oee"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.orderNumber != null and e.orderNumber != '' ">
            AND t.order_number = #{e.orderNumber}
        </if>
        <if test="e.theoreticalSpeed != null and e.theoreticalSpeed != '' ">
            AND t.theoretical_speed = #{e.theoreticalSpeed}
        </if>
        <if test="e.amount != null and e.amount != '' ">
            AND t.amount = #{e.amount}
        </if>
        <if test="e.unqualified != null and e.unqualified != '' ">
            AND t.unqualified = #{e.unqualified}
        </if>
        <if test="e.runTime != null and e.runTime != '' ">
            AND t.run_time = #{e.runTime}
        </if>
        <if test="e.loadTime != null and e.loadTime != '' ">
            AND t.load_time = #{e.loadTime}
        </if>
        <if test="e.yield != null and e.yield != '' ">
            AND t.yield = #{e.yield}
        </if>
        <if test="e.performance != null and e.performance != '' ">
            AND t.performance = #{e.performance}
        </if>
        <if test="e.timeEfficiency != null and e.timeEfficiency != '' ">
            AND t.time_efficiency = #{e.timeEfficiency}
        </if>
        <if test="e.oee != null and e.oee != '' ">
            AND t.oee = #{e.oee}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>