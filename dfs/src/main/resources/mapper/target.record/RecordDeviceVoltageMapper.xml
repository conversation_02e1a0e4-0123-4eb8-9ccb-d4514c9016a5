<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceVoltageMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceVoltageEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="ua" property="ua"/>
        <result column="ub" property="ub"/>
        <result column="uc" property="uc"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.ua != null and e.ua != '' ">
            AND t.ua = #{e.ua}
        </if>
        <if test="e.ub != null and e.ub != '' ">
            AND t.ub = #{e.ub}
        </if>
        <if test="e.uc != null and e.uc != '' ">
            AND t.uc = #{e.uc}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>
