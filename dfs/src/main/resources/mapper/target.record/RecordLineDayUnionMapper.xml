<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordLineDayUnionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordLineDayUnionEntity">
        <result column="id" property="id"/>
        <result column="production_line_id" property="productionLineId"/>
        <result column="count" property="count"/>
        <result column="unqualified" property="unqualified"/>
        <result column="time_efficiency" property="timeEfficiency"/>
        <result column="performance" property="performance"/>
        <result column="yield" property="yield"/>
        <result column="oee" property="oee"/>
        <result column="finish_rate" property="finishRate"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.count != null and e.count != '' ">
            AND t.count = #{e.count}
        </if>
        <if test="e.unqualified != null and e.unqualified != '' ">
            AND t.unqualified = #{e.unqualified}
        </if>
        <if test="e.timeEfficiency != null and e.timeEfficiency != '' ">
            AND t.time_efficiency = #{e.timeEfficiency}
        </if>
        <if test="e.performance != null and e.performance != '' ">
            AND t.performance = #{e.performance}
        </if>
        <if test="e.yield != null and e.yield != '' ">
            AND t.yield = #{e.yield}
        </if>
        <if test="e.oee != null and e.oee != '' ">
            AND t.oee = #{e.oee}
        </if>
        <if test="e.finishRate != null and e.finishRate != '' ">
            AND t.finish_rate = #{e.finishRate}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>