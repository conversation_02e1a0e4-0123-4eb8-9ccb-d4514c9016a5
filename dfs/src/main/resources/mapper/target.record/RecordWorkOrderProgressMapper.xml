<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordWorkOrderProgressMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordWorkOrderProgressEntity">
        <result column="id" property="id"/>
        <result column="work_order" property="workOrder"/>
        <result column="progress" property="progress"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.progress != null and e.progress != '' ">
            AND t.progress = #{e.progress}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>
