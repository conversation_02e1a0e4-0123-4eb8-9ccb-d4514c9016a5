<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.ReportFacStateMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.ReportFacStateEntity">
        <result column="id" property="id"/>
        <result column="aid" property="aid"/>
        <result column="gid" property="gid"/>
        <result column="production_line_id" property="productionLineId"/>
        <result column="fid" property="fid"/>
        <result column="fname" property="fname"/>
        <result column="state" property="state"/>
        <result column="user_name" property="userName"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.gid != null and e.gid != '' ">
            AND t.gid = #{e.gid}
        </if>
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.fname != null and e.fname != '' ">
            AND t.fname = #{e.fname}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.userName != null and e.userName != '' ">
            AND t.user_name = #{e.userName}
        </if>
        <if test="e.userNickname != null and e.userNickname != '' ">
            AND t.user_nickname = #{e.userNickname}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>