<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.ReportLineMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.ReportLineEntity">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="line_id" property="lineId"/>
        <result column="line_name" property="lineName"/>
        <result column="work_order" property="workOrder"/>
        <result column="finish_count" property="finishCount"/>
        <result column="unqualified" property="unqualified"/>
        <result column="user_name" property="userName"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="create_time" property="createTime"/>
        <result column="effective_hours" property="effectiveHours"/>
        <result column="defect_desc" property="defectDesc"/>
        <result column="operator" property="operator"/>
        <result column="report_time" property="reportTime"/>
        <result column="report_date" property="reportDate"/>
        <result column="operator_name" property="operatorName"/>
        <result column="update_time" property="updateTime"/>
        <result column="batch" property="batch"/>
        <result column="shift_id" property="shiftId"/>
        <result column="shift_type" property="shiftType"/>
        <result column="report_count_id" property="reportCountId"/>
        <result column="vehicle_code" property="vehicleCode"/>
    </resultMap>

    <insert id="insertWriteBackId" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into dfs_report_line (type, line_id, line_name, work_order,finish_count,unqualified,user_name,
        user_nickname,create_time,effective_hours,defect_desc,operator,report_time,report_date,operator_name,
        update_time,batch,shift_id,shift_type,report_count_id,vehicle_code)
        values (#{type}, #{lineId}, #{lineName}, #{workOrder},#{finishCount},#{unqualified},#{userName},
        #{userNickname},#{createTime},#{effectiveHours},#{defectDesc},#{operator},#{reportTime},#{reportDate},#{operatorName},
        #{updateTime},#{batch},#{shiftId},#{shiftType},#{reportCountId},#{vehicleCode})
    </insert>

    <update id="updateReportLine">
        update dfs_report_line set finish_count = #{finishCount},
        auto_count_record_time=#{time},
        update_time=#{time}
        where work_order = #{workOrder} and `type`=#{type}
        <if test="batch != null and batch != ''">
            AND batch = #{batch}
        </if>
    </update>

    <update id="updateReportLineByProgress">
        update dfs_report_line set finish_count = #{finishCount},auto_count = #{finishCount},auto_count_record_time=#{now},
        report_date=#{entity.reportDate},report_end_time=#{now}
        where id = #{entity.id}
        and update_time=#{entity.updateTime};
    </update>

    <update id="updateUnqualifiedLineByProgress">
        update dfs_report_line set unqualified = #{unqualified},auto_unqualified = #{unqualified},auto_count_record_time=#{now},
        report_date=#{entity.reportDate},report_end_time=#{now}
        where id = #{entity.id}
        and update_time=#{entity.updateTime};
    </update>

    <select id="getFinishCountGroupByUser" resultType="com.yelink.dfs.entity.target.record.ReportLineEntity">
        SELECT
            report.operator,
            IFNULL( SUM( report.finish_count ), 0 ) AS finish_count
        FROM
            dfs_report_line report
            LEFT JOIN dfs_production_line line ON line.production_line_id = report.line_id
        <where>
            <if test="lineModelId != null and lineModelId != ''">
                AND line.model_id = #{lineModelId}
            </if>
            <if test="recordDate != null ">
                AND report.report_date = #{recordDate}
            </if>
        </where>
        GROUP BY
            report.operator
    </select>

    <select id="batchProcedureCount" resultType="com.yelink.dfscommon.entity.ams.dto.ProcedureProcessVO">
        select craft_procedure_id as craftProcedureId, count(l.id) as passCount from dfs_report_line l
        left join dfs_work_order_procedure_relation r on l.work_order = r.work_order_number
        where l.batch = #{productFlowCode} GROUP BY r.craft_procedure_id;
    </select>
    <select id="getList" resultType="com.yelink.dfs.entity.target.record.ReportLineEntity">
        select report.* from dfs_report_line report
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
