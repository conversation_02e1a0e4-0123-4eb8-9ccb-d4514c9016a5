<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordLineYieldMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordLineYieldEntity">
        <result column="id" property="id"/>
        <result column="line_id" property="lineId"/>
        <result column="yield" property="yield"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.yield != null and e.yield != '' ">
            AND t.yield = #{e.yield}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

    <select id="yieldTrend" resultType="java.util.Map">
        SELECT
        CONVERT(SUBSTR(datelist.date,6,LENGTH(datelist.date)), UNSIGNED INTEGER) `date`,
        IFNULL(`count`,0) `count`
        from(
        select date_format(date,'%Y-%m') date
        from(
        select date_format(date_add(CONCAT(YEAR(#{currentDate}),'-01-01'),interval +t.help_topic_id month),'%Y-%m-%d') as date
        from mysql.help_topic t where t.help_topic_id &lt; 12
        )a where date &lt;= YEAR(#{currentDate})+1
        ) datelist
        LEFT JOIN (
        SELECT
        DATE_FORMAT(`time`, '%Y-%m') as `time`,
        avg(yield) `count`
        FROM
        dfs_record_line_day_union t
        where t.production_line_id=#{lineId}
        group by DATE_FORMAT(`time`, '%Y-%m')
        ) yield ON datelist.date = yield.`time`
        ORDER BY date ASC
    </select>
</mapper>
