<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordLineInputOutputOrderMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordLineInputOutputOrderEntity">
        <result column="id" property="id"/>
        <result column="work_order" property="workOrder"/>
        <result column="line_id" property="lineId"/>
        <result column="input" property="input"/>
        <result column="output" property="output"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.input != null and e.input != '' ">
            AND t.input = #{e.input}
        </if>
        <if test="e.output != null and e.output != '' ">
            AND t.output = #{e.output}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>
    <select id="sumOfInputs" resultType="java.lang.Double">
     SELECT
	    IFNULL( SUM( `output` ), 0 ) `output`
    FROM
        dfs_record_line_input_output_order
    WHERE
	    work_order = #{orderNumber}
	AND line_id =  #{lineId}
	</select>
    <select id="sumOfIns" resultType="java.lang.Double">
     SELECT
	    IFNULL( SUM( `input` ), 0 ) `input`
    FROM
        dfs_record_line_input_output_order
    WHERE
	    work_order = #{orderNumber}
	AND line_id =  #{lineId}
	</select>
    <select id="getTodayInputAndOutput"
            resultType="com.yelink.dfs.entity.target.record.dto.TodayInputAndOutputDTO">
        SELECT
        DATE_FORMAT( `time`, '%H:00' ) `time`,
        SUM( `input` ) `input`,
        SUM( `output` ) `output`
    FROM
        dfs_record_line_input_output_order
        where
         line_id = #{lineId}
    GROUP BY
        DATE_FORMAT( time, '%H:00' )
    ORDER BY
        DATE_FORMAT(time,'%H:00' )

    </select>

</mapper>