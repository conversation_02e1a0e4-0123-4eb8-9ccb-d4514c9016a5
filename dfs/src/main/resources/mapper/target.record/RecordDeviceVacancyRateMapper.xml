<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceVacancyRateMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceVacancyRateEntity">
		    <result column="id" property="id" />
		    <result column="device_id" property="deviceId" />
		    <result column="vacancy_rate" property="vacancyRate" />
		    <result column="batch" property="batch" />
		    <result column="time" property="time" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.deviceId != null and e.deviceId != '' ">
					AND t.device_id = #{e.deviceId}
				</if>
				<if test="e.vacancyRate != null and e.vacancyRate != '' ">
					AND t.vacancy_rate = #{e.vacancyRate}
				</if>
				<if test="e.batch != null and e.batch != '' ">
					AND t.batch = #{e.batch}
				</if>
				<if test="e.time != null and e.time != '' ">
					AND t.time = #{e.time}
				</if>
	</sql>

</mapper>