<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.ReportCountMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.ReportCountEntity">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="aid" property="aid"/>
        <result column="aname" property="aname"/>
        <result column="gid" property="gid"/>
        <result column="gname" property="gname"/>
        <result column="line_id" property="lineId"/>
        <result column="line_name" property="lineName"/>
        <result column="fid" property="fid"/>
        <result column="fname" property="fname"/>
        <result column="work_order" property="workOrder"/>
        <result column="finish_count" property="finishCount"/>
        <result column="unqualified" property="unqualified"/>
        <result column="user_name" property="userName"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="create_time" property="createTime"/>
        <result column="effective_hours" property="effectiveHours"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.aname != null and e.aname != '' ">
            AND t.aname = #{e.aname}
        </if>
        <if test="e.gid != null and e.gid != '' ">
            AND t.gid = #{e.gid}
        </if>
        <if test="e.gname != null and e.gname != '' ">
            AND t.gname = #{e.gname}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.lineName != null and e.lineName != '' ">
            AND t.line_name = #{e.lineName}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.fname != null and e.fname != '' ">
            AND t.fname = #{e.fname}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.finishCount != null and e.finishCount != '' ">
            AND t.finish_count = #{e.finishCount}
        </if>
        <if test="e.unqualified != null and e.unqualified != '' ">
            AND t.unqualified = #{e.unqualified}
        </if>
        <if test="e.userName != null and e.userName != '' ">
            AND t.user_name = #{e.userName}
        </if>
        <if test="e.userNickname != null and e.userNickname != '' ">
            AND t.user_nickname = #{e.userNickname}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.effectiveHours != null and e.effectiveHours != '' ">
            AND t.effective_hours = #{e.effectiveHours}
        </if>
    </sql>

</mapper>