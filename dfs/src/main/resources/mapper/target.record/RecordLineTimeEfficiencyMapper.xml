<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordLineTimeEfficiencyMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordLineTimeEfficiencyEntity">
        <result column="id" property="id"/>
        <result column="line_id" property="lineId"/>
        <result column="efficiency" property="efficiency"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.efficiency != null and e.efficiency != '' ">
            AND t.efficiency = #{e.efficiency}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>
