<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordLineBeatMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordLineBeatEntity">
		    <result column="id" property="id" />
		    <result column="line_id" property="lineId" />
		    <result column="beat_5min" property="beat5min" />
		    <result column="beat_10min" property="beat10min" />
		    <result column="beat_60min" property="beat60min" />
		    <result column="time" property="time" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.lineId != null and e.lineId != '' ">
					AND t.line_id = #{e.lineId}
				</if>
				<if test="e.beat5min != null and e.beat5min != '' ">
					AND t.beat_5min = #{e.beat5min}
				</if>
				<if test="e.beat10min != null and e.beat10min != '' ">
					AND t.beat_10min = #{e.beat10min}
				</if>
				<if test="e.beat60min != null and e.beat60min != '' ">
					AND t.beat_60min = #{e.beat60min}
				</if>
				<if test="e.time != null and e.time != '' ">
					AND t.time = #{e.time}
				</if>
	</sql>

</mapper>