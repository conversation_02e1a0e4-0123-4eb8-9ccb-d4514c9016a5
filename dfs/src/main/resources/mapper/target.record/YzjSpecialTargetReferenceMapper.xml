<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.YzjSpecialTargetReferenceMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.YzjSpecialTargetReferenceEntity">
        <result column="id" property="id"/>
        <result column="target_name" property="targetName"/>
        <result column="target_cnname" property="targetCnname"/>
        <result column="reference_upper" property="referenceUpper"/>
        <result column="reference_lower" property="referenceLower"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.targetName != null and e.targetName != '' ">
            AND t.target_name = #{e.targetName}
        </if>
        <if test="e.targetCnname != null and e.targetCnname != '' ">
            AND t.target_cnname = #{e.targetCnname}
        </if>
        <if test="e.referenceUpper != null and e.referenceUpper != '' ">
            AND t.reference_upper = #{e.referenceUpper}
        </if>
        <if test="e.referenceLower != null and e.referenceLower != '' ">
            AND t.reference_lower = #{e.referenceLower}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>

</mapper>