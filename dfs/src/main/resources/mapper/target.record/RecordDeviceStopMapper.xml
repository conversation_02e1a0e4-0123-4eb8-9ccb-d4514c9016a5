<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceStopMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceStopEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="type_code" property="typeCode"/>
        <result column="stop" property="stop"/>
        <result column="batch" property="batch"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.typeCode != null and e.typeCode != '' ">
            AND t.type_code = #{e.typeCode}
        </if>
        <if test="e.stop != null and e.stop != '' ">
            AND t.stop = #{e.stop}
        </if>
        <if test="e.batch != null and e.batch != '' ">
            AND t.batch = #{e.batch}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>
    <select id="getStopState" resultType="com.yelink.dfs.entity.target.record.RecordDeviceStopEntity">
        SELECT
        id,
        device_id,
        stop,
        `time`
        FROM
        dfs_record_device_stop
        WHERE
        device_id = #{deviceId}
        AND to_days(`time`) = to_days(#{currentDate})
        ORDER BY `time` DESC

    </select>

</mapper>