<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceCurrentMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceCurrentEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="ia" property="ia"/>
        <result column="ib" property="ib"/>
        <result column="ic" property="ic"/>
        <result column="time" property="time"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.ia != null and e.ia != '' ">
            AND t.ia = #{e.ia}
        </if>
        <if test="e.ib != null and e.ib != '' ">
            AND t.ib = #{e.ib}
        </if>
        <if test="e.ic != null and e.ic != '' ">
            AND t.ic = #{e.ic}
        </if>
        <if test="e.time != null and e.time != '' ">
            AND t.time = #{e.time}
        </if>
    </sql>

</mapper>
