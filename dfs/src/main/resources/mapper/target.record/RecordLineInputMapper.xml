<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordLineInputMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordLineInputEntity">
		    <result column="id" property="id" />
		    <result column="production_line_id" property="productionLineId" />
		    <result column="input" property="input" />
		    <result column="time" property="time" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.productionLineId != null and e.productionLineId != '' ">
					AND t.production_line_id = #{e.productionLineId}
				</if>
				<if test="e.input != null and e.input != '' ">
					AND t.input = #{e.input}
				</if>
				<if test="e.time != null and e.time != '' ">
					AND t.time = #{e.time}
				</if>
	</sql>

</mapper>