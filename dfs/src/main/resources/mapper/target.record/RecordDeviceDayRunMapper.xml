<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordDeviceDayRunMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordDeviceDayRunEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="add_type" property="addType"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_name" property="orderName"/>
        <result column="stop_type" property="stopType"/>
        <result column="state" property="state"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="duration" property="duration"/>
        <result column="remark" property="remark"/>
        <result column="record_date" property="recordDate"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.addType != null and e.addType != '' ">
            AND t.add_type = #{e.addType}
        </if>
        <if test="e.orderNumber != null and e.orderNumber != '' ">
            AND t.order_number = #{e.orderNumber}
        </if>
        <if test="e.orderName != null and e.orderName != '' ">
            AND t.order_name = #{e.orderName}
        </if>
        <if test="e.stopType != null and e.stopType != '' ">
            AND t.stop_type = #{e.stopType}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.startTime != null and e.startTime != '' ">
            AND t.start_time = #{e.startTime}
        </if>
        <if test="e.endTime != null and e.endTime != '' ">
            AND t.end_time = #{e.endTime}
        </if>
        <if test="e.duration != null and e.duration != '' ">
            AND t.duration = #{e.duration}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.recordDate != null and e.recordDate != '' ">
            AND t.record_date = #{e.recordDate}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>