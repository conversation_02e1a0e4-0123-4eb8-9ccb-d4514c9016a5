<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.RecordManualCollectionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.RecordManualCollectionEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="operator" property="operator"/>
        <result column="batch" property="batch"/>
        <result column="report_content" property="reportContent"/>
        <result column="report_time" property="reportTime"/>
        <result column="create_time" property="createTime"/>
        <result column="upadte_time" property="upadteTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.operator != null and e.operator != '' ">
            AND t.operator = #{e.operator}
        </if>
        <if test="e.batch != null and e.batch != '' ">
            AND t.batch = #{e.batch}
        </if>
        <if test="e.reportContent != null and e.reportContent != '' ">
            AND t.report_content = #{e.reportContent}
        </if>
        <if test="e.reportTime != null and e.reportTime != '' ">
            AND t.report_time = #{e.reportTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.upadteTime != null and e.upadteTime != '' ">
            AND t.upadte_time = #{e.upadteTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>
    <select id="getRecordsByBatch" resultType="com.yelink.dfs.entity.target.record.RecordManualCollectionEntity">
        SELECT
        *
        FROM dfs_record_manual_collection
        WHERE batch = #{batch}
        <if test="deviceIds != null and deviceIds.size > 0">
            and device_id in
            <foreach item="item" collection="deviceIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>


    </select>


</mapper>