<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.record.SterilizationLimitSettingMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.record.SterilizationLimitSettingEntity">
        <result column="id" property="id"/>
        <result column="target_name" property="targetName"/>
        <result column="batch" property="batch"/>
        <result column="reference" property="reference"/>
        <result column="upper" property="upper"/>
        <result column="lower" property="lower"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.targetName != null and e.targetName != '' ">
            AND t.target_name = #{e.targetName}
        </if>
        <if test="e.batch != null and e.batch != '' ">
            AND t.batch = #{e.batch}
        </if>
        <if test="e.reference != null and e.reference != '' ">
            AND t.reference = #{e.reference}
        </if>
        <if test="e.upper != null and e.upper != '' ">
            AND t.upper = #{e.upper}
        </if>
        <if test="e.lower != null and e.lower != '' ">
            AND t.lower = #{e.lower}
        </if>
    </sql>

</mapper>