<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.metrics.MetricsLineMaterialDefectDailyMapper">

    <select id="listDefectDistribution" resultType="com.yelink.dfs.entity.defect.vo.DefectDistributionVO" >
        SELECT record_date as recordDate ,
               material_code as materialCode,
               material_name as materialName,
               defect_type_code as defectTypeCode,
               defect_type_name as defectTypeName,
               defect_id as defectId,
               defect_name as defectName,
               SUM(unqualified_record_item_quantity) as unqualifiedRecordItemQuantity
        FROM dfs_metrics_line_material_defect_daily
        where 1=1
        <if test="selectDTO.recordDateStart != null and selectDTO.recordDateEnd != null">
            AND record_date BETWEEN #{selectDTO.recordDateStart} AND #{selectDTO.recordDateEnd}
        </if>
        <if test="selectDTO.materialCodes != null and selectDTO.materialCodes != '' ">
            AND material_code in
            <foreach item="item" collection="selectDTO.materialCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.defectTypes != null and selectDTO.defectTypes != '' ">
            AND defect_type_code in
            <foreach item="item" collection="selectDTO.defectTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.defectName != null and selectDTO.defectName != ''">
            and defect_name LIKE CONCAT('%',#{selectDTO.defectName},'%')
        </if>
        GROUP BY material_code
    </select>
</mapper>
