<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.metrics.MetricsWorkOrder10minMapper">

	<update id="updateDelta">
        UPDATE dfs_metrics_work_order_10min
        SET
            produce_quantity = produce_quantity + #{deltaProduceQuantity},
            unqualified_quantity = unqualified_quantity + #{deltaUnqualifiedQuantity},
            `time` = #{now}
        WHERE
            work_order_number = #{workOrderNumber}
        AND record_time = #{startTime}
	</update>

</mapper>
