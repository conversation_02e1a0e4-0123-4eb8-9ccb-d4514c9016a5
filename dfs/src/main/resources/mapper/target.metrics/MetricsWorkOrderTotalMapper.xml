<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.metrics.MetricsWorkOrderTotalMapper">

	<select id="listGroupUnitMaterial"
			resultType="com.yelink.dfs.entity.statement.vo.WorkOrderTotalStatisticsByUnitMaterialVO">
        SELECT
        time,
        material_code,
        material_name,
        production_basic_unit_id,
        production_basic_unit_name,
        production_basic_unit_type_name,
        -- 工单状态是完成或关闭状态
        SUM(IF(STATE=4 || STATE=5,1,0)) work_order_quantity,
        SUM(IFNULL(produce_quantity, 0)) produce_quantity,
        SUM(
        IFNULL(unqualified_quantity, 0)
        ) unqualified_quantity,
        SUM(
        IFNULL(actual_working_hour, 0)
        ) actual_working_hour
        FROM
        dfs_metrics_work_order_total
        <where>
            <include refid="selectConditions"></include>
        </where>
        GROUP BY
        production_basic_unit_id,
        material_code
	</select>

	<sql id="selectConditions">
            AND material_code is not null
            AND work_center_id is not null
        <if test="params.materialCodes != null and params.materialCodes.size>0 ">
            AND material_code in
            <foreach item="item" index="index" collection="params.materialCodes" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.productionBasicUnitIds != null and params.productionBasicUnitIds.size>0 ">
            AND production_basic_unit_id in
            <foreach item="item" index="index" collection="params.productionBasicUnitIds" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.workCenterIds != null and params.workCenterIds.size>0 ">
            AND work_center_id in
            <foreach item="item" index="index" collection="params.workCenterIds" open="(" separator="," close=")" >
                #{item}
            </foreach>
        </if>
        <if test="params.materialCode != null and params.materialCode != '' ">
            AND material_code like  concat('%',#{params.materialCode},'%')
        </if>
        <if test="params.materialName != null and params.materialName != '' ">
            AND material_name like  concat('%',#{params.materialName},'%')
        </if>
        <if test="params.workCenterName != null and params.workCenterName != '' ">
            AND work_center_name like  concat('%',#{params.workCenterName},'%')
        </if>
        <if test="params.productionBasicUnitTypeName != null and params.productionBasicUnitTypeName != '' ">
            AND production_basic_unit_type_name like  concat('%',#{params.productionBasicUnitTypeName},'%')
        </if>
        <if test="params.productionBasicUnitName != null and params.productionBasicUnitName != '' ">
            AND production_basic_unit_name like  concat('%',#{params.productionBasicUnitName},'%')
        </if>
	</sql>

    <select id="listGroupMaterial"
            resultType="com.yelink.dfs.entity.statement.vo.WorkOrderTotalStatisticsByMaterialVO">
        SELECT
            time,
            material_code,
            material_name,
            SUM(IFNULL(produce_quantity, 0)) produce_quantity,
            SUM(
                IFNULL(unqualified_quantity, 0)
            ) unqualified_quantity,
            SUM(
                IFNULL(actual_working_hour, 0)
            ) actual_working_hour
        FROM
        dfs_metrics_work_order_total
        <where>
            <include refid="selectConditions"></include>
        </where>
        GROUP BY
            material_code
    </select>

    <select id="listGroupCenterMaterial"
            resultType="com.yelink.dfs.entity.statement.vo.WorkOrderTotalStatisticsByCenterMaterialVO">
        SELECT
            time,
            material_code,
            material_name,
            work_center_id,
            work_center_name,
            SUM(IF(STATE=4 || STATE=5,1,0)) work_order_quantity,
            SUM(IFNULL(produce_quantity, 0)) produce_quantity,
            SUM(
                IFNULL(unqualified_quantity, 0)
            ) unqualified_quantity,
            SUM(
                IFNULL(actual_working_hour, 0)
            ) actual_working_hour
        FROM
        dfs_metrics_work_order_total
        <where>
            <include refid="selectConditions"></include>
        </where>
        GROUP BY
            work_center_id,
            material_code
    </select>


</mapper>