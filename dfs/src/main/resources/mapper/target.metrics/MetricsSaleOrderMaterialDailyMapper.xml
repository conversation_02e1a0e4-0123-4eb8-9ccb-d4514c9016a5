<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.target.metrics.MetricsSaleOrderMaterialDailyMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.target.metrics.MetricsSaleOrderMaterialDailyEntity">
		    <result column="id" property="id" />
	</resultMap>
	<select id="getSaleMonthProductionProgress"
			resultType="com.yelink.dfs.entity.target.metrics.MetricsSaleOrderMaterialDailyEntity"
			parameterType="java.util.Date">
		SELECT
		sale.sale_order_id  as  saleOrderId,
		sale.sale_order_number as  saleOrderNumber,
		sale.material_code as materialCode,
		sale.material_name as  materialName,
		count( produce_quantity ) as monthQuantity
		FROM
		dfs_metrics_sale_order_material_daily as sale
		<where>
			<include refid="selectConditions"></include>
		</where>
		GROUP BY
		sale_order_number,
		material_code
	</select>
	<sql id="selectConditions">
		<if test="selectDTO.reportDate != null and selectDTO.reportDate != '' ">
			AND sale.record_date > #{selectDTO.reportDate}
		</if>
		<if test="selectDTO.saleOrderIds != null and selectDTO.saleOrderIds != '' ">
			AND sale.`sale_order_id`  IN
			<foreach item="item" index="index" collection="selectDTO.saleOrderIds" open="(" separator="," close=")" >
				#{item}
			</foreach>
		</if>
	</sql>


	<select id="listGroupMaterial"
			resultType="com.yelink.dfs.entity.statement.vo.SaleOrderTotalStatisticsByMaterialVO">
		SELECT
		sale.record_date,
		sale.material_code,
		sale.material_name,
		count(1) sale_order_quantity,
		SUM(IFNULL(material.sales_quantity, 0)) sales_quantity,
		SUM(IFNULL(sale.produce_quantity, 0)) produce_quantity,
		SUM(
		IFNULL(sale.unqualified_quantity, 0)
		) unqualified_quantity
		FROM
		dfs_metrics_sale_order_material_daily AS sale
		left join dfs_metrics_sale_order_material material
		on sale.sale_order_material_id = material.sale_order_material_id
		and sale.sale_order_id = material.sale_order_id
		left join dfs_metrics.v_ams_sale_order sale_order on sale_order.sale_order_number =sale.sale_order_number
		<where>
			<include refid="selectStatisticsConditions"></include>
		</where>
		GROUP BY
		sale.material_code,
		sale.record_date
		order by sale.record_date desc
	</select>
	<sql id="selectStatisticsConditions">
		<if test="params.materialCode != null and params.materialCode != '' ">
			AND sale.material_code like  concat('%',#{params.materialCode},'%')
		</if>
		<if test="params.materialName != null and params.materialName != '' ">
			AND sale.material_name like  concat('%',#{params.materialName},'%')
		</if>
		<if test="params.salesmanName != null and params.salesmanName != '' ">
			AND sale_order.salesman_name like  concat('%',#{params.salesmanName},'%')
		</if>
		<if test="params.customerName != null and params.customerName != '' ">
			AND sale_order.customer_name like  concat('%',#{params.customerName},'%')
		</if>
		<if test="params.recordDateStart != null and params.recordDateStart != '' ">
			AND sale.record_date &gt;= str_to_date(#{params.recordDateStart},'%Y-%m-%d %T')
		</if>
		<if test="params.recordDateEnd != null and params.recordDateEnd != '' ">
			AND sale.record_date &lt;= str_to_date(#{params.recordDateEnd},'%Y-%m-%d %T')
		</if>
	</sql>

	<select id="listGroupSalesmanCustomer"
			resultType="com.yelink.dfs.entity.statement.vo.SaleOrderTotalStatisticsBySalesmanCustomerVO">
		SELECT
		sale.record_date,
		sale_order.salesman_code,
		sale_order.salesman_name,
		sale_order.customer_code,
		sale_order.customer_name,
		sale.material_code,
		sale.material_name,
		SUM(IFNULL(material.sales_quantity, 0)) sales_quantity,
		count(1) sale_order_quantity
		FROM
		dfs_metrics_sale_order_material_daily AS sale
		left join dfs_metrics_sale_order_material material
		on sale.sale_order_material_id = material.sale_order_material_id
		and sale.sale_order_id = material.sale_order_id
		left join dfs_metrics.v_ams_sale_order sale_order on sale_order.sale_order_number =sale.sale_order_number
		<where>
			<include refid="selectStatisticsConditions"></include>
		</where>
		GROUP BY
		sale_order.salesman_code,
		sale_order.customer_code,
		sale.record_date,
		sale.material_code
		order by sale.record_date desc
	</select>

	<select id="listGroupCustomer"
			resultType="com.yelink.dfs.entity.statement.vo.SaleOrderTotalStatisticsByCustomerVO">
		SELECT
		sale.record_date,
		sale_order.customer_code,
		sale_order.customer_name,
		sale.material_code,
		sale.material_name,
		SUM(IFNULL(material.sales_quantity, 0)) sales_quantity,
		count(1) sale_order_quantity
		FROM
		dfs_metrics_sale_order_material_daily AS sale
		left join dfs_metrics_sale_order_material material
		on sale.sale_order_material_id = material.sale_order_material_id
		and sale.sale_order_id = material.sale_order_id
		left join dfs_metrics.v_ams_sale_order sale_order on sale_order.sale_order_number =sale.sale_order_number
		<where>
			<include refid="selectStatisticsConditions"></include>
		</where>
		GROUP BY
		sale_order.customer_code,
		sale.record_date,
		sale.material_code
		order by sale.record_date desc
	</select>
</mapper>
