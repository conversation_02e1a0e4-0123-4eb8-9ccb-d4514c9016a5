<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yelink.dfs.mapper.user.SysUserMapper">

    <resultMap id="selectRoleByUsernameMap" type="com.yelink.dfs.entity.user.SysUserEntity">
        <id column="id" property="id"/>
        <result column="user_name" property="username"/>
        <result column="pass_word" property="password"/>
        <result column="header_url" property="headerUrl"/>
        <result column="mobile" property="mobile"/>
        <result column="id_card" property="idCard"/>
        <result column="email" property="email"/>
        <result column="nick_name" property="nickname"/>
        <result column="comp_id" property="compId"/>
        <result column="bind_range" property="bindRange"/>
        <result column="job_number" property="jobNumber"/>
        <result column="unit_name" property="unitName"/>
        <result column="enabled" property="enabled"/>
        <result column="account_non_expired" property="accountNonExpired"/>
        <result column="credentials_non_expired" property="credentialsNonExpired"/>
        <result column="account_non_locked" property="accountNonLocked"/>
        <result column="age" property="age"/>
        <result column="sex" property="sex"/>
        <result column="skill_level" property="skillLevel"/>
        <result column="wechat" property="wechat"/>
        <result column="department_id" property="departmentId"/>
        <result column="entry_date" property="entryDate"/>
        <result column="post" property="post"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="role_name" property="roleName"/>
        <result column="create_time" property="createTime"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_level" property="roleLevel"/>
        <result column="role_status" property="roleStatus"/>
        <result column="post_ids" property="postIds"/>
        <result column="post_names" property="postNames"/>
        <result column="role_code" property="roleCode"/>
        <result column="creat_by_jz" property="creatByJz"/>
    </resultMap>

    <select id="getUsersByPath" resultMap="selectRoleByUsernameMap">
        select DISTINCT(a.id), a.*
        from sys_users a
                 join sys_user_role b on a.id = b.user_id
                 join sys_role_permission c on b.role_id = c.role_id
                 join sys_permissions d on c.permission_id = d.id
        where a.`enabled` = 'enable'
          AND path = #{path}
    </select>

    <select id="getUsersByPathAndNickName" resultMap="selectRoleByUsernameMap">
        select DISTINCT(a.id), a.*
        from sys_users a
                 join sys_user_role b on a.id = b.user_id
                 join sys_role_permission c on b.role_id = c.role_id
                 join sys_permissions d on c.permission_id = d.id
        where a.`enabled` = 'enable'
          AND path = #{path}
          AND a.nick_name = #{approverName}
    </select>
    <select id="getList" resultType="com.yelink.dfs.entity.user.SysUserEntity">
        select user.* from sys_users user
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
