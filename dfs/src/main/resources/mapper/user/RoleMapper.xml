<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yelink.dfs.mapper.user.SysRoleMapper">

    <resultMap id="selectMap" type="com.yelink.dfs.entity.user.SysRoleEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_level" property="roleLevel"/>
        <result column="status"/>
        <result column="description" property="description"/>
        <result column="all_work_center" property="allWorkCenter"/>
        <result column="keycloak_id" property="keycloakId"/>
    </resultMap>

    <resultMap id="selectUserMap" type="com.yelink.dfs.entity.user.SysUserEntity">
        <id column="id" property="id"/>
        <result column="user_name" property="username"/>
        <result column="department_name" property="departmentName"/>
        <result column="pass_word" property="password"/>
        <result column="header_url" property="headerUrl"/>
        <result column="mobile" property="mobile"/>
        <result column="email" property="email"/>
        <result column="nick_name" property="nickname"/>
        <result column="comp_id" property="compId"/>
        <result column="unit_name" property="unitName"/>
        <result column="id_card" property="idCard"/>
        <result column="bind_range" property="bindRange"/>
        <result column="enabled" property="enabled"/>
        <result column="account_non_expired" property="accountNonExpired"/>
        <result column="credentials_non_expired" property="credentialsNonExpired"/>
        <result column="account_non_locked" property="accountNonLocked"/>
        <result column="age" property="age"/>
        <result column="sex" property="sex"/>
        <result column="skill_level" property="skillLevel"/>
        <result column="wechat" property="wechat"/>
        <result column="department_id" property="departmentId"/>
        <result column="entry_date" property="entryDate"/>
        <result column="post" property="post"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="listAll" resultMap="selectMap">
        select mm.`id`,mm.`status`,mm.`name`,mm.`role_level`,mm.`description`,nn.`users`,nn.`pads`,mm.`role_code`
        from sys_roles mm
        left join (select a.name,
        GROUP_CONCAT(distinct c.`nick_name` SEPARATOR ',') as `users`,
        GROUP_CONCAT(distinct e.`name` SEPARATOR ',')as `pads`
        from sys_roles a
        left join sys_user_role b on a.id = b.role_id
        left join sys_users c on b.user_id = c.id
        left join sys_pad_role d on d.role_id = a.id
        left join sys_pads e on e.id = d.pad_id
        GROUP BY a.`name`) nn
        on mm.`name` = nn.`name`
        <where>
            <if test="name!=null and name!=''">
                and mm.`name` like concat('%',#{name},'%')
            </if>
            <if test="code!=null and code!=''">
                and mm.`role_code` like concat('%',#{code},'%')
            </if>
        </where>
    </select>

    <insert id="insertPadRoleBatch">
        <if test="list != null and list.size != 0">
        insert into sys_pad_role(pad_id,role_id) values
        <foreach collection="list" separator="," item="item">
            (#{item.padId},#{item.roleId})
        </foreach>
        </if>
    </insert>

    <delete id="removeRoleBindLineByLineId">
        delete from sys_role_line where line_id = #{lineId}
    </delete>

    <delete id="deletePadRoleByRoleId">
        delete
        from sys_pad_role
        where role_id = #{roleId}
    </delete>
    <select id="selectRoleBindPad" resultType="com.yelink.dfs.entity.terminal.SysPadBindRoleEntity">
        select *
        from sys_pad_role
        where pad_id = #{id}
    </select>
    <select id="getPadMemberList" resultType="com.yelink.dfs.entity.terminal.SysPadEntity">
         select b.*
        from sys_pad_role a
        join sys_pads b on a.`pad_id` = b.`id`
        where a.`role_id` = #{id}
    </select>

</mapper>
