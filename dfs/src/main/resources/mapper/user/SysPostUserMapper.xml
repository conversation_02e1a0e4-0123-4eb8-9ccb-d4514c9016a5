<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.user.SysPostUserMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.SysPostUserEntity">
		    <result column="id" property="id" />
		    <result column="post_id" property="postId" />
		    <result column="user_id" property="userId" />
			<result column="user_name" property="userName" />
			<result column="post_ids" property="postIds" />
			<result column="post_names" property="postNames" />
	</resultMap>
	<select id="getPostNameList" resultType="com.yelink.dfscommon.entity.dfs.SysPostUserEntity">
		select pu.user_name, GROUP_CONCAT(CONCAT(p.post_name,p.post_level)) as post_names, GROUP_CONCAT(p.id) as post_ids
		from sys_post p
		left join sys_post_user pu on p.id = pu.post_id
		<where>
			<if test="userNames != null and userNames.size() > 0">
				and pu.user_name in
				<foreach item="item" collection="userNames" separator="," open="(" close=")" index="">
					#{item}
				</foreach>
			</if>
		</where>
		GROUP BY pu.user_id
	</select>

</mapper>