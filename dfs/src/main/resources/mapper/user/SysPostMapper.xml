<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.user.SysPostMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.SysPostEntity">
		    <result column="id" property="id" />
		    <result column="post_code" property="postCode" />
		    <result column="post_name" property="postName" />
		    <result column="post_level" property="postLevel" />
		    <result column="relate_level" property="relateLevel" />
			<result column="relate_level_name" property="relateLevelName" />
			<result column="status" property="status" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>
	<select id="getParentLevelCount" resultType="java.lang.Long">
		select count(1) from sys_post where find_in_set(#{id}, relate_level);
	</select>

</mapper>