<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.user.SysUserColumnRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.user.SysUserColumnRecordEntity">
        <result column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="column_information" property="columnInformation"/>
        <result column="modular_type" property="modularType"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.userName != null and e.userName != '' ">
            AND t.user_name = #{e.userName}
        </if>
        <if test="e.columnInformation != null and e.columnInformation != '' ">
            AND t.column_information = #{e.columnInformation}
        </if>
        <if test="e.modularType != null and e.modularType != '' ">
            AND t.modular_type = #{e.modularType}
        </if>
    </sql>

</mapper>