<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yelink.dfs.mapper.user.SysPermissionMapper">

    <update id="updateAppNameById">
        <foreach item="item" collection="appsEntities" open="" separator=";" close=";" index="">
            UPDATE `sys_app_permission_info` SET `name` = #{item.title} WHERE `id` = #{item.applicationId}
        </foreach>
    </update>

    <update id="updateAppById">
            UPDATE `sys_app_permission_info` SET `name` = #{name}, `path` = #{path}, `description` = #{description}, id = #{newId} WHERE `id` = #{id};
            UPDATE `sys_app_permission_info` SET `parent_path` = #{path}, parent_id = #{newId} WHERE `parent_id` = #{id};
    </update>

    <update id="updatePermissionNameById">
        <foreach item="item" collection="appsEntities" open="" separator=";" close=";" index="">
            UPDATE `sys_permissions` SET `name` = #{item.title} WHERE `id` = #{item.applicationId}
        </foreach>
    </update>

    <select id="selectListByUserId" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        SELECT e.name,
               e.path,
               e.method
        FROM sys_users a,
             sys_user_role b,
             sys_roles c,
             sys_role_permission d,
             sys_permissions e
        WHERE a.id = b.user_id
          and a.id = #{id}
          AND b.role_id = c.id
          AND c.id = d.role_id
          AND d.permission_id = e.id
    </select>

    <delete id="deletePermissionRoleByRoleId">
        delete
        from sys_role_permission
        where role_id = #{roleId}
        <if test="permissionIds != null and permissionIds.size > 0">
            and permission_id in
            <foreach item="item" collection="permissionIds" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
    </delete>


    <insert id="insertUserPermissionBatch" parameterType="java.util.List">
        insert into sys_role_permission(permission_id,role_id) values
        <foreach collection="list" separator="," item="item">
            (#{item.permissionId},#{item.roleId})
        </foreach>
    </insert>

    <insert id="insertUserLine">
        insert into sys_role_line(line_id, role_id)
        values (#{lineId}, #{roleId})
    </insert>

    <select id="getPermissionListByRoleId" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select nn.*
        from sys_role_permission mm
        left join sys_permissions nn on mm.permission_id = nn.id
        where role_id = #{roleId}
    </select>

    <select id="getLineByRoleId" resultType="com.yelink.dfs.entity.manufacture.ProductionLineEntity">
        select *
        from sys_role_line mm
        left join dfs_production_line nn on mm.line_id = nn.production_line_id
        where role_id = #{roleId}
    </select>

    <select id="getPermissionListByUsername" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select d.*
        from sys_users a
                 join sys_user_role b on a.id = b.user_id
                 join sys_role_permission c on b.role_id = c.role_id
                 join sys_permissions d on c.permission_id = d.id
        where a.user_name = #{username}
          and d.is_web = #{isWeb} and d.is_enable= 1 and b.enabled = 'enable'
        <if test="serviceNames != null and serviceNames.size > 0">
            and d.service_name in
            <foreach item="item" collection="serviceNames" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        group by d.`id`
        order by d.`sort`
    </select>

    <delete id="deletePermissionBatch" parameterType="java.util.List">
        delete from sys_permissions
        where id in
        <foreach item="item" collection="permissionList" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </delete>

    <insert id="insertPermissionBatch" parameterType="java.util.List">
        insert into sys_permissions(id, name, path, description,
        status, method, parent_id, type, is_web, is_back_stage,is_enable,parent_path,create_time,update_time,service_name) values
        <foreach collection="permissionList" separator="," item="item">
            (#{item.id},#{item.name},#{item.path},#{item.description},#{item.status},#{item.method},
            #{item.parentId},#{item.type},#{item.isWeb},#{item.isBackStage},1,#{item.parentPath},#{item.createTime},#{item.updateTime},#{item.serviceName})
        </foreach>
    </insert>


    <insert id="insertPermission">
        insert into sys_permissions(id, name, path, description,
        status, method, parent_id, type, is_web, is_back_stage,is_enable,parent_path,create_time,update_time,service_name,is_sys_data) values
        (#{id},#{name},#{path},#{description},#{status},#{method},
        #{parentId},#{type},#{isWeb},#{isBackStage},1,#{parentPath},#{createTime},#{updateTime},#{serviceName},#{isSysData})
    </insert>

    <insert id="addAppPermission">
        insert into sys_app_permission_info(id, name, path, description,
            status, method, parent_id, type, is_web, is_back_stage,is_enable,parent_path,create_time,update_time,is_sys_data) values
            (#{id},#{name},#{path},#{description},#{status},#{method},
            #{parentId},#{type},#{isWeb},#{isBackStage},1,#{parentPath},#{createTime},#{updateTime},#{isSysData})
    </insert>

    <select id="getPadPermissionListByUsername" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select d.*
        from sys_users a
                 join sys_user_role b on a.id = b.user_id
                 join sys_role_permission c on b.role_id = c.role_id
                 join sys_permissions d on c.permission_id = d.id
                 join sys_pad_permission p on c.permission_id = p.permission_id
        where a.user_name = #{username}
          and d.is_web = 0 and d.is_enable= 1
        group by d.`id`
    </select>

    <select id="getPermissionListByPadId" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select a.*
        from sys_pad_permission b
        left join sys_permissions a on b.permission_id = a.id
        where pad_id = #{padId}
    </select>

    <select id="getAllFirstPadPermission" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select *
        from sys_app_permission_info
        where is_web = 0
          and type = 0
    </select>
    <select id="selectAllAppPermission" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select *
        from sys_app_permission_info
    </select>

    <delete id="deleteUserPermissionBatch">
        delete
        from sys_role_permission
        where id not in (
            select t.max_id
            from (select max(id) as max_id from sys_role_permission group by role_id, permission_id) as t);
    </delete>

    <delete id="deleteRolePermissions">
        delete from sys_role_permission where
        permission_id in
        <foreach item="item" collection="permissionIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </delete>

    <select id="selectPadPermissionById" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select nn.*
        from sys_role_permission mm
                 left join sys_permissions nn on mm.permission_id = nn.id
        where role_id = #{roleId}
          and is_web = 0
    </select>
    <select id="selectWebPermissionById" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select nn.*
        from sys_role_permission mm
                 left join sys_permissions nn on mm.permission_id = nn.id
        where role_id = #{roleId}
          and is_web = 1
    </select>

    <select id="selectAllEnablePadPermission" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select * from sys_permissions
        where is_web = 0 and type = 2 or id in
        (SELECT DISTINCT parent_id FROM sys_permissions where is_web = 0 and type = 2)
        HAVING is_enable  = 1
</select>

    <select id="getListByAppPermissionIds" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        <if test="ids != null">
            select * from sys_app_permission_info where id in(
            <foreach item="item" collection="ids" separator=",">#{item}</foreach>)
            or parent_id in(
            <foreach item="item" collection="ids" separator=",">#{item}</foreach>)
        </if>
    </select>

    <select id="getAllButtonPermission" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select * from sys_app_permission_info where type = 2 and is_web = 0 and parent_id in(
        <foreach item="item" collection="someAppIds" separator=",">#{item}</foreach>
        )
    </select>
    <select id="selectPermissionParentId" resultType="java.lang.String">
        select parent_id from sys_app_permission_info where id = #{buttonPermission}
    </select>
    <select id="getAppPermissionListByRoleId" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select nn.*
        from sys_role_permission mm
        left join sys_permissions nn on mm.permission_id = nn.id
        where role_id = #{roleId} and is_web = 0 and is_enable = 1
    </select>

    <select id="getAppByAppId" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
         select * from sys_app_permission_info where `id` = #{appId}
    </select>

    <select id="getButtonByAppId" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select * from sys_app_permission_info where `parent_id` = #{appId} and `type` = 2 order by `sort`
    </select>

    <select id="initRolePermission">
        call init_new_role_permission(#{id})
    </select>

    <delete id="deleteAppPermissions">
        delete from sys_app_permission_info where
        `id` in
        <foreach item="item" collection="permissionIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        or `parent_id` in
        <foreach item="item" collection="permissionIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </delete>

</mapper>
