<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.user.DepartmentUserMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.user.DepartmentUserEntity">
        <result column="id" property="id"/>
        <result column="department_id" property="departmentId"/>
        <result column="user_id" property="userId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.departmentId != null and e.departmentId != '' ">
            AND t.department_id = #{e.departmentId}
        </if>
        <if test="e.userId != null and e.userId != '' ">
            AND t.user_id = #{e.userId}
        </if>
    </sql>

</mapper>