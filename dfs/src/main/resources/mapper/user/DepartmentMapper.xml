<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.user.DepartmentMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.user.DepartmentEntity">
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="responsible_name" property="responsibleName"/>
        <result column="pid" property="pid"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <resultMap id="selectMap" type="com.yelink.dfs.entity.user.DepartmentUserEntity">
        <result column="id" property="id"/>
        <result column="department_id" property="departmentId"/>
        <result column="user_id" property="userId"/>
        <result column="nick_name" property="nickname"/>
        <result column="user_name" property="username"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.departmentId != null and e.departmentId != '' ">
            AND t.department_id = #{e.departmentId}
        </if>
        <if test="e.departmentName != null and e.departmentName != '' ">
            AND t.department_name = #{e.departmentName}
        </if>
        <if test="e.responsibleName != null and e.responsibleName != '' ">
            AND t.responsible_name = #{e.responsibleName}
        </if>
        <if test="e.pid != null and e.pid != '' ">
            AND t.pid = #{e.pid}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>
</mapper>
