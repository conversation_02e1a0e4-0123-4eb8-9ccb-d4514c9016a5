<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.user.SysTeamMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.SysTeamEntity">
		    <result column="id" property="id" />
		    <result column="team_type" property="teamType" />
		    <result column="team_code" property="teamCode" />
		    <result column="team_name" property="teamName" />
		    <result column="remark" property="remark" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
			<result column="leader_name" property="leaderName" />
	</resultMap>

	<resultMap id="WorkOrderTeamMap" type="com.yelink.dfs.entity.order.WorkOrderTeamEntity">
		<result column="id" property="id"/>
		<result column="member_id" property="memberId"/>
		<result column="user_name" property="memberName"/>
		<result column="nick_name" property="memberNickName"/>
		<result column="team_role" property="teamRole"/>
		<result column="team_role_name" property="teamRoleName"/>
		<result column="post_ids" property="postIds"/>
		<result column="post_names" property="postNames"/>
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.teamType != null and e.teamType != '' ">
					AND t.team_type = #{e.teamType}
				</if>
				<if test="e.teamCode != null and e.teamCode != '' ">
					AND t.team_code = #{e.teamCode}
				</if>
				<if test="e.teamName != null and e.teamName != '' ">
					AND t.team_name = #{e.teamName}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.leaderName != null and e.leaderName != '' ">
					AND t.leader_name = #{e.leaderName}
				</if>
				<if test="e.workCenterId != null and e.workCenterId != '' ">
					AND t.work_center_id = #{e.workCenterId}
				</if>
	</sql>

	<select id="getDefaultPersonnelList" resultMap="WorkOrderTeamMap">
		<!--班组长信息-->
		select u.id as member_id, u.nick_name,u.user_name, 1 as team_role, '班组长' as team_role_name, l.post_ids, l.post_names
		from sys_team t
		left join
		(select aa.user_id, aa.user_name, GROUP_CONCAT(CONCAT(bb.post_name,bb.post_level)) as post_names,
		GROUP_CONCAT(bb.id) as post_ids from sys_post_user aa
		left join sys_post bb on aa.post_id = bb.id
		GROUP BY aa.user_id) l on l.user_name = t.leader_name
		left join sys_users u on u.user_name = t.leader_name
		where t.id in
		<foreach item="teamId" collection="teamIds" separator="," open="(" close=")" index="">
			#{teamId}
		</foreach>
		union all
		<!--组员信息-->
		select u.id as member_id, u.nick_name,u.user_name, 2 as team_role, '组员' as team_role_name, n.post_ids, n.post_names
		from sys_team_user tu
		left join sys_team t on t.id = tu.team_id
		left join
		(select aa.user_id, aa.user_name, GROUP_CONCAT(CONCAT(bb.post_name,bb.post_level)) as post_names,
		GROUP_CONCAT(bb.id) as post_ids from sys_post_user aa
		left join sys_post bb on aa.post_id = bb.id
		GROUP BY aa.user_id) n on n.user_id = tu.user_id
		left join sys_users u on tu.user_id = u.id
		where t.id in
		<foreach item="teamId" collection="teamIds" separator="," open="(" close=")" index="">
			#{teamId}
		</foreach>
	</select>
	<select id="getPostInfos" resultMap="WorkOrderTeamMap">
		select u.id as member_id, u.nick_name,u.user_name, l.post_ids, l.post_names
		from sys_users u
		left join
		(select aa.user_id, aa.user_name, GROUP_CONCAT(CONCAT(bb.post_name,bb.post_level)) as post_names, GROUP_CONCAT(bb.id) as post_ids from sys_post_user aa
		left join sys_post bb on aa.post_id = bb.id
		GROUP BY aa.user_id) l on l.user_id = u.id
		where u.id in
		<foreach item="item" collection="userIdList" separator="," open="(" close=")" index="">
			#{item}
		</foreach>
	</select>
</mapper>