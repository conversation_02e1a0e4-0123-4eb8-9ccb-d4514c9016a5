<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.weight.WeightTargetMapper">

    <resultMap id="ResultExtendMap" type="com.yelink.dfs.entity.weight.WeightTargetExtendEntity">
        <id column="id" property="id"/>
        <result column="work_order_code" property="workOrderCode" />
        <result column="feeding_weight_value" property="feedingWeightValue" />
        <result column="blank_weight_value" property="blankWeightValue" />
        <result column="waste_weight_value" property="wasteWeightValue" />
        <result column="theory_output_rate" property="theoryOutputRate" />
        <result column="actual_output_rate" property="actualOutputRate" />
        <result column="output_compliance_rate" property="outputComplianceRate" />
        <result column="theory_waste_rate" property="theoryWasteRate" />
        <result column="actual_waste_rate" property="actualWasteRate" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="product_code" property="productCode" />
        <result column="product_name" property="productName" />
        <result column="plan_quantity" property="planQuantity" />
        <result column="finish_count" property="finishCount" />
        <result column="unqualified" property="unqualified" />
    </resultMap>
    <select id="pageBy" resultMap="ResultExtendMap"  parameterType="com.yelink.dfs.controller.weight.dto.WeightTargetPageDto">

        SELECT
            a.*,
            b.work_order_name as workOrderName,
            b.material_code AS product_code,
            m.`name` AS product_name,
            b.plan_quantity,
            b.finish_count,
            b.unqualified,
            b.start_date as planStartTime,
            b.end_date as planEndTime
        FROM
            dfs_weight_target a
                LEFT JOIN dfs_work_order b ON a.work_order_code = b.work_order_number
                LEFT JOIN dfs_material m ON m.`code` = b.material_code
        where 1 = 1
        <if test="weightTargetPageDto.workOrderCode != null and weightTargetPageDto.workOrderCode != '' ">
            and a.work_order_code like concat('%',#{weightTargetPageDto.workOrderCode},'%')
        </if>

        <if test="weightTargetPageDto.productCode != null and weightTargetPageDto.productCode != '' ">
            and b.material_code like concat('%',#{weightTargetPageDto.productCode},'%')
        </if>

        <if test="weightTargetPageDto.productName != null and weightTargetPageDto.productName != '' ">
            and m.`name` like concat('%',#{weightTargetPageDto.productName},'%')
        </if>

    </select>
</mapper>
