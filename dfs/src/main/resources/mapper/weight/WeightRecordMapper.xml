<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.weight.WeightRecordMapper">

    <resultMap id="ResultExtendMap" type="com.yelink.dfs.entity.weight.WeightRecordExtendEntity">
        <id column="id" property="id"/>
        <result column="work_order_code" property="workOrderCode" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="batch_code" property="batchCode" />
        <result column="weight_type" property="weightType"/>
        <result column="weight_value" property="weightValue" />
        <result column="operator" property="operator" />
        <result column="operator_time" property="operatorTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="create_by" property="createBy" />
        <result column="product_code" property="productCode" />
        <result column="product_name" property="productName" />
        <result column="plan_quantity" property="planQuantity" />
        <result column="finish_count" property="finishCount" />
        <result column="unqualified" property="unqualified" />
    </resultMap>



    <select id="pageBy" resultMap="ResultExtendMap" parameterType="com.yelink.dfs.controller.weight.dto.WeightRecordPageDto">

        SELECT
        a.*, b.material_code AS product_code,
        m.`name` AS product_name,
        b.plan_quantity,
        b.finish_count,
        b.unqualified
        FROM
        dfs_weight_record a
        LEFT JOIN dfs_work_order b ON a.work_order_code = b.work_order_number
        LEFT JOIN dfs_material m ON m.`code` = b.material_code
        where 1 = 1
        <if test="weightRecordPageDto.workOrderCode != null and weightRecordPageDto.workOrderCode != '' ">
            and a.work_order_code like concat('%',#{weightRecordPageDto.workOrderCode},'%')
        </if>
        <if test="weightRecordPageDto.productCode != null and weightRecordPageDto.productCode != '' ">
            and b.material_code like concat('%',#{weightRecordPageDto.productCode},'%')
        </if>
        <if test="weightRecordPageDto.productName != null and weightRecordPageDto.productName != '' ">
            and m.`name` like concat('%',#{weightRecordPageDto.productName},'%')
        </if>
        <if test="weightRecordPageDto.materialCode != null and weightRecordPageDto.materialCode != '' ">
            and a.material_code like concat('%',#{weightRecordPageDto.materialCode},'%')
        </if>
        <if test="weightRecordPageDto.materialName != null and weightRecordPageDto.materialName != '' ">
            and a.material_name like concat('%',#{weightRecordPageDto.materialName},'%')
        </if>
        <if test="weightRecordPageDto.materialType != null">
            and a.material_code in (SELECT `code` FROM dfs_material WHERE type = #{weightRecordPageDto.materialType})
        </if>
        <if test="weightRecordPageDto.weightType != null">
            and a.weight_type like concat('%',#{weightRecordPageDto.weightType},'%')
        </if>
        <if test="weightRecordPageDto.operatorStartTime != null and weightRecordPageDto.operatorStartTime != ''
                  and weightRecordPageDto.operatorEndTime != null and weightRecordPageDto.operatorEndTime != ''">
            and a.operator_time BETWEEN #{weightRecordPageDto.operatorStartTime} and #{weightRecordPageDto.operatorEndTime}
        </if>

    </select>
</mapper>
