<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.task.TaskMapper">

    <resultMap id="TaskOverviewCountResultMap" type="com.yelink.dfs.entity.task.dto.TaskOverviewCountDTO">
        <result column="total" property="total"/>
        <result column="progress_count" property="progressCount"/>
        <result column="complete_count" property="completeCount"/>
    </resultMap>

    <select id="getTaskOverviewCount" resultMap="TaskOverviewCountResultMap">
        SELECT 
            COUNT(1) as total,
            SUM(CASE WHEN t.task_state = 'inProgress' THEN 1 ELSE 0 END) as progress_count,
            SUM(CASE WHEN t.task_state = 'complete' THEN 1 ELSE 0 END) as complete_count
        FROM dfs_task t
        <where>
            <if test="orderCategory != null and orderCategory != ''">
                AND t.order_category = #{orderCategory}
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                AND t.order_number = #{orderNumber}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND t.material_code = #{materialCode}
            </if>
            <if test="materialLineId != null">
                AND t.material_line_id = #{materialLineId}
            </if>
            <if test="taskStates != null and taskStates.size() > 0">
                AND t.task_state IN
                <foreach collection="taskStates" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="upstreamOrderCategory != null and upstreamOrderCategory != ''">
                AND t.upstream_order_category = #{upstreamOrderCategory}
            </if>
            <if test="upstreamOrderNumber != null and upstreamOrderNumber != ''">
                AND t.upstream_order_number = #{upstreamOrderNumber}
            </if>
            <if test="upstreamMaterialLineId != null">
                AND t.upstream_material_line_id = #{upstreamMaterialLineId}
            </if>
            <if test="orderState != null and orderState != ''">
                AND t.order_state = #{orderState}
            </if>
            <if test="rootOrderCategory != null and rootOrderCategory != ''">
                AND t.root_order_category = #{rootOrderCategory}
            </if>
            <if test="rootOrderNumber != null and rootOrderNumber != ''">
                AND t.root_order_number = #{rootOrderNumber}
            </if>
            <if test="rootMaterialLineId != null">
                AND t.root_material_line_id = #{rootMaterialLineId}
            </if>
            <!-- 角色过滤：按单据类型 -->
            <if test="roleOrderCategories != null and roleOrderCategories.size() > 0">
                AND t.order_category IN
                <foreach collection="roleOrderCategories" item="category" open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>
            <!-- 关系过滤：按任务ID -->
            <if test="relationTaskIds != null and relationTaskIds.size() > 0">
                AND t.task_id IN
                <foreach collection="relationTaskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
