<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yelink.dfs.mapper.terminal.SysPadBindPermissionMapper">
    <resultMap id="selectMap" type="com.yelink.dfs.entity.terminal.SysPadBindPermissionEntity">
        <id column="id" property="id"/>
        <result column="permissionId" property="permissionId"/>
        <result column="padId" property="padId"/>
    </resultMap>
    <update id="bandPadPermission" parameterType="list">
        insert into sys_pad_permission(pad_id,permission_id) values
        <foreach collection="list" separator="," item="item">
            (#{item.padId},#{item.permissionId})
        </foreach>
    </update>
    <delete id="deletePermissionByPadId">
        delete from sys_pad_permission where pad_id=#{padId}
    </delete>

</mapper>