<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yelink.dfs.mapper.terminal.SysPadMapper">
    <resultMap id="selectMap" type="com.yelink.dfs.entity.terminal.SysPadEntity">
        <id column="id" property="id"/>
        <result column="mac" property="mac"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="free_login" property="freeLogin"/>
    </resultMap>

    <update id="updatePad" parameterType="com.yelink.dfs.entity.terminal.SysPadEntity">
        update sys_pads
        set mac=#{mac},
        description=#{description},
        name =#{name},
        free_login =#{freeLogin}
        where id = #{id}
    </update>
    <delete id="deleteByPadId">
        delete
        from sys_pads
        where id = #{padId}
    </delete>
    <select id="getPadPermission" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select a.id,a.name from sys_permissions a
		join sys_pad_permission b on a.id = b.permission_id
		join sys_pads c on c.id = b.pad_id
		where is_web = 0
		and a.type = 0
		and c.id = #{id}
    </select>
    <select id="selectByPadMac" resultType="com.yelink.dfs.entity.terminal.SysPadEntity">
        select * from sys_pads where mac = #{mac}
    </select>
    <select id="getCrossPadPermission" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select d.* from sys_users a
        join sys_user_role b on a.id = b.user_id
        join sys_role_permission c on b.role_id = c.role_id
        join sys_permissions d on c.permission_id = d.id
		join sys_pad_permission p on c.permission_id = p.permission_id
		join sys_pads e on p.pad_id = e.id
        where a.user_name = #{username}
		and e.id = #{id}
        and d.is_web = 0
		and d.type = 0
        group by d.`id`
    </select>
    <select id="getPadPermissionList" resultType="com.yelink.dfs.entity.user.SysPermissionEntity">
        select d.* from sys_users a
        join sys_user_role b on a.id = b.user_id
        join sys_role_permission c on b.role_id = c.role_id
        join sys_permissions d on c.permission_id = d.id
		join(select d.* from sys_pads a
        join sys_pad_permission c on a.id = c.pad_id
        join sys_permissions d on c.permission_id = d.id
        where a.mac = #{mac}) e on d.id = e.id
        where a.user_name = #{username}
        group by d.`id`
		having is_web = 0
    </select>

    <select id="selectPadListByRoleId" resultType="com.yelink.dfs.entity.terminal.SysPadEntity">
		SELECT p.*
        FROM sys_pads p
                 LEFT JOIN sys_pad_role r ON r.pad_id = p.id
        WHERE r.role_id = #{id}
    </select>

    <select id="selectByIds" resultType="com.yelink.dfs.entity.terminal.SysPadEntity">
        SELECT * FROM sys_pads
        WHERE id in
        <foreach item="item" collection="roleIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

</mapper>