<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.energy.manage.BaseDeviceConsumptionMapper">


    <select id="consumptionMonthPage" resultType="com.yelink.dfs.entity.energy.manage.vo.ConsumptionVO">
		select
		    DATE_FORMAT(record_date, '%Y-%m') AS recordDateStr,
			device_id,
			device_code,
			device_name,
			sum(consumption) AS consumption,
			min(start_consumption) AS startConsumption,
			max(end_consumption) AS endConsumption,
			min(start_time) AS startTime,
			max(end_time) AS endTime
		from ${dto.tableName}
		<where>
			<if test="dto.deviceIds != null and dto.deviceIds.size > 0">
				AND device_id in
				<foreach item="item" collection="dto.deviceIds" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			AND record_date between #{dto.start} and #{dto.end}
		</where>
		group by recordDateStr, device_id
	</select>

</mapper>
