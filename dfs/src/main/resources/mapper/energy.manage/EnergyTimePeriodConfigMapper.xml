<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.energy.manage.EnergyTimePeriodConfigMapper">

    <select id="countEffectiveTimeRecord" resultType="integer">
        SELECT COUNT(*) FROM dfs_energy_time_interval
        WHERE relate_id in (SELECT id FROM dfs_energy_time_period_config <if test="excludedId != null"> where id != #{excludedId} </if>)
        AND
        <foreach collection="effectiveTimes" item="effectiveTime" open="(" close=")" separator="OR">
            (#{effectiveTime.startTime} >= start_time AND #{effectiveTime.startTime} <![CDATA[<=]]> end_time) OR
            (#{effectiveTime.endTime} >= start_time AND #{effectiveTime.endTime} <![CDATA[<=]]> end_time) OR
            (#{effectiveTime.startTime} <![CDATA[<=]]> start_time AND #{effectiveTime.endTime} >= end_time)
        </foreach>
    </select>

</mapper>
