<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.energy.manage.DeviceTimePeriodEnergyConsumptionMapper">
    <insert id="saveOrUpdateRecord">
        INSERT INTO dfs_device_time_period_energy_consumption(device_id, device_code, eui, name_type,
                                                              consumption, record_date)
        VALUES (#{record.deviceId}, #{record.deviceCode}, #{record.eui}, #{record.nameType},
                #{record.consumption}, #{record.recordDate})
        ON DUPLICATE KEY UPDATE consumption = consumption + #{record.consumption};
    </insert>


    <select id="selectDeviceTimePeriodEnergyConsumptionInfo" resultType="com.yelink.dfs.entity.statement.dto.ProductionDailyResultDTO$DeviceUseElectricityRecord">
        SELECT
	ddtpec.record_date,                                                            <!-- 设备用电表：日期 -->
	dg.gname,                                                                      <!-- 车间表：车间名称 -->
	sum(ddtpec.consumption) AS 'consumption',                                      <!-- 设备用电表：能耗 -->
	sum(case when ddtpec.name_type = 0 then ddtpec.consumption else 0 end) 'jian', <!-- 纵向转横向 -->
	sum(case when ddtpec.name_type = 1 then ddtpec.consumption else 0 end) 'feng', <!-- 纵向转横向 -->
	sum(case when ddtpec.name_type = 2 then ddtpec.consumption else 0 end) 'ping', <!-- 纵向转横向 -->
	sum(case when ddtpec.name_type = 3 then ddtpec.consumption else 0 end) 'gu'    <!-- 纵向转横向 -->
    FROM
        dfs_device_time_period_energy_consumption AS ddtpec
    left JOIN dfs_device AS dd ON dd.device_id = ddtpec.device_id                 <!-- 设备表device_id = 用电表device_id （设备表的device_id是中间链接作用） -->
    left JOIN dfs_grid dg ON dg.gid = dd.gid                                      <!-- 车间表gid =  设备表gid （主要是想拿到车间名称） -->
    WHERE
        1 = 1
    <if test="startTime != null and endTime != null">
            AND ddtpec.record_date >= #{startTime}
            AND ddtpec.record_date <![CDATA[<=]]>  #{endTime}
    </if>
        GROUP BY dg.gid,ddtpec.record_date
        ORDER BY ddtpec.record_date DESC;
    </select>

</mapper>
