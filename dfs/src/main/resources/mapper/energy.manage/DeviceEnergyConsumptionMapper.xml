<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.energy.manage.DeviceEnergyConsumptionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.energy.manage.DeviceEnergyConsumptionEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_name" property="deviceName"/>
        <result column="eui" property="eui"/>
        <result column="consumption" property="consumption"/>
        <result column="record_date" property="recordDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.deviceCode != null and e.deviceCode != '' ">
            AND t.device_code = #{e.deviceCode}
        </if>
        <if test="e.deviceName != null and e.deviceName != '' ">
            AND t.device_name = #{e.deviceName}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.consumption != null and e.consumption != '' ">
            AND t.consumption = #{e.consumption}
        </if>
        <if test="e.recordDate != null and e.recordDate != '' ">
            AND t.record_date = #{e.recordDate}
        </if>
    </sql>

    <select id="getByDeviceCodeByTimeRange" resultMap="BaseResultMap">
		select max(device_id) device_id,max(device_code) device_code,max(device_name) device_name,sum(consumption) consumption
		from dfs_device_energy_consumption
		where device_code=#{deviceCode} and record_date between #{startTime} and #{endTime} group by device_code
	</select>

</mapper>
