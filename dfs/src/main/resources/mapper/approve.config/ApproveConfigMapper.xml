<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.approve.config.ApproveConfigMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.approve.config.ApproveConfigEntity">
		    <result column="id" property="id" />
		    <result column="code" property="code" />
		    <result column="name" property="name" />
		    <result column="is_approve" property="isApprove" />
		    <result column="update_by" property="updateBy" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.code != null and e.code != '' ">
					AND t.code = #{e.code}
				</if>
				<if test="e.name != null and e.name != '' ">
					AND t.name = #{e.name}
				</if>
				<if test="e.isApprove != null and e.isApprove != '' ">
					AND t.is_approve = #{e.isApprove}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>