<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.synchronize.SynchronizeConfigMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.synchronize.SynchronizeConfigEntity">
		    <result column="id" property="id" />
		    <result column="url" property="url" />
		    <result column="dbId" property="dbId" />
		    <result column="user" property="user" />
		    <result column="password" property="password" />
		    <result column="lang" property="lang" />
		    <result column="time_cron" property="timeCron" />
		    <result column="update_by" property="updateBy" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.url != null and e.url != '' ">
					AND t.url = #{e.url}
				</if>
				<if test="e.dbId != null and e.dbId != '' ">
					AND t.dbId = #{e.dbId}
				</if>
				<if test="e.user != null and e.user != '' ">
					AND t.user = #{e.user}
				</if>
				<if test="e.password != null and e.password != '' ">
					AND t.password = #{e.password}
				</if>
				<if test="e.lang != null and e.lang != '' ">
					AND t.lang = #{e.lang}
				</if>
				<if test="e.timeCron != null and e.timeCron != '' ">
					AND t.time_cron = #{e.timeCron}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>