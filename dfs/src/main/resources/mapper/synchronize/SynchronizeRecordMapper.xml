<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.synchronize.SynchronizeRecordMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.synchronize.SynchronizeRecordEntity">
		    <result column="id" property="id" />
		    <result column="ename" property="ename" />
		    <result column="model_name" property="modelName" />
		    <result column="des" property="des" />
		    <result column="is_success" property="isSuccess" />
		    <result column="time" property="time" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.ename != null and e.ename != '' ">
					AND t.table = #{e.table}
				</if>
				<if test="e.modelName != null and e.modelName != '' ">
					AND t.name = #{e.name}
				</if>
				<if test="e.des != null and e.des != '' ">
					AND t.describe = #{e.describe}
				</if>
				<if test="e.isSuccess != null and e.isSuccess != '' ">
					AND t.is_success = #{e.isSuccess}
				</if>
				<if test="e.time != null and e.time != '' ">
					AND t.time = #{e.time}
				</if>
	</sql>

</mapper>