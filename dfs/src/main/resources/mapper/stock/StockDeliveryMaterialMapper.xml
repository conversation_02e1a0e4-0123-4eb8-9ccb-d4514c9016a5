<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.stock.StockDeliveryMaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.stock.StockDeliveryMaterialEntity">
        <result column="id" property="id"/>
        <result column="delivery_id" property="deliveryId"/>
        <result column="materiel_code" property="materielCode"/>
        <result column="materiel_name" property="materielName"/>
        <result column="amount" property="amount"/>
        <result column="unit" property="unit"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deliveryId != null and e.deliveryId != '' ">
            AND t.delivery_id = #{e.deliveryId}
        </if>
        <if test="e.materielCode != null and e.materielCode != '' ">
            AND t.materiel_code = #{e.materielCode}
        </if>
        <if test="e.materielName != null and e.materielName != '' ">
            AND t.materiel_name = #{e.materielName}
        </if>
        <if test="e.amount != null and e.amount != '' ">
            AND t.amount = #{e.amount}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
    </sql>

</mapper>