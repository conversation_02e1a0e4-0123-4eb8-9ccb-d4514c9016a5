<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.stock.StockDeliveryMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.stock.StockDeliveryEntity">
        <result column="delivery_id" property="deliveryId"/>
        <result column="send_number" property="sendNumber"/>
        <result column="output_order" property="outputOrder"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_address" property="customerAddress"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="sender" property="sender"/>
        <result column="sender_phone" property="senderPhone"/>
        <result column="sender_address" property="senderAddress"/>
        <result column="state" property="state"/>
        <result column="remark" property="remark"/>
        <result column="plan_time" property="planTime"/>
        <result column="actual_time" property="actualTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.deliveryId != null and e.deliveryId != '' ">
            AND t.delivery_id = #{e.deliveryId}
        </if>
        <if test="e.sendNumber != null and e.sendNumber != '' ">
            AND t.send_number = #{e.sendNumber}
        </if>
        <if test="e.outputOrder != null and e.outputOrder != '' ">
            AND t.output_order = #{e.outputOrder}
        </if>
        <if test="e.customerName != null and e.customerName != '' ">
            AND t.customer_name = #{e.customerName}
        </if>
        <if test="e.customerAddress != null and e.customerAddress != '' ">
            AND t.customer_address = #{e.customerAddress}
        </if>
        <if test="e.customerPhone != null and e.customerPhone != '' ">
            AND t.customer_phone = #{e.customerPhone}
        </if>
        <if test="e.sender != null and e.sender != '' ">
            AND t.sender = #{e.sender}
        </if>
        <if test="e.senderPhone != null and e.senderPhone != '' ">
            AND t.sender_phone = #{e.senderPhone}
        </if>
        <if test="e.senderAddress != null and e.senderAddress != '' ">
            AND t.sender_address = #{e.senderAddress}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.planSendTime != null and e.planSendTime != '' ">
            AND t.plan_time = #{e.planTime}
        </if>
        <if test="e.actualSendTime != null and e.actualSendTime != '' ">
            AND t.actual_time = #{e.actualTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
    </sql>

</mapper>