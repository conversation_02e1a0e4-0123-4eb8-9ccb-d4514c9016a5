<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.stock.DeliveryApplicationMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.stock.DeliveryApplicationEntity">
        <result column="id" property="id"/>
        <result column="application_num" property="applicationNum"/>
        <result column="sale_order_id" property="saleOrderId"/>
        <result column="sale_order_number" property="saleOrderNumber"/>
        <result column="approver" property="approver"/>
        <result column="plan_delivery_time" property="planDeliveryTime"/>
        <result column="state" property="state"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.applicationNum != null and e.applicationNum != '' ">
            AND t.application_num = #{e.applicationNum}
        </if>
        <if test="e.saleOrderId != null and e.saleOrderId != '' ">
            AND t.sale_order_id = #{e.saleOrderId}
        </if>
        <if test="e.saleOrderNumber != null and e.saleOrderNumber != '' ">
            AND t.sale_order_number = #{e.saleOrderNumber}
        </if>
        <if test="e.reviewer != null and e.reviewer != '' ">
            AND t.reviewer = #{e.reviewer}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.planDeliveryTime != null and e.planDeliveryTime != '' ">
            AND t.plan_delivery_time = #{e.planDeliveryTime}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
    </sql>
    <select id="getList" resultType="com.yelink.dfs.entity.stock.DeliveryApplicationEntity">
        select * from dfs_delivery_application where id in
        (
        SELECT
        deliveryApplicationEntity.id
        FROM
        dfs_delivery_application deliveryApplicationEntity
        <if test="sql != null and sql != ''">
            <if test="sql.contains('deliveryApplicationMaterialEntity') or sql.contains('material')  ">
                LEFT JOIN dfs_delivery_application_material deliveryApplicationMaterialEntity ON deliveryApplicationEntity.id = deliveryApplicationMaterialEntity.delivery_application_id
                INNER JOIN dfs_material material
                ON deliveryApplicationMaterialEntity.material_code = material.code
            </if>
            ${sql}
        </if>
        )
    </select>
    <select id="materialsList" resultType="com.yelink.dfs.open.v2.order.dto.DeliveryApplicationMaterialVO">
        SELECT
        deliveryApplicationMaterialEntity.*
        FROM
        dfs_delivery_application deliveryApplicationEntity
        LEFT JOIN dfs_delivery_application_material deliveryApplicationMaterialEntity ON deliveryApplicationEntity.id = deliveryApplicationMaterialEntity.delivery_application_id
        INNER JOIN dfs_material material
        ON deliveryApplicationMaterialEntity.material_code = material.code
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>