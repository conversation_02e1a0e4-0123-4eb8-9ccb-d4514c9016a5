<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.stock.DeliveryApplicationMaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.stock.DeliveryApplicationMaterialEntity">
        <result column="id" property="id"/>
        <result column="delivery_application_id" property="deliveryApplicationId"/>
        <result column="delivery_application_num" property="deliveryApplicationNum"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="amount" property="amount"/>
        <result column="unit" property="unit"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deliveryApplicationId != null and e.deliveryApplicationId != '' ">
            AND t.delivery_application_id = #{e.deliveryApplicationId}
        </if>
        <if test="e.deliveryApplicationNum != null and e.deliveryApplicationNum != '' ">
            AND t.delivery_application_num = #{e.deliveryApplicationNum}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.amount != null and e.amount != '' ">
            AND t.amount = #{e.amount}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>