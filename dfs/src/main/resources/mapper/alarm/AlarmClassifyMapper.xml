<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.alarm.AlarmClassifyMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.alarm.AlarmClassifyEntity">
		    <result column="alarm_classify_id" property="alarmClassifyId" />
		    <result column="alarm_classify_name" property="alarmClassifyName" />
		    <result column="alarm_classify_desc" property="alarmClassifyDesc" />
			<result column="update_by" property="updateBy"/>
			<result column="create_by" property="createBy"/>
			<result column="update_time" property="updateTime"/>
			<result column="create_time" property="createTime"/>
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.alarmClassifyId != null and e.alarmClassifyId != '' ">
					AND t.alarm_classify_id = #{e.alarmClassifyId}
				</if>
				<if test="e.alarmClassifyName != null and e.alarmClassifyName != '' ">
					AND t.alarm_classify_name = #{e.alarmClassifyName}
				</if>
				<if test="e.alarmClassifyDesc != null and e.alarmClassifyDesc != '' ">
					AND t.alarm_classify_desc = #{e.alarmClassifyDesc}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>