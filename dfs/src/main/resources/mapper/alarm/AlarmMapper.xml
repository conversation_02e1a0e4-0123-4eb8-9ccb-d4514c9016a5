<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.alarm.AlarmMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.alarm.AlarmEntity">
        <result column="alarm_id" property="alarmId"/>
        <result column="alarm_code_id" property="alarmCodeId"/>
        <result column="aggregate_code" property="aggregateCode"/>
        <result column="alarm_value" property="alarmValue"/>
        <result column="alarm_des" property="alarmDes"/>
        <result column="alarm_time" property="alarmTime"/>
        <result column="alarm_report_time" property="alarmReportTime"/>
        <result column="alarm_type_number" property="alarmTypeNumber"/>
        <result column="alarm_type_name" property="alarmTypeName"/>
        <result column="target_model_id" property="targetModelId"/>
        <result column="deal_name" property="dealName"/>
        <result column="deal_phone" property="dealPhone"/>
        <result column="deal_time" property="dealTime"/>
        <result column="deal_des" property="dealDes"/>
        <result column="deal_result" property="dealResult"/>
        <result column="deal_img_url" property="dealImgUrl"/>
        <result column="deal_state" property="dealState"/>
        <result column="cid" property="cid"/>
        <result column="cname" property="cname"/>
        <result column="caddr" property="caddr"/>
        <result column="cmag_name" property="cmagName"/>
        <result column="cmag_nick_name" property="cmagNickName"/>
        <result column="cmag_phone" property="cmagPhone"/>
        <result column="aid" property="aid"/>
        <result column="acode" property="acode"/>
        <result column="aname" property="aname"/>
        <result column="aaddr" property="aaddr"/>
        <result column="amag_name" property="amagName"/>
        <result column="amag_nick_name" property="amagNickName"/>
        <result column="amag_phone" property="amagPhone"/>
        <result column="gid" property="gid"/>
        <result column="gcode" property="gcode"/>
        <result column="gname" property="gname"/>
        <result column="gaddr" property="gaddr"/>
        <result column="gmag_name" property="gmagName"/>
        <result column="gmag_nick_name" property="gmagNickName"/>
        <result column="gmag_phone" property="gmagPhone"/>
        <result column="production_line_id" property="productionLineId"/>
        <result column="production_line_code" property="productionLineCode"/>
        <result column="production_line_name" property="productionLineName"/>
        <result column="production_line_place" property="productionLinePlace"/>
        <result column="production_line_mag_name" property="productionLineMagName"/>
        <result column="production_line_mag_nick_name" property="productionLineMagNickName"/>
        <result column="production_line_mag_phone" property="productionLineMagPhone"/>
        <result column="subline_id" property="sublineId"/>
        <result column="subline_name" property="sublineName"/>
        <result column="fid" property="fid"/>
        <result column="fcode" property="fcode"/>
        <result column="fname" property="fname"/>
        <result column="flat" property="flat"/>
        <result column="flng" property="flng"/>
        <result column="faddr" property="faddr"/>
        <result column="fimg_url" property="fimgUrl"/>
        <result column="fmag_name" property="fmagName"/>
        <result column="fmag_nick_name" property="fmagNickName"/>
        <result column="fmag_phone" property="fmagPhone"/>
        <result column="eui" property="eui"/>
        <result column="stype" property="stype"/>
        <result column="stype_name" property="stypeName"/>
        <result column="sname" property="sname"/>
        <result column="slat" property="slat"/>
        <result column="slng" property="slng"/>
        <result column="salt" property="salt"/>
        <result column="salarm_threshold" property="salarmThreshold"/>
        <result column="saddr" property="saddr"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_level_name" property="alarmLevelName"/>
        <result column="alarm_advice" property="alarmAdvice"/>
        <result column="alarm_advice_des" property="alarmAdviceDes"/>
        <result column="target_name" property="targetName"/>
        <result column="target_cnname" property="targetCnname"/>
        <result column="model_type" property="modelType"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_place" property="devicePlace"/>
        <result column="device_mag_name" property="deviceMagName"/>
        <result column="device_mag_nick_name" property="deviceMagNickName"/>
        <result column="device_mag_phone" property="deviceMagPhone"/>
        <result column="alarm_add_type" property="alarmAddType"/>
        <result column="photo_url" property="photoUrl"/>
        <result column="alarm_detail" property="alarmDetail"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_recovery_time" property="alarmRecoveryTime"/>
        <result column="alarm_recovery_type" property="alarmRecoveryType"/>
        <result column="alarm_update_time" property="alarmUpdateTime"/>
        <result column="alarm_definition_code" property="alarmDefinitionCode"/>
        <result column="alarm_report_personnel" property="alarmReportPersonnel"/>
        <result column="alarm_classify_name" property="alarmClassifyName"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="last_upgrade_time" property="lastUpgradeTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.alarmId != null and e.alarmId != '' ">
            AND t.alarm_id = #{e.alarmId}
        </if>
        <if test="e.alarmCodeId != null and e.alarmCodeId != '' ">
            AND t.alarm_code_id = #{e.alarmCodeId}
        </if>
        <if test="e.aggregateCode != null and e.aggregateCode != '' ">
            AND t.aggregate_code = #{e.aggregateCode}
        </if>
        <if test="e.alarmValue != null and e.alarmValue != '' ">
            AND t.alarm_value = #{e.alarmValue}
        </if>
        <if test="e.alarmDes != null and e.alarmDes != '' ">
            AND t.alarm_des = #{e.alarmDes}
        </if>
        <if test="e.alarmTime != null and e.alarmTime != '' ">
            AND t.alarm_time = #{e.alarmTime}
        </if>
        <if test="e.alarmReportTime != null and e.alarmReportTime != '' ">
            AND t.alarm_report_time = #{e.alarmReportTime}
        </if>
        <if test="e.alarmTypeNumber != null and e.alarmTypeNumber != '' ">
            AND t.alarm_type_number = #{e.alarmTypeNumber}
        </if>
        <if test="e.alarmTypeName != null and e.alarmTypeName != '' ">
            AND t.alarm_type_name = #{e.alarmTypeName}
        </if>
        <if test="e.targetModelId != null and e.targetModelId != '' ">
            AND t.target_model_id = #{e.targetModelId}
        </if>
        <if test="e.dealName != null and e.dealName != '' ">
            AND t.deal_name = #{e.dealName}
        </if>
        <if test="e.dealPhone != null and e.dealPhone != '' ">
            AND t.deal_phone = #{e.dealPhone}
        </if>
        <if test="e.dealTime != null and e.dealTime != '' ">
            AND t.deal_time = #{e.dealTime}
        </if>
        <if test="e.dealDes != null and e.dealDes != '' ">
            AND t.deal_des = #{e.dealDes}
        </if>
        <if test="e.dealResult != null and e.dealResult != '' ">
            AND t.deal_result = #{e.dealResult}
        </if>
        <if test="e.dealImgUrl != null and e.dealImgUrl != '' ">
            AND t.deal_img_url = #{e.dealImgUrl}
        </if>
        <if test="e.dealState != null and e.dealState != '' ">
            AND t.deal_state = #{e.dealState}
        </if>
        <if test="e.cid != null and e.cid != '' ">
            AND t.cid = #{e.cid}
        </if>
        <if test="e.cname != null and e.cname != '' ">
            AND t.cname = #{e.cname}
        </if>
        <if test="e.caddr != null and e.caddr != '' ">
            AND t.caddr = #{e.caddr}
        </if>
        <if test="e.cmagName != null and e.cmagName != '' ">
            AND t.cmag_name = #{e.cmagName}
        </if>
        <if test="e.cmagNickName != null and e.cmagNickName != '' ">
            AND t.cmag_nick_name = #{e.cmagNickName}
        </if>
        <if test="e.cmagPhone != null and e.cmagPhone != '' ">
            AND t.cmag_phone = #{e.cmagPhone}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.acode != null and e.acode != '' ">
            AND t.acode = #{e.acode}
        </if>
        <if test="e.aname != null and e.aname != '' ">
            AND t.aname = #{e.aname}
        </if>
        <if test="e.aaddr != null and e.aaddr != '' ">
            AND t.aaddr = #{e.aaddr}
        </if>
        <if test="e.amagName != null and e.amagName != '' ">
            AND t.amag_name = #{e.amagName}
        </if>
        <if test="e.amagNickName != null and e.amagNickName != '' ">
            AND t.amag_nick_name = #{e.amagNickName}
        </if>
        <if test="e.amagPhone != null and e.amagPhone != '' ">
            AND t.amag_phone = #{e.amagPhone}
        </if>
        <if test="e.gid != null and e.gid != '' ">
            AND t.gid = #{e.gid}
        </if>
        <if test="e.gcode != null and e.gcode != '' ">
            AND t.gcode = #{e.gcode}
        </if>
        <if test="e.gname != null and e.gname != '' ">
            AND t.gname = #{e.gname}
        </if>
        <if test="e.gaddr != null and e.gaddr != '' ">
            AND t.gaddr = #{e.gaddr}
        </if>
        <if test="e.gmagName != null and e.gmagName != '' ">
            AND t.gmag_name = #{e.gmagName}
        </if>
        <if test="e.gmagNickName != null and e.gmagNickName != '' ">
            AND t.gmag_nick_name = #{e.gmagNickName}
        </if>
        <if test="e.gmagPhone != null and e.gmagPhone != '' ">
            AND t.gmag_phone = #{e.gmagPhone}
        </if>
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.productionLineCode != null and e.productionLineCode != '' ">
            AND t.production_line_code = #{e.productionLineCode}
        </if>
        <if test="e.productionLineName != null and e.productionLineName != '' ">
            AND t.production_line_name = #{e.productionLineName}
        </if>
        <if test="e.productionLineMagName != null and e.productionLineMagName != '' ">
            AND t.production_line_mag_name = #{e.productionLineMagName}
        </if>
        <if test="e.productionLineMagNickName != null and e.productionLineMagNickName != '' ">
            AND t.production_line_mag_nick_name = #{e.productionLineMagNickName}
        </if>
        <if test="e.productionLineMagPhone != null and e.productionLineMagPhone != '' ">
            AND t.production_line_mag_phone = #{e.productionLineMagPhone}
        </if>
        <if test="e.sublineId != null and e.sublineId != '' ">
            AND t.subline_id = #{e.sublineId}
        </if>
        <if test="e.sublineName != null and e.sublineName != '' ">
            AND t.subline_name = #{e.sublineName}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.fcode != null and e.fcode != '' ">
            AND t.fcode = #{e.fcode}
        </if>
        <if test="e.fname != null and e.fname != '' ">
            AND t.fname = #{e.fname}
        </if>
        <if test="e.flat != null and e.flat != '' ">
            AND t.flat = #{e.flat}
        </if>
        <if test="e.flng != null and e.flng != '' ">
            AND t.flng = #{e.flng}
        </if>
        <if test="e.faddr != null and e.faddr != '' ">
            AND t.faddr = #{e.faddr}
        </if>
        <if test="e.fimgUrl != null and e.fimgUrl != '' ">
            AND t.fimg_url = #{e.fimgUrl}
        </if>
        <if test="e.fmagName != null and e.fmagName != '' ">
            AND t.fmag_name = #{e.fmagName}
        </if>
        <if test="e.fmagNickName != null and e.fmagNickName != '' ">
            AND t.fmag_nick_name = #{e.fmagNickName}
        </if>
        <if test="e.fmagPhone != null and e.fmagPhone != '' ">
            AND t.fmag_phone = #{e.fmagPhone}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.stype != null and e.stype != '' ">
            AND t.stype = #{e.stype}
        </if>
        <if test="e.stypeName != null and e.stypeName != '' ">
            AND t.stype_name = #{e.stypeName}
        </if>
        <if test="e.sname != null and e.sname != '' ">
            AND t.sname = #{e.sname}
        </if>
        <if test="e.slat != null and e.slat != '' ">
            AND t.slat = #{e.slat}
        </if>
        <if test="e.slng != null and e.slng != '' ">
            AND t.slng = #{e.slng}
        </if>
        <if test="e.salt != null and e.salt != '' ">
            AND t.salt = #{e.salt}
        </if>
        <if test="e.salarmThreshold != null and e.salarmThreshold != '' ">
            AND t.salarm_threshold = #{e.salarmThreshold}
        </if>
        <if test="e.saddr != null and e.saddr != '' ">
            AND t.saddr = #{e.saddr}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.alarmLevel != null and e.alarmLevel != '' ">
            AND t.alarm_level = #{e.alarmLevel}
        </if>
        <if test="e.alarmLevelName != null and e.alarmLevelName != '' ">
            AND t.alarm_level_name = #{e.alarmLevelName}
        </if>
        <if test="e.alarmAdvice != null and e.alarmAdvice != '' ">
            AND t.alarm_advice = #{e.alarmAdvice}
        </if>
        <if test="e.alarmAdviceDes != null and e.alarmAdviceDes != '' ">
            AND t.alarm_advice_des = #{e.alarmAdviceDes}
        </if>
        <if test="e.targetName != null and e.targetName != '' ">
            AND t.target_name = #{e.targetName}
        </if>
        <if test="e.targetCnname != null and e.targetCnname != '' ">
            AND t.target_cnname = #{e.targetCnname}
        </if>
        <if test="e.modelType != null and e.modelType != '' ">
            AND t.model_type = #{e.modelType}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.deviceCode != null and e.deviceCode != '' ">
            AND t.device_code = #{e.deviceCode}
        </if>
        <if test="e.deviceName != null and e.deviceName != '' ">
            AND t.device_name = #{e.deviceName}
        </if>
        <if test="e.devicePlace != null and e.devicePlace != '' ">
            AND t.device_place = #{e.devicePlace}
        </if>
        <if test="e.deviceMagName != null and e.deviceMagName != '' ">
            AND t.device_mag_name = #{e.deviceMagName}
        </if>
        <if test="e.deviceMagNickName != null and e.deviceMagNickName != '' ">
            AND t.device_mag_nick_name = #{e.deviceMagNickName}
        </if>
        <if test="e.deviceMagPhone != null and e.deviceMagPhone != '' ">
            AND t.device_mag_phone = #{e.deviceMagPhone}
        </if>
        <if test="e.alarmAddType != null and e.alarmAddType != '' ">
            AND t.alarm_add_type = #{e.alarmAddType}
        </if>
        <if test="e.photoUrl != null and e.photoUrl != '' ">
            AND t.photo_url = #{e.photoUrl}
        </if>
        <if test="e.alarmClassifyId != null and e.alarmClassifyId != '' ">
            AND t.alarm_classify_id = #{e.alarmClassifyId}
        </if>
        <if test="e.alarmDetail != null and e.alarmDetail != '' ">
            AND t.alarm_detail = #{e.alarmDetail}
        </if>
        <if test="e.alarmType != null and e.alarmType != '' ">
            AND t.alarm_type = #{e.alarmType}
        </if>
        <if test="e.alarmRecoveryTime != null and e.alarmRecoveryTime != '' ">
            AND t.alarm_recovery_time = #{e.alarmRecoveryTime}
        </if>
        <if test="e.alarmRecoveryType != null and e.alarmRecoveryType != '' ">
            AND t.alarm_recovery_type = #{e.alarmRecoveryType}
        </if>
        <if test="e.alarmUpdateTime != null and e.alarmUpdateTime != '' ">
            AND t.alarm_update_time = #{e.alarmUpdateTime}
        </if>
        <if test="e.alarmDefinitionCode != null and e.alarmDefinitionCode != '' ">
            AND t.alarm_definition_code = #{e.alarmDefinitionCode}
        </if>
        <if test="e.alarmReportPersonnel != null and e.alarmReportPersonnel != '' ">
            AND t.alarm_report_personnel = #{e.alarmReportPersonnel}
        </if>
        <if test="e.alarmClassifyName != null and e.alarmClassifyName != '' ">
            AND t.alarm_classify_name = #{e.alarmClassifyName}
        </if>
        <if test="e.lastUpgradeTime != null and e.lastUpgradeTime != '' ">
            AND t.last_upgrade_time = #{e.lastUpgradeTime}
        </if>
    </sql>

    <select id="getDayAlarmTopEight" resultType="com.yelink.dfs.entity.alarm.AlarmEntity">
        select *
        from dfs_alarm
        where device_id = #{deviceId}
          and (to_days(alarm_report_time) = to_days(now()) or alarm_recovery_time is null)
    </select>

    <select id="getMonthAlarmTopEight" resultType="com.yelink.dfs.entity.alarm.AlarmEntity">
        select *
        from dfs_alarm
        where device_id = #{deviceId}
          and (DATE_FORMAT(alarm_report_time, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m') or alarm_recovery_time is null)
    </select>

    <select id="getWeekAlarmTopEight" resultType="com.yelink.dfs.entity.alarm.AlarmEntity">
        select *
        from dfs_alarm
        where device_id = #{deviceId}
          and (DATE_SUB(CURDATE(), INTERVAL 7 DAY) &lt;= date(alarm_report_time) or alarm_recovery_time is null)
    </select>
    <select id="getList" resultType="com.yelink.dfs.entity.alarm.AlarmEntity">
        select alarm.* from dfs_alarm alarm
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
