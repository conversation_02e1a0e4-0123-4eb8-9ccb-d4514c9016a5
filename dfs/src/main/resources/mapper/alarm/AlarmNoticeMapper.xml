<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.alarm.AlarmNoticeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.alarm.AlarmNoticeEntity">
        <result column="id" property="id"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_level_name" property="alarmLevelName"/>
        <result column="user_id" property="userId"/>
        <result column="type" property="type"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="upgradeDuration" property="upgradeDuration"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.alarmLevel != null and e.alarmLevel != '' ">
            AND t.alarm_level = #{e.alarmLevel}
        </if>
        <if test="e.alarmLevelName != null and e.alarmLevelName != '' ">
            AND t.alarmLevelName = #{e.alarmLevelName}
        </if>
        <if test="e.userId != null and e.userId != '' ">
            AND t.user_id = #{e.userId}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.upgradeDuration != null and e.upgradeDuration != '' ">
            AND t.upgrade_duration = #{e.upgradeDuration}
        </if>
    </sql>

</mapper>
