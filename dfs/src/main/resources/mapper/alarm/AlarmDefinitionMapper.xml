<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.alarm.AlarmDefinitionMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.alarm.AlarmDefinitionEntity">
		    <result column="id" property="id" />
		    <result column="alarm_definition_code" property="alarmDefinitionCode" />
		    <result column="alarm_definition_name" property="alarmDefinitionName" />
		    <result column="alarm_classify_id" property="alarmClassifyId" />
		    <result column="alarm_classify_name" property="alarmClassifyName" />
		    <result column="alarm_level" property="alarmLevel" />
		    <result column="alarm_level_name" property="alarmLevelName" />
		    <result column="data_sources" property="dataSources" />
			<result column="update_by" property="updateBy"/>
			<result column="create_by" property="createBy"/>
			<result column="update_time" property="updateTime"/>
			<result column="create_time" property="createTime"/>
			<result column="knowledge_base" property="knowledgeBase"/>
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.alarmDefinitionCode != null and e.alarmDefinitionCode != '' ">
					AND t.alarm_definition_code = #{e.alarmDefinitionCode}
				</if>
				<if test="e.alarmDefinitionName != null and e.alarmDefinitionName != '' ">
					AND t.alarm_definition_name = #{e.alarmDefinitionName}
				</if>
				<if test="e.alarmClassifyId != null and e.alarmClassifyId != '' ">
					AND t.alarm_classify_id = #{e.alarmClassifyId}
				</if>
				<if test="e.alarmClassifyName != null and e.alarmClassifyName != '' ">
					AND t.alarm_classify_name = #{e.alarmClassifyName}
				</if>
				<if test="e.alarmLevel != null and e.alarmLevel != '' ">
					AND t.alarm_level = #{e.alarmLevel}
				</if>
				<if test="e.alarmLevelName != null and e.alarmLevelName != '' ">
					AND t.alarm_level_name = #{e.alarmLevelName}
				</if>
				<if test="e.dataSources != null and e.dataSources != '' ">
					AND t.data_sources = #{e.dataSources}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>