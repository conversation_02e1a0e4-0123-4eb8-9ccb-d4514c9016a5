<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.alarm.AlarmRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.alarm.AlarmRecordEntity">
        <result column="alarm_record_id" property="alarmRecordId"/>
        <result column="alarm_id" property="alarmId"/>
        <result column="eui" property="eui"/>
        <result column="alarm_value" property="alarmValue"/>
        <result column="alarm_time" property="alarmTime"/>
        <result column="alarm_report_time" property="alarmReportTime"/>
        <result column="alarm_type_number" property="alarmTypeNumber"/>
        <result column="alarm_type_name" property="alarmTypeName"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_level_name" property="alarmLevelName"/>
        <result column="alarm_advice" property="alarmAdvice"/>
        <result column="alarm_advice_des" property="alarmAdviceDes"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.alarmRecordId != null and e.alarmRecordId != '' ">
            AND t.alarm_record_id = #{e.alarmRecordId}
        </if>
        <if test="e.alarmId != null and e.alarmId != '' ">
            AND t.alarm_id = #{e.alarmId}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.alarmValue != null and e.alarmValue != '' ">
            AND t.alarm_value = #{e.alarmValue}
        </if>
        <if test="e.alarmTime != null and e.alarmTime != '' ">
            AND t.alarm_time = #{e.alarmTime}
        </if>
        <if test="e.alarmReportTime != null and e.alarmReportTime != '' ">
            AND t.alarm_report_time = #{e.alarmReportTime}
        </if>
        <if test="e.alarmTypeNumber != null and e.alarmTypeNumber != '' ">
            AND t.alarm_type_number = #{e.alarmTypeNumber}
        </if>
        <if test="e.alarmTypeName != null and e.alarmTypeName != '' ">
            AND t.alarm_type_name = #{e.alarmTypeName}
        </if>
        <if test="e.alarmLevel != null and e.alarmLevel != '' ">
            AND t.alarm_level = #{e.alarmLevel}
        </if>
        <if test="e.alarmLevelName != null and e.alarmLevelName != '' ">
            AND t.alarm_level_name = #{e.alarmLevelName}
        </if>
        <if test="e.alarmAdvice != null and e.alarmAdvice != '' ">
            AND t.alarm_advice = #{e.alarmAdvice}
        </if>
        <if test="e.alarmAdviceDes != null and e.alarmAdviceDes != '' ">
            AND t.alarm_advice_des = #{e.alarmAdviceDes}
        </if>
    </sql>

</mapper>
