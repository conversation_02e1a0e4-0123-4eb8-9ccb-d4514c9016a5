<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.alarm.AlarmNotificationMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.alarm.AlarmNotificationEntity">
		    <result column="id" property="id" />
		    <result column="alarm_classify_code" property="alarmClassifyCode" />
		    <result column="alarm_definition_code" property="alarmDefinitionCode" />
			<result column="alarm_definition_name" property="alarmDefinitionName"/>
			<result column="alarm_level_code" property="alarmLevelCode"/>
			<result column="notice_method" property="noticeMethod"/>
			<result column="state" property="state"/>
			<result column="remark" property="remark"/>
			<result column="device_model_id" property="deviceModelId"/>
			<result column="device_id" property="deviceId"/>
			<result column="device_name" property="deviceName"/>
            <result column="create_by" property="createBy"/>
            <result column="update_by" property="updateBy"/>
            <result column="create_time" property="createTime"/>
            <result column="update_time" property="updateTime"/>
	</resultMap>
	

</mapper>