<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.NumberRuleMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="prefix_detail" property="prefixDetail"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


</mapper>
