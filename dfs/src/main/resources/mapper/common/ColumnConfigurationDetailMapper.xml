<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.ColumnConfigurationDetailMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.common.ColumnConfigurationDetailEntity">
        <result column="id" property="id"/>
        <result column="user_name" property="columnConfigurationId"/>
        <result column="configuration_name" property="configurationName"/>
        <result column="modular_type" property="columnInformation"/>
        <result column="configuration_name" property="columnSearchInformation"/>
        <result column="is_default" property="isUser"/>
    </resultMap>


</mapper>