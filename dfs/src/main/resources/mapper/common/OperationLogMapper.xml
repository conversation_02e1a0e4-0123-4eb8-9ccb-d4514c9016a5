<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.OperationLogMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.OperationLogEntity">
        <result column="id" property="id"/>
        <result column="module" property="module"/>
        <result column="type" property="type"/>
        <result column="des" property="des"/>
        <result column="req_param" property="reqParam"/>
        <result column="resp_param" property="respParam"/>
        <result column="username" property="username"/>
        <result column="nickname" property="nickname"/>
        <result column="method" property="method"/>
        <result column="ip" property="ip"/>
        <result column="uri" property="uri"/>
        <result column="exc_name" property="excName"/>
        <result column="exc_message" property="excMessage"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.module != null and e.module != '' ">
            AND t.module = #{e.module}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.des != null and e.des != '' ">
            AND t.des = #{e.des}
        </if>
        <if test="e.reqParam != null and e.reqParam != '' ">
            AND t.req_param = #{e.reqParam}
        </if>
        <if test="e.respParam != null and e.respParam != '' ">
            AND t.resp_param = #{e.respParam}
        </if>
        <if test="e.username != null and e.username != '' ">
            AND t.username = #{e.username}
        </if>
        <if test="e.nickname != null and e.nickname != '' ">
            AND t.nickname = #{e.nickname}
        </if>
        <if test="e.method != null and e.method != '' ">
            AND t.method = #{e.method}
        </if>
        <if test="e.ip != null and e.ip != '' ">
            AND t.ip = #{e.ip}
        </if>
        <if test="e.uri != null and e.uri != '' ">
            AND t.uri = #{e.uri}
        </if>
        <if test="e.excName != null and e.excName != '' ">
            AND t.exc_name = #{e.excName}
        </if>
        <if test="e.excMessage != null and e.excMessage != '' ">
            AND t.exc_message = #{e.excMessage}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>
    <select id="selectCountByDate" resultType="java.lang.Integer">
        SELECT count(1) from (
        select 1 from dfs_operation_log group by DATE_FORMAT(create_time, '%Y-%m-%d')
        ) t
    </select>

</mapper>
