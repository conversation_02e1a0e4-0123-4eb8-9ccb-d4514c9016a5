<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.DictMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.DictEntity">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="des" property="des"/>
        <result column="url" property="url"/>
        <result column="unit" property="unit"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>

        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.des != null and e.des != '' ">
            AND t.des = #{e.des}
        </if>
        <if test="e.url != null and e.url != '' ">
            AND t.url = #{e.url}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
    </sql>

    <select id="getMaterialTypeList" resultType="com.yelink.dfscommon.entity.dfs.DictEntity">
        select materialType.* from dfs_dict materialType
        <where>
            <if test="sqlDTO != null and sqlDTO.materialTypeSql != null and sqlDTO.materialTypeSql != ''">
                AND ${sqlDTO.materialTypeSql}
            </if>
            AND materialType.`type` = 'materialType'
        </where>
        <if test="sqlDTO != null and sqlDTO.orderBySql != null and sqlDTO.orderBySql != ''">
            ORDER BY
            ${sqlDTO.orderBySql}
        </if>
    </select>

    <select id="getList" resultType="com.yelink.dfs.open.v2.common.dto.DictVO">
        SELECT dict.* FROM dfs_dict dict
        LEFT JOIN dfs_dict_file dictFile ON dict.id = dictFile.dict_id
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
    <select id="unitOpenList2" resultType="com.yelink.dfscommon.entity.dfs.DictEntity">
        SELECT unit.* FROM dfs_dict unit
        where unit.type = 'unit'
        <if test="dto != null and dto.filterSql != null and dto.filterSql != ''">
            AND ${dto.filterSql}
        </if>
        <if test="dto != null and dto.orderBySql != null and dto.orderBySql != ''">
            ORDER BY ${dto.orderBySql}
        </if>
    </select>
</mapper>
