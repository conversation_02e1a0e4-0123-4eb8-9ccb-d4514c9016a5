<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.OrderChangeLogMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.common.OrderChangeLogEntity">
        <result column="id" property="id"/>
        <result column="model" property="model"/>
        <result column="title" property="title"/>
        <result column="order_number" property="orderNumber"/>
        <result column="des" property="des"/>
        <result column="username" property="username"/>
        <result column="nickname" property="nickname"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.model != null and e.model != '' ">
            AND t.model = #{e.model}
        </if>
        <if test="e.title != null and e.title != '' ">
            AND t.title = #{e.title}
        </if>
        <if test="e.orderNumber != null and e.orderNumber != '' ">
            AND t.order_number = #{e.orderNumber}
        </if>
        <if test="e.des != null and e.des != '' ">
            AND t.des = #{e.des}
        </if>
        <if test="e.username != null and e.username != '' ">
            AND t.username = #{e.username}
        </if>
        <if test="e.nickname != null and e.nickname != '' ">
            AND t.nickname = #{e.nickname}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>


</mapper>
