<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.ImportDataRecordMapper">

    <select id="unionPage" resultType="com.yelink.dfs.entity.common.ImportDataRecordVO">
        SELECT t.*
        FROM
        (
        SELECT
            import_file_name,
            import_type,
            operator,
            import_time,
            success_number,
            fail_number,
            log_url,
            update_by,
            create_by,
            update_time,
            create_time
        FROM `dfs_import_data_record`
            <include refid="select_content" />
            UNION All
        SELECT
            import_file_name,
            import_type,
            operator,
            import_time,
            success_number,
            fail_number,
            log_url,
            update_by,
            create_by,
            update_time,
            create_time
        FROM `dfs_metrics`.`v_ams_import_record`
            <include refid="select_content" />
        )t
        ORDER BY t.import_time desc
    </select>

    <sql id="select_content">
        <where>
            <if test="dto.importTypes != null and dto.importTypes.size > 0">
                AND import_type IN
                <foreach item="item" collection="dto.importTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="dto.fileName!=null and dto.fileName!=''">
                AND `import_file_name` LIKE concat('%',#{dto.fileName},'%')
            </if>
            <if test="dto.startTime!=null and dto.startTime!=''">
                AND `import_time` <![CDATA[ >= ]]>  #{dto.startTime}
            </if>
            <if test="dto.endTime!=null and dto.endTime!=''">
                AND `import_time` <![CDATA[ <= ]]>  #{dto.endTime}
            </if>
        </where>
    </sql>
</mapper>
