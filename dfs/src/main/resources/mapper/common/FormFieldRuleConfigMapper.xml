<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.config.FormFieldRuleConfigMapper">

    <select id="getList" resultType="com.yelink.dfscommon.entity.dfs.FormFieldRuleConfigEntity">
        select form.* from dfs_form_field_rule_config form
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
