<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.MetricsMapper">

    <update id="refreshView">
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_capacity` AS SELECT * FROM `dfs_capacity`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_device` AS SELECT * FROM `dfs_device`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_work_order` AS SELECT * FROM `dfs_work_order`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_record_work_order_day_count` AS SELECT * FROM `dfs_record_work_order_day_count`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_record_work_order_line_day_count` AS SELECT * FROM `dfs_record_work_order_line_day_count`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_work_order_procedure_relation` AS SELECT * FROM `dfs_work_order_procedure_relation`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_material` AS SELECT * FROM `dfs_material`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_order_procedure_finish` AS SELECT * FROM `dfs_order_procedure_finish`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_record_work_order_unqualified` AS SELECT * FROM `dfs_record_work_order_unqualified`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_customer` AS SELECT * FROM `dfs_customer`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_report_line` AS SELECT * FROM `dfs_report_line`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_maintain_record` AS SELECT * FROM `dfs_maintain_record`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_attendance` AS SELECT * FROM `dfs_attendance`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_attendance_record` AS SELECT * FROM `dfs_attendance_record`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_defect_define` AS SELECT * FROM `dfs_defect_define`;
        CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_dict` AS SELECT * FROM `dfs_dict`;

        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_alarm_classify` AS SELECT * FROM `dfs_alarm_classify`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_alarm` AS SELECT * FROM `dfs_alarm`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_alarm_definition` AS SELECT * FROM `dfs_alarm_definition`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_area` AS SELECT * FROM `dfs_area`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_bar_code` AS SELECT * FROM `dfs_bar_code`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_bom` AS SELECT * FROM `dfs_bom`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_bom_raw_material` AS SELECT * FROM `dfs_bom_raw_material`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_code_report` AS SELECT * FROM `dfs_code_report`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_company` AS SELECT * FROM `dfs_company`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_craft` AS SELECT * FROM `dfs_craft`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_craft_procedure` AS SELECT * FROM `dfs_craft_procedure`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_customer_material_list` AS SELECT * FROM `dfs_customer_material_list`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_default_capacity` AS SELECT * FROM `dfs_default_capacity`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_defect_record` AS SELECT * FROM `dfs_defect_record`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_device_energy_consumption` AS SELECT * FROM `dfs_device_energy_consumption`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_device_gas_energy_consumption` AS SELECT * FROM `dfs_device_gas_energy_consumption`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_device_shift_carbon_consumption` AS SELECT * FROM `dfs_device_shift_carbon_consumption`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_device_shift_energy_consumption` AS SELECT * FROM `dfs_device_shift_energy_consumption`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_device_shift_gas_consumption` AS SELECT * FROM `dfs_device_shift_gas_consumption`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_device_shift_water_consumption` AS SELECT * FROM `dfs_device_shift_water_consumption`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_device_time_period_energy_consumption` AS SELECT * FROM `dfs_device_time_period_energy_consumption`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_facilities` AS SELECT * FROM `dfs_facilities`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_grid` AS SELECT * FROM `dfs_grid`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_maintain_define` AS SELECT * FROM `dfs_maintain_define`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_maintain_fault_type_define` AS SELECT * FROM `dfs_maintain_fault_type_define`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_model` AS SELECT * FROM `dfs_model`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_production_line` AS SELECT * FROM `dfs_production_line`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_shift` AS SELECT * FROM `dfs_shift`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_dfs_work_center` AS SELECT * FROM `dfs_work_center`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_sys_team` AS SELECT * FROM `sys_team`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_sys_users` AS SELECT * FROM `sys_users`;
        CREATE OR REPLACE VIEW `dfs_metrics`.`v_sys_department` AS SELECT * FROM `sys_department`;
    </update>

</mapper>
