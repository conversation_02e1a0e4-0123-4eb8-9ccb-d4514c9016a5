<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.ColumnConfigurationMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.common.ColumnConfigurationEntity">
        <result column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="modular_type" property="modularType"/>
        <result column="is_default" property="isDefault"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


</mapper>