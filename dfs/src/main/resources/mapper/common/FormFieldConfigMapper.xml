<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.common.config.FormFieldConfigMapper">

    <insert id="addField">
        <foreach item="item" collection="insertDTOS">
            call proc_add_form_field_module(#{item.fullPathCode}, #{item.fieldCode}, #{item.fieldName}, #{item.moduleCode});
        </foreach>
    </insert>
</mapper>
