<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.customization.CustomComponentConfigMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.customization.CustomComponentConfigEntity">
		    <result column="id" property="id" />
		    <result column="location_marker_name" property="locationMarkerName" />
		    <result column="component_name" property="componentName" />
		    <result column="supplementary_data" property="supplementaryData" />
		    <result column="remark" property="remark" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.locationMarkerName != null and e.locationMarkerName != '' ">
					AND t.location_marker_name = #{e.locationMarkerName}
				</if>
				<if test="e.componentName != null and e.componentName != '' ">
					AND t.component_name = #{e.componentName}
				</if>
				<if test="e.supplementaryData != null and e.supplementaryData != '' ">
					AND t.supplementary_data = #{e.supplementaryData}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>