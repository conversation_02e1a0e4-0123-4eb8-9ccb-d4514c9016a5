<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.barcode.LabelMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.barcode.LabelEntity">
		    <result column="rule_id" property="ruleId" />
		    <result column="rule_name" property="ruleName" />
		    <result column="code_type" property="codeType" />
		    <result column="state" property="state" />
	</resultMap>


	<sql id="select_content">
				<if test="e.ruleId != null and e.ruleId != '' ">
					AND t.rule_id = #{e.ruleId}
				</if>
				<if test="e.ruleName != null and e.ruleName != '' ">
					AND t.rule_name = #{e.ruleName}
				</if>
				<if test="e.codeType != null and e.codeType != '' ">
					AND t.code_type = #{e.codeType}
				</if>
				<if test="e.state != null and e.state != '' ">
					AND t.state = #{e.state}
				</if>
				<if test="e.codeInfo != null and e.codeInfo != '' ">
					AND t.code_info = #{e.codeInfo}
				</if>
	</sql>
	<select id="getList" resultType="com.yelink.dfscommon.entity.dfs.barcode.LabelEntity">
		select barCodeRule.* from dfs_bar_code_rule barCodeRule
		<if test="sql != null and sql != ''">
			${sql}
		</if>
	</select>

</mapper>