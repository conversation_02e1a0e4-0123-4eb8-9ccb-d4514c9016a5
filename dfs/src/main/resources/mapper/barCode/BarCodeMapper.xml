<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.barcode.BarCodeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.barcode.BarCodeEntity">
        <result column="bar_code_id" property="barCodeId"/>
        <result column="bar_code" property="barCode"/>
        <result column="rule_type" property="ruleType"/>
        <result column="relate_number" property="relateNumber"/>
        <result column="state" property="state"/>
        <result column="code_info" property="codeInfo"/>
        <result column="print_time" property="printTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="supplier" property="supplier"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.barCodeId != null and e.barCodeId != '' ">
            AND t.bar_code_id = #{e.barCodeId}
        </if>
        <if test="e.barCode != null and e.barCode != '' ">
            AND t.bar_code = #{e.barCode}
        </if>
        <if test="e.ruleCode != null and e.ruleCode != '' ">
            AND t.rule_code = #{e.ruleCode}
        </if>
        <if test="e.ruleType != null and e.ruleType != '' ">
            AND t.rule_type = #{e.ruleType}
        </if>
        <if test="e.relateNumber != null and e.relateNumber != '' ">
            AND t.relate_number = #{e.relateNumber}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.codeInfo != null and e.codeInfo != '' ">
            AND t.code_info = #{e.codeInfo}
        </if>
        <if test="e.printTime != null and e.printTime != '' ">
            AND t.print_time = #{e.printTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.supplier != null and e.supplier != '' ">
            AND t.supplier = #{e.supplier}
        </if>
        <if test="e.warehousingDate != null and e.warehousingDate != '' ">
            AND t.warehousing_date = #{e.warehousingDate}
        </if>
        <if test="e.deliveryDate != null and e.deliveryDate != '' ">
            AND t.delivery_date = #{e.deliveryDate}
        </if>
        <if test="e.warehouse != null and e.warehouse != '' ">
            AND t.warehouse = #{e.warehouse}
        </if>
        <if test="e.currentInvenroty != null and e.currentInvenroty != '' ">
            AND t.current_invenroty = #{e.currentInvenroty}
        </if>
        <if test="e.outboundQuantity != null and e.outboundQuantity != '' ">
            AND t.outbound_quantity = #{e.outboundQuantity}
        </if>
        <if test="e.receiptQuantity != null and e.receiptQuantity != '' ">
            AND t.receipt_quantity = #{e.receiptQuantity}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
    </sql>

    <select id="getRelationNumberListByRelatedType" resultType="java.lang.String">
        SELECT relate_number FROM `dfs_bar_code`
        where rule_type = #{relatedType}
        <if test="relationNumber != null">
            and relate_number like '%${relationNumber}%'
        </if>
        GROUP BY relate_number
    </select>
    <select id="getList" resultType="com.yelink.dfs.entity.barcode.BarCodeEntity">
        select barCode.* from dfs_bar_code barCode
        <if test="sql != null and sql != ''">
            <if test="sql.contains('material')">
                LEFT JOIN dfs_material material ON material.code = barCode.material_code
            </if>
            ${sql}
        </if>
    </select>

</mapper>