<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.event.EventRecordMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.event.EventRecordEntity">
		    <result column="id" property="id" />
		    <result column="event_code_id" property="eventCodeId" />
		    <result column="event_definition_code" property="eventDefinitionCode" />
		    <result column="event_definition_name" property="eventDefinitionName" />
		    <result column="event_classify_id" property="eventClassifyId" />
		    <result column="event_classify_name" property="eventClassifyName" />
		    <result column="event_detail" property="eventDetail" />
		    <result column="time" property="time" />
		    <result column="event_report_personnel" property="eventReportPersonnel" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.eventCodeId != null and e.eventCodeId != '' ">
					AND t.event_code_id = #{e.eventCodeId}
				</if>
				<if test="e.eventDefinitionCode != null and e.eventDefinitionCode != '' ">
					AND t.event_definition_code = #{e.eventDefinitionCode}
				</if>
				<if test="e.eventDefinitionName != null and e.eventDefinitionName != '' ">
					AND t.event_definition_name = #{e.eventDefinitionName}
				</if>
				<if test="e.eventClassifyCode != null and e.eventClassifyCode != '' ">
					AND t.event_classify_code = #{e.eventClassifyCode}
				</if>
				<if test="e.eventClassifyName != null and e.eventClassifyName != '' ">
					AND t.event_classify_name = #{e.eventClassifyName}
				</if>
				<if test="e.eventDetail != null and e.eventDetail != '' ">
					AND t.event_detail = #{e.eventDetail}
				</if>
				<if test="e.time != null and e.time != '' ">
					AND t.time = #{e.time}
				</if>
				<if test="e.eventReportPersonnel != null and e.eventReportPersonnel != '' ">
					AND t.event_report_personnel = #{e.eventReportPersonnel}
				</if>
	</sql>

</mapper>