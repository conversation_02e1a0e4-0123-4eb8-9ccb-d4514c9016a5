<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.event.EventDefinitionMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.event.EventDefinitionEntity">
		    <result column="id" property="id" />
		    <result column="event_definition_code" property="eventDefinitionCode" />
		    <result column="event_definition_name" property="eventDefinitionName" />
		    <result column="event_classify_id" property="eventClassifyId" />
		    <result column="event_classify_name" property="eventClassifyName" />
		    <result column="data_sources" property="dataSources" />
		    <result column="binding_type" property="bindingType" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_by" property="createBy" />
		    <result column="update_time" property="updateTime" />
		    <result column="create_time" property="createTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.eventDefinitionCode != null and e.eventDefinitionCode != '' ">
					AND t.event_definition_code = #{e.eventDefinitionCode}
				</if>
				<if test="e.eventDefinitionName != null and e.eventDefinitionName != '' ">
					AND t.event_definition_name = #{e.eventDefinitionName}
				</if>
				<if test="e.eventClassifyCode != null and e.eventClassifyCode != '' ">
					AND t.event_classify_code = #{e.eventClassifyCode}
				</if>
				<if test="e.eventClassifyName != null and e.eventClassifyName != '' ">
					AND t.event_classify_name = #{e.eventClassifyName}
				</if>
				<if test="e.dataSources != null and e.dataSources != '' ">
					AND t.data_sources = #{e.dataSources}
				</if>
				<if test="e.bindingType != null and e.bindingType != '' ">
					AND t.binding_type = #{e.bindingType}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>