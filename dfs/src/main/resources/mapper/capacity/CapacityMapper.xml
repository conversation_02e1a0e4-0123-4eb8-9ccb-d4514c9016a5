<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.capacity.CapacityMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.capacity.CapacityEntity">
		    <result column="capacity_id" property="capacityId" />
		    <result column="line_id" property="lineId" />
		    <result column="line_code" property="lineCode" />
		    <result column="line_name" property="lineName" />
		    <result column="material_id" property="materialId" />
		    <result column="material_code" property="materialCode" />
		    <result column="capacity" property="capacity" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>


	<sql id="select_content">
				<if test="e.capacityId != null and e.capacityId != '' ">
					AND t.capacity_id = #{e.capacityId}
				</if>
				<if test="e.lineId != null and e.lineId != '' ">
					AND t.line_id = #{e.lineId}
				</if>
				<if test="e.lineCode != null and e.lineCode != '' ">
					AND t.line_code = #{e.lineCode}
				</if>
				<if test="e.lineName != null and e.lineName != '' ">
					AND t.line_name = #{e.lineName}
				</if>
				<if test="e.materialId != null and e.materialId != '' ">
					AND t.material_id = #{e.materialId}
				</if>
				<if test="e.materialCode != null and e.materialCode != '' ">
					AND t.material_code = #{e.materialCode}
				</if>
				<if test="e.capacity != null and e.capacity != '' ">
					AND t.capacity = #{e.capacity}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>