<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.node.SaleOrderNodeConfigureMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.node.SaleOrderNodeConfigureEntity">
        <result column="id" property="id"/>
        <result column="sale_order_number" property="saleOrderNumber"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="node_configure_code" property="nodeConfigureCode"/>
        <result column="customer_name" property="customerName"/>
        <result column="material_remark" property="materialRemark"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="quantity" property="quantity"/>
        <result column="planned_delivery_date" property="plannedDeliveryDate"/>
        <result column="priority" property="priority"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="approver" property="approver"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="actual_approver" property="actualApprover"/>
        <result column="state" property="state"/>
        <result column="order_date" property="orderDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.saleOrderNumber != null and e.saleOrderNumber != '' ">
            AND t.sale_order_number = #{e.saleOrderNumber}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.nodeConfigureCode != null and e.nodeConfigureCode != '' ">
            AND t.node_configure_code = #{e.nodeConfigureCode}
        </if>
        <if test="e.customerName != null and e.customerName != '' ">
            AND t.customer_name = #{e.customerName}
        </if>
        <if test="e.materialRemark != null and e.materialRemark != '' ">
            AND t.material_remark = #{e.materialRemark}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.quantity != null and e.quantity != '' ">
            AND t.quantity = #{e.quantity}
        </if>
        <if test="e.plannedDeliveryDate != null and e.plannedDeliveryDate != '' ">
            AND t.planned_delivery_date = #{e.plannedDeliveryDate}
        </if>
        <if test="e.priority != null and e.priority != '' ">
            AND t.priority = #{e.priority}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.actualApprover != null and e.actualApprover != '' ">
            AND t.actual_approver = #{e.actualApprover}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.orderDate != null and e.orderDate != '' ">
            AND t.order_date = #{e.orderDate}
        </if>
    </sql>

</mapper>