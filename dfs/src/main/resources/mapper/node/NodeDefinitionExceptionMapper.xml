<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.node.NodeDefinitionExceptionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.node.NodeDefinitionExceptionEntity">
        <result column="id" property="id"/>
        <result column="node_definition_id" property="nodeDefinitionId"/>
        <result column="node_exception_id" property="nodeExceptionId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.nodeDefinitionId != null and e.nodeDefinitionId != '' ">
            AND t.node_definition_id = #{e.nodeDefinitionId}
        </if>
        <if test="e.nodeExceptionId != null and e.nodeExceptionId != '' ">
            AND t.node_exception_id = #{e.nodeExceptionId}
        </if>
    </sql>

</mapper>