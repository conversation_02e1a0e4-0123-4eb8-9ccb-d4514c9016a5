<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.node.NodeSequenceMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.node.NodeSequenceEntity">
        <result column="id" property="id"/>
        <result column="nodeConfigureCode" property="nodeConfigureCode"/>
        <result column="sequence" property="sequence"/>
        <result column="node_definition_code" property="nodeDefinitionCode"/>
        <result column="node_definition_name" property="nodeDefinitionName"/>
        <result column="is_select" property="isSelect"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.nodeConfigureCode != null and e.nodeConfigureCode != '' ">
            AND t.node_configure_code = #{e.nodeConfigureCode}
        </if>
        <if test="e.sequence != null and e.sequence != '' ">
            AND t.sequence = #{e.sequence}
        </if>
        <if test="e.nodeDefinitionCode != null and e.nodeDefinitionCode != '' ">
            AND t.node_definition_code = #{e.nodeDefinitionCode}
        </if>
        <if test="e.nodeDefinitionName != null and e.nodeDefinitionName != '' ">
            AND t.node_definition_name = #{e.nodeDefinitionName}
        </if>
        <if test="e.isSelect != null and e.isSelect != '' ">
            AND t.is_select = #{e.isSelect}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>