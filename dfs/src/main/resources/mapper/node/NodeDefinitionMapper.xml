<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.node.NodeDefinitionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.node.NodeDefinitionEntity">
        <result column="id" property="id"/>
        <result column="node_definition_code" property="nodeDefinitionCode"/>
        <result column="node_definition_name" property="nodeDefinitionName"/>
        <result column="report_type" property="reportType"/>
        <result column="report_sub_type" property="reportSubType"/>
        <result column="report_bill" property="reportBill"/>
        <result column="department_name" property="departmentName"/>
        <result column="state" property="state"/>
        <result column="approver" property="approver"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="actual_approver" property="actualApprover"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.nodeDefinitionCode != null and e.nodeDefinitionCode != '' ">
            AND t.node_definition_code = #{e.nodeDefinitionCode}
        </if>
        <if test="e.nodeDefinitionName != null and e.nodeDefinitionName != '' ">
            AND t.node_definition_name = #{e.nodeDefinitionName}
        </if>
        <if test="e.reportType != null and e.reportType != '' ">
            AND t.report_type = #{e.reportType}
        </if>
        <if test="e.reportSubType != null and e.reportSubType != '' ">
            AND t.report_sub_type = #{e.reportSubType}
        </if>
        <if test="e.reportBill != null and e.reportBill != '' ">
            AND t.report_bill = #{e.reportBill}
        </if>
        <if test="e.departmentName != null and e.departmentName != '' ">
            AND t.department_name = #{e.departmentName}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.approvalTime != null and e.approvalTime != '' ">
            AND t.approval_time = #{e.approvalTime}
        </if>
        <if test="e.actualApprover != null and e.actualApprover != '' ">
            AND t.actual_approver = #{e.actualApprover}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>