<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.node.NodeExceptionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.node.NodeExceptionEntity">
        <result column="id" property="id"/>
        <result column="node_exception_code" property="nodeExceptionCode"/>
        <result column="node_exception_describe" property="nodeExceptionDescribe"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.nodeExceptionCode != null and e.nodeExceptionCode != '' ">
            AND t.node_exception_code = #{e.nodeExceptionCode}
        </if>
        <if test="e.nodeExceptionDescribe != null and e.nodeExceptionDescribe != '' ">
            AND t.node_exception_describe = #{e.nodeExceptionDescribe}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>