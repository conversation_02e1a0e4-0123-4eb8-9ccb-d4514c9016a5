<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.node.NodeConfigureMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.node.NodeConfigureEntity">
        <result column="id" property="id"/>
        <result column="configure_code" property="configureCode"/>
        <result column="specify_object" property="specifyObject"/>
        <result column="sale_order_number" property="saleOrderNumber"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="material_standard" property="materialStandard"/>
        <result column="material_type" property="materialType"/>
        <result column="state" property="state"/>
        <result column="approver" property="approver"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="actual_approver" property="actualApprover"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
        <result column="add_type" property="addType"/>
        <result column="pid" property="pid"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.configureCode != null and e.configureCode != '' ">
            AND t.configure_code = #{e.configureCode}
        </if>
        <if test="e.specifyObject != null and e.specifyObject != '' ">
            AND t.specify_object = #{e.specifyObject}
        </if>
        <if test="e.saleOrderNumber != null and e.saleOrderNumber != '' ">
            AND t.sale_order_number = #{e.saleOrderNumber}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.materialStandard != null and e.materialStandard != '' ">
            AND t.material_standard = #{e.materialStandard}
        </if>
        <if test="e.materialType != null and e.materialType != '' ">
            AND t.material_type = #{e.materialType}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.approvalTime != null and e.approvalTime != '' ">
            AND t.approval_time = #{e.approvalTime}
        </if>
        <if test="e.actualApprover != null and e.actualApprover != '' ">
            AND t.actual_approver = #{e.actualApprover}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>