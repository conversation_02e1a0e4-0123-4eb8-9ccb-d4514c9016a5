<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.node.NodeReportFiledMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.node.NodeReportFiledEntity">
		    <result column="id" property="id" />
		    <result column="type" property="type" />
		    <result column="type_name" property="typeName" />
		    <result column="filed_name" property="filedName" />
		    <result column="data_source" property="dataSource" />
		    <result column="input_type" property="inputType" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.type != null and e.type != '' ">
					AND t.type = #{e.type}
				</if>
				<if test="e.typeName != null and e.typeName != '' ">
					AND t.type_name = #{e.typeName}
				</if>
				<if test="e.filedName != null and e.filedName != '' ">
					AND t.filed_name = #{e.filedName}
				</if>
				<if test="e.dataSource != null and e.dataSource != '' ">
					AND t.data_source = #{e.dataSource}
				</if>
				<if test="e.inputType != null and e.inputType != '' ">
					AND t.input_type = #{e.inputType}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>