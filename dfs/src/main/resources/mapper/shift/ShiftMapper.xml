<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.shift.ShiftMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.shift.ShiftEntity">
        <result column="id" property="id"/>
        <result column="shift_type" property="shiftType"/>
        <result column="work_time" property="workTime"/>
        <result column="break_time" property="breakTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.shiftType != null and e.shiftType != '' ">
            AND t.shift_type = #{e.shiftType}
        </if>
        <if test="e.workTime != null and e.workTime != '' ">
            AND t.work_time = #{e.workTime}
        </if>
        <if test="e.breakTime != null and e.breakTime != '' ">
            AND t.break_time = #{e.breakTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>
    <select id="pageV2" resultType="com.yelink.dfs.entity.shift.ShiftEntity">
        select shift.* from dfs_shift shift
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
</mapper>