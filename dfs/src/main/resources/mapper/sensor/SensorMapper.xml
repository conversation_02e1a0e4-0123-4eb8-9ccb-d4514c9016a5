<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sensor.SensorMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.sensor.SensorEntity">
        <result column="id" property="id"/>
        <result column="cid" property="cid"/>
        <result column="aid" property="aid"/>
        <result column="gid" property="gid"/>
        <result column="fid" property="fid"/>
        <result column="parent_eui" property="parentEui"/>
        <result column="eui" property="eui"/>
        <result column="device_id" property="deviceId"/>
        <result column="name" property="name"/>
        <result column="rssi" property="rssi"/>
        <result column="online" property="online"/>
        <result column="lorasnr" property="lorasnr"/>
        <result column="trans_mode" property="transMode"/>
        <result column="support_set_down" property="supportSetDown"/>
        <result column="packet_loss_rate" property="packetLossRate"/>
        <result column="sensor_type" property="sensorType"/>
        <result column="sensor_type_name" property="sensorTypeName"/>
        <result column="power" property="power"/>
        <result column="power_type" property="powerType"/>
        <result column="power_voltage" property="powerVoltage"/>
        <result column="brand" property="brand"/>
        <result column="firm" property="firm"/>
        <result column="sp_model" property="spModel"/>
        <result column="agreement_version" property="agreementVersion"/>
        <result column="lat" property="lat"/>
        <result column="lng" property="lng"/>
        <result column="alt" property="alt"/>
        <result column="rsrp" property="rsrp"/>
        <result column="sp" property="sp"/>
        <result column="sensor_value" property="sensorValue"/>
        <result column="gateway_id" property="gatewayId"/>
        <result column="status_code" property="statusCode"/>
        <result column="alarm_config" property="alarmConfig"/>
        <result column="place" property="place"/>
        <result column="img" property="img"/>
        <result column="remark" property="remark"/>
        <result column="bind_state" property="bindState"/>
        <result column="arrive_time" property="arriveTime"/>
        <result column="event_time" property="eventTime"/>
        <result column="heart_period" property="heartPeriod"/>
        <result column="model_name" property="modelName"/>
        <result column="product_id" property="productId"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.cid != null and e.cid != '' ">
            AND t.cid = #{e.cid}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.gid != null and e.gid != '' ">
            AND t.gid = #{e.gid}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.parentEui != null and e.parentEui != '' ">
            AND t.parent_eui = #{e.parentEui}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.rssi != null and e.rssi != '' ">
            AND t.rssi = #{e.rssi}
        </if>
        <if test="e.online != null and e.online != '' ">
            AND t.online = #{e.online}
        </if>
        <if test="e.lorasnr != null and e.lorasnr != '' ">
            AND t.lorasnr = #{e.lorasnr}
        </if>
        <if test="e.transMode != null and e.transMode != '' ">
            AND t.trans_mode = #{e.transMode}
        </if>
        <if test="e.supportSetDown != null and e.supportSetDown != '' ">
            AND t.support_set_down = #{e.supportSetDown}
        </if>
        <if test="e.packetLossRate != null and e.packetLossRate != '' ">
            AND t.packet_loss_rate = #{e.packetLossRate}
        </if>
        <if test="e.sensorType != null and e.sensorType != '' ">
            AND t.sensor_type = #{e.sensorType}
        </if>
        <if test="e.sensorTypeName != null and e.sensorTypeName != '' ">
            AND t.sensor_type_name = #{e.sensorTypeName}
        </if>
        <if test="e.power != null and e.power != '' ">
            AND t.power = #{e.power}
        </if>
        <if test="e.powerType != null and e.powerType != '' ">
            AND t.power_type = #{e.powerType}
        </if>
        <if test="e.powerVoltage != null and e.powerVoltage != '' ">
            AND t.power_voltage = #{e.powerVoltage}
        </if>
        <if test="e.brand != null and e.brand != '' ">
            AND t.brand = #{e.brand}
        </if>
        <if test="e.firm != null and e.firm != '' ">
            AND t.firm = #{e.firm}
        </if>
        <if test="e.spModel != null and e.spModel != '' ">
            AND t.sp_model = #{e.spModel}
        </if>
        <if test="e.agreementVersion != null and e.agreementVersion != '' ">
            AND t.agreement_version = #{e.agreementVersion}
        </if>
        <if test="e.lat != null and e.lat != '' ">
            AND t.lat = #{e.lat}
        </if>
        <if test="e.lng != null and e.lng != '' ">
            AND t.lng = #{e.lng}
        </if>
        <if test="e.alt != null and e.alt != '' ">
            AND t.alt = #{e.alt}
        </if>
        <if test="e.rsrp != null and e.rsrp != '' ">
            AND t.rsrp = #{e.rsrp}
        </if>
        <if test="e.sp != null and e.sp != '' ">
            AND t.sp = #{e.sp}
        </if>
        <if test="e.sensorValue != null and e.sensorValue != '' ">
            AND t.sensor_value = #{e.sensorValue}
        </if>
        <if test="e.gatewayId != null and e.gatewayId != '' ">
            AND t.gateway_id = #{e.gatewayId}
        </if>
        <if test="e.statusCode != null and e.statusCode != '' ">
            AND t.status_code = #{e.statusCode}
        </if>
        <if test="e.alarmConfig != null and e.alarmConfig != '' ">
            AND t.alarm_config = #{e.alarmConfig}
        </if>
        <if test="e.place != null and e.place != '' ">
            AND t.place = #{e.place}
        </if>
        <if test="e.img != null and e.img != '' ">
            AND t.img = #{e.img}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.bindState != null and e.bindState != '' ">
            AND t.bind_state = #{e.bindState}
        </if>
        <if test="e.arriveTime != null and e.arriveTime != '' ">
            AND t.arrive_time = #{e.arriveTime}
        </if>
        <if test="e.eventTime != null and e.eventTime != '' ">
            AND t.event_time = #{e.eventTime}
        </if>
        <if test="e.heartPeriod != null and e.heartPeriod != '' ">
            AND t.heart_period = #{e.heartPeriod}
        </if>
        <if test="e.modelName != null and e.modelName != '' ">
            AND t.model_name = #{e.modelName}
        </if>
        <if test="e.productId != null and e.productId != '' ">
            AND t.product_id = #{e.productId}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

    <update id="updateHeartPeriodByEui">
        update dfs_sensor set heart_period = #{heartPeriod} where eui = #{eui}
    </update>

    <select id="getSensorListByFid" resultMap="BaseResultMap">
        select s.*
        from dfs_fac_sensor fs
        left join dfs_sensor s on s.eui=fs.eui
        where fs.fid=#{fid} and s.fid is null
    </select>

    <select id="getSensorPageByFid" resultMap="BaseResultMap">
        select s.*
        from dfs_fac_sensor fs
        left join dfs_sensor s on s.eui=fs.eui
        where fs.fid=#{fid} and s.fid is null
    </select>

    <select id="screenList" resultType="com.yelink.dfs.entity.sensor.SensorEntity">
        select
        s.id,f.fid,s.eui,
        f.fname name,f.state remark,f.place place,
        s.sensor_type sensorType,
        s.sensor_value sensorValue,
        s.sensor_type_name sensorTypeName
        from dfs_facilities f
        left join(
        select * from(
        SELECT *,
        case when @groupid=fid then @row:=@row+1 else @row:=1 end rownum,@groupid:=fid
        FROM dfs_fac_sensor,(select @row:= 0) tmp_row,(select @groupid:= '')tmp_group order by
        fid,FIELD(position,'1','0','2')
        ) a where rownum=1
        ) fs on fs.fid=f.fid
        left join
        dfs_sensor s on s.eui=fs.eui
        where f.type_one_code in(#{cubicleCode},#{produceCode})
        and s.sensor_type in
        <foreach item="item" collection="code" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by f.production_line_id,line_order
    </select>

    <select id="safetyList" resultType="com.yelink.dfs.entity.sensor.SensorEntity">
        select
        s.id,f.fid,s.eui,
        f.fname name,f.place place,
        s.sensor_type sensorType,
        s.sensor_value sensorValue,
        s.sensor_type_name sensorTypeName
        from dfs_facilities f
        left join
        dfs_fac_sensor fs on fs.fid=f.fid
        left join
        dfs_sensor s on s.eui=fs.eui
        where f.type_one_code = #{securityCode}
        and s.sensor_type in
        <foreach item="item" collection="code" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="sensorList" resultType="com.yelink.dfs.entity.sensor.SensorEntity">
        select * from dfs_sensor t
        where 1=1
        <if test="eui != null and eui !='' ">
            and t.eui like '%${eui}%'
        </if>
        <if test="name != null and name !='' ">
            and t.`name` like '%${name}%'
        </if>
        <if test="sensorType != null and sensorType !='' ">
            and t.sensor_type in (${sensorType})
        </if>
        order by t.sensor_type desc

    </select>

    <update id="addPartition">
        CALL create_partition_by_day('dfs_sensor_record',NOW());
    </update>
</mapper>
