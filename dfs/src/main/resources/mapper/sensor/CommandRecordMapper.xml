<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sensor.CommandRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.sensor.CommandRecordEntity">
        <result column="id" property="id"/>
        <result column="command_id" property="commandId"/>
        <result column="timestamp" property="timestamp"/>
        <result column="result" property="result"/>
        <result column="result_detail" property="resultDetail"/>
        <result column="eui" property="eui"/>
        <result column="command_detail" property="commandDetail"/>
        <result column="operator" property="operator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.commandId != null and e.commandId != '' ">
            AND t.command_id = #{e.commandId}
        </if>
        <if test="e.timestamp != null and e.timestamp != '' ">
            AND t.timestamp = #{e.timestamp}
        </if>
        <if test="e.result != null and e.result != '' ">
            AND t.result = #{e.result}
        </if>
        <if test="e.resultDetail != null and e.resultDetail != '' ">
            AND t.result_detail = #{e.resultDetail}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.commandDetail != null and e.commandDetail != '' ">
            AND t.command_detail = #{e.commandDetail}
        </if>
        <if test="e.operator != null and e.operator != '' ">
            AND t.operator = #{e.operator}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>
