<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sensor.SensorRelatedMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.sensor.SensorRelatedEntity">
		    <result column="id" property="id" />
		    <result column="sensor_id" property="sensorId" />
		    <result column="sensor_eui" property="sensorEui" />
		    <result column="sensor_name" property="sensorName" />
		    <result column="related_sensor_id" property="relatedSensorId" />
		    <result column="related_sensor_eui" property="relatedSensorEui" />
		    <result column="related_sensor_name" property="relatedSensorName" />
		    <result column="range_start" property="rangeStart" />
		    <result column="range_end" property="rangeEnd" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.sensorId != null and e.sensorId != '' ">
					AND t.sensor_id = #{e.sensorId}
				</if>
				<if test="e.sensorEui != null and e.sensorEui != '' ">
					AND t.sensor_eui = #{e.sensorEui}
				</if>
				<if test="e.sensorName != null and e.sensorName != '' ">
					AND t.sensor_name = #{e.sensorName}
				</if>
				<if test="e.relatedSensorId != null and e.relatedSensorId != '' ">
					AND t.related_sensor_id = #{e.relatedSensorId}
				</if>
				<if test="e.relatedSensorEui != null and e.relatedSensorEui != '' ">
					AND t.related_sensor_eui = #{e.relatedSensorEui}
				</if>
				<if test="e.relatedSensorName != null and e.relatedSensorName != '' ">
					AND t.related_sensor_name = #{e.relatedSensorName}
				</if>
				<if test="e.rangeStart != null and e.rangeStart != '' ">
					AND t.range_start = #{e.rangeStart}
				</if>
				<if test="e.rangeEnd != null and e.rangeEnd != '' ">
					AND t.range_end = #{e.rangeEnd}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>