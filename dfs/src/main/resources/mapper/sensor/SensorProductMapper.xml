<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sensor.SensorProductMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.sensor.SensorProductEntity">
        <result column="id" property="id"/>
        <result column="sensor_type_name" property="sensorTypeName"/>
        <result column="product_key" property="productKey"/>
        <result column="sensor_type" property="sensorType"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.sensorTypeName != null and e.sensorTypeName != '' ">
            AND t.sensor_type_name = #{e.sensorTypeName}
        </if>
        <if test="e.productKey != null and e.productKey != '' ">
            AND t.product_key = #{e.productKey}
        </if>
        <if test="e.sensorType != null and e.sensorType != '' ">
            AND t.sensor_type = #{e.sensorType}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>