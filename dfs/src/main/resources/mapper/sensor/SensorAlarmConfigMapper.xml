<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sensor.SensorAlarmConfigMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.sensor.SensorAlarmConfigEntity">
        <result column="id" property="id"/>
        <result column="eui" property="eui"/>
        <result column="sensor_type" property="sensorType"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_des" property="alarmDes"/>
        <result column="alarm_advice" property="alarmAdvice"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.sensorType != null and e.sensorType != '' ">
            AND t.sensor_type = #{e.sensorType}
        </if>
        <if test="e.alarmType != null and e.alarmType != '' ">
            AND t.alarm_type = #{e.alarmType}
        </if>
        <if test="e.alarmLevel != null and e.alarmLevel != '' ">
            AND t.alarm_level = #{e.alarmLevel}
        </if>
        <if test="e.alarmDes != null and e.alarmDes != '' ">
            AND t.alarm_des = #{e.alarmDes}
        </if>
        <if test="e.alarmAdvice != null and e.alarmAdvice != '' ">
            AND t.alarm_advice = #{e.alarmAdvice}
        </if>
    </sql>

</mapper>
