<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.sensor.SensorRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.sensor.SensorRecordEntity">
        <result column="sensor_record_id" property="sensorRecordId"/>
        <result column="eui" property="eui"/>
        <result column="create_time" property="createTime"/>
        <result column="production_line_id" property="productionLineId"/>
        <result column="production_line_name" property="productionLineName"/>
        <result column="schedule_id" property="scheduleId"/>
        <result column="schedule_name" property="scheduleName"/>
        <result column="user_name" property="userName"/>
        <result column="nick_name" property="nickName"/>
        <result column="fid" property="fid"/>
        <result column="fname" property="fname"/>
        <result column="sensor_value" property="sensorValue"/>
        <result column="message_code" property="messageCode"/>
        <result column="message_code_name" property="messageCodeName"/>
        <result column="des" property="des"/>
        <result column="initSensorValue" property="initSensorValue"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.sensorRecordId != null and e.sensorRecordId != '' ">
            AND t.sensor_record_id = #{e.sensorRecordId}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.productionLineName != null and e.productionLineName != '' ">
            AND t.production_line_name = #{e.productionLineName}
        </if>
        <if test="e.scheduleId != null and e.scheduleId != '' ">
            AND t.schedule_id = #{e.scheduleId}
        </if>
        <if test="e.scheduleName != null and e.scheduleName != '' ">
            AND t.schedule_name = #{e.scheduleName}
        </if>
        <if test="e.userName != null and e.userName != '' ">
            AND t.user_name = #{e.userName}
        </if>
        <if test="e.nickName != null and e.nickName != '' ">
            AND t.nick_name = #{e.nickName}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.fname != null and e.fname != '' ">
            AND t.fname = #{e.fname}
        </if>
        <if test="e.sensorValue != null and e.sensorValue != '' ">
            AND t.sensor_value = #{e.sensorValue}
        </if>
        <if test="e.messageCode != null and e.messageCode != '' ">
            AND t.message_code = #{e.messageCode}
        </if>
        <if test="e.messageCodeName != null and e.messageCodeName != '' ">
            AND t.message_code_name = #{e.messageCodeName}
        </if>
        <if test="e.des != null and e.des != '' ">
            AND t.des = #{e.des}
        </if>
    </sql>

    <insert id="add">
        insert into dfs_sensor_record (eui,create_time,sensor_value,des,init_sensor_value)
        values (#{sensorEntity.eui},
        <choose>
            <when test="sensorEntity.eventTime != null">
                #{sensorEntity.eventTime}
            </when>
            <otherwise>
                #{currentDate}
            </otherwise>
        </choose>
        ,#{sensorEntity.sensorValue},#{sensorEntity.des},#{sensorEntity.initSensorValue});
    </insert>

    <insert id="addBatch">
        insert into dfs_sensor_record (eui,create_time,sensor_value,des,init_sensor_value)
        values
        <foreach item="sensorEntity" collection="sensorEntities" separator="," index="">
            (#{sensorEntity.eui},
            <choose>
                <when test="sensorEntity.eventTime != null">
                    #{sensorEntity.eventTime}
                </when>
                <otherwise>
                    #{currentDate}
                </otherwise>
            </choose>
            ,#{sensorEntity.sensorValue},#{sensorEntity.des},#{sensorEntity.initSensorValue})
        </foreach>

    </insert>

    <insert id="saveBatch">
        insert into dfs_sensor_record(sensor_record_id,eui, create_time, production_line_id,production_line_name, schedule_id, schedule_name, user_name,
         nick_name, fid,fname,sensor_value,message_code,message_code_name,des,init_sensor_value) values
        <foreach collection="sensorRecordEntities" separator="," item="item">
            (#{item.sensorRecordId},#{item.eui},#{item.createTime},#{item.productionLineId},#{item.productionLineName},#{item.scheduleId},#{item.scheduleName},
            #{item.userName},#{item.nickName},#{item.fid},#{item.fname},#{item.sensorValue},#{item.messageCode},#{item.messageCodeName},#{item.des},#{item.initSensorValue})
        </foreach>
    </insert>


    <select id="limitByEuiCode" resultType="java.util.Date">
        select create_time from dfs_sensor_record where eui = #{eui} and message_code = #{alarmCode} order by
        sensor_record_id desc limit 0,#{limit}
    </select>

    <select id="getCreateTimeAndSensorValueList" resultMap="BaseResultMap">
        select create_time,sensor_value from dfs_sensor_record where sensor_record_id in (
            select sensor_record_id from dfs_sensor_record where eui = #{eui}
            <if test="startDate != null and startDate != '' ">
                AND create_time &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' ">
                AND create_time &lt;= #{endDate}
            </if>
        ) ORDER BY create_time DESC, sensor_record_id DESC;
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
        select max(sensor_record_id) from dfs_sensor_record
    </select>

</mapper>
