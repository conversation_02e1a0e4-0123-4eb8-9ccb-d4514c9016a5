<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.maintain.MaintainSchemeFaultFacilitiesMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.maintain.MaintainSchemeFaultFacilitiesEntity">
		    <result column="id" property="id" />
		    <result column="scheme_id" property="schemeId" />
		    <result column="fault_fid" property="faultFid" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.schemeId != null and e.schemeId != '' ">
					AND t.scheme_id = #{e.schemeId}
				</if>
				<if test="e.faultFid != null and e.faultFid != '' ">
					AND t.fault_fid = #{e.faultFid}
				</if>
	</sql>

</mapper>