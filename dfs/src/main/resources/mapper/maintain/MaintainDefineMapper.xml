<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.maintain.MaintainDefineMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.maintain.MaintainDefineEntity">
		    <result column="maintain_id" property="maintainId" />
		    <result column="maintain_code" property="maintainCode" />
		    <result column="maintain_name" property="maintainName" />
		    <result column="maintain_type" property="maintainType" />
		    <result column="description" property="description" />
		    <result column="remark" property="remark" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.maintainId != null and e.maintainId != '' ">
					AND t.maintain_id = #{e.maintainId}
				</if>
				<if test="e.maintainCode != null and e.maintainCode != '' ">
					AND t.maintain_code = #{e.maintainCode}
				</if>
				<if test="e.maintainName != null and e.maintainName != '' ">
					AND t.maintain_name = #{e.maintainName}
				</if>
				<if test="e.maintainType != null and e.maintainType != '' ">
					AND t.maintain_type = #{e.maintainType}
				</if>
				<if test="e.description != null and e.description != '' ">
					AND t.description = #{e.description}
				</if>
				<if test="e.remark != null and e.remark != '' ">
					AND t.remark = #{e.remark}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>