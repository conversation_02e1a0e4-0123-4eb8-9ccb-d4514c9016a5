<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.maintain.MaintainRecordFacilitiesMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.maintain.MaintainRecordFacilitiesEntity">
		    <result column="id" property="id" />
		    <result column="record_id" property="recordId" />
		    <result column="fid" property="fid" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.recordId != null and e.recordId != '' ">
					AND t.record_id = #{e.recordId}
				</if>
				<if test="e.fid != null and e.fid != '' ">
					AND t.fid = #{e.fid}
				</if>
	</sql>

</mapper>