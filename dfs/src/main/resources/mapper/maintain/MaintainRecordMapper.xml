<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.maintain.MaintainRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.maintain.MaintainRecordEntity">
        <result column="record_id" property="recordId"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="bar_code" property="barCode"/>
        <result column="scheme_id" property="schemeId"/>
        <result column="maintain_id" property="maintainId"/>
        <result column="maintain_name" property="maintainName"/>
        <result column="maintain_type" property="maintainType"/>
        <result column="fault_type" property="faultType"/>
        <result column="work_order" property="workOrder"/>
        <result column="fid" property="fid"/>
        <result column="fname" property="fname"/>
        <result column="remark" property="remark"/>
        <result column="pic_url" property="picUrl"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <resultMap id="count" type="integer">
        <result column="total"></result>
    </resultMap>

    <sql id="select_content">
        <if test="e.recordId != null and e.recordId != '' ">
            AND t.record_id = #{e.recordId}
        </if>
        <if test="e.serialNumber != null and e.serialNumber != '' ">
            AND t.serial_number = #{e.serialNumber}
        </if>
        <if test="e.barCode != null and e.barCode != '' ">
            AND t.bar_code = #{e.barCode}
        </if>
        <if test="e.schemeId != null and e.schemeId != '' ">
            AND t.scheme_id = #{e.schemeId}
        </if>
        <if test="e.maintainId != null and e.maintainId != '' ">
            AND t.maintain_id = #{e.maintainId}
        </if>
        <if test="e.maintainName != null and e.maintainName != '' ">
            AND t.maintain_name = #{e.maintainName}
        </if>
        <if test="e.maintainType != null and e.maintainType != '' ">
            AND t.maintain_type = #{e.maintainType}
        </if>
        <if test="e.faultType != null and e.faultType != '' ">
            AND t.fault_type = #{e.faultType}
        </if>
        <if test="e.workOrder != null and e.workOrder != '' ">
            AND t.work_order = #{e.workOrder}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.fname != null and e.fname != '' ">
            AND t.fname = #{e.fname}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.picUrl != null and e.picUrl != '' ">
            AND t.pic_url = #{e.picUrl}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>
    <select id="getPoorStationData" resultType="java.util.Map">
        SELECT
        a.fid,
        count( 1 ) `num`
        FROM
        (
        SELECT
        m.bar_code,
        f.fid
        FROM
        dfs_maintain_record m
        INNER JOIN dfs_work_order wo  on  m.work_order = wo.work_order_number
        JOIN dfs_maintain_record_facilities f ON f.record_id = m.record_id
        <where>
            AND m.create_time between #{startTime} and #{endTime}
            AND bar_code IS NOT NULL
            AND wo.line_id = #{lineId}
        </where>
        GROUP BY
        bar_code,
        f.fid
        ) a
        GROUP BY
        a.fid

    </select>

    <select id="getMaintainByDate" resultType="java.lang.Integer">
        SELECT count(1) FROM (
        SELECT
        max( work_order ) work_order,
        max( fid ) fid,
        date_format( max( create_time ), '%Y-%m-%d' ) create_time
        FROM
        dfs_maintain_record
        GROUP BY
        record_id
        )a WHERE 1=1
        <if test="createTime!=null">
            and a.create_time=DATE_FORMAT(#{createTime},'%Y-%m-%d')
        </if>
        <if test="workOrder!=null and workOrder!=''">
            and a.work_order=#{workOrder}
        </if>
        <if test="fid!=null">
            and a.fid=#{fid}
        </if>
    </select>

    <select id="getStatementList" resultType="com.yelink.dfs.entity.maintain.dto.MaintainStatementDTO">
        SELECT
        new_tb.*, COUNT(new_tb.record_id) AS maintainCount,  DATE_FORMAT(new_tb.create_time,'%Y-%m-%d') AS date
        FROM (
            SELECT mr.*, wr.material_code, wr.line_id, wr.line_name, wr.finish_count,m.name as materialName, m.factory_model as factoryModel , m.standard, wr.sale_order_number
                    , wr.work_order_name,wr.product_order_number
            --  维修记录单
            FROM dfs_maintain_record mr
            -- 	工单
            LEFT JOIN dfs_work_order wr ON wr.work_order_number = mr.work_order
            --  物表
            LEFT JOIN dfs_material m on m.code = wr.material_code
            <where>
                <if test="fid != null and fid != ''">
                    AND mr.fid = #{fid}
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    AND mr.work_order like CONCAT ('%',#{workOrderNumber},'%')
                </if>
                <if test="materialCode != null and materialCode != ''">
                    AND wr.material_code like CONCAT ('%',#{materialCode},'%')
                </if>
                <if test="factoryModel != null and factoryModel != ''">
                    AND m.factory_model like CONCAT ('%',#{factoryModel},'%')
                </if>
                <if test="start != null and start != '' and end != null and end != ''">
                    AND mr.create_time between #{start} and #{end}
                </if>
            </where>
            -- 条码去重
            GROUP BY bar_code
        ) AS new_tb
        --  按工位,工单,日期分组
        GROUP BY new_tb.fid,new_tb.work_order,DATE_FORMAT(new_tb.create_time,'%Y-%m-%d')
        --  时间排倒序
        ORDER BY new_tb.create_time DESC
    </select>

    <select id="getSumForStatement" resultType="com.yelink.dfs.entity.maintain.dto.MaintainStatementDTO">
        SELECT
        IFNULL( sum( count_tb.finish_count ), 0 ) AS finish_count,
        IFNULL( sum( count_tb.maintainCount ), 0 ) AS maintainCount
        FROM (
            SELECT
            new_tb.finish_count AS finish_count, COUNT(new_tb.record_id) AS maintainCount
            FROM (
                SELECT
                mr.*, wr.finish_count
                --  维修记录单
                FROM dfs_maintain_record mr
                -- 	工单
                LEFT JOIN dfs_work_order wr ON wr.work_order_number = mr.work_order
                --  物表
                LEFT JOIN dfs_material m on m.code = wr.material_code
                <where>
                    <if test="workOrderNumber != null and workOrderNumber != ''">
                        AND mr.work_order like CONCAT ('%',#{workOrderNumber},'%')
                    </if>
                    <if test="materialCode != null and materialCode != ''">
                        AND wr.material_code like CONCAT ('%',#{materialCode},'%')
                    </if>
                    <if test="factoryModel != null and factoryModel != ''">
                        AND m.factory_model like CONCAT ('%',#{factoryModel},'%')
                    </if>
                    <if test="fid != null and fid != ''">
                        AND mr.fid = #{fid}
                    </if>
                    <if test="start != null and start != '' and end != null and end != ''">
                        AND mr.create_time between #{start} and #{end}
                    </if>
                </where>
                -- 条码去重
                GROUP BY bar_code
            ) AS new_tb
            --  按工位,工单,日期分组
            GROUP BY new_tb.fid,new_tb.work_order,DATE_FORMAT(new_tb.create_time,'%Y-%m-%d')
        ) AS count_tb
    </select>

    <select id="getRecordLists" resultType="com.yelink.dfs.entity.maintain.MaintainRecordEntity">
        SELECT
        new_tb.*, DATE_FORMAT(new_tb.create_time,'%Y-%m-%d') AS date
        FROM (
        SELECT
            mr.*,
            wr.material_code,
            wr.line_id,
            wr.line_name,
            wr.finish_count,
            wr.sale_order_number,
            wr.work_order_name,
            wr.product_order_number,
            wr.work_center_id,
            wr.work_center_name,
            m.NAME AS materialName,
            m.factory_model AS factoryModel,
            m.standard
        FROM dfs_maintain_record mr
        -- 	工单
        LEFT JOIN dfs_work_order wr ON wr.work_order_number = mr.work_order
        --  物表
        LEFT JOIN dfs_material m on m.code = wr.material_code
        -- 	维修记录与工位关联表
        LEFT JOIN dfs_maintain_record_facilities mrf ON mrf.record_id = mr.record_id
        -- 	用户表
        LEFT JOIN sys_users u ON u.user_name = mr.create_by
        <where>
            <if test="selectDTO.fids != null and selectDTO.fids.size > 0">
                AND mr.fid IN
                <foreach item="item" collection="selectDTO.fids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.defectIds != null and selectDTO.defectIds.size > 0">
                AND mr.defect_id IN
                <foreach item="item" collection="selectDTO.defectIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.maintainTypes != null and selectDTO.maintainTypes.size > 0">
                AND mr.maintain_type IN
                <foreach item="item" collection="selectDTO.maintainTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.maintainResultTypes != null and selectDTO.maintainResultTypes.size > 0">
                AND mr.maintain_result_type IN
                <foreach item="item" collection="selectDTO.maintainResultTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.backCraftProcedureIds != null and selectDTO.backCraftProcedureIds.size > 0">
                AND mr.back_procedure_id IN
                <foreach item="item" collection="selectDTO.backCraftProcedureIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.workOrder != null and selectDTO.workOrder != ''">
                AND mr.work_order like CONCAT ('%',#{selectDTO.workOrder},'%')
            </if>
            <if test="selectDTO.workOrderNumbers != null and selectDTO.workOrderNumbers.size > 0">
                AND mr.work_order IN
                <foreach item="item" collection="selectDTO.workOrderNumbers" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.factoryModel != null and selectDTO.factoryModel != ''">
                AND m.factory_model like CONCAT ('%',#{selectDTO.factoryModel},'%')
            </if>
            <if test="selectDTO.materialCode != null and selectDTO.materialCode != ''">
                AND wr.material_code like CONCAT ('%',#{selectDTO.materialCode},'%')
            </if>
            <if test="selectDTO.start != null and selectDTO.start != '' and selectDTO.end != null and selectDTO.end != ''">
                AND mr.create_time between #{selectDTO.start} and #{selectDTO.end}
            </if>
            <if test="selectDTO.serialNumber != null and selectDTO.serialNumber != ''">
                AND mr.serial_number like CONCAT ('%',#{selectDTO.serialNumber},'%')
            </if>
            <if test="selectDTO.barCode != null and selectDTO.barCode != ''">
                AND mr.bar_code like CONCAT ('%',#{selectDTO.barCode},'%')
            </if>
            <if test="selectDTO.fullBarCode != null and selectDTO.fullBarCode != ''">
                AND mr.bar_code = #{selectDTO.fullBarCode}
            </if>
            <if test="selectDTO.maintainName != null and selectDTO.maintainName != ''">
                AND mr.maintain_name like CONCAT ('%',#{selectDTO.maintainName},'%')
            </if>
            <if test="selectDTO.faultTypes != null and selectDTO.faultTypes.size > 0">
                AND mr.fault_type IN
                <foreach item="item" collection="selectDTO.faultTypes" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.faultFids != null and selectDTO.faultFids.size > 0">
                AND mrf.fid IN
                <foreach item="item" collection="selectDTO.faultFids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.remark != null and selectDTO.remark != ''">
                AND mr.remark like CONCAT ('%',#{selectDTO.remark},'%')
            </if>
            <if test="selectDTO.materialName != null and selectDTO.materialName != ''">
                AND m.name like CONCAT ('%',#{selectDTO.materialName},'%')
            </if>
            <if test="selectDTO.materialStandard != null and selectDTO.materialStandard != ''">
                AND m.standard like CONCAT ('%',#{selectDTO.materialStandard},'%')
            </if>
            <if test="selectDTO.productOrderNumber != null and selectDTO.productOrderNumber != ''">
                AND wr.product_order_number like CONCAT ('%',#{selectDTO.productOrderNumber},'%')
            </if>
            <if test="selectDTO.saleOrderNumber != null and selectDTO.saleOrderNumber != ''">
                AND wr.sale_order_number like CONCAT ('%',#{selectDTO.saleOrderNumber},'%')
            </if>
            <if test="selectDTO.workOrderName != null and selectDTO.workOrderName != ''">
                AND wr.work_order_name like CONCAT ('%',#{selectDTO.workOrderName},'%')
            </if>
            <if test="selectDTO.createByName != null and selectDTO.createByName != ''">
                AND u.nick_name like CONCAT ('%',#{selectDTO.createByName},'%')
            </if>
            <if test="selectDTO.workCenterIdList != null and selectDTO.workCenterIdList.size > 0">
                AND wr.work_center_id IN
                <foreach item="item" collection="selectDTO.workCenterIdList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="selectDTO.codeRecordIds != null and selectDTO.codeRecordIds.size > 0">
                AND mr.code_record_id IN
                <foreach item="item" collection="selectDTO.codeRecordIds" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY mr.record_id
        ) AS new_tb
        --  时间排倒序
        ORDER BY new_tb.create_time DESC
    </select>

    <select id="getMaintainBylineAndDate" resultMap="BaseResultMap">
        SELECT mr.* FROM dfs_maintain_record mr INNER JOIN dfs_work_order wo  on  mr.work_order = wo.work_order_number
        where  mr.create_time BETWEEN #{startTime} and #{endTime}  and wo.line_id = #{lineId}
    </select>

    <select id="getMaintainGridProportion" resultType="com.yelink.dfs.entity.maintain.dto.MaintainGridProportionDTO">
        SELECT
        r.maintain_name AS `maintainName`,
        COUNT(1) AS `count`
        FROM `dfs_maintain_record`  r
        INNER JOIN `dfs_facilities` f ON r.fid = f.fid
        INNER JOIN `dfs_grid` g ON g.gid = f.gid
        WHERE
        DATE_FORMAT(r.create_time,'%Y-%m') = DATE_FORMAT(now(),'%Y-%m')
        <if test="gid!=null">
            and g.gid=#{gid}
        </if>
        GROUP BY r.maintain_name
        ORDER BY COUNT(1) DESC, r.create_time DESC
    </select>
</mapper>
