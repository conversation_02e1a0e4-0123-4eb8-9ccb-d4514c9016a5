<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.maintain.MaintainRecordPicMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.maintain.MaintainRecordPicEntity">
		    <result column="detail_id" property="id" />
		    <result column="record_id" property="recordId" />
		    <result column="serial_number" property="serialNumber" />
		    <result column="bar_code" property="barCode" />
		    <result column="url" property="url" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.recordId != null and e.recordId != '' ">
					AND t.record_id = #{e.recordId}
				</if>
				<if test="e.serialNumber != null and e.serialNumber != '' ">
					AND t.serial_number = #{e.serialNumber}
				</if>
				<if test="e.barCode != null and e.barCode != '' ">
					AND t.bar_code = #{e.barCode}
				</if>
				<if test="e.url != null and e.url != '' ">
					AND t.url = #{e.url}
				</if>
	</sql>

</mapper>