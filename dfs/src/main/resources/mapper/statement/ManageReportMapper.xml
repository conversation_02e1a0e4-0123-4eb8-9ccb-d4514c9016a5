<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.statement.ManageReportMapper">

    <select id="pageTaskExecuteResult" resultType="com.yelink.dfs.entity.statement.vo.TaskExecuteResultVO">
        SELECT
            downloadRecord.excel_task_id AS excelTaskId,
            downloadRecord.task_code AS taskCode,
            downloadRecord.report_name AS reportName,
            downloadRecord.execution_frequency AS executionFrequency,
            downloadRecord.create_time AS executeTime,
            excelTask.`status` AS `status`,
            excelTask.total_count AS totalCount,
            excelTask.success_count AS successCount,
            excelTask.failed_count AS failedCount,
            excelTask.file_name AS fileName,
            excelTask.file_url AS fileUrl
        FROM dfs_manage_report_download_record downloadRecord
        INNER JOIN excel_task excelTask ON downloadRecord.excel_task_id=excelTask.id
        where downloadRecord.deleted = 0
        <if test="selectDTO.isOnlySelectTask != null and selectDTO.isOnlySelectTask == true">
            AND downloadRecord.task_code is not null
        </if>
        <if test="selectDTO.isOnlySelectDownload != null and selectDTO.isOnlySelectDownload == true">
            AND downloadRecord.task_code is null
        </if>
        <if test="selectDTO.model != null and selectDTO.model != ''">
            AND downloadRecord.model = #{selectDTO.model}
        </if>
        <if test="selectDTO.taskCode != null and selectDTO.taskCode != ''">
            and downloadRecord.task_code LIKE CONCAT('%',#{selectDTO.taskCode},'%')
        </if>
        <if test="selectDTO.fullTaskCode != null and selectDTO.fullTaskCode != ''">
            and downloadRecord.task_code = #{selectDTO.fullTaskCode}
        </if>
        <if test="selectDTO.reportName != null and selectDTO.reportName != ''">
            and downloadRecord.report_name LIKE CONCAT('%',#{selectDTO.reportName},'%')
        </if>
        <if test="selectDTO.executionFrequency != null and selectDTO.executionFrequency != ''">
            and downloadRecord.execution_frequency in
            <foreach item="item" collection="selectDTO.executionFrequency.split(',')" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.status != null and selectDTO.status != '' ">
            AND excelTask.`status` in
            <foreach item="item" collection="selectDTO.status.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="selectDTO.fileUrl != null and selectDTO.fileUrl != ''">
            and excelTask.file_url = #{selectDTO.fileUrl}
        </if>
        <if test="selectDTO.executeStartTime != null and selectDTO.executeEndTime != null">
            AND downloadRecord.create_time BETWEEN #{selectDTO.executeStartTime} AND #{selectDTO.executeEndTime}
        </if>
        ORDER BY downloadRecord.create_time DESC
    </select>

    <update id="updateManageReportById" parameterType="com.yelink.dfs.entity.statement.ManageReportEntity">
        UPDATE dfs_manage_report
        <set>
            <if test="manageReport.reportName != null">
                report_name = #{manageReport.reportName},
            </if>
            <if test="manageReport.subhead != null">
                subhead = #{manageReport.subhead},
            </if>
            <if test="manageReport.state != null">
                `state` = #{manageReport.state},
            </if>
            <if test="manageReport.defaultInner != null">
                default_inner = #{manageReport.defaultInner},
            </if>
            <if test="manageReport.model != null">
                model = #{manageReport.model},
            </if>
            <if test="manageReport.dataSources != null">
                data_sources = #{manageReport.dataSources},
            </if>
            <if test="manageReport.dataSourceConfig != null">
                data_source_config = #{manageReport.dataSourceConfig},
            </if>
            <if test="manageReport.templateId != null">
                template_id = #{manageReport.templateId},
            </if>
            update_time = NOW(),
            <if test="manageReport.updateBy != null">
                update_by = #{manageReport.updateBy},
            </if>
        </set>
        WHERE report_id = #{manageReport.reportId}
    </update>
</mapper>
