<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.statement.ManageSourceMapper">

    <insert id="insertManageSource" >
        INSERT INTO dfs_manage_source (source_code, source_name, source_type, source_desc, api_url, method, header, body, `table_schema`, `table_name`, script_type, trans_script, create_time, update_time, create_by, update_by)
        VALUES (#{manageSource.sourceCode}, #{manageSource.sourceName}, #{manageSource.sourceType},
                #{manageSource.sourceDesc}, #{manageSource.apiUrl}, #{manageSource.method},
                #{manageSource.header}, #{manageSource.body}, #{manageSource.tableSchema},
                #{manageSource.tableName}, #{manageSource.scriptType}, #{manageSource.transScript},
                NOW(), #{manageSource.updateTime}, #{manageSource.createBy}, #{manageSource.updateBy});
    </insert>

    <insert id="insertManageSourceField">
        INSERT INTO dfs_manage_source_field (source_code, `table_schema`, `table_name`, field_code, field_name, field_type, field_length, field_point, field_default_value, field_remark)
        VALUES (#{sourceField.sourceCode}, #{sourceField.tableSchema}, #{sourceField.tableName}, #{sourceField.fieldCode}, #{sourceField.fieldName}, #{sourceField.fieldType}, #{sourceField.fieldLength}, #{sourceField.fieldPoint}, #{sourceField.fieldDefaultValue}, #{sourceField.fieldRemark});
    </insert>

    <insert id="insertManageSourceParam">
        INSERT INTO dfs_manage_source_param (source_code, param_code, param_name, param_type, param_start_time_field, param_end_time_field, param_remark)
        VALUES (#{sourceParam.sourceCode}, #{sourceParam.paramCode}, #{sourceParam.paramName}, #{sourceParam.paramType}, #{sourceParam.paramStartTimeField}, #{sourceParam.paramEndTimeField}, #{sourceParam.paramRemark});
    </insert>


</mapper>
