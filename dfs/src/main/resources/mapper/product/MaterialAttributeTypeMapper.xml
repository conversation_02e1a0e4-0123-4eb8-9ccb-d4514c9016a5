<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.MaterialAttributeTypeMapper">

    <update id="updateSonFullCode">
        update dfs_material_attribute_type
        set full_attribute_type_code = REPLACE(full_attribute_type_code, #{oldParentFullCode}, #{newParentFullCode})
        where full_attribute_type_code like CONCAT(#{oldParentFullCode}, '%');
    </update>
</mapper>
