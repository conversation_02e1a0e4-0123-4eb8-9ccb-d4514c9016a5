<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.CraftMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.CraftEntity">
        <result column="craft_id" property="craftId"/>
        <result column="craft_code" property="craftCode"/>
        <result column="craft_version" property="craftVersion"/>
        <result column="name" property="name"/>
        <result column="material_id" property="materialId"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="validity_from" property="validityFrom"/>
        <result column="validity_to" property="validityTo"/>
        <result column="state" property="state"/>
        <result column="craft_desc" property="craftDesc"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
        <result column="procedure_controller_check" property="procedureControllerCheck"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>

        <if test="e.craftCode != null and e.craftCode != '' ">
            AND t.craft_code = #{e.craftCode}
        </if>
        <if test="e.craftVersion != null and e.craftVersion != '' ">
            AND t.craft_version = #{e.craftVersion}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.materialId != null and e.materialId != '' ">
            AND t.material_id = #{e.materialId}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.validityFrom != null and e.validityFrom != '' ">
            AND t.validity_from = #{e.validityFrom}
        </if>
        <if test="e.validityTo != null and e.validityTo != '' ">
            AND t.validity_to = #{e.validityTo}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.craftDesc != null and e.craftDesc != '' ">
            AND t.craft_desc = #{e.craftDesc}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
        <if test="e.procedure_controller_check != null and e.procedureControllerCheck != '' ">
            AND t.procedure_controller_check = #{e.procedureControllerCheck}
        </if>
    </sql>

    <select id="pageV2" resultType="com.yelink.dfs.entity.product.CraftEntity">
        select craft.* from dfs_craft craft
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
</mapper>