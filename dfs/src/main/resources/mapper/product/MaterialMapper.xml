<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.MaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.MaterialEntity">
        <result column="id" property="id"/>
        <result column="state" property="state"/>
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="type_name" property="typeName"/>
        <result column="name" property="name"/>
        <result column="sort" property="sort"/>
        <result column="standard" property="standard"/>
        <result column="is_double_unit" property="isDoubleUnit"/>
        <result column="unit" property="unit"/>
        <result column="scale_factor" property="scaleFactor"/>
        <result column="unit_numerator" property="unitNumerator"/>
        <result column="unit_denominator" property="unitDenominator"/>
        <result column="comp" property="comp"/>
        <result column="version" property="version"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="file_url" property="fileUrl"/>
        <result column="is_batch_mag" property="isBatchMag"/>
        <result column="delivery_rules" property="deliveryRules"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
        <result column="level" property="level"/>
        <result column="approver" property="approver"/>
        <result column="is_auxiliary_material" property="isAuxiliaryMaterial"/>
        <result column="extend" property="extend"/>
        <result column="is_support_code_manage" property="isSupportCodeManage"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.isDoubleUnit != null and e.isDoubleUnit != '' ">
            AND t.is_double_unit = #{e.isDoubleUnit}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
        <if test="e.scaleFactor != null and e.scaleFactor != '' ">
            AND t.scale_factor = #{e.scaleFactor}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.typeName != null and e.typeName != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.sort != null and e.sort != '' ">
            AND t.sort = #{e.sort}
        </if>
        <if test="e.standard != null and e.standard != '' ">
            AND t.standard = #{e.standard}
        </if>
        <if test="e.comp != null and e.comp != '' ">
            AND t.comp = #{e.comp}
        </if>
        <if test="e.bomDescribe != null and e.bomDescribe != '' ">
            AND t.bom_describe = #{e.bomDescribe}
        </if>
        <if test="e.version != null and e.version != '' ">
            AND t.version = #{e.version}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.fileUrl != null and e.fileUrl != '' ">
            AND t.file_url = #{e.fileUrl}
        </if>
        <if test="e.isBatchMag != null and e.isBatchMag != '' ">
            AND t.is_batch_mag = #{e.isBatchMag}
        </if>
        <if test="e.deliveryRules != null and e.deliveryRules != '' ">
            AND t.delivery_rules = #{e.deliveryRules}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
        <if test="e.level != null and e.level != '' ">
            AND t.level = #{e.level}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.isAuxiliaryMaterial != null and e.isAuxiliaryMaterial != '' ">
            AND t.is_auxiliary_material = #{e.isAuxiliaryMaterial}
        </if>
        <if test="e.extend != null and e.extend != '' ">
            AND t.extend = #{e.extend}
        </if>
        <if test="e.isSupportCodeManage != null and e.isSupportCodeManage != '' ">
            AND t.is_support_code_manage = #{e.isSupportCodeManage}
        </if>
    </sql>

    <select id="getRelateBomData" resultType="com.yelink.dfs.entity.product.dto.MaterialRelateDataDTO">
        select distinct c.`bom_num`, c.`state` as `state`, c.`bom_num` as `number`, c.`code` as `material_code`, 'BOM'
        as `type`
        from dfs_material a
        left join dfs_bom_raw_material b on a.`code` = b.`code`
        left join dfs_bom c on b.`bom_id` = c.`id`
        where c.`code` = #{code}
    </select>

    <select id="getCountByProcessBomData" resultType="integer">
        select count(*) from (
        select DISTINCT c.`bom_num`
        from dfs_material a
        left join dfs_bom_raw_material b on a.`code` = b.`code`
        left join dfs_bom c on b.`bom_id` = c.`id`
        where c.`state` = 2
        and c.`code` = #{code}
        ) mm
    </select>

    <select id="getRelateTakeOutApplicationData" resultType="com.yelink.dfs.entity.product.dto.MaterialRelateDataDTO">
        select a.`application_num` as `number`,a.state as `state`,'领料单' as `type`
        from dfs_take_out_application a
        left join dfs_take_out_application_material b
        on a.`id` = b.`take_out_application_id`
        where b.`code` = #{code}
    </select>

    <select id="getCountByProcessTakeOutApplicationData" resultType="integer">
        select count(*)
        from dfs_take_out_application a
        left join dfs_take_out_application_material b
        on a.`id` = b.`take_out_application_id`
        where a.`state` in (1,2)
        and b.`code` = #{code}
    </select>

    <select id="getRelateWorkOrderData" resultType="com.yelink.dfs.entity.product.dto.MaterialRelateDataDTO">
        select `work_order_number` as `number`,`state`, '工单' as `type`
        from dfs_work_order
        where `material_code` = #{code}
    </select>

    <select id="getBomMaterialsByMaterialCode" resultType="com.yelink.dfs.entity.product.MaterialEntity">
        SELECT
            bm.`code`,
            m.`name`,
            m.`comp`
        FROM
            dfs_bom_raw_material bm
                LEFT JOIN dfs_material m ON m.`code` =bm.`code`
        WHERE
            bom_id =(
            SELECT
                id
            FROM
                dfs_bom bom
            WHERE
                bom.`code` = #{materialCode}
              AND bom.state = #{state}
            ORDER BY
                bom.create_time
            LIMIT 1)
    </select>

    <select id="getList" resultType="com.yelink.dfs.entity.product.MaterialEntity">
        select material.* from dfs_material material
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
