<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.AuxiliaryAttrValueMapper">


    <select id="getList" resultType="com.yelink.dfscommon.entity.dfs.AuxiliaryAttrValueEntity">
        select auxiliaryAttrValue.* from dfs_auxiliary_attr_value auxiliaryAttrValue
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
</mapper>
