<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureMaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureMaterialEntity">
        <result column="id" property="id"/>
        <result column="craft_id" property="craftId"/>
        <result column="procedure_id" property="procedureId"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>
        <if test="e.procedureId != null and e.procedureId != '' ">
            AND t.procedure_id = #{e.procedureId}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
    </sql>

</mapper>