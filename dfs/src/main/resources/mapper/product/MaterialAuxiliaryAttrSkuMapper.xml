<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.MaterialAuxiliaryAttrSkuMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrSkuEntity">
        <result column="id" property="id"/>
        <result column="material_code" property="materialCode"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sku_name" property="skuName"/>
        <result column="auxiliary_attr_code" property="auxiliaryAttrCode"/>
        <result column="auxiliary_attr_name" property="auxiliaryAttrName"/>
        <result column="value_code" property="valueCode"/>
        <result column="value_name" property="valueName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    <!-- 分开查询符合条件的物料编码和skuID，然后再取交集-->
    <select id="selectSkuList" resultType="com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrSkuEntity">
        select * from (
        <foreach item="item" collection="auxiliaryAttrs" separator="union all" open="(" close=")" index="">
            SELECT distinct material_code, sku_id
            FROM dfs_material_auxiliary_attr_sku
            WHERE auxiliary_attr_code = #{item.auxiliaryAttrCode}
            AND value_name LIKE concat('%',#{item.valueName},'%')
            <if test="materialCodes != null and materialCodes.size > 0">
                and material_code in
                <foreach item="materialCode" collection="materialCodes" separator="," open="(" close=")" index="">
                    #{materialCode}
                </foreach>
            </if>
        </foreach>
        ) as r
        GROUP BY r.material_code, r.sku_id
        HAVING count(*) >= #{skuConditionSize}
    </select>

    <select id="getList" resultType="com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrSkuEntity">
        select auxiliaryAttrSku.* from dfs_material_auxiliary_attr_sku auxiliaryAttrSku
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
