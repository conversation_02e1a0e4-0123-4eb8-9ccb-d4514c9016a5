<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.AuxiliaryAttrMapper">

    <select id="getList" resultType="com.yelink.dfscommon.entity.dfs.AuxiliaryAttrEntity">
        select auxiliaryAttr.* from dfs_auxiliary_attr auxiliaryAttr
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
</mapper>
