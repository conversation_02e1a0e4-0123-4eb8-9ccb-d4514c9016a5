<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.BomRawMaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.BomRawMaterialEntity">
        <result column="id" property="id"/>
        <result column="bom_id" property="bomId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="bom_describe" property="bomDescribe"/>
        <result column="standard" property="standard"/>
        <result column="num" property="num"/>
        <result column="comp" property="comp"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="bom_type" property="bomType"/>
        <result column="procedure_code" property="procedureCode"/>
        <result column="bom_num" property="bomNum"/>
        <result column="version" property="version"/>
        <result column="fixed_damage" property="fixedDamage"/>
        <result column="number" property="number"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.bomId != null and e.bomId != '' ">
            AND t.bom_id = #{e.bomId}
        </if>
        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.bomDescribe != null and e.bomDescribe != '' ">
            AND t.bom_describe = #{e.bomDescribe}
        </if>
        <if test="e.standard != null and e.standard != '' ">
            AND t.standard = #{e.standard}
        </if>
        <if test="e.num != null and e.num != '' ">
            AND t.num = #{e.num}
        </if>
        <if test="e.comp != null and e.comp != '' ">
            AND t.comp = #{e.comp}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.sort != null and e.sort != '' ">
            AND t.sort = #{e.sort}
        </if>
    </sql>

</mapper>