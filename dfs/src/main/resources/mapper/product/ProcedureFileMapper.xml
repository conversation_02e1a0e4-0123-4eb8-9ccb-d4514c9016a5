<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureFileMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureFileEntity">
        <result column="id" property="id"/>
        <result column="file_url" property="fileUrl"/>
        <result column="name" property="name"/>
        <result column="procedure_id" property="procedureId"/>
        <result column="craft_id" property="craftId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.fileUrl != null and e.fileUrl != '' ">
            AND t.file_url = #{e.fileUrl}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.procedureId != null and e.procedureId != '' ">
            AND t.procedure_id = #{e.procedureId}
        </if>
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>
    </sql>

</mapper>