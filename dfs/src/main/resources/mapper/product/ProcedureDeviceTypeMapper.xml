<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureDeviceTypeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureDeviceTypeEntity">
        <result column="id" property="id"/>
        <result column="procedure_id" property="procedureId"/>
        <result column="craft_id" property="craftId"/>
        <result column="device_model_id" property="deviceModelId"/>
        <result column="device_type_name" property="deviceTypeName"/>
        <result column="formula_num" property="formulaNum"/>
        <result column="formula_file_url" property="formulaFileUrl"/>
        <result column="formula_file_name" property="formulaFileName"/>
        <result column="theoretical_speed" property="theoreticalSpeed"/>
        <result column="unit" property="unit"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.procedureId != null and e.procedureId != '' ">
            AND t.procedure_id = #{e.procedureId}
        </if>
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>
        <if test="e.deviceModelId != null and e.deviceModelId != '' ">
            AND t.device_model_id = #{e.deviceModelId}
        </if>
        <if test="e.deviceTypeName != null and e.deviceTypeName != '' ">
            AND t.device_type_name = #{e.deviceTypeName}
        </if>
        <if test="e.formulaNum != null and e.formulaNum != '' ">
            AND t.formula_num = #{e.formulaNum}
        </if>
        <if test="e.formulaFileUrl != null and e.formulaFileUrl != '' ">
            AND t.formula_file_url = #{e.formulaFileUrl}
        </if>
        <if test="e.formulaFileName != null and e.formulaFileName != '' ">
            AND t.formula_file_name = #{e.formulaFileName}
        </if>
        <if test="e.theoreticalSpeed != null and e.theoreticalSpeed != '' ">
            AND t.theoretical_speed = #{e.theoreticalSpeed}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
    </sql>

</mapper>