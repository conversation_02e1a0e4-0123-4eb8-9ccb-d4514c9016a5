<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureRelationWorkHoursMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureRelationWorkHoursEntity">
        <result column="id" property="id"/>
        <result column="procedure_id" property="procedureId"/>
        <result column="craft_id" property="craftId"/>
        <result column="preparation_time" property="preparationTime"/>
        <result column="processing_hours" property="processingHours"/>
        <result column="degree_of_difficulty" property="degreeOfDifficulty"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.procedureId != null and e.procedureId != '' ">
            AND t.procedure_id = #{e.procedureId}
        </if>
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>
        <if test="e.preparationTime != null and e.preparationTime != '' ">
            AND t.preparation_time = #{e.preparationTime}
        </if>
        <if test="e.processingHours != null and e.processingHours != '' ">
            AND t.processing_hours = #{e.processingHours}
        </if>
        <if test="e.degreeOfDifficulty != null and e.degreeOfDifficulty != '' ">
            AND t.degree_of_difficulty = #{e.degreeOfDifficulty}
        </if>
    </sql>

</mapper>