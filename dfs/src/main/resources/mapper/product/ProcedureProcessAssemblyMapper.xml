<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureProcessAssemblyMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureProcessAssemblyEntity">
        <result column="id" property="id"/>
        <result column="craft_id" property="craftId"/>
        <result column="procedure_id" property="procedureId"/>
        <result column="process_assembly_code" property="processAssemblyCode"/>
        <result column="process_assembly_name" property="processAssemblyName"/>
        <result column="process_assembly_type" property="processAssemblyType"/>
        <result column="number" property="number"/>
        <result column="unit" property="unit"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>

    </resultMap>

</mapper>