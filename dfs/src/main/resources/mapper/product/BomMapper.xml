<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.BomMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.BomEntity">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="is_stop_using" property="stopUsing"/>
        <result column="state" property="state"/>
        <result column="approver" property="approver"/>
        <result column="bom_num" property="bomNum"/>
        <result column="bom_name" property="bomName"/>
        <result column="bom_describe" property="bomDescribe"/>
        <result column="version" property="version"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="erp_document_code" property="erpDocumentCode"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
        <result column="craft_code" property="craftCode"/>
        <result column="craft_version" property="craftVersion"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.stopUsing != null and e.stopUsing != '' ">
            AND t.is_stop_using = #{e.stopUsing}
        </if>
        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.bomNum != null and e.bomNum != '' ">
            AND t.bom_num = #{e.bomNum}
        </if>
        <if test="e.bomName != null and e.bomName != '' ">
            AND t.bom_name = #{e.bomName}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.reviewer != null and e.reviewer != '' ">
            AND t.reviewer = #{e.reviewer}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.standard != null and e.standard != '' ">
            AND t.standard = #{e.standard}
        </if>
        <if test="e.comp != null and e.comp != '' ">
            AND t.comp = #{e.comp}
        </if>
        <if test="e.bomDescribe != null and e.bomDescribe != '' ">
            AND t.bom_describe = #{e.bomDescribe}
        </if>
        <if test="e.version != null and e.version != '' ">
            AND t.version = #{e.version}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.erpDocumentCode != null and e.erpDocumentCode != '' ">
            AND t.erp_document_code = #{e.erpDocumentCode}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
    </sql>

    <select id="getList" resultType="com.yelink.dfs.entity.product.BomEntity">
        select bom.* from dfs_bom bom
        left join dfs_bom_raw_material bomMaterial on bomMaterial.bom_id = bom.id
        <where>
            <if test="sqlDTO != null and sqlDTO.bomSql != null and sqlDTO.bomSql != ''">
                AND ${sqlDTO.bomSql}
            </if>
            <if test="sqlDTO != null and sqlDTO.bomMaterialSql != null and sqlDTO.bomMaterialSql != ''">
                AND ${sqlDTO.bomMaterialSql}
            </if>
        </where>
        <if test="sqlDTO != null and sqlDTO.orderBySql != null and sqlDTO.orderBySql != ''">
            ORDER BY
            ${sqlDTO.orderBySql}
        </if>
    </select>

</mapper>
