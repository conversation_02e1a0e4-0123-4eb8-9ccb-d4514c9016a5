<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureEntity">
        <result column="procedure_id" property="procedureId"/>
        <result column="craft_id" property="craftId"/>
        <result column="craft_code" property="craftCode"/>
        <result column="name" property="name"/>
        <result column="procedure_code" property="procedureCode"/>
        <result column="procedure_path_code" property="procedurePathCode"/>
        <result column="model_id" property="modelId"/>
        <result column="model_name" property="modelName"/>
        <result column="parallel" property="parallel"/>
        <result column="last_procedure" property="lastProcedure"/>
        <result column="sop_code" property="sopCode"/>
        <result column="procedure_desc" property="procedureDesc"/>
        <result column="appendix_url" property="appendixUrl"/>
        <result column="img_url" property="imgUrl"/>
        <result column="working_hours" property="workingHours"/>
        <result column="theory_efficiency" property="theoryEfficiency"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="state" property="state"/>
        <result column="approver" property="approver"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
        <result column="line_model_names" property="lineModelNames"/>
        <result column="is_sub_contracting_operation" property="isSubContractingOperation"/>
        <result column="out_sourcing_supplier" property="outSourcingSupplier"/>
        <result column="is_inspection_operation" property="isInspectionOperation"/>
        <result column="line_model_ids" property="lineModelIds"/>
        <result column="out_sourcing_supplier_code" property="outSourcingSupplierCode"/>
        <result column="is_maintenance_procedure" property="isMaintenanceProcedure"/>
        <result column="is_quality_process" property="isQualityProcess"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.procedureId != null and e.procedureId != '' ">
            AND t.procedure_id = #{e.procedureId}
        </if>
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>
        <if test="e.craftCode != null and e.craftCode != '' ">
            AND t.craft_code = #{e.craftCode}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.procedureCode != null and e.procedureCode != '' ">
            AND t.procedure_code = #{e.procedureCode}
        </if>
        <if test="e.sopCode != null and e.sopCode != '' ">
            AND t.sop_code = #{e.sopCode}
        </if>
        <if test="e.procedurePathCode != null and e.procedurePathCode != '' ">
            AND t.procedure_path_code = #{e.procedurePathCode}
        </if>
        <if test="e.modelId != null and e.modelId != '' ">
            AND t.model_id = #{e.modelId}
        </if>
        <if test="e.modelName != null and e.modelName != '' ">
            AND t.model_name = #{e.modelName}
        </if>
        <if test="e.parallel != null and e.parallel != '' ">
            AND t.parallel = #{e.parallel}
        </if>
        <if test="e.lastProcedure != null and e.lastProcedure != '' ">
            AND t.last_procedure = #{e.lastProcedure}
        </if>
        <if test="e.procedureDesc != null and e.procedureDesc != '' ">
            AND t.procedure_desc = #{e.procedureDesc}
        </if>
        <if test="e.appendixUrl != null and e.appendixUrl != '' ">
            AND t.appendix_url = #{e.appendixUrl}
        </if>
        <if test="e.imgUrl != null and e.imgUrl != '' ">
            AND t.img_url = #{e.imgUrl}
        </if>
        <if test="e.workingHours != null and e.workingHours != '' ">
            AND t.working_hours = #{e.workingHours}
        </if>
        <if test="e.theoryEfficiency != null and e.theoryEfficiency != '' ">
            AND t.theory_efficiency = #{e.theoryEfficiency}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.reviewer != null and e.reviewer != '' ">
            AND t.reviewer = #{e.reviewer}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
        <if test="e.lineModelNames != null and e.lineModelNames != '' ">
            AND t.line_model_name = #{e.lineModelNames}
        </if>
        <if test="e.isSubContractingOperation != null and e.isSubContractingOperation != '' ">
            AND t.is_sub_contracting_operation = #{e.isSubContractingOperation}
        </if>
        <if test="e.outSourcingSupplier != null and e.outSourcingSupplier != '' ">
            AND t.out_sourcing_supplier = #{e.outSourcingSupplier}
        </if>
        <if test="e.isInspectionOperation != null and e.isInspectionOperation != '' ">
            AND t.is_inspection_operation = #{e.isInspectionOperation}
        </if>
        <if test="e.lineModelIds != null and e.lineModelIds != '' ">
            AND t.line_model_ids = #{e.lineModelIds}
        </if>
        <if test="e.outSourcingSupplierCode != null and e.outSourcingSupplierCode != '' ">
            AND t.is_inspection_operation_code = #{e.outSourcingSupplierCode}
        </if>
    </sql>

    <select id="countByWorkCenterId" resultType="java.lang.Long">
        select count(1) from dfs_procedure where find_in_set(#{workCenterId}, work_center_ids);
    </select>
    <select id="getList" resultType="com.yelink.dfs.entity.product.ProcedureEntity">
        select procedureEntity.* from dfs_procedure procedureEntity
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
