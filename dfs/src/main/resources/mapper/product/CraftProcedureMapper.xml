<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.CraftProcedureMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.CraftProcedureEntity">
        <result column="id" property="id"/>
        <result column="craft_id" property="craftId"/>
        <result column="procedure_id" property="procedureId"/>
        <result column="procedure_name" property="procedureName"/>
        <result column="sup_procedure_id" property="supProcedureId"/>
        <result column="sup_procedure_name" property="supProcedureName"/>
        <result column="is_last_procedure" property="isLastProcedure"/>
        <result column="fac_type" property="facType"/>
        <result column="is_import" property="whetherImport"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_name" property="fileName"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="line_model_name" property="lineModelName"/>
        <result column="is_sub_contracting_operation" property="isSubContractingOperation"/>
        <result column="out_sourcing_supplier" property="outSourcingSupplier"/>
        <result column="is_inspection_operation" property="isInspectionOperation"/>
        <result column="line_model_id" property="lineModelId"/>
        <result column="out_sourcing_supplier_code" property="outSourcingSupplierCode"/>
        <result column="is_maintenance_procedure" property="isMaintenanceProcedure"/>
        <result column="is_quality_process" property="isQualityProcess"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>
        <if test="e.procedureId != null and e.procedureId != '' ">
            AND t.procedure_id = #{e.procedureId}
        </if>
        <if test="e.procedureName != null and e.procedureName != '' ">
            AND t.procedure_name = #{e.procedureName}
        </if>
        <if test="e.supProcedureId != null and e.supProcedureId != '' ">
            AND t.sup_procedure_id = #{e.supProcedureId}
        </if>
        <if test="e.whetherImport != null and e.whetherImport != '' ">
            AND t.is_import = #{e.whetherImport}
        </if>
        <if test="e.supProcedureName != null and e.supProcedureName != '' ">
            AND t.sup_procedure_name = #{e.supProcedureName}
        </if>
        <if test="e.isLastProcedure != null and e.isLastProcedure != '' ">
            AND t.is_last_procedure = #{e.isLastProcedure}
        </if>
        <if test="e.facType != null and e.facType != '' ">
            AND t.fac_type = #{e.facType}
        </if>
        <if test="e.fileUrl != null and e.fileUrl != '' ">
            AND t.file_url = #{e.fileUrl}
        </if>
        <if test="e.fileName != null and e.fileName != '' ">
            AND t.file_name = #{e.fileName}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.lineModelName != null and e.lineModelName != '' ">
            AND t.line_model_name = #{e.lineModelName}
        </if>
        <if test="e.isSubContractingOperation != null and e.isSubContractingOperation != '' ">
            AND t.is_sub_contracting_operation = #{e.isSubContractingOperation}
        </if>
        <if test="e.outSourcingSupplier != null and e.outSourcingSupplier != '' ">
            AND t.out_sourcing_supplier = #{e.outSourcingSupplier}
        </if>
        <if test="e.isInspectionOperation != null and e.isInspectionOperation != '' ">
            AND t.is_inspection_operation = #{e.isInspectionOperation}
        </if>
        <if test="e.lineModelId != null and e.lineModelId != '' ">
            AND t.line_model_id = #{e.lineModelId}
        </if>
        <if test="e.outSourcingSupplierCode != null and e.outSourcingSupplierCode != '' ">
            AND t.is_inspection_operation_code = #{e.outSourcingSupplierCode}
        </if>
    </sql>

    <select id="getCraftProcedureGroupByWorkOrder" resultType="com.yelink.dfs.entity.product.dto.CraftProcedureWorkOrderDTO" >
        SELECT
            wopr.work_order_number,
            cp1.*
        FROM
            dfs_work_order_procedure_relation wopr
            -- 	工艺工序关联表 ，获取匹配工艺工序id的工序组
            LEFT JOIN dfs_craft_procedure cp ON cp.id = wopr.craft_procedure_id
            -- 	工艺工序关联表,获取匹配工序组的所有工序
            LEFT JOIN dfs_craft_procedure cp1 ON  cp1.craft_procedure_group = cp.craft_procedure_group
        <where>
            <if test="workOrderNumbers != null and workOrderNumbers.size > 0 ">
                AND wopr.work_order_number IN
                <foreach item="item" index="index" collection="workOrderNumbers" open="(" separator="," close=")" >
                    #{item}
                </foreach>
            </if>
            AND  cp1.id IS NOT NULL
        </where>
        GROUP BY
            wopr.work_order_number,cp1.id
    </select>

    <select id="pageV2" resultType="com.yelink.dfs.entity.product.CraftProcedureEntity">
        select craftProcedure.* from dfs_craft_procedure craftProcedure
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
