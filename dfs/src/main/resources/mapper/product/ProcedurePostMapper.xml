<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedurePostMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedurePostEntity">
        <result column="id" property="id"/>
        <result column="craft_id" property="craftId"/>
        <result column="procedure_id" property="procedureId"/>
        <result column="post_id" property="postId"/>
        <result column="post_code" property="postCode"/>
        <result column="post_name" property="postName"/>
        <result column="post_level" property="postLevel"/>
        <result column="number" property="number"/>
        <result column="unit" property="unit"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <select id="getReleasedCountByPostId" resultType="java.lang.Long">
        select count(1) from dfs_procedure_post pp
        left join dfs_craft c on c.craft_id = pp.craft_id
        where pp.post_id = #{postId} and c.state = '4';
    </select>


</mapper>