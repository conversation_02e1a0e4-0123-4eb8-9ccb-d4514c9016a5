<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.FacTypeProcedureMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.FacTypeProcedureEntity">
		    <result column="id" property="id" />
		    <result column="model_id" property="modelId" />
		    <result column="procedure_id" property="procedureId" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.modelId != null and e.modelId != '' ">
					AND t.model_id = #{e.modelId}
				</if>
				<if test="e.procedureId != null and e.procedureId != '' ">
					AND t.procedure_id = #{e.procedureId}
				</if>
	</sql>

</mapper>