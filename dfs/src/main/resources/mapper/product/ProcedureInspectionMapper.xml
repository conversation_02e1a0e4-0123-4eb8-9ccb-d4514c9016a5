<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureInspectionMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureInspectionEntity">
        <result column="id" property="id"/>
        <result column="inspection_code" property="inspectionCode"/>
        <result column="inspection_name" property="inspectionName"/>
        <result column="status" property="status"/>
        <result column="inspection_standard" property="inspectionStandard"/>
        <result column="inspection_instrument" property="inspectionInstrument"/>
        <result column="data_type" property="dataType"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>

        <if test="e.inspectionCode != null and e.inspectionCode != '' ">
            AND t.inspection_code = #{e.inspectionCode}
        </if>
        <if test="e.inspectionName != null and e.inspectionName != '' ">
            AND t.inspection_name = #{e.inspectionName}
        </if>
        <if test="e.status != null and e.status != '' ">
            AND t.status = #{e.status}
        </if>
        <if test="e.inspectionStandard != null and e.inspectionStandard != '' ">
            AND t.inspection_standard = #{e.inspectionStandard}
        </if>
        <if test="e.inspectionInstrument != null and e.inspectionInstrument != '' ">
            AND t.inspection_instrument = #{e.inspectionInstrument}
        </if>
        <if test="e.dataType != null and e.dataType != '' ">
            AND t.data_type = #{e.dataType}
        </if>

        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>

</mapper>