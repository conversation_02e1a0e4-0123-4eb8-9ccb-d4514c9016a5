<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProductMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProductEntity">
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_code" property="productCode"/>
        <result column="product_version" property="productVersion"/>
        <result column="product_spec" property="productSpec"/>
        <result column="unit" property="unit"/>
        <result column="per_unit" property="perUnit"/>
        <result column="total_spec" property="totalSpec"/>
        <result column="total_unit" property="totalUnit"/>
        <result column="state" property="state"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.productId != null and e.productId != '' ">
            AND t.product_id = #{e.productId}
        </if>
        <if test="e.productName != null and e.productName != '' ">
            AND t.product_name = #{e.productName}
        </if>
        <if test="e.productCode != null and e.productCode != '' ">
            AND t.product_code = #{e.productCode}
        </if>
        <if test="e.productVersion != null and e.productVersion != '' ">
            AND t.product_version = #{e.productVersion}
        </if>
        <if test="e.productSpec != null and e.productSpec != '' ">
            AND t.product_spec = #{e.productSpec}
        </if>
        <if test="e.unit != null and e.unit != '' ">
            AND t.unit = #{e.unit}
        </if>
        <if test="e.perUnit != null and e.perUnit != '' ">
            AND t.per_unit = #{e.perUnit}
        </if>
        <if test="e.totalSpec != null and e.totalSpec != '' ">
            AND t.total_spec = #{e.totalSpec}
        </if>
        <if test="e.totalUnit != null and e.totalUnit != '' ">
            AND t.total_unit = #{e.totalUnit}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
        <if test="e.updateDate != null and e.updateDate != '' ">
            AND t.update_date = #{e.updateDate}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>

</mapper>
