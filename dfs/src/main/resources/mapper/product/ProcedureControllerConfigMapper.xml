<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureControllerConfigMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.product.ProcedureControllerConfigEntity">
		    <result column="id" property="id" />
		    <result column="craft_procedure_id" property="craftProcedureId" />
		    <result column="increase_production_volume" property="increaseProductionVolume" />
		    <result column="jump_station_check" property="jumpStationCheck" />
		    <result column="reform_check" property="reformCheck" />
		    <result column="whether_the_rework" property="whetherTheRework" />
		    <result column="material_check" property="materialCheck" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_by" property="createBy" />
		    <result column="update_time" property="updateTime" />
		    <result column="create_time" property="createTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.craftProcedureId != null and e.craftProcedureId != '' ">
					AND t.craft_procedure_id = #{e.craftProcedureId}
				</if>
				<if test="e.increaseProductionVolume != null and e.increaseProductionVolume != '' ">
					AND t.increase_production_volume = #{e.increaseProductionVolume}
				</if>
				<if test="e.jumpStationCheck != null and e.jumpStationCheck != '' ">
					AND t.jump_station_check = #{e.jumpStationCheck}
				</if>
				<if test="e.reformCheck != null and e.reformCheck != '' ">
					AND t.reform_check = #{e.reformCheck}
				</if>
				<if test="e.whetherTheRework != null and e.whetherTheRework != '' ">
					AND t.whether_the_rework = #{e.whetherTheRework}
				</if>
				<if test="e.materialCheck != null and e.materialCheck != '' ">
					AND t.material_check = #{e.materialCheck}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>