<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.product.ProcedureInspectionConfigMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigEntity">
        <result column="id" property="id"/>
        <result column="craft_id" property="craftId"/>
        <result column="craft_procedure_id" property="craftProcedureId"/>
        <result column="procedure_inspection_id" property="procedureInspectionId"/>
        <result column="comparator" property="comparator"/>
        <result column="standard_value" property="standardValue"/>
        <result column="upper_limit" property="upperLimit"/>
        <result column="down_limit" property="downLimit"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.craftId != null and e.craftId != '' ">
            AND t.craft_id = #{e.craftId}
        </if>
        <if test="e.craftProcedureId != null and e.craftProcedureId != '' ">
            AND t.craft_procedure_id = #{e.craftProcedureId}
        </if>

        <if test="e.comparator != null and e.comparator != '' ">
            AND t.comparator = #{e.comparator}
        </if>
        <if test="e.standardValue != null and e.standardValue != '' ">
            AND t.standard_value = #{e.standardValue}
        </if>
        <if test="e.upperLimit != null and e.upperLimit != '' ">
            AND t.upper_limit = #{e.upperLimit}
        </if>
        <if test="e.downLimit != null and e.downLimit != '' ">
            AND t.down_limit = #{e.downLimit}
        </if>


        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>

    </sql>

</mapper>