<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.attendance.AttendanceRecordMapper">

    <select id="getNoDownRecord" resultType="com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO">
        SELECT
            t.id as attendanceId,
            t.work_order_number,
            t.record_date,
            r.id,
            r.start_record_time
        FROM dfs_attendance_record r
            INNER JOIN `dfs_attendance` t ON t.id = r.attendance_id
        <where>
            r.end_record_time IS NULL
            AND t.user_id = #{userId}
        </where>
        ORDER BY r.start_record_time DESC

    </select>

    <select id="recordList" resultType="com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO">
        SELECT
        t.id as attendanceId,
        t.work_order_number,
        t.work_center_type,
        t.relate_id,
        t.record_date,
        r.id,
        r.start_record_time,
        r.end_record_time,
        r.duration,
        r.fid,
        ifnull(r.update_by, r.create_by) as submitter,
        ifnull(r.update_time, r.create_time) as submitTime,
        t.user_id,
        u.user_name,
        t.temp_user_name,
        ifnull(u.nick_name, t.temp_user_name) as nickName
        FROM dfs_attendance t
        INNER JOIN dfs_attendance_record r  ON t.id = r.attendance_id
        LEFT JOIN sys_users u on u.id = t.user_id
        <include refid="queryCondition"/>
        ORDER BY r.update_time DESC
    </select>

    <sql id="queryCondition">
        <where>
            <if test="dto.workOrderNumber == 'null'">
                AND t.work_order_number is null
            </if>
            <if test="dto.userId != null">
                AND t.user_id = #{dto.userId}
            </if>
            <if test="dto.userNames != null and dto.userNames.size > 0">
                AND u.user_name in
                <foreach item="item" collection="dto.userNames" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="dto.submitterNames != null and dto.submitterNames.size > 0">
                AND r.update_by in
                <foreach item="item" collection="dto.submitterNames" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="dto.recordDates != null and dto.recordDates.size > 0">
                AND t.record_date in
                <foreach item="item" collection="dto.recordDates" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="dto.recordDate != null">
                AND t.record_date = #{dto.recordDate}
            </if>
            <if test="dto.startRecordDate != null">
                AND t.record_date <![CDATA[ >= ]]> #{dto.startRecordDate}
            </if>
            <if test="dto.endRecordDate != null">
                AND t.record_date <![CDATA[ <= ]]> #{dto.endRecordDate}
            </if>
            <if test="dto.workOrderNumbers != null and dto.workOrderNumbers.size > 0">
                AND t.work_order_number in
                <foreach item="item" collection=" dto.workOrderNumbers" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="dto.startRecordStartTime != null and dto.startRecordEndTime != null">
                AND r.start_record_time between #{dto.startRecordStartTime} and #{dto.startRecordEndTime}
            </if>
            <if test="dto.endRecordStartTime != null and dto.endRecordEndTime != null">
                AND r.end_record_time between #{dto.endRecordStartTime} and #{dto.endRecordEndTime}
            </if>
            <if test="dto.submitStartTime != null and dto.submitEndTime != null">
                AND r.update_time between #{dto.submitStartTime} and #{dto.submitEndTime}
            </if>
        </where>
    </sql>

    <select id="recentCheckIn" resultType="com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO">
        SELECT
            u.user_name,
            u.nick_name,
            a.user_id,
            a.record_date,
            a.id AS attendanceId,
            a.work_order_number,
            r.id,
            r.start_record_time,
            r.end_record_time
        FROM dfs_attendance_record r
        INNER JOIN dfs_attendance a on r.attendance_id = a.id
        INNER JOIN sys_users u on u.id = a.user_id
        WHERE r.id in(
            SELECT
                max(r.id)
            FROM dfs_attendance_record r
            INNER JOIN dfs_attendance a on r.attendance_id = a.id
            -- 最近一个月
            WHERE DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[ <= ]]> date(a.record_date )
                AND a.work_order_number in
                <foreach item="item" collection="workOrderNumbers" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            GROUP BY a.user_id
        )
        -- 按人名排序
        ORDER BY u.user_name

    </select>
</mapper>