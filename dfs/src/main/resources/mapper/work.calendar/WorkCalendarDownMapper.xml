<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.work.calendar.WorkCalendarDownMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.work.calendar.WorkCalendarDownEntity">
		    <result column="down_id" property="downId" />
		    <result column="instance_id" property="instanceId" />
		    <result column="instance_code" property="instanceCode" />
		    <result column="instance_name" property="instanceName" />
		    <result column="model_id" property="modelId" />
		    <result column="model_type" property="modelType" />
		    <result column="name" property="name" />
		    <result column="start" property="start" />
		    <result column="end" property="end" />
		    <result column="duration" property="duration" />
		    <result column="week" property="week" />
		    <result column="validity_start" property="validityStart" />
		    <result column="validity_end" property="validityEnd" />
		    <result column="description" property="description" />
		    <result column="create_by" property="createBy" />
		    <result column="update_by" property="updateBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.downId != null and e.downId != '' ">
					AND t.down_id = #{e.downId}
				</if>
				<if test="e.instanceId != null and e.instanceId != '' ">
					AND t.instance_id = #{e.instanceId}
				</if>
				<if test="e.instanceCode != null and e.instanceCode != '' ">
					AND t.instance_code = #{e.instanceCode}
				</if>
				<if test="e.instanceName != null and e.instanceName != '' ">
					AND t.instance_name = #{e.instanceName}
				</if>
				<if test="e.modelId != null and e.modelId != '' ">
					AND t.model_id = #{e.modelId}
				</if>
				<if test="e.modelType != null and e.modelType != '' ">
					AND t.model_type = #{e.modelType}
				</if>
				<if test="e.name != null and e.name != '' ">
					AND t.name = #{e.name}
				</if>
				<if test="e.start != null and e.start != '' ">
					AND t.start = #{e.start}
				</if>
				<if test="e.end != null and e.end != '' ">
					AND t.end = #{e.end}
				</if>
				<if test="e.duration != null and e.duration != '' ">
					AND t.duration = #{e.duration}
				</if>
				<if test="e.week != null and e.week != '' ">
					AND t.week = #{e.week}
				</if>
				<if test="e.validityStart != null and e.validityStart != '' ">
					AND t.validity_start = #{e.validityStart}
				</if>
				<if test="e.validityEnd != null and e.validityEnd != '' ">
					AND t.validity_end = #{e.validityEnd}
				</if>
				<if test="e.description != null and e.description != '' ">
					AND t.description = #{e.description}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>

</mapper>