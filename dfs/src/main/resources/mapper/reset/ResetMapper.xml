<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.reset.ResetMapper">

    <delete id="resetDfs">
        DROP DATABASE IF EXISTS dfs;
        DROP DATABASE IF EXISTS dfs_target;
        DROP DATABASE IF EXISTS dfs_metrics;

        CREATE DATABASE dfs CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    </delete>

    <delete id="resetOthers">
        DROP DATABASE IF EXISTS dfs_scr;
        CREATE DATABASE dfs_scr CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS dfs_assignment;
        CREATE DATABASE dfs_assignment CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS ams;
        CREATE DATABASE ams CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS dfs_maintain;
        CREATE DATABASE dfs_maintain CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS dfs_doc;
        CREATE DATABASE dfs_doc CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS dfs_generator;
        CREATE DATABASE dfs_generator CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS aps;
        CREATE DATABASE aps CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS pms;
        CREATE DATABASE pms CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS qms;
        CREATE DATABASE qms CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS qms_process_inspection;
        CREATE DATABASE qms_process_inspection CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS qms_product_inspection;
        CREATE DATABASE qms_product_inspection CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS qms_aftersale_inspection;
        CREATE DATABASE qms_aftersale_inspection CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

        DROP DATABASE IF EXISTS wms;
        CREATE DATABASE wms CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    </delete>

    <!--备份用户数据    -->
    <delete id="backUserConfig">
        CREATE DATABASE IF NOT EXISTS dfs_reset CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
        #角色权限配置
        DROP TABLE IF EXISTS dfs_reset.sys_permissions;
        CREATE TABLE dfs_reset.sys_permissions LIKE dfs.sys_permissions;
        INSERT INTO dfs_reset.sys_permissions SELECT * FROM dfs.sys_permissions;
        DROP TABLE IF EXISTS dfs_reset.sys_role_permission;
        CREATE TABLE dfs_reset.sys_role_permission LIKE dfs.sys_role_permission;
        INSERT INTO dfs_reset.sys_role_permission SELECT * FROM dfs.sys_role_permission;
        DROP TABLE IF EXISTS dfs_reset.sys_roles;
        CREATE TABLE dfs_reset.sys_roles LIKE dfs.sys_roles;
        INSERT INTO dfs_reset.sys_roles SELECT * FROM dfs.sys_roles;
        #软件参数配置
        DROP TABLE IF EXISTS dfs_reset.dfs_business_config;
        CREATE TABLE dfs_reset.dfs_business_config LIKE dfs.dfs_business_config;
        INSERT INTO dfs_reset.dfs_business_config SELECT * FROM dfs.dfs_business_config;
        DROP TABLE IF EXISTS dfs_reset.dfs_business_config_value;
        CREATE TABLE dfs_reset.dfs_business_config_value LIKE dfs.dfs_business_config_value;
        INSERT INTO dfs_reset.dfs_business_config_value SELECT * FROM dfs.dfs_business_config_value;
        #单据下推配置
        DROP TABLE IF EXISTS dfs_reset.dfs_order_push_down_item;
        CREATE TABLE dfs_reset.dfs_order_push_down_item LIKE dfs.dfs_order_push_down_item;
        INSERT INTO dfs_reset.dfs_order_push_down_item SELECT * FROM dfs.dfs_order_push_down_item;
        DROP TABLE IF EXISTS dfs_reset.dfs_order_push_down_config_value;
        CREATE TABLE dfs_reset.dfs_order_push_down_config_value LIKE dfs.dfs_order_push_down_config_value;
        INSERT INTO dfs_reset.dfs_order_push_down_config_value SELECT * FROM dfs.dfs_order_push_down_config_value;
        DROP TABLE IF EXISTS dfs_reset.dfs_order_push_down_item_verify;
        CREATE TABLE dfs_reset.dfs_order_push_down_item_verify LIKE dfs.dfs_order_push_down_item_verify;
        INSERT INTO dfs_reset.dfs_order_push_down_item_verify SELECT * FROM dfs.dfs_order_push_down_item_verify;
        DROP TABLE IF EXISTS dfs_reset.dfs_order_push_down_item_write_back;
        CREATE TABLE dfs_reset.dfs_order_push_down_item_write_back LIKE dfs.dfs_order_push_down_item_write_back;
        INSERT INTO dfs_reset.dfs_order_push_down_item_write_back SELECT * FROM dfs.dfs_order_push_down_item_write_back;
        DROP TABLE IF EXISTS dfs_reset.dfs_order_push_down_config;
        CREATE TABLE dfs_reset.dfs_order_push_down_config LIKE dfs.dfs_order_push_down_config;
        INSERT INTO dfs_reset.dfs_order_push_down_config SELECT * FROM dfs.dfs_order_push_down_config;
        #表单配置
        DROP TABLE IF EXISTS dfs_reset.dfs_form_field_config;
        CREATE TABLE dfs_reset.dfs_form_field_config LIKE dfs.dfs_form_field_config;
        INSERT INTO dfs_reset.dfs_form_field_config SELECT * FROM dfs.dfs_form_field_config;
        DROP TABLE IF EXISTS dfs_reset.dfs_form_field_rule_config;
        CREATE TABLE dfs_reset.dfs_form_field_rule_config LIKE dfs.dfs_form_field_rule_config;
        INSERT INTO dfs_reset.dfs_form_field_rule_config SELECT * FROM dfs.dfs_form_field_rule_config;
        DROP TABLE IF EXISTS dfs_reset.dfs_form_overall_field_config;
        CREATE TABLE dfs_reset.dfs_form_overall_field_config LIKE dfs.dfs_form_overall_field_config;
        INSERT INTO dfs_reset.dfs_form_overall_field_config SELECT * FROM dfs.dfs_form_overall_field_config;
        DROP TABLE IF EXISTS dfs_reset.dfs_form_overall_field_mapping_config;
        CREATE TABLE dfs_reset.dfs_form_overall_field_mapping_config LIKE dfs.dfs_form_overall_field_mapping_config;
        INSERT INTO dfs_reset.dfs_form_overall_field_mapping_config SELECT * FROM dfs.dfs_form_overall_field_mapping_config;
        DROP TABLE IF EXISTS dfs_reset.dfs_form_overall_field_rule_config;
        CREATE TABLE dfs_reset.dfs_form_overall_field_rule_config LIKE dfs.dfs_form_overall_field_rule_config;
        INSERT INTO dfs_reset.dfs_form_overall_field_rule_config SELECT * FROM dfs.dfs_form_overall_field_rule_config;
        DROP TABLE IF EXISTS dfs_reset.dfs_field_mapping;
        CREATE TABLE dfs_reset.dfs_field_mapping LIKE dfs.dfs_field_mapping;
        INSERT INTO dfs_reset.dfs_field_mapping SELECT * FROM dfs.dfs_field_mapping;
    </delete>
    <!-- 恢复用户数据   -->
    <delete id="resetUserConfig">
        #角色权限配置
        DROP TABLE IF EXISTS dfs.sys_permissions;
        CREATE TABLE dfs.sys_permissions LIKE dfs_reset.sys_permissions;
        INSERT INTO dfs.sys_permissions SELECT * FROM dfs_reset.sys_permissions;
        DROP TABLE IF EXISTS dfs.sys_role_permission;
        CREATE TABLE dfs.sys_role_permission LIKE dfs_reset.sys_role_permission;
        INSERT INTO dfs.sys_role_permission SELECT * FROM dfs_reset.sys_role_permission;
        DROP TABLE IF EXISTS dfs.sys_roles;
        CREATE TABLE dfs.sys_roles LIKE dfs_reset.sys_roles;
        INSERT INTO dfs.sys_roles SELECT * FROM dfs_reset.sys_roles;
        #软件参数配置
        DROP TABLE IF EXISTS dfs.dfs_business_config;
        CREATE TABLE dfs.dfs_business_config LIKE dfs_reset.dfs_business_config;
        INSERT INTO dfs.dfs_business_config SELECT * FROM dfs_reset.dfs_business_config;
        DROP TABLE IF EXISTS dfs.dfs_business_config_value;
        CREATE TABLE dfs.dfs_business_config_value LIKE dfs_reset.dfs_business_config_value;
        INSERT INTO dfs.dfs_business_config_value SELECT * FROM dfs_reset.dfs_business_config_value;
        #单据下推配置
        DROP TABLE IF EXISTS dfs.dfs_order_push_down_item;
        CREATE TABLE dfs.dfs_order_push_down_item LIKE dfs_reset.dfs_order_push_down_item;
        INSERT INTO dfs.dfs_order_push_down_item SELECT * FROM dfs_reset.dfs_order_push_down_item;
        DROP TABLE IF EXISTS dfs.dfs_order_push_down_config_value;
        CREATE TABLE dfs.dfs_order_push_down_config_value LIKE dfs_reset.dfs_order_push_down_config_value;
        INSERT INTO dfs.dfs_order_push_down_config_value SELECT * FROM dfs_reset.dfs_order_push_down_config_value;
        DROP TABLE IF EXISTS dfs.dfs_order_push_down_item_verify;
        CREATE TABLE dfs.dfs_order_push_down_item_verify LIKE dfs_reset.dfs_order_push_down_item_verify;
        INSERT INTO dfs.dfs_order_push_down_item_verify SELECT * FROM dfs_reset.dfs_order_push_down_item_verify;
        DROP TABLE IF EXISTS dfs.dfs_order_push_down_item_write_back;
        CREATE TABLE dfs.dfs_order_push_down_item_write_back LIKE dfs_reset.dfs_order_push_down_item_write_back;
        INSERT INTO dfs.dfs_order_push_down_item_write_back SELECT * FROM dfs_reset.dfs_order_push_down_item_write_back;
        DROP TABLE IF EXISTS dfs.dfs_order_push_down_config;
        CREATE TABLE dfs.dfs_order_push_down_config LIKE dfs_reset.dfs_order_push_down_config;
        INSERT INTO dfs.dfs_order_push_down_config SELECT * FROM dfs_reset.dfs_order_push_down_config;
        #表单配置
        DROP TABLE IF EXISTS dfs.dfs_form_field_config;
        CREATE TABLE dfs.dfs_form_field_config LIKE dfs_reset.dfs_form_field_config;
        INSERT INTO dfs.dfs_form_field_config SELECT * FROM dfs_reset.dfs_form_field_config;
        DROP TABLE IF EXISTS dfs.dfs_form_field_rule_config;
        CREATE TABLE dfs.dfs_form_field_rule_config LIKE dfs_reset.dfs_form_field_rule_config;
        INSERT INTO dfs.dfs_form_field_rule_config SELECT * FROM dfs_reset.dfs_form_field_rule_config;
        DROP TABLE IF EXISTS dfs.dfs_form_overall_field_config;
        CREATE TABLE dfs.dfs_form_overall_field_config LIKE dfs_reset.dfs_form_overall_field_config;
        INSERT INTO dfs.dfs_form_overall_field_config SELECT * FROM dfs_reset.dfs_form_overall_field_config;
        DROP TABLE IF EXISTS dfs.dfs_form_overall_field_mapping_config;
        CREATE TABLE dfs.dfs_form_overall_field_mapping_config LIKE dfs_reset.dfs_form_overall_field_mapping_config;
        INSERT INTO dfs.dfs_form_overall_field_mapping_config SELECT * FROM dfs_reset.dfs_form_overall_field_mapping_config;
        DROP TABLE IF EXISTS dfs.dfs_form_overall_field_rule_config;
        CREATE TABLE dfs.dfs_form_overall_field_rule_config LIKE dfs_reset.dfs_form_overall_field_rule_config;
        INSERT INTO dfs.dfs_form_overall_field_rule_config SELECT * FROM dfs_reset.dfs_form_overall_field_rule_config;
        DROP TABLE IF EXISTS dfs.dfs_field_mapping;
        CREATE TABLE dfs.dfs_field_mapping LIKE dfs_reset.dfs_field_mapping;
        INSERT INTO dfs.dfs_field_mapping SELECT * FROM dfs_reset.dfs_field_mapping;

    </delete>
<!--    备份开发数据-->
    <delete id="backDevelopmentConfig">
        CREATE DATABASE IF NOT EXISTS dfs_reset CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
        # API重定向
        DROP TABLE IF EXISTS dfs_reset.dfs_api_transform;
        CREATE TABLE dfs_reset.dfs_api_transform LIKE dfs.dfs_api_transform;
        INSERT INTO dfs_reset.dfs_api_transform SELECT * FROM dfs.dfs_api_transform;
        # 页面重定向
        DROP TABLE IF EXISTS dfs_reset.dfs_page_redirect;
        CREATE TABLE dfs_reset.dfs_page_redirect LIKE dfs.dfs_page_redirect;
        INSERT INTO dfs_reset.dfs_page_redirect SELECT * FROM dfs.dfs_page_redirect;
        DROP TABLE IF EXISTS dfs_reset.dfs_page_redirect_param;
        CREATE TABLE dfs_reset.dfs_page_redirect_param LIKE dfs.dfs_page_redirect_param;
        INSERT INTO dfs_reset.dfs_page_redirect_param SELECT * FROM dfs.dfs_page_redirect_param;
        #工厂指标
        DROP TABLE IF EXISTS dfs_reset.dfs_target_alarm;
        CREATE TABLE dfs_reset.dfs_target_alarm LIKE dfs.dfs_target_alarm;
        INSERT INTO dfs_reset.dfs_target_alarm SELECT * FROM dfs.dfs_target_alarm;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_device_manual;
        CREATE TABLE dfs_reset.dfs_target_device_manual LIKE dfs.dfs_target_device_manual;
        INSERT INTO dfs_reset.dfs_target_device_manual SELECT * FROM dfs.dfs_target_device_manual;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_dict;
        CREATE TABLE dfs_reset.dfs_target_dict LIKE dfs.dfs_target_dict;
        INSERT INTO dfs_reset.dfs_target_dict SELECT * FROM dfs.dfs_target_dict;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_dict;
        CREATE TABLE dfs_reset.dfs_target_dict LIKE dfs.dfs_target_dict;
        INSERT INTO dfs_reset.dfs_target_dict SELECT * FROM dfs.dfs_target_dict;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_instance_collect;
        CREATE TABLE dfs_reset.dfs_target_instance_collect LIKE dfs.dfs_target_instance_collect;
        INSERT INTO dfs_reset.dfs_target_instance_collect SELECT * FROM dfs.dfs_target_instance_collect;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_limit_config;
        CREATE TABLE dfs_reset.dfs_target_limit_config LIKE dfs.dfs_target_limit_config;
        INSERT INTO dfs_reset.dfs_target_limit_config SELECT * FROM dfs.dfs_target_limit_config;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_method;
        CREATE TABLE dfs_reset.dfs_target_method LIKE dfs.dfs_target_method;
        INSERT INTO dfs_reset.dfs_target_method SELECT * FROM dfs.dfs_target_method;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_model;
        CREATE TABLE dfs_reset.dfs_target_model LIKE dfs.dfs_target_model;
        INSERT INTO dfs_reset.dfs_target_model SELECT * FROM dfs.dfs_target_model;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_model_table_relate;
        CREATE TABLE dfs_reset.dfs_target_model_table_relate LIKE dfs.dfs_target_model_table_relate;
        INSERT INTO dfs_reset.dfs_target_model_table_relate SELECT * FROM dfs.dfs_target_model_table_relate;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_model_tag;
        CREATE TABLE dfs_reset.dfs_target_model_tag LIKE dfs.dfs_target_model_tag;
        INSERT INTO dfs_reset.dfs_target_model_tag SELECT * FROM dfs.dfs_target_model_tag;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_status_formula;
        CREATE TABLE dfs_reset.dfs_target_status_formula LIKE dfs.dfs_target_status_formula;
        INSERT INTO dfs_reset.dfs_target_status_formula SELECT * FROM dfs.dfs_target_status_formula;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_tag;
        CREATE TABLE dfs_reset.dfs_target_tag LIKE dfs.dfs_target_tag;
        INSERT INTO dfs_reset.dfs_target_tag SELECT * FROM dfs.dfs_target_tag;
        DROP TABLE IF EXISTS dfs_reset.dfs_target_threshold;
        CREATE TABLE dfs_reset.dfs_target_threshold LIKE dfs.dfs_target_threshold;
        INSERT INTO dfs_reset.dfs_target_threshold SELECT * FROM dfs.dfs_target_threshold;
    </delete>

    <!--    恢复开发数据-->
    <delete id="resetDevelopmentConfig">
        # API重定向
        DROP TABLE IF EXISTS dfs.dfs_api_transform;
        CREATE TABLE dfs.dfs_api_transform LIKE dfs_reset.dfs_api_transform;
        INSERT INTO dfs.dfs_api_transform SELECT * FROM dfs_reset.dfs_api_transform;
        # 页面重定向
        DROP TABLE IF EXISTS dfs.dfs_page_redirect;
        CREATE TABLE dfs.dfs_page_redirect LIKE dfs_reset.dfs_page_redirect;
        INSERT INTO dfs.dfs_page_redirect SELECT * FROM dfs_reset.dfs_page_redirect;
        DROP TABLE IF EXISTS dfs.dfs_page_redirect_param;
        CREATE TABLE dfs.dfs_page_redirect_param LIKE dfs_reset.dfs_page_redirect_param;
        INSERT INTO dfs.dfs_page_redirect_param SELECT * FROM dfs_reset.dfs_page_redirect_param;
        #工厂指标
        DROP TABLE IF EXISTS dfs.dfs_target_alarm;
        CREATE TABLE dfs.dfs_target_alarm LIKE dfs_reset.dfs_target_alarm;
        INSERT INTO dfs.dfs_target_alarm SELECT * FROM dfs_reset.dfs_target_alarm;
        DROP TABLE IF EXISTS dfs.dfs_target_device_manual;
        CREATE TABLE dfs.dfs_target_device_manual LIKE dfs_reset.dfs_target_device_manual;
        INSERT INTO dfs.dfs_target_device_manual SELECT * FROM dfs_reset.dfs_target_device_manual;
        DROP TABLE IF EXISTS dfs.dfs_target_dict;
        CREATE TABLE dfs.dfs_target_dict LIKE dfs_reset.dfs_target_dict;
        INSERT INTO dfs.dfs_target_dict SELECT * FROM dfs_reset.dfs_target_dict;
        DROP TABLE IF EXISTS dfs.dfs_target_dict;
        CREATE TABLE dfs.dfs_target_dict LIKE dfs_reset.dfs_target_dict;
        INSERT INTO dfs.dfs_target_dict SELECT * FROM dfs_reset.dfs_target_dict;
        DROP TABLE IF EXISTS dfs.dfs_target_instance_collect;
        CREATE TABLE dfs.dfs_target_instance_collect LIKE dfs_reset.dfs_target_instance_collect;
        INSERT INTO dfs.dfs_target_instance_collect SELECT * FROM dfs_reset.dfs_target_instance_collect;
        DROP TABLE IF EXISTS dfs.dfs_target_limit_config;
        CREATE TABLE dfs.dfs_target_limit_config LIKE dfs_reset.dfs_target_limit_config;
        INSERT INTO dfs.dfs_target_limit_config SELECT * FROM dfs_reset.dfs_target_limit_config;
        DROP TABLE IF EXISTS dfs.dfs_target_method;
        CREATE TABLE dfs.dfs_target_method LIKE dfs_reset.dfs_target_method;
        INSERT INTO dfs.dfs_target_method SELECT * FROM dfs_reset.dfs_target_method;
        DROP TABLE IF EXISTS dfs.dfs_target_model;
        CREATE TABLE dfs.dfs_target_model LIKE dfs_reset.dfs_target_model;
        INSERT INTO dfs.dfs_target_model SELECT * FROM dfs_reset.dfs_target_model;
        DROP TABLE IF EXISTS dfs.dfs_target_model_table_relate;
        CREATE TABLE dfs.dfs_target_model_table_relate LIKE dfs_reset.dfs_target_model_table_relate;
        INSERT INTO dfs.dfs_target_model_table_relate SELECT * FROM dfs_reset.dfs_target_model_table_relate;
        DROP TABLE IF EXISTS dfs.dfs_target_model_tag;
        CREATE TABLE dfs.dfs_target_model_tag LIKE dfs_reset.dfs_target_model_tag;
        INSERT INTO dfs.dfs_target_model_tag SELECT * FROM dfs_reset.dfs_target_model_tag;
        DROP TABLE IF EXISTS dfs.dfs_target_status_formula;
        CREATE TABLE dfs.dfs_target_status_formula LIKE dfs_reset.dfs_target_status_formula;
        INSERT INTO dfs.dfs_target_status_formula SELECT * FROM dfs_reset.dfs_target_status_formula;
        DROP TABLE IF EXISTS dfs.dfs_target_tag;
        CREATE TABLE dfs.dfs_target_tag LIKE dfs_reset.dfs_target_tag;
        INSERT INTO dfs.dfs_target_tag SELECT * FROM dfs_reset.dfs_target_tag;
        DROP TABLE IF EXISTS dfs.dfs_target_threshold;
        CREATE TABLE dfs.dfs_target_threshold LIKE dfs_reset.dfs_target_threshold;
        INSERT INTO dfs.dfs_target_threshold SELECT * FROM dfs_reset.dfs_target_threshold;
    </delete>
</mapper>
