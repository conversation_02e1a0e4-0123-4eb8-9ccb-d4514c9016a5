<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.supplier.SupplierMaterialMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.supplier.SupplierMaterialEntity">
        <result column="id" property="id"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="material_id" property="materialId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.supplierId != null and e.supplierId != '' ">
            AND t.supplier_id = #{e.supplierId}
        </if>
        <if test="e.materialId != null and e.materialId != '' ">
            AND t.material_id = #{e.materialId}
        </if>
    </sql>

    <select id="getList" resultType="com.yelink.dfs.entity.supplier.SupplierMaterialEntity">
        select supplierMaterial.* from dfs_supplier_material supplierMaterial
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>