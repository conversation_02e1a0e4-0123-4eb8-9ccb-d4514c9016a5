<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.supplier.SupplierMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.supplier.SupplierEntity">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="addr" property="addr"/>
        <result column="type" property="type"/>
        <result column="state" property="state"/>
        <result column="approver" property="approver"/>
        <result column="contacts" property="contacts"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_suggestion" property="approvalSuggestion"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.code != null and e.code != '' ">
            AND t.code = #{e.code}
        </if>
        <if test="e.name != null and e.name != '' ">
            AND t.name = #{e.name}
        </if>
        <if test="e.phone != null and e.phone != '' ">
            AND t.phone = #{e.phone}
        </if>
        <if test="e.addr != null and e.addr != '' ">
            AND t.addr = #{e.addr}
        </if>
        <if test="e.contacts != null and e.contacts != '' ">
            AND t.contacts = #{e.contacts}
        </if>
        <if test="e.contactsPhone != null and e.contactsPhone != '' ">
            AND t.contacts_phone = #{e.contactsPhone}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.reviewer != null and e.reviewer != '' ">
            AND t.reviewer = #{e.reviewer}
        </if>
        <if test="e.approver != null and e.approver != '' ">
            AND t.approver = #{e.approver}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.approvalStatus != null and e.approvalStatus != '' ">
            AND t.approval_status = #{e.approvalStatus}
        </if>
        <if test="e.approvalSuggestion != null and e.approvalSuggestion != '' ">
            AND t.approval_suggestion = #{e.approvalSuggestion}
        </if>
    </sql>


    <select id="getList" resultType="com.yelink.dfs.entity.supplier.SupplierEntity">
        select supplier.* from dfs_supplier supplier
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>