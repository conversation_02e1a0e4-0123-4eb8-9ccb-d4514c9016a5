<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.CodeResolverRuleLineMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.code.CodeResolverRuleLineEntity">
		    <result column="id" property="id" />
		    <result column="rule_id" property="ruleId" />
		    <result column="line_id" property="lineId" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.ruleId != null and e.ruleId != '' ">
					AND t.rule_id = #{e.ruleId}
				</if>
				<if test="e.lineId != null and e.lineId != '' ">
					AND t.line_id = #{e.lineId}
				</if>
	</sql>

</mapper>