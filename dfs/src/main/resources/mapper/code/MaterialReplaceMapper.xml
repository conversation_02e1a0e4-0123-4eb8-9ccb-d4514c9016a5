<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.MaterialReplaceMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.code.MaterialReplaceEntity">
		    <result column="id" property="id" />
		    <result column="material_code" property="materialCode" />
		    <result column="replace_material_code" property="replaceMaterialCode" />
		    <result column="code_record_id" property="codeRecordId" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.materialCode != null and e.materialCode != '' ">
					AND t.material_code = #{e.materialCode}
				</if>
				<if test="e.replaceMaterialCode != null and e.replaceMaterialCode != '' ">
					AND t.replace_material_code = #{e.replaceMaterialCode}
				</if>
				<if test="e.codeRecordId != null and e.codeRecordId != '' ">
					AND t.code_record_id = #{e.codeRecordId}
				</if>
	</sql>

</mapper>