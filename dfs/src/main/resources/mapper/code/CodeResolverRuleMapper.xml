<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.CodeResolverRuleMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.code.CodeResolverRuleEntity">
		    <result column="id" property="id" />
		    <result column="code" property="code" />
		    <result column="name" property="name" />
		    <result column="code_prefix" property="codePrefix" />
		    <result column="create_by" property="createBy" />
		    <result column="create_time" property="createTime" />
		    <result column="update_by" property="updateBy" />
		    <result column="update_time" property="updateTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.code != null and e.code != '' ">
					AND t.code = #{e.code}
				</if>
				<if test="e.name != null and e.name != '' ">
					AND t.name = #{e.name}
				</if>
				<if test="e.codePrefix != null and e.codePrefix != '' ">
					AND t.code_prefix = #{e.codePrefix}
				</if>
				<if test="e.createBy != null and e.createBy != '' ">
					AND t.create_by = #{e.createBy}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
				<if test="e.updateBy != null and e.updateBy != '' ">
					AND t.update_by = #{e.updateBy}
				</if>
				<if test="e.updateTime != null and e.updateTime != '' ">
					AND t.update_time = #{e.updateTime}
				</if>
	</sql>
    <select id="getList" resultType="com.yelink.dfs.entity.code.CodeResolverRuleEntity">
		select distinct codeResolverRule.* from dfs_code_resolver_rule codeResolverRule
		left join dfs_code_resolver_rule_line codeResolverRuleLine on codeResolverRule.id = codeResolverRuleLine.rule_id
		<if test="sql != null and sql != ''">
			${sql}
		</if>
	</select>

</mapper>