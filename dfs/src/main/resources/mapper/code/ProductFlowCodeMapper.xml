<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.ProductFlowCodeMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.code.ProductFlowCodeEntity">
        <result column="id" property="id"/>
        <result column="product_flow_code" property="productFlowCode"/>
        <result column="state" property="state"/>
        <result column="relation_number" property="relationNumber"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="type" property="type"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.productFlowCode != null and e.productFlowCode != '' ">
            AND t.product_flow_code = #{e.productFlowCode}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.relationNumber != null and e.relationNumber != '' ">
            AND t.relation_number = #{e.relationNumber}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
    </sql>
    <select id="generateCount" resultType="com.yelink.dfscommon.dto.BatchGenerateCodeDTO">
        select relation_number as orderNumber,count(product_flow_code) as existQuantity from dfs_product_flow_code
        where relation_number in
        <foreach item="item" index="index" collection="dto.orderNumbers" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="dto.type != null">
            AND type = #{dto.type}
        </if>
        GROUP BY relation_number
    </select>

    <delete id="deleteDuplicateProductFlowCodeData" >
        DELETE FROM dfs_product_flow_code
        WHERE relation_number=#{relationNumber} AND
                id IN
                (SELECT t.id FROM
                    ( SELECT  MIN( id ) AS id,count(1) count FROM dfs_product_flow_code WHERE relation_number=#{relationNumber} GROUP BY `product_flow_code` HAVING count>1) t);
    </delete>

    <select id="selectByRecord" resultType="com.yelink.dfs.entity.code.ProductFlowCodeEntity">
        select DISTINCT a.* from dfs_product_flow_code a
        left JOIN dfs_product_flow_code_record b ON a.product_flow_code = b.product_flow_code
        where a.relation_number = #{dto.workOrder}
        <if test="dto.craftProcedureId != null and dto.craftProcedureId != ''">
            and  b.produre_id  =#{dto.craftProcedureId}
        </if>
        <if test="dto.isRecord == 0">
            and b.product_flow_code is null
        </if>
        <if test="dto.isRecord == 1">
            and b.product_flow_code is not null
        </if>
        <if test="dto.materialCode != null and dto.materialCode != ''">
            and a.material_code =#{dto.materialCode}
        </if>
        <if test="dto.materialName != null and dto.materialName != ''">
            and a.material_name =#{dto.materialName}
        </if>
        <if test="dto.type != null and dto.type != ''">
            and a.type =#{dto.type}
        </if>
        <if test="dto.productFlowCode != null and dto.productFlowCode != ''">
            and a.product_flow_code =#{dto.productFlowCode}
        </if>
        <if test="dto.state != null and dto.state != ''">
            a.state  IN
            <foreach item="item" collection="dto.state.split(',')" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="dto.skuId != null">
            and a.sku_id =#{dto.skuId}
        </if>
        <if test="dto.isPrint != null">
            and a.is_print =#{dto.isPrint}
        </if>
        <if test="dto.isMaintain == 0">
            and a.maintain_state is null
        </if>
        <if test="dto.isMaintain == 1">
            and a.maintain_state is not null
        </if>
        <if test="dto.maintainState != null">
            and a.maintain_state =#{dto.maintainState}
        </if>
        <if test="dto.qualityState != null">
            and a.quality_state =#{dto.qualityState}
        </if>

        <if test="dto.craftProcedureIdsString != null and dto.craftProcedureIdsString != ''" >
            and b.produre_id  IN
            <foreach item="item" collection="dto.craftProcedureIdsString.split(',')" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <!--      <if test="dto.currentPage != null and dto.size != null" >-->
        <!--      LIMIT #{dto.currentPage}, #{dto.size}-->
        <!--      </if>-->
    </select>

    <select id="selectByRecordNot" resultType="com.yelink.dfs.entity.code.ProductFlowCodeEntity">
        select * from dfs_product_flow_code a where a.relation_number = #{dto.workOrder}
        <if test="dto.materialCode != null and dto.materialCode != ''">
            and a.material_code =#{dto.materialCode}
        </if>
        <if test="dto.materialName != null and dto.materialName != ''">
            and a.material_name =#{dto.materialName}
        </if>
        <if test="dto.type != null">
            and a.type =#{dto.type}
        </if>
        <if test="dto.state != null and dto.state != ''">
            a.state IN
            <foreach item="item" collection="dto.state.split(',')" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        <if test="dto.skuId != null">
            and a.sku_id =#{dto.skuId}
        </if>
        <if test="dto.isPrint != null">
            and a.is_print =#{dto.isPrint}
        </if>
        <if test="dto.isMaintain == 0">
            and a.maintain_state is null
        </if>
        <if test="dto.isMaintain == 1">
            and a.maintain_state is not null
        </if>
        <if test="dto.productFlowCode != null and dto.productFlowCode != ''">
            and a.product_flow_code =#{dto.productFlowCode}
        </if>
        <if test="dto.maintainState != null">
            and a.maintain_state =#{dto.maintainState}
        </if>
        <if test="dto.qualityState != null">
            and a.quality_state =#{dto.qualityState}
        </if>
        and product_flow_code  not in (
        select DISTINCT t.product_flow_code from dfs_product_flow_code t
        left JOIN dfs_product_flow_code_record b ON t.product_flow_code = b.product_flow_code
        where t.relation_number = #{dto.workOrder}
        <if test="dto.craftProcedureId != null">
            and b.produre_id =#{dto.craftProcedureId}
        </if>
        <if test="dto.craftProcedureIdsString != null and dto.craftProcedureIdsString != ''" >
            and b.produre_id  IN
            <foreach item="item" collection="dto.craftProcedureIdsString.split(',')" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        )
        <!--      <if test="dto.currentPage != null and dto.size != null" >-->
        <!--      LIMIT #{dto.currentPage}, #{dto.size}-->
        <!--      </if>-->
    </select>

    <select id="getRelationNumberListByRelatedType" resultType="java.lang.String">
        SELECT relation_number FROM `dfs_product_flow_code`
        where type = #{relatedType}
        <if test="relationNumber != null">
            and relation_number like '%${relationNumber}%'
        </if>
        GROUP BY relation_number
    </select>


    <select id="exportList" resultType="com.yelink.dfs.entity.code.ProductFlowCodeEntity">
        SELECT
        a.*
        FROM
        dfs_product_flow_code a
        <where >
            1 = 1
            <if test="productFlowCodeSelectDTO.ids != null and productFlowCodeSelectDTO.ids.size() > 0 ">
                AND a.id in
                <foreach item="item" collection="productFlowCodeSelectDTO.ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="productFlowCodeSelectDTO.productFlowCode!=null  and productFlowCodeSelectDTO.productFlowCode != '' ">
                AND a.product_flow_code like CONCAT ('%',#{productFlowCodeSelectDTO.productFlowCode},'%')
            </if>
            <if test="productFlowCodeSelectDTO.workOrder!=null  and productFlowCodeSelectDTO.workOrder != '' ">
                AND a.relation_number like CONCAT ('%',#{productFlowCodeSelectDTO.workOrder},'%')
            </if>
            <if test="productFlowCodeSelectDTO.materialType!=null  and productFlowCodeSelectDTO.materialType != '' ">
                AND a.material_type =#{productFlowCodeSelectDTO.materialType}
            </if>
            <if test="productFlowCodeSelectDTO.type!=null  and productFlowCodeSelectDTO.type != '' ">
                AND a.type =#{productFlowCodeSelectDTO.type}
            </if>
            <if test="productFlowCodeSelectDTO.types != null and productFlowCodeSelectDTO.types != ''">
                AND   a.type  IN
                <foreach item="item" collection="productFlowCodeSelectDTO.types.split(',')" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="productFlowCodeSelectDTO.materialCode!=null  and productFlowCodeSelectDTO.materialCode != '' ">
                AND a.material_code like CONCAT ('%',#{productFlowCodeSelectDTO.materialCode},'%')
            </if>
            <if test="productFlowCodeSelectDTO.materialName!=null  and codeRecordSelectDTO.materialName != '' ">
                AND a.material_name like CONCAT ('%',#{productFlowCodeSelectDTO.materialName},'%')
            </if>
            <if test="productFlowCodeSelectDTO.productTimeStart != null and productFlowCodeSelectDTO.productTimeEnd != null">
                AND a.end_time between #{productFlowCodeSelectDTO.productTimeStart} and #{productFlowCodeSelectDTO.productTimeEnd}
            </if>
            <if test="productFlowCodeSelectDTO.currentMinId != null">
            AND #{productFlowCodeSelectDTO.currentMinId} > a.id
            </if>

            ORDER BY id desc
            limit ${productFlowCodeSelectDTO.limit}
        </where>
    </select>

    <select id="exportListTotal" resultType="java.lang.Double">
        SELECT
        count(*)
        FROM
        dfs_product_flow_code a
        <where >
            1 = 1
            <if test="productFlowCodeSelectDTO.ids != null and productFlowCodeSelectDTO.ids.size() > 0 ">
                AND a.id in
                <foreach item="item" collection="productFlowCodeSelectDTO.ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="productFlowCodeSelectDTO.productFlowCode!=null  and productFlowCodeSelectDTO.productFlowCode != '' ">
                AND a.product_flow_code like CONCAT ('%',#{productFlowCodeSelectDTO.productFlowCode},'%')
            </if>
            <if test="productFlowCodeSelectDTO.workOrder!=null  and productFlowCodeSelectDTO.workOrder != '' ">
                AND a.relation_number like CONCAT ('%',#{productFlowCodeSelectDTO.workOrder},'%')
            </if>
            <if test="productFlowCodeSelectDTO.materialType!=null  and productFlowCodeSelectDTO.materialType != '' ">
                AND a.material_type =#{productFlowCodeSelectDTO.materialType}
            </if>
            <if test="productFlowCodeSelectDTO.type!=null  and productFlowCodeSelectDTO.type != '' ">
                AND a.type =#{productFlowCodeSelectDTO.type}
            </if>
            <if test="productFlowCodeSelectDTO.types != null and productFlowCodeSelectDTO.types != ''">
                AND  a.type  IN
                <foreach item="item" collection="productFlowCodeSelectDTO.types.split(',')" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="productFlowCodeSelectDTO.materialCode!=null  and productFlowCodeSelectDTO.materialCode != '' ">
                AND a.material_code like CONCAT ('%',#{productFlowCodeSelectDTO.materialCode},'%')
            </if>
            <if test="productFlowCodeSelectDTO.materialName!=null  and productFlowCodeSelectDTO.materialName != '' ">
                AND a.material_name like CONCAT ('%',#{productFlowCodeSelectDTO.materialName},'%')
            </if>
            <if test="productFlowCodeSelectDTO.productTimeStart != null and productFlowCodeSelectDTO.productTimeEnd != null">
                AND a.end_time between #{productFlowCodeSelectDTO.productTimeStart} and #{productFlowCodeSelectDTO.productTimeEnd}
            </if>

        </where>
    </select>
</mapper>
