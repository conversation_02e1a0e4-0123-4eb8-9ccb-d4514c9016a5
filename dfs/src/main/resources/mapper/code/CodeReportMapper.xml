<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.CodeReportMapper">

    <select id="accessQuantityMap" resultType="com.yelink.dfs.entity.reporter.dto.CodeReportGroupVO">
        SELECT
            relation_number AS relationNumber,
            COUNT(*) AS total
        FROM dfs_code_report
        WHERE is_report = 1
        <if test="dto.reportTimeDown != null">
            AND report_time <![CDATA[ >= ]]> #{dto.reportTimeDown}
        </if>
        <if test="dto.reportTimeUp != null ">
            AND report_time <![CDATA[ <= ]]> #{dto.reportTimeUp}
        </if>
        <if test="dto.isFirstUnqualified != null ">
            AND is_first_unqualified = #{dto.isFirstUnqualified}
        </if>
        <if test="dto.hasProcedure != null and dto.hasProcedure">
            AND report_produre_id IS NOT NULL
        </if>
        <if test="dto.hasProcedure != null and !dto.hasProcedure">
            AND report_produre_id IS NULL
        </if>
        <if test="dto.relationNumbers != null and dto.relationNumbers.size > 0">
            AND relation_number IN
            <foreach item="item" collection="dto.relationNumbers" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY relation_number
    </select>
</mapper>