<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.CodeRelevanceMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.productFlowCode.CodeRelevanceEntity">
		    <result column="id" property="id" />
		    <result column="code" property="code" />
		    <result column="relevance_code" property="relevanceCode" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.code != null and e.code != '' ">
					AND t.code = #{e.code}
				</if>
				<if test="e.relevanceCode != null and e.relevanceCode != '' ">
					AND t.relevance_code = #{e.relevanceCode}
				</if>
	</sql>

</mapper>