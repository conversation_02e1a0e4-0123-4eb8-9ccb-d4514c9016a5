<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.ProductFlowCodeRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity">
        <result column="id" property="id" />
        <result column="product_flow_code" property="productFlowCode" />
        <result column="relation_number" property="relationNumber" />
        <result column="line_id" property="lineId" />
        <result column="fac_id" property="facId" />
        <result column="produre_id" property="produreId" />
        <result column="material_code" property="materialCode" />
        <result column="material_name" property="materialName" />
        <result column="report_by" property="reportBy" />
        <result column="report_time" property="reportTime" />
        <result column="device_id" property="deviceId" />
        <result column="report_attribute" property="reportAttribute" />
        <result column="type" property="type" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.productFlowCode != null and e.productFlowCode != '' ">
            AND t.product_flow_code = #{e.productFlowCode}
        </if>
        <if test="e.relationNumber != null and e.relationNumber != '' ">
            AND t.relation_number = #{e.relationNumber}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.lineId != null and e.lineId != '' ">
            AND t.line_id = #{e.lineId}
        </if>
        <if test="e.FacId != null and e.FacId != '' ">
            AND t.fac_id = #{e.FacId}
        </if>
        <if test="e.produreId != null and e.produreId != '' ">
            AND t.produre_id = #{e.produreId}
        </if>
        <if test="e.reportBy != null and e.reportBy != '' ">
            AND t.report_by = #{e.reportBy}
        </if>
        <if test="e.reportTime != null and e.reportTime != '' ">
            AND t.report_time = #{e.reportTime}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.reportAttribute != null and e.reportAttribute != '' ">
            AND t.report_attribute = #{e.reportAttribute}
        </if>
        <if test="e.type != null and e.type != '' ">
            AND t.type = #{e.type}
        </if>
        <if test="e.startTime != null and e.startTime != '' ">
            AND t.start_time = #{e.startTime}
        </if>
        <if test="e.endTime != null and e.endTime != '' ">
            AND t.end_time = #{e.endTime}
        </if>
    </sql>
    <select id="selectListDetail" resultType="com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity">
        SELECT T1.*, T2.name lineName, T3.fname FacName,T4.procedure_name produreName
        from dfs_product_flow_code_record T1
        LEFT JOIN dfs_production_line T2 ON T2.production_line_id = T1.line_id
        LEFT JOIN dfs_facilities T3 on T3.fid = T1.fac_id
        LEFT JOIN dfs_craft_procedure T4 on T4.id = T1.produre_id
        where 1=1
        <if test="relationNumber!=null">
            AND T1.relation_number like CONCAT ('%',#{relationNumber},'%')
        </if>
        <if test="productFlowCode!=null">
            AND T1.product_flow_code like CONCAT ('%',#{productFlowCode},'%')
        </if>
        <if test="facId!=null">
            AND T1.fac_id =#{facId}
        </if>
        <if test="startTime!=null and endTime!=null">
            AND T1.report_time between #{startDate} and #{endDate}
        </if>
        order by T1.report_time
    </select>
    <select id="getCheckProduceId" resultType="java.lang.Integer">
        select pfr.id FROM  dfs_product_flow_code_record pfr inner join  dfs_craft_procedure cp on pfr.produre_id = cp.id
        INNER JOIN dfs_craft c on cp.craft_id = c.craft_id
        where   c.material_code = #{code}  and cp.procedure_name = #{name} and pfr.product_flow_code = #{flowCard}
        ORDER BY  pfr.end_time DESC  limit 1
    </select>
    <select id="pageList" resultType="com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity">
        SELECT
            a.*, b.`name` AS lineName,
            c.fname AS facName,
            d.procedure_name produreName
        FROM
            dfs_product_flow_code_record a
                LEFT JOIN dfs_production_line b ON a.line_id = b.production_line_id
                LEFT JOIN dfs_facilities c ON a.fac_id = c.fid
                LEFT JOIN dfs_craft_procedure d ON a.produre_id = d.id
        <where >
            1 = 1
            <if test="codeRecordSelectDTO.ids != null and codeRecordSelectDTO.ids.size() > 0 ">
                AND a.id in
                <foreach item="item" collection="codeRecordSelectDTO.ids" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="codeRecordSelectDTO.productFlowCode!=null  and codeRecordSelectDTO.productFlowCode != '' ">
                AND a.product_flow_code =#{codeRecordSelectDTO.productFlowCode}
            </if>
            <if test="codeRecordSelectDTO.relationNumber!=null  and codeRecordSelectDTO.relationNumber != '' ">
                AND a.relation_number =#{codeRecordSelectDTO.relationNumber}
            </if>
            <if test="codeRecordSelectDTO.productOrderNumber!=null  and codeRecordSelectDTO.productOrderNumber != '' ">
                AND a.product_order_number =#{codeRecordSelectDTO.productOrderNumber}
            </if>
            <if test="codeRecordSelectDTO.type != null and codeRecordSelectDTO.type .size() > 0 ">
                AND a.type in
                <foreach item="item" collection="codeRecordSelectDTO.type" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="codeRecordSelectDTO.lineId != null and codeRecordSelectDTO.lineId .size() > 0 ">
                AND a.line_id in
                <foreach item="item" collection="codeRecordSelectDTO.lineId" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="codeRecordSelectDTO.facId != null and codeRecordSelectDTO.facId .size() > 0 ">
                AND a.fac_id in
                <foreach item="item" collection="codeRecordSelectDTO.facId" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
            <if test="codeRecordSelectDTO.produreId!=null  and codeRecordSelectDTO.produreId != '' ">
                AND a.produre_id =#{codeRecordSelectDTO.produreId}
            </if>
            <if test="codeRecordSelectDTO.materialName!=null  and codeRecordSelectDTO.materialName != '' ">
                AND a.material_name like CONCAT ('%',#{codeRecordSelectDTO.materialName},'%')
            </if>
            <if test="codeRecordSelectDTO.materialCode!=null  and codeRecordSelectDTO.materialCode != '' ">
                AND a.material_code =#{codeRecordSelectDTO.materialCode}
            </if>
            <if test="codeRecordSelectDTO.produreName!=null  and codeRecordSelectDTO.produreName != '' ">
                AND d.procedure_name like CONCAT ('%',#{codeRecordSelectDTO.produreName},'%')
            </if>
            <if test="codeRecordSelectDTO.startTime!=null  and codeRecordSelectDTO.startTime != ''  and codeRecordSelectDTO.endTime!=null  and codeRecordSelectDTO.endTime != '' ">
                AND a.report_time BETWEEN #{codeRecordSelectDTO.startTime} and #{codeRecordSelectDTO.endTime}
            </if>
            <if test="currentMinId != null">
                AND #{currentMinId} > a.id
            </if>
            ORDER BY id desc
            limit ${limit}
        </where>
    </select>

    <select id="procedureProcess" resultType="com.yelink.dfscommon.entity.ams.dto.ProcedureProcessVO">
        select count( distinct product_flow_code) as finishSum,produre_id as craftProcedureId from dfs_product_flow_code_record
        where relation_number = #{workOrderNumber} and state = 1 GROUP BY produre_id;
    </select>
    <select id="getList" resultType="com.yelink.dfs.open.v2.code.vo.ProductFlowCodeRecordVO">
        select codeRecord.* from dfs_product_flow_code_record codeRecord
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

    <select id="productOrderProcedureProcess" resultType="com.yelink.dfscommon.entity.ams.dto.ProcedureProcessVO">
        select count( distinct product_flow_code) as finishSum,produre_id as craftProcedureId from dfs_product_flow_code_record
        where product_order_number = #{productOrderNumber} and state = 1 GROUP BY produre_id;
    </select>
</mapper>