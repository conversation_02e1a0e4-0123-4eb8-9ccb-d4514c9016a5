<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.ScanCodeRuleMapper">


    <select id="getList" resultType="com.yelink.dfs.entity.code.ScanCodeRuleEntity">
        select scanCodeRule.* from dfs_scan_code_rule scanCodeRule
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>
</mapper>