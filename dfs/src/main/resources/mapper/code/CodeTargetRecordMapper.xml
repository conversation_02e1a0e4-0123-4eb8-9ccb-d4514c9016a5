<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.code.CodeTargetRecordMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfscommon.entity.dfs.productFlowCode.CodeTargetRecordEntity">
		    <result column="id" property="id" />
		    <result column="code_record_id" property="codeRecordId" />
		    <result column="target_name" property="targetName" />
		    <result column="target_ename" property="targetEname" />
		    <result column="value" property="value" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.codeRecordId != null and e.codeRecordId != '' ">
					AND t.code_record_id = #{e.codeRecordId}
				</if>
				<if test="e.targetName != null and e.targetName != '' ">
					AND t.target_name = #{e.targetName}
				</if>
				<if test="e.targetEname != null and e.targetEname != '' ">
					AND t.target_ename = #{e.targetEname}
				</if>
				<if test="e.value != null and e.value != '' ">
					AND t.value = #{e.value}
				</if>
	</sql>

</mapper>