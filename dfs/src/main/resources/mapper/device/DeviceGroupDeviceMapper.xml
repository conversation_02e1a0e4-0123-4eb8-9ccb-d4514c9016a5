<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.device.DeviceGroupDeviceMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.device.DeviceGroupDeviceEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="groud_id" property="groudId"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.groudId != null and e.groudId != '' ">
            AND t.groud_id = #{e.groudId}
        </if>
    </sql>

</mapper>