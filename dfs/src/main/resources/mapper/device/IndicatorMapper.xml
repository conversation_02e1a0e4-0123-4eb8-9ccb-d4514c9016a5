<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.device.IndicatorMapper">

    <resultMap id="ResultMap" type="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="data_val" property="dataVal"/>
        <result column="time" property="time"/>
        <result column="batch" property="batch"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
    </resultMap>
    <update id="updateRecord">
        UPDATE ${tableName}
        SET `batch`=#{batch}
        WHERE device_id = #{deviceId}
        AND `time` between #{startDate} and #{endDate};
    </update>
    <update id="deleteBatchRecord">
        UPDATE ${tableName}
        SET `batch`= ''
        WHERE device_id = #{deviceId}
        AND `time` between #{startDate} and #{endDate};
    </update>
    <update id="createTable">
    DROP TABLE IF EXISTS `${tableName}`;
    CREATE TABLE ${tableName}(
         `id` int NOT NULL AUTO_INCREMENT COMMENT '记录id',
         `device_id` int DEFAULT NULL COMMENT '设施id',
         `${fieldName}` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '${targetCname}',
         `batch` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '批次号',
         `time` datetime NOT NULL COMMENT '记录时间',
          PRIMARY KEY (`id`,`time`) USING BTREE,
          KEY `time` (`time`) USING BTREE,
          KEY `device_id` (`device_id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT= '${targetCname}指标记录表'
    PARTITION BY RANGE (TO_DAYS(`time`)) (
        PARTITION ${partitionName} VALUES LESS THAN (${toDays})
    );
    </update>


    <select id="getListData" resultMap="ResultMap">
        SELECT
        target.id,
        target.device_id,
        target.`${fieldName}` data_val ,
        target.`time`,
        target.batch,
        m.name material_name,
        workOrder.material_code
        FROM
        ${tableName} target
        left join dfs_work_order workOrder
        on workOrder.work_order_number = target.batch LEFT JOIN dfs_material m on workOrder.material_code = m.code
        <where>
            <if test="deviceId !=null">
                target.device_id=#{deviceId}
            </if>
            <if test="startDate !=null">
                AND target.`time` &gt; #{startDate}
            </if>
            <if test=" endDate != null">
                AND target.`time` &lt; #{endDate}
            </if>
            <if test="workOrderNumber != null and workOrderNumber!=''">
                and target.batch = #{workOrderNumber}
            </if>
            <if test="materialName != null and materialName!=''">
                and m.name = #{materialName}
            </if>
        </where>
        order by target.`time` desc
    </select>


    <select id="selectTable" resultType="java.lang.String">
        select TABLE_NAME
        from information_schema.TABLES
        where TABLE_NAME = #{tableName} and table_schema = ( SELECT DATABASE () )
    </select>

    <select id="selectAverage" resultMap="ResultMap">
        select device_id, AVG(`${fieldName}`) data_val, batch
        from ${tableName}
        where time &gt;= #{startTime}
        and time &lt; #{endTime}
        GROUP BY device_id;
    </select>

    <insert id="addIndicator" parameterType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        insert into ${tableName}(device_id, `${fieldName}`, `time`, `batch`)
        values (#{deviceId}, #{dataVal}, #{time}, #{batch})
    </insert>

    <select id="getTheSameTimeData" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`
        from ${tableName}
        where `time` = #{time}
        and device_id = #{deviceId}
    </select>

    <select id="getTenRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` &gt; DATE_SUB(#{currentDate}, INTERVAL 10 MINUTE)
        AND `time` &lt; #{currentDate}
        order by `time` asc
    </select>

    <select id="getTenRecordsByGrid" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, grid_id, `${fieldName}` data_val, `time`
        from ${tableName}
        where grid_id = #{gridId}
        AND `time` &gt; DATE_SUB(#{currentDate}, INTERVAL 10 MINUTE)
        order by `time` asc
    </select>

    <select id="getDayRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` &gt; DATE_SUB(#{currentDate}, INTERVAL 10 MINUTE)
        order by `time` asc
    </select>
    <select id="getWeekRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        and DATE_SUB(CURDATE(), INTERVAL 7 DAY) &lt; date(`time`)
    </select>
    <select id="getMonthRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        and DATE_SUB(CURDATE(), INTERVAL 30 DAY) &lt; date(`time`)
    </select>
    <select id="getNewVal" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`
        from ${tableName}
        where device_id = #{deviceId}
        order by `time` DESC
        limit 1
    </select>
    <select id="getNewValByTime" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        and `time` &lt; #{time}
        order by `id` DESC
        limit 1
    </select>
    <select id="getLatestValByTimeRange" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        and `time` > #{timeDateStart}
        and `time` &lt; #{timeDateEnd}
        order by `time` DESC
        limit 1
    </select>
    <select id="getNewValBeforeTime" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where time &lt; #{time}
        order by `id` DESC
        limit 1
    </select>
    <select id="getRealTimeRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id,device_id,`${fieldName}` data_val, `time`,`batch`
        from ${tableName}
        where device_id=#{deviceId}
        <if test="batchs != null and batchs.length > 0">
            and batch in
            <foreach item="item" collection="batchs" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        AND `time` &gt; DATE_SUB(#{currentDate}, INTERVAL 10 MINUTE )
        order by `time` desc
    </select>
    <select id="getRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id,device_id,`${fieldName}` data_val, `time`,`batch`
        from ${tableName}
        where device_id=#{deviceId}
        <if test="batchs != null and batchs.length > 0">
            and `batch` in
            <foreach item="item" collection="batchs" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </if>
        AND `time` between #{startDate} and #{endDate}
        ORDER BY `time` ASC
    </select>
    <select id="getMoreBatchRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        AND batch = #{batch}
        ORDER BY `time` ASC
    </select>
    <select id="getSevenRecordsTrue" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        order by `time` DESC
        limit 7
    </select>

    <select id="getBatchByDeviceId" resultType="string">
        select batch
        from ${tableName}
        where device_id = #{deviceId}
        group by batch
    </select>

    <select id="getDataByBatch" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        and batch = #{batch}
        order by `time` DESC
        limit 1
    </select>
    <select id="getNewValByBatch" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        and batch = #{batch}
        order by `time` DESC
        limit 1
    </select>

    <select id="getRecordInmMinute" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` &gt; DATE_SUB(#{currentDate}, INTERVAL 60 MINUTE)
    </select>
    <select id="getComparisonRecord" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        SELECT id, device_id, `${fieldName}` data_val, record_date `time`
        FROM ${tableName}
        WHERE device_id = #{deviceId}
        AND TO_DAYS(#{currentDate}) - TO_DAYS(record_date) &lt;= 7
        AND TO_DAYS(#{currentDate}) - TO_DAYS(record_date) &gt;= 0
        ORDER BY record_date ASC
    </select>

    <select id="getTargetRecords" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        <if test="startTime != null">
            and `time` &lt; #{startTime}
        </if>
        order by `time` desc
        limit #{limit}
    </select>

    <select id="getStateDateList" resultMap="ResultMap">
        select id,device_id,`state` data_val, `time` from dfs_record_device_state
        <if test="deviceId !=null and ''!=deviceId">
            where device_id=#{deviceId}
        </if>
        order by `time` DESC
    </select>

    <select id="getBatchByTime" resultType="string">
        select `batch`
        from ${tableName}
        where device_id = #{deviceId}
        and `time` = #{time}
    </select>
    <select id="getPreviousData" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        order by `time` desc
        limit 1
    </select>
    <select id="getToDayRunRecordList" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
       SELECT
       id,device_id,`running` data_val, `time`  FROM dfs_record_device_run
        WHERE
            device_id = #{deviceId} and
            TO_DAYS( time ) = TO_DAYS(#{currentDate})
        ORDER BY
            `time` DESC
            LIMIT 1;

    </select>

    <select id="getByBatchOrTime" resultMap="ResultMap">
        select id,device_id,`${fieldName}` data_val, `time` ,batch from ${tableName}
        <if test="batch !=null and ''!=batch">
            where batch=#{batch}
        </if>
        <if test="startDate !=null and endDate != null">
            AND `time` between #{startTime} and #{endTime}
        </if>
        order by `time` DESC
    </select>
    <select id="getBatch" resultType="java.lang.String">
        select distinct batch from ${tableName}
        <where>
            <if test="deviceId !=null and ''!=deviceId">
                device_id=#{deviceId}
            </if>
            <if test="startDate !=null and endDate != null">
                AND `time` between #{startTime} and #{endTime}
            </if>
        </where>
        order by `time` desc
    </select>

    <select id="getRecentValue" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` between #{startDate} and #{endDate}
        <if test="isFirst">
            order by `time` ASC
            limit 1
        </if>
        <if test="!isFirst">
            order by `time` DESC
            limit 1
        </if>
    </select>

    <select id="getFarthestValue" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` between #{startDate} and #{endDate}
        <if test="isFirst">
            order by `time` DESC
            limit 1
        </if>
        <if test="!isFirst">
            order by `time` ASC
            limit 1
        </if>
    </select>

    <select id="getMaxValue" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select MAX(CAST(`${fieldName}` as decimal(11,4))) data_val, `time`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` between #{startDate} and #{endDate}
        group by `time`
    </select>

    <select id="getMinValue" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select MIN(CAST(`${fieldName}` as decimal(11,4))) data_val, `time`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` between #{startDate} and #{endDate}
        group by `time`
    </select>

    <select id="getAvgValue" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select AVG(CAST(`${fieldName}` as decimal(11,4))) data_val, `time`
        from ${tableName}
        where device_id = #{deviceId}
        AND `time` between #{startDate} and #{endDate}
        group by `time`
    </select>

    <select id="getListByDeviceAndTime" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        select id, device_id, `${fieldName}` data_val, `time`, `batch` from ${tableName}
        where `device_id` in
        <foreach item="item" collection="deviceIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        AND `time` between #{startDate} and #{endDate}
    </select>

    <select id="getMaxMinTargetValue" resultType="com.yelink.dfs.entity.device.dto.IndicatorEntityDTO">
        SELECT
        device_id as deviceId,
        IFNULL( MAX(`${fieldName}` + 0), 0 ) AS `maxValue`,
        IFNULL( MIN(`${fieldName}` + 0), 0 ) AS `minValue`
        FROM
        ${tableName}
        WHERE
        device_id=${deviceId}
        AND `time` between #{startDate} and #{endDate}
    </select>

    <select id="getToDays" resultType="java.lang.String">
        SELECT TO_DAYS(#{date})
    </select>

    <update id="addPartition">
        CALL create_partition_by_day(#{tableName},NOW());
    </update>

    <select id="getMetricsTables" resultType="java.lang.String">
        SELECT SUBSTRING_INDEX(a.`table_name`, '_1h', 1)
        FROM information_schema.tables a
        WHERE table_schema= 'dfs' AND a.`table_name` LIKE '%_1h'
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
        SELECT max(${idField}) FROM ${tableName}
    </select>

    <select id="selectByIdRange" resultType="java.util.Map">
        SELECT * FROM ${tableName} WHERE ${idField} BETWEEN ${startId} AND ${endId}
    </select>


</mapper>
