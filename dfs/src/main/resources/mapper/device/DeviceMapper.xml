<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.device.DeviceMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.device.DeviceEntity">
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_code" property="deviceCode"/>
        <result column="cid" property="cid"/>
        <result column="aid" property="aid"/>
        <result column="gid" property="gid"/>
        <result column="fid" property="fid"/>
        <result column="production_line_id" property="productionLineId"/>
        <result column="type_code" property="typeCode"/>
        <result column="type_name" property="typeName"/>
        <result column="state" property="state"/>
        <result column="sort" property="sort"/>
        <result column="lat" property="lat"/>
        <result column="lng" property="lng"/>
        <result column="alt" property="alt"/>
        <result column="place" property="place"/>
        <result column="img" property="img"/>
        <result column="model_img" property="modelImg"/>
        <result column="simulation_img" property="simulationImg"/>
        <result column="mag_employee_id" property="magEmployeeId"/>
        <result column="mag_nickname" property="magNickname"/>
        <result column="mag_name" property="magName"/>
        <result column="mag_phone" property="magPhone"/>
        <result column="remark" property="remark"/>
        <result column="location" property="location"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="pro_target_id" property="proTargetId"/>
        <result column="adm_target_id" property="admTargetId"/>
        <result column="sp_model" property="spModel"/>
        <result column="seq" property="seq"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_name" property="fileName"/>
        <result column="state_update_time" property="stateUpdateTime"/>
        <result column="tag_id" property="tagId"/>
        <result column="maintenance_state" property="maintenanceState"/>

    </resultMap>


    <sql id="select_content">
        <if test="e.proTargetId != null and e.proTargetId != '' ">
            AND t.pro_target_id = #{e.proTargetId}
        </if>
        <if test="e.admTargetId != null and e.admTargetId != '' ">
            AND t.adm_target_id = #{e.admTargetId}
        </if>
        <if test="e.deviceName != null and e.deviceName != '' ">
            AND t.device_name = #{e.deviceName}
        </if>
        <if test="e.deviceCode != null and e.deviceCode != '' ">
            AND t.device_code = #{e.deviceCode}
        </if>
        <if test="e.cid != null and e.cid != '' ">
            AND t.cid = #{e.cid}
        </if>
        <if test="e.aid != null and e.aid != '' ">
            AND t.aid = #{e.aid}
        </if>
        <if test="e.gid != null and e.gid != '' ">
            AND t.gid = #{e.gid}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.sort != null and e.sort != '' ">
            AND t.sort = #{e.sort}
        </if>
        <if test="e.productionLineId != null and e.productionLineId != '' ">
            AND t.production_line_id = #{e.productionLineId}
        </if>
        <if test="e.typeCode != null and e.typeCode != '' ">
            AND t.type_code = #{e.typeCode}
        </if>
        <if test="e.typeName != null and e.typeName != '' ">
            AND t.type_name = #{e.typeName}
        </if>
        <if test="e.state != null and e.state != '' ">
            AND t.state = #{e.state}
        </if>
        <if test="e.lat != null and e.lat != '' ">
            AND t.lat = #{e.lat}
        </if>
        <if test="e.lng != null and e.lng != '' ">
            AND t.lng = #{e.lng}
        </if>
        <if test="e.alt != null and e.alt != '' ">
            AND t.alt = #{e.alt}
        </if>
        <if test="e.place != null and e.place != '' ">
            AND t.place = #{e.place}
        </if>
        <if test="e.img != null and e.img != '' ">
            AND t.img = #{e.img}
        </if>
        <if test="e.modelImg != null and e.modelImg != '' ">
            AND t.model_img = #{e.modelImg}
        </if>
        <if test="e.simulationImg != null and e.simulationImg != '' ">
            AND t.simulation_img = #{e.simulationImg}
        </if>
        <if test="e.magEmployeeId != null and e.magEmployeeId != '' ">
            AND t.mag_employee_id = #{e.magEmployeeId}
        </if>
        <if test="e.magNickname != null and e.magNickname != '' ">
            AND t.mag_nickname = #{e.magNickname}
        </if>
        <if test="e.magName != null and e.magName != '' ">
            AND t.mag_name = #{e.magName}
        </if>
        <if test="e.magPhone != null and e.magPhone != '' ">
            AND t.mag_phone = #{e.magPhone}
        </if>
        <if test="e.remark != null and e.remark != '' ">
            AND t.remark = #{e.remark}
        </if>
        <if test="e.location != null and e.location != '' ">
            AND t.location = #{e.location}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.spModel != null and e.spModel != '' ">
            AND t.sp_model = #{e.spModel}
        </if>
        <if test="e.fileUrl != null and e.fileUrl != '' ">
            AND t.file_url = #{e.fileUrl}
        </if>
        <if test="e.fileName != null and e.fileName != '' ">
            AND t.file_name = #{e.fileName}
        </if>
        <if test="e.stateUpdateTime != null and e.stateUpdateTime != '' ">
            AND t.state_update_time = #{e.stateUpdateTime}
        </if>
        <if test="e.maintenanceState != null and e.maintenanceState != '' ">
            AND t.maintenance_state = #{e.maintenanceState}
        </if>
    </sql>

    <select id="getlistByFid" resultMap="BaseResultMap">
        select a.*, c.seq
        from dfs_device a
        left join dfs_facilities b on a.fid = b.fid
        left join dfs_model c on b.model_id = c.id
        where a.fid = #{fid}
        order by c.seq asc
    </select>

    <select id="getListByFidAndUsername" resultMap="BaseResultMap">
        select a.*
        from dfs_device a
        where a.fid=#{fid}
        and a.mag_name = #{username}
    </select>

    <select id="getFacListByUsername" resultType="integer">
        select fid
        from dfs_facilities
        where mag_name = #{username}
    </select>

    <select id="getFidByUsername" resultType="integer">
        select fid from dfs_fac_user where user_name = #{username}
    </select>
    <select id="getDeviceByModelCode" resultType="com.yelink.dfs.entity.device.DeviceEntity">
        SELECT
            *
        FROM
            dfs_device a
        LEFT JOIN dfs_model b ON a.model_id = b.id
        WHERE
            b.`code` = #{code}
    </select>

    <select id="getCountDeviceAndTarget" resultType="com.yelink.dfs.entity.model.FacSensorEntity">
        SELECT d.device_id deviceId, tm.relate_target_name countTarget
        from dfs_device d
        left join dfs_counter_target_config tm on d.model_id = tm.model_id
        where d.fid = #{fid} and count_object = #{type} limit 1
    </select>

    <select id="getDeviceWithCounter" resultType="java.lang.String">
        select s.eui from dfs_device d
        left join dfs_device_sensor ds on ds.device_id = d.device_id
        left join dfs_sensor s on ds.eui=s.eui
        where s.sensor_type=#{sensorType} and d.fid=#{fid}
        limit 1
    </select>

    <select id="getList" resultType="com.yelink.dfs.entity.device.DeviceEntity">
        select device.* from dfs_device device
        <if test="sql != null and sql != ''">
            ${sql}
        </if>
    </select>

</mapper>
