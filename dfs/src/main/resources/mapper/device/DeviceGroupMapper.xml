<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.device.DeviceGroupMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.device.DeviceGroupEntity">
        <result column="id" property="id"/>
        <result column="group_name" property="groupName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.groupName != null and e.groupName != '' ">
            AND t.group_name = #{e.groupName}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
    </sql>
    <select id="listByModelCode" resultType="com.yelink.dfs.entity.device.DeviceGroupEntity">
        select distinct g.* from dfs_device_group g
        left join dfs_device_group_device gd on g.id = gd.groud_id
        left join dfs_device d on d.device_id = gd.device_id
        left join dfs_model m on m.id = d.model_id and m.type = 'device'
        where m.code = #{modelCode}
        ORDER BY g.id;
    </select>

</mapper>