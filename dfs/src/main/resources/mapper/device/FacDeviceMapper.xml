<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.FacDeviceMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.device.FacDeviceEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="fid" property="fid"/>
        <result column="is_key" property="isKey"/>
        <result column="create_date" property="createDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.fid != null and e.fid != '' ">
            AND t.fid = #{e.fid}
        </if>
        <if test="e.isKey != null and e.isKey != '' ">
            AND t.is_key = #{e.isKey}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
    </sql>

</mapper>