<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.device.DeviceCalendarMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.device.DeviceCalendarEntity">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="order_number" property="orderNumber"/>
        <result column="order_name" property="orderName"/>
        <result column="theoretical_speed" property="theoreticalSpeed"/>
        <result column="shift" property="shift"/>
        <result column="work_time" property="workTime"/>
        <result column="rest_time" property="restTime"/>
        <result column="down_time" property="downTime"/>
        <result column="calendar_date" property="calendarDate"/>
        <result column="week" property="week"/>
        <result column="work_duration" property="workDuration"/>
        <result column="rest_duration" property="restDuration"/>
        <result column="down_duration" property="downDuration"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.orderNumber != null and e.orderNumber != '' ">
            AND t.order_number = #{e.orderNumber}
        </if>
        <if test="e.orderName != null and e.orderName != '' ">
            AND t.order_name = #{e.orderName}
        </if>
        <if test="e.theoreticalSpeed != null and e.theoreticalSpeed != '' ">
            AND t.theoretical_speed = #{e.theoreticalSpeed}
        </if>
        <if test="e.shift != null and e.shift != '' ">
            AND t.shift = #{e.shift}
        </if>
        <if test="e.workTime != null and e.workTime != '' ">
            AND t.work_time = #{e.workTime}
        </if>
        <if test="e.restTime != null and e.restTime != '' ">
            AND t.rest_time = #{e.restTime}
        </if>
        <if test="e.downTime != null and e.downTime != '' ">
            AND t.down_time = #{e.downTime}
        </if>
        <if test="e.calendarDate != null and e.calendarDate != '' ">
            AND t.calendar_date = #{e.calendarDate}
        </if>
        <if test="e.week != null and e.week != '' ">
            AND t.week = #{e.week}
        </if>
        <if test="e.workDuration != null and e.workDuration != '' ">
            AND t.work_duration = #{e.workDuration}
        </if>
        <if test="e.restDuration != null and e.restDuration != '' ">
            AND t.rest_duration = #{e.restDuration}
        </if>
        <if test="e.downDuration != null and e.downDuration != '' ">
            AND t.down_duration = #{e.downDuration}
        </if>
        <if test="e.updateBy != null and e.updateBy != '' ">
            AND t.update_by = #{e.updateBy}
        </if>
        <if test="e.createBy != null and e.createBy != '' ">
            AND t.create_by = #{e.createBy}
        </if>
        <if test="e.createTime != null and e.createTime != '' ">
            AND t.create_time = #{e.createTime}
        </if>
        <if test="e.updateTime != null and e.updateTime != '' ">
            AND t.update_time = #{e.updateTime}
        </if>
    </sql>

</mapper>