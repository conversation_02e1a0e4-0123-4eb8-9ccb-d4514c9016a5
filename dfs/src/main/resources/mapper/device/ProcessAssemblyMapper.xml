<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.device.ProcessAssemblyMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.device.ProcessAssemblyEntity">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="model_id" property="modelId"/>
        <result column="type" property="type"/>
        <result column="unit" property="unit"/>
        <result column="classification" property="classification"/>
        <result column="state" property="state"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

</mapper>
