<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.device.DeviceSensorMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.device.DeviceSensorEntity">
        <result column="id" property="id"/>
        <result column="eui" property="eui"/>
        <result column="device_id" property="deviceId"/>
        <result column="create_date" property="createDate"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.eui != null and e.eui != '' ">
            AND t.eui = #{e.eui}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.createDate != null and e.createDate != '' ">
            AND t.create_date = #{e.createDate}
        </if>
    </sql>

    <select id="getSensorByDeviceId" resultType="com.yelink.dfs.entity.sensor.SensorEntity">
        select s.* from
        dfs_device_sensor ds
        left join
        dfs_sensor s
        on ds.eui=s.eui where ds.device_id=#{deviceId}
    </select>
    <select id="getSensorByDeviceIdAndEui" resultType="com.yelink.dfs.entity.sensor.SensorEntity">
        SELECT
        s.*
        FROM
        dfs_device_sensor ds
        LEFT JOIN
        dfs_sensor s
        ON
        ds.eui = s.eui
        WHERE
        ds.device_id = #{deviceId}
        AND
        s.eui = #{eui}
    </select>

    <select id="getSensorByDeviceIdsAndSensorType" resultType="java.lang.String">
        SELECT ds.eui FROM
        dfs_device_sensor ds
        left join
        dfs_sensor s
        on ds.eui=s.eui
        WHERE
        ds.device_id in
        <foreach item="item" collection="deviceIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        and
        s.sensor_type in
        <foreach item="item" collection="sensorType" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>

    <select id="getSensorByDeviceIdAndSensorType" resultType="com.yelink.dfs.entity.sensor.SensorEntity">
        select s.* from
        dfs_device_sensor ds
        left join
        dfs_sensor s
        on ds.eui=s.eui where ds.device_id=#{deviceId}
        <if test="sensorType!= null">
          and sensor_type=#{sensorType}
        </if>

    </select>

    <select id="getDevicesByEui" resultType="com.yelink.dfs.entity.device.DeviceEntity">
        SELECT
        d.*
        FROM
        dfs_device_sensor ds
        LEFT JOIN
        dfs_device d
        ON
        ds.device_id = d.device_id
        WHERE
        ds.eui = #{eui}
    </select>

    <select id="getEuiByDeviceIdAndTarget" resultType="java.lang.String">
        SELECT ds.eui FROM
        dfs_device_sensor ds
        LEFT JOIN
        dfs_sensor s
        ON ds.eui=s.eui
        WHERE
        ds.device_id=#{deviceId}
        AND
        JSON_CONTAINS(s.sensor_value,JSON_OBJECT('ename', #{targetName}))
        limit 1
    </select>

    <select id="getCounterByDeviceId" resultType="java.lang.String">
        SELECT ds.eui FROM
        dfs_device_sensor ds
        LEFT JOIN
        dfs_sensor s
        ON ds.eui=s.eui
        WHERE
        ds.device_id=#{deviceId}
        AND
        s.sensor_type=#{code}
        limit 1
    </select>


</mapper>
