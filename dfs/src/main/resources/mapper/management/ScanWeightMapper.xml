<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.management.ScanWeightMapper">

	<resultMap id="BaseResultMap" type="com.yelink.dfs.entity.management.ScanWeightEntity">
		    <result column="id" property="id" />
		    <result column="bar_code" property="barCode" />
		    <result column="scanner_eui" property="scannerEui" />
		    <result column="scan_time" property="scanTime" />
		    <result column="weight" property="weight" />
		    <result column="unit" property="unit" />
		    <result column="weighing_eui" property="weighingEui" />
		    <result column="weighing_time" property="weighingTime" />
		    <result column="create_time" property="createTime" />
	</resultMap>

	
	<sql id="select_content">	
				<if test="e.id != null and e.id != '' ">
					AND t.id = #{e.id}
				</if>
				<if test="e.barCode != null and e.barCode != '' ">
					AND t.bar_code = #{e.barCode}
				</if>
				<if test="e.scannerEui != null and e.scannerEui != '' ">
					AND t.scanner_eui = #{e.scannerEui}
				</if>
				<if test="e.scanTime != null and e.scanTime != '' ">
					AND t.scan_time = #{e.scanTime}
				</if>
				<if test="e.weight != null and e.weight != '' ">
					AND t.weight = #{e.weight}
				</if>
				<if test="e.unit != null and e.unit != '' ">
					AND t.unit = #{e.unit}
				</if>
				<if test="e.weighingEui != null and e.weighingEui != '' ">
					AND t.weighing_eui = #{e.weighingEui}
				</if>
				<if test="e.weighingTime != null and e.weighingTime != '' ">
					AND t.weighing_time = #{e.weighingTime}
				</if>
				<if test="e.createTime != null and e.createTime != '' ">
					AND t.create_time = #{e.createTime}
				</if>
	</sql>

</mapper>