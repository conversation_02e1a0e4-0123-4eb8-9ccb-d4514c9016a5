<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yelink.dfs.mapper.management.PackageRecordMapper">

    <resultMap id="BaseResultMap" type="com.yelink.dfs.entity.management.PackageRecordEntity">
        <result column="id" property="id"/>
        <result column="bar_code" property="barCode"/>
        <result column="work_order_number" property="workOrderNumber"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="report_time" property="reportTime"/>
        <result column="device_id" property="deviceId"/>
        <result column="package_attribute" property="packageAttribute"/>
    </resultMap>


    <sql id="select_content">
        <if test="e.id != null and e.id != '' ">
            AND t.id = #{e.id}
        </if>
        <if test="e.barCode != null and e.barCode != '' ">
            AND t.bar_code = #{e.barCode}
        </if>
        <if test="e.workOrderNumber != null and e.workOrderNumber != '' ">
            AND t.work_order_number = #{e.workOrderNumber}
        </if>
        <if test="e.materialCode != null and e.materialCode != '' ">
            AND t.material_code = #{e.materialCode}
        </if>
        <if test="e.materialName != null and e.materialName != '' ">
            AND t.material_name = #{e.materialName}
        </if>
        <if test="e.reportTime != null and e.reportTime != '' ">
            AND t.report_time = #{e.reportTime}
        </if>
        <if test="e.deviceId != null and e.deviceId != '' ">
            AND t.device_id = #{e.deviceId}
        </if>
        <if test="e.packageAttribute != null and e.packageAttribute != '' ">
            AND t.package_attribute = #{e.packageAttribute}
        </if>
    </sql>

</mapper>