{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader", "parameter": {"username": "sa", "password": "112233", "column": ["fldCounterId", "fldProductId", "fldBatchId", "fldSubBatchId", "fldTrashed", "fldValue", "fldPercent", "fldDescription"], "connection": [{"table": ["[dbPB (2ml-zuoke - ${workOrderName})].[dbo].[tbdCounters]"], "jdbcUrl": ["****************************************************** (${materialCode});socketTimeout=5000"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"topic": "cbetxdqrt8w", "bootstrapServers": "***************:9092", "batchSize": 200, "notTopicCreate": true, "topicNumPartition": 1, "topicReplicationFactor": 1, "writeSize": 1000, "productKey": "cbetxdqrt8w", "devName": "2ml-zuoke szc", "dataType": "mark", "fieldList": "fldCounterId,fldProductId,fldBatchId,fldSubBatchId,fldTrashed,fldValue,fldPercent,fldDescription"}}}]}}