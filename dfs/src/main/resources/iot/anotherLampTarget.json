{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader2005", "parameter": {"username": "sa", "password": "yelink123", "column": ["fldCounterId", "fldProductId", "fldBatchId", "fldSubBatchId", "fldTrashed", "fldValue", "fldPercent", "fldDescription"], "connection": [{"table": ["[dbPB (2ml-zuoke - ${workOrderName})].[dbo].[tbdCounters]"], "jdbcUrl": ["*************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"topic": "sqlServer-topic", "bootstrapServers": "************:9092", "batchSize": 200, "notTopicCreate": true, "topicNumPartition": 1, "topicReplicationFactor": 1, "writeSize": 100, "productKey": "sqlServer-topic", "devName": "2ml-zuoke szc", "dataType": "mark", "fieldList": "fldCounterId,fldProductId,fldBatchId,fldSubBatchId,fldTrashed,fldValue,fldPercent,fldDescription"}}}]}}