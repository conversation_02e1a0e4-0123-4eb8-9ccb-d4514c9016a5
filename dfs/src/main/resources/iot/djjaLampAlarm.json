{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader", "parameter": {"username": "sa", "password": "112233", "connection": [{"querySql": ["SELECT * FROM [dbPB (2ml-zuoke - ${workOrderName})].[dbo].[tbdAlarmsAndWarnings] where [fldDate] > dateadd(hour,-20000,GETDATE());"], "jdbcUrl": ["****************************************************** (${materialCode});socketTimeout=5000"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"topic": "cbetxdqrt8w", "bootstrapServers": "192.168.101.160:9092", "batchSize": 200, "notTopicCreate": true, "topicNumPartition": 1, "topicReplicationFactor": 1, "writeSize": 100, "productKey": "cbetxdqrt8w", "devName": "2ml-zuoke szc", "dataType": "alarm", "fieldList": "fldAlarmWarningId,fldProductId,fldBatchId,fldSubBatchId,fldTrashed,fldDate,fldUserId,fldUserName,fldComment,fldLayoutCode"}}}]}}