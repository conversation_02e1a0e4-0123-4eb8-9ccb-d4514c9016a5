{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader", "parameter": {"username": "yelink", "password": "yelink123", "connection": [{"querySql": ["SELECT TOP (1) [strBatch],[strProductName],[TT001_ACT],[TT002_ACT],[TT003_ACT],[g_RP100_PRD_SPEED],[g_EM_EM100_HEAT_IP_SP],[PT001_ACT],[PT002_ACT],[PT003_ACT],[PT004_ACT],[PT005_ACT],[RecordTime]  FROM [WashingTunnel].[dbo].[Tofflon_HisData_WM_History]    order by RecordTime desc;"], "jdbcUrl": ["************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"bootstrapServers": "************:9092", "notTopicCreate": true, "writeSize": 100, "dataType": "mark", "fieldList": "strBatch,str<PERSON>roduct<PERSON><PERSON>,TT001_ACT,TT002_ACT,TT003_ACT,g_RP100_PRD_SPEED,g_EM_EM100_HEAT_IP_SP,PT001_ACT,PT002_ACT,PT003_ACT,PT004_ACT,PT005_ACT,RecordTime"}}}]}}