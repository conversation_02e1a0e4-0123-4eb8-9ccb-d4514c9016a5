{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader", "parameter": {"username": "sa", "password": "yelink123", "connection": [{"querySql": ["SELECT TOP (100) [ALM_GROUP_NAME],[ALM_TRIGGER],[ALM_MSG],[ALM_FIRST_TIME],[ALM_LAST_TIME],[ALM_END_TIME],[ALM_FIRST_VALUE],[ALM_CURRENT_VALUE],[ALM_CONTINUOUS_TIME],[ALM_RELATED],[ALM_LEVEL_NAME],[ALM_LEVEL_NUM],[ALM_STATUS],[ALM_COMPUTER],[strBatch],[strProductName] FROM [Filling].[dbo].[Tofflon_Alarm_Notify_Table] WHERE [ALM_STATUS] = '发生';"], "jdbcUrl": ["***************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"bootstrapServers": "192.168.101.160:9092", "notTopicCreate": true, "writeSize": 1000, "dataType": "alarm", "fieldList": "alarmGroupName,alarmTrigger,alarmMsg,firstTime,lastTime,endTime,firstValue,currentValue,continuousTime,alarmRelated,alarmLevelName,alarmLevelNum,alarmStatus,alarmComputer,batchId,productName"}}}]}}