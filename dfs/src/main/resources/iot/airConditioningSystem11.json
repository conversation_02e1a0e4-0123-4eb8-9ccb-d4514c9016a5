{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "mysqlreader", "parameter": {"username": "root", "password": "yelink123", "connection": [{"querySql": ["select CreatTime as creatTime,P2 as dewPointTemperature11,P3 as freshAirTemperature11,P4 as airSupplyTemperature11,P7 as returnAirHumidity11,P8 as returnAirTemperature11 from t_datalog11_hour order by id desc limit 1;"], "jdbcUrl": ["******************************************************************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"bootstrapServers": "192.168.101.160:9092", "notTopicCreate": true, "writeSize": 1000, "dataType": "mark", "fieldList": "creatTime,dewPointTemperature11,freshAirTemperature11,airSupplyTemperature11,returnAirHumidity11,returnAirTemperature11"}}}]}}