{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader2005", "parameter": {"username": "sa", "password": "yelink123", "connection": [{"querySql": ["SELECT * FROM [dbPB (2ml-zuoke - ${workOrderName})].[dbo].[tbdAlarmsAndWarnings] where [fldDate] > dateadd(hour,-20000,GETDATE());"], "jdbcUrl": ["*************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"topic": "sqlServer-topic", "bootstrapServers": "172.16.1.121:9092", "batchSize": 200, "notTopicCreate": true, "topicNumPartition": 1, "topicReplicationFactor": 1, "writeSize": 2000, "productKey": "sqlServer-topic", "devName": "2ml-zuoke szc", "dataType": "alarm", "fieldList": "fldAlarmWarningId,fldProductId,fldBatchId,fldSubBatchId,fldTrashed,fldDate,fldUserId,fldUserName,fldComment,fldLayoutCode"}}}]}}