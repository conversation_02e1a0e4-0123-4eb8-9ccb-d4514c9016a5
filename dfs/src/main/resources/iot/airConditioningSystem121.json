{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "mysqlreader", "parameter": {"username": "root", "password": "yelink123", "connection": [{"querySql": ["select CreatTime as creatTime,P2 as dewPointTemperature,P3 as freshAirTemperature,P4 as airSupplyHumidity,P5 as airSupplyTemperature,P7 as returnAirHumidity,P8 as returnAirTemperature,p9 as surfaceCoolingTemperature from t_datalog121_hour order by id desc limit 1;"], "jdbcUrl": ["******************************************************************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"bootstrapServers": "192.168.101.160:9092", "notTopicCreate": true, "writeSize": 1000, "dataType": "mark", "fieldList": "creatTime,dewPointTemperature121,freshAirTemperature121,airSupplyHumidity121,airSupplyTemperature121,returnAirHumidity121,returnAirTemperature121,surfaceCoolingTemperature121"}}}]}}