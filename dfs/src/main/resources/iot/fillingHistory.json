{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader", "parameter": {"username": "sa", "password": "yelink123", "connection": [{"querySql": ["SELECT TOP (1) [strBatch] ,[strProductName],[g_Number_Sum] ,[g_Number_Good],[PT003_particle_ACT_R] ,[PT004_particle_ACT_R],[RecordTime] FROM [Filling].[dbo].[<PERSON><PERSON><PERSON>_HisData_FM_History] where RecordTime  > dateadd(minute,-29000,GETDATE());"], "jdbcUrl": ["***************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"bootstrapServers": "192.168.101.160:9092", "notTopicCreate": true, "writeSize": 1000, "dataType": "index", "fieldList": "batchId,productName,numberSum,numberGood,pt003ParticleActR,pt004ParticleActR,recordTime"}}}]}}