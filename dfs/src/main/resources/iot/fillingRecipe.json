{"job": {"setting": {"speed": {"byte": 1048576}}, "content": [{"reader": {"name": "sqlserverreader", "parameter": {"username": "sa", "password": "yelink123", "connection": [{"querySql": ["SELECT TOP (1) [strBatch],[g_RP100_PRD_SPEED],[g_RP100_Liquid_Density],[g_RP100_DOSE_VOLUME],[g_GP100_Diameter],[g_RP100_DOSE_REVOLUME],[GI100_OUTFEED_BELT],[GI100_INFEED_BELT],[GI100_REJECT_BELT],[GI100_TURNTABLE],[RecordTime] FROM [Filling].[dbo].[Tofflon_HisData_FM_Recipe] order by RecordTime desc"], "jdbcUrl": ["***************************************************************************"]}]}}, "writer": {"name": "kafkawriter", "parameter": {"bootstrapServers": "***************:9092", "notTopicCreate": true, "writeSize": 1000, "dataType": "index", "fieldList": "batchId,productSpeed,liquidDensity,doseVolume,diameter,doseRevolume,outfeedBelt,infeedBelt,rejectBelt,turnTable,recordTime"}}}]}}