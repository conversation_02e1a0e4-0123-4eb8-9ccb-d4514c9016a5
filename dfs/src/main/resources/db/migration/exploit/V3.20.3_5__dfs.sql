-- 解决由于物料导入更新时未填入分子分母导致分子分母数据为空的问题
update dfs_material set `unit_denominator` = 1 WHERE `unit_denominator` IS NULL;
update dfs_material set `unit_numerator` = 1 WHERE `unit_numerator` IS NULL;
update dfs_material set `scale_factor` = 1 WHERE `scale_factor` IS NULL;

-- 由于编辑物料的字段配置会影响到其他状态的同字段配置，这里需要初始化物料的字段配置
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'code';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'typeName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'sortName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'comp';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'createByNickname';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'editorName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'createTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'stateName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'updateTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'version';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'level';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'skuName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'isAuxiliaryMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'isBatchMag';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'isSupportCodeManage';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'code';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'type';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'sort';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'state';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'comp';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'level';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'version';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'isBatchMag';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'isSupportCodeManage';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'isAuxiliaryMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'isDoubleUnit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'editor';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'createByNickname';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'createTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'updateByName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'updateTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1  WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'unit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'qualityLevel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'code';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'type';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'sort';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'state';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'comp';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'level';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'version';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'isBatchMag';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'isSupportCodeManage';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'isAuxiliaryMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'isDoubleUnit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'editor';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'createByNickname';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'createTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'updateByName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'updateTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1  WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'unit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'qualityLevel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'code';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'type';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'sort';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'state';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'comp';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'level';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'version';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'isBatchMag';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'isSupportCodeManage';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'isAuxiliaryMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'isDoubleUnit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'editor';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'createByNickname';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'createTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'updateByName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'updateTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'unit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'qualityLevel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'code';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'type';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'sort';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'state';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'comp';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'level';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'version';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'isBatchMag';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'isSupportCodeManage';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'isAuxiliaryMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'isDoubleUnit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'editor';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'createByNickname';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'createTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'updateByName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'updateTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'unit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'qualityLevel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'code';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'typeName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'sortName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'state';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'comp';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'level';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'version';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'isBatchMag';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'isSupportCodeManage';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'isAuxiliaryMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'isDoubleUnit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'editorName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'createByNickname';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'createTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'updateByName';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'updateTime';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'unit';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'qualityLevel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'haveBom';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'haveCraft';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'unitNumerator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'unitDenominator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'unitNumerator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'unitDenominator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'unitNumerator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'unitDenominator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'unitNumerator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'unitDenominator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'unitNumerator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'unitDenominator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'unitNumerator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'unitDenominator';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'standard';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'remark';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'drawingNumber';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'materialPrice';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'loseRate';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'rawMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'nameEnglish';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'minimumProductionLot';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.list' AND `field_code` = 'factoryModel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'standard';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'remark';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'drawingNumber';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'materialPrice';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'loseRate';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'rawMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'nameEnglish';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'minimumProductionLot';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByCreate' AND `field_code` = 'factoryModel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'standard';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'remark';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'drawingNumber';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'materialPrice';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'loseRate';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'rawMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'nameEnglish';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'minimumProductionLot';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByRelease' AND `field_code` = 'factoryModel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'standard';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'remark';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'drawingNumber';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'materialPrice';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'loseRate';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'rawMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'nameEnglish';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'minimumProductionLot';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByStopUsing' AND `field_code` = 'factoryModel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'standard';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'remark';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'drawingNumber';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'materialPrice';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'loseRate';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'rawMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'nameEnglish';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'minimumProductionLot';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.editByAbandon' AND `field_code` = 'factoryModel';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'standard';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'remark';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'drawingNumber';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'materialPrice';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'loseRate';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'rawMaterial';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'nameEnglish';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'minimumProductionLot';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 1 WHERE `route` = '/product-management/supplies' AND `full_path_code` = 'material.detail' AND `field_code` = 'factoryModel';
