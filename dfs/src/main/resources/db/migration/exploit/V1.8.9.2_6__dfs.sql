set autocommit = 0;

CREATE TABLE  IF NOT EXISTS `dfs_report_line` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) DEFAULT NULL COMMENT 'investment-投入 hangup-挂起 report-报工 finish-完工',
  `line_id` int(11) DEFAULT NULL COMMENT '产线id',
  `line_name` varchar(50) DEFAULT NULL COMMENT '产线名称',
  `work_order` varchar(50) DEFAULT NULL COMMENT '工单号',
  `finish_count` int(11) DEFAULT NULL COMMENT '完成数量',
  `unqualified` int(11) DEFAULT NULL COMMENT '不合格数量',
  `user_name` varchar(50) DEFAULT NULL COMMENT '账号',
  `user_nickname` varchar(50) DEFAULT NULL COMMENT '上报人姓名',
  `batch` varchar(50) DEFAULT NULL COMMENT '批次',
  `shift_id` int(11) DEFAULT NULL COMMENT '班次id',
  `shift_type` varchar(50) DEFAULT NULL COMMENT '班次名称',
  `auto_count_record_time` datetime DEFAULT NULL COMMENT '记录显示采集器采集数量的时间点',
  `auto_count` int(11) DEFAULT NULL COMMENT '采集器采集数量(上个采集时间点到当前时间)',
  `effective_hours` double(20,1) DEFAULT NULL COMMENT '有效工时',
  `report_count_id` int(11) DEFAULT NULL COMMENT 'dfs_report_count表主键',
  `report_date` date DEFAULT NULL COMMENT '上报时间 ',
  `create_time` datetime DEFAULT NULL COMMENT '上报时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `report_date` (`report_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='产线数量报工表';


INSERT INTO `dfs_report_line`(`report_count_id`, `type`, `line_id`, `line_name`, `work_order`, `finish_count`, `unqualified`, `user_name`, `user_nickname`, `batch`, `shift_id`, `shift_type`, `auto_count_record_time`, `auto_count`, `effective_hours`, `report_date`, `create_time`, `update_time`)
(
SELECT `id`, `type`, `line_id`, `line_name`, `work_order`, `finish_count`, `unqualified`, `user_name`, `user_nickname`, `batch`, `shift_id`, `shift_type`, `auto_count_record_time`, `auto_count`, `effective_hours`, `report_date`, `create_time`, `update_time` from dfs_report_count where `type`='report'
);

commit






