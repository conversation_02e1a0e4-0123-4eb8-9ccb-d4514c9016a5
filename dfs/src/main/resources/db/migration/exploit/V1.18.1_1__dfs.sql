set autocommit = 0;

-- DDL

-- 新增工位维修时长表
CREATE TABLE IF NOT EXISTS `dfs_record_device_maintain` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `device_id` int(11) NOT NULL COMMENT '设备id',
  `type_code` int(11) DEFAULT NULL COMMENT '设备类型',
  `maintain` int(11) NOT NULL COMMENT '工位维修时长（分钟）',
  `time` datetime NOT NULL COMMENT '记录时间',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`) USING BTREE,
  KEY `time` (`time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工位维修时长表';

-- 设备每天按时段能耗表
CREATE TABLE IF NOT EXISTS `dfs_device_time_period_energy_consumption` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `device_id` int(11) NOT NULL COMMENT '设备id',
   `device_code` varchar(255) NOT NULL COMMENT '设备编号',
   `eui` varchar(50) DEFAULT NULL COMMENT '传感器eui',
   `consumption` decimal(16,3) NOT NULL COMMENT '能耗,kwh',
   `name_type` tinyint(2) NOT NULL COMMENT '时段名称类型，尖0、峰1、平2、谷3',
   `record_date` datetime NOT NULL COMMENT '日期',
   PRIMARY KEY (`id`),
   UNIQUE KEY `unique_device_type_date` (`device_id`,`name_type`,`record_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备每天按时段能耗表';

-- 时间段表
CREATE TABLE IF NOT EXISTS `dfs_energy_time_interval` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `start_time` datetime NOT NULL COMMENT '开始时间，作为生效开始时间yyyy-MM-dd或时段配置开始时间HH:mm:ss',
    `end_time` datetime NOT NULL COMMENT '结束时间，作为生效结束时间或时段配置结束时间',
    `relate_id` int(11) NOT NULL COMMENT '关联id，关联dfs_energy_time_period_config或dfs_energy_time_period表',
    `relate_type` varchar(255) NOT NULL COMMENT '关联id类型，生效时间EFFECTIVE_TIME或时段TIME_PERIOD',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='时间段表';

-- 用电时段表
CREATE TABLE IF NOT EXISTS `dfs_energy_time_period` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name_type` tinyint(2) NOT NULL COMMENT '时段名称类型，尖0、峰1、平2、谷3',
    `config_id` int(11) NOT NULL COMMENT '关联dfs_energy_time_period_config表id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_configId_type` (`name_type`,`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用电时段表';

-- 用电时段配置表
CREATE TABLE IF NOT EXISTS `dfs_energy_time_period_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `description` varchar(255) NOT NULL COMMENT '用电时段配置说明',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(255) NOT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用电时段配置表';

-- 新增单据操作记录表
CREATE TABLE IF NOT EXISTS `dfs_order_change_log`
(
    `id`      int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `order_number` varchar(255) NOT NULL COMMENT '单据编号',
    `model` varchar(255) NOT NULL COMMENT '操作模块',
    `title` varchar(255) DEFAULT NULL COMMENT '标题',
    `des` varchar(255) DEFAULT NULL COMMENT '操作描述',
    `username` varchar(255) DEFAULT NULL COMMENT '操作人账号',
    `nickname` varchar(255) DEFAULT NULL COMMENT '操作人姓名',
    `create_time` datetime DEFAULT NULL COMMENT '操作日期',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `order_number`(`order_number`) USING BTREE,
    INDEX `model`(`model`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='单据操作记录表';


-- 设备状态表增加维修状态
call proc_add_column(
        'dfs_record_device_state',
        'maintenance_state',
        'ALTER TABLE `dfs_record_device_state` ADD COLUMN `maintenance_state`  tinyint(4) NOT NULL DEFAULT 0 COMMENT ''维修状态：是（有生效的维修工单），否（没有生效的维修工单）'' AFTER `time`');

call proc_add_column(
        'dfs_work_order_procedure_relation',
        'work_order_id',
        'ALTER TABLE `dfs_work_order_procedure_relation` ADD COLUMN `work_order_id` int(11) NULL COMMENT ''工单ID'' AFTER `id`');

call proc_add_column(
        'dfs_record_work_order_count',
        'maintain_quantity',
        'ALTER TABLE `dfs_record_work_order_count` ADD COLUMN `maintain_quantity` int(10) DEFAULT 0 COMMENT ''维修数量''');


-- 工单新增报工系数字段
call proc_add_column(
'dfs_work_order',
'coefficient',
'ALTER TABLE `dfs_work_order` ADD COLUMN `coefficient` double(11,2) DEFAULT 1.00 COMMENT ''报工系数''');

call proc_add_column(
        'dfs_procedure_controller_config',
        'standard_circulation_duration',
        'ALTER TABLE `dfs_procedure_controller_config` ADD COLUMN `standard_circulation_duration` double(20,2) DEFAULT ''0.00'' COMMENT ''标准流转时长'' AFTER `increase_production_volume`');

-- 角色新增是否全选产线字段 默认为否
call proc_add_column(
'sys_roles',
'all_line',
'ALTER TABLE `sys_roles` ADD COLUMN `all_line` TINYINT DEFAULT 0 COMMENT ''是否全选产线 1-是 0-否''');

-- 检验项目表新增md5字段
call proc_add_column(
        'dfs_inspection_item',
        'md5_code',
        'ALTER TABLE `dfs_inspection_item` ADD COLUMN `md5_code` varchar(255) NULL COMMENT ''MD5值'' AFTER `is_export`');

-- 检验方案与检验项目关联表新增数值字段
call proc_add_column(
        'dfs_quality_inspection_item_group',
        'item_comparator',
        'ALTER TABLE `dfs_quality_inspection_item_group` ADD COLUMN `item_comparator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''比较符'' AFTER `init_value`');
-- 检验方案与检验项目关联表新增数值字段
call proc_add_column(
        'dfs_quality_inspection_item_group',
        'item_reference_value',
        'ALTER TABLE `dfs_quality_inspection_item_group` ADD COLUMN `item_reference_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''标准/参考值'' AFTER `item_comparator`');
-- 检验方案与检验项目关联表新增数值字段
call proc_add_column(
        'dfs_quality_inspection_item_group',
        'item_upper_limit_value',
        'ALTER TABLE `dfs_quality_inspection_item_group` ADD COLUMN `item_upper_limit_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''上限值'' AFTER `item_reference_value`');
-- 检验方案与检验项目关联表新增数值字段
call proc_add_column(
        'dfs_quality_inspection_item_group',
        'item_down_limit_value',
        'ALTER TABLE `dfs_quality_inspection_item_group` ADD COLUMN `item_down_limit_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''下限值'' AFTER `item_upper_limit_value`');

-- 产线新增 `是否自动完工`字段
call proc_add_column(
        'dfs_production_line',
        'is_auto_completed',
        'ALTER TABLE `dfs_production_line` ADD COLUMN `is_auto_completed` TINYINT DEFAULT 0 COMMENT ''是否自动完工 1-是 0-否''');

-- 工单每日产量记录表
call proc_modify_column(
        'dfs_record_work_order_day_count',
        'input',
        'ALTER TABLE `dfs_record_work_order_day_count`  MODIFY COLUMN `input` double(20, 2) DEFAULT 0 COMMENT ''投入数''');

-- 计数器占用时间表
CREATE TABLE IF NOT EXISTS `dfs_eui_occupy` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `eui` varchar(255) DEFAULT NULL COMMENT 'eui',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `line_id` int(11) DEFAULT NULL COMMENT '产线id',
  `line_code` varchar(255) DEFAULT NULL COMMENT '产线编码',
  `line_name` varchar(255) DEFAULT NULL COMMENT '产线名称',
  `number` varchar(255) DEFAULT NULL COMMENT '工单号',
  `type` varchar(255) DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`id`),
  KEY `eui` (`eui`) USING BTREE,
  KEY `number` (`number`),
  KEY `type` (`type`),
  KEY `lineId` (`line_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COMMENT='计数器占用时间表';

CREATE TABLE IF NOT EXISTS `dfs_report_day_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `line_id` int(11) DEFAULT NULL COMMENT '产线id',
  `line_name` varchar(255) DEFAULT NULL COMMENT '产线名称',
  `work_order` varchar(255) DEFAULT NULL COMMENT '工单号',
  `auto_count` double(11,3) DEFAULT '0.000' COMMENT '自动计数参考值数量(默认为0)',
  `finish_count` double(11,3) DEFAULT '0.000' COMMENT '实际数量(默认为0)',
  `report_date` date DEFAULT NULL COMMENT '归属日期',
  `report_line_id` int(11) DEFAULT NULL COMMENT '报工id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `work_order` (`work_order`)
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=utf8mb4 COMMENT='每日报工记录表';

-- 生产订单修改字段备注
call proc_modify_column(
        'dfs_order',
        'planned_delivery_date',
        'ALTER TABLE `dfs_order`  MODIFY COLUMN `planned_delivery_date` datetime DEFAULT NULL COMMENT ''计划开始生产时间''');
call proc_modify_column(
        'dfs_order',
        'end_date',
        'ALTER TABLE `dfs_order`  MODIFY COLUMN `end_date` datetime DEFAULT NULL COMMENT ''计划结束时间''');
call proc_modify_column(
        'dfs_order',
        'start_date',
        'ALTER TABLE `dfs_order`  MODIFY COLUMN `start_date` datetime DEFAULT NULL COMMENT ''该字段无用，请勿当作计划开始时间''');

call proc_modify_column(
        'dfs_work_order',
        'is_pid',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `is_pid` TINYINT DEFAULT 0 COMMENT ''是否有父工单 1是 0否''');

call proc_modify_column(
        'dfs_work_order',
        'time_difference',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `time_difference` double(20,2) DEFAULT NULL COMMENT ''时差''');

call proc_modify_column(
        'dfs_work_order',
        'planned_working_hours',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `planned_working_hours` double(20,2) DEFAULT NULL COMMENT ''计划工时''');

call proc_modify_column(
        'dfs_work_order',
        'actual_working_hours',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `actual_working_hours` double(20,2) DEFAULT NULL COMMENT ''实际工时，生产时长''');

call proc_modify_column(
        'dfs_work_order',
        'effective_hours',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `effective_hours` double(20,2) DEFAULT NULL COMMENT ''有效工时''');

call proc_modify_column(
        'dfs_purchase_receipt_material',
        'materiel_code',
        'ALTER TABLE `dfs_purchase_receipt_material` MODIFY COLUMN `materiel_code` varchar(255) DEFAULT NULL COMMENT ''物料编号''');

call proc_modify_column(
        'dfs_purchase_receipt_material',
        'materiel_name',
        'ALTER TABLE `dfs_purchase_receipt_material` MODIFY COLUMN `materiel_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call proc_modify_column(
        'dfs_stock_material_detail',
        'product_code',
        'ALTER TABLE `dfs_stock_material_detail` MODIFY COLUMN `product_code` varchar(255) DEFAULT NULL COMMENT ''物料编号''');

call proc_modify_column(
        'dfs_stock_material_detail',
        'product_name',
        'ALTER TABLE `dfs_stock_material_detail` MODIFY COLUMN `product_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call proc_modify_column(
        'dfs_stock_transfer_materiel',
        'materiel_code',
        'ALTER TABLE `dfs_stock_transfer_materiel` MODIFY COLUMN `materiel_code` varchar(255) DEFAULT NULL COMMENT ''物料编号''');

call proc_modify_column(
        'dfs_stock_transfer_materiel',
        'materiel_name',
        'ALTER TABLE `dfs_stock_transfer_materiel` MODIFY COLUMN `materiel_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call proc_modify_column(
        'dfs_stock_inventory_record',
        'material_code',
        'ALTER TABLE `dfs_stock_inventory_record` MODIFY COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编号''');

call proc_modify_column(
        'dfs_stock_inventory_record',
        'material_name',
        'ALTER TABLE `dfs_stock_inventory_record` MODIFY COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call proc_modify_column(
        'dfs_stock_delivery_material',
        'materiel_code',
        'ALTER TABLE `dfs_stock_delivery_material` MODIFY COLUMN `materiel_code` varchar(255) DEFAULT NULL COMMENT ''物料编号''');

call proc_modify_column(
        'dfs_stock_delivery_material',
        'materiel_name',
        'ALTER TABLE `dfs_stock_delivery_material` MODIFY COLUMN `materiel_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

call proc_modify_column(
        'dfs_stock_check_materiel',
        'material_code',
        'ALTER TABLE `dfs_stock_check_materiel` MODIFY COLUMN `material_code` varchar(255) DEFAULT NULL COMMENT ''物料编号''');

call proc_modify_column(
        'dfs_stock_check_materiel',
        'material_name',
        'ALTER TABLE `dfs_stock_check_materiel` MODIFY COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称''');

-- DML
-- 添加编码规则
INSERT INTO `dfs_config_rule`( `rule_type`, `rule_type_name`) VALUES ( 15, '产品检测-送检单编号');

-- 兼容历史数据--工单工序表
UPDATE dfs_work_order_procedure_relation a, dfs_work_order b
SET a.work_order_id = b.work_order_id
WHERE a.work_order_number = b.work_order_number;

-- 兼容历史数据--检验方案与检验项目关联表
UPDATE dfs_quality_inspection_item_group a, dfs_inspection_item b
SET a.item_comparator = b.comparator,
    a.item_reference_value = b.reference_value,
    a.item_upper_limit_value = b.upper_limit_value,
    a.item_down_limit_value = b.down_limit_value
WHERE a.inspection_item_id = b.inspection_item_id;

-- 兼容历史数据--工单表的`是否为父工单` 如果为null,则默认置0
UPDATE dfs_work_order
SET is_pid = 0
WHERE is_pid is null;

-- 新增权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11001', '能源信息', '/energy-consumption-management/energy-information', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '110', 1, 1, 0, '/energy-consumption-management', 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11002', '用电时段管理', '/energy-consumption-management/time-period-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '110', 1, 1, 0, '/energy-consumption-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11002010', '创建', 'time.period:create', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '11002', 2, 1, 1, '/energy-consumption-management/time-period-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11002020', '生效时间编辑', 'time.period:edit1', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '11002', 2, 1, 1, '/energy-consumption-management/time-period-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11002030', '时段编辑', 'time.period:edit2', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '11002', 2, 1, 1, '/energy-consumption-management/time-period-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11002040', '删除', 'time.period:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '11002', 2, 1, 1, '/energy-consumption-management/time-period-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES ('1090102060', '订单物料附件查看', 'sales.order:drawing', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1090102', 2, 1, 0, 1, '/supply-chain-collaboration/order-model/salesOrder');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES ('1090102090', '导入模板上传', 'sales.order:saleOrderImportTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, 1, '/supply-chain-collaboration/order-model/salesOrder');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES ('1090102100', '数据导入', 'sales.order:saleOrderImportData', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, 1, '/supply-chain-collaboration/order-model/salesOrder');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES ('1090102110', '查看导入日志', 'sales.order:saleOrderImportHistoryData', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, 1, '/supply-chain-collaboration/order-model/salesOrder');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES ('1090102120', '导入模板下载', 'sales.order:saleOrderImportTemplateExport', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, 1, '/supply-chain-collaboration/order-model/salesOrder');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES ('1090102130', '日志记录', 'sales.order:saleOrderLogExport', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, 1, '/supply-chain-collaboration/order-model/salesOrder');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES ('1090102140', '下推', 'sales.order:pushDown', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1090102', 2, 1, 0, 1, '/supply-chain-collaboration/order-model/salesOrder');

-- 新增权限给admin 赋权
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES
(1, '1090102060'),(1, '1090102090'),( 1, '1090102100'),(1, '1090102110'),(1, '1090102120'),(1, '1090102130'),(1, '1090102140');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES
 (1, '11001'),(1, '11002'),( 1, '11002010'),(1, '11002020'),(1, '11002030'),(1, '11002040');

-- 更新小程序权限id
truncate table sys_app_permission_info;

INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('aging_management_app', '老化管理', '/aging-management', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('alarm-work', '告警作业', '/alarm-operation', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('batch-scan', '工单批次', '/repair-order-batch', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('com.hl.peiliao', '配料管理', '/recipe-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('com.yelink.industrial_cube', '工业魔方管理', '/industrial-cube-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('component-testing-station-machine', '成分化验工位机', '/composition-test-machine', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('downMaterialInStation', '入库配送', '/warehousing-distribution', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('dy.test', 'token测试', '/token-test', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('electric-app-one', '电炉工位机', '/electric-station', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('furnance-app-one', '精炼炉工位机', '/bloomery-station-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('jun-lan', 'SMT上料防错', '/SMT-hang-up-prevention', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('location-assignment-copy', '工位作业', '/work-station', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('mation', '机台工位机', '/table-machine', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('mobile-transparency-app', '移动透明', '/mobile-transparent', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('msc-app', '渊联视频云', '/yelink-video', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomework', '生产作业', '/production-operation', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton1', '派工', 'operation:dispatch', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton10', '作业工单-修改', 'operation:amend', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton2', '投产/挂起', 'operation:product:hang', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton3', '报工', 'operation:report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton4', '开工', 'operation:start', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton5', '完工', 'operation:all:finish', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton6', '作业工单完成', 'operation:finish', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton7', '工单完成', 'operation:workorder:finish', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton8', '流转', 'operation:flow', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton9', '报工记录-修改', 'operation:record:amend', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('screen-management', '数字运营中心', '/operat-center-screen', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink-alarmHandle', '设备告警处理', '/handleAlarm', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.batch.management.com', '批次管理', '/batch-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.inventoryQuery.com', '库存查询', '/stock-inquiry', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.aging.machine', '老化工位机', '/burn-in-station-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.general.machine', '一般工位机', '/ordinary-station-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.intermediateInspection.machine', '中间检测工位机', '/median-detection-station-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.maintenance.machine', '维修工位机', '/maintenance-station-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.packaging.machine', '包装工位机', '/packaging-station-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.pressure.machine', '压力工位机', '/pressure-station-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.scanInput.machine', '扫码投入工位机', '/sweep-code-into-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.label.print', '标签打印', '/bar-print', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.report', '产线报工', '/production-line-report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton1', '产线列表更多', 'report:line:more', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton10', '开工', 'report:work:start', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton11', '完工', 'report:work:complete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton12', '查看文件', 'report:view:file', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton13', '修改计划数', 'report:plan:modify', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton2', '工单列表更多', 'report:workorder:more', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton3', '工单列表追溯码打印', 'report:workorder:print', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton4', '开始投产', 'report:production:start', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton5', '工位列表', 'report:work:list', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton6', '编辑报工记录', 'report:work:record', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton7', '报工', 'report:work:submit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton8', '生产完成', 'report:production:completion', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.line.reportbutton9', '挂起', 'report:work:stop', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.line.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.maintenance.app', '质检返工', '/maintenance-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.outboundApp.com', '扫码出库', '/warehouse-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.putStorageShelves.com', '入库上架', '/putaway', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.storageApp.com', '扫码入库', '/stock-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.workorder.report', '订单报工', '/order-report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.workorder.reportbutton1', '投产/挂起', 'order:report:product:hang', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.workorder.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.workorder.reportbutton3', '报工', 'order:report:report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.workorder.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.workorder.reportbutton4', '完工', 'order:report:finish', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.workorder.report', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.workorder.reportbutton5', '报工记录-修改', 'order:report:amend', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.workorder.report', 2, 0, 1, NULL, 1);


-- 新增送检单小程序权限
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.inspection.sheet', '送检单小程序', '/inspection-sheet', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.inspection.sheet.button1', '新增', 'product.inspection.sheet:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', 'yelink.inspection.sheet', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.inspection.sheet.button2', '编辑', 'product.inspection.sheet:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', 'yelink.inspection.sheet', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.inspection.sheet.button3', '删除', 'product.inspection.sheet:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', 'yelink.inspection.sheet', 2, 0, 1, NULL, 1);

-- 作业工单小程序新增设备界面按钮权限
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('productHomeworkbutton11', '连续生产任务-新增', 'operation:addContinuousProductionTask', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'productHomework', 2, 0, 1, NULL, 1);

-- 工单每日产量记录表
update dfs_record_work_order_day_count set input=0 where isnull(input);
commit;
