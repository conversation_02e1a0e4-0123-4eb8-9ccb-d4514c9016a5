set autocommit = 0;

CREATE TABLE IF NOT EXISTS `dfs_bom_tile` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `bom_code` varchar(255) DEFAULT NULL COMMENT '成品编码',
                                `material_code` varchar(255) DEFAULT NULL COMMENT '原料编码',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `dfs_config_reverse_material_detail` (
                                                      `id` int NOT NULL AUTO_INCREMENT,
                                                      `column_name` varchar(100) DEFAULT NULL COMMENT '列名',
                                                      `material_type_id` int DEFAULT NULL COMMENT '物料类型',
                                                      `material_name_match_rule` varchar(100) DEFAULT NULL COMMENT '物料名称规则',
                                                      `material_name_match_val` varchar(100) DEFAULT NULL COMMENT '物料名称值',
                                                      `material_standard_match_rule` varchar(100) DEFAULT NULL COMMENT '物料规格规则',
                                                      `material_standard_match_val` varchar(100) DEFAULT NULL COMMENT '物料规则名称',
                                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='倒排物料的详情表';


commit
