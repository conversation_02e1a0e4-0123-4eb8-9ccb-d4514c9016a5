-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到其他脚本=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到其他脚本=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到其他脚本=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到其他脚本=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到其他脚本=======================================================

-- 质量每日统计-按成品&不良
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'gid',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `gid` int DEFAULT NULL COMMENT ''车间id''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'gname',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `gname` varchar(255) DEFAULT NULL COMMENT ''车间名称 [维度]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'material_type_name',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `material_type_name` varchar(255) DEFAULT NULL COMMENT ''物料类型 [维度]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'material_sort_name',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `material_sort_name` varchar(255) DEFAULT NULL COMMENT ''物料分类''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'material_standard',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `material_standard` varchar(255) DEFAULT NULL COMMENT ''物料规格''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'material_drawing_number',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `material_drawing_number` varchar(255) DEFAULT NULL COMMENT ''物料图号''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'material_raw_material',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `material_raw_material` varchar(255) DEFAULT NULL COMMENT ''物料材质''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'material_sku_attr_name',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` ADD COLUMN `material_sku_attr_name` varchar(255) DEFAULT NULL COMMENT ''物料特征参数名称 [多个按,分隔]''');
-- 增加维度标识
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'dfs_metrics_quality_line_material_defect_daily'
  AND field_code in ('line_name', 'material_code', 'material_name', 'defectTypeName', 'defect_name');





-- 质量每日统计-按产线&物料不良
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_daily',
        'gid',
        'ALTER TABLE `dfs_metrics_quality_line_material_daily` ADD COLUMN `gid` int DEFAULT NULL COMMENT ''车间id''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_daily',
        'gname',
        'ALTER TABLE `dfs_metrics_quality_line_material_daily` ADD COLUMN `gname` varchar(255) DEFAULT NULL COMMENT ''车间名称 [维度]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_daily',
        'material_standard',
        'ALTER TABLE `dfs_metrics_quality_line_material_daily` ADD COLUMN `material_standard` varchar(255) DEFAULT NULL COMMENT ''物料规格''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_daily',
        'material_drawing_number',
        'ALTER TABLE `dfs_metrics_quality_line_material_daily` ADD COLUMN `material_drawing_number` varchar(255) DEFAULT NULL COMMENT ''物料图号''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_daily',
        'material_raw_material',
        'ALTER TABLE `dfs_metrics_quality_line_material_daily` ADD COLUMN `material_raw_material` varchar(255) DEFAULT NULL COMMENT ''物料材质''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_quality_line_material_daily',
        'material_sku_attr_name',
        'ALTER TABLE `dfs_metrics_quality_line_material_daily` ADD COLUMN `material_sku_attr_name` varchar(255) DEFAULT NULL COMMENT ''物料特征参数名称 [多个按,分隔]''');
-- 增加维度标识
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'dfs_metrics_quality_line_material_daily'
  AND field_code in ('line_name', 'material_code', 'material_name');


-- 质量每日统计-按订单&不良类型
-- 增加维度标识
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'dfs_metrics_quality_product_order_defect_daily'
  AND field_code in ('defect_type_name', 'material_name');


-- 改表名
update dfs_table_config set table_remark = '质量每日统计-按产线&物料不良' WHERE table_name = 'dfs_metrics_quality_line_material_daily';
update dfs_target_model set target_cnname = '质量每日统计-按产线&物料不良' WHERE target_name = 'qualityLineMaterialDaily';

update dfs_table_config set table_remark = '质量每日统计-按订单&不良类型' WHERE table_name = 'dfs_metrics_quality_product_order_defect_daily';
update dfs_target_model set target_cnname = '质量每日统计-按订单&不良类型' WHERE target_name = 'qualityProductOrderDefectDaily';