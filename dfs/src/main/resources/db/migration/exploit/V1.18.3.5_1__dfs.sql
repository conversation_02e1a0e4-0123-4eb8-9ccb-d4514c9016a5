set autocommit = 0;

-- DDL
call proc_add_column(
'dfs_record_device_run',
'batch',
'ALTER TABLE `dfs_record_device_run` ADD COLUMN `batch` varchar(255) NULL COMMENT ''批次号'' AFTER `running`');

call proc_add_column(
'dfs_record_device_run_5min',
'batch',
'ALTER TABLE `dfs_record_device_run_5min` ADD COLUMN `batch` varchar(255) NULL COMMENT ''批次号'' AFTER `running`');

call proc_add_column(
'dfs_record_device_run_20min',
'batch',
'ALTER TABLE `dfs_record_device_run_20min` ADD COLUMN `batch` varchar(255) NULL COMMENT ''批次号'' AFTER `running`');

call proc_add_column(
'dfs_record_device_run_1h',
'batch',
'ALTER TABLE `dfs_record_device_run_1h` ADD COLUMN `batch` varchar(255) NULL COMMENT ''批次号'' AFTER `running`');

CREATE TABLE IF NOT EXISTS `dfs_device_gas_constant_consumption` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `device_id` int(11) DEFAULT NULL COMMENT '设备id',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `eui` varchar(50) DEFAULT NULL COMMENT '传感器eui',
  `consumption` double(11,4) DEFAULT NULL COMMENT '能耗',
  `record_date` datetime DEFAULT NULL COMMENT '日期',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`),
  KEY `record_date` (`record_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='设备每天用气能耗实时表';

CREATE TABLE IF NOT EXISTS `dfs_device_gas_energy_consumption` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `device_id` int(11) DEFAULT NULL COMMENT '设备id',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `eui` varchar(50) DEFAULT NULL COMMENT '传感器eui',
  `consumption` double(11,4) DEFAULT NULL COMMENT '能耗',
  `record_date` datetime DEFAULT NULL COMMENT '日期',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`),
  KEY `record_date` (`record_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='设备每天用气能耗表';

-- 条码表新增扩展字段
call proc_add_column(
        'dfs_bar_code',
        'extend_fields',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `extend_fields`  mediumtext COMMENT ''扩展字段''');


-- 条码表新增扩展字段
call proc_add_column(
        'dfs_bar_code',
        'grade',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `grade`  varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT ''等级''');

call proc_modify_column(
        'dfs_report_count',
        'finish_count',
        'ALTER TABLE `dfs_report_count` MODIFY COLUMN `finish_count` double(11, 2) NULL DEFAULT NULL COMMENT ''完成数量'' AFTER `work_order`');
call proc_modify_column(
        'dfs_report_line',
        'finish_count',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `finish_count` double(11, 2) NULL DEFAULT NULL COMMENT ''完成数量'' AFTER `work_order`');
call proc_modify_column(
        'dfs_work_order',
        'finish_count',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `finish_count` double(11, 2) NULL DEFAULT 0.00 COMMENT ''已完成数量'' AFTER `priority`');
call proc_modify_column(
        'dfs_work_order',
        'unqualified',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `unqualified` double(11, 2) NULL DEFAULT 0 COMMENT ''不合格量'' AFTER `notice_username`');
call proc_modify_column(
        'dfs_record_work_order_day_count',
        'count',
        'ALTER TABLE `dfs_record_work_order_day_count` MODIFY COLUMN `count` double(11, 2) NULL DEFAULT 0.000 COMMENT ''已检查数量'' AFTER `work_order_number`');
call proc_modify_column(
        'dfs_record_work_order_day_count',
        'unqualified',
        'ALTER TABLE `dfs_record_work_order_day_count` MODIFY COLUMN `unqualified` double(11, 2) NULL DEFAULT 0.000 COMMENT ''不合格数量'' AFTER `count`');
call proc_modify_column(
        'dfs_record_output',
        'planQuantity',
        'ALTER TABLE `dfs_record_output` MODIFY COLUMN `planQuantity` double(11, 2) NULL DEFAULT NULL COMMENT ''计划数量'' AFTER `id`');
call proc_modify_column(
        'dfs_record_output',
        'completed',
        'ALTER TABLE `dfs_record_output` MODIFY COLUMN `completed` double(11, 2) NULL DEFAULT NULL COMMENT ''完成数量'' AFTER `planQuantity`');
call proc_modify_column(
        'dfs_record_output',
        'order_plan_quantity',
        'ALTER TABLE `dfs_record_output` MODIFY COLUMN `order_plan_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''工单计划数量'' AFTER `time`');
call proc_modify_column(
        'dfs_record_output',
        'order_completed',
        'ALTER TABLE `dfs_record_output` MODIFY COLUMN `order_completed` double(11, 2) NULL DEFAULT NULL COMMENT ''工单完成数量'' AFTER `order_plan_quantity`');
call proc_modify_column(
        'dfs_report_day_count',
        'finish_count',
        'ALTER TABLE `dfs_report_day_count` MODIFY COLUMN `finish_count` double(11, 2) NULL DEFAULT 0.000 COMMENT ''实际数量(默认为0)'' AFTER `auto_count`');

-- 修改库存字段长度
call proc_modify_column(
        'dfs_stock_check_materiel',
        'amount_before_check',
        'ALTER TABLE `dfs_stock_check_materiel` MODIFY COLUMN `amount_before_check` double(20, 3) NULL DEFAULT NULL COMMENT ''盘点前库存''');
call proc_modify_column(
        'dfs_stock_check_materiel',
        'amount_after_check',
        'ALTER TABLE `dfs_stock_check_materiel` MODIFY COLUMN `amount_after_check` double(20, 3) NULL DEFAULT NULL COMMENT ''盘点后库存''');
call proc_modify_column(
        'dfs_stock_check_materiel',
        'amount_of_change',
        'ALTER TABLE `dfs_stock_check_materiel` MODIFY COLUMN `amount_of_change` double(20, 3) NULL DEFAULT NULL COMMENT ''库存变化量''');
call proc_modify_column(
        'dfs_stock_inventory_detail',
        'stock_location_quantity',
        'ALTER TABLE `dfs_stock_inventory_detail` MODIFY COLUMN `stock_location_quantity` double(20, 4) NULL DEFAULT NULL COMMENT ''库位数量''');
call proc_modify_column(
        'dfs_stock_inventory_detail',
        'inventory_quantity',
        'ALTER TABLE `dfs_stock_inventory_detail` MODIFY COLUMN `inventory_quantity` double(20, 3) NULL DEFAULT NULL COMMENT ''物料库存''');
call proc_modify_column(
        'dfs_stock_inventory_record',
        'original_quantity',
        'ALTER TABLE `dfs_stock_inventory_record` MODIFY COLUMN `original_quantity` double(20, 3) NULL DEFAULT NULL COMMENT ''原库存''');
call proc_modify_column(
        'dfs_stock_inventory_record',
        'current_quantity',
        'ALTER TABLE `dfs_stock_inventory_record` MODIFY COLUMN `current_quantity` double(20, 3) NULL DEFAULT NULL COMMENT ''当前库存''');
call proc_modify_column(
        'dfs_stock_inventory_record',
        'change_amount',
        'ALTER TABLE `dfs_stock_inventory_record` MODIFY COLUMN `change_amount` double(20, 3) NULL DEFAULT NULL COMMENT ''变化量''');

-- 订单物料关联表 加索引
call proc_add_column_index(
        'dfs_order_material',
        'order_number',
        'order_number');

call proc_add_column_index(
        'dfs_order_material',
        'material_name',
        'material_name');

call proc_add_column_index(
        'dfs_order_material',
        'material_code',
        'material_code');

-- DML
UPDATE `dfs_target_model` SET  `method_cname` = '产线oee' WHERE `method_name` = 'lineOEE';
UPDATE `dfs_target_method` SET `method_cname` = '产线oee' WHERE `method_name` = 'lineOEE';

-- 大屏小程序权限
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('wisdom-screen-video', '监控大屏', '/wisdom-screen-video', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('wisdom-screen-line-group', '产线组大屏', '/wisdom-screen-line-group', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('wisdom-screen-line', '产线大屏', '/wisdom-screen-line', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('wisdom-screen-warehouse', '仓储大屏', '/wisdom-screen-warehouse', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('wisdom-screen-center', '中央大屏', '/wisdom-screen-center', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);

commit;
