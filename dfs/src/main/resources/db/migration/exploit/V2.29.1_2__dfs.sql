-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================

-- 采购需求单路由
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'purchaseRequestOrder', '采购需求', 'purchaseRequestOrder', '', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'purchaseRequestOrderList', '采购需求列表', 'purchaseRequestOrder.list', 'purchaseRequestOrder', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'purchaseRequestOrderAdd', '新增采购需求', 'purchaseRequestOrder.add', 'purchaseRequestOrder', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'purchaseRequestOrderEdit', '编辑采购需求', 'purchaseRequestOrder.edit', 'purchaseRequestOrder', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'purchaseRequestOrderEditByCreate', '创建态', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'purchaseRequestOrderEditByRelease', '生效态', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'purchaseRequestOrderEditByFinish', '完成态', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'purchaseRequestOrderEditByClosed', '关闭态', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'purchaseRequestOrderEditByCancel', '取消态', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'purchaseRequestOrderDetail', '采购需求详情', 'purchaseRequestOrder.detail', 'purchaseRequestOrder', NULL, 'admin', 'admin', '2024-01-25 11:07:04', '2024-01-25 11:07:04');

-- 采购需求单配置
call proc_add_form_field("purchaseRequestOrder.list", "requestNum", "采购需求单号");
call proc_add_form_field("purchaseRequestOrder.list", "orderTypeName", "单据类型");
call proc_add_form_field("purchaseRequestOrder.list", "stateName", "状态");
call proc_add_form_field("purchaseRequestOrder.list", "orderNumTypeName", "关联单据类型");
call proc_add_form_field("purchaseRequestOrder.list", "orderNum", "关联单据编号");
call proc_add_form_field("purchaseRequestOrder.list", "applicantName", "申请人");
call proc_add_form_field("purchaseRequestOrder.list", "applicationDate", "申请日期");
call proc_add_form_field("purchaseRequestOrder.list", "projectName", "项目备注");
call proc_add_form_field("purchaseRequestOrder.list", "baseInfoRemark", "备注");
call proc_add_form_field("purchaseRequestOrder.list", "code", "物料编码");
call proc_add_form_field("purchaseRequestOrder.list", "name", "物料名称");
call proc_add_form_field("purchaseRequestOrder.list", "comp", "单位");
call proc_add_form_field("purchaseRequestOrder.list", "quantity", "参考数量");
call proc_add_form_field("purchaseRequestOrder.list", "num", "申请数量");
call proc_add_form_field("purchaseRequestOrder.list", "purchaseDate", "要货日期");
call proc_add_form_field("purchaseRequestOrder.list", "suggestedSupplier", "建议供应商");
call proc_add_form_field("purchaseRequestOrder.list", "stockQuantity", "当前库存数量");
call proc_add_form_field("purchaseRequestOrder.list", "materialRemarks", "行备注");
call proc_add_form_field("purchaseRequestOrder.list", "purchaseQuantity", "采购数量");
call proc_add_form_field("purchaseRequestOrder.list", "receiptQuantity", "收货数量");
call proc_add_form_field("purchaseRequestOrder.list", "inventoryQuantity", "入库数量");
call proc_add_form_field("purchaseRequestOrder.list", "qualifiedQuantity", "来料合格数量");
call proc_add_form_field("purchaseRequestOrder.list", "unqualifiedQuantity", "来料不合格数量");
call proc_add_form_field("purchaseRequestOrder.list", "auxiliaryAttr", "特征参数");
call proc_add_form_field("purchaseRequestOrder.list", "projectDefineName", "项目名称");
call proc_add_form_field("purchaseRequestOrder.list", "contractName", "合同名称");
call proc_add_form_field("purchaseRequestOrder.list", "createByNickname", "创建人");
call proc_add_form_field("purchaseRequestOrder.list", "createTime", "创建时间");
call proc_add_form_field("purchaseRequestOrder.list", "updateByName", "更新人");
call proc_add_form_field("purchaseRequestOrder.list", "updateTime", "更新时间");

call proc_add_form_field("purchaseRequestOrder.add", "orderNumber", "采购需求单号");
call proc_add_form_field("purchaseRequestOrder.add", "orderType", "单据类型");
call proc_add_form_field("purchaseRequestOrder.add", "orderNumType", "关联单据类型");
call proc_add_form_field("purchaseRequestOrder.add", "orderId", "关联单据编号");
call proc_add_form_field("purchaseRequestOrder.add", "applicant", "申请人");
call proc_add_form_field("purchaseRequestOrder.add", "applicationDate", "申请日期");
call proc_add_form_field("purchaseRequestOrder.add", "contractId", "采购合同");
call proc_add_form_field("purchaseRequestOrder.add", "projectName", "项目备注");
call proc_add_form_field("purchaseRequestOrder.add", "remark", "备注");
call proc_add_form_field("purchaseRequestOrder.add", "projectDefineId", "项目名称");
call proc_add_form_field("purchaseRequestOrder.add", "code", "物料编码");
call proc_add_form_field("purchaseRequestOrder.add", "name", "物料名称");
call proc_add_form_field("purchaseRequestOrder.add", "comp", "单位");
call proc_add_form_field("purchaseRequestOrder.add", "quantity", "参考数量");
call proc_add_form_field("purchaseRequestOrder.add", "actualNum", "申请数量");
call proc_add_form_field("purchaseRequestOrder.add", "purchaseDate", "要货日期");
call proc_add_form_field("purchaseRequestOrder.add", "suggestedSupplier", "建议供应商");
call proc_add_form_field("purchaseRequestOrder.add", "stockQuantity", "当前库存数量");
call proc_add_form_field("purchaseRequestOrder.add", "materialRemarks", "行备注");
call proc_add_form_field("purchaseRequestOrder.add", "auxiliaryAttr", "特征参数");

call proc_add_form_field("purchaseRequestOrder.edit", "requestNum", "采购需求单号");
call proc_add_form_field("purchaseRequestOrder.edit", "orderType", "单据类型");
call proc_add_form_field("purchaseRequestOrder.edit", "state", "状态");
call proc_add_form_field("purchaseRequestOrder.edit", "orderNumType", "关联单据类型");
call proc_add_form_field("purchaseRequestOrder.edit", "orderId", "关联单据编号");
call proc_add_form_field("purchaseRequestOrder.edit", "applicant", "申请人");
call proc_add_form_field("purchaseRequestOrder.edit", "applicationDate", "申请日期");
call proc_add_form_field("purchaseRequestOrder.edit", "projectName", "项目备注");
call proc_add_form_field("purchaseRequestOrder.edit", "remark", "备注");
call proc_add_form_field("purchaseRequestOrder.edit", "projectDefineId", "项目名称");
call proc_add_form_field("purchaseRequestOrder.edit", "contractId", "采购合同");
call proc_add_form_field("purchaseRequestOrder.edit", "code", "物料编码");
call proc_add_form_field("purchaseRequestOrder.edit", "name", "物料名称");
call proc_add_form_field("purchaseRequestOrder.edit", "comp", "单位");
call proc_add_form_field("purchaseRequestOrder.edit", "num", "参考数量");
call proc_add_form_field("purchaseRequestOrder.edit", "actualNum", "申请数量");
call proc_add_form_field("purchaseRequestOrder.edit", "purchaseDate", "要货日期");
call proc_add_form_field("purchaseRequestOrder.edit", "suggestedSupplier", "建议供应商");
call proc_add_form_field("purchaseRequestOrder.edit", "stockQuantity", "当前库存数量");
call proc_add_form_field("purchaseRequestOrder.edit", "materialRemarks", "行备注");
call proc_add_form_field("purchaseRequestOrder.edit", "auxiliaryAttr", "特征参数");
call proc_add_form_field("purchaseRequestOrder.edit", "purchaseQuantity", "采购数量");
call proc_add_form_field("purchaseRequestOrder.edit", "receiptQuantity", "收货数量");
call proc_add_form_field("purchaseRequestOrder.edit", "inventoryQuantity", "入库数量");
call proc_add_form_field("purchaseRequestOrder.edit", "qualifiedQuantity", "来料合格数量");
call proc_add_form_field("purchaseRequestOrder.edit", "unqualifiedQuantity", "来料不合格数量");
call proc_add_form_field("purchaseRequestOrder.edit", "createByNickname", "创建人");
call proc_add_form_field("purchaseRequestOrder.edit", "createTime", "创建时间");
call proc_add_form_field("purchaseRequestOrder.edit", "updateByName", "更新人");
call proc_add_form_field("purchaseRequestOrder.edit", "updateTime", "更新时间");

call proc_add_form_field("purchaseRequestOrder.detail", "requestNum", "采购需求单号");
call proc_add_form_field("purchaseRequestOrder.detail", "orderTypeName", "单据类型");
call proc_add_form_field("purchaseRequestOrder.detail", "stateName", "状态");
call proc_add_form_field("purchaseRequestOrder.detail", "orderNumTypeName", "关联单据类型");
call proc_add_form_field("purchaseRequestOrder.detail", "orderNum", "关联单据编号");
call proc_add_form_field("purchaseRequestOrder.detail", "applicant", "申请人");
call proc_add_form_field("purchaseRequestOrder.detail", "applicationDate", "申请日期");
call proc_add_form_field("purchaseRequestOrder.detail", "projectName", "项目备注");
call proc_add_form_field("purchaseRequestOrder.detail", "remark", "备注");
call proc_add_form_field("purchaseRequestOrder.detail", "projectDefineName", "项目名称");
call proc_add_form_field("purchaseRequestOrder.detail", "contractName", "合同名称");
call proc_add_form_field("purchaseRequestOrder.detail", "code", "物料编码");
call proc_add_form_field("purchaseRequestOrder.detail", "name", "物料名称");
call proc_add_form_field("purchaseRequestOrder.detail", "comp", "单位");
call proc_add_form_field("purchaseRequestOrder.detail", "referenceQuantity", "参考数量");
call proc_add_form_field("purchaseRequestOrder.detail", "actualNum", "申请数量");
call proc_add_form_field("purchaseRequestOrder.detail", "purchaseDate", "要货日期");
call proc_add_form_field("purchaseRequestOrder.detail", "suggestedSupplier", "建议供应商");
call proc_add_form_field("purchaseRequestOrder.detail", "stockQuantity", "当前库存数量");
call proc_add_form_field("purchaseRequestOrder.detail", "materialRemarks", "行备注");
call proc_add_form_field("purchaseRequestOrder.detail", "auxiliaryAttr", "特征参数");
call proc_add_form_field("purchaseRequestOrder.detail", "purchaseQuantity", "采购数量");
call proc_add_form_field("purchaseRequestOrder.detail", "receiptQuantity", "收货数量");
call proc_add_form_field("purchaseRequestOrder.detail", "inventoryQuantity", "入库数量");
call proc_add_form_field("purchaseRequestOrder.detail", "qualifiedQuantity", "来料合格数量");
call proc_add_form_field("purchaseRequestOrder.detail", "unqualifiedQuantity", "来料不合格数量");
call proc_add_form_field("purchaseRequestOrder.detail", "createByNickname", "创建人");
call proc_add_form_field("purchaseRequestOrder.detail", "createTime", "创建时间");
call proc_add_form_field("purchaseRequestOrder.detail", "updateByName", "更新人");
call proc_add_form_field("purchaseRequestOrder.detail", "updateTime", "更新时间");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'requestNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'stateName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'orderNumTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'orderNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'applicantName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'applicationDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'projectName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'baseInfoRemark', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'code', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'quantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'num', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'suggestedSupplier', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'materialRemarks', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'purchaseQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'inventoryQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'projectDefineName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'contractName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'orderNumber', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'orderType', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'orderNumType', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'orderId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'applicant', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'applicationDate', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'projectName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'contractId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'projectDefineId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'code', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'quantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'actualNum', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'purchaseDate', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'suggestedSupplier', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'materialRemarks', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'requestNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'orderType', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'orderNumType', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'orderId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'applicant', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'applicationDate', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'projectName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'projectDefineId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'contractId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'code', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'num', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'actualNum', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'purchaseDate', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'suggestedSupplier', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'materialRemarks', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'purchaseQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'inventoryQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'updateByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'requestNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'orderNumType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'orderId', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'applicant', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'applicationDate', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'projectName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'projectDefineId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'contractId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'num', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'actualNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'suggestedSupplier', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'materialRemarks', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'purchaseQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'inventoryQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'updateByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'requestNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'orderNumType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'orderId', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'applicant', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'applicationDate', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'projectName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'projectDefineId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'contractId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'num', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'actualNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'suggestedSupplier', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'materialRemarks', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'purchaseQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'inventoryQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'updateByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'requestNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'state', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'orderNumType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'orderId', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'applicant', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'applicationDate', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'projectName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'projectDefineId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'contractId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'num', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'actualNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'suggestedSupplier', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'materialRemarks', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'purchaseQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'inventoryQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'updateByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'requestNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'state', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'orderNumType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'orderId', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'applicant', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'applicationDate', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'projectName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'projectDefineId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'contractId', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'num', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'actualNum', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'suggestedSupplier', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'materialRemarks', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'purchaseQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'inventoryQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'updateByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'requestNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'stateName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'orderNumTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'orderNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'applicant', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'applicationDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'projectName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'remark', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'projectDefineName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'contractName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'code', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'name', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'referenceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'actualNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'suggestedSupplier', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'materialRemarks', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'purchaseQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'receiptQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'inventoryQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'qualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'unqualifiedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'updateByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);



-- 理论工时
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'produceTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'planTheoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.editReport', 'workOrderReport.editReport', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.addReport', 'workOrderReport.addReport', 'theoryHour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("workOrder.list", "theoryHour", "单件理论工时");
call proc_add_form_field("workOrder.list", "produceTheoryHour", "产出理论工时");
call proc_add_form_field("workOrder.list", "planTheoryHour", "计划理论工时");
call proc_add_form_field("workOrder.edit", "theoryHour", "单件理论工时");
call proc_add_form_field("workOrder.edit", "produceTheoryHour", "产出理论工时");
call proc_add_form_field("workOrder.edit", "planTheoryHour", "计划理论工时");
call proc_add_form_field("workOrderReport.editReport", "theoryHour", "单件理论工时");
call proc_add_form_field("workOrderReport.addReport", "theoryHour", "单件理论工时");

-- 更新工单的表单配置的拓展字段，允许设置可选值
UPDATE `dfs_form_field_rule_config` SET `input_gray` = 0,`value_gray` = 0 WHERE `full_path_code` = 'workOrder.list' AND (`field_code` like 'workOrderExtend%' or `field_code` like 'workOrderMaterialExtend%');

-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================