-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.21.2.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.21.2.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.21.2.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.21.2.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.21.2.1=======================================================21.2

CREATE TABLE IF NOT EXISTS `dfs_task_relation` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `task_id` int(11) NOT NULL COMMENT '主任务id',
    `relate_type` varchar(255) NOT NULL COMMENT '关联类型',
    `relate_id` varchar(11) NOT NULL COMMENT '关联id',
    `username` varchar(255) DEFAULT NULL COMMENT '用户名',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`task_id`,`relate_type`,`relate_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务关联项表';

CREATE TABLE IF NOT EXISTS `dfs_task_row` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `task_id` int(11) NOT NULL COMMENT '主任务id',
    `material_code` varchar(255) NOT NULL COMMENT '物料',
    `material_line_id` int(11) NOT NULL COMMENT '物料行id',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`task_id`,`material_line_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='子任务表';


-- 客户物料清单增加价格
call proc_add_column(
        'dfs_customer_material_list',
        'price',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `price` double(11,2) DEFAULT NULL COMMENT ''价格''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_one',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_one` varchar(512) DEFAULT NULL COMMENT ''扩展字段1''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_two',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_two` varchar(512) DEFAULT NULL COMMENT ''扩展字段2''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_three',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_three` varchar(512) DEFAULT NULL COMMENT ''扩展字段3''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_four',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_four` varchar(512) DEFAULT NULL COMMENT ''扩展字段4''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_five',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_five` varchar(512) DEFAULT NULL COMMENT ''扩展字段5''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_six',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_six` varchar(512) DEFAULT NULL COMMENT ''扩展字段6''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_seven',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_seven` varchar(512) DEFAULT NULL COMMENT ''扩展字段7''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_eight',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_eight` varchar(512) DEFAULT NULL COMMENT ''扩展字段8''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_nine',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_nine` varchar(512) DEFAULT NULL COMMENT ''扩展字段9''');

call `proc_add_column`(
        'dfs_customer_material_list',
        'customer_material_extend_field_ten',
        'ALTER TABLE `dfs_customer_material_list` ADD COLUMN `customer_material_extend_field_ten` varchar(512) DEFAULT NULL COMMENT ''扩展字段10''');

-- 客户物料清单
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerMaterialList', '客户物料清单', 'customerMaterialList', 'dfs', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerMaterialListList', '客户物料清单列表页', 'customerMaterialList.list', 'customerMaterialList', 1, 'list');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerMaterialListEdit', '客户物料清单编辑页', 'customerMaterialList.edit', 'customerMaterialList', 1, 'edit-other');
--
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.list', 'customerMaterialList.list', 'baseField', '单据基本字段', 'base', '基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.list', 'customerMaterialList.list', 'baseMaterialLineField', '单据物料行字段', 'base', '基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.list', 'customerMaterialList.list', 'materialBaseField', '物料基础字段', 'base', '基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.list', 'customerMaterialList.list', 'materialExtendField', '物料扩展字段', 'extend', '扩展字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.list', 'customerMaterialList.list', 'baseMaterialLineExtendField', '单据物料行扩展字段', 'extend', '扩展字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.edit', 'customerMaterialList.edit', 'baseField', '单据基本字段', 'base', '基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.edit', 'customerMaterialList.edit', 'baseMaterialLineField', '单据物料行字段', 'base', '基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.edit', 'customerMaterialList.edit', 'materialBaseField', '物料基础字段', 'base', '基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.edit', 'customerMaterialList.edit', 'materialExtendField', '物料扩展字段', 'extend', '扩展字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('customerMaterialList.edit', 'customerMaterialList.edit', 'baseMaterialLineExtendField', '单据物料行扩展字段', 'extend', '扩展字段');

-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialStandard', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'price', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'code', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldOne', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldTwo', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldThree', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldFour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldFive', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldSix', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldSeven', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldEight', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldNine', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customerMaterialExtendFieldTen', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'name', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'sortName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'typeName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'state', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'comp', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'level', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'version', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'isBatchMag', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'isSupportCodeManage', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'isAuxiliaryMaterial', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'isDoubleUnit', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'editorName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'createByNickname', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'createTime', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'updateByName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'updateTime', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'unit', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'qualityLevel', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'unitNumerator', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'unitDenominator', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'standard', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'remark', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'drawingNumber', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'materialPrice', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'loseRate', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'rawMaterial', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'nameEnglish', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'minimumProductionLot', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'factoryModel', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.list', 'customerMaterialList.list', 'customFieldEleven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');

call proc_add_form_field_module('customerMaterialList.list', 'customerCode', '客户编码', 'baseField');
call proc_add_form_field_module('customerMaterialList.list', 'customerName', '客户名称', 'baseField');
--
call proc_add_form_field_module('customerMaterialList.list', 'stateName', '状态', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialCode', '客户物料编码', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialName', '客户物料名称', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialStandard', '客户物料规格', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.list', 'remark', '备注', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.list', 'price', '价格', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.list', 'code', '物料编码', 'baseMaterialLineField');
--
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldOne', '扩展字段1', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldTwo', '扩展字段2', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldThree', '扩展字段3', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldFour', '扩展字段4', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldFive', '扩展字段5', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldSix', '扩展字段6', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldSeven', '扩展字段7', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldEight', '扩展字段8', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldNine', '扩展字段9', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customerMaterialExtendFieldTen', '扩展字段10', 'baseMaterialLineExtendField');
--
call proc_add_form_field_module('customerMaterialList.list', 'name', '物料名称', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'sortName', '物料分类', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'typeName', '物料类型', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'state', '物料状态', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'comp', '物料单位', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'level', '物料等级', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'version', '物料版本', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'isBatchMag', '物料是否按批次管理', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'isSupportCodeManage', '物料是否按流水码管理', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'isAuxiliaryMaterial', '物料是否辅料', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'isDoubleUnit', '物料是否为双单位', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'editorName', '物料编制人', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'createByNickname', '物料创建人', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'createTime', '物料创建时间', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'updateByName', '物料更新人', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'updateTime', '物料更新时间', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'unit', '物料计量单位', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'qualityLevel', '物料质量等级', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'unitNumerator', '物料计量系数分子', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'unitDenominator', '物料计量系数分母', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'standard', '物料规格', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'remark', '物料备注', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'drawingNumber', '图号', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'materialPrice', '物料单价', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'loseRate', '损耗率', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'rawMaterial', '材质', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'nameEnglish', '物料英文名称', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'minimumProductionLot', '最小生产批量', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.list', 'factoryModel', '工厂型号', 'materialBaseField');
--
call proc_add_form_field_module('customerMaterialList.list', 'customFieldOne', '扩展字段1', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldTwo', '扩展字段2', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldThree', '扩展字段3', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldFour', '扩展字段4', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldFive', '扩展字段5', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldSix', '扩展字段6', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldSeven', '扩展字段7', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldEight', '扩展字段8', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldNine', '扩展字段9', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldTen', '扩展字段10', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.list', 'customFieldEleven', '扩展字段11', 'materialExtendField');






-- 编辑
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerAddr', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerContacts', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'stateName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialStandard', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'price', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'code', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customerMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineExtendField');

--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'name', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'sortName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'typeName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'state', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'comp', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'level', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'version', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'isBatchMag', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'isSupportCodeManage', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'isDoubleUnit', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'editorName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'createByNickname', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'createTime', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'updateByName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'updateTime', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'unit', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'qualityLevel', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'unitNumerator', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'unitDenominator', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'standard', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'remark', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'drawingNumber', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'materialPrice', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'loseRate', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'rawMaterial', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'nameEnglish', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'minimumProductionLot', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'factoryModel', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialBaseField');
--
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/customer-material-list', 'customerMaterialList.edit', 'customerMaterialList.edit', 'customFieldEleven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'materialExtendField');
--
call proc_add_form_field_module('customerMaterialList.edit', 'customerCode', '客户编码', 'baseField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerName', '客户名称', 'baseField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerAddr', '客户地址', 'baseField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerContacts', '客户联系人', 'baseField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMobile', '客户电话', 'baseField');
--
call proc_add_form_field_module('customerMaterialList.edit', 'stateName', '状态', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialCode', '客户物料编码', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialName', '客户物料名称', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialStandard', '客户物料规格', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.edit', 'remark', '备注', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.edit', 'price', '价格', 'baseMaterialLineField');
call proc_add_form_field_module('customerMaterialList.edit', 'code', '物料编码', 'baseMaterialLineField');
--
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldOne', '扩展字段1', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldTwo', '扩展字段2', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldThree', '扩展字段3', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldFour', '扩展字段4', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldFive', '扩展字段5', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldSix', '扩展字段6', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldSeven', '扩展字段7', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldEight', '扩展字段8', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldNine', '扩展字段9', 'baseMaterialLineExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customerMaterialExtendFieldTen', '扩展字段10', 'baseMaterialLineExtendField');
--
call proc_add_form_field_module('customerMaterialList.edit', 'name', '物料名称', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'sortName', '物料分类', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'typeName', '物料类型', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'state', '物料状态', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'comp', '物料单位', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'level', '物料等级', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'version', '物料版本', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'isBatchMag', '物料是否按批次管理', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'isSupportCodeManage', '物料是否按流水码管理', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'isAuxiliaryMaterial', '物料是否辅料', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'isDoubleUnit', '物料是否为双单位', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'editorName', '物料编制人', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'createByNickname', '物料创建人', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'createTime', '物料创建时间', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'updateByName', '物料更新人', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'updateTime', '物料更新时间', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'unit', '物料计量单位', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'qualityLevel', '物料质量等级', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'unitNumerator', '物料计量系数分子', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'unitDenominator', '物料计量系数分母', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'standard', '物料规格', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'remark', '物料备注', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'drawingNumber', '图号', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'materialPrice', '物料单价', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'loseRate', '损耗率', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'rawMaterial', '材质', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'nameEnglish', '物料英文名称', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'minimumProductionLot', '最小生产批量', 'materialBaseField');
call proc_add_form_field_module('customerMaterialList.edit', 'factoryModel', '工厂型号', 'materialBaseField');
--
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldOne', '扩展字段1', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldTwo', '扩展字段2', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldThree', '扩展字段3', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldFour', '扩展字段4', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldFive', '扩展字段5', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldSix', '扩展字段6', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldSeven', '扩展字段7', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldEight', '扩展字段8', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldNine', '扩展字段9', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldTen', '扩展字段10', 'materialExtendField');
call proc_add_form_field_module('customerMaterialList.edit', 'customFieldEleven', '扩展字段11', 'materialExtendField');




-- 设备台账
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'device', '设备台账', 'device', 'dfs', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceList', '设备台账列表页', 'device.list', 'device', 1, 'list');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceDetail', '设备台账详情页', 'device.detail', 'device', 1, 'detail');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceAdd', '设备台账新增页', 'device.add', 'device', 1, 'edit-create');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceEdit', '设备台账编辑页', 'device.edit', 'device', 1, 'edit-other');
--
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name) VALUES('device.list', 'device.list', 'baseField', '设备基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name) VALUES('device.detail', 'device.detail', 'baseField', '设备基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name) VALUES('device.add', 'device.add', 'baseField', '设备基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name) VALUES('device.edit', 'device.edit', 'baseField', '设备基础字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('device.list', 'device.list', 'extendField', '设备扩展字段', 'extend', '扩展字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('device.detail', 'device.detail', 'extendField', '设备扩展字段', 'extend', '扩展字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('device.add', 'device.add', 'extendField', '设备扩展字段', 'extend', '扩展字段');
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, parent_module_code, parent_module_name) VALUES('device.edit', 'device.edit', 'extendField', '设备扩展字段', 'extend', '扩展字段');

-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'brandModel', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'state', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'useStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'modelName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'magNickname', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'maintainerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'euiNames', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'supplier', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'supplierContactWay', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'bindingDate', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'gname', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'fname', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendOne', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendTwo', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendThree', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendFour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendFive', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendSix', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendSeven', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendEight', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendNine', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.list', 'device.list', 'deviceExtendTen', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');


call proc_add_form_field_module('device.list', 'deviceCode', '设备编码', 'baseField');
call proc_add_form_field_module('device.list', 'deviceName', '设备名称', 'baseField');
call proc_add_form_field_module('device.list', 'brandModel', '品牌型号', 'baseField');
call proc_add_form_field_module('device.list', 'state', '状态', 'baseField');
call proc_add_form_field_module('device.list', 'useStateName', '使用状态', 'baseField');
call proc_add_form_field_module('device.list', 'modelName', '设备类型', 'baseField');
call proc_add_form_field_module('device.list', 'magNickname', '设备负责人', 'baseField');
call proc_add_form_field_module('device.list', 'maintainerName', '设备维护人', 'baseField');
call proc_add_form_field_module('device.list', 'euiNames', '关联采集设备', 'baseField');
call proc_add_form_field_module('device.list', 'supplier', '供应商', 'baseField');
call proc_add_form_field_module('device.list', 'supplierContactWay', '供应商联系方式', 'baseField');
call proc_add_form_field_module('device.list', 'bindingDate', '绑定日期', 'baseField');
call proc_add_form_field_module('device.list', 'purchaseDate', '购置日期', 'baseField');
call proc_add_form_field_module('device.list', 'gname', '车间', 'baseField');
call proc_add_form_field_module('device.list', 'fname', '工位', 'baseField');
call proc_add_form_field_module('device.list', 'remark', '备注', 'baseField');
call proc_add_form_field_module('device.list', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('device.list', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('device.list', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('device.list', 'updateTime', '更新时间', 'baseField');

call proc_add_form_field_module('device.list', 'deviceExtendOne', '扩展字段1', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendTwo', '扩展字段2', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendThree', '扩展字段3', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendFour', '扩展字段4', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendFive', '扩展字段5', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendSix', '扩展字段6', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendSeven', '扩展字段7', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendEight', '扩展字段8', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendNine', '扩展字段9', 'extendField');
call proc_add_form_field_module('device.list', 'deviceExtendTen', '扩展字段10', 'extendField');



-- 详情
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'brandModel', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'state', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'useStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'modelName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'magNickname', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'maintainerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'euiNames', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'supplier', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'supplierContactWay', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'bindingDate', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'purchaseDate', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'gname', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'fname', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'modelImg', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'simulationImg', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'place', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendOne', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendTwo', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendThree', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendFour', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendFive', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendSix', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendSeven', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendEight', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendNine', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.detail', 'device.detail', 'deviceExtendTen', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');

call proc_add_form_field_module('device.detail', 'deviceCode', '设备编码', 'baseField');
call proc_add_form_field_module('device.detail', 'deviceName', '设备名称', 'baseField');
call proc_add_form_field_module('device.detail', 'brandModel', '品牌型号', 'baseField');
call proc_add_form_field_module('device.detail', 'state', '状态', 'baseField');
call proc_add_form_field_module('device.detail', 'useStateName', '使用状态', 'baseField');
call proc_add_form_field_module('device.detail', 'modelName', '设备类型', 'baseField');
call proc_add_form_field_module('device.detail', 'magNickname', '设备负责人', 'baseField');
call proc_add_form_field_module('device.detail', 'maintainerName', '设备维护人', 'baseField');
call proc_add_form_field_module('device.detail', 'euiNames', '关联采集设备', 'baseField');
call proc_add_form_field_module('device.detail', 'supplier', '供应商', 'baseField');
call proc_add_form_field_module('device.detail', 'supplierContactWay', '供应商联系方式', 'baseField');
call proc_add_form_field_module('device.detail', 'bindingDate', '绑定日期', 'baseField');
call proc_add_form_field_module('device.detail', 'purchaseDate', '购置日期', 'baseField');
call proc_add_form_field_module('device.detail', 'gname', '车间', 'baseField');
call proc_add_form_field_module('device.detail', 'fname', '工位', 'baseField');
call proc_add_form_field_module('device.detail', 'remark', '备注', 'baseField');
call proc_add_form_field_module('device.detail', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('device.detail', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('device.detail', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('device.detail', 'updateTime', '更新时间', 'baseField');
call proc_add_form_field_module('device.detail', 'modelImg', '设备图片', 'baseField');
call proc_add_form_field_module('device.detail', 'simulationImg', '设备仿真图', 'baseField');
call proc_add_form_field_module('device.detail', 'place', '区域位置', 'baseField');

call proc_add_form_field_module('device.detail', 'deviceExtendOne', '扩展字段1', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendTwo', '扩展字段2', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendThree', '扩展字段3', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendFour', '扩展字段4', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendFive', '扩展字段5', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendSix', '扩展字段6', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendSeven', '扩展字段7', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendEight', '扩展字段8', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendNine', '扩展字段9', 'extendField');
call proc_add_form_field_module('device.detail', 'deviceExtendTen', '扩展字段10', 'extendField');

-- 新增
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'brandModel', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'useStateName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'modelName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'magNickname', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'maintainerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'euiNames', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'supplier', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'supplierContactWay', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'purchaseDate', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'gname', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'fname', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'modelImg', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'simulationImg', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'place', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendOne', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendTwo', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendThree', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendFour', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendFive', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendSix', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendSeven', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendEight', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendNine', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.add', 'device.add', 'deviceExtendTen', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');

call proc_add_form_field_module('device.add', 'deviceCode', '设备编码', 'baseField');
call proc_add_form_field_module('device.add', 'deviceName', '设备名称', 'baseField');
call proc_add_form_field_module('device.add', 'brandModel', '品牌型号', 'baseField');
call proc_add_form_field_module('device.add', 'useStateName', '使用状态', 'baseField');
call proc_add_form_field_module('device.add', 'modelName', '设备类型', 'baseField');
call proc_add_form_field_module('device.add', 'magNickname', '设备负责人', 'baseField');
call proc_add_form_field_module('device.add', 'maintainerName', '设备维护人', 'baseField');
call proc_add_form_field_module('device.add', 'euiNames', '关联采集设备', 'baseField');
call proc_add_form_field_module('device.add', 'supplier', '供应商', 'baseField');
call proc_add_form_field_module('device.add', 'supplierContactWay', '供应商联系方式', 'baseField');
call proc_add_form_field_module('device.add', 'purchaseDate', '购置日期', 'baseField');
call proc_add_form_field_module('device.add', 'gname', '车间', 'baseField');
call proc_add_form_field_module('device.add', 'fname', '工位', 'baseField');
call proc_add_form_field_module('device.add', 'remark', '备注', 'baseField');
call proc_add_form_field_module('device.add', 'modelImg', '设备图片', 'baseField');
call proc_add_form_field_module('device.add', 'simulationImg', '设备仿真图', 'baseField');
call proc_add_form_field_module('device.add', 'place', '区域位置', 'baseField');

call proc_add_form_field_module('device.add', 'deviceExtendOne', '扩展字段1', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendTwo', '扩展字段2', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendThree', '扩展字段3', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendFour', '扩展字段4', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendFive', '扩展字段5', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendSix', '扩展字段6', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendSeven', '扩展字段7', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendEight', '扩展字段8', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendNine', '扩展字段9', 'extendField');
call proc_add_form_field_module('device.add', 'deviceExtendTen', '扩展字段10', 'extendField');




-- 编辑
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'brandModel', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'state', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'useStateName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'modelName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 0, 0, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'magNickname', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'maintainerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'euiNames', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'supplier', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'supplierContactWay', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'bindingDate', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'purchaseDate', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'gname', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'fname', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'modelImg', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'simulationImg', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'place', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendOne', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendTwo', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendThree', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendFour', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendFive', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendSix', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendSeven', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendEight', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendNine', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/equipment/device', 'device.edit', 'device.edit', 'deviceExtendTen', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'extendField');


call proc_add_form_field_module('device.edit', 'deviceCode', '设备编码', 'baseField');
call proc_add_form_field_module('device.edit', 'deviceName', '设备名称', 'baseField');
call proc_add_form_field_module('device.edit', 'brandModel', '品牌型号', 'baseField');
call proc_add_form_field_module('device.edit', 'state', '状态', 'baseField');
call proc_add_form_field_module('device.edit', 'useStateName', '使用状态', 'baseField');
call proc_add_form_field_module('device.edit', 'modelName', '设备类型', 'baseField');
call proc_add_form_field_module('device.edit', 'magNickname', '设备负责人', 'baseField');
call proc_add_form_field_module('device.edit', 'maintainerName', '设备维护人', 'baseField');
call proc_add_form_field_module('device.edit', 'euiNames', '关联采集设备', 'baseField');
call proc_add_form_field_module('device.edit', 'supplier', '供应商', 'baseField');
call proc_add_form_field_module('device.edit', 'supplierContactWay', '供应商联系方式', 'baseField');
call proc_add_form_field_module('device.edit', 'bindingDate', '绑定日期', 'baseField');
call proc_add_form_field_module('device.edit', 'purchaseDate', '购置日期', 'baseField');
call proc_add_form_field_module('device.edit', 'gname', '车间', 'baseField');
call proc_add_form_field_module('device.edit', 'fname', '工位', 'baseField');
call proc_add_form_field_module('device.edit', 'remark', '备注', 'baseField');
call proc_add_form_field_module('device.edit', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('device.edit', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('device.edit', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('device.edit', 'updateTime', '更新时间', 'baseField');
call proc_add_form_field_module('device.edit', 'modelImg', '设备图片', 'baseField');
call proc_add_form_field_module('device.edit', 'simulationImg', '设备仿真图', 'baseField');
call proc_add_form_field_module('device.edit', 'place', '区域位置', 'baseField');

call proc_add_form_field_module('device.edit', 'deviceExtendOne', '扩展字段1', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendTwo', '扩展字段2', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendThree', '扩展字段3', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendFour', '扩展字段4', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendFive', '扩展字段5', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendSix', '扩展字段6', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendSeven', '扩展字段7', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendEight', '扩展字段8', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendNine', '扩展字段9', 'extendField');
call proc_add_form_field_module('device.edit', 'deviceExtendTen', '扩展字段10', 'extendField');

-- 指标表定义
UPDATE dfs_table_config
SET
    field_define = 'TIME_DIMENSION',
    field_remark = CASE
                   WHEN table_name LIKE '%daily%' THEN '日期，数据库实际值为yyyy-MM-dd'
                   WHEN table_name LIKE '%monthly%' THEN '月份，数据库实际值为yyyy-MM-01'
                   WHEN table_name LIKE '%hourly%' THEN '小时及以下，数据库实际值为yyyy-MM-dd HH:mm:ss'
                   WHEN table_name LIKE '%10min%' THEN '小时及以下，数据库实际值为yyyy-MM-dd HH:mm:ss'
                   ELSE field_remark
    END
WHERE
  field_type IN ('date', 'datetime')
  AND field_code IN ('record_date', 'record_hour', 'record_month', 'record_time')
  AND (
            table_name LIKE '%daily%' OR
            table_name LIKE '%weekly%' OR
            table_name LIKE '%monthly%' OR
            table_name LIKE '%hourly%' OR
            table_name LIKE '%10min%'
    );
UPDATE dfs_table_config  SET field_define = 'TIME_DIMENSION' , field_remark = 'yyyy-MM-dd' WHERE field_code = 'record_date' AND table_name = 'dfs_metrics_device_oee';
