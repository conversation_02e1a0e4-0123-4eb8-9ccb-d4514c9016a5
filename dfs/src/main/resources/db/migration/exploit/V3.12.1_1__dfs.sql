-- DDL

-- 物料新增计量系数分子、计量系数分母
call proc_add_column(
        'dfs_material',
        'unit_numerator',
        'ALTER TABLE `dfs_material` ADD COLUMN `unit_numerator` double(11,4) NULL DEFAULT 1.0000 COMMENT ''计量系数分子'' AFTER `scale_factor`');
call proc_add_column(
        'dfs_material',
        'unit_denominator',
        'ALTER TABLE `dfs_material` ADD COLUMN `unit_denominator` double(11,4) NULL DEFAULT 1.0000 COMMENT ''计量系数分母'' AFTER `unit_numerator`');

-- 兼容历史数据，将原来的计量系数同步到 计量系数分子的字段上
update dfs_material set `unit_numerator` = `scale_factor`;
update dfs_material set `unit_denominator` = 1 WHERE `scale_factor` IS NOT NULL;

-- 表单配置新增计量系数分子、计量系数分母
call proc_add_form_field('material.list', 'unitNumerator', '计量系数分子');
call proc_add_form_field('material.list', 'unitDenominator', '计量系数分母');
call proc_add_form_field('material.edit', 'unitNumerator', '计量系数分子');
call proc_add_form_field('material.edit', 'unitDenominator', '计量系数分母');
call proc_add_form_field('material.detail', 'unitNumerator', '计量系数分子');
call proc_add_form_field('material.detail', 'unitDenominator', '计量系数分母');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.list', 'material.list', 'unitNumerator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.list', 'material.list', 'unitDenominator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByCreate', 'material.edit', 'unitNumerator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByCreate', 'material.edit', 'unitDenominator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByRelease', 'material.edit', 'unitNumerator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByRelease', 'material.edit', 'unitDenominator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByStopUsing', 'material.edit', 'unitNumerator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByStopUsing', 'material.edit', 'unitDenominator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByAbandon', 'material.edit', 'unitNumerator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.editByAbandon', 'material.edit', 'unitDenominator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.detail', 'material.detail', 'unitNumerator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/product-management/supplies', 'material.detail', 'material.detail', 'unitDenominator', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和是否开启双单位冲突，请以物料双单位开关为准，此字段置灰，仅允许重命名');
-- 删除原计量系数的表单配置
DELETE FROM `dfs_form_field_config` WHERE `field_code` = 'scaleFactor' AND `full_path_code` in ('material.edit', 'material.detail');
DELETE FROM `dfs_form_field_rule_config` WHERE `field_code` = 'scaleFactor' AND `field_name_full_path_code` in ('material.edit', 'material.detail');

-- 将物料的原计量系数字段更新为保留9位小数
call proc_modify_column(
        'dfs_material',
        'scale_factor',
        'ALTER TABLE `dfs_material` CHANGE COLUMN `scale_factor` `scale_factor` double(10,9) NULL COMMENT ''换算系数''');

call proc_add_column(
        'dfs_scan_code_rule',
        'type',
        'ALTER TABLE `dfs_scan_code_rule` ADD COLUMN `type` varchar(50) NOT NULL DEFAULT ''script'' COMMENT ''类型''');

call proc_add_column_index('dfs_scan_code_rule','code','code');

-- 表单配置新增是否进行了全局配置
call proc_add_column(
    'dfs_form_field_rule_config',
    'is_overall',
    'ALTER TABLE `dfs_form_field_rule_config` ADD COLUMN `is_overall` tinyint(4) NOT NULL DEFAULT 0 COMMENT ''是否进行了全局配置''');

-- 表单全局字段配置表
CREATE TABLE IF NOT EXISTS `dfs_form_overall_field_config`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT,
    `field_custom_name`  varchar(255) DEFAULT NULL COMMENT '全局配置自定义名称',
    `input_type`         varchar(255) DEFAULT NULL COMMENT '输入框类型，单选，多选，输入框，文本域，json框，文本',
    `option_values_type` varchar(50)  DEFAULT NULL COMMENT '数据来源类型（api接口、table表格、formula公式）',
    `related_api_code`   varchar(255) DEFAULT NULL COMMENT '关联API编码',
    `all_option`         text COMMENT '全选值',
    `remark`             varchar(255) DEFAULT NULL COMMENT '说明',
    `sort`               int(11) DEFAULT '1' COMMENT '排序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局表单字段配置表';

CREATE TABLE IF NOT EXISTS `dfs_form_overall_field_rule_config`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT,
    `relate_overall_id` int(11) NOT NULL COMMENT '全局配置关联的id',
    `type`              varchar(255) DEFAULT NULL COMMENT '规则所属类型（列表list/编辑-创建态edit-create/编辑-其他态edit-other/详情detail）',
    `is_show`           tinyint(4) DEFAULT '1' COMMENT '是否显示',
    `is_need`           tinyint(4) DEFAULT '0' COMMENT '是否必填',
    `is_edit`           tinyint(4) DEFAULT '0' COMMENT '是否可编辑',
    PRIMARY KEY (`id`),
    KEY                 `relate_overall_id` (`relate_overall_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局表单字段配置关联规则表';

-- 上下游单据关系配置表
CREATE TABLE IF NOT EXISTS `dfs_form_up_down_stream_relationship_config`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT,
    `upstream_service`          varchar(100) DEFAULT NULL COMMENT '上游所属服务',
    `upstream_order_type`       varchar(255) DEFAULT NULL COMMENT '上游单据类型',
    `upstream_order_name`       varchar(255) DEFAULT NULL COMMENT '上游单据名称',
    `downstream_service`        varchar(100) DEFAULT NULL COMMENT '下游所属服务',
    `downstream_order_type`     varchar(255) DEFAULT NULL COMMENT '下游单据类型',
    `downstream_order_name`     varchar(255) DEFAULT NULL COMMENT '下游单据名称',
    PRIMARY KEY (`id`),
    KEY `upstream_order_type` (`upstream_order_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上下游单据关系配置表';

-- 全局表单允许配置下推的字段
CREATE TABLE IF NOT EXISTS `dfs_form_overall_allow_field_config`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT,
    `route`             varchar(255) DEFAULT NULL COMMENT '单据所属表单路由',
    `order_type`        varchar(255) DEFAULT NULL COMMENT '字段所属的对应单据类型',
    `field_code`        varchar(255) DEFAULT NULL COMMENT '单据字段编码',
    `field_name`        varchar(255) DEFAULT NULL COMMENT '单据字段源名称',
    PRIMARY KEY (`id`),
    UNIQUE KEY `type_name` (`order_type`,`field_name`) USING BTREE,
    KEY `order_type` (`order_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局表单允许配置下推的字段';

-- 全局表单字段映射配置表
CREATE TABLE IF NOT EXISTS `dfs_form_overall_field_mapping_config`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT,
    `relate_overall_id` int(11) NOT NULL COMMENT '全局配置关联的id',
    `route`             varchar(255) DEFAULT NULL COMMENT '单据所属表单路由',
    `order_type`        varchar(255) DEFAULT NULL COMMENT '字段所属的对应单据类型编码',
    `order_type_name`   varchar(255) DEFAULT NULL COMMENT '字段所属的对应单据类型名称',
    `field_code`        varchar(255) DEFAULT NULL COMMENT '单据字段编码',
    `field_name`        varchar(255) DEFAULT NULL COMMENT '单据字段源名称',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id_type_name` (`relate_overall_id`,`order_type`,`field_name`) USING BTREE,
    KEY                 `relate_overall_id` (`relate_overall_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='全局表单字段映射配置表';

CREATE TABLE IF NOT EXISTS `dfs_scan_code_rule_resolver` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code_rule_id` int(11) NOT NULL COMMENT '条码规则ID',
    `field_code` varchar(255) NULL COMMENT '字段编码',
    `resolver_type` varchar(50) NULL COMMENT '解码函数类型',
    `resolver_config_one` varchar(255) NULL COMMENT '解码配置1',
    `resolver_config_two` varchar(255) NULL COMMENT '解码配置2',
    PRIMARY KEY (`id`),
    KEY `code_rule_field` (`code_rule_id`,`field_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='解码函数关联表';

-- 生产工单用料清单增加累计补料出库数
call proc_add_column(
        'dfs_work_order_material_list_material',
        'total_material_issued',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `total_material_issued` decimal(22, 10) NULL DEFAULT 0.0000000000 COMMENT ''累计补料出库数''');

-- 下推记录表新增字段
-- 由于下推记录计算的维度变了，改成了记录目标单据的记录，所以之前的下推记录都要删除掉
TRUNCATE TABLE `dfs_order_push_down_record`;
call proc_add_column(
        'dfs_order_push_down_record',
        'target_order_push_down_quantity',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `target_order_push_down_quantity` double(11,4) DEFAULT NULL COMMENT ''目标单据下推数量'' AFTER `push_down_quantity`');
call proc_add_column(
        'dfs_order_push_down_record',
        'push_down_code',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `push_down_code` varchar(255) DEFAULT NULL COMMENT ''本次下推的标识'' AFTER `target_order_push_down_quantity`');
call proc_add_column(
        'dfs_order_push_down_record',
        'source_order_batch',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `source_order_batch` varchar(255) DEFAULT NULL COMMENT ''源单据批次号'' AFTER `material_code`');
call proc_add_column(
        'dfs_order_push_down_record',
        'target_order_material_code',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `target_order_material_code` varchar(255) DEFAULT NULL COMMENT ''目标单据物料编码'' AFTER `target_order_number`');
call proc_add_column(
        'dfs_order_push_down_record',
        'target_order_material_id',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `target_order_material_id` int(11) DEFAULT NULL COMMENT ''目标单据物料行id'' AFTER `target_order_material_code`');
call proc_add_column(
        'dfs_order_push_down_record',
        'target_order_batch',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `target_order_batch` varchar(255) DEFAULT NULL COMMENT ''目标单据批次号'' AFTER `target_order_material_id`');
-- 修改记录表备注
call proc_modify_column(
        'dfs_order_push_down_record',
        'material_code',
        'ALTER TABLE `dfs_order_push_down_record` CHANGE COLUMN `material_code` `material_code` varchar(255) COMMENT ''源单据物料编码''');
call proc_modify_column(
        'dfs_order_push_down_record',
        'push_down_quantity',
        'ALTER TABLE `dfs_order_push_down_record` CHANGE COLUMN `push_down_quantity` `push_down_quantity` double(11,4) COMMENT ''源单据下推数量''');
-- 下推记录表加索引
call proc_add_column_index('dfs_order_push_down_record','push_down_code','push_down_code');
call proc_add_column_index('dfs_order_push_down_record','source_order_type,source_order_number,source_order_material_id','type_number_material_id');
-- 理论工时
call proc_add_column(
        'dfs_procedure_relation_work_hours',
        'input_num',
        'ALTER TABLE `dfs_procedure_relation_work_hours` ADD COLUMN `input_num` int(11) DEFAULT 0 COMMENT ''投入人数''');
call proc_add_column(
        'dfs_procedure_relation_work_hours',
        'theory_hours',
        'ALTER TABLE `dfs_procedure_relation_work_hours` ADD COLUMN `theory_hours` double(20,2) DEFAULT 0.00  COMMENT ''理论工时''');
call proc_add_column(
        'dfs_procedure_relation_work_hours',
        'theory_hours_unit',
        'ALTER TABLE `dfs_procedure_relation_work_hours` ADD COLUMN `theory_hours_unit` varchar(255) DEFAULT NULL COMMENT ''理论工时单位''');

call proc_add_column(
        'dfs_procedure_def_relation_work_hours',
        'input_num',
        'ALTER TABLE `dfs_procedure_def_relation_work_hours` ADD COLUMN `input_num` int(11) DEFAULT 0 COMMENT ''投入人数''');
call proc_add_column(
        'dfs_procedure_def_relation_work_hours',
        'theory_hours',
        'ALTER TABLE `dfs_procedure_def_relation_work_hours` ADD COLUMN `theory_hours` double(20,2) DEFAULT 0.00  COMMENT ''理论工时''');
call proc_add_column(
        'dfs_procedure_def_relation_work_hours',
        'theory_hours_unit',
        'ALTER TABLE `dfs_procedure_def_relation_work_hours` ADD COLUMN `theory_hours_unit` varchar(255) DEFAULT NULL COMMENT ''理论工时单位''');

-- 工单：业务单元编码默认值为0000
call proc_modify_column(
        'dfs_work_order',
        'business_unit_code',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `business_unit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''0000'' COMMENT ''业务单元编码''');

-- 工单：业务单元名称默认值为无
call proc_modify_column(
        'dfs_work_order',
        'business_unit_name',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''无'' COMMENT ''业务单元名称''');

-- 销售发货单：业务单元编码默认值为0000
call proc_modify_column(
        'dfs_stock_delivery_material',
        'business_unit_code',
        'ALTER TABLE `dfs_stock_delivery_material` MODIFY COLUMN `business_unit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''0000'' COMMENT ''业务单元编码''');

-- 销售发货单：业务单元名称默认值为无
call proc_modify_column(
        'dfs_stock_delivery_material',
        'business_unit_name',
        'ALTER TABLE `dfs_stock_delivery_material` MODIFY COLUMN `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''无'' COMMENT ''业务单元名称''');

-- 出货申请单：业务单元编码默认值为0000
call proc_modify_column(
        'dfs_delivery_application_material',
        'business_unit_code',
        'ALTER TABLE `dfs_delivery_application_material` MODIFY COLUMN `business_unit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''0000'' COMMENT ''业务单元编码''');

-- 出货申请单：业务单元名称默认值为无
call proc_modify_column(
        'dfs_delivery_application_material',
        'business_unit_name',
        'ALTER TABLE `dfs_delivery_application_material` MODIFY COLUMN `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''无'' COMMENT ''业务单元名称''');

-- 生产工单用料清单：业务单元编码默认值为0000
call proc_modify_column(
        'dfs_work_order_material_list_material',
        'business_unit_code',
        'ALTER TABLE `dfs_work_order_material_list_material` MODIFY COLUMN `business_unit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''0000'' COMMENT ''业务单元编码''');

-- 生产工单用料清单：业务单元名称默认值为无
call proc_modify_column(
        'dfs_work_order_material_list_material',
        'business_unit_name',
        'ALTER TABLE `dfs_work_order_material_list_material` MODIFY COLUMN `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''无'' COMMENT ''业务单元名称''');

-- DML

-- 采购退料单 下载/打印单据权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090209150', '下载/打印单据(下载默认打印模板)', 'return.order:downDefaultTemplate', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090209', '2', '1', '0', '/supply-chain-collaboration/procurement-management/return-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090209160', '下载/打印单据(上传/下载自定义打印模板)', 'return.order:downUploadPrintTemplate', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'POST', '1090209', '2', '1', '0', '/supply-chain-collaboration/procurement-management/return-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090209170', '下载/打印单据(下载/打印pdf)', 'return.order:downPrintPdf', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090209', '2', '1', '0', '/supply-chain-collaboration/procurement-management/return-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090209180', '下载/打印单据(导出Excel)', 'return.order:excel', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090209', '2', '1', '0', '/supply-chain-collaboration/procurement-management/return-order', '1', NULL, '');
call init_new_role_permission('1090209150');
call init_new_role_permission('1090209160');
call init_new_role_permission('1090209170');
call init_new_role_permission('1090209180');

-- 仓库相关的下推配置进行平铺到对应菜单里
-- 删除无用的下推数据
DELETE FROM `dfs_dict` WHERE `code` in ('saleOrder.pushDownConfig.purchaseRequest.jumpPage','saleOrder.pushDownConfig.deliveryApplication.jumpPage','saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut','saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt','production.productOrderPushDownConfig.purchaseRequest.jumpPage','production.productOrderPushDownConfig.productMaterialsList.jumpPage','production.productOrderPushDownConfig.productionIn.jumpPage','production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage','production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage','production.productMaterialsListPushDownConfig.transferOrder.jumpPage','production.workOrderPushDownConfig.takeOutApplication.jumpPage','production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct','production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn','production.workOrderPushDownConfig.materialList.jumpPage','purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage','purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage','purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage','purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn','purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage','production.productOrderPushDownConfig.workOrder.craftRouteAuto','saleOrder.pushDownConfig.productOrder.bomAuto');
DELETE FROM `dfs_order_push_down_config_value_dict` WHERE `config_full_path_code` in ('saleOrder.pushDownConfig.purchaseRequest.jumpPage','saleOrder.pushDownConfig.deliveryApplication.jumpPage','saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut','saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt','production.productOrderPushDownConfig.purchaseRequest.jumpPage','production.productOrderPushDownConfig.productMaterialsList.jumpPage','production.productOrderPushDownConfig.productionIn.jumpPage','production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage','production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage','production.productMaterialsListPushDownConfig.transferOrder.jumpPage','production.workOrderPushDownConfig.takeOutApplication.jumpPage','production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct','production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn','production.workOrderPushDownConfig.materialList.jumpPage','purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage','purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage','purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage','purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn','purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage','production.productOrderPushDownConfig.workOrder.craftRouteAuto','saleOrder.pushDownConfig.productOrder.bomAuto');
UPDATE `dfs_dict` SET `value` = null WHERE `type` in ('saleOrder.pushDownConfig.productOrder','saleOrder.pushDownConfig.workOrder','saleOrder.pushDownConfig.purchaseRequest','saleOrder.pushDownConfig.deliveryApplication','saleOrder.pushDownConfig.saleInAndOut','saleOrder.pushDownConfig.subcontractOrder','production.productOrderPushDownConfig.workOrder','production.productOrderPushDownConfig.purchaseRequest','production.productOrderPushDownConfig.productMaterialsList','production.productOrderPushDownConfig.productionIn','production.productMaterialsListPushDownConfig.outputPickingProduct','production.productMaterialsListPushDownConfig.workOrderSupplement','production.productMaterialsListPushDownConfig.transferOrder','production.workOrderPushDownConfig.takeOutApplication','production.workOrderPushDownConfig.productStockInAndOut','production.workOrderPushDownConfig.materialList','purchase.purchaseRequestPushDownConfig.purchaseOrder','purchase.purchaseOrderPushDownConfig.supplierBom','purchase.purchaseOrderPushDownConfig.supplierBom','purchase.purchaseOrderPushDownConfig.purchaseReceipt','purchase.purchaseOrderPushDownConfig.purchaseIn','purchase.purchaseReceiptPushDownConfig.requestStockInAndOut','purchase.purchaseReceiptPushDownConfig.returnOrder','purchase.purchaseReceiptPushDownConfig.incomingInspection','purchase.purchaseReceiptPushDownConfig.incomingInspection','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut','purchase.purchaseOrderPushDownConfig.purchaseReceipt','saleOrder.pushDownConfig.saleReturnOrder','purchase.purchaseReceiptPushDownConfig.inspectOrder','purchase.purchaseReceiptPushDownConfig.inspectOrder','saleOrder.pushDownConfig.saleReturnOrder','saleOrder.pushDownConfig.purchaseOrder','saleOrder.pushDownConfig.purchaseOrder','saleOrder.pushDownConfig.subcontractOrder','production.productOrderPushDownConfig.subcontractOrder','production.productOrderPushDownConfig.subcontractOrder','saleOrder.pushDownConfig.subcontractOrder','production.productOrderPushDownConfig.subcontractOrder','saleOrder.pushDownConfig.purchaseRequest','saleOrder.pushDownConfig.productOrder','production.productOrderPushDownConfig.productMaterialsList','saleOrder.pushDownConfig.workOrder','production.productOrderPushDownConfig.workOrder','production.workOrderPushDownConfig.materialList');
-- 更新销售订单下推销售出入库的下推配置
DELETE FROM `dfs_order_push_down_config` WHERE `full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut';
DELETE FROM `dfs_order_push_down_item` WHERE `config_path` = 'saleOrder.pushDownConfig.saleInAndOut';
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('saleOut', '销售出库单', 'saleOrder.pushDownConfig.saleOut', 'saleOrder.pushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'saleOut', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('salesReturnReceipt', '销售退货入库单', 'saleOrder.pushDownConfig.salesReturnReceipt', 'saleOrder.pushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'salesReturnReceipt', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'saleOrder.pushDownConfig.saleOut');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'saleOrder.pushDownConfig.salesReturnReceipt');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.originalOrderStates', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', '[2]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.openPage', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('applicationId', 'applicationId', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.applicationId', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesIssueDoc\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('url', '前端路由', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.url', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesIssueDoc?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.sourceOrderType', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.targetOrderType', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleInAndOut\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.originalOrderStates', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.openPage', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('applicationId', 'applicationId', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.applicationId', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('url', '前端路由', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.url', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.sourceOrderType', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.targetOrderType', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleInAndOut\"', 'sysConf');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('saleOrder.pushDownConfig.saleOut', NULL, '下推销售出库单(新建页签)', 'RULE', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('saleOrder.pushDownConfig.salesReturnReceipt', NULL, '下推销售退货入库单(新建页签)', 'RULE', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt';
-- 更新生产工单下推生产出入库的下推配置
DELETE FROM `dfs_order_push_down_config` WHERE `full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut';
DELETE FROM `dfs_order_push_down_item` WHERE `config_path` = 'production.workOrderPushDownConfig.productStockInAndOut';
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('outputPickingProduct', '生产领料出库单', 'production.workOrderPushDownConfig.outputPickingProduct', 'production.workOrderPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'outputPickingProduct', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('workOrderSupplement', '生产补料出库单', 'production.workOrderPushDownConfig.workOrderSupplement', 'production.workOrderPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'workOrderSupplement', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('productionReturnReceipt', '生产退料入库单', 'production.workOrderPushDownConfig.productionReturnReceipt', 'production.workOrderPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'productionReturnReceipt', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('productionIn', '生产入库单', 'production.workOrderPushDownConfig.productionIn', 'production.workOrderPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'productionIn', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.outputPickingProduct');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.workOrderSupplement');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.productionReturnReceipt');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.productionIn');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', '[2]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', '[2]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', '[2]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('originalOrderStates', '源单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[3,4,5]', '[3,4,5]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.url', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut?pushDownOrigin=workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.sourceOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.targetOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"outputPickingProduct\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderSupplement\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.url', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement?pushDownOrigin=workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.sourceOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.targetOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderSupplement\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.applicationReturn\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.url', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/applicationReturn?pushDownOrigin=workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.sourceOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.targetOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionReturnReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.applicationId', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderComplete\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.url', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete?pushDownOrigin=workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.sourceOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.targetOrderType', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionIn\"', 'sysConf');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.outputPickingProduct', NULL, '生产领料出库单(新建页签)', 'RULE', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.workOrderSupplement', NULL, '生产补料出库单(新建页签)', 'RULE', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.productionReturnReceipt', NULL, '生产退料入库单(新建页签)', 'RULE', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.productionIn', NULL, '生产入库单(新建页签)', 'RULE', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn';
-- 更新下推菜单：采购出入库 修改成 采购入库单
UPDATE `dfs_order_push_down_config` SET `name` = '采购入库单' WHERE `full_path_code` = 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut';


-- 采购需求单下推采购订单
-- 删除之前"下推采购订单(新建页签)"配置
DELETE FROM dfs_order_push_down_item WHERE instance_type = "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage";
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('purchase.purchaseRequestPushDownConfig.purchaseOrder.push', '通用下推', 'purchase.purchaseRequestPushDownConfig.purchaseOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.originalOrderStates', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select-multiple', 'api', '/ams/requests/state', 'get', NULL, 'code,name', '[4,5]', '[4,5]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.description', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.targetOrderState', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'mergeSampleMaterial', '是否合并相同物料', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.mergeSampleMaterial', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'mergeSampleSupplier', '是否合并相同供应商', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.mergeSampleSupplier', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.applicationId', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.purchasing-list\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.url', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-list?pushDownOrigin=purchaseRequest\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.sourceOrderType', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseRequest\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.targetOrderType', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'purchase.purchaseRequestPushDownConfig.purchaseOrder',  '下推采购订单', 'RULE', 1, 1, 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.originalOrderStates', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select-multiple', 'api', '/ams/requests/state', 'get', NULL, 'code,name', '[4,5]', '[4,5]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.description', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.targetOrderState', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'mergeSampleMaterial', '是否合并相同物料', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.mergeSampleMaterial', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'mergeSampleSupplier', '是否合并相同供应商', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.mergeSampleSupplier', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.applicationId', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.purchasing-list\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.url', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-list?pushDownOrigin=purchaseRequest\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.sourceOrderType', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseRequest\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push.targetOrderType', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseRequestPushDownConfig.purchaseOrder.push';

-- 采购订单下推采购收料单
-- 删除之前"下推采购收料单(新建页签)"配置
DELETE FROM dfs_order_push_down_item WHERE instance_type = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage";
-- 删除之前没有按下推规范来的的配置
DELETE FROM dfs_dict WHERE `type` = "purchase.purchaseOrderPushDownConfig.purchaseReceipt" AND `code` = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.pushBatch";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.pushBatch";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.pushBatch";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.pushBatch";
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', '通用下推', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.description', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.targetOrderState', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'select', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[1]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.delivery-order\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.url', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/delivery-order?pushDownOrigin=purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.sourceOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.targetOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'purchase.purchaseOrderPushDownConfig.purchaseReceipt',  '下推采购收料单', 'RULE', 1, 1, 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.description', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.targetOrderState', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'select', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[1]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.delivery-order\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.url', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/delivery-order?pushDownOrigin=purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.sourceOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.targetOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push';

update dfs_form_config  set name = '采购收料单'  where code = 'purchaseReceiptOrder';
update dfs_business_config_value  set value = '[2]'  where value_full_path_code = 'subcontract.subcontractOrderPushDownConfig.subcontractSendMaterialsList.jumpPage.originalOrderStates';

-- 出货申请单新增扩展字段
call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_one',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_one`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段1'' AFTER `warehouse_code`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_two',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_two`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段2'' AFTER `delivery_application_extend_field_one`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_three',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_three`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段3'' AFTER `delivery_application_extend_field_two`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_four',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_four`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段4'' AFTER `delivery_application_extend_field_three`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_five',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_five`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段5'' AFTER `delivery_application_extend_field_four`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_six',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_six`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段6'' AFTER `delivery_application_extend_field_five`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_seven',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_seven`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段7'' AFTER `delivery_application_extend_field_six`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_eight',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_eight`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段8'' AFTER `delivery_application_extend_field_seven`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_nine',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_nine`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段9'' AFTER `delivery_application_extend_field_eight`;');

call proc_add_column(
        'dfs_delivery_application',
        'delivery_application_extend_field_ten',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `delivery_application_extend_field_ten`  varchar(255) DEFAULT null    COMMENT ''出货申请单扩展字段10'' AFTER `delivery_application_extend_field_nine`;');
--  出货申请单物料新增扩展字段
call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_one',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_one`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段1'' AFTER `business_unit_name`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_two',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_two`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段2'' AFTER `delivery_application_material_extend_field_one`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_three',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_three`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段3'' AFTER `delivery_application_material_extend_field_two`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_four',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_four`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段4'' AFTER `delivery_application_material_extend_field_three`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_five',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_five`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段5'' AFTER `delivery_application_material_extend_field_four`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_six',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_six`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段6'' AFTER `delivery_application_material_extend_field_five`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_seven',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_seven`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段7'' AFTER `delivery_application_material_extend_field_six`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_eight',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_eight`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段8'' AFTER `delivery_application_material_extend_field_seven`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_nine',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_nine`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段9'' AFTER `delivery_application_material_extend_field_eight`;');

call proc_add_column(
        'dfs_delivery_application_material',
        'delivery_application_material_extend_field_ten',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `delivery_application_material_extend_field_ten`  varchar(255) DEFAULT null    COMMENT ''出货申请单物料扩展字段10'' AFTER `delivery_application_material_extend_field_nine`;');

-- 批次增加生产时间
call proc_add_column(
        'dfs_bar_code',
        'production_time',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `production_time` datetime DEFAULT NULL COMMENT ''生产时间'';');

-- 销售订单下推采购需求、采购订单下推配置新增按物料分类过滤
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.filterMaterialSorts', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase",\"product\",\"outsourcing\"]', '[\"product\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.filterMaterialSorts', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"product\",\"outsourcing\"]', '[\"product\",\"outsourcing\"]', NULL);
-- 销售订单下推采购订单下推配置新增按物料分类过滤
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式(单选)', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.bomSplitType', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'api', '/ams/requests/bom/split', 'get', NULL, 'code,name', '[\"direct\",\"byTierOneBom\",\"byMultiLevelBom\"]','\"byMultiLevelBom\"', NULL);

-- 采购收料单下推采购退料配置
delete  from  dfs_order_push_down_item   where config_path = 'purchase.purchaseReceiptPushDownConfig.returnOrder';
delete  from  dfs_dict   where type = 'purchase.purchaseReceiptPushDownConfig.returnOrder';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder';
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', '通用下推', 'purchase.purchaseReceiptPushDownConfig.returnOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'purchase.purchaseReceiptPushDownConfig.returnOrder', 'show');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'purchase.purchaseReceiptPushDownConfig.returnOrder',  '下推采购退料单', 'RULE', 1, 1, 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.enable', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.isSupportMaterialLineReversePushDown', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.description', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.targetOrderStates', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select', 'api', '/ams/purchase/return/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.return-order\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.url', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/return-order?pushDownOrigin=purchaseReceipt\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReturn\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.enable', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.isSupportMaterialLineReversePushDown', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.description', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.targetOrderStates', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'select', 'api', '/ams/purchase/return/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.applicationId', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.return-order\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.url', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/return-order?pushDownOrigin=purchaseReceipt\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReturn\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseReceiptPushDownConfig.returnOrder.pushBatch';

-- 物料特征参数导入
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10807090', '下载默认转换模板', 'auxiliary.attr:export-default-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10807', 2, 1, 0, '/product-management/auxiliary-attr', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10807100', '下载自定义转换模板', 'auxiliary.attr:export-custom-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10807', 2, 1, 0, '/product-management/auxiliary-attr', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10807110', '上传自定义转换模板', 'auxiliary.attr:import-custom-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10807', 2, 1, 0, '/product-management/auxiliary-attr', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10807120', '下载导入模板', 'auxiliary.attr:import-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10807', 2, 1, 0, '/product-management/auxiliary-attr', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10807130', '导入数据', 'auxiliary.attr:import-data', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10807', 2, 1, 0, '/product-management/auxiliary-attr', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10807140', '查看日志', 'auxiliary.attr:import-log', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10807', 2, 1, 0, '/product-management/auxiliary-attr', 1, NULL, '', 1);
call init_new_role_permission('10807%');

-- 插入下推单据可映射的字段数据
Truncate Table `dfs_form_overall_allow_field_config`;
-- 销售订单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldOne', '销售订单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldTwo', '销售订单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldThree', '销售订单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldFour', '销售订单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldFive', '销售订单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldSix', '销售订单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldSeven', '销售订单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldEight', '销售订单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldNine', '销售订单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderExtendFieldTen', '销售订单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldOne', '销售订单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldTwo', '销售订单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldThree', '销售订单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldFour', '销售订单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldFive', '销售订单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldSix', '销售订单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldSeven', '销售订单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldEight', '销售订单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldNine', '销售订单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('saleOrder', '/order-model/salesOrder', 'saleOrderMaterialExtendFieldTen', '销售订单物料扩展字段10');
-- 生产订单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldOne', '生产订单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldTwo', '生产订单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldThree', '生产订单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldFour', '生产订单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldFive', '生产订单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldSix', '生产订单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldSeven', '生产订单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldEight', '生产订单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldNine', '生产订单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderExtendFieldTen', '生产订单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldOne', '生产订单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldTwo', '生产订单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldThree', '生产订单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldFour', '生产订单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldFive', '生产订单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldSix', '生产订单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldSeven', '生产订单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldEight', '生产订单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldNine', '生产订单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productOrder', '/order-model/product-order', 'productOrderMaterialExtendFieldTen', '生产订单物料扩展字段10');
-- 生产工单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldOne', '工单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldTwo', '工单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldThree', '工单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldFour', '工单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldFive', '工单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldSix', '工单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldSeven', '工单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldEight', '工单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldNine', '工单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderExtendFieldTen', '工单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldOne', '工单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldTwo', '工单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldThree', '工单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldFour', '工单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldFive', '工单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldSix', '工单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldSeven', '工单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldEight', '工单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldNine', '工单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('workOrder', '/order-model/production-workorder', 'workOrderMaterialExtendFieldTen', '工单物料扩展字段10');
-- 采购需求
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldOne', '采购需求单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldTwo', '采购需求单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldThree', '采购需求单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldFour', '采购需求单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldFive', '采购需求单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldSix', '采购需求单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldSeven', '采购需求单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldEight', '采购需求单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldNine', '采购需求单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestExtendFieldTen', '采购需求单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldOne', '采购需求单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldTwo', '采购需求单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldThree', '采购需求单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldFour', '采购需求单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldFive', '采购需求单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldSix', '采购需求单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldSeven', '采购需求单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldEight', '采购需求单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldNine', '采购需求单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseRequest', '/supply-chain-collaboration/procurement-management/purchasing-demand', 'requestMaterialExtendFieldTen', '采购需求单物料扩展字段10');
-- 采购订单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldOne', '采购订单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldTwo', '采购订单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldThree', '采购订单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldFour', '采购订单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldFive', '采购订单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldSix', '采购订单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldSeven', '采购订单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldEight', '采购订单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldNine', '采购订单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseExtendFieldTen', '采购订单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldOne', '采购订单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldTwo', '采购订单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldThree', '采购订单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldFour', '采购订单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldFive', '采购订单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldSix', '采购订单物料扩展字段');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldSeven', '采购订单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldEight', '采购订单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldNine', '采购订单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseOrder', '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseMaterialExtendFieldTen', '采购订单物料扩展字段10');
-- 采购收料单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldOne', '采购收料单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldTwo', '采购收料单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldThree', '采购收料单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldFour', '采购收料单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldFive', '采购收料单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldSix', '采购收料单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldSeven', '采购收料单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldEight', '采购收料单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldNine', '采购收料单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptExtendFieldTen', '采购收料单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldOne', '采购收料单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldTwo', '采购收料单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldThree', '采购收料单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldFour', '采购收料单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldFive', '采购收料单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldSix', '采购收料单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldSeven', '采购收料单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldEight', '采购收料单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldNine', '采购收料单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('purchaseReceipt', '/supply-chain-collaboration/procurement-management/delivery-order', 'receiptMaterialExtendFieldTen', '采购收料单物料扩展字段10');
-- 委外订单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldOne', '委外订单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldTwo', '委外订单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldThree', '委外订单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldFour', '委外订单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldFive', '委外订单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldSix', '委外订单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldSeven', '委外订单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldEight', '委外订单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldNine', '委外订单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderExtendFieldTen', '委外订单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldOne', '委外订单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldTwo', '委外订单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldThree', '委外订单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldFour', '委外订单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldFive', '委外订单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldSix', '委外订单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldSeven', '委外订单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldEight', '委外订单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldNine', '委外订单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('subcontractOrder', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'orderMaterialExtendFieldTen', '委外订单物料扩展字段10');
-- 生产订单用料清单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldOne', '生产订单用料清单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldTwo', '生产订单用料清单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldThree', '生产订单用料清单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldFour', '生产订单用料清单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldFive', '生产订单用料清单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldSix', '生产订单用料清单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldSeven', '生产订单用料清单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldEight', '生产订单用料清单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldNine', '生产订单用料清单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListExtendFieldTen', '生产订单用料清单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldOne', '生产订单用料清单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldTwo', '生产订单用料清单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldThree', '生产订单用料清单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldFour', '生产订单用料清单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldFive', '生产订单用料清单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldSix', '生产订单用料清单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldSeven', '生产订单用料清单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldEight', '生产订单用料清单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldNine', '生产订单用料清单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('productMaterialsList', '/order-model/production-materials', 'materialListMaterialExtendFieldTen', '生产订单用料清单物料扩展字段10');
-- 采购退料
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldOne', '采购退料单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldTwo', '采购退料单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldThree', '采购退料单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldFour', '采购退料单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldFive', '采购退料单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldSix', '采购退料单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldSeven', '采购退料单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldEight', '采购退料单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldNine', '采购退料单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturnExtendFieldTen', '采购退料单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldOne', '采购退料单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldTwo', '采购退料单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldThree', '采购退料单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldFour', '采购退料单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldFive', '采购退料单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldSix', '采购退料单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldSeven', '采购退料单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldEight', '采购退料单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldNine', '采购退料单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('returnOrder', '/supply-chain-collaboration/procurement-management/return-order', 'returnMaterialExtendFieldTen', '采购退料单物料扩展字段10');
-- 出货申请单
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldOne', '出货申请单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldTwo', '出货申请单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldThree', '出货申请单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldFour', '出货申请单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldFive', '出货申请单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldSix', '出货申请单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldSeven', '出货申请单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldEight', '出货申请单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldNine', '出货申请单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationExtendFieldTen', '出货申请单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldOne', '出货申请单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldTwo', '出货申请单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldThree', '出货申请单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldFour', '出货申请单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldFive', '出货申请单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldSix', '出货申请单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldSeven', '出货申请单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldEight', '出货申请单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldNine', '出货申请单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`order_type`, `route`, `field_code`, `field_name`) VALUES ('deliveryApplication', '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplicationMaterialExtendFieldTen', '出货申请单物料扩展字段10');

-- 插入上游游关系映射表数据
Truncate Table `dfs_form_up_down_stream_relationship_config`;
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'ams', 'productOrder', '生产订单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'ams', 'purchaseRequest', '采购需求单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'dfs', 'deliveryApplication', '出货申请');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'ams', 'subcontractOrder', '委外订单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'wms', 'saleReturnOrder', '销售退货单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'ams', 'purchaseOrder', '采购订单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'wms', 'saleOut', '销售出库单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'saleOrder', '销售订单', 'wms', 'salesReturnReceipt', '销售退货入库单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productOrder', '生产订单', 'dfs', 'workOrder', '生产工单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productOrder', '生产订单', 'ams', 'purchaseRequest', '采购需求单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productOrder', '生产订单', 'ams', 'productMaterialsList', '生产订单用料清单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productOrder', '生产订单', 'wms', 'productionIn', '生产入库单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productOrder', '生产订单', 'ams', 'subcontractOrder', '委外订单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productMaterialsList', '生产订单用料清单', 'wms', 'outputPickingProduct', '领料出库单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productMaterialsList', '生产订单用料清单', 'wms', 'workOrderSupplement', '生产补料出库');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'productMaterialsList', '生产订单用料清单', 'wms', 'transferOrder', '调拨单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'dfs', 'workOrder', '生产工单', 'dfs', 'workOrderMaterialList', '生产工单用料清单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'dfs', 'workOrder', '生产工单', 'wms', 'outputPickingProduct', '生产领料出库单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'dfs', 'workOrder', '生产工单', 'wms', 'workOrderSupplement', '生产补料出库单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'dfs', 'workOrder', '生产工单', 'wms', 'productionReturnReceipt', '生产退料入库单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'dfs', 'workOrder', '生产工单', 'wms', 'productionIn', '生产入库单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'dfs', 'workOrderMaterialsList', '生产工单用料清单', 'wms', 'takeOutOutbound', '领料出库单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'dfs', 'takeOutApplication', '领料申请单', 'wms', 'takeOutOutbound', '领料出库单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseRequest', '采购需求', 'ams', 'purchaseOrder', '采购订单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseOrder', '采购订单', 'ams', 'purchaseReceipt', '采购收料');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseOrder', '采购订单', 'wms', 'purchaseIn', '采购入库');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseOrder', '采购订单', 'qms', 'supplierBom', '产品检验');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseReceipt', '采购收料', 'wms', 'requestStockInAndOut', '采购入库单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseReceipt', '采购收料', 'wms', 'returnOrder', '采购退料');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseReceipt', '采购收料', 'qms', 'inspectOrder', '检验单');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseReceipt', '采购收料', 'qms', 'incomingInspection', '产品检验');
-- INSERT INTO `dfs_form_up_down_stream_relationship_config`(`id`, `upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES (NULL, 'ams', 'purchaseReturnApplication', '采购退料申请单', 'wms', 'purchaseReturnOut', '采购退料出库单');

-- 生产订单用料清单下推领料出库单
update dfs_order_push_down_config set name = '领料出库单'  where full_path_code = 'production.productMaterialsListPushDownConfig.outputPickingProduct';
delete  from  dfs_order_push_down_item   where config_path = 'production.productMaterialsListPushDownConfig.outputPickingProduct';
delete  from  dfs_dict   where type = 'production.productMaterialsListPushDownConfig.outputPickingProduct';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage';
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', '通用下推', 'production.productMaterialsListPushDownConfig.outputPickingProduct', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'production.productMaterialsListPushDownConfig.outputPickingProduct', 'show');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productMaterialsListPushDownConfig.outputPickingProduct',  '下推领料出库单', 'RULE', 1, 1, 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.enable', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.originalOrderStates', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select-multiple', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.isSupportMaterialLineReversePushDown', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.description', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.targetOrderStates', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0,1]', '0', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.applicationId', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.url', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut/add?pushDownOrigin=productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.sourceOrderType', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.targetOrderType', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"outputPickingProduct\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.enable', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.originalOrderStates', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select-multiple', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.isSupportMaterialLineReversePushDown', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.description', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.targetOrderStates', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0,1]', '0', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.applicationId', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.url', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut/add?pushDownOrigin=productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.sourceOrderType', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch.targetOrderType', 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"outputPickingProduct\"', NULL);

-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productMaterialsListPushDownConfig.outputPickingProduct.pushBatch';


UPDATE `dfs_business_config_value` SET `option_values` = '[{\"value\":\"all\",\"label\":\"全入库\"},{\"value\":\"part\",\"label\":\"采样入库\"},{\"value\":\"none\",\"label\":\"不入库\"}]' WHERE `value_full_path_code` = 'device.iotData.save';
UPDATE `dfs_business_config_value` SET `value` = '\"all\"' WHERE `value_full_path_code` = 'device.iotData.save' AND `value` = 'true';
UPDATE `dfs_business_config_value` SET `value` = '\"none\"' WHERE `value_full_path_code` = 'device.iotData.save' AND `value` = 'false';
-- 生产订单用料清单下推生产补料出库
update dfs_order_push_down_config set name = '生产补料出库'  where full_path_code = 'production.productMaterialsListPushDownConfig.workOrderSupplement';
delete  from  dfs_order_push_down_item   where config_path = 'production.productMaterialsListPushDownConfig.workOrderSupplement';
delete  from  dfs_dict   where type = 'production.productMaterialsListPushDownConfig.workOrderSupplement';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage';
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', '通用下推', 'production.productMaterialsListPushDownConfig.workOrderSupplement', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'production.productMaterialsListPushDownConfig.workOrderSupplement', 'show');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productMaterialsListPushDownConfig.workOrderSupplement',  '下推生产补料出库单', 'RULE', 1, 1, 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.enable', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.originalOrderStates', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select-multiple', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.isSupportMaterialLineReversePushDown', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.description', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.targetOrderStates', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0,1]', '0', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.applicationId', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderSupplement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.url', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement/add?pushDownOrigin=productMaterialsList \"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.sourceOrderType', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.targetOrderType', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderSupplement\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.enable', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.originalOrderStates', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select-multiple', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.isSupportMaterialLineReversePushDown', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.description', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.targetOrderStates', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0,1]', '0', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.applicationId', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderSupplement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.url', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement/add?pushDownOrigin=productMaterialsList \"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.sourceOrderType', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch.targetOrderType', 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderSupplement\"', NULL);

-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productMaterialsListPushDownConfig.workOrderSupplement.pushBatch';

-- 生产订单用料清单下推调拨单
update dfs_order_push_down_config set name = '调拨单'  where full_path_code = 'production.productMaterialsListPushDownConfig.transferOrder';
delete  from  dfs_order_push_down_item   where config_path = 'production.productMaterialsListPushDownConfig.transferOrder';
delete  from  dfs_dict   where type = 'production.productMaterialsListPushDownConfig.transferOrder';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage';
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productMaterialsListPushDownConfig.transferOrder.pushBatch', '通用下推', 'production.productMaterialsListPushDownConfig.transferOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'production.productMaterialsListPushDownConfig.transferOrder', 'show');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productMaterialsListPushDownConfig.transferOrder',  '下推调拨单', 'RULE', 1, 1, 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.enable', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.originalOrderStates', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select-multiple', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.isSupportMaterialLineReversePushDown', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.description', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.targetOrderStates', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0,1]', '0', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.applicationId', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.cougnyManage.allocation\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.url', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/cougnyManage/allocation/add?pushDownOrigin=productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.sourceOrderType', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.targetOrderType', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"transferOrder\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.enable', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.originalOrderStates', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select-multiple', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.isSupportMaterialLineReversePushDown', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.description', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.targetOrderStates', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0,1]', '0', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.applicationId', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.cougnyManage.allocation\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.url', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/cougnyManage/allocation/add?pushDownOrigin=productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.sourceOrderType', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch.targetOrderType', 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"transferOrder\"', NULL);

-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productMaterialsListPushDownConfig.transferOrder.pushBatch';

-- 审批批量配置
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('12002010', '批量配置', 'approve.config:batchConfig', NULL, NULL, NULL, NULL, '2024-10-24 09:25:13', 'enable', 'POST', '12002', 2, 1, 0, '/documents-config/approveConfig', 1, NULL, '', 1);
call init_new_role_permission('12002%');

-- 工艺复制业务配置重命名
UPDATE `dfs_business_config` SET `description` = '将工艺关联的工序参数复制到新的工艺上' WHERE `full_path_code` = 'design.craftConfig.craftCopyConf';
UPDATE `dfs_business_config_value` SET `value_name` = '是否复制工序参数' WHERE `value_full_path_code` = 'design.craftConfig.craftCopyConf.enable';


--  工位类型按钮
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11306060', '工位类型主按钮', 'station.type:main', 'enable', 'DELETE', '11306', 2, 1, 1, '/factory-model/station', 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11306070', '工位类型新增', 'station.type:add', 'enable', 'DELETE', '11306', 2, 1, 1, '/factory-model/station', 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11306080', '工位类型编辑', 'station.type:update', 'enable', 'DELETE', '11306', 2, 1, 1, '/factory-model/station', 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11306090', '工位类型删除', 'station.type:delete', 'enable', 'DELETE', '11306', 2, 1, 1, '/factory-model/station', 1, NULL, '', 1);
call init_new_role_permission('113060%');


-- 销售订单下推销售出库单
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.saleOut.push', '通用下推', 'saleOrder.pushDownConfig.saleOut', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.saleOut.push.originalOrderStates', 'saleOrder.pushDownConfig.saleOut.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.saleOut.push.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.saleOut.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.saleOut.push.description', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.saleOut.push.targetOrderState', 'saleOrder.pushDownConfig.saleOut.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.saleOut.push.applicationId', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesIssueDoc\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.saleOut.push.url', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesIssueDoc/add?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.saleOut.push.sourceOrderType', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.saleOut.push.targetOrderType', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleInAndOut\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.saleOut',  '下推销售出库单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.saleOut.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.saleOut.push.originalOrderStates', 'saleOrder.pushDownConfig.saleOut.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.saleOut.push.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.saleOut.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.saleOut.push.description', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.saleOut.push.targetOrderState', 'saleOrder.pushDownConfig.saleOut.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.saleOut.push.applicationId', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesIssueDoc\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.saleOut.push.url', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesIssueDoc/add?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.saleOut.push.sourceOrderType', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.saleOut.push.targetOrderType', 'saleOrder.pushDownConfig.saleOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleInAndOut\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.saleOut.push';

-- 销售订单下推销售退货入库单
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.salesReturnReceipt.push', '通用下推', 'saleOrder.pushDownConfig.salesReturnReceipt', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.salesReturnReceipt.push.originalOrderStates', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.salesReturnReceipt.push.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.salesReturnReceipt.push.description', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.salesReturnReceipt.push.targetOrderState', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.salesReturnReceipt.push.applicationId', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.salesReturnReceipt.push.url', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc/add?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.salesReturnReceipt.push.sourceOrderType', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.salesReturnReceipt.push.targetOrderType', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"salesReturnReceipt\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.salesReturnReceipt',  '下推销售退货入库单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.salesReturnReceipt.push.originalOrderStates', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.salesReturnReceipt.push.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.salesReturnReceipt.push.description', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.salesReturnReceipt.push.targetOrderState', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.salesReturnReceipt.push.applicationId', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.salesReturnReceipt.push.url', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc/add?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.salesReturnReceipt.push.sourceOrderType', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.salesReturnReceipt.push.targetOrderType', 'saleOrder.pushDownConfig.salesReturnReceipt.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"salesReturnReceipt\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.salesReturnReceipt.push';

-- 生产订单下推生产入库单
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productOrderPushDownConfig.productionIn.push', '通用下推', 'production.productOrderPushDownConfig.productionIn', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.productionIn.push.originalOrderStates', 'production.productOrderPushDownConfig.productionIn.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.productionIn.push.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.productionIn.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.productionIn.push.description', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.productionIn.push.targetOrderState', 'production.productOrderPushDownConfig.productionIn.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'production.productOrderPushDownConfig.productionIn.push.applicationId', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderComplete\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.productionIn.push.url', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete/add?pushDownOrigin=productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.productionIn.push.sourceOrderType', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.productionIn.push.targetOrderType', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionIn\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productOrderPushDownConfig.productionIn',  '下推生产入库单', 'RULE', 1, 1, 'production.productOrderPushDownConfig.productionIn.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.productionIn.push.originalOrderStates', 'production.productOrderPushDownConfig.productionIn.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.productionIn.push.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.productionIn.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.productionIn.push.description', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.productionIn.push.targetOrderState', 'production.productOrderPushDownConfig.productionIn.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'production.productOrderPushDownConfig.productionIn.push.applicationId', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderComplete\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.productionIn.push.url', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete/add?pushDownOrigin=productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.productionIn.push.sourceOrderType', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.productionIn.push.targetOrderType', 'production.productOrderPushDownConfig.productionIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionIn\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.productionIn.push';

-- 采购收料单下推采购入库单
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', '通用下推', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.description', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.targetOrderState', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.applicationId', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.url', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage/add?pushDownOrigin=purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"requestStockInAndOut\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut',  '下推采购入库单', 'RULE', 1, 1, 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.description', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.targetOrderState', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.applicationId', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.url', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage/add?pushDownOrigin=purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.sourceOrderType', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReceipt\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push.targetOrderType', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"requestStockInAndOut\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.push';


-- 设备检查项目组 按钮权限移动到 检查项里
call route_rm('/equipment-management/equipment-checked/team');
call permission_mv('/equipment-management/equip-checked/equipCheckProject', '/equipment-management/equipment-checked/team');
update sys_permissions set name = CONCAT('项目组',name) WHERE path like 'equipment.checked.team%' and name not like '项目组%';

-- 模型初始化->系统还原
update sys_permissions set name ='系统还原' where path='/system_settings/init-config';
update sys_route set title='系统还原' where path='/system_settings/init-config';

-- 工序控制阀值类型
update dfs_procedure_def_controller_config set flow_timeout_threshold_configuration_unit_type = 'percentage' where flow_timeout_threshold_configuration_unit_type = 'percent';
update dfs_procedure_def_controller_config set production_timeout_threshold_configuration_unit = 'percentage' where production_timeout_threshold_configuration_unit = 'percent';

-- 删除工艺模板的导出权限（泽杰和恒秋说没用到）
DELETE FROM `sys_permissions` WHERE `id` = '10809130';
DELETE FROM `sys_role_permission` WHERE `permission_id` = '10809130';

-- 修改业务配置中相关的仓库下推的状态接口
UPDATE `dfs_business_config_value` SET `option_values` = '[4,5]', `value` = '[4,5]' WHERE `value_full_path_code` = 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage.originalOrderStates';
UPDATE `dfs_business_config_value` SET `option_values` = '[4,5]', `value` = '[4,5]' WHERE `value_full_path_code` = 'delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage.originalOrderStates';

-- 单据回退路由不知为何遗失
call route_add_l2('/task-center', '/documents-config/return-order', '单据回退');
update sys_route set module_name = 'dfs'WHERE path = '/documents-config/return-order';

-- 删除生产订单用料清单下推仓库原配置
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig';
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.outputPickingProduct';
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.workOrderSupplement';
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.transferOrder';
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage';
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage';
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage';

-- 删除采购收料表单配置多余字段
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.editByCreate' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.list.material' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.list.order' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.add' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.editByRelease' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.editByFinish' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.editByClosed' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.editByCancel' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderType' ;
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` = 'purchaseReceiptOrder.detail' and `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and field_code = 'orderTypeName' ;

-- 工单创建态表单配置-派工状态不能填写
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `is_edit` = 0, `need_gray` = 1, `edit_gray` = 1 WHERE `full_path_code` = 'workOrder.editByCreate' and `field_code` = 'assignmentState';

--  业务单元更新历史数据
UPDATE `dfs_work_order` SET `business_unit_code` = '0000' WHERE `business_unit_code` IS NULL;
UPDATE `dfs_work_order` SET `business_unit_name` = '无' WHERE `business_unit_name` IS NULL;
UPDATE `dfs_stock_delivery_material` SET `business_unit_code` = '0000' WHERE `business_unit_code` IS NULL;
UPDATE `dfs_stock_delivery_material` SET `business_unit_name` = '无' WHERE `business_unit_name` IS NULL;
UPDATE `dfs_delivery_application_material` SET `business_unit_code` = '0000' WHERE `business_unit_code` IS NULL;
UPDATE `dfs_delivery_application_material` SET `business_unit_name` = '无' WHERE `business_unit_name` IS NULL;
UPDATE `dfs_work_order_material_list_material` SET `business_unit_code` = '0000' WHERE `business_unit_code` IS NULL;
UPDATE `dfs_work_order_material_list_material` SET `business_unit_name` = '无' WHERE `business_unit_name` IS NULL;


-- 工单的表单配置已入库数应该取inventoryQuantity字段
UPDATE `dfs_form_field_config` SET `field_code` = 'inventoryQuantity' WHERE `field_code` = 'inputCount' AND `full_path_code` in ('workOrder.list', 'workOrder.edit', 'workOrder.detail');
UPDATE `dfs_form_field_config` SET `field_name` = 'inventoryQuantity' WHERE `field_name` = 'inputCount' AND `full_path_code` in ('workOrder.list', 'workOrder.edit', 'workOrder.detail');
UPDATE `dfs_form_field_rule_config` SET `field_code` = 'inventoryQuantity' WHERE `field_code` = 'inputCount' AND `full_path_code` in ('workOrder.list', 'workOrder.detail');
UPDATE `dfs_form_field_rule_config` SET `field_code` = 'inventoryQuantity' WHERE `field_code` = 'inputCount' AND `field_name_full_path_code` in ('workOrder.edit');

-- 部门新增导入权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('12101050', '导入管理', 'organization:import', NULL, NULL, NULL, NULL, '2024-11-20 15:26:10', 'enable', 'DELETE', '12101', 2, 1, 1, '/base-info/organization', 1, NULL, '', 1);
call init_new_role_permission('12101050');

-- 更新表单配置备注
UPDATE `dfs_form_field_rule_config` SET `remark` = '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)' WHERE `remark` = '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突位置：配置中心-->系统配置-->业务配置配置-->物料特征参数配置)';
UPDATE `dfs_form_field_rule_config` SET `remark` = '该字段和敏感字段配置冲突，请以敏感字段配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：角色业务权限，位置：权限分配-->单据敏感字段权限维护)' WHERE `remark` = '该字段和敏感字段配置冲突，请以敏感字段配置为准，此字段置灰，仅允许重命名 (冲突位置：人员信息-->角色权限-->权限分配-->单据敏感字段权限维护)';
-- 配置加载
INSERT INTO sys_permissions(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
 VALUES ('12209080', '配置加载', 'init.config:configLoading', NULL, NULL, NULL, NULL, '2024-10-24 09:25:13', 'enable', 'GET', '12209', 2, 1, 0, '/system_settings/init-config', 1, NULL, '', 1);
call init_new_role_permission('12209080');
update sys_permissions set parent_path = '/system_settings/init-config' where parent_id = '12209';
call init_new_role_permission('12209%');

-- 调整采购需求单-参考数量的表单配置
UPDATE `dfs_form_field_config` SET `field_code` = 'referenceQuantity' WHERE `field_code` = 'quantity' AND `full_path_code` in ('purchaseRequestOrder.list');
UPDATE `dfs_form_field_config` SET `field_name` = 'referenceQuantity' WHERE `field_name` = 'quantity' AND `full_path_code` in ('purchaseRequestOrder.list');
UPDATE `dfs_form_field_config` SET `field_code` = 'referenceQuantity' WHERE `field_code` = 'num' AND `full_path_code` in ('purchaseRequestOrder.edit');
UPDATE `dfs_form_field_config` SET `field_name` = 'referenceQuantity' WHERE `field_name` = 'num' AND `full_path_code` in ('purchaseRequestOrder.edit');

UPDATE `dfs_form_field_rule_config` SET `field_code` = 'referenceQuantity' WHERE `field_code` = 'quantity' AND `full_path_code` in ('purchaseRequestOrder.list');
UPDATE `dfs_form_field_rule_config` SET `field_code` = 'referenceQuantity' WHERE `field_code` = 'num' AND `field_name_full_path_code` in ('purchaseRequestOrder.edit');

-- 工单表单配置调整
UPDATE `dfs_form_field_rule_config` SET `value_gray` = 0 WHERE `full_path_code` = 'workOrder.detail' AND `field_code` like 'workOrderExtend%';
UPDATE `dfs_form_field_rule_config` SET `value_gray` = 0 WHERE `full_path_code` = 'workOrder.detail' AND `field_code` like 'workOrderMaterialExtend%';

-- 采购收料表单配置调整
DELETE FROM `dfs_form_field_config` WHERE `field_code` = 'receiveMaterialSum';
DELETE FROM `dfs_form_field_rule_config` WHERE `field_code` = 'receiveMaterialSum';
UPDATE `dfs_form_field_config` SET `field_name` = '实际数量' WHERE `type_code` = 'sysField' and `field_code` = 'amount' AND `full_path_code` in ('purchaseReceiptOrder.list.material');

-- 删除生产工单用料清单的下推权限
DELETE FROM `sys_permissions` WHERE `id` = '10012060';
DELETE FROM `sys_role_permission` WHERE `permission_id` = '10012060';


-- 角色-小程序关系
CREATE TABLE IF NOT EXISTS `sys_role_app` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `role_name` varchar(255) NOT NULL COMMENT '角色名称',
    `application_id` varchar(512) NOT NULL COMMENT '小程序applicationId',
    `description` varchar(255) DEFAULT NULL COMMENT '描述',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uni` (`role_name`,`application_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COMMENT='角色-小程序关系 全量表';

INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('采购', 'yelink.label.print', '标签打印');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.workorder.report', '订单报工');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.workOrder.report.com', '工单报工');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.scanCodeReportWork.com', '扫码报工');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.kangjuren.general.machine', '标准工位机');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.label.print', '标签打印');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.supplementaryRecord.machine', '补录工位机');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.kangjuren.esop.machine', 'ESOP展示');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'package-general-machine', '包装工位机（通用）');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'jun-lan', 'SMT上料防错');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.work.attendance', '打卡上工');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.station.qualitity.inspection', '工位质检(工单)');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.kangjuren.qualityInspection.machine', '质检工位机');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.maintenance.app', '质检返工(单品)');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.station.maintenance.app', '质检返工(工单)');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.kangjuren.maintenance.machine', '维修工位机');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.line.report', '产线报工');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'productHomework', '生产作业');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink.device.maintaince.com', '设备维修');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'alarm-work', '告警作业');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('生产', 'yelink-alarmHandle', '告警处理');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('仓库', 'yelink.label.print', '标签打印');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('质检', 'yelink.label.print', '标签打印');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('质检', 'yelink.station.qualitity.inspection', '工位质检(工单)');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('质检', 'yelink.kangjuren.qualityInspection.machine', '质检工位机');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('质检', 'yelink.maintenance.app', '质检返工(单品)');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('质检', 'yelink.station.maintenance.app', '质检返工(工单)');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('质检', 'yelink.kangjuren.maintenance.machine', '维修工位机');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('质检', 'quality-inspection', '产品质检');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('财务', 'yelink.label.print', '标签打印');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('设备', 'yelink.device.maintaince.com', '设备维修');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('设备', 'alarm-work', '告警作业');
INSERT INTO `sys_role_app` (`role_name`, `application_id`, `description`) VALUES ('设备', 'yelink-alarmHandle', '告警处理');

-- 新增查询精制小程序接口
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'getJzAppV2', '查询精制app列表V2', 'GET', '/jingzhi/app/v2');

-- 业务单元插入默认记录
INSERT INTO `dfs_business_unit`(`code`, `name`, `state`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('0000', '无', 'RELEASED', 'admin', 'admin', now(), now());

-- 权限导入导出
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('12103060', '导出', 'role.permission:export', NULL, NULL, NULL, NULL, '2024-11-20 15:26:10', 'enable', 'GET', '12103', 2, 1, 1, '/base-info/role-permission', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('12103070', '下载导入模板', 'role.permission:defaultImportTemplate', NULL, NULL, NULL, NULL, '2024-11-20 15:26:10', 'enable', 'GET', '12103', 2, 1, 1, '/base-info/role-permission', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('12103080', '导入数据', 'role.permission:import', NULL, NULL, NULL, NULL, '2024-11-20 15:26:10', 'enable', 'GET', '12103', 2, 1, 1, '/base-info/role-permission', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('12103090', '查看导入日志', 'role.permission:importLog', NULL, NULL, NULL, NULL, '2024-11-20 15:26:10', 'enable', 'GET', '12103', 2, 1, 1, '/base-info/role-permission', 1, NULL, '', 1);
call init_new_role_permission('12103060');
call init_new_role_permission('12103070');
call init_new_role_permission('12103080');
call init_new_role_permission('12103090');

update sys_route set title= '条码中心' WHERE  path ='/identification-center';
update sys_route set title= '物料条码' WHERE  path ='/identification-center/material-identification';
update sys_permissions set name= '条码中心' WHERE  id ='131';
update sys_permissions set name= '物料条码' WHERE  id ='13101';

-- 表单重命名表新增唯一索引
call proc_add_unique_index('dfs_form_field_config','full_path_code,field_code,type_code','unique_idx');

UPDATE `dfs_form_field_config` SET `field_name` = '' WHERE `field_name` is NULL AND `type_code` = 'customField';

call proc_modify_column(
        'dfs_form_field_config',
        'field_name',
        'ALTER TABLE `dfs_form_field_config` CHANGE COLUMN `field_name` `field_name` varchar(255) DEFAULT "" COMMENT ''字段名称''');

-- 脏数据处理
UPDATE `dfs_work_order` a, `dfs_work_center` b SET a.`work_center_name` = b.`name`
WHERE a.`work_center_id` = b.`id`;

-- 表单配置--报工小程序新增、修改报工取消置灰必填选项
UPDATE `dfs_form_field_rule_config` SET `need_gray` = 0 WHERE `field_code` in ('reportFieldOne','reportFieldTwo','reportFieldThree','reportFieldFour','reportFieldFive') AND `full_path_code` in ('orderReportApp.addReport', 'orderReportApp.editReport', 'workOrderReport.addReport', 'workOrderReport.editReport');


-- 处理可能已经发生的脏数据，防止生产工单用料清单报错
UPDATE `dfs_work_order_material_list_material` SET `bom_denominator` = 1 WHERE `bom_denominator` IS NULL;
UPDATE `dfs_work_order_material_list_material` SET `bom_numerator` = 1 WHERE `bom_numerator` IS NULL;