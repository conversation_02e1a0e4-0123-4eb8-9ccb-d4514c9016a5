-- DDL
call proc_add_column_index(
        'dfs_import_data_record',
        'import_time',
        'import_time');

call proc_add_column_index(
        'dfs_import_data_record',
        'import_type',
        'import_type');

-- 下推配置数新增字段
call proc_add_column(
        'dfs_order_push_down_config',
        'type_code',
        'ALTER TABLE `dfs_order_push_down_config` ADD COLUMN `type_code` varchar(255) DEFAULT NULL COMMENT ''单据类型编码''');
call proc_add_column(
        'dfs_order_push_down_config',
        'app_id',
        'ALTER TABLE `dfs_order_push_down_config` ADD COLUMN `app_id` varchar(255) DEFAULT NULL COMMENT ''appId''');
call proc_add_column(
        'dfs_order_push_down_config',
        'url',
        'ALTER TABLE `dfs_order_push_down_config` ADD COLUMN `url` varchar(1000) DEFAULT NULL COMMENT ''前端路由''');

CREATE TABLE IF NOT EXISTS `dfs_manage_report_type` (
    `type_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '报表类型id',
    `type_name` varchar(255) NOT NULL COMMENT '报表类型名称',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_by` varchar(255) NOT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表类型';

CREATE TABLE IF NOT EXISTS `dfs_manage_report_type_relation` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `report_id` int(11) NOT NULL COMMENT '报表id',
    `type_id` int(11) NOT NULL COMMENT '报表类型id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_code` (`report_id`,`type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表和类型关联表';

call proc_add_column(
        'dfs_manage_report',
        'subhead',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `subhead`  varchar(255) NULL COMMENT ''副标题'' AFTER `report_name`;');
call proc_add_column(
        'dfs_manage_report',
        'default_inner',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `default_inner`  tinyint(2) NULL DEFAULT 0 COMMENT ''是否内置，1：内置，0：否'' AFTER `state`;');

call proc_add_column(
        'dfs_manage_source_param',
        'option_value_type',
        'ALTER TABLE `dfs_manage_source_param` ADD COLUMN `option_value_type`  varchar(50) NULL COMMENT ''可选值来源类型：api数据集:apiSource等'';');
call proc_add_column(
        'dfs_manage_source_param',
        'option_value',
        'ALTER TABLE `dfs_manage_source_param` ADD COLUMN `option_value`  text NULL COMMENT ''可选值'';');


CREATE TABLE IF NOT EXISTS `dfs_file_import_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `code` varchar(50) NOT NULL COMMENT '导入类型名',
    `enable` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否启用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='计数表';

-- 工单报工
call proc_add_column_index(
        'dfs_report_line',
        'type',
        'type');
call proc_add_column_index(
        'dfs_report_line',
        'finish_count',
        'finish_count');

-- 表结构增加外部映射字段
call proc_add_column(
        'dfs_table_config',
        'field_mapping',
        'ALTER TABLE `dfs_table_config` ADD COLUMN `field_mapping` varchar(255) NULL COMMENT ''映射外部字段''');

call proc_add_column(
        'dfs_target_model',
        'api_transform_id',
        'ALTER TABLE `dfs_target_model` ADD COLUMN `api_transform_id` int(11) DEFAULT NULL COMMENT ''api转换模型id''');

CREATE TABLE `dfs_api_transform` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(255) DEFAULT NULL COMMENT '名称',
    `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求路径',
    `request_method` varchar(255) DEFAULT NULL COMMENT '请求方式',
    `request_header` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求头',
    `request_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '请求体',
    `transform_script` text COMMENT '转换模板',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `target_model_id` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='api转换';


-- DML

-- 报工业务配置, 隐藏数量为0的记录
INSERT INTO `dfs_business_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('hideAutoZero', '隐藏设备报工数为0的记录', 'production.workOrderVerificationReportConfig.reportGeneralConfig.hideAutoZero', 'production.workOrderVerificationReportConfig.reportGeneralConfig', '开启后将隐藏设备自动报工且数量为0的记录', 'yelinkoncall', 'admin', '2023-09-08 06:15:49', '2024-03-19 11:32:36');
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否开启', 'production.workOrderVerificationReportConfig.reportGeneralConfig.hideAutoZero.enable', 'production.workOrderVerificationReportConfig.reportGeneralConfig.hideAutoZero', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);


-- 表结构增加外部映射字段
call proc_add_column(
        'dfs_delivery_application_material',
        'sales_quantity',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `sales_quantity` double(11,3) DEFAULT 0.000 COMMENT ''销售数量''');
-- 销售订单下推采购订单配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.purchaseOrder.pushBatch', '下推采购订单(批量)', 'saleOrder.pushDownConfig.purchaseOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'saleOrder.pushDownConfig.purchaseOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'originalOrderStates', '源单状态', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.originalOrderStates', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.targetOrderStates', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'mergeSampleMaterial', '是否合并相同物料', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleMaterial', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'mergeSampleSupplier', '是否合并相同供应商', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleSupplier', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.appId', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.dfs.supply.procurement-management\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.url', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-list?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.sourceOrderType', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.targetOrderType', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.description', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- 销售订单下推采购订单内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.purchaseOrder',  '下推采购订单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'originalOrderStates', '源单状态(多选)', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.originalOrderStates', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.targetOrderStates', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'table', null, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'mergeSampleMaterial', '是否合并相同物料', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleMaterial', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'mergeSampleSupplier', '是否合并相同供应商',  'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleSupplier', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.appId', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.dfs.supply.procurement-management\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.url', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch','input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-list?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.sourceOrderType', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.targetOrderType', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch','input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.description', 'saleOrder.pushDownConfig.purchaseOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.purchaseOrder.pushBatch';



-- ocs打印类型
INSERT INTO dfs_dict (id, code, name, `type`, url, unit, des, value, p_code, pid, create_time, update_time, create_by, update_by, quality_level, is_process_assembly) VALUES(NULL, 'productFlowCode', '生产流水码', 'ocsPrintType', NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-07 17:55:43', '2024-02-26 17:35:21', 'admin', 'admin', NULL, 0);
INSERT INTO dfs_dict (id, code, name, `type`, url, unit, des, value, p_code, pid, create_time, update_time, create_by, update_by, quality_level, is_process_assembly) VALUES(NULL, 'finished', '生产批次号', 'ocsPrintType', NULL, NULL, NULL, NULL, NULL, NULL, '2024-01-07 17:55:43', '2024-02-28 09:36:53', 'admin', 'admin', NULL, 0);

-- 下推类型树赋予默认值
UPDATE `dfs_order_push_down_config` SET `type_code` = 'saleOrder' WHERE `full_path_code` = 'saleOrder.pushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'productOrder' WHERE `full_path_code` = 'saleOrder.pushDownConfig.productOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseRequest' WHERE `full_path_code` = 'saleOrder.pushDownConfig.purchaseRequest';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'deliveryApplication' WHERE `full_path_code` = 'saleOrder.pushDownConfig.deliveryApplication';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'saleInAndOut' WHERE `full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'subcontractOrder' WHERE `full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'saleReturnOrder' WHERE `full_path_code` = 'saleOrder.pushDownConfig.saleReturnOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseOrder' WHERE `full_path_code` = 'saleOrder.pushDownConfig.purchaseOrder';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'productOrder' WHERE `full_path_code` = 'production.productOrderPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'workOrder' WHERE `full_path_code` = 'production.productOrderPushDownConfig.workOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseRequest' WHERE `full_path_code` = 'production.productOrderPushDownConfig.purchaseRequest';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'productMaterialsList' WHERE `full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'productionIn' WHERE `full_path_code` = 'production.productOrderPushDownConfig.productionIn';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'productMaterialsList' WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'outputPickingProduct' WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.outputPickingProduct';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'workOrderSupplement' WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.workOrderSupplement';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'transferOrder' WHERE `full_path_code` = 'production.productMaterialsListPushDownConfig.transferOrder';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'workOrder' WHERE `full_path_code` = 'production.workOrderPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'takeOutApplication' WHERE `full_path_code` = 'production.workOrderPushDownConfig.takeOutApplication';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'productStockInAndOut' WHERE `full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'workOrderMaterialList' WHERE `full_path_code` = 'production.workOrderPushDownConfig.materialList';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'workOrderMaterialsList' WHERE `full_path_code` = 'production.workOrderMaterialsListPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'takeOutOutbound' WHERE `full_path_code` = 'production.workOrderMaterialsListPushDownConfig.takeOutOutbound';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'takeOutApplication' WHERE `full_path_code` = 'production.takeOutApplicationPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'takeOutOutbound' WHERE `full_path_code` = 'production.takeOutApplicationPushDownConfig.takeOutOutbound';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseRequest' WHERE `full_path_code` = 'purchase.purchaseRequestPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseOrder' WHERE `full_path_code` = 'purchase.purchaseRequestPushDownConfig.purchaseOrder';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseOrder' WHERE `full_path_code` = 'purchase.purchaseOrderPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'supplierBom' WHERE `full_path_code` = 'purchase.purchaseOrderPushDownConfig.supplierBom';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseReceipt' WHERE `full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseIn' WHERE `full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseIn';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseReceipt' WHERE `full_path_code` = 'purchase.purchaseReceiptPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'requestStockInAndOut' WHERE `full_path_code` = 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'returnOrder' WHERE `full_path_code` = 'purchase.purchaseReceiptPushDownConfig.returnOrder';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'incomingInspection' WHERE `full_path_code` = 'purchase.purchaseReceiptPushDownConfig.incomingInspection';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'inspectOrder' WHERE `full_path_code` = 'purchase.purchaseReceiptPushDownConfig.inspectOrder';

UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseReturnApplication' WHERE `full_path_code` = 'purchase.purchaseReturnApplicationPushDownConfig';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'purchaseReturnOut' WHERE `full_path_code` = 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut';

-- 更新表单配置树
UPDATE `dfs_form_config` SET `name` = '采购收料单(当前版本暂不支持配置)' WHERE `name` = '采购收料单';
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1 WHERE `full_path_code` = 'workOrder.editByInvestment' AND `field_code` = 'lineId';

-- 新增下推树
INSERT INTO `dfs_order_push_down_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`) VALUES (NULL, 'subcontractOrder', '委外订单', 'production.productOrderPushDownConfig.subcontractOrder', 'production.productOrderPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'subcontractOrder', NULL, NULL);


-- 销售订单按工艺路线下推委外订单
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', '按工艺路线下推委外订单', 'saleOrder.pushDownConfig.subcontractOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.originalOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.targetOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.bomSplitTypes', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"notSplit\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'subProcedureType', '委外工序类型', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.subProcedureType', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/api/procedures/outsourcing/procedure/enum', 'get', NULL, 'code,name', '[1,2]', '[1,2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.appId', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.url', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.sourceOrderType', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.targetOrderType', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.subcontractOrder',  '按工艺路线下推委外订单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.originalOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.targetOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.bomSplitTypes', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"notSplit\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'subProcedureType', '委外工序类型', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.subProcedureType', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/api/procedures/outsourcing/procedure/enum', 'get', NULL, 'code,name', '[1,2]', '[1,2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.appId', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.url', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.sourceOrderType', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.targetOrderType', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch';

-- 生产订单按工艺路线下推委外订单
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', '按工艺路线下推委外订单', 'production.productOrderPushDownConfig.subcontractOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'production.productOrderPushDownConfig.subcontractOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.originalOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.targetOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'subProcedureType', '委外工序类型', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.subProcedureType', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/api/procedures/outsourcing/procedure/enum', 'get', NULL, 'code,name', '[1,2]', '[1,2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.appId', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.url', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.sourceOrderType', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.targetOrderType', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productOrderPushDownConfig.subcontractOrder',  '按工艺路线下推委外订单', 'RULE', 1, 1, 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.originalOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.targetOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'subProcedureType', '委外工序类型', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.subProcedureType', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select-multiple', 'api', '/api/procedures/outsourcing/procedure/enum', 'get', NULL, 'code,name', '[1,2]', '[1,2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.appId', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.url', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.sourceOrderType', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.targetOrderType', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch';

-- 销售订单按BOM下推委外订单
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.subcontractOrder.bom', '按BOM下推委外订单', 'saleOrder.pushDownConfig.subcontractOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'saleOrder.pushDownConfig.subcontractOrder.bom.originalOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.subcontractOrder.bom.targetOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'saleOrder.pushDownConfig.subcontractOrder.bom.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.subcontractOrder.bom.bomSplitTypes', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"firstLevelBomNotFilter\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.subcontractOrder.bom.appId', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.subcontractOrder.bom.url', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.bom.sourceOrderType', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.bom.targetOrderType', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.subcontractOrder',  '按BOM下推委外订单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.subcontractOrder.bom', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'saleOrder.pushDownConfig.subcontractOrder.bom.originalOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.subcontractOrder.bom.targetOrderStates', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'saleOrder.pushDownConfig.subcontractOrder.bom.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.subcontractOrder.bom.bomSplitTypes', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"firstLevelBomNotFilter\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.subcontractOrder.bom.appId', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.subcontractOrder.bom.url', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.bom.sourceOrderType', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.subcontractOrder.bom.targetOrderType', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.subcontractOrder.bom';

-- 生产订单按BOM下推委外订单
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productOrderPushDownConfig.subcontractOrder.bom', '按BOM下推委外订单', 'production.productOrderPushDownConfig.subcontractOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'production.productOrderPushDownConfig.subcontractOrder.bom.originalOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.subcontractOrder.bom.targetOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'production.productOrderPushDownConfig.subcontractOrder.bom.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'production.productOrderPushDownConfig.subcontractOrder.bom.bomSplitTypes', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"firstLevelBomNotFilter\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.subcontractOrder.bom.appId', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.subcontractOrder.bom.url', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.bom.sourceOrderType', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.bom.targetOrderType', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productOrderPushDownConfig.subcontractOrder',  '按BOM下推委外订单', 'RULE', 1, 1, 'production.productOrderPushDownConfig.subcontractOrder.bom', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'production.productOrderPushDownConfig.subcontractOrder.bom.originalOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.subcontractOrder.bom.targetOrderStates', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'production.productOrderPushDownConfig.subcontractOrder.bom.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'production.productOrderPushDownConfig.subcontractOrder.bom.bomSplitTypes', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"firstLevelBomNotFilter\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.subcontractOrder.bom.appId', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.supply.outsourcingManagement\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.subcontractOrder.bom.url', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingOrder?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.bom.sourceOrderType', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.subcontractOrder.bom.targetOrderType', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.subcontractOrder.bom';

-- 数据集配置-维表示例数据
INSERT INTO `dfs_manage_source` (`source_code`, `source_name`, `source_type`, `source_desc`, `api_url`, `method`, `header`, `body`, `table_schema`, `table_name`, `script_type`, `trans_script`, `create_time`, `update_time`, `create_by`, `update_by`)
VALUES ('example_workOrderState', '示例_生产工单状态', 'apiBase', '', 'http://127.0.0.1:8181/api/workorders/all/state', 'get', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', '{}', NULL, NULL, 'js', 'function dataTransform(data){\n	//自定义脚本内容\n	return data.get(0).get(\'data\');\n}', NOW(), NOW(), 'admin', 'admin');

-- 报表管理相关权限修改
update sys_permissions SET `name`="下载记录" WHERE path="report.manage:taskResult";
-- /order-model/reportManage 计划调度-报表管理
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10011120', '报表类型', 'report.manage:type', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10011', '2', '1', '0', '/order-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10011130', '数据集_新增', 'report.manage.source:add', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10011', '2', '1', '0', '/order-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10011140', '数据集_编辑', 'report.manage.source:edit', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10011', '2', '1', '0', '/order-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10011150', '数据集_删除', 'report.manage.source:delete', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10011', '2', '1', '0', '/order-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10011160', '数据集_维表', 'report.manage.source:dimension', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10011', '2', '1', '0', '/order-model/reportManage', '1');
call init_new_role_permission('10011120');
call init_new_role_permission('10011130');
call init_new_role_permission('10011140');
call init_new_role_permission('10011150');
call init_new_role_permission('10011160');

-- /workorder-model/reportManage 生产作业-报表管理
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10112120', '报表类型', 'report.manage:type', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10112', '2', '1', '0', '/workorder-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10112130', '数据集_新增', 'report.manage.source:add', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10112', '2', '1', '0', '/workorder-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10112140', '数据集_编辑', 'report.manage.source:edit', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10112', '2', '1', '0', '/workorder-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10112150', '数据集_删除', 'report.manage.source:delete', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10112', '2', '1', '0', '/workorder-model/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10112160', '数据集_维表', 'report.manage.source:dimension', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10112', '2', '1', '0', '/workorder-model/reportManage', '1');
call init_new_role_permission('10112120');
call init_new_role_permission('10112130');
call init_new_role_permission('10112140');
call init_new_role_permission('10112150');
call init_new_role_permission('10112160');

-- /qualities-manage/reportManage 质量管理-报表管理
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10413120', '报表类型', 'report.manage:type', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10413', '2', '1', '0', '/qualities-manage/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10413130', '数据集_新增', 'report.manage.source:add', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10413', '2', '1', '0', '/qualities-manage/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10413140', '数据集_编辑', 'report.manage.source:edit', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10413', '2', '1', '0', '/qualities-manage/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10413150', '数据集_删除', 'report.manage.source:delete', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10413', '2', '1', '0', '/qualities-manage/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10413160', '数据集_维表', 'report.manage.source:dimension', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '10413', '2', '1', '0', '/qualities-manage/reportManage', '1');
call init_new_role_permission('10413120');
call init_new_role_permission('10413130');
call init_new_role_permission('10413140');
call init_new_role_permission('10413150');
call init_new_role_permission('10413160');

-- /statementCenter/reportManage 报表中心-报表管理
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13004120', '报表类型', 'report.manage:type', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '13004', '2', '1', '0', '/statementCenter/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13004130', '数据集_新增', 'report.manage.source:add', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '13004', '2', '1', '0', '/statementCenter/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13004140', '数据集_编辑', 'report.manage.source:edit', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '13004', '2', '1', '0', '/statementCenter/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13004150', '数据集_删除', 'report.manage.source:delete', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '13004', '2', '1', '0', '/statementCenter/reportManage', '1');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13004160', '数据集_维表', 'report.manage.source:dimension', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '13004', '2', '1', '0', '/statementCenter/reportManage', '1');
call init_new_role_permission('13004120');
call init_new_role_permission('13004130');
call init_new_role_permission('13004140');
call init_new_role_permission('13004150');
call init_new_role_permission('13004160');

-- 更新下推配置表
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '[]' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bom.filterMaterialTypes';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '[]' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.filterMaterialTypes';
UPDATE `dfs_order_push_down_config_value` SET `value` = '[]' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bom.filterMaterialTypes' AND `value` = '[19,20]';
UPDATE `dfs_order_push_down_config_value` SET `value` = '[]' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.filterMaterialTypes' AND `value` = '[19,20]';


-- 删除历史aps路由和权限相关数据
DELETE
FROM
    sys_route
WHERE
        path = '/order-model/auxiliary_plan'
  AND module_name = 'ams';

DELETE
FROM
    sys_permissions
WHERE
        id IN (
               '10002',
               '10002010',
               '10002020',
               '10002030',
               '10002040',
               '10002050',
               '10002060',
               '10002070',
               '10002080',
               '10002090',
               '10002100',
               '10002110',
               '10002120'
        );

DELETE
FROM
    sys_role_permission
WHERE
        permission_id IN (
                          '10002',
                          '10002010',
                          '10002020',
                          '10002030',
                          '10002040',
                          '10002050',
                          '10002060',
                          '10002070',
                          '10002080',
                          '10002090',
                          '10002100',
                          '10002110',
                          '10002120'
        );

-- 自动完工业务配置删除
delete from `dfs_business_config`  WHERE full_path_code = 'production.productOrderStateConfig.autoCompleteConfig';
delete from `dfs_business_config_value`  WHERE config_full_path_code = 'production.productOrderStateConfig.autoCompleteConfig';

-- 解绑所有角色关联的工单状态变更权限
DELETE FROM `sys_role_permission` WHERE permission_id = '10005080' or permission_id = '10101080';

-- 更新业务配置描述
UPDATE `dfs_business_config` SET `description` = '启用后，订单报工小程序/工单报工小程序报工时，操作员只能从制造单元内的员工进行选择' WHERE `full_path_code` = 'production.workOrderVerificationReportConfig.filterOperatorConfiguration';
-- 生产订单流转卡打印权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10003490', '打印流转卡', 'product.order:print:flowCard', NULL, NULL, NULL, NULL, '2024-03-04 09:50:29', 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1, NULL, '');
call init_new_role_permission('10003490');

-- bom物料历史数据分母为0或者为空的统一刷新成1
UPDATE `dfs_bom_raw_material` SET `number` = 1.0 WHERE `number` = 0.0 OR `number` IS NULL;
UPDATE `dfs_bom_raw_material` SET `num` = 1.0 WHERE `num` IS NULL;

-- 修改新下推配置的描述，目前暂不支持下推到wms的单据
UPDATE `dfs_order_push_down_config` SET `name` = CONCAT (`name`, '(wms2.25版本后支持)') WHERE `full_path_code` in ('production.productMaterialsListPushDownConfig.outputPickingProduct', 'production.productMaterialsListPushDownConfig.workOrderSupplement', 'production.productMaterialsListPushDownConfig.transferOrder', 'production.takeOutApplicationPushDownConfig.takeOutOutbound', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut');