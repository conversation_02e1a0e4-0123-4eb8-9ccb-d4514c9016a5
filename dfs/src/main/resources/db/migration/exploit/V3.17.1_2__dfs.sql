-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================


-- DML
-- 将原来baseField的中文名重命名为基本信息
UPDATE `dfs_form_field_module_config` SET `module_name` = '基本信息' WHERE `module_code` = 'baseField';

-- 需要先将表单规则配置的module_code 重新进行归类，再对模块配置进行重新归类
-- 预处理为 baseField(基本信息)、baseExtendField(基本信息扩展字段)、
-- baseMaterialLineField(物料行字段)、baseMaterialLineExtendField(物料行字段扩展字段)、
-- batchField(批次行字段)、
-- materialBaseField(物料基础字段)、materialExtendField（物料扩展字段）

-- 生产工单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/order-model/production-workorder' AND `field_code` in ('workOrderExtendFieldOneName','workOrderExtendFieldTwoName','workOrderExtendFieldThreeName','workOrderExtendFieldFourName','workOrderExtendFieldFiveName','workOrderExtendFieldSixName','workOrderExtendFieldSevenName','workOrderExtendFieldEightName','workOrderExtendFieldNineName','workOrderExtendFieldTenName','workOrderExtendFieldOne','workOrderExtendFieldTwo','workOrderExtendFieldThree','workOrderExtendFieldFour','workOrderExtendFieldFive','workOrderExtendFieldSix','workOrderExtendFieldSeven','workOrderExtendFieldEight','workOrderExtendFieldNine','workOrderExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/order-model/production-workorder' AND `field_code` in ('workOrderMaterialExtendFieldOne','workOrderMaterialExtendFieldTwo','workOrderMaterialExtendFieldThree','workOrderMaterialExtendFieldFour','workOrderMaterialExtendFieldFive','workOrderMaterialExtendFieldSix','workOrderMaterialExtendFieldSeven','workOrderMaterialExtendFieldEight','workOrderMaterialExtendFieldNine','workOrderMaterialExtendFieldTen','workOrderMaterialExtendFieldOneName','workOrderMaterialExtendFieldTwoName','workOrderMaterialExtendFieldThreeName','workOrderMaterialExtendFieldFourName','workOrderMaterialExtendFieldFiveName','workOrderMaterialExtendFieldSixName','workOrderMaterialExtendFieldSevenName','workOrderMaterialExtendFieldEightName','workOrderMaterialExtendFieldNineName','workOrderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/order-model/production-workorder' AND `field_code` in ('priority','code','planQuantity','finishCount','plannedBatches','actualBatches','plansPerBatch','passRate','unqualified','inventoryQuantity','progress','haveBom','haveCraft','procedureName','lineName','workCenterName','teamName','deviceName','startDate','endDate','actualStartDate','actualEndDate','auxiliaryAttr','inStockCount','actualWorkingHours','circulationDuration','executionStatusName','theoryHour','produceTheoryHour','planTheoryHour','customerMaterialCode','customerMaterialName','customerSpecification', 'relatedSaleOrderMaterialLineNumber', 'relatedProductOrderMaterialLineNumber', 'schemeName', 'quantity','craftCode','procedureIds','lineId','workCenterId','relevanceSource', 'lineNumber', 'stockQuantity', 'inputTotal', 'circulationStateName', 'planStateName', 'coefficient', 'supplierCode');
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.list', 'workOrder.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByCreate', 'workOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByRelease', 'workOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByInvestment', 'workOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByHangUp', 'workOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByFinish', 'workOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByClosed', 'workOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByCancel', 'workOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.detail', 'workOrder.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.list', 'workOrder.list', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByCreate', 'workOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByRelease', 'workOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByInvestment', 'workOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByHangUp', 'workOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByFinish', 'workOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByClosed', 'workOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByCancel', 'workOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.detail', 'workOrder.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.list', 'workOrder.list', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByCreate', 'workOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByRelease', 'workOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByInvestment', 'workOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByHangUp', 'workOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByFinish', 'workOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByClosed', 'workOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.editByCancel', 'workOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrder.detail', 'workOrder.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('workOrder.list', 'workOrder.edit', 'workOrder.detail') AND `field_code` in ('workOrderExtendFieldOneName','workOrderExtendFieldTwoName','workOrderExtendFieldThreeName','workOrderExtendFieldFourName','workOrderExtendFieldFiveName','workOrderExtendFieldSixName','workOrderExtendFieldSevenName','workOrderExtendFieldEightName','workOrderExtendFieldNineName','workOrderExtendFieldTenName','workOrderExtendFieldOne','workOrderExtendFieldTwo','workOrderExtendFieldThree','workOrderExtendFieldFour','workOrderExtendFieldFive','workOrderExtendFieldSix','workOrderExtendFieldSeven','workOrderExtendFieldEight','workOrderExtendFieldNine','workOrderExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('workOrder.list', 'workOrder.edit', 'workOrder.detail') AND `field_code` in ('workOrderMaterialExtendFieldOne','workOrderMaterialExtendFieldTwo','workOrderMaterialExtendFieldThree','workOrderMaterialExtendFieldFour','workOrderMaterialExtendFieldFive','workOrderMaterialExtendFieldSix','workOrderMaterialExtendFieldSeven','workOrderMaterialExtendFieldEight','workOrderMaterialExtendFieldNine','workOrderMaterialExtendFieldTen','workOrderMaterialExtendFieldOneName','workOrderMaterialExtendFieldTwoName','workOrderMaterialExtendFieldThreeName','workOrderMaterialExtendFieldFourName','workOrderMaterialExtendFieldFiveName','workOrderMaterialExtendFieldSixName','workOrderMaterialExtendFieldSevenName','workOrderMaterialExtendFieldEightName','workOrderMaterialExtendFieldNineName','workOrderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('workOrder.list', 'workOrder.edit', 'workOrder.detail') AND `field_code` in ('priority','code','planQuantity','finishCount','plannedBatches','actualBatches','plansPerBatch','passRate','unqualified','inventoryQuantity','progress','haveBom','haveCraft','procedureName','lineName','workCenterName','teamName','deviceName','startDate','endDate','actualStartDate','actualEndDate','auxiliaryAttr','inStockCount','actualWorkingHours','circulationDuration','executionStatusName','theoryHour','produceTheoryHour','planTheoryHour','customerMaterialCode','customerMaterialName','customerSpecification', 'relatedSaleOrderMaterialLineNumber', 'relatedProductOrderMaterialLineNumber', 'schemeName', 'quantity','craftCode','procedureIds','lineId','workCenterId','relevanceSource', 'lineNumber', 'stockQuantity', 'inputTotal', 'circulationStateName', 'planStateName', 'coefficient', 'supplierCode');

-- 生产订单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/order-model/product-order' AND `field_code` in ('productOrderExtendFieldOneName','productOrderExtendFieldTwoName','productOrderExtendFieldThreeName','productOrderExtendFieldFourName','productOrderExtendFieldFiveName','productOrderExtendFieldSixName','productOrderExtendFieldSevenName','productOrderExtendFieldEightName','productOrderExtendFieldNineName','productOrderExtendFieldTenName','productOrderExtendFieldOne','productOrderExtendFieldTwo','productOrderExtendFieldThree','productOrderExtendFieldFour','productOrderExtendFieldFive','productOrderExtendFieldSix','productOrderExtendFieldSeven','productOrderExtendFieldEight','productOrderExtendFieldNine','productOrderExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/order-model/product-order' AND `field_code` in ('productOrderMaterialExtendFieldOne','productOrderMaterialExtendFieldTwo','productOrderMaterialExtendFieldThree','productOrderMaterialExtendFieldFour','productOrderMaterialExtendFieldFive','productOrderMaterialExtendFieldSix','productOrderMaterialExtendFieldSeven','productOrderMaterialExtendFieldEight','productOrderMaterialExtendFieldNine','productOrderMaterialExtendFieldTen','productOrderMaterialExtendFieldOneName','productOrderMaterialExtendFieldTwoName','productOrderMaterialExtendFieldThreeName','productOrderMaterialExtendFieldFourName','productOrderMaterialExtendFieldFiveName','productOrderMaterialExtendFieldSixName','productOrderMaterialExtendFieldSevenName','productOrderMaterialExtendFieldEightName','productOrderMaterialExtendFieldNineName','productOrderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/order-model/product-order' AND `field_code` in ('priority','code','procedureProcessDetail','procedureProcess','scheduledProductionQuantity','auxiliaryAttr','planQuantity','finishCount','identifierList','unqualifiedCount','plannedBatches','plansPerBatch','actualBatches','passRate','progress','planProductStartTime','planProductEndTime','gridName','customerCode','customerName','orderDate','salesQuantity','haveBom','haveCraft','mergedStateName','mergeOrderNumber','requireGoodsDate','yield','actualProductStartTime','actualProductEndTime','schedulingStatusName','materialRemark','processStatusName','lineNumber','relatedSaleOrderMaterialLineNumber','schemeName','quantity','gridCode','processVOS','orderMaterialRemark','needProduceQuantity','scheduleModeName','bomLayerNum', 'customerMaterialCode','customerMaterialName','customerSpecification','stockQuantity','customerOrderNumber');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.list', 'productOrder.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByCreate', 'productOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByRelease', 'productOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByFinish', 'productOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByClosed', 'productOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByCancel', 'productOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.detail', 'productOrder.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.list', 'productOrder.list', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByCreate', 'productOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByRelease', 'productOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByFinish', 'productOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByClosed', 'productOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByCancel', 'productOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.detail', 'productOrder.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.list', 'productOrder.list', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByCreate', 'productOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByRelease', 'productOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByFinish', 'productOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByClosed', 'productOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.editByCancel', 'productOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrder.detail', 'productOrder.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('productOrder.list', 'productOrder.edit', 'productOrder.detail') AND `field_code` in ('productOrderExtendFieldOneName','productOrderExtendFieldTwoName','productOrderExtendFieldThreeName','productOrderExtendFieldFourName','productOrderExtendFieldFiveName','productOrderExtendFieldSixName','productOrderExtendFieldSevenName','productOrderExtendFieldEightName','productOrderExtendFieldNineName','productOrderExtendFieldTenName','productOrderExtendFieldOne','productOrderExtendFieldTwo','productOrderExtendFieldThree','productOrderExtendFieldFour','productOrderExtendFieldFive','productOrderExtendFieldSix','productOrderExtendFieldSeven','productOrderExtendFieldEight','productOrderExtendFieldNine','productOrderExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('productOrder.list', 'productOrder.edit', 'productOrder.detail') AND `field_code` in ('productOrderMaterialExtendFieldOne','productOrderMaterialExtendFieldTwo','productOrderMaterialExtendFieldThree','productOrderMaterialExtendFieldFour','productOrderMaterialExtendFieldFive','productOrderMaterialExtendFieldSix','productOrderMaterialExtendFieldSeven','productOrderMaterialExtendFieldEight','productOrderMaterialExtendFieldNine','productOrderMaterialExtendFieldTen','productOrderMaterialExtendFieldOneName','productOrderMaterialExtendFieldTwoName','productOrderMaterialExtendFieldThreeName','productOrderMaterialExtendFieldFourName','productOrderMaterialExtendFieldFiveName','productOrderMaterialExtendFieldSixName','productOrderMaterialExtendFieldSevenName','productOrderMaterialExtendFieldEightName','productOrderMaterialExtendFieldNineName','productOrderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('productOrder.list', 'productOrder.edit', 'productOrder.detail') AND `field_code` in ('priority','code','procedureProcessDetail','procedureProcess','scheduledProductionQuantity','auxiliaryAttr','planQuantity','finishCount','identifierList','unqualifiedCount','plannedBatches','plansPerBatch','actualBatches','passRate','progress','planProductStartTime','planProductEndTime','gridName','customerCode','customerName','orderDate','salesQuantity','haveBom','haveCraft','mergedStateName','mergeOrderNumber','requireGoodsDate','yield','actualProductStartTime','actualProductEndTime','schedulingStatusName','materialRemark','processStatusName','lineNumber','relatedSaleOrderMaterialLineNumber','schemeName','quantity','gridCode','processVOS','orderMaterialRemark','needProduceQuantity','scheduleModeName','bomLayerNum', 'customerMaterialCode','customerMaterialName','customerSpecification','stockQuantity','customerOrderNumber');

-- 生产工单用料清单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/order-model/workOrder-materials' AND `field_code` in ('materialListExtendFieldOneName','materialListExtendFieldTwoName','materialListExtendFieldThreeName','materialListExtendFieldFourName','materialListExtendFieldFiveName','materialListExtendFieldSixName','materialListExtendFieldSevenName','materialListExtendFieldEightName','materialListExtendFieldNineName','materialListExtendFieldTenName','materialListExtendFieldOne','materialListExtendFieldTwo','materialListExtendFieldThree','materialListExtendFieldFour','materialListExtendFieldFive','materialListExtendFieldSix','materialListExtendFieldSeven','materialListExtendFieldEight','materialListExtendFieldNine','materialListExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/order-model/workOrder-materials' AND `field_code` in ('materialListMaterialExtendFieldOne','materialListMaterialExtendFieldTwo','materialListMaterialExtendFieldThree','materialListMaterialExtendFieldFour','materialListMaterialExtendFieldFive','materialListMaterialExtendFieldSix','materialListMaterialExtendFieldSeven','materialListMaterialExtendFieldEight','materialListMaterialExtendFieldNine','materialListMaterialExtendFieldTen','materialListMaterialExtendFieldOneName','materialListMaterialExtendFieldTwoName','materialListMaterialExtendFieldThreeName','materialListMaterialExtendFieldFourName','materialListMaterialExtendFieldFiveName','materialListMaterialExtendFieldSixName','materialListMaterialExtendFieldSevenName','materialListMaterialExtendFieldEightName','materialListMaterialExtendFieldNineName','materialListMaterialExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/order-model/workOrder-materials' AND `field_code` in ('code','auxiliaryAttr','subTypeName','planQuantity','actualQuantity','stockQuantity','pushDownQuantity','bomNumerator','bomDenominator','stockQuantity','referenceQuantity','actualQuantity','loseRate','pushDownQuantity','returnQuantity','totalPlanQuantity','totalReceiveQuantity','totalMaterialIssued','mainReplaceScale','replaceQuantity', 'fixedDamage');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('workOrderMaterialList.list.order', 'workOrderMaterialList.list.material', 'workOrderMaterialList.edit', 'workOrderMaterialList.detail') AND `field_code` in ('materialListExtendFieldOneName','materialListExtendFieldTwoName','materialListExtendFieldThreeName','materialListExtendFieldFourName','materialListExtendFieldFiveName','materialListExtendFieldSixName','materialListExtendFieldSevenName','materialListExtendFieldEightName','materialListExtendFieldNineName','materialListExtendFieldTenName','materialListExtendFieldOne','materialListExtendFieldTwo','materialListExtendFieldThree','materialListExtendFieldFour','materialListExtendFieldFive','materialListExtendFieldSix','materialListExtendFieldSeven','materialListExtendFieldEight','materialListExtendFieldNine','materialListExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('workOrderMaterialList.list.order', 'workOrderMaterialList.list.material', 'workOrderMaterialList.edit', 'workOrderMaterialList.detail') AND `field_code` in ('materialListMaterialExtendFieldOne','materialListMaterialExtendFieldTwo','materialListMaterialExtendFieldThree','materialListMaterialExtendFieldFour','materialListMaterialExtendFieldFive','materialListMaterialExtendFieldSix','materialListMaterialExtendFieldSeven','materialListMaterialExtendFieldEight','materialListMaterialExtendFieldNine','materialListMaterialExtendFieldTen','materialListMaterialExtendFieldOneName','materialListMaterialExtendFieldTwoName','materialListMaterialExtendFieldThreeName','materialListMaterialExtendFieldFourName','materialListMaterialExtendFieldFiveName','materialListMaterialExtendFieldSixName','materialListMaterialExtendFieldSevenName','materialListMaterialExtendFieldEightName','materialListMaterialExtendFieldNineName','materialListMaterialExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('workOrderMaterialList.list.order', 'workOrderMaterialList.list.material', 'workOrderMaterialList.edit', 'workOrderMaterialList.detail') AND `field_code` in ('code','auxiliaryAttr','subTypeName','planQuantity','actualQuantity','stockQuantity','pushDownQuantity','bomNumerator','bomDenominator','stockQuantity','referenceQuantity','actualQuantity','loseRate','pushDownQuantity','returnQuantity','totalPlanQuantity','totalReceiveQuantity','totalMaterialIssued','mainReplaceScale','replaceQuantity', 'fixedDamage');


-- 生产订单用料清单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/order-model/production-materials' AND `field_code` in ('materialListExtendFieldOne','materialListExtendFieldTwo','materialListExtendFieldThree','materialListExtendFieldFour','materialListExtendFieldFive','materialListExtendFieldSix','materialListExtendFieldSeven','materialListExtendFieldEight','materialListExtendFieldNine','materialListExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/order-model/production-materials' AND `field_code` in ('materialListMaterialExtendFieldOne','materialListMaterialExtendFieldTwo','materialListMaterialExtendFieldThree','materialListMaterialExtendFieldFour','materialListMaterialExtendFieldFive','materialListMaterialExtendFieldSix','materialListMaterialExtendFieldSeven','materialListMaterialExtendFieldEight','materialListMaterialExtendFieldNine','materialListMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/order-model/production-materials' AND `field_code` in ('code','auxiliaryAttr','subTypeName','planQuantity','actualQuantity','stockQuantity','pushDownQuantity','bomNumerator','bomDenominator','stockQuantity','referenceQuantity','actualQuantity','loseRate','pushDownQuantity','returnQuantity','totalPlanQuantity','totalReceiveQuantity','totalMaterialIssued','mainReplaceScale','replaceQuantity', 'fixedDamage');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.list.order', 'productOrderMaterialList.list.order', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.list.material', 'productOrderMaterialList.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByCreate', 'productOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByRelease', 'productOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByFinish', 'productOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByClosed', 'productOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByCancel', 'productOrderMaterialList.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.detail', 'productOrderMaterialList.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.list.material', 'productOrderMaterialList.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByCreate', 'productOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByRelease', 'productOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByFinish', 'productOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByClosed', 'productOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByCancel', 'productOrderMaterialList.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.detail', 'productOrderMaterialList.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.list.material', 'productOrderMaterialList.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByCreate', 'productOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByRelease', 'productOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByFinish', 'productOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByClosed', 'productOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.editByCancel', 'productOrderMaterialList.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('productOrderMaterialList.detail', 'productOrderMaterialList.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('productOrderMaterialList.list.order', 'productOrderMaterialList.list.material', 'productOrderMaterialList.edit', 'productOrderMaterialList.detail') AND `field_code` in ('materialListExtendFieldOne','materialListExtendFieldTwo','materialListExtendFieldThree','materialListExtendFieldFour','materialListExtendFieldFive','materialListExtendFieldSix','materialListExtendFieldSeven','materialListExtendFieldEight','materialListExtendFieldNine','materialListExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('productOrderMaterialList.list.order', 'productOrderMaterialList.list.material', 'productOrderMaterialList.edit', 'productOrderMaterialList.detail') AND `field_code` in ('materialListMaterialExtendFieldOne','materialListMaterialExtendFieldTwo','materialListMaterialExtendFieldThree','materialListMaterialExtendFieldFour','materialListMaterialExtendFieldFive','materialListMaterialExtendFieldSix','materialListMaterialExtendFieldSeven','materialListMaterialExtendFieldEight','materialListMaterialExtendFieldNine','materialListMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('productOrderMaterialList.list.order', 'productOrderMaterialList.list.material', 'productOrderMaterialList.edit', 'productOrderMaterialList.detail') AND `field_code` in ('code','auxiliaryAttr','subTypeName','planQuantity','actualQuantity','stockQuantity','pushDownQuantity','bomNumerator','bomDenominator','stockQuantity','referenceQuantity','actualQuantity','loseRate','pushDownQuantity','returnQuantity','totalPlanQuantity','totalReceiveQuantity','totalMaterialIssued','mainReplaceScale','replaceQuantity', 'fixedDamage');

-- 销售订单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/order-model/salesOrder' AND `field_code` in ('saleOrderExtendFieldOneName','saleOrderExtendFieldTwoName','saleOrderExtendFieldThreeName','saleOrderExtendFieldFourName','saleOrderExtendFieldFiveName','saleOrderExtendFieldSixName','saleOrderExtendFieldSevenName','saleOrderExtendFieldEightName','saleOrderExtendFieldNineName','saleOrderExtendFieldTenName','saleOrderExtendFieldOne','saleOrderExtendFieldTwo','saleOrderExtendFieldThree','saleOrderExtendFieldFour','saleOrderExtendFieldFive','saleOrderExtendFieldSix','saleOrderExtendFieldSeven','saleOrderExtendFieldEight','saleOrderExtendFieldNine','saleOrderExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/order-model/salesOrder' AND `field_code` in ('saleOrderMaterialExtendFieldOne','saleOrderMaterialExtendFieldTwo','saleOrderMaterialExtendFieldThree','saleOrderMaterialExtendFieldFour','saleOrderMaterialExtendFieldFive','saleOrderMaterialExtendFieldSix','saleOrderMaterialExtendFieldSeven','saleOrderMaterialExtendFieldEight','saleOrderMaterialExtendFieldNine','saleOrderMaterialExtendFieldTen','saleOrderMaterialExtendFieldOneName','saleOrderMaterialExtendFieldTwoName','saleOrderMaterialExtendFieldThreeName','saleOrderMaterialExtendFieldFourName','saleOrderMaterialExtendFieldFiveName','saleOrderMaterialExtendFieldSixName','saleOrderMaterialExtendFieldSevenName','saleOrderMaterialExtendFieldEightName','saleOrderMaterialExtendFieldNineName','saleOrderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/order-model/salesOrder' AND `field_code` in ('planDeliveryDate','orderDate','code','planTotalPrice','erpDocumentCode','plannedProductionQuantity','scheduledProductionQuantity','productionQuantity','appliedShipmentQuantity','salesQuantity','appliedQuantity','plannedBatches','plansPerBatch','actualBatches','identifierList','priority','requireGoodsDate','unqualifiedQuantity','haveBom','haveCraft','shipmentStatusName','customerOrderNumber','customerMaterialCode','customerMaterialName','customerSpecification','materialChangeTime','materialIsChange','synchronizationTime','deliveryDate','notDeliveredQuantity','outStockQuantity','returnQuantity','passRate','completionRate','inventoryQuantity','processStatusName','auxiliaryAttr', 'index','materialCode','price','materialRemark','orderMaterialRemark','stockQuantity','schemeName','auxiliaryMaterialModel', 'unitSalesQuantity');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.list.order', 'saleOrder.list.order', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.list.material', 'saleOrder.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByCreate', 'saleOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByRelease', 'saleOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByFinish', 'saleOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByClosed', 'saleOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByCancel', 'saleOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.detail', 'saleOrder.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.list.material', 'saleOrder.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByCreate', 'saleOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByRelease', 'saleOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByFinish', 'saleOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByClosed', 'saleOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByCancel', 'saleOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.detail', 'saleOrder.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.list.material', 'saleOrder.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByCreate', 'saleOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByRelease', 'saleOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByFinish', 'saleOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByClosed', 'saleOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.editByCancel', 'saleOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleOrder.detail', 'saleOrder.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('saleOrder.list.order', 'saleOrder.list.material', 'saleOrder.edit', 'saleOrder.detail') AND `field_code` in ('saleOrderExtendFieldOneName','saleOrderExtendFieldTwoName','saleOrderExtendFieldThreeName','saleOrderExtendFieldFourName','saleOrderExtendFieldFiveName','saleOrderExtendFieldSixName','saleOrderExtendFieldSevenName','saleOrderExtendFieldEightName','saleOrderExtendFieldNineName','saleOrderExtendFieldTenName','saleOrderExtendFieldOne','saleOrderExtendFieldTwo','saleOrderExtendFieldThree','saleOrderExtendFieldFour','saleOrderExtendFieldFive','saleOrderExtendFieldSix','saleOrderExtendFieldSeven','saleOrderExtendFieldEight','saleOrderExtendFieldNine','saleOrderExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('saleOrder.list.order', 'saleOrder.list.material', 'saleOrder.edit', 'saleOrder.detail') AND `field_code` in ('saleOrderMaterialExtendFieldOne','saleOrderMaterialExtendFieldTwo','saleOrderMaterialExtendFieldThree','saleOrderMaterialExtendFieldFour','saleOrderMaterialExtendFieldFive','saleOrderMaterialExtendFieldSix','saleOrderMaterialExtendFieldSeven','saleOrderMaterialExtendFieldEight','saleOrderMaterialExtendFieldNine','saleOrderMaterialExtendFieldTen','saleOrderMaterialExtendFieldOneName','saleOrderMaterialExtendFieldTwoName','saleOrderMaterialExtendFieldThreeName','saleOrderMaterialExtendFieldFourName','saleOrderMaterialExtendFieldFiveName','saleOrderMaterialExtendFieldSixName','saleOrderMaterialExtendFieldSevenName','saleOrderMaterialExtendFieldEightName','saleOrderMaterialExtendFieldNineName','saleOrderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('saleOrder.list.order', 'saleOrder.list.material', 'saleOrder.edit', 'saleOrder.detail') AND `field_code` in ('planDeliveryDate','orderDate','code','planTotalPrice','erpDocumentCode','plannedProductionQuantity','scheduledProductionQuantity','productionQuantity','appliedShipmentQuantity','salesQuantity','appliedQuantity','plannedBatches','plansPerBatch','actualBatches','identifierList','priority','requireGoodsDate','unqualifiedQuantity','haveBom','haveCraft','shipmentStatusName','customerOrderNumber','customerMaterialCode','customerMaterialName','customerSpecification','materialChangeTime','materialIsChange','synchronizationTime','deliveryDate','notDeliveredQuantity','outStockQuantity','returnQuantity','passRate','completionRate','inventoryQuantity','processStatusName','auxiliaryAttr', 'index','materialCode','price','materialRemark','orderMaterialRemark','stockQuantity','schemeName','auxiliaryMaterialModel', 'unitSalesQuantity');

-- 出货申请单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/order-model/shipment_application' AND `field_code` in ('deliveryApplicationExtendFieldOne','deliveryApplicationExtendFieldTwo','deliveryApplicationExtendFieldThree','deliveryApplicationExtendFieldFour','deliveryApplicationExtendFieldFive','deliveryApplicationExtendFieldSix','deliveryApplicationExtendFieldSeven','deliveryApplicationExtendFieldEight','deliveryApplicationExtendFieldNine','deliveryApplicationExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/order-model/shipment_application' AND `field_code` in ('deliveryApplicationMaterialExtendFieldOne','deliveryApplicationMaterialExtendFieldTwo','deliveryApplicationMaterialExtendFieldThree','deliveryApplicationMaterialExtendFieldFour','deliveryApplicationMaterialExtendFieldFive','deliveryApplicationMaterialExtendFieldSix','deliveryApplicationMaterialExtendFieldSeven','deliveryApplicationMaterialExtendFieldEight','deliveryApplicationMaterialExtendFieldNine','deliveryApplicationMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/order-model/shipment_application' AND `field_code` in ('code','isGift','materialNum','salesQuantity','standard','amount');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.list.material', 'deliveryApplication.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByCreate', 'deliveryApplication.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByRelease', 'deliveryApplication.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByFinish', 'deliveryApplication.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByClosed', 'deliveryApplication.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByCancel', 'deliveryApplication.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.detail', 'deliveryApplication.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.list.material', 'deliveryApplication.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByCreate', 'deliveryApplication.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByRelease', 'deliveryApplication.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByFinish', 'deliveryApplication.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByClosed', 'deliveryApplication.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByCancel', 'deliveryApplication.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.detail', 'deliveryApplication.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.list.material', 'deliveryApplication.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByCreate', 'deliveryApplication.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByRelease', 'deliveryApplication.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByFinish', 'deliveryApplication.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByClosed', 'deliveryApplication.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.editByCancel', 'deliveryApplication.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('deliveryApplication.detail', 'deliveryApplication.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('deliveryApplication.list.material', 'deliveryApplication.edit', 'deliveryApplication.detail') AND `field_code` in ('deliveryApplicationExtendFieldOne','deliveryApplicationExtendFieldTwo','deliveryApplicationExtendFieldThree','deliveryApplicationExtendFieldFour','deliveryApplicationExtendFieldFive','deliveryApplicationExtendFieldSix','deliveryApplicationExtendFieldSeven','deliveryApplicationExtendFieldEight','deliveryApplicationExtendFieldNine','deliveryApplicationExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('deliveryApplication.list.material', 'deliveryApplication.edit', 'deliveryApplication.detail') AND `field_code` in ('deliveryApplicationMaterialExtendFieldOne','deliveryApplicationMaterialExtendFieldTwo','deliveryApplicationMaterialExtendFieldThree','deliveryApplicationMaterialExtendFieldFour','deliveryApplicationMaterialExtendFieldFive','deliveryApplicationMaterialExtendFieldSix','deliveryApplicationMaterialExtendFieldSeven','deliveryApplicationMaterialExtendFieldEight','deliveryApplicationMaterialExtendFieldNine','deliveryApplicationMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('deliveryApplication.list.material', 'deliveryApplication.edit', 'deliveryApplication.detail') AND `field_code` in ('code','isGift','materialNum','salesQuantity','standard','amount');

-- 销售退货单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/order-model/sales_returns' AND `field_code` in ('saleReturnExtendFieldOne','saleReturnExtendFieldTwo','saleReturnExtendFieldThree','saleReturnExtendFieldFour','saleReturnExtendFieldFive','saleReturnExtendFieldSix','saleReturnExtendFieldSeven','saleReturnExtendFieldEight','saleReturnExtendFieldNine','saleReturnExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/order-model/sales_returns' AND `field_code` in ('saleReturnMaterialExtendFieldOne','saleReturnMaterialExtendFieldTwo','saleReturnMaterialExtendFieldThree','saleReturnMaterialExtendFieldFour','saleReturnMaterialExtendFieldFive','saleReturnMaterialExtendFieldSix','saleReturnMaterialExtendFieldSeven','saleReturnMaterialExtendFieldEight','saleReturnMaterialExtendFieldNine','saleReturnMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/order-model/sales_returns' AND `field_code` in ('materialCode','customerMaterialCode','customerMaterialName','customerSpecification','planAmount','actualAmount', 'isGift');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.list.material', 'saleReturn.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByCreate', 'saleReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByRelease', 'saleReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByFinish', 'saleReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByClosed', 'saleReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByCancel', 'saleReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.detail', 'saleReturn.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.list.material', 'saleReturn.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByCreate', 'saleReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByRelease', 'saleReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByFinish', 'saleReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByClosed', 'saleReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByCancel', 'saleReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.detail', 'saleReturn.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.list.material', 'saleReturn.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByCreate', 'saleReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByRelease', 'saleReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByFinish', 'saleReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByClosed', 'saleReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.editByCancel', 'saleReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('saleReturn.detail', 'saleReturn.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('saleReturn.list.material', 'saleReturn.edit', 'saleReturn.detail') AND `field_code` in ('saleReturnExtendFieldOne','saleReturnExtendFieldTwo','saleReturnExtendFieldThree','saleReturnExtendFieldFour','saleReturnExtendFieldFive','saleReturnExtendFieldSix','saleReturnExtendFieldSeven','saleReturnExtendFieldEight','saleReturnExtendFieldNine','saleReturnExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('saleReturn.list.material', 'saleReturn.edit', 'saleReturn.detail') AND `field_code` in ('saleReturnMaterialExtendFieldOne','saleReturnMaterialExtendFieldTwo','saleReturnMaterialExtendFieldThree','saleReturnMaterialExtendFieldFour','saleReturnMaterialExtendFieldFive','saleReturnMaterialExtendFieldSix','saleReturnMaterialExtendFieldSeven','saleReturnMaterialExtendFieldEight','saleReturnMaterialExtendFieldNine','saleReturnMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('saleReturn.list.material', 'saleReturn.edit', 'saleReturn.detail') AND `field_code` in ('materialCode','customerMaterialCode','customerMaterialName','customerSpecification','planAmount','actualAmount', 'isGift');

-- 采购需求
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/purchasing-demand' AND `field_code` in ('purchaseRequestExtendFieldOne','purchaseRequestExtendFieldTwo','purchaseRequestExtendFieldThree','purchaseRequestExtendFieldFour','purchaseRequestExtendFieldFive','purchaseRequestExtendFieldSix','purchaseRequestExtendFieldSeven','purchaseRequestExtendFieldEight','purchaseRequestExtendFieldNine','purchaseRequestExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/purchasing-demand' AND `field_code` in ('RequestMaterialExtendFieldOne','RequestMaterialExtendFieldTwo','RequestMaterialExtendFieldThree','RequestMaterialExtendFieldFour','RequestMaterialExtendFieldFive','RequestMaterialExtendFieldSix','RequestMaterialExtendFieldSeven','RequestMaterialExtendFieldEight','RequestMaterialExtendFieldNine','RequestMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/procurement-management/purchasing-demand' AND `field_code` in ('code','referenceQuantity','actualNum','purchaseDate','suggestedSupplier','stockQuantity','materialRemarks','auxiliaryAttr','purchaseQuantity','receiptQuantity','inventoryQuantity','qualifiedQuantity','unqualifiedQuantity', 'num', 'customerCode', 'customerName');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseRequestOrder.list', 'purchaseRequestOrder.edit', 'purchaseRequestOrder.detail') AND `field_code` in ('purchaseRequestExtendFieldOne','purchaseRequestExtendFieldTwo','purchaseRequestExtendFieldThree','purchaseRequestExtendFieldFour','purchaseRequestExtendFieldFive','purchaseRequestExtendFieldSix','purchaseRequestExtendFieldSeven','purchaseRequestExtendFieldEight','purchaseRequestExtendFieldNine','purchaseRequestExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseRequestOrder.list', 'purchaseRequestOrder.edit', 'purchaseRequestOrder.detail') AND `field_code` in ('RequestMaterialExtendFieldOne','RequestMaterialExtendFieldTwo','RequestMaterialExtendFieldThree','RequestMaterialExtendFieldFour','RequestMaterialExtendFieldFive','RequestMaterialExtendFieldSix','RequestMaterialExtendFieldSeven','RequestMaterialExtendFieldEight','RequestMaterialExtendFieldNine','RequestMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseRequestOrder.list', 'purchaseRequestOrder.edit', 'purchaseRequestOrder.detail') AND `field_code` in ('code','referenceQuantity','actualNum','purchaseDate','suggestedSupplier','stockQuantity','materialRemarks','auxiliaryAttr','purchaseQuantity','receiptQuantity','inventoryQuantity','qualifiedQuantity','unqualifiedQuantity', 'num', 'customerCode', 'customerName');

-- 采购订单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/purchasing-list' AND `field_code` in ('purchaseExtendFieldOne','purchaseExtendFieldTwo','purchaseExtendFieldThree','purchaseExtendFieldFour','purchaseExtendFieldFive','purchaseExtendFieldSix','purchaseExtendFieldSeven','purchaseExtendFieldEight','purchaseExtendFieldNine','purchaseExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/purchasing-list' AND `field_code` in ('purchaseMaterialExtendFieldOne','purchaseMaterialExtendFieldTwo','purchaseMaterialExtendFieldThree','purchaseMaterialExtendFieldFour','purchaseMaterialExtendFieldFive','purchaseMaterialExtendFieldSix','purchaseMaterialExtendFieldSeven','purchaseMaterialExtendFieldEight','purchaseMaterialExtendFieldNine','purchaseMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/procurement-management/purchasing-list' AND `field_code` in ('code','num','planNum','price','sumprice','purchaseDate','stockQuantity','materialRemark','receiptQuantity','inventoryQuantity','qualifiedQuantity','unqualifiedQuantity','warehouseName','warehouseCode','auxiliaryAttr','receiptProgress','inventoryProgress','qualifiedRate','giftQuantity','waitReceiveGiveawayQuantity','giveawayQuantity','returnQuantity','isGift','customerName','customerCode');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.list.order', 'purchaseOrder.list.order', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.list.material', 'purchaseOrder.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByCreate', 'purchaseOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByRelease', 'purchaseOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByFinish', 'purchaseOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByClosed', 'purchaseOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByCancel', 'purchaseOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.detail', 'purchaseOrder.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.list.material', 'purchaseOrder.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByCreate', 'purchaseOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByRelease', 'purchaseOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByFinish', 'purchaseOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByClosed', 'purchaseOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByCancel', 'purchaseOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.detail', 'purchaseOrder.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.list.material', 'purchaseOrder.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByCreate', 'purchaseOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByRelease', 'purchaseOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByFinish', 'purchaseOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByClosed', 'purchaseOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.editByCancel', 'purchaseOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseOrder.detail', 'purchaseOrder.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseOrder.list.order','purchaseOrder.list.material','purchaseOrder.edit','purchaseOrder.detail') AND `field_code` in ('purchaseExtendFieldOne','purchaseExtendFieldTwo','purchaseExtendFieldThree','purchaseExtendFieldFour','purchaseExtendFieldFive','purchaseExtendFieldSix','purchaseExtendFieldSeven','purchaseExtendFieldEight','purchaseExtendFieldNine','purchaseExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseOrder.list.order','purchaseOrder.list.material','purchaseOrder.edit','purchaseOrder.detail') AND `field_code` in ('purchaseMaterialExtendFieldOne','purchaseMaterialExtendFieldTwo','purchaseMaterialExtendFieldThree','purchaseMaterialExtendFieldFour','purchaseMaterialExtendFieldFive','purchaseMaterialExtendFieldSix','purchaseMaterialExtendFieldSeven','purchaseMaterialExtendFieldEight','purchaseMaterialExtendFieldNine','purchaseMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseOrder.list.order','purchaseOrder.list.material','purchaseOrder.edit','purchaseOrder.detail') AND `field_code` in ('code','num','planNum','price','sumprice','purchaseDate','stockQuantity','materialRemark','receiptQuantity','inventoryQuantity','qualifiedQuantity','unqualifiedQuantity','warehouseName','warehouseCode','auxiliaryAttr','receiptProgress','inventoryProgress','qualifiedRate','giftQuantity','waitReceiveGiveawayQuantity','giveawayQuantity','returnQuantity','isGift','customerName','customerCode');

-- 采购收料单
DELETE FROM `dfs_form_field_module_config` WHERE `full_path_code` = 'purchaseReceiptOrder.list.order' AND `module_code` = 'materialBaseField';
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` in ('purchaseReceiptExtendFieldOne','purchaseReceiptExtendFieldTwo','purchaseReceiptExtendFieldThree','purchaseReceiptExtendFieldFour','purchaseReceiptExtendFieldFive','purchaseReceiptExtendFieldSix','purchaseReceiptExtendFieldSeven','purchaseReceiptExtendFieldEight','purchaseReceiptExtendFieldNine','purchaseReceiptExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` in ('receiptMaterialExtendFieldOne','receiptMaterialExtendFieldTwo','receiptMaterialExtendFieldThree','receiptMaterialExtendFieldFour','receiptMaterialExtendFieldFive','receiptMaterialExtendFieldSix','receiptMaterialExtendFieldSeven','receiptMaterialExtendFieldEight','receiptMaterialExtendFieldNine','receiptMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` in ('code','amount','returnQuantity','giftQuantity','batchCount','purchaseQuantity','returnCodes','auxiliaryAttr','qualifiedQuantity','unqualifiedQuantity','warehouseName','warehouseCode','skuName','planAmount','amount','purchaseBatchNumber','inspectStateName','count', 'bacthNum', 'materialTypes', 'isGift', 'batchInspectStateName', 'returnOutQuantity', 'customerCode', 'customerName', 'relateInspectOrder', 'rejectedQuantity', 'concessionAcceptanceQuantity', 'warehousingAmount', 'industrialWasteQuantity', 'materialWasteQuantity', 'rejectedQty', 'rejectedReason', 'unitPrice', 'warehouseStateName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'batchField' WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` in ('barCode');
UPDATE `dfs_form_field_rule_config` SET `show_gray` = 1 WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` in ('barCode') AND `module_code` = 'batchField';
DELETE FROM `dfs_form_field_rule_config` WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` in ('barCode') AND `show_gray` = 0;

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.list.order', 'purchaseOrder.list.order', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.list.material', 'purchaseReceiptOrder.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.list.material', 'purchaseReceiptOrder.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.list.material', 'purchaseReceiptOrder.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseReceiptOrder.list.order','purchaseReceiptOrder.list.material','purchaseReceiptOrder.edit','purchaseReceiptOrder.detail') AND `field_code` in ('purchaseReceiptExtendFieldOne','purchaseReceiptExtendFieldTwo','purchaseReceiptExtendFieldThree','purchaseReceiptExtendFieldFour','purchaseReceiptExtendFieldFive','purchaseReceiptExtendFieldSix','purchaseReceiptExtendFieldSeven','purchaseReceiptExtendFieldEight','purchaseReceiptExtendFieldNine','purchaseReceiptExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseReceiptOrder.list.order','purchaseReceiptOrder.list.material','purchaseReceiptOrder.edit','purchaseReceiptOrder.detail') AND `field_code` in ('receiptMaterialExtendFieldOne','receiptMaterialExtendFieldTwo','receiptMaterialExtendFieldThree','receiptMaterialExtendFieldFour','receiptMaterialExtendFieldFive','receiptMaterialExtendFieldSix','receiptMaterialExtendFieldSeven','receiptMaterialExtendFieldEight','receiptMaterialExtendFieldNine','receiptMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseReceiptOrder.list.order','purchaseReceiptOrder.list.material','purchaseReceiptOrder.edit','purchaseReceiptOrder.detail') AND `field_code` in ('code','amount','returnQuantity','giftQuantity','batchCount','purchaseQuantity','returnCodes','auxiliaryAttr','qualifiedQuantity','unqualifiedQuantity','warehouseName','warehouseCode','skuName','planAmount','amount','purchaseBatchNumber','inspectStateName','count', 'bacthNum', 'materialTypes', 'isGift', 'batchInspectStateName', 'returnOutQuantity', 'customerCode', 'customerName', 'relateInspectOrder', 'rejectedQuantity', 'concessionAcceptanceQuantity', 'warehousingAmount', 'industrialWasteQuantity', 'materialWasteQuantity', 'rejectedQty', 'rejectedReason', 'unitPrice', 'warehouseStateName');
-- UPDATE `dfs_form_field_config` SET `module_code` = 'batchField' WHERE `full_path_code` in ('purchaseReceiptOrder.list.order','purchaseReceiptOrder.list.material','purchaseReceiptOrder.edit','purchaseReceiptOrder.detail') AND `field_code` in ('barCode');

-- 采购退料单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/return-order' AND `field_code` in ('purchaseReturnExtendFieldOne','purchaseReturnExtendFieldTwo','purchaseReturnExtendFieldThree','purchaseReturnExtendFieldFour','purchaseReturnExtendFieldFive','purchaseReturnExtendFieldSix','purchaseReturnExtendFieldSeven','purchaseReturnExtendFieldEight','purchaseReturnExtendFieldNine','purchaseReturnExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/return-order' AND `field_code` in ('returnMaterialExtendFieldOne','returnMaterialExtendFieldTwo','returnMaterialExtendFieldThree','returnMaterialExtendFieldFour','returnMaterialExtendFieldFive','returnMaterialExtendFieldSix','returnMaterialExtendFieldSeven','returnMaterialExtendFieldEight','returnMaterialExtendFieldNine','returnMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/procurement-management/return-order' AND `field_code` in ('code','amount','purchaseBatchNumber','unqualifiedQuantity','isGift','giftQuantity','returnAmount','batchAmount');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'batchField' WHERE `route` = '/supply-chain-collaboration/procurement-management/return-order' AND `field_code` in ('barCode');
UPDATE `dfs_form_field_rule_config` SET `show_gray` = 1 WHERE `route` = '/supply-chain-collaboration/procurement-management/return-order' AND `field_code` in ('barCode') AND `module_code` = 'batchField';
DELETE FROM `dfs_form_field_rule_config` WHERE `route` = '/supply-chain-collaboration/procurement-management/return-order' AND `field_code` in ('barCode') AND `show_gray` = 0;

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.list.material', 'purchaseReturn.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByCreate', 'purchaseReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByRelease', 'purchaseReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByFinish', 'purchaseReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByClosed', 'purchaseReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByCancel', 'purchaseReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.detail', 'purchaseReturn.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.list.material', 'purchaseReturn.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByCreate', 'purchaseReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByRelease', 'purchaseReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByFinish', 'purchaseReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByClosed', 'purchaseReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByCancel', 'purchaseReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.detail', 'purchaseReturn.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.list.material', 'purchaseReturn.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByCreate', 'purchaseReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByRelease', 'purchaseReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByFinish', 'purchaseReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByClosed', 'purchaseReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.editByCancel', 'purchaseReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('purchaseReturn.detail', 'purchaseReturn.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseReturn.list.material','purchaseReturn.edit','purchaseReturn.detail') AND `field_code` in ('purchaseReturnExtendFieldOne','purchaseReturnExtendFieldTwo','purchaseReturnExtendFieldThree','purchaseReturnExtendFieldFour','purchaseReturnExtendFieldFive','purchaseReturnExtendFieldSix','purchaseReturnExtendFieldSeven','purchaseReturnExtendFieldEight','purchaseReturnExtendFieldNine','purchaseReturnExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseReturn.list.material','purchaseReturn.edit','purchaseReturn.detail') AND `field_code` in ('returnMaterialExtendFieldOne','returnMaterialExtendFieldTwo','returnMaterialExtendFieldThree','returnMaterialExtendFieldFour','returnMaterialExtendFieldFive','returnMaterialExtendFieldSix','returnMaterialExtendFieldSeven','returnMaterialExtendFieldEight','returnMaterialExtendFieldNine','returnMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('purchaseReturn.list.material','purchaseReturn.edit','purchaseReturn.detail') AND `field_code` in ('code','amount','purchaseBatchNumber','unqualifiedQuantity','isGift','giftQuantity','returnAmount','batchAmount');
-- UPDATE `dfs_form_field_config` SET `module_code` = 'batchField' WHERE `full_path_code` in ('purchaseReturn.list.material','purchaseReturn.edit','purchaseReturn.detail') AND `field_code` in ('barCode');

-- 委外订单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder' AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder' AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder' AND `field_code` in ('code','materialCode', 'quantity', 'auxiliaryAttr','receiptQuantity','deliveryQuantity','inventoryQuantity','procedureName','materialRemark','haveBom','haveCraft','priority','planStartTime','planEndTime', 'supplier','supplierName','supplierPhone','supplierAddr');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.list.order', 'subcontractOrder.list.order', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.list.material', 'subcontractOrder.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByCreate', 'subcontractOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByRelease', 'subcontractOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByFinish', 'subcontractOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByClosed', 'subcontractOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByCancel', 'subcontractOrder.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.detail', 'subcontractOrder.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.list.material', 'subcontractOrder.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByCreate', 'subcontractOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByRelease', 'subcontractOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByFinish', 'subcontractOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByClosed', 'subcontractOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByCancel', 'subcontractOrder.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.detail', 'subcontractOrder.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.list.material', 'subcontractOrder.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByCreate', 'subcontractOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByRelease', 'subcontractOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByFinish', 'subcontractOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByClosed', 'subcontractOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.editByCancel', 'subcontractOrder.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrder.detail', 'subcontractOrder.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractOrder.list.order','subcontractOrder.list.material','subcontractOrder.edit','subcontractOrder.detail') AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractOrder.list.order','subcontractOrder.list.material','subcontractOrder.edit','subcontractOrder.detail') AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractOrder.list.order','subcontractOrder.list.material','subcontractOrder.edit','subcontractOrder.detail') AND `field_code` in ('code', 'materialCode', 'quantity', 'auxiliaryAttr','receiptQuantity','deliveryQuantity','inventoryQuantity','procedureName','materialRemark','haveBom','haveCraft','priority','planStartTime','planEndTime', 'supplier','supplierName','supplierPhone','supplierAddr');

-- 委外订单收货单
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractReceipt.detail', 'subcontractReceipt.detail', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractReceipt.detail', 'subcontractReceipt.detail', 'materialExtendField', '物料扩展字段', '', '', 0);

call proc_add_form_field_module("subcontractReceipt.detail", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractReceipt.detail", "unitDenominator", "物料计量系数分母", "materialBaseField");

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder' AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder' AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder' AND `field_code` in ('materialCode','receiptQuantity','inputQuantity','auxiliaryAttr');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.list', 'subcontractReceipt.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByCreate', 'subcontractReceipt.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByRelease', 'subcontractReceipt.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByFinish', 'subcontractReceipt.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByClosed', 'subcontractReceipt.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByCancel', 'subcontractReceipt.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.detail', 'subcontractReceipt.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.list', 'subcontractReceipt.list', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByCreate', 'subcontractReceipt.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByRelease', 'subcontractReceipt.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByFinish', 'subcontractReceipt.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByClosed', 'subcontractReceipt.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByCancel', 'subcontractReceipt.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.detail', 'subcontractReceipt.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.list', 'subcontractReceipt.list', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByCreate', 'subcontractReceipt.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByRelease', 'subcontractReceipt.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByFinish', 'subcontractReceipt.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByClosed', 'subcontractReceipt.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.editByCancel', 'subcontractReceipt.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReceipt.detail', 'subcontractReceipt.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractReceipt.list','subcontractReceipt.edit','subcontractReceipt.detail') AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractReceipt.list','subcontractReceipt.edit','subcontractReceipt.detail') AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractReceipt.list','subcontractReceipt.edit','subcontractReceipt.detail') AND `field_code` in ('materialCode','receiptQuantity','inputQuantity','auxiliaryAttr');

-- 委外订单退货单
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder' AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen','orderExtendFieldOneName','orderExtendFieldTwoName','orderExtendFieldThreeName','orderExtendFieldFourName','orderExtendFieldFiveName','orderExtendFieldSixName','orderExtendFieldSevenName','orderExtendFieldEightName','orderExtendFieldNineName','orderExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder' AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen','orderMaterialExtendFieldOneName','orderMaterialExtendFieldTwoName','orderMaterialExtendFieldThreeName','orderMaterialExtendFieldFourName','orderMaterialExtendFieldFiveName','orderMaterialExtendFieldSixName','orderMaterialExtendFieldSevenName','orderMaterialExtendFieldEightName','orderMaterialExtendFieldNineName','orderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder' AND `field_code` in ('materialCode','auxiliaryAttr','amount');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.list', 'subcontractReturn.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.detail', 'subcontractReturn.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByCreate', 'subcontractReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByRelease', 'subcontractReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByFinish', 'subcontractReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByClosed', 'subcontractReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByCancel', 'subcontractReturn.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.list', 'subcontractReturn.list', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.detail', 'subcontractReturn.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByCreate', 'subcontractReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByRelease', 'subcontractReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByFinish', 'subcontractReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByClosed', 'subcontractReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByCancel', 'subcontractReturn.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.list', 'subcontractReturn.list', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.detail', 'subcontractReturn.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByCreate', 'subcontractReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByRelease', 'subcontractReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByFinish', 'subcontractReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByClosed', 'subcontractReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractReturn.editByCancel', 'subcontractReturn.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractReturn.list','subcontractReturn.edit','subcontractReturn.detail') AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen', 'orderExtendFieldOneName','orderExtendFieldTwoName','orderExtendFieldThreeName','orderExtendFieldFourName','orderExtendFieldFiveName','orderExtendFieldSixName','orderExtendFieldSevenName','orderExtendFieldEightName','orderExtendFieldNineName','orderExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractReturn.list','subcontractReturn.edit','subcontractReturn.detail') AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen','orderMaterialExtendFieldOneName','orderMaterialExtendFieldTwoName','orderMaterialExtendFieldThreeName','orderMaterialExtendFieldFourName','orderMaterialExtendFieldFiveName','orderMaterialExtendFieldSixName','orderMaterialExtendFieldSevenName','orderMaterialExtendFieldEightName','orderMaterialExtendFieldNineName','orderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractReturn.list','subcontractReturn.edit','subcontractReturn.detail') AND `field_code` in ('materialCode','auxiliaryAttr','amount');

-- 委外发料单
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractDelivery.list', 'subcontractDelivery.list', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractDelivery.detail', 'subcontractDelivery.detail', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'materialBaseField', '物料基础字段', '', '', 1);

call proc_add_form_field_module("subcontractDelivery.detail", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.detail", "unitDenominator", "物料计量系数分母", "materialBaseField");

call proc_add_form_field_module("subcontractDelivery.edit", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.edit", "unitDenominator", "物料计量系数分母", "materialBaseField");

call proc_add_form_field_module("subcontractDelivery.list", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractDelivery.list", "unitDenominator", "物料计量系数分母", "materialBaseField");

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

DELETE FROM `dfs_form_field_config` WHERE `field_code` in ('name','standard','comp') and `full_path_code` in ('subcontractDelivery.list', 'subcontractDelivery.detail', 'subcontractDelivery.edit') and `module_code` = 'baseField';
DELETE FROM `dfs_form_field_rule_config` WHERE `field_code` in ('name','standard','comp') and `field_name_full_path_code` in ('subcontractDelivery.list', 'subcontractDelivery.detail', 'subcontractDelivery.edit') and `module_code` = 'baseField';

UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial' AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen', 'orderExtendFieldOneName','orderExtendFieldTwoName','orderExtendFieldThreeName','orderExtendFieldFourName','orderExtendFieldFiveName','orderExtendFieldSixName','orderExtendFieldSevenName','orderExtendFieldEightName','orderExtendFieldNineName','orderExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial' AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen', 'orderMaterialExtendFieldOneName','orderMaterialExtendFieldTwoName','orderMaterialExtendFieldThreeName','orderMaterialExtendFieldFourName','orderMaterialExtendFieldFiveName','orderMaterialExtendFieldSixName','orderMaterialExtendFieldSevenName','orderMaterialExtendFieldEightName','orderMaterialExtendFieldNineName','orderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial' AND `field_code` in ('materialCode','auxiliaryAttr','deliveryQuantity','remark');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.list', 'subcontractDelivery.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.detail', 'subcontractDelivery.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.list', 'subcontractDelivery.list', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.detail', 'subcontractDelivery.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.list', 'subcontractDelivery.list', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.detail', 'subcontractDelivery.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByCreate', 'subcontractDelivery.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByRelease', 'subcontractDelivery.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByFinish', 'subcontractDelivery.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractDelivery.editByCancel', 'subcontractDelivery.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractDelivery.list','subcontractDelivery.edit','subcontractDelivery.detail') AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen', 'orderExtendFieldOneName','orderExtendFieldTwoName','orderExtendFieldThreeName','orderExtendFieldFourName','orderExtendFieldFiveName','orderExtendFieldSixName','orderExtendFieldSevenName','orderExtendFieldEightName','orderExtendFieldNineName','orderExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractDelivery.list','subcontractDelivery.edit','subcontractDelivery.detail') AND `field_code` in ('orderMaterialExtendFieldOne','orderMaterialExtendFieldTwo','orderMaterialExtendFieldThree','orderMaterialExtendFieldFour','orderMaterialExtendFieldFive','orderMaterialExtendFieldSix','orderMaterialExtendFieldSeven','orderMaterialExtendFieldEight','orderMaterialExtendFieldNine','orderMaterialExtendFieldTen', 'orderMaterialExtendFieldOneName','orderMaterialExtendFieldTwoName','orderMaterialExtendFieldThreeName','orderMaterialExtendFieldFourName','orderMaterialExtendFieldFiveName','orderMaterialExtendFieldSixName','orderMaterialExtendFieldSevenName','orderMaterialExtendFieldEightName','orderMaterialExtendFieldNineName','orderMaterialExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractDelivery.list','subcontractDelivery.edit','subcontractDelivery.detail') AND `field_code` in ('materialCode','auxiliaryAttr','deliveryQuantity','remark');

-- 委外订单用料清单
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'materialBaseField', '物料基础字段', '', '', 1);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button) VALUES('subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'materialBaseField', '物料基础字段', '', '', 1);

call proc_add_form_field_module("subcontractOrderMaterial.detail", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.detail", "unitDenominator", "物料计量系数分母", "materialBaseField");

call proc_add_form_field_module("subcontractOrderMaterial.edit", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.edit", "unitDenominator", "物料计量系数分母", "materialBaseField");

call proc_add_form_field_module("subcontractOrderMaterial.list.material", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractOrderMaterial.list.material", "unitDenominator", "物料计量系数分母", "materialBaseField");

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

DELETE FROM `dfs_form_field_config` WHERE `field_code` in ('name', 'unit') and `full_path_code` in ('subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.edit') and `module_code` = 'baseField';
DELETE FROM `dfs_form_field_rule_config` WHERE `field_code` in ('name', 'unit') and `field_name_full_path_code` in ('subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.edit') and `module_code` = 'baseField';

UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder' AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen', 'orderExtendFieldOneName','orderExtendFieldTwoName','orderExtendFieldThreeName','orderExtendFieldFourName','orderExtendFieldFiveName','orderExtendFieldSixName','orderExtendFieldSevenName','orderExtendFieldEightName','orderExtendFieldNineName','orderExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder' AND `field_code` in ('orderLineExtendFieldOne','orderLineExtendFieldTwo','orderLineExtendFieldThree','orderLineExtendFieldFour','orderLineExtendFieldFive','orderLineExtendFieldSix','orderLineExtendFieldSeven','orderLineExtendFieldEight','orderLineExtendFieldNine','orderLineExtendFieldTen','orderLineMaterialExtendFieldOne','orderLineMaterialExtendFieldTwo','orderLineMaterialExtendFieldThree','orderLineMaterialExtendFieldFour','orderLineMaterialExtendFieldFive','orderLineMaterialExtendFieldSix','orderLineMaterialExtendFieldSeven','orderLineMaterialExtendFieldEight','orderLineMaterialExtendFieldNine','orderLineMaterialExtendFieldTen', 'orderLineMaterialExtendFieldOneName','orderLineMaterialExtendFieldTwoName','orderLineMaterialExtendFieldThreeName','orderLineMaterialExtendFieldFourName','orderLineMaterialExtendFieldFiveName','orderLineMaterialExtendFieldSixName','orderLineMaterialExtendFieldSevenName','orderLineMaterialExtendFieldEightName','orderLineMaterialExtendFieldNineName','orderLineMaterialExtendFieldTenName');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder' AND `field_code` in ('lineMaterialCode','lineName','lineAuxiliaryAttr','linePlanQuantity','lineSupplierName','materialCode','auxiliaryAttr','bomNumerator','bomDenominator','planQuantity','actualQuantity','stockQuantity','takeOutQuantity','returnQuantity','subType','mainReplaceScale','replaceQuantity');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.list.order', 'subcontractOrderMaterial.list.order', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByCreate', 'subcontractOrderMaterial.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByRelease', 'subcontractOrderMaterial.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByFinish', 'subcontractOrderMaterial.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByClosed', 'subcontractOrderMaterial.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('subcontractOrderMaterial.editByCancel', 'subcontractOrderMaterial.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractOrderMaterial.list.order','subcontractOrderMaterial.list.material','subcontractOrderMaterial.edit','subcontractOrderMaterial.detail') AND `field_code` in ('orderExtendFieldOne','orderExtendFieldTwo','orderExtendFieldThree','orderExtendFieldFour','orderExtendFieldFive','orderExtendFieldSix','orderExtendFieldSeven','orderExtendFieldEight','orderExtendFieldNine','orderExtendFieldTen', 'orderExtendFieldOneName','orderExtendFieldTwoName','orderExtendFieldThreeName','orderExtendFieldFourName','orderExtendFieldFiveName','orderExtendFieldSixName','orderExtendFieldSevenName','orderExtendFieldEightName','orderExtendFieldNineName','orderExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractOrderMaterial.list.order','subcontractOrderMaterial.list.material','subcontractOrderMaterial.edit','subcontractOrderMaterial.detail') AND `field_code` in ('orderLineExtendFieldOne','orderLineExtendFieldTwo','orderLineExtendFieldThree','orderLineExtendFieldFour','orderLineExtendFieldFive','orderLineExtendFieldSix','orderLineExtendFieldSeven','orderLineExtendFieldEight','orderLineExtendFieldNine','orderLineExtendFieldTen','orderLineMaterialExtendFieldOne','orderLineMaterialExtendFieldTwo','orderLineMaterialExtendFieldThree','orderLineMaterialExtendFieldFour','orderLineMaterialExtendFieldFive','orderLineMaterialExtendFieldSix','orderLineMaterialExtendFieldSeven','orderLineMaterialExtendFieldEight','orderLineMaterialExtendFieldNine','orderLineMaterialExtendFieldTen', 'orderLineMaterialExtendFieldOneName','orderLineMaterialExtendFieldTwoName','orderLineMaterialExtendFieldThreeName','orderLineMaterialExtendFieldFourName','orderLineMaterialExtendFieldFiveName','orderLineMaterialExtendFieldSixName','orderLineMaterialExtendFieldSevenName','orderLineMaterialExtendFieldEightName','orderLineMaterialExtendFieldNineName','orderLineMaterialExtendFieldTenName');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('subcontractOrderMaterial.list.order','subcontractOrderMaterial.list.material','subcontractOrderMaterial.edit','subcontractOrderMaterial.detail') AND `field_code` in ('lineMaterialCode','lineName','lineAuxiliaryAttr','linePlanQuantity','lineSupplierName','materialCode','auxiliaryAttr','bomNumerator','bomDenominator','planQuantity','actualQuantity','stockQuantity','takeOutQuantity','returnQuantity','subType','mainReplaceScale','replaceQuantity');

-- 生产工单报工记录
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/workorder-model/production-records' AND `field_code` in ('reportFieldOneName','reportFieldTwoName','reportFieldThreeName','reportFieldFourName','reportFieldFiveName');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('workOrderReport.list', 'workOrderReport.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('workOrderReport.list') AND `field_code` in ('reportFieldOneName','reportFieldTwoName','reportFieldThreeName','reportFieldFourName','reportFieldFiveName');


-- 供应商物料清单
DELETE FROM `dfs_form_field_config` WHERE `field_code` in ('name', 'comp') and `full_path_code` in ('supplierMaterial.list') and `module_code` = 'baseField';
DELETE FROM `dfs_form_field_rule_config` WHERE `field_code` in ('name', 'comp') and `field_name_full_path_code` in ('supplierMaterial.list') and `module_code` = 'baseField';

UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/supplyList' AND `field_code` in ('supplierMaterialList.supplierMaterialExtendFieldOne','supplierMaterialList.supplierMaterialExtendFieldTwo','supplierMaterialList.supplierMaterialExtendFieldThree','supplierMaterialList.supplierMaterialExtendFieldFour','supplierMaterialList.supplierMaterialExtendFieldFive','supplierMaterialList.supplierMaterialExtendFieldSix','supplierMaterialList.supplierMaterialExtendFieldSeven','supplierMaterialList.supplierMaterialExtendFieldEight','supplierMaterialList.supplierMaterialExtendFieldNine','supplierMaterialList.supplierMaterialExtendFieldTen','supplierMaterialExtendFieldOne','supplierMaterialExtendFieldTwo','supplierMaterialExtendFieldThree','supplierMaterialExtendFieldFour','supplierMaterialExtendFieldFive','supplierMaterialExtendFieldSix','supplierMaterialExtendFieldSeven','supplierMaterialExtendFieldEight','supplierMaterialExtendFieldNine','supplierMaterialExtendFieldTen');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/procurement-management/supplyList' AND `field_code` in ('stateName','supplierMaterialCode','supplierMaterialName','supplierMaterialStandard','supplierMaterialList.materialFields.code', 'no', 'supplierMaterialList.state','supplierMaterialList.supplierMaterialCode','supplierMaterialList.supplierMaterialName', 'supplierMaterialList.supplierMaterialStandard');
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseMaterialLineField' WHERE `route` = '/supply-chain-collaboration/procurement-management/supplyList' AND `field_code` in ('code') AND `full_path_code` = 'supplierMaterial.list';

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplierMaterial.list', 'supplierMaterial.list', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplierMaterial.edit', 'supplierMaterial.edit', 'baseMaterialLineField', '物料行字段', '', '', 1, 0, 2);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplierMaterial.list', 'supplierMaterial.list', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplierMaterial.edit', 'supplierMaterial.edit', 'baseMaterialLineExtendField', '物料行字段扩展字段', '', '', 1, 0, 3);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('supplierMaterial.list','supplierMaterial.edit') AND `field_code` in ('supplierMaterialList.supplierMaterialExtendFieldOne','supplierMaterialList.supplierMaterialExtendFieldTwo','supplierMaterialList.supplierMaterialExtendFieldThree','supplierMaterialList.supplierMaterialExtendFieldFour','supplierMaterialList.supplierMaterialExtendFieldFive','supplierMaterialList.supplierMaterialExtendFieldSix','supplierMaterialList.supplierMaterialExtendFieldSeven','supplierMaterialList.supplierMaterialExtendFieldEight','supplierMaterialList.supplierMaterialExtendFieldNine','supplierMaterialList.supplierMaterialExtendFieldTen','supplierMaterialExtendFieldOne','supplierMaterialExtendFieldTwo','supplierMaterialExtendFieldThree','supplierMaterialExtendFieldFour','supplierMaterialExtendFieldFive','supplierMaterialExtendFieldSix','supplierMaterialExtendFieldSeven','supplierMaterialExtendFieldEight','supplierMaterialExtendFieldNine','supplierMaterialExtendFieldTen');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('supplierMaterial.list','supplierMaterial.edit') AND `field_code` in ('stateName','supplierMaterialCode','supplierMaterialName','supplierMaterialStandard','supplierMaterialList.materialFields.code', 'no', 'supplierMaterialList.state','supplierMaterialList.supplierMaterialCode','supplierMaterialList.supplierMaterialName', 'supplierMaterialList.supplierMaterialStandard');
UPDATE `dfs_form_field_config` SET `module_code` = 'baseMaterialLineField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('supplierMaterial.list') AND `field_code` in ('code');

-- 供应商档案
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'baseExtendField' WHERE `route` = '/supply-chain-collaboration/procurement-management/supplier-profile' AND `field_code` in ('supplierExtendFieldOne','supplierExtendFieldTwo','supplierExtendFieldThree','supplierExtendFieldFour','supplierExtendFieldFive','supplierExtendFieldSix','supplierExtendFieldSeven','supplierExtendFieldEight','supplierExtendFieldNine','supplierExtendFieldTen');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplier.list', 'supplier.list', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplier.detail', 'supplier.detail', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplier.edit.byRelease', 'supplier.edit.byRelease', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplier.edit.byDisable', 'supplier.edit.byDisable', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('supplier.edit.byCreate', 'supplier.edit.byCreate', 'baseExtendField', '基本信息扩展字段', '', '', 1, 0, 1);

UPDATE `dfs_form_field_config` SET `module_code` = 'baseExtendField' WHERE `module_code` = 'baseField' AND `full_path_code` in ('supplier.list', 'supplier.detail', 'supplier.edit.byRelease', 'supplier.edit.byDisable', 'supplier.edit.byCreate') AND `field_code` in ('supplierExtendFieldOne','supplierExtendFieldTwo','supplierExtendFieldThree','supplierExtendFieldFour','supplierExtendFieldFive','supplierExtendFieldSix','supplierExtendFieldSeven','supplierExtendFieldEight','supplierExtendFieldNine','supplierExtendFieldTen');

-- 销售订单-详情增加创建人、创建时间字段
call proc_add_form_field("saleOrder.detail", "createName", "创建人");
call proc_add_form_field("saleOrder.detail", "createTime", "创建时间");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/order-model/salesOrder', 'saleOrder.detail', 'saleOrder.detail', 'createName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/order-model/salesOrder', 'saleOrder.detail', 'saleOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 生产订单-详情增加创建人、创建时间字段
call proc_add_form_field("productOrder.detail", "createName", "创建人");
call proc_add_form_field("productOrder.detail", "createTime", "创建时间");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'createName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购订单-详情增加创建人、创建时间字段
call proc_add_form_field("purchaseOrder.detail", "createByNickname", "创建人");
call proc_add_form_field("purchaseOrder.detail", "createTime", "创建时间");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.detail', 'purchaseOrder.detail', 'createByNickname', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.detail', 'purchaseOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购收料单-详情增加创建人、创建时间字段
DELETE FROM dfs_form_field_config WHERE module_code = "baseField" AND full_path_code="purchaseReceiptOrder.detail" AND field_code = "createByNickName";
DELETE FROM dfs_form_field_config WHERE module_code = "baseField" AND full_path_code="purchaseReceiptOrder.detail" AND field_code = "createTime";
call proc_add_form_field("purchaseReceiptOrder.detail", "createByNickName", "创建人");
call proc_add_form_field("purchaseReceiptOrder.detail", "createTime", "创建时间");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'createByNickName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购退料单-详情增加创建人、创建时间字段
call proc_add_form_field("purchaseReturn.detail", "createByNickName", "创建人");
call proc_add_form_field("purchaseReturn.detail", "createTime", "创建时间");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'createByNickName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 委外订单退货单-详情增加创建人、创建时间字段
call proc_add_form_field("subcontractReturn.detail", "createByNickName", "创建人");
call proc_add_form_field("subcontractReturn.detail", "createTime", "创建时间");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'createByNickName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 出货申请单-客户物料编码、客户物料名称字段增加表单配置
call proc_add_form_field_module("deliveryApplication.detail", "customerMaterialCode", "客户物料编码", "baseMaterialLineField");
call proc_add_form_field_module("deliveryApplication.detail", "customerMaterialName", "客户物料名称", "baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.detail', 'deliveryApplication.detail', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`)VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.detail', 'deliveryApplication.detail', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
call proc_add_form_field_module("deliveryApplication.list.material", "customerMaterialCode", "客户物料编码", "baseMaterialLineField");
call proc_add_form_field_module("deliveryApplication.list.material", "customerMaterialName", "客户物料名称", "baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.list.material', 'deliveryApplication.list.material', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.list.material', 'deliveryApplication.list.material', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
call proc_add_form_field_module("deliveryApplication.edit", "customerMaterialCode", "客户物料编码", "baseMaterialLineField");
call proc_add_form_field_module("deliveryApplication.edit", "customerMaterialName", "客户物料名称", "baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCreate', 'deliveryApplication.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByRelease', 'deliveryApplication.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByFinish', 'deliveryApplication.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByClosed', 'deliveryApplication.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCancel', 'deliveryApplication.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCreate', 'deliveryApplication.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByRelease', 'deliveryApplication.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByFinish', 'deliveryApplication.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByClosed', 'deliveryApplication.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCancel', 'deliveryApplication.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');


-- 委外发料单单据类型、业务类型字段
call proc_add_form_field("subcontractDelivery.edit", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', "subcontractDelivery.edit", "orderType", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', "subcontractDelivery.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', "subcontractDelivery.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', "subcontractDelivery.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractDelivery.detail", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractDelivery.list", "orderTypeName", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.list', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractDelivery.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCreate', "subcontractDelivery.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByRelease', "subcontractDelivery.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByFinish', "subcontractDelivery.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.editByCancel', "subcontractDelivery.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractDelivery.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.detail', 'subcontractDelivery.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractDelivery.list", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDelivery.list', 'subcontractDelivery.list', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 委外订单用料清单单据类型、业务类型字段
call proc_add_form_field("subcontractOrderMaterial.edit", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', "subcontractOrderMaterial.edit", "orderType", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', "subcontractOrderMaterial.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', "subcontractOrderMaterial.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', "subcontractOrderMaterial.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', "subcontractOrderMaterial.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrderMaterial.detail", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrderMaterial.list.order", "orderTypeName", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.order', 'subcontractOrderMaterial.list.order', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrderMaterial.list.material", "orderTypeName", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrderMaterial.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCreate', "subcontractOrderMaterial.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByRelease', "subcontractOrderMaterial.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByFinish', "subcontractOrderMaterial.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByClosed', "subcontractOrderMaterial.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.editByCancel', "subcontractOrderMaterial.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrderMaterial.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.detail', 'subcontractOrderMaterial.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrderMaterial.list.order", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.order', 'subcontractOrderMaterial.list.order', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrderMaterial.list.material", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial.list.material', 'subcontractOrderMaterial.list.material', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.17.1.1=======================================================


-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- 最后执行
-- 将baseExtendField(单据基本信息扩展字段)、baseMaterialLineExtendField(单据物料行扩展字段)、materialExtendField(物料扩展字段)归纳为扩展字段
UPDATE `dfs_form_field_module_config` SET `parent_module_code` = 'extend', `parent_module_name` = '扩展字段' WHERE `module_code` = 'baseExtendField' OR `module_code` = 'baseMaterialLineExtendField' OR `module_code` = 'materialExtendField';
-- 将baseField(基本信息)、baseMaterialLineField(物料行字段)、materialBaseField(物料基础字段)、batchField(批次字段)归纳为基础字段
UPDATE `dfs_form_field_module_config` SET `parent_module_code` = 'base', `parent_module_name` = '基础字段' WHERE `module_code` = 'baseField' OR `module_code` = 'baseMaterialLineField' OR `module_code` = 'materialBaseField' OR `module_code` = 'batchField';

-- 将'基本信息'重命名成'单据基本信息'，'物料行字段'重命名成'单据物料行字段'
UPDATE `dfs_form_field_module_config` SET `module_name` = '单据物料行字段' WHERE `module_code` = 'baseMaterialLineField';
UPDATE `dfs_form_field_module_config` SET `module_name` = '单据基本信息' WHERE `module_code` = 'baseField';
UPDATE `dfs_form_field_module_config` SET `module_name` = '单据物料行扩展字段' WHERE `module_code` = 'baseMaterialLineExtendField';
UPDATE `dfs_form_field_module_config` SET `module_name` = '单据基本信息扩展字段' WHERE `module_code` = 'baseExtendField';

-- 更新类型分类顺序
UPDATE `dfs_form_field_module_config` SET `sort` = 1 WHERE `module_code` = 'baseField';
UPDATE `dfs_form_field_module_config` SET `sort` = 10 WHERE `module_code` = 'baseMaterialLineField';
UPDATE `dfs_form_field_module_config` SET `sort` = 20 WHERE `module_code` = 'materialBaseField';
UPDATE `dfs_form_field_module_config` SET `sort` = 2 WHERE `module_code` = 'baseExtendField';
UPDATE `dfs_form_field_module_config` SET `sort` = 11 WHERE `module_code` = 'baseMaterialLineExtendField';
UPDATE `dfs_form_field_module_config` SET `sort` = 21 WHERE `module_code` = 'materialExtendField';

-- 存在重复数据，需要删除其中一条数据
DELETE t1 FROM dfs_form_field_rule_config t1
INNER JOIN dfs_form_field_rule_config t2
WHERE
    t1.route = t2.route
    AND t1.full_path_code = t2.full_path_code
    AND t1.field_code = t2.field_code
    AND t1.module_code = t2.module_code
    AND t1.id > t2.id;

-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
-- ================================================此段代码最后执行！！！请勿再此插入代码=======================================================
