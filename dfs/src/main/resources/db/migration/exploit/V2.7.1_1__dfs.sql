-- DDL

call proc_add_column(
        'dfs_order_execute_seq',
        'line_id',
        'ALTER TABLE `dfs_order_execute_seq` ADD COLUMN `line_id` int(0) NULL COMMENT ''产线id'' AFTER `device_id`');

-- 先删索引再加
call proc_drop_column_index(
        'dfs_order_execute_seq',
        'device_id',
        'union');

call proc_add_column_index('dfs_order_execute_seq','order_number,device_id,line_id,create_time','union');

-- 工位加是否同时计算工单字段
call proc_add_column(
        'dfs_facilities',
        'is_cal_concurrently',
        'ALTER TABLE `dfs_facilities` ADD COLUMN `is_cal_concurrently` tinyint(1) NULL DEFAULT 0 COMMENT ''是否支持同时采集多个工单''');

-- 状态配置公式输入值改为字符
call proc_modify_column(
        'dfs_target_status_formula',
        'target_value_one',
        'ALTER TABLE `dfs_target_status_formula` MODIFY COLUMN `target_value_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''0.00'' COMMENT ''状态公式的输入值1''');
call proc_modify_column(
        'dfs_target_status_formula',
        'target_value_two',
        'ALTER TABLE `dfs_target_status_formula` MODIFY COLUMN `target_value_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT ''0.00'' COMMENT ''状态公式的输入值2''');

call proc_add_column(
'dfs_record_device_maintain',
'last_update_time',
'ALTER TABLE `dfs_record_device_maintain` ADD COLUMN `last_update_time` datetime(0) NULL COMMENT ''最后一次更新时间'' AFTER `time`');

-- DML
-- 取消出入口位置，更新工位绑定采集器的位置
update dfs_fac_sensor SET `position` = 2;

-- 工艺工序导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10803310', '工艺工序导出', 'craftProcedureExport', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1);

-- 角色初始化脚本
UPDATE `sys_roles` SET `name` = '系统运维' WHERE `role_code` = 'oncall';
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('销售', NULL, NULL, 'enable', NULL, 0);
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('采购', NULL, NULL, 'enable', NULL, 0);
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('仓库', NULL, NULL, 'enable', NULL, 0);
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('生产', NULL, NULL, 'enable', NULL, 0);
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('设计', NULL, NULL, 'enable', NULL, 0);
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('质检', NULL, NULL, 'enable', NULL, 0);
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('财务', NULL, NULL, 'enable', NULL, 0);
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`, `description`, `all_work_center`) VALUES ('运营', NULL, NULL, 'enable', NULL, 0);

-- 默认下推配置 销售订单下推销售出库单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('saleOrder', 'saleInAndOut', 'jumpPageSaleOut', 'enable', 'false', 'saleOrder下推saleInAndOut配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('saleOrder', 'saleInAndOut', 'jumpPageSaleOut', 'originalOrderStates', '[2]', 'saleOrder下推saleInAndOut配置');
-- 默认下推配置 销售订单下推销售退货入库单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('saleOrder', 'saleInAndOut', 'jumpPageSalesReturnReceipt', 'enable', 'false', 'saleOrder下推saleInAndOut配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('saleOrder', 'saleInAndOut', 'jumpPageSalesReturnReceipt', 'originalOrderStates', '[2]', 'saleOrder下推saleInAndOut配置');
-- 默认下推配置 采购订单下推采购入库单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('purchaseOrder', 'purchaseIn', 'jumpPage', 'enable', 'false', 'purchaseOrder下推purchaseIn配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('purchaseOrder', 'purchaseIn', 'jumpPage', 'originalOrderStates', '[2]', 'purchaseOrder下推purchaseIn配置');
-- 默认下推配置 生产订单用料清单 下推 生产补料出库单、调拨单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('productMaterialsList', 'workOrderSupplement', 'jumpPage', 'enable', 'false', 'productMaterialsList下推workOrderSupplement配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('productMaterialsList', 'workOrderSupplement', 'jumpPage', 'originalOrderStates', '[2]', 'productMaterialsList下推workOrderSupplement配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('productMaterialsList', 'transferOrder', 'jumpPage', 'enable', 'false', 'productMaterialsList下推transferOrder配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('productMaterialsList', 'transferOrder', 'jumpPage', 'originalOrderStates', '[2]', 'productMaterialsList下推transferOrder配置');
-- 默认下推配置 委外订单用料清单 下推 调拨单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractMaterialsList', 'transferOrder', 'jumpPage', 'enable', 'false', 'subcontractMaterialsList下推transferOrder配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractMaterialsList', 'transferOrder', 'jumpPage', 'originalOrderStates', '[2]', 'subcontractMaterialsList下推transferOrder配置');
-- 默认下推配置 委外订单 下推 委外收货单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractOrder', 'subcontractReceiptOrder', 'jumpPage', 'enable', 'false', 'subcontractOrder下推subcontractReceiptOrder配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractOrder', 'subcontractReceiptOrder', 'jumpPage', 'originalOrderStates', '[2]', 'subcontractOrder下推subcontractReceiptOrder配置');

-- 处理 产线表 aid 字段填充
UPDATE dfs_production_line line SET line.aid = (SELECT gird.aid FROM dfs_grid gird WHERE gird.gid = line.gid);

-- 计数器和机器手环设置默认productKey
update dfs_sensor_product set product_key = 'YL_WiFiCounter' where sensor_type = 'newCounter' and (product_key is null or product_key = '');
update dfs_sensor_product set product_key = 'YL_WiFiModbox' where sensor_type = 'wristband' and (product_key is null or product_key = '');

-- 仅备份表结构的额外配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('BackupTableStructure', '仅备份表结构的表', 'BackupTableStructure', NULL, NULL, 'dfs_sensor_record,dfs_count,dfs_operation_log', NULL, NULL, NULL, NULL, NULL);

update dfs_bom_raw_material  SET  bom_type ='1001'   WHERE  bom_type = '标准件';
update dfs_bom_raw_material  SET  bom_type ='1002'   WHERE  bom_type = '替代件';
