-- 指标相关sql

update `dfs_metrics`.`dfs_metrics_valuation_cal` v
    left join `dfs_valuation_config` vc on v.valuation_config_id = vc.id
    set v.valuation =
        CASE
        WHEN vc.valuation_method = 'duration' THEN v.cal_duration_h * vc.unit_price
        WHEN vc.valuation_method = 'count'    THEN v.cal_count * vc.unit_price
        ELSE 0  -- 如果既不是duration也不是count，保持原值（或设为NULL/默认值）
END
WHERE v.valuation is null	and v.state = 0;