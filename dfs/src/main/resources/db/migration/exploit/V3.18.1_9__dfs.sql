-- 上料防错增加字段
call  `proc_add_column`(
        'dfs_work_order_material_check_material',
        'is_support_code_manage',
        'ALTER TABLE `dfs_work_order_material_check_material` ADD COLUMN `is_support_code_manage`  tinyint(4) DEFAULT 1 COMMENT  ''是否支持生产流水码管理'';');

-- 委外订单退料单补齐物料基础字段和物料扩展字段
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button, `sort`) VALUES('subcontractReturn.list', 'subcontractReturn.list', 'materialBaseField', '物料基础字段', 'base', '基础字段', 1, 20);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button, `sort`) VALUES('subcontractReturn.detail', 'subcontractReturn.detail', 'materialBaseField', '物料基础字段', 'base', '基础字段', 1, 20);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button, `sort`) VALUES('subcontractReturn.editByCreate', 'subcontractReturn.edit', 'materialBaseField', '物料基础字段', 'base', '基础字段', 1, 20);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button, `sort`) VALUES('subcontractReturn.editByRelease', 'subcontractReturn.edit', 'materialBaseField', '物料基础字段', 'base', '基础字段', 1, 20);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button, `sort`) VALUES('subcontractReturn.editByFinish', 'subcontractReturn.edit', 'materialBaseField', '物料基础字段', 'base', '基础字段', 1, 20);
INSERT INTO dfs_form_field_module_config (full_path_code, field_name_full_path_code, module_code, module_name, `parent_module_code`, `parent_module_name`, have_save_button, `sort`) VALUES('subcontractReturn.editByCancel', 'subcontractReturn.edit', 'materialBaseField', '物料基础字段', 'base', '基础字段', 1, 20);

call proc_add_form_field_module("subcontractReturn.list", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "unitDenominator", "物料计量系数分母", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "standard", "物料规格", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "remark", "物料备注", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "drawingNumber", "图号", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "materialPrice", "物料单价", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "loseRate", "损耗率", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "rawMaterial", "材质", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "nameEnglish", "物料英文名称", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "minimumProductionLot", "最小生产批量", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.list", "factoryModel", "工厂型号", "materialBaseField");

call proc_add_form_field_module("subcontractReturn.edit", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "unitDenominator", "物料计量系数分母", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "standard", "物料规格", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "remark", "物料备注", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "drawingNumber", "图号", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "materialPrice", "物料单价", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "loseRate", "损耗率", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "rawMaterial", "材质", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "nameEnglish", "物料英文名称", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "minimumProductionLot", "最小生产批量", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.edit", "factoryModel", "工厂型号", "materialBaseField");

call proc_add_form_field_module("subcontractReturn.detail", "name", "物料名称", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "sortName", "物料分类", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "typeName", "物料类型", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "state", "物料状态", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "comp", "物料单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "level", "物料等级", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "version", "物料版本", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "isBatchMag", "物料是否按批次管理", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "isSupportCodeManage", "物料是否按流水码管理", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "isAuxiliaryMaterial", "物料是否辅料", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "isDoubleUnit", "物料是否为双单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "editorName", "物料编制人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "createByNickname", "物料创建人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "createTime", "物料创建时间", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "updateByName", "物料更新人", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "updateTime", "物料更新时间", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "unit", "物料计量单位", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "qualityLevel", "物料质量等级", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "unitNumerator", "物料计量系数分子", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "unitDenominator", "物料计量系数分母", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "standard", "物料规格", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "remark", "物料备注", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "drawingNumber", "图号", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "materialPrice", "物料单价", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "loseRate", "损耗率", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "rawMaterial", "材质", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "nameEnglish", "物料英文名称", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "minimumProductionLot", "最小生产批量", "materialBaseField");
call proc_add_form_field_module("subcontractReturn.detail", "factoryModel", "工厂型号", "materialBaseField");

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);


INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'name', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'sortName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'typeName', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'state', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'comp', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'level', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'version', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'isBatchMag', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'isSupportCodeManage', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'isAuxiliaryMaterial', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'isDoubleUnit', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'editorName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'createByNickname', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'createTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'updateByName', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'updateTime', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'unit', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'qualityLevel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'unitNumerator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'unitDenominator', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);



call proc_add_form_field_module("subcontractReturn.detail", "customFieldOne", "扩展字段1", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldTwo", "扩展字段2", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldThree", "扩展字段3", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldFour", "扩展字段4", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldFive", "扩展字段5", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldSix", "扩展字段6", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldSeven", "扩展字段7", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldEight", "扩展字段8", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldNine", "扩展字段9", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldTen", "扩展字段10", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.detail", "customFieldEleven", "扩展字段11", "materialExtendField");

call proc_add_form_field_module("subcontractReturn.edit", "customFieldOne", "扩展字段1", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldTwo", "扩展字段2", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldThree", "扩展字段3", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldFour", "扩展字段4", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldFive", "扩展字段5", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldSix", "扩展字段6", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldSeven", "扩展字段7", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldEight", "扩展字段8", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldNine", "扩展字段9", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldTen", "扩展字段10", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.edit", "customFieldEleven", "扩展字段11", "materialExtendField");

call proc_add_form_field_module("subcontractReturn.list", "customFieldOne", "扩展字段1", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldTwo", "扩展字段2", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldThree", "扩展字段3", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldFour", "扩展字段4", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldFive", "扩展字段5", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldSix", "扩展字段6", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldSeven", "扩展字段7", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldEight", "扩展字段8", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldNine", "扩展字段9", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldTen", "扩展字段10", "materialExtendField");
call proc_add_form_field_module("subcontractReturn.list", "customFieldEleven", "扩展字段11", "materialExtendField");



INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldOne', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldTwo', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldThree', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldFour', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldFive', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldSix', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldSeven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldEight', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldNine', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldTen', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'customFieldEleven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldOne', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldTwo', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldThree', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldFour', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldFive', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldSix', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldSeven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldEight', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldNine', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldTen', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'customFieldEleven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldOne', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldTwo', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldThree', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldFour', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldFive', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldSix', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldSeven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldEight', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldNine', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldTen', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'customFieldEleven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldOne', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldTwo', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldThree', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldFour', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldFive', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldSix', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldSeven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldEight', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldNine', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldTen', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'customFieldEleven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldOne', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldTwo', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldThree', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldFour', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldFive', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldSix', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldSeven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldEight', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldNine', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldTen', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'customFieldEleven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldOne', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldTwo', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldThree', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldFour', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldFive', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldSix', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldSeven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldEight', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldNine', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldTen', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'customFieldEleven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldOne', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldTwo', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldThree', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldFour', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldFive', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldSix', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldSeven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldEight', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldNine', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldTen', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'customFieldEleven', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialExtendField', 0);


INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'standard', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'remark', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'drawingNumber', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'materialPrice', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'loseRate', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'rawMaterial', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'nameEnglish', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'minimumProductionLot', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.list', 'subcontractReturn.list', 'factoryModel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'standard', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'remark', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'drawingNumber', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'materialPrice', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'loseRate', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'rawMaterial', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'nameEnglish', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'minimumProductionLot', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCancel', 'subcontractReturn.edit', 'factoryModel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'standard', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'remark', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'drawingNumber', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'materialPrice', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'loseRate', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'rawMaterial', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'nameEnglish', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'minimumProductionLot', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByClosed', 'subcontractReturn.edit', 'factoryModel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'standard', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'remark', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'drawingNumber', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'materialPrice', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'loseRate', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'rawMaterial', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'nameEnglish', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'minimumProductionLot', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByCreate', 'subcontractReturn.edit', 'factoryModel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'standard', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'remark', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'drawingNumber', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'materialPrice', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'loseRate', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'rawMaterial', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'nameEnglish', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'minimumProductionLot', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByFinish', 'subcontractReturn.edit', 'factoryModel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'standard', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'remark', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'drawingNumber', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'materialPrice', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'loseRate', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'rawMaterial', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'nameEnglish', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'minimumProductionLot', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.editByRelease', 'subcontractReturn.edit', 'factoryModel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);

INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'standard', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'remark', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'drawingNumber', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'materialPrice', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'loseRate', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'rawMaterial', 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'nameEnglish', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'minimumProductionLot', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);
INSERT INTO dfs_form_field_rule_config (route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn.detail', 'subcontractReturn.detail', 'factoryModel', 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'materialBaseField', 0);


-- 历史数据需要兼容(包括重命名和字段规则配置)
UPDATE `dfs_form_field_rule_config` a, `dfs_material_config_field` b
SET a.`is_show` = b.`is_using`, a.`is_need` = b.`is_not_null`, a.`is_edit` = b.`is_change`
WHERE a.`field_code` = b.`field_code`
  AND a.`module_code` = 'materialExtendField'
  AND a.`route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder';

UPDATE `dfs_form_field_rule_config` a, `dfs_material_config_field` b
SET a.`show_gray` = 1
WHERE a.`field_code` = b.`field_code`
  AND a.`module_code` = 'materialExtendField'
  AND b.`is_using` = 0
  AND a.`route` = '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder';


-- 采购收料单批次相关字段配置，重新刷新上去
delete from `dfs_form_field_rule_config`where `route` = '/supply-chain-collaboration/procurement-management/delivery-order' and `field_code` in ('createByNickName', 'createTime');
delete from `dfs_form_field_config` where `full_path_code` in ('purchaseReceiptOrder.edit', 'purchaseReceiptOrder.detail') and `field_code` in ('createByNickName', 'createTime');

call proc_add_form_field_module("purchaseReceiptOrder.edit", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "createTime", "创建时间", "batchField");

call proc_add_form_field_module("purchaseReceiptOrder.detail", "createByNickName", "创建人", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.detail", "createTime", "创建时间", "batchField");

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'createByNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
