set autocommit = 0;
#DDL
ALTER TABLE `dfs_user_column_record`
    ADD COLUMN `modular_type` varchar(50) NULL DEFAULT NULL COMMENT '模块类型' AFTER `column_information`;

CREATE TABLE IF NOT EXISTS `dfs_config_special_material_type`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT,
    `material_type_id`   int(11)      DEFAULT NULL COMMENT '物料类型ID',
    `material_type_name` varchar(100) DEFAULT NULL COMMENT '物料类型名称',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='辅助计划-特殊物料类型配置（用于齐套详情）';

CREATE TABLE IF NOT EXISTS `dfs_tmp_work_order_schedule`
(
    `id`                    int(11)                                               NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `order_id`              int(11)                                                        DEFAULT NULL COMMENT '订单id',
    `order_number`          varchar(255)                                          NOT NULL COMMENT '生产订单号',
    `order_date`            datetime                                                       DEFAULT NULL COMMENT '接单日期',
    `delivery_deadline`     datetime                                                       DEFAULT NULL COMMENT '交货期',
    `planned_delivery_date` datetime                                                       DEFAULT NULL COMMENT '计划生产开始时间',
    `planned_ship_date`     datetime                                                       DEFAULT NULL COMMENT '计划发货日期',
    `model_type`            varchar(50)                                                    DEFAULT NULL COMMENT '类型',
    `customer_name`         varchar(255)                                                   DEFAULT NULL COMMENT '客户名称',
    `material_name`         varchar(255)                                                   DEFAULT NULL COMMENT '物料名称',
    `need_produce_quantity` double(11, 4)                                                  DEFAULT NULL COMMENT '下发数量',
    `drawing_code`          varchar(100)                                                   DEFAULT NULL COMMENT '图纸编号',
    `sale_order_code`       varchar(100)                                                   DEFAULT NULL COMMENT '销售单号',
    `product_code`          varchar(100)                                                   DEFAULT NULL COMMENT '订单产品型号',
    `product_reserve_num`   double(11, 4)                                                  DEFAULT NULL COMMENT '订单数量',
    `packing_method`        varchar(100)                                                   DEFAULT NULL COMMENT '包装方式',
    `work_order_id`         int(11)                                                        DEFAULT NULL COMMENT '工单id',
    `work_order_number`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '生产工单号',
    `line_name`             varchar(50)                                                    DEFAULT NULL COMMENT '产线名称',
    `plan_quantity`         double(11, 2)                                         NOT NULL DEFAULT '0.00' COMMENT '计划数量',
    `finish_count`          double(11, 2)                                                  DEFAULT '0.00' COMMENT '完成数量',
    `start_date`            datetime                                                       DEFAULT NULL COMMENT '计划开始时间',
    `end_date`              datetime                                                       DEFAULT NULL COMMENT '计划完成时间',
    `material_is_complete`  tinyint(4)                                                     DEFAULT NULL COMMENT '是否齐套',
    `material_owe_num`      double(11, 4)                                                  DEFAULT NULL COMMENT '欠套数量',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET=utf8mb4 COMMENT ='工单辅助排程临时表';


ALTER TABLE `sys_permissions` ADD COLUMN `is_web` INT
NULL COMMENT '是否web端1-web端,0-pad端' AFTER `type`;

#新增Pad信息表
CREATE TABLE IF NOT EXISTS `sys_pads` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'Pad id',
  `mac` varchar(255) NOT NULL COMMENT 'Pad-Mac地址',
  `description` varchar(255) DEFAULT NULL COMMENT 'Pad说明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Pad信息表';

#新增Pad-权限中间表
CREATE TABLE IF NOT EXISTS `sys_pad_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `pad_id` int NOT NULL COMMENT 'Pad id',
  `permission_id` int NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `pad_id` (`pad_id`) USING BTREE,
  KEY `permission_id` (`permission_id`) USING BTREE,
  CONSTRAINT `sys_pad_permission_ibfk_1` FOREIGN KEY (`pad_id`) REFERENCES `sys_pads` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Pad-权限中间表';

#DML


commit