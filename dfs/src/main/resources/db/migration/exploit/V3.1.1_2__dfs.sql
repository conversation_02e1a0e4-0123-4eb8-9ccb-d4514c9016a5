-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- 工单报工小程序 新增/更新
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.addReport', 'workOrderReport.addReport', 'scaleFactor', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.addReport', 'workOrderReport.addReport', 'totalScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.editReport', 'workOrderReport.editReport', 'scaleFactor', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.editReport', 'workOrderReport.editReport', 'totalScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
call proc_add_form_field("workOrderReport.addReport", "scaleFactor", "单重");
call proc_add_form_field("workOrderReport.addReport", "totalScale", "总重");
call proc_add_form_field("workOrderReport.editReport", "scaleFactor", "单重");
call proc_add_form_field("workOrderReport.editReport", "totalScale", "总重");

-- 订单报工小程序 新增/更新
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workorder.report', 'orderReportApp.addReport', 'orderReportApp.addReport', 'scaleFactor', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workorder.report', 'orderReportApp.addReport', 'orderReportApp.addReport', 'totalScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workorder.report', 'orderReportApp.editReport', 'orderReportApp.editReport', 'scaleFactor', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workorder.report', 'orderReportApp.editReport', 'orderReportApp.editReport', 'totalScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);

call proc_add_form_field("orderReportApp.addReport", "scaleFactor", "单重");
call proc_add_form_field("orderReportApp.addReport", "totalScale", "总重");
call proc_add_form_field("orderReportApp.editReport", "scaleFactor", "单重");
call proc_add_form_field("orderReportApp.editReport", "totalScale", "总重");

-- 生产工单派工状态可编辑
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1, `need_gray` = 0, `edit_gray` = 0 WHERE `full_path_code` = 'workOrder.editByCreate' AND `field_code` = 'assignmentState';

-- 修改生效态的生产订单表单规则，生产订单号不能编辑
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0 WHERE `field_code` = 'productOrderNumber' AND `full_path_code` IN ("productOrder.editByCreate", "productOrder.editByRelease", "productOrder.editByFinish", "productOrder.editByClosed", "productOrder.editByCancel");
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0 WHERE `field_code` = 'saleOrderNumber' AND `full_path_code` IN ("saleOrder.editByCreate", "saleOrder.editByRelease", "saleOrder.editByFinish", "saleOrder.editByClosed", "saleOrder.editByCancel");
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0 WHERE `field_code` = 'workOrderNumber' AND `full_path_code` IN ("workOrder.editByCreate", "workOrder.editByRelease", "workOrder.editByInvestment", "workOrder.editByHangUp", "workOrder.editByFinish", "workOrder.editByClosed", "workOrder.editByCancel");

-- 物料双单位表单规则调整
UPDATE `dfs_form_field_rule_config` SET `is_need` = 0, `need_gray` = 1, `remark` = '和双单位开关冲突，必填项以双单位开关为准' WHERE `field_code` in ("scaleFactor", "unit") AND `full_path_code` IN ("material.editByCreate", "material.editByRelease", "material.editByStopUsing", "material.editByAbandon", "material.add");

-- 备注字段规则打开，允许下拉选择
UPDATE `dfs_form_field_rule_config` SET `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'remark' AND (`field_name_full_path_code` NOT LIKE '.list%' OR `field_name_full_path_code` NOT LIKE '.detail%');

-- 客户物料字段增加
call proc_add_form_field("saleOrder.list.material", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("saleOrder.add", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("saleOrder.edit", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("saleOrder.detail", "customerMaterialCode", "客户物料编码");

call proc_add_form_field("productOrder.list", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("productOrder.add", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("productOrder.edit", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("productOrder.detail", "customerMaterialCode", "客户物料编码");

call proc_add_form_field("productOrder.list", "customerMaterialName", "客户物料名称");
call proc_add_form_field("productOrder.add", "customerMaterialName", "客户物料名称");
call proc_add_form_field("productOrder.edit", "customerMaterialName", "客户物料名称");
call proc_add_form_field("productOrder.detail", "customerMaterialName", "客户物料名称");

call proc_add_form_field("productOrder.list", "customerSpecification", "客户物料规格");
call proc_add_form_field("productOrder.add", "customerSpecification", "客户物料规格");
call proc_add_form_field("productOrder.edit", "customerSpecification", "客户物料规格");
call proc_add_form_field("productOrder.detail", "customerSpecification", "客户物料规格");

call proc_add_form_field("workOrder.list", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("workOrder.add", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("workOrder.edit", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("workOrder.detail", "customerMaterialCode", "客户物料编码");

call proc_add_form_field("workOrder.list", "customerMaterialName", "客户物料名称");
call proc_add_form_field("workOrder.add", "customerMaterialName", "客户物料名称");
call proc_add_form_field("workOrder.edit", "customerMaterialName", "客户物料名称");
call proc_add_form_field("workOrder.detail", "customerMaterialName", "客户物料名称");

call proc_add_form_field("workOrder.list", "customerSpecification", "客户物料规格");
call proc_add_form_field("workOrder.add", "customerSpecification", "客户物料规格");
call proc_add_form_field("workOrder.edit", "customerSpecification", "客户物料规格");
call proc_add_form_field("workOrder.detail", "customerSpecification", "客户物料规格");


-- 销售订单加客户物料编码
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.list.material', 'saleOrder.list.material', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.editByCreate', 'saleOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.editByRelease', 'saleOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.editByFinish', 'saleOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.editByClosed', 'saleOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.editByCancel', 'saleOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.detail', 'saleOrder.detail', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/salesOrder', 'saleOrder.add', 'saleOrder.add', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);

-- 生产订单加客户物料编码
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.list', 'productOrder.list', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.add', 'productOrder.add', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);

-- 生产订单加客户物料名称
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.list', 'productOrder.list', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.add', 'productOrder.add', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);

-- 生产订单加客户物料规格
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.list', 'productOrder.list', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.add', 'productOrder.add', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);


-- 生产工单加客户物料编码
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL,NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.add', 'workOrder.add', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0,1, 1, 1, 1, 1, NULL, NULL);

-- 生产工单加客户物料名称
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL,NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.add', 'workOrder.add', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);


-- 生产工单加客户物料规格
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,   0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,   0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL,NULL,   0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,   0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('/order-model/production-workorder', 'workOrder.add', 'workOrder.add', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,   0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL);



-- 生产订单加当前物料库存
call proc_add_form_field("productOrder.edit", "stockQuantity", "物料当前库存数量");
call proc_add_form_field("productOrder.detail", "stockQuantity", "物料当前库存数量");


-- 生产订单加当前物料库存
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config`( `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ( '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);


-- 工序别名相关
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workorder.report', 'orderReportApp.orderDetail.workOrderDetail', 'orderReportApp.orderDetail.workOrderDetail', 'procedureAlias', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `filter_information`) VALUES ('yelink.workorder.report', 'orderReportApp.productReportWorkOrderDetail', 'orderReportApp.productReportWorkOrderDetail', 'procedureAlias', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL);
call proc_add_form_field("orderReportApp.orderDetail.workOrderDetail", "procedureAlias", "工序别名");
call proc_add_form_field("orderReportApp.productReportWorkOrderDetail", "procedureAlias", "工序别名");

-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.1.1.1=======================================================