set autocommit = 0;
#DD<PERSON>

call proc_add_column_if_not_exist(
        'dfs_company',
        'layout_url',
        'ALTER TABLE `dfs_company` ADD COLUMN `layout_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''布局图url'' AFTER `industry`');

CREATE TABLE IF NOT EXISTS `dfs_fac_input_record`
(
    `id`                int      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `work_order`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单号',
    `fid`               int                                                          DEFAULT NULL COMMENT '工位id',
    `input_count`       double(11, 4)                                                DEFAULT NULL COMMENT '累计投入量',
    `loss_count`        double(11, 4)                                                DEFAULT NULL COMMENT '累计损耗量',
    `defect_count`      double(11, 4)                                                DEFAULT NULL COMMENT '累计质检不合格量',
    `last_record_time`  datetime                                                     DEFAULT NULL COMMENT '采集器上次采集时间',
    `input_record_time` datetime NOT NULL COMMENT '采集器采集时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 73
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = DYNAMIC COMMENT ='工位投入量记录表';

INSERT INTO `dfs_target_dict`( `target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`) VALUES ('facNewCount', '工位投入量', '1', 'facility', NULL, NULL, '个', '2022-02-15 10:27:32', NULL, NULL);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('facNewCount', 'facility', '根据绑定的计数器计算', 'facNewCount', 1, NULL);

DROP PROCEDURE if EXISTS proc_add_column_index;
delimiter $$
CREATE PROCEDURE `proc_add_column_index`(in var_table_name varchar(64),in var_column_name varchar(64),in var_index_name varchar(64))
top:begin

    -- 表不存在则直接返回
    set @p_tablenum='';
    set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
    prepare stmt1 from @sqlstr1;
    execute stmt1;
    deallocate prepare stmt1;
    if(@p_tablenum<1)then
        leave top;
    end if;

    -- 字段不存在则直接返回
    set @p_columnnum='';
    set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
    prepare stmt2 from @sqlstr;
    execute stmt2;
    deallocate prepare stmt2;
    if(@p_columnnum<=0)then
        leave top;
    end if;

    -- 字段且索引存在才创建索引
    set @str=concat(' alter table `',var_table_name,'` add index `',var_index_name,'` (`',var_column_name,'`);');
    set @cnt = '';
    select count(*) into @cnt from information_schema.statistics where table_schema=database() and table_name=var_table_name and index_name=var_index_name;
    if (@cnt = 0) then
        PREPARE stmt FROM @str;
        EXECUTE stmt;
    end if;
--
end $$
delimiter;

DROP PROCEDURE if EXISTS proc_drop_column_index;
delimiter $$
CREATE PROCEDURE `proc_drop_column_index`(in var_table_name varchar(64),in var_column_name varchar(64),in var_index_name varchar(64))
top:begin

    -- 表不存在则直接返回
    set @p_tablenum='';
    set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
    prepare stmt1 from @sqlstr1;
    execute stmt1;
    deallocate prepare stmt1;
    if(@p_tablenum<1)then
        leave top;
    end if;

    -- 字段不存在则直接返回
    set @p_columnnum='';
    set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
    prepare stmt2 from @sqlstr;
    execute stmt2;
    deallocate prepare stmt2;
    if(@p_columnnum<=0)then
        leave top;
    end if;

    -- 字段且索引存在才删除索引
    set @str=concat(' drop index `',var_index_name,'` on `',var_table_name,'`;');
    set @cnt = '';
    select count(*) into @cnt from information_schema.statistics where table_schema=database() and table_name=var_table_name and index_name=var_index_name;
    if (@cnt > 0) then
        PREPARE stmt FROM @str;
        EXECUTE stmt;
    end if;

end $$
delimiter;

call proc_drop_column_index(
        'dfs_order',
        'order_name',
        'orderName');
#DML

commit