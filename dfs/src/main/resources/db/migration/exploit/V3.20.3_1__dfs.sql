-- DDL
-- 报工增加sku
call proc_add_column(
        'dfs_report_line',
        'sku_id',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `sku_id` int(11) DEFAULT NULL COMMENT ''特征参数skuId'';');

call proc_add_column(
        'dfs_report_line',
        'report_count',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `report_count` double(11,2) NULL DEFAULT null COMMENT ''上报完成数（在审核后会将 finish_count 填进来）''');

call proc_add_column(
        'dfs_report_line',
        'report_unqualified',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `report_unqualified` double(11,2) NULL DEFAULT null COMMENT ''上报不良数（在审核后会将 unqualified 填进来）''');

call proc_add_column(
        'dfs_report_line',
        'audit_human_hour',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `audit_human_hour` double(11,2) NULL DEFAULT null COMMENT ''审核人员工时''');

call proc_add_column(
        'dfs_report_line',
        'audit_time',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `audit_time` datetime DEFAULT NULL COMMENT ''审核时间''');

call proc_add_column(
        'dfs_report_line',
        'auditor',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `auditor` varchar(255) DEFAULT NULL COMMENT ''审核人''');

CREATE TABLE IF NOT EXISTS `dfs_report_line_audit_log` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '记录id',
    `report_line_id` int NOT NULL COMMENT '报工记录id',
    `audit_count` double(11,2) DEFAULT NULL COMMENT '审核完成数',
    `audit_unqualified` double(11,2) DEFAULT NULL COMMENT '审核不良数',
    `audit_human_hour` double(11,2) DEFAULT NULL COMMENT '审核不良数',
    `auditor` varchar(255) DEFAULT NULL COMMENT '审核人',
    `audit_time` datetime NOT NULL COMMENT '审核时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `report_line_id` (`report_line_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='报工记录审核日志表';


-- 下推记录增加sku
call proc_add_column(
        'dfs_order_push_down_record',
        'source_sku_id',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `source_sku_id` int(11) DEFAULT NULL COMMENT ''源单据skuId'';');
call proc_add_column(
        'dfs_order_push_down_record',
        'target_sku_id',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `target_sku_id` int(11) DEFAULT NULL COMMENT ''目标单据skuId'';');

-- 修改列配置长度
call `proc_modify_column`(
        'dfs_user_column_record',
        'column_information',
        'ALTER TABLE `dfs_user_column_record` MODIFY COLUMN `column_information`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT ''列信息'' AFTER `user_name`;');


CREATE TABLE IF NOT EXISTS `dfs_craft_material_relation`
(
    `id`            int NOT NULL AUTO_INCREMENT,
    `craft_code`    varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工艺编码',
    `material_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料编码',
    PRIMARY KEY (`id`),
    KEY             `craft_code` (`craft_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工艺关联物料表';
-- DML

-- 过站记录
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'id', '记录id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'productFlowCode', '条码号', 'product_flow_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'relationNumber', '关联单号', 'relation_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'type', '类型', 'type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'lineId', '产线id', 'line_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'FacId', '工位id', 'fac_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'produreId', '工艺工序id', 'produre_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'materialCode', '物料编码', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'reportTime', '上报时间', 'report_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'reportBy', '上报人', 'report_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'state', '状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'isMaintain', '是否是维修', 'is_maintain', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'codeRecord', 'remark', '备注', 'remark', NULL);

call route_add_l2('/workorder-model', '/workorder-model/report-summary', '报工汇总');
-- 采购订单附件删除按钮
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`) VALUES
('1090205320', '附件删除', 'purchasing.order:delete-file', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, 1, '/supply-chain-collaboration/procurement-management/purchasing-list');
-- 初始化权限
call init_new_role_permission('1090205320');
-- bom版本配置描述
update dfs_business_config set description = '仅一个：一个物料只允许一个bom下的一个版本为生效状态。 多个：一个物料可以存在多个bom实例为生效状态，且每个实例可以存在多个版本为生效状态' where full_path_code = 'design.bomConfig.versionMng';

-- 修改生产订单用料清单字段配置
update dfs_form_field_rule_config  set is_edit = 1 ,edit_gray = 0,is_need = 1,need_gray=0 where full_path_code =  'productOrderMaterialList.editByCreate'  and field_code = 'bomNum';
update dfs_form_field_rule_config  set is_edit = 1 ,edit_gray = 0,is_need = 1,need_gray=0 where full_path_code =  'productOrderMaterialList.editByRelease'  and field_code = 'bomNum';
-- bom版本配置描述
update dfs_business_config set description = '仅一个：一个物料只允许一个bom下的一个版本为生效状态。 多个：一个物料可以存在多个bom实例为生效状态，且每个实例可以存在多个版本为生效状态' where full_path_code = 'design.bomConfig.versionMng';

-- 删除默认工艺的全局配置
DELETE FROM `dfs_business_config_value` WHERE `value_full_path_code` = 'design.craftConfig.defaultCraftConfig.enable';
DELETE FROM `dfs_business_config` WHERE `full_path_code` = 'design.craftConfig.defaultCraftConfig';

-- 工单新增`正在投产的生产基本单元`字段配置
call proc_add_form_field("workOrder.list", "investProductBasicUnits", "正在投产的生产基本单元");
call proc_add_form_field("workOrder.add", "investProductBasicUnits", "正在投产的生产基本单元");
call proc_add_form_field("workOrder.edit", "investProductBasicUnits", "正在投产的生产基本单元");
call proc_add_form_field("workOrder.detail", "investProductBasicUnits", "正在投产的生产基本单元");

INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL,NULL,  0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 1, 1, 1, 1, 1, 1, 1, NULL);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES ('/order-model/production-workorder', 'workOrder.add', 'workOrder.add', 'investProductBasicUnits', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL,  0, 1, 1, 1, 1, 1, 1, 1, NULL);

-- BOM版本日志默认给权限
call init_new_role_permission('10802250');

-- 软件参数报工默认值不设置校验规则
UPDATE `dfs_business_config_value` SET `rules` = null WHERE `value_full_path_code` = 'production.workOrderVerificationReportConfig.workOrderReportDefaultValue.fixedValue';


-- 报工批次 fix备注
update dfs_report_line l
    join dfs_bar_code bc on l.batch = bc.bar_code
    set l.batch_remark = bc.remark
WHERE bc.remark is not null and bc.remark != '';
update dfs_bar_code bc
    join dfs_report_line l on l.batch = bc.bar_code
    set bc.remark = l.batch_remark
WHERE l.batch_remark is not null and l.batch_remark != '';


-- 生产工单表单配置
call proc_add_form_field_module("workOrder.edit", "skuName", "特征参数", "batchField");
call proc_add_form_field_module("workOrder.detail", "skuName", "特征参数", "batchField");

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'skuName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)', NULL, 1, 0, 'batchField', 0);
