-- 字段配置
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'route', '路由', 'route');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'fullPathCode', '配置的全路径编码', 'full_path_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'fieldNameFullPathCode', '字段父路径编码', 'field_name_full_path_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'fieldCode', '字段编码', 'field_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'isShow', '是否显示字段', 'is_show');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'isNeed', '是否必填', 'is_need');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'isEdit', '是否允许编辑', 'is_edit');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'moduleCode', '所属模块编码', 'module_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'isDynamicAdd', '是否是动态添加', 'is_dynamic_add');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('form', 'remark', '字段说明', 'remark');