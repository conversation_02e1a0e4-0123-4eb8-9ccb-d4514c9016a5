-- DDL

-- 新增路由信息表
CREATE TABLE IF NOT EXISTS `sys_route`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `path`          varchar(255) DEFAULT NULL COMMENT '路径',
    `parent_path`   varchar(255) DEFAULT NULL COMMENT '父路径',
    `name`          varchar(255) DEFAULT NULL COMMENT '菜单名称',
    `is_back_stage` int(11)      DEFAULT NULL COMMENT '是否为后台管理（1-是 0-否）',
    `title`         varchar(255) DEFAULT NULL COMMENT '标题',
    `icon`          varchar(255) DEFAULT NULL COMMENT '图标',
    `module_name`   varchar(255) DEFAULT NULL COMMENT '模块名称',
    `affix`         tinyint(1) DEFAULT NULL COMMENT '前端拓展',
    `is_menu`       tinyint(1) DEFAULT NULL COMMENT '前端拓展',
    `keep_alive`    tinyint(1) DEFAULT NULL COMMENT '前端拓展',
    `sort`          int(11)      DEFAULT NULL COMMENT '菜单顺序',
    `sec_sort`      int(11)      DEFAULT 0 COMMENT '菜单二次排序',
    `type`          int(11)      DEFAULT NULL COMMENT '菜单层级 0一级菜单',
    PRIMARY KEY (`id`),
    UNIQUE KEY `path` (`path`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='路由信息表';


-- 白牌配置表新增字段
call proc_add_column(
        'dfs_white_card_settings',
        'screen_english_name',
        'ALTER TABLE `dfs_white_card_settings` ADD COLUMN `screen_english_name` varchar(255) NULL COMMENT ''大屏英文名称'' AFTER `screen_name`');

-- 指标库
CREATE DATABASE IF NOT EXISTS dfs_target;

-- datax 辅助表
CREATE TABLE IF NOT EXISTS `dfs_target`.`dfs_datax_id_table` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `origin_id` int(11) DEFAULT NULL COMMENT '源表id',
    `table_name` varchar(255) DEFAULT NULL COMMENT '需同步的表名',
    PRIMARY KEY (`id`),
    KEY `idx_o_id` (`origin_id`) USING BTREE,
    KEY `idx_t_name` (`table_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='datax用于同步删除的辅助表';

-- 生产工单相关指标统计
CREATE TABLE IF NOT EXISTS `dfs_target`.`dfs_record_work_order_hourly_monitor` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `inspection_quantity` double(11,2) DEFAULT NULL COMMENT '检测数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `input_quantity` double(11,2) DEFAULT NULL COMMENT '投入数量',
    `making_quantity` double(11,2) DEFAULT NULL COMMENT '在制数量',
    `qualified_rate` double(11,2) DEFAULT NULL COMMENT '合格率',
    `is_sum` tinyint(4) DEFAULT NULL COMMENT '是否累加之前的',
    `record_time` datetime DEFAULT NULL COMMENT '记录时间(取整点) 统计包含整个小时内的',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    KEY `idx_record_time` (`record_time`) USING BTREE,
    KEY `idx_work_order_number` (`work_order_number`),
    KEY `idx_is_sum` (`is_sum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产工单小时监控';

CREATE TABLE IF NOT EXISTS `dfs_target`.`dfs_record_work_order_daily_progress` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '当日计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '当日产出数量',
    `inspection_quantity` double(11,2) DEFAULT NULL COMMENT '当日检测数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '当日不良数量',
    `inbound_quantity` double(11,2) DEFAULT NULL COMMENT '当日入库数量',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '当日直通数量',
    `start_work_time` datetime DEFAULT NULL COMMENT '当日开工时间(小程序录入)',
    `working_people_quantity` double(11,2) DEFAULT NULL COMMENT '当日在岗人数',
    `actual_working_hour` double(11,2) DEFAULT NULL COMMENT '实际投入工时',
    `theory_working_hour` double(11,2) DEFAULT NULL COMMENT '理论产出工时',
    `qualified_rate` double(11,2) DEFAULT NULL COMMENT '当日合格率',
    `product_efficiency_rate` double(11,2) DEFAULT NULL COMMENT '生产效率',
    `achievements` double(11,2) DEFAULT NULL COMMENT '绩效',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '当日返修数',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '当日返修良品数（维修判定: 不报废）',
    `repair_quantity_rate` double(11,2) DEFAULT NULL COMMENT '当日返修率',
    `record_date` datetime DEFAULT NULL COMMENT '记录日期',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    KEY `idx_uni` (`work_order_number`,`record_date`)
) ENGINE=InnoDB AUTO_INCREMENT=194 DEFAULT CHARSET=utf8mb4 COMMENT='生产工单每日进度';

CREATE TABLE IF NOT EXISTS `dfs_target`.`dfs_record_work_order_statistics` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `inspection_quantity` double(11,2) DEFAULT NULL COMMENT '检测数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `inbound_quantity` double(11,2) DEFAULT NULL COMMENT '入库数量',
    `actual_capacity` double(11,2) DEFAULT NULL COMMENT '实际产能',
    `actual_working_hour` double(11,2) DEFAULT NULL COMMENT '实际投入工时',
    `theory_working_hour` double(11,2) DEFAULT NULL COMMENT '理论产出工时',
    `stop_line_hour` double(11,2) DEFAULT NULL COMMENT '停线时长',
    `finish_rate` double(11,2) DEFAULT NULL COMMENT '达成率',
    `qualified_rate` double(11,2) DEFAULT NULL COMMENT '合格率',
    `product_efficiency_rate` double(11,2) DEFAULT NULL COMMENT '生产效率',
    `achievements` double(11,2) DEFAULT NULL COMMENT '绩效',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '返修数',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '返修良品数（维修判定: 不报废）',
    `repair_quantity_rate` double(11,2) DEFAULT NULL COMMENT '返修率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`work_order_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产整体进度';

-- 删除 dfs_config_open_api 中的domain、aksk字段
call proc_modify_column(
        'dfs_config_open_api',
        'domain',
        'alter table dfs_config_open_api drop domain');
call proc_modify_column(
        'dfs_config_open_api',
        'access_key_id',
        'alter table dfs_config_open_api drop access_key_id');

call proc_modify_column(
        'dfs_config_open_api',
        'access_key_secret',
        'alter table dfs_config_open_api drop access_key_secret');

CREATE TABLE IF NOT EXISTS `dfs_business_config` (
	`id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`code` VARCHAR ( 100 ) DEFAULT NULL COMMENT '配置编码',
	`name` VARCHAR ( 255 ) DEFAULT NULL COMMENT '配置名称',
	`full_path_code` VARCHAR ( 750 ) DEFAULT NULL COMMENT '配置的全路径编码，父级编码.子级编码',
	`parent_full_path_code` VARCHAR ( 750 ) DEFAULT '' COMMENT '父级配置的全路径编码',
	`description` text DEFAULT NULL COMMENT '描述',
	`create_by` VARCHAR ( 255 ) DEFAULT NULL COMMENT '创建人',
	`update_by` VARCHAR ( 255 ) DEFAULT NULL COMMENT '修改人',
	`create_time` datetime DEFAULT NULL COMMENT '创建时间',
	`update_time` datetime DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY ( `id` ) USING BTREE,
	UNIQUE KEY `full_path_code` ( `full_path_code` ) USING BTREE,
	KEY `parent_full_path_code` ( `parent_full_path_code` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '业务配置表';

CREATE TABLE IF NOT EXISTS `dfs_business_config_value` (
	`id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`value_code` varchar(100) DEFAULT NULL COMMENT '配置值编码',
	`value_name` varchar(255) DEFAULT NULL COMMENT '配置值名称',
	`value_full_path_code` varchar(750) DEFAULT NULL COMMENT '配置值的全路径编码，父级编码.子级编码',
	`config_full_path_code` varchar(750) NULL COMMENT '配置的全路径编码',
	`input_type` varchar(50) DEFAULT NULL COMMENT '输入框类型，单选，多选，输入框，文本域，json框，文本',
	`option_values_type` varchar(50) DEFAULT 'api' COMMENT '可选值来源类型，api接口、table表字段option_values',
	`url` text DEFAULT NULL COMMENT '可选值请求接口',
	`method` varchar(10) DEFAULT NULL COMMENT '接口请求方式，get，post',
	`params` text DEFAULT NULL COMMENT '接口请求参数，json格式',
	`resp_param_field` varchar(255) DEFAULT NULL COMMENT '接口响应参数取值定义，可选值唯一辨识,名称，如：code,name',
	`option_values` text DEFAULT NULL COMMENT '可选值选项，option_values_type为api时，可进一步过滤接口值，为table时，就是具体的可选值选项，json格式',
	`value` text DEFAULT NULL COMMENT '实际值，json格式',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE KEY `value_full_path_code` (`value_full_path_code`) USING BTREE,
	KEY `config_full_path_code` (`config_full_path_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='业务配置值表';

-- 添加特征参数skuId字段
call proc_add_column(
        'dfs_bar_code',
        'sku_id',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `sku_id` int(11) NULL DEFAULT 0 COMMENT ''特征参数skuId''');

-- 标签类型表添加调用对外接口路径字段
call proc_add_column(
        'dfs_config_label_type',
        'open_url',
        'ALTER TABLE `dfs_config_label_type` ADD COLUMN `open_url`  varchar(255) DEFAULT NULL COMMENT ''调用对外接口路径''');

CREATE TABLE IF NOT EXISTS `dfs_package_box` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `box_number` varchar(255) DEFAULT NULL COMMENT '箱编号',
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `package_container_id` int(11) DEFAULT NULL COMMENT '包装容器层级id',
    `state` int(2) NOT NULL COMMENT '0:未完成包装,1:已完成包装,2:已拆箱,3:合箱中,4:合箱完成',
    `operate_time` datetime DEFAULT NULL COMMENT '操作时间：状态改变时的时间',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `print_box_time` datetime DEFAULT NULL COMMENT '外箱打印时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_box` (`box_number`,`work_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工单包装容器层级';

CREATE TABLE IF NOT EXISTS `dfs_package_box_mac_sn_relation` (
    `box_id` int(11) NOT NULL,
    `box_number` varchar(255) NOT NULL COMMENT '箱编号',
    `nameplate_sn` varchar(255) NOT NULL COMMENT 'sn码',
    `create_time` datetime NOT NULL COMMENT '创建时间,扫码时间',
    PRIMARY KEY (`box_id`,`nameplate_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='箱编号与生成的sn关联表';

call proc_add_column_index(
        'dfs_product_flow_code_record',
        'report_time',
        'report_time');

-- DML


-- 生产日、周、月报表权限设置
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001020', '导出_下载默认模板', 'daily.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001030', '导出_上传下载自定义导出模板', 'daily.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001040', '导出_导出excel', 'daily.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
call init_new_role_permission('13001020');
call init_new_role_permission('13001030');
call init_new_role_permission('13001040');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001050', '导出_下载默认模板', 'week.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001060', '导出_上传下载自定义导出模板', 'week.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001070', '导出_导出excel', 'week.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
call init_new_role_permission('13001050');
call init_new_role_permission('13001060');
call init_new_role_permission('13001070');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001080', '导出_下载默认模板', 'month.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001090', '导出_上传下载自定义导出模板', 'month.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('13001110', '导出_导出excel', 'month.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, 1, '/statementCenter');
call init_new_role_permission('13001080');
call init_new_role_permission('13001090');
call init_new_role_permission('13001110');


-- 采购需求新增按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090204120', '批量编辑', 'purchasing.demand:updateBatch', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1090204', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-demand', 1);
call init_new_role_permission('1090204120');
-- 采购订单新增按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090205140', '批量编辑', 'purchasing.list:updateBatch', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
call init_new_role_permission('1090205140');

UPDATE `sys_permissions` SET `parent_path` = '/supply-chain-collaboration/procurement-management/purchasing-list' WHERE `id` = '1090205130';

-- 指标
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`)
VALUES ( 'workOrderDailyProgress', '工单每日进度', '1', 'workOrderTarget', NULL, NULL, '', NULL, NULL, NULL, 1);

INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`)
VALUES ('workOrderDailyProgress', 'workOrderTarget', '系统统计', 'systemCollectWorkOrderDailyProgress', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`)
VALUES ( 'workOrderHourlyMonitor', '工单每小时监控', '1', 'workOrderTarget', NULL, NULL, '', NULL, NULL, NULL, 1);

INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`)
VALUES ('workOrderHourlyMonitor', 'workOrderTarget', '系统统计', 'systemCollectWorkOrderHourlyMonitor', 1, NULL);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12210', '业务配置(新)', '/system_settings/business-config', NULL, NULL, NULL, NULL, '2023-01-02 10:06:41', 'enable', 'GET', '122', 1, 1, 1, '/system_settings', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12210010', '编辑', 'business.config:update', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '12210', 2, 1, 0, '/system_settings/business-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12210020', '详情', 'business.config:detail', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '12210', 2, 1, 0, '/system_settings/business-config', 1);
call init_new_role_permission('12210%');

-- 报表中心-销售报表权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13003', '销售报表', '/statementCenter/salesReport', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '130', 1, 1, 0, '/statementCenter', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13003010', '列配置_列表', 'salesReport:columnConfigList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', 2, 1, 0, '/statementCenter/salesReport', 1);
call init_new_role_permission('13003');
call init_new_role_permission('13003010');


-- 路由表初始数据
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model/salesOrder', '/order-model', 'salesOrder', NULL, '销售订单', NULL, 'ams', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model/product-order', '/order-model', 'productOrder', NULL, '生产订单', NULL, 'ams', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model/production-materials', '/order-model', 'productionMaterials', NULL, '生产订单用料清单', NULL, 'ams', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model/auxiliary_plan', '/order-model', 'auxiliaryPlan', NULL, '辅助计划', NULL, 'ams', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model/workorder-scheduling', '/order-model', 'workorderScheduling', NULL, '工单调度', NULL, 'ams', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model/production-workorder', '/order-model', 'orderModelWorkOrder', NULL, '生产工单', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model/automaticscheduling', '/order-model', 'automaticScheduling', NULL, '自动排程', NULL, 'ams', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/order-model', NULL, 'orderManagement', 0, '计划调度', 'menu_plan_scheduling', NULL, NULL, NULL, NULL, 0, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/workorder-model/production-workorder', '/workorder-model', 'productionOrders', NULL, '生产工单', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/production-requisition/list', '/workorder-model', 'productionRequisition', NULL, '领料申请', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-report/index', '/workorder-model', 'productionDaily', NULL, '生产报表', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/workorder-model/home-workorder', '/workorder-model', 'homeWorkOrder', NULL, '作业工单', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-checked/productionCheckTeam', '/workorder-model', 'productionCheckedProjectTeam', NULL, '生产检查项目组', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-checked/productionCheckProject', '/workorder-model', 'productionCheckedProject', NULL, '生产检查项目', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-checked/productionCheckPlan', '/workorder-model', 'productionCheckedOrder', NULL, '生产检查方案', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/hourly-piece-board/hourlyPieceBoard', '/workorder-model', 'hourlyPieceBoard', NULL, '人员计时计件看板', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/workorder-model/production-records', '/workorder-model', 'productionRecords', NULL, '生产工单报工记录', NULL, 'dfs', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/workorder-model/workorder-records', '/workorder-model', 'workorderRecords', NULL, '作业工单报工记录', NULL, 'dfs', NULL, NULL, NULL, 9, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/workorder-model', NULL, 'workorderManagement', 0, '生产作业', 'menu_production_homework', NULL, NULL, NULL, NULL, 1, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/trace/order', '/trace', 'orderTrace', NULL, '订单追溯', NULL, 'ams', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/trace/order-transparency', '/trace', 'orderTransparency', NULL, '订单透明', NULL, 'ams', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/trace/work-order', '/trace', 'workorderTrace', NULL, '工单追溯', NULL, 'ams', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/trace/single-product', '/trace', 'singleProductTrace', NULL, '单品追溯', NULL, 'ams', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/trace-management/package-record', '/trace', 'PackageRecoed', NULL, '包装记录', NULL, 'ams', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/trace-management/furnace-info', '/trace', 'furnaceInfo', NULL, '炉号信息', NULL, 'ams', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/trace', NULL, '', 0, '生产追溯', 'menu_production_trace', NULL, NULL, NULL, NULL, 2, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/device', '/equipment-management', 'device', NULL, '生产设备', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/warranty', '/equipment-management', 'equipmentWarranty', NULL, '设备维修', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/alarmToOrder', '/equipment-management', 'equipmentAlarmToOrder', NULL, '告警转工单', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/equipment-checked/team', '/equipment-management', 'equipmentCheckedProjectTeam', NULL, '设备检查项目组', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/equip-checked/equipCheckProject', '/equipment-management', 'equipmentCheckedProject', NULL, '设备检查项目', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/equip-checked/equipCheckPlan', '/equipment-management', 'equipCheckedPlan', NULL, '设备检查方案', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/deviceInspection/index', '/equipment-management', 'deviceInspection', NULL, '设备巡检', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/deviceDetection/index', '/equipment-management', 'deviceDetection', NULL, '设备点检', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management/deviceMaintenance/index', '/equipment-management', 'deviceMaintenance', NULL, '设备保养', NULL, 'dfs', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment-management', NULL, '', 0, '设备管理', 'menu_device_management', NULL, NULL, NULL, NULL, 3, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/processQuality/qualityDefinition', '/qualities-manage', 'qualityDefinition', NULL, '不良定义', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/processQuality', '/qualities-manage', 'qualityRecord', NULL, '工位质检', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/processQuality/paln', '/qualities-manage', 'productInspectionPlan', NULL, '工位质检方案', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/reworkQuality/define', '/qualities-manage', 'qualityDefinition', NULL, '质检返工定义', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/reworkQuality', '/qualities-manage', 'reworkQuality', NULL, '质检返工', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/reworkQuality/plan/project', '/qualities-manage', 'qualityDefinition', NULL, '质检返工方案', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/processQuality/qualityFeedbackDefinition', '/qualities-manage', 'qualityDefinition', NULL, '品质反馈定义', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/productInspection/define', '/qualities-manage', 'qualityDefinition', NULL, '产品检测项目组定义', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/productInspection/project', '/qualities-manage', 'qualityDefinition', NULL, '产品检测项目', NULL, 'dfs', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/productInspection/plan', '/qualities-manage', 'qualityDefinition', NULL, '产品检测方案', NULL, 'dfs', NULL, NULL, NULL, 9, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/productInspection', '/qualities-manage', 'productInspection', NULL, '产品检测', NULL, 'dfs', NULL, NULL, NULL, 10, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage/processQuality/qualityRecord', '/qualities-manage', 'productionLineManagement', NULL, '质检记录', NULL, 'dfs', NULL, NULL, NULL, 11, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/qualities-manage', NULL, '质量管理', 0, '质量管理', 'menu_quality_management', NULL, NULL, NULL, NULL, 4, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smtManagement/bosaManagement', '/smt-management', 'bosaManagement', NULL, 'BOSA管理', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smtManagement/logManagement', '/smt-management', 'logManagement', NULL, 'log管理', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management/material-station', '/smt-management', 'materialStation', NULL, '物料站位表', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management/order-station', '/smt-management', 'orderStation', NULL, '工单站位表', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smtManagement/feederManage', '/smt-management', 'feederManagement', NULL, 'Feeder管理', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-Management/ink-painting-recipients-record', '/smt-management', 'inkPaintingRecipientsRecord', NULL, '水墨领用记录', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-Management/aging-management', '/smt-management', 'agingManagement', NULL, '老化管理', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management/scrap-material-management', '/smt-management', 'scrapMaterialManagement', NULL, '废料管理', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management/antenatal-preparation/inspection-items', '/smt-management/antenatal-preparation', 'antenatalPreparationInspectionItems', NULL, '检查项目', NULL, 'dfs', NULL, 1, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management/antenatal-preparation/inspection-plan', '/smt-management/antenatal-preparation', 'antenatalPreparationInspectionPlan', NULL, '检查方案', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management/antenatal-preparation/inspection-record', '/smt-management/antenatal-preparation', 'antenatalPreparationInspectionRecord', NULL, '检查记录', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management/antenatal-preparation', '/smt-management', 'antenatalPreparation', NULL, '产前准备', NULL, NULL, NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/smt-management', NULL, 'smtManagement', 0, '现场管理', 'menu_scene_management', NULL, NULL, NULL, NULL, 5, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/list', '/alarm', 'alarmList', NULL, '告警列表', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/config', '/alarm', 'alarmConfig', NULL, '告警设置', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/historyAlarm', '/alarm', 'historyAlarmList', NULL, '历史告警', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/alarmTypeDefine', '/alarm', 'alarmTypeDefineList', NULL, '告警类型定义', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/alarmIDDefine', '/alarm', 'alarmIDDefineList', NULL, '告警ID定义', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/event-definition', '/alarm', 'eventDefinition', NULL, '事件定义', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/event-record', '/alarm', 'eventRecord', NULL, '事件记录', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm/event-type-definition', '/alarm', 'eventTypeDefinition', NULL, '事件类型定义', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/alarm', NULL, '', 0, '告警管理', 'menu_alarm_management', NULL, NULL, NULL, NULL, 6, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/supplies', '/product-management', 'supplies', NULL, '物料', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/replace-scheme', '/product-management', 'replaceScheme', NULL, '替代方案', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/auxiliary-attr', '/product-management', 'auxiliaryAttr', NULL, '物料特征参数', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/bom', '/product-management', 'bom', NULL, 'BOM', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/bomTemplate', '/product-management', 'bomTemplate', NULL, 'BOM模板', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/technologyTemplate', '/product-management', 'technologyTemplate', NULL, '工艺模板', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/technology', '/product-management', 'productManagement-technology', NULL, '工艺', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/procedure', '/product-management', 'productManagement-procedure', NULL, '工序定义', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management/process-inspection-item', '/product-management', 'processInspectionItem', NULL, '工序检验项定义', NULL, 'dfs', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-management', NULL, '产品管理', 0, '产品定义', 'menu_product_define', NULL, NULL, NULL, NULL, 7, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/order-model/clientFile', '/supply-chain-collaboration/order-model', 'clientFile', NULL, '客户档案', NULL, 'dfs', NULL, 1, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/order-model/salesOrder', '/supply-chain-collaboration/order-model', 'salesOrder', NULL, '销售订单', NULL, 'ams', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/order-model/shipment_application', '/supply-chain-collaboration/order-model', 'shipmentApplication', NULL, '出货申请', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/order-model/deliver_goods', '/supply-chain-collaboration/order-model', 'deliverGoods', NULL, '发货管理', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/order-model', '/supply-chain-collaboration', 'orderManagement', NULL, '销售管理', NULL, NULL, NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', '/supply-chain-collaboration/procurement-management', 'supplierProfile', NULL, '供应商档案', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', '/supply-chain-collaboration/procurement-management', 'supplyList', NULL, '供应商物料清单', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/materialAnalysis', '/supply-chain-collaboration/procurement-management', 'materialAnalysis', NULL, '物料需求分析', NULL, 'ams', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/purchasing-demand', '/supply-chain-collaboration/procurement-management', 'purchasingDemand', NULL, '采购需求', NULL, 'ams', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/purchasing-list', '/supply-chain-collaboration/procurement-management', 'purchasingList', NULL, '采购订单', NULL, 'ams', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/delivery-order', '/supply-chain-collaboration/procurement-management', 'deliveryOrder', NULL, '采购收料', NULL, 'ams', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/return-order', '/supply-chain-collaboration/procurement-management', 'returnOrder', NULL, '采购退料', NULL, 'ams', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/materials-list', '/supply-chain-collaboration/procurement-management', 'materialsList', NULL, '来料检验', NULL, 'ams', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management/purchase-material-return-application', '/supply-chain-collaboration/procurement-management', 'purchaseMaterialReturnApplicationList', NULL, '采购退料申请单', NULL, 'ams', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/procurement-management', '/supply-chain-collaboration', '采购管理', NULL, '采购管理', NULL, NULL, NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/inbound-record', '/supply-chain-collaboration/warehouse-management', 'inboundRecord', NULL, '物料台账', NULL, 'wms', NULL, NULL, 1, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/batch', '/supply-chain-collaboration/warehouse-management', 'batch', NULL, '物料批次', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/factory-record', '/supply-chain-collaboration/warehouse-management', 'factoryRecord', NULL, '出库单', NULL, 'wms', NULL, NULL, 1, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/access-record', '/supply-chain-collaboration/warehouse-management', 'inputRecord', NULL, '入库单', NULL, 'wms', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/material-allocation', '/supply-chain-collaboration/warehouse-management', 'materialAllocation', NULL, '调拨单', NULL, 'wms', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/inventory', '/supply-chain-collaboration/warehouse-management', 'inventory', NULL, '库存盘点', NULL, 'wms', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/warehouse-manage', '/supply-chain-collaboration/warehouse-management', 'warehouseManage', NULL, '仓库定义', NULL, 'wms', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/team', '/supply-chain-collaboration/warehouse-management', 'teamManagement', NULL, '仓管组管理', NULL, 'wms', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/record-query/location-define', '/supply-chain-collaboration/warehouse-management', 'locationDefine', NULL, '库位定义', NULL, 'wms', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/record-query/inbound-record/access-product', '/supply-chain-collaboration/warehouse-management', 'accessProduct', NULL, '生产入库单', NULL, 'wms', NULL, NULL, NULL, 9, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/warehouse-management', '/supply-chain-collaboration', NULL, NULL, '仓储配送', NULL, NULL, NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', '/supply-chain-collaboration/outsourcingManagement', 'outsourcingOrder', NULL, '委外订单', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', '/supply-chain-collaboration/outsourcingManagement', 'outsourcingReceiptOrder', NULL, '委外订单收货单', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', '/supply-chain-collaboration/outsourcingManagement', 'outsourcingMaterailOrder', NULL, '委外订单用料清单', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration/outsourcingManagement', '/supply-chain-collaboration', 'outsourcingManagement', NULL, '委外管理', NULL, NULL, NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/supply-chain-collaboration', NULL, '供应链协同', 0, '供应链协同', 'menu_supply_chain_collaboration', NULL, NULL, NULL, NULL, 8, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/baseManagement/warehouse-manage', '/warehousingDistribution/baseManagement', 'warehouseManage', NULL, '仓库定义', NULL, 'wms', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/shelves-definition', '/warehousingDistribution/baseManagement', 'shelvesDefinition', NULL, '货架定义', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/baseManagement/location-define', '/warehousingDistribution/baseManagement', 'locationDefine', NULL, '库位定义', NULL, 'wms', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/baseManagement/team', '/warehousingDistribution/baseManagement', 'teamManagement', NULL, '仓管组管理', NULL, 'wms', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/baseManagement', '/warehousingDistribution', '基础管理', NULL, '基础管理', NULL, NULL, NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', '/warehousingDistribution/purchaseOutOfStorage', 'purchasePutInStorage', NULL, '采购入库单', NULL, 'wms', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', '/warehousingDistribution/purchaseOutOfStorage', 'purchaseReturnMaterialOutOfStorage', NULL, '采购退料出库单', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/purchaseOutOfStorage', '/warehousingDistribution', '采购出入库', NULL, '采购出入库', NULL, NULL, NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/productInOrOut/workOrderComplete', '/warehousingDistribution/productInOrOut', 'accessProduct', NULL, '生产入库单', NULL, 'wms', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/productInOrOut/workOrderTakeOut', '/warehousingDistribution/productInOrOut', 'OutputPickingProduct', NULL, '生产领料出库单', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/productInOrOut/workOrderSupplement', '/warehousingDistribution/productInOrOut', 'OutputPatchProduct', NULL, '生产补料出库单', NULL, 'wms', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/productInOrOut/applicationReturn', '/warehousingDistribution/productInOrOut', 'OutputRefundProduct', NULL, '生产退料入库单', NULL, 'wms', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/productInOrOut', '/warehousingDistribution', '生产出入库', NULL, '生产出入库', NULL, 'wms', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/salesIssueReceipt/salesIssueDoc', '/warehousingDistribution/salesIssueReceipt', 'salesIssueDoc', NULL, '销售出库单', NULL, 'wms', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc', '/warehousingDistribution/salesIssueReceipt', 'salesMaterialReturnReceiptDoc', NULL, '销售退货入库单', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/salesIssueReceipt', '/warehousingDistribution', '销售出入库', NULL, '销售出入库', NULL, NULL, NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/subcontracting/order', '/warehousingDistribution/subcontracting', 'subcontractingOrder', NULL, '委外订单入库单', NULL, 'wms', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/subcontracting/outstoreMaterail', '/warehousingDistribution/subcontracting', 'outstoreMaterail', NULL, '委外领料出库单', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/subcontracting/repairMaterail', '/warehousingDistribution/subcontracting', 'repairMaterail', NULL, '委外补料出库单', NULL, 'wms', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/subcontracting/refundMaterail', '/warehousingDistribution/subcontracting', 'refundMaterail', NULL, '委外退料入库单', NULL, 'wms', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/subcontracting', '/warehousingDistribution', 'subcontracting', NULL, '委外出入库', '', 'wms', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/cougnyManage/inventory', '/warehousingDistribution/cougnyManage', 'inventory', NULL, '盘点单', NULL, 'wms', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/cougnyManage/allocation', '/warehousingDistribution/cougnyManage', 'allocation', NULL, '调拨单', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/cougnyManage/displacement/index', '/warehousingDistribution/cougnyManage', 'displacement', NULL, '移位单', NULL, 'wms', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/cougnyManage', '/warehousingDistribution', '库内管理', NULL, '库内管理', NULL, NULL, NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/mixReceive/stockApplication', '/warehousingDistribution/mixReceive', 'stockApplication', NULL, '其他入库申请单', NULL, 'wms', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/mixReceive/issueRequisition', '/warehousingDistribution/mixReceive', 'issueRequisition', NULL, '其他出库申请单', NULL, 'wms', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/mixReceive/warehousing', '/warehousingDistribution/mixReceive', 'warehousing', NULL, '其他入库单', NULL, 'wms', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/mixReceive/issue', '/warehousingDistribution/mixReceive', 'issue', NULL, '其他出库单', NULL, 'wms', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/mixReceive', '/warehousingDistribution', '杂收杂发', NULL, '杂收杂发', NULL, NULL, NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/record-query/inbound-record', '/warehousingDistribution', 'inboundRecord', NULL, '物料台账', NULL, 'wms', NULL, NULL, 1, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/batch', '/warehousingDistribution', 'batch', NULL, '物料批次', NULL, 'wms', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/inboundOutboundReport', '/warehousingDistribution', 'inboundOutboundReport', NULL, '出入库报表', NULL, 'wms', NULL, NULL, NULL, 9, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution/warehouseConfigSystem', '/warehousingDistribution', 'warehouseConfigSystem', NULL, '仓储配置中心', NULL, 'wms', NULL, NULL, NULL, 10, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/warehousingDistribution', NULL, '仓储配送', 0, '仓储配送', 'menu_warehousing_distribution', NULL, NULL, NULL, NULL, 9, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/energy-consumption-management/energy-information', '/energy-consumption-management', 'energyConsumptionManagement', NULL, '能源信息', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/energy-consumption-management/time-period-management', '/energy-consumption-management', 'powerConsumptionPeriodManagement', NULL, '用电时段管理', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/energy-consumption-management', NULL, '能源管理', 0, '能源管理', 'menu_energy_management', NULL, NULL, NULL, NULL, 10, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/expert-system/quality-analysis', '/expert-system', 'qualityAnalysis', NULL, '质量分析', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/expert-system/light-inspection-analysis', '/expert-system', 'qlightInspectionAnalysis', NULL, '灯检分析', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/expert-system/sterilization-cabinet-analysis', '/expert-system', 'sterilizationCabinetAnalysis', NULL, '灭菌柜分析', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/expert-system/quality-testing', '/expert-system', 'qualityTestingAnalysis', NULL, '质检分析', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/expert-system/power-consumption-analysis', '/expert-system', 'powerConsumptionAnalysis', NULL, '电耗分析', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/expert-system', NULL, '专家系统', 0, '专家系统', 'menu_expert_system', NULL, NULL, NULL, NULL, 11, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/projectManagement/nodeDefinition', '/projectManagement', 'nodeDefinition', NULL, '节点定义', NULL, 'ams', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/projectManagement/nodeConfigure', '/projectManagement', 'nodeConfig', NULL, '项目节点配置', NULL, 'ams', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/projectManagement/projectSelect', '/projectManagement', 'projectQuery', NULL, '项目查询', NULL, 'ams', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/projectManagement', NULL, '项目管理', 0, '项目管理', 'menu_label_management', NULL, NULL, NULL, NULL, 12, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/intelligentCreation/productionOrderProgress', '/intelligentCreation/production', 'intelligentCreationProductionOrderProgress', NULL, '生产订单进度 ', NULL, 'ams', NULL, 1, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/intelligentCreation/production', '/intelligentCreation', 'intelligentCreationProduction', NULL, '生产看板', NULL, NULL, NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/intelligentCreation/output-summary-perday', '/intelligentCreation', 'intelligentCreationProductionOrderProgress', NULL, '车间每日产品产量报表 ', NULL, 'ams', NULL, 1, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/intelligentCreation', NULL, '制造看板', 0, '制造看板', 'menu-intelligentCreation', NULL, NULL, NULL, NULL, 13, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/statementCenter/production', '/statementCenter', 'statementCenterProduction', NULL, '生产报表', NULL, 'ams', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/statementCenter/equipmentProduction', '/statementCenter', 'equipmentProduction', NULL, '设备产量报表', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/statementCenter', NULL, '报表中心', 0, '报表中心', 'menu_report', NULL, NULL, NULL, NULL, 14, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model/companyInfo/index', '/factory-model', '公司信息', NULL, '公司信息', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/product-model/index', '/factory-model', 'productModelList', NULL, '工厂模型', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model/factory-area', '/factory-model', '厂区', NULL, '厂区', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model/workshop', '/factory-model', '车间', NULL, '车间', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model/work-center', '/factory-model', 'workCenter', NULL, '工作中心', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model/product-line', '/factory-model', '制造单元', NULL, '制造单元', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model/station', '/factory-model', '工位', NULL, '工位', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model/team', '/factory-model', 'team', NULL, '班组', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/factory-model', NULL, '工厂建模', 1, '工厂建模', 'menu_factory_model', NULL, NULL, NULL, NULL, 15, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/production-config/workCalendar/worksCalendar', '/production-config', 'worksCalendar', NULL, '工作日历', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/production-config/flight-config', '/production-config', 'flightConfig', NULL, '班次配置', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/production-config/firstShiftTimeConfig', '/production-config', 'firstShiftTimeConfig', NULL, '首班时间配置', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/production-config/capacityList', '/production-config', 'capacityList', NULL, '产能列表', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/production-config', NULL, '', 1, '生产配置', 'menu_production_config', NULL, NULL, NULL, NULL, 16, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/target/factoryTarget', '/target', 'targetList', NULL, '工厂指标', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/target/synthesizeTarget', '/target', 'synthesizeTarget', NULL, '综合指标', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/target', NULL, NULL, 1, '工厂指标', 'menu_factory_target', NULL, NULL, NULL, NULL, 17, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment/device', '/equipment', 'device', NULL, '生产设备', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment/test-equipment', '/equipment', '采集设备', NULL, '采集设备', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment/station-machine', '/equipment', '终端设备', NULL, '终端设备', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment/video-device', '/equipment', '视频设备', NULL, '视频设备', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment/technology-device', '/equipment', 'technologyDevice', NULL, '工艺装备', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/screen-management-old', '/equipment', 'screenManagement', NULL, '大屏管理(旧)', NULL, 'dfs', 1, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/screen-management', '/equipment', 'ScreenManage', NULL, '大屏管理(新)', NULL, 'dfs', 1, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment/equipment-model', '/equipment', 'equipmentModel', NULL, '设备模型', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment/warranty', '/equipment', 'equipmentWarranty', NULL, '设备维修', NULL, 'dfs', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/equipment', NULL, '', 1, '设备信息', 'menu_device_info', NULL, NULL, NULL, NULL, 18, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/avg/dispath/index', '/avg/dispath', 'configurationLocation', NULL, '点位配置', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/point/list', '/avg/dispath', 'agvPointList', NULL, '线路配置', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/avg/dispath', NULL, '配送配置', 1, '配送配置', 'menu_distribution_configuration', NULL, NULL, NULL, NULL, 19, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/material-config/material-type-config', '/material-config', 'materialConfig', NULL, '物料类型配置', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/material-config/extend-config', '/material-config', 'extendConfig', NULL, '扩展字段配置', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/material-config/element-config', '/material-config', 'elementConfig', NULL, '化学元素配置', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/material-config/unit-config', '/material-config', 'unitConfig', NULL, '单位配置', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/material-config', NULL, '', 1, '物料配置', 'menu_material_config', NULL, NULL, NULL, NULL, 20, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/label-manage/labelRules', '/label-manage', 'labelManagement', NULL, '标签管理', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/label-manage', NULL, '标签管理', 1, '标签管理', 'menu_label_management', NULL, NULL, NULL, NULL, 21, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config/num-rule', '/documents-config', 'numRule', NULL, '编码规则', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config/approveConfig', '/documents-config', 'approveConfig', NULL, '审批配置', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config/print-config', '/documents-config', 'printConfig', NULL, '打印配置', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config/clause', '/documents-config', 'clauseIndex', NULL, '条款管理', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config/inbound-outbound-type-confige', '/documents-config', 'inboundOutboundTypeConfig', NULL, '出入库类型配置', NULL, 'wms', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config/scan-rule', '/documents-config', 'scanRule', NULL, '扫码规则', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config/decode-rule', '/documents-config', 'decodeRule', NULL, '解码规则', NULL, 'dfs', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/documents-config', NULL, NULL, 1, '单据配置', 'menu_documents_config', NULL, NULL, NULL, NULL, 22, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/base-info/organization', '/base-info', 'organization', NULL, '组织机构', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/base-info/post-manager', '/base-info', 'postManager', NULL, '岗位管理', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/base-info/user', '/base-info', 'baseInfo-user', NULL, '用户管理', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/base-info/role-permission', '/base-info', 'rolePermission', NULL, '角色权限', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/base-info/terminal-permission', '/base-info', 'terminalPermission', NULL, '终端权限管理', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/base-info', NULL, '人员信息', 1, '人员信息', 'menu_personnal_info', NULL, NULL, NULL, NULL, 23, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/chang-system-status', '/system_settings', 'changeSystemStatus', NULL, '系统状态设置', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/DBclear/', '/system_settings', 'DBclear', NULL, 'DB容量设置', NULL, 'dfs', NULL, NULL, NULL, 1, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/init-config', '/system_settings', 'initConfig', NULL, '初始化配置', NULL, 'dfs', NULL, NULL, NULL, 2, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/BusinessConfiguration', '/system_settings', 'BusinessConfiguration', NULL, '业务配置', NULL, 'dfs', NULL, NULL, NULL, 3, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/BusinessConfigurationNew', '/system_settings', 'BusinessConfigurationNew', NULL, '业务配置(新)', NULL, 'dfs', NULL, NULL, NULL, 4, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/initialize', '/system_settings', 'initialConfiguration', NULL, '系统同步配置', NULL, 'dfs', NULL, NULL, NULL, 5, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/function-setting', '/system_settings', 'functionSetting', NULL, '功能设置', NULL, 'ams', NULL, NULL, NULL, 6, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/whiteCardSetting', '/system_settings', 'whiteCardSetting', NULL, '白牌设置', NULL, 'dfs', NULL, NULL, NULL, 7, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/app-permission-config', '/system_settings', 'appPermissionConfig', NULL, '小程序设置', NULL, 'dfs', NULL, NULL, NULL, 8, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings/circulation-notice-config', '/system_settings', 'flowNotificationConfiguration', NULL, '流转通知配置', NULL, 'dfs', NULL, NULL, NULL, 9, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system_settings', NULL, '系统设置', 1, '系统设置', 'menu_system_setting', NULL, NULL, NULL, NULL, 24, 0, 0);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system/operation', '/system-operation', 'operation', NULL, '操作日志', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_route`(`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`) VALUES ('/system-operation', NULL, 'operation', 1, '操作日志', 'menu_operation_log', NULL, NULL, NULL, NULL, 25, 0, 0);

-- 下推配置按钮权限调整
UPDATE `sys_permissions` SET  `is_back_stage` = 1 WHERE `id` in('1220801','1220802','1220803','1220804','1220805','1220806','1220807','1220808') ;

update dfs_work_order set sku_id = 0 where sku_id is null;
INSERT INTO `dfs_sku`(`sku_id`, `sku_code`, `sku_name`) VALUES (-1, '', '');
update dfs_sku set sku_id = 0 where sku_id = -1;
-- 生产订单新增对外接口
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('ams', 'productOrder', 'getReportRemarkListByProductOrderNumber', '根据生产订单编号获取生产订单报工备注', 'POST', '/ams/v1/open/product_orders/report/remark/list');
