set autocommit = 0;

-- <PERSON>L
call proc_add_column(
        'dfs_rule_seq',
        'uuid',
        'ALTER TABLE `dfs_rule_seq` ADD COLUMN `uuid` varchar(255) NULL COMMENT ''自动生成序号所需id，用于找到对应的自增序号''');

call proc_add_column(
        'dfs_rule_seq',
        'relation_type',
        'ALTER TABLE `dfs_rule_seq` ADD COLUMN `relation_type` varchar(255) NULL COMMENT ''关联的编号类型'' AFTER `relation_number`');

call proc_add_column(
        'dfs_stock_transfer',
        'transfer_number',
        'ALTER TABLE `dfs_stock_transfer` ADD COLUMN `transfer_number` varchar(255) NULL COMMENT ''调拨单号'' AFTER `transfer_id`');

-- 添加唯一索引
alter table dfs_stock_transfer add unique (transfer_number);

call proc_add_column(
        'dfs_stock_check',
        'check_number',
        'ALTER TABLE `dfs_stock_check` ADD COLUMN `check_number` varchar(255) NULL COMMENT ''盘点单号'' AFTER `check_id`');

-- 添加唯一索引
alter table dfs_stock_check add unique (check_number);

-- 检验方案与检验项目关联表增加"检验方式"字段
call proc_add_column(
        'dfs_quality_inspection_item_group',
        'inspection_method',
        'ALTER TABLE `dfs_quality_inspection_item_group` ADD COLUMN `inspection_method` varchar(255) NULL COMMENT ''检验方式'' AFTER `quality_inspection_scheme_id`');

-- 新增检验方案文件关联表
CREATE TABLE IF NOT EXISTS `dfs_quality_inspection_scheme_files` (
       `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
       `scheme_id` int(11) DEFAULT NULL COMMENT '检验方案id',
       `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
       `url` varchar(255) DEFAULT NULL COMMENT '文件路径',
       `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检验方案文件关联表';


-- DML
-- 编码规则枚举
truncate table dfs_config_rule;
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (1, '销售订单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (16, '出货申请-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (17, '销售发货-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (10, '生产订单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (2, '生产工单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (5, '生产工单-生产批次号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (12, '生产工单-生产流水码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (14, '生产工单-生产成品码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (18, '领料申请-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (9, '作业工单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (19, '采购需求-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (20, '采购订单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (21, '采购订单-采购批次号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (22, '采购订单-采购单品码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (23, '采购收货-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (11, '采购收货-采购批次号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (24, '来料检验-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (25, '来料检验-采购批次号(拆批)');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (4, 'BOM-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (26, '工艺-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (27, '入库记录-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (6, '出库记录-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (28, '仓库调拨-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (29, '仓库盘点-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (13, '质检报工-质检号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (15, '产品检测-送检单号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (30, '产品检测-质检单号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (31, '产品检测-报告单号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (32, '生产任务-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (3, '供应商档案-供应商编码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (7, '炉号信息-电炉炉次号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (8, '炉号信息-精炼炉炉次号');

-- 历史数据兼容
UPDATE dfs_rule_seq SET relation_type = 'workOrder'
WHERE relation_number is not null;

UPDATE dfs_config_number_rules SET auto_increment_configure_type = 'workOrder'
WHERE auto_increment_configure_type = 'bill';

UPDATE dfs_rule_seq SET relation_type = 'workOrder'
WHERE relation_type = 'bill';

-- 新增默认的编码规则数据，组成方式为人工输入
DELIMITER $$
DROP PROCEDURE IF EXISTS init_number_rules_config;

CREATE DEFINER=`root`@`%` PROCEDURE `init_number_rules_config`()
BEGIN
    -- 定义变量
    DECLARE s int DEFAULT 0;
    DECLARE type_id int;
    -- 定义游标，并将sql结果集赋值到游标中
    DECLARE cur CURSOR FOR SELECT rule_type
                           FROM dfs_config_rule
                           WHERE rule_type not in (SELECT type FROM dfs_config_number_rules)
                           ORDER BY rule_type;
    -- 声明当游标遍历完后将标志变量置成某个值
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET s = 1;
    -- 打开游标
    open cur;
    -- 将游标中的值赋值给变量，注意：变量名不要和返回的列名同名，变量顺序要和sql结果列的顺序一致
    fetch cur into type_id;
    -- 当s不等于1，也就是未遍历完时，会一直循环
    while s<>1 do
    -- 执行业务逻辑
    INSERT INTO `dfs_config_number_rules`(`type`, `prefix_detail`, `create_by`, `update_by`, `create_time`, `update_time`, `auto_increment_configure_type`) VALUES (type_id, '[{\"code\":1,\"name\":\"人工输入\",\"example\":\"--\"}]', 'admin', NULL, NOW(), NULL, 'day');
    -- 将游标中的值再赋值给变量，供下次循环使用
    fetch cur into type_id;
    -- 当s等于1时表明遍历以完成，退出循环
    end while;
    -- 关闭游标
    close cur;

END$$
DELIMITER ;

call init_number_rules_config();

commit;
