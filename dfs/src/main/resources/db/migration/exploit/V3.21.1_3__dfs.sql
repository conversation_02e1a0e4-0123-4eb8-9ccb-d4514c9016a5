-- 注册V2接口可查询字段
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('materialType', 'id', '物料类型id', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('materialType', 'code', '物料类型编码', 'code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('materialType', 'name', '物料类型名称', 'name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('materialType', 'des', '描述', 'des');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('materialType', 'unitName', '默认单位名称', 'url');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('materialType', 'qualityLevel', '质量等级', 'quality_level');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('materialType', 'isProcessAssembly', '是否是工装品', 'is_process_assembly');

DELETE FROM `dfs_query_field_config` WHERE `prefix` = 'material';
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'id', '物料ID', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'state', '状态', 'state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'code', '物料编码', 'code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'type', '物料类型', 'type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'name', '物料名称', 'name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'standard', '物料规格', 'standard');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'version', '版本', 'version');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'sort', '分类', 'sort');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'updateBy', '更新人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'isBatchMag', '是否按批次管理', 'is_batch_mag');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'deliveryRules', '出库规则', 'delivery_rules');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'approver', '审核人', 'approver');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'actualApprover', '实际审批人', 'actual_approver');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'approvalStatus', '审批状态', 'approval_status');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'approvalSuggestion', '审批建议', 'approval_suggestion');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'approvalTime', '审批时间', 'approval_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'isAuxiliaryMaterial', '是否为辅料', 'is_auxiliary_material');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'level', '物料等级', 'level');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'isSupportCodeManage', '是否生产流水码管理', 'is_support_code_manage');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'loseRate', '损耗率', 'lose_rate');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'drawingNumber', '图号', 'drawing_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'materialPrice', '物料单价', 'material_price');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'rawMaterial', '材质', 'raw_material');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'haveBom', '有无bom', 'have_bom');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'haveCraft', '有无工艺', 'have_craft');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'nameEnglish', '物料英文名称', 'name_english');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'minimumProductionLot', '最小生产批量', 'minimum_production_lot');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'factoryModel', '工厂型号', 'factory_model');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'editor', '编制人用户名', 'editor');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'editorName', '编制人姓名', 'editor_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldOne', '扩展字段1', 'custom_field_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldTwo', '扩展字段2', 'custom_field_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldThree', '扩展字段3', 'custom_field_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldFour', '扩展字段4', 'custom_field_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldFive', '扩展字段5', 'custom_field_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldSix', '扩展字段6', 'custom_field_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldSeven', '扩展字段7', 'custom_field_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldEight', '扩展字段8', 'custom_field_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldNine', '扩展字段9', 'custom_field_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldTen', '扩展字段10', 'custom_field_ten');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'customFieldEleven', '扩展字段11', 'custom_field_eleven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'packageSchemeCode', '包装方案编码', 'package_scheme_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'isPrint', '是否已打印', 'is_print');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'qualityLevel', '质量等级', 'quality_level');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'storageConditions', '储藏条件', 'storage_conditions');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'isEnableShelfLife', '是否启用保质期', 'is_enable_shelf_life');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'shelfLifeTime', '保质期', 'shelf_life_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'shelfLifeUnit', '保质期单位', 'shelf_life_unit');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('material', 'releaseTime', '生效时间', 'release_time');

-- 用户列表
DELETE FROM `dfs_query_field_config` WHERE `prefix` = 'user';
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'id', '用户id', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'username', '用户名', 'user_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'mobile', '手机号', 'mobile');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'email', '邮箱', 'email');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'nickname', '用户昵称', 'nick_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'jobNumber', '工号', 'job_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'enabled', '账号是否可用', 'enabled');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'updateBy', '更新人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('user', 'updateTime', '更新时间', 'update_time');

-- 供应商物料
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'id', '供应商物料id', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierId', '供应商id', 'supplier_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierCode', '供应商编码', 'supplier_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'materialId', '物料id', 'material_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialCode', '供应商物料编号', 'supplier_material_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialName', '供应商物料名称', 'supplier_material_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialStandard', '供应商物料规格', 'supplier_material_standard');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'state', '状态（1-创建 2-生效 3-停用 4-废弃）', 'state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldOne', '扩展字段1', 'supplier_material_extend_field_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldTwo', '扩展字段2', 'supplier_material_extend_field_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldThree', '扩展字段3', 'supplier_material_extend_field_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldFour', '扩展字段4', 'supplier_material_extend_field_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldFive', '扩展字段5', 'supplier_material_extend_field_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldSix', '扩展字段6', 'supplier_material_extend_field_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldSeven', '扩展字段7', 'supplier_material_extend_field_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldEight', '扩展字段8', 'supplier_material_extend_field_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldNine', '扩展字段9', 'supplier_material_extend_field_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplierMaterial', 'supplierMaterialExtendFieldTen', '扩展字段10', 'supplier_material_extend_field_ten');

-- 工单关联表 --> 生产基本单元
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderBasicUnitRelation', 'workOrderNumber', '生产工单号', 'work_order_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderBasicUnitRelation', 'productionBasicUnitId', '生产基本单元id', 'production_basic_unit_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderBasicUnitRelation', 'productionBasicUnitCode', '生产基本单元编码', 'production_basic_unit_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderBasicUnitRelation', 'productionBasicUnitName', '生产基本单元名称', 'production_basic_unit_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderBasicUnitRelation', 'workCenterId', '工作中心id', 'work_center_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderBasicUnitRelation', 'workCenterType', '工作中心类型', 'work_center_type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderBasicUnitRelation', 'isProducing', '是否投产', 'is_producing');

-- 制造单元
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'productionLineId', '制造单元id', 'production_line_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'productionLineCode', '制造单元编码', 'production_line_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'name', '制造单元名称', 'name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'aid', '关联的厂区id', 'aid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'gid', '关联的车间id', 'gid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'modelId', '模型id', 'model_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'magName', '负责人用户名', 'mag_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'magNickname', '负责人姓名', 'mag_nickname');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'magPhone', '负责人电话', 'mag_phone');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'createDate', '创建时间', 'create_date');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'updateDate', '更新时间', 'update_date');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('line', 'updateBy', '更新人', 'update_by');

-- 供应商档案
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'id', '供应商ID', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'code', '供应商编码', 'code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'name', '供应商名称', 'name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'phone', '联系方式', 'phone');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'addr', '地址', 'addr');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'type', '供应商类型', 'type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'contacts', '联系人', 'contacts');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'comprehensiveEvaluation', '综合评价', 'comprehensive_evaluation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'qualityEvaluation', '质量评价', 'quality_evaluation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'deliveryEvaluation', '交付评价', 'delivery_evaluation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'priceLevelEvaluation', '价格水平评价', 'price_level_evaluation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'serviceEvaluation', '服务评价', 'service_evaluation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'manageEvaluation', '管理能力评价', 'manage_evaluation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'counterpart', '本厂对接人', 'counterpart');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'level', '等级', 'level');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldOne', '扩展字段1', 'supplier_extend_field_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldTwo', '扩展字段2', 'supplier_extend_field_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldThree', '扩展字段3', 'supplier_extend_field_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldFour', '扩展字段4', 'supplier_extend_field_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldFive', '扩展字段5', 'supplier_extend_field_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldSix', '扩展字段6', 'supplier_extend_field_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldSeven', '扩展字段7', 'supplier_extend_field_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldEight', '扩展字段8', 'supplier_extend_field_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldNine', '扩展字段9', 'supplier_extend_field_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'supplierExtendFieldTen', '扩展字段10', 'supplier_extend_field_ten');

-- 工作中心
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workCenter', 'id', '工作中心id', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workCenter', 'code', '工作中心编码', 'code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workCenter', 'name', '工作中心名称', 'name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workCenter', 'magNickname', '负责人姓名', 'mag_nickname');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workCenter', 'magName', '负责人账号', 'mag_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workCenter', 'magPhone', '负责人手机号', 'mag_phone');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workCenter', 'aid', '厂区id', 'aid');


-- 质检记录
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'workOrderNumber', '工单号', 'work_order_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'materialCode', '物料物料编码', 'material_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'fid', '工位号', 'fid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'fname', '工位名称', 'fname');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'sequenceId', '序列号id', 'sequence_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'serialNumber', '记录号', 'serial_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'schemeId', '质检方案id', 'scheme_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'defectId', '不良定义id', 'defect_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'state', '状态（1-未处理 0-已处理）', 'state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'defectType', '不良类型', 'defect_type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'abnormalName', '不良名称', 'abnormal_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'recordDate', '记录日期', 'record_date');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('recordWorkOrderUnqualified', 'createBy', '创建人', 'create_by');

-- 工位
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('facilities', 'fid', '工位id', 'fid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('facilities', 'fname', '工位名称', 'fname');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('facilities', 'gid', '车间id', 'gid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('facilities', 'productionLineId', '产线id', 'production_line_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('model', 'name', '模型名称', 'name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('facUser', 'userName', '员工账号', 'user_name');

-- 班次
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('shift', 'shiftType', '班次类型', 'shift_type');

-- 设备
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceId', '设备id', 'device_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceName', '设备名称', 'device_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceCode', '设备编号', 'device_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'cid', '单位id', 'cid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'aid', '厂区id', 'aid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'gid', '车间id', 'gid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'fid', '工位id', 'fid');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'productionLineId', '制造单元id', 'production_line_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'modelId', '模型id', 'model_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'typeCode', '设备分类', 'type_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'typeName', '设备分类名称', 'type_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'state', '运行状态(0-停机 1-运行中 2-待机 3-故障 4-运行到位)', 'state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'useState', '设备使用状态(0在用、1闲置、2维修、3移交、4报废、5借出)', 'use_state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'place', '位置', 'place');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'magName', '负责人账号', 'mag_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'updateBy', '更新人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'purchaseDate', '购置日期', 'purchase_date');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'maintainer', '设备维护人', 'maintainer');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'brandModel', '品牌型号', 'brand_model');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendOne', '扩展字段1', 'device_extend_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendTwo', '扩展字段2', 'device_extend_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendThree', '扩展字段3', 'device_extend_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendFour', '扩展字段4', 'device_extend_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendFive', '扩展字段5', 'device_extend_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendSix', '扩展字段6', 'device_extend_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendSeven', '扩展字段7', 'device_extend_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendEight', '扩展字段8', 'device_extend_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendNine', '扩展字段9', 'device_extend_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('device', 'deviceExtendTen', '扩展字段10', 'device_extend_ten');

-- 工单关联工艺工序表
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'workOrderId', '工单id', 'work_order_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'workOrderNumber', '工单号', 'work_order_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'procedureId', '工序定义id', 'procedure_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'procedureName', '工序定义编码', 'procedure_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'craftId', '工艺id', 'craft_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'craftCode', '工艺编码', 'craft_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'craftProcedureId', '工艺工序id', 'craft_procedure_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('workOrderProcedureRelation', 'craftProcedureName', '工艺工序名称', 'craft_procedure_name');


-- 销售退货单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnId', '销售退货主键id', 'sale_return_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnCode', '销售退货单号', 'sale_return_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnName', '销售退货单名称', 'sale_return_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'returnType', '单据类型', 'return_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'businessType', '业务类型', 'business_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'relatedType', '关联单据类型', 'related_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'relatedCode', '关联单据', 'related_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'customerName', '客户名称', 'customer_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'customerCode', '客户编码', 'customer_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'state', '销售退货单状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'updateTime', '更新时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'approval', '审核人', 'approval', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'actualApproval', '实际审批人', 'actual_approval', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'approvalStatus', '审批状态', 'approval_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'approvalSuggestion', '审批建议', 'approval_suggestion', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'approvalTime', '审批时间', 'approval_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldOne', '销售退货单拓展字段1', 'sale_return_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldTwo', '销售退货单拓展字段2', 'sale_return_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldThree', '销售退货单拓展字段3', 'sale_return_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldFour', '销售退货单拓展字段4', 'sale_return_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldFive', '销售退货单拓展字段5', 'sale_return_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldSix', '销售退货单拓展字段6', 'sale_return_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldSeven', '销售退货单拓展字段7', 'sale_return_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldEight', '销售退货单拓展字段8', 'sale_return_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldNine', '销售退货单拓展字9', 'sale_return_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnEntity', 'saleReturnExtendFieldTen', '销售退货单拓展字段10', 'sale_return_extend_field_ten', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialId', 'saleReturnMaterialId', 'sale_return_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnId', '销售退货单号ID', 'sale_return_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnCode', '销售退货单号', 'sale_return_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'materialCode', '物料编号', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'materialName', '物料名称', 'material_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'customerMaterialCode', '客户物料编码', 'customer_material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'customerMaterialName', '客户物料名称', 'customer_material_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'customerSpecification', '客户物料规格', 'customer_specification', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'isGift', '是否赠品,0非赠品、1赠品', 'is_gift', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'planAmount', '计划数量', 'plan_amount', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'actualAmount', '实际数量', 'actual_amount', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'returnType', '退货类型', 'return_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'materialRemark', '备注', 'material_remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'updateTime', '更新时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'businessUnitCode', '业务单元编码', 'business_unit_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'businessUnitName', '业务单元名称', 'business_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldOne', '销售退货单物料拓展字段1', 'sale_return_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldTwo', '销售退货单物料拓展字段2', 'sale_return_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldThree', '销售退货单物料拓展字段3', 'sale_return_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldFour', '销售退货单物料拓展字段4', 'sale_return_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldFive', '销售退货单物料拓展字段5', 'sale_return_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldSix', '销售退货单物料拓展字段6', 'sale_return_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldSeven', '销售退货单物料拓展字段7', 'sale_return_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldEight', '销售退货单物料拓展字段8', 'sale_return_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldNine', '销售退货单物料拓展字段9', 'sale_return_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialEntity', 'saleReturnMaterialExtendFieldTen', '销售退货单物料拓展字段10', 'sale_return_material_extend_field_ten', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'returnMaterialBatchId', '销售退货单批次信息', 'return_material_batch_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'saleReturnId', '销售退货单号ID', 'sale_return_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'saleReturnCode', '销售退货单号', 'sale_return_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'materialCode', '物料编号', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'barCode', '批次编码', 'bar_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'barCount', '计划数量', 'bar_count', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleReturnMaterialBatchEntity', 'returnType', '退货类型', 'return_type', NULL);

-- 委外退货单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'id', '委外退货单主键', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'returnCode', '退料单号', 'return_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'subcontractOrderNumber', '委外订单号', 'subcontract_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'relevanceCode', '关联单号', 'relevance_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'returnType', '单据类型', 'return_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'businessType', '业务类型', 'business_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'returnReason', '退料原因', 'return_reason', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'relevanceType', '关联单据类型', 'relevance_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'supplierName', '供应商名称', 'supplier_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'supplierCode', '供应商编码', 'supplier_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'state', '退料单状态 1-创建 2-生效 3-完成 4-关闭 5-取消', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'finishTime', '完成时间', 'finish_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'updateTime', '更新时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'approve', '审核人', 'approve', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'actualApprove', '实际审批人', 'actual_approve', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'approvalStatus', '审批状态', 'approval_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'approvalSuggestion', '审批建议', 'approval_suggestion', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'approvalTime', '审批时间', 'approval_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldOne', '单据拓展字段1', 'order_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldTwo', '单据拓展字段2', 'order_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldThree', '单据拓展字段3', 'order_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldFour', '单据拓展字段4', 'order_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldFive', '单据拓展字段5', 'order_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldSix', '单据拓展字段6', 'order_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldSeven', '单据拓展字段7', 'order_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldEight', '单据拓展字段8', 'order_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldNine', '单据拓展字段9', 'order_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnEntity', 'orderExtendFieldTen', '单据拓展字段10', 'order_extend_field_ten', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'id', '物料行id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'returnId', '退料单id', 'return_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'returnCode', '退料单号', 'return_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'materialCode', '物料编号', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'materialName', '物料名称', 'material_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'amount', '退料数量', 'amount', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'businessUnitCode', '业务单元编码', 'business_unit_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'businessUnitName', '业务单元名称', 'business_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'updateTime', '更新时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'returnTime', '退料时间', 'return_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldOne', '单据物料行拓展字段1', 'order_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldTwo', '单据物料行拓展字段1', 'order_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldThree', '单据物料行拓展字段1', 'order_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldFour', '单据物料行拓展字段1', 'order_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldFive', '单据物料行拓展字段1', 'order_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldSix', '单据物料行拓展字段1', 'order_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldSeven', '单据物料行拓展字段1', 'order_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldEight', '单据物料行拓展字段1', 'order_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldNine', '单据物料行拓展字段1', 'order_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnMaterialEntity', 'orderMaterialExtendFieldTen', '单据物料行拓展字段1', 'order_material_extend_field_ten', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnBatchEntity', 'id', '批次id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnBatchEntity', 'barCode', '批次号', 'bar_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnBatchEntity', 'returnMaterialId', '退料单物料关联表id', 'return_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnBatchEntity', 'materielCode', '物料编号', 'materiel_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnBatchEntity', 'returnAmount', '退料数量', 'return_amount', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnBatchEntity', 'batchAmount', '批次数量', 'batch_amount', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReturnBatchEntity', 'returnCode', '退料单号', 'return_code', NULL);

-- 销售出货申请
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'id', '主键id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'applicationNum', '申请单号', 'application_num', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'saleOrderId', '订单id', 'sale_order_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'saleOrderNumber', '订单编号(销售订单)', 'sale_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'orderType', '单据类型', 'order_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'businessType', '业务类型', 'business_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'planDeliveryTime', '计划出货时间', 'plan_delivery_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'state', '状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'customerCode', '客户编码', 'customer_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'customerName', '客户名称', 'customer_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'updateTime', '更新时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'approver', '审核人', 'approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'actualApprover', '实际审批人', 'actual_approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'approvalStatus', '审批状态', 'approval_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'approvalSuggestion', '审批建议', 'approval_suggestion', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'approvalTime', '审批时间', 'approval_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'applicant', '申请人', 'applicant', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'warehouseCode', '仓库编码', 'warehouse_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldOne', '仓库编码', 'delivery_application_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldTwo', '仓库编码', 'delivery_application_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldThree', '仓库编码', 'delivery_application_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldFour', '仓库编码', 'delivery_application_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldFive', '仓库编码', 'delivery_application_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldSix', '仓库编码', 'delivery_application_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldSeven', '仓库编码', 'delivery_application_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldEight', '仓库编码', 'delivery_application_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldNine', '仓库编码', 'delivery_application_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationEntity', 'deliveryApplicationExtendFieldTen', '仓库编码', 'delivery_application_extend_field_ten', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationId', '销售出货单id', 'delivery_application_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationNum', '销售出货单号', 'delivery_application_num', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'relateOrderMaterialId', '关联的销售订单物料行ID', 'relate_order_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'materialCode', '物料编码', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'isGift', '是否赠品', 'is_gift', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'amount', '物料数量', 'amount', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'salesQuantity', '销售数量', 'sales_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'outStockQuantity', '已出库数', 'out_stock_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'returnInQuantity', '已退货数', 'return_in_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'customerMaterialCode', '客户物料编码', 'customer_material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'customerMaterialName', '客户物料名称', 'customer_material_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'requireGoodsDate', '要货日期', 'require_goods_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'businessUnitCode', '业务主体编码', 'business_unit_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'businessUnitName', '业务主体名称', 'business_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldOne', '销售出货单物料扩展字段1', 'delivery_application_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldTwo', '销售出货单物料扩展字段2', 'delivery_application_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldThree', '销售出货单物料扩展字段3', 'delivery_application_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldFour', '销售出货单物料扩展字段4', 'delivery_application_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldFive', '销售出货单物料扩展字段5', 'delivery_application_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldSix', '销售出货单物料扩展字段6', 'delivery_application_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldSeven', '销售出货单物料扩展字段7', 'delivery_application_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldEight', '销售出货单物料扩展字段8', 'delivery_application_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldNine', '销售出货单物料扩展字段9', 'delivery_application_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'deliveryApplicationMaterialEntity', 'deliveryApplicationMaterialExtendFieldTen', '销售出货单物料扩展字段10', 'delivery_application_material_extend_field_ten', NULL);

-- 委外订单收货单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'receiptNumber', '收货单号', 'receipt_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`)VALUES (null, 'subcontractReceiptEntity', 'supplier', '供应商编码', 'supplier', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`)VALUES (null, 'subcontractReceiptEntity', 'priority', '优先级', 'priority', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'supplierContact', '供应商联系方式', 'supplier_contact', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'receiptTime', '收货时间', 'receipt_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'state', '状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderType', '单据类型', 'order_type', '标准收货单 standardSubcontractOrderReceiptOrder');
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'businessType', '业务类型', 'business_type', '标准收货 standardSubcontractOrderReceiptOrder');
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'approvalStatus', '审批状态', 'approval_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'approver', '审批人', 'approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'actualApprover', '实际审批人', 'actual_approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'approvalSuggestion', '审批意见', 'approval_suggestion', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'approvalTime', '审批时间', 'approval_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'updateBy', '修改人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'updateTime', '修改时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldOne', '单据拓展字段1', 'order_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldTwo', '单据拓展字段2', 'order_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldThree', '单据拓展字段3', 'order_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldFour', '单据拓展字段4', 'order_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldFive', '单据拓展字段5', 'order_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldSix', '单据拓展字段6', 'order_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldSeven', '单据拓展字段7', 'order_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldEight', '单据拓展字段8', 'order_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldNine', '单据拓展字段9', 'order_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptEntity', 'orderExtendFieldTen', '单据拓展字段10', 'order_extend_field_ten', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'receiptId', '收货单号ID', 'receipt_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'receiptNumber', '收货单号', 'receipt_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'subcontractOrder', '委外订单号', 'subcontract_order', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'materialCode', '物料编码', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'receiptQuantity', '收货数量', 'receipt_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'inputQuantity', '入库数量', 'input_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'businessUnitCode', '业务单元编码', 'business_unit_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'businessUnitName', '业务单元名称', 'business_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldOne', '单据物料行拓展字段1', 'order_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldTwo', '单据物料行拓展字段2', 'order_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldThree', '单据物料行拓展字段3', 'order_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldFour', '单据物料行拓展字段4', 'order_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldFive', '单据物料行拓展字段5', 'order_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldSix', '单据物料行拓展字段6', 'order_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldSeven', '单据物料行拓展字段7', 'order_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldEight', '单据物料行拓展字段8', 'order_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldNine', '单据物料行拓展字段9', 'order_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractReceiptDetailEntity', 'orderMaterialExtendFieldTen', '单据物料行拓展字段10', 'order_material_extend_field_ten', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`)
VALUES
(null, 'subcontractReceiptDetailBatchEntity', 'id', '主键', 'id', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'receiptId', '委外收货单id', 'receipt_id', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'receiptNumber', '委外收货单单号', 'receipt_number', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'relateMaterialRowId', '关联委外收货单物料行id', 'relate_material_row_id', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'materielCode', '物料编码', 'materiel_code', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'barCode', '批次号', 'bar_code', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'barCount', '批次数量', 'bar_count', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'receiveState', '收货状态', 'receive_state', '0未收货、1已收货'),
(null, 'subcontractReceiptDetailBatchEntity', 'receiveTime', '批次收料时间', 'receive_time', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'createBy', '创建人', 'create_by', NULL),
(null, 'subcontractReceiptDetailBatchEntity', 'createTime', '创建时间', 'create_time', NULL);


-- 采购需求
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`)
VALUES
(null, 'purchaseRequestEntity', 'id', '采购需求ID', 'id', NULL),
(null, 'purchaseRequestEntity', 'projectContract', '存在项目合同', 'project_contract', NULL),
(null, 'purchaseRequestEntity', 'state', '状态（1-创建 4-生效 5-完成 6-关闭 7-取消）', 'state', NULL),
(null, 'purchaseRequestEntity', 'requestNum', '采购需求单号', 'request_num', NULL),
(null, 'purchaseRequestEntity', 'orderType', '单据类型', 'order_type', NULL),
(null, 'purchaseRequestEntity', 'businessType', '业务类型', 'business_type', NULL),
(null, 'purchaseRequestEntity', 'purchaseState', '采购状态', 'purchase_state', NULL),
(null, 'purchaseRequestEntity', 'createBy', '创建人', 'create_by', NULL),
(null, 'purchaseRequestEntity', 'updateBy', '更新人', 'update_by', NULL),
(null, 'purchaseRequestEntity', 'updateTime', '更新时间', 'update_time', NULL),
(null, 'purchaseRequestEntity', 'createTime', '创建时间', 'create_time', NULL),
(null, 'purchaseRequestEntity', 'approver', '审核人', 'approver', NULL),
(null, 'purchaseRequestEntity', 'actualApprover', '实际审批人', 'actual_approver', NULL),
(null, 'purchaseRequestEntity', 'approvalStatus', '审批状态', 'approval_status', NULL),
(null, 'purchaseRequestEntity', 'approvalSuggestion', '审批建议', 'approval_suggestion', NULL),
(null, 'purchaseRequestEntity', 'remark', '备注', 'remark', NULL),
(null, 'purchaseRequestEntity', 'approvalTime', '审批时间', 'approval_time', NULL),
(null, 'purchaseRequestEntity', 'applicant', '申请人', 'applicant', NULL),
(null, 'purchaseRequestEntity', 'applicationDate', '申请日期', 'application_date', NULL),
(null, 'purchaseRequestEntity', 'projectName', '项目名称', 'project_name', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldOne', '采购需求扩展字段1', 'purchase_request_extend_field_one', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldTwo', '采购需求扩展字段2', 'purchase_request_extend_field_two', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldThree', '采购需求扩展字段3', 'purchase_request_extend_field_three', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldFour', '采购需求扩展字段4', 'purchase_request_extend_field_four', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldFive', '采购需求扩展字段5', 'purchase_request_extend_field_five', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldSix', '采购需求扩展字段6', 'purchase_request_extend_field_six', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldSeven', '采购需求扩展字段7', 'purchase_request_extend_field_seven', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldEight', '采购需求扩展字段8', 'purchase_request_extend_field_eight', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldNine', '采购需求扩展字段9', 'purchase_request_extend_field_nine', NULL),
(null, 'purchaseRequestEntity', 'purchaseRequestExtendFieldTen', '采购需求扩展字段10', 'purchase_request_extend_field_ten', NULL),
(null, 'purchaseRequestMaterialEntity', 'id', 'ID', 'id', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestId', '需求表ID', 'request_id', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestNum', '需求表编号', 'request_num', NULL),
(null, 'purchaseRequestMaterialEntity', 'code', '物料编码', 'code', NULL),
(null, 'purchaseRequestMaterialEntity', 'materialId', '物料id', 'material_id', NULL),
(null, 'purchaseRequestMaterialEntity', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL),
(null, 'purchaseRequestMaterialEntity', 'businessUnitCode', '业务单元编码', 'business_unit_code', NULL),
(null, 'purchaseRequestMaterialEntity', 'businessUnitName', '业务单元名称', 'business_unit_name', NULL),
(null, 'purchaseRequestMaterialEntity', 'num', '所需物料数量, 1.18.1变更改为 计划数量', 'num', NULL),
(null, 'purchaseRequestMaterialEntity', 'referenceQuantity', '参考数量', 'reference_quantity', NULL),
(null, 'purchaseRequestMaterialEntity', 'materialRemarks', '备注', 'material_remark', NULL),
(null, 'purchaseRequestMaterialEntity', 'purchaseRequestNum', '采购数量(关联的采购订单数量，计划数量之和)', 'purchase_request_num', NULL),
(null, 'purchaseRequestMaterialEntity', 'skuId', '特征参数skuId', 'sku_id', NULL),
(null, 'purchaseRequestMaterialEntity', 'purchaseDate', '要货日期', 'purchase_date', NULL),
(null, 'purchaseRequestMaterialEntity', 'suggestedSupplier', '建议供应商', 'suggested_supplier', NULL),
(null, 'purchaseRequestMaterialEntity', 'updateBy', '更新人', 'update_by', NULL),
(null, 'purchaseRequestMaterialEntity', 'createBy', '创建人', 'create_by', NULL),
(null, 'purchaseRequestMaterialEntity', 'updateTime', '更新时间', 'update_time', NULL),
(null, 'purchaseRequestMaterialEntity', 'createTime', '创建时间', 'create_time', NULL),
(null, 'purchaseRequestMaterialEntity', 'customerCode', '客户编码', 'customer_code', NULL),
(null, 'purchaseRequestMaterialEntity', 'customerName', '客户名称', 'customer_name', NULL),
(null, 'purchaseRequestMaterialEntity', 'relateMaterialRowId', '关联上级物料行id', 'relate_material_row_id', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldOne', '采购需求单物料扩展字段1', 'request_material_extend_field_one', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldTwo', '采购需求单物料扩展字段2', 'request_material_extend_field_two', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldThree', '采购需求单物料扩展字段3', 'request_material_extend_field_three', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldFour', '采购需求单物料扩展字段4', 'request_material_extend_field_four', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldFive', '采购需求单物料扩展字段5', 'request_material_extend_field_five', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldSix', '采购需求单物料扩展字段6', 'request_material_extend_field_six', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldSeven', '采购需求单物料扩展字段7', 'request_material_extend_field_seven', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldEight', '采购需求单物料扩展字段8', 'request_material_extend_field_eight', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldNine', '采购需求单物料扩展字段9', 'request_material_extend_field_nine', NULL),
(null, 'purchaseRequestMaterialEntity', 'requestMaterialExtendFieldTen', '采购需求单物料扩展字段10', 'request_material_extend_field_ten', NULL);

-- 字典条款查询字段配置
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','id','主键id','id',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','code','码值','code',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','name','名称','name',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','type','类型','type',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','des','描述','des',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','url','链接地址','url',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','unit','单位','unit',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','value','值(多个按逗号分隔)','value',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','pid','上级id','pid',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','pCode','上级编码','p_code',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','createTime','创建时间','create_time',null);
INSERT INTO `dfs_query_field_config`(id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dict','createBy','创建人','create_by',null);

INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dictFile','id','主键id','id',null);
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dictFile','dictId','字典id','dict_id',null);
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dictFile','name','名称','name',null);
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dictFile','fileUrl','文件地址url','file_url',null);
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dictFile','createTime','创建时间','create_time',null);
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark)
VALUES (null,'dictFile','createBy','创建人','create_by',null);

-- 生产订单用料清单字段配置
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark) VALUES
                                                                                                        (null,'productMaterialList','materialListId','生产订单用料清单id','material_list_id',null),
                                                                                                        (null,'productMaterialList','materialListCode','生产订单用料清单编号','material_list_code',null),
                                                                                                        (null,'productMaterialList','state','生产订单用料清单状态（1-创建 2-生效 3-完成 4-关闭 5-取消）','state',null),
                                                                                                        (null,'productMaterialList','relateType','关联单据类型','relate_type',null),
                                                                                                        (null,'productMaterialList','orderType','单据类型','order_type',null),
                                                                                                        (null,'productMaterialList','businessType','业务类型','business_type',null),
                                                                                                        (null,'productMaterialList','relateNumber','关联单据编号','relate_number',null),
                                                                                                        (null,'productMaterialList','relateMaterialCode','关联单据物料编码','relate_material_code',null),
                                                                                                        (null,'productMaterialList','relateQuantity','关联单据数量','relate_quantity',null),
                                                                                                        (null,'productMaterialList','createBy','创建人','create_by',null),
                                                                                                        (null,'productMaterialList','createTime','创建时间','create_time',null),
                                                                                                        (null,'productMaterialList','approver','实际审批人','approver',null),
                                                                                                        (null,'productMaterialList','actualApprover','实际审批人','actual_approver',null),
                                                                                                        (null,'productMaterialList','approvalStatus','审批状态','approval_status',null),
                                                                                                        (null,'productMaterialList','approvalSuggestion','审批建议','approval_suggestion',null),
                                                                                                        (null,'productMaterialList','approvalTime','审批时间','approval_time',null),
                                                                                                        (null,'productMaterialList','remark','备注','remark',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldOne','生产订单用料清单扩展字段1','material_list_extend_field_one',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldTwo','生产订单用料清单扩展字段2','material_list_extend_field_two',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldThree','生产订单用料清单扩展字段3','material_list_extend_field_three',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldFour','生产订单用料清单扩展字段4','material_list_extend_field_four',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldFive','生产订单用料清单扩展字段5','material_list_extend_field_five',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldSix','生产订单用料清单扩展字段6','material_list_extend_field_six',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldSeven','生产订单用料清单扩展字段7','material_list_extend_field_seven',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldEight','生产订单用料清单扩展字段8','material_list_extend_field_eight',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldNine','生产订单用料清单扩展字段9','material_list_extend_field_nine',null),
                                                                                                        (null,'productMaterialList','materialListExtendFieldTen','生产订单用料清单扩展字段10','material_list_extend_field_ten',null),
                                                                                                        (null,'productMaterialListMaterial','id','用料清单关联物料id','id',null),
                                                                                                        (null,'productMaterialListMaterial','materialListId','生产订单用料清单id','material_list_id',null),
                                                                                                        (null,'productMaterialListMaterial','materialListCode','生产订单用料清单编号','material_list_code',null),
                                                                                                        (null,'productMaterialListMaterial','materialListPlanQuantity','生产订单用料清单计划数量','material_list_plan_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','materialCode','物料编码','material_code',null),
                                                                                                        (null,'productMaterialListMaterial','externalMaterialId','ERP关联物料行id','external_material_id',null),
                                                                                                        (null,'productMaterialListMaterial','bomNumerator','bom分子','bom_numerator',null),
                                                                                                        (null,'productMaterialListMaterial','bomDenominator','bom分母','bom_denominator',null),
                                                                                                        (null,'productMaterialListMaterial','planQuantity','计划数量','plan_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','actualQuantity','实际数量','actual_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','uncollectedQuantity','未领数量','uncollected_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','referenceQuantity','参考数量','reference_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','returnQuantity','退料数量','return_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','pushDownQuantity','已下推数量','push_down_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','subType','子项类型','sub_type',null),
                                                                                                        (null,'productMaterialListMaterial','createTime','创建时间','create_time',null),
                                                                                                        (null,'productMaterialListMaterial','replaceQuantity','替代数量','replace_quantity',null),
                                                                                                        (null,'productMaterialListMaterial','min_material_code','主物料编号','main_material_code',null),
                                                                                                        (null,'productMaterialListMaterial','skuId','特征参数skuId','sku_id',null),
                                                                                                        (null,'productMaterialListMaterial', 'bomRawMaterialId', 'BOM子物料id', 'bom_raw_material_id', null),
                                                                                                        (null,'productMaterialListMaterial', 'bomRawMaterialExtendOne', 'BOM子物料扩展字段', 'bom_raw_material_extend_one', null),
                                                                                                        (null,'productMaterialListMaterial', 'businessUnitCode', '业务单元编码', 'business_unit_code', null),
                                                                                                        (null,'productMaterialListMaterial', 'businessUnitName', '业务单元名称', 'business_unit_name', null),
                                                                                                        (null,'productMaterialListMaterial', 'supplementOutQuantity', '补料出库数', 'supplement_out_quantity', null),
                                                                                                        (null,'productMaterialListMaterial', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldOne', '生产订单用料清单物料扩展字段1', 'material_list_material_extend_field_one', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldTwo', '生产订单用料清单物料扩展字段2', 'material_list_material_extend_field_two', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldThree', '生产订单用料清单物料扩展字段3', 'material_list_material_extend_field_three', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldFour', '生产订单用料清单物料扩展字段4', 'material_list_material_extend_field_four', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldFive', '生产订单用料清单物料扩展字段5', 'material_list_material_extend_field_five', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldSix', '生产订单用料清单物料扩展字段6', 'material_list_material_extend_field_six', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldSeven', '生产订单用料清单物料扩展字段7', 'material_list_material_extend_field_seven', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldEight', '生产订单用料清单物料扩展字段8', 'material_list_material_extend_field_eight', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldNine', '生产订单用料清单物料扩展字段9', 'material_list_material_extend_field_nine', null),
                                                                                                        (null,'productMaterialListMaterial', 'materialListMaterialExtendFieldTen', '生产订单用料清单物料扩展字段10', 'material_list_material_extend_field_ten', null);

-- 采购退料字段配置
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark) VALUES
                                                                                                        (null, 'purchaseReturn', 'id', '主键', 'id', null),
                                                                                                        (null, 'purchaseReturn', 'returnCode', '退料单号', 'return_code', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseCode', '采购单号', 'purchase_code', null),
                                                                                                        (null, 'purchaseReturn', 'relevanceCode', '关联单号', 'relevance_code', null),
                                                                                                        (null, 'purchaseReturn', 'returnType', '单据类型：inspectionReturn 检验退料单,receiveReturn 收料退料单, warehouseReturn 库存退料单', 'return_type', null),
                                                                                                        (null, 'purchaseReturn', 'businessType', '业务类型：inspectReturnOrder 检验退料,receiveMaterialReturnOrder 收料退料,inventoryReturnOrder 库存退料', 'business_type', null),
                                                                                                        (null, 'purchaseReturn', 'returnReason', '退料原因', 'return_reason', null),
                                                                                                        (null, 'purchaseReturn', 'relevanceType', '关联单据类型：receipt、purchaseReturnIssueDoc', 'relevance_type', null),
                                                                                                        (null, 'purchaseReturn', 'supplierName', '供应商名称', 'supplier_name', null),
                                                                                                        (null, 'purchaseReturn', 'supplierCode', '供应商编码', 'supplier_code', null),
                                                                                                        (null, 'purchaseReturn', 'supplierPhone', '供应商联系方式', 'supplier_phone', null),
                                                                                                        (null, 'purchaseReturn', 'returnTime', '退料时间', 'return_time', null),
                                                                                                        (null, 'purchaseReturn', 'state', '退料单状态 1-创建 2-生效 3-完成 4-关闭 5-取消', 'state', null),
                                                                                                        (null, 'purchaseReturn', 'finishTime', '完成时间', 'finish_time', null),
                                                                                                        (null, 'purchaseReturn', 'updateBy', '更新人', 'update_by', null),
                                                                                                        (null, 'purchaseReturn', 'createBy', '创建人', 'create_by', null),
                                                                                                        (null, 'purchaseReturn', 'updateTime', '更新时间', 'update_time', null),
                                                                                                        (null, 'purchaseReturn', 'createTime', '创建时间', 'create_time', null),
                                                                                                        (null, 'purchaseReturn', 'approve', '审核人', 'approve', null),
                                                                                                        (null, 'purchaseReturn', 'actualApprove', '实际审批人', 'actual_approve', null),
                                                                                                        (null, 'purchaseReturn', 'approvalStatus', '审批状态', 'approval_status', null),
                                                                                                        (null, 'purchaseReturn', 'remark', '备注', 'remark', null),
                                                                                                        (null, 'purchaseReturn', 'approvalSuggestion', '审批建议', 'approval_suggestion', null),
                                                                                                        (null, 'purchaseReturn', 'approvalTime', '审批时间', 'approval_time', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldOne', '采购退料单扩展字段1', 'purchase_return_extend_field_one', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldTwo', '采购退料单扩展字段2', 'purchase_return_extend_field_two', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldThree', '采购退料单扩展字段3', 'purchase_return_extend_field_three', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldFour', '采购退料单扩展字段4', 'purchase_return_extend_field_four', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldFive', '采购退料单扩展字段5', 'purchase_return_extend_field_five', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldSix', '采购退料单扩展字段6', 'purchase_return_extend_field_six', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldSeven', '采购退料单扩展字段7', 'purchase_return_extend_field_seven', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldEight', '采购退料单扩展字段8', 'purchase_return_extend_field_eight', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldNine', '采购退料单扩展字段9', 'purchase_return_extend_field_nine', null),
                                                                                                        (null, 'purchaseReturn', 'purchaseReturnExtendFieldTen', '采购退料单扩展字段10', 'purchase_return_extend_field_ten', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'id', '主键', 'id', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnId', '退料单id', 'return_id', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnCode', '退料单号', 'return_code', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'materialCode', '物料编号', 'material_code', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'businessUnitCode', '业务单元编码', 'business_unit_code', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'businessUnitName', '业务单元名称', 'business_unit_name', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'materialName', '物料名称', 'material_name', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'isGift', '是否赠品', 'is_gift', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'planAmount', '计划退料数量', 'plan_amount', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'amount', '退料数量', 'amount', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'unit', '单位', 'unit', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'updateBy', '更新人', 'update_by', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'updateTime', '更新时间', 'update_time', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnTime', '退料时间', 'return_time', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'skuId', '特征参数skuId', 'sku_id', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'purchaseBatchNumber', '采购批次号', 'purchase_batch_number', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldOne', '采购退料单物料扩展字段1', 'return_material_extend_field_one', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldTwo', '采购退料单物料扩展字段2', 'return_material_extend_field_two', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldThree', '采购退料单物料扩展字段3', 'return_material_extend_field_three', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldFour', '采购退料单物料扩展字段4', 'return_material_extend_field_four', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldFive', '采购退料单物料扩展字段5', 'return_material_extend_field_five', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldSix', '采购退料单物料扩展字段6', 'return_material_extend_field_six', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldSeven', '采购退料单物料扩展字段7', 'return_material_extend_field_seven', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldEight', '采购退料单物料扩展字段8', 'return_material_extend_field_eight', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldNine', '采购退料单物料扩展字段9', 'return_material_extend_field_nine', null),
                                                                                                        (null, 'purchaseReturnMaterial', 'returnMaterialExtendFieldTen', '采购退料单物料扩展字段10', 'return_material_extend_field_ten', null);

-- 委外发料字段配置
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark) VALUES
                                                                                                        (null, 'subcontractDelivery', 'deliveryId', '委外发料单id', 'delivery_id', '主键ID'),
                                                                                                        (null, 'subcontractDelivery', 'deliveryNumber', '委外发料单号', 'delivery_number', null),
                                                                                                        (null, 'subcontractDelivery', 'state', '状态', 'state', '状态值：创建1、生效2、完成3、取消5'),
                                                                                                        (null, 'subcontractDelivery', 'orderType', '单据类型', 'order_type', '标准发料单'),
                                                                                                        (null, 'subcontractDelivery', 'businessType', '业务类型', 'business_type', '标准发料'),
                                                                                                        (null, 'subcontractDelivery', 'supplierCode', '供应商代号', 'supplier_code', null),
                                                                                                        (null, 'subcontractDelivery', 'supplierName', '供应商名称', 'supplier_name', null),
                                                                                                        (null, 'subcontractDelivery', 'finishTime', '完成时间', 'finish_time', '状态变更为完成时记录'),
                                                                                                        (null, 'subcontractDelivery', 'createBy', '创建人', 'create_by', null),
                                                                                                        (null, 'subcontractDelivery', 'updateBy', '更新人', 'update_by', null),
                                                                                                        (null, 'subcontractDelivery', 'createTime', '创建时间,发料时间', 'create_time', null),
                                                                                                        (null, 'subcontractDelivery', 'updateTime', '更新时间', 'update_time', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldOne', '单据拓展字段1', 'order_extend_field_one', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldTwo', '单据拓展字段2', 'order_extend_field_two', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldThree', '单据拓展字段3', 'order_extend_field_three', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldFour', '单据拓展字段4', 'order_extend_field_four', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldFive', '单据拓展字段5', 'order_extend_field_five', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldSix', '单据拓展字段6', 'order_extend_field_six', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldSeven', '单据拓展字段7', 'order_extend_field_seven', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldEight', '单据拓展字段8', 'order_extend_field_eight', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldNine', '单据拓展字段9', 'order_extend_field_nine', null),
                                                                                                        (null, 'subcontractDelivery', 'orderExtendFieldTen', '单据拓展字段10', 'order_extend_field_ten', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'deliveryMaterialId', '主键', 'delivery_material_id', '主键,deliveryMaterialId'),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'deliveryId', '委外发料单id', 'delivery_id', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'deliveryNumber', '委外发料单号', 'delivery_number', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'subcontractOrderNumber', '关联委外订单号', 'subcontract_order_number', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'subcontractOrderMaterialRowId', '关联委外订单物料行id', 'subcontract_order_material_row_id', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'procedureId', '工序id', 'procedure_id', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'procedureName', '工序名称，关联委外订单物料行的工序名称', 'procedure_name', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'materialCode', '物料编码', 'material_code', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'skuId', '特征参数skuId', 'sku_id', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'deliveryQuantity', '发货数量', 'delivery_quantity', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'remark', '备注', 'remark', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'receiptTime', '收料时间,这个物料被引用到收料单上的时间', 'receipt_time', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'receiptQuantity', '收料数量', 'receipt_quantity', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'businessUnitCode', '业务单元编码', 'business_unit_code', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'businessUnitName', '业务单元名称', 'business_unit_name', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldOne', '单据物料行拓展字段1', 'order_material_extend_field_one', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldTwo', '单据物料行拓展字段2', 'order_material_extend_field_two', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldThree', '单据物料行拓展字段3', 'order_material_extend_field_three', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldFour', '单据物料行拓展字段4', 'order_material_extend_field_four', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldFive', '单据物料行拓展字段5', 'order_material_extend_field_five', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldSix', '单据物料行拓展字段6', 'order_material_extend_field_six', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldSeven', '单据物料行拓展字段7', 'order_material_extend_field_seven', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldEight', '单据物料行拓展字段8', 'order_material_extend_field_eight', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldNine', '单据物料行拓展字段9', 'order_material_extend_field_nine', null),
                                                                                                        (null, 'subcontractDeliveryMaterial', 'orderMaterialExtendFieldTen', '单据物料行拓展字段10', 'order_material_extend_field_ten', null);

-- 采购收料配置
INSERT INTO `dfs_query_field_config` (id, prefix, field_code, field_name, table_field_code, remark) VALUES
                                                                                                        (null, 'receipt', 'receiptId', '主键', 'receipt_id', '主键'),
                                                                                                        (null, 'receipt', 'receiptCode', '发货单号', 'receipt_code', '发货单号'),
                                                                                                        (null, 'receipt', 'purchaseCode', '采购单号', 'purchase_code', '采购单号'),
                                                                                                        (null, 'receipt', 'receiptName', '发货单名称', 'receipt_name', '发货单名称'),
                                                                                                        (null, 'receipt', 'receiptType', '单据类型:标准收料单 purchase,其他收料单 other', 'receipt_type', '单据类型'),
                                                                                                        (null, 'receipt', 'businessType', '业务类型:standardPurchaseReceipt 标准收料、otherBatchPurchaseReceipt 其他批量收料', 'business_type', '业务类型'),
                                                                                                        (null, 'receipt', 'supplierName', '供应商名称', 'supplier_name', '供应商名称'),
                                                                                                        (null, 'receipt', 'supplierCode', '供应商编码', 'supplier_code', '供应商编码'),
                                                                                                        (null, 'receipt', 'supplierPhone', '供应商联系方式', 'supplier_phone', '供应商联系方式'),
                                                                                                        (null, 'receipt', 'receiptTime', '计划收料时间', 'receipt_time', '计划收料时间'),
                                                                                                        (null, 'receipt', 'state', '收货单状态', 'state', '收货单状态'),
                                                                                                        (null, 'receipt', 'finishTime', '完成时间', 'finish_time', '完成时间'),
                                                                                                        (null, 'receipt', 'updateBy', '更新人', 'update_by', '更新人'),
                                                                                                        (null, 'receipt', 'createBy', '创建人', 'create_by', '创建人'),
                                                                                                        (null, 'receipt', 'updateTime', '更新时间', 'update_time', '更新时间'),
                                                                                                        (null, 'receipt', 'createTime', '创建时间', 'create_time', '创建时间'),
                                                                                                        (null, 'receipt', 'approver', '审核人', 'approver', '审核人'),
                                                                                                        (null, 'receipt', 'actualApprover', '实际审批人', 'actual_approver', '实际审批人'),
                                                                                                        (null, 'receipt', 'approvalStatus', '审批状态', 'approval_status', '审批状态'),
                                                                                                        (null, 'receipt', 'remark', '备注', 'remark', '备注'),
                                                                                                        (null, 'receipt', 'approvalSuggestion', '审批建议', 'approval_suggestion', '审批建议'),
                                                                                                        (null, 'receipt', 'documentIdentification', '单据标识', 'document_identification', '单据标识'),
                                                                                                        (null, 'receipt', 'approvalTime', '审批时间', 'approval_time', '审批时间'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldOne', '采购收料单拓展字段1', 'purchase_receipt_extend_field_one', '采购收料单拓展字段1'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldTwo', '采购收料单拓展字段2', 'purchase_receipt_extend_field_two', '采购收料单拓展字段2'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldThree', '采购收料单拓展字段3', 'purchase_receipt_extend_field_three', '采购收料单拓展字段3'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldFour', '采购收料单拓展字段4', 'purchase_receipt_extend_field_four', '采购收料单拓展字段4'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldFive', '采购收料单拓展字段5', 'purchase_receipt_extend_field_five', '采购收料单拓展字段5'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldSix', '采购收料单拓展字段6', 'purchase_receipt_extend_field_six', '采购收料单拓展字段6'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldSeven', '采购收料单拓展字段7', 'purchase_receipt_extend_field_seven', '采购收料单拓展字段7'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldEight', '采购收料单拓展字段8', 'purchase_receipt_extend_field_eight', '采购收料单拓展字段8'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldNine', '采购收料单拓展字段9', 'purchase_receipt_extend_field_nine', '采购收料单拓展字段9'),
                                                                                                        (null, 'receipt', 'purchaseReceiptExtendFieldTen', '采购收料单拓展字段10', 'purchase_receipt_extend_field_ten', '采购收料单拓展字段10'),
                                                                                                        (null, 'receiptMaterial', 'id', '主键', 'id', '主键'),
                                                                                                        (null, 'receiptMaterial', 'lineNumber', '行号', 'line_number', '行号'),
                                                                                                        (null, 'receiptMaterial', 'receiptId', '收货单id', 'receipt_id', '收货单id'),
                                                                                                        (null, 'receiptMaterial', 'receiptCode', '收货单号', 'receipt_code', '收货单号'),
                                                                                                        (null, 'receiptMaterial', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', '关联上级单据物料行id'),
                                                                                                        (null, 'receiptMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', 'ERP关联物料行id'),
                                                                                                        (null, 'receiptMaterial', 'businessUnitCode', '业务单元编码', 'business_unit_code', '业务单元编码'),
                                                                                                        (null, 'receiptMaterial', 'businessUnitName', '业务单元名称', 'business_unit_name', '业务单元名称'),
                                                                                                        (null, 'receiptMaterial', 'purchaseCode', '采购订单号', 'purchase_code', '采购订单号'),
                                                                                                        (null, 'receiptMaterial', 'purchaseBatchNumber', '采购批次号', 'purchase_batch_number', '采购批次号'),
                                                                                                        (null, 'receiptMaterial', 'materielCode', '物料编号', 'materiel_code', '物料编号'),
                                                                                                        (null, 'receiptMaterial', 'materielName', '物料名称', 'materiel_name', '物料名称'),
                                                                                                        (null, 'receiptMaterial', 'planAmount', '计划数量', 'plan_amount', '计划数量'),
                                                                                                        (null, 'receiptMaterial', 'amount', '实际数量', 'amount', '实际数量'),
                                                                                                        (null, 'receiptMaterial', 'unitPrice', '单价', 'unit_price', '单价'),
                                                                                                        (null, 'receiptMaterial', 'unit', '单位', 'unit', '单位'),
                                                                                                        (null, 'receiptMaterial', 'checkAmount', '全检关联数量', 'check_amount', '全检关联数量'),
                                                                                                        (null, 'receiptMaterial', 'rejectsAmount', '不良品数量', 'rejects_amount', '不良品数量'),
                                                                                                        (null, 'receiptMaterial', 'rejectedQty', '拒收数量', 'rejected_qty', '拒收数量'),
                                                                                                        (null, 'receiptMaterial', 'rejectedReason', '拒收理由', 'rejected_reason', '拒收理由'),
                                                                                                        (null, 'receiptMaterial', 'rejectedQuantity', '判退数量', 'rejected_quantity', '判退数量'),
                                                                                                        (null, 'receiptMaterial', 'concessionAcceptanceQuantity', '让步接收数量', 'concession_acceptance_quantity', '让步接收数量'),
                                                                                                        (null, 'receiptMaterial', 'industrialWasteQuantity', '工废数量', 'industrial_waste_quantity', '工废数量'),
                                                                                                        (null, 'receiptMaterial', 'materialWasteQuantity', '料废数量', 'material_waste_quantity', '料废数量'),
                                                                                                        (null, 'receiptMaterial', 'stackAmount', '剩余堆放数量', 'stack_amount', '剩余堆放数量'),
                                                                                                        (null, 'receiptMaterial', 'warehousingAmount', '入库数量', 'warehousing_amount', '入库数量'),
                                                                                                        (null, 'receiptMaterial', 'returnOutQuantity', '已退料出库数', 'return_out_quantity', '已退料出库数'),
                                                                                                        (null, 'receiptMaterial', 'inspectState', '来料检验状态', 'inspect_state', '来料检验状态'),
                                                                                                        (null, 'receiptMaterial', 'state', '状态', 'state', '状态'),
                                                                                                        (null, 'receiptMaterial', 'updateBy', '更新人', 'update_by', '更新人'),
                                                                                                        (null, 'receiptMaterial', 'updateTime', '更新时间', 'update_time', '更新时间'),
                                                                                                        (null, 'receiptMaterial', 'receiveState', '收货状态', 'receive_state', '收货状态'),
                                                                                                        (null, 'receiptMaterial', 'warehouseState', '入库状态', 'warehouse_state', '入库状态'),
                                                                                                        (null, 'receiptMaterial', 'receivingTime', '收料时间', 'receiving_time', '收料时间'),
                                                                                                        (null, 'receiptMaterial', 'skuId', '特征参数skuId', 'sku_id', '特征参数skuId'),
                                                                                                        (null, 'receiptMaterial', 'isGift', '是否赠品', 'is_gift', '是否赠品'),
                                                                                                        (null, 'receiptMaterial', 'giftQuantity', '赠品数量', 'gift_quantity', '赠品数量'),
                                                                                                        (null, 'receiptMaterial', 'returnQuantity', '退料数量', 'return_quantity', '退料数量'),
                                                                                                        (null, 'receiptMaterial', 'qualifiedQuantity', '检验合格数量', 'qualified_quantity', '检验合格数量'),
                                                                                                        (null, 'receiptMaterial', 'unqualifiedQuantity', '检验不合格数量', 'unqualified_quantity', '检验不合格数量'),
                                                                                                        (null, 'receiptMaterial', 'materialRemark', '备注', 'material_remark', '备注'),
                                                                                                        (null, 'receiptMaterial', 'customerCode', '客户编码', 'customer_code', '客户编码'),
                                                                                                        (null, 'receiptMaterial', 'customerName', '客户名称', 'customer_name', '客户名称'),
                                                                                                        (null, 'receiptMaterial', 'warehouseCode', '来源物料默认仓库', 'warehouse_code', '来源物料默认仓库'),
                                                                                                        (null, 'receiptMaterial', 'warehouseName', '来源物料默认仓库', 'warehouse_name', '来源物料默认仓库'),
                                                                                                        (null, 'receiptMaterial', 'relateInspectOrder', '关联检验单号', 'relate_inspect_order', '关联检验单号'),
                                                                                                        (null, 'receiptMaterial', 'addGift', '是否新增赠品', 'add_gift', '是否新增赠品'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldOne', '采购收料单物料扩展字段1', 'receipt_material_extend_field_one', '采购收料单物料扩展字段1'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldTwo', '采购收料单物料扩展字段2', 'receipt_material_extend_field_two', '采购收料单物料扩展字段2'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldThree', '采购收料单物料扩展字段3', 'receipt_material_extend_field_three', '采购收料单物料扩展字段3'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldFour', '采购收料单物料扩展字段4', 'receipt_material_extend_field_four', '采购收料单物料扩展字段4'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldFive', '采购收料单物料扩展字段5', 'receipt_material_extend_field_five', '采购收料单物料扩展字段5'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldSix', '采购收料单物料扩展字段6', 'receipt_material_extend_field_six', '采购收料单物料扩展字段6'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldSeven', '采购收料单物料扩展字段7', 'receipt_material_extend_field_seven', '采购收料单物料扩展字段7'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldEight', '采购收料单物料扩展字段8', 'receipt_material_extend_field_eight', '采购收料单物料扩展字段8'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldNine', '采购收料单物料扩展字段9', 'receipt_material_extend_field_nine', '采购收料单物料扩展字段9'),
                                                                                                        (null, 'receiptMaterial', 'receiptMaterialExtendFieldTen', '采购收料单物料扩展字段10', 'receipt_material_extend_field_ten', '采购收料单物料扩展字段10');

-- 委外订单用料清单字段配置
INSERT INTO dfs_query_field_config (id, prefix, field_code, field_name, table_field_code, remark) VALUES
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'id', '主键', 'id', '主键'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'materialOrderNumber', '用料清单号', 'material_order_number', '用料清单号'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'relateType', '关联单据类型', 'relate_type', '关联单据类型'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'relateNumber', '关联单据', 'relate_number', '关联单据'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'state', '状态', 'state', '状态'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderType', '单据类型：标准委外用料清单 subcontractMaterialList', 'order_type', '单据类型'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'businessType', '业务类型：标准委外用料清单 standardSubcontractMaterialList', 'business_type', '业务类型'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'approvalStatus', '审批状态', 'approval_status', '审批状态'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'approver', '审批人', 'approver', '审批人'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'actualApprover', '实际审批人', 'actual_approver', '实际审批人'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'approvalSuggestion', '审批意见', 'approval_suggestion', '审批意见'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'approvalTime', '审批时间', 'approval_time', '审批时间'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'businessUnitCode', '业务单元编码', 'business_unit_code', '业务单元编码'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'businessUnitName', '业务单元名称', 'business_unit_name', '业务单元名称'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'createBy', '创建人', 'create_by', '创建人'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'updateBy', '修改人', 'update_by', '修改人'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'createTime', '创建时间', 'create_time', '创建时间'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'updateTime', '修改时间', 'update_time', '修改时间'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldOne', '单据拓展字段1', 'order_extend_field_one', '单据拓展字段1'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldTwo', '单据拓展字段2', 'order_extend_field_two', '单据拓展字段2'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldThree', '单据拓展字段3', 'order_extend_field_three', '单据拓展字段3'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldFour', '单据拓展字段4', 'order_extend_field_four', '单据拓展字段4'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldFive', '单据拓展字段5', 'order_extend_field_five', '单据拓展字段5'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldSix', '单据拓展字段6', 'order_extend_field_six', '单据拓展字段6'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldSeven', '单据拓展字段7', 'order_extend_field_seven', '单据拓展字段7'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldEight', '单据拓展字段8', 'order_extend_field_eight', '单据拓展字段8'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldNine', '单据拓展字段9', 'order_extend_field_nine', '单据拓展字段9'),
                                                                                                      (null, 'subcontractOrderMaterialEntity', 'orderExtendFieldTen', '单据拓展字段10', 'order_extend_field_ten', '单据拓展字段10'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'id', '主键', 'id', '主键'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'materialOrderId', '委外订单用料清单id', 'material_order_id', '委外订单用料清单id'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'materialOrderNumber', '用料清单号', 'material_order_number', '用料清单号'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', '关联上级单据物料行id'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'lineNumber', '行号', 'line_number', '行号'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'materialCode', '物料编码', 'material_code', '物料编码'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'skuId', '特征参数skuId', 'sku_id', '特征参数skuId'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'supplierCode', '供应商编码', 'supplier_code', '供应商编码'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'supplierName', '供应商名称', 'supplier_name', '供应商名称'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'planQuantity', '计划数量', 'plan_quantity', '计划数量'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'actualQuantity', '实际数量', 'actual_quantity', '实际数量'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldOne', '单据-行拓展字段1', 'order_line_extend_field_one', '单据-行拓展字段1'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldTwo', '单据-行拓展字段2', 'order_line_extend_field_two', '单据-行拓展字段2'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldThree', '单据-行拓展字段3', 'order_line_extend_field_three', '单据-行拓展字段3'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldFour', '单据-行拓展字段4', 'order_line_extend_field_four', '单据-行拓展字段4'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldFive', '单据-行拓展字段5', 'order_line_extend_field_five', '单据-行拓展字段5'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldSix', '单据-行拓展字段6', 'order_line_extend_field_six', '单据-行拓展字段6'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldSeven', '单据-行拓展字段7', 'order_line_extend_field_seven', '单据-行拓展字段7'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldEight', '单据-行拓展字段8', 'order_line_extend_field_eight', '单据-行拓展字段8'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldNine', '单据-行拓展字段9', 'order_line_extend_field_nine', '单据-行拓展字段9'),
                                                                                                      (null, 'subcontractOrderMaterialLine', 'orderLineExtendFieldTen', '单据-行拓展字段10', 'order_line_extend_field_ten', '单据-行拓展字段10');









