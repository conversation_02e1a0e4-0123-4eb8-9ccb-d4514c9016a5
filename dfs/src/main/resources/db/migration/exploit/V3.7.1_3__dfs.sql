-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================

-- 历史指标组数据需要兼容脚本
-- dfs_metrics_device_oee
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'id', '记录id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'device_id', '设备id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'theoretical_speed', '理论生产速度', 'double', 11, 3, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'amount', '生产数量', 'double', 11, 3, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'unqualified', '不合格量', 'double', 11, 3, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'run_time', '开动时长（小时）', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'load_time', '负荷时长（小时）', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'yield', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'performance', '性能开动率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'time_efficiency', '时间开动率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'oee', 'OEE', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'record_date', '日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'create_time', '创建时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_oee', '设备每天OEE记录表', 'update_time', '修改时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_device_state_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'device_id', '设备id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'running', '工位运行时长（分钟）', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'pause', '产线暂停时长（分钟）', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'stop', '产线停止时长（分钟）', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'record_date', '日期', 'date', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'fault_quantity', 'fault_quantity', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_device_state_daily', '设备每日状态持续时长记录表', 'time', 'last update time', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_end_material
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'sale_quantity', '销售数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'produce_quantity', '生产数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'unqualified_quantity', '不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'per_working_hours', '单件工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material', '产成品 整体统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_end_material_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'produce_quantity', '生产数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'unqualified_quantity', '不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'direct_access_quantity', '直通数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'working_hours', '当日工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'per_working_hours', '单件工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'direct_access_rate', '直通率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_end_material_daily', '产成品 每日统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_exception_classify_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'type', '告警/事件： alarm/event', 'varchar', 32, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'classify_code', '分类编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'classify_name', '分类名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'count', '异常发生次数 (当天存在的异常)', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'stop_hour', '停线时长(unit: h)', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'recovery_hour', '恢复时长(unit: h)', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_exception_classify_daily', '异常管理：异常类型每日', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_incoming_inspection_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'supplier_code', '供应商代号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'coming_quantity', '来料数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'sampling_quantity', '抽样数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'unqualified_quantity', '不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'qualified_quantity', '合格数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily', '来料检验 每日统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_line_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'line_name', '主键id', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'need_order_quantity', '当日需要生产工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'plan_order_quantity', '当日计划生产工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'actual_order_quantity', '当日实际生产工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'finished_order_quantity', '当日已完成工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'finished_order_rate', '当日工单达成率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'plan_quantity', '当日计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'produce_quantity', '当日产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'input_quantity', '当日投入数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'unqualified_quantity', '当日不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'direct_access_quantity', '当日直通数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'working_people_quantity', '当日在岗人数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'actual_working_hour', '实际投入工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'theory_working_hour', '理论产出工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'product_working_hours', '生产时长：工单实际工时sum', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'product_efficiency_rate', '生产效率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'actual_capacity', '实际产能', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'finish_rate', '达成率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'qualified_rate', '合格率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'uni_code', '主键id', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_daily', '制造单元每日生产进度表', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_line_material_defect_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'defect_type_code', '不良类型编号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'defect_type_name', '不良类型名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'defect_id', '不良项id', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'defect_name', '不良项名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_material_defect_daily', '每日：产品不良项统计 (物料，不良类型，不良项)', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_line_monthly
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'line_name', '产线名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'order_quantity', '当月生产工单数(有产量)', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'finished_order_quantity', '当月已完成工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'plan_quantity', '当月计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'produce_quantity', '当月产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'input_quantity', '当月投入数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'unqualified_quantity', '当月不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'direct_access_quantity', '当月直通数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'loss_quantity', '当月损耗数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'actual_working_hour', '实际投入工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'theory_working_hour', '理论产出工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'product_efficiency_rate', '生产效率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'actual_capacity', '实际产能', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'qualified_rate', '合格率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'record_month', '记录月份', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_line_monthly', '产线指标 - 每月', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_maintain_line_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'uni_code', '唯一标识', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'line_name', '产线名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'fid', '工位id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'fname', '工位名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'maintain_quantity', '维修记录数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'defect_item_quantity', '维修不良项数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'finish_quantity', '维修合格数: *没有维修结果的均算合格', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'scrap_quantity', '维修报废数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'success_rate', '维修成功率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_line_daily', '维修指标 - 产线维度', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_maintain_team_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'team_id', '班组id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'team_name', '班组名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'maintain_quantity', '维修记录数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'defect_item_quantity', '维修不良项数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'finish_quantity', '维修合格数: *没有维修结果的均算合格', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'scrap_quantity', '维修报废数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'success_rate', '维修成功率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_maintain_team_daily', '维修指标 - 班组维度', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_material_trace_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'produce_material_code', '产成物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'uni_code', '唯一标识', 'varchar', 512, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'input_quantity', '投入数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'theory_quantity', '理论数量： 产成品bom推出来的', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'loss_quantity', '损耗数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'produce_quantity', '产成数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_material_trace_daily', '生产指标 物料追踪', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_procedure_hourly
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'procedure_id', '工序id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'procedure_name', '工序名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'record_time', '记录时间，每整点', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'input_quantity', '投入数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'produce_quantity', '产出数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'unqualified_quantity', '不良数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_procedure_hourly', '工序小时统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_product_order
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'product_order_id', '销售订单id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'product_order_number', '销售订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'end_plan_quantity', '成品计划数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'end_produce_quantity', '成品生产数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'end_unqualified_quantity', '成品不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'order_plan_quantity', '订单计划总数:  所有工单物料计划生产数量之和', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'order_produce_quantity', '订单已生产总数:  所有工单物料已生产数量之和', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'all_order_count', '生产工单计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'invested_order_count', '投产工单计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'finish_order_count', '已完成工单计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'delay_order_count', '延期工单计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'finish_rate', '达成率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'procedure_working_hour', '工序时长', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order', '生产订单 整体统计', 'procedure_circulation_hour', '工序流转时长', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_product_order_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'product_order_id', '销售订单id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'product_order_number', '销售订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'end_plan_quantity', '成品计划数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'end_unqualified_quantity', '成品不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'end_produce_quantity', '成品产出数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'end_qualified_rate', '成品合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'plan_quantity', '计划数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'unqualified_quantity', '不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'produce_quantity', '产出数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'end_unqualified_record_quantity', '成品不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_daily', '生产订单 每日统计', 'end_unqualified_record_item_quantity', '成品不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_product_order_monthly
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'product_order_id', '销售订单id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'product_order_number', '销售订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'record_month', '月份: yyyy-MM', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'end_plan_quantity', '成品计划数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'end_produce_quantity', '成品产出数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'end_unqualified_record_quantity', '成品不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_monthly', '生产订单 每月统计', 'end_unqualified_record_item_quantity', '成品不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_product_order_procedure
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'product_order_id', '生产订单id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'product_order_number', '生产订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'sale_order_number', '销售订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'line_name', '产线名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'procedure_id', '工序id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'procedure_name', '工序名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'plan_quantity', '计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'produce_quantity', '产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'unqualified_quantity', 'unqualified', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'direct_access_quantity', 'direct_access', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_procedure', '生产订单：产线工序的生产进度', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_product_order_summary_monthly
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly', '生产订单汇总 每月统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly', '生产订单汇总 每月统计', 'record_month', '月份: yyyy-MM', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly', '生产订单汇总 每月统计', 'new_count', '本月新增订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly', '生产订单汇总 每月统计', 'finish_count', '本月已完成订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly', '生产订单汇总 每月统计', 'finish_delay_count', '本月延期完成订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly', '生产订单汇总 每月统计', 'no_finish_delay_count', '本月延期未完成订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly', '生产订单汇总 每月统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_quality_defect_distribution_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_defect_distribution_daily', '质量：不良分布每日。eg: 耳机出现了2项不良(可相同), 则记录一条 [单品不良项数 = 2,  对应不良项数的计数=1]的记录', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_defect_distribution_daily', '质量：不良分布每日。eg: 耳机出现了2项不良(可相同), 则记录一条 [单品不良项数 = 2,  对应不良项数的计数=1]的记录', 'unqualified_item_count', '单品不良项数: 1,2,3,...,N', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_defect_distribution_daily', '质量：不良分布每日。eg: 耳机出现了2项不良(可相同), 则记录一条 [单品不良项数 = 2,  对应不良项数的计数=1]的记录', 'unqualified_item_count_quantity', '对应不良项数的不良品件数（出现次数）', 'int', 11, 0, 0, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_defect_distribution_daily', '质量：不良分布每日。eg: 耳机出现了2项不良(可相同), 则记录一条 [单品不良项数 = 2,  对应不良项数的计数=1]的记录', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_defect_distribution_daily', '质量：不良分布每日。eg: 耳机出现了2项不良(可相同), 则记录一条 [单品不良项数 = 2,  对应不良项数的计数=1]的记录', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_quality_line_material_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'line_name', '产线名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'unqualified_quantity', '不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_daily', '制造单元每日不良表（产线+物料）', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
-- dfs_metrics_quality_line_material_defect_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'line_name', '产线名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'defect_type_code', '不良类型编号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'defect_id', '不良项id', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'defect_name', '不良项名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'unqualified_quantity', '不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily', '制造单元每日不良表（产线+物料+不良项）', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
-- dfs_metrics_quality_product_order_defect_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'product_order_number', '生产订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'defect_type_code', '不良类型编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'unqualified_item_quantity', '不良项记录数：不去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'unqualified_product_quantity', '不良品记录数：流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_quality_product_order_defect_daily', '质量：生产订单不良每日', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_sale_order
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'sale_order_id', '销售订单id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'sale_order_number', '销售订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'customer_code', '客户代号（客户编号）', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'customer_name', '客户名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'salesman_code', '销售员编号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'salesman_name', '销售员名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'sales_quantity', '销售数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'plan_quantity', '计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'produce_quantity', '产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'unqualified_quantity', '不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'purchase_quantity', '采购数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'applied_shipment_quantity', '出货申请数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'applied_quantity', '发货数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'delay_quantity', '延期数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'out_stock_quantity', '出库数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'finish_rate', '达成率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order', '销售订单 整体统计', 'sale_order_material_id', '销售订单物料行id', 'int', 11, 0, NULL, NULL);
-- dfs_metrics_sale_order_material
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'sale_order_material_id', '销售订单物料行id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'sale_order_id', '销售订单id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'sale_order_number', '销售订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'sales_quantity', '销售数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'plan_quantity', '计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'produce_quantity', '产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'unqualified_quantity', '不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'applied_shipment_quantity', '出货申请数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'applied_quantity', '发货数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'delay_quantity', '延期数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'out_stock_quantity', '出库数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'finish_rate', '达成率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material', '销售订单-物料 整体统计', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_sale_order_material_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'sale_order_material_id', '销售订单物料行id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'sale_order_id', '销售订单id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'sale_order_number', '销售订单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'material_code', '物料编码', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'material_name', '物料名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'plan_quantity', '计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'produce_quantity', '产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'unqualified_quantity', '不合格数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'qualified_rate', '合格率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'time`', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'customer_code', '客户编号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'customer_name', '客户名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'salesman_code', '销售员编号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'salesman_name', '销售员名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_material_daily', '销售订单-物料 每日统计', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_sale_order_summary_monthly
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'record_month', '月份: yyyy-MM', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'all_order_count', '总订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_order_count', '已交付订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_timely_order_count', '及时交付订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_delay_order_count', '延期交付订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'no_delivered_delay_order_count', '延期未交付订单数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'all_order_material_quantity', '销售产品总数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_order_material_quantity', '已交付产品总数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_timely_order_material_quantity', '及时交付产品总数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'no_delivered_order_material_quantity', '待交付产品总数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'no_delivered_delay_order_material_quantity', '超期未交付产品总数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_order_rate', '订单交付率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_timely_order_rate', '订单及时交付率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'finish_order_material_rate', '销售订单完成率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_timely_order_material_rate', '产品及时交付率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'delivered_timely_rate', '交货及时率', 'double', 11, 4, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly', '销售订单 每月汇总', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_staff_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_staff_daily', '员工每日统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_staff_daily', '员工每日统计', 'username', '人员名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_staff_daily', '员工每日统计', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_staff_daily', '员工每日统计', 'input_working_hour', '投入工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_staff_daily', '员工每日统计', 'valid_working_hour', '有效工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_staff_daily', '员工每日统计', 'produce_quantity', '生产数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_staff_daily', '员工每日统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_team_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'team_id', '班组id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'team_name', '班组名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'need_order_quantity', '当日需要生产工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'plan_order_quantity', '当日计划生产工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'actual_order_quantity', '当日实际生产工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'finished_order_quantity', '当日已完成工单数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'finished_order_rate', '当日工单达成率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'plan_quantity', '当日计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'produce_quantity', '当日产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'input_quantity', '当日投入数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'unqualified_quantity', '当日不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'direct_access_quantity', '当日直通数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'working_people_quantity', '当日在岗人数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'actual_working_hour', '实际投入工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'finish_rate', '达成率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'qualified_rate', '合格率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_team_daily', '班组每日生产进度表', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_top_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'record_date', '记录日期', 'date', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'rank', '排行', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'defect_id', '不良项id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'defect_name', '不良项名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'defect_count', '不良项计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'maintain_id', '维修项id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'maintain_name', '维修项名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'maintain_count', '维修项计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'defect_fac_model_id', '不良上报工位类型id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'defect_fac_model_name', '不良上报工位类型名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'defect_fac_model_count', '不良上报工位类型计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'maintain_fac_model_id', '维修上报工位类型id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'maintain_fac_model_name', '维修上报工位类型名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_daily', 'Top每日排行', 'maintain_fac_model_count', '维修上报工位类型计数', 'int', 11, 0, NULL, NULL);
-- dfs_metrics_top_work_order
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'work_order_number', '工单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'rank', '排行', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'uni_code', '唯一标识', 'varchar', 512, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'defect_id', '不良项id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'defect_name', '不良项名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'defect_count', '不良项计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'maintain_id', '维修项id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'maintain_name', '维修项名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'maintain_count', '维修项计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'defect_fac_model_id', '不良上报工位类型id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'defect_fac_model_name', '不良上报工位类型名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'defect_fac_model_count', '不良上报工位类型计数', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'maintain_fac_model_id', '维修上报工位类型id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'maintain_fac_model_name', '维修上报工位类型名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_top_work_order', 'Top工单排行', 'maintain_fac_model_count', '维修上报工位类型计数', 'int', 11, 0, NULL, NULL);
-- dfs_metrics_valuation_cal
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'valuation_config_id', '工资配置id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'username', '用户名', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'work_order_number', '工单号', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'material_code', '物料编码', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'material_name', '物料名称', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'craft_procedure_id', '工序id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'cal_count', '算出的数量', 'decimal', 11, 0, 0, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'cal_duration_h', '算出的时长: h', 'decimal', 11, 0, 0, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'audit_count', '审核的数量', 'decimal', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'audit_duration_h', '审核的时长: h', 'decimal', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'valuation', '审核后计算出来的', 'decimal', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'auditor', '审核人', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'audit_time', '审核时间', 'datetime', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'state', '状态: 0创建 1已审核', 'int', 11, 0, 0, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'record_date', '记录日期: yyyy-MM-dd', 'datetime', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'work_center_id', '工作中心id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'line_id', '产线id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'device_id', '设备id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'team_id', '班组id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'create_by', '创建人', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'update_by', '更新人', 'varchar', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'create_time', '创建时间', 'datetime', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_valuation_cal', '工资计算', 'update_time', '更新时间', 'datetime', 11, 0, NULL, NULL);
-- dfs_metrics_work_order
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'work_order_number', '工单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'plan_quantity', '计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'produce_quantity', '产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'inspection_quantity', '检测数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'unqualified_quantity', '不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'inbound_quantity', '入库数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'actual_capacity', '实际产能', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'actual_working_hour', '实际投入工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'theory_working_hour', '理论产出工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'single_theory_hour', '单件理论工时', 'double', 15, 6, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'stop_line_hour', '停线时长', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'finish_rate', '达成率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'qualified_rate', '合格率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'product_efficiency_rate', '生产效率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'achievements', '绩效', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'repair_quantity', '返修数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'repair_qualified_quantity', '返修良品数（维修判定: 不报废）', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'repair_quantity_rate', '返修率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order', '生产整体进度', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_work_order_10min
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'work_order_number', '工单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'record_time', '记录时间(取整10分) ', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'produce_quantity', '产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'unqualified_quantity', '不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'unqualified_record_quantity', '不良品记录数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'unqualified_record_item_quantity', '不良项记录数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'input_quantity', '投入数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'direct_access_quantity', '直通数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'working_people_quantity', '打卡人数(上机点)', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_10min', '生产工单10分钟监控', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
-- dfs_metrics_work_order_daily
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'work_order_number', '工单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'plan_quantity', '当日计划数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'produce_quantity', '当日产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'inspection_quantity', '当日检测数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'unqualified_quantity', '当日不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'inbound_quantity', '当日入库数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'direct_access_quantity', '当日直通数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'start_work_time', '当日开工时间(小程序录入)', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'working_people_quantity', '当日在岗人数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'actual_working_hour', '实际投入工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'theory_working_hour', '理论产出工时', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'qualified_rate', '当日合格率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'product_efficiency_rate', '生产效率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'achievements', '绩效', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'repair_quantity', '当日返修数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'repair_qualified_quantity', '当日返修良品数（维修判定: 不报废）', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'repair_quantity_rate', '当日返修率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'record_date', '记录日期', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_daily', '生产工单每日进度', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_work_order_hourly
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'work_order_number', '工单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'produce_quantity', '产出数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'inspection_quantity', '检测数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'unqualified_quantity', '不良数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'input_quantity', '投入数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'making_quantity', '在制数量', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'qualified_rate', '合格率', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'record_time', '记录时间(取整点) 统计包含整个小时内的', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'unqualified_record_quantity', '不良品记录数量: 流水码去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'unqualified_record_item_quantity', '不良项记录数量: 流水码不去重', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_hourly', '生产工单小时监控', 'direct_access_quantity', '直通数', 'double', 11, 2, NULL, NULL);
-- dfs_metrics_work_order_procedure
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'id', '主键id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'uni_code', '唯一标识', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'work_order_number', '工单号', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'craft_procedure_id', '工艺工序id', 'int', 11, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'craft_procedure_name', '工艺工序名称', 'varchar', 255, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'crossing_quantity', '过站数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'unqualified_quantity', '不良数', 'double', 11, 2, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'time', '最新统计时间', 'datetime', 0, 0, NULL, NULL);
INSERT INTO `dfs_table_config`(`id`, `table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`) VALUES (NULL, 'dfs_metrics', 'dfs_metrics_work_order_procedure', '工单工序统计', 'seq', '近似排序', 'int', 11, 0, NULL, NULL);

-- 插入到关联表里
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('saleOrderMaterialDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'saleOrderTarget'), 'dfs_metrics', 'dfs_metrics_sale_order_material_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('saleOrderMaterial', (SELECT id FROM `dfs_model` WHERE `code` = 'saleOrderTarget'), 'dfs_metrics', 'dfs_metrics_sale_order_material');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('saleOrderSummaryMonthly', (SELECT id FROM `dfs_model` WHERE `code` = 'saleOrderTarget'), 'dfs_metrics', 'dfs_metrics_sale_order_summary_monthly');

INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('workOrderStatistics', (SELECT id FROM `dfs_model` WHERE `code` = 'workOrderTarget'), 'dfs_metrics', 'dfs_metrics_work_order');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('workOrderDailyProgress', (SELECT id FROM `dfs_model` WHERE `code` = 'workOrderTarget'), 'dfs_metrics', 'dfs_metrics_work_order_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('workOrderHourlyMonitor', (SELECT id FROM `dfs_model` WHERE `code` = 'workOrderTarget'), 'dfs_metrics', 'dfs_metrics_work_order_hourly');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('workOrder10min', (SELECT id FROM `dfs_model` WHERE `code` = 'workOrderTarget'), 'dfs_metrics', 'dfs_metrics_work_order_10min');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('workOrderProcedure', (SELECT id FROM `dfs_model` WHERE `code` = 'workOrderTarget'), 'dfs_metrics', 'dfs_metrics_work_order_procedure');

INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('productOrderProcedureStatistics', (SELECT id FROM `dfs_model` WHERE `code` = 'productOrderTarget'), 'dfs_metrics', 'dfs_metrics_product_order_procedure');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('productOrder', (SELECT id FROM `dfs_model` WHERE `code` = 'productOrderTarget'), 'dfs_metrics', 'dfs_metrics_product_order');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('productOrderDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'productOrderTarget'), 'dfs_metrics', 'dfs_metrics_product_order_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('productOrderMonthly', (SELECT id FROM `dfs_model` WHERE `code` = 'productOrderTarget'), 'dfs_metrics', 'dfs_metrics_product_order_monthly');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('productOrderSummaryMonthly', (SELECT id FROM `dfs_model` WHERE `code` = 'productOrderTarget'), 'dfs_metrics', 'dfs_metrics_product_order_summary_monthly');

INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('qualityLineMaterialDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'qualityTarget'), 'dfs_metrics', 'dfs_metrics_quality_line_material_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('qualityLineMaterialDefectDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'qualityTarget'), 'dfs_metrics', 'dfs_metrics_quality_line_material_defect_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('incomingInspectionDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'qualityTarget'), 'dfs_metrics', 'dfs_metrics_incoming_inspection_daily');

INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('endMaterialDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'materialTarget'), 'dfs_metrics', 'dfs_metrics_end_material_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('endMaterial', (SELECT id FROM `dfs_model` WHERE `code` = 'materialTarget'), 'dfs_metrics', 'dfs_metrics_end_material');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('materialTraceDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'materialTarget'), 'dfs_metrics', 'dfs_metrics_material_trace_daily');

INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('topDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'topTarget'), 'dfs_metrics', 'dfs_metrics_top_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('topWorkOrder', (SELECT id FROM `dfs_model` WHERE `code` = 'topTarget'), 'dfs_metrics', 'dfs_metrics_top_work_order');

INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('maintainLineDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'maintainTarget'), 'dfs_metrics', 'dfs_metrics_maintain_line_daily');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('maintainTeamDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'maintainTarget'), 'dfs_metrics', 'dfs_metrics_maintain_team_daily');

-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.7.1.1=======================================================

