-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ****** 存储过程1：删除字段的 ******
DROP PROCEDURE if EXISTS `dfs_metrics`.`drop_column_if_exists`;
DELIMITER $$

CREATE PROCEDURE `dfs_metrics`.`drop_column_if_exists`(
    IN in_table_name VARCHAR(255),
    IN in_column_name VARCHAR(255)
)
BEGIN
    DECLARE cnt INT DEFAULT 0;
    DECLARE sql_text TEXT;

    -- 查询信息模式，检查列是否存在
SELECT COUNT(*)
INTO cnt
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = in_table_name
  AND COLUMN_NAME = in_column_name;

-- 如果存在，动态拼SQL执行
IF cnt > 0 THEN
        SET @sql_text = CONCAT('ALTER TABLE `', in_table_name, '` DROP COLUMN `', in_column_name, '`;');
PREPARE stmt FROM @sql_text;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
END IF;
END$$

DELIMITER ;


-- ****** 存储过程2：刷入指标组 ******
DROP PROCEDURE if EXISTS `insert_target_data`;
DELIMITER $$

CREATE PROCEDURE insert_target_data (
    IN in_modelCode VARCHAR(100),
    IN in_modelName VARCHAR(100),
    IN in_targetName VARCHAR(100),
    IN in_targetCnname VARCHAR(100),
    IN in_tableName VARCHAR(100),
    IN in_frequency INT
)
BEGIN
    DECLARE v_modelId BIGINT;

    -- 1. 插入 dfs_model（如不存在）
    IF NOT EXISTS (SELECT 1 FROM dfs_model WHERE code = in_modelCode and type = 'baseTargetGroupObject') THEN
        INSERT INTO dfs_model (`type`, `name`, `code`, `seq`, `pid`)
        VALUES ('baseTargetGroupObject', in_modelName, in_modelCode, 15, -1);
END IF;

    -- 2. 获取 modelId
SELECT id INTO v_modelId FROM dfs_model WHERE code = in_modelCode and type = 'baseTargetGroupObject';

-- 3. 插入 dfs_target_model（如不存在）
IF NOT EXISTS (
        SELECT 1 FROM dfs_target_model
        WHERE target_name = in_targetName AND model_id = v_modelId
    ) THEN
        INSERT INTO dfs_target_model (
            target_name, target_cnname, model_id, method_name, method_cname,
            frequency, frequency_unit, source, data_source_type, enable
        ) VALUES (
            in_targetName, in_targetCnname, v_modelId, in_targetName, '系统统计',
            in_frequency, 'm', '自动', 'inner', 1
        );
END IF;

    -- 4. 插入 dfs_target_model_table_relate（如不存在）
    IF NOT EXISTS (
        SELECT 1 FROM dfs_target_model_table_relate
        WHERE target_name = in_targetName AND model_id = v_modelId
    ) THEN
        INSERT INTO dfs_target_model_table_relate (
            target_name, model_id, table_schema, table_name
        ) VALUES (
            in_targetName, v_modelId, 'dfs_metrics', in_tableName
        );
END IF;

    -- 5. 插入 dfs_target_dict（如不存在）
    IF NOT EXISTS (
        SELECT 1 FROM dfs_target_dict
        WHERE target_name = in_targetName AND model_code = in_modelCode
    ) THEN
        INSERT INTO dfs_target_dict (
            target_name, target_cnname, model_type, model_code,
            is_inner_target, default_frequency, default_frequency_unit
        ) VALUES (
            in_targetName, in_targetCnname, 'baseTargetGroupObject', in_modelCode,
            1, in_frequency, 'm'
        );
END IF;

    -- 6. 插入 dfs_target_method（如不存在）
    IF NOT EXISTS (
        SELECT 1 FROM dfs_target_method
        WHERE target_name = in_targetName
    ) THEN
        INSERT INTO dfs_target_method (
            target_name, model_type, method_cname, method_name, has_frequency
        ) VALUES (
            in_targetName, 'baseTargetGroupObject', '系统统计', in_targetName, 1
        );
END IF;

END$$
DELIMITER ;



-- 员工工单-每日统计
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_staff_work_order_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) NOT NULL COMMENT '唯一标识',
    `record_time` datetime NOT NULL COMMENT '记录时间',
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `username` varchar(255) NOT NULL COMMENT '人员名称',
    `nickname` varchar(255) DEFAULT NULL COMMENT '人员昵称',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
    `work_center_id` int(11) DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) DEFAULT NULL COMMENT '工作中心名称',
    `gid` int(11) DEFAULT NULL COMMENT '车间id',
    `gname` varchar(255) DEFAULT NULL COMMENT '车间名称',
    `report_hour` double(11,2) DEFAULT NULL COMMENT '报工工时',
    `attendance_hour` double(11,2) DEFAULT NULL COMMENT '打卡工时',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '生产数量',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    UNIQUE KEY `idx_union` (`username`,`work_order_number`,`record_time`),
    KEY `idx_work_center` (`work_center_id`),
    KEY `idx_work_order` (`work_order_number`),
    KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工-工单每日统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_device_consumption_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `uni_code` varchar(512) NOT NULL COMMENT '唯一标识 [device_id + record_time]',
    `device_id` int(11) NOT NULL COMMENT '设备id',
    `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
    `device_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
    `record_time` date NOT NULL COMMENT '记录日期',
    `energy_consumption` double(16,4) DEFAULT NULL COMMENT '用电能耗',
    `water_consumption` double(16,4) DEFAULT NULL COMMENT '用水能耗',
    `gas_consumption` double(16,4) DEFAULT NULL COMMENT '用气能耗',
    `carbon_consumption` double(16,4) DEFAULT NULL COMMENT '碳排能耗',
    `gid` int(11) DEFAULT NULL COMMENT '车间id[附属信息]',
    `gname` varchar(255) DEFAULT NULL COMMENT '车间名称[附属信息]',
    `work_center_id` int(11) DEFAULT NULL COMMENT '工作中心id[附属信息]',
    `work_center_name` varchar(255) DEFAULT NULL COMMENT '工作中心名称[附属信息]',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`device_id`,`record_time`) USING BTREE,
    KEY `idx_uni_code` (`uni_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备能耗-每日统计 [该统计依赖于设备基础能耗指标]';

-- 指标: 异常
DROP TABLE IF EXISTS `dfs_metrics`.`dfs_metrics_exception_classify_daily`;
DELETE FROM dfs_table_config WHERE table_schema = 'dfs_metrics' AND table_name = 'dfs_metrics_exception_classify_daily';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_exception_classify_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(512) NOT NULL COMMENT '唯一标识 [type + definition_code + record_time]',
    `type` varchar(32) NOT NULL COMMENT '类型 [告警-alarm, 事件-event]',
    `classify_code` varchar(255) NOT NULL COMMENT '分类编码',
    `classify_name` varchar(255) DEFAULT NULL COMMENT '分类名称',
    `definition_code` varchar(255) NOT NULL COMMENT '定义编码',
    `definition_name` varchar(255) DEFAULT NULL COMMENT '定义名称',
    `count` int(11) NOT NULL COMMENT '发生次数 ',
    `influence_hour` double(11,2) DEFAULT NULL COMMENT '影响时长[unit: h]',
    `stop_hour` double(11,2) DEFAULT NULL COMMENT '停机时长[unit: h]',
    `record_time` datetime NOT NULL COMMENT '记录日期',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uni` (`type`,`definition_code`,`record_time`) USING BTREE,
    KEY `idx_uni_code` (`uni_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常管理-每日统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_exception_classify_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(512) NOT NULL COMMENT '唯一标识 [type + definition_code + record_time]',
    `type` varchar(32) NOT NULL COMMENT '类型 [告警-alarm, 事件-event]',
    `classify_code` varchar(255) NOT NULL COMMENT '分类编码',
    `classify_name` varchar(255) DEFAULT NULL COMMENT '分类名称',
    `definition_code` varchar(255) NOT NULL COMMENT '定义编码',
    `definition_name` varchar(255) DEFAULT NULL COMMENT '定义名称',
    `count` int(11) NOT NULL COMMENT '发生次数 ',
    `influence_hour` double(11,2) DEFAULT NULL COMMENT '影响时长[unit: h]',
    `stop_hour` double(11,2) DEFAULT NULL COMMENT '停机时长[unit: h]',
    `record_time` datetime NOT NULL COMMENT '记录日期',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uni` (`type`,`definition_code`,`record_time`) USING BTREE,
    KEY `idx_uni_code` (`uni_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常管理-每月统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_maintain_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(512) NOT NULL COMMENT '唯一标识 [work_center_id + record_time]',
    `work_center_id` int(11) NOT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) DEFAULT NULL COMMENT '工作中心名称',
    `grid_names` varchar(1024) DEFAULT NULL COMMENT '车间名称列表[多个按逗号分隔]',
    `unqualified_to_repaired_quantity` double(11,2) DEFAULT NULL COMMENT '不良待维修数 [报工不良+扫码不良]',
    `finish_quantity` double(11,2) DEFAULT NULL COMMENT '维修成功数 [没有维修结果的均算合格]',
    `scrap_quantity` double(11,2) DEFAULT NULL COMMENT '维修报废数',
    `success_rate` double(11,2) DEFAULT NULL COMMENT '维修成功率',
    `record_time` datetime NOT NULL COMMENT '记录时间[按月份统计:yyyy-MM-01 00:00:00]',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`record_time`,`work_center_id`),
    KEY `idx_uni_code` (`uni_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维修-每月统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_work_center_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `uni_code` varchar(512) NOT NULL COMMENT '唯一标识 [work_center_id + record_time]',
    `work_center_id` int(11) DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) DEFAULT NULL COMMENT '工作中心名称',
    `record_time` date NOT NULL COMMENT '记录时间 [按月份统计:yyyy-MM-01]',
    `basic_unit_count` int(11) DEFAULT NULL COMMENT '生产基本单元数量',
    `finish_work_order_count` int(11) DEFAULT NULL COMMENT '完成工单数',
    `finish_material_count` int(11) DEFAULT NULL COMMENT '完成工单物料款数 [工单物料去重计数]',
    `finish_produce_quantity` double(16,2) DEFAULT NULL COMMENT '完成工单产出数 [工单产出数求和]',
    `finish_unqualified_quantity` double(16,2) DEFAULT NULL COMMENT '完成工单不良数  [工单不良数求和]',
    `finish_direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '完成工单直通数 [参与报工的流水码去重 - 此批流水码有维修记录的]',
    `finish_exception_count` int(11) DEFAULT NULL COMMENT '完成工单异常次数 [报工小程序上报异常次数]',
    `finish_exception_hour` double(11,2) DEFAULT NULL COMMENT '完成工单异常时长 [报工小程序上报异常工时求和]',
    `gname_list` varchar(1024) DEFAULT NULL COMMENT '车间名称列表 [多个按逗号分隔]',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`record_time`,`work_center_id`) USING BTREE,
    KEY `idx_uni_code` (`uni_code`)
) ENGINE=InnoDB AUTO_INCREMENT=161 DEFAULT CHARSET=utf8mb4 COMMENT='工作中心-每月统计 ';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_work_order_summary_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) NOT NULL COMMENT '唯一标识 [work_center_id + record_time]',
    `record_time` datetime DEFAULT NULL COMMENT '记录日期',
    `work_center_id` int(11) DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) DEFAULT NULL COMMENT '工作中心名称',
    `finish_order_count` int(11) DEFAULT NULL COMMENT '完成工单数量',
    `no_delay_finish_order_count` int(11) DEFAULT NULL COMMENT '及时完成工单数 [计划完成时间<=实际完成时间]',
    `delay_finish_order_count` int(11) DEFAULT NULL COMMENT '延期完成工单数 [计划完成时间>实际完成时间]',
    `material_item_count` int(11) DEFAULT NULL COMMENT '生产物料款数 [完成的工单中，物料编码去重计数]',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '当月产出数量  [依赖工单每日统计指标]',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '当月不良数量  [依赖工单每日统计指标]',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '当月直通数量  [依赖工单每日统计指标]',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`work_center_id`,`record_time`) USING BTREE,
    KEY `idx_uni_code` (`uni_code`),
    KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单-每月汇总';

-- 刷入指标数据
call insert_target_data(
	'userTarget',
	'人员',
	'staffWorkOrderDaily',
	'员工-工单每日统计',
	'dfs_metrics_staff_work_order_daily',
    10
);
call insert_target_data(
	'exceptionTarget',
	'异常',
	'exceptionClassifyDaily',
	'异常管理-每日统计',
	'dfs_metrics_exception_classify_daily',
    10
);

call insert_target_data(
	'exceptionTarget',
	'异常',
	'exceptionClassifyMonthly',
	'异常管理-每月统计',
	'dfs_metrics_exception_classify_monthly',
    30
);

call insert_target_data(
	'maintainTarget',
	'维修',
	'maintainMonthly',
	'维修-每月统计',
	'dfs_metrics_maintain_monthly',
    30
);
call insert_target_data(
	'deviceTarget',
	'设备',
	'deviceConsumptionDaily',
	'设备能耗-每日统计',
	'dfs_metrics_device_consumption_daily',
    10
);
call insert_target_data(
	'areaTarget',
	'工厂',
	'workCenterMonthlyDaily',
	'工作中心-每月统计',
	'dfs_metrics_work_center_monthly',
   30
);
call insert_target_data(
	'workOrderTarget',
	'生产工单',
	'workOrderSummaryMonthly',
	'工单-每月汇总',
	'dfs_metrics_work_order_summary_monthly',
   30
);



-- 清除脏数据
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order','production_basic_unit_id');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order','production_basic_unit_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order','production_basic_unit_type_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order','production_basic_unit_type_id');

call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_daily','production_basic_unit_id');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_daily','production_basic_unit_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_daily','production_basic_unit_type_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_daily','production_basic_unit_type_id');

call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_hourly','production_basic_unit_id');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_hourly','production_basic_unit_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_hourly','production_basic_unit_type_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_hourly','production_basic_unit_type_id');

call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_10min','production_basic_unit_id');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_10min','production_basic_unit_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_10min','production_basic_unit_type_name');
call `dfs_metrics`.`drop_column_if_exists`('dfs_metrics_work_order_10min','production_basic_unit_type_id');

--
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'work_center_type',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''工作中心类型''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'production_basic_unit_id',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `production_basic_unit_id` varchar(255) NULL COMMENT ''生产基本单元id''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'production_basic_unit_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `production_basic_unit_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''生产基本单元名称''');
UPDATE `dfs_metrics`.`dfs_metrics_work_order` mwo
    INNER JOIN `dfs`.`dfs_work_order` wo on wo.work_order_number = mwo.`work_order_number`
    LEFT JOIN (
        SELECT
            work_order_number,
            GROUP_CONCAT(production_basic_unit_id SEPARATOR ',') AS production_basic_unit_id,
            GROUP_CONCAT(production_basic_unit_name SEPARATOR ',') AS production_basic_unit_name
        FROM `dfs`.`dfs_work_order_basic_unit_relation`
        GROUP BY work_order_number
    ) AS bu on mwo.work_order_number = bu.work_order_number
    SET
        mwo.work_center_type = wo.work_center_type,
        mwo.production_basic_unit_id = bu.production_basic_unit_id,
        mwo.production_basic_unit_name = bu.production_basic_unit_name
WHERE mwo.work_center_type is null;

--
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'work_center_type',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''工作中心类型''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'production_basic_unit_id',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `production_basic_unit_id` varchar(255) NULL COMMENT ''生产基本单元id''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'production_basic_unit_name',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `production_basic_unit_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''生产基本单元名称''');
UPDATE `dfs_metrics`.`dfs_metrics_work_order_daily` mwo
    INNER JOIN `dfs`.`dfs_work_order` wo on wo.work_order_number = mwo.`work_order_number`
    LEFT JOIN (
        SELECT
            work_order_number,
            GROUP_CONCAT(production_basic_unit_id SEPARATOR ',') AS production_basic_unit_id,
            GROUP_CONCAT(production_basic_unit_name SEPARATOR ',') AS production_basic_unit_name
        FROM `dfs`.`dfs_work_order_basic_unit_relation`
        GROUP BY work_order_number
    ) AS bu on mwo.work_order_number = bu.work_order_number
    SET
        mwo.work_center_type = wo.work_center_type,
        mwo.production_basic_unit_id = bu.production_basic_unit_id,
        mwo.production_basic_unit_name = bu.production_basic_unit_name
WHERE mwo.work_center_type is null;

-- hour、10min 这两张不处理历史数据，数据量太大了
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_hourly',
        'work_center_type',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''工作中心类型''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_hourly',
        'production_basic_unit_id',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `production_basic_unit_id` varchar(255) NULL COMMENT ''生产基本单元id''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_hourly',
        'production_basic_unit_name',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `production_basic_unit_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''生产基本单元名称''');

--
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_10min',
        'work_center_type',
        'ALTER TABLE `dfs_metrics_work_order_10min` ADD COLUMN `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''工作中心类型''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_10min',
        'production_basic_unit_id',
        'ALTER TABLE `dfs_metrics_work_order_10min` ADD COLUMN `production_basic_unit_id` varchar(255) NULL COMMENT ''生产基本单元id''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_10min',
        'production_basic_unit_name',
        'ALTER TABLE `dfs_metrics_work_order_10min` ADD COLUMN `production_basic_unit_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''生产基本单元名称''');

-- 一些错误数据矫正
update dfs_table_config set field_name = '生产订单id' WHERE field_code = 'product_order_id';
update dfs_table_config set field_name = '生产订单号' WHERE field_code = 'product_order_number';


call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'work_center_id',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `work_center_id` int(11) DEFAULT NULL COMMENT ''工作中心id''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'work_center_name',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `work_center_name` varchar(255) DEFAULT NULL COMMENT ''工作中心名称''');

-- 更新工单每日的工作中心
UPDATE dfs_metrics.dfs_metrics_work_order_daily d
    LEFT JOIN dfs.dfs_work_order o ON d.work_order_number = o.work_order_number
SET d.work_center_id = o.work_center_id, d.work_center_name = o.work_center_name;


-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.21.1.1=======================================================
