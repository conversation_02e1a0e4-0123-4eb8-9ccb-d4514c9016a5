drop table IF EXISTS dfs_target_alarm;

CREATE TABLE IF NOT EXISTS `dfs_target_alarm` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `target_name` varchar(255) NOT NULL COMMENT '指标模型名称',
    `alarm_definition_code` varchar(255) NOT NULL COMMENT '告警定义',
    `type` varchar(50) NOT NULL COMMENT '类型枚举: SCRIPT-脚本, RULE-规则',
    `rule_type` varchar(50) DEFAULT NULL COMMENT 'RULE类型枚举: OR-或, AND-且',
    `script` text COMMENT '脚本内容',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `target_name` (`target_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='指标告警配置表';


CREATE TABLE IF NOT EXISTS `dfs_target_alarm_rule` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `target_alarm_id` int(11) NOT NULL COMMENT '指标告警id',
    `field_code` varchar(50) NOT NULL COMMENT '表字段',
    `operator` varchar(50) NOT NULL COMMENT '操作运算符枚举: \r\nGT-大于, \r\nGE-大于等于, \r\nLT-小于，\r\nLE-小于等于，\r\nNE-不等于,\r\nEQ-等于',
    `compare_value` varchar(512) NOT NULL COMMENT '对比内容',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `target_alarm_id` (`target_alarm_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='指标告警配置规则表';