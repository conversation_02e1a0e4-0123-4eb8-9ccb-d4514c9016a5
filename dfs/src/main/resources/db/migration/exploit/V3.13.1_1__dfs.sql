-- DDL
-- 新增单据类型表
CREATE TABLE IF NOT EXISTS `dfs_order_type_config`
(
    `id`                    int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `code`                  varchar(100) DEFAULT NULL COMMENT '编码',
    `name`                  varchar(255) DEFAULT NULL COMMENT '名称',
    `related_category_code` varchar(750) DEFAULT '' COMMENT '关联的单据大类编码',
    `description`           text COMMENT '描述',
    `is_default`            tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认',
    `create_by`             varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by`             varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time`           datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime     DEFAULT NULL COMMENT '修改时间',
    `is_inner`              tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否内置',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `code` (`code`) USING BTREE,
    KEY                     `related_category_code` (`related_category_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='单据类型菜单配置表';

CREATE TABLE IF NOT EXISTS `dfs_order_type_item`
(
    `id`                    int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `business_type_code`    varchar(512) NOT NULL COMMENT '业务类型编码',
    `code`                  varchar(255) DEFAULT NULL COMMENT '编码',
    `name`                  varchar(255) NOT NULL COMMENT '名称',
    `enable`                tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否启用',
    `sort`                  int(11) DEFAULT '1' COMMENT '排序',
    `is_default`            tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认',
    `is_inner`              tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否内置',
    `description`           text COMMENT '描述',
    `create_by`             varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by`             varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time`           datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime     DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `uni_code` (`business_type_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='单据类型实例表';

CREATE TABLE IF NOT EXISTS `dfs_code_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code` varchar(255) DEFAULT NULL COMMENT '条码',
    `relation_type` varchar(100) DEFAULT NULL COMMENT '关联类型',
    `relation_number` varchar(750) DEFAULT NULL COMMENT '关联编码',
    `type` varchar(50) DEFAULT NULL COMMENT '操作类型',
    `des` longtext COMMENT '操作描述',
    `username` varchar(255) DEFAULT NULL COMMENT '操作人账号',
    `nickname` varchar(255) DEFAULT NULL COMMENT '操作人姓名',
    `create_time` datetime DEFAULT NULL COMMENT '日期',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `url_type` varchar(50) DEFAULT NULL COMMENT '链接类型',
    `app_id` varchar(255) DEFAULT NULL COMMENT 'appid',
    `url` varchar(255) DEFAULT NULL COMMENT '链接信息',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `create_time` (`create_time`) USING BTREE,
    KEY `code` (`code`) USING BTREE,
    KEY `relation_number` (`relation_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='条码日志记录表';

-- 生产工单用料清单扩展字段
call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_one',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_one`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段1'' AFTER `remark`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_two',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_two`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段2'' AFTER `material_list_extend_field_one`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_three',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_three`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段3'' AFTER `material_list_extend_field_two`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_four',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_four`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段4'' AFTER `material_list_extend_field_three`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_five',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_five`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段5'' AFTER `material_list_extend_field_four`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_six',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_six`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段6'' AFTER `material_list_extend_field_five`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_seven',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_seven`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段7'' AFTER `material_list_extend_field_six`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_eight',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_eight`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段8'' AFTER `material_list_extend_field_seven`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_nine',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_nine`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段9'' AFTER `material_list_extend_field_eight`;');

call proc_add_column(
        'dfs_work_order_material_list',
        'material_list_extend_field_ten',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `material_list_extend_field_ten`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单扩展字段10'' AFTER `material_list_extend_field_nine`;');

-- 生产工单用料清单物料行新增扩展字段
call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_one',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_one`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段1'' AFTER `business_unit_name`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_two',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_two`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段2'' AFTER `material_list_material_extend_field_one`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_three',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_three`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段3'' AFTER `material_list_material_extend_field_two`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_four',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_four`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段4'' AFTER `material_list_material_extend_field_three`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_five',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_five`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段5'' AFTER `material_list_material_extend_field_four`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_six',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_six`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段6'' AFTER `material_list_material_extend_field_five`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_seven',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_seven`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段7'' AFTER `material_list_material_extend_field_six`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_eight',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_eight`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段8'' AFTER `material_list_material_extend_field_seven`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_nine',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_nine`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段9'' AFTER `material_list_material_extend_field_eight`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'material_list_material_extend_field_ten',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `material_list_material_extend_field_ten`  varchar(255) DEFAULT null    COMMENT ''生产工单用料清单物料扩展字段10'' AFTER `material_list_material_extend_field_nine`;');

-- DML

-- 单据类型默认值设置
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('saleOrder', '销售订单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardSaleOrder', '标准销售订单', 'saleOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('productOrder', '生产订单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardProductOrder', '标准生产订单', 'productOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('reworkProductOrder', '返工订单', 'productOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('workOrder', '生产工单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardWorkOrder', '标准工单', 'workOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('reworkWorkOrder', '返工工单', 'workOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('productOrderMaterialList', '生产订单用料清单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardProductOrderMaterialList', '标准订单用料清单', 'productOrderMaterialList', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('workOrderMaterialList', '生产工单用料清单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardWorkOrderMaterialList', '标准工单用料清单', 'workOrderMaterialList', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('deliveryApplication', '销售出货单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardDeliveryApplication', '标准出货', 'deliveryApplication', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('saleReturnOrder', '销售退货单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardSaleReturnOrder', '标准退货', 'saleReturnOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('otherBatchSaleReturnOrder', '其他批量退货', 'saleReturnOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('purchaseRequest', '采购需求', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardPurchaseRequest', '标准采购需求', 'purchaseRequest', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('purchaseOrder', '采购订单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardPurchaseOrder', '标准采购', 'purchaseOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('purchaseReceipt', '采购收料单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardPurchaseReceipt', '标准收料', 'purchaseReceipt', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('otherBatchPurchaseReceipt', '其他批量收料', 'purchaseReceipt', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('returnOrder', '采购退料单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('inspectReturnOrder', '检验退料', 'returnOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('receiveMaterialReturnOrder', '收料退料', 'returnOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('inventoryReturnOrder', '库存退料', 'returnOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('subcontractOrder', '委外订单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardSubcontractOrder', '标准委外', 'subcontractOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('subcontractOrderReceiptOrder', '委外订单收货单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('standardSubcontractOrderReceiptOrder', '标准收货', 'subcontractOrderReceiptOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('subcontractOrderReturnOrder', '委外订单退货单', '', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('inspectSubcontractOrderReturnOrder', '检验退料', 'subcontractOrderReturnOrder', NULL, 1, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('receiveMaterialSubcontractOrderReturnOrder', '收料退料', 'subcontractOrderReturnOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('inventorySubcontractOrderReturnOrder', '库存退料', 'subcontractOrderReturnOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);

INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSaleOrder', 'forecastOrder', '预测订单', 1, 1, 0, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSaleOrder', 'customerOrder', '客户订单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardProductOrder', 'standardProductOrder', '标准生产订单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('reworkProductOrder', 'reworkProductOrder', '返工订单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardWorkOrder', '1', '标准工单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('reworkWorkOrder', '2', '返工工单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardProductOrderMaterialList', 'standardProductOrderMaterialList', '标准订单用料清单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardWorkOrderMaterialList', 'standardWorkOrderMaterialList', '标准工单用料清单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardDeliveryApplication', 'standardDeliveryApplication', '标准出货单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSaleReturnOrder', 'standardSaleReturnOrder', '标准退货单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('otherBatchSaleReturnOrder', 'otherBatchSaleReturnOrder', '批量退货单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardPurchaseRequest', 'standardPurchaseRequest', '标准采购需求单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardPurchaseOrder', '0', '正常采购单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardPurchaseOrder', '1', '临时采购单', 1, 1, 0, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardPurchaseReceipt', 'purchase', '标准收料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('otherBatchPurchaseReceipt', 'other', '其他收料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('inspectReturnOrder', 'inspectionReturn', '检验退料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('receiveMaterialReturnOrder', 'receiveReturn', '收料退料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('inventoryReturnOrder', 'warehouseReturn', '库存退料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSubcontractOrder', 'singleProcessSubcontractOrder', '单工序委外订单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSubcontractOrder', 'multiProcessSubcontractOrder', '多工序委外订单', 1, 1, 0, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('standardSubcontractOrderReceiptOrder', 'standardSubcontractOrderReceiptOrder', '标准收货单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('inspectSubcontractOrderReturnOrder', 'inspectionReturn', '检验退料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('receiveMaterialSubcontractOrderReturnOrder', 'receiveReturn', '收料退料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('inventorySubcontractOrderReturnOrder', 'warehouseReturn', '库存退料单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');

-- 生产工单新增业务类型字段和单据类型字段
call proc_modify_column(
        'dfs_work_order',
        'order_type',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `order_type` varchar(255) NOT NULL DEFAULT ''1'' COMMENT ''单据类型'';');
call proc_add_column(
        'dfs_work_order',
        'business_type',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `business_type` varchar(255) NOT NULL DEFAULT ''standardWorkOrder'' COMMENT ''业务类型'' AFTER `order_type`;');
-- 兼容历史数据
UPDATE `dfs_work_order` SET `business_type` = 'standardWorkOrder' WHERE `order_type` = '1';
UPDATE `dfs_work_order` SET `business_type` = 'reworkWorkOrder' WHERE `order_type` = '2';

-- 添加表单配置
UPDATE `dfs_form_field_config` SET `field_name` = '单据类型' WHERE `full_path_code` in ('workOrder.edit', 'workOrder.detail') AND `field_code` = 'orderType' AND `type_code` = 'sysField';
call proc_add_form_field("workOrder.list", "orderTypeName", "单据类型");
call proc_add_form_field("workOrder.list", "businessTypeName", "业务类型");
call proc_add_form_field("workOrder.edit", "businessType", "业务类型");
call proc_add_form_field("workOrder.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);

INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'businessTypeName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.editByCreate', 'workOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.editByRelease', 'workOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.editByInvestment', 'workOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.editByHangUp', 'workOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.editByFinish', 'workOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.editByClosed', 'workOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.editByCancel', 'workOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/production-workorder', 'workOrder.detail', 'workOrder.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);

-- 生产工单用料清单新增单据类型、业务类型
call proc_add_column(
        'dfs_work_order_material_list',
        'order_type',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `order_type` varchar(255) NOT NULL DEFAULT ''standardWorkOrderMaterialList'' COMMENT ''单据类型'' AFTER `state`;');
call proc_add_column(
        'dfs_work_order_material_list',
        'business_type',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `business_type` varchar(255) NOT NULL DEFAULT ''standardWorkOrderMaterialList'' COMMENT ''业务类型'' AFTER `order_type`;');

call proc_add_form_field("workOrderMaterialList.list.material", "orderTypeName", "单据类型");
call proc_add_form_field("workOrderMaterialList.list.order", "orderTypeName", "单据类型");
call proc_add_form_field("workOrderMaterialList.edit", "orderType", "单据类型");
call proc_add_form_field("workOrderMaterialList.detail", "orderType", "单据类型");

call proc_add_form_field("workOrderMaterialList.list.material", "businessTypeName", "业务类型");
call proc_add_form_field("workOrderMaterialList.list.order", "businessTypeName", "业务类型");
call proc_add_form_field("workOrderMaterialList.edit", "businessType", "业务类型");
call proc_add_form_field("workOrderMaterialList.detail", "businessType", "业务类型");

INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'orderType', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'orderType', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);


INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'businessTypeName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'businessTypeName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);
INSERT INTO `dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`)
VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0);



-- 生产工单计划数量配置
INSERT INTO `dfs_business_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `service_name`) VALUES ('planQuantityVerify', '单据计划数量校验', 'production.workOrderConfig.planQuantityVerify', 'production.workOrderConfig', NULL, 'yelinkoncall', 'yelinkoncall', '');
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('zeroPass', '计划数为0时是否可以创建或变更', 'production.workOrderConfig.planQuantityVerify.zeroPass', 'production.workOrderConfig.planQuantityVerify', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"可以\"},{\"value\":false,\"label\":\"不可以\"}]', 'true', NULL);

-- 将出货申请单变更为销售出货单
update sys_permissions set name = '销售出货单'  where id = '1090103';
update dfs_config_rule set rule_type_name = '销售出货单-单据编号'  where rule_type = 16;
update dfs_form_config set name = '销售出货单'  where full_path_code = 'deliveryApplication';
update dfs_form_config set name = '销售出货单列表页'  where full_path_code = 'deliveryApplication.list.material';
update dfs_form_config set name = '销售出货单编辑页'  where full_path_code = 'deliveryApplication.edit';
update dfs_form_config set name = '销售出货单详情'  where full_path_code = 'deliveryApplication.detail';

-- 销售订单下推销售退货单
delete  from  dfs_order_push_down_item   where config_path = 'saleOrder.pushDownConfig.saleReturnOrder';
delete  from  dfs_dict   where type = 'saleOrder.pushDownConfig.saleReturnOrder';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch';
delete from dfs_order_push_down_config_value_dict  where config_full_path_code  = 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch';

INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.saleReturnOrder.pushBatch', '通用下推', 'saleOrder.pushDownConfig.saleReturnOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'saleOrder.pushDownConfig.saleReturnOrder', 'show');
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.saleReturnOrder',  '下推销售退货单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.enable', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.originalOrderStates', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '选择物料行', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.pushBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.description', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.targetOrderStates', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.applicationId', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.order-model.sales_returns\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.pushBatch.url', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/order-model/sales_returns?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.sourceOrderType', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.targetOrderType', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleReturnOrder\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.enable', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.originalOrderStates', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '选择物料行', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.pushBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.description', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.targetOrderStates', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.applicationId', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.order-model.sales_returns\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.pushBatch.url', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/order-model/sales_returns?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.sourceOrderType', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch.targetOrderType', 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleReturnOrder\"', NULL);

-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.saleReturnOrder.pushBatch';

-- 单据下推配置【反选物料行】修改成【选择物料行】
UPDATE `dfs_order_push_down_config_value_dict` SET `value_name` = '选择物料行' WHERE `value_code` = 'isSupportMaterialLineReversePushDown';
UPDATE `dfs_order_push_down_config_value` SET `value_name` = '选择物料行' WHERE `value_code` = 'isSupportMaterialLineReversePushDown';

-- 生产订单下推生产订单用料清单增加按物料类型过滤
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialTypes', '物料类型过滤', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialTypes', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select-multiple', 'api','/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL);


-- 销售订单下推出货申请单（即销售出货单）
-- 删除之前"下推出货申请(新建页签)"配置
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.deliveryApplication.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.deliveryApplication.jumpPage";
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.deliveryApplication.push', '通用下推', 'saleOrder.pushDownConfig.deliveryApplication', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.deliveryApplication.push.originalOrderStates', 'saleOrder.pushDownConfig.deliveryApplication.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '选择物料行', 'saleOrder.pushDownConfig.deliveryApplication.push.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.deliveryApplication.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.deliveryApplication.push.description', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.deliveryApplication.push.targetOrderState', 'saleOrder.pushDownConfig.deliveryApplication.push', 'select', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[1,4]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.deliveryApplication.push.applicationId', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.dfs.scc.order-model.shipment_application\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.deliveryApplication.push.url', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/supply-chain-collaboration/order-model/shipment_application?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.deliveryApplication.push.sourceOrderType', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.deliveryApplication.push.targetOrderType', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"deliveryApplication\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.deliveryApplication',  '下推销售出货单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.deliveryApplication.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.deliveryApplication.push.originalOrderStates', 'saleOrder.pushDownConfig.deliveryApplication.push', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '选择物料行', 'saleOrder.pushDownConfig.deliveryApplication.push.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.deliveryApplication.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.deliveryApplication.push.description', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.deliveryApplication.push.targetOrderState', 'saleOrder.pushDownConfig.deliveryApplication.push', 'select', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[1,4]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'saleOrder.pushDownConfig.deliveryApplication.push.applicationId', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.dfs.scc.order-model.shipment_application\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.deliveryApplication.push.url', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/supply-chain-collaboration/order-model/shipment_application?pushDownOrigin=saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.deliveryApplication.push.sourceOrderType', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.deliveryApplication.push.targetOrderType', 'saleOrder.pushDownConfig.deliveryApplication.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"deliveryApplication\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.deliveryApplication.push';

-- 采购订单下推采购入库单
-- 删除之前的“下推采购入库单(新建页签)”
DELETE FROM dfs_order_push_down_item WHERE instance_type = "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage";
-- 新加的配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('purchase.purchaseOrderPushDownConfig.purchaseIn.push', '通用下推', 'purchase.purchaseOrderPushDownConfig.purchaseIn', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.description', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.targetOrderState', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.url', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage/add?pushDownOrigin=purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.sourceOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.targetOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseIn\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'purchase.purchaseOrderPushDownConfig.purchaseIn',  '下推采购入库单', 'RULE', 1, 1, 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.isSupportMaterialLineReversePushDown', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.description', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.targetOrderState', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.applicationId', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.url', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage/add?pushDownOrigin=purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.sourceOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push.targetOrderType', 'purchase.purchaseOrderPushDownConfig.purchaseIn.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseIn\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchase.purchaseOrderPushDownConfig.purchaseIn.push';

-- 采购收料单下推采购入库单，删除之前的“采购入库单(新建页签)”
DELETE FROM dfs_order_push_down_item WHERE instance_type = "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn";

-- 生产工单用料清单、销售退货单 设置全局配置
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES ('dfs', 'workOrder', '生产工单', 'dfs', 'workOrderMaterialList', '生产工单用料清单');
INSERT INTO `dfs_form_up_down_stream_relationship_config`(`upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES ('ams', 'saleOrder', '销售订单', 'ams', 'saleReturn', '销售退货单');

INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldOne', '生产工单用料清单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldTwo', '生产工单用料清单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldThree', '生产工单用料清单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldFour', '生产工单用料清单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldFive', '生产工单用料清单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldSix', '生产工单用料清单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldSeven', '生产工单用料清单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldEight', '生产工单用料清单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldNine', '生产工单用料清单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListExtendFieldTen', '生产工单用料清单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldOne', '生产工单用料清单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldTwo', '生产工单用料清单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldThree', '生产工单用料清单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldFour', '生产工单用料清单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldFive', '生产工单用料清单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldSix', '生产工单用料清单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldSeven', '生产工单用料清单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldEight', '生产工单用料清单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldNine', '生产工单用料清单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList', 'materialListMaterialExtendFieldTen', '生产工单用料清单物料扩展字段10');

INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldOne', '销售退货单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldTwo', '销售退货单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldThree', '销售退货单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldFour', '销售退货单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldFive', '销售退货单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldSix', '销售退货单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldSeven', '销售退货单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldEight', '销售退货单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldNine', '销售退货单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnExtendFieldTen', '销售退货单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldOne', '销售退货单物料拓展字段1');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldTwo', '销售退货单物料拓展字段2');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldThree', '销售退货单物料拓展字段3');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldFour', '销售退货单物料拓展字段4');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldFive', '销售退货单物料拓展字段5');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldSix', '销售退货单物料拓展字段6');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldSeven', '销售退货单物料拓展字段7');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldEight', '销售退货单物料拓展字段8');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldNine', '销售退货单物料拓展字段9');
INSERT INTO `dfs_form_overall_allow_field_config`(`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/order-model/sales_returns', 'saleReturn', 'saleReturnMaterialExtendFieldTen', '销售退货单物料拓展字段10');

-- 单据类型新增路由和权限
call route_add_l2('/task-center', '/documents-config/order-type', '单据类型');
update sys_route set module_name = 'dfs' WHERE path = '/documents-config/order-type';
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('/documents-config/order-type/add', '新增', 'order.type:add', NULL, NULL, NULL, NULL, '2024-11-20 15:26:10', 'enable', 'GET', '/documents-config/order-type', 2, 1, 0, '/base-info/role-permission', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('/documents-config/order-type/update', '编辑', 'order.type:update', NULL, NULL, NULL, NULL, '2024-11-20 15:26:10', 'enable', 'GET', '/documents-config/order-type', 2, 1, 0, '/base-info/role-permission', 1, NULL, '', 1);
call init_new_role_permission('/documents-config/order-type/add');
call init_new_role_permission('/documents-config/order-type/update');

-- 告警聚合码
call proc_modify_column(
        'dfs_alarm',
        'aggregate_code',
        'ALTER TABLE `dfs_alarm` MODIFY COLUMN `aggregate_code` varchar(512) DEFAULT NULL COMMENT ''告警聚合码：（设备的故障代码）''');

INSERT INTO `dfs_config_notice_level_relation`(`id`, `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES (null, 'deviceAlarmNotice', '设备告警通知', '{设备编号},{设备名称},{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人}', 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`id`, `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES (null, 'productAlarmNotice', '生产告警通知', '{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人}', 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`id`, `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES (null, 'safeAlarmNotice', '安全告警通知', '{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人}', 'unusualInfoNotice');
INSERT INTO `dfs_config_notice_level_relation`(`id`, `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES (null, 'otherAlarmNotice', '其他告警通知', '{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人}', 'unusualInfoNotice');

call proc_add_column(
        'dfs_alarm_notification',
        'circulation_notice_config_id',
        'ALTER TABLE `dfs_alarm_notification` ADD COLUMN `circulation_notice_config_id` int(11) DEFAULT NULL COMMENT ''流转通知配置id''');

-- 委外订单导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090401190', '导出_下载默认模板', 'outsourcingOrder:export:downDefaultTemplate', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090401200', '导出_上传下载自定义导出模板', 'outsourcingOrder:export:downUploadTemplate', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090401210', '导出_导出excel', 'outsourcingOrder:export:excel', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090401220', '导出_查看日志', 'outsourcingOrder:export:log', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
call init_new_role_permission('1090401190');
call init_new_role_permission('1090401200');
call init_new_role_permission('1090401210');
call init_new_role_permission('1090401220');

-- 生产工单用料清单导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10012160', '导出_下载默认模板', 'workOrder.materials:export:downDefaultTemplate', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10012170', '导出_上传下载自定义导出模板', 'workOrder.materials:export:downUploadTemplate', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10012180', '导出_导出excel', 'workOrder.materials:export:excel', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10012190', '导出_查看日志', 'workOrder.materials:export:log', NULL, NULL, NOW(), NULL, NULL, 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1);
call init_new_role_permission('10012160');
call init_new_role_permission('10012170');
call init_new_role_permission('10012180');
call init_new_role_permission('10012190');

-- 生产工单下推仓库相关配置调整
DELETE FROM `dfs_dict` WHERE `type` in ('production.workOrderPushDownConfig.outputPickingProduct','production.workOrderPushDownConfig.workOrderSupplement','production.workOrderPushDownConfig.productionReturnReceipt','production.workOrderPushDownConfig.productionIn');
DELETE FROM `dfs_order_push_down_item` WHERE `instance_type` in ('production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct','production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn');
DELETE FROM `dfs_order_push_down_config_value` WHERE `config_full_path_code` in ('production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct','production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn');

INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('production.workOrderPushDownConfig.outputPickingProduct.general', '通用下推', 'production.workOrderPushDownConfig.outputPickingProduct');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.outputPickingProduct');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('production.workOrderPushDownConfig.workOrderSupplement.general', '通用下推', 'production.workOrderPushDownConfig.workOrderSupplement');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.workOrderSupplement');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('production.workOrderPushDownConfig.productionReturnReceipt.general', '通用下推', 'production.workOrderPushDownConfig.productionReturnReceipt');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.productionReturnReceipt');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('production.workOrderPushDownConfig.productionIn.general', '通用下推', 'production.workOrderPushDownConfig.productionIn');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.productionIn');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.outputPickingProduct.general.originalOrderStates', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', '[2,3,4]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.outputPickingProduct.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.outputPickingProduct.general.description', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.outputPickingProduct.general.targetOrderState', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.outputPickingProduct.general.applicationId', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.outputPickingProduct.general.url', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.outputPickingProduct.general.sourceOrderType', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.outputPickingProduct.general.targetOrderType', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"outputPickingProduct\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.workOrderSupplement.general.originalOrderStates', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', '[2,3,4]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.workOrderSupplement.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.workOrderSupplement.general.description', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.workOrderSupplement.general.targetOrderState', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.workOrderSupplement.general.applicationId', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderSupplement\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.workOrderSupplement.general.url', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.workOrderSupplement.general.sourceOrderType', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.workOrderSupplement.general.targetOrderType', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderSupplement\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.productionReturnReceipt.general.originalOrderStates', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', '[2,3,4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.productionReturnReceipt.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.productionReturnReceipt.general.description', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.productionReturnReceipt.general.targetOrderState', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productionReturnReceipt.general.applicationId', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.applicationReturn\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productionReturnReceipt.general.url', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/applicationReturn/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productionReturnReceipt.general.sourceOrderType', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productionReturnReceipt.general.targetOrderType', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionReturnReceipt\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.productionIn.general.originalOrderStates', 'production.workOrderPushDownConfig.productionIn.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[3,4,5]', '[3,4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.productionIn.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.productionIn.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.productionIn.general.description', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.productionIn.general.targetOrderState', 'production.workOrderPushDownConfig.productionIn.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productionIn.general.applicationId', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderComplete\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productionIn.general.url', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productionIn.general.sourceOrderType', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productionIn.general.targetOrderType', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionIn\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.outputPickingProduct.general.originalOrderStates', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', '[2,3,4]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.outputPickingProduct.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.outputPickingProduct.general.description', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.outputPickingProduct.general.targetOrderState', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.outputPickingProduct.general.applicationId', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.outputPickingProduct.general.url', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.outputPickingProduct.general.sourceOrderType', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.outputPickingProduct.general.targetOrderType', 'production.workOrderPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"outputPickingProduct\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.workOrderSupplement.general.originalOrderStates', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', '[2,3,4]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.workOrderSupplement.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.workOrderSupplement.general.description', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.workOrderSupplement.general.targetOrderState', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.workOrderSupplement.general.applicationId', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderSupplement\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.workOrderSupplement.general.url', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.workOrderSupplement.general.sourceOrderType', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.workOrderSupplement.general.targetOrderType', 'production.workOrderPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderSupplement\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.productionReturnReceipt.general.originalOrderStates', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', '[2,3,4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.productionReturnReceipt.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.productionReturnReceipt.general.description', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.productionReturnReceipt.general.targetOrderState', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productionReturnReceipt.general.applicationId', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.applicationReturn\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productionReturnReceipt.general.url', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/applicationReturn/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productionReturnReceipt.general.sourceOrderType', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productionReturnReceipt.general.targetOrderType', 'production.workOrderPushDownConfig.productionReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionReturnReceipt\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.productionIn.general.originalOrderStates', 'production.workOrderPushDownConfig.productionIn.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[3,4,5]', '[3,4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.productionIn.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.productionIn.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.productionIn.general.description', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.productionIn.general.targetOrderState', 'production.workOrderPushDownConfig.productionIn.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.productionIn.general.applicationId', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderComplete\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.productionIn.general.url', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.productionIn.general.sourceOrderType', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.productionIn.general.targetOrderType', 'production.workOrderPushDownConfig.productionIn.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionIn\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.outputPickingProduct', NULL, '下推生产领料出库单', 'RULE', 'production.workOrderPushDownConfig.outputPickingProduct.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.workOrderSupplement', NULL, '下推生产补料出库单', 'RULE', 'production.workOrderPushDownConfig.workOrderSupplement.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.productionReturnReceipt', NULL, '下推生产退料入库单', 'RULE', 'production.workOrderPushDownConfig.productionReturnReceipt.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.productionIn', NULL, '下推生产入库单', 'RULE', 'production.workOrderPushDownConfig.productionIn.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');

-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.outputPickingProduct.general';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.workOrderSupplement.general';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.productionReturnReceipt.general';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.productionIn.general';

-- 生产工单用料清单下推仓库相关单据配置
DELETE FROM `dfs_order_push_down_config` WHERE `full_path_code` = 'production.workOrderMaterialsListPushDownConfig.takeOutOutbound';
DELETE FROM `dfs_dict` WHERE `type` = 'production.workOrderMaterialsListPushDownConfig.takeOutOutbound';
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('outputPickingProduct', '生产领料出库单', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct', 'production.workOrderMaterialsListPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'outputPickingProduct', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', '通用下推', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('workOrderSupplement', '生产补料出库单', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement', 'production.workOrderMaterialsListPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'workOrderSupplement', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', '通用下推', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.originalOrderStates', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'select-multiple', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.isSupportMaterialLineReversePushDown', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.description', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.targetOrderState', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.applicationId', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.url', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut/add?pushDownOrigin=workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.sourceOrderType', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.targetOrderType', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"outputPickingProduct\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.originalOrderStates', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'select-multiple', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.isSupportMaterialLineReversePushDown', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.description', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.targetOrderState', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.applicationId', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderSupplement\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.url', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement/add?pushDownOrigin=workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.sourceOrderType', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.targetOrderType', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderSupplement\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.originalOrderStates', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'select-multiple', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.isSupportMaterialLineReversePushDown', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.description', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.targetOrderState', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.applicationId', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.url', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut/add?pushDownOrigin=workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.sourceOrderType', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general.targetOrderType', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"outputPickingProduct\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.originalOrderStates', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'select-multiple', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.isSupportMaterialLineReversePushDown', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.description', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.targetOrderState', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.applicationId', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderSupplement\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.url', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement/add?pushDownOrigin=workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.sourceOrderType', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialsList\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general.targetOrderType', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderSupplement\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderMaterialsListPushDownConfig.outputPickingProduct', NULL, '下推领料出库单', 'RULE', 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderMaterialsListPushDownConfig.workOrderSupplement', NULL, '下推补料出库单', 'RULE', 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');

UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general';

-- 新增出货申请（即销售出货单）下推销售出库单、调拨单配置
-- 删除软件参数里相关的下推配置
DELETE FROM `dfs_business_config` WHERE `full_path_code` in ('delivery','delivery.deliveryApplicationPushDownConfig','delivery.deliveryApplicationPushDownConfig.saleInAndOut','delivery.deliveryApplicationPushDownConfig.transferOrder', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage','delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage');
DELETE FROM `dfs_business_config_value` WHERE `value_full_path_code` in ('delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage.enable','delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage.originalOrderStates','delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage.enable','delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage.originalOrderStates');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('pushDownConfig', '销售出货单下推配置', 'deliveryApplication.pushDownConfig', 'saleOrder', NULL, NULL, NULL, NULL, NULL, 1, 0, 'deliveryApplication', NULL, NULL, '');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('salesIssueDoc', '销售出库单', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut', 'deliveryApplication.pushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'salesIssueDoc', NULL, NULL, '');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('transferOrder', '调拨单', 'delivery.deliveryApplicationPushDownConfig.transferOrder', 'deliveryApplication.pushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 1, 'transferOrder', NULL, NULL, '');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', '通用下推', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('delivery.deliveryApplicationPushDownConfig.transferOrder.general', '通用下推', 'delivery.deliveryApplicationPushDownConfig.transferOrder');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'delivery.deliveryApplicationPushDownConfig.transferOrder');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.originalOrderStates', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'select-multiple', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[4,5]', '[4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.isSupportMaterialLineReversePushDown', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.description', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.targetOrderState', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.applicationId', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesIssueDoc\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.url', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesIssueDoc/add?pushDownOrigin=deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.sourceOrderType', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.targetOrderType', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"salesIssueDoc\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.originalOrderStates', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'select-multiple', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[4,5]', '[4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.isSupportMaterialLineReversePushDown', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.description', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.targetOrderState', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.applicationId', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.cougnyManage.allocation\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.url', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/cougnyManage/allocation/add?pushDownOrigin=deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.sourceOrderType', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.targetOrderType', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"transferOrder\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.originalOrderStates', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'select-multiple', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[4,5]', '[4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.isSupportMaterialLineReversePushDown', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.description', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.targetOrderState', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.applicationId', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesIssueDoc\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.url', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/salesIssueReceipt/salesIssueDoc/add?pushDownOrigin=deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.sourceOrderType', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general.targetOrderType', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"salesIssueDoc\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.originalOrderStates', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'select-multiple', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[4,5]', '[4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.isSupportMaterialLineReversePushDown', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.description', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.targetOrderState', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.applicationId', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.cougnyManage.allocation\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.url', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/cougnyManage/allocation/add?pushDownOrigin=deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.sourceOrderType', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"deliveryApplication\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general.targetOrderType', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"transferOrder\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('delivery.deliveryApplicationPushDownConfig.saleInAndOut', NULL, '下推销售出库单', 'RULE', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('delivery.deliveryApplicationPushDownConfig.transferOrder', NULL, '下推调拨单', 'RULE', 'delivery.deliveryApplicationPushDownConfig.transferOrder.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');

UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.general';
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'delivery.deliveryApplicationPushDownConfig.transferOrder.general';

-- 生产工单下推生产工单用料清单配置优化
UPDATE `dfs_order_push_down_config_value` SET `value_name` = '用料类型' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.materialChoose';
UPDATE `dfs_order_push_down_config_value_dict` SET `value_name` = '用料类型' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.materialChoose';
UPDATE `dfs_order_push_down_config_value` SET `option_values` = '["product","purchase","outsourcing"]' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts';
UPDATE `dfs_order_push_down_config_value_dict` SET `option_values` = '["product","purchase","outsourcing"]' WHERE `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts';
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES ('filterMaterialTypes', '物料类型过滤', 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialTypes', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL, 'targetConf');

-- 仓库部分下推配置从软参移至 单据下推配置中
-- 销售出库单下推销售退货入库单
DELETE FROM `dfs_business_config_value` WHERE `value_full_path_code` in ('saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage.enable','saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage.originalOrderStates');
DELETE FROM `dfs_business_config` WHERE `full_path_code` in ('saleInAndOut','saleInAndOut.saleOutPushDownConfig','saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc','saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage');

INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('warehouse', '仓库模块', 'warehouse', '', NULL, NULL, NULL, NULL, NULL, 1, 0, NULL, NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('saleOutPushDownConfig', '销售出库单下推配置', 'saleInAndOut.saleOutPushDownConfig', 'warehouse', NULL, NULL, NULL, NULL, NULL, 1, 0, 'salesIssueDoc', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('salesReturnReceiptDoc', '销售退货入库单', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc', 'saleInAndOut.saleOutPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'salesReturnReceiptDoc', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', '通用下推', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.originalOrderStates', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":1,\"label\":\"生效\"},{\"value\":2,\"label\":\"完成\"}]', '[1,2]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.isSupportMaterialLineReversePushDown', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.description', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.targetOrderState', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.applicationId', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.url', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc?pushDownOrigin=saleOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.sourceOrderType', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.targetOrderType', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"salesReturnReceiptDoc\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.originalOrderStates', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":1,\"label\":\"生效\"},{\"value\":2,\"label\":\"完成\"}]', '[1,2]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.isSupportMaterialLineReversePushDown', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.description', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.targetOrderState', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.applicationId', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.url', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc?pushDownOrigin=saleOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.sourceOrderType', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOut\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general.targetOrderType', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"salesReturnReceiptDoc\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc', NULL, '下推销售退货入库单', 'RULE', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general';
-- 采购入库单下推采购退料出库单
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('purchaseInPushDownConfig', '采购入库单下推配置', 'purchaseInAndOut.purchaseInPushDownConfig', 'warehouse', NULL, NULL, NULL, NULL, NULL, 1, 0, 'purchaseIn', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('purchaseReturnOut', '采购退料出库单(当前版本暂不支持)', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut', 'purchaseInAndOut.purchaseInPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'purchaseReturnOut', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', '通用下推', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.originalOrderStates', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":3,\"label\":\"完成\"}]', '[3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.isSupportMaterialLineReversePushDown', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.description', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.targetOrderState', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.applicationId', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.purchaseOutOfStorage.purchaseReturnOutOfStorage\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.url', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage?pushDownOrigin=purchaseIn\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.sourceOrderType', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseIn\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.targetOrderType', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReturnOut\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.originalOrderStates', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":3,\"label\":\"完成\"}]', '[3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.isSupportMaterialLineReversePushDown', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.description', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.targetOrderState', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.applicationId', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.purchaseOutOfStorage.purchaseReturnOutOfStorage\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.url', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage?pushDownOrigin=purchaseIn\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.sourceOrderType', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseIn\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general.targetOrderType', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseReturnOut\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut', NULL, '下推采购退料出库单', 'RULE', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.general';

-- 委外领料出库单下推委外退料入库单
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('subcontractProductOutboundPushDownConfig', '委外领料出库单下推配置', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig', 'warehouse', NULL, NULL, NULL, NULL, NULL, 1, 0, 'subcontractProductOutbound', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('subcontractReturnReceipt', '委外退料入库单(当前版本暂不支持)', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'subcontractReturnReceipt', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', '通用下推', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.originalOrderStates', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":3,\"label\":\"完成\"}]', '[3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.isSupportMaterialLineReversePushDown', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.description', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.targetOrderState', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.applicationId', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.refundMaterail\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.url', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/subcontracting/refundMaterail?pushDownOrigin=subcontractProductOutbound\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.sourceOrderType', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductOutbound\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.targetOrderType', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReturnReceipt\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.originalOrderStates', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":3,\"label\":\"完成\"}]', '[3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.isSupportMaterialLineReversePushDown', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.description', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.targetOrderState', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.applicationId', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.refundMaterail\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.url', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/subcontracting/refundMaterail?pushDownOrigin=subcontractProductOutbound\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.sourceOrderType', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductOutbound\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general.targetOrderType', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReturnReceipt\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt', NULL, '下推委外退料入库单', 'RULE', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.general';

-- 委外补料出库单下推委外退料入库单
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('subcontractSupplementaryFoodPushDownConfig', '委外补料出库单下推配置', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig', 'warehouse', NULL, NULL, NULL, NULL, NULL, 1, 0, 'subcontractSupplementaryFood', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('subcontractReturnReceipt', '委外退料入库单(当前版本暂不支持)', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'subcontractReturnReceipt', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', '通用下推', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.originalOrderStates', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":3,\"label\":\"完成\"}]', '[3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.isSupportMaterialLineReversePushDown', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.description', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.targetOrderState', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.applicationId', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.refundMaterail\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.url', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/subcontracting/refundMaterail?pushDownOrigin=subcontractSupplementaryFood\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.sourceOrderType', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractSupplementaryFood\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.targetOrderType', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReturnReceipt\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.originalOrderStates', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":3,\"label\":\"完成\"}]', '[3]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.isSupportMaterialLineReversePushDown', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.description', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.targetOrderState', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.applicationId', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.refundMaterail\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.url', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/wms/warehousingDistribution/subcontracting/refundMaterail?pushDownOrigin=subcontractSupplementaryFood\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.sourceOrderType', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractSupplementaryFood\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general.targetOrderType', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReturnReceipt\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt', NULL, '下推委外退料入库单', 'RULE', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.general';

-- 其他出库申请单下推调拨单
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('stockOtherAppFormPushDownConfig', '其他出库申请单下推配置', 'otherInAndOut.stockOtherAppFormPushDownConfig', 'warehouse', NULL, NULL, NULL, NULL, NULL, 1, 0, 'stockOtherAppForm', NULL, NULL, 'wms');
INSERT INTO `dfs_order_push_down_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('stockAllocation', '调拨单(当前版本暂不支持)', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation', 'otherInAndOut.stockOtherAppFormPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'transferOrder', NULL, NULL, 'wms');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.general', '通用下推', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc');

INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.originalOrderStates', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":1,\"label\":\"生效\"},{\"value\":2,\"label\":\"完成\"}]', '[1,2]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.isSupportMaterialLineReversePushDown', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.description', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.targetOrderState', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.applicationId', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.cougnyManage.allocation\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.url', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/cougnyManage/allocation/add?pushDownOrigin=productMaterialsList?pushDownOrigin=stockOtherAppForm\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.sourceOrderType', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"stockOtherAppForm\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.targetOrderType', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"transferOrder\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.originalOrderStates', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":1,\"label\":\"生效\"},{\"value\":2,\"label\":\"完成\"}]', '[1,2]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.isSupportMaterialLineReversePushDown', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.description', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.targetOrderState', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.applicationId', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.cougnyManage.allocation\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.url', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/cougnyManage/allocation/add?pushDownOrigin=productMaterialsList?pushDownOrigin=stockOtherAppForm\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.sourceOrderType', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"stockOtherAppForm\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general.targetOrderType', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"transferOrder\"', NULL, 'sysConf');

INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation', NULL, '下推调拨单', 'RULE', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.general';

-- 多级bom导出
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10802330', '导出多级BOM基础数据excel', 'bom:export.multiBomBaseDataExcel', NULL, NULL, NULL, NULL, '2024-12-03 16:03:47', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10802340', '查看多级BOM基础数据日志', 'bom:export.multiBomBaseDataLog', NULL, NULL, NULL, NULL, '2024-12-03 16:03:47', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10802350', '导出多级BOM详细数据excel', 'bom:export.multiBomDetailDataExcel', NULL, NULL, NULL, NULL, '2024-12-03 16:03:47', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10802360', '查看多级BOM详细数据日志', 'bom:export.multiBomDetailDataLog', NULL, NULL, NULL, NULL, '2024-12-03 16:03:47', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
call init_new_role_permission('10802330');
call init_new_role_permission('10802340');
call init_new_role_permission('10802350');
call init_new_role_permission('10802360');

-- 供应类别
call proc_add_column(
        'dfs_supplier',
        'category',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `category`varchar(255) NOT NULL COMMENT ''供应类别''');
update `dfs_supplier` set `category` = 'COMPREHENSIVE';

-- 将单据类型调整到单据下推配置下
UPDATE `sys_route` SET `sort` = 3, `sec_sort` = 3 WHERE `path` = '/documents-config/order-type';

-- 销售退货单增加业务类型单据类型字段
call proc_add_column(
        'dfs_delivery_application',
        'order_type',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `order_type`  varchar(255) DEFAULT null    COMMENT ''单据类型'' AFTER `sale_order_number`;');

call proc_add_column(
        'dfs_delivery_application',
        'business_type',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `business_type`  varchar(255) DEFAULT null    COMMENT ''业务类型'' AFTER `order_type`;');
update dfs_delivery_application set order_type = 'standardDeliveryApplication' where order_type is null;
update dfs_delivery_application set business_type = 'standardDeliveryApplication' where business_type is null;


-- 增加销售订单列配置信息
delete from dfs_form_field_rule_config where full_path_code like 'saleOrder%'  and field_code = 'saleOrderTypeName';
delete from dfs_form_field_config where full_path_code like 'saleOrder%'  and field_code = 'saleOrderTypeName';
delete from dfs_form_field_rule_config where full_path_code like 'saleOrder%'  and field_code = 'saleOrderType';
delete from dfs_form_field_config where full_path_code like 'saleOrder%'  and field_code = 'saleOrderType';
call proc_add_form_field("saleOrder.list.order", "saleOrderTypeName", "单据类型");
call proc_add_form_field("saleOrder.list.order", "businessTypeName", "业务类型");

call proc_add_form_field("saleOrder.list.material", "saleOrderTypeName", "单据类型");
call proc_add_form_field("saleOrder.list.material", "businessTypeName", "业务类型");

call proc_add_form_field("saleOrder.edit", "saleOrderType", "单据类型");
call proc_add_form_field("saleOrder.edit", "businessType", "业务类型");

call proc_add_form_field("saleOrder.detail", "saleOrderType", "单据类型");
call proc_add_form_field("saleOrder.detail", "businessType", "业务类型");


INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.list.order', 'saleOrder.list.order', 'saleOrderTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.list.order', 'saleOrder.list.order', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.list.material', 'saleOrder.list.material', 'saleOrderTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.list.material', 'saleOrder.list.material', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByCreate', 'saleOrder.edit', 'saleOrderType', 1, 1, 1, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByCreate', 'saleOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByRelease', 'saleOrder.edit', 'saleOrderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByRelease', 'saleOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByFinish', 'saleOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByFinish', 'saleOrder.edit', 'saleOrderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByClosed', 'saleOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByClosed', 'saleOrder.edit', 'saleOrderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByCancel', 'saleOrder.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.editByCancel', 'saleOrder.edit', 'saleOrderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.detail', 'saleOrder.detail', 'saleOrderType', 1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.detail', 'saleOrder.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);


-- 增加生产订单列配置信息
delete from dfs_form_field_rule_config where full_path_code like 'productOrder%'  and field_code = 'type';
delete from dfs_form_field_config where full_path_code like 'productOrder%'  and field_code = 'type';
delete from dfs_form_field_rule_config where full_path_code like 'productOrder%'  and field_code = 'typeName';
delete from dfs_form_field_config where full_path_code like 'productOrder%'  and field_code = 'typeName';
call proc_add_form_field("productOrder.list", "orderTypeName", "单据类型");
call proc_add_form_field("productOrder.list", "typeName", "业务类型");

call proc_add_form_field("productOrder.edit", "orderType", "单据类型");
call proc_add_form_field("productOrder.edit", "type", "业务类型");

call proc_add_form_field("productOrder.detail", "orderType", "单据类型");
call proc_add_form_field("productOrder.detail", "type", "业务类型");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.list', 'productOrder.list', 'orderTypeName',1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.list', 'productOrder.list', 'typeName',1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByCreate', 'productOrder.edit', 'orderType',1, 1, 1, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByCreate', 'productOrder.edit', 'type',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByRelease', 'productOrder.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByRelease', 'productOrder.edit', 'type',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByFinish', 'productOrder.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByFinish', 'productOrder.edit', 'type',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByClosed', 'productOrder.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByClosed', 'productOrder.edit', 'type',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByCancel', 'productOrder.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.editByCancel', 'productOrder.edit', 'type',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.detail', 'productOrder.detail', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/product-management/supplies', 'productOrder.detail', 'productOrder.detail', 'type',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);


-- 增加生产订单用料清单列配置信息
call proc_add_form_field("productOrderMaterialList.list.order", "orderTypeName", "单据类型");
call proc_add_form_field("productOrderMaterialList.list.order", "businessTypeName", "业务类型");
call proc_add_form_field("productOrderMaterialList.list.material", "orderTypeName", "单据类型");
call proc_add_form_field("productOrderMaterialList.list.material", "businessTypeName", "业务类型");
call proc_add_form_field("productOrderMaterialList.edit", "orderType", "单据类型");
call proc_add_form_field("productOrderMaterialList.edit", "businessType", "业务类型");
call proc_add_form_field("productOrderMaterialList.detail", "orderType", "单据类型");
call proc_add_form_field("productOrderMaterialList.detail", "businessType", "业务类型");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.list.order', 'productOrderMaterialList.list.order', 'orderTypeName',1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.list.order', 'productOrderMaterialList.list.order', 'businessTypeName',1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.list.material', 'productOrderMaterialList.list.material', 'orderTypeName',1, 1, 1, 0, NULL, NULL, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.list.material', 'productOrderMaterialList.list.material', 'businessTypeName',1, 1, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByCreate', 'productOrderMaterialList.edit', 'orderType',1, 1, 1, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByCreate', 'productOrderMaterialList.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByRelease', 'productOrderMaterialList.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByRelease', 'productOrderMaterialList.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByFinish', 'productOrderMaterialList.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByFinish', 'productOrderMaterialList.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByClosed', 'productOrderMaterialList.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.editByClosed', 'productOrderMaterialList.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.detail', 'productOrderMaterialList.detail', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/production-materials', 'productOrderMaterialList.detail', 'productOrderMaterialList.detail', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 增加销售出货单列配置信息
call proc_add_form_field("deliveryApplication.list.material", "orderTypeName", "单据类型");
call proc_add_form_field("deliveryApplication.list.material", "businessTypeName", "业务类型");
call proc_add_form_field("deliveryApplication.edit", "orderType", "单据类型");
call proc_add_form_field("deliveryApplication.edit", "businessType", "业务类型");
call proc_add_form_field("deliveryApplication.detail", "orderType", "单据类型");
call proc_add_form_field("deliveryApplication.detail", "businessType", "业务类型");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.list.material', 'deliveryApplication.list.material', 'orderTypeName',1, 1, 1, 0, NULL, NULL, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.list.material', 'deliveryApplication.list.material', 'businessTypeName',1, 1, 1, 0, NULL, NULL, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCreate', 'deliveryApplication.edit', 'orderType',1, 1, 1, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCreate', 'deliveryApplication.edit', 'businessType',1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByRelease', 'deliveryApplication.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByRelease', 'deliveryApplication.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByFinish', 'deliveryApplication.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByFinish', 'deliveryApplication.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByClosed', 'deliveryApplication.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByClosed', 'deliveryApplication.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCancel', 'deliveryApplication.edit', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.editByCancel', 'deliveryApplication.edit', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.detail', 'deliveryApplication.detail', 'orderType',1, 1, 0, 0, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/order-model/shipment_application', 'deliveryApplication.detail', 'deliveryApplication.detail', 'businessType',0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 单据下推配置删除采购退料申请单下推，领料申请单下推
DELETE FROM `dfs_order_push_down_config` WHERE `full_path_code` IN ('purchase.purchaseReturnApplicationPushDownConfig', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut', 'production.takeOutApplicationPushDownConfig', 'production.takeOutApplicationPushDownConfig.takeOutOutbound');
DELETE FROM `dfs_order_push_down_config_value` WHERE `config_full_path_code` IN ('purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage');
DELETE FROM `dfs_order_push_down_config_value_dict` WHERE `config_full_path_code` IN ('purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage');
DELETE FROM `dfs_dict` WHERE `type` IN ('purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut');
-- 物料附加属性改名为条码扩展字段
update sys_route set title = '条码扩展字段' where title = '物料附加属性';
update sys_permissions set name = '条码扩展字段' where name = '物料附加属性';

-- 处理sys_permissions表parent_path与parent_id不匹配问题
UPDATE sys_permissions permissions JOIN sys_permissions parent ON permissions.parent_id = parent.id SET permissions.parent_path = parent.path WHERE permissions.parent_id IS NOT NULL;
-- 客户档案 导入按钮名称与前端不一致
update `sys_permissions` set `name` = '导入管理(下载导入模板)' WHERE id = '1090101110';

-- 采购退料申请批量审批权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208090', '批量审批', 'return:application:approvalBatch', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
call init_new_role_permission('1090208090');
delete FROM sys_permissions where id = '1090209060';
delete FROM sys_permissions where id = '1090204050';
update sys_permissions  set name = '导出列表'  where id = 'dfs1020902';
update sys_permissions  set name = '导出'  where id = '1090206090';
update dfs_order_push_down_config  set name = '销售出货单'  where full_path_code = 'saleOrder.pushDownConfig.deliveryApplication';
UPDATE dfs_form_field_config
SET field_name = REPLACE(field_name, '出货申请', '销售出货')
WHERE full_path_code like 'deliveryApplication%' and  field_name LIKE '%出货申请单%';
update dfs_form_field_config set field_name= '销售订单编号'  where full_path_code in ('deliveryApplication.edit','deliveryApplication.detail') and  field_code = 'saleOrderNumber' and field_name = '订单编号';
update dfs_form_field_rule_config  set is_need = 0  where full_path_code = 'deliveryApplication.editByRelease' and  field_code in ( 'updateBy','updateTime');


INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206170', '下载默认转换模板', 'delivery.order:export-default-template', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206180', '下载自定义转换模板', 'delivery.order:export-custom-template', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206190', '上传自定义转换模板', 'delivery.order:import-custom-template', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206200', '下载导入模板', 'delivery.order:export-template', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206210', '导入数据', 'delivery.order:import-data', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206220', '查看日志', 'delivery.order:import-log', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
call init_new_role_permission('1090206170');
call init_new_role_permission('1090206180');
call init_new_role_permission('1090206190');
call init_new_role_permission('1090206200');
call init_new_role_permission('1090206210');
call init_new_role_permission('1090206220');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206230', '导出_下载默认模板', 'delivery.order:downDefaultTemplate', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206240', '导出_上传下载自定义导出模板', 'delivery.order:downUploadTemplate', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206250', '导出_导出excel', 'delivery.order:export:excel', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('1090206260', '导出_查看日志', 'delivery.order:view:log', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090206', '2', '1', '0', '/supply-chain-collaboration/procurement-management/delivery-order', '1', NULL, '');
call init_new_role_permission('1090206230');
call init_new_role_permission('1090206240');
call init_new_role_permission('1090206250');
call init_new_role_permission('1090206260');


-- 将saleInAndOut 的下推配置修改为salesIssueDoc
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"salesIssueDoc\"' WHERE `value` = '\"saleInAndOut\"';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"salesIssueDoc\"' WHERE `value` = '\"saleInAndOut\"';
UPDATE `dfs_order_push_down_config` SET `type_code` = 'workOrder' WHERE `full_path_code` = 'saleOrder.pushDownConfig.workOrder';

-- 替代方案的物料分子分母修改为6位小数
call proc_modify_column(
        'dfs_replace_scheme',
        'main_molecule',
        'ALTER TABLE `dfs_replace_scheme` MODIFY COLUMN `main_molecule` double(11,6) NULL DEFAULT ''1.0'' COMMENT ''主用量：分子'';');
call proc_modify_column(
        'dfs_replace_scheme',
        'main_denominator',
        'ALTER TABLE `dfs_replace_scheme` MODIFY COLUMN `main_denominator` double(11,6) NULL DEFAULT ''1.0'' COMMENT ''主用量：分母'';');

call proc_modify_column(
        'dfs_replace_scheme_material',
        'replace_molecule',
        'ALTER TABLE `dfs_replace_scheme_material` MODIFY COLUMN `replace_molecule` double(11,6) NULL DEFAULT ''1.0'' COMMENT ''替代用量：分子'';');
call proc_modify_column(
        'dfs_replace_scheme_material',
        'replace_denominator',
        'ALTER TABLE `dfs_replace_scheme_material` MODIFY COLUMN `replace_denominator` double(11,6) NULL DEFAULT ''1.0'' COMMENT ''替代用量：分母'';');

-- 工单不合格数、工单报工记录的不合格数需要保留6位小数
call proc_modify_column(
        'dfs_report_line',
        'unqualified',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `unqualified` double(11,6) NULL DEFAULT ''0.00'' COMMENT ''不合格数量'';');
call proc_modify_column(
        'dfs_work_order',
        'unqualified',
        'ALTER TABLE `dfs_work_order` MODIFY COLUMN `unqualified` double(11,6) NULL DEFAULT ''0.00'' COMMENT ''不合格数量'';');

-- 修改仓库下推的源单状态
update `dfs_business_config_value` set value = '[3]' where `value_full_path_code` = 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage.originalOrderStates';
update `dfs_business_config_value` set value = '[3]' where `value_full_path_code` = 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage.originalOrderStates';

UPDATE `dfs_order_push_down_config_value_dict` SET `option_values` = '[\"purchase\",\"outsourcing\",\"product\"]' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialSorts';
UPDATE `dfs_order_push_down_config_value` SET `option_values` = '[\"purchase\",\"outsourcing\",\"product\"]' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialSorts';

-- 单据回退 物料名称
call proc_add_column(
        'dfs_delete_record',
        'material_name',
        'ALTER TABLE `dfs_delete_record` ADD COLUMN `material_name` varchar(255) DEFAULT NULL COMMENT ''物料名称'';');
UPDATE dfs_delete_record d INNER JOIN dfs_material m ON d.material_code = m.`code` set d.material_name = m.`name`;


-- 单品追溯增加导出日志权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES
('10205100', '查看日志', 'trace.single:exportLog', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1, NULL, '', 1);
call init_new_role_permission('10205100');


-- 系统还原-》配置初始化
delete from sys_permissions where id ='12209070';
delete from sys_role_permission where permission_id='12209070';
update sys_permissions set name ='配置初始化' where id='12209';

-- 生产订单用料清单
UPDATE `dfs_form_field_config` SET `field_name` = '单位' WHERE `field_name` = '物料物料单位';

-- 供应商编辑权限
update dfs_form_field_rule_config set is_edit = 1 WHERE full_path_code = 'supplier.edit.byCreate' and field_code = 'code';