--  ***********下推配置***********
-- 树节点
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('reportCollect', '生产汇总', 'production.workOrderPushDownConfig.reportCollect', 'production.workOrderPushDownConfig', NULL, NULL, NULL, NULL, NULL, 1, 0, 'workOrder', NULL, NULL, '');
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`, `is_show`, `type_code`, `app_id`, `url`, `service_name`) VALUES ('workOrderReport', '生产入库单', 'production.workOrderPushDownConfig.reportCollect.workOrderReport', 'production.workOrderPushDownConfig.reportCollect', NULL, NULL, NULL, NULL, NULL, 1, 1, 'workOrder', NULL, NULL, '');


-- dict
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('production.workOrderPushDownConfig.reportCollect.workOrderReport.general', '通用下推', 'production.workOrderPushDownConfig.reportCollect.workOrderReport');
INSERT INTO `dfs_dict`(`code`, `name`, `type`) VALUES ('custom', '自定义', 'production.workOrderPushDownConfig.reportCollect.workOrderReport');

-- config_value_dict
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.originalOrderStates', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[3,4,5]', '[3,4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.description', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.targetOrderState', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.applicationId', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderComplete\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.url', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.sourceOrderType', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.targetOrderType', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionIn\"', NULL, 'sysConf');

-- config_value
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.originalOrderStates', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[3,4,5]', '[3,4,5]', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('isSupportMaterialLineReversePushDown', '选择物料行', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('description', '描述', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.description', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.targetOrderState', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'select', 'api', '/wms/stocks/state/list', 'get', NULL, 'code,name', '[0]', '0', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('applicationId', 'applicationId', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.applicationId', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.productInOrOut.workOrderComplete\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('url', '前端路由', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.url', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete/add?pushDownOrigin=workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.sourceOrderType', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL, 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`) VALUES ('targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general.targetOrderType', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productionIn\"', NULL, 'sysConf');

-- item
INSERT INTO `dfs_order_push_down_item`(`config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('production.workOrderPushDownConfig.reportCollect.workOrderReport', NULL, '下推生产入库单', 'RULE', 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general', NULL, 1, 1, 1, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
-- 更新关系
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.reportCollect.workOrderReport.general';