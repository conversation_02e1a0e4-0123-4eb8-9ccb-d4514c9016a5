-- DDL

-- 表结构定义表
CREATE TABLE IF NOT EXISTS `dfs_table_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `table_schema` varchar(255) DEFAULT NULL COMMENT '数据库',
    `table_name` varchar(255) NOT NULL COMMENT '表名',
    `table_remark` varchar(255) DEFAULT NULL COMMENT '表注释',
    `field_code` varchar(255) NOT NULL COMMENT '字段编码',
    `field_name` varchar(255) NOT NULL COMMENT '字段中文名',
    `field_type` varchar(100) NOT NULL COMMENT '字段类型',
    `field_length` int(11) DEFAULT NULL COMMENT '字段长度',
    `field_point` int(11) DEFAULT NULL COMMENT '小数点',
    `field_default_value` varchar(255) DEFAULT NULL COMMENT '默认值',
    `field_remark` varchar(1000) DEFAULT NULL COMMENT '字段备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_code` (`table_schema`,`table_name`,`field_code`) USING BTREE,
    KEY `schema_table_name` (`table_schema`,`table_name`) USING BTREE,
    KEY `field_code` (`field_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表结构定义表';

-- 创建指标模型与表的关联表
CREATE TABLE IF NOT EXISTS `dfs_target_model_table_relate` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `target_name` varchar(255) DEFAULT NULL COMMENT '指标名',
     `model_id` int(11) DEFAULT NULL COMMENT '模型id',
     `table_schema` varchar(255) DEFAULT NULL COMMENT '所属数据库',
     `table_name` varchar(255) DEFAULT NULL COMMENT '所属表名',
     PRIMARY KEY (`id`),
     UNIQUE KEY `target_name_model_id` (`target_name`,`model_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='指标模型与表的关联表';

-- 创建表索引定义表
CREATE TABLE IF NOT EXISTS `dfs_table_index_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `table_schema` varchar(255) DEFAULT NULL COMMENT '数据库',
    `table_name` varchar(255) NOT NULL COMMENT '表名',
    `index_name` varchar(255) DEFAULT NULL COMMENT '索引名称',
    `index_field` varchar(255) NOT NULL COMMENT '索引字段(多个按英文逗号分隔)',
    `index_type` varchar(255) NOT NULL COMMENT '索引类型',
    PRIMARY KEY (`id`),
    KEY `schema_table_name` (`table_schema`,`table_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表索引定义表';


-- target_model新增`执行脚本`字段
call proc_add_column(
        'dfs_target_model',
        'script',
        'ALTER TABLE `dfs_target_model` ADD COLUMN `script` varchar(2000) NULL DEFAULT NULL COMMENT ''执行脚本'';');

-- target_dict新增`模型编码`字段
call proc_add_column(
        'dfs_target_dict',
        'model_code',
        'ALTER TABLE `dfs_target_dict` ADD COLUMN `model_code` varchar(255) NULL DEFAULT NULL COMMENT ''模型编码'' AFTER `model_type`;');

call proc_add_column(
        'dfs_page_redirect',
        'tag',
        'ALTER TABLE `dfs_page_redirect` ADD COLUMN `tag` varchar(255) DEFAULT NULL COMMENT ''标记'';');

CREATE TABLE IF NOT EXISTS `dfs_manage_source` (
    `source_id` int(11) NOT NULL AUTO_INCREMENT,
    `source_code` varchar(255) NOT NULL COMMENT '数据源编码',
    `source_name` varchar(255) NOT NULL COMMENT '数据源名称',
    `source_type` varchar(255) NOT NULL DEFAULT '1' COMMENT '来源类型，dfs自定义数据',
    `table_schema` varchar(255) DEFAULT NULL COMMENT '所属数据库',
    `table_name` varchar(255) DEFAULT NULL COMMENT '所属表名',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_by` varchar(255) NOT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`source_id`),
    UNIQUE KEY `unique_code` (`source_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表数据源';

-- 修改字段的类型大小
call proc_modify_column(
        'dfs_manage_report',
        'data_sources',
        'ALTER TABLE `dfs_manage_report` MODIFY COLUMN `data_sources`  text NOT NULL COMMENT ''配置的数据源,多个逗号分割'' AFTER `model`;');
call proc_modify_column(
        'dfs_manage_report',
        'data_source_config',
        'ALTER TABLE `dfs_manage_report` MODIFY COLUMN `data_source_config`  longtext NULL COMMENT ''数据源的配置'' AFTER `data_sources`;');



-- DML

-- 更新模型表
UPDATE `dfs_model` SET `code` = `type`  WHERE `type` in ("saleOrderTarget", "workOrderTarget", "qualityTarget", "productOrderTarget", "baseTeam", "materialTarget", "maintainTarget", "gz", "topTarget");
UPDATE `dfs_model` SET `type` = 'baseTargetGroupObject' WHERE `type` in ("saleOrderTarget", "workOrderTarget", "qualityTarget", "productOrderTarget", "baseTeam", "materialTarget", "maintainTarget", "gz", "topTarget");
UPDATE `dfs_model` SET `type` = 'baseDevice' WHERE `code` = "devicecode";
-- 调整更新指标字典数据
UPDATE `dfs_target_dict` SET `model_code` = `model_type` WHERE `model_type` in ("saleOrderTarget", "workOrderTarget", "qualityTarget", "productOrderTarget", "baseTeam", "materialTarget", "maintainTarget", "gz", "topTarget");
UPDATE `dfs_target_dict` SET `model_type` = "baseTargetGroupObject" WHERE `model_code` in ("saleOrderTarget", "workOrderTarget", "qualityTarget", "productOrderTarget", "baseTeam", "materialTarget", "maintainTarget", "gz", "topTarget");
-- 调整更新指标方法数据
UPDATE `dfs_target_method` SET `model_type` = "baseTargetGroupObject" WHERE `model_type` in ("saleOrderTarget", "workOrderTarget", "qualityTarget", "productOrderTarget", "baseTeam", "materialTarget", "maintainTarget", "gz", "topTarget");
-- 更正指标名
UPDATE `dfs_target_dict` SET `target_cnname` = '销售订单每月' WHERE `target_name` = 'saleOrderSummaryMonthly';
UPDATE `dfs_target_model` SET `target_cnname` = '销售订单每月' WHERE `target_name` = 'saleOrderSummaryMonthly';
-- 模型表新增自定义指标组对象
INSERT INTO `dfs_model`(`id`, `type`, `name`, `model_img`, `simulation_img`, `code`, `seq`, `batch_number`, `pid`, `theory_efficiency`, `working_hours`, `update_by`, `create_by`, `update_time`, `create_time`, `remark`) VALUES (NULL, 'baseTargetGroupObject', '自定义指标组对象', NULL, NULL, 'customTarget', NULL, NULL, -1, NULL, NULL, NULL, NULL, '2024-06-24 10:04:47', NULL, NULL);


-- 采购需求新增`客户编码`、`客户名称`的表单配置
call proc_add_form_field("purchaseRequestOrder.list", "customerCode", "客户编码");
call proc_add_form_field("purchaseRequestOrder.list", "customerName", "客户名称");
call proc_add_form_field("purchaseRequestOrder.add", "customerCode", "客户编码");
call proc_add_form_field("purchaseRequestOrder.add", "customerName", "客户名称");
call proc_add_form_field("purchaseRequestOrder.edit", "customerCode", "客户编码");
call proc_add_form_field("purchaseRequestOrder.edit", "customerName", "客户名称");
call proc_add_form_field("purchaseRequestOrder.detail", "customerCode", "客户编码");
call proc_add_form_field("purchaseRequestOrder.detail", "customerName", "客户名称");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.add', 'purchaseRequestOrder.add', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', 'purchaseRequestOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', 'purchaseRequestOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', 'purchaseRequestOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', 'purchaseRequestOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', 'purchaseRequestOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

-- 采购订单新增`客户编码`、`客户名称`的表单配置
call proc_add_form_field("purchaseOrder.list.material", "customerCode", "客户编码");
call proc_add_form_field("purchaseOrder.list.material", "customerName", "客户名称");
call proc_add_form_field("purchaseOrder.add", "customerCode", "客户编码");
call proc_add_form_field("purchaseOrder.add", "customerName", "客户名称");
call proc_add_form_field("purchaseOrder.edit", "customerCode", "客户编码");
call proc_add_form_field("purchaseOrder.edit", "customerName", "客户名称");
call proc_add_form_field("purchaseOrder.detail", "customerCode", "客户编码");
call proc_add_form_field("purchaseOrder.detail", "customerName", "客户名称");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.list.material', 'purchaseOrder.list.material', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.list.material', 'purchaseOrder.list.material', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.add', 'purchaseOrder.add', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.add', 'purchaseOrder.add', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByCreate', 'purchaseOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByCreate', 'purchaseOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByRelease', 'purchaseOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByRelease', 'purchaseOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByFinish', 'purchaseOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByFinish', 'purchaseOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByClosed', 'purchaseOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByClosed', 'purchaseOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByCancel', 'purchaseOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByCancel', 'purchaseOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.detail', 'purchaseOrder.detail', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.detail', 'purchaseOrder.detail', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[]', NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

-- 敏感字段权限配置基础数据
INSERT INTO `sys_field_permissions`(`unique_code`, `code`, `default_name`, `custom_name`, `route`, `parent_code`, `is_enable`, `service_name`, `create_by`, `create_time`) VALUES ('purchase_customerCode', 'customerCode', '客户编码', NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchase', 1, 'ams', 'admin', '2024-05-28 11:32:54');
INSERT INTO `sys_field_permissions`(`unique_code`, `code`, `default_name`, `custom_name`, `route`, `parent_code`, `is_enable`, `service_name`, `create_by`, `create_time`) VALUES ('purchase_customerName', 'customerName', '客户名称', NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchase', 1, 'ams', 'admin', '2024-05-28 11:32:54');
INSERT INTO `sys_field_permissions`(`unique_code`, `code`, `default_name`, `custom_name`, `route`, `parent_code`, `is_enable`, `service_name`, `create_by`, `create_time`) VALUES ('purchaseRequest', 'purchaseRequest', '采购需求', NULL, NULL, NULL, 1, 'ams', 'admin', '2024-05-28 11:32:54');
INSERT INTO `sys_field_permissions`(`unique_code`, `code`, `default_name`, `custom_name`, `route`, `parent_code`, `is_enable`, `service_name`, `create_by`, `create_time`) VALUES ('purchase_request_customerCode', 'customerCode', '客户编码', NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequest', 1, 'ams', 'admin', '2024-05-28 11:32:54');
INSERT INTO `sys_field_permissions`(`unique_code`, `code`, `default_name`, `custom_name`, `route`, `parent_code`, `is_enable`, `service_name`, `create_by`, `create_time`) VALUES ('purchase_request_customerName', 'customerName', '客户名称', NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequest', 1, 'ams', 'admin', '2024-05-28 11:32:54');

--  更新单据敏感字段的表单配置备注
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1, `remark` = '该字段和敏感字段配置冲突，请以敏感字段配置为准，此字段置灰，仅允许重命名 (冲突位置：人员信息-->角色权限-->权限分配-->单据敏感字段权限维护)' WHERE `field_code` = 'customerCode' AND `route` = '/supply-chain-collaboration/procurement-management/purchasing-list';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1, `remark` = '该字段和敏感字段配置冲突，请以敏感字段配置为准，此字段置灰，仅允许重命名 (冲突位置：人员信息-->角色权限-->权限分配-->单据敏感字段权限维护)' WHERE `field_code` = 'customerName' AND `route` = '/supply-chain-collaboration/procurement-management/purchasing-list';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1, `remark` = '该字段和敏感字段配置冲突，请以敏感字段配置为准，此字段置灰，仅允许重命名 (冲突位置：人员信息-->角色权限-->权限分配-->单据敏感字段权限维护)' WHERE `field_code` = 'customerCode' AND `route` = '/supply-chain-collaboration/procurement-management/purchasing-demand';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1, `remark` = '该字段和敏感字段配置冲突，请以敏感字段配置为准，此字段置灰，仅允许重命名 (冲突位置：人员信息-->角色权限-->权限分配-->单据敏感字段权限维护)' WHERE `field_code` = 'customerName' AND `route` = '/supply-chain-collaboration/procurement-management/purchasing-demand';


INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES
('1090405060', '列配置_列表', 'outsourcingManagement.outsourcingReturnOrder:config', NULL, NULL, NULL, NULL, '2022-11-11 01:31:02', 'enable', 'GET', '1090405', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 1);
call init_new_role_permission('1090405060');

-- 销售订单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'saleOrder.listConfig.listColor.listDisplayFormat', 'saleOrder.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'saleOrder.listConfig.listDisplayFormat.materialOrOrder', 'saleOrder.listConfig.listColor.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);

-- 生产订单用料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '生产订单用料清单列表配置', 'production.productMaterialsList.listConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'production.productMaterialsList.listConfig.listDisplayFormat', 'production.productMaterialsList.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'production.productMaterialsList.listConfig.listDisplayFormat.materialOrOrder', 'production.productMaterialsList.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);
-- 生产工单用料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '生产工单用料清单列表配置', 'production.workerMaterialsList.listConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'production.workerMaterialsList.listConfig.listDisplayFormat', 'production.workerMaterialsList.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'production.workerMaterialsList.listConfig.listDisplayFormat.materialOrOrder', 'production.workerMaterialsList.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);
-- 采购订单列表配置料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '采购订单列表配置', 'purchase.purchaseOrder.listConfig', 'purchase', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'purchase.purchaseOrder.listConfig.listDisplayFormat', 'purchase.purchaseOrder.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'purchase.purchaseOrder.listConfig.listDisplayFormat.materialOrOrder', 'purchase.purchaseOrder.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);

-- 采购收料列表配置料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'purchase.receiptOrder.listConfig.listDisplayFormat', 'purchase.receiptOrder.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'purchase.receiptOrder.listConfig.listDisplayFormat.materialOrOrder', 'purchase.receiptOrder.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);

-- 采购退料申请单列表配置料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '采购退料申请单列表配置', 'purchase.purchaseReturnApplication.listConfig', 'purchase', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'purchase.purchaseReturnApplication.listConfig.listDisplayFormat', 'purchase.purchaseReturnApplication.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'purchase.purchaseReturnApplication.listConfig.listDisplayFormat.materialOrOrder', 'purchase.purchaseReturnApplication.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);
-- 委外订单列表配置料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '委外订单列表配置', 'subcontract.subcontractOrder.listConfig', 'subcontract', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'subcontract.subcontractOrder.listConfig.listDisplayFormat', 'subcontract.subcontractOrder.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'subcontract.subcontractOrder.listConfig.listDisplayFormat.materialOrOrder', 'subcontract.subcontractOrder.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);
-- 委外订单用料清单列表配置料清单列表展示形式配置
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '委外订单用料清单列表配置', 'subcontract.subcontractMaterialsList.listConfig', 'subcontract', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'listDisplayFormat', '列表展示形式', 'subcontract.subcontractMaterialsList.listConfig.listDisplayFormat', 'subcontract.subcontractMaterialsList.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'materialOrOrder', '按单/物料', 'subcontract.subcontractMaterialsList.listConfig.listDisplayFormat.materialOrOrder', 'subcontract.subcontractMaterialsList.listConfig.listDisplayFormat', 'select', 'api', '/api/common/model/material/order/list', 'get', NULL, 'code,name', NULL, NULL,NULL);


-- 委外订单收料单 按模板导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090402090', '导出_下载默认模板', 'outsourcingManagement.outsourcingReceiptOrder:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090402', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090402100', '导出_上传下载自定义导出模板', 'outsourcingManagement.outsourcingReceiptOrder:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090402', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090402110', '导出_导出excel', 'outsourcingManagement.outsourcingReceiptOrder:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090402', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 1);
call init_new_role_permission('1090402090');
call init_new_role_permission('1090402100');
call init_new_role_permission('1090402110');

-- 委外订单退料 按模板导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090405070', '导出_下载默认模板', 'outsourcingManagement.outsourcingReturnOrder:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090405', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090405080', '导出_上传下载自定义导出模板', 'outsourcingManagement.outsourcingReturnOrder:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090405', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090405090', '导出_导出excel', 'outsourcingManagement.outsourcingReturnOrder:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090405', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 1);
call init_new_role_permission('1090405070');
call init_new_role_permission('1090405080');
call init_new_role_permission('1090405090');


-- 委外订单用料清单-批量编辑、导出权限权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
VALUES ('1090403090', '批量编辑', 'outsourcingMaterialOrder:batchUpdate', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090403', '2', '1', '0', '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', '1', NULL, '', '1');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
VALUES ('1090403110', '导出_下载默认模板', 'outsourcingMaterialOrder:downDefaultTemplate', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090403', '2', '1', '0', '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', '1', NULL, '', '1');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
VALUES ('1090403120', '导出_上传下载自定义导出模板', 'outsourcingMaterialOrder:downUploadTemplate', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090403', '2', '1', '0', '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', '1', NULL, '', '1');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
VALUES ('1090403130', '导出_导出excel', 'outsourcingMaterialOrder:excel', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090403', '2', '1', '0', '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', '1', NULL, '', '1');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
VALUES ('1090403140', '导出_查看日志', 'outsourcingMaterialOrder:log', NULL, NOW(), NULL, NOW(), 'enable', 'GET', '1090403', '2', '1', '0', '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', '1', NULL, '', '1');
call init_new_role_permission('1090403090');
call init_new_role_permission('1090403110');
call init_new_role_permission('1090403120');
call init_new_role_permission('1090403130');
call init_new_role_permission('1090403140');

-- 委外收料单-批量编辑按钮新增权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090402120', '批量编辑', 'outsourcingManagement.outsourcingReceiptOrder:batchUpdate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090402', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 1);
call init_new_role_permission('1090402120');

-- 委外退料单-批量编辑按钮新增权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090405100', '批量编辑', 'outsourcingManagement.outsourcingReturnOrder:batchUpdate', NULL, NULL, NULL, NULL, '2022-11-11 01:31:02', 'enable', 'GET', '1090405', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 1);
call init_new_role_permission('1090405100');

INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`) VALUES ('yelink.kangjuren.maintenance.machine.freeze', '冻结', 'maintenance.station.machine:freeze', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.maintenance.machine', 2, 0, 1, NULL, 1, NULL);



INSERT INTO `dfs_dict`(`code`, `name`, `type`, `url`, `unit`, `des`, `value`, `p_code`, `pid`, `create_time`,
                       `update_time`, `create_by`, `update_by`, `quality_level`, `is_process_assembly`)
VALUES ('packageCode', '包装码', '单品打印', NULL, NULL, 'barCodePrintType', 'show', NULL, NULL, '2024-01-07 17:55:43',
        '2024-02-26 17:35:21', 'admin', 'admin', NULL, 0);

INSERT INTO `dfs_config_label_type`(`type_code`, `type_name`, `apps`, `open_url`)
VALUES ('packageCode', '包装码', '包装工位机', NULL);

INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`)
VALUES ('packageCode', 'packageCode');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`)
VALUES ('packageCode', 'packageOrder');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`)
VALUES ('packageCode', 'materialCode');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`)
VALUES ('packageCode', 'materialName');

INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`)
VALUES ('packageCode', '包装码', "\\$\\{packageCode\\}", 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`)
VALUES ('packageOrderNumber', '包装工单编号', "\\$\\{packageOrderNumber\\}", 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`)
VALUES ('packageOrderName', '包装工单名称', "\\$\\{packageOrderName\\}", 1);


-- 不良定义-不良类型编码规则。 并添加默认编码规则
call init_number_rules(88, '不良定义-不良类型编号');
delete from `dfs_config_number_rules` where `type` = "88";
INSERT INTO `dfs_config_number_rules` (`type`, `name`, `prefix_detail`, `create_by`, `update_by`, `create_time`, `update_time`, `auto_increment_configure_type`, `is_default`)
VALUES ('88', '默认编码规则', '[{\"autoIncrementConfigureType\":\"\",\"code\":2,\"example\":\"202406\",\"initValue\":1,\"name\":\"当前日期\",\"rule\":\"yyyyMM\",\"uuid\":\"\"},{\"autoIncrementConfigureType\":\"year\",\"code\":4,\"example\":\"001\",\"initValue\":1,\"name\":\"自动生成序号\",\"rule\":\"3\",\"uuid\":\"22d10cc1-a65b-49b3-9f0a-b44433dba73e\"}]', 'admin', NULL, NOW(), NULL, NULL, '1');

