DROP PROCEDURE if E<PERSON>IS<PERSON> conditional_execute;

DEL<PERSON>ITER $$

CREATE PROCEDURE conditional_execute(
    IN stmt1 TEXT,
    IN stmt2 TEXT
)
BEGIN
    DECLARE count_result INT DEFAULT 0;

    -- 动态执行第一个查询，结果存入用户变量
    SET @dynamic_sql = CONCAT('SELECT (', stmt1, ') INTO @tmp_count');
PREPARE prep_stmt FROM @dynamic_sql;
EXECUTE prep_stmt;
DEALLOCATE PREPARE prep_stmt;

-- 将用户变量转存到局部变量
SET count_result = @tmp_count;
    SET @tmp_count = NULL;  -- 清理临时变量

    -- 判断是否需要执行第二个语句
    IF count_result = 0 THEN
        SET @dynamic_sql = stmt2;
PREPARE prep_stmt FROM @dynamic_sql;
EXECUTE prep_stmt;
DEALLOCATE PREPARE prep_stmt;
END IF;
END$$

DELIMITER ;



CALL conditional_execute(
    'SELECT COUNT(1) FROM dfs_target_model WHERE target_name = ''increaseBaseWeekly''',
    'INSERT INTO `dfs_target_model` (`target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `frequency_unit`, `alarm_definition_code`, `is_count_target`, `run_target_status_formula`, `not_run_target_status_formula`, `stop_target_status_formula`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `data_type`, `data_decimal_point`, `remark`, `source`, `script`, `data_source_type`, `enable`, `api_transform_id`, `last_time`) VALUES (''increaseBaseWeekly'', ''基础数据增量每周统计'', 165, ''increaseBaseWeekly'', ''系统统计'', NULL, 5, NULL, ''m'', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, ''2025-02-28 10:41:11'', ''2025-02-28 10:41:11'', ''number'', 1, NULL, ''自动'', NULL, ''inner'', 1, NULL, ''2025-03-18 19:35:34'');'
);


CALL conditional_execute(
    'SELECT COUNT(1) FROM dfs_target_model WHERE target_name = ''increaseOrderWeekly''',
    'INSERT INTO `dfs_target_model` (`target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `frequency_unit`, `alarm_definition_code`, `is_count_target`, `run_target_status_formula`, `not_run_target_status_formula`, `stop_target_status_formula`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `data_type`, `data_decimal_point`, `remark`, `source`, `script`, `data_source_type`, `enable`, `api_transform_id`, `last_time`) VALUES (''increaseOrderWeekly'', ''单据增量每周统计'', 165, ''increaseOrderWeekly'', ''系统统计'', NULL, 5, NULL, ''m'', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, ''2025-02-28 10:41:11'', ''2025-02-28 10:41:11'', ''number'', 1, NULL, ''自动'', NULL, ''inner'', 1, NULL, ''2025-03-18 19:35:38'');'
);

-- 指标组-工厂： 默认开启
update dfs_target_model set enable = 1 WHERE target_name in ('increaseBaseWeekly', 'increaseOrderWeekly');
