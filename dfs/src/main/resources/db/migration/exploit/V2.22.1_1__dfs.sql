-- DDL
-- 生产工单新增领料状态名称
call proc_add_column(
        'dfs_work_order',
        'picking_state_name',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `picking_state_name` varchar(100) DEFAULT NULL COMMENT ''领料状态名称''');
-- 工作日历新增班次时间字段
call proc_add_column(
        'dfs_work_calendar',
        'work_calendar_time',
        'ALTER TABLE `dfs_work_calendar` ADD COLUMN `work_calendar_time` varchar(255) DEFAULT NULL COMMENT ''班次时间'' after `duration`');

-- 用户表、角色枚举类型字段改成varchar字段
call proc_modify_column(
        'sys_users',
        'enabled',
        'ALTER TABLE `sys_users` MODIFY COLUMN `enabled` varchar(100) NULL DEFAULT "enable" COMMENT ''账号是否可用''');
call proc_modify_column(
        'sys_users',
        'account_non_expired',
        'ALTER TABLE `sys_users` MODIFY COLUMN `account_non_expired` varchar(100) NULL DEFAULT "enable" COMMENT ''账号是否过期''');
call proc_modify_column(
        'sys_users',
        'credentials_non_expired',
        'ALTER TABLE `sys_users` MODIFY COLUMN `credentials_non_expired` varchar(100) NULL DEFAULT "enable" COMMENT ''密码是否过期''');
call proc_modify_column(
        'sys_users',
        'account_non_locked',
        'ALTER TABLE `sys_users` MODIFY COLUMN `account_non_locked` varchar(100) NULL DEFAULT "enable" COMMENT ''账号是否锁定''');
call proc_modify_column(
        'sys_roles',
        'status',
        'ALTER TABLE `sys_roles` MODIFY COLUMN `status` varchar(100) NULL DEFAULT "enable" COMMENT ''角色是否可用''');

CREATE TABLE IF NOT EXISTS `dfs_record_device_resume_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `device_id` int(11) NOT NULL,
    `device_state` tinyint(4) NOT NULL COMMENT '设备状态，0-停机 1-运行中 2-待机 3-故障',
    `record_date` date NOT NULL COMMENT '记录日期',
    `record_time` datetime NOT NULL COMMENT '记录时间',
    `work_order_number` varchar(255) DEFAULT NULL COMMENT '工单号',
    `work_order_state` tinyint(4) DEFAULT NULL COMMENT '工单状态',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `duration_h` decimal(10,2) DEFAULT NULL COMMENT '持续时长(小时)',
    `reason` varchar(2048) DEFAULT NULL COMMENT '原因',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx` (`device_id`,`record_date`,`device_state`),
    KEY `idx_record_date` (`record_date`),
    KEY `idx_work_order` (`work_order_number`),
    KEY `idx_metarial` (`material_code`),
    KEY `idx_device_state` (`device_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 指标月份表 - 新
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_product_order_monthly`;
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_product_order_summary_monthly`;
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_sale_order_summary`;

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_order_id` int(11) NOT NULL COMMENT '销售订单id',
    `product_order_number` varchar(255) NOT NULL COMMENT '销售订单号',
    `record_month` datetime NOT NULL COMMENT '月份: yyyy-MM',
    `end_plan_quantity` double(11,2) DEFAULT NULL COMMENT '成品计划数',
    `end_produce_quantity` double(11,2) DEFAULT NULL COMMENT '成品产出数',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`product_order_number`,`record_month`) USING BTREE,
    KEY `idx_record_month` (`record_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单 每月统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order_summary_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `record_month` datetime NOT NULL COMMENT '月份: yyyy-MM',
    `new_count` int(11) DEFAULT NULL COMMENT '本月新增订单数',
    `finish_count` int(11) DEFAULT NULL COMMENT '本月已完成订单数',
    `finish_delay_count` int(11) DEFAULT NULL COMMENT '本月延期完成订单数',
    `no_finish_delay_count` int(11) DEFAULT NULL COMMENT '本月延期未完成订单数',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`record_month`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单汇总 每月统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_sale_order_summary_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `record_month` datetime NOT NULL COMMENT '月份: yyyy-MM',
    `all_order_count` int(11) DEFAULT NULL COMMENT '总订单数',
    `delivered_order_count` int(11) DEFAULT NULL COMMENT '已交付订单数',
    `delivered_timely_order_count` int(11) DEFAULT NULL COMMENT '及时交付订单数',
    `delivered_delay_order_count` int(11) DEFAULT NULL COMMENT '延期交付订单数',
    `no_delivered_delay_order_count` int(11) DEFAULT NULL COMMENT '延期未交付订单数',
    `all_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '销售产品总数',
    `delivered_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '已交付产品总数',
    `delivered_timely_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '及时交付产品总数',
    `no_delivered_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '待交付产品总数',
    `no_delivered_delay_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '超期未交付产品总数',
    `delivered_order_rate` double(11,4) DEFAULT NULL COMMENT '订单交付率',
    `delivered_timely_order_rate` double(11,4) DEFAULT NULL COMMENT '订单及时交付率',
    `finish_order_material_rate` double(11,4) DEFAULT NULL COMMENT '销售订单完成率',
    `delivered_timely_order_material_rate` double(11,4) DEFAULT NULL COMMENT '产品及时交付率',
    `delivered_timely_rate` double(11,4) DEFAULT NULL COMMENT '交货及时率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`record_month`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单 每月汇总';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_work_order_10min` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `record_time` datetime NOT NULL COMMENT '记录时间(取整10分) ',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `unqualified_record_quantity` double(11,2) DEFAULT NULL COMMENT '不良记录数量',
    `input_quantity` double(11,2) DEFAULT NULL COMMENT '投入数量',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数',
    `working_people_quantity` double(11,2) DEFAULT NULL COMMENT '打卡人数(上机点)',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`work_order_number`,`record_time`) USING BTREE,
    KEY `idx_record_time` (`record_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产工单10分钟监控';

CREATE TABLE IF NOT EXISTS `dfs_work_order_10min_sum` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `record_time` datetime NOT NULL COMMENT '记录时间(取整10分) ',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `input_quantity` double(11,2) DEFAULT NULL COMMENT '投入数量',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`work_order_number`,`record_time`) USING BTREE,
    KEY `idx_record_time` (`record_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产工单10分钟快照';

CREATE TABLE IF NOT EXISTS `dfs_work_order_remark` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) DEFAULT NULL COMMENT '工单号',
    `username` varchar(255) DEFAULT NULL COMMENT '操作用户',
    `remark` varchar(4096) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_work_order` (`work_order_number`) USING BTREE,
    KEY `idx_user` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='工单备注表';
-- 修改code_report的索引
call proc_drop_column_index('dfs_code_report','is_report','report');
call proc_drop_column_index('dfs_code_report','relation_number','relation_number_report');
call proc_drop_column_index('dfs_code_report','relation_number','relation_number_input');
call proc_drop_column_index('dfs_code_report','relation_number','relation_number_unqualified');

call proc_add_column_index('dfs_code_report','is_report,report_time,relation_number', 'report');
call proc_add_column_index('dfs_code_report','is_input,input_time,relation_number','input');
call proc_add_column_index('dfs_code_report','is_unqualified,unqualified_time,relation_number','unqualified');


-- DML

-- 新增检验方案影响分析权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10409120', '数据影响分析', 'product.inspection.plan:dataImpactAnalysis', NULL, NULL, NULL, NULL, '2023-02-06 16:04:45', 'enable', 'GET', '10409', 2, 1, 0, '/qualities-manage/productInspection/plan', 1, NULL, '');
call init_new_role_permission('10409120');

-- 消息通知层级关系配置
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('defectNumThresholdNotice', '不良数量达到阈值通知', '{工单号},{不良数量阈值},{工序名称},{物料编号},{物料名称}', 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('defectRateThresholdNotice', '不良率达到阈值通知', '{工单号},{不良率阈值},{工序名称},{物料编号},{物料名称}', 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('concreteDefectNumThresholdNotice', '具体不良超数量阈值通知', '{工单号},{不良名称},{不良数量阈值},{工序名称},{物料编号},{物料名称}', 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('manualConcreteDefectNotice', '手动具体不良通知', '{工单号},{不良数量},{不良名称},{工序名称},{物料编号},{物料名称}', 'qualityInfoNotice');
-- 消息通知配置
INSERT INTO `dfs_circulation_notice_config` (`config_name`, `notice_type`, `notice_content`, `frequency`, `notice_method`, `state`, `remark`, `create_by`, `update_by`, `create_time`, `update_time`, `contact_ids`, `limit_scope`, `trigger_mode`, `is_notice_repeat`, `max_repeat_notice`) VALUES ('不良数量达到阈值推送', 'defectNumThresholdNotice', '{工单号}的不良数量超过{不良数量阈值}，请检查{工序名称}', NULL, '1', '1', '', 'admin', NULL, '2023-08-25 10:14:58', NULL, '', NULL, 'manual', '0', '0');
INSERT INTO `dfs_circulation_notice_config` (`config_name`, `notice_type`, `notice_content`, `frequency`, `notice_method`, `state`, `remark`, `create_by`, `update_by`, `create_time`, `update_time`, `contact_ids`, `limit_scope`, `trigger_mode`, `is_notice_repeat`, `max_repeat_notice`) VALUES ('不良率达到阈值推送', 'defectRateThresholdNotice', '{工单号}的不良率超过{不良率阈值}，请检查{工序名称}', NULL, '1', '1', '', 'admin', NULL, '2023-08-25 10:14:58', NULL, '', NULL, 'manual', '0', '0');
INSERT INTO `dfs_circulation_notice_config` (`config_name`, `notice_type`, `notice_content`, `frequency`, `notice_method`, `state`, `remark`, `create_by`, `update_by`, `create_time`, `update_time`, `contact_ids`, `limit_scope`, `trigger_mode`, `is_notice_repeat`, `max_repeat_notice`) VALUES ('具体不良超数量阈值推送', 'concreteDefectNumThresholdNotice', '{工单号}的{不良名称}超过{不良数量阈值}，请检查{工序名称}', NULL, '1', '1', '', 'admin', NULL, '2023-08-25 10:14:58', NULL, '', NULL, 'manual', '0', '0');
INSERT INTO `dfs_circulation_notice_config` (`config_name`, `notice_type`, `notice_content`, `frequency`, `notice_method`, `state`, `remark`, `create_by`, `update_by`, `create_time`, `update_time`, `contact_ids`, `limit_scope`, `trigger_mode`, `is_notice_repeat`, `max_repeat_notice`) VALUES ('手动具体不良推送', 'manualConcreteDefectNotice', '{工单号}中存在{不良数量}个{不良名称}', NULL, '1', '1', '', 'admin', NULL, '2023-08-25 10:14:58', NULL, '', NULL, 'manual', '0', '0');
-- 流转通知配置不良关联表
CREATE TABLE IF NOT EXISTS `dfs_circulation_notice_defect` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'isd',
  `config_id` int(11) DEFAULT NULL COMMENT '通知配置id',
  `defect_id` int(11) DEFAULT NULL COMMENT '不良id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='流转通知配置不良关联表';

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('workOrder10min', '工单每10分钟', '1', 'workOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('workOrder10min', 'workOrderTarget', '系统统计', 'workOrder10min', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('saleOrderSummaryMonthly', '销售订单每日', '1', 'saleOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('saleOrderSummaryMonthly', 'saleOrderTarget', '系统统计', 'saleOrderSummaryMonthly', 1, NULL);

DELETE from dfs_target_method WHERE target_name in ('saleOrder');
DELETE from dfs_target_dict WHERE target_name in ('saleOrder');
DELETE from dfs_target_model WHERE target_name in ('saleOrder');

-- 消息配置，添加不良数量、不良率
call proc_add_column(
        'dfs_circulation_notice_config',
        'defect_num',
        'ALTER TABLE `dfs_circulation_notice_config` ADD COLUMN `defect_num` double DEFAULT 0 COMMENT ''不良数量''');
call proc_add_column(
        'dfs_circulation_notice_config',
        'defect_rate',
        'ALTER TABLE `dfs_circulation_notice_config` ADD COLUMN `defect_rate` double DEFAULT 0 COMMENT ''不良率''');
--  更新消息配置
update dfs_circulation_notice_config set notice_content='{工单号}的不良数量超过{不良数量阈值}，请检查{工序名称}'  where notice_type='defectNumThresholdNotice';
update dfs_circulation_notice_config set notice_content='{工单号}的不良率超过{不良率阈值}，请检查{工序名称}'  where notice_type='defectRateThresholdNotice';
update dfs_circulation_notice_config set notice_content='{工单号}的{不良名称}超过{不良数量阈值}，请检查{工序名称}'  where notice_type='concreteDefectNumThresholdNotice';


-- 兼容工作日历数据
UPDATE dfs_work_calendar SET work_calendar_time = (CONCAT(`start`,'-',`end`));
-- iqc消息
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('iqcNotice', 'IQC呼叫通知', '{产线名称},{工位名称},{工序名称},{物料编号},{物料名称},{生产订单号},{工单号},{操作人名称},{来料呼叫类型},{备注}', 'productCirculationNotice');

-- 删除dfs中有关smt的权限
delete from sys_route where id in  (select id from (SELECT id FROM `sys_route` where parent_path like '%smt-management%' and `name` not like '%cell%') p1);
delete from sys_permissions where id in ( select id from(SELECT id FROM sys_permissions where parent_path like '%smt%' and id !='10612' and  parent_path not like '%cell%') p1 );

-- 下推是否新建页面:销售订单下推生产订单
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.productOrder.jumpPage.openPage', 'saleOrder.pushDownConfig.productOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
-- 下推是否新建页面:销售订单下推采购需求
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.openPage', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.deliveryApplication.jumpPage.openPage', 'saleOrder.pushDownConfig.deliveryApplication.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.openPage', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.openPage', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'saleOrder.pushDownConfig.subcontractOrder.jumpPage.openPage', 'saleOrder.pushDownConfig.subcontractOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');

-- 生产订单下推配置
-- 下推是否新建页面:下推生产工单
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.productOrderPushDownConfig.workOrder.jumpPage.openPage', 'production.productOrderPushDownConfig.workOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
-- 下推是否新建页面:销售订单下推采购需求
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.openPage', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.productOrderPushDownConfig.productMaterialsList.jumpPage.openPage', 'production.productOrderPushDownConfig.productMaterialsList.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.productOrderPushDownConfig.productionIn.jumpPage.openPage', 'production.productOrderPushDownConfig.productionIn.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');


-- 生产订单用料清单下推
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.openPage', 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.openPage', 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage.openPage', 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');

-- 生产工单下推
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage.openPage', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.openPage', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');


-- 采购需求下推
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.openPage', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.openPage', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.openPage', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.openPage', 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn.openPage', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder.openPage', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.openPage', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
INSERT INTO `dfs_business_config_value` VALUES (null, 'openPage', '是否新建页面(单选)', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.openPage', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true');
-- 批量配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('batchOpenPage', '下推新建页面', 'general.batchOpenPage', 'general', NULL, 'yelinkoncall', 'yelinkoncall', '2023-07-17 03:11:36', '2023-07-17 03:11:36');

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'general.batchOpenPage.enable', 'general.batchOpenPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"全部开启"},{"value":false,"label":"单独配置"}]', 'true');




INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.aging.bind.machine', '老化绑定工位机', '/aging-bind-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.ocs.callOutIQC.com', '呼叫IQC', '/call-out-iqc', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);

-- 质量报表异步导出模板配置权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003050', '当前订单明细下载默认导出模板', 'sales.report:orderDetails:exportDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');


INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003060', '当前订单明细上传或下载自定义导出模板', 'sales.report:orderDetails:customerTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003070', '当前订单明细查看导出记录', 'sales.report:orderDetails:exportRecord', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');


INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003080', '订单进度下载默认导出模板', 'sales.report:orderProgress:exportDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');


INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003090', '订单进度上传或下载自定义导出模板', 'sales.report:orderProgress:customerTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003110', '订单进度查看导出记录', 'sales.report:orderProgress:exportRecord', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003120', '订单质量追踪下载默认导出模板', 'sales.report:orderQualityTracking:exportDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003130', '订单质量追踪上传或下载自定义导出模板', 'sales.report:orderQualityTracking:customerTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');


INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003140', '订单质量追踪查看导出记录', 'sales.report:orderQualityTracking:exportRecord', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

-- 销售订单
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_sale_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sale_order_material_id` int(11) NOT NULL COMMENT '销售订单物料id',
  `sale_order_id` int(11) NOT NULL COMMENT '销售订单id',
  `sale_order_number` varchar(255) NOT NULL COMMENT '销售订单号',
  `material_code` varchar(255) NOT NULL COMMENT '物料编码',
  `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `customer_code` varchar(255) DEFAULT NULL COMMENT '客户代号（客户编号）',
  `customer_name` varchar(255) DEFAULT NULL COMMENT '客户名称',
  `salesman_code` varchar(255) DEFAULT NULL COMMENT '销售员编号',
  `salesman_name` varchar(255) DEFAULT NULL COMMENT '销售员名称',
  `sales_quantity` double(11,2) DEFAULT NULL COMMENT '销售数',
  `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
  `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
  `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不合格数',
  `purchase_quantity` double(11,2) DEFAULT NULL COMMENT '采购数',
  `applied_shipment_quantity` double(11,2) DEFAULT NULL COMMENT '出货申请数量',
  `applied_quantity` double(11,2) DEFAULT NULL COMMENT '发货数量',
  `delay_quantity` double(11,2) DEFAULT NULL COMMENT '延期数量',
  `out_stock_quantity` double(11,2) DEFAULT NULL COMMENT '出库数量',
  `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率',
  `finish_rate` double(11,4) DEFAULT NULL COMMENT '达成率',
  `time` datetime DEFAULT NULL COMMENT '最新统计时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni` (`sale_order_material_id`) USING BTREE,
  KEY `idx_sale_order` (`sale_order_number`),
  KEY `idx_material` (`material_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单 整体统计';

-- 工单
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column material_code varchar(255)   COMMENT '物料编码' ;
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column production_basic_unit_id int   COMMENT '生产基本单元ID' ;
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column work_center_id int  COMMENT '工作中心id' ;
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column material_name varchar(255)   COMMENT '物料名称' ;
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column production_basic_unit_name varchar(255)   COMMENT '生产基本单元名称';
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column production_basic_unit_type_name varchar(255)   COMMENT '生产基本单元类型名称';
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column production_basic_unit_type_id int   COMMENT '生产基本单元类型ID';
ALTER TABLE dfs_metrics.dfs_metrics_work_order add column work_center_name varchar(255)  COMMENT '工作中心名称' ;

-- 保存并返回配置
update dfs_business_config_value   set `option_values` =   '[\"salesOrder\",\"productOrder\",\"workOrder\",\"qualityInspection\"]'        where  value_full_path_code  = 'general.saveAndBackConfig.selectOrder';

-- ----------------------------
-- Table structure for dfs_metrics_work_order_total
-- ----------------------------
DROP TABLE IF EXISTS `dfs_metrics`.`dfs_metrics_work_order_total`;
CREATE TABLE `dfs_metrics`.`dfs_metrics_work_order_total` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
  `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
  `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
  `inspection_quantity` double(11,2) DEFAULT NULL COMMENT '检测数量',
  `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
  `inbound_quantity` double(11,2) DEFAULT NULL COMMENT '入库数量',
  `actual_capacity` double(11,2) DEFAULT NULL COMMENT '实际产能',
  `actual_working_hour` double(11,2) DEFAULT NULL COMMENT '实际投入工时',
  `theory_working_hour` double(11,2) DEFAULT NULL COMMENT '理论产出工时',
  `stop_line_hour` double(11,2) DEFAULT NULL COMMENT '停线时长',
  `finish_rate` double(11,2) DEFAULT NULL COMMENT '达成率',
  `qualified_rate` double(11,2) DEFAULT NULL COMMENT '合格率',
  `product_efficiency_rate` double(11,2) DEFAULT NULL COMMENT '生产效率',
  `achievements` double(11,2) DEFAULT NULL COMMENT '绩效',
  `repair_quantity` double(11,2) DEFAULT NULL COMMENT '返修数',
  `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '返修良品数（维修判定: 不报废）',
  `repair_quantity_rate` double(11,2) DEFAULT NULL COMMENT '返修率',
  `time` datetime DEFAULT NULL COMMENT '最新统计时间',
  `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `production_basic_unit_id` int(11) DEFAULT NULL COMMENT '生产基本单元ID',
  `work_center_id` int(11) DEFAULT NULL COMMENT '工作中心id',
  `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `production_basic_unit_name` varchar(255) DEFAULT NULL COMMENT '生产基本单元名称',
  `production_basic_unit_type_name` varchar(255) DEFAULT NULL COMMENT '生产基本单元类型名称',
  `production_basic_unit_type_id` int(11) DEFAULT NULL COMMENT '生产基本单元类型ID',
  `work_center_name` varchar(255) DEFAULT NULL COMMENT '工作中心名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni` (`work_order_number`)
) ENGINE=InnoDB AUTO_INCREMENT=672 DEFAULT CHARSET=utf8mb4 COMMENT='生产工单数据汇总表';

-- 报表按钮权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001170', '生产汇总-生产单元+产品导出_下载默认模板', 'productionUnitMaterial.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001180', '生产汇总-生产单元+产品导出_导出excel', 'productionUnitMaterial.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001190', '生产汇总-生产单元+产品导出列表', 'productionUnitMaterial.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001200', '生产汇总-生产单元+产品导出_查看日志', 'productionUnitMaterial.statement.center:view:log', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');


INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001210', '生产汇总-产品导出_下载默认模板', 'productionMaterial.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001220', '生产汇总-产品导出_导出excel', 'productionMaterial.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001230', '生产汇总-产品导出列表', 'productionMaterial.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001240', '生产汇总-产品导出_查看日志', 'productionMaterial.statement.center:view:log', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001250', '生产汇总-工作中心+产品导出_下载默认模板', 'productionCenterMaterial.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001260', '生产汇总-工作中心+产品导出_导出excel', 'productionCenterMaterial.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001270', '生产汇总-工作中心+产品导出列表', 'productionCenterMaterial.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001280', '生产汇总-工作中心+产品导出_查看日志', 'productionCenterMaterial.statement.center:view:log', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003150', '销售汇总-销售员+客户导出_下载默认模板', 'saleOrderTotalSalesmanCustomer.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003160', '销售汇总-销售员+客户导出_导出excel', 'saleOrderTotalSalesmanCustomer.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003170', '销售汇总-销售员+客户导出列表', 'saleOrderTotalSalesmanCustomer.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003180', '销售汇总-销售员+客户导出_查看日志', 'saleOrderTotalSalesmanCustomer.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003190', '销售汇总-产品导出_下载默认模板', 'saleOrderTotalMaterial.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003200', '销售汇总-产品导出_导出excel', 'saleOrderTotalMaterial.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003210', '销售汇总-产品导出列表', 'saleOrderTotalMaterial.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003220', '销售汇总-产品导出_查看日志', 'saleOrderTotalMaterial.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003230', '销售汇总-客户导出_下载默认模板', 'saleOrderTotalCustomer.statement.center:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003240', '销售汇总-客户导出_导出excel', 'saleOrderTotalCustomer.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003250', '销售汇总-客户导出列表', 'saleOrderTotalCustomer.statement.center:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003260', '销售汇总-客户导出_查看日志', 'saleOrderTotalCustomer.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001290', '生产汇总-生产单元+产品导出_上传下载自定义导出模板', 'productionUnitMaterial.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001300', '生产汇总-生产单元导出_上传下载自定义导出模板', 'productionMaterial.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13001310', '生产汇总-工作中心+产品导出_上传下载自定义导出模板', 'productionCenterMaterial.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', '2', '1', '0', '/statementCenter', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003270', '销售汇总-销售员+客户导出_上传下载自定义导出模板', 'saleOrderTotalSalesmanCustomer.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003280', '销售汇总-产品导出_上传下载自定义导出模板', 'saleOrderTotalMaterial.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('13003290', '销售汇总-客户导出_上传下载自定义导出模板', 'saleOrderTotalCustomer.statement.center:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', '2', '1', '0', '/statementCenter/salesReport', '1', NULL, '');

-- 生产工单汇总表，添加工单状态
alter table dfs_metrics.dfs_metrics_work_order_total add column state int DEFAULT 0 COMMENT '0-创建、1-发放、2-投入、3-挂起、4-完成、5-关闭、6-取消';

-- 帅楠在2.16.1_1写错脚本，需要重新插入
TRUNCATE TABLE `aql_contrast`;
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('A', '1,8', 'A', 'A', 'A', 'A', 'A', 'A', 'B');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('B', '9,15', 'A', 'A', 'A', 'A', 'A', 'B', 'C');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('C', '16,25', 'A', 'A', 'B', 'B', 'B', 'C', 'D');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('D', '26,50', 'A', 'B', 'B', 'C', 'C', 'D', 'E');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('E', '51,90', 'B', 'B', 'C', 'C', 'C', 'E', 'F');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('F', '151,280', 'B', 'B', 'C', 'D', 'D', 'F', 'G');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('G', '91,150', 'B', 'C', 'D', 'E', 'E', 'G', 'H');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('H', '281,500', 'B', 'C', 'D', 'E', 'F', 'H', 'J');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('J', '501,1200', 'C', 'C', 'E', 'F', 'G', 'J', 'K');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('K', '1201,3200', 'C', 'D', 'E', 'G', 'H', 'K', 'L');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('L', '3201,10000', 'C', 'D', 'F', 'G', 'J', 'L', 'M');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('M', '10001,35000', 'C', 'D', 'F', 'H', 'K', 'M', 'N');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('N', '35001,150000', 'D', 'E', 'G', 'J', 'L', 'N', 'P');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('P', '150001,500000', 'D', 'E', 'G', 'J', 'M', 'P', 'Q');
INSERT INTO `aql_contrast`(`scope_code`, `scope_num`, `s1`, `s2`, `s3`, `s4`, `g1`, `g2`, `g3`) VALUES ('Q', '500001,0x7fffffff', 'D', 'E', 'H', 'K', 'N', 'Q', 'R');