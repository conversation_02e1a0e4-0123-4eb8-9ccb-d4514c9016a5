-- 供应商信息支持字段配置
-- 委外模块
-- 委外订单
--  列表
--  按物料展示，将 单据基本信息 的字段移到 单据物料行字段
delete from `dfs_form_field_config` where full_path_code = 'subcontractOrder.list.material' and field_code in ('supplier','supplierName','supplierContacts','supplierPhone','supplierAddr','supplierShortName') and module_code = 'baseField';
delete from `dfs_form_field_rule_config` where full_path_code = 'subcontractOrder.list.material' and field_code in ('supplier','supplierName','supplierContacts','supplierPhone','supplierAddr','supplierShortName') and module_code = 'baseField';
call proc_add_form_field_module('subcontractOrder.list.material', 'supplierContacts', '供应商联系人', 'baseMaterialLineField');
call proc_add_form_field_module('subcontractOrder.list.material', 'supplierPhone', '供应商联系方式', 'baseMaterialLineField');
call proc_add_form_field_module('subcontractOrder.list.material', 'supplierAddr', '供应商地址', 'baseMaterialLineField');
call proc_add_form_field_module('subcontractOrder.list.material', 'supplierShortName', '供应商简称', 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', 'subcontractOrder.list.material', 'supplierContacts', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', 'subcontractOrder.list.material', 'supplierPhone', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', 'subcontractOrder.list.material', 'supplierAddr', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', 'subcontractOrder.list.material', 'supplierShortName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseMaterialLineField');



-- DDL
-- 指标： 生产日汇总-按生产订单
-- 重命名
RENAME TABLE `dfs_metrics`.`dfs_metrics_product_order_daily` TO `dfs_metrics`.`dfs_metrics_product_order_daily_old`;
-- 创建新表
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order_daily` (
    `id` int NOT NULL AUTO_INCREMENT,
    `product_order_id` int NOT NULL COMMENT '生产订单id',
    `product_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产订单号',
    `record_date` date NOT NULL COMMENT '记录日期',
    `end_plan_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品计划数',
    `end_unqualified_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品不合格数',
    `end_produce_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品产出数',
    `end_qualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT '成品合格率',
    `plan_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '计划数',
    `unqualified_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '不合格数',
    `produce_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '产出数',
    `qualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT '合格率',
    `time` datetime NULL DEFAULT NULL COMMENT '最新统计时间',
    `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '不良品记录数量: 流水码去重',
    `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '不良项记录数量: 流水码不去重',
    `end_unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品不良品记录数量: 流水码去重',
    `end_unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品不良项记录数量: 流水码不去重',
    `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料名称',
    `sale_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '销售订单号',
    `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
    `plan_start_time` datetime NULL DEFAULT NULL COMMENT '计划开始时间',
    `plan_end_date` datetime NULL DEFAULT NULL COMMENT '计划结束时间',
    `actual_start_date` datetime NULL DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_date` datetime NULL DEFAULT NULL COMMENT '实际结束时间',
    `single_theory_hour` double(15, 6) NULL DEFAULT NULL COMMENT '单件理论工时',
    `plan_theory_hour` double(15, 6) NULL DEFAULT NULL COMMENT '计划理论工时',
    `grid_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '车间名称',
    `craft_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工艺名称',
    `material_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料类型名称',
    `material_sort_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料分类名称',
    `material_standard` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料规格',
    `end_direct_access_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品日直通数',
    `end_unqualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT '成品日不良率',
    `end_direct_rate` double(11, 4) NULL DEFAULT NULL COMMENT '成品日直通率',
    `direct_access_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '工序日直通数',
    `unqualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT '工序日不良率',
    `direct_rate` double(11, 4) NULL DEFAULT NULL COMMENT '工序日直通率',
    `procedure_working_hour` double(11, 2) NULL DEFAULT NULL COMMENT '工序时长',
    `procedure_circulation_hour` double(11, 2) NULL DEFAULT NULL COMMENT '工序流转时长',
    `circulation_hour_rate` double(11, 4) NULL DEFAULT NULL COMMENT '流转时长占比',
    `end_total_plan_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品总计划数',
    `end_total_produce_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品总完成数',
    `end_total_unqualified_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品总不良数',
    `end_total_direct_access_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品总直通数',
    `end_total_qualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT '成品总合格率',
    `end_total_unqualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT '成品总不良率',
    `end_total_direct_rate` double(11, 4) NULL DEFAULT NULL COMMENT '成品总直通率',
    `material_custom_field_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段1',
    `material_custom_field_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段2',
    `material_custom_field_three` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段3',
    `material_custom_field_four` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段4',
    `material_custom_field_five` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段5',
    `material_custom_field_six` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段6',
    `material_custom_field_seven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段7',
    `material_custom_field_eight` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段8',
    `material_custom_field_nine` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段9',
    `material_custom_field_ten` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段10',
    `material_custom_field_eleven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料扩展字段11',
    `end_total_unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品总不良品记录数量: 流水码去重',
    `end_total_unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT '成品总不良项记录数量: 流水码不去重',
    `material_sku_attr_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料特征参数名称 [多个按,分隔]',
    `material_drawing_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料图号',
    `material_raw_material` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料材质',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `idx_uni`(`product_order_number` ASC, `record_date` ASC) USING BTREE,
    INDEX `idx_record_date`(`record_date` ASC) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生产订单-每日统计';


-- 插入这两天的数据
INSERT INTO `dfs_metrics`.`dfs_metrics_product_order_daily` (
    `id`, `product_order_id`, `product_order_number`, `record_date`, `end_plan_quantity`,
    `end_unqualified_quantity`, `end_produce_quantity`, `end_qualified_rate`, `plan_quantity`,
    `unqualified_quantity`, `produce_quantity`, `qualified_rate`, `time`, `material_code`,
    `material_name`, `end_unqualified_record_item_quantity`, `unqualified_record_quantity`,
    `unqualified_record_item_quantity`, `end_unqualified_record_quantity`
)
SELECT
    `id`, `product_order_id`, `product_order_number`, `record_date`, `end_plan_quantity`,
    `end_unqualified_quantity`, `end_produce_quantity`, `end_qualified_rate`, `plan_quantity`,
    `unqualified_quantity`, `produce_quantity`, `qualified_rate`, `time`, `material_code`,
    `material_name`, `end_unqualified_record_item_quantity`, `unqualified_record_quantity`,
    `unqualified_record_item_quantity`, `end_unqualified_record_quantity`
FROM `dfs_metrics`.`dfs_metrics_product_order_daily_old`
WHERE record_date > CURDATE() - INTERVAL 2 DAY;

-- 指标： 生产月度汇总-按生产订单
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'sale_order_number',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `sale_order_number` varchar(255) NULL DEFAULT NULL COMMENT ''销售订单号''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'customer_name',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `customer_name` varchar(255) NULL DEFAULT NULL COMMENT ''客户名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'plan_start_time',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `plan_start_time` datetime NULL DEFAULT NULL COMMENT ''计划开始时间''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'plan_end_date',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `plan_end_date` datetime NULL DEFAULT NULL COMMENT ''计划结束时间''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'actual_start_date',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `actual_start_date` datetime NULL DEFAULT NULL COMMENT ''实际开始时间''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'actual_end_date',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `actual_end_date` datetime NULL DEFAULT NULL COMMENT ''实际结束时间''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'single_theory_hour',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `single_theory_hour` double(15,6) NULL DEFAULT NULL COMMENT ''单件理论工时''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'plan_theory_hour',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `plan_theory_hour` double(15,6) NULL DEFAULT NULL COMMENT ''计划理论工时''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'grid_name',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `grid_name` varchar(255) NULL DEFAULT NULL COMMENT ''车间名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'craft_name',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `craft_name` varchar(255) NULL DEFAULT NULL COMMENT ''工艺名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_type_name',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_type_name` varchar(255) NULL DEFAULT NULL COMMENT ''物料类型名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_sort_name',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_sort_name` varchar(255) NULL DEFAULT NULL COMMENT ''物料分类名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_standard',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_standard` varchar(255) NULL DEFAULT NULL COMMENT ''物料规格''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_drawing_number',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_drawing_number` varchar(255) NULL DEFAULT NULL COMMENT ''物料图号''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_raw_material',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_raw_material` varchar(255) NULL DEFAULT NULL COMMENT ''物料材质''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_sku_attr_name',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_sku_attr_name` varchar(255) DEFAULT NULL COMMENT ''物料特征参数名称 [多个按,分隔]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_one',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_one` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段1''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_two',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_two` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段2''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_three',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_three` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段3''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_four',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_four` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段4''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_five',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_five` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段5''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_six',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_six` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段6''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_seven',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_seven` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段7''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_eight',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_eight` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段8''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_nine',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_nine` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段9''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_ten',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_ten` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段10''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'material_custom_field_eleven',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `material_custom_field_eleven` varchar(255) NULL DEFAULT NULL COMMENT ''物料扩展字段11''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'end_unqualified_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `end_unqualified_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''成品不良数''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'end_direct_access_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `end_direct_access_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''成品日直通数''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'end_qualified_rate',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `end_qualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT ''成品合格率''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'end_unqualified_rate',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `end_unqualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT ''成品不良率''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'end_direct_rate',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `end_direct_rate` double(11, 4) NULL DEFAULT NULL COMMENT ''成品直通率''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'produce_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `produce_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''工序完成数''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'unqualified_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `unqualified_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''工序不良数''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''工序不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''工序不良项记录数量: 流水码不去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'direct_access_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `direct_access_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''工序直通数''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'qualified_rate',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `qualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT ''工序合格率''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'unqualified_rate',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `unqualified_rate` double(11, 4) NULL DEFAULT NULL COMMENT ''工序不良率''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'direct_rate',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `direct_rate` double(11, 4) NULL DEFAULT NULL COMMENT ''工序直通率''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'procedure_working_hour',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `procedure_working_hour` double(11, 2) NULL DEFAULT NULL COMMENT ''工序时长''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'procedure_circulation_hour',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `procedure_circulation_hour` double(11, 2) NULL DEFAULT NULL COMMENT ''工序流转时长''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'circulation_hour_rate',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `circulation_hour_rate` double(11, 4) NULL DEFAULT NULL COMMENT ''流转时长占比''');


-- 销售订单  销售月度汇总
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'all_order_material_amount',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `all_order_material_amount` double(11, 4) NULL DEFAULT NULL COMMENT ''销售产品总额''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'return_count',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `return_count` int NULL DEFAULT NULL COMMENT ''退货次数''');
