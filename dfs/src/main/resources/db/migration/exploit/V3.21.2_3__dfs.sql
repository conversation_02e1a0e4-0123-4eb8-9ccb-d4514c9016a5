-- bom
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'id', 'bomId', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'bomNum', 'bom编号', 'bom_num');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'bomName', 'bom名称', 'bom_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'state', '状态', 'state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'code', '物料编码', 'code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'bomDescribe', '描述', 'bom_describe');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'version', '版本', 'version');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'updateBy', '更新人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'importTime', '导入时间', 'import_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'isTemplate', '是否是模板', 'is_template');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'skuId', '备注', 'sku_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'releaseTime', '生效时间', 'release_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'id', 'bom物料清单id', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'code', 'bom物料清单物料编码', 'code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'num', 'bom物料清单用量:分子', 'num');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'number', 'bom物料清单用量:分母', 'number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'sort', '分类', 'sort');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'bomType', 'bom子项类型（1001-普通件 1002-替代件  1003-返还件  1004-联产品  1005-副产品）', 'bom_type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'version', '版本', 'version');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'fixedDamage', 'bom物料清单固定损耗', 'fixed_damage');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'lossRate', 'bom物料清单固定损耗', 'loss_rate');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'skuId', '特征参数skuId', 'sku_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bomMaterial', 'extendOne', '扩展字段1', 'extend_one');

-- 特征参数
INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('auxiliaryAttr', 'id', 'id', 'id'),
    ('auxiliaryAttr', 'auxiliaryAttrCode', '特征参数编号', 'auxiliary_attr_code'),
    ('auxiliaryAttr', 'auxiliaryAttrName', '特征参数名称', 'auxiliary_attr_name'),
    ('auxiliaryAttr', 'state', '状态（1-创建 2-启用 3-停用 4-废弃）', 'state'),
    ('auxiliaryAttr', 'approver', '审核人', 'approver'),
    ('auxiliaryAttr', 'actualApprover', '实际审批人', 'actual_approver'),
    ('auxiliaryAttr', 'approvalStatus', '审批状态', 'approval_status'),
    ('auxiliaryAttr', 'approvalSuggestion', '审批建议', 'approval_suggestion'),
    ('auxiliaryAttr', 'approvalTime', '审批时间', 'approval_time'),
    ('auxiliaryAttr', 'updateBy', '更新人', 'update_by'),
    ('auxiliaryAttr', 'createBy', '创建人', 'create_by'),
    ('auxiliaryAttr', 'updateTime', '更新时间', 'update_time'),
    ('auxiliaryAttr', 'createTime', '创建时间', 'create_time'),
    ('auxiliaryAttr', 'remark', '备注', 'remark');

-- 特征值
INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('auxiliaryAttrValue', 'id', 'id', 'id'),
    ('auxiliaryAttrValue', 'auxiliaryAttrCode', '特征参数编号', 'auxiliary_attr_code'),
    ('auxiliaryAttrValue', 'valueCode', '特征值编码', 'value_code'),
    ('auxiliaryAttrValue', 'valueName', '特征值名称', 'value_name'),
    ('auxiliaryAttrValue', 'state', '状态（1-显示 2-隐藏）', 'state'),
    ('auxiliaryAttrValue', 'updateBy', '更新人', 'update_by'),
    ('auxiliaryAttrValue', 'createBy', '创建人', 'create_by'),
    ('auxiliaryAttrValue', 'updateTime', '更新时间', 'update_time'),
    ('auxiliaryAttrValue', 'createTime', '创建时间', 'create_time'),
    ('auxiliaryAttrValue', 'remark', '备注', 'remark');

-- 扫码规则
INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('codeResolverRule', 'id', 'id', 'id'),
    ('codeResolverRule', 'code', '条码code', 'code'),
    ('codeResolverRule', 'name', '条码名字', 'name'),
    ('codeResolverRule', 'codePrefix', '条码前缀', 'code_prefix'),
    ('codeResolverRule', 'createBy', '创建人', 'create_by'),
    ('codeResolverRule', 'createTime', '创建时间', 'create_time'),
    ('codeResolverRule', 'updateBy', '修改人', 'update_by'),
    ('codeResolverRule', 'updateTime', '修改时间', 'update_time');

-- 扫码规则产线关联
INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('codeResolverRuleLine', 'lineId', '产线id', 'line_id');

-- 单位
INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('unit', 'id', '主键id', 'id'),
    ('unit', 'code', '码值', 'code'),
    ('unit', 'name', '单位名称', 'name'),
    ('unit', 'des', '描述', 'des'),
    ('unit', 'unit', '单位精度位数', 'unit'),
    ('unit', 'value', '舍入类型', 'value'),
    ('unit', 'createTime', '创建时间', 'create_time'),
    ('unit', 'updateTime', '更新时间', 'update_time'),
    ('unit', 'createBy', '创建人', 'create_by'),
    ('unit', 'updateBy', '更新人', 'update_by');

-- sku
INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('auxiliaryAttrSku', 'id', '主键ID', 'id'),
    ('auxiliaryAttrSku', 'materialCode', '物料编码', 'material_code'),
    ('auxiliaryAttrSku', 'skuId', 'skuId', 'sku_id'),
    ('auxiliaryAttrSku', 'skuCode', 'skuCode', 'sku_code'),
    ('auxiliaryAttrSku', 'skuName', 'skuName', 'sku_name'),
    ('auxiliaryAttrSku', 'auxiliaryAttrCode', '特征参数编号', 'auxiliary_attr_code'),
    ('auxiliaryAttrSku', 'auxiliaryAttrName', '特征参数名称', 'auxiliary_attr_name'),
    ('auxiliaryAttrSku', 'valueCode', '特征值编码', 'value_code'),
    ('auxiliaryAttrSku', 'valueName', '特征值名称', 'value_name'),
    ('auxiliaryAttrSku', 'createBy', '创建人', 'create_by'),
    ('auxiliaryAttrSku', 'createTime', '创建时间', 'create_time');

-- 解码规则
INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('scanCodeRule', 'id', 'id', 'id'),
    ('scanCodeRule', 'code', '标识code', 'code'),
    ('scanCodeRule', 'value', '转换脚本', 'value'),
    ('scanCodeRule', 'remark', '备注', 'remark'),
    ('scanCodeRule', 'state', '状态（1-启用2-停用）', 'state'),
    ('scanCodeRule', 'createBy', '创建人', 'create_by'),
    ('scanCodeRule', 'createTime', '创建时间', 'create_time'),
    ('scanCodeRule', 'updateBy', '修改人', 'update_by'),
    ('scanCodeRule', 'updateTime', '修改时间', 'update_time'),
    ('scanCodeRule', 'type', '类型', 'type'),
    ('scanCodeRule', 'name', '名称', 'name'),
    ('scanCodeRule', 'testCode', '测试数据', 'test_code'),
    ('scanCodeRule', 'dataFormat', '返回数据格式', 'data_format'),
    ('scanCodeRule', 'testResult', '测试结果', 'test_result');


INSERT INTO dfs_query_field_config(prefix, field_code, field_name, table_field_code)
VALUES
    ('craft', 'craftId', '工艺id', 'craft_id'),
    ('craft', 'craftCode', '工艺编号', 'craft_code'),
    ('craft', 'originCode', '原始编码', 'origin_code'),
    ('craft', 'craftVersion', '工艺版本号', 'craft_version'),
    ('craft', 'name', '工艺名称', 'name'),
    ('craft', 'originName', '原始名称', 'origin_name'),
    ('craft', 'materialId', '物料id', 'material_id'),
    ('craft', 'materialCode', '物料编号', 'material_code'),
    ('craft', 'state', '状态', 'state'),
    ('craft', 'isTemplate', '是否是模板', 'is_template'),
    ('craft', 'extend', '扩展字段', 'extend'),
    ('craft', 'procedureControllerCheck', '工序控制检查', 'procedure_controller_check'),
    ('craft', 'releaseTime', '生效时间', 'release_time'),
    ('craft', 'createTime', '创建时间', 'create_time'),
    ('craft', 'updateTime', '更新时间', 'update_time'),
    ('craft', 'createBy', '创建人', 'create_by'),
    ('craft', 'updateBy', '更新人', 'update_by');