set autocommit = 0;

CREATE TABLE IF NOT EXISTS `dfs_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `eui` varchar(50) DEFAULT NULL COMMENT 'eui',
  `count` double(11,3) DEFAULT NULL COMMENT '计数',
  `remark` varchar(50) DEFAULT NULL COMMENT '备注信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `eui` (`eui`),
  KEY `remark` (`remark`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='计数表';

commit
