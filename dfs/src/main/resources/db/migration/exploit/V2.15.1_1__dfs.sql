-- 注意！！！！！！！！！！该脚本为新业务配置迁移的初始化脚本，请勿在此脚本添加sql！！！！！！！！！！！！！！
-- 注意！！！！！！！！！！该脚本为新业务配置迁移的初始化脚本，请勿在此脚本添加sql！！！！！！！！！！！！！！
-- 注意！！！！！！！！！！该脚本为新业务配置迁移的初始化脚本，请勿在此脚本添加sql！！！！！！！！！！！！！！


-- 存储过程：init_new_config_value
-- 迁移配置值和配置描述
DROP PROCEDURE IF EXISTS `init_new_config_value`;
DELIMITER $$
CREATE DEFINER=`root`@`%` PROCEDURE `init_new_config_value`(in var_operation_object varchar(100),in var_target_object varchar(100),in var_operation_type varchar(100),in var_config_full_path_code varchar(750))
begin

	if (var_target_object = '') then
		update dfs_business_config_value v set value = (select property_value from dfs_common_config where operation_object= var_operation_object and target_object is null and operation_type = var_operation_type and property_key = v.value_code) where v.config_full_path_code like var_config_full_path_code;
		update dfs_business_config c set description = (select description from dfs_common_config where operation_object= var_operation_object and target_object is null and operation_type = var_operation_type limit 1) where c.full_path_code like var_config_full_path_code;
	else
		update dfs_business_config_value v set value = (select property_value from dfs_common_config where operation_object= var_operation_object and target_object = var_target_object and operation_type = var_operation_type and property_key = v.value_code) where v.config_full_path_code like var_config_full_path_code;
		update dfs_business_config c set description = (select description from dfs_common_config where operation_object= var_operation_object and target_object = var_target_object and operation_type = var_operation_type limit 1) where c.full_path_code like var_config_full_path_code;
  end if;

end $$
DELIMITER;

truncate table dfs_business_config;
-- 销售
INSERT INTO `dfs_business_config` VALUES (null, 'saleOrder', '销售模块', 'saleOrder', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'production', '生产模块', 'production', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchase', '采购模块', 'purchase', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'delivery', '发货模块', 'delivery', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontract', '委外模块', 'subcontract', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'applet', '小程序模块', 'applet', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'factoryModel', '工厂模型模块', 'factoryModel', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'material', '物料模块', 'material', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'design', '设计模块', 'design', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'warehouse', '仓库模块', 'warehouse', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseInAndOut', '采购出入库', 'purchaseInAndOut', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractInAndOut', '委外出入库', 'subcontractInAndOut', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'saleInAndOut', '销售出入库', 'saleInAndOut', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'otherInAndOut', '其他出入库', 'otherInAndOut', '', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'pushDownConfig', '销售订单下推配置', 'saleOrder.pushDownConfig', 'saleOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'listConfig', '销售订单列表配置', 'saleOrder.listConfig', 'saleOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'productOrder', '生产订单', 'saleOrder.pushDownConfig.productOrder', 'saleOrder.pushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'workOrder', '生产工单', 'saleOrder.pushDownConfig.workOrder', 'saleOrder.pushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseRequest', '采购需求单', 'saleOrder.pushDownConfig.purchaseRequest', 'saleOrder.pushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'deliveryApplication', '出货申请', 'saleOrder.pushDownConfig.deliveryApplication', 'saleOrder.pushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'saleInAndOut', '销售出入库', 'saleOrder.pushDownConfig.saleInAndOut', 'saleOrder.pushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推生产订单(跳转新页面方式)', 'saleOrder.pushDownConfig.productOrder.jumpPage', 'saleOrder.pushDownConfig.productOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'bom', '按BOM下推生产订单', 'saleOrder.pushDownConfig.productOrder.bom', 'saleOrder.pushDownConfig.productOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'bomBatch', '按BOM下推生产订单(批量)', 'saleOrder.pushDownConfig.productOrder.bomBatch', 'saleOrder.pushDownConfig.productOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'mergeOrder', '按生产订单合单下推', 'saleOrder.pushDownConfig.productOrder.mergeOrder', 'saleOrder.pushDownConfig.productOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'bomMergeOrder', '按BOM合单下推生产订单', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder', 'saleOrder.pushDownConfig.productOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'bomAuto', '自动按BOM下推生产订单', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'saleOrder.pushDownConfig.productOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'craftRoute', '按工艺路线下推生产工单', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'saleOrder.pushDownConfig.workOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'craftRouteBatch', '按工艺路线下推生产工单(批量)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'saleOrder.pushDownConfig.workOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'craftRouteMergeOrder', '按工艺路线合单下推生产工单', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'saleOrder.pushDownConfig.workOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推采购需求(跳转新页面方式)', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage', 'saleOrder.pushDownConfig.purchaseRequest', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推出货申请(跳转新页面方式)', 'saleOrder.pushDownConfig.deliveryApplication.jumpPage', 'saleOrder.pushDownConfig.deliveryApplication', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPageSaleOut', '下推销售出库单(跳转新页面方式)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'saleOrder.pushDownConfig.saleInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPageSalesReturnReceipt', '下推销售退货入库单(跳转新页面方式)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'saleOrder.pushDownConfig.saleInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'listColor', '列表行的颜色标识', 'saleOrder.listConfig.listColor', 'saleOrder.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--

-- 生产
INSERT INTO `dfs_business_config` VALUES (null, 'productOrderPushDownConfig', '生产订单下推配置', 'production.productOrderPushDownConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'productOrderListConfig', '生产订单列表配置', 'production.productOrderListConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'productMaterialsListPushDownConfig', '生产订单用料清单下推配置', 'production.productMaterialsListPushDownConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'productionMaterialsIntelligentRecommendationConfig', '用料清单智能推荐配置', 'production.productionMaterialsIntelligentRecommendationConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'workOrderPushDownConfig', '生产工单下推配置', 'production.workOrderPushDownConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'workOrderVerificationReportConfig', '工单报工校验配置', 'production.workOrderVerificationReportConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'supplementaryReportConfig', '补报功能配置', 'production.supplementaryReportConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'operationOrderVerificationReportConfig', '作业工单报工校验配置', 'production.operationOrderVerificationReportConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'productOrderStateConfig', '生产订单状态配置', 'production.productOrderStateConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'workOrder', '生产工单', 'production.productOrderPushDownConfig.workOrder', 'production.productOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseRequest', '采购需求单', 'production.productOrderPushDownConfig.purchaseRequest', 'production.productOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'productMaterialsList', '生产订单用料清单', 'production.productOrderPushDownConfig.productMaterialsList', 'production.productOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'productionIn', '生产入库单', 'production.productOrderPushDownConfig.productionIn', 'production.productOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'outputPickingProduct', '领料出库单', 'production.productMaterialsListPushDownConfig.outputPickingProduct', 'production.productMaterialsListPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'workOrderSupplement', '生产补料出库单', 'production.productMaterialsListPushDownConfig.workOrderSupplement', 'production.productMaterialsListPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'transferOrder', '调拨单', 'production.productMaterialsListPushDownConfig.transferOrder', 'production.productMaterialsListPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'takeOutApplication', '领料申请', 'production.workOrderPushDownConfig.takeOutApplication', 'production.workOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'productStockInAndOut', '生产出入库', 'production.workOrderPushDownConfig.productStockInAndOut', 'production.workOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'verificationReportQuantity', '数量校验', 'production.workOrderVerificationReportConfig.verificationReportQuantity', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- INSERT INTO `dfs_business_config` VALUES (null, 'multipleOperatorConfiguration', '操作员配置', 'production.workOrderVerificationReportConfig.multipleOperatorConfiguration', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- INSERT INTO `dfs_business_config` VALUES (null, 'reportInputTotalConfiguration', '投入数配置', 'production.workOrderVerificationReportConfig.reportInputTotalConfiguration', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- INSERT INTO `dfs_business_config` VALUES (null, 'productFlowCodeSupplementaryReport', '流水码补报', 'production.supplementaryReportConfig.productFlowCodeSupplementaryReport', 'production.supplementaryReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'verificationReportQuantity', '数量校验', 'production.operationOrderVerificationReportConfig.verificationReportQuantity', 'production.operationOrderVerificationReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推生产工单(跳转新页面方式)', 'production.productOrderPushDownConfig.workOrder.jumpPage', 'production.productOrderPushDownConfig.workOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'craftRoute', '按工艺路线下推生产工单', 'production.productOrderPushDownConfig.workOrder.craftRoute', 'production.productOrderPushDownConfig.workOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'craftRouteBatch', '按工艺路线下推生产工单(批量)', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch', 'production.productOrderPushDownConfig.workOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'craftRouteAuto', '自动按工艺路线下推生产工单', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'production.productOrderPushDownConfig.workOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推采购需求单(跳转新页面方式)', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage', 'production.productOrderPushDownConfig.purchaseRequest', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推生产订单用料清单(跳转新页面方式)', 'production.productOrderPushDownConfig.productMaterialsList.jumpPage', 'production.productOrderPushDownConfig.productMaterialsList', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推生产入库单(跳转新页面方式)', 'production.productOrderPushDownConfig.productionIn.jumpPage', 'production.productOrderPushDownConfig.productionIn', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'listColor', '列表行的颜色标识', 'production.productOrderListConfig.listColor', 'production.productOrderListConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '领料出库单(跳转新页面方式)', 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage', 'production.productMaterialsListPushDownConfig.outputPickingProduct', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '生产补料出库单(跳转新页面方式)', 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage', 'production.productMaterialsListPushDownConfig.workOrderSupplement', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推调拨单(跳转新页面方式)', 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage', 'production.productMaterialsListPushDownConfig.transferOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'intelligentRecommendationConfig', '智能推荐配置', 'production.productionMaterialsIntelligentRecommendationConfig.intelligentRecommendationConfig', 'production.productionMaterialsIntelligentRecommendationConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推领料申请单(跳转新页面方式)', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'production.workOrderPushDownConfig.takeOutApplication', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPageOutputPickingProduct', '生产领料出库单(跳转新页面方式)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'production.workOrderPushDownConfig.productStockInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPageWorkOrderSupplement', '生产补料出库单(跳转新页面方式)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'production.workOrderPushDownConfig.productStockInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPageProductionReturnReceipt', '生产退料入库单(跳转新页面方式)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'production.workOrderPushDownConfig.productStockInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPageProductionIn', '生产入库单(跳转新页面方式)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'production.workOrderPushDownConfig.productStockInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'quantityVerificationEnable', '报工数量校验(手动报工)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'verCompleted', '完成数大于计划数校验', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'verExcessCompletedRatio', '完成数超额校验', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'verInputCountMoreThanProcedureCount', '投入数大于上工序完成数校验', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verInputCountMoreThanProcedureCount', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'verFinishCountMoreThanInputCount', '完成数大于投入数校验', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanInputCount', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'verFinishCountMoreThanProcedureCount', '完成数大于上工序完成数校验', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanProcedureCount', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'applets', '适用小程序(多选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.applets', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'procedures', '适用工序(多选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.procedures', 'production.workOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'multipleOperatorConfiguration', '操作员配置', 'production.workOrderVerificationReportConfig.multipleOperatorConfiguration', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'reportInputTotalConfiguration', '投入数配置', 'production.workOrderVerificationReportConfig.reportInputTotalConfiguration', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'productFlowCodeSupplementaryReport', '流水码补报', 'production.supplementaryReportConfig.productFlowCodeSupplementaryReport', 'production.supplementaryReportConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'quantityVerificationEnable', '报工数量校验(手动报工)', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable', 'production.operationOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'verCompleted', '完成数大于计划数校验', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'production.operationOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'verExcessCompletedRatio', '完成数超额校验', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'production.operationOrderVerificationReportConfig.verificationReportQuantity', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'autoCompleteConfig', '自动完成配置', 'production.productOrderStateConfig.autoCompleteConfig', 'production.productOrderStateConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- )--
-- 采购
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseRequestPushDownConfig', '采购需求下推配置', 'purchase.purchaseRequestPushDownConfig', 'purchase', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseOrderPushDownConfig', '采购订单下推配置', 'purchase.purchaseOrderPushDownConfig', 'purchase', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseReceiptPushDownConfig', '采购收料下推配置', 'purchase.purchaseReceiptPushDownConfig', 'purchase', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseReturnApplicationPushDownConfig', '采购退料申请单下推配置', 'purchase.purchaseReturnApplicationPushDownConfig', 'purchase', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseOrderConfig', '采购订单配置', 'purchase.purchaseOrderConfig', 'purchase', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'purchaseOrder', '采购订单', 'purchase.purchaseRequestPushDownConfig.purchaseOrder', 'purchase.purchaseRequestPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'supplierBom', '来料检验', 'purchase.purchaseOrderPushDownConfig.supplierBom', 'purchase.purchaseOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseReceipt', '采购收货', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt', 'purchase.purchaseOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseIn', '采购入库', 'purchase.purchaseOrderPushDownConfig.purchaseIn', 'purchase.purchaseOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'requestStockInAndOut', '采购出入库', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut', 'purchase.purchaseReceiptPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'returnOrder', '采购退料', 'purchase.purchaseReceiptPushDownConfig.returnOrder', 'purchase.purchaseReceiptPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'purchaseReturnOut', '采购退料出库单', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut', 'purchase.purchaseReturnApplicationPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推采购订单(跳转新页面方式)', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage', 'purchase.purchaseRequestPushDownConfig.purchaseOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推来料检验单(跳转新页面方式)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'purchase.purchaseOrderPushDownConfig.supplierBom', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推采购收货单(跳转新页面方式)', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推采购入库单(跳转新页面方式)', 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage', 'purchase.purchaseOrderPushDownConfig.purchaseIn', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPagePurchaseIn', '采购入库单(跳转新页面方式)', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPageReturnOrder', '采购退料单(跳转新页面方式)', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder', 'purchase.purchaseReceiptPushDownConfig.returnOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '采购退料出库单(跳转新页面方式)', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'verificationPurchaseDemandQuantity', '采购数量校验', 'purchase.purchaseOrderConfig.verificationPurchaseDemandQuantity', 'purchase.purchaseOrderConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- )--

-- 发货
INSERT INTO `dfs_business_config` VALUES (null, 'deliveryApplicationPushDownConfig', '出货申请单下推配置', 'delivery.deliveryApplicationPushDownConfig', 'delivery', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'saleInAndOut', '销售出库单', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut', 'delivery.deliveryApplicationPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'transferOrder', '调拨单', 'delivery.deliveryApplicationPushDownConfig.transferOrder', 'delivery.deliveryApplicationPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推销售出库单(跳转新页面方式)', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推调拨单(跳转新页面方式)', 'delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage', 'delivery.deliveryApplicationPushDownConfig.transferOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--

-- 委外
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractOrderPushDownConfig', '委外订单下推配置', 'subcontract.subcontractOrderPushDownConfig', 'subcontract', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractMaterialsListPushDownConfig', '委外订单用料清单下推配置', 'subcontract.subcontractMaterialsListPushDownConfig', 'subcontract', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractReceiptOrderPushDownConfig', '委外订单收货下推配置', 'subcontract.subcontractReceiptOrderPushDownConfig', 'subcontract', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'subcontractReceiptOrder', '委外订单收货单', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder', 'subcontract.subcontractOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractMaterialsList', '委外订单用料清单', 'subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList', 'subcontract.subcontractOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'subcontractOutputPicking', '委外领料出库单', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking', 'subcontract.subcontractMaterialsListPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractSupplement', '委外补料出库单', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement', 'subcontract.subcontractMaterialsListPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'transferOrder', '调拨单', 'subcontract.subcontractMaterialsListPushDownConfig.transferOrder', 'subcontract.subcontractMaterialsListPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'subcontractIn', '委外入库单', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn', 'subcontract.subcontractReceiptOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '委外订单收货单(跳转新页面方式)', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.jumpPage', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '委外订单用料清单(跳转新页面方式)', 'subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList.jumpPage', 'subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推委外领料出库单(跳转新页面方式)', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking.jumpPage', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推委外补料出库单(跳转新页面方式)', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement.jumpPage', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推调拨单(跳转新页面方式)', 'subcontract.subcontractMaterialsListPushDownConfig.transferOrder.jumpPage', 'subcontract.subcontractMaterialsListPushDownConfig.transferOrder', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '委外入库单(跳转新页面方式)', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.jumpPage', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- )--

-- 小程序
INSERT INTO `dfs_business_config` VALUES (null, 'appletConfig', '小程序配置', 'applet.appletConfig', 'applet', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'license', '权限过滤', 'applet.appletConfig.license', 'applet.appletConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--

-- 工厂模型模块
INSERT INTO `dfs_business_config` VALUES (null, 'workCenterConfig', '工作中心配置', 'factoryModel.workCenterConfig', 'factoryModel', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'productionBasicUnitConfig', '生产基本单元配置', 'factoryModel.workCenterConfig.productionBasicUnitConfig', 'factoryModel.workCenterConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--

-- 物料模块
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'materialSkuConfig', '物料特征参数配置', 'material.materialSkuConfig', 'material', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--

-- 设计模块
INSERT INTO `dfs_business_config` VALUES (null, 'craftConfig', '工艺配置', 'design.craftConfig', 'design', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'defaultCraftConfig', '默认工艺配置', 'design.craftConfig.defaultCraftConfig', 'design.craftConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--

-- 仓库模块
INSERT INTO `dfs_business_config` VALUES (null, 'warehouseViewPermissionConfig', '仓库查看权限配置', 'warehouse.warehouseViewPermissionConfig', 'warehouse', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'accountPermission', '物料台账', 'warehouse.warehouseViewPermissionConfig.accountPermission', 'warehouse.warehouseViewPermissionConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'batchPermission', '物料批次', 'warehouse.warehouseViewPermissionConfig.batchPermission', 'warehouse.warehouseViewPermissionConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'warehouseBillPermission', '出入库单', 'warehouse.warehouseViewPermissionConfig.warehouseBillPermission', 'warehouse.warehouseViewPermissionConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- )--

-- 采购出入库
INSERT INTO `dfs_business_config` VALUES (null, 'purchaseInPushDownConfig', '采购入库单下推配置', 'purchaseInAndOut.purchaseInPushDownConfig', 'purchaseInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'purchaseReturnOut', '采购退料出库单', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut', 'purchaseInAndOut.purchaseInPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推采购退料出库单(跳转新页面方式)', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.jumpPage', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- )--

-- 委外出入库
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractProductOutboundPushDownConfig', '委外领料出库单下推配置', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig', 'subcontractInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractSupplementaryFoodPushDownConfig', '委外补料出库单下推配置', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig', 'subcontractInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'subcontractReturnReceipt', '委外退料入库单', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'subcontractReturnReceipt', '委外退料入库单', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推委外退料入库单(跳转新页面方式)', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推委外退料入库单(跳转新页面方式)', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

-- )--

-- 销售出入库
INSERT INTO `dfs_business_config` VALUES (null, 'saleOutPushDownConfig', '销售出库单下推配置', 'saleInAndOut.saleOutPushDownConfig', 'saleInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'salesReturnReceiptDoc', '销售退货入库单', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc', 'saleInAndOut.saleOutPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推销售退货入库单(跳转新页面方式)', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--

-- 其他出入库
INSERT INTO `dfs_business_config` VALUES (null, 'stockOtherAppFormPushDownConfig', '其他出库申请单下推配置', 'otherInAndOut.stockOtherAppFormPushDownConfig', 'otherInAndOut', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());

INSERT INTO `dfs_business_config` VALUES (null, 'stockAllocation', '调拨单', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation', 'otherInAndOut.stockOtherAppFormPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- (--
INSERT INTO `dfs_business_config` VALUES (null, 'jumpPage', '下推调拨单(跳转新页面方式)', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.jumpPage', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
-- )--


truncate table dfs_business_config_value;

-- 销售
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.productOrder.jumpPage.enable', 'saleOrder.pushDownConfig.productOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.productOrder.jumpPage.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.jumpPage', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.productOrder.bom.enable', 'saleOrder.pushDownConfig.productOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.productOrder.bom.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bom', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.productOrder.bom.productOrderState', 'saleOrder.pushDownConfig.productOrder.bom', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'saleOrder.pushDownConfig.productOrder.bom.bomSplitTypes', 'saleOrder.pushDownConfig.productOrder.bom', 'select-multiple', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBom","multiLevelBom","firstLevelBomNotFilter","multiLevelBomNotFilter"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.productOrder.bom.enableFilterOutsourcing', 'saleOrder.pushDownConfig.productOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.productOrder.bom.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bom', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.productOrder.bomBatch.enable', 'saleOrder.pushDownConfig.productOrder.bomBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.productOrder.bomBatch.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bomBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.productOrder.bomBatch.productOrderState', 'saleOrder.pushDownConfig.productOrder.bomBatch', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'saleOrder.pushDownConfig.productOrder.bomBatch.bomSplitTypes', 'saleOrder.pushDownConfig.productOrder.bomBatch', 'select-multiple', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBom","multiLevelBom","firstLevelBomNotFilter","multiLevelBomNotFilter"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.productOrder.bomBatch.enableFilterOutsourcing', 'saleOrder.pushDownConfig.productOrder.bomBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.productOrder.bomBatch.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bomBatch', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.productOrder.mergeOrder.enable', 'saleOrder.pushDownConfig.productOrder.mergeOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.productOrder.mergeOrder.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.mergeOrder', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.productOrder.mergeOrder.productOrderState', 'saleOrder.pushDownConfig.productOrder.mergeOrder', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.enable', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.productOrderState', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.bomSplitTypes', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder', 'select-multiple', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBom","multiLevelBom","firstLevelBomNotFilter","multiLevelBomNotFilter"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.enableFilterOutsourcing', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bomMergeOrder', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.enable', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.productOrderState', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitType', 'BOM拆分方式(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.bomSplitType', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBom","multiLevelBom","firstLevelBomNotFilter","multiLevelBomNotFilter"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.enableFilterOutsourcing', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'ruleId', '生产订单编码规则(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.ruleId', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/api/numberRule/getDetailByType?typeCode=10', 'get', NULL, 'id,prefixDetailName', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.enable', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.originalOrderStates', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.productOrderState', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.bomSplitTypes', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select-multiple', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBom","multiLevelBom"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.enableFilterOutsourcing', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.filterMaterialTypes', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'workOrderState', '生产工单状态(单选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.workOrderState', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'defaultCraft', '是否支持默认工艺(单选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.defaultCraft', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'craftSplitType', '工艺拆分方式(单选)', 'saleOrder.pushDownConfig.workOrder.craftRoute.craftSplitType', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.enable', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.originalOrderStates', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.productOrderState', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.bomSplitTypes', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select-multiple', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBom","multiLevelBom"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.enableFilterOutsourcing', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.filterMaterialTypes', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'workOrderState', '生产工单状态(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.workOrderState', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'defaultCraft', '是否支持默认工艺(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.defaultCraft', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'craftSplitType', '工艺拆分方式(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch.craftSplitType', 'saleOrder.pushDownConfig.workOrder.craftRouteBatch', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.enable', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.originalOrderStates', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.productOrderState', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.bomSplitTypes', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select-multiple', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBom","multiLevelBom"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.enableFilterOutsourcing', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.filterMaterialTypes', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'workOrderState', '生产工单状态(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.workOrderState', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'defaultCraft', '是否支持默认工艺(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.defaultCraft', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'craftSplitType', '工艺拆分方式(单选)', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.craftSplitType', 'saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.enable', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.originalOrderStates', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.bomSplitTypes', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage', 'select-multiple', 'api', '/api/config/push/down/not/filter/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBomFilterSelf","multiLevelBomFilterSelf"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.enableFilterOutsourcing', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterProduct', '是否过滤生产品(单选)', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.enableFilterProduct', 'saleOrder.pushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.deliveryApplication.jumpPage.enable', 'saleOrder.pushDownConfig.deliveryApplication.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.deliveryApplication.jumpPage.originalOrderStates', 'saleOrder.pushDownConfig.deliveryApplication.jumpPage', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.enable', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.originalOrderStates', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.enable', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.originalOrderStates', 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'markMeetDelivery', '标识生产数量满足发货', 'saleOrder.listConfig.listColor.markMeetDelivery', 'saleOrder.listConfig.listColor', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'markNotMeetDelivery', '标识已排产(不满足发货)', 'saleOrder.listConfig.listColor.markNotMeetDelivery', 'saleOrder.listConfig.listColor', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

-- 生产
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderPushDownConfig.workOrder.jumpPage.enable', 'production.productOrderPushDownConfig.workOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productOrderPushDownConfig.workOrder.jumpPage.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.jumpPage', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderPushDownConfig.workOrder.craftRoute.enable', 'production.productOrderPushDownConfig.workOrder.craftRoute', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productOrderPushDownConfig.workOrder.craftRoute.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.craftRoute', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'workOrderState', '生产工单状态(单选)', 'production.productOrderPushDownConfig.workOrder.craftRoute.workOrderState', 'production.productOrderPushDownConfig.workOrder.craftRoute', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'defaultCraft', '是否支持默认工艺(单选)', 'production.productOrderPushDownConfig.workOrder.craftRoute.defaultCraft', 'production.productOrderPushDownConfig.workOrder.craftRoute', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'craftSplitType', '工艺拆分方式(单选)', 'production.productOrderPushDownConfig.workOrder.craftRoute.craftSplitType', 'production.productOrderPushDownConfig.workOrder.craftRoute', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch.enable', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'workOrderState', '生产工单状态(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch.workOrderState', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'defaultCraft', '是否支持默认工艺(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch.defaultCraft', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'craftSplitType', '工艺拆分方式(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch.craftSplitType', 'production.productOrderPushDownConfig.workOrder.craftRouteBatch', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.enable', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'workOrderState', '生产工单状态(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.workOrderState', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'defaultCraft', '是否支持默认工艺(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.defaultCraft', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'craftSplitType', '工艺拆分方式(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.craftSplitType', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'ruleId', '生产订单编码规则(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.ruleId', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/numberRule/getDetailByType?typeCode=2', 'get', NULL, 'id,prefixDetailName', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.enable', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.originalOrderStates', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.bomSplitTypes', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage', 'select-multiple', 'api', '/api/config/push/down/not/filter/bom/split', 'get', NULL, 'code,name', '["notSplit","firstLevelBomFilterSelf","multiLevelBomFilterSelf"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.enableFilterOutsourcing', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterProduct', '是否过滤生产品(单选)', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.enableFilterProduct', 'production.productOrderPushDownConfig.purchaseRequest.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderPushDownConfig.productMaterialsList.jumpPage.enable', 'production.productOrderPushDownConfig.productMaterialsList.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productOrderPushDownConfig.productMaterialsList.jumpPage.originalOrderStates', 'production.productOrderPushDownConfig.productMaterialsList.jumpPage', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderPushDownConfig.productionIn.jumpPage.enable', 'production.productOrderPushDownConfig.productionIn.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productOrderPushDownConfig.productionIn.jumpPage.originalOrderStates', 'production.productOrderPushDownConfig.productionIn.jumpPage', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'markMeetDelivery', '标识已完成', 'production.productOrderListConfig.listColor.markMeetDelivery', 'production.productOrderListConfig.listColor', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'markNotMeetDelivery', '标识生产中', 'production.productOrderListConfig.listColor.markNotMeetDelivery', 'production.productOrderListConfig.listColor', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.enable', 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.originalOrderStates', 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.enable', 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.originalOrderStates', 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage.enable', 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage.originalOrderStates', 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productionMaterialsIntelligentRecommendationConfig.intelligentRecommendationConfig.enable', 'production.productionMaterialsIntelligentRecommendationConfig.intelligentRecommendationConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enabledFeatures', '启用功能(多选)', 'production.productionMaterialsIntelligentRecommendationConfig.intelligentRecommendationConfig.enabledFeatures', 'production.productionMaterialsIntelligentRecommendationConfig.intelligentRecommendationConfig', 'select-multiple', 'api', '/api/config/product/order/material/list/smart/config/list', 'get', NULL, 'code,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage.enable', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage.originalOrderStates', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'bomSplitTypes', 'BOM拆分方式(多选)', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage.bomSplitTypes', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'select-multiple', 'api', '/api/config/push/down/not/filter/bom/split', 'get', NULL, 'code,name', '["firstLevelBomNotFilter","multiLevelBomNotFilter","firstLevelBomFilterSelf","multiLevelBomFilterSelf"]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage.enableFilterOutsourcing', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'enableFilterProduct', '是否过滤生产品(单选)', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage.enableFilterProduct', 'production.workOrderPushDownConfig.takeOutApplication.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.enable', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.enable', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.enable', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.enable', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.originalOrderStates', 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[3,4,5]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable.enable', 'production.workOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'verCompletedEnable', '校验方式(单选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted.verCompletedEnable', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"null","label":"无"},{"value":"Tips","label":"提示语"},{"value":"Limit","label":"限制语"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verCompletedTips', '提示语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted.verCompletedTips', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verCompletedLimit', '限制语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted.verCompletedLimit', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedRatio', '超额比例(%)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedRatio', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedEnable', '校验方式(单选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedEnable', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"null","label":"无"},{"value":"Tips","label":"提示语"},{"value":"Limit","label":"限制语"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedTips', '提示语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedTips', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedLimit', '限制语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedLimit', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'inputOverLastCompletedEnable', '校验方式(单选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verInputCountMoreThanProcedureCount.inputOverLastCompletedEnable', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verInputCountMoreThanProcedureCount', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"null","label":"无"},{"value":"Tips","label":"提示语"},{"value":"Limit","label":"限制语"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'inputOverLastCompletedTips', '提示语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verInputCountMoreThanProcedureCount.inputOverLastCompletedTips', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verInputCountMoreThanProcedureCount', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'inputOverLastCompletedLimit', '限制语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verInputCountMoreThanProcedureCount.inputOverLastCompletedLimit', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verInputCountMoreThanProcedureCount', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'completedOverInputEnable', '校验方式(单选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanInputCount.completedOverInputEnable', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanInputCount', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"null","label":"无"},{"value":"Tips","label":"提示语"},{"value":"Limit","label":"限制语"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'completedOverInputTips', '提示语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanInputCount.completedOverInputTips', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanInputCount', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'completedOverInputLimit', '限制语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanInputCount.completedOverInputLimit', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanInputCount', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'completedOverLastCompletedEnable', '校验方式(单选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanProcedureCount.completedOverLastCompletedEnable', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanProcedureCount', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"null","label":"无"},{"value":"Tips","label":"提示语"},{"value":"Limit","label":"限制语"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'completedOverLastCompletedTips', '提示语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanProcedureCount.completedOverLastCompletedTips', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanProcedureCount', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'completedOverLastCompletedLimit', '限制语', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanProcedureCount.completedOverLastCompletedLimit', 'production.workOrderVerificationReportConfig.verificationReportQuantity.verFinishCountMoreThanProcedureCount', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'applicableApplet', '适用小程序(多选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.applets.applicableApplet', 'production.workOrderVerificationReportConfig.verificationReportQuantity.applets', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{"value":"yelink.workOrder.report.com","label":"工单报工"},{"value":"yelink.workorder.report","label":"订单报工"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'applicableProcedure', '适用工序(多选)', 'production.workOrderVerificationReportConfig.verificationReportQuantity.procedures.applicableProcedure', 'production.workOrderVerificationReportConfig.verificationReportQuantity.procedures', 'select-multiple', 'pageApi', '/api/procedures/list', 'get', NULL, 'procedureId,name', NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '操作员是否可多选', 'production.workOrderVerificationReportConfig.multipleOperatorConfiguration.enable', 'production.workOrderVerificationReportConfig.multipleOperatorConfiguration', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '投入数是否开启', 'production.workOrderVerificationReportConfig.reportInputTotalConfiguration.enable', 'production.workOrderVerificationReportConfig.reportInputTotalConfiguration', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'addReportEnable', '是否启用(单选)', 'production.supplementaryReportConfig.productFlowCodeSupplementaryReport.addReportEnable', 'production.supplementaryReportConfig.productFlowCodeSupplementaryReport', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable.enable', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'production.productOrderStateConfig.autoCompleteConfig.enable', 'production.productOrderStateConfig.autoCompleteConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'verCompletedEnable', '校验方式(单选)', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted.verCompletedEnable', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"null","label":"无"},{"value":"Tips","label":"提示语"},{"value":"Limit","label":"限制语"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verCompletedTips', '提示语', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted.verCompletedTips', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verCompletedLimit', '限制语', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted.verCompletedLimit', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedRatio', '超额比例(%)', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedRatio', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedEnable', '校验方式(单选)', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedEnable', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"null","label":"无"},{"value":"Tips","label":"提示语"},{"value":"Limit","label":"限制语"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedTips', '提示语', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedTips', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'verExcessCompletedLimit', '限制语', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio.verExcessCompletedLimit', 'production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

-- 采购
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.enable', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.originalOrderStates', 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage', 'select-multiple', 'api', '/ams/requests/state', 'get', NULL, 'code,name', '[4]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.enable', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.enable', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.enable', 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.originalOrderStates', 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn.enable', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder.enable', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder.originalOrderStates', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder', 'select-multiple', 'api', '/ams/receipt/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.enable', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.originalOrderStates', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage', 'select-multiple', 'api', '/ams/purchases/return/state', 'get', NULL, 'code,name', '[2]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enablePurchaseDemandQuantityConfig', '是否允许采购数量大于需求数量(单选)', 'purchase.purchaseOrderConfig.verificationPurchaseDemandQuantity.enablePurchaseDemandQuantityConfig', 'purchase.purchaseOrderConfig.verificationPurchaseDemandQuantity', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

-- 发货
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage.enable', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage.originalOrderStates', 'delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage', 'select-multiple', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[4]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage.enable', 'delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage.originalOrderStates', 'delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage', 'select-multiple', 'api', '/api/delivery/application/state', 'get', NULL, 'code,name', '[4]', NULL);

-- 委外
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.jumpPage.enable', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.jumpPage.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList.jumpPage.enable', 'subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList.jumpPage.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking.jumpPage.enable', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking.jumpPage.originalOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement.jumpPage.enable', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement.jumpPage.originalOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontract.subcontractMaterialsListPushDownConfig.transferOrder.jumpPage.enable', 'subcontract.subcontractMaterialsListPushDownConfig.transferOrder.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontract.subcontractMaterialsListPushDownConfig.transferOrder.jumpPage.originalOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.transferOrder.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.jumpPage.enable', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.jumpPage.originalOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.jumpPage', 'select-multiple', 'api', '/ams/purchases/state', 'get', NULL, 'code,name', '[2,3]', NULL);

-- 小程序
INSERT INTO `dfs_business_config_value` VALUES (null, 'configFormat', '配置格式', 'applet.appletConfig.license.configFormat', 'applet.appletConfig.license', 'span', 'input', NULL, NULL, NULL, NULL, NULL, '"JSON"');
INSERT INTO `dfs_business_config_value` VALUES (null, 'configContent', '配置内容', 'applet.appletConfig.license.configContent', 'applet.appletConfig.license', 'json', 'input', NULL, NULL, NULL, NULL, NULL, NULL);

-- 工厂模型模块
INSERT INTO `dfs_business_config_value` VALUES (null, 'productionBasicUnit', '生产基本单元', 'factoryModel.workCenterConfig.productionBasicUnitConfig.productionBasicUnit', 'factoryModel.workCenterConfig.productionBasicUnitConfig', 'select', 'api', '/api/work/center/types', 'get', NULL, 'type,name', NULL, NULL);

-- 物料模块
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'material.materialSkuConfig.enable', 'material.materialSkuConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

-- 设计模块
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'design.craftConfig.defaultCraftConfig.enable', 'design.craftConfig.defaultCraftConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

-- 仓库模块
INSERT INTO `dfs_business_config_value` VALUES (null, 'warehousePermission', '是否启用(单选)', 'warehouse.warehouseViewPermissionConfig.accountPermission.warehousePermission', 'warehouse.warehouseViewPermissionConfig.accountPermission', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'warehousePermission', '是否启用(单选)', 'warehouse.warehouseViewPermissionConfig.batchPermission.warehousePermission', 'warehouse.warehouseViewPermissionConfig.batchPermission', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'warehousePermission', '是否启用(单选)', 'warehouse.warehouseViewPermissionConfig.warehouseBillPermission.warehousePermission', 'warehouse.warehouseViewPermissionConfig.warehouseBillPermission', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);

-- 采购出入库
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.jumpPage.enable', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.jumpPage.originalOrderStates', 'purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.jumpPage', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{"value":3,"label":"完成"}]', NULL);

-- 委外出入库
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage.enable', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage.originalOrderStates', 'subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{"value":3,"label":"完成"}]', NULL);

INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage.enable', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage.originalOrderStates', 'subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{"value":3,"label":"完成"}]', NULL);

-- 销售出入库
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage.enable', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage.originalOrderStates', 'saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{"value":1,"label":"生效"},{"value":2,"label":"完成"}]', NULL);

-- 其他出入库
INSERT INTO `dfs_business_config_value` VALUES (null, 'enable', '是否启用(单选)', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.jumpPage.enable', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'originalOrderStates', '原单状态(多选)', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.jumpPage.originalOrderStates', 'otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.jumpPage', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{"value":1,"label":"生效"},{"value":2,"label":"完成"}]', NULL);

-- 删除重复的旧配置
delete from dfs_common_config where id in (
select id from (
select max(id) as id from dfs_common_config group by operation_object,target_object,operation_type,property_key HAVING count(id) > 1
) a);

-- 迁移旧配置值
call init_new_config_value('saleOrder','productOrder','jumpPage','saleOrder.pushDownConfig.productOrder.jumpPage');
call init_new_config_value('saleOrder','productOrder','bom','saleOrder.pushDownConfig.productOrder.bom');
call init_new_config_value('saleOrder','productOrder','mergeOrder','saleOrder.pushDownConfig.productOrder.mergeOrder');
call init_new_config_value('saleOrder','productOrder','bomMergeOrder','saleOrder.pushDownConfig.productOrder.bomMergeOrder');
call init_new_config_value('saleOrder','productOrder','bomBatch','saleOrder.pushDownConfig.productOrder.bomBatch');
call init_new_config_value('saleOrder','productOrder','bomAuto','saleOrder.pushDownConfig.productOrder.bomAuto');
call init_new_config_value('saleOrder','workOrder','craftRoute','saleOrder.pushDownConfig.workOrder.craftRoute');
call init_new_config_value('saleOrder','workOrder','craftRouteBatch','saleOrder.pushDownConfig.workOrder.craftRouteBatch');
call init_new_config_value('saleOrder','workOrder','craftRouteMergeOrder','saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder');
call init_new_config_value('saleOrder','purchaseRequest','jumpPage','saleOrder.pushDownConfig.purchaseRequest.jumpPage');
call init_new_config_value('saleOrder','deliveryApplication','jumpPage','saleOrder.pushDownConfig.deliveryApplication.jumpPage');
call init_new_config_value('saleOrder','saleInAndOut','jumpPageSaleOut','saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut');
call init_new_config_value('saleOrder','saleInAndOut','jumpPageSalesReturnReceipt','saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt');
call init_new_config_value('saleOrder','','listConfig','saleOrder.listConfig.listColor');
call init_new_config_value('productOrder','workOrder','jumpPage','production.productOrderPushDownConfig.workOrder.jumpPage');
call init_new_config_value('productOrder','workOrder','craftRoute','production.productOrderPushDownConfig.workOrder.craftRoute');
call init_new_config_value('productOrder','workOrder','craftRouteBatch','production.productOrderPushDownConfig.workOrder.craftRouteBatch');
call init_new_config_value('productOrder','workOrder','craftRouteAuto','production.productOrderPushDownConfig.workOrder.craftRouteAuto');
call init_new_config_value('productOrder','purchaseRequest','jumpPage','production.productOrderPushDownConfig.purchaseRequest.jumpPage');
call init_new_config_value('productOrder','productMaterialsList','jumpPage','production.productOrderPushDownConfig.productMaterialsList.jumpPage');
call init_new_config_value('productOrder','productionIn','jumpPage','production.productOrderPushDownConfig.productionIn.jumpPage');
call init_new_config_value('productOrder','','listConfig','production.productOrderListConfig.listColor');
call init_new_config_value('productMaterialsList','outputPickingProduct','jumpPage','production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage');
call init_new_config_value('productMaterialsList','workOrderSupplement','jumpPage','production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage');
call init_new_config_value('productMaterialsList','transferOrder','jumpPage','production.productMaterialsListPushDownConfig.transferOrder.jumpPage');
call init_new_config_value('productMaterialsList','','enableProductOrderMaterialListSmartConfig','production.productionMaterialsIntelligentRecommendationConfig.intelligentRecommendationConfig');
call init_new_config_value('workOrder','takeOutApplication','jumpPage','production.workOrderPushDownConfig.takeOutApplication.jumpPage');
call init_new_config_value('workOrder','productStockInAndOut','jumpPageOutputPickingProduct','production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct');
call init_new_config_value('workOrder','productStockInAndOut','jumpPageWorkOrderSupplement','production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement');
call init_new_config_value('workOrder','productStockInAndOut','jumpPageProductionReturnReceipt','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt');
call init_new_config_value('workOrder','productStockInAndOut','jumpPageProductionIn','production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn');
-- call init_new_config_value('workOrder','','verificationReportQuantity','production.workOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable');
-- call init_new_config_value('workOrder','','verificationReportQuantity','production.workOrderVerificationReportConfig.verificationReportQuantity.verCompleted');
-- call init_new_config_value('workOrder','','verificationReportQuantity','production.workOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio');
-- call init_new_config_value('workOrder','','verificationReportQuantity','production.workOrderVerificationReportConfig.verificationReportQuantity.applets');
call init_new_config_value('workOrder','','multipleOperatorConfiguration','production.workOrderVerificationReportConfig.multipleOperatorConfiguration');
call init_new_config_value('workOrder','','reportInputTotalConfiguration','production.workOrderVerificationReportConfig.reportInputTotalConfiguration');
call init_new_config_value('workOrder','','enableConfig','production.supplementaryReportConfig.productFlowCodeSupplementaryReport');
-- call init_new_config_value('operationOrder','','verificationReportQuantity','production.operationOrderVerificationReportConfig.verificationReportQuantity.quantityVerificationEnable');
-- call init_new_config_value('operationOrder','','verificationReportQuantity','production.operationOrderVerificationReportConfig.verificationReportQuantity.verCompleted');
-- call init_new_config_value('operationOrder','','verificationReportQuantity','production.operationOrderVerificationReportConfig.verificationReportQuantity.verExcessCompletedRatio');
call init_new_config_value('productOrderStateAutoFinish','','enableConfig','production.productOrderStateConfig.autoCompleteConfig');
call init_new_config_value('purchaseRequest','purchaseOrder','jumpPage','purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage');
call init_new_config_value('purchaseOrder','supplierBom','jumpPage','purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage');
call init_new_config_value('purchaseOrder','purchaseReceipt','jumpPage','purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage');
call init_new_config_value('purchaseOrder','purchaseIn','jumpPage','purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage');
call init_new_config_value('purchaseReceipt','requestStockInAndOut','jumpPagePurchaseIn','purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn');
call init_new_config_value('purchaseReceipt','returnOrder','jumpPageReturnOrder','purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder');
call init_new_config_value('purchaseReturnApplication','purchaseReturnOut','jumpPage','purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage');
call init_new_config_value('purchaseOrder','','enablePurchaseDemandQuantityConfig','purchase.purchaseOrderConfig.verificationPurchaseDemandQuantity');
call init_new_config_value('deliveryApplication','saleInAndOut','jumpPage','delivery.deliveryApplicationPushDownConfig.saleInAndOut.jumpPage');
call init_new_config_value('deliveryApplication','transferOrder','jumpPage','delivery.deliveryApplicationPushDownConfig.transferOrder.jumpPage');
call init_new_config_value('subcontractOrder','subcontractReceiptOrder','jumpPage','subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.jumpPage');
call init_new_config_value('subcontractOrder','subcontractMaterialsList','jumpPage','subcontract.subcontractOrderPushDownConfig.subcontractMaterialsList.jumpPage');
call init_new_config_value('subcontractMaterialsList','subcontractOutputPicking','jumpPage','subcontract.subcontractMaterialsListPushDownConfig.subcontractOutputPicking.jumpPage');
call init_new_config_value('subcontractMaterialsList','subcontractSupplement','jumpPage','subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplement.jumpPage');
call init_new_config_value('subcontractMaterialsList','transferOrder','jumpPage','subcontract.subcontractMaterialsListPushDownConfig.transferOrder.jumpPage');
call init_new_config_value('subcontractReceiptOrder','subcontractIn','jumpPage','subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.jumpPage');
-- call init_new_config_value('','','appletsLicense','applet.appletConfig.license');
-- call init_new_config_value('','','productionBasicUnit','factoryModel.workCenterConfig.productionBasicUnitConfig');
call init_new_config_value('materialSku','','enableConfig','material.materialSkuConfig');
call init_new_config_value('defaultCraft','','enableConfig','design.craftConfig.defaultCraftConfig');
call init_new_config_value('accountPermission','','jurisdictionConfig','warehouse.warehouseViewPermissionConfig.accountPermission');
call init_new_config_value('batchPermission','','jurisdictionConfig','warehouse.warehouseViewPermissionConfig.batchPermission');
call init_new_config_value('warehouseBillPermission','','jurisdictionConfig','warehouse.warehouseViewPermissionConfig.warehouseBillPermission');
call init_new_config_value('purchaseIn','purchaseReturnOut','jumpPage','purchaseInAndOut.purchaseInPushDownConfig.purchaseReturnOut.jumpPage');
call init_new_config_value('subcontractProductOutbound','subcontractReturnReceipt','jumpPage','subcontractInAndOut.subcontractProductOutboundPushDownConfig.subcontractReturnReceipt.jumpPage');
call init_new_config_value('subcontractSupplementaryFood','subcontractReturnReceipt','jumpPage','subcontractInAndOut.subcontractSupplementaryFoodPushDownConfig.subcontractReturnReceipt.jumpPage');
call init_new_config_value('salesIssueDoc','salesReturnReceiptDoc','jumpPage','saleInAndOut.saleOutPushDownConfig.salesReturnReceiptDoc.jumpPage');
call init_new_config_value('stockOtherAppForm','stockAllocation','jumpPage','otherInAndOut.stockOtherAppFormPushDownConfig.stockAllocation.jumpPage');

-- 特殊值处理
call init_new_config_value('workOrder','','verificationReportQuantity','production.workOrderVerificationReportConfig.verificationReportQuantity.%');
call init_new_config_value('operationOrder','','verificationReportQuantity','production.operationOrderVerificationReportConfig.verificationReportQuantity.%');
update dfs_business_config_value v set value = (select JSON_ARRAYAGG(JSON_OBJECT('id', target_object, 'enable', property_value)) as value from dfs_common_config where operation_type ='appletsLicense')
where v.value_full_path_code = 'applet.appletConfig.license.configContent';
update dfs_business_config_value v set value = (select concat('"',code,'"') as value from dfs_dict where type = 'productionBasicUnit') where v.config_full_path_code = 'factoryModel.workCenterConfig.productionBasicUnitConfig';
update dfs_business_config_value set value = '"Tips"' where value = '["Tips"]';-- 取值变动
update dfs_business_config_value set value = '"Limit"' where value = '["Limit"]';
update dfs_business_config_value v set value = (select REPLACE(REPLACE(property_value,'[',''),']','') as value from dfs_common_config where operation_object= 'saleOrder' and target_object = 'productOrder' and operation_type = 'bomAuto' and property_key = 'bomSplitTypes')
where v.value_full_path_code = 'saleOrder.pushDownConfig.productOrder.bomAuto.bomSplitType';
update dfs_business_config_value set value = REPLACE(value,'"','')+0 where value_code = 'ruleId';-- 转成数字
update dfs_business_config_value set value = null where value_code = 'ruleId' and value = 0;
update dfs_business_config_value set `value` = '"multiLevelBom"' where value_full_path_code = 'saleOrder.pushDownConfig.productOrder.bomAuto.bomSplitType' and `value` like '%,%';
update dfs_business_config_value set `value` = '"null"' where value_name = '校验方式(单选)' and (`value` = '' or `value` = '[]' or `value` = '["null"]');

-- 注意！！！！！！！！！！该脚本为新业务配置迁移的初始化脚本，请勿在此脚本添加sql！！！！！！！！！！！！！！
-- 注意！！！！！！！！！！该脚本为新业务配置迁移的初始化脚本，请勿在此脚本添加sql！！！！！！！！！！！！！！
-- 注意！！！！！！！！！！该脚本为新业务配置迁移的初始化脚本，请勿在此脚本添加sql！！！！！！！！！！！！！！
