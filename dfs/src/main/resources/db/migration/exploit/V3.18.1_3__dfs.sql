-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- *该脚本需要在V3_18_1_3__JavaMigration前执行


-- 产线指标
-- 是否计算节拍开关
call proc_add_column(
        'dfs_production_line',
        'is_cal_beat',
        'ALTER TABLE `dfs_production_line` ADD COLUMN `is_cal_beat` tinyint(1) NULL DEFAULT 0 COMMENT ''是否计算节拍''');
DELETE FROM `dfs_target_model` WHERE `target_name` in ('lineOEE', 'lineBeat');
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('lineGroupOEE', '产线oee', NULL, 'baseTargetGroupObject', 'lineTarget', NULL, NULL, NULL, '2021-03-25 10:20:11', '2021-03-25 10:20:11', NULL, 1, 1, 'm');
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('lineGroupBeat', '产线节拍', NULL, 'baseTargetGroupObject', 'lineTarget', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL, 1, 1, 'm');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('lineGroupOEE', (SELECT id FROM `dfs_model` WHERE `code` = 'lineTarget'), 'dfs', 'dfs_record_line_oee');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('lineGroupBeat', (SELECT id FROM `dfs_model` WHERE `code` = 'lineTarget'), 'dfs', 'dfs_record_line_beat');

INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('lineGroupOEE', 'lineTarget', '产线oee', 'lineOEE', 1, NULL);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('lineGroupBeat', 'lineTarget', '产线节拍=时间/数量', 'lineBeat', 1, NULL);
INSERT INTO `dfs_target_model`(`target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `frequency_unit`, `alarm_definition_code`, `is_count_target`, `run_target_status_formula`, `not_run_target_status_formula`, `stop_target_status_formula`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `data_type`, `data_decimal_point`, `remark`, `source`, `script`, `data_source_type`, `enable`) VALUES ('lineGroupOEE', '产线oee', (SELECT id FROM `dfs_model` WHERE `code` = 'lineTarget'), 'lineOEE', '产线oee', NULL, 1, NULL, 'm', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-08-12 11:11:13', '2024-08-12 11:11:13', 'number', 1, NULL, '自动', NULL, 'inner', 1);
INSERT INTO `dfs_target_model`(`target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `frequency_unit`, `alarm_definition_code`, `is_count_target`, `run_target_status_formula`, `not_run_target_status_formula`, `stop_target_status_formula`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `data_type`, `data_decimal_point`, `remark`, `source`, `script`, `data_source_type`, `enable`) VALUES ('lineGroupBeat', '产线节拍', (SELECT id FROM `dfs_model` WHERE `code` = 'lineTarget'), 'lineBeat', '产线节拍=时间/数量', NULL, 1, NULL, 'm', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-08-12 11:11:13', '2024-08-12 11:11:13', 'number', 1, NULL, '自动', NULL, 'inner', 1);


INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_oee', '产线OEE实时记录表', 'id', '记录id', 'int', 11, 0, NULL, NULL, 'input', 'input', NULL, NULL, NULL);
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_oee', '产线OEE实时记录表', 'line_id', '产线id', 'int', 11, 0, NULL, NULL, 'input', 'input', NULL, NULL, 'DIMENSION');
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_oee', '产线OEE实时记录表', 'oee', 'oee', 'double', 11, 6, NULL, '理论工作时间总和/当天工作时长', 'input', 'input', NULL, NULL, 'TARGET');
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_oee', '产线OEE实时记录表', 'time', '记录时间', 'datetime', 0, 0, NULL, NULL, 'input', 'input', NULL, NULL, 'ATTACHMENT');
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_beat', '产线节拍实时记录表', 'id', '记录id', 'int', 11, 0, NULL, NULL, 'input', 'input', NULL, NULL, NULL);
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_beat', '产线节拍实时记录表', 'line_id', '产线id', 'int', 11, 0, NULL, NULL, 'input', 'input', NULL, NULL, 'DIMENSION');
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_beat', '产线节拍实时记录表', 'beat_5min', '5分钟节拍', 'double', 11, 2, NULL, NULL, 'input', 'input', NULL, NULL, 'TARGET');
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_beat', '产线节拍实时记录表', 'beat_10min', '10分钟节拍', 'double', 11, 2, NULL, NULL, 'input', 'input', NULL, NULL, 'TARGET');
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_beat', '产线节拍实时记录表', 'beat_60min', '60分钟节拍', 'double', 11, 2, NULL, NULL, 'input', 'input', NULL, NULL, 'TARGET');
INSERT INTO `dfs_table_config` (`table_schema`, `table_name`, `table_remark`, `field_code`, `field_name`, `field_type`, `field_length`, `field_point`, `field_default_value`, `field_remark`, `input_type`, `option_values_type`, `all_option`, `field_mapping`, `field_define`) VALUES ('dfs', 'dfs_record_line_beat', '产线节拍实时记录表', 'time', '记录时间', 'datetime', 0, 0, NULL, NULL, 'input', 'input', NULL, NULL, 'ATTACHMENT');



-- 新增的销售订单
 -- 每日指标
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_sale_order_summary_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `record_date` datetime NOT NULL COMMENT '记录日期',
    `material_row_add_count` int(11) DEFAULT '0' COMMENT '新增物料行数',
    `material_row_delivery_count` int(11) DEFAULT '0' COMMENT '发货物料行数',
    `material_row_delay_count` int(11) DEFAULT '0' COMMENT '超期物料行数',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`record_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单汇总 每日统计';
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('saleOrderSummaryDaily', '销售订单汇总 每日统计', NULL, 'baseTargetGroupObject', 'saleOrderTarget', NULL, NULL, NULL, '2021-03-25 10:20:11', '2021-03-25 10:20:11', NULL, 1, 1, 'm');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('saleOrderSummaryDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'saleOrderTarget'), 'dfs_metrics', 'dfs_metrics_sale_order_summary_daily');

INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('saleOrderSummaryDaily', 'saleOrderTarget', '系统统计', 'saleOrderSummaryDaily', 1, NULL);
INSERT INTO `dfs_target_model`(`target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `frequency_unit`, `alarm_definition_code`, `is_count_target`, `run_target_status_formula`, `not_run_target_status_formula`, `stop_target_status_formula`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `data_type`, `data_decimal_point`, `remark`, `source`, `script`, `data_source_type`, `enable`) VALUES ('saleOrderSummaryDaily', '销售订单汇总 每日统计', (SELECT id FROM `dfs_model` WHERE `code` = 'saleOrderTarget'), 'saleOrderSummaryDaily', '系统统计', NULL, 1, NULL, 'm', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-08-12 11:11:13', '2024-08-12 11:11:13', 'number', 1, NULL, '自动', NULL, 'inner', 1);


-- 每月指标 客户数统计
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'customer_add_count',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `customer_add_count` int(11) NULL COMMENT ''新增客户数'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'customer_repeat_count',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `customer_repeat_count` int(11) NULL COMMENT ''复购客户数'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'customer_active_count',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `customer_active_count` int(11) NULL COMMENT ''活跃客户数'';');


-- 工单工序
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_procedure',
        'crossing_finish_quantity',
        'ALTER TABLE `dfs_metrics_work_order_procedure` ADD COLUMN `crossing_finish_quantity` double(11,2) NULL COMMENT ''过站完成数'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_procedure',
        'procedure_attrition_rate',
        'ALTER TABLE `dfs_metrics_work_order_procedure` ADD COLUMN `procedure_attrition_rate` double(11,4) NULL COMMENT ''工序损耗率'';');


-- 订单工序 重构
rename table `dfs_metrics`.`dfs_metrics_product_order_procedure` to `dfs_metrics`.`dfs_metrics_product_order_procedure_temp`;
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order_procedure` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) DEFAULT NULL COMMENT '唯一标识',
    `product_order_id` int(11) DEFAULT NULL COMMENT '生产订单id',
    `product_order_number` varchar(255) NOT NULL COMMENT '生产订单号',
    `sale_order_number` varchar(255) DEFAULT NULL COMMENT '销售订单号',
    `work_order_number` varbinary(255) NOT NULL COMMENT '工单号',
    `line_id` int(11) DEFAULT NULL COMMENT '产线id',
    `line_name` varchar(255) DEFAULT NULL COMMENT '产线名称',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
    `procedure_id` int(11) DEFAULT NULL COMMENT '工序id',
    `procedure_name` varchar(255) DEFAULT NULL COMMENT '工序名称',
    `craft_procedure_id` int(11) NOT NULL COMMENT '工艺工序id',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数',
    `crossing_quantity` double(11,2) DEFAULT NULL COMMENT '过站数',
    `crossing_finish_quantity` double(11,2) DEFAULT NULL COMMENT '过站完成数',
    `procedure_attrition_rate` double(11,4) DEFAULT NULL COMMENT '工序损耗率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_product` (`product_order_number`),
    KEY `idx_material` (`material_code`),
    KEY `idx_procedure` (`procedure_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单工序 整体统计';


-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================

