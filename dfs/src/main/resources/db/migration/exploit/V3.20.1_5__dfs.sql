
-- 删除在dfs错误创建的指标视图
DROP VIEW IF EXISTS `v_discreteProcessingProductionSchedule`;
DROP VIEW IF EXISTS dfs_metrics.`v_discreteProcessingProductionSchedule`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`%` SQL SECURITY DEFINER VIEW dfs_metrics.`v_discreteProcessingProductionSchedule` AS select vdrl.report_date,vdwo.line_name ,vdm.`code`, vdm.`name`, vdm.`standard`,vdm.`drawing_number`,
                                                                                                                           vdrl.`work_order`,vdwo.product_order_number ,vdwo.`sale_order_number`,vdrl.`finish_count`,vdrl.unqualified,
                                                                                                                           vdrl.user_nickname ,vdrl.operator_name,vdrl.shift_type ,vdrl.device_code,vdrl.device_id
                                                                                                                    from dfs_metrics.v_dfs_report_line vdrl,dfs_metrics.v_dfs_work_order vdwo,dfs_metrics.v_dfs_material vdm
                                                                                                                    where vdrl.work_order = vdwo.work_order_number
                                                                                                                      and vdwo.material_code = vdm.code;