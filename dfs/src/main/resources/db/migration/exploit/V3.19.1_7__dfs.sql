-- 删除并重新添加采购收料单-物料行字段表”收料状态“字段
DELETE FROM `dfs_form_field_rule_config` WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` = 'receiveStateName' AND `field_name_full_path_code` in ('purchaseReceiptOrder.detail', 'purchaseReceiptOrder.edit') AND `module_code` = 'baseMaterialLineField';
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseMaterialLineField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseMaterialLineField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseMaterialLineField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseMaterialLineField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseMaterialLineField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'receiveStateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseMaterialLineField', 0);

DELETE FROM `dfs_form_field_config` WHERE `field_code` = 'receiveStateName' AND `full_path_code` in ('purchaseReceiptOrder.detail', 'purchaseReceiptOrder.edit') AND `module_code` = 'baseMaterialLineField';
call proc_add_form_field_module("purchaseReceiptOrder.detail", "receiveStateName", "收料状态", "baseMaterialLineField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "receiveStateName", "收料状态", "baseMaterialLineField");
-- 删除并重新添加采购收料单-物料行字段表”入库数量“字段
DELETE FROM `dfs_form_field_rule_config` WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` = 'warehousingAmount' AND `field_name_full_path_code` in ('purchaseReceiptOrder.detail', 'purchaseReceiptOrder.edit') AND `module_code` = 'batchField';
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

DELETE FROM `dfs_form_field_config` WHERE `field_code` = 'warehousingAmount' AND `full_path_code` in ('purchaseReceiptOrder.detail', 'purchaseReceiptOrder.edit') AND `module_code` = 'batchField';
call proc_add_form_field_module("purchaseReceiptOrder.detail", "warehousingAmount", "入库数量", "batchField");
call proc_add_form_field_module("purchaseReceiptOrder.edit", "warehousingAmount", "入库数量", "batchField");