set autocommit = 0;
#DDL

#定制化组件配置表
CREATE TABLE IF NOT EXISTS `dfs_custom_component_config`
(
    `id`                    int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `location_marker_name`  varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '位置标识名',
    `location_marker_cname` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '位置标识中文名',
    `component_name`        varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '组件标识名',
    `component_cname`       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '组件标识中文名',
    `supplementary_data`    varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '补充数据',
    `remark`                varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    `create_by`             varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
    `update_by`             varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
    `create_time`           datetime                         DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime                         DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='定制化组件配置表';
#DML


commit
