-- DDL
CREATE TABLE IF NOT EXISTS `dfs_work_order_basic_unit_relation` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `work_center_id` int(11) DEFAULT NULL COMMENT '工作中心id(冗余)',
    `work_center_type` varchar(255) DEFAULT NULL COMMENT '工作中心类型(冗余)',
    `production_basic_unit_id` int(11) NOT NULL COMMENT '生产基本单元id',
    `production_basic_unit_name` varchar(255) DEFAULT NULL COMMENT '生产基本单元名称(冗余)',
    `isolation_id` varchar(255) NOT NULL COMMENT '隔离id: 工作中心id-生产基本单元id',
    `is_producing` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否投产',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`work_order_number`,`production_basic_unit_id`) USING BTREE,
    KEY `idx_isolation` (`isolation_id`) USING BTREE,
    KEY `idx_producing` (`work_center_type`,`is_producing`) USING BTREE,
    KEY `idx_work_center` (`work_center_id`),
    KEY `idx_basic_unit` (`work_center_type`,`production_basic_unit_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单-生产基本单元关联表';


CREATE TABLE IF NOT EXISTS `dfs_work_order_basic_unit_input_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `work_center_id` int(11) DEFAULT NULL COMMENT '工作中心id(冗余)',
    `work_center_type` varchar(255) DEFAULT NULL COMMENT '工作中心类型(冗余)',
    `production_basic_unit_id` int(11) NOT NULL COMMENT '生产基本单元id',
    `production_basic_unit_name` varchar(255) DEFAULT NULL COMMENT '生产基本单元名称(冗余)',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `username` varchar(255) DEFAULT NULL COMMENT '操作人',
    PRIMARY KEY (`id`),
    KEY `idx_base` (`work_order_number`,`production_basic_unit_id`) USING BTREE,
    KEY `idx_start_time` (`start_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单-生产基本单元投产记录表';

call proc_add_column(
        'dfs_work_order',
        'work_center_type',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `work_center_type` varchar(255) NULL COMMENT ''工作中心类型'';');

-- 工序检验项记录基本单元关联表
CREATE TABLE IF NOT EXISTS `dfs_procedure_inspect_record_base_unit_relation`
(
    `id`                          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `procedure_inspect_record_id` int(11) NOT NULL COMMENT '工序检验记录ID',
    `work_center_type`            varchar(255) DEFAULT NULL COMMENT '工作中心类型',
    `production_basic_unit_id`    int(11) NOT NULL COMMENT '生产基本单元id',
    `production_basic_unit_name`   varchar(255) NOT NULL COMMENT '生产基本单元名称',
    PRIMARY KEY (`id`),
    KEY                           `idx_procedure_inspect_record_id` (`procedure_inspect_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序检验项记录基本单元关联表';

-- 工单日计划
call proc_add_column(
        'dfs_work_order_plan',
        'production_basic_unit_type',
        'ALTER TABLE `dfs_work_order_plan` ADD COLUMN `production_basic_unit_type` varchar(255) DEFAULT NULL COMMENT ''生产基本单元类型'';');
call proc_add_column(
        'dfs_work_order_plan',
        'production_basic_unit_id',
        'ALTER TABLE `dfs_work_order_plan` ADD COLUMN `production_basic_unit_id` int(11) DEFAULT NULL COMMENT ''生产基本单元id'';');
call proc_add_column(
        'dfs_work_order_plan',
        'production_basic_unit_name',
        'ALTER TABLE `dfs_work_order_plan` ADD COLUMN `production_basic_unit_name` varchar(255) DEFAULT NULL COMMENT ''生产基本单元名称(冗余)'';');
call proc_add_column(
        'dfs_work_order_plan',
        'is_main',
        'ALTER TABLE `dfs_work_order_plan` ADD COLUMN `is_main` tinyint(4) DEFAULT NULL COMMENT ''是否为主生产基本单元'';');
-- 日计划索引
call proc_drop_column_index('dfs_work_order_plan','unique_number_time');
call proc_add_unique_index('dfs_work_order_plan','work_order_number,time,production_basic_unit_type,production_basic_unit_id,is_main','idx_uni');

-- 报工
call proc_add_column(
        'dfs_report_line',
        'work_center_type',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `work_center_type` varchar(255) DEFAULT NULL COMMENT ''工作中心类型'';');
call proc_add_column(
        'dfs_report_line',
        'device_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `device_name` varchar(255) DEFAULT NULL COMMENT ''设备名称'';');
call proc_add_column(
        'dfs_report_line',
        'team_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `team_name` varchar(255) DEFAULT NULL COMMENT ''班组名称'';');
call proc_add_column(
        'dfs_report_line',
        'resource_device_code',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_device_code` varchar(255) DEFAULT NULL COMMENT ''关联资源-设备编号'';');

-- 工序报工
call proc_add_column(
        'dfs_report_line_procedure',
        'work_center_type',
        'ALTER TABLE `dfs_report_line_procedure` ADD COLUMN `work_center_type` varchar(255) DEFAULT NULL COMMENT ''工作中心类型'';');

-- 消息通知配置 -
call proc_add_column(
        'dfs_circulation_notice_config',
        'notice_detail',
        'ALTER TABLE `dfs_circulation_notice_config` ADD COLUMN `notice_detail` varchar(2048) DEFAULT NULL COMMENT ''通知详情(占位符集合) 多个按,分隔'';');

-- 消息通知层级结构
call proc_add_column(
        'dfs_config_notice_level_relation',
        'application_id',
        'ALTER TABLE `dfs_config_notice_level_relation` ADD COLUMN `application_id` varchar(512) DEFAULT NULL COMMENT ''小程序applicationId'';');


CREATE TABLE IF NOT EXISTS `dfs_work_order_basic_unit_count` (
`id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
`work_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单号',
`production_basic_unit_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产基本单元类型\r\nline-制造单元\r\nteam-班组\r\ndevice-设备',
`production_basic_unit_id` int NOT NULL COMMENT '生产基本单元id',
`material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料编号',
`count` double(11,2) DEFAULT '0.00' COMMENT '已检查数量',
`unqualified` double(11,2) DEFAULT '0.00' COMMENT '不合格数量',
`repair_qualified_number` double(11,2) DEFAULT '0.000' COMMENT '返修良品数',
`input` double(11,2) DEFAULT '0.00' COMMENT '投入数',
`update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `work_order_number` (`work_order_number`) USING BTREE,
KEY `update_time` (`update_time`) USING BTREE,
KEY `basic` (`production_basic_unit_type`,`production_basic_unit_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='工单生产单元产量记录表';


-- 工单增加索引
call proc_add_column_index('dfs_work_order','start_date,end_date','time');

CREATE TABLE IF NOT EXISTS `dfs_work_order_basic_unit_day_count` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `uni_code` varchar(512) DEFAULT NULL COMMENT '唯一键',
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `production_basic_unit_type` varchar(16) NOT NULL COMMENT '生产基本单元类型\r\nline-制造单元\r\nteam-班组\r\ndevice-设备',
    `production_basic_unit_id` int(11) NOT NULL COMMENT '生产基本单元id',
    `record_date` datetime NOT NULL COMMENT '记录日期',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编号',
    `count` double(11,2) DEFAULT '0.00' COMMENT '已检查数量',
    `unqualified` double(11,2) DEFAULT '0.00' COMMENT '不合格数量',
    `repair_qualified_number` double(11,2) DEFAULT '0.00' COMMENT '返修良品数',
    `input` double(11,2) DEFAULT '0.00' COMMENT '投入数',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_union` (`work_order_number`,`record_date`,`production_basic_unit_type`,`production_basic_unit_id`) USING BTREE,
    UNIQUE KEY `idx_uni` (`uni_code`),
    KEY `basic` (`production_basic_unit_type`,`production_basic_unit_id`),
    KEY `record_date` (`record_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工单生产单元产量每日记录表';

CREATE TABLE IF NOT EXISTS `dfs_query_field_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `prefix` varchar(255) NOT NULL COMMENT '前缀',
    `field_code` varchar(255) NOT NULL COMMENT '字段编码',
    `field_name` varchar(255) DEFAULT '' COMMENT '字段名称',
    `table_field_code` varchar(255) NULL COMMENT '表字段',
    `remark` varchar(255) NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_idx` (`prefix`,`field_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询字段配置表';

-- DML

-- 初始化数据
INSERT INTO dfs_work_order_basic_unit_relation (
    `work_order_number`,
    `work_center_id`,
    `work_center_type`,
    `production_basic_unit_id`,
    `production_basic_unit_name`,
    `isolation_id`
)
SELECT * FROM (
      SELECT
          wo.work_order_number,
          wo.work_center_id,
          wc.type,
          wo.production_basic_unit_id,
          COALESCE(
                  CASE wc.type
                      WHEN 'device' THEN d.device_name
                      WHEN 'line' THEN pl.name
                      WHEN 'team' THEN t.team_name
                      END,  null -- 添加兜底值
              ) AS production_basic_unit_name,
          wo.isolation_id
      FROM dfs_work_order wo
               INNER JOIN dfs_work_center wc ON wo.work_center_id = wc.id  -- 改为INNER JOIN确保有效数据
               LEFT JOIN dfs_device d ON wc.type = 'device' AND wo.production_basic_unit_id = d.device_id
               LEFT JOIN dfs_production_line pl ON wc.type = 'line' AND wo.production_basic_unit_id = pl.production_line_id
               LEFT JOIN sys_team t ON wc.type = 'team' AND wo.production_basic_unit_id = t.id
      WHERE wo.production_basic_unit_id is not null and wc.id is not null
)a WHERE a.production_basic_unit_name is not null;

-- 工单表：工作中心类型刷新
UPDATE dfs_work_order wo
    INNER JOIN dfs_work_center wc ON wo.work_center_id = wc.id
    SET wo.work_center_type = wc.`type`
WHERE wo.work_center_type is null;

-- 工序检验项记录基本单元关联表数据迁移
-- 处理 work_center_type 为 'device' 的情况
INSERT INTO dfs_procedure_inspect_record_base_unit_relation
(procedure_inspect_record_id, work_center_type, production_basic_unit_id, production_basic_unit_name)
SELECT
    pir.id as procedure_inspect_record_id,
    wo.work_center_type,
    d.device_id as production_basic_unit_id,
    pir.production_basic_unit_name
FROM dfs_procedure_inspect_record pir
         INNER JOIN dfs_work_order wo ON pir.work_order_number = wo.work_order_number
         INNER JOIN dfs_device d ON pir.production_basic_unit_name = d.device_name
WHERE wo.work_center_type = 'device';

-- 处理 work_center_type 为 'line' 的情况
INSERT INTO dfs_procedure_inspect_record_base_unit_relation
(procedure_inspect_record_id, work_center_type, production_basic_unit_id, production_basic_unit_name)
SELECT
    pir.id as procedure_inspect_record_id,
    wo.work_center_type,
    pl.production_line_id as production_basic_unit_id,
    pir.production_basic_unit_name
FROM dfs_procedure_inspect_record pir
         INNER JOIN dfs_work_order wo ON pir.work_order_number = wo.work_order_number
         INNER JOIN dfs_production_line pl ON pir.production_basic_unit_name = pl.name
WHERE wo.work_center_type = 'line';

-- 处理 work_center_type 为 'team' 的情况
INSERT INTO dfs_procedure_inspect_record_base_unit_relation
(procedure_inspect_record_id, work_center_type, production_basic_unit_id, production_basic_unit_name)
SELECT
    pir.id as procedure_inspect_record_id,
    wo.work_center_type,
    st.id as production_basic_unit_id,
    pir.production_basic_unit_name
FROM dfs_procedure_inspect_record pir
         INNER JOIN dfs_work_order wo ON pir.work_order_number = wo.work_order_number
         INNER JOIN sys_team st ON pir.production_basic_unit_name = st.team_name
WHERE wo.work_center_type = 'team';

-- 删除无用事件
drop event if exists e_create_partition_today;



-- 修改工单字段配置
UPDATE `dfs_form_field_config` SET `field_code` = 'productBasicUnits' WHERE `field_code` = 'lineId';
UPDATE `dfs_form_field_config` SET `field_name` = 'productBasicUnits' WHERE `field_name` = 'lineId';
UPDATE `dfs_form_field_rule_config` SET `field_code` = 'productBasicUnits' WHERE `field_code` = 'lineId';

-- 报工 - 工作中心类型、设备名称、班组名称 字段初始化
update dfs_report_line rl
    left join dfs_work_order wo on wo.work_order_number = rl.work_order
    set rl.work_center_type = wo.work_center_type
WHERE rl.work_center_type is null;

update dfs_report_line_procedure rl
    left join dfs_work_order wo on wo.work_order_number = rl.work_order_number
    set rl.work_center_type = wo.work_center_type
WHERE rl.work_center_type is null;

update dfs_report_line rl
    inner join dfs_device d on rl.device_id = d.device_id
    set rl.device_name = d.device_name
WHERE rl.device_name is null;

update dfs_report_line rl
    inner join sys_team t on rl.team_id = t.id
    set rl.team_name = t.team_name
WHERE rl.team_name is null;

update dfs_report_line rl
    inner join dfs_production_line l on rl.line_id = l.production_line_id
    set rl.line_name = l.name
WHERE rl.line_name is null;

-- 精制api
INSERT INTO `dfs_config_open_api` (`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'pushMessageToJzV2', '发送新消息至精制V2', 'POST', '/jingzhi/push/notificationMsg/v2');



-- dfs_manage_report_task表修改唯一索引
ALTER TABLE `dfs_manage_report_task` DROP INDEX `unique_task_report`;
ALTER TABLE `dfs_manage_report_task` ADD UNIQUE INDEX `unique_task_code` (`task_code`);
-- dfs_manage_report_download_record新增字段
call proc_add_column(
        'dfs_manage_report_download_record',
        'execution_frequency',
        'ALTER TABLE `dfs_manage_report_download_record` ADD COLUMN `execution_frequency`  varchar(255) NULL COMMENT ''对应定时任务的执行频率'';');
call proc_add_column(
        'dfs_manage_report_download_record',
        'deleted',
        'ALTER TABLE `dfs_manage_report_download_record` ADD COLUMN `deleted`  bit(1) NOT NULL DEFAULT 0;');
-- 处理历史数据
UPDATE dfs_manage_report_download_record downloadRecord SET execution_frequency = (SELECT reportTask.execution_frequency FROM dfs_manage_report_task reportTask WHERE reportTask.task_code=downloadRecord.task_code) WHERE downloadRecord.task_code IS NOT NULL;

-- 定时报表汇总 小程序
INSERT INTO `sys_route` (`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`)
VALUES ('/statementCenter/periodicReportSummary', '/statementCenter', 'periodicReportSummary', NULL, '定时报表汇总', NULL, 'ams', NULL, NULL, NULL, '0', '0', NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`)
VALUES ('13007', '定时报表汇总', '/statementCenter/periodicReportSummary', NULL, NULL, NOW(), NULL, NOW(), 'enable', 'GET', '130', '1', '1', '0', '/statementCenter', '1', NULL);
call init_new_role_permission('13007');
INSERT INTO `dfs_app_online` (`permission_id`, `permission_path`, `application_id`, `name`)
VALUES ('13007', '/statementCenter/periodicReportSummary', 'new.yk.genieos.ams.statementCenter.periodicReportSummary', '定时报表汇总');

call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_flag',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_flag`  bit(1) NULL DEFAULT 0 COMMENT ''是否同步创建小程序快捷方式'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_app_id',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_app_id`  varchar(255) NULL COMMENT ''创建的小程序快捷方式的appId'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_name',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_name`  varchar(255) NULL COMMENT ''小程序快捷方式名称'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_weight',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_weight`  int(4) NULL COMMENT ''权重'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_category_id',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_category_id`  int(11) NULL COMMENT ''类型'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_category',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_category`  varchar(255) NULL COMMENT ''类型'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_label',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_label`  varchar(255) NULL COMMENT ''标签'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_param',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_param`  varchar(255) NULL COMMENT ''参数'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_icon',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_icon`  varchar(255) NULL COMMENT ''图标'';');
call proc_add_column(
        'dfs_manage_report',
        'program_shortcut_remark',
        'ALTER TABLE `dfs_manage_report` ADD COLUMN `program_shortcut_remark`  varchar(255) NULL COMMENT ''备注'';');

-- 修改报工表完成数量、不合格数量位数
call proc_modify_column(
        'dfs_report_line',
        'finish_count',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `finish_count` double(11, 3) NULL DEFAULT NULL COMMENT ''完成数量''');

call proc_modify_column(
        'dfs_report_line',
        'unqualified',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `unqualified` double(11, 3) NULL DEFAULT 0.000000 COMMENT ''不合格数量''');

-- 工作中心模型id改为varchar类型，支持关联多个生产基本单元类型
call proc_modify_column(
        'dfs_work_center',
        'line_model_id',
        'ALTER TABLE `dfs_work_center` MODIFY COLUMN `line_model_id` varchar(255) NULL DEFAULT NULL COMMENT ''制造单元类型id''');
call proc_modify_column(
        'dfs_work_center',
        'device_model_id',
        'ALTER TABLE `dfs_work_center` MODIFY COLUMN `device_model_id` varchar(255) NULL DEFAULT NULL COMMENT ''设备类型id''');


-- 新增精制接口
INSERT INTO `dfs_config_open_api` (`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'createShortcut', '创建小程序快捷方式', 'POST', '/jingzhi/shortcut');
INSERT INTO `dfs_config_open_api` (`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'jingzhi', 'deleteShortcut', '删除小程序快捷方式', 'DELETE', '/jingzhi/shortcut/%s');


-- 维修工单新增 导入权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('tpm10302140', '下载默认转换模板', 'maintenance:export-default-template', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10302', 2, 1, 0, '/equipment-management/warranty', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('tpm10302150', '下载自定义转换模板', 'maintenance:export-custom-template', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10302', 2, 1, 0, '/equipment-management/warranty', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('tpm10302160', '上传自定义转换模板', 'maintenance:import-custom-template', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10302', 2, 1, 0, '/equipment-management/warranty', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('tpm10302170', '下载导入模板', 'maintenance:export-templat', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10302', 2, 1, 0, '/equipment-management/warranty', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('tpm10302180', '导入数据', 'maintenance:import-data', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10302', 2, 1, 0, '/equipment-management/warranty', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('tpm10302190', '查看日志', 'maintenance:import-log', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10302', 2, 1, 0, '/equipment-management/warranty', 1, NULL, '', 1);
call init_new_role_permission('tpm10302140');
call init_new_role_permission('tpm10302150');
call init_new_role_permission('tpm10302160');
call init_new_role_permission('tpm10302170');
call init_new_role_permission('tpm10302180');
call init_new_role_permission('tpm10302190');

-- 新增质检方案和生产基本单元的关联表
CREATE TABLE IF NOT EXISTS `dfs_defect_scheme_product_basic_unit_relation`
(
    `id`                      int(11) NOT NULL AUTO_INCREMENT,
    `scheme_id`               int(11) NOT NULL COMMENT '质检方案id',
    `type`                    varchar(255) NOT NULL COMMENT '关联类型',
    `product_basic_unit_id`   int(11) NOT NULL COMMENT '生产基本单元id',
    `product_basic_unit_code` varchar(255) DEFAULT NULL COMMENT '生产基本单元编码',
    `product_basic_unit_name` varchar(255) DEFAULT NULL COMMENT '生产基本单元名称',
    PRIMARY KEY (`id`),
    KEY                       `scheme_id` (`scheme_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质检方案和生产基本单元的关联表';


-- 工单生产基本单元产量表历史记录
INSERT INTO `dfs_work_order_basic_unit_count`(`work_order_number`,`production_basic_unit_type`,`production_basic_unit_id`,`material_code`,`count`,`unqualified`,`input`,`update_time`)
SELECT * from (
          SELECT
              work_order_number,
              case
                  when line_id is not null then 'line'
                  when device_id is not null then 'device'
                  when team_id is not null then 'team'
                  end as production_basic_unit_type,
              case
                  when line_id is not null then line_id
                  when device_id is not null then device_id
                  when team_id is not null then team_id
                  end as production_basic_unit_id,
              material_code,
              finish_count as count,
    unqualified,
    input_total,
    create_date as update_time
from dfs_work_order
)a where production_basic_unit_id is not null;

-- 下推记录新增`是否异常`,`异常情况描述`字段
call proc_add_column(
        'dfs_order_push_down_record',
        'is_abnormal',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `is_abnormal` tinyint(4) DEFAULT 0 COMMENT ''单据是否异常'' after `push_down_code`;');
call proc_add_column(
        'dfs_order_push_down_record',
        'abnormal_remark',
        'ALTER TABLE `dfs_order_push_down_record` ADD COLUMN `abnormal_remark` varchar(255) DEFAULT NULL COMMENT ''异常情况描述'' after `is_abnormal`;');

-- 通知根节点默认的小程序id
update dfs_config_notice_level_relation set application_id = 'new.yk.genieos.ams.order-model.salesOrder' WHERE code = 'saleOrder';
update dfs_config_notice_level_relation set application_id = 'new.yk.genieos.ams.order-model.product-order' WHERE code = 'productOrder';
update dfs_config_notice_level_relation set application_id = 'new.yk.genieos.dfs.order-model.production-workorder' WHERE code = 'workOrder';
update dfs_config_notice_level_relation set application_id = 'new.yk.genieos.ams.scc.procurement-management.purchasing-list' WHERE code = 'purchaseOrder';
update dfs_config_notice_level_relation set application_id = 'yelink.productInspection.ocs.com' WHERE code = 'inspectionForm';
update dfs_config_notice_level_relation set application_id = 'new.yk.genieos.dfs.product-management.technology' WHERE code = 'craft';
update dfs_config_notice_level_relation set application_id = 'new.yk.genieos.dfs.product-management.bom' WHERE code = 'bom';
update dfs_config_notice_level_relation set application_id = 'new.yk.genieos.dfs.product-management.supplies' WHERE code = 'material';
update dfs_config_notice_level_relation set application_id = 'ocs.myjob' WHERE code = 'taskInfoNotice';

-- 质量追溯表单配置
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.qualityTrace.com', '质量追溯', '/quality-trace', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.qualityTrace.com.select', '查询', 'qualityTrace:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.qualityTrace.com', 2, 0, 1, NULL, 1, NULL, 1);

INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (null, 'web', NULL, 'qualityTrace', '质量追溯', 'qualityTrace', 'amsProduct', NULL, 'admin', 'admin', '2023-11-21 11:07:04', '2023-11-21 11:07:04', NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (null, 'web', NULL, 'qualityTrace.detail', '质量追溯详情页', 'qualityTrace.detail', 'qualityTrace', NULL, 'admin', 'admin', '2023-11-21 11:07:04', '2023-11-21 11:07:04', 'detail', 1);

INSERT INTO `dfs_form_field_module_config`(`id`, `full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES (null, 'qualityTrace.detail', 'qualityTrace.detail', 'baseField', '基础信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`id`, `full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES (null, 'qualityTrace.detail', 'qualityTrace.detail', 'codeField', '条码扩展字段', 'extend', '扩展字段', 1, 1, 4);

call proc_add_form_field_module("qualityTrace.detail", "productFlowCode", "条码号", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "typeName", "条码类型", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "materialCode", "物料编码", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "materialName", "物料名称", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "materialStandard", "物料规格", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "count", "数量", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "relationTypeName", "创建单据类型", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "relationNumber", "创建单据编码", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "customerCode", "客户编码", "baseField");
call proc_add_form_field_module("qualityTrace.detail", "customerName", "客户名称", "baseField");

INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'productFlowCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'typeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'materialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'materialName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'materialStandard', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'count', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'relationTypeName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'relationNumber', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, 'yelink.qualityTrace.com', 'qualityTrace.detail', 'qualityTrace.detail', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);



-- 工单生产基本单元每日表初始化
INSERT INTO `dfs_work_order_basic_unit_day_count`(`uni_code`,`work_order_number`,`production_basic_unit_type`,`production_basic_unit_id`,`material_code`,`count`,`unqualified`,`repair_qualified_number`,`input`,`record_date`)
SELECT CONCAT_WS('_', a.work_order_number, a.production_basic_unit_type, a.production_basic_unit_id, a.record_date), a.* FROM (
  SELECT
      t1.work_order_number,
      case
          when t1.line_id is not null then 'line'
          when t1.device_id is not null then 'device'
          when t1.team_id is not null then 'team'
          end as production_basic_unit_type,
      case
          when t1.line_id is not null then line_id
          when t1.device_id is not null then device_id
          when t1.team_id is not null then team_id
          end as production_basic_unit_id,
      t1.material_code,
      t1.count,
      t1.unqualified,
      t1.repair_qualified_number,
      t1.input,
      DATE_FORMAT(t1.time, '%Y-%m-%d') as record_date
  FROM dfs_record_work_order_day_count t1
  JOIN (
      SELECT
          work_order_number,
          DATE_FORMAT(time, '%Y-%m-%d') as format_time,
          MAX(id) AS max_id  -- 取最大 id 表示最新记录
      FROM dfs_record_work_order_day_count
      GROUP BY work_order_number, DATE_FORMAT(time, '%Y-%m-%d')
  ) t2 ON t1.work_order_number = t2.work_order_number  AND DATE_FORMAT(t1.time, '%Y-%m-%d') = t2.format_time  AND t1.id = t2.max_id
)a WHERE a.production_basic_unit_id is not null;


-- 兼容历史数据，历史正在投产的生产工单需要同步到新表上去
UPDATE `dfs_work_order` w, `dfs_work_order_basic_unit_relation` b
SET b.`is_producing` = 1
WHERE w.`work_order_number` = b.`work_order_number`
AND w.`state` = 3 AND b.`create_time` is null;

-- 兼容历史数据，将工单-生产基本单元关联表的创建时间赋值为工单创建时间
UPDATE `dfs_work_order` w, `dfs_work_order_basic_unit_relation` b
SET b.`create_time` = w.`create_date`
WHERE w.`work_order_number` = b.`work_order_number`;

-- 兼容历史数据，新的质检方案和生产基本单元的关联表需要关联工位
insert into `dfs_defect_scheme_product_basic_unit_relation` (`scheme_id`, `type`, `product_basic_unit_id`, `product_basic_unit_code`, `product_basic_unit_name`)
SELECT d.`scheme_id`, 'station', f.`fid`, f.`fcode`, f.`fname` from `dfs_defect_scheme_facilities` d
left join `dfs_facilities` f on d.`fid` = f.`fid`;

-- 去重工单关联的班组数据
DELETE t1
FROM dfs_work_order_team t1
JOIN dfs_work_order_team t2
WHERE t1.work_order_number = t2.work_order_number
  AND t1.member_name = t2.member_name
  AND t1.id > t2.id;


-- 删除无用的下推配置项
DELETE FROM `dfs_order_push_down_config_value_dict` WHERE `value_full_path_code` in ('saleOrder.pushDownConfig.workOrder.craftPush.applicationId','saleOrder.pushDownConfig.workOrder.craftPush.url','saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType','saleOrder.pushDownConfig.workOrder.craftPush.targetOrderType');
DELETE FROM `dfs_order_push_down_config_value` WHERE `value_full_path_code` in ('saleOrder.pushDownConfig.workOrder.craftPush.applicationId','saleOrder.pushDownConfig.workOrder.craftPush.url','saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType','saleOrder.pushDownConfig.workOrder.craftPush.targetOrderType');

UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"saleOrder\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType-2';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"saleOrder\"' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType-2';


-- 物料sku关联表
CREATE OR REPLACE VIEW  `dfs_metrics`.`v_dfs_material_auxiliary_attr_sku` AS SELECT * FROM `dfs`.`dfs_material_auxiliary_attr_sku`;



--
-- 删除脏数据
DELETE FROM dfs_work_order_plan
WHERE id NOT IN (
    SELECT max_id FROM (
        SELECT MAX(id) AS max_id
        FROM dfs_work_order_plan
        GROUP BY work_order_number, time
    ) tmp
);

-- 设置工作中心类型
update dfs_work_order_plan p
    left join dfs_work_order wo on wo.work_order_number = p.work_order_number
    set p.production_basic_unit_type = wo.work_center_type
WHERE p.production_basic_unit_type is null;

-- 设置生产基本单元: 产线
update dfs_work_order_plan p
    left join dfs_work_order wo on wo.work_order_number = p.work_order_number
    left join dfs_production_line l on l.production_line_id = wo.line_id
    set
        p.production_basic_unit_id = l.production_line_id,
        p.production_basic_unit_name = l.name,
        p.is_main = 1
WHERE p.production_basic_unit_id is null and p.production_basic_unit_type = 'line';

-- 设置生产基本单元： 设备
update dfs_work_order_plan p
    left join dfs_work_order wo on wo.work_order_number = p.work_order_number
    left join dfs_device d on d.device_id = wo.device_id
    set
        p.production_basic_unit_id = d.device_id,
        p.production_basic_unit_name = d.device_name,
        p.is_main = 1
WHERE p.production_basic_unit_id is null and p.production_basic_unit_type = 'device';

-- 设置生产基本单元： 班组
update dfs_work_order_plan p
    left join dfs_work_order wo on wo.work_order_number = p.work_order_number
    left join sys_team t on t.id = wo.team_id
    set
        p.production_basic_unit_id = t.id,
        p.production_basic_unit_name = t.team_name,
        p.is_main = 1
WHERE p.production_basic_unit_id is null and p.production_basic_unit_type = 'team';

-- 采购收料单-批次相关字段支持编辑
update dfs_form_field_rule_config set is_edit=0,edit_gray=0,is_need=0,need_gray=0 where full_path_code="purchaseReceiptOrder.editByCreate" and field_code in ("qualifiedQuantity","unqualifiedQuantity","inspectStateName","relateInspectOrder","rejectedQty","warehouseStateName","concessionAcceptanceQuantity","returnAmount","industrialWasteQuantity","materialWasteQuantity","rejectedReason") and module_code = "batchField";
update dfs_form_field_rule_config set is_edit=0,edit_gray=0,is_need=0,need_gray=0 where full_path_code="purchaseReceiptOrder.editByRelease" and field_code in ("qualifiedQuantity","unqualifiedQuantity","inspectStateName","relateInspectOrder","rejectedQty","warehouseStateName","concessionAcceptanceQuantity","returnAmount","industrialWasteQuantity","materialWasteQuantity","rejectedReason") and module_code = "batchField";
update dfs_form_field_rule_config set is_need=1,need_gray=1 where full_path_code="purchaseReceiptOrder.editByCreate" and field_code = "count" and module_code = "batchField";
update dfs_form_field_rule_config set is_need=1,need_gray=1 where full_path_code="purchaseReceiptOrder.editByRelease" and field_code = "count" and module_code = "batchField";

-- 采购收货批次号的关联单据是采购收料单
update dfs_product_flow_code set relation_type = 7 where type = 9;

-- 查询字段配置
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'type', '上报方式', 'type', 'report-报工、auto-自动记录');
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'lineId', '产线id', 'line_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'deviceId', '设备id', 'device_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'teamId', '班组id', 'team_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'workOrder', '工单号', 'work_order', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'userNickname', '上报人姓名', 'user_nickname', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'reportDate', '报工日期', 'report_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'batch', '批次', 'batch', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'shiftType', '班次', 'shift_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'operatorName', '操作员姓名', 'operator_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'workCenterId', '工作中心id', 'work_center_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'workCenterName', '工作中心名称', 'work_center_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'reportFieldOne', '报工扩展字段1', 'report_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'reportFieldTwo', '报工扩展字段2', 'report_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'reportFieldThree', '报工扩展字段3', 'report_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'reportFieldFour', '报工扩展字段4', 'report_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'report', 'reportFieldFive', '报工扩展字段5', 'report_field_five', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'input', 'workOrderNumber', '工单号', 'work_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'input', 'createDate', '操作时间', 'create_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'input', 'reportDate', '上报日期', 'report_date', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'alarmId', '告警ID', 'alarm_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'aggregateCode', '告警聚合码', 'aggregate_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'alarmTime', '告警时间', 'alarm_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'dealState', '处理状态', 'deal_state', '0-未处理、1-处理中、2-已处理');
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'productionLineCode', '产线编号', 'production_line_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'fcode', '工位编号', 'fcode', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'updateTime', '更新时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'deviceCode', '设备编号', 'device_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'dealName', '处理人账号', 'deal_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'workOrderNumber', '工单编号', 'work_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'alarmExtendFieldOne', '告警扩展字段1', 'alarm_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'alarmExtendFieldTwo', '告警扩展字段2', 'alarm_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'alarmExtendFieldThree', '告警扩展字段3', 'alarm_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'alarm', 'alarmExtendFieldFour', '告警扩展字段4', 'alarm_extend_field_four', NULL);

INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'barCodeId', '批次ID', 'bar_code_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'barCode', '批次号', 'bar_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'ruleType', '关联单据类型', 'rule_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'relateNumber', '关联单号', 'relate_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'relateMaterialId', '关联物料行Id', 'relate_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'materialCode', '物料编号', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'state', '状态', 'state', '0-可以使用 1-已被使用 2-已出库完 3-已上架 4-不可使用');
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'updateTime', '修改时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'barCodeExtendFieldOne', '批次扩展字段1', 'bar_code_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCode', 'isPrint', '是否已打印', 'is_print', NULL);


-- 物料
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'state', '状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'code', '物料编码', 'code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'type', '物料类型', 'type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'typeName', '物料类型名称', 'type_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'name', '物料名称', 'name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'nameEnglish', '物料英文名称', 'name_english', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'standard', '物料规格', 'standard', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'sort', '分类', 'sort', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'level', '物料等级', 'level', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'drawingNumber', '创建人', 'drawing_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'rawMaterial', '材质', 'raw_material', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'factoryModel', '工厂型号', 'factory_model', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'isBatchMag', '是否按批次管理', 'is_batch_mag', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldOne', '扩展字段1', 'custom_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldTwo', '扩展字段2', 'custom_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldThree', '扩展字段3', 'custom_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldFour', '扩展字段4', 'custom_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldFive', '扩展字段5', 'custom_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldSix', '扩展字段6', 'custom_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldSeven', '扩展字段7', 'custom_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldEight', '扩展字段8', 'custom_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldNine', '扩展字段9', 'custom_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'material', 'customFieldTen', '扩展字段10', 'custom_field_ten', NULL);

-- 销售订单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderNumber', '销售订单编号', 'sale_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'state', '销售订单状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'stateChangeTime', '状态变更时间', 'state_change_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderType', '单据类型', 'sale_order_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'businessType', '业务类型', 'business_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderSource', '订单来源', 'sale_order_source', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'customerCode', '客户编码', 'customer_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'customerName', '客户名称', 'customer_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'customerMobile', '客户联系方式', 'customer_mobile', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'customerAddr', '客户地址', 'customer_addr', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'customerContractNo', '客户合同号', 'customer_contract_no', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'customerDemand', '客户需求', 'customer_demand', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'salesmanCode', '销售员编号', 'salesman_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'salesmanName', '销售人名称', 'salesman_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'salesmanMobile', '销售人手机号', 'salesman_mobile', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'remark', '针对整个销售订单的备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldOne', '销售订单拓展字段1', 'sale_order_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldTwo', '销售订单拓展字段2', 'sale_order_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldThree', '销售订单拓展字段3', 'sale_order_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldFour', '销售订单拓展字段4', 'sale_order_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldFive', '销售订单拓展字段5', 'sale_order_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldSix', '销售订单拓展字段6', 'sale_order_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldSeven', '销售订单拓展字段7', 'sale_order_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldEight', '销售订单拓展字段8', 'sale_order_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldNine', '销售订单拓展字段9', 'sale_order_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrder', 'saleOrderExtendFieldTen', '销售订单拓展字段10', 'sale_order_extend_field_ten', NULL);
-- 销售订单物料行
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'lineNumber', '销售订单物料行号', 'line_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderId', '销售订单id', 'sale_order_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderNumber', '销售订单号', 'sale_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'productRowId', '产品行标识', 'product_row_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'materialId', '物料id', 'material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'materialCode', '物料编码', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'priority', '优先级', 'priority', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'price', '单价', 'price', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'totalPrice', '总价', 'total_price', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'materialRemark', '订单物料备注', 'material_remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'requireGoodsDate', '要货日期', 'require_goods_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'planDeliveryDate', '计划发货日期', 'plan_delivery_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'deliveryDate', '发货日期', 'delivery_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'customerOrderNumber', '客户订单编号', 'customer_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'customerMaterialCode', '客户物料编码', 'customer_material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'customerMaterialName', '客户物料名称', 'customer_material_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'customerSpecification', '客户物料规格', 'customer_specification', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'salesQuantity', '销售数量', 'sales_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'mergedState', '合单状态', 'merged_state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'mergeOrderNumber', '关联的合单号', 'merge_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'shipmentStatus', '发货状态', 'shipment_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'isGift', '是否赠品', 'is_gift', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'packingMethod', '包装方式', 'packing_method', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'auxiliaryMaterialModel', '辅材型号', 'auxiliary_material_model', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'orderDate', '下单日期', 'order_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'packageSchemeCode', '包装方案编码', 'package_scheme_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldOne', '销售订单物料拓展字段1', 'saleOrderMaterialExtendFieldOne', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldTwo', '销售订单物料拓展字段2', 'saleOrderMaterialExtendFieldTwo', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldThree', '销售订单物料拓展字段3', 'saleOrderMaterialExtendFieldThree', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldFour', '销售订单物料拓展字段4', 'saleOrderMaterialExtendFieldFour', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldFive', '销售订单物料拓展字段5', 'saleOrderMaterialExtendFieldFive', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldSix', '销售订单物料拓展字段6', 'saleOrderMaterialExtendFieldSix', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldSeven', '销售订单物料拓展字段7', 'saleOrderMaterialExtendFieldSeven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldEight', '销售订单物料拓展字段8', 'saleOrderMaterialExtendFieldEight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldNine', '销售订单物料拓展字段9', 'saleOrderMaterialExtendFieldNine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'saleOrderMaterial', 'saleOrderMaterialExtendFieldTen', '销售订单物料拓展字段10', 'saleOrderMaterialExtendFieldTen', NULL);

-- 生产订单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderNumber', '生产订单编号' ,'product_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'state', '状态' ,'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'customerCode', '客户编码' ,'customer_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'customerName', '客户名称' ,'customer_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'relatedOrderNum', '关联的上级生产订单号' ,'related_order_num', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'relatedRootOrderNum', '关联的根订单' ,'related_root_order_num', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'remark', '备注' ,'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'createTime' ,'创建时间' ,'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'updateTime' ,'修改时间' ,'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'createBy' , '创建人' ,'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'updateBy' , '修改人' ,'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'type' , '业务类型' ,'type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'orderType' , '单据类型' ,'order_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldOne', '生产订单拓展字段1' ,'product_order_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldTwo', '生产订单拓展字段2' ,'product_order_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldThree', '生产订单拓展字段3' ,'product_order_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldFour', '生产订单拓展字段4' ,'product_order_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldFive', '生产订单拓展字段5' ,'product_order_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldSix', '生产订单拓展字段6' ,'product_order_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldSeven', '生产订单拓展字段7' ,'product_order_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldEight', '生产订单拓展字段8' ,'product_order_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldNine', '生产订单拓展字段9' ,'product_order_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrder', 'productOrderExtendFieldTen', '生产订单拓展字段10' ,'product_order_extend_field_ten', NULL);
-- 生产订单物料行
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'materialCode', '物料编码', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'planQuantity', '计划数量', 'plan_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'saleOrderCode', '销售订单编码', 'sale_order_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'relatedSaleOrderMaterialLineNumber', '关联的销售订单物料行号', 'related_sale_order_material_line_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'scheduledProductionQuantity', '排产数量', 'scheduled_production_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'needProduceQuantity', '下发数量', 'need_produce_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'finishCount', '完成数量', 'finish_count', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'unqualifiedCount', '不合格数', 'unqualified_count', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'plannedBatches', '计划批数', 'planned_batches', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'plansPerBatch', '每批计划数', 'plans_per_batch', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'actualBatches', '实际批数', 'actual_batches', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'planProductStartTime', '计划开始生产时间', 'plan_product_start_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'planProductEndTime', '计划生产完成时间', 'plan_product_end_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'actualProductStartTime', '实际开始生产时间', 'actual_product_start_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'actualProductEndTime', '实际生产完成时间', 'actual_product_end_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'progress', '完成率', 'progress', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'procedureProcess', '生产订单工序进度', 'procedure_process', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'procedureProcessDetail', '生产订单工序进度详情', 'procedure_process_detail', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'mergedState', '合单状态', 'merged_state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'mergeOrderNumber', '关联的合单号', 'merge_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'priority', '优先级', 'priority', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'gridCode', '车间编码', 'grid_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'packageSchemeCode', '包装方案编码', 'package_scheme_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'processStatus', '过程状态', 'process_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'pickingQuantity', '已领料数', 'picking_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'inventoryQuantity', '已入库数', 'inventory_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'customerOrderNumber', '客户订单编号', 'customer_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'customerMaterialCode', '客户物料编码', 'customer_material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'customerMaterialName', '客户物料名称', 'customer_material_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'customerSpecification', '客户物料规格', 'customer_specification', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'businessUnitCode', '业务单元编码', 'business_unit_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'businessUnitName', '业务单元名称', 'business_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldOne', '生产订单物料拓展字段1', 'product_order_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldTwo', '生产订单物料拓展字段2', 'product_order_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldThree', '生产订单物料拓展字段3', 'product_order_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldFour', '生产订单物料拓展字段4', 'product_order_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldFive', '生产订单物料拓展字段5', 'product_order_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldSix', '生产订单物料拓展字段6', 'product_order_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldSeven', '生产订单物料拓展字段7', 'product_order_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldEight', '生产订单物料拓展字段8', 'product_order_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldNine', '生产订单物料拓展字段9', 'product_order_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'productOrderMaterial', 'productOrderMaterialExtendFieldTen', '生产订单物料拓展字段10', 'product_order_material_extend_field_ten', NULL);

-- 工单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderNumber', '生产工单号', 'work_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'materialCode', '物料编号', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'businessType', '业务类型', 'business_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'orderType', '单据类型', 'order_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'priority', '优先级', 'priority', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'state', '状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'assignmentState', '派工状态', 'assignment_state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'customerName', '客户名称', 'customer_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'customerCode', '客户编号', 'customer_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'productOrderNumber', '关联的生产订单号', 'product_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'saleOrderNumber', '关联的销售订单号', 'sale_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'craftCode', '工艺编码', 'craft_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workCenterId', '工作中心ID', 'work_center_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workCenterType', '工作中心类型', 'work_center_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workCenterName', '工作中心名称', 'work_center_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'skuId', '特征参数skuId', 'skuId', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'pendentQuantity', '待排数量', 'pendent_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'schedulingState', '排程状态', 'scheduling_state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'executionStatus', '工单执行状态', 'execution_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'startDate', '计划开始时间', 'start_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'endDate', '计划结束时间', 'end_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'actualStartDate', '实际开始时间', 'actual_start_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'actualEndDate', '实际结束时间', 'actual_end_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'magNickname', '计划员姓名', 'mag_nickname', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'magName', '计划员账号', 'mag_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'customerMaterialCode', '客户物料编码', 'customer_material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'customerMaterialName', '客户物料名称', 'customer_material_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'customerSpecification', '客户物料规格', 'customer_specification', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldOne', '工单扩展字段1', 'work_order_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldTwo', '工单扩展字段2', 'work_order_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldThree', '工单扩展字段3', 'work_order_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldFour', '工单扩展字段4', 'work_order_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldFive', '工单扩展字段5', 'work_order_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldSix', '工单扩展字段6', 'work_order_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldSeven', '工单扩展字段7', 'work_order_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldEight', '工单扩展字段8', 'work_order_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldNine', '工单扩展字段9', 'work_order_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderExtendFieldTen', '工单扩展字段10', 'work_order_extend_field_ten', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldOne', '工单物料扩展字段1', 'work_order_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldTwo', '工单物料扩展字段2', 'work_order_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldThree', '工单物料扩展字段3', 'work_order_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldFour', '工单物料扩展字段4', 'work_order_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldFive', '工单物料扩展字段5', 'work_order_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldSix', '工单物料扩展字段6', 'work_order_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldSeven', '工单物料扩展字段7', 'work_order_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldEight', '工单物料扩展字段8', 'work_order_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldNine', '工单物料扩展字段9', 'work_order_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderMaterialExtendFieldTen', '工单物料扩展字段10', 'work_order_material_extend_field_ten', NULL);


-- 报工小程序默认隐藏 质量等级、理论工时
UPDATE dfs_form_field_rule_config SET is_show = 0 WHERE full_path_code IN('orderReportApp.addReport', 'orderReportApp.editReport', 'workOrderReport.addReport', 'workOrderReport.editReport') AND field_code IN('qualityLevel', 'theoryHour');

-- 历史数据处理，生产基本单元只能关联一个工作中心
-- 工作中心--班组关联表
-- 1. 创建临时表
CREATE TEMPORARY TABLE `dfs_work_center_team_temp` LIKE `dfs_work_center_team`;
-- 2. 插入去重后的数据到临时表
INSERT INTO `dfs_work_center_team_temp` (`work_center_id`, `team_id`)
SELECT
    MIN(`work_center_id`) AS `work_center_id`,
    `team_id`
FROM `dfs_work_center_team`
GROUP BY `team_id`;
-- 3. 删除原表数据
DELETE FROM `dfs_work_center_team`;
-- 4. 将临时表数据插入原表
INSERT INTO `dfs_work_center_team` (`work_center_id`, `team_id`)
SELECT `work_center_id`, `team_id`
FROM `dfs_work_center_team_temp`;
-- 5. 删除临时表
-- DROP TEMPORARY TABLE IF EXISTS `dfs_work_center_team_temp`;

-- 工作中心--设备关联表
-- 1. 创建临时表
CREATE TEMPORARY TABLE `dfs_work_center_device_temp` LIKE `dfs_work_center_device`;
-- 2. 插入去重后的数据到临时表
INSERT INTO `dfs_work_center_device_temp` (`work_center_id`, `device_id`)
SELECT
    MIN(`work_center_id`) AS `work_center_id`,
    `device_id`
FROM `dfs_work_center_device`
GROUP BY `device_id`;
-- 3. 删除原表数据
DELETE FROM `dfs_work_center_device`;
-- 4. 将临时表数据插入原表
INSERT INTO `dfs_work_center_device` (`work_center_id`, `device_id`)
SELECT `work_center_id`, `device_id`
FROM `dfs_work_center_device_temp`;
-- 5. 删除临时表
-- DROP TEMPORARY TABLE IF EXISTS `dfs_work_center_device_temp`;


UPDATE dfs_config_notice_level_relation SET parent_code = 'workOrder' WHERE code = 'productTimeOut';
DELETE from dfs_config_notice_level_relation
WHERE `code` IN('inspectionForm', 'taskInfoNotice', 'unusualInfoNotice', 'productCirculationNotice', 'productCollaborationNotice', 'qualityInfoNotice', 'projectNotice', 'processAssemblyNotice')
OR `parent_code` IN ('inspectionForm', 'taskInfoNotice', 'unusualInfoNotice', 'productCirculationNotice', 'productCollaborationNotice', 'qualityInfoNotice', 'projectNotice', 'processAssemblyNotice');

-- 委外用料清单、委外发料单据类型保留一个设为默认的单据类型
UPDATE dfs_order_type_item SET is_default=1 WHERE business_type_code="standardSubcontractDelivery" AND `code`="subcontractDelivery";
UPDATE dfs_order_type_item SET is_default=1 WHERE business_type_code="standardSubcontractMaterialList" AND `code`="subcontractMaterialList";
