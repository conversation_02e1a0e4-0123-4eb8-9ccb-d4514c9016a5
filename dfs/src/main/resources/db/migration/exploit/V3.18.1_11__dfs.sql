UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"0', '"code":\"10\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"2', '"code":\"12\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"3', '"code":\"13\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"4', '"code":\"14\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"5', '"code":\"15\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"6', '"code":\"16\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"7', '"code":\"17\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"8', '"code":\"18\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"1"9', '"code":\"19\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"0', '"code":\"20\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"1', '"code":\"21\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"2', '"code":\"22\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"3', '"code":\"23\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"4', '"code":\"24\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"5', '"code":\"25\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"6', '"code":\"26\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"7', '"code":\"27\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"8', '"code":\"28\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"2"9', '"code":\"29\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"3"0', '"code":\"30\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"3"1', '"code":\"31\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"3"2', '"code":\"32\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":"3"3', '"code":\"33\"');