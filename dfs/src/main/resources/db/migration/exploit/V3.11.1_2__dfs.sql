-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- 表单配置新增`关联API编码`，并兼容历史配置的api数据
call proc_add_column(
        'dfs_form_field_rule_config',
        'related_api_code',
        'ALTER TABLE `dfs_form_field_rule_config` ADD COLUMN `related_api_code` varchar(255) NULL COMMENT ''关联API编码'' AFTER `option_values_type`');

-- 默认将输入方式打开，且为空的数据赋值为 '输入框'
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input' WHERE `value_gray` = 0 AND `input_type` is NULL;
-- 更新表单配置编辑页的字段编码
UPDATE `dfs_form_field_rule_config` SET `field_code` = 'sort' WHERE `field_name_full_path_code` = 'material.edit' and `field_code` = 'sortName';
-- 表单配置将可必填的配置字段的操作选项打开
-- 为稳定上线需求，前端仅对接表单配置的必填项，以下注释内容，接口调用及对应逻辑由前端完成
-- 生产订单
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getProductOrderStateList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'state' AND `field_name_full_path_code` = 'productOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'remark' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getProductOrderTypeList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'type' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getSaleOrderList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'saleOrderCode' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getProjectDefineList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'projectDefineId' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getContractInfoList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'contractId' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getMaterialList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'code' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getPackageSchemeList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'schemeName' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getGridList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'gridCode' AND `field_name_full_path_code` = 'productOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getProductOrderGradeList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'priority' AND `field_name_full_path_code` = 'productOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'orderMaterialRemark' AND `field_name_full_path_code` = 'productOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'customerMaterialCode' AND `field_name_full_path_code` = 'productOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'customerMaterialName' AND `field_name_full_path_code` = 'productOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'customerSpecification' AND `field_name_full_path_code` = 'productOrder.edit';
--
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getProductOrderStateList', '获取生产订单状态列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/ams/product/orders/all/state', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 0, 1, 'function transform(){\r\n}', 'function transform(){\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getProductOrderTypeList', '获取生产订单类型列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/ams/product/orders/get/type', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 0, 1, 'function transform(){\r\n}', 'function transform(){\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getSaleOrderList', '获取销售订单列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/ams/sale/orders/order/list', 'POST', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', '生效、完成态的', 0, 1, 1, 'function transform(){\r\n}', 'function transform(){\r\n    return {\r\n    \"size\": 20,\r\n    \"current\": 1,\r\n    \"states\": \"2,3\",\r\n    \"showType\": \"material\"\r\n    };\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getProjectDefineList', '获取项目名称列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/pms/project/define/simple/list', 'POST', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 1, 1, 'function transform(){\r\n}', 'function transform(){\r\n    return {\r\n    \"states\": \"2,3,4\"\r\n    };\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getContractInfoList', '获取销售合同列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/pms/contract/info/simple/list', 'POST', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 1, 1, 'function transform(){\r\n}', 'function transform(){\r\n    return {\r\n    \"states\": \"2\",\r\n    \"contractType\": \"saleContract\"\r\n    };\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getMaterialList', '获取物料列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/api/materials/list', 'POST', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 1, 1, 'function transform(){\r\n}', 'function transform(){\r\n    return {\r\n    \"size\": 20,\r\n    \"current\": 1,\r\n    \"states\": \"2\"\r\n    };\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getPackageSchemeList', '获取包装方案列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/api/packages/scheme/list', 'POST', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 1, 1, 'function transform(){\r\n}', 'function transform(){\r\n    return {\r\n    \"current\": 1,\r\n    \"size\": 9999,\r\n    \"states\": 2\r\n    };\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getGridList', '获取车间列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/api/grids/list', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 0, 1, 'function transform(){\r\n}', 'function transform(){\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getProductOrderGradeList', '获取生产订单优先级列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/ams/product/orders/all/grades', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 0, 1, 'function transform(){\r\n}', 'function transform(){\r\n}', 'function transform(data){\r\n  return data;\r\n}');
-- 生产工单
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getWorkOrderStateList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'state' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getWorkOrderTypeList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'orderType' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getProductOrderList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'productOrderNumber' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getSaleOrderList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'saleOrderNumber' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getMaterialList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'code' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getMaterialList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'name' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getCustomerList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getCustomerList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'supplierCode' AND `field_name_full_path_code` = 'workOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'remark' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getProjectDefineList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'projectDefineId' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getContractInfoList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'contractId' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getWorkOrderAssignmentStateList', `input_type` = 'select', `option_values_type` = 'api', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'assignmentState' AND `field_name_full_path_code` = 'workOrder.edit';
-- UPDATE `dfs_form_field_rule_config` SET `related_api_code` = 'getProductOrderGradeList', `input_type` = 'select', `option_values_type` = 'api', `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'priority' AND `field_name_full_path_code` = 'workOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'customerMaterialCode' AND `field_name_full_path_code` = 'workOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'customerMaterialName' AND `field_name_full_path_code` = 'workOrder.edit';
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input', `input_gray` = 0, `value_gray` = 0 WHERE `field_code` = 'customerSpecification' AND `field_name_full_path_code` = 'workOrder.edit';
--
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getWorkOrderStateList', '获取生产工单状态列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/api/workorders/all/state', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 0, 1, 'function transform(){\r\n}', 'function transform(){\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getWorkOrderTypeList', '获取生产工单类型列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/api/workorders/type', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 0, 1, 'function transform(){\r\n}', 'function transform(){\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getWorkOrderAssignmentStateList', '获取生产工单派工状态列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/api/workorders/assignment/state', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', NULL, 0, 0, 1, 'function transform(){\r\n}', 'function transform(){\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getCustomerList', '获取客户列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/api/customers/list', 'GET', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', '创建、生效态的', 0, 1, 1, 'function transform(){\r\n}', 'function transform(){\r\n    return {\r\n    \"cusState\": \"1,2\"\r\n    };\r\n}', 'function transform(data){\r\n  return data;\r\n}');
INSERT INTO `dfs_api_transform`(`id`, `code`, `name`, `state`, `url`, `request_method`, `request_header`, `remark`, `req_param_script_enable`, `req_body_script_enable`, `res_body_script_enable`, `req_param_script`, `req_body_script`, `res_body_script`) VALUES (NULL, 'getProductOrderList', '获取生产订单列表', 'ENABLE', 'https://127.0.0.1:8080/factory/v1/ams/product/orders/list', 'POST', '{\"Content-Type\":\"application/json;charset=UTF-8\"}', '生效、完成态的', 0, 1, 1, 'function transform(){\r\n}', 'function transform(){\r\n    return {\r\n    \"size\": 20,\r\n    \"current\": 1,\r\n    \"states\": \"2,3\"\r\n    };\r\n}', 'function transform(data){\r\n  return data;\r\n}');

-- 表单配置编辑创建态的单据编号允许修改
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1 WHERE `field_code` = 'workOrderNumber' AND `full_path_code` = 'workOrder.editByCreate';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1 WHERE `field_code` = 'saleOrderNumber' AND `full_path_code` = 'saleOrder.editByCreate';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1 WHERE `field_code` = 'productOrderNumber' AND `full_path_code` = 'productOrder.editByCreate';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1 WHERE `field_code` = 'code' AND `full_path_code` = 'material.editByCreate';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1 WHERE `field_code` = 'requestNum' AND `full_path_code` = 'purchaseRequestOrder.editByCreate';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1 WHERE `field_code` = 'purchaseCode' AND `full_path_code` = 'purchaseOrder.editByCreate';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1 WHERE `field_code` = 'receiptCode' AND `full_path_code` = 'purchaseReceiptOrder.editByCreate';





-- 委外订单路由
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'subcontractOrder', '委外订单(当前版本仅下推有效)', 'subcontractOrder', '', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'subcontractOrderList', '委外订单列表', 'subcontractOrder.list', 'subcontractOrder', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'subcontractOrderListByOrder', '按单展示', 'subcontractOrder.list.order', 'subcontractOrder.list', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'subcontractOrderListByMaterial', '按物料展示', 'subcontractOrder.list.material', 'subcontractOrder.list', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'subcontractOrderEdit', '委外订单(状态)', 'subcontractOrder.edit', 'subcontractOrder', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'subcontractOrderEditByCreate', '创建态', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'subcontractOrderEditByRelease', '生效态', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'subcontractOrderEditByFinish', '完成态', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'subcontractOrderEditByClosed', '关闭态', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', 0, 'subcontractOrderEditByCancel', '取消态', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', NULL, 'admin', 'admin', NOW(), NOW());
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'web', NULL, 'subcontractOrderDetail', '委外订单详情', 'subcontractOrder.detail', 'subcontractOrder', NULL, 'admin', 'admin', NOW(), NOW());

-- 委外订单相关字段配置
call proc_add_form_field("subcontractOrder.list.material", "subcontractOrderNumber", "委外订单号");
call proc_add_form_field("subcontractOrder.list.material", "state", "状态");
call proc_add_form_field("subcontractOrder.list.material", "supplier", "供应商编码");
call proc_add_form_field("subcontractOrder.list.material", "supplierName", "供应商名称");
call proc_add_form_field("subcontractOrder.list.material", "customer", "客户编码");
call proc_add_form_field("subcontractOrder.list.material", "customerName", "客户名称");
call proc_add_form_field("subcontractOrder.list.material", "code", "物料编码");
call proc_add_form_field("subcontractOrder.list.material", "name", "物料名称");
call proc_add_form_field("subcontractOrder.list.material", "standard", "物料规格");
call proc_add_form_field("subcontractOrder.list.material", "auxiliaryAttr", "特征参数");
call proc_add_form_field("subcontractOrder.list.material", "quantity", "订单数量");
call proc_add_form_field("subcontractOrder.list.material", "receiptQuantity", "收货数量");
call proc_add_form_field("subcontractOrder.list.material", "deliveryQuantity", "发料数量");
call proc_add_form_field("subcontractOrder.list.material", "inventoryQuantity", "入库数量");
call proc_add_form_field("subcontractOrder.list.material", "saleOrderNumber", "销售订单号");
call proc_add_form_field("subcontractOrder.list.material", "productOrderNumber", "生产订单号");
call proc_add_form_field("subcontractOrder.list.material", "procedureName", "工序名称");
call proc_add_form_field("subcontractOrder.list.material", "orderRemark", "订单备注");
call proc_add_form_field("subcontractOrder.list.material", "materialRemark", "物料行备注");
call proc_add_form_field("subcontractOrder.list.material", "createBy", "创建人");
call proc_add_form_field("subcontractOrder.list.material", "createTime", "创建时间");
call proc_add_form_field("subcontractOrder.list.material", "nameEnglish", "物料英文名称");
call proc_add_form_field("subcontractOrder.list.material", "drawingNumber", "图号");
call proc_add_form_field("subcontractOrder.list.material", "rawMaterial", "材质");
call proc_add_form_field("subcontractOrder.list.material", "factoryModel", "工厂型号");
call proc_add_form_field("subcontractOrder.list.material", "materialPrice", "物料单价");
call proc_add_form_field("subcontractOrder.list.material", "minimumProductionLot", "最小生产批量");
call proc_add_form_field("subcontractOrder.list.material", "loseRate", "损耗率");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_remark", "物料备注");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldOne", "扩展字段1");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldTwo", "扩展字段2");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldThree", "扩展字段3");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldFour", "扩展字段4");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldFive", "扩展字段5");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldSix", "扩展字段6");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldSeven", "扩展字段7");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldEight", "扩展字段8");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldNine", "扩展字段9");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldTen", "扩展字段10");
call proc_add_form_field("subcontractOrder.list.material", "materialFields_customFieldEleven", "扩展字段11");

call proc_add_form_field("subcontractOrder.list.order", "subcontractOrderNumber", "委外订单号");
call proc_add_form_field("subcontractOrder.list.order", "state", "状态");
call proc_add_form_field("subcontractOrder.list.order", "customer", "客户编码");
call proc_add_form_field("subcontractOrder.list.order", "customerName", "客户名称");
call proc_add_form_field("subcontractOrder.list.order", "materialsQuantity", "订单数量");
call proc_add_form_field("subcontractOrder.list.order", "materialsReceiptQuantity", "收货数量");
call proc_add_form_field("subcontractOrder.list.order", "materialsDeliveryQuantity", "发料数量");
call proc_add_form_field("subcontractOrder.list.order", "materialsInventoryQuantity", "入库数量");
call proc_add_form_field("subcontractOrder.list.order", "saleOrderNumber", "销售订单号");
call proc_add_form_field("subcontractOrder.list.order", "orderRemark", "订单备注");

call proc_add_form_field("subcontractOrder.edit", "subcontractOrderNumber", "委外订单号");
call proc_add_form_field("subcontractOrder.edit", "saleOrderNumber", "销售订单号");
call proc_add_form_field("subcontractOrder.edit", "productOrderNumber", "生产订单号");
call proc_add_form_field("subcontractOrder.edit", "state", "状态");
call proc_add_form_field("subcontractOrder.edit", "customer", "客户编码");
call proc_add_form_field("subcontractOrder.edit", "customerName", "客户名称");
call proc_add_form_field("subcontractOrder.edit", "customerContact", "客户电话");
call proc_add_form_field("subcontractOrder.edit", "customerAddr", "客户地址");
call proc_add_form_field("subcontractOrder.edit", "approver", "审批人");
call proc_add_form_field("subcontractOrder.edit", "actualApprover", "实际审批人");
call proc_add_form_field("subcontractOrder.edit", "createBy", "创建人");
call proc_add_form_field("subcontractOrder.edit", "createTime", "创建时间");
call proc_add_form_field("subcontractOrder.edit", "updateBy", "更新人");
call proc_add_form_field("subcontractOrder.edit", "updateTime", "更新时间");
call proc_add_form_field("subcontractOrder.edit", "orderRemark", "订单备注");
call proc_add_form_field("subcontractOrder.edit", "materialCode", "物料编码");
call proc_add_form_field("subcontractOrder.edit", "name", "物料名称");
call proc_add_form_field("subcontractOrder.edit", "typeName", "物料类型");
call proc_add_form_field("subcontractOrder.edit", "auxiliaryAttr", "特征参数");
call proc_add_form_field("subcontractOrder.edit", "procedureName", "工序");
call proc_add_form_field("subcontractOrder.edit", "quantity", "订单数量");
call proc_add_form_field("subcontractOrder.edit", "comp", "单位");
call proc_add_form_field("subcontractOrder.edit", "haveBom", "BOM");
call proc_add_form_field("subcontractOrder.edit", "haveCraft", "工艺信息");
call proc_add_form_field("subcontractOrder.edit", "supplier", "供应商编码");
call proc_add_form_field("subcontractOrder.edit", "supplierName", "供应商");
call proc_add_form_field("subcontractOrder.edit", "supplierPhone", "供应商联系方式");
call proc_add_form_field("subcontractOrder.edit", "supplierAddr", "供应商地址");
call proc_add_form_field("subcontractOrder.edit", "priority", "优先级");
call proc_add_form_field("subcontractOrder.edit", "planStartTime", "计划生产开始时间");
call proc_add_form_field("subcontractOrder.edit", "planEndTime", "计划生产完成时间");
call proc_add_form_field("subcontractOrder.edit", "materialRemark", "行备注");

call proc_add_form_field("subcontractOrder.detail", "subcontractOrderNumber", "委外订单号");
call proc_add_form_field("subcontractOrder.detail", "saleOrderNumber", "销售订单号");
call proc_add_form_field("subcontractOrder.detail", "productOrderNumber", "生产订单号");
call proc_add_form_field("subcontractOrder.detail", "state", "状态");
call proc_add_form_field("subcontractOrder.detail", "customer", "客户编码");
call proc_add_form_field("subcontractOrder.detail", "customerName", "客户名称");
call proc_add_form_field("subcontractOrder.detail", "customerContact", "客户电话");
call proc_add_form_field("subcontractOrder.detail", "customerAddr", "客户地址");
call proc_add_form_field("subcontractOrder.detail", "approver", "审批人");
call proc_add_form_field("subcontractOrder.detail", "actualApprover", "实际审批人");
call proc_add_form_field("subcontractOrder.detail", "createBy", "创建人");
call proc_add_form_field("subcontractOrder.detail", "createTime", "创建时间");
call proc_add_form_field("subcontractOrder.detail", "updateBy", "更新人");
call proc_add_form_field("subcontractOrder.detail", "updateTime", "更新时间");
call proc_add_form_field("subcontractOrder.detail", "orderRemark", "订单备注");
call proc_add_form_field("subcontractOrder.detail", "materialCode", "物料编码");
call proc_add_form_field("subcontractOrder.detail", "name", "物料名称");
call proc_add_form_field("subcontractOrder.detail", "typeName", "物料类型");
call proc_add_form_field("subcontractOrder.detail", "auxiliaryAttr", "特征参数");
call proc_add_form_field("subcontractOrder.detail", "procedureName", "工序");
call proc_add_form_field("subcontractOrder.detail", "quantity", "订单数量");
call proc_add_form_field("subcontractOrder.detail", "comp", "单位");
call proc_add_form_field("subcontractOrder.detail", "haveBom", "BOM");
call proc_add_form_field("subcontractOrder.detail", "haveCraft", "工艺信息");
call proc_add_form_field("subcontractOrder.detail", "supplier", "供应商编码");
call proc_add_form_field("subcontractOrder.detail", "supplierName", "供应商");
call proc_add_form_field("subcontractOrder.detail", "supplierPhone", "供应商联系方式");
call proc_add_form_field("subcontractOrder.detail", "supplierAddr", "供应商地址");
call proc_add_form_field("subcontractOrder.detail", "priority", "优先级");
call proc_add_form_field("subcontractOrder.detail", "planStartTime", "计划生产开始时间");
call proc_add_form_field("subcontractOrder.detail", "planEndTime", "计划生产完成时间");
call proc_add_form_field("subcontractOrder.detail", "materialRemark", "行备注");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "subcontractOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "state", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "code", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "standard", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "auxiliaryAttr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "quantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "receiptQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "deliveryQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "inventoryQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderRemark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialRemark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "nameEnglish", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "drawingNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "rawMaterial", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "factoryModel", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialPrice", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "minimumProductionLot", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "loseRate", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_remark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldOne", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldTwo", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldThree", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldFour", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldFive", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldSix", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldSeven", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldEight", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldNine", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldTen", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "materialFields_customFieldEleven", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "subcontractOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "state", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "materialsQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "materialsReceiptQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "materialsDeliveryQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "materialsInventoryQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderRemark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "subcontractOrderNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "saleOrderNumber", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "state", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "customerContact", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "customerAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "approver", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "updateBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "updateTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "orderRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "materialCode", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "typeName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "auxiliaryAttr", 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "procedureName", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "quantity", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "haveBom", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "haveCraft", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "supplier", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "supplierName", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "planStartTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "planEndTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', "materialRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "subcontractOrderNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "state", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "customerContact", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "customerAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "updateBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "updateTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "orderRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "typeName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "auxiliaryAttr", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "quantity", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "haveBom", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "haveCraft", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "supplier", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "supplierName", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "planStartTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "planEndTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', "materialRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "subcontractOrderNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "state", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "customerContact", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "customerAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "updateBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "updateTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "orderRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "typeName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "auxiliaryAttr", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "quantity", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "haveBom", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "haveCraft", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "supplier", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "supplierName", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "planStartTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "planEndTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', "materialRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "subcontractOrderNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "state", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "customerContact", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "customerAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "updateBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "updateTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "orderRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "typeName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "auxiliaryAttr", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "quantity", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "haveBom", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "haveCraft", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "supplier", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "supplierName", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "planStartTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "planEndTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', "materialRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "subcontractOrderNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "state", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "customerContact", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "customerAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "updateBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "updateTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "orderRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "typeName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "auxiliaryAttr", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "quantity", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "haveBom", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "haveCraft", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "supplier", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "supplierName", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "planStartTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "planEndTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', "materialRemark", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "subcontractOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "saleOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "productOrderNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "state", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "customer", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "customerName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "customerContact", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "customerAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "updateBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "updateTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderRemark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "materialCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "typeName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "auxiliaryAttr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "procedureName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "quantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "haveBom", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "haveCraft", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "priority", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "planStartTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "planEndTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "materialRemark", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 审批字段、特征参数统一增加备注说明
UPDATE `dfs_form_field_rule_config`
SET `remark` = "该字段和审批配置冲突，请以审批配置为准，此字段置灰，仅允许重命名 (冲突位置：配置中心-->单据配置-->审批配置)"
WHERE `field_code` like "%approver%" and full_path_code like "subcontractOrder%";

UPDATE `dfs_form_field_rule_config`
SET `remark` = "该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突位置：配置中心-->系统配置-->业务配置配置-->物料特征参数配置)"
WHERE `field_code` = "auxiliaryAttr" and full_path_code like "subcontractOrder%";

-- 采购订单、采购需求单的客户名称禁用不可编辑
UPDATE `dfs_form_field_rule_config` SET `need_gray` = 1, `is_need` = 0, `edit_gray` = 1, `is_edit` = 0 WHERE `field_name_full_path_code` = 'purchaseOrder.edit' AND `field_code` = 'customerName';
UPDATE `dfs_form_field_rule_config` SET `need_gray` = 1, `is_need` = 0, `edit_gray` = 1, `is_edit` = 0 WHERE `field_name_full_path_code` = 'purchaseRequestOrder.edit' AND `field_code` = 'customerName';

-- 采购订单的采购员、供应商编码、供应商名称应该是非必填
UPDATE `dfs_form_field_rule_config` SET `is_show`=1,`is_need`=1,`is_edit`=1,`show_gray`=1,`need_gray`=0,`edit_gray`=0 WHERE `field_name_full_path_code` = 'purchaseOrder.edit' AND `field_code` = 'buyer';
UPDATE `dfs_form_field_rule_config` SET `is_show`=1,`is_need`=1,`is_edit`=1,`show_gray`=1,`need_gray`=0,`edit_gray`=0 WHERE `field_name_full_path_code` = 'purchaseOrder.edit' AND `field_code` = 'supplierCode';
UPDATE `dfs_form_field_rule_config` SET `is_show`=1,`is_need`=1,`is_edit`=1,`show_gray`=1,`need_gray`=0,`edit_gray`=0 WHERE `field_name_full_path_code` = 'purchaseOrder.edit' AND `field_code` = 'supplierName';

-- 物料编辑分类字段有误
UPDATE `dfs_form_field_config` SET `field_code` = 'sort' WHERE (`full_path_code` = 'material.edit') AND `field_code` = 'sortName';
UPDATE `dfs_form_field_config` SET `field_name` = 'sort' WHERE (`full_path_code` = 'material.edit') AND `field_code` = 'sort' AND `type_code` = 'originalField';

-- 采购订单物料编码的表单配置必填
UPDATE `dfs_form_field_rule_config` SET `is_need` = 1 WHERE `field_name_full_path_code` = 'purchaseOrder.edit' AND `field_code` = 'code';
-- 生效的工单支持编辑工艺编码
UPDATE `dfs_form_field_rule_config` SET `edit_gray` = 0, `is_edit` = 1 WHERE `full_path_code` = 'workOrder.editByRelease' AND `field_code` = 'craftCode';
-- 采购需求编辑态的客户编码的比`是否编辑`置灰可填
UPDATE `dfs_form_field_rule_config` SET `edit_gray` = 1, `is_edit` = 1 WHERE `field_name_full_path_code` = 'purchaseRequestOrder.edit' AND `field_code` = 'customerCode';
-- 生产订单行备注因前端不支持下拉赋值，故后台不提供下拉配置
UPDATE `dfs_form_field_rule_config` SET `input_gray` = 1, `value_gray` = 1 WHERE `field_name_full_path_code` = 'productOrder.edit' AND `field_code` = 'orderMaterialRemark';












-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.11.1.1=======================================================
