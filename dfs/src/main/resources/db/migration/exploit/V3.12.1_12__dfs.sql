-- 树结构
INSERT INTO `dfs_model` (`type`, `name`, `code`, `seq`, `pid`) VALUES ('baseTargetGroupObject', '工序', 'procedureTarget', 12, -1);
INSERT INTO `dfs_model` (`type`, `name`, `code`, `seq`, `pid`) VALUES ('baseTargetGroupObject', '人员', 'userTarget', 13, -1);
update `dfs_model` set seq = 100 where `code` = 'customTarget' and `type` = 'baseTargetGroupObject';

-- 指标
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('lineMonthly', '产线每月统计', '1', 'baseTargetGroupObject', 'lineTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('lineMonthly', 'baseTargetGroupObject', '系统统计', 'lineMonthly', 1, NULL);
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('lineMonthly', (SELECT id FROM `dfs_model` WHERE `code` = 'lineTarget'), 'dfs_metrics', 'dfs_metrics_line_monthly');

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('lineMaterialDefectDaily', '产线物料每日不良项统计', '1', 'baseTargetGroupObject', 'lineTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('lineMaterialDefectDaily', 'baseTargetGroupObject', '系统统计', 'lineMaterialDefectDaily', 1, NULL);
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('lineMaterialDefectDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'lineTarget'), 'dfs_metrics', 'dfs_metrics_line_material_defect_daily');

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('procedureHourly', '工序小时统计', '1', 'baseTargetGroupObject', 'procedureTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('procedureHourly', 'baseTargetGroupObject', '系统统计', 'procedureHourly', 1, NULL);
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('procedureHourly', (SELECT id FROM `dfs_model` WHERE `code` = 'procedureTarget'), 'dfs_metrics', 'dfs_metrics_procedure_hourly');

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('staffDaily', '员工每日统计', '1', 'baseTargetGroupObject', 'userTarget', NULL, NULL, '1', NULL, NULL, NULL, 1, 5, 'm');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('staffDaily', 'baseTargetGroupObject', '系统统计', 'staffDaily', 1, NULL);
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('staffDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'userTarget'), 'dfs_metrics', 'dfs_metrics_staff_daily');


-- 班组每日遗漏
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('teamDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'baseTeam'), 'dfs_metrics', 'dfs_metrics_team_daily');
