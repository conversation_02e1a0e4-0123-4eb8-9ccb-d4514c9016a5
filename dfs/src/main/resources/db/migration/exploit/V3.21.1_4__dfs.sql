-- 工艺工序
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'id', '工艺工序id', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'craftId', '工艺id', 'craft_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'procedureId', '工序id', 'procedure_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'procedureName', '工序名称', 'procedure_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'facType', '工位类型', 'fac_type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'outSourcingSupplier', '委外供应商', 'out_sourcing_supplier');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'isSubContractingOperation', '是否委外工序', 'is_sub_contracting_operation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'isLastProcedure', '是否为最后一道工序', 'is_last_procedure');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'isInspectionOperation', '是否为检验工序', 'is_inspection_operation');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'type', '生产基本单元类型', 'type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'extend', '扩展字段', 'extend');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craftProcedure', 'remark', '备注', 'remark');

-- 任务表
call proc_add_column(
        'dfs_task',
        'order_id',
        'ALTER TABLE `dfs_task` ADD COLUMN `order_id`  varchar(255) DEFAULT NULL COMMENT ''单据表主键''');