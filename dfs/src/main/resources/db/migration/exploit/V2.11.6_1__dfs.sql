-- DDL

call proc_add_column(
        'dfs_material_config_field',
        'remark',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `remark` varchar(255) NULL DEFAULT null COMMENT ''备注''');

-- 物料表新增多个拓展字段
call proc_add_column(
        'dfs_material',
        'custom_field_one',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_one` varchar(255) NULL DEFAULT null COMMENT ''扩展字段1''');
call proc_add_column(
        'dfs_material',
        'custom_field_two',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_two` varchar(255) NULL DEFAULT null COMMENT ''扩展字段2''');
call proc_add_column(
        'dfs_material',
        'custom_field_three',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_three` varchar(255) NULL DEFAULT null COMMENT ''扩展字段3''');
call proc_add_column(
        'dfs_material',
        'custom_field_four',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_four` varchar(255) NULL DEFAULT null COMMENT ''扩展字段4''');
call proc_add_column(
        'dfs_material',
        'custom_field_five',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_five` varchar(255) NULL DEFAULT null COMMENT ''扩展字段5''');
call proc_add_column(
        'dfs_material',
        'custom_field_six',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_six` varchar(255) NULL DEFAULT null COMMENT ''扩展字段6''');
call proc_add_column(
        'dfs_material',
        'custom_field_seven',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_seven` varchar(255) NULL DEFAULT null COMMENT ''扩展字段7''');
call proc_add_column(
        'dfs_material',
        'custom_field_eight',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_eight` varchar(255) NULL DEFAULT null COMMENT ''扩展字段8''');
call proc_add_column(
        'dfs_material',
        'custom_field_nine',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_nine` varchar(255) NULL DEFAULT null COMMENT ''扩展字段9''');
call proc_add_column(
        'dfs_material',
        'custom_field_ten',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_ten` varchar(255) NULL DEFAULT null COMMENT ''扩展字段10''');
call proc_add_column(
        'dfs_material',
        'custom_field_eleven',
        'ALTER TABLE `dfs_material` ADD COLUMN `custom_field_eleven` varchar(255) NULL DEFAULT null COMMENT ''扩展字段11''');

-- 物料拓展字段表新增 默认字段名
call proc_add_column(
        'dfs_material_config_field',
        'default_field_name',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `default_field_name` varchar(255) NULL DEFAULT null COMMENT '' 默认字段名'' after `field_code`');

-- DML


-- 新增扩展字段配置 菜单
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`,`update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`,`parent_path`, `is_enable`)
VALUES ('11804', '扩展字段配置', '/material-config/extend-config', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable','GET', '118', 1, 1, 1, '/material-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`,`update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`,`parent_path`, `is_enable`)
VALUES ('11804010', '编辑', 'extend.config:update', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable','GET', '11804', 2, 1, 1, '/material-config/extend-config', 1);
-- 初始化权限
call init_new_role_permission('11804%');

-- 物料新增可配置的默认扩展字段
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldOne', '扩展字段1', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldTwo', '扩展字段2', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldThree', '扩展字段3', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldFour', '扩展字段4', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldFive', '扩展字段5', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldSix', '扩展字段6', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldSeven', '扩展字段7', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldEight', '扩展字段8', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldNine', '扩展字段9', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldTen', '扩展字段10', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('customFieldEleven', '扩展字段11', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);

-- 将字段名copy到默认字段名称中
UPDATE dfs_material_config_field SET default_field_name = field_name;

-- 兼容 物料类型可配置扩展字段的历史数据
DROP PROCEDURE IF EXISTS `proc_compatible_material_type_field`;
DELIMITER $$
CREATE DEFINER=`root`@`%` PROCEDURE `proc_compatible_material_type_field`()
top:BEGIN

    -- 因新增多个物料拓展字段，此存储过程目的是兼容 历史的 物料类型拓展字段的默认数据，此数据仅可执行一次

    -- 定义变量
    DECLARE s int DEFAULT 0;
    DECLARE type_id int;
    DECLARE type_name varchar(255);
    DECLARE exec_count int;

    -- 定义游标，并将sql结果集赋值到游标中
    DECLARE cur CURSOR FOR SELECT `id`,`name` FROM dfs_dict where type = 'materialType';

    -- 声明当游标遍历完后将标志变量置成某个值
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET s = 1;

    -- 如果已经执行过，则直接返回
    SELECT count(field_code) INTO exec_count FROM dfs_material_type_config_field WHERE field_code = 'customFieldOne' ;
    IF(exec_count >= 1) THEN
        leave top;
    END IF;

    -- 打开游标
    open cur;
    -- 将游标中的值赋值给变量，注意：变量名不要和返回的列名同名，变量顺序要和sql结果列的顺序一致
    fetch cur into type_id, type_name;
    -- 当s不等于1，也就是未遍历完时，会一直循环
    while s<>1 do
    -- 执行业务逻辑
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldOne', '扩展字段1', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldTwo', '扩展字段2', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldThree', '扩展字段3', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldFour', '扩展字段4', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldFive', '扩展字段5', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldSix', '扩展字段6', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldSeven', '扩展字段7', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldEight', '扩展字段8', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldNine', '扩展字段9', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldTen', '扩展字段10', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');
    INSERT INTO `dfs_material_type_config_field`(`material_type_id`, `material_type_name`, `field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (type_id, type_name, 'customFieldEleven', '扩展字段11', 'input', 0, 0, 0, NULL, '系统自动默认配置', '2022-10-13 19:09:12', '2022-12-06 09:19:24', 'admin', 'admin');

    -- 将游标中的值再赋值给变量，供下次循环使用
    fetch cur into type_id, type_name;
    -- 当s等于1时表明遍历以完成，退出循环
    end while;
    -- 关闭游标
    close cur;

END $$
DELIMITER ;
call proc_compatible_material_type_field();


-- 兼容标签打印模板历史数据
update dfs_bar_code_rule set content = REPLACE(content,'{生产工单号}',"\\$\\{workOrderNumber\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{生产订单号}',"\\$\\{productOrderNumber\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{采购单号}',"\\$\\{purchaseCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{采购单品码}',"\\$\\{purchaseSingleProductCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{生产批次号}',"\\$\\{productBatch\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{生产批号}',"\\$\\{productBatch\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{采购批次}',"\\$\\{purchaseBatch\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{生产成品码}',"\\$\\{finishedProductCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{订单流水码}',"\\$\\{orderProductCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{物料编码}',"\\$\\{materialCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{物料名称}',"\\$\\{materialName\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{物料规格}',"\\$\\{standard\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{供应商编码}',"\\$\\{supplier\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{供应商名称}',"\\$\\{supplierName\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{供应商物料编码}',"\\$\\{supplierMaterialCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{生产商}',"\\$\\{producer\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{批次数量}',"\\$\\{count\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{批次数量}',"\\$\\{batchRemark\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{批次备注}',"\\$\\{lineModelCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{制造单元类型编号}',"\\$\\{lineCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{制造单元编号}',"\\$\\{lineModelCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{生产流水号}',"\\$\\{productFlowCode\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{工单实际完成时间}',"\\$\\{workOrderActualFinishDate\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{厂区名称}',"\\$\\{areaName\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{客户名称}',"\\$\\{customerName\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{当前日期}',"\\$\\{currentDate\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{入库时间}',"\\$\\{inputTime\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{收货日期}',"\\$\\{receiptDate\\}");
update dfs_bar_code_rule set content = REPLACE(content,'{序号}',"\\$\\{serialNo\\}");

update dfs_common_config set operation_type = 'enableConfig',description = 'materialSku启用配置' where operation_object = 'materialSku';
delete from dfs_common_config where operation_object = 'defaultCraft' and operation_type = 'enableConfig';
INSERT INTO `dfs_common_config`(`id`, `operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES (null, 'defaultCraft', null, 'enableConfig', 'enable', 'false', 'defaultCraft启用配置');

update dfs_dict set unit = 'false' where type = 'materialType';

-- 销售订单 按模板导出权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('10001230', '导出_下载默认模板', 'sales.order:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, 1, '/order-model/salesOrder');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('10001240', '导出_上传下载自定义导出模板', 'sales.order:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, 1, '/order-model/salesOrder');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('10001250', '导出_导出excel', 'sales.order:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, 1, '/order-model/salesOrder');
call init_new_role_permission('10001230');
call init_new_role_permission('10001240');
call init_new_role_permission('10001250');
-- 生产订单 按模板导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('10003250', '导出_下载默认模板', 'product.order:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, 1, '/order-model/product-order');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('10003260', '导出_上传下载自定义导出模板', 'product.order:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, 1, '/order-model/product-order');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `is_enable`, `parent_path`)
VALUES ('10003270', '导出_导出excel', 'product.order:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, 1, '/order-model/product-order');
call init_new_role_permission('10003250');
call init_new_role_permission('10003260');
call init_new_role_permission('10003270');
-- 生产工单 按模板导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10005320', '导出_下载默认模板', 'production.workorder:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10005330', '导出_上传下载自定义导出模板', 'production.workorder:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10005340', '导出_导出excel', 'production.workorder:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
call init_new_role_permission('10005320');
call init_new_role_permission('10005330');
call init_new_role_permission('10005340');
-- 采购需求 按模板导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090204090', '导出_下载默认模板', 'purchasing.demand:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090204', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-demand', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090204100', '导出_上传下载自定义导出模板', 'purchasing.demand:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090204', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-demand', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090204110', '导出_导出excel', 'purchasing.demand:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090204', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-demand', 1);
call init_new_role_permission('1090204090');
call init_new_role_permission('1090204100');
call init_new_role_permission('1090204110');
-- 采购订单 按模板导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090205100', '导出_下载默认模板', 'purchasing.list:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090205110', '导出_上传下载自定义导出模板', 'purchasing.list:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090205120', '导出_导出excel', 'purchasing.list:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
call init_new_role_permission('1090205100');
call init_new_role_permission('1090205110');
call init_new_role_permission('1090205120');
