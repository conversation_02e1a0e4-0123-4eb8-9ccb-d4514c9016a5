-- DDL


-- DML
-- 生产工单报工记录相关字段配置
call proc_add_form_field("workOrderReport.list", "projectDefineName", "项目名称");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/workorder-model/production-records', 'workOrderReport.list', 'workOrderReport.list', 'projectDefineName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (null, 'approverName', '审批人', '{审批人}', 1);
INSERT INTO `dfs_notice_field_info`(`id`, `code`, `name`, `placeholder`, `is_sys_field`) VALUES (null, 'departmentName', '部门名称', '{部门名称}', 1);

