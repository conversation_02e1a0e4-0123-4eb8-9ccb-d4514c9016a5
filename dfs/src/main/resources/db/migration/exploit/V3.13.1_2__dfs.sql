-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================

-- 销售退货单表单配置
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'saleReturn', '销售退货单', 'saleReturn', '', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'saleReturnList', '销售退货单列表页', 'saleReturn.list.material', 'saleReturn', NULL, 'admin', 'admin', NOW(), NOW(), 'list', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'saleReturnEdit', '销售退货单编辑页', 'saleReturn.edit', 'saleReturn', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleReturnEditByCreate', '创建态', 'saleReturn.editByCreate', 'saleReturn.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-create', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleReturnEditByRelease', '生效态', 'saleReturn.editByRelease', 'saleReturn.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleReturnEditByFinish', '完成态', 'saleReturn.editByFinish', 'saleReturn.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleReturnEditByClosed', '关闭态', 'saleReturn.editByClosed', 'saleReturn.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleReturnEditByCancel', '取消态', 'saleReturn.editByCancel', 'saleReturn.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'saleReturnDetail', '销售退货单详情', 'saleReturn.detail', 'saleReturn', NULL, 'admin', 'admin', NOW(), NOW(), 'detail', 1);
-- 销售退货单
call proc_add_form_field("saleReturn.list.material", "saleReturnCode", "单据编码");
call proc_add_form_field("saleReturn.list.material", "relatedTypeName", "关联单据类型");
call proc_add_form_field("saleReturn.list.material", "relatedCode", "关联单据编码");
call proc_add_form_field("saleReturn.list.material", "stateName", "状态");
call proc_add_form_field("saleReturn.list.material", "orderTypeName", "单据类型");
call proc_add_form_field("saleReturn.list.material", "businessTypeName", "业务类型");
call proc_add_form_field("saleReturn.list.material", "materialCode", "物料编码");
call proc_add_form_field("saleReturn.list.material", "materialName", "物料名称");
call proc_add_form_field("saleReturn.list.material", "customerCode", "客户编码");
call proc_add_form_field("saleReturn.list.material", "customerName", "客户名称");
call proc_add_form_field("saleReturn.list.material", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("saleReturn.list.material", "customerMaterialName", "客户物料名称");
call proc_add_form_field("saleReturn.list.material", "customerSpecification", "客户物料规格");
call proc_add_form_field("saleReturn.list.material", "planAmount", "计划数量");
call proc_add_form_field("saleReturn.list.material", "actualAmount", "完成数量");
call proc_add_form_field("saleReturn.list.material", "createTime", "创建时间");
call proc_add_form_field("saleReturn.list.material", "createByName", "创建人");
call proc_add_form_field("saleReturn.list.material", "remark", "备注");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldOne", "销售退货单扩展字段1");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldTwo", "销售退货单扩展字段2");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldThree", "销售退货单扩展字段3");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldFour", "销售退货单扩展字段4");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldFive", "销售退货单扩展字段5");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldSix", "销售退货单扩展字段6");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldSeven", "销售退货单扩展字段7");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldEight", "销售退货单扩展字段8");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldNine", "销售退货单扩展字段9");
call proc_add_form_field("saleReturn.list.material", "saleReturnExtendFieldTen", "销售退货单扩展字段10");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldOne", "销售退货单物料扩展字段1");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldTwo", "销售退货单物料扩展字段2");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldThree", "销售退货单物料扩展字段3");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldFour", "销售退货单物料扩展字段4");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldFive", "销售退货单物料扩展字段5");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldSix", "销售退货单物料扩展字段6");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldSeven", "销售退货单物料扩展字段7");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldEight", "销售退货单物料扩展字段8");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldNine", "销售退货单物料扩展字段9");
call proc_add_form_field("saleReturn.list.material", "saleReturnMaterialExtendFieldTen", "销售退货单物料扩展字段10");


call proc_add_form_field("saleReturn.edit", "saleReturnCode", "单据编码");
call proc_add_form_field("saleReturn.edit", "relatedType", "关联单据类型");
call proc_add_form_field("saleReturn.edit", "relatedCode", "关联单据");
call proc_add_form_field("saleReturn.edit", "state", "状态");
call proc_add_form_field("saleReturn.edit", "orderType", "单据类型");
call proc_add_form_field("saleReturn.edit", "businessType", "业务类型");
call proc_add_form_field("saleReturn.edit", "customerCode", "客户编码");
call proc_add_form_field("saleReturn.edit", "customerName", "客户名称");
call proc_add_form_field("saleReturn.edit", "remark", "备注");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldOne", "销售退货单扩展字段1");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldTwo", "销售退货单扩展字段2");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldThree", "销售退货单扩展字段3");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldFour", "销售退货单扩展字段4");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldFive", "销售退货单扩展字段5");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldSix", "销售退货单扩展字段6");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldSeven", "销售退货单扩展字段7");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldEight", "销售退货单扩展字段8");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldNine", "销售退货单扩展字段9");
call proc_add_form_field("saleReturn.edit", "saleReturnExtendFieldTen", "销售退货单扩展字段10");
call proc_add_form_field("saleReturn.edit", "materialCode", "物料编码");
call proc_add_form_field("saleReturn.edit", "materialName", "物料名称");
call proc_add_form_field("saleReturn.edit", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("saleReturn.edit", "customerMaterialName", "客户物料名称");
call proc_add_form_field("saleReturn.edit", "customerSpecification", "客户物料规格");
call proc_add_form_field("saleReturn.edit", "isGift", "是否赠品");
call proc_add_form_field("saleReturn.edit", "planAmount", "计划数量");
call proc_add_form_field("saleReturn.edit", "returnType", "退货类型");
call proc_add_form_field("saleReturn.edit", "actualAmount", "实际数量");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldOne", "销售退货单物料扩展字段1");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldTwo", "销售退货单物料扩展字段2");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldThree", "销售退货单物料扩展字段3");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldFour", "销售退货单物料扩展字段4");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldFive", "销售退货单物料扩展字段5");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldSix", "销售退货单物料扩展字段6");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldSeven", "销售退货单物料扩展字段7");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldEight", "销售退货单物料扩展字段8");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldNine", "销售退货单物料扩展字段9");
call proc_add_form_field("saleReturn.edit", "saleReturnMaterialExtendFieldTen", "销售退货单物料扩展字段10");


call proc_add_form_field("saleReturn.detail", "saleReturnCode", "单据编码");
call proc_add_form_field("saleReturn.detail", "relatedType", "关联单据类型");
call proc_add_form_field("saleReturn.detail", "relatedCode", "关联单据");
call proc_add_form_field("saleReturn.detail", "state", "状态");
call proc_add_form_field("saleReturn.detail", "orderType", "单据类型");
call proc_add_form_field("saleReturn.detail", "businessType", "业务类型");
call proc_add_form_field("saleReturn.detail", "customerCode", "客户编码");
call proc_add_form_field("saleReturn.detail", "customerName", "客户名称");
call proc_add_form_field("saleReturn.detail", "remark", "备注");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldOne", "销售退货单扩展字段1");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldTwo", "销售退货单扩展字段2");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldThree", "销售退货单扩展字段3");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldFour", "销售退货单扩展字段4");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldFive", "销售退货单扩展字段5");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldSix", "销售退货单扩展字段6");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldSeven", "销售退货单扩展字段7");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldEight", "销售退货单扩展字段8");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldNine", "销售退货单扩展字段9");
call proc_add_form_field("saleReturn.detail", "saleReturnExtendFieldTen", "销售退货单扩展字段10");
call proc_add_form_field("saleReturn.detail", "materialCode", "物料编码");
call proc_add_form_field("saleReturn.detail", "materialName", "物料名称");
call proc_add_form_field("saleReturn.detail", "customerMaterialCode", "客户物料编码");
call proc_add_form_field("saleReturn.detail", "customerMaterialName", "客户物料名称");
call proc_add_form_field("saleReturn.detail", "customerSpecification", "客户物料规格");
call proc_add_form_field("saleReturn.detail", "isGift", "是否赠品");
call proc_add_form_field("saleReturn.detail", "planAmount", "计划数量");
call proc_add_form_field("saleReturn.detail", "returnType", "退货类型");
call proc_add_form_field("saleReturn.detail", "actualAmount", "实际数量");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldOne", "销售退货单物料扩展字段1");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldTwo", "销售退货单物料扩展字段2");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldThree", "销售退货单物料扩展字段3");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldFour", "销售退货单物料扩展字段4");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldFive", "销售退货单物料扩展字段5");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldSix", "销售退货单物料扩展字段6");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldSeven", "销售退货单物料扩展字段7");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldEight", "销售退货单物料扩展字段8");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldNine", "销售退货单物料扩展字段9");
call proc_add_form_field("saleReturn.detail", "saleReturnMaterialExtendFieldTen", "销售退货单物料扩展字段10");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'relatedTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'relatedCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'stateName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'materialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'materialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'customerCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'planAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'actualAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'createByName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'remark', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldTwo',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldSeven',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldNine',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnExtendFieldTen',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldOne',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldTwo',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldThree',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldFour',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldFive',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldSix',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldSeven',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldEight',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldNine',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'saleReturnMaterialExtendFieldTen',  0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnCode', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'relatedType', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'relatedCode', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'state', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'orderType', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldFour',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'materialCode', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'materialName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'customerMaterialCode', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'customerMaterialName', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'customerSpecification', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'isGift', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'planAmount', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'returnType', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'actualAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldThree',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSeven',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCreate', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'relatedType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'relatedCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'state', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'customerCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'customerName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldFour',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldSix',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldSeven',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'materialCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'materialName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'isGift', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'planAmount', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'returnType', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'actualAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldEight',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByRelease', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);


INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'relatedType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'relatedCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'state', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'customerCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'customerName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'materialCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'materialName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'isGift', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'planAmount', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'returnType', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'actualAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByFinish', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);


INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'relatedType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'relatedCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'state', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'customerCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'customerName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'materialCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'materialName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'isGift', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'planAmount', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'returnType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'actualAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldOne',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByClosed', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'relatedType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'relatedCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'state', 1, 1, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'orderType', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'customerCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'customerName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'materialCode', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'materialName', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'isGift', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'planAmount', 1, 1, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'returnType', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'actualAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.editByCancel', 'saleReturn.edit', 'saleReturnMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'relatedType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'relatedCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'state', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'orderType', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'customerCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'remark', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldTwo',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldSeven',0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'materialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'materialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'customerMaterialCode', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'customerMaterialName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'customerSpecification', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'planAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'returnType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'actualAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.detail', 'saleReturn.detail', 'saleReturnMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

-- 生产工单用料清单新增扩展字段
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldOneName", "生产工单用料清单扩展字段1");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldTwoName", "生产工单用料清单扩展字段2");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldThreeName", "生产工单用料清单扩展字段3");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldFourName", "生产工单用料清单扩展字段4");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldFiveName", "生产工单用料清单扩展字段5");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldSixName", "生产工单用料清单扩展字段6");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldSevenName", "生产工单用料清单扩展字段7");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldEightName", "生产工单用料清单扩展字段8");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldNineName", "生产工单用料清单扩展字段9");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListExtendFieldTenName", "生产工单用料清单扩展字段10");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldOneName", "生产工单用料清单物料扩展字段1");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldTwoName", "生产工单用料清单物料扩展字段2");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldThreeName", "生产工单用料清单物料扩展字段3");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldFourName", "生产工单用料清单物料扩展字段4");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldFiveName", "生产工单用料清单物料扩展字段5");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldSixName", "生产工单用料清单物料扩展字段6");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldSevenName", "生产工单用料清单物料扩展字段7");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldEightName", "生产工单用料清单物料扩展字段8");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldNineName", "生产工单用料清单物料扩展字段9");
call proc_add_form_field("workOrderMaterialList.list.material", "materialListMaterialExtendFieldTenName", "生产工单用料清单物料扩展字段10");

call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldOneName", "生产工单用料清单扩展字段1");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldTwoName", "生产工单用料清单扩展字段2");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldThreeName", "生产工单用料清单扩展字段3");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldFourName", "生产工单用料清单扩展字段4");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldFiveName", "生产工单用料清单扩展字段5");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldSixName", "生产工单用料清单扩展字段6");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldSevenName", "生产工单用料清单扩展字段7");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldEightName", "生产工单用料清单扩展字段8");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldNineName", "生产工单用料清单扩展字段9");
call proc_add_form_field("workOrderMaterialList.list.order", "materialListExtendFieldTenName", "生产工单用料清单扩展字段10");

call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldOne", "生产工单用料清单扩展字段1");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldTwo", "生产工单用料清单扩展字段2");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldThree", "生产工单用料清单扩展字段3");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldFour", "生产工单用料清单扩展字段4");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldFive", "生产工单用料清单扩展字段5");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldSix", "生产工单用料清单扩展字段6");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldSeven", "生产工单用料清单扩展字段7");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldEight", "生产工单用料清单扩展字段8");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldNine", "生产工单用料清单扩展字段9");
call proc_add_form_field("workOrderMaterialList.edit", "materialListExtendFieldTen", "生产工单用料清单扩展字段10");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldOne", "生产工单用料清单物料扩展字段1");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldTwo", "生产工单用料清单物料扩展字段2");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldThree", "生产工单用料清单物料扩展字段3");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldFour", "生产工单用料清单物料扩展字段4");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldFive", "生产工单用料清单物料扩展字段5");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldSix", "生产工单用料清单物料扩展字段6");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldSeven", "生产工单用料清单物料扩展字段7");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldEight", "生产工单用料清单物料扩展字段8");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldNine", "生产工单用料清单物料扩展字段9");
call proc_add_form_field("workOrderMaterialList.edit", "materialListMaterialExtendFieldTen", "生产工单用料清单物料扩展字段10");

call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldOne", "生产工单用料清单扩展字段1");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldTwo", "生产工单用料清单扩展字段2");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldThree", "生产工单用料清单扩展字段3");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldFour", "生产工单用料清单扩展字段4");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldFive", "生产工单用料清单扩展字段5");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldSix", "生产工单用料清单扩展字段6");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldSeven", "生产工单用料清单扩展字段7");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldEight", "生产工单用料清单扩展字段8");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldNine", "生产工单用料清单扩展字段9");
call proc_add_form_field("workOrderMaterialList.detail", "materialListExtendFieldTen", "生产工单用料清单扩展字段10");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldOne", "生产工单用料清单物料扩展字段1");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldTwo", "生产工单用料清单物料扩展字段2");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldThree", "生产工单用料清单物料扩展字段3");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldFour", "生产工单用料清单物料扩展字段4");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldFive", "生产工单用料清单物料扩展字段5");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldSix", "生产工单用料清单物料扩展字段6");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldSeven", "生产工单用料清单物料扩展字段7");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldEight", "生产工单用料清单物料扩展字段8");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldNine", "生产工单用料清单物料扩展字段9");
call proc_add_form_field("workOrderMaterialList.detail", "materialListMaterialExtendFieldTen", "生产工单用料清单物料扩展字段10");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldOneName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldTwoName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldThreeName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldFourName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldFiveName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldSixName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldSevenName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldEightName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldNineName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListExtendFieldTenName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldOneName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldTwoName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldThreeName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldFourName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldFiveName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldSixName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldSevenName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldEightName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldNineName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListExtendFieldTenName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldOneName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldTwoName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldThreeName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldFourName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldFiveName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldSixName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldSevenName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldEightName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldNineName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListMaterialExtendFieldTenName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);



-- 供应类别
-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'category', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
call proc_add_form_field('supplier.list', 'category', '供应类别');
-- 详情
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'category', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
call proc_add_form_field('supplier.detail', 'category', '供应类别');
-- 编辑 - 创建
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'category', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
call proc_add_form_field('supplier.edit.byCreate', 'category', '供应类别');
-- 编辑 - 生效
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'category', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
call proc_add_form_field('supplier.edit.byRelease', 'category', '供应类别');
-- 编辑 - 停用
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'category', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
call proc_add_form_field('supplier.edit.byDisable', 'category', '供应类别');

-- 采购需求单增加业务类型表单配置
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 1, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseRequestOrder.editByCreate' AND `field_code` = 'orderType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseRequestOrder.editByRelease' AND `field_code` = 'orderType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseRequestOrder.editByFinish' AND `field_code` = 'orderType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseRequestOrder.editByClosed' AND `field_code` = 'orderType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseRequestOrder.editByCancel' AND `field_code` = 'orderType';
call proc_add_form_field("purchaseRequestOrder.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCreate', "purchaseRequestOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByRelease', "purchaseRequestOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByFinish', "purchaseRequestOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByClosed', "purchaseRequestOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.editByCancel', "purchaseRequestOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseRequestOrder.list", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.list', 'purchaseRequestOrder.list', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseRequestOrder.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-demand', 'purchaseRequestOrder.detail', 'purchaseRequestOrder.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购订单增加业务类型字段
-- 删除之前添加的多余字段
delete from dfs_form_field_config where full_path_code in ("purchaseOrder.list.order", "purchaseOrder.list.material", "purchaseOrder.detail")
                                    and field_code in ("orderTypeName");
delete from dfs_form_field_rule_config where full_path_code in ("purchaseOrder.list.order", "purchaseOrder.list.material", "purchaseOrder.detail")
                                         and field_code in ("orderTypeName");
delete from dfs_form_field_config where full_path_code in ("purchaseOrder.edit")
                                    and field_code in ("orderType");
delete from dfs_form_field_rule_config where field_name_full_path_code in ("purchaseOrder.edit")
                                         and field_code in ("orderType");
-- 将原来“采购类型”改为“单据类型”
update dfs_form_field_config set field_name = "单据类型" where field_code = "purchaseType" and type_code = "sysField" and full_path_code in ("purchaseOrder.list.order", "purchaseOrder.list.material", "purchaseOrder.detail", "purchaseOrder.edit");
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 1, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseOrder.editByCreate' AND `field_code` = 'purchaseType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseOrder.editByRelease' AND `field_code` = 'purchaseType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseOrder.editByFinish' AND `field_code` = 'purchaseType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseOrder.editByClosed' AND `field_code` = 'purchaseType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseOrder.editByCancel' AND `field_code` = 'purchaseType';
call proc_add_form_field("purchaseOrder.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByCreate', "purchaseOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByRelease', "purchaseOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByFinish', "purchaseOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByClosed', "purchaseOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.editByCancel', "purchaseOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseOrder.list.order", "businessTypeName", "业务类型");
call proc_add_form_field("purchaseOrder.list.material", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.list.order', 'purchaseOrder.list.order', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.list.material', 'purchaseOrder.list.material', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseOrder.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.detail', 'purchaseOrder.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购收料单增加业务类型字段
delete from dfs_form_field_config where full_path_code in ("purchaseReceiptOrder.list.order", "purchaseReceiptOrder.list.material", "purchaseReceiptOrder.detail")
                                    and field_code in ("orderTypeName");
delete from dfs_form_field_rule_config where full_path_code in ("purchaseReceiptOrder.list.order", "purchaseReceiptOrder.list.material", "purchaseReceiptOrder.detail")
                                         and field_code in ("orderTypeName");
delete from dfs_form_field_config where full_path_code in ("purchaseReceiptOrder.edit")
                                    and field_code in ("orderType");
delete from dfs_form_field_rule_config where field_name_full_path_code in ("purchaseReceiptOrder.edit")
                                         and field_code in ("orderType");
-- 将原来“收料类型”改为“单据类型”
update dfs_form_field_config set field_name = "单据类型" where field_code = "receiptType" and type_code = "sysField" and full_path_code in ("purchaseReceiptOrder.detail", "purchaseReceiptOrder.edit");
update dfs_form_field_config set field_name = "单据类型" where field_code = "receiptTypeName" and type_code = "sysField" and full_path_code in ("purchaseReceiptOrder.list.order", "purchaseReceiptOrder.list.material");
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 1, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReceiptOrder.editByCreate' AND `field_code` = 'receiptType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReceiptOrder.editByRelease' AND `field_code` = 'receiptType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReceiptOrder.editByFinish' AND `field_code` = 'receiptType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReceiptOrder.editByClosed' AND `field_code` = 'receiptType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReceiptOrder.editByCancel' AND `field_code` = 'receiptType';
call proc_add_form_field("purchaseReceiptOrder.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', "purchaseReceiptOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', "purchaseReceiptOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', "purchaseReceiptOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', "purchaseReceiptOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', "purchaseReceiptOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseReceiptOrder.list.order", "businessTypeName", "业务类型");
call proc_add_form_field("purchaseReceiptOrder.list.material", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.list.order', 'purchaseReceiptOrder.list.order', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.list.material', 'purchaseReceiptOrder.list.material', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseReceiptOrder.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 采购退料单增加业务类型字段
-- 将原来“退料类型”改为“单据类型”
update dfs_form_field_config set field_name = "单据类型" where field_code = "returnType" and type_code = "sysField" and full_path_code in ("purchaseReturn.detail", "purchaseReturn.edit");
update dfs_form_field_config set field_name = "单据类型" where field_code = "returnTypeName" and type_code = "sysField" and full_path_code in ("purchaseReturn.list.material");
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 1, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReturn.editByCreate' AND `field_code` = 'returnType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReturn.editByRelease' AND `field_code` = 'returnType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReturn.editByFinish' AND `field_code` = 'returnType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReturn.editByClosed' AND `field_code` = 'returnType';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1,`is_need` = 1, `need_gray` = 1, `is_edit` = 0, `edit_gray` = 1, input_gray = 1, value_gray=1, input_type=null WHERE `full_path_code` = 'purchaseReturn.editByCancel' AND `field_code` = 'returnType';
call proc_add_form_field("purchaseReturn.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCreate', "purchaseReturn.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByRelease', "purchaseReturn.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByFinish', "purchaseReturn.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByClosed', "purchaseReturn.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCancel', "purchaseReturn.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseReturn.list.material", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.list.material', 'purchaseReturn.list.material', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("purchaseReturn.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 委外订单单据类型、业务类型字段
call proc_add_form_field("subcontractOrder.edit", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', "subcontractOrder.edit", "orderType", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', "subcontractOrder.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', "subcontractOrder.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', "subcontractOrder.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', "subcontractOrder.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrder.list.order", "orderTypeName", "单据类型");
call proc_add_form_field("subcontractOrder.list.material", "orderTypeName", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', 'subcontractOrder.list.order', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', 'subcontractOrder.list.material', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrder.detail", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrder.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', "subcontractOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', "subcontractOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', "subcontractOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', "subcontractOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', "subcontractOrder.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrder.list.order", "businessTypeName", "业务类型");
call proc_add_form_field("subcontractOrder.list.material", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', 'subcontractOrder.list.order', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', 'subcontractOrder.list.material', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractOrder.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 委外订单收料单单据类型、业务类型字段
call proc_add_form_field("subcontractReceipt.edit", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderType", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderType", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractReceipt.list", "orderTypeName", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', 'subcontractReceipt.list', 'orderTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractReceipt.detail", "orderType", "单据类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'orderType', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractReceipt.edit", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "businessType", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractReceipt.list", "businessTypeName", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', 'subcontractReceipt.list', 'businessTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
call proc_add_form_field("subcontractReceipt.detail", "businessType", "业务类型");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`)VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', 'subcontractReceipt.detail', 'businessType', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

