-- DDL
-- 报工新增人员数量
call proc_add_column(
        'dfs_report_line',
        'crew_size',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `crew_size` int(11) NULL COMMENT ''人员数量''');
-- 报工新增工作中心类型、关联资源-班组、产线code name字段
call proc_add_column(
        'dfs_report_line',
        'resource_team_code',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_team_code` varchar(50) NULL COMMENT ''关联资源-班组code''');

call proc_add_column(
        'dfs_report_line',
        'resource_line_code',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_line_code` varchar(50) NULL COMMENT ''关联资源-产线code''');

call proc_add_column(
        'dfs_report_line',
        'work_center_type_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `work_center_type_name` varchar(100) NULL COMMENT ''基本生产单元类型名称''');

call proc_add_column(
        'dfs_report_line',
        'resource_type_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_type_name` varchar(100) NULL COMMENT ''关联资源类型名称''');

call proc_add_column(
        'dfs_report_line',
        'resource_device_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_device_name` varchar(100) NULL COMMENT ''关联资源-设备名称''');

call proc_add_column(
        'dfs_report_line',
        'resource_line_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_line_name` varchar(100) NULL COMMENT ''关联资源-产线名称''');

call proc_add_column(
        'dfs_report_line',
        'resource_team_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_team_name` varchar(100) NULL COMMENT ''关联资源-班组名称''');

call proc_add_column(
        'dfs_report_line',
        'work_center_name',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `work_center_name` varchar(100) NULL COMMENT ''工作中心名称''');

call proc_add_column(
        'dfs_report_line',
        'work_center_id',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `work_center_id` int(11) NULL COMMENT ''工作中心id''');

-- 工作中心表新增是否首检防漏检字段
call proc_add_column(
        'dfs_work_center',
        'first_inspection',
        'ALTER TABLE `dfs_work_center` ADD COLUMN  `first_inspection`  tinyint(1) DEFAULT 0 COMMENT ''是否首检防漏检''');
-- 扫码记录id加索引
call proc_add_column_index('dfs_record_work_order_unqualified','code_record_id','code_record_id');
call proc_add_column_index('dfs_maintain_record','code_record_id','code_record_id');

call proc_add_column(
        'dfs_bom_raw_material',
        'extend_one',
        'ALTER TABLE `dfs_bom_raw_material` ADD COLUMN `extend_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''扩展字段1''');

call proc_add_column(
        'dfs_replace_bom_material',
        'extend_one',
        'ALTER TABLE `dfs_replace_bom_material` ADD COLUMN `extend_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT ''BOM子物料拓展字段''');

-- 删除无用指标表
DROP TABLE IF EXISTS dfs_record_facility_unqualified_order;
DROP TABLE IF EXISTS dfs_record_line_unqualified_order;
DROP TABLE IF EXISTS dfs_record_facility_unqualified;
DROP TABLE IF EXISTS dfs_record_facility_performance;
DROP TABLE IF EXISTS dfs_record_facility_efficiency;
DROP TABLE IF EXISTS dfs_record_facility_day_union;
DROP TABLE IF EXISTS dfs_record_line_count_order;
DROP TABLE IF EXISTS dfs_record_facility_count_order;
DROP TABLE IF EXISTS dfs_record_line_avg_time_order;
DROP TABLE IF EXISTS dfs_record_line_unqualified;
DROP TABLE IF EXISTS dfs_record_facility_yield;
DROP TABLE IF EXISTS dfs_record_facility_state;
DROP TABLE IF EXISTS dfs_record_facility_pause;
DROP TABLE IF EXISTS dfs_record_line_yield_order;
DROP TABLE IF EXISTS dfs_record_facility_count;
DROP TABLE IF EXISTS dfs_record_line_finish_rate;
DROP TABLE IF EXISTS dfs_record_facility_stop;
DROP TABLE IF EXISTS dfs_record_facility_run;
DROP TABLE IF EXISTS dfs_record_facility_oee;
DROP TABLE IF EXISTS dfs_record_grid_state;
DROP TABLE IF EXISTS dfs_record_area_state;
DROP TABLE IF EXISTS dfs_record_line_state;
DROP TABLE IF EXISTS dfs_record_line_pause;
DROP TABLE IF EXISTS dfs_record_line_count;
DROP TABLE IF EXISTS dfs_record_line_stop;
DROP TABLE IF EXISTS dfs_record_line_run;
DROP TABLE IF EXISTS dfs_record_line_order;
DROP TABLE IF EXISTS dfs_output_order;

-- websocket历史表换联合索引
call proc_drop_column_index('dfs_web_socket_history','topic','topic');
call proc_add_column_index('dfs_web_socket_history','topic,tag','topic');
-- DML

-- 更新路由
UPDATE `sys_route` SET `path` = '/smt-management/bosaManagement' WHERE `path` = '/smtManagement/bosaManagement';
UPDATE `sys_route` SET `path` = '/smt-management/logManagement' WHERE `path` = '/smtManagement/logManagement';
UPDATE `sys_route` SET `path` = '/smt-management/feederManage' WHERE `path` = '/smtManagement/feederManage';
UPDATE `sys_route` SET `path` = '/smt-management/ink-painting-recipients-record' WHERE `path` = '/smt-Management/ink-painting-recipients-record';
UPDATE `sys_route` SET `path` = '/smt-management/aging-management' WHERE `path` = '/smt-Management/aging-management';


-- 更新路由对应的权限
UPDATE `sys_permissions` SET `path` = '/smt-management/bosaManagement' WHERE `path` = '/smtManagement/bosaManagement';
UPDATE `sys_permissions` SET `path` = '/smt-management/logManagement' WHERE `path` = '/smtManagement/logManagement';
UPDATE `sys_permissions` SET `path` = '/smt-management/feederManage' WHERE `path` = '/smtManagement/feederManage';
UPDATE `sys_permissions` SET `path` = '/smt-management/ink-painting-recipients-record' WHERE `path` = '/smt-Management/ink-painting-recipients-record';
UPDATE `sys_permissions` SET `path` = '/smt-management/aging-management' WHERE `path` = '/smt-Management/aging-management';

-- 更新工单报工校验配置名称
UPDATE `dfs_business_config` SET  `name` = '生产工单报工校验配置' WHERE `full_path_code` = 'production.workOrderVerificationReportConfig';
-- 报工完成是否可修改配置 非必填字段是否可见配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('reportCompletedEditConfiguration', '报工记录修改配置', 'production.workOrderVerificationReportConfig.reportCompletedEditConfiguration', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'admin', '2023-06-14 16:42:52', '2023-06-14 16:43:43');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('shownOptionalFieldsConfiguration', '订单报工非必填字段配置', 'production.workOrderVerificationReportConfig.shownOptionalFieldsConfiguration', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'admin', '2023-06-14 16:42:52', '2023-06-14 16:43:38');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('enable', '完成状态是否支持修改记录', 'production.workOrderVerificationReportConfig.reportCompletedEditConfiguration.enable', 'production.workOrderVerificationReportConfig.reportCompletedEditConfiguration', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('codeType', '显示字段', 'production.workOrderVerificationReportConfig.shownOptionalFieldsConfiguration.codeType', 'production.workOrderVerificationReportConfig.shownOptionalFieldsConfiguration', 'select-multiple', 'api', '/api/lines/optional/fields', 'get', NULL, 'code,name', '["resourceType","effectiveHours","vehicleCode","shiftType","batch","operator","unqualified","crewSize"]', '["resourceType","effectiveHours","vehicleCode","shiftType","batch","operator","unqualified","crewSize"]');

-- 报工物料拓展字段是否展示配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('shownMaterialFieldsConfiguration', '订单报工物料字段展示配置', 'production.workOrderVerificationReportConfig.shownMaterialFieldsConfiguration', 'production.workOrderVerificationReportConfig', NULL, 'yelinkoncall', 'admin', '2023-06-14 16:42:52', '2023-06-14 16:43:38');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('codeType', '展示字段', 'production.workOrderVerificationReportConfig.shownMaterialFieldsConfiguration.codeType', 'production.workOrderVerificationReportConfig.shownMaterialFieldsConfiguration', 'select-multiple', 'api', '/api/material/filed/config/list', 'get', NULL, 'fieldCode,fieldName', '["remark","drawingNumber","materialPrice","loseRate","rawMaterial","nameEnglish","minimumProductionLot","factoryModel","customFieldOne","customFieldTwo","customFieldThree","customFieldFour","customFieldFive","customFieldSix","customFieldSeven","customFieldEight","customFieldNine","customFieldTen","customFieldEleven"]', '["standard","remark","drawingNumber","materialPrice","loseRate","rawMaterial","nameEnglish","minimumProductionLot","factoryModel","customFieldOne","customFieldTwo","customFieldThree","customFieldFour","customFieldFive","customFieldSix","customFieldSeven","customFieldEight","customFieldNine","customFieldTen","customFieldEleven"]');

-- 工单报工修改按钮权限
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.workOrder.report.combutton16', '报工记录-修改', 'report:record:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.workOrder.report.com', 2, 0, 1, NULL, 1);

-- 物料批量编辑按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10801180', '批量编辑', 'material:multipleEdit', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10801', 2, 1, 0, '/product-management/supplies', 1);

--  不良业务配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('quality', '质量模块', 'quality', '', NULL);
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('defectConfig', '不良配置', 'quality.defectConfig', 'quality', NULL);
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('qualityInspectionConfig', '质检配置', 'quality.qualityInspectionConfig', 'quality', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('workOrderUnqualifiedSource', '工单不良数来源(多选)', 'quality.defectConfig.workOrderUnqualifiedSource', 'quality.defectConfig', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":1,\"label\":\"工单报工\"},{\"value\":2,\"label\":\"工单质检\"}]', '[1,2]');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('originalOrderStates', '其他可质检的工单状态(多选)', 'quality.qualityInspectionConfig.originalOrderStates', 'quality.qualityInspectionConfig', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,4,5,6]', '[5,4]');

--  防跳站业务配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('avoidSkipSiteAlarm', '防跳站告警', 'applet.tipsConfig.avoidSkipSiteAlarm', 'applet.tipsConfig', NULL);
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('tipsConfig', '提示配置', 'applet.tipsConfig', 'applet', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('enableAutoCloseAlarm', '告警提醒(单选)', 'applet.tipsConfig.avoidSkipSiteAlarm.enableAutoCloseAlarm', 'applet.tipsConfig.avoidSkipSiteAlarm', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"自动告警\"},{\"value\":false,\"label\":\"手动告警\"}]', 'true');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('appTypes', '小程序类型(多选)', 'applet.tipsConfig.avoidSkipSiteAlarm.appTypes', 'applet.tipsConfig.avoidSkipSiteAlarm', 'select-multiple', 'api', '/api/location/machine/enum', 'get', NULL, 'code,name', '[\"ordinaryStationMachine\"]', '[\"ordinaryStationMachine\"]');

-- 工艺新增批量编辑按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10803390', '批量编辑', 'technology:batchUpdate', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1);
call init_new_role_permission('10803390');
-- 新增调用qms的openApi接口配置
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('qms', 'inspectionSheet', 'getList', '通过条件查询送检单信息', 'POST', '/qms/v1/open/inspection/sheets/page');

-- 工序导出按钮
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804160', '工序导出', 'procedure:export', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
call init_new_role_permission('10804160');


-- 小程序打卡
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.general.machine.button3', '打卡', 'ordinary.station.machine:clickIn', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.general.machine', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.maintenance.machine.button1', '打卡', 'maintenance.station.machine:clickIn', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.maintenance.machine', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.packaging.machine.button2', '打卡', 'packaging.station.machine:clickIn', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.packaging.machine', 2, 0, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.qualityInspection.machine.button1', '打卡', 'qualityInspection.station.machine:clickIn', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.qualityInspection.machine', 2, 0, 1, NULL, 1);

-- 销售订单下推配置，新增是否过滤数量为0的单据
INSERT INTO `dfs_business_config_value` VALUES (null, 'isFilter', '是否过滤生产订单数量0单据', 'saleOrder.pushDownConfig.productOrder.bom.isFilter', 'saleOrder.pushDownConfig.productOrder.bom', 'select', 'table', null,null, NULL, null, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'isFilter', '是否过滤生产订单数量0单据', 'saleOrder.pushDownConfig.workOrder.craftRoute.isFilter', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'table', null,null, NULL, null, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'isFilterWorkOrder', '是否过滤生产工单数量0单据', 'saleOrder.pushDownConfig.workOrder.craftRoute.isFilterWorkOrder', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'table', null,null, NULL, null, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);


-- web端按钮权限补充

-- 销售订单
UPDATE `sys_permissions` SET  `path` = 'product.order:detail' WHERE `path` = 'product.order:select';

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001260', '附件上传', 'sales.order:appendixUpload', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102230', '导出_下载默认模板', 'sales.order:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102240', '导出_上传下载自定义导出模板', 'sales.order:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102250', '导出_导出excel', 'sales.order:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
-- 生产订单
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003290', '下载/打印单据(下载默认打印模板)', 'product.order:downPrintReceipt:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003300', '下载/打印单据(上传/下载自定义打印模板)', 'product.order:downPrintReceipt:downUploadPrintTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003310', '下载/打印单据(下载/打印pdf)', 'product.order:downPrintReceipt:downPrintPdf', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003320', '下载/打印单据(导出Excel)', 'product.order:downPrintReceipt:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003330', '导出列表', 'product.order:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003340', '导入管理', 'product.order:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003350', '订单流水码新增', 'product.order:orderPipelineCode:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003360', '订单流水码导出列表', 'product.order:orderPipelineCode:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003370', '增加物料', 'product.order:addMaterial', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003380', '附件上传', 'product.order:appendixUpload', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003390', '下载/打印单据', 'product.order:downPrintReceipt', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003400', '打印单据', 'product.order:printReceipt', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
-- 辅助计划
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10002110', '车间计划汇总', 'auxiliary.plan:gridPlanSum', NULL, NULL, NULL, NULL, '2023-01-31 07:13:06', 'enable', 'GET', '10002', 2, 1, 0, '/order-model/auxiliary_plan', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10002120', '拆解算法配置', 'auxiliary.plan:dismantlingAlgorithmConfig', NULL, NULL, NULL, NULL, '2023-01-31 07:13:06', 'enable', 'GET', '10002', 2, 1, 0, '/order-model/auxiliary_plan', 1);
-- 工单调度
UPDATE `sys_permissions` SET  `path` = 'workorder.scheduling:edit' WHERE `path` = 'workorder.scheduling:update';
UPDATE `sys_permissions` SET  `path` = 'workorder.scheduling:issue' WHERE `path` = 'workorder.scheduling:state';
UPDATE `sys_permissions` SET  `path` = 'workorder.scheduling:seeDetail' WHERE `path` = 'workorder.scheduling:select';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10004100', '导入生产订单', 'workorder.scheduling:importProductOrder', NULL, NULL, NULL, NULL, '2023-01-31 07:15:54', 'enable', 'GET', '10004', 2, 1, 0, '/order-model/workorder-scheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10004110', '导出列表', 'workorder.scheduling:exportList', NULL, NULL, NULL, NULL, '2023-01-31 07:15:54', 'enable', 'GET', '10004', 2, 1, 0, '/order-model/workorder-scheduling', 1);
-- 自动排程
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007010', '导入生产工单', 'automatic.scheduling:importWorkOrder', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007020', '物料齐套性检查', 'automatic.scheduling:checkMaterial', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007030', '自动排程', 'automatic.scheduling:autoScheduling', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007040', '确认排程结果', 'automatic.scheduling:confirmSchedulingResult', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007050', '导出列表', 'automatic.scheduling:importList', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007060', '算法配置', 'automatic.scheduling:algorithmConfig', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007070', '产能锁定', 'automatic.scheduling:capacityLocking', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007080', '单据锁定', 'automatic.scheduling:receiptLocking', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007090', '取消锁定', 'automatic.scheduling:CancelLocking', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007100', '编辑', 'automatic.scheduling:edit', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10007110', '齐套详情', 'automatic.scheduling:seeDetail', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10007', 2, 1, 0, '/order-model/automaticscheduling', 1);
-- 工单追溯
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10202010', '下载/打印单据', 'workorder.trace:downPrintExceipt', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10202', 2, 1, 0, '/trace/work-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10202020', '下载/打印单据(下载默认打印模板)', 'workorder.trace:downPrintExceipt:downDefaultTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10202', 2, 1, 0, '/trace/work-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10202030', '下载/打印单据(上传/下载自定义打印模板)', 'workorder.trace:downPrintExceipt:uploadDownPrintTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10202', 2, 1, 0, '/trace/work-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10202040', '下载/打印单据(下载/打印pdf)', 'workorder.trace:downPrintExceipt:downPrintPdf', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10202', 2, 1, 0, '/trace/work-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10202050', '下载/打印单据(导出Excel)', 'workorder.trace:downPrintExceipt:exportExcel', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10202', 2, 1, 0, '/trace/work-order', 1);
-- 单品追溯
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10205050', '追溯报表下载/打印单据', 'single.trace:traceStatement:downPrintExceipt', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10205060', '追溯报表下载/打印单据(下载默认打印模板)', 'single.trace:traceStatement:downPrintExceipt:downDefaultTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10205070', '追溯报表下载/打印单据(上传/下载自定义打印模板)', 'single.trace:traceStatement:downPrintExceipt:uploadDownPrintTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10205080', '追溯报表下载/打印单据(下载/打印pdf)', 'single.trace:traceStatement:downPrintExceipt:downPrintPdf', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10205090', '追溯报表下载/打印单据(导出Excel)', 'single.trace:traceStatement:downPrintExceipt:exportExcel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1);
-- 物料追溯
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10208010', '导出列表', 'feed.record:exportList', NULL, NULL, NULL, NULL, '2023-05-16 10:32:38', 'enable', 'GET', '10208', 2, 1, 0, '/trace-management/feed-record', 1);
-- 包装记录
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10203010', '包装视图导出', 'package.trace:packageView:export', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10203', 2, 1, 0, '/trace-management/package-record', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10203020', '导出列表', 'package.trace:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10203', 2, 1, 0, '/trace-management/package-record', 1);
-- 炉号信息
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10204020', '导出列表', 'furnace.info:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10204', 2, 1, 0, '/trace-management/furnace-info', 1);
-- 销售订单追溯
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10207010', '物料追溯', 'sale.order.trace:materialTrace', NULL, NULL, NULL, NULL, '2023-05-16 10:32:38', 'enable', 'GET', '10207', 2, 1, 0, '/trace/saleOrder-trace', 1);
-- 客户档案
UPDATE `sys_permissions` SET `path`= 'client.file:importData',`name`='导入管理(导入数据)' WHERE `path` = 'export:customer';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090101080', '对接人配置', 'client.file:contactPersonConfig', NULL, NULL, NULL, NULL, '2023-02-01 01:11:53', 'enable', 'GET', '1090101', 2, 1, 0, '/supply-chain-collaboration/order-model/clientFile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090101090', '导出列表', 'client.file:exportList', NULL, NULL, NULL, NULL, '2023-02-01 01:11:53', 'enable', 'GET', '1090101', 2, 1, 0, '/supply-chain-collaboration/order-model/clientFile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090101100', '导入管理', 'client.file:importManagement', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090101', 2, 1, 0, '/supply-chain-collaboration/order-model/clientFile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090101110', '导入管理(下载默认转换模板)', 'client.file:downDefaultTemplate', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090101', 2, 1, 0, '/supply-chain-collaboration/order-model/clientFile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090101120', '导入管理(查看日志)', 'client.file:log', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090101', 2, 1, 0, '/supply-chain-collaboration/order-model/clientFile', 1);
-- 供应商档案
UPDATE `sys_permissions` SET `path` = 'supplier.profile:importManagement' WHERE `path` = 'export:supplier';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201090', '对接人配置', 'supplier.profile:contactPersonConfig', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201100', '供应商类型', 'supplier.profile:supplierType', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201110', '导出列表', 'supplier.profile:exportList', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201120', '导入管理(下载默认转换模板)', 'supplier.profile:downDefaultTemplate', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201130', '导入管理(导入数据)', 'supplier.profile:importData', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201140', '导入管理(查看日志)', 'supplier.profile:log', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
-- 物料需求分析
UPDATE `sys_permissions` SET `name` = '导出列表', `path` = 'material.analysis:exportList' WHERE `path` = 'material.analysis:export';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090203020', '物料需求辅助分析', 'material.analysis:subsidiaryAnalysis', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '1090203', 2, 1, 0, '/supply-chain-collaboration/procurement-management/materialAnalysis', 1);
-- 采购订单
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090205160', '下载/打印单据', 'purchasing.list:downPrintExceipt', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090205170', '下载/打印单据(下载默认打印模板)', 'purchasing.list:downPrintExceipt:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090205180', '下载/打印单据(上传/下载自定义打印模板)', 'purchasing.list:downPrintExceipt:uploadDownPrintTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090205190', '下载/打印单据(下载/打印pdf)', 'purchasing.list:downPrintExceipt:downPrintPdf', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090205200', '下载/打印单据(导出Excel)', 'purchasing.list:downPrintExceipt:exportExcel', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
-- 采购收料
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206090', '导出列表', 'delivery.order:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206100', '下载/打印单据', 'delivery.order:downPrintExceipt', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206110', '下载/打印单据(下载默认打印模板)', 'delivery.order:downPrintExceipt:downDefaultTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'DELETE', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206120', '下载/打印单据(上传/下载自定义打印模板)', 'delivery.order:downPrintExceipt:uploadDownPrintTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206130', '下载/打印单据(下载/打印pdf)', 'delivery.order:downPrintExceipt:downPrintPdf', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206140', '下载/打印单据(导出Excel)', 'delivery.order:downPrintExceipt:exportExcel', NULL, NULL, NULL, NULL, '2023-02-01 03:05:21', 'enable', 'GET', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
-- 委外订单
UPDATE `sys_permissions` SET  `path` = 'outsourcingManagement.outsourcingOrder:edit' WHERE `path` = 'outsourcingManagement.outsourcingOrder:update';
UPDATE `sys_permissions` SET  `path` = 'outsourcingManagement.outsourcingOrder:detail' WHERE `path` = 'outsourcingManagement.outsourcingOrder:select';
-- 委外订单收货单
UPDATE `sys_permissions` SET  `path` = 'outsourcingManagement.outsourcingReceiptOrder:edit' WHERE `path` = 'outsourcingManagement.outsourcingReceiptOrder:update';
UPDATE `sys_permissions` SET  `path` = 'outsourcingManagement.outsourcingReceiptOrder:detail' WHERE `path` = 'outsourcingManagement.outsourcingReceiptOrder:select';
-- 委外订单用料清单
UPDATE `sys_permissions` SET  `path` = 'outsourcingManagement.outsourcingMaterialOrder:edit' WHERE `path` = 'outsourcingManagement.outsourcingMaterialOrder:update';
UPDATE `sys_permissions` SET  `path` = 'outsourcingManagement.outsourcingMaterialOrder:detail' WHERE `path` = 'outsourcingManagement.outsourcingMaterialOrder:select';
-- 车间每日产品产量报表
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12904010', '导出列表', 'output.summary.perday:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12904', 2, 1, 0, '/intelligentCreation/output-summary-perday', 1);
-- 生产报表
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('13001120', '生产日报导出列表', 'daily.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('13001130', '生产周报导出列表', 'week.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('13001140', '生产月报导出列表', 'month.statement.center:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('13001150', '生产绩效导出报表', 'production.performance:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter', 1);
-- 销售报表
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('13003020', '当前订单明细导出报表', 'sales.report:orderDetails:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', 2, 1, 0, '/statementCenter/salesReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('13003030', '订单进度导出报表', 'sales.report:orderProgress:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', 2, 1, 0, '/statementCenter/salesReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('13003040', '订单质量追踪导出报表', 'sales.report:orderQualityTracking:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '13003', 2, 1, 0, '/statementCenter/salesReport', 1);
-- 生产工单
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005430', '流水码新增', 'production.workorder:serialNumber:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005440', '流水码导出列表', 'production.workorder:serialNumber:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005450', '过站记录新增', 'production.workorder:passingRecord:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005460', '过站记录导出', 'production.workorder:passingRecord:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005470', '过站记录删除', 'production.workorder:passingRecord:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005480', '过站记录标签', 'production.workorder:passingRecord:label', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005490', '打卡记录补录', 'production.workorder:attendanceRecord:supplementaryRecord', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005500', '打卡记录导出', 'production.workorder:attendanceRecord:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
-- 生产工单2
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101390', '导出_下载默认模板', 'production.workorder:export:downDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101400', '导出_上传下载自定义导出模板', 'production.workorder:export:downUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101410', '导出_导出excel', 'production.workorder:export:excel', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101420', '补报', 'production.workorder:supplementaryReport', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101430', '流水码新增', 'production.workorder:serialNumber:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101440', '流水码导出列表', 'production.workorder:serialNumber:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101450', '过站记录新增', 'production.workorder:passingRecord:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101460', '过站记录导出', 'production.workorder:passingRecord:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101470', '过站记录删除', 'production.workorder:passingRecord:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101480', '过站记录标签', 'production.workorder:passingRecord:label', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101490', '打卡记录补录', 'production.workorder:attendanceRecord:supplementaryRecord', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101500', '打卡记录导出', 'production.workorder:attendanceRecord:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
-- 作业工单
UPDATE `sys_permissions` SET `name` = '下载/打印单据', `path` = 'home.workorder.report.record:downPrintExceipt' WHERE `id` = '10110010';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10103060', '导出列表', 'home.workorder:export', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10103', 2, 1, 0, '/workorder-model/home-workorder', 1);
-- 作业工单报工记录
UPDATE `sys_permissions` SET `name` = '下载/打印单据', `path` = 'home.workorder.report.record:downPrintExceipt' WHERE `id` = '10110010';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10110030', '下载/打印单据(下载默认打印模板)', 'home.workorder.report.record:downPrintExceipt:downDefaultTemplate', NULL, NULL, NULL, NULL, '2023-01-31 08:52:59', 'enable', 'GET', '10110', 2, 1, 0, '/workorder-model/workorder-records', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10110040', '下载/打印单据(上传自定义打印模板)', 'home.workorder.report.record:downPrintExceipt:uploadPrintTemplate', NULL, NULL, NULL, NULL, '2023-01-31 08:52:59', 'enable', 'GET', '10110', 2, 1, 0, '/workorder-model/workorder-records', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10110050', '下载/打印单据(下载自定义打印模板)', 'home.workorder.report.record:downPrintExceipt:downPrintTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10110', 2, 1, 0, '/workorder-model/workorder-records', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10110060', '下载/打印单据(导出Excel)', 'home.workorder.report.record:downPrintExceipt:exportExcel', NULL, NULL, NULL, NULL, '2023-01-31 08:52:59', 'enable', 'GET', '10110', 2, 1, 0, '/workorder-model/workorder-records', 1);
-- 生产设备
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10301060', '设备分类对比', 'device:deviceClassificationAndComparison', NULL, NULL, NULL, NULL, '2023-01-31 09:00:53', 'enable', 'GET', '10301', 2, 1, 0, '/equipment-management/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10301070', '大屏展示设置', 'device:largeScreenDisplaySettings', NULL, NULL, NULL, NULL, '2023-01-31 09:00:53', 'enable', 'GET', '10301', 2, 1, 0, '/equipment-management/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11601060', '设备分类对比', 'device:deviceClassificationAndComparison', NULL, NULL, NULL, NULL, '2023-02-01 07:05:01', 'enable', 'GET', '11601', 2, 1, 1, '/equipment/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11601070', '大屏展示设置', 'device:largeScreenDisplaySettings', NULL, NULL, NULL, NULL, '2023-02-01 07:05:01', 'enable', 'GET', '11601', 2, 1, 1, '/equipment/device', 1);
-- 告警转工单
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10303010', '新增', 'alarm.to.order:add', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '103', 2, 1, 0, '/equipment-management/alarmToOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10303020', '编辑', 'alarm.to.order:edit', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '103', 2, 1, 0, '/equipment-management/alarmToOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10303030', '详情', 'alarm.to.order:detail', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '103', 2, 1, 0, '/equipment-management/alarmToOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10303040', '删除', 'alarm.to.order:delete', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '103', 2, 1, 0, '/equipment-management/alarmToOrder', 1);
-- 不良定义
UPDATE `sys_permissions` SET `name` = '新增定义', `path` = 'quality.definition:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '10401', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityDefinition', `is_enable` = 1 WHERE `id` = '10401010';
UPDATE `sys_permissions` SET `name` = '导入', `path` = 'quality.definition:import', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10401', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityDefinition', `is_enable` = 1 WHERE `id` = '10401020';
UPDATE `sys_permissions` SET `name` = '导出列表', `path` = 'quality.definition:exportList', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10401', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityDefinition', `is_enable` = 1 WHERE `id` = '10401030';
UPDATE `sys_permissions` SET `name` = '不良类型定义', `path` = 'quality.definition:badTypeDefinition', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10401', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityDefinition', `is_enable` = 1 WHERE `id` = '10401040';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'quality.definition:edit', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10401', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityDefinition', `is_enable` = 1 WHERE `id` = '10401050';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'quality.definition:detail', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'PUT', `parent_id` = '10401', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityDefinition', `is_enable` = 1 WHERE `id` = '10401060';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'quality.definition:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'DELETE', `parent_id` = '10401', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityDefinition', `is_enable` = 1 WHERE `id` = '10401070';
DELETE FROM `sys_permissions` WHERE id = '10401080';
-- 工位质检
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402110', '检测记录查看图片', 'processQuality.record:seePicture', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402120', '检测记录导出列表', 'processQuality.record:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402130', '检测报表导出列表', 'processQuality.statement:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402140', '检测报表编辑', 'processQuality.statement:edit', NULL, NULL, NULL, NULL, '2023-02-01 02:16:06', 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402150', '检测报表详情', 'processQuality.statement:detail', NULL, NULL, NULL, NULL, '2023-01-31 09:53:19', 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
-- 工位质检方案
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10403010', '新增方案', 'station.quality.plan:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10403', 2, 1, 0, '/qualities-manage/processQuality/paln', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10403020', '编辑', 'station.quality.plan:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10403', 2, 1, 0, '/qualities-manage/processQuality/paln', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10403030', '详情', 'station.quality.plan:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10403', 2, 1, 0, '/qualities-manage/processQuality/paln', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10403040', '删除', 'station.quality.plan:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10403', 2, 1, 0, '/qualities-manage/processQuality/paln', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10403050', '不良类型排序', 'station.quality.plan:badTypeSort', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10403', 2, 1, 0, '/qualities-manage/processQuality/paln', 1);
-- 质检返工定义
UPDATE `sys_permissions` SET `name` = '故障类型定义', `path` = 'quality.rework.define:maintainTypeDefine', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10404', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/reworkQuality/define', `is_enable` = 1 WHERE `id` = '10404010';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10404020', '新增定义', 'quality.rework.define:add', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10404030', '导入', 'quality.rework.define:import', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10404040', '导出列表', 'quality.rework.define:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10404050', '维修类型定义', 'quality.rework.define:maintainTypeDefine', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10404060', '详情', 'quality.rework.define:detail', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10404070', '编辑', 'quality.rework.define:edit', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10404080', '删除', 'quality.rework.define:delete', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1);
-- 质检返工
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405110', '返工记录导出列表', 'return:record:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405120', '返工记录查看图片', 'return:record:seePicture', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405130', '返工记录来料反馈', 'return:record:feedback', NULL, NULL, NULL, NULL, '2023-02-01 02:19:35', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405140', '返工报表导出列表', 'return:statement:exportList', NULL, NULL, NULL, NULL, '2023-02-01 02:19:40', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
-- 质检返工方案
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10406010', '新增方案', 'quality.rework.plan:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10406', 2, 1, 0, '/qualities-manage/reworkQuality/plan/project', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10406020', '编辑', 'quality.rework.plan:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10406', 2, 1, 0, '/qualities-manage/reworkQuality/plan/project', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10406030', '详情', 'quality.rework.plan:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10406', 2, 1, 0, '/qualities-manage/reworkQuality/plan/project', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10406040', '删除', 'quality.rework.plan:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10406', 2, 1, 0, '/qualities-manage/reworkQuality/plan/project', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10406050', '维修类型排序', 'quality.rework.plan:maintainTypeSort', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10406', 2, 1, 0, '/qualities-manage/reworkQuality/plan/project', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10406060', '增加返工项', 'quality.rework.plan:addReworkItem', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10406', 2, 1, 0, '/qualities-manage/reworkQuality/plan/project', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10406070', '删除返工项', 'quality.rework.plan:deleteReworkItem', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10406', 2, 1, 0, '/qualities-manage/reworkQuality/plan/project', 1);

-- 品质反馈定义
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'quality.feedback.definition:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '10411', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityFeedbackDefinition', `is_enable` = 1 WHERE `id` = '10411010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'quality.feedback.definition:edit', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10411', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityFeedbackDefinition', `is_enable` = 1 WHERE `id` = '10411020';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'quality.feedback.definition:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10411', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/qualities-manage/processQuality/qualityFeedbackDefinition', `is_enable` = 1 WHERE `id` = '10411030';

-- 产品检测
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10410260', '送检单导出列表', 'product.inspection.inspectionsheets:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10410', 2, 1, 0, '/qualities-manage/productInspection', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10410270', '送检单下载/打印单据', 'inspection.sheet:downPrintExceipt', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10410', 2, 1, 0, '/qualities-manage/productInspection', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10410280', '产品检测结果导出列表', 'inspection.product.result:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10410', 2, 1, 0, '/qualities-manage/productInspection', 1);

-- BOSA管理
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10609010', '导出', 'bosa.management:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10609', 2, 1, 0, '/smt-management/bosaManagement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10609020', '详情', 'bosa.management:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10609', 2, 1, 0, '/smt-management/bosaManagement', 1);

-- Feeder管理
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'feeder.manage:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10604', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/smtManagement/feederManage', `is_enable` = 1 WHERE `id` = '10604010';
UPDATE `sys_permissions` SET `name` = '导入', `path` = 'feeder.manage:import', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10604', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/smtManagement/feederManage', `is_enable` = 1 WHERE `id` = '10604020';
UPDATE `sys_permissions` SET `name` = '导出列表', `path` = 'feeder.manage:export', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10604', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/smtManagement/feederManage', `is_enable` = 1 WHERE `id` = '10604030';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'feeder.manage:edit', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10604', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/smtManagement/feederManage', `is_enable` = 1 WHERE `id` = '10604040';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10604050', '详情', 'operation.manage:detail', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10604', 2, 1, 0, '/smtManagement/feederManage', 1);

-- 老化管理
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'aging.management:detail', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10606', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/smt-Management/aging-management', `is_enable` = 1 WHERE `id` = '1060601';

-- 废料管理
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10607010', '称重记录导出列表', 'scrap.material.management:weighingRecord:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10607', 2, 1, 0, '/smt-management/scrap-material-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10607020', '称重统计导出记录', 'scrap.material.management:weighingStatistics:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10607', 2, 1, 0, '/smt-management/scrap-material-management', 1);

-- 产前准备
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10608040', '导出列表', 'antenatal.preparation.inspection.record:exportList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10608', 3, 1, 0, '/smt-management/antenatal-preparation', 1);

-- 告警列表
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10701080', '查看设备维修工单', 'list:checkTheEquipmentMaintenanceWorkOrder', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10701', 2, 1, 0, '/alarm/list', 1);

-- 告警设置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10702020', '通知设置-新建', 'alarm.setting:notificSetting:add', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10702', 2, 1, 0, '/alarm/config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10702030', '通知设置-编辑', 'alarm.setting:notificSetting:edit', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10702', 2, 1, 0, '/alarm/config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10702040', '通知设置-查看', 'alarm.setting:notificSetting:detail', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10702', 2, 1, 0, '/alarm/config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10702050', '通知设置-删除', 'alarm.setting:notificSetting:delete', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10702', 2, 1, 0, '/alarm/config', 1);

-- 告警类型定义
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10704020', '新增', 'alarm.type.define:add', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10704', 2, 1, 0, '/alarm/alarmTypeDefine', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10704030', '编辑', 'alarm.type.define:edit', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10704', 2, 1, 0, '/alarm/alarmTypeDefine', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10704040', '删除', 'alarm.type.define:delete', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10704', 2, 1, 0, '/alarm/alarmTypeDefine', 1);

-- 工序定义
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804170', '导入管理', 'procedure:import', NULL, NULL, NULL, NULL, '2023-02-06 16:04:45', 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804180', '导入管理下载默认转换模板', 'procedure:downDefaultTemplate', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);

-- 质检分析
UPDATE `sys_permissions` SET `name` = '设置标准', `path` = 'quality.testing.analysis:setStandard', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '11204', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/expert-system/sterilization-cabinet-analysis', `is_enable` = 1 WHERE `id` = '11204010';

-- 班次配置
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'flight.config:edit', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'PUT', `parent_id` = '11402', `type` = 2, `is_web` = 1, `is_back_stage` = 1, `parent_path` = '/production-config/flight-config', `is_enable` = 1 WHERE `id` = '11402020';

-- 首班时间配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11403010', '配置时间', 'first.shift.time.config:configTime', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11403', 2, 1, 1, '/production-config/firstShiftTimeConfig', 1);

-- 产能列表
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'capacity.list:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '11404', `type` = 2, `is_web` = 1, `is_back_stage` = 1, `parent_path` = '/production-config/capacityList', `is_enable` = 1 WHERE `id` = '11404010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'capacity.list:edit', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '11404', `type` = 2, `is_web` = 1, `is_back_stage` = 1, `parent_path` = '/production-config/capacityList', `is_enable` = 1 WHERE `id` = '11404020';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11404030', '导出列表', 'capacity.list:exportList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'POST', '11404', 2, 1, 1, '/production-config/capacityList', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11404040', '导入', 'capacity.list:import', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'POST', '11404', 2, 1, 1, '/production-config/capacityList', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11404050', '默认产能配置', 'capacity.list:defaultCapacityConfig', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'POST', '11404', 2, 1, 1, '/production-config/capacityList', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11404060', '每月标准产量', 'capacity.list:standardOutput', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'POST', '11404', 2, 1, 1, '/production-config/capacityList', 1);

-- 工厂指标
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501010', '新增', 'factory.target:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501020', '导入', 'factory.target:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501030', '自动指标编辑', 'factory.target:autoIndicator:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501040', '自动指标阈值设置', 'factory.target:autoIndicator:thresholdSetting', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501050', '自动指标详情', 'factory.target:autoIndicator:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501060', '自动指标删除', 'factory.target:autoIndicator:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501070', '手动指标编辑', 'factory.target:manualIndicator:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501080', '手动指标删除', 'factory.target:manualIndicator:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501090', '组合指标编辑', 'factory.target:compositeIndicator:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501100', '组合指标详情', 'factory.target:compositeIndicator:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11501110', '组合指标删除', 'factory.target:compositeIndicator:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11501', 2, 1, 1, '/target/factoryTarget', 1);

-- 综合指标
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11502010', '新增', 'synthesis.target:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11502', 2, 1, 1, '/target/synthesizeTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11502020', '基础指标编辑', 'synthesis.target:baseIndicator.edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11502', 2, 1, 1, '/target/synthesizeTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11502030', '基础指标阈值设置', 'synthesis.target:baseIndicator.threshold', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11502', 2, 1, 1, '/target/synthesizeTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11502040', '基础指标详情', 'synthesis.target:baseIndicator.detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11502', 2, 1, 1, '/target/synthesizeTarget', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11502050', '基础指标删除', 'synthesis.target:baseIndicator.delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11502', 2, 1, 1, '/target/synthesizeTarget', 1);

-- 采集设备
UPDATE `sys_permissions` SET `name` = '创建新采集设备', `path` = 'test.equipment:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '11602', `type` = 2, `is_web` = 1, `is_back_stage` = 1, `parent_path` = '/equipment/test-equipment', `is_enable` = 1 WHERE `id` = '11602010';
UPDATE `sys_permissions` SET `name` = '类型配置', `path` = 'test.equipment:typeConfig', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'PUT', `parent_id` = '11602', `type` = 2, `is_web` = 1, `is_back_stage` = 1, `parent_path` = '/equipment/test-equipment', `is_enable` = 1 WHERE `id` = '11602060';

-- 视频设备
UPDATE `sys_permissions` SET `name` = '创建新视频设备', `path` = 'video.device:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '11604', `type` = 2, `is_web` = 1, `is_back_stage` = 1, `parent_path` = '/equipment/video-device', `is_enable` = 1 WHERE `id` = '11604010';

-- 工艺装备
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11608010', '新增工艺装备', 'technology.device:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11608', 2, 1, 1, '/equipment/technology-device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11608020', '工装类型', 'technology.device:toolType', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11608', 2, 1, 1, '/equipment/technology-device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11608030', '编辑', 'technology.device:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11608', 2, 1, 1, '/equipment/technology-device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11608040', '删除', 'technology.device:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11608', 2, 1, 1, '/equipment/technology-device', 1);

-- 设备类型
UPDATE `sys_permissions` SET `name` = '创建新设备类型', `path` = 'equipment.model:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '11606', `type` = 2, `is_web` = 1, `is_back_stage` = 1, `parent_path` = '/equipment/equipment-model', `is_enable` = 1 WHERE `id` = '11606010';

-- 点位配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11701010', '同步点位', 'configuration.location:syncPoint', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11701', 2, 1, 1, '/avg/point/list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11701020', '关联工位', 'configuration.location:associatedStation', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11701', 2, 1, 1, '/avg/point/list', 1);

-- 化学元素配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11802010', '增加元素', 'element.config:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11802', 2, 1, 1, '/material-config/element-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11802020', '编辑', 'element.config:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11802', 2, 1, 1, '/material-config/element-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11802030', '删除', 'element.config:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '11802', 2, 1, 1, '/material-config/element-config', 1);

-- 打印配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12006010', '新增打印模板', 'print.config:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12006', 2, 1, 1, '/documents-config/print-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12006020', '编辑', 'print.config:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12006', 2, 1, 1, '/documents-config/print-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12006030', '删除', 'print.config:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12006', 2, 1, 1, '/documents-config/print-config', 1);

-- 出入库类型配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12004040', '保存', 'inbound.outbound.type.config:save', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '12004', 2, 1, 1, '/documents-config/inbound-outbound-type-confige', 1);

-- 初始化配置
UPDATE `sys_permissions` SET `name` = '数据清除（保存选中内容并清除其他数据）', `path` = 'init.config:dataClear:save', id = '12209020' WHERE `id` = '12209011';
UPDATE `sys_permissions` SET  `name` = '试运行态切换', `path` = 'init.config:statusChange', id = '12209030' WHERE `id` = '12209012';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12209040', '基础数据导入（下载导入模板）', 'init.config:baseDataImport:downImportTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12209', 2, 1, 0, '/product-management/auxiliary-attr', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12209050', '基础数据导入（导入数据）', 'init.config:baseDataImport:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12209', 2, 1, 0, '/product-management/auxiliary-attr', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12209060', '基础数据导入（查看日志）', 'init.config:baseDataImport:log', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12209', 2, 1, 0, '/product-management/auxiliary-attr', 1);

-- 流转通知配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12207010', '新增', 'circulation.notice.config:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12207', 2, 1, 1, '/system_settings/circulation-notice-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12207020', '编辑', 'circulation.notice.config:edit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12207', 2, 1, 1, '/system_settings/circulation-notice-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12207030', '查看', 'circulation.notice.config:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12207', 2, 1, 1, '/system_settings/circulation-notice-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12207040', '删除', 'circulation.notice.config:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12207', 2, 1, 1, '/system_settings/circulation-notice-config', 1);

-- 生产设备
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10301080', '附件上传', 'device:attachmentUploading', NULL, NULL, NULL, NULL, '2023-01-31 09:00:53', 'enable', 'GET', '10301', 2, 1, 0, '/equipment-management/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10301090', '设备类型新增', 'device:typeAdd', NULL, NULL, NULL, NULL, '2023-01-31 09:00:53', 'enable', 'GET', '10301', 2, 1, 0, '/equipment-management/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11601080', '附件上传', 'device:attachmentUploading', NULL, NULL, NULL, NULL, '2023-02-01 07:05:01', 'enable', 'GET', '11601', 2, 1, 1, '/equipment/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11601090', '设备类型新增', 'device:typeAdd', NULL, NULL, NULL, NULL, '2023-02-01 07:05:01', 'enable', 'GET', '11601', 2, 1, 1, '/equipment/device', 1);

-- 物料特征参数
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10807080', '保存并生效', 'auxiliary.attr:saveAndTakeEffect', NULL, NULL, NULL, NULL, '2023-02-02 14:32:32', 'enable', 'GET', '10807', 2, 1, 0, '/product-management/auxiliary-attr', 1);

-- BOM
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10802230', '查看工艺', 'bom:viewProcess', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10802240', '查看BOM', 'bom:viewBOM', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1);

-- 工序
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804190', '新增工艺参数', 'procedure:addTechnologParams', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804200', '删除工艺参数', 'procedure:deleteTechnologParams', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804210', '新增检测项目', 'procedure:addProcessInspection', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804220', '编辑检测项目', 'procedure:editProcessInspection', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804230', '删除检测项目', 'procedure:deleteProcessInspection', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804240', '新增不良项目', 'procedure:addBadProject', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804250', '删除不良项目', 'procedure:deleteBadProject', NULL, NULL, '2023-06-20 10:44:10', NULL, NULL, 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);

-- 工艺
UPDATE `sys_permissions` SET `name` = '工艺详情', `path` = 'technology:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '10803', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/product-management/technology', `is_enable` = 1 WHERE `id` = '10803030';

-- 物料
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10801200', '批量附件导入', 'material:batchAttachmentImport', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10801', 2, 1, 0, '/product-management/supplies', 1);


-- 新增SMT拼版-组合码编码规则
call init_number_rules_config_in_new_rule("INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (67, 'SMT拼版-组合码');");

-- 新增拼码管理菜单
INSERT INTO `sys_route`( `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`)
VALUES ('/smt-management/combine-management', '/smt-management', 'combineManagement', NULL, '拼码管理', NULL, 'dfs', NULL, NULL, NULL, 9, 0, NULL);
-- 新增拼码管理权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10613', '拼码管理', '/smt-management/combine-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '106', 1, 1, 0, '/smt-management', 1),
('10613010', '列配置_列表', 'combine-management:columnConfigList', NULL, NULL, NULL, NULL, now(), 'enable', 'GET', '10613', 2, 1, 0, '/smt-management/combine-management', 1);
call init_new_role_permission('10613');
call init_new_role_permission('10613010');
-- 新增拼码管理权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES
('10613020', '下载默认导出模板', 'combine-management:downDefaultExportTemplate', NULL, NULL, NULL, NULL, NOW(), 'enable', 'POST', '10613', 2, 1, 0, '/smt-management/combine-management', 1),
('10613030', '导出excel', 'combine-management:export', NULL, NULL, NULL, NULL, NOW(), 'enable', 'POST', '10613', 2, 1, 0, '/smt-management/combine-management', 1),
('10613040', '下载默认导入模板', 'combine-management:downDefaultImportTemplate', NULL, NULL, NULL, NULL, NOW(), 'enable', 'POST', '10613', 2, 1, 0, '/smt-management/combine-management', 1),
('10613050', '导入数据', 'combine-management:import', NULL, NULL, NULL, NULL, NOW(), 'enable', 'POST', '10613', 2, 1, 0, '/smt-management/combine-management', 1),
('10613060', '查看日志', 'combine-management:logs', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '10613', 2, 1, 0, '/smt-management/combine-management', 1);
call init_new_role_permission('10613020');
call init_new_role_permission('10613030');
call init_new_role_permission('10613040');
call init_new_role_permission('10613050');
call init_new_role_permission('10613060');

-- web端按钮权限补充
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102270', '查看BOM', 'sales.order:viewBom', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102280', '查看工艺', 'sales.order:viewProcess', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102290', '批量审批', 'sales.order:approvalBatch', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001270', '查看BOM', 'sales.order:viewBom', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001280', '查看工艺', 'sales.order:viewProcess', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001290', '批量审批', 'sales.order:approvalBatch', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003410', '查看BOM', 'product.order:viewBom', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003420', '查看工艺', 'product.order:viewProcess', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006080', '批量审批', 'production.materials:approvalBatch', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10006', 2, 1, 0, '/order-model/production-materials', 1);
UPDATE `sys_permissions` SET `name` = '创建来料检验单', `path` = 'materials.list:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '1090207', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/supply-chain-collaboration/procurement-management/materials-list', `is_enable` = 1 WHERE `id` = '1090207010';
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003430', '附件删除', 'product.order:appendixDelete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
UPDATE `sys_permissions` SET `name` = '创建来料检验单', `path` = 'incoming.inspection:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'POST', `parent_id` = '1090207', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/supply-chain-collaboration/procurement-management/materials-list', `is_enable` = 1 WHERE `id` = '1090207010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'incoming.inspection:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'PUT', `parent_id` = '1090207', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/supply-chain-collaboration/procurement-management/materials-list', `is_enable` = 1 WHERE `id` = '1090207020';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'incoming.inspection:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'GET', `parent_id` = '1090207', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/supply-chain-collaboration/procurement-management/materials-list', `is_enable` = 1 WHERE `id` = '1090207030';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'incoming.inspection:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-12-06 10:27:20', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090207', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/supply-chain-collaboration/procurement-management/materials-list', `is_enable` = 1 WHERE `id` = '1090207040';
UPDATE `sys_permissions` SET `name` = '审批', `path` = 'incoming.inspection:approval', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = NULL, `status` = 'enable', `method` = 'GET', `parent_id` = '1090207', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/supply-chain-collaboration/procurement-management/materials-list', `is_enable` = 1 WHERE `id` = '1090207050';
UPDATE `sys_permissions` SET `name` = '上传自定义转换模板', `path` = 'product:order:import-custom-template', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = NULL, `status` = 'enable', `method` = 'POST', `parent_id` = '10003', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/order-model/product-order', `is_enable` = 1 WHERE `id` = '10003110';

-- web端补充按钮权限初始化
call init_new_role_permission('10001260');
call init_new_role_permission('1090102230');
call init_new_role_permission('1090102240');
call init_new_role_permission('1090102250');
call init_new_role_permission('10003290');
call init_new_role_permission('10003300');
call init_new_role_permission('10003310');
call init_new_role_permission('10003320');
call init_new_role_permission('10003330');
call init_new_role_permission('10003340');
call init_new_role_permission('10003350');
call init_new_role_permission('10003360');
call init_new_role_permission('10003370');
call init_new_role_permission('10003380');
call init_new_role_permission('10003390');
call init_new_role_permission('10003400');
call init_new_role_permission('10002110');
call init_new_role_permission('10002120');
call init_new_role_permission('10004100');
call init_new_role_permission('10004110');
call init_new_role_permission('10007010');
call init_new_role_permission('10007020');
call init_new_role_permission('10007030');
call init_new_role_permission('10007040');
call init_new_role_permission('10007050');
call init_new_role_permission('10007060');
call init_new_role_permission('10007070');
call init_new_role_permission('10007080');
call init_new_role_permission('10007090');
call init_new_role_permission('10007100');
call init_new_role_permission('10007110');
call init_new_role_permission('10202010');
call init_new_role_permission('10202020');
call init_new_role_permission('10202030');
call init_new_role_permission('10202040');
call init_new_role_permission('10202050');
call init_new_role_permission('10205050');
call init_new_role_permission('10205060');
call init_new_role_permission('10205070');
call init_new_role_permission('10205080');
call init_new_role_permission('10205090');
call init_new_role_permission('10208010');
call init_new_role_permission('10203010');
call init_new_role_permission('10203020');
call init_new_role_permission('10204020');
call init_new_role_permission('10207010');
call init_new_role_permission('1090101080');
call init_new_role_permission('1090101090');
call init_new_role_permission('1090101100');
call init_new_role_permission('1090101110');
call init_new_role_permission('1090101120');
call init_new_role_permission('1090201090');
call init_new_role_permission('1090201100');
call init_new_role_permission('1090201110');
call init_new_role_permission('1090201120');
call init_new_role_permission('1090201130');
call init_new_role_permission('1090201140');
call init_new_role_permission('1090203020');
call init_new_role_permission('1090205160');
call init_new_role_permission('1090205170');
call init_new_role_permission('1090205180');
call init_new_role_permission('1090205190');
call init_new_role_permission('1090205200');
call init_new_role_permission('1090206090');
call init_new_role_permission('1090206100');
call init_new_role_permission('1090206110');
call init_new_role_permission('1090206120');
call init_new_role_permission('1090206130');
call init_new_role_permission('1090206140');
call init_new_role_permission('12904010');
call init_new_role_permission('13001120');
call init_new_role_permission('13001130');
call init_new_role_permission('13001140');
call init_new_role_permission('13001150');
call init_new_role_permission('13003020');
call init_new_role_permission('13003030');
call init_new_role_permission('13003040');
call init_new_role_permission('10005430');
call init_new_role_permission('10005440');
call init_new_role_permission('10005450');
call init_new_role_permission('10005460');
call init_new_role_permission('10005470');
call init_new_role_permission('10005480');
call init_new_role_permission('10005490');
call init_new_role_permission('10005500');
call init_new_role_permission('10101390');
call init_new_role_permission('10101400');
call init_new_role_permission('10101410');
call init_new_role_permission('10101420');
call init_new_role_permission('10101430');
call init_new_role_permission('10101440');
call init_new_role_permission('10101450');
call init_new_role_permission('10101460');
call init_new_role_permission('10101470');
call init_new_role_permission('10101480');
call init_new_role_permission('10101490');
call init_new_role_permission('10101500');
call init_new_role_permission('10103060');
call init_new_role_permission('10110030');
call init_new_role_permission('10110040');
call init_new_role_permission('10110050');
call init_new_role_permission('10110060');
call init_new_role_permission('10301060');
call init_new_role_permission('10301070');
call init_new_role_permission('11601060');
call init_new_role_permission('11601070');
call init_new_role_permission('10303010');
call init_new_role_permission('10303020');
call init_new_role_permission('10303030');
call init_new_role_permission('10303040');
call init_new_role_permission('10402110');
call init_new_role_permission('10402120');
call init_new_role_permission('10402130');
call init_new_role_permission('10402140');
call init_new_role_permission('10402150');
call init_new_role_permission('10403010');
call init_new_role_permission('10403020');
call init_new_role_permission('10403030');
call init_new_role_permission('10403040');
call init_new_role_permission('10403050');
call init_new_role_permission('10404020');
call init_new_role_permission('10404030');
call init_new_role_permission('10404040');
call init_new_role_permission('10404050');
call init_new_role_permission('10404060');
call init_new_role_permission('10404070');
call init_new_role_permission('10404080');
call init_new_role_permission('10405110');
call init_new_role_permission('10405120');
call init_new_role_permission('10405130');
call init_new_role_permission('10405140');
call init_new_role_permission('10406010');
call init_new_role_permission('10406020');
call init_new_role_permission('10406030');
call init_new_role_permission('10406040');
call init_new_role_permission('10406050');
call init_new_role_permission('10406060');
call init_new_role_permission('10406070');
call init_new_role_permission('10410260');
call init_new_role_permission('10410270');
call init_new_role_permission('10410280');
call init_new_role_permission('10609010');
call init_new_role_permission('10609020');
call init_new_role_permission('10604050');
call init_new_role_permission('10607010');
call init_new_role_permission('10607020');
call init_new_role_permission('10608040');
call init_new_role_permission('10701080');
call init_new_role_permission('10702020');
call init_new_role_permission('10702030');
call init_new_role_permission('10702040');
call init_new_role_permission('10702050');
call init_new_role_permission('10704020');
call init_new_role_permission('10704030');
call init_new_role_permission('10704040');
call init_new_role_permission('10804170');
call init_new_role_permission('10804180');
call init_new_role_permission('11403010');
call init_new_role_permission('11404030');
call init_new_role_permission('11404040');
call init_new_role_permission('11404050');
call init_new_role_permission('11404060');
call init_new_role_permission('11501010');
call init_new_role_permission('11501020');
call init_new_role_permission('11501030');
call init_new_role_permission('11501040');
call init_new_role_permission('11501050');
call init_new_role_permission('11501060');
call init_new_role_permission('11501070');
call init_new_role_permission('11501080');
call init_new_role_permission('11501090');
call init_new_role_permission('11501100');
call init_new_role_permission('11501110');
call init_new_role_permission('11502010');
call init_new_role_permission('11502020');
call init_new_role_permission('11502030');
call init_new_role_permission('11502040');
call init_new_role_permission('11502050');
call init_new_role_permission('11608010');
call init_new_role_permission('11608020');
call init_new_role_permission('11608030');
call init_new_role_permission('11608040');
call init_new_role_permission('11701010');
call init_new_role_permission('11701020');
call init_new_role_permission('11802010');
call init_new_role_permission('11802020');
call init_new_role_permission('11802030');
call init_new_role_permission('12006010');
call init_new_role_permission('12006020');
call init_new_role_permission('12006030');
call init_new_role_permission('12004040');
call init_new_role_permission('12209040');
call init_new_role_permission('12209050');
call init_new_role_permission('12209060');
call init_new_role_permission('12207010');
call init_new_role_permission('12207020');
call init_new_role_permission('12207030');
call init_new_role_permission('12207040');
call init_new_role_permission('10301080');
call init_new_role_permission('10301090');
call init_new_role_permission('11601080');
call init_new_role_permission('11601090');
call init_new_role_permission('10807080');
call init_new_role_permission('10802230');
call init_new_role_permission('10802240');
call init_new_role_permission('10804190');
call init_new_role_permission('10804200');
call init_new_role_permission('10804210');
call init_new_role_permission('10804220');
call init_new_role_permission('10804230');
call init_new_role_permission('10804240');
call init_new_role_permission('10804250');
call init_new_role_permission('10801200');
call init_new_role_permission('1090102270');
call init_new_role_permission('1090102280');
call init_new_role_permission('1090102290');
call init_new_role_permission('10001270');
call init_new_role_permission('10001280');
call init_new_role_permission('10001290');
call init_new_role_permission('10003410');
call init_new_role_permission('10003420');
call init_new_role_permission('10006080');
call init_new_role_permission('10003430');

-- 处理报工记录历史数据
update dfs_report_line,dfs_device set dfs_report_line.resource_device_name = dfs_device.device_name where dfs_report_line.device_code = dfs_device.device_code;

