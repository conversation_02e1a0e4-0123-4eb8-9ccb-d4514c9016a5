-- 创建班次能耗表
CREATE TABLE IF NOT EXISTS `dfs_device_shift_energy_consumption` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `device_id` int(11) DEFAULT NULL COMMENT '设备id',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `shift_id` int(11) DEFAULT NULL COMMENT '班次id',
  `shift_start_time` datetime DEFAULT NULL COMMENT '班次开始时间',
  `shift_end_time` datetime DEFAULT NULL COMMENT '班次结束时间',
  `shift_start_consumption` double(11,3) DEFAULT NULL COMMENT '班次开始能耗',
  `shift_end_consumption` double(11,3) DEFAULT NULL COMMENT '班次结束能耗',
  `consumption` double(11,3) DEFAULT NULL COMMENT '能耗',
  `record_date` datetime DEFAULT NULL COMMENT '日期',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`) USING BTREE,
  KEY `shift_id` (`shift_id`),
  KEY `shift_start_time` (`shift_start_time`),
  KEY `shift_end_time` (`shift_end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='班次能耗表';

-- 创建班次耗气表
CREATE TABLE IF NOT EXISTS `dfs_device_shift_gas_consumption` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `device_id` int(11) DEFAULT NULL COMMENT '设备id',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `shift_id` int(11) DEFAULT NULL COMMENT '班次id',
  `shift_start_time` datetime DEFAULT NULL COMMENT '班次开始时间',
  `shift_end_time` datetime DEFAULT NULL COMMENT '班次结束时间',
  `shift_start_consumption` double(11,3) DEFAULT NULL COMMENT '班次开始能耗',
  `shift_end_consumption` double(11,3) DEFAULT NULL COMMENT '班次结束能耗',
  `consumption` double(11,3) DEFAULT NULL COMMENT '能耗',
  `record_date` datetime DEFAULT NULL COMMENT '日期',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`) USING BTREE,
  KEY `shift_id` (`shift_id`),
  KEY `shift_start_time` (`shift_start_time`),
  KEY `shift_end_time` (`shift_end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='班次耗气表';

-- 创建班次产量表
CREATE TABLE IF NOT EXISTS `dfs_shift_count` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `device_id` int(11) DEFAULT NULL COMMENT '设备id',
  `line_id` int(11) DEFAULT NULL COMMENT '产线id',
  `team_id` int(11) DEFAULT NULL COMMENT '班组id',
  `work_order_number` varchar(255) DEFAULT NULL COMMENT '工单号',
  `shift_id` int(11) DEFAULT NULL COMMENT '班次id',
  `shift_start_time` datetime DEFAULT NULL COMMENT '班次开始时间',
  `shift_end_time` datetime DEFAULT NULL COMMENT '班次结束时间',
  `finish_count` double(11,3) DEFAULT NULL COMMENT '完成数量',
  `input` double(11,3) DEFAULT NULL COMMENT '投入数',
  `unqualified` double(11,3) DEFAULT NULL COMMENT '不合格数',
  `record_date` datetime DEFAULT NULL COMMENT '日期',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`) USING BTREE,
  KEY `shift_id` (`shift_id`),
  KEY `shift_start_time` (`shift_start_time`),
  KEY `shift_end_time` (`shift_end_time`),
  KEY `work_order_number` (`work_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='班次产量表';
