-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================

-- 工单指标
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'human_working_hour',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `human_working_hour` double(11,2) NULL COMMENT ''人员投入工时'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'resource_working_hour',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `resource_working_hour` double(11,2) NULL COMMENT ''资源投入工时'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'human_working_hour',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `human_working_hour` double(11,2) NULL COMMENT ''人员投入工时'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'resource_working_hour',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `resource_working_hour` double(11,2) NULL COMMENT ''资源投入工时'';');

--
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_work_order_basic_unit_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) NOT NULL COMMENT '唯一标识',
    `record_time` datetime NOT NULL COMMENT '记录时间',
    `work_order_number` varchar(255) NOT NULL COMMENT '工单号',
    `production_basic_unit_type` varchar(255) NOT NULL COMMENT '生产基本单元类型',
    `production_basic_unit_id` int(11) NOT NULL COMMENT '生产基本单元id',
    `production_basic_unit_name` varchar(255) DEFAULT NULL COMMENT '生产基本单元名称(冗余)',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `human_working_hour` double(11,2) DEFAULT NULL COMMENT '人员投入工时',
    `resource_working_hour` double(11,2) DEFAULT NULL COMMENT '资源投入工时',
    `actual_working_hour` double(11,2) DEFAULT NULL COMMENT '实际投入工时',
    `theory_working_hour` double(11,2) DEFAULT NULL COMMENT '理论产出工时',
    `qualified_rate` double(11,2) DEFAULT NULL COMMENT '合格率',
    `product_efficiency_rate` double(11,2) DEFAULT NULL COMMENT '生产效率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    `relevance_unit_type` varchar(255) DEFAULT NULL COMMENT '关联资源类型',
    `relevance_unit_id` int(11) DEFAULT NULL COMMENT '关联资源id',
    `relevance_unit_name` varchar(255) DEFAULT NULL COMMENT '关联资源名称',
    `cross_quantity` double(11,2) DEFAULT NULL COMMENT '过站数',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数',
    `unqualified_record_quantity` double(11,2) DEFAULT NULL COMMENT '不良品记录数量',
    `unqualified_record_item_quantity` double(11,2) DEFAULT NULL COMMENT '不良项记录数量',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '维修数',
    `repair_scrap_quantity` double(11,2) DEFAULT NULL COMMENT '维报废修数',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_union` (`work_order_number`,`production_basic_unit_id`,`production_basic_unit_type`,`record_time`) USING BTREE,
    UNIQUE KEY `idx_uni` (`uni_code`),
    KEY `idx_record_time` (`record_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单-生产基本单元每日统计';


INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('workOrderBasicUnitDaily', '工单-生产基本单元每日', NULL, 'baseTargetGroupObject', 'workOrderTarget', NULL, NULL, NULL, '2021-03-25 10:20:11', '2021-03-25 10:20:11', NULL, 1, 1, 'm');
INSERT INTO `dfs_target_model_table_relate`(`target_name`, `model_id`, `table_schema`, `table_name`) VALUES ('workOrderBasicUnitDaily', (SELECT id FROM `dfs_model` WHERE `code` = 'workOrderTarget'), 'dfs_metrics', 'dfs_metrics_work_order_basic_unit_daily');
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('workOrderBasicUnitDaily', 'workOrderTarget', '工单-生产基本单元每日', 'workOrderBasicUnitDaily', 1, NULL);
INSERT INTO `dfs_target_model`(`target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `frequency_unit`, `alarm_definition_code`, `is_count_target`, `run_target_status_formula`, `not_run_target_status_formula`, `stop_target_status_formula`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `data_type`, `data_decimal_point`, `remark`, `source`, `script`, `data_source_type`, `enable`) VALUES ('workOrderBasicUnitDaily', '工单-生产基本单元每日', (SELECT id FROM `dfs_model` WHERE `code` = 'workOrderTarget'), 'workOrderBasicUnitDaily', '系统统计', NULL, 1, NULL, 'm', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-08-12 11:11:13', '2024-08-12 11:11:13', 'number', 1, NULL, '自动', NULL, 'inner', 1);



-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到********=======================================================
