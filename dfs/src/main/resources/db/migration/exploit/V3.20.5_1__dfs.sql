call proc_add_column(
        'dfs_bar_code',
        'inventory_quantity',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `inventory_quantity` double(11,2) NULL DEFAULT null COMMENT ''入库数量''');

-- DML
-- 部门编码
update sys_department set department_code = department_name where department_code is null or department_code = '';

delete from dfs_notice_field_info where placeholder in ('{任务编号}','{任务名称}','{项目编号}', '{项目名称}');
delete from dfs_notice_type_placeholder where placeholder in ('{任务编号}','{任务名称}','{项目编号}', '{项目名称}');


delete from dfs_config_label_type_info_relate where label_type_code = 'workOrderReport';
update dfs_label_module_config set module_code = 'reporterRecordVO' ,module_name = '报工记录信息' ,have_all_field = 1  where   type_code = 'workOrderReport' and module_code = 'base';
INSERT INTO dfs_label_module_config (type_code, module_code, module_name, have_all_field, form_full_path_code) values
("workOrderReport","reporterRecordVO.materialFields","物料基础信息",1,null);
update dfs_config_label_type set relate_type = 'reporterRecordVO' where type_code = 'workOrderReport';
delete from dfs_label_module_config where type_code = 'workOrderReport' and module_code = 'materialExtendField';
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.workOrderNumber', '生产工单号', '\$\{reporterRecordVO.workOrderNumber\}', 1, 'reporterRecordVO', 'businessTypeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.state', '状态', '\$\{reporterRecordVO.state\}', 1, 'reporterRecordVO', 'state');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.orderReportName', '报工类型', '\$\{reporterRecordVO.orderReportName\}', 1, 'reporterRecordVO', 'orderReportName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.processAssemblyCodeList', '工装', '\$\{reporterRecordVO.processAssemblyCodeList\}', 1, 'reporterRecordVO', 'processAssemblyCodeList');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.customerName', '客户名称', '\$\{reporterRecordVO.customerName\}', 1, 'reporterRecordVO', 'customerName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialCode', '产品编码', '\$\{reporterRecordVO.materialCode\}', 1, 'reporterRecordVO', 'materialCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.planQuantity', '计划数量', '\$\{reporterRecordVO.planQuantity\}', 1, 'reporterRecordVO', 'planQuantity');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.lossQuantity', '损耗数量', '\$\{reporterRecordVO.lossQuantity\}', 1, 'reporterRecordVO', 'lossQuantity');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.standardScaleFactor', '标准单重', '\$\{reporterRecordVO.standardScaleFactor\}', 1, 'reporterRecordVO', 'standardScaleFactor');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.scaleFactor', '单重', '\$\{reporterRecordVO.scaleFactor\}', 1, 'reporterRecordVO', 'scaleFactor');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.totalScale', '总重', '\$\{reporterRecordVO.totalScale\}', 1, 'reporterRecordVO', 'totalScale');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.productStandard', '产品规格', '\$\{reporterRecordVO.productStandard\}', 1, 'reporterRecordVO', 'productStandard');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.lineModelName', '制造单元类型', '\$\{reporterRecordVO.lineModelName\}', 1, 'reporterRecordVO', 'lineModelName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.lineName', '制造单元', '\$\{reporterRecordVO.lineName\}', 1, 'reporterRecordVO', 'lineName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.teamName', '班组', '\$\{reporterRecordVO.teamName\}', 1, 'reporterRecordVO', 'teamName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.counterPerReported', '计数器参考值', '\$\{reporterRecordVO.counterPerReported\}', 1, 'reporterRecordVO', 'counterPerReported');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.counterReported', '计数器累计参考值', '\$\{reporterRecordVO.counterReported\}', 1, 'reporterRecordVO', 'counterReported');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportTime', '报工开始时间', '\$\{reporterRecordVO.reportTime\}', 1, 'reporterRecordVO', 'reportTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportEndTime', '报工结束时间', '\$\{reporterRecordVO.reportEndTime\}', 1, 'reporterRecordVO', 'reportEndTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.deviceName', '设备', '\$\{reporterRecordVO.deviceName\}', 1, 'reporterRecordVO', 'deviceName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.shiftType', '班次', '\$\{reporterRecordVO.shiftType\}', 1, 'reporterRecordVO', 'shiftType');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.userNickname', '上报人', '\$\{reporterRecordVO.userNickname\}', 1, 'reporterRecordVO', 'userNickname');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.operator', '操作员', '\$\{reporterRecordVO.operator\}', 1, 'reporterRecordVO', 'operator');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.finishCountTwo', '上报完成数', '\$\{reporterRecordVO.finishCountTwo\}', 1, 'reporterRecordVO', 'finishCountTwo');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.batch', '批次', '\$\{reporterRecordVO.batch\}', 1, 'reporterRecordVO', 'batch');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportDate', '生产日期', '\$\{reporterRecordVO.reportDate\}', 1, 'reporterRecordVO', 'reportDate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.workCenterName', '工作中心', '\$\{reporterRecordVO.workCenterName\}', 1, 'reporterRecordVO', 'workCenterName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.name', '物料名称', '\$\{reporterRecordVO.materialFields.name\}', 1, 'reporterRecordVO.materialFields', 'name');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.skuEntity.skuName', '特征参数', '\$\{reporterRecordVO.skuEntity.skuName\}', 1, 'reporterRecordVO.materialFields', 'skuName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.sortName', '物料分类', '\$\{reporterRecordVO.materialFields.sortName\}', 1, 'reporterRecordVO.materialFields', 'sortName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.typeName', '物料类型', '\$\{reporterRecordVO.materialFields.typeName\}', 1, 'reporterRecordVO.materialFields', 'typeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.stateName', '物料状态', '\$\{reporterRecordVO.materialFields.stateName\}', 1, 'reporterRecordVO.materialFields', 'stateName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.comp', '物料单位', '\$\{reporterRecordVO.materialFields.comp\}', 1, 'reporterRecordVO.materialFields', 'comp');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.level', '物料等级', '\$\{reporterRecordVO.materialFields.level\}', 1, 'reporterRecordVO.materialFields', 'level');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.version', '物料版本', '\$\{reporterRecordVO.materialFields.version\}', 1, 'reporterRecordVO.materialFields', 'version');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.unit', '物料计量单位', '\$\{reporterRecordVO.materialFields.unit\}', 1, 'reporterRecordVO.materialFields', 'unit');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.qualityLevel', '物料质量等级', '\$\{reporterRecordVO.materialFields.qualityLevel\}', 1, 'reporterRecordVO.materialFields', 'qualityLevel');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.unitNumerator', '物料计量系数分子', '\$\{reporterRecordVO.materialFields.unitNumerator\}', 1, 'reporterRecordVO.materialFields', 'unitNumerator');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.unitDenominator', '物料计量系数分母', '\$\{reporterRecordVO.materialFields.unitDenominator\}', 1, 'reporterRecordVO.materialFields', 'unitDenominator');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.standard', '物料规格', '\$\{reporterRecordVO.materialFields.standard\}', 1, 'reporterRecordVO.materialFields', 'standard');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.remark', '物料备注', '\$\{reporterRecordVO.materialFields.remark\}', 1, 'reporterRecordVO.materialFields', 'remark');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.drawingNumber', '图号', '\$\{reporterRecordVO.materialFields.drawingNumber\}', 1, 'reporterRecordVO.materialFields', 'drawingNumber');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.materialPrice', '物料单价', '\$\{reporterRecordVO.materialFields.materialPrice\}', 1, 'reporterRecordVO.materialFields', 'materialPrice');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.loseRate', '损耗率', '\$\{reporterRecordVO.materialFields.loseRate\}', 1, 'reporterRecordVO.materialFields', 'loseRate');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.rawMaterial', '材质', '\$\{reporterRecordVO.materialFields.rawMaterial\}', 1, 'reporterRecordVO.materialFields', 'rawMaterial');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.nameEnglish', '物料英文名称', '\$\{reporterRecordVO.materialFields.nameEnglish\}', 1, 'reporterRecordVO.materialFields', 'nameEnglish');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.minimumProductionLot', '最小生产批量', '\$\{reporterRecordVO.materialFields.minimumProductionLot\}', 1, 'reporterRecordVO.materialFields', 'minimumProductionLot');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.factoryModel', '工厂型号', '\$\{reporterRecordVO.materialFields.factoryModel\}', 1, 'reporterRecordVO.materialFields', 'factoryModel');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportFieldOneName', '报工扩展字段1', '\$\{reporterRecordVO.reportFieldOneName\}', 1, 'reporterRecordVO', 'reportFieldOneName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportFieldTwoName', '报工扩展字段2', '\$\{reporterRecordVO.reportFieldTwoName\}', 1, 'reporterRecordVO', 'reportFieldTwoName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportFieldThreeName', '报工扩展字段3', '\$\{reporterRecordVO.reportFieldThreeName\}', 1, 'reporterRecordVO', 'reportFieldThreeName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportFieldFourName', '报工扩展字段4', '\$\{reporterRecordVO.reportFieldFourName\}', 1, 'reporterRecordVO', 'reportFieldFourName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.reportFieldFiveName', '报工扩展字段5', '\$\{reporterRecordVO.reportFieldFiveName\}', 1, 'reporterRecordVO', 'reportFieldFiveName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldOne', '扩展字段1', '\$\{reporterRecordVO.materialFields.customFieldOne\}', 1, 'reporterRecordVO.materialFields', 'customFieldOne');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldTwo', '扩展字段2', '\$\{reporterRecordVO.materialFields.customFieldTwo\}', 1, 'reporterRecordVO.materialFields', 'customFieldTwo');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldThree', '扩展字段3', '\$\{reporterRecordVO.materialFields.customFieldThree\}', 1, 'reporterRecordVO.materialFields', 'customFieldThree');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldFour', '扩展字段4', '\$\{reporterRecordVO.materialFields.customFieldFour\}', 1, 'reporterRecordVO.materialFields', 'customFieldFour');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldFive', '扩展字段5', '\$\{reporterRecordVO.materialFields.customFieldFive\}', 1, 'reporterRecordVO.materialFields', 'customFieldFive');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldSix', '扩展字段6', '\$\{reporterRecordVO.materialFields.customFieldSix\}', 1, 'reporterRecordVO.materialFields', 'customFieldSix');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldSeven', '扩展字段7', '\$\{reporterRecordVO.materialFields.customFieldSeven\}', 1, 'reporterRecordVO.materialFields', 'customFieldSeven');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldEight', '扩展字段8', '\$\{reporterRecordVO.materialFields.customFieldEight\}', 1, 'reporterRecordVO.materialFields', 'customFieldEight');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldNine', '扩展字段9', '\$\{reporterRecordVO.materialFields.customFieldNine\}', 1, 'reporterRecordVO.materialFields', 'customFieldNine');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldTen', '扩展字段10', '\$\{reporterRecordVO.materialFields.customFieldTen\}', 1, 'reporterRecordVO.materialFields', 'customFieldTen');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.materialFields.customFieldEleven', '扩展字段11', '\$\{reporterRecordVO.materialFields.customFieldEleven\}', 1, 'reporterRecordVO.materialFields', 'customFieldEleven');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.batchFinishCount', '批次完成数', '\$\{batchFinishCount\}', 1, 'reporterRecordVO', 'batchFinishCount');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.batchRemark', '批次备注', '\$\{batchRemark\}', 1, 'reporterRecordVO', 'batchRemark');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.count', '批次计划数', '\$\{count\}', 1, 'reporterRecordVO', 'count');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.lineReporter', '报工操作员', '\$\{lineReporter\}', 1, 'reporterRecordVO', 'lineReporter');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.lineReportFinishCount', '报工完成数量', '\$\{lineReportFinishCount\}', 1, 'reporterRecordVO', 'lineReportFinishCount');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.lineReportTime', '报工时间', '\$\{lineReportTime\}', 1, 'reporterRecordVO', 'lineReportTime');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.lineReportUnqualifiedCount', '报工不良数量', '\$\{lineReportUnqualifiedCount\}', 1, 'reporterRecordVO', 'lineReportUnqualifiedCount');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.scaleFactor1', '计量系数', '\$\{scaleFactor\}', 1, 'reporterRecordVO', 'scaleFactor');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.workOrderCustomerMaterialCode', '生产工单客户物料编码', '\$\{workOrderCustomerMaterialCode\}', 1, 'reporterRecordVO', 'workOrderCustomerMaterialCode');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.workOrderCustomerMaterialName', '生产工单客户物料名称', '\$\{workOrderCustomerMaterialName\}', 1, 'reporterRecordVO', 'workOrderCustomerMaterialName');
INSERT INTO dfs_config_label_info (code, name, placeholder, is_sys_field, module_code, form_field_code) VALUES('reporterRecordVO.workOrderCustomerSpecification', '生产工单客户物料规格', '\$\{workOrderCustomerSpecification\}', 1, 'reporterRecordVO', 'workOrderCustomerSpecification');

-- 采购订单物料行加入库状态字段
call proc_add_form_field_module("purchaseOrder.list.material", "storageStatusName", "入库状态",'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`,`module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/purchasing-list', 'purchaseOrder.list.material', 'purchaseOrder.list.material', 'storageStatusName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,'baseMaterialLineField');
-- 将字段配置materialsAppliedQuantity默认设置为不显示
update dfs_form_field_rule_config set  is_show = 0 where field_name_full_path_code = 'saleOrder.list.order' and field_code = 'materialsSalesQuantity';

-- 报工小程序SKU字段配置
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`, `is_dynamic_add`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.addReport', 'workOrderReport.addReport', 'skuName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 0, 0, 1, NULL, NULL, 1, 0, 'baseField', 0);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`, `is_dynamic_add`) VALUES ('yelink.workOrder.report.com', 'workOrderReport.editReport', 'workOrderReport.editReport', 'skuName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 0, 0, 0, NULL, NULL, 1, 0, 'baseField', 0);
call proc_add_form_field_module("workOrderReport.addReport", "skuName", "特征参数", "baseField");
call proc_add_form_field_module("workOrderReport.editReport", "skuName", "特征参数", "baseField");