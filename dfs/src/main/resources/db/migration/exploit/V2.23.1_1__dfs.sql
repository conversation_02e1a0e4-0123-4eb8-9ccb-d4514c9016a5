-- DDL
-- 工单加有效工时字段，有效工时=报工数量/标准产能
call proc_add_column(
        'dfs_work_order',
        'effective_working_hour',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `effective_working_hour` double(20, 2) NULL DEFAULT 0 COMMENT ''有效工时=报工数量/标准产能''');

call proc_add_column(
        'dfs_work_order',
        'auto_count',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `auto_count` double NULL DEFAULT 0 COMMENT ''计数器累计参考值''');

-- 新增 是否已打印 字段
call proc_add_column(
        'dfs_report_line',
        'is_print',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `is_print`  tinyint(4) default 0 COMMENT ''是否已打印'';');
call proc_add_column(
        'dfs_target_threshold',
        'extra_interface_enable',
        'ALTER TABLE `dfs_target_threshold` ADD COLUMN `extra_interface_enable` tinyint(4) NOT NULL DEFAULT 0 COMMENT ''附加接口开关''');

call proc_add_column(
        'dfs_material_replace',
        'feed_record_id',
        'ALTER TABLE `dfs_material_replace` ADD COLUMN `feed_record_id` int(11)  DEFAULT NULL COMMENT ''上料记录id''');
call proc_add_column(
        'dfs_target_threshold',
        'extra_interface_url',
        'ALTER TABLE `dfs_target_threshold` ADD COLUMN `extra_interface_url` text NULL COMMENT ''附加接口地址''');

-- 生产工单物料新增已领料数和已入库数
call proc_add_column(
        'dfs_work_order',
        'picking_quantity',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `picking_quantity`  double DEFAULT 0 COMMENT ''已领料数'';');

call proc_add_column(
        'dfs_work_order',
        'inventory_quantity',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `inventory_quantity`  double DEFAULT 0 COMMENT ''已入库数'';');
-- 维修时间加索引
call proc_add_column_index(
        'dfs_maintain_record',
        'create_time',
        'create_time');

call proc_add_column(
        'dfs_procedure_inspection_config',
        'is_defect',
        'ALTER TABLE `dfs_procedure_inspection_config` ADD COLUMN `is_defect`  tinyint(4) default 0 COMMENT ''是否关联不良'';');
call proc_add_column(
        'dfs_procedure_inspection_config',
        'defect_id',
        'ALTER TABLE `dfs_procedure_inspection_config` ADD COLUMN `defect_id`  int(11) DEFAULT NULL COMMENT ''不良定义id'';');

call proc_add_column(
        'dfs_procedure_def_inspection_config',
        'is_defect',
        'ALTER TABLE `dfs_procedure_def_inspection_config` ADD COLUMN `is_defect`  tinyint(4) default 0 COMMENT ''是否关联不良'';');
call proc_add_column(
        'dfs_procedure_def_inspection_config',
        'defect_id',
        'ALTER TABLE `dfs_procedure_def_inspection_config` ADD COLUMN `defect_id`  int(11) DEFAULT NULL COMMENT ''不良定义id'';');
-- 微服务注册信息表
CREATE TABLE IF NOT EXISTS `dfs_register_info`
(
    `register_service_name` varchar(100) NOT NULL COMMENT '注册服务',
    `register_time`         datetime DEFAULT NULL COMMENT '注册时间',
    UNIQUE KEY `register_service_name` (`register_service_name`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='微服务注册信息表';

-- 修改计数器自动计数为double类型
call proc_modify_column(
        'dfs_report_line',
        'auto_count',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `auto_count` double(11, 2) NULL DEFAULT NULL COMMENT ''采集器采集数量(上个采集时间点到当前时间)''');

-- DML
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10805120', '保存生效', 'process.inspection.item:saveAndEffect', NULL, NULL, NULL, NULL, '2023-09-27 06:54:41', 'enable', 'GET', '10805', 2, 1, 0, '/product-management/process-inspection-item', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10805130', '批量编辑', 'process.inspection.item:batchUpdate', NULL, NULL, NULL, NULL, '2023-09-27 06:54:41', 'enable', 'GET', '10805', 2, 1, 0, '/product-management/process-inspection-item', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10404090', '保存生效', 'quality.rework.define:saveAndEffect', NULL, NULL, NULL, NULL, '2023-09-27 06:54:41', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10404100', '批量编辑', 'quality.rework.define:batchUpdate', NULL, NULL, NULL, NULL, '2023-09-27 06:54:41', 'enable', 'GET', '10404', 2, 1, 0, '/qualities-manage/reworkQuality/define', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10401110', '保存生效', 'quality.definition:saveAndEffect', NULL, NULL, NULL, NULL, '2023-09-27 06:54:41', 'enable', 'DELETE', '10401', 2, 1, 0, '/qualities-manage/processQuality/qualityDefinition', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10401120', '批量编辑', 'quality.definition:batchUpdate', NULL, NULL, NULL, NULL, '2023-09-27 06:54:41', 'enable', 'DELETE', '10401', 2, 1, 0, '/qualities-manage/processQuality/qualityDefinition', 1, NULL, '');
call init_new_role_permission('10805120');
call init_new_role_permission('10805130');
call init_new_role_permission('10404090');
call init_new_role_permission('10404100');
call init_new_role_permission('10401110');
call init_new_role_permission('10401120');

INSERT INTO `dfs_config_label_type`(`type_code`, `type_name`, `apps`, `open_url`) VALUES ('device', '设备标签', '设备标签', NULL);
INSERT INTO `dfs_config_label_type`(`type_code`, `type_name`, `apps`, `open_url`) VALUES ('workOrderReport', '报工标签', '工单报工、订单报工', NULL);

INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'deviceCode');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'deviceName');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'deviceTypeName');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'deviceBrandModel');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'deviceRemark');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'devicePurchaseDate');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'deviceMagName');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'deviceMaintainer');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'devicePlace');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'supplierContactWay');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('device', 'supplierName');

INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('workOrderReport', 'count');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('workOrderReport', 'batchFinishCount');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('workOrderReport', 'batchRemark');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('workOrderReport', 'lineReportFinishCount');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('workOrderReport', 'lineReportUnqualifiedCount');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('workOrderReport', 'lineReportTime');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('workOrderReport', 'lineReporter');

INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('finished', 'batchFinishCount');

INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('deviceCode', '设备编码', "\$\{deviceCode\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('deviceName', '设备名称', "\$\{deviceName\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('deviceTypeName', '设备类型名称', "\$\{deviceTypeName\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('deviceBrandModel', '品牌型号', "\$\{deviceBrandModel\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('deviceRemark', '设备备注', "\$\{deviceRemark\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('devicePurchaseDate', '购置日期', "\$\{devicePurchaseDate\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('deviceMagName', '设备负责人', "\$\{deviceMagName\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('deviceMaintainer', '设备维护人', "\$\{deviceMaintainer\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('devicePlace', '区域位置', "\$\{devicePlace\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('supplierContactWay', '供应商联系方式', "\$\{supplierContactWay\}");

INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('batchFinishCount', '批次完成数', "\$\{batchFinishCount\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('lineReportFinishCount', '报工完成数量', "\$\{lineReportFinishCount\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('lineReportUnqualifiedCount', '报工不良数量', "\$\{lineReportUnqualifiedCount\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('lineReportTime', '报工时间', "\$\{lineReportTime\}");
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`) VALUES ('lineReporter', '报工操作员', "\$\{lineReporter\}");

UPDATE dfs_config_label_type SET `apps` = '工单报工、订单报工、工单批次' WHERE `type_code` = 'finished';
UPDATE dfs_config_label_info SET `name` = '批次计划数' WHERE `code` = 'count';

-- 生产订单修改计划开始、结束时间是否更新生产工单配置
INSERT INTO `dfs_business_config` VALUES (null, 'productOrderUpdateConfig', '生产订单更新配置', 'production.productOrderUpdateConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config` VALUES (null, 'updatePlaneTime', '更新计划开始、结束时间', 'production.productOrderUpdateConfig.updatePlaneTime', 'production.productOrderUpdateConfig', NULL, 'yelinkoncall', 'yelinkoncall', now(), now());
INSERT INTO `dfs_business_config_value` VALUES (null, 'updateWorkOrder', '是否更新生产工单计划开始、结束时间', 'production.productOrderUpdateConfig.updatePlaneTime.updateWorkOrder', 'production.productOrderUpdateConfig.updatePlaneTime', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'false');

-- 设备相关的按钮
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10301100', '下载默认模板', 'device:downTemplate', NULL, NULL, NULL, NULL, '2023-10-16 07:11:57', 'enable', 'GET', '10301', 2, 1, 0, '/equipment-management/device', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10301110', '导入数据', 'device:import-data', NULL, NULL, NULL, NULL, '2023-10-16 07:13:42', 'enable', 'POST', '10301', 2, 1, 0, '/equipment-management/device', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10301120', '查看日志', 'device:import-log', NULL, NULL, NULL, NULL, '2023-09-27 06:54:41', 'enable', 'GET', '10301', 2, 1, 0, '/equipment-management/device', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10301130', '导出列表', 'device:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:15:02', 'enable', 'POST', '10301', 2, 1, 0, '/equipment-management/device', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10302130', '导出列表', 'maintenance.order:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:18:26', 'enable', 'POST', '10302', 2, 1, 0, '/equipment-management/warranty', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10304120', '计划导出列表', 'device.inspect.plan:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:20:03', 'enable', 'POST', '10304', 2, 1, 0, '/equipment-management/deviceInspection/index', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10304130', '任务导出列表', 'device.inspect.task:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:20:24', 'enable', 'POST', '10304', 2, 1, 0, '/equipment-management/deviceInspection/index', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10305120', '计划导出列表', 'device.maintenance.plan:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:21:20', 'enable', 'POST', '10305', 2, 1, 0, '/equipment-management/deviceMaintenance/index', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10305130', '任务导出列表', 'device.maintenance.task:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:20:03', 'enable', 'POST', '10305', 2, 1, 0, '/equipment-management/deviceMaintenance/index', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10309120', '计划导出列表', 'device.detection.plan:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:22:28', 'enable', 'POST', '10309', 2, 1, 0, '/equipment-management/deviceDetection/index', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10309130', '任务导出列表', 'device.detection.task:exportList', NULL, NULL, NULL, NULL, '2023-10-16 07:21:20', 'enable', 'POST', '10309', 2, 1, 0, '/equipment-management/deviceDetection/index', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11601100', '下载默认模板', 'device:downTemplate', NULL, NULL, NULL, NULL, '2023-10-24 10:18:41', 'enable', 'GET', '11601', 2, 1, 0, '/equipment/device', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11601110', '导入数据', 'device:import-data', NULL, NULL, NULL, NULL, '2023-10-24 10:18:44', 'enable', 'POST', '11601', 2, 1, 0, '/equipment/device', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11601120', '查看日志', 'device:import-log', NULL, NULL, NULL, NULL, '2023-10-24 10:18:47', 'enable', 'GET', '11601', 2, 1, 0, '/equipment/device', 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11601130', '后台导出列表', 'device:exportList', NULL, NULL, NULL, NULL, '2023-10-24 10:18:51', 'enable', 'POST', '11601', 2, 1, 0, '/equipment/device', 1, NULL, '');

call init_new_role_permission('10301100');
call init_new_role_permission('10301110');
call init_new_role_permission('10301120');
call init_new_role_permission('10301130');
call init_new_role_permission('10302130');
call init_new_role_permission('10304120');
call init_new_role_permission('10304130');
call init_new_role_permission('10305120');
call init_new_role_permission('10305130');
call init_new_role_permission('10309120');
call init_new_role_permission('10309130');
call init_new_role_permission('11601100');
call init_new_role_permission('11601110');
call init_new_role_permission('11601120');
call init_new_role_permission('11601130');

-- 工艺工序附件
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10803450', '工艺工序附件导入', 'craftProcedureFileImport', NULL, NULL, NULL, NULL, '2023-10-16 08:57:12', 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1, NULL, '');
call init_new_role_permission('10803450');

-- 销售订单下推是否填写计划开始、结束时间
INSERT INTO `dfs_business_config_value` VALUES (null, 'isInputPlaneTime', '是否填写计划开始、完成时间', 'saleOrder.pushDownConfig.productOrder.bom.isInputPlaneTime', 'saleOrder.pushDownConfig.productOrder.bom', 'select', 'table', null,null, NULL, null, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
INSERT INTO `dfs_business_config_value` VALUES (null, 'isInputPlaneTime', '是否填写计划开始、完成时间', 'saleOrder.pushDownConfig.workOrder.craftRoute.isInputPlaneTime', 'saleOrder.pushDownConfig.workOrder.craftRoute', 'select', 'table', null,null, NULL, null, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', NULL);
-- 采购需求列配置方案配置权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090204130', '方案配置', 'purchasing.demand:schemeConfiguration', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1090204', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-demand', 1);
call init_new_role_permission('1090204130');

-- 采购需求列配置方案配置权限 按产品、按产线产品、产线不良、产线产品不良
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10412070', '不良汇总按产品方案配置', 'defectSummary:product', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '10412', 2, 1, 0, '/qualities-manage/qualityReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10412080', '不良汇总按产线+产品方案配置', 'defectSummary:lineProduct', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '10412', 2, 1, 0, '/qualities-manage/qualityReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10412090', '不良汇总按产线+不良项方案配置', 'defectSummary:lineBad', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '10412', 2, 1, 0, '/qualities-manage/qualityReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10412110', '不良汇总按产线+产品+不良项品方案配置', 'defectSummary:lineProductBad', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '10412', 2, 1, 0, '/qualities-manage/qualityReport', 1);
call init_new_role_permission('10412070');
call init_new_role_permission('10412080');
call init_new_role_permission('10412090');
call init_new_role_permission('10412110');


INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13005070', '不良汇总按产品方案配置', 'statementRecord:product', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '13005', 2, 1, 0, '/statementCenter/qualityReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13005080', '不良汇总按产线+产品方案配置', 'statementRecord:lineProduct', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '13005', 2, 1, 0, '/statementCenter/qualityReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13005090', '不良汇总按产线+不良项方案配置', 'statementRecord:lineBad', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '13005', 2, 1, 0, '/statementCenter/qualityReport', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13005110', '不良汇总按产线+产品+不良项品方案配置', 'statementRecord:lineProductBad', NULL, NULL, NULL, NULL, NOW(), 'enable', 'GET', '13005', 2, 1, 0, '/statementCenter/qualityReport', 1);
call init_new_role_permission('13005070');
call init_new_role_permission('13005080');
call init_new_role_permission('13005090');
call init_new_role_permission('13005110');

-- 销售订单“临近计划发货日期的标识”业务配置
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('nearPlanDeliveryDateFlag', '临近计划发货日期-是否标识(淡红色)', 'saleOrder.listConfig.listColor.nearPlanDeliveryDateFlag', 'saleOrder.listConfig.listColor', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false');
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('nearPlanDeliveryDateFlagNearDay', '临近计划发货日期-临近天数(非负整数)', 'saleOrder.listConfig.listColor.nearPlanDeliveryDateFlagNearDay', 'saleOrder.listConfig.listColor', 'input-number', 'input', NULL, NULL, NULL, NULL, NULL, '3');

-- 前端移除 报表中心-设备产量看板tab,故删除设备产量看板的路由和权限
delete from sys_route where path="/statementCenter/equipmentProduction";
delete from sys_permissions WHERE id='13002';
delete from sys_permissions WHERE parent_id='13002';

-- 增加异步导出后,报表中心-生产看板 下的权限十分混乱,所以把下面的权限全部删除,然后重新添加
delete from sys_permissions WHERE parent_id='13001';

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001010', '投入产出明细表_列配置_列表', 'inputOutputStatement:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001020', '投入产出明细表_导出_下载默认模板', 'inputOutputStatement:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001030', '投入产出明细表_导出_上传下载自定义导出模板', 'inputOutputStatement:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001040', '投入产出明细表_导出_导出Excel', 'inputOutputStatement:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001050', '投入产出明细表_导出_查看日志', 'inputOutputStatement:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001060', '生产日看板_列配置_列表', 'productionDaily:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001070', '生产日看板_导出_下载默认模板', 'productionDaily:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001080', '生产日看板_导出_上传下载自定义导出模板', 'productionDaily:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001090', '生产日看板_导出_导出Excel', 'productionDaily:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001100', '生产日看板_导出_查看日志', 'productionDaily:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001110', '生产周看板_列配置_列表', 'productionWeek:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001120', '生产周看板_导出_下载默认模板', 'productionWeek:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001130', '生产周看板_导出_上传下载自定义导出模板', 'productionWeek:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001140', '生产周看板_导出_导出Excel', 'productionWeek:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001150', '生产周看板_导出_查看日志', 'productionWeek:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001160', '生产月看板_列配置_列表', 'productionMonth:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001170', '生产月看板_导出_下载默认模板', 'productionMonth:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001180', '生产月看板_导出_上传下载自定义导出模板', 'productionMonth:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001190', '生产月看板_导出_导出Excel', 'productionMonth:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001200', '生产月看板_导出_查看日志', 'productionMonth:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001210', '设备产量看板_生产日报_列配置_列表', 'deviceProductionDaily:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001220', '设备产量看板_生产月报_列配置_列表', 'deviceProductionMonth:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001230', '生产报工明细表_列配置_列表', 'reporter:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001240', '生产报工明细表_导出_下载默认模板', 'reporter:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001250', '生产报工明细表_导出_上传下载自定义导出模板', 'reporter:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001260', '生产报工明细表_导出_导出Excel', 'reporter:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001270', '生产报工明细表_导出_查看日志', 'reporter:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001280', '单品过站明细表_列配置_列表', 'codeRecord:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001290', '单品过站明细表_导出_下载默认模板', 'codeRecord:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001300', '单品过站明细表_导出_上传下载自定义导出模板', 'codeRecord:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001310', '单品过站明细表_导出_导出Excel', 'codeRecord:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001320', '单品过站明细表_导出_查看日志', 'codeRecord:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001330', '生产工单明细表_列配置_列表', 'workOrderDetail:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001340', '生产工单明细表_导出_下载默认模板', 'workOrderDetail:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001350', '生产工单明细表_导出_上传下载自定义导出模板', 'workOrderDetail:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001360', '生产工单明细表_导出_导出Excel', 'workOrderDetail:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001370', '生产工单明细表_导出_查看日志', 'workOrderDetail:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001380', '生产绩效_列配置_列表', 'efficiency:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001390', '生产绩效_导出_下载默认模板', 'efficiency:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001400', '生产绩效_导出_上传下载自定义导出模板', 'efficiency:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001410', '生产绩效_导出_导出Excel', 'efficiency:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001420', '生产绩效_导出_查看日志', 'efficiency:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001430', '工序进度明细表_列配置_列表', 'procedureProgress:columnConfigList', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001440', '工序进度明细表_导出_下载默认模板', 'procedureProgress:export:defaultTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001450', '工序进度明细表_导出_上传下载自定义导出模板', 'procedureProgress:export:uploadTemplate', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001460', '工序进度明细表_导出_导出Excel', 'procedureProgress:export:excel', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('13001470', '工序进度明细表_导出_查看日志', 'procedureProgress:export:log', NOW(), 'enable', 'GET', '13001', 2, 1, 0, '/statementCenter/production', 1);
call init_new_role_permission('13001%');

-- 增加异步导出后,生产作业-生产看板 下的权限十分混乱,所以把下面的权限全部删除,然后重新添加
delete from sys_permissions WHERE parent_id='10104';

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104010', '生产工单明细表_列配置_列表', 'workOrderDetail:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104020', '生产工单明细表_导出_下载默认模板', 'workOrderDetail:export:defaultTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104030', '生产工单明细表_导出_上传下载自定义导出模板', 'workOrderDetail:export:uploadTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104040', '生产工单明细表_导出_导出Excel', 'workOrderDetail:export:excel', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104050', '生产工单明细表_导出_查看日志', 'workOrderDetail:export:log', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104060', '生产报工明细表_列配置_列表', 'reporter:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104070', '生产报工明细表_导出_下载默认模板', 'reporter:export:defaultTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104080', '生产报工明细表_导出_上传下载自定义导出模板', 'reporter:export:uploadTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104090', '生产报工明细表_导出_导出Excel', 'reporter:export:excel', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104100', '生产报工明细表_导出_查看日志', 'reporter:export:log', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104110', '单品过站明细表_列配置_列表', 'codeRecord:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104120', '单品过站明细表_导出_下载默认模板', 'codeRecord:export:defaultTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104130', '单品过站明细表_导出_上传下载自定义导出模板', 'codeRecord:export:uploadTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104140', '单品过站明细表_导出_导出Excel', 'codeRecord:export:excel', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104150', '单品过站明细表_导出_查看日志', 'codeRecord:export:log', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104160', '生产工单绩效明细_列配置_列表', 'efficiency:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104170', '生产工单绩效明细_导出_下载默认模板', 'efficiency:export:defaultTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104180', '生产工单绩效明细_导出_上传下载自定义导出模板', 'efficiency:export:uploadTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104190', '生产工单绩效明细_导出_导出Excel', 'efficiency:export:excel', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104200', '生产工单绩效明细_导出_查看日志', 'efficiency:export:log', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104210', '投入产出明细表_列配置_列表', 'inputOutputStatement:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104220', '投入产出明细表_导出_下载默认模板', 'inputOutputStatement:export:defaultTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104230', '投入产出明细表_导出_上传下载自定义导出模板', 'inputOutputStatement:export:uploadTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104240', '投入产出明细表_导出_导出Excel', 'inputOutputStatement:export:excel', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104250', '投入产出明细表_导出_查看日志', 'inputOutputStatement:export:log', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104260', '工序进度明细表_列配置_列表', 'procedureProgress:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104270', '工序进度明细表_导出_下载默认模板', 'procedureProgress:export:defaultTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104280', '工序进度明细表_导出_上传下载自定义导出模板', 'procedureProgress:export:uploadTemplate', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104290', '工序进度明细表_导出_导出Excel', 'procedureProgress:export:excel', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104300', '工序进度明细表_导出_查看日志', 'procedureProgress:export:log', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `create_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('10104310', '生产工单日计划_列配置_列表', 'workOrderDailyPlan:columnConfigList', NOW(), 'enable', 'GET', '10104', 2, 1, 0, '/workorder-model/product-report/index', 1);
call init_new_role_permission('10104%');

-- 销售订单按钮权限补充
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`)
VALUES ('10001360', '批量删除', 'sales.order:BatchDelete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1, NULL);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`)
VALUES ('1090102320', '批量删除', 'sales.order:BatchDelete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1, NULL);
 call init_new_role_permission('10001360');
 call init_new_role_permission('1090102320');
-- 生产订单按钮权限补充
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
 VALUES ('10003460', '批量删除', 'product.order:BatchDelete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
 call init_new_role_permission('10003460');
-- 采购订单方案配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES ('1090205210', '方案配置', 'purchasing.list:schemeConfiguration', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
 call init_new_role_permission('1090205210');

--
INSERT INTO `sys_route` (`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES ('/system_settings/demo-data-config', '/system_settings', 'demoDataConfig', NULL, '演示数据配置', NULL, 'dfs', NULL, NULL, NULL, 10, 0, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('12230', '演示数据配置', '/system_settings/demo-data-config', NULL, NULL, NULL, NULL, '2023-10-23 06:50:23', 'enable', 'GET', '122', 1, 1, 1, '/system_settings', 1, NULL, '');
call init_new_role_permission('12230');

-- 生产工单
call proc_add_column(
        'dfs_work_order',
        'project_contract',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `project_contract`  tinyint DEFAULT 0 COMMENT ''存在项目合同'';');

-- 下推跳转页面改成新建页签
UPDATE dfs_business_config   SET  name = REPLACE(name,'跳转新页面方式','新建页签')  where  name like  '%(跳转新页面方式)%';
