set autocommit = 0;

-- <PERSON>L
call proc_modify_column(
        'dfs_device_state_duration_day_union',
        'time',
        'ALTER TABLE `dfs_device_state_duration_day_union` MODIFY COLUMN `time` date NULL DEFAULT NULL COMMENT ''时间'' AFTER `stop`');

call proc_modify_column(
        'dfs_device_state_duration_day_union',
        'running',
        'ALTER TABLE `dfs_device_state_duration_day_union` MODIFY COLUMN `running` int(11) NULL DEFAULT NULL COMMENT ''工位运行时长（分钟）'' AFTER `device_id`');

call proc_modify_column(
        'dfs_device_state_duration_day_union',
        'pause',
        'ALTER TABLE `dfs_device_state_duration_day_union` MODIFY COLUMN `pause` int(11) NULL DEFAULT NULL COMMENT ''产线暂停时长（分钟）'' AFTER `running`');
call proc_modify_column(
        'dfs_device_state_duration_day_union',
        'stop',
        'ALTER TABLE `dfs_device_state_duration_day_union` MODIFY COLUMN `stop` int(11) NULL DEFAULT NULL COMMENT ''产线停止时长（分钟）'' AFTER `pause`');




commit;
