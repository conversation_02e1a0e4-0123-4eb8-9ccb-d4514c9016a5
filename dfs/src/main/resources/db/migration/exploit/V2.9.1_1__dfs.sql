-- DDL
-- 工序表 删除无用字段
call proc_modify_column(
        'dfs_procedure',
        'craft_id',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `craft_id`');
call proc_modify_column(
        'dfs_procedure',
        'craft_code',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `craft_code`');
call proc_modify_column(
        'dfs_procedure',
        'procedure_path_code',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `procedure_path_code`');
call proc_modify_column(
        'dfs_procedure',
        'model_id',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `model_id`');
call proc_modify_column(
        'dfs_procedure',
        'model_name',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `model_name`');
call proc_modify_column(
        'dfs_procedure',
        'parallel',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `parallel`');
call proc_modify_column(
        'dfs_procedure',
        'last_procedure',
        '<PERSON>TER TABLE `dfs_procedure` DROP COLUMN `last_procedure`');
call proc_modify_column(
        'dfs_procedure',
        'appendix_url',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `appendix_url`');
call proc_modify_column(
        'dfs_procedure',
        'procedure_desc',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `procedure_desc`');
call proc_modify_column(
        'dfs_procedure',
        'img_url',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `img_url`');
call proc_modify_column(
        'dfs_procedure',
        'sop_code',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `sop_code`');
call proc_modify_column(
        'dfs_procedure',
        'working_hours',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `working_hours`');
call proc_modify_column(
        'dfs_procedure',
        'theory_efficiency',
        'ALTER TABLE `dfs_procedure` DROP COLUMN `theory_efficiency`');


-- 工单删除无用字段
call proc_modify_column(
        'dfs_work_order',
        'craft_code',
        'ALTER TABLE `dfs_work_order` DROP COLUMN `craft_code`');
call proc_modify_column(
        'dfs_work_order',
        'craft_name',
        'ALTER TABLE `dfs_work_order` DROP COLUMN `craft_name`');
call proc_modify_column(
        'dfs_work_order',
        'craft_version',
        'ALTER TABLE `dfs_work_order` DROP COLUMN `craft_version`');

-- 新增工序定义 的 检验项和不良定义关联表
CREATE TABLE IF NOT EXISTS `dfs_procedure_def_inspection_config`
(
    `id`                      int(11) NOT NULL AUTO_INCREMENT,
    `procedure_code`          varchar(255) DEFAULT NULL COMMENT '工序定义编号',
    `procedure_inspection_id` int(11)      DEFAULT NULL COMMENT '工序检验项id',
    `description`             varchar(255) DEFAULT NULL COMMENT '检验说明',
    `inspect_type`            varchar(100) DEFAULT NULL COMMENT '检验类型 (selfInspection--自检、mutualInspection--互检、specialInspection--专检)',
    `comparator`              varchar(255) DEFAULT NULL COMMENT '比较符',
    `standard_value`          varchar(255) DEFAULT NULL COMMENT '标准值',
    `upper_limit`             varchar(255) DEFAULT NULL COMMENT '上限值',
    `down_limit`              varchar(255) DEFAULT NULL COMMENT '下限值',
    `create_by`               varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by`               varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time`             datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`             datetime     DEFAULT NULL COMMENT '修改时间',
    `default_value`           varchar(255) DEFAULT NULL COMMENT '默认值',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `procedure_code` (`procedure_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 21
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='工序定义检验配置表';

CREATE TABLE IF NOT EXISTS `dfs_procedure_def_defect_define`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `procedure_code`   varchar(255) DEFAULT NULL COMMENT '工序定义编号',
    `defect_define_id` int(11)      DEFAULT NULL COMMENT '不良项目id',
    `remark`           varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by`        varchar(255) DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='工序定义不良项目关联表';

-- 工艺工序的校验表新增 检验类型字段
call proc_add_column(
        'dfs_procedure_inspection_config',
        'inspect_type',
        'ALTER TABLE `dfs_procedure_inspection_config` ADD COLUMN `inspect_type` varchar(100) NULL DEFAULT null COMMENT ''检验类型 (selfInspection--自检、mutualInspection--互检、specialInspection--专检)''');

-- 添加父模板ID字段
call proc_add_column(
        'dfs_bom',
        'parent_template_id',
        'ALTER TABLE `dfs_bom` ADD COLUMN `parent_template_id` int(11) NULL DEFAULT null COMMENT ''父模板ID''');
call proc_add_column(
        'dfs_craft',
        'parent_template_id',
        'ALTER TABLE `dfs_craft` ADD COLUMN `parent_template_id` int(11) NULL DEFAULT null COMMENT ''父模板ID''');
-- 添加是否是模板字段
call proc_add_column(
        'dfs_bom',
        'is_template',
        'ALTER TABLE `dfs_bom` ADD COLUMN `is_template` tinyint(1) NULL DEFAULT 0 COMMENT ''是否是模板，默认否''');
call proc_add_column(
        'dfs_craft',
        'is_template',
        'ALTER TABLE `dfs_craft` ADD COLUMN `is_template` tinyint(1) NULL DEFAULT 0 COMMENT ''是否是模板，默认否''');

-- 扫码规则配置表
CREATE TABLE IF NOT EXISTS `dfs_scan_code_rule`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `code`        varchar(255) DEFAULT NULL COMMENT 'code',
    `value`       text         DEFAULT NULL COMMENT '规则内容',
    `remark`      varchar(255) DEFAULT NULL COMMENT '备注',
    `state`       int(11)      DEFAULT NULL COMMENT '状态（1-启用2-停用）',
    `create_by`   varchar(255) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(255) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime     DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 19
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='扫码规则配置表';
call proc_modify_column(
        'dfs_bom',
        'code',
        'ALTER TABLE `dfs_bom` MODIFY COLUMN `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT ''物料编码（根据生成规则生成或输入）''');
call proc_modify_column(
        'dfs_bom_raw_material',
        'code',
        'ALTER TABLE `dfs_bom_raw_material` MODIFY COLUMN `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT ''物料编码''');
-- 扫码记录表 加索引
call proc_add_column_index(
        'dfs_product_flow_code_record',
        'fac_id',
        'fac_id');

-- 物料表新增编制人字段
call proc_add_column(
        'dfs_material',
        'editor',
        'ALTER TABLE `dfs_material` ADD COLUMN `editor` varchar(255) NULL COMMENT ''编制人''');

-- BOM表新增编制人字段
call proc_add_column(
        'dfs_bom',
        'editor',
        'ALTER TABLE `dfs_bom` ADD COLUMN `editor` varchar(255) NULL COMMENT ''编制人''');

-- 工艺表新增编制人字段
call proc_add_column(
        'dfs_craft',
        'editor',
        'ALTER TABLE `dfs_craft` ADD COLUMN `editor` varchar(255) NULL COMMENT ''编制人''');

-- 附件表新增编制人字段
call proc_add_column(
        'dfs_appendix',
        'editor',
        'ALTER TABLE `dfs_appendix` ADD COLUMN `editor` varchar(255) NULL COMMENT ''编制人''');


-- 物料表新增编制人字段
call proc_add_column(
        'dfs_material',
        'editor_name',
        'ALTER TABLE `dfs_material` ADD COLUMN `editor_name` varchar(255) NULL COMMENT ''编制人姓名''');

-- BOM表新增编制人字段
call proc_add_column(
        'dfs_bom',
        'editor_name',
        'ALTER TABLE `dfs_bom` ADD COLUMN `editor_name` varchar(255) NULL COMMENT ''编制人姓名''');

-- 工艺表新增编制人字段
call proc_add_column(
        'dfs_craft',
        'editor_name',
        'ALTER TABLE `dfs_craft` ADD COLUMN `editor_name` varchar(255) NULL COMMENT ''编制人姓名''');

-- 附件表新增编制人字段
call proc_add_column(
        'dfs_appendix',
        'editor_name',
        'ALTER TABLE `dfs_appendix` ADD COLUMN `editor_name` varchar(255) NULL COMMENT ''编制人姓名''');

-- 工序附件表新增编制人字段
call proc_add_column(
        'dfs_procedure_file',
        'editor_name',
        'ALTER TABLE `dfs_procedure_file` ADD COLUMN `editor_name` varchar(255) NULL COMMENT ''编制人姓名''');

-- 工序附件表新增编制人字段
call proc_add_column(
        'dfs_procedure_file',
        'editor',
        'ALTER TABLE `dfs_procedure_file` ADD COLUMN `editor` varchar(255) NULL COMMENT ''编制人''');

-- 报工表加索引
call proc_drop_column_index('dfs_report_line','report_date','union_key');
call proc_add_column_index('dfs_report_line','work_order','work_order');
call proc_add_column_index('dfs_report_line','type','type');
call proc_add_column_index('dfs_report_line','report_date','report_date');

-- 工单每日计数加索引
call proc_drop_column_index('dfs_record_work_order_day_count','work_order_number','num_time');
call proc_add_column_index('dfs_record_work_order_day_count','work_order_number','work_order_number');
call proc_add_column_index('dfs_record_work_order_day_count','time','time');

-- 每日能耗表加索引
call proc_add_column_index('dfs_device_energy_consumption','record_date','record_date');
-- 每日用气表加索引
call proc_add_column_index('dfs_device_gas_energy_consumption','record_date','record_date');
-- 设备每日数据集合表加索引
call proc_add_column_index('dfs_record_device_day_union','record_date','record_date');
-- 每日报工记录加索引
call proc_add_column_index('dfs_report_day_count','report_date','report_date');

-- 工单关联工艺工序表新增 工艺id和工艺code 字段
call proc_add_column(
        'dfs_work_order_procedure_relation',
        'craft_id',
        'ALTER TABLE `dfs_work_order_procedure_relation` ADD COLUMN `craft_id`  int NULL COMMENT ''工艺id'' AFTER `procedure_name`;');
call proc_add_column(
        'dfs_work_order_procedure_relation',
        'craft_code',
        'ALTER TABLE `dfs_work_order_procedure_relation` ADD COLUMN `craft_code`  varchar(255) NULL COMMENT ''工艺code'' AFTER `craft_id`;');
-- 工单关联工艺工序表原始数据处理
UPDATE dfs_work_order_procedure_relation
SET craft_id = (SELECT craft_id FROM dfs_craft_procedure craftProcedure WHERE craftProcedure.id = craft_procedure_id),
    craft_code = (SELECT dfs_craft.craft_code FROM dfs_craft WHERE craft_id = (SELECT craft_id FROM dfs_craft_procedure craftProcedure WHERE craftProcedure.id = craft_procedure_id));


CREATE TABLE IF NOT EXISTS `dfs_device_energy_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `model_id` int(11) DEFAULT NULL COMMENT '设备模型id',
  `type` varchar(255) DEFAULT NULL COMMENT '类型 energy-耗电 gas-耗气',
  `target_name` varchar(255) DEFAULT NULL COMMENT '用于计算能耗的指标名',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `model_id` (`model_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='设备能耗指标配置表';


-- 新增上传模块文件记录表
CREATE TABLE IF NOT EXISTS `dfs_model_upload_file` (
  `id` int NOT NULL AUTO_INCREMENT,
  `model_type` int DEFAULT NULL COMMENT '上传模块类型',
  `file_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '文件名',
  `file_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '文件地址',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `upload_person` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '上传人',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='上传模块文件记录表';

-- DML
-- 新增bom模板权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808', 'BOM模板', '/product-management/bomTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '108', 1, 1, 0, '/product-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808010', '新增', 'bom.template:add', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808020', '编辑', 'bom.template:update', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808030', '详情', 'bom.template:detail', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808040', '删除', 'bom.template:delete', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808050', '审批', 'bom.template:approval', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808060', '下推', 'bom.template:pushDown', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
-- 新增工艺模板权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809', '工艺模板', '/product-management/technologyTemplate', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '108', 1, 1, 0, '/product-management', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809010', '新增', 'technology.template:add', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809020', '编辑', 'technology.template:update', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809030', '详情', 'technology.template:detail', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809040', '删除', 'technology.template:delete', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809050', '审批', 'technology.template:approval', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809060', '下推', 'technology.template:pushDown', NULL, NULL, NULL, NULL, '2022-12-09 08:44:13', 'enable', 'POST', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);

delete from dfs_approve_config where code in ('bomTemplate','craftTemplate');
INSERT INTO `dfs_approve_config`(`id`, `code`, `name`, `is_approve`, `update_by`, `update_time`) VALUES (null, 'bomTemplate', 'BOM模板', 0, 'yelinkoncall', '2022-04-25 17:43:05');
INSERT INTO `dfs_approve_config`(`id`, `code`, `name`, `is_approve`, `update_by`, `update_time`) VALUES (null, 'craftTemplate', '工艺模板', 0, 'yelinkoncall', '2022-04-25 17:43:05');


INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12007', '解码规则', '/documents-config/decode-rule', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '120', 1, 1, 1, '/documents-config', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12007010', '新增', 'decode.rule:add', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'POST', '12007', 2, 1, 1, '/documents-config/decode-rule', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12007020', '编辑', 'decode.rule:update', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '12007', 2, 1, 1, '/documents-config/decode-rule', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12007030', '删除', 'decode.rule:delete', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'DELETE', '12007', 2, 1, 1, '/documents-config/decode-rule', 1);

-- 全局列配置权限按钮
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001150', '列配置_列表', 'sales.order:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 02:47:36', 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001160', '列配置_新增_物料信息', 'sales.order:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-01-31 02:47:43', 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001170', '列配置_编辑_物料信息', 'sales.order:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-01-31 02:47:43', 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001180', '列配置_详情_物料信息', 'sales.order:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-01-31 02:47:43', 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10002100', '列配置_列表', 'auxiliary.plan:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 07:13:06', 'enable', 'GET', '10002', 2, 1, 0, '/order-model/auxiliary_plan', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003180', '列配置_列表', 'product.order:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 06:49:28', 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003190', '列配置_新增_物料信息', 'product.order:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-01-31 06:49:28', 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003200', '列配置_编辑_物料信息', 'product.order:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-01-31 06:49:28', 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003210', '列配置_详情_物料信息', 'product.order:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-01-31 06:49:28', 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10004090', '列配置_列表', 'workorder.scheduling:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 07:15:54', 'enable', 'GET', '10004', 2, 1, 0, '/order-model/workorder-scheduling', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005200', '列配置_列表', 'production.workorder:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 08:25:26', 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005210', '列配置_新增_物料信息', 'production.workorder:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-01-31 07:43:36', 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005220', '列配置_编辑_物料信息', 'production.workorder:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-01-31 10:02:45', 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005230', '列配置_详情_物料信息', 'production.workorder:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-01-31 10:02:45', 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006070', '列配置_列表', 'production.materials:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 07:10:35', 'enable', 'GET', '10006', 2, 1, 0, '/order-model/production-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101200', '列配置_列表', 'production.workorder.model:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 10:48:14', 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101210', '列配置_新增_物料信息', 'production.workorder.model:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-01-31 10:48:08', 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101220', '列配置_编辑_物料信息', 'production.workorder.model:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-01-31 10:48:17', 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101230', '列配置_详情_物料信息', 'production.workorder.model:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-01-31 10:48:19', 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10109010', '列配置_列表', 'production.records:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 08:52:26', 'enable', 'GET', '10109', 2, 1, 0, '/workorder-model/production-records', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10110020', '列配置_列表', 'workorder.records:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 08:52:59', 'enable', 'GET', '10110', 2, 1, 0, '/workorder-model/workorder-records', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10206010', '列配置_列表', 'order.transparency:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 08:58:03', 'enable', 'GET', '10206', 2, 1, 0, '/trace/order-transparency', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10301050', '列配置_列表', 'device:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 09:00:53', 'enable', 'GET', '10301', 2, 1, 0, '/equipment-management/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402090', '列配置_检测报表', 'processQuality.statement:columnConfigForm', NULL, NULL, NULL, NULL, '2023-02-01 02:16:06', 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402100', '列配置_检测记录', 'processQuality.statement:columnConfigRecord', NULL, NULL, NULL, NULL, '2023-01-31 09:53:19', 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405090', '列配置_返工报表', 'return:statement:columnConfigForm', NULL, NULL, NULL, NULL, '2023-02-01 02:19:35', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405100', '列配置_返工记录', 'return:statement:columnConfigRecord', NULL, NULL, NULL, NULL, '2023-02-01 02:19:40', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10409090', '列配置_列表', 'product.inspection.plan:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 01:37:21', 'enable', 'GET', '10409', 2, 1, 0, '/qualities-manage/productInspection/plan', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10410190', '列配置_产品检测结果', 'product.inspection.report:columnConfigResult', NULL, NULL, NULL, NULL, '2023-02-01 02:40:22', 'enable', 'GET', '10410', 2, 1, 0, '/qualities-manage/productInspection', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10801150', '列配置_列表', 'material:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 10:27:20', 'enable', 'GET', '10801', 2, 1, 0, '/product-management/supplies', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10802130', '列配置_列表', 'bom:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 11:05:15', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10802140', '列配置_新增_物料信息', 'bom:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-01-31 11:05:15', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10802150', '列配置_编辑_物料信息', 'bom:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-01-31 11:05:15', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10802160', '列配置_详情_物料信息', 'bom:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-01-31 11:05:15', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10803350', '列配置_列表', 'technology:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:38:52', 'enable', 'GET', '10803', 2, 1, 0, '/product-management/technology', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804120', '列配置_列表', 'procedure:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 11:14:33', 'enable', 'GET', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806160', '列配置_列表', 'replace.scheme:columnConfigList', NULL, NULL, NULL, NULL, '2023-01-31 11:03:00', 'enable', 'GET', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806090', '列配置_新增_主物料', 'replace.scheme:columnConfigAddMain', NULL, NULL, NULL, NULL, '2023-01-31 11:03:00', 'enable', 'GET', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806100', '列配置_编辑_主物料', 'replace.scheme:columnConfigEditMain', NULL, NULL, NULL, NULL, '2023-01-31 11:03:00', 'enable', 'GET', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806110', '列配置_详情_主物料', 'replace.scheme:columnConfigDetailMain', NULL, NULL, NULL, NULL, '2023-01-31 11:03:00', 'enable', 'GET', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806120', '列配置_新增_替代物料', 'replace.scheme:columnConfigAddReplace', NULL, NULL, NULL, NULL, '2023-01-31 11:03:00', 'enable', 'GET', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806130', '列配置_编辑_替代物料', 'replace.scheme:columnConfigEditReplace', NULL, NULL, NULL, NULL, '2023-01-31 11:03:00', 'enable', 'GET', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806140', '列配置_详情_替代物料', 'replace.scheme:columnConfigDetailReplace', NULL, NULL, NULL, NULL, '2023-01-31 11:03:00', 'enable', 'GET', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090101060', '列配置_列表', 'client.file:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 01:11:53', 'enable', 'GET', '1090101', 2, 1, 0, '/supply-chain-collaboration/order-model/clientFile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102150', '列配置_列表', 'sales.order.supply:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:07:11', 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102160', '列配置_新增_物料信息', 'sales.order.supply:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-02-01 04:07:11', 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102170', '列配置_编辑_物料信息', 'sales.order.supply:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-02-01 04:07:11', 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102180', '列配置_详情_物料信息', 'sales.order.supply:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-02-01 04:07:11', 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201070', '列配置_列表', 'supplier.profile:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 02:51:50', 'enable', 'GET', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090204070', '列配置_列表', 'purchasing.demand:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 02:54:40', 'enable', 'GET', '1090204', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-demand', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090205080', '列配置_列表', 'purchasing.list:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 02:57:36', 'enable', 'GET', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206070', '列配置_列表', 'delivery.order:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:05:21', 'enable', 'GET', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208070', '列配置_列表', 'return:application:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:08:39', 'enable', 'GET', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090301050', '列配置_列表', 'inbound.record:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 06:35:24', 'enable', 'GET', '1090301', 2, 1, 0, '/warehousingDistribution/record-query/inbound-record', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090302060', '列配置_列表', 'batch:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 07:05:34', 'enable', 'GET', '1090302', 2, 1, 0, '/supply-chain-collaboration/batch', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090303060', '列配置_列表', 'factory.record:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 07:05:10', 'enable', 'GET', '1090303', 2, 1, 0, '/warehousingDistribution/record-query/factory-record', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090304060', '列配置_列表', 'access.record:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 07:05:10', 'enable', 'GET', '1090304', 2, 1, 0, '/warehousingDistribution/record-query/access-record', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090305060', '列配置_列表', 'material.allocation:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 06:41:21', 'enable', 'GET', '1090305', 2, 1, 0, '/warehousingDistribution/record-query/material-allocation', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090306030', '列配置_列表', 'inventory.warehousing:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 08:53:34', 'enable', 'GET', '1090306', 2, 1, 0, '/warehousingDistribution/record-query/inventory', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090307050', '列配置_列表', 'warehouse.manage:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:55:50', 'enable', 'GET', '1090307', 2, 1, 0, '/warehousingDistribution/record-query/warehouse-manage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001070', '列配置_列表', 'purchasePutInStorage:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:00:53', 'enable', 'GET', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002070', '列配置_列表', 'purchaseReturnMaterialOutOfStorage:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:02:12', 'enable', 'GET', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101070', '列配置_列表', 'workOrderComplete:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:09:22', 'enable', 'GET', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102080', '列配置_列表', 'workOrderTakeOut:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:11:15', 'enable', 'GET', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103080', '列配置_列表', 'workOrderSupplement:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 07:05:34', 'enable', 'GET', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104070', '列配置_列表', 'applicationReturn:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:14:03', 'enable', 'GET', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090313080', '列配置_列表', 'shelves.define:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:58:00', 'enable', 'GET', '1090313', 2, 1, 0, '/warehousingDistribution/shelves-definition', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031401070', '列配置_列表', 'salesIssueDoc:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:15:25', 'enable', 'GET', '109031401', 2, 1, 0, '/warehousingDistribution/salesIssueReceipt/salesIssueDoc', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031402070', '列配置_列表', 'salesMaterialReturnReceiptDoc:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:17:17', 'enable', 'GET', '109031402', 2, 1, 0, '/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031501070', '列配置_列表', 'subcontracting.order:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:18:59', 'enable', 'GET', '109031501', 2, 1, 0, '/warehousingDistribution/subcontracting/order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031502070', '列配置_列表', 'outstoreMaterail:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:20:49', 'enable', 'GET', '109031502', 2, 1, 0, '/warehousingDistribution/subcontracting/outstoreMaterail', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031503070', '列配置_列表', 'repairMaterail:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:21:58', 'enable', 'GET', '109031503', 2, 1, 0, '/warehousingDistribution/subcontracting/repairMaterail', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031504070', '列配置_列表', 'refundMaterail:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:22:50', 'enable', 'GET', '109031504', 2, 1, 0, '/warehousingDistribution/subcontracting/refundMaterail', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031601060', '列配置_列表', 'inventory:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:24:23', 'enable', 'GET', '109031601', 2, 1, 0, '/warehousingDistribution/cougnyManage/inventory', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031602060', '列配置_列表', 'allocation:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:25:36', 'enable', 'GET', '109031602', 2, 1, 0, '/warehousingDistribution/cougnyManage/allocation', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031701070', '列配置_列表', 'stockApplication:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:27:34', 'enable', 'GET', '109031701', 2, 1, 0, '/warehousingDistribution/mixReceive/stockApplication', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031702070', '列配置_列表', 'issueRequisition:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:26:36', 'enable', 'GET', '109031702', 2, 1, 0, '/warehousingDistribution/mixReceive/issueRequisition', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031703060', '列配置_列表', 'warehousing:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:28:54', 'enable', 'GET', '109031703', 2, 1, 0, '/warehousingDistribution/mixReceive/warehousing', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031704060', '列配置_列表', 'issue:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 04:29:58', 'enable', 'GET', '109031704', 2, 1, 0, '/warehousingDistribution/mixReceive/issue', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090401070', '列配置_列表', 'outsourcingManagement.outsourcingOrder:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:43:02', 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090401080', '列配置_新增_物料信息', 'outsourcingManagement.outsourcingOrder:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-02-01 03:43:02', 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090401090', '列配置_编辑_物料信息', 'outsourcingManagement.outsourcingOrder:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-02-01 03:43:02', 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090401100', '列配置_详情_物料信息', 'outsourcingManagement.outsourcingOrder:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-02-01 03:43:02', 'enable', 'GET', '1090401', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090402070', '列配置_列表', 'outsourcingManagement.outsourcingReceiptOrder:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:47:17', 'enable', 'GET', '1090402', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090403070', '列配置_列表', 'outsourcingManagement.outsourcingMaterialOrder:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 03:48:37', 'enable', 'GET', '1090403', 2, 1, 0, '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11601050', '列配置_列表', 'device.equipment:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 07:05:01', 'enable', 'GET', '11601', 2, 1, 1, '/equipment/device', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12102050', '列配置_列表', 'user:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-01 06:56:55', 'enable', 'GET', '12102', 2, 1, 1, '/base-info/user', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10302100', '列配置_设备知识库', 'maintenance.knowledge:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-07 02:38:17', 'enable', 'GET', '10302', 2, 1, 0, '/equipment-management/warranty', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102190', '列配置_详情_生产信息', 'sales.order.supply:columnConfigDetailProduction', NULL, NULL, NULL, NULL, '2023-02-01 04:07:11', 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090102200', '列配置_编辑_生产信息', 'sales.order.supply:columnConfigEditProduction', NULL, NULL, NULL, NULL, '2023-02-01 04:07:11', 'enable', 'GET', '1090102', 2, 1, 0, '/supply-chain-collaboration/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001190', '列配置_详情_生产信息', 'sales.order:columnConfigDetailProduction', NULL, NULL, NULL, NULL, '2023-01-31 02:47:43', 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10001200', '列配置_编辑_生产信息', 'sales.order:columnConfigEditProduction', NULL, NULL, NULL, NULL, '2023-01-31 02:47:43', 'enable', 'GET', '10001', 2, 1, 0, '/order-model/salesOrder', 1);
-- 工艺 BOM 模板列配置按钮
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808070', '列配置_列表', 'bom.template:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-02 14:32:32', 'enable', 'GET', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808080', '列配置_新增_物料信息', 'bom.template:columnConfigAddMaterial', NULL, NULL, NULL, NULL, '2023-02-02 14:32:32', 'enable', 'GET', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808090', '列配置_编辑_物料信息', 'bom.template:columnConfigEditMaterial', NULL, NULL, NULL, NULL, '2023-02-02 14:32:32', 'enable', 'GET', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808100', '列配置_详情_物料信息', 'bom.template:columnConfigDetailMaterial', NULL, NULL, NULL, NULL, '2023-02-02 14:32:32', 'enable', 'GET', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809070', '列配置_列表', 'technology.template:columnConfigList', NULL, NULL, NULL, NULL, '2023-02-02 14:31:57', 'enable', 'GET', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);

-- 角色批量按钮赋权 列配置
INSERT INTO `sys_role_permission`(`role_id`, `permission_id`)
SELECT DISTINCT a.id as role_id, b.id as permission_id
FROM sys_roles a,sys_permissions b,sys_role_permission c
WHERE b.name like '列配置%' and a.id =c.role_id and b.parent_id =c.permission_id;


-- 默认下推配置 生产订单下推生产订单用料清单
-- DELETE,防止重复
DELETE FROM dfs_common_config WHERE operation_object = 'productOrder' AND target_object = 'productMaterialsList' AND operation_type = 'jumpPage';
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('productOrder', 'productMaterialsList', 'jumpPage', 'enable', 'false', 'productOrder下推productMaterialsList配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('productOrder', 'productMaterialsList', 'jumpPage', 'originalOrderStates', '[2]', 'productOrder下推productMaterialsList配置');
-- 默认下推配置 生产工单下推生产领料出库单
-- DELETE,防止重复
DELETE FROM dfs_common_config WHERE operation_object = 'workOrder' AND target_object = 'outputPickingProduct' AND operation_type = 'jumpPage';
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ( 'workOrder', 'outputPickingProduct', 'jumpPage', 'enable', 'false', 'workOrder下推outputPickingProduct配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ( 'workOrder', 'outputPickingProduct', 'jumpPage', 'originalOrderStates', '[2]', 'workOrder下推outputPickingProduct配置');
-- 默认下推配置 生产工单下推生产补料出库单
-- DELETE,防止重复
DELETE FROM dfs_common_config WHERE operation_object = 'workOrder' AND target_object = 'workOrderSupplement' AND operation_type = 'jumpPage';
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ( 'workOrder', 'workOrderSupplement', 'jumpPage', 'enable', 'false', 'workOrder下推workOrderSupplement配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ( 'workOrder', 'workOrderSupplement', 'jumpPage', 'originalOrderStates', '[2]', 'workOrder下推workOrderSupplement配置');
-- 默认下推配置 生产工单下推生产退料入库单
-- DELETE,防止重复
DELETE FROM dfs_common_config WHERE operation_object = 'workOrder' AND target_object = 'productionReturnReceipt' AND operation_type = 'jumpPage';
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ( 'workOrder', 'productionReturnReceipt', 'jumpPage', 'enable', 'false', 'workOrder下推productionReturnReceipt配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ( 'workOrder', 'productionReturnReceipt', 'jumpPage', 'originalOrderStates', '[2]', 'workOrder下推productionReturnReceipt配置');


-- 仅备份表结构的表格更新
UPDATE `dfs_dict` SET `des` = 'dfs_sensor_record,dfs_count,dfs_operation_log,dfs_alarm,dfs_alarm_record,dfs_bar_code,dfs_bar_code_track,dfs_code_relevance,dfs_code_target_record,dfs_combine_target_record,dfs_device_energy_constant_consumption,dfs_device_energy_consumption,dfs_device_gas_constant_consumption,dfs_device_gas_energy_consumption,dfs_device_state_duration_day_union,dfs_device_time_period_energy_consumption,dfs_eui_mark,dfs_fac_input_record,dfs_feed_record,dfs_feed_record_file,dfs_import_data_record,dfs_input_record,dfs_maintain_record,dfs_maintain_material_record,dfs_maintain_record_facilities,dfs_maintain_record_pic,dfs_order_change_log,dfs_product_flow_code,dfs_product_flow_code_record' WHERE `code` = 'BackupTableStructure';

-- 删除多余表格
DROP TABLE IF EXISTS `dfs_fac_task`;
DROP TABLE IF EXISTS `dfs_fac_item`;

-- 工艺新增权限  更新工艺  数据分析
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10803360', '更新工艺', 'technology:updateReleased', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10803370', '数据分析', 'technology:impactAnalysis', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`)
VALUES ('dailyGasConsumption', '每日耗气量', '1', 'device', NULL, NULL, NULL, '2023-01-12 14:57:19', NULL, NULL);


INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`)
VALUES ('dailyGasConsumption', 'device', '每日耗气量', 'dailyGasConsumptionConstantAutoCollect', 1, NULL);


UPDATE `dfs_target_method` SET `method_cname` = '每日能耗' WHERE `method_name` = 'dailyEnergyConsumptionConstantAutoCollect';
UPDATE `dfs_target_model` SET `method_cname` = '每日能耗' WHERE `method_name` = 'dailyEnergyConsumptionConstantAutoCollect';

-- 报工数量校验配置初始化sql
DELETE FROM dfs_common_config WHERE operation_object = 'workOrder' AND operation_type = 'verificationReportQuantity';
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'verExcessCompletedTips', '\"\"', 'workOrder报工数量校验配置');
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'verCompletedEnable', '[\"null\"]', 'workOrder报工数量校验配置');
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'verExcessCompletedEnable', '[\"null\"]', 'workOrder报工数量校验配置');
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'verExcessCompletedLimit', '\"\"', 'workOrder报工数量校验配置');
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'enable', 'false', 'workOrder报工数量校验配置');
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'verCompletedLimit', '\"\"', 'workOrder报工数量校验配置');
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'applicableApplet', '[]', 'workOrder报工数量校验配置');
INSERT INTO `dfs_common_config`(`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('workOrder', NULL, 'verificationReportQuantity', 'verCompletedTips', '\"\"', 'workOrder报工数量校验配置');

-- 生产工单单据打印权限配置
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005250', '单据下载默认模板', 'production.workorder:docDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005260', '单据上传单据模板', 'production.workorder:docUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005270', '单据打印单据', 'production.workorder:docPrint', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005280', '导出Excel单据', 'production.workorder:docExcel', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10005', 2, 1, 0, '/order-model/production-workorder', 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101250', '单据下载默认模板', 'production.workorder:docDefaultTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101260', '单据上传单据模板', 'production.workorder:docUploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101270', '单据打印单据', 'production.workorder:docPrint', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101280', '导出Excel单据', 'production.workorder:docExcel', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);


-- 全局保存并生效/完成权限按钮
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10003220', '保存并生效', 'product.order:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:32', 'enable', 'POST', '10003', 2, 1, 0, '/order-model/product-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10005240', '保存并生效', 'production.workorder:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '10005', 2, 1, 0, '/order-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10101240', '保存并生效', 'production.workorder.model:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10102060', '保存并生效', 'production.requisition:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 15:50:15', 'enable', 'POST', '10102', 2, 1, 0, '/production-requisition/list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10408100', '保存并生效', 'product.inspection.project:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 15:53:46', 'enable', 'POST', '10408', 2, 1, 0, '/qualities-manage/productInspection/project', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10409100', '保存并生效', 'product.inspection.plan:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 16:04:45', 'enable', 'POST', '10409', 2, 1, 0, '/qualities-manage/productInspection/plan', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10801140', '保存并生效', 'material:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 16:04:45', 'enable', 'POST', '10801', 2, 1, 0, '/product-management/supplies', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10802170', '保存并生效', 'bom:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '10802', 2, 1, 0, '/product-management/bom', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10803380', '保存并生效', 'technology:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10804150', '保存并生效', 'procedure:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 16:04:45', 'enable', 'POST', '10804', 2, 1, 0, '/product-management/procedure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10806150', '保存并生效', 'replace.scheme:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '10806', 2, 1, 0, '/product-management/replace-scheme', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10808110', '保存并生效', 'bom.template:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 14:27:50', 'enable', 'POST', '10808', 2, 1, 0, '/product-management/bomTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10809110', '保存并生效', 'technology.template:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 14:31:15', 'enable', 'POST', '10809', 2, 1, 0, '/product-management/technologyTemplate', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090101070', '保存并生效', 'client.file:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090101', 2, 1, 0, '/supply-chain-collaboration/order-model/clientFile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090103070', '保存并生效', 'shipment.application:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090103', 2, 1, 0, '/supply-chain-collaboration/order-model/shipment_application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090104060', '保存并生效', 'deliver.goods:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090104', 2, 1, 0, '/supply-chain-collaboration/order-model/deliver_goods', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090201080', '保存并生效', 'supplier.profile:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090201', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplier-profile', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090204080', '保存并生效', 'purchasing.demand:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090204', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-demand', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090205090', '保存并生效', 'purchasing.list:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090205', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchasing-list', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090206080', '保存并生效', 'delivery.order:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090206', 2, 1, 0, '/supply-chain-collaboration/procurement-management/delivery-order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208080', '保存并生效', 'return:application:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090303070', '保存并生效', 'factory.record:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:53', 'enable', 'POST', '1090303', 2, 1, 0, '/warehousingDistribution/record-query/factory-record', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090304070', '保存并生效', 'access.record:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:54', 'enable', 'POST', '1090304', 2, 1, 0, '/warehousingDistribution/record-query/access-record', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090305070', '保存并生效', 'material.allocation:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090305', 2, 1, 0, '/warehousingDistribution/record-query/material-allocation', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090307060', '保存并生效', 'warehouse.manage:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090307', 2, 1, 0, '/warehousingDistribution/record-query/warehouse-manage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090308080', '保存并生效', 'location.define:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:33', 'enable', 'POST', '1090308', 2, 1, 0, '/warehousingDistribution/record-query/location-define', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001080', '保存并生效', 'purchasePutInStorage:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001090', '保存并完成', 'purchasePutInStorage:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002080', '保存并生效', 'purchaseReturnMaterialOutOfStorage:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002090', '保存并完成', 'purchaseReturnMaterialOutOfStorage:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101080', '保存并生效', 'workOrderComplete:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 16:13:47', 'enable', 'POST', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101090', '保存并完成', 'workOrderComplete:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-06 16:13:43', 'enable', 'POST', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102090', '保存并生效', 'workOrderTakeOut:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102100', '保存并完成', 'workOrderTakeOut:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103090', '保存并生效', 'workOrderSupplement:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103100', '保存并完成', 'workOrderSupplement:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104080', '保存并生效', 'applicationReturn:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104090', '保存并完成', 'applicationReturn:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:34', 'enable', 'POST', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090313090', '保存并生效', 'shelves.define:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '1090313', 2, 1, 0, '/warehousingDistribution/shelves-definition', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031401080', '保存并生效', 'salesIssueDoc:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031401', 2, 1, 0, '/warehousingDistribution/salesIssueReceipt/salesIssueDoc', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031401090', '保存并完成', 'salesIssueDoc:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031401', 2, 1, 0, '/warehousingDistribution/salesIssueReceipt/salesIssueDoc', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031402080', '保存并生效', 'salesMaterialReturnReceiptDoc:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031402', 2, 1, 0, '/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031402090', '保存并完成', 'salesMaterialReturnReceiptDoc:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031402', 2, 1, 0, '/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031501080', '保存并生效', 'subcontracting.order:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031501', 2, 1, 0, '/warehousingDistribution/subcontracting/order', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031502080', '保存并生效', 'outstoreMaterail:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031502', 2, 1, 0, '/warehousingDistribution/subcontracting/outstoreMaterail', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031503080', '保存并生效', 'repairMaterail:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031503', 2, 1, 0, '/warehousingDistribution/subcontracting/repairMaterail', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031504080', '保存并生效', 'refundMaterail:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031504', 2, 1, 0, '/warehousingDistribution/subcontracting/refundMaterail', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031602070', '保存并生效', 'allocation:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031602', 2, 1, 0, '/warehousingDistribution/cougnyManage/allocation', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031701080', '保存并生效', 'stockApplication:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:54', 'enable', 'POST', '109031701', 2, 1, 0, '/warehousingDistribution/mixReceive/stockApplication', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031702080', '保存并生效', 'issueRequisition:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-06 17:53:54', 'enable', 'POST', '109031702', 2, 1, 0, '/warehousingDistribution/mixReceive/issueRequisition', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031703070', '保存并生效', 'warehousing:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031703', 2, 1, 0, '/warehousingDistribution/mixReceive/warehousing', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031703080', '保存并完成', 'warehousing:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031703', 2, 1, 0, '/warehousingDistribution/mixReceive/warehousing', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031704070', '保存并生效', 'issue:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031704', 2, 1, 0, '/warehousingDistribution/mixReceive/issue', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031704080', '保存并完成', 'issue:saveAndFinish', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '109031704', 2, 1, 0, '/warehousingDistribution/mixReceive/issue', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12801007', '保存并生效', 'node.definition:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '12801', 2, 1, 0, '/projectManagement/nodeDefinition', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12802007', '保存并生效', 'node.configure:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-02 17:45:35', 'enable', 'POST', '12802', 2, 1, 0, '/projectManagement/nodeConfigure', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12802008', '工厂配置_保存并生效', 'node.configure.factoryConfiguration:saveAndEffect', NULL, NULL, NULL, NULL, '2023-02-07 18:04:17', 'enable', 'POST', '12802', 2, 1, 0, '/projectManagement/nodeConfigure', 1);

-- 角色批量赋权 保存并生效/完成
INSERT INTO `sys_role_permission`(`role_id`, `permission_id`)
SELECT DISTINCT a.id as role_id, b.id as permission_id
FROM sys_roles a,sys_permissions b,sys_role_permission c
WHERE b.name like '保存并%' and a.id =c.role_id and b.parent_id =c.permission_id;

-- 客户编码、供应商代号字段统一,删除之前不符合的列配置
DELETE FROM dfs_user_column_record WHERE column_information LIKE "%客户代号%";
DELETE FROM dfs_user_column_record WHERE column_information LIKE "%供应商代号%";

-- 删除大屏管理（旧）菜单权限 邓尹杰
DELETE  FROM `sys_permissions` WHERE id = '11699' or parent_id = '11699';

-- 默认下推配置 销售出库单下推销售退货入库
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('salesIssueDoc', 'salesReturnReceiptDoc', 'jumpPage', 'enable', 'false', 'salesIssueDoc下推salesReturnReceiptDoc配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('salesIssueDoc', 'salesReturnReceiptDoc', 'jumpPage', 'originalOrderStates', '[2]', 'salesIssueDoc下推salesReturnReceiptDoc配置');


-- 默认下推配置 其他出库申请单下推调拨单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('stockOtherAppForm', 'stockAllocation', 'jumpPage', 'enable', 'false', 'stockOtherAppForm下推stockAllocation配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('stockOtherAppForm', 'stockAllocation', 'jumpPage', 'originalOrderStates', '[2]', 'stockOtherAppForm下推stockAllocation配置');

-- 默认下推配置  委外领料出库单下推委外退料入库单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractProductOutbound', 'subcontractReturnReceipt', 'jumpPage', 'enable', 'false', 'subcontractProductOutbound下推subcontractReturnReceipt配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractProductOutbound', 'subcontractReturnReceipt', 'jumpPage', 'originalOrderStates', '[2]', 'subcontractProductOutbound下推subcontractReturnReceipt配置');

-- 默认下推配置  委外补料出库单下推委外退料入库单
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractSupplementaryFood', 'subcontractReturnReceipt', 'jumpPage', 'enable', 'false', 'stockOtherAppForm下推subcontractReturnReceipt配置');
INSERT INTO `dfs_common_config` (`operation_object`, `target_object`, `operation_type`, `property_key`, `property_value`, `description`) VALUES ('subcontractSupplementaryFood', 'subcontractReturnReceipt', 'jumpPage', 'originalOrderStates', '[2]', 'stockOtherAppForm下推subcontractReturnReceipt配置');


