set autocommit = 0;
#DDL
DROP TABLE `sys_permissions`;
CREATE TABLE `sys_permissions` (
                                   `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限id',
                                   `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限名',
                                   `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '接口路径',
                                   `description` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明',
                                   `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                                   `create_time` datetime DEFAULT NULL,
                                   `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                                   `update_time` datetime DEFAULT NULL,
                                   `status` enum('enable','disable') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'enable' COMMENT '角色状态''ENABLE''开启,''DISABLE''关闭',
                                   `method` enum('GET','POST','PUT','DELETE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'GET' COMMENT '请求方式',
                                   `parent_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限等级：1级代表菜单、2级也代菜单，3级代表具体权限',
                                   `type` int DEFAULT NULL COMMENT '0：一级菜单   1：二级菜单   2：按钮',
                                   `is_web` int DEFAULT NULL COMMENT '是否web端1-web端,0-pad端',
                                   `is_back_stage` int DEFAULT NULL COMMENT '是否为后台管理（1-是 0-否）',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
#DML

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1000', '生产订单', '/order-model', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1010', '销售订单', '/order-model/salesOrder', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1000', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1011', '新增', 'sales.order:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1012', '编辑', 'sales.order:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1013', '详情', 'sales.order:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1014', '删除', 'sales.order:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1015', '同步销售订单', 'sales.order:synchroniseSaleOrder', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1016', '查看图纸', 'sales.order:drawing', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1017', '追溯', 'sales.order:trace', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1019', '审批', 'sales.order:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1010', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1020', '客户档案', '/order-model/clientFile', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1000', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1021', '新增', 'client.file:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1020', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1022', '编辑', 'client.file:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1020', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1024', '删除', 'client.file:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1020', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1026', '审批', 'client.file:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1020', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1030', '出货申请', '/order-model/shipment_application', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1000', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1031', '新增', 'shipment.application:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1030', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1032', '编辑', 'shipment.application:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1030', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1033', '详情', 'shipment.application:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1030', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1034', '删除', 'shipment.application:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1030', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1036', '审批', 'shipment.application:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1030', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1040', '发货管理', '/order-model/deliver_goods', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1000', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1041', '新增', 'deliver.goods:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1040', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1042', '编辑', 'deliver.goods:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1040', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1043', '详情', 'deliver.goods:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1040', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1044', '删除', 'deliver.goods:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1040', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1046', '审批', 'deliver.goods:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1040', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1050', '辅助计划', '/order-model/auxiliary_plan', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1000', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1051', '导入', 'auxiliary.plan:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1052', '导出', 'auxiliary.plan:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1053', '物料齐备性检查', 'auxiliary.plan:check', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1054', '确认辅助计划', 'auxiliary.plan:confirm', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1055', '拆单', 'auxiliary.plan:split', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1056', '编辑', 'auxiliary.plan:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1057', '齐套详情', 'auxiliary.plan:completeDetail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1058', '查看图纸', 'auxiliary.plan:drawing', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1050', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1070', '生产订单', '/order-model/product-order', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1000', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1071', '新增', 'product.order:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1072', '编辑', 'product.order:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1073', '详情', 'product.order:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1074', '删除', 'product.order:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1075', '同步销售订单', 'product.order:synchroniseSaleOrder', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1076', '查看图纸', 'product.order:drawing', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1077', '追溯', 'product.order:trace', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1079', '审批', 'product.order:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1070', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1100', '工单管理', '/workorder-model', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1110', '生产工单', '/workorder-model/production-workorder', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1100', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('111010', '审批', 'production.workorder:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('111011', '报工记录', 'production.workorder:report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1111', '新增', 'production.workorder:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1112', '编辑', 'production.workorder:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1113', '详情', 'production.workorder:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1114', '删除', 'production.workorder:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1115', '标签', 'production.workorder:label', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1116', '状态更改', 'production.workorder:stateChange', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1117', '复制', 'production.workorder:copy', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1118', '追溯', 'production.workorder:trace', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1110', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1120', '生产领料', '/production-requisition/list', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1100', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1121', '新增', 'production.requisition:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1120', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1122', '编辑', 'production.requisition:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1120', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1123', '详情', 'production.requisition:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1120', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1124', '删除', 'production.requisition:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1120', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1126', '审批', 'production.requisition:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1120', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1130', '辅助排程', '/workorder-model/workorder-scheduling', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1100', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1131', '导入', 'workorder.scheduling:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1132', '导出', 'workorder.scheduling:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1133', '物料齐备性检查', 'workorder.scheduling:inspect', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1134', '产能负荷', 'workorder.scheduling:load', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1135', '新增', 'workorder.scheduling:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1136', '齐套详情', 'workorder.scheduling:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1137', '编辑', 'workorder.scheduling:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1138', '下发', 'workorder.scheduling:state', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1130', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1200', '采购管理', '/procurement-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1210', '供应商档案', '/procurement-management/supplier-profile', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1211', '新增', 'supplier.profile:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1210', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1212', '编辑', 'supplier.profile:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1210', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1213', '详情', 'supplier.profile:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1210', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1214', '删除', 'supplier.profile:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1210', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1216', '审批', 'supplier.profile:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1210', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1220', '采购需求', '/procurement-management/purchasing-demand', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1221', '新增', 'purchasing.demand:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1220', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1222', '编辑', 'purchasing.demand:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1220', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1223', '详情', 'purchasing.demand:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1220', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1224', '删除', 'purchasing.demand:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1220', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1226', '审批', 'purchasing.demand:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1220', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1230', '采购订单', '/procurement-management/purchasing-list', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1231', '新增', 'purchasing.list:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1230', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1232', '编辑', 'purchasing.list:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1230', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1233', '详情', 'purchasing.list:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1230', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1234', '删除', 'purchasing.list:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1230', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1236', '审批', 'purchasing.list:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1230', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1240', '来料检验', '/procurement-management/materials-list', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1241', '新增', 'materials.list:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1240', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1242', '编辑', 'materials.list:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1240', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1243', '详情', 'materials.list:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1240', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1244', '删除', 'materials.list:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1240', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1250', '收货单', '/procurement-management/delivery-order', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1251', '新增', 'delivery.order:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1250', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1252', '编辑', 'delivery.order:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1250', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1253', '详情', 'delivery.order:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1250', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1254', '删除', 'delivery.order:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1250', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1256', '审批', 'delivery.order:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1250', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1260', '物料辅助分析', '/procurement-management/materialAnalysis', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1261', '导出', 'material.analysis:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1260', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1270', '供应商物料清单', '/procurement-management/supplyList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1272', '审批', 'supplier.list:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1270', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1300', '产品定义', '/product-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1310', '物料', '/product-management/supplies', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1300', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1311', '新增', 'material:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1310', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1312', '编辑', 'material:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1310', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1313', '详情', 'material:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1310', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1314', '删除', 'material:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1310', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1315', '物料导入', 'material:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1310', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1316', '物料导出', 'material:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1310', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1318', '审批', 'material:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1310', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1320', 'BOM', '/product-management/bom', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1300', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1321', '新增', 'bom:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1320', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1322', '编辑', 'bom:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1320', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1323', '详情', 'bom:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1320', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1324', '删除', 'bom:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1320', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1326', '审批', 'bom:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1320', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1330', '工艺', '/product-management/technology', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1300', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1331', '新增', 'technology:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1330', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1332', '编辑', 'technology:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1330', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1333', '工艺路线', 'technology:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1330', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1334', '删除', 'technology:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1330', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1335', '模板导出', 'technology:out', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1330', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1336', 'Excel导入', 'technology:in', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1330', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1338', '审批', 'technology:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1330', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1340', '工序', '/product-management/procedure', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1300', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1341', '新增', 'procedure:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1340', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1342', '编辑', 'procedure:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1340', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1343', '详情', 'procedure:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1340', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1344', '删除', 'procedure:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1340', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1346', '审批', 'procedure:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1340', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1400', '数字仓储', '/warehouse-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1410', '物料库存', '/record-query/inbound-record', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1400', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1411', '库存初始化', 'inbound.record:initialize', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1410', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1412', '编辑', 'inbound.record:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1410', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1413', '详情', 'inbound.record:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1410', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1414', '删除', 'inbound.record:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1410', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1420', '出库记录', '/record-query/factory-record', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1400', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1421', '新增', 'factory.record:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1420', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1422', '编辑', 'factory.record:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1420', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1423', '详情', 'factory.record:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1420', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1424', '删除', 'factory.record:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1420', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1426', '审批', 'factory.record:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1420', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1430', '入库记录', '/record-query/access-record', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1400', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1431', '新增', 'access.record:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1430', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1432', '编辑', 'access.record:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1430', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1433', '详情', 'access.record:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1430', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1434', '删除', 'access.record:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1430', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1436', '审批', 'access.record:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1430', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1440', '物料调拨', '/record-query/material-allocation', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1400', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1441', '新增', 'material.allocation:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1440', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1442', '编辑', 'material.allocation:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1440', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1443', '详情', 'material.allocation:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1440', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1444', '删除', 'material.allocation:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '1440', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1446', '审批', 'material.allocation:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1440', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1450', '库房盘点', '/record-query/inventory', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1400', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1451', '新增', 'inventory:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1450', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1452', '编辑', 'inventory:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1450', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1460', '库房管理', '/record-query/warehouse-manage', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1400', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1461', '新增', 'warehouse.manage:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '1460', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1462', '编辑', 'warehouse.manage:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1460', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1464', '审批', 'warehouse.manage:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1460', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1500', '告警管理', '/alarm', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1510', '告警列表', '/alarm/list', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1500', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1511', '查询', 'list:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1510', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1512', '导出', 'list:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1510', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1513', '手动恢复', 'list:manualRecovery', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1510', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1514', '详情', 'list:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1510', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1515', '指定处理人', 'list:designateProcessor', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1510', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1516', '视频播放', 'list:video', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1510', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1520', '告警设置', '/alarm/config', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1500', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1521', '编辑', 'config:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1520', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1530', '历史告警', '/alarm/historyAlarm', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1500', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1531', '查询', 'history.alarm:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1530', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1532', '详情', 'history.alarm:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1530', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1533', '导出', 'history.alarm:export', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1530', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1540', '告警类型定义', '/alarm/alarmTypeDefine', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1500', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1541', '查询', 'alarm.type.define:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1540', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1550', '告警ID定义', '/alarm/alarmIDDefine', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1500', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1551', '查询', 'alarm.id.define:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1550', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1552', '新增', 'alarm.id.define:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1550', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1553', '导入', 'alarm.id.define:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1550', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1554', '模板下载', 'alarm.id.define:mouldDownload', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1550', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1555', '编辑', 'alarm.id.define:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '1550', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1600', '专家系统', '/expert-system', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1610', '质量分析', '/expert-system/quality-analysis', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1611', '分析指标配置', 'quality.analysis:config', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1610', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1613', '编辑', 'analysisi.config:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1610', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1620', '灯检分析', '/expert-system/light-inspection-analysis', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1630', '灭菌柜分析', '/expert-system/sterilization-cabinet-analysis', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1631', '编辑', 'sterilization.cabinet.analysis:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1630', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1640', '电耗分析', '/expert-system/power-consumption-analysis', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1650', '质检分析', '/expert-system/quality-testing', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('1651', '设置标准', 'quality.testing:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '1650', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2000', '工厂配置', '/factory-model', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2010', '产线模型', '/product-model/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2000', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2020', '厂区', '/factory-model/factory-area', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2000', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2021', '新增', 'factory.area:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2020', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2022', '编辑', 'factory.area:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2020', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2023', '详情', 'factory.area:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2020', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2024', '删除', 'factory.area:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2020', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2030', '车间', '/factory-model/workshop', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2000', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2031', '新增', 'workshop:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2030', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2032', '编辑', 'workshop:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2030', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2033', '详情', 'workshop:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2030', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2034', '删除', 'workshop:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2030', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2040', '产线', '/factory-model/product-line', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2000', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2041', '新增', 'product.line:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2040', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2042', '编辑', 'product.line:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2040', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2043', '详情', 'product.line:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2040', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2044', '删除', 'product.line:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2040', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2045', '绑定', 'product.line:bind', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2040', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2050', '工位', '/factory-model/station', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2000', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2051', '新增', 'station:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2050', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2052', '编辑', 'station:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2050', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2053', '详情', 'station:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2050', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2054', '删除', 'station:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2050', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2100', '设备信息', '/equipment', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2110', '设备模型', '/equipment/equipment-model', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2100', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2120', '生产设备', '/equipment/device', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2100', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2121', '新增', 'device:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2120', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2122', '编辑', 'device:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2120', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2123', '详情', 'device:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2120', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2124', '删除', 'device:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2120', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2130', '采集设备', '/equipment/test-equipment', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2100', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2131', '新增', 'test.equipment:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2132', '编辑', 'test.equipment:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2133', '详情', 'test.equipment:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2134', '删除', 'test.equipment:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2135', '设置', 'test.equipment:set', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2136', '类型配置', 'test.equipment:setting', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2140', '大屏设备', '/equipment/station-machine', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2100', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2141', '新增', 'station.machine:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2140', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2142', '编辑', 'station.machine:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2140', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2143', '详情', 'station.machine:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2140', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2144', '删除', 'station.machine:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2140', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2150', '视频设备', '/equipment/video-device', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2100', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2151', '新增', 'video.device:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2150', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2153', '播放', 'video.device:player', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2150', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2154', '删除', 'video.device:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2150', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2160', '设备维修', '/equipment/warranty', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2100', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2161', '工单新增', 'maintenance.order.add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2162', '工单编辑', 'maintenance.order.update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2163', '工单删除', 'maintenance.order.delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2164', '工单详情', 'maintenance.order.select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2165', '记录单新增', 'maintenance.record.add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2166', '记录单编辑', 'maintenance.record.update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2167', '记录单删除', 'maintenance.record.delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2168', '记录单详情', 'maintenance.record.select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2200', '工厂指标', '/target', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2300', '基础信息', '/base-info', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2310', '用户管理', '/base-info/user', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23100', '初始配置', '/base-info/initialize', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23101', '初始化', 'initialize:initialize', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '23100', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2311', '新增', 'user:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2310', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23110', '产能配置', '/base-info/capacityList', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23111', '新增', 'capacityList:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '23110', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23112', '编辑', 'capacityList:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '23110', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2312', '编辑', 'user:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2310', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23120', '工作日历模板', '/base-info/workCalendar/calendarTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2313', '详情', 'user:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2310', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23130', '工作日历', '/base-info/workCalendar/worksCalendar', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23131', '新增', 'worksCalendar:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '23130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23132', '修改', 'worksCalendar:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '23130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23133', '删除', 'worksCalendar:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '23130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23134', '详情', 'worksCalendar:detail', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '23130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23135', '日历汇总', 'worksCalendar:summary', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '23130', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23140', '工作日历配置', '/base-info/workCalendar/calendarConfig', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23150', '首班时间配置', '/base-info/firstShiftTimeConfig', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23160', '编号规则', '/base-info/num-rule', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23161', '新增', 'num.rule:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '23160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23162', '编辑', 'num.rule:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '23160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23163', '删除', 'num.rule:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '23160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23164', '详情', 'num.rule:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '23160', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23170', '审批配置', '/base-info/approveConfig', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2320', '条款管理', '/base-info/clause', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2321', '新增', 'clause:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2320', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2322', '编辑', 'clause:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2320', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2323', '详情', 'clause:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2320', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2330', '单位配置', '/base-info/unit-config', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2331', '新增', 'unit.config:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2330', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2332', '编辑', 'unit.config:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2330', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2340', '物料信息配置', '/base-info/material-config', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2341', '新增', 'material.config:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2340', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2342', '编辑', 'material.config:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2340', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2350', '角色权限控制', '/base-info/role-permission', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2351', '新增', 'role.permission:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2350', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2352', '编辑', 'role.permission:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2350', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2353', '成员维护', 'role.permission:member', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2350', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2354', '删除', 'role.permission:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2350', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2355', '权限维护', 'role.permission:permission', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2350', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2360', '组织机构管理', '/base-info/organization', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2361', '新增', 'organization:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2360', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2362', '编辑', 'organization:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2360', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2363', '详情', 'organization:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2360', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2364', '删除', 'organization:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2360', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2370', '设备类型配置', '/base-info/device-type-config', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2371', '新增', 'device.config.add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2370', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2372', '编辑', 'device.config.update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2370', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2373', '详情', 'device.config.select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2370', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2374', '删除', 'device.config.delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2370', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2380', '班次配置', '/base-info/flight-config', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2381', '新增', 'flight.config:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2380', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2382', '编辑', 'flight.config:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2380', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2383', '删除', 'flight.config:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2380', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2390', '化验元素配置', '/base-info/element-config', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2391', '新增', 'element.config:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2390', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2392', '编辑', 'element.config:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2390', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2393', '删除', 'element.config:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2390', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2400', '系统设置', '/system_settings', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2410', 'DB容量设置', '/system_settings/DBclear/', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2400', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2411', '清理', 'db.clear:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2410', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2420', '系统状态设置', '/system_settings/chang-system-status', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2400', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2421', '系统状态更改', 'chang.system.status:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2420', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2422', '删除', 'chang.system.status:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2420', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2500', '操作历史', '/system-operation', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2600', '追溯管理', '/trace', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2610', '工单追溯', '/trace/work-order', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2620', '订单追溯', '/trace/order', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2700', '质量检验', '/qualities-manage', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2710', '工位质检', '/qualities-manage/processQuality', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('27110', '工位质检方案', '/qualities-manage/processQuality/paln', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2720', '质检返工', '/qualities-manage/reworkQuality', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2730', '产品检测汇总', '/qualities-manage/productInspection', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2731', '审批', 'product.inspection.result:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2730', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2740', '不良定义', '/qualities-manage/processQuality/qualityDefinition', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2750', '产品检测项目组定义', '/qualities-manage/productInspection/define', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2760', '产品检测项目', '/qualities-manage/productInspection/project', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2761', '审批', 'product.inspection.project:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2760', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2770', '产品检测方案', '/qualities-manage/productInspection/plan', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2771', '审批', 'product.inspection.plan:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2770', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2780', '质检返工定义', '/qualities-manage/reworkQuality/define', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2790', '质检返工方案', '/qualities-manage/reworkQuality/plan/project', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2700', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2800', '标签管理', '/label-manage', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2810', '标签规则', '/label-manage/labelRules', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2800', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2811', '新增', 'label.rules:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '2810', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2812', '编辑', 'label.rules:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '2810', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2813', '详情', 'label.rules:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2810', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2814', '删除', 'label.rules:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '2810', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2900', '炉号信息', '/furnace-info', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('2910', '炉次追溯', 'furnace.info:trace', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2900', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3000', '能耗管理', '/energy-consumption-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3100', '大屏管理', '/screen-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3101', '新增', 'screen.management:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '3100', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3102', '编辑', 'screen.management:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '3100', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3103', '删除', 'screen.management:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '3100', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3200', '批号管理', '/batch-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3210', '批号', '/batch-management/batch', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3200', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3211', '详情', 'batch:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3210', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3212', '编辑', 'batch:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3200', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3300', '生产报表', '/product-report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3310', '生产日报', '/product-report/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3300', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3400', '包装管理', '/package-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3410', '包装记录', '/package-management/package-record', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3400', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3500', 'AGV调度', '/avg/dispath', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3501', 'AGV调度', '/avg/dispath/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3500', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3502', '点位管理', '/point/list', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3500', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3600', 'SMT管理', '/smt-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3610', '物料站位表', '/smt-management/material-station', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3611', '新增', 'material.station:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '3610', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3612', '编辑', 'material.station:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '3610', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3613', '详情', 'material.station:select', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3610', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3614', '删除', 'material.station:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '3610', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3615', '站位', 'material.station:station', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3610', 2, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3620', '工单站位表', '/smt-management/order-station', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '3600', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3700', '维修管理', '/maintenance-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3800', '告警管理', '/alarm-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('3900', '入库管理', '/stock-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4000', '出库管理', '/warehouse-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4100', '盘点管理', '/inventory-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4200', '质检管理', '/quality-inspection-management', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4300', '工位作业', '/work-station', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4310', '要料呼叫', '/material-call', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4300', 1, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4311', '呼叫', 'material:call', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4310', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4312', '收货', 'material:receiving', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4310', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4313', '要料记录', 'material:record', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4310', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4320', '操作人员', '/operation-staff', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4300', 1, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4321', '上线', 'operation:online', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4320', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4322', '下线', 'operation:online', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4320', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4323', '操作记录', 'operation:offline', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4320', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4330', '工位物料', '/location-material', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4300', 1, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4331', '上料', 'location:load', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4330', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4332', '下料', 'location:lay', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4330', 2, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4340', '工艺信息', '/technique-information', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '4300', 1, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('4400', '产线报工', '/production-line-report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('700', '首页', '/display-plan/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('800', '生产监控大屏', '/product-monitor', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('810', '设备状态大屏', '/device-line/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '800', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('820', '设备监控大屏', '/device-monitor/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '800', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('830', '产线工艺大屏', '/line-technology/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '800', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('900', '生产状态总览', '/display-production-line', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('910', '产线工位看板', '/display-production-line/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '900', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('920', '产线加工看板', '/display-line-status/index', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '900', 1, 1, 0);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23180', '终端权限管理', '/base-info/terminal-permission', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '2300', 1, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23181', '新增', 'terminal.permission:add', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '23180', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23182', '编辑', 'terminal.permission:update', NULL, NULL, NULL, NULL, NULL, 'enable', 'PUT', '23180', 2, 1, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`) VALUES ('23183', '删除', 'terminal.permission:delete', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '23180', 2, 1, 1);

commit