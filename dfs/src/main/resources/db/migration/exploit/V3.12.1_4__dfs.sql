-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.12.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.12.1.1=======================================================

-- 扩展字段1
call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_one',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_one` varchar(512) DEFAULT NULL COMMENT ''扩展字段1''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_two',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_two` varchar(512) DEFAULT NULL COMMENT ''扩展字段2''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_three',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_three` varchar(512) DEFAULT NULL COMMENT ''扩展字段3''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_four',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_four` varchar(512) DEFAULT NULL COMMENT ''扩展字段4''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_five',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_five` varchar(512) DEFAULT NULL COMMENT ''扩展字段5''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_six',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_six` varchar(512) DEFAULT NULL COMMENT ''扩展字段6''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_seven',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_seven` varchar(512) DEFAULT NULL COMMENT ''扩展字段7''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_eight',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_eight` varchar(512) DEFAULT NULL COMMENT ''扩展字段8''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_nine',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_nine` varchar(512) DEFAULT NULL COMMENT ''扩展字段9''');

call `proc_add_column`(
        'dfs_supplier',
        'supplier_extend_field_ten',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `supplier_extend_field_ten` varchar(512) DEFAULT NULL COMMENT ''扩展字段10''');


call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_one',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_one` varchar(512) DEFAULT NULL COMMENT ''扩展字段1''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_two',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_two` varchar(512) DEFAULT NULL COMMENT ''扩展字段2''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_three',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_three` varchar(512) DEFAULT NULL COMMENT ''扩展字段3''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_four',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_four` varchar(512) DEFAULT NULL COMMENT ''扩展字段4''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_five',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_five` varchar(512) DEFAULT NULL COMMENT ''扩展字段5''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_six',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_six` varchar(512) DEFAULT NULL COMMENT ''扩展字段6''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_seven',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_seven` varchar(512) DEFAULT NULL COMMENT ''扩展字段7''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_eight',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_eight` varchar(512) DEFAULT NULL COMMENT ''扩展字段8''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_nine',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_nine` varchar(512) DEFAULT NULL COMMENT ''扩展字段9''');

call `proc_add_column`(
        'dfs_supplier_material',
        'supplier_material_extend_field_ten',
        'ALTER TABLE `dfs_supplier_material` ADD COLUMN `supplier_material_extend_field_ten` varchar(512) DEFAULT NULL COMMENT ''扩展字段10''');

-- **********供应商**********
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'supplier', '供应商档案', 'supplier', '', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'supplierList', '供应商档案列表页', 'supplier.list', 'supplier', 1, 'list');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'supplierDetail', '供应商档案列详情页', 'supplier.detail', 'supplier', 1, 'detail');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'supplierEdit', '供应商档案列编辑页', 'supplier.edit', 'supplier', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'supplierEditByCreate', '创建态', 'supplier.edit.byCreate', 'supplier.edit', 1, 'edit-create');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'supplierEditByRelease', '生效态', 'supplier.edit.byRelease', 'supplier.edit', 1, 'edit-other');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'supplierEditByDisable', '停用态', 'supplier.edit.byDisable', 'supplier.edit', 1, 'edit-other');

-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'code', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'name', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'counterpartName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'contactsName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'phone', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'addr', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'type', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'comprehensiveEvaluation', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'supplierExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

call proc_add_form_field('supplier.list', 'code', '供应商编码');
call proc_add_form_field('supplier.list', 'name', '供应商名称');
call proc_add_form_field('supplier.list', 'stateName', '状态');
call proc_add_form_field('supplier.list', 'counterpartName', '本厂对接人');
call proc_add_form_field('supplier.list', 'contactsName', '供应商联系人');
call proc_add_form_field('supplier.list', 'phone', '供应商联系方式');
call proc_add_form_field('supplier.list', 'addr', '供应商地址');
call proc_add_form_field('supplier.list', 'type', '供应商类型');
call proc_add_form_field('supplier.list', 'remark', '备注');
call proc_add_form_field('supplier.list', 'comprehensiveEvaluation', '综合评价');
call proc_add_form_field('supplier.list', 'createByName', '创建人');
call proc_add_form_field('supplier.list', 'createTime', '创建时间');

call proc_add_form_field('supplier.list', 'supplierExtendFieldOne', '供应商扩展字段1');
call proc_add_form_field('supplier.list', 'supplierExtendFieldTwo', '供应商扩展字段2');
call proc_add_form_field('supplier.list', 'supplierExtendFieldThree', '供应商扩展字段3');
call proc_add_form_field('supplier.list', 'supplierExtendFieldFour', '供应商扩展字段4');
call proc_add_form_field('supplier.list', 'supplierExtendFieldFive', '供应商扩展字段5');
call proc_add_form_field('supplier.list', 'supplierExtendFieldSix', '供应商扩展字段6');
call proc_add_form_field('supplier.list', 'supplierExtendFieldSeven', '供应商扩展字段7');
call proc_add_form_field('supplier.list', 'supplierExtendFieldEight', '供应商扩展字段8');
call proc_add_form_field('supplier.list', 'supplierExtendFieldNine', '供应商扩展字段9');
call proc_add_form_field('supplier.list', 'supplierExtendFieldTen', '供应商扩展字段10');


-- 详情
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'code', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'name', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'counterpartName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'contactsName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'phone', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'addr', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'type', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'comprehensiveEvaluation', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'qualityEvaluation', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'deliveryEvaluation', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'priceLevelEvaluation', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'serviceEvaluation', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'manageEvaluation', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'supplierExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);


call proc_add_form_field('supplier.detail', 'code', '供应商编码');
call proc_add_form_field('supplier.detail', 'name', '供应商名称');
call proc_add_form_field('supplier.detail', 'stateName', '状态');
call proc_add_form_field('supplier.detail', 'counterpartName', '本厂对接人');
call proc_add_form_field('supplier.detail', 'contactsName', '供应商联系人');
call proc_add_form_field('supplier.detail', 'phone', '供应商联系方式');
call proc_add_form_field('supplier.detail', 'addr', '供应商地址');
call proc_add_form_field('supplier.detail', 'type', '供应商类型');
call proc_add_form_field('supplier.detail', 'remark', '备注');
call proc_add_form_field('supplier.detail', 'comprehensiveEvaluation', '综合评价');
call proc_add_form_field('supplier.detail', 'qualityEvaluation', '质量评价');
call proc_add_form_field('supplier.detail', 'deliveryEvaluation', '交付评价');
call proc_add_form_field('supplier.detail', 'priceLevelEvaluation', '价格水平评价');
call proc_add_form_field('supplier.detail', 'serviceEvaluation', '服务评价');
call proc_add_form_field('supplier.detail', 'manageEvaluation', '管理能力评价');

call proc_add_form_field('supplier.detail', 'supplierExtendFieldOne', '供应商扩展字段1');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldTwo', '供应商扩展字段2');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldThree', '供应商扩展字段3');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldFour', '供应商扩展字段4');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldFive', '供应商扩展字段5');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldSix', '供应商扩展字段6');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldSeven', '供应商扩展字段7');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldEight', '供应商扩展字段8');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldNine', '供应商扩展字段9');
call proc_add_form_field('supplier.detail', 'supplierExtendFieldTen', '供应商扩展字段10');

-- 编辑 - 创建
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'code', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'name', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'counterpart', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'contacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'phone', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'addr', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'type', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'comprehensiveEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'qualityEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'deliveryEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'priceLevelEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'serviceEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'manageEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'supplierExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);

call proc_add_form_field('supplier.edit.byCreate', 'code', '供应商编码');
call proc_add_form_field('supplier.edit.byCreate', 'name', '供应商名称');
call proc_add_form_field('supplier.edit.byCreate', 'state', '状态');
call proc_add_form_field('supplier.edit.byCreate', 'counterpart', '本厂对接人');
call proc_add_form_field('supplier.edit.byCreate', 'contacts', '供应商联系人');
call proc_add_form_field('supplier.edit.byCreate', 'phone', '供应商联系方式');
call proc_add_form_field('supplier.edit.byCreate', 'addr', '供应商地址');
call proc_add_form_field('supplier.edit.byCreate', 'type', '供应商类型');
call proc_add_form_field('supplier.edit.byCreate', 'remark', '备注');
call proc_add_form_field('supplier.edit.byCreate', 'comprehensiveEvaluation', '综合评价');
call proc_add_form_field('supplier.edit.byCreate', 'qualityEvaluation', '质量评价');
call proc_add_form_field('supplier.edit.byCreate', 'deliveryEvaluation', '交付评价');
call proc_add_form_field('supplier.edit.byCreate', 'priceLevelEvaluation', '价格水平评价');
call proc_add_form_field('supplier.edit.byCreate', 'serviceEvaluation', '服务评价');
call proc_add_form_field('supplier.edit.byCreate', 'manageEvaluation', '管理能力评价');

call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldOne', '供应商扩展字段1');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldTwo', '供应商扩展字段2');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldThree', '供应商扩展字段3');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldFour', '供应商扩展字段4');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldFive', '供应商扩展字段5');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldSix', '供应商扩展字段6');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldSeven', '供应商扩展字段7');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldEight', '供应商扩展字段8');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldNine', '供应商扩展字段9');
call proc_add_form_field('supplier.edit.byCreate', 'supplierExtendFieldTen', '供应商扩展字段10');


-- 编辑 - 生效
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'code', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'name', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'counterpart', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'contacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'phone', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'addr', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'type', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'comprehensiveEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'qualityEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'deliveryEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'priceLevelEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'serviceEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'manageEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'supplierExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);

call proc_add_form_field('supplier.edit.byRelease', 'code', '供应商编码');
call proc_add_form_field('supplier.edit.byRelease', 'name', '供应商名称');
call proc_add_form_field('supplier.edit.byRelease', 'state', '状态');
call proc_add_form_field('supplier.edit.byRelease', 'counterpart', '本厂对接人');
call proc_add_form_field('supplier.edit.byRelease', 'contacts', '供应商联系人');
call proc_add_form_field('supplier.edit.byRelease', 'phone', '供应商联系方式');
call proc_add_form_field('supplier.edit.byRelease', 'addr', '供应商地址');
call proc_add_form_field('supplier.edit.byRelease', 'type', '供应商类型');
call proc_add_form_field('supplier.edit.byRelease', 'remark', '备注');
call proc_add_form_field('supplier.edit.byRelease', 'comprehensiveEvaluation', '综合评价');
call proc_add_form_field('supplier.edit.byRelease', 'qualityEvaluation', '质量评价');
call proc_add_form_field('supplier.edit.byRelease', 'deliveryEvaluation', '交付评价');
call proc_add_form_field('supplier.edit.byRelease', 'priceLevelEvaluation', '价格水平评价');
call proc_add_form_field('supplier.edit.byRelease', 'serviceEvaluation', '服务评价');
call proc_add_form_field('supplier.edit.byRelease', 'manageEvaluation', '管理能力评价');


call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldOne', '供应商扩展字段1');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldTwo', '供应商扩展字段2');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldThree', '供应商扩展字段3');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldFour', '供应商扩展字段4');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldFive', '供应商扩展字段5');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldSix', '供应商扩展字段6');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldSeven', '供应商扩展字段7');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldEight', '供应商扩展字段8');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldNine', '供应商扩展字段9');
call proc_add_form_field('supplier.edit.byRelease', 'supplierExtendFieldTen', '供应商扩展字段10');


-- 编辑 - 停用
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'code', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'name', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'counterpart', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'contacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'phone', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'addr', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'type', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'comprehensiveEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'qualityEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'deliveryEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'priceLevelEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'serviceEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'manageEvaluation', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'supplierExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);

call proc_add_form_field('supplier.edit.byDisable', 'code', '供应商编码');
call proc_add_form_field('supplier.edit.byDisable', 'name', '供应商名称');
call proc_add_form_field('supplier.edit.byDisable', 'state', '状态');
call proc_add_form_field('supplier.edit.byDisable', 'counterpart', '本厂对接人');
call proc_add_form_field('supplier.edit.byDisable', 'contacts', '供应商联系人');
call proc_add_form_field('supplier.edit.byDisable', 'phone', '供应商联系方式');
call proc_add_form_field('supplier.edit.byDisable', 'addr', '供应商地址');
call proc_add_form_field('supplier.edit.byDisable', 'type', '供应商类型');
call proc_add_form_field('supplier.edit.byDisable', 'remark', '备注');
call proc_add_form_field('supplier.edit.byDisable', 'comprehensiveEvaluation', '综合评价');
call proc_add_form_field('supplier.edit.byDisable', 'qualityEvaluation', '质量评价');
call proc_add_form_field('supplier.edit.byDisable', 'deliveryEvaluation', '交付评价');
call proc_add_form_field('supplier.edit.byDisable', 'priceLevelEvaluation', '价格水平评价');
call proc_add_form_field('supplier.edit.byDisable', 'serviceEvaluation', '服务评价');
call proc_add_form_field('supplier.edit.byDisable', 'manageEvaluation', '管理能力评价');

call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldOne', '供应商扩展字段1');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldTwo', '供应商扩展字段2');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldThree', '供应商扩展字段3');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldFour', '供应商扩展字段4');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldFive', '供应商扩展字段5');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldSix', '供应商扩展字段6');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldSeven', '供应商扩展字段7');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldEight', '供应商扩展字段8');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldNine', '供应商扩展字段9');
call proc_add_form_field('supplier.edit.byDisable', 'supplierExtendFieldTen', '供应商扩展字段10');















-- ***********供应商物料清单************
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'supplierMaterial', '供应商物料清单', 'supplierMaterial', '', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'supplierMaterialList', '供应商物料清单列表页', 'supplierMaterial.list', 'supplierMaterial', 1, 'list');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'supplierMaterialEdit', '供应商物料清单编辑页', 'supplierMaterial.edit', 'supplierMaterial', 1, 'edit-other');

-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialStandard', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'code', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'name', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'comp', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.list', 'supplierMaterial.list', 'supplierMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

call proc_add_form_field('supplierMaterial.list', 'supplierCode', '供应商编码');
call proc_add_form_field('supplierMaterial.list', 'supplierName', '供应商名称');
call proc_add_form_field('supplierMaterial.list', 'stateName', '状态');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialCode', '供应商物料编码');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialName', '供应商物料名称');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialStandard', '供应商物料规格');
call proc_add_form_field('supplierMaterial.list', 'code', '物料编码');
call proc_add_form_field('supplierMaterial.list', 'name', '物料名称');
call proc_add_form_field('supplierMaterial.list', 'comp', '物料单位');

call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldOne', '物料扩展字段1');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldTwo', '物料扩展字段2');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldThree', '物料扩展字段3');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldFour', '物料扩展字段4');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldFive', '物料扩展字段5');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldSix', '物料扩展字段6');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldSeven', '物料扩展字段7');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldEight', '物料扩展字段8');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldNine', '物料扩展字段9');
call proc_add_form_field('supplierMaterial.list', 'supplierMaterialExtendFieldTen', '物料扩展字段10');


-- 编辑
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'code', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'name', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'contactsName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'phone', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'addr', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'type', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'no', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.materialFields.code', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.materialFields.name', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.materialFields.comp', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialStandard', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/procurement-management/supplyList', 'supplierMaterial.edit', 'supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);

call proc_add_form_field('supplierMaterial.edit', 'code', '供应商编码');
call proc_add_form_field('supplierMaterial.edit', 'name', '供应商名称');
call proc_add_form_field('supplierMaterial.edit', 'contactsName', '供应商联系人');
call proc_add_form_field('supplierMaterial.edit', 'phone', '供应商联系方式');
call proc_add_form_field('supplierMaterial.edit', 'addr', '供应商地址');
call proc_add_form_field('supplierMaterial.edit', 'type', '供应商类型');
call proc_add_form_field('supplierMaterial.edit', 'no', '序号');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.materialFields.code', '物料编码');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.materialFields.name', '物料名称');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.materialFields.comp', '物料单位');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.state', '状态');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialCode', '供应商物料编码');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialName', '供应商物料名称');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialStandard', '供应商物料规格');

call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldOne', '物料扩展字段1');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldTwo', '物料扩展字段2');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldThree', '物料扩展字段3');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldFour', '物料扩展字段4');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldFive', '物料扩展字段5');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldSix', '物料扩展字段6');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldSeven', '物料扩展字段7');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldEight', '物料扩展字段8');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldNine', '物料扩展字段9');
call proc_add_form_field('supplierMaterial.edit', 'supplierMaterialList.supplierMaterialExtendFieldTen', '物料扩展字段10');



-- 生产工单用料清单表单配置
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'workOrderMaterialList', '生产工单用料清单', 'workOrderMaterialList', '', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'workOrderMaterialListList', '生产工单用料清单列表页', 'workOrderMaterialList.list', 'workOrderMaterialList', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'workOrderMaterialListByOrder', '按单展示', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list', NULL, 'admin', 'admin', NOW(), NOW(), 'list', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'workOrderMaterialListListByMaterial', '按物料展示', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list', NULL, 'admin', 'admin', NOW(), NOW(), 'list', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'workOrderMaterialListEdit', '生产工单用料清单编辑页', 'workOrderMaterialList.edit', 'workOrderMaterialList', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'workOrderMaterialListEditByCreate', '创建态', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-create', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'workOrderMaterialListEditByRelease', '生效态', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'workOrderMaterialListEditByFinish', '完成态', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'workOrderMaterialListEditByClosed', '关闭态', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'workOrderMaterialListEditByCancel', '取消态', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'workOrderMaterialListDetail', '生产工单用料清单详情', 'workOrderMaterialList.detail', 'workOrderMaterialList', NULL, 'admin', 'admin', NOW(), NOW(), 'detail', 1);

call proc_add_form_field("workOrderMaterialList.list.material", "materialListCode", "用料清单号");
call proc_add_form_field("workOrderMaterialList.list.material", "relateType", "关联单据类型");
call proc_add_form_field("workOrderMaterialList.list.material", "relateNumber", "关联单据编号");
call proc_add_form_field("workOrderMaterialList.list.material", "relateMaterialCode", "关联单据物料编码");
call proc_add_form_field("workOrderMaterialList.list.material", "relateMaterialName", "关联单据物料名称");
call proc_add_form_field("workOrderMaterialList.list.material", "relateAuxiliaryAttr", "关联单据特征参数");
call proc_add_form_field("workOrderMaterialList.list.material", "createTime", "创建时间");
call proc_add_form_field("workOrderMaterialList.list.material", "state", "用料清单状态");
call proc_add_form_field("workOrderMaterialList.list.material", "name", "子项物料名称");
call proc_add_form_field("workOrderMaterialList.list.material", "code", "子项物料编码");
call proc_add_form_field("workOrderMaterialList.list.material", "comp", "子项物料单位");
call proc_add_form_field("workOrderMaterialList.list.material", "planQuantity", "计划数量");
call proc_add_form_field("workOrderMaterialList.list.material", "subTypeName", "子项类型");
call proc_add_form_field("workOrderMaterialList.list.material", "auxiliaryAttr", "子项特征参数");
call proc_add_form_field("workOrderMaterialList.list.material", "createName", "创建人");
call proc_add_form_field("workOrderMaterialList.list.material", "actualQuantity", "实际数量");
call proc_add_form_field("workOrderMaterialList.list.material", "stockQuantity", "即时库存");
call proc_add_form_field("workOrderMaterialList.list.material", "pushDownQuantity", "已下推数量");

call proc_add_form_field("workOrderMaterialList.list.order", "materialListCode", "用料清单号");
call proc_add_form_field("workOrderMaterialList.list.order", "relateType", "关联单据类型");
call proc_add_form_field("workOrderMaterialList.list.order", "relateNumber", "关联单据编号");
call proc_add_form_field("workOrderMaterialList.list.order", "createName", "创建人");
call proc_add_form_field("workOrderMaterialList.list.order", "createTime", "创建时间");
call proc_add_form_field("workOrderMaterialList.list.order", "state", "用料清单状态");
call proc_add_form_field("workOrderMaterialList.list.order", "takeOutState", "领料状态");

call proc_add_form_field("workOrderMaterialList.edit", "materialListCode", "用料清单号");
call proc_add_form_field("workOrderMaterialList.edit", "state", "单据状态");
call proc_add_form_field("workOrderMaterialList.edit", "relateType", "关联单据类型");
call proc_add_form_field("workOrderMaterialList.edit", "relateNumber", "关联单据编号");
call proc_add_form_field("workOrderMaterialList.edit", "relateMaterialCode", "关联单据物料编码");
call proc_add_form_field("workOrderMaterialList.edit", "relateMaterialName", "关联单据物料名称");
call proc_add_form_field("workOrderMaterialList.edit", "relateAuxiliaryAttr", "关联单据特征参数");
call proc_add_form_field("workOrderMaterialList.edit", "relateQuantity", "关联单据计划数量");
call proc_add_form_field("workOrderMaterialList.edit", "remark", "备注");
call proc_add_form_field("workOrderMaterialList.edit", "createName", "创建人");
call proc_add_form_field("workOrderMaterialList.edit", "createTime", "创建时间");
call proc_add_form_field("workOrderMaterialList.edit", "updateName", "更新人");
call proc_add_form_field("workOrderMaterialList.edit", "updateTime", "更新时间");
call proc_add_form_field("workOrderMaterialList.edit", "takeOutState", "领料状态");
call proc_add_form_field("workOrderMaterialList.edit", "code", "子项物料编码");
call proc_add_form_field("workOrderMaterialList.edit", "name", "子项物料名称");
call proc_add_form_field("workOrderMaterialList.edit", "comp", "子项物料单位");
call proc_add_form_field("workOrderMaterialList.edit", "auxiliaryAttr", "子项特征参数");
call proc_add_form_field("workOrderMaterialList.edit", "bomNumerator", "BOM分子");
call proc_add_form_field("workOrderMaterialList.edit", "bomDenominator", "BOM分母");
call proc_add_form_field("workOrderMaterialList.edit", "stockQuantity", "即时库存");
call proc_add_form_field("workOrderMaterialList.edit", "referenceQuantity", "参考数量");
call proc_add_form_field("workOrderMaterialList.edit", "planQuantity", "计划用量");
call proc_add_form_field("workOrderMaterialList.edit", "actualQuantity", "实际用量");
call proc_add_form_field("workOrderMaterialList.edit", "uncollectedQuantity", "未领数量");
call proc_add_form_field("workOrderMaterialList.edit", "loseRate", "损耗率");
call proc_add_form_field("workOrderMaterialList.edit", "pushDownQuantity", "已下推数量");
call proc_add_form_field("workOrderMaterialList.edit", "returnQuantity", "退料数量");
call proc_add_form_field("workOrderMaterialList.edit", "totalPlanQuantity", "累计计划数量");
call proc_add_form_field("workOrderMaterialList.edit", "totalReceiveQuantity", "累计领用数量");
call proc_add_form_field("workOrderMaterialList.edit", "totalMaterialIssued", "累计补料出库数");
call proc_add_form_field("workOrderMaterialList.edit", "subTypeName", "子项类型");
call proc_add_form_field("workOrderMaterialList.edit", "mainReplaceScale", "主替用量比");
call proc_add_form_field("workOrderMaterialList.edit", "replaceQuantity", "替代数量");


call proc_add_form_field("workOrderMaterialList.detail", "materialListCode", "用料清单号");
call proc_add_form_field("workOrderMaterialList.detail", "state", "单据状态");
call proc_add_form_field("workOrderMaterialList.detail", "relateType", "关联单据类型");
call proc_add_form_field("workOrderMaterialList.detail", "relateNumber", "关联单据编号");
call proc_add_form_field("workOrderMaterialList.detail", "relateMaterialCode", "关联单据物料编码");
call proc_add_form_field("workOrderMaterialList.detail", "relateMaterialName", "关联单据物料名称");
call proc_add_form_field("workOrderMaterialList.detail", "relateAuxiliaryAttr", "关联单据特征参数");
call proc_add_form_field("workOrderMaterialList.detail", "relateQuantity", "关联单据计划数量");
call proc_add_form_field("workOrderMaterialList.detail", "remark", "备注");
call proc_add_form_field("workOrderMaterialList.detail", "createName", "创建人");
call proc_add_form_field("workOrderMaterialList.detail", "createTime", "创建时间");
call proc_add_form_field("workOrderMaterialList.detail", "updateName", "更新人");
call proc_add_form_field("workOrderMaterialList.detail", "updateTime", "更新时间");
call proc_add_form_field("workOrderMaterialList.detail", "takeOutState", "领料状态");
call proc_add_form_field("workOrderMaterialList.detail", "code", "子项物料编码");
call proc_add_form_field("workOrderMaterialList.detail", "name", "子项物料名称");
call proc_add_form_field("workOrderMaterialList.detail", "comp", "子项物料单位");
call proc_add_form_field("workOrderMaterialList.detail", "auxiliaryAttr", "子项特征参数");
call proc_add_form_field("workOrderMaterialList.detail", "bomNumerator", "BOM分子");
call proc_add_form_field("workOrderMaterialList.detail", "bomDenominator", "BOM分母");
call proc_add_form_field("workOrderMaterialList.detail", "stockQuantity", "即时库存");
call proc_add_form_field("workOrderMaterialList.detail", "referenceQuantity", "参考数量");
call proc_add_form_field("workOrderMaterialList.detail", "planQuantity", "计划用量");
call proc_add_form_field("workOrderMaterialList.detail", "actualQuantity", "实际用量");
call proc_add_form_field("workOrderMaterialList.detail", "uncollectedQuantity", "未领数量");
call proc_add_form_field("workOrderMaterialList.detail", "loseRate", "损耗率");
call proc_add_form_field("workOrderMaterialList.detail", "pushDownQuantity", "已下推数量");
call proc_add_form_field("workOrderMaterialList.detail", "returnQuantity", "退料数量");
call proc_add_form_field("workOrderMaterialList.detail", "totalPlanQuantity", "累计计划数量");
call proc_add_form_field("workOrderMaterialList.detail", "totalReceiveQuantity", "累计领用数量");
call proc_add_form_field("workOrderMaterialList.detail", "totalMaterialIssued", "累计补料出库数");
call proc_add_form_field("workOrderMaterialList.detail", "subTypeName", "子项类型");
call proc_add_form_field("workOrderMaterialList.detail", "mainReplaceScale", "主替用量比");
call proc_add_form_field("workOrderMaterialList.detail", "replaceQuantity", "替代数量");


INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'materialListCode', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'relateType', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'relateNumber', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'takeOutState', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'createName', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'state', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'createTime', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'materialListCode', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'relateType', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'relateNumber', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'relateMaterialCode', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'relateMaterialName', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'relateAuxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'state', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'code', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'name', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'auxiliaryAttr', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'comp', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'subTypeName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'createName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'planQuantity', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'actualQuantity', 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'pushDownQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'materialListCode', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'relateType', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'relateNumber', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'relateMaterialCode', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'relateMaterialName', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'relateAuxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'relateQuantity', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'createName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'createTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'updateName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'updateTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'takeOutState', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'code', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'name', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'auxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'planQuantity', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'bomNumerator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'bomDenominator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'referenceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'actualQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'loseRate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'pushDownQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'totalPlanQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'totalReceiveQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'totalMaterialIssued', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'subTypeName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'mainReplaceScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'replaceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'materialListCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'relateType', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'relateNumber', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'relateMaterialCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'relateMaterialName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'relateAuxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'relateQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'createName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'createTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'updateName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'updateTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'takeOutState', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'name', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'auxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'planQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'bomNumerator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'bomDenominator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'referenceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'actualQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'loseRate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'pushDownQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'totalPlanQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'totalReceiveQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'totalMaterialIssued', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'subTypeName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'mainReplaceScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'replaceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'materialListCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'relateType', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'relateNumber', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'relateMaterialCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'relateMaterialName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'relateAuxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'relateQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'createName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'createTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'updateName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'updateTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'takeOutState', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'name', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'auxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'planQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'bomNumerator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'bomDenominator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'referenceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'actualQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'loseRate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'pushDownQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'totalPlanQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'totalReceiveQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'totalMaterialIssued', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'subTypeName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'mainReplaceScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'replaceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'materialListCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'relateType', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'relateNumber', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'relateMaterialCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'relateMaterialName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'relateAuxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'relateQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'createName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'createTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'updateName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'updateTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'takeOutState', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'name', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'auxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'planQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'bomNumerator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'bomDenominator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'referenceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'actualQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'loseRate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'pushDownQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'totalPlanQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'totalReceiveQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'totalMaterialIssued', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'subTypeName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'mainReplaceScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'replaceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'materialListCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'state', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'relateType', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'relateNumber', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'relateMaterialCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'relateMaterialName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'relateAuxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'relateQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'remark', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'createName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'createTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'updateName', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'updateTime', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'takeOutState', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'name', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'auxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'planQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'bomNumerator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'bomDenominator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'referenceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'actualQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'loseRate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'pushDownQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'totalPlanQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'totalReceiveQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'totalMaterialIssued', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'subTypeName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'mainReplaceScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'replaceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'materialListCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'state', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'relateType', 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'relateNumber', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'relateMaterialCode', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'relateMaterialName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'relateAuxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'relateQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'remark', 1, 0, 0, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'createName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'updateName', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'updateTime', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'takeOutState', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'code', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'name', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'comp', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'auxiliaryAttr', 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, '该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突在线小程序：物料特征参数)');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'planQuantity', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'bomNumerator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'bomDenominator', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'stockQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'referenceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'actualQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'loseRate', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'pushDownQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'totalPlanQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'totalReceiveQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'totalMaterialIssued', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'subTypeName', 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'mainReplaceScale', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'replaceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);



-- ****设备检查项目****
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceInspectionPlanItem', '设备检查项目', 'deviceInspectionPlanItem', '', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceInspectionPlanItemList', '设备检查项目列表页', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem', 1, 'list');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceInspectionPlanItemDetail','设备检查项目列详情页', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem', 1, 'detail');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'deviceInspectionPlanItemEdit', '设备检查项目编辑页', 'deviceInspectionPlanItem.edit', 'deviceInspectionPlanItem', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'deviceInspectionPlanItemEditByCreate', '创建态', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit', 1, 'edit-create');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'deviceInspectionPlanItemEditByRelease', '生效态', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit', 1, 'edit-other');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'deviceInspectionPlanItemEditByDisable', '废弃态', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit', 1, 'edit-other');


-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'deviceInspectionProjectTeamCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'deviceInspectionProjectTeamName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'deviceInspectionItemCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'deviceInspectionItemName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'inspectionStandardName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'inspectionContents', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'referenceValue', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldOneName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldTwoName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldThreeName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldFourName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldFiveName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldSixName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldSevenName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldEightName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldNineName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.list', 'itemExtendFieldTenName', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);

call proc_add_form_field('deviceInspectionPlanItem.list', 'deviceInspectionProjectTeamCode', '检查项目组编码');
call proc_add_form_field('deviceInspectionPlanItem.list', 'deviceInspectionProjectTeamName', '检查项目组名称');
call proc_add_form_field('deviceInspectionPlanItem.list', 'deviceInspectionItemCode', '检查项目编码');
call proc_add_form_field('deviceInspectionPlanItem.list', 'deviceInspectionItemName', '检查项目名称');
call proc_add_form_field('deviceInspectionPlanItem.list', 'inspectionStandardName', '检查标准名称');
call proc_add_form_field('deviceInspectionPlanItem.list', 'inspectionContents', '检查内容');
call proc_add_form_field('deviceInspectionPlanItem.list', 'stateName', '状态');
call proc_add_form_field('deviceInspectionPlanItem.list', 'referenceValue', '参考值');
call proc_add_form_field('deviceInspectionPlanItem.list', 'remark', '备注');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldOneName', '扩展字段1');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldTwoName', '扩展字段2');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldThreeName', '扩展字段3');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldFourName', '扩展字段4');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldFiveName', '扩展字段5');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldSixName', '扩展字段6');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldSevenName', '扩展字段7');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldEightName', '扩展字段8');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldNineName', '扩展字段9');
call proc_add_form_field('deviceInspectionPlanItem.list', 'itemExtendFieldTenName', '扩展字段10');


-- 详情
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'deviceInspectionProjectTeamCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'deviceInspectionProjectTeamName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'deviceInspectionItemCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'deviceInspectionItemName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'inspectionStandardName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'inspectionContents', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'referenceValue', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'knowledgeBase', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'conclusionPositive', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'conclusionNegative', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.detail', 'deviceInspectionPlanItem.detail', 'itemExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1, NULL, NULL, 1);

call proc_add_form_field('deviceInspectionPlanItem.detail', 'deviceInspectionProjectTeamCode', '检查项目组编码');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'deviceInspectionProjectTeamName', '检查项目组名称');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'deviceInspectionItemCode', '检查项目编码');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'deviceInspectionItemName', '检查项目名称');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'inspectionStandardName', '检查标准名称');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'inspectionContents', '检查内容');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'stateName', '状态');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'referenceValue', '参考值');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'remark', '备注');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'knowledgeBase', '知识库');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'conclusionPositive', '结论-正向');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'conclusionNegative', '结论-负向');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldOne', '扩展字段1');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldTwo', '扩展字段2');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldThree', '扩展字段3');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldFour', '扩展字段4');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldFive', '扩展字段5');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldSix', '扩展字段6');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldSeven', '扩展字段7');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldEight', '扩展字段8');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldNine', '扩展字段9');
call proc_add_form_field('deviceInspectionPlanItem.detail', 'itemExtendFieldTen', '扩展字段10');



-- 编辑 创建
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionProjectTeamName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionItemCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionItemName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'inspectionStandardName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'inspectionContents', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'stateName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'referenceValue', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'knowledgeBase', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'conclusionPositive', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'conclusionNegative', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldOne', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldTwo', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldThree', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldFour', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldFive', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldSix', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldSeven', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldEight', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldNine', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldTen', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionProjectTeamName', '检查项目组名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionItemCode', '检查项目编码');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'deviceInspectionItemName', '检查项目名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'inspectionStandardName', '检查标准名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'inspectionContents', '检查内容');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'stateName', '状态');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'referenceValue', '参考值');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'remark', '备注');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'knowledgeBase', '知识库');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'conclusionPositive', '结论-正向');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'conclusionNegative', '结论-负向');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldOne', '扩展字段1');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldTwo', '扩展字段2');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldThree', '扩展字段3');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldFour', '扩展字段4');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldFive', '扩展字段5');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldSix', '扩展字段6');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldSeven', '扩展字段7');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldEight', '扩展字段8');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldNine', '扩展字段9');
call proc_add_form_field('deviceInspectionPlanItem.edit.byCreate', 'itemExtendFieldTen', '扩展字段10');



-- 编辑 生效态
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionProjectTeamName', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionItemCode', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionItemName', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'inspectionStandardName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'inspectionContents', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'stateName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'referenceValue', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'knowledgeBase', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'conclusionPositive', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'conclusionNegative', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldOne', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldTwo', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldThree', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldFour', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldFive', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldSix', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldSeven', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldEight', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldNine', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldTen', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionProjectTeamName', '检查项目组名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionItemCode', '检查项目编码');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'deviceInspectionItemName', '检查项目名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'inspectionStandardName', '检查标准名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'inspectionContents', '检查内容');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'stateName', '状态');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'referenceValue', '参考值');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'remark', '备注');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'knowledgeBase', '知识库');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'conclusionPositive', '结论-正向');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'conclusionNegative', '结论-负向');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldOne', '扩展字段1');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldTwo', '扩展字段2');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldThree', '扩展字段3');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldFour', '扩展字段4');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldFive', '扩展字段5');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldSix', '扩展字段6');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldSeven', '扩展字段7');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldEight', '扩展字段8');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldNine', '扩展字段9');
call proc_add_form_field('deviceInspectionPlanItem.edit.byRelease', 'itemExtendFieldTen', '扩展字段10');



-- 编辑 废弃
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionProjectTeamName', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionItemCode', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionItemName', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'inspectionStandardName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'inspectionContents', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'stateName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'referenceValue', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'knowledgeBase', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'conclusionPositive', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'conclusionNegative', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldOne', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldTwo', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldThree', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldFour', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldFive', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldSix', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldSeven', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldEight', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldNine', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/equipment-management/equip-checked/equipCheckProject', 'deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldTen', 0, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 1);
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionProjectTeamName', '检查项目组名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionItemCode', '检查项目编码');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'deviceInspectionItemName', '检查项目名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'inspectionStandardName', '检查标准名称');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'inspectionContents', '检查内容');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'stateName', '状态');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'referenceValue', '参考值');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'remark', '备注');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'knowledgeBase', '知识库');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'conclusionPositive', '结论-正向');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'conclusionNegative', '结论-负向');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldOne', '扩展字段1');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldTwo', '扩展字段2');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldThree', '扩展字段3');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldFour', '扩展字段4');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldFive', '扩展字段5');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldSix', '扩展字段6');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldSeven', '扩展字段7');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldEight', '扩展字段8');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldNine', '扩展字段9');
call proc_add_form_field('deviceInspectionPlanItem.edit.byDisable', 'itemExtendFieldTen', '扩展字段10');
