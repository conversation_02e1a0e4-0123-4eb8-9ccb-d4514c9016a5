-- DDL
-- 客户档案新增扩展字段
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_one',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_one` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段1''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_two',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_two` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段2''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_three',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_three` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段3''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_four',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_four` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段4''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_five',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_five` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段5''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_six',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_six` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段6''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_seven',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_seven` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段7''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_eight',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_eight` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段8''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_nine',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_nine` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段9''');
call proc_add_column(
        'dfs_customer',
        'customer_extend_field_ten',
        'ALTER TABLE `dfs_customer` ADD COLUMN `customer_extend_field_ten` varchar(255) DEFAULT "" COMMENT ''客户档案拓展字段10''');
-- 客户地址新增 `是否为默认地址`字段
call proc_add_column(
        'dfs_customer_address',
        'is_default',
        'ALTER TABLE `dfs_customer_address` ADD COLUMN `is_default` tinyint(4) DEFAULT 0 COMMENT ''是否为默认地址''');
-- 为每个客户设置第一个地址为默认地址（如果存在地址的话）
UPDATE `dfs_customer_address` ca1
SET `is_default` = 1
WHERE ca1.id = (
    SELECT MIN(ca2.id)
    FROM (SELECT * FROM `dfs_customer_address`) ca2
    WHERE ca2.customer_id = ca1.customer_id
);

call proc_add_column_index('dfs_device_shift_energy_consumption','record_date','record_date');




-- DML
-- 刷新dfs编码规则相关类型提示
UPDATE dfs_rule_type_config SET type_code = 'customerCode' WHERE `type` = '78';
UPDATE dfs_rule_type_config SET type_code = 'supplierCode' WHERE `type` = '3';
UPDATE dfs_rule_type_config SET type_code = 'packageOrderNumber' WHERE `type` = '86';
UPDATE dfs_rule_type_config SET type_code = 'batteryCode' WHERE `type` = '97';
UPDATE dfs_rule_type_config SET type_code = 'bomVersion' WHERE `type` = '105';
UPDATE dfs_rule_type_config SET type_code = 'craftVersion' WHERE `type` = '106';
UPDATE dfs_rule_type_config SET type_code = 'defectCode' WHERE `type` = '102';
UPDATE dfs_rule_type_config SET type_code = 'maintainCode' WHERE `type` = '103';
UPDATE dfs_rule_type_config SET type_code = 'defectTypeCode' WHERE `type` = '88';
UPDATE dfs_rule_type_config SET type_code = 'workOrderMaterialList' WHERE `type` = '77';
UPDATE dfs_rule_type_config SET type_code = 'bomCode' WHERE `type` = '4';
UPDATE dfs_rule_type_config SET type_code = 'craftCode' WHERE `type` = '26';
UPDATE dfs_rule_type_config SET type_code = 'replaceSchemeCode' WHERE `type` = '56';
UPDATE dfs_rule_type_config SET type_code = 'auxiliaryAttrCode' WHERE `type` = '58';
UPDATE dfs_rule_type_config SET type_code = 'procedureDefCode' WHERE `type` = '98';
UPDATE dfs_rule_type_config SET type_code = 'packageSchemeCode' WHERE `type` = '60';
UPDATE dfs_rule_type_config SET type_code = 'capacityLabel' WHERE `type` = 'capacityLabel';

-- 销售订单 编辑页
delete from `dfs_form_field_config` where full_path_code in ('saleOrder.edit') and field_code = 'materialRemark' and module_code = 'baseMaterialLineField';

-- 销售订单 详情页
delete from `dfs_form_field_rule_config` where full_path_code in ('saleOrder.editByCreate','saleOrder.editByRelease','saleOrder.editByFinish','saleOrder.editByClosed','saleOrder.editByCancel','saleOrder.detail') and field_code = 'materialRemark' and module_code = 'baseMaterialLineField';

-- 修复企业微信审批物料编码字段
update dfs_approve_node_field set  field_code = 'materialCode' where full_path_code = 'saleOrder.releasedApprove' and  field_code = 'code';

-- 工单报工 项目名称
call proc_add_form_field("workOrderReport.workOrderList", "projectDefineName", "项目名称");
call proc_add_form_field("workOrderReport.workOrderDetail.detail", "projectDefineName", "项目名称");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, 'yelink.workOrder.report.com', 'workOrderReport.workOrderList', 'workOrderReport.workOrderList', 'projectDefineName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, 'yelink.workOrder.report.com', 'workOrderReport.workOrderDetail.detail', 'workOrderReport.workOrderDetail.detail', 'projectDefineName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 创建单据下推标识表
CREATE TABLE IF NOT EXISTS `dfs_order_push_down_identifier`
    `id`                    int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `order_type`            varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '源单据类型',
    `order_material_id`     varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单据物料行id',
    `batch_number`          varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批次号',
    `value_full_path_code`  varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '软件参数全路径编码',
    `target_order_type`     varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目标单据类型',
    `state`                 varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下推标识状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `unique_key` (`order_type`,`order_material_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='单据下推标识表';

-- 销售订单 项目名称
call proc_add_form_field("saleOrder.list.order", "projectDefineName", "项目名称");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/order-model/salesOrder', 'saleOrder.list.order', 'saleOrder.list.order', 'projectDefineName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
