-- DDL

call proc_add_column(
        'dfs_manage_source_field',
        'input_type',
        'ALTER TABLE `dfs_manage_source_field` ADD COLUMN `input_type`  varchar(255) NULL COMMENT ''输入框类型'';');
call proc_add_column(
        'dfs_manage_source_field',
        'option_values_type',
        'ALTER TABLE `dfs_manage_source_field` ADD COLUMN `option_values_type`  varchar(255) NULL COMMENT ''可选值来源类型'' AFTER `input_type`;');
call proc_add_column(
        'dfs_manage_source_field',
        'all_option',
        'ALTER TABLE `dfs_manage_source_field` ADD COLUMN `all_option`  text NULL COMMENT ''全选值'' AFTER `option_values_type`;');

call proc_add_column(
        'dfs_table_config',
        'field_define',
        'ALTER TABLE `dfs_table_config` ADD COLUMN `field_define` varchar(255) DEFAULT NULL COMMENT ''字段定义: DIMENSION-维度, TARGET-指标，ATTACHMENT-附属信息, '';');

ALTER TABLE `dfs_bom_tile` COMMENT = 'BOM父子物料关系表';
ALTER TABLE `dfs_command_record` COMMENT = '下发命令记录表';
ALTER TABLE `dfs_config_cleanable_table` COMMENT = '可清除表';
ALTER TABLE `dfs_info_notice_record` COMMENT = '消息通知记录表';
ALTER TABLE `dfs_order_execute_seq` COMMENT = '工单投产顺序表';
ALTER TABLE `dfs_procedure_file` COMMENT = '工序附件表';
ALTER TABLE `dfs_procedure_material` COMMENT = '工序用料表表';
ALTER TABLE `dfs_record_device_resume_daily` COMMENT = '设备履历表';
ALTER TABLE `dfs_redis_call_back` COMMENT = '回调备份表';
ALTER TABLE `dfs_supplier` COMMENT = '供应商表';
ALTER TABLE `dfs_target_threshold` COMMENT = '指标阈值表';
ALTER TABLE `dfs_video` COMMENT = '视频设备表';
ALTER TABLE `dfs_work_order_material_list_material` COMMENT = '工单用料清单表';
ALTER TABLE `sys_department_user` COMMENT = '部门用户关联表';
ALTER TABLE `sys_permissions` COMMENT = '权限表';
ALTER TABLE `sys_role_line` COMMENT = '角色产线关联表';
ALTER TABLE `sys_role_permission` COMMENT = '角色权限关联表';
ALTER TABLE `sys_roles` COMMENT = '角色表';
ALTER TABLE `sys_user_role` COMMENT = '用户角色关联表';
ALTER TABLE `dfs_facilities` COMMENT = '工位表';

-- 列配置默认计划、生产方案
CREATE TABLE IF NOT EXISTS `dfs_column_configuration_default`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `modular_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '列配置所属模块',
  `column_type` varchar(50) NOT NULL COMMENT '列配置名称',
  `column_information` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '列配置列展示信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_column_name_modular_type` (`column_type`, `modular_type`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '列配置默认计划、生产方案';
-- 编码规则相关表字段修改为字符类型
call proc_modify_column(
        'dfs_rule_prefix_config',
        'code',
        'ALTER TABLE `dfs_rule_prefix_config` CHANGE COLUMN `code` `code` varchar(100) NOT NULL COMMENT ''编码''');
call proc_modify_column(
        'dfs_rule_prefix_extend',
        'prefix_code',
        'ALTER TABLE `dfs_rule_prefix_extend` CHANGE COLUMN `prefix_code` `prefix_code` varchar(100) NOT NULL COMMENT ''关联的编码规则组成信息''');
call proc_modify_column(
        'dfs_rule_prefix_config',
        'type',
        'ALTER TABLE `dfs_config_number_rules` CHANGE COLUMN `type` `type` varchar(100) NOT NULL COMMENT ''编号规则类型编码''');
call proc_modify_column(
        'dfs_rule_type_config',
        'type',
        'ALTER TABLE `dfs_rule_type_config` CHANGE COLUMN `type` `type` varchar(100) NOT NULL COMMENT ''编号规则类型编码''');

-- 是否支持企微审批
call proc_add_column(
        'dfs_approve_node_config',
        'can_wechat_approve',
        'ALTER TABLE `dfs_approve_node_config` ADD COLUMN `can_wechat_approve` tinyint(4) NULL COMMENT ''是否支持企微审批'';');

-- 条码添加客户和供应商信息
call proc_add_column(
        'dfs_product_flow_code',
        'customer_code',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `customer_code` varchar(255) NULL COMMENT ''客户编码'';');
call proc_add_column(
        'dfs_product_flow_code',
        'customer_name',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `customer_name` varchar(255) NULL COMMENT ''客户名称'';');
call proc_add_column(
        'dfs_product_flow_code',
        'customer_material_code',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `customer_material_code` varchar(255) NULL COMMENT ''客户物料编码'';');
call proc_add_column(
        'dfs_product_flow_code',
        'customer_material_name',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `customer_material_name` varchar(255) NULL COMMENT ''客户物料名称'';');
call proc_add_column(
        'dfs_product_flow_code',
        'customer_specification',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `customer_specification` varchar(255) NULL COMMENT ''客户物料规格'';');
call proc_add_column(
        'dfs_product_flow_code',
        'supplier_material_code',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `supplier_material_code` varchar(255) NULL COMMENT ''供应商物料编码'';');
call proc_add_column(
        'dfs_product_flow_code',
        'supplier_material_name',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `supplier_material_name` varchar(255) NULL COMMENT ''供应商物料名称'';');
call proc_add_column(
        'dfs_product_flow_code',
        'supplier_specification',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `supplier_specification` varchar(255) NULL COMMENT ''供应商物料规格'';');

call proc_add_column(
        'dfs_bar_code',
        'customer_code',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `customer_code` varchar(255) NULL COMMENT ''客户编码'';');
call proc_add_column(
        'dfs_bar_code',
        'customer_name',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `customer_name` varchar(255) NULL COMMENT ''客户名称'';');
call proc_add_column(
        'dfs_bar_code',
        'customer_material_code',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `customer_material_code` varchar(255) NULL COMMENT ''客户物料编码'';');
call proc_add_column(
        'dfs_bar_code',
        'customer_material_name',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `customer_material_name` varchar(255) NULL COMMENT ''客户物料名称'';');
call proc_add_column(
        'dfs_bar_code',
        'customer_specification',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `customer_specification` varchar(255) NULL COMMENT ''客户物料规格'';');
call proc_add_column(
        'dfs_bar_code',
        'supplier_material_code',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `supplier_material_code` varchar(255) NULL COMMENT ''供应商物料编码'';');
call proc_add_column(
        'dfs_bar_code',
        'supplier_material_name',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `supplier_material_name` varchar(255) NULL COMMENT ''供应商物料名称'';');
call proc_add_column(
        'dfs_bar_code',
        'supplier_specification',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `supplier_specification` varchar(255) NULL COMMENT ''供应商物料规格'';');
call proc_add_column(
        'dfs_bar_code',
        'supplier_code',
        'ALTER TABLE `dfs_bar_code` ADD COLUMN `supplier_code` varchar(255) NULL COMMENT ''供应商编码'';');

call proc_modify_column(
        'dfs_scan_code_rule_resolver',
        'field_code',
        'ALTER TABLE `dfs_scan_code_rule_resolver` MODIFY COLUMN `field_code` varchar(255) NULL DEFAULT ''result'' COMMENT ''字段编码'';');

call proc_modify_column(
        'dfs_info_notice_record',
        'notice_desc',
        'ALTER TABLE `dfs_info_notice_record` MODIFY COLUMN `notice_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT ''消息通知描述''');


-- DML

update dfs_approve_node_config set can_wechat_approve = 1 where full_path_code = 'workOrder.releasedApprove';
-- 删除旧qms配置
delete from dfs_approve_node_config where full_path_code = 'productInspectionReportProject' or parent_full_path_code = 'productInspectionReportProject';
delete from dfs_approve_node_config where full_path_code = 'productInspectionProject' or parent_full_path_code = 'productInspectionProject';
delete from dfs_approve_node_config where full_path_code = 'productInspectionPlan' or parent_full_path_code = 'productInspectionPlan';
delete from dfs_approve_node_config where full_path_code = 'productInspectionResultProject' or parent_full_path_code = 'productInspectionResultProject';
delete from dfs_approve_node_config where full_path_code = 'antenatalPreparationInspectionItems' or parent_full_path_code = 'antenatalPreparationInspectionItems';
delete from dfs_approve_node_config where full_path_code = 'antenatalPreparationInspectionPlan' or parent_full_path_code = 'antenatalPreparationInspectionPlan';
delete from dfs_approve_node_config where full_path_code = 'incomingInspection' or parent_full_path_code = 'incomingInspection';
delete from dfs_approve_node_config where full_path_code = 'inspectionSheet' or parent_full_path_code = 'inspectionSheet';
delete from dfs_approve_node_config where full_path_code = 'productInspectionSample' or parent_full_path_code = 'productInspectionSample';

-- 生产订单日计划下推 修改为 生产订单计划下推
UPDATE `dfs_dict` SET `name` = '按订单计划下推' WHERE `code`= 'production.productOrderPushDownConfig.workOrder.productOrderDaily';
UPDATE `dfs_order_push_down_item` SET `name` = '生产订单计划下推' WHERE `instance_type` = 'production.productOrderPushDownConfig.workOrder.productOrderDaily';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"生产订单如果创建的是工序日计划，且工艺路线中仅有单个模型(制造单元/设备/班组)，无论什么情况都是按工序下推。工艺路线中有混合模型，则制造单元按工作中心下推，设备/班组按工序下推\"' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.workOrder.productOrderDaily.description';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"生产订单如果创建的是工序日计划，且工艺路线中仅有单个模型(制造单元/设备/班组)，无论什么情况都是按工序下推。工艺路线中有混合模型，则制造单元按工作中心下推，设备/班组按工序下推\"' WHERE `value_full_path_code` = 'production.productOrderPushDownConfig.workOrder.productOrderDaily.description';


-- 销售订单审批配置
INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`) VALUES ('saleOrder.releasedApprove', 'baseField', '基础信息', 1);
INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`,`value_source`) VALUES ('saleOrder.releasedApprove', 'materialField', '物料信息', 2,'saleOrderMaterials');


INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderNumber','订单编号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'stateName','状态');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'customerName','客户名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'salesmanName','销售员');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'createName','创建人');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'updateName','更新人');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'createTime','创建时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'importTime','导入时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'approvalTime','审批时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'updateTime','更新时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'customerCode','客户编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'actualApproverName','实际审批人');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderSource','订单来源');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'customerContractNo','客户合同号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'remark','订单备注');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'salesmanCode','销售员简称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'projectDefineName','项目名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'contractName','销售合同');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderTypeName','单据类型');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'businessTypeName','业务类型');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'customerDemand','客户需求');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldOneName','销售订单扩展字段1');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldTwoName','销售订单扩展字段2');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldThreeName','销售订单扩展字段3');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldFourName','销售订单扩展字段4');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldFiveName','销售订单扩展字段5');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldSixName','销售订单扩展字段6');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldSevenName','销售订单扩展字段7');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldEightName','销售订单扩展字段8');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldNineName','销售订单扩展字段9');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'baseField', 'saleOrderExtendFieldTenName','销售订单扩展字段10');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'planDeliveryDate','计划发货日期');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'orderDate','下单日期');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'code','物料编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'materialFields.name','物料名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'materialFields.standard','物料规格');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'planTotalPrice','总价');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'erpDocumentCode','ERP同步单号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'plannedProductionQuantity','计划生产数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'scheduledProductionQuantity','排产数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'productionQuantity','生产数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'appliedShipmentQuantity','出货申请数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'salesQuantity','销售数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'appliedQuantity','出货数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'plannedBatches','计划批数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'plansPerBatch','每批计划数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'identifierList','单据标识');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'priority','优先级');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'requireGoodsDate','要货日期');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'unqualifiedQuantity','不合格数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'haveBom','BOM');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'haveCraft','物料工艺');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'shipmentStatusName','发货状态');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'customerOrderNumber','客户订单编号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'customerMaterialName','客户物料名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'customerSpecification','客户物料规格');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'materialChangeTime','订单物料变更时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'materialIsChange','订单物料是否变更');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'synchronizationTime','ERP同步时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'deliveryDate','发货日期');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'notDeliveredQuantity','未发货数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'outStockQuantity','出库数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'returnQuantity','退货数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'passRate','合格率');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'completionRate','完成率');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'inventoryQuantity','当前库存');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'processStatusName','生产状态');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'auxiliaryAttr','特征参数');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'customerMaterialCode','客户物料编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'unitSalesQuantity','销售数量（计量单位)');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldOneName','销售订单物料扩展字段1');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldTwoName','销售订单物料扩展字段2');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldThreeName','销售订单物料扩展字段3');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldFourName','销售订单物料扩展字段4');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldFiveName','销售订单物料扩展字段5');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldSixName','销售订单物料扩展字段6');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldSevenName','销售订单物料扩展字段7');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldNineName','销售订单物料扩展字段9');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldEightName','销售订单物料扩展字段8');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('saleOrder.releasedApprove', 'materialField', 'saleOrderMaterialExtendFieldTenName','销售订单物料扩展字段10');

update dfs_approve_node_config  set code = 'saleOrder' ,full_path_code = 'saleOrder'  where  name = '销售订单';
update dfs_approve_node_config  set full_path_code = 'saleOrder.releasedApprove' ,parent_full_path_code = 'saleOrder'  where  full_path_code = 'salesOrder.releasedApprove';

-- 兼容历史数据
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":1', '"code":\"1\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":2', '"code":\"2\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":4', '"code":\"4\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":5', '"code":\"5\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":3', '"code":\"3\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":6', '"code":\"6\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":7', '"code":\"7\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":9', '"code":\"9\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":10', '"code":\"10\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":12', '"code":\"12\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":13', '"code":\"13\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":14', '"code":\"14\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":15', '"code":\"15\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":16', '"code":\"16\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":17', '"code":\"17\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":18', '"code":\"18\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":19', '"code":\"19\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":20', '"code":\"20\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":21', '"code":\"21\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":22', '"code":\"22\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":23', '"code":\"23\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":24', '"code":\"24\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":25', '"code":\"25\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":26', '"code":\"26\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":27', '"code":\"27\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":28', '"code":\"28\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":29', '"code":\"29\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":30', '"code":\"30\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":31', '"code":\"31\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":32', '"code":\"32\"');
UPDATE dfs_config_number_rules SET prefix_detail = REPLACE(prefix_detail,'"code":33', '"code":\"33\"');

call proc_add_column(
        'dfs_approve_docking_config',
        'submit_approve_user',
        'ALTER TABLE `dfs_approve_docking_config` ADD COLUMN `submit_approve_user` varchar(255) NULL COMMENT ''提交审批申请人账号'';');
call proc_add_column(
        'dfs_approve_record',
        'approver_id',
        'ALTER TABLE `dfs_approve_record` ADD COLUMN `approver_id` varchar(255) NULL COMMENT ''审批人账号'';');

-- 工艺工序参数
call proc_add_column(
        'dfs_process_parameter_config',
        'reference_upper',
        'ALTER TABLE `dfs_process_parameter_config` ADD COLUMN `reference_upper` double(16, 8) NULL DEFAULT NULL COMMENT ''参考值上限'';');
call proc_add_column(
        'dfs_process_parameter_config',
        'reference_lower',
        'ALTER TABLE `dfs_process_parameter_config` ADD COLUMN `reference_lower` double(16, 8) NULL DEFAULT NULL COMMENT ''参考值下限'';');
call proc_add_column(
        'dfs_process_parameter_config',
        'device_model_id',
        'ALTER TABLE `dfs_process_parameter_config` ADD COLUMN `device_model_id` int(11) DEFAULT NULL COMMENT ''设备模型id'';');


-- 工序定义增加启用状态
call proc_add_column(
        'dfs_procedure',
        'is_enable',
        'ALTER TABLE `dfs_procedure` ADD COLUMN `is_enable` tinyint(4) DEFAULT ''1'' COMMENT ''是否启用'';');
update dfs_procedure set is_enable = 0 WHERE type is null or type = '';


-- 新增业务配置（工序定义关联工作中心为单选还是多选）
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('procedureDefConfig', '工序定义配置', 'design.procedureDefConfig', 'design', NULL, 'yelinkoncall', 'yelinkoncall', '2024-05-09 15:50:32', '2024-05-09 15:50:32', '');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('relatedWorkCenterConfig', '工作中心配置', 'design.procedureDefConfig.relatedWorkCenterConfig', 'design.procedureDefConfig', '该配置用来限制工序定义关联工作中心的数量，建议关联单个工作中心，如果需要关联多个，可通过工作中心关联多个资源来解决问题', 'yelinkoncall', 'admin', '2024-05-09 15:50:32', '2024-12-19 10:49:15', '');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '工序关联多工作中心', 'design.procedureDefConfig.relatedWorkCenterConfig.enable', 'design.procedureDefConfig.relatedWorkCenterConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"单个工作中心\"},{\"value\":false,\"label\":\"多个工作中心\"}]', 'true', NULL);

-- 单据删除记录中的物料编码调整为没必填
call proc_modify_column(
        'dfs_delete_record',
        'material_code',
        'ALTER TABLE `dfs_delete_record` CHANGE COLUMN `material_code` `material_code` varchar(255) NULL COMMENT ''物料编码''');


call proc_add_column(
        'dfs_target_threshold',
        'trigger_type',
        'ALTER TABLE `dfs_target_threshold` ADD COLUMN `trigger_type` varchar(255) NOT NULL DEFAULT ''CONSTANT'' COMMENT ''CONSTANT-固定值; INTERFACE-按接口; CRAFT_PROCEDURE-按工艺工序'';');
update dfs_target_threshold set trigger_type = 'INTERFACE' where extra_interface_enable = 1;


call proc_add_column(
        'dfs_scan_code_rule',
        'can_delete',
        'ALTER TABLE `dfs_scan_code_rule` ADD COLUMN `can_delete` tinyint(4) DEFAULT ''1'' COMMENT ''是否可以删除'';');
call proc_add_column(
        'dfs_scan_code_rule',
        'name',
        'ALTER TABLE `dfs_scan_code_rule` ADD COLUMN `name` varchar(255) NULL COMMENT ''名称'';');
call proc_add_column(
        'dfs_scan_code_rule',
        'test_code',
        'ALTER TABLE `dfs_scan_code_rule` ADD COLUMN `test_code` varchar(255) NULL COMMENT ''测试数据'';');
call proc_add_column(
        'dfs_scan_code_rule',
        'data_format',
        'ALTER TABLE `dfs_scan_code_rule` ADD COLUMN `data_format` varchar(255) NULL DEFAULT ''json'' COMMENT ''返回数据格式'';');
call proc_add_column(
        'dfs_scan_code_rule',
        'test_result',
        'ALTER TABLE `dfs_scan_code_rule` ADD COLUMN `test_result` varchar(255) NULL COMMENT ''测试结果'';');

call proc_add_column(
        'dfs_scan_code_rule_resolver',
        'test_result',
        'ALTER TABLE `dfs_scan_code_rule_resolver` ADD COLUMN `test_result` varchar(255) NULL COMMENT ''测试结果'';');

-- 先取备注值，还为空，则取code值
update dfs_scan_code_rule set name = remark where name is null or name = '';
update dfs_scan_code_rule set name = code where name is null or name = '';
update dfs_scan_code_rule set can_delete = 0;

-- 字段配置：工单投产、挂起状态不允许编辑工艺工序
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0,`edit_gray` = 1,`is_need` = 0 WHERE `field_code` = 'procedureIds' AND `full_path_code` in ('workOrder.editByInvestment', 'workOrder.editByHangUp');

-- 历史脏数据
update dfs_model set pid = 1 WHERE pid is null and type = 'device';
delete from sys_permissions WHERE path = 'procedure:downDefaultTemplate';

INSERT INTO sys_permissions
(id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data)
VALUES('10013110', '导出_下载默认模板', 'work.schedule.production:export:downDefaultTemplate', NULL, NULL, NULL, NULL, '2024-05-17 16:51:21', 'enable', 'POST', '10013', 2, 1, 0, '/order-model/work-schedule-production', 1, NULL, '', 1);
INSERT INTO sys_permissions
(id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data)
VALUES('10013120', '导出_上传下载自定义导出模板', 'work.schedule.production:export:downUploadTemplate', NULL, NULL, NULL, NULL, '2024-05-17 16:51:21', 'enable', 'POST', '10013', 2, 1, 0, '/order-model/work-schedule-production', 1, NULL, '', 1);
INSERT INTO sys_permissions
(id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data)
VALUES('10013130', '导出_导出excel', 'work.schedule.production:export:excel', NULL, NULL, NULL, NULL, '2024-05-17 16:51:21', 'enable', 'POST', '10013', 2, 1, 0, '/order-model/work-schedule-production', 1, NULL, '', 1);
INSERT INTO sys_permissions
(id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data)
VALUES('10013140', '导出_查看日志', 'work.schedule.production:export:log', NULL, NULL, NULL, NULL, '2024-05-17 16:51:21', 'enable', 'POST', '10013', 2, 1, 0, '/order-model/work-schedule-production', 1, NULL, '', 1);
call init_new_role_permission('10013%');

-- 销售出货单增加外部审批编号
call proc_add_column(
        'dfs_delivery_application',
        'outer_approve_number',
        'ALTER TABLE `dfs_delivery_application` ADD COLUMN `outer_approve_number` varchar(255) DEFAULT NULL COMMENT ''外部审批编号'';');


-- 销售订单审批配置
INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`) VALUES ('deliveryApplication.releasedApprove', 'baseField', '基础信息', 1);
INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`,`value_source`) VALUES ('deliveryApplication.releasedApprove', 'materialField', '物料信息', 2,'materialEntities');


update dfs_approve_node_config set can_wechat_approve = 1 where full_path_code = 'deliveryApplication.releasedApprove';
update dfs_approve_node_config set name = '销售出货单' where full_path_code = 'deliveryApplication' and name = '出货申请';
update dfs_approve_node_config set can_wechat_approve = 1 where full_path_code = 'saleOrder.releasedApprove';


INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'applicationNum','销售出货单号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'saleOrderNumber','销售订单编号');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'stateName','状态');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'relateWarehouses','物料所在仓库');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'planDeliveryTime','计划出货时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'customerName','客户名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'createByName','创建人');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'createTime','创建时间');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'orderTypeName','单据类型');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'baseField', 'businessTypeName','业务类型');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'materialFields.code','物料编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'materialFields.name','物料名称');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'isGiftName','是否赠品');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'amount','出货数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'salesQuantity','销售数量');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'materialFields.standard','物料规格');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'customerMaterialCode','客户物料编码');
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`) VALUES ('deliveryApplication.releasedApprove', 'materialField', 'customerMaterialName','客户物料名称');

delete from dfs_form_field_config  where  full_path_code = 'deliveryApplication.list.material' and field_code = 'materialNum';
call proc_add_form_field("deliveryApplication.list.material", "amount", "出货数量");

-- 部门组织的负责人可能存在存姓名和用户名的问题，现需使用脚本统一刷新成用户名
UPDATE `sys_department` d,`sys_users` u
SET d.`responsible_name` = u.`user_name`
WHERE d.`responsible_name` = u.`nick_name`
AND d.`responsible_name` IS NOT NULL
AND d.`responsible_name` != '';
-- 部门中不存在的部门负责人设置为NULL
UPDATE sys_department d
SET d.responsible_name = NULL
WHERE d.responsible_name IS NOT NULL
  AND NOT EXISTS (
    SELECT 1
    FROM sys_users u
    WHERE u.user_name = d.responsible_name
);

-- 将下推配置中的描述字段设置为textarea，适配文字比较长的描述
UPDATE `dfs_order_push_down_config_value_dict` SET `input_type` = 'textarea' WHERE `value_code` = 'description';
UPDATE `dfs_order_push_down_config_value` SET `input_type` = 'textarea' WHERE `value_code` = 'description';


-- 为防止类似于君兰名成工艺路线下级工序关联的数据混乱问题，需要跑脚本统一刷新为正确的关系
CREATE TABLE dfs_craft_procedure_dfsV3_18_backup LIKE dfs_craft_procedure;
INSERT INTO dfs_craft_procedure_dfsV3_18_backup SELECT * FROM dfs_craft_procedure;

UPDATE dfs_craft_procedure SET `next_procedure_id` = NULL, `next_procedure_name` = NULL;

DROP PROCEDURE IF EXISTS FillNextProcedureInfo;
DELIMITER $$
CREATE DEFINER=`root`@`%` PROCEDURE `FillNextProcedureInfo`()
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE current_id INT;
    DECLARE current_procedure_name VARCHAR(100);
    DECLARE sup_procedure_ids VARCHAR(255);

    -- 游标遍历所有有上级节点的记录
    DECLARE cur CURSOR FOR
    SELECT id, procedure_name, sup_procedure_id
    FROM dfs_craft_procedure
    WHERE sup_procedure_id IS NOT NULL;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    OPEN cur;

    read_loop: LOOP
        FETCH cur INTO current_id, current_procedure_name, sup_procedure_ids;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 遍历每个上级节点（支持多级上级）
        WHILE LENGTH(sup_procedure_ids) > 0 DO
            SET @sup_id = SUBSTRING_INDEX(sup_procedure_ids, ',', 1);
            SET sup_procedure_ids = CASE
                WHEN LOCATE(',', sup_procedure_ids) > 0
                THEN SUBSTRING(sup_procedure_ids, LOCATE(',', sup_procedure_ids) + 1)
                ELSE ''
        END;

        -- 更新上级节点的下级信息
        UPDATE dfs_craft_procedure
        SET
            next_procedure_id = CASE
                    WHEN next_procedure_id IS NULL THEN current_id
                    ELSE CONCAT(next_procedure_id, ',', current_id)
            END,
            next_procedure_name = CASE
                    WHEN next_procedure_name IS NULL THEN current_procedure_name
                    ELSE CONCAT(next_procedure_name, ',', current_procedure_name)
            END
        WHERE id = @sup_id and next_procedure_id is null;
        END WHILE;
        END LOOP;
    CLOSE cur;
END $$
DELIMITER ;
call FillNextProcedureInfo();
-- 重新删除存储过程
DROP PROCEDURE IF EXISTS FillNextProcedureInfo;
