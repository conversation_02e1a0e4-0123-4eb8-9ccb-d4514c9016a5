-- 新增设备不良数
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `model_code`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`, `default_frequency`, `default_frequency_unit`) VALUES ('dayUnqualified', '设备不良数', '1', 'device', NULL, NULL, NULL, '', now(), NULL, NULL, 1, NULL, NULL);

-- 删除设备产量的方法-通过计数器上报
DELETE FROM `dfs_target_method` WHERE `target_name` = 'dayCount' and `method_name` = 'dayCountCalCounter';

-- 增加不合格数指标方法
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('dayUnqualified', 'device', '指标上报不合格数', 'dayUnqualifiedCalByTarget', 1, NULL);

CREATE TABLE IF NOT EXISTS `dfs_counter_target_config` (
 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
 `relate_target_name` varchar(255) NOT NULL COMMENT '关联指标',
 `model_id` int(11) DEFAULT NULL COMMENT '模型id',
 `target_model_id` int(11) DEFAULT NULL COMMENT 'targetModelId',
 `count_object` varchar(255) DEFAULT NULL COMMENT '计数对象',
 PRIMARY KEY (`id`),
 KEY `model_id` (`model_id`),
 KEY `count_object` (`count_object`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计数指标配置表';

-- 将计数指标迁移至新表
INSERT INTO `dfs_counter_target_config`(`relate_target_name`, `model_id`, `target_model_id`, `count_object`) SELECT target_name,model_id,id,'reportLine' FROM `dfs_target_model` where is_count_target = true;

-- 注释说明字段废弃
call proc_modify_column(
        'dfs_target_model',
        'is_count_target',
        'ALTER TABLE `dfs_target_model` MODIFY COLUMN `is_count_target` tinyint(4) NULL DEFAULT 0 COMMENT ''是否参与产线计数(废弃)''');

-- 修改注释
call proc_modify_column(
        'dfs_report_line',
        'auto_count',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `auto_count` double(11, 2) NULL DEFAULT NULL COMMENT ''采集器采集数量''');

-- 增加自动采集不良数
call proc_add_column(
        'dfs_report_line',
        'auto_unqualified',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `auto_unqualified` double(11, 2) NULL COMMENT ''采集器采集不良数'' AFTER `auto_count`');

call proc_modify_column(
        'dfs_report_line',
        'finish_count',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `finish_count` double(11, 3) NULL DEFAULT 0.000 COMMENT ''完成数量''');

-- 自动计数设置autoCount
update `dfs_report_line` set auto_count = finish_count where type = 'auto' and auto_count is null;

-- 工单报工、订单报工允许不填完成数量
UPDATE `dfs_form_field_rule_config` SET `need_gray` = 0 WHERE `route` = 'yelink.workOrder.report.com' and `full_path_code` = 'workOrderReport.addReport' and `field_name_full_path_code` = 'workOrderReport.addReport' and `field_code` = 'finishCount';

UPDATE `dfs_form_field_rule_config` SET `need_gray` = 0 WHERE `route` = 'yelink.workOrder.report.com' and `full_path_code` = 'workOrderReport.editReport' and `field_name_full_path_code` = 'workOrderReport.editReport' and `field_code` = 'finishCount';

UPDATE `dfs_form_field_rule_config` SET `need_gray` = 0 WHERE `route` = 'yelink.workorder.report' and `full_path_code` = 'orderReportApp.addReport' and `field_name_full_path_code` = 'orderReportApp.addReport' and `field_code` = 'finishCount';

UPDATE `dfs_form_field_rule_config` SET `need_gray` = 0 WHERE `route` = 'yelink.workorder.report' and `full_path_code` = 'orderReportApp.editReport' and `field_name_full_path_code` = 'orderReportApp.editReport' and `field_code` = 'finishCount';






