-- DDL
-- 下推配置新增`组类型`，用于细分配置项的分类，目前有源单据配置、目标单据配置、系统配置
call proc_add_column(
        'dfs_order_push_down_config_value_dict',
        'group_type',
        'ALTER TABLE `dfs_order_push_down_config_value_dict` ADD COLUMN `group_type` varchar(100) NOT NULL DEFAULT ''targetConf'' COMMENT ''组类型，用于细分配置项的分类，目前有源单据配置、目标单据配置、系统配置''');
call proc_add_column(
        'dfs_order_push_down_config_value',
        'group_type',
        'ALTER TABLE `dfs_order_push_down_config_value` ADD COLUMN `group_type` varchar(100) NOT NULL DEFAULT ''targetConf'' COMMENT ''组类型，用于细分配置项的分类，目前有源单据配置、目标单据配置、系统配置''');
-- api新增唯一编码字段
call proc_add_column(
        'dfs_api_transform',
        'code',
        'ALTER TABLE `dfs_api_transform` ADD COLUMN `code` varchar(255) NULL NOT NULL COMMENT ''唯一编码''');

-- 工艺参数新增排序字段
call proc_add_column(
        'dfs_procedure_process_parameter',
        'sort',
        'ALTER TABLE `dfs_procedure_process_parameter` ADD COLUMN `sort` int(11) NULL DEFAULT 1 COMMENT ''排序''');
call proc_add_column(
        'dfs_process_parameter_config',
        'sort',
        'ALTER TABLE `dfs_process_parameter_config` ADD COLUMN `sort` int(11) NULL DEFAULT 1 COMMENT ''排序''');

-- 指标模型
call proc_add_column(
        'dfs_target_model',
        'last_time',
        'ALTER TABLE `dfs_target_model` ADD COLUMN `last_time` datetime DEFAULT NULL COMMENT ''最近一次触发时间''');

-- 删除设备表无用字段
call proc_modify_column(
        'dfs_device',
        'lat',
        'ALTER TABLE `dfs_device` DROP COLUMN `lat`');
call proc_modify_column(
        'dfs_device',
        'lng',
        'ALTER TABLE `dfs_device` DROP COLUMN `lng`');
call proc_modify_column(
        'dfs_device',
        'alt',
        'ALTER TABLE `dfs_device` DROP COLUMN `alt`');
call proc_modify_column(
        'dfs_device',
        'location',
        'ALTER TABLE `dfs_device` DROP COLUMN `location`');

-- 批次表增加索引
call proc_add_column_index('dfs_bar_code','relate_number','relate_number');


-- api新增唯一编码字段
call proc_add_column(
        'dfs_maintain_record',
        'back_craft_procedure_id',
        'ALTER TABLE `dfs_maintain_record` ADD COLUMN `back_craft_procedure_id`  int(11) DEFAULT NULL COMMENT ''指定返修工艺工序id'' AFTER `maintain_result_type`');
call proc_add_column(
        'dfs_maintain_record',
        'back_procedure_id',
        'ALTER TABLE `dfs_maintain_record` ADD COLUMN `back_procedure_id`  int(11) DEFAULT NULL COMMENT ''指定返修工序id'' AFTER `back_craft_procedure_id`');

-- DML
-- 删除已经在单据下推配置的 业务配置下推配置
DELETE FROM `dfs_business_config` WHERE `full_path_code` LIKE 'saleOrder.pushDownConfig%';
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` LIKE 'saleOrder.pushDownConfig%';
DELETE FROM `dfs_business_config` WHERE `full_path_code` LIKE 'production.productOrderPushDownConfig%';
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` LIKE 'production.productOrderPushDownConfig%';
DELETE FROM `dfs_business_config` WHERE `full_path_code` LIKE 'production.workOrderPushDownConfig%';
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` LIKE 'production.workOrderPushDownConfig%';
DELETE FROM `dfs_business_config` WHERE `full_path_code` LIKE 'purchase.purchaseRequestPushDownConfig%';
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` LIKE 'purchase.purchaseRequestPushDownConfig%';
DELETE FROM `dfs_business_config` WHERE `full_path_code` LIKE 'purchase.purchaseOrderPushDownConfig%';
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` LIKE 'purchase.purchaseOrderPushDownConfig%';

DELETE FROM `dfs_business_config` WHERE `full_path_code` IN ('purchase.purchaseReceiptPushDownConfig.incomingInspection', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder', 'purchase.purchaseReceiptPushDownConfig.returnOrder');
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` IN ('purchase.purchaseReceiptPushDownConfig.incomingInspection', 'purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage', 'purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn', 'purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder', 'purchase.purchaseReceiptPushDownConfig.returnOrder');

-- 销售订单下推采购需求配置
delete from dfs_order_push_down_item  where instance_type  = 'saleOrder.pushDownConfig.purchaseRequest.jumpPage';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'saleOrder.pushDownConfig.purchaseRequest.jumpPage';
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.purchaseRequest.pushBatch', '按BOM下推', 'saleOrder.pushDownConfig.purchaseRequest', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.enable', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.originalOrderStates', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.description', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.targetOrderStates', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/state', 'get', NULL, 'code,name', '[1,4]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式(单选)', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.bomSplitType', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/bom/split', 'get', NULL, 'code,name', '["direct","byTierOneBom","byMultiLevelBom"]','\"direct\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeSampleMaterial', '是否合并相同物料', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.mergeSampleMaterial', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeOrder', '是否合单', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.mergeOrder', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.appId', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.dfs.supply.procurement-management\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.url', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-demand?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.sourceOrderType', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.targetOrderType', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseRequest\"', NULL);
-- 销售订单下推采购需求内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.purchaseRequest',  '下推采购需求', 'RULE', 1, 1, 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.enable', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.originalOrderStates', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.description', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.targetOrderStates', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/state', 'get', NULL, 'code,name', '[1,4]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式(单选)', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.bomSplitType', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/bom/split', 'get', NULL, 'code,name', '["direct","byTierOneBom","byMultiLevelBom"]','\"direct\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeSampleMaterial', '是否合并相同物料', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.mergeSampleMaterial', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeOrder', '是否合单', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.mergeOrder', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.appId', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.dfs.supply.procurement-management\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.url', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-demand?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.sourceOrderType', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.targetOrderType', 'saleOrder.pushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseRequest\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.purchaseRequest.pushBatch';



-- 销售订单按BOM下推生产订单
-- 删除之前的销售订单下推生产订单下推配置
DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.productOrder" AND `code` = "saleOrder.pushDownConfig.productOrder.jumpPage";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.jumpPage";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.productOrder.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.jumpPage";

DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.productOrder" AND `code` = "saleOrder.pushDownConfig.productOrder.bom";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.bom";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.productOrder.bom";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.bom";

DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.productOrder" AND `code` = "saleOrder.pushDownConfig.productOrder.bomAuto";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.bomAuto";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.productOrder.bomAuto";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.bomAuto";

DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.productOrder" AND `code` = "saleOrder.pushDownConfig.productOrder.bomMergeOrder";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.bomMergeOrder";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.productOrder.bomMergeOrder";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.bomMergeOrder";

DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.productOrder" AND `code` = "saleOrder.pushDownConfig.productOrder.mergeOrder";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.mergeOrder";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.productOrder.mergeOrder";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.productOrder.mergeOrder";
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.productOrder.bomPush', '按BOM下推', 'saleOrder.pushDownConfig.productOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.productOrder.bomPush.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.productOrder.bomPush.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.productOrder.bomPush.description', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.productOrder.bomPush.targetOrderStates', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.productOrder.bomPush.bomSplitTypes', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"notSplit\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialSorts', '过滤物料分类', 'saleOrder.pushDownConfig.productOrder.bomPush.filterMaterialSorts', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialTypes', '过滤物料类型', 'saleOrder.pushDownConfig.productOrder.bomPush.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'saleOrder.pushDownConfig.productOrder.bomPush.enableAutoFillPlanTime', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isFilter', '是否过滤数量为0的生产订单', 'saleOrder.pushDownConfig.productOrder.bomPush.isFilter', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableMergeOrder', '是否合单', 'saleOrder.pushDownConfig.productOrder.bomPush.enableMergeOrder', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.productOrder.bomPush.appId', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.productOrder.bomPush.url', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/order-model/product-order?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomPush.sourceOrderType', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomPush.targetOrderType', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.productOrder',  '按BOM下推生产订单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.productOrder.bomPush', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.productOrder.bomPush.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.productOrder.bomPush.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.productOrder.bomPush.description', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.productOrder.bomPush.targetOrderStates', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.productOrder.bomPush.bomSplitTypes', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"notSplit\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialSorts', '过滤物料分类', 'saleOrder.pushDownConfig.productOrder.bomPush.filterMaterialSorts', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialTypes', '过滤物料类型', 'saleOrder.pushDownConfig.productOrder.bomPush.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'saleOrder.pushDownConfig.productOrder.bomPush.enableAutoFillPlanTime', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isFilter', '是否过滤数量为0的生产订单', 'saleOrder.pushDownConfig.productOrder.bomPush.isFilter', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableMergeOrder', '是否合单', 'saleOrder.pushDownConfig.productOrder.bomPush.enableMergeOrder', 'saleOrder.pushDownConfig.productOrder.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.productOrder.bomPush.appId', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.productOrder.bomPush.url', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/order-model/product-order?pushDownOrigin=saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomPush.sourceOrderType', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomPush.targetOrderType', 'saleOrder.pushDownConfig.productOrder.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.productOrder.bomPush';

-- 删除之前的内置的下推配置：销售订单下推委外订单(新建页签)
DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.subcontractOrder" AND `code` = "saleOrder.pushDownConfig.subcontractOrder.jumpPage";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.subcontractOrder.jumpPage";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.subcontractOrder.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.subcontractOrder.jumpPage";


-- 销售订单、生产订单按工艺路线下推委外订单，增加工序物料配置、描述
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.description', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableProcedureMaterial', '使用工序物料', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.enableProcedureMaterial', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.description', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableProcedureMaterial', '使用工序物料', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.enableProcedureMaterial', 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'false', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch';

INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.description', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableProcedureMaterial', '使用工序物料', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.enableProcedureMaterial', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.description', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableProcedureMaterial', '使用工序物料', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.enableProcedureMaterial', 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'false', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch';

-- 销售订单、生产订单按BOM下推委外订单,增加描述
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.subcontractOrder.bom.description', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.subcontractOrder.bom.description', 'saleOrder.pushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.subcontractOrder.bom';

INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.subcontractOrder.bom.description', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.subcontractOrder.bom.description', 'production.productOrderPushDownConfig.subcontractOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.subcontractOrder.bom';

-- 减少生产订单进度及工序进度指标计算执行频率
UPDATE `dfs_target_model` SET `frequency` = 24, `frequency_unit` = 'h' WHERE `target_name` = 'orderProgress';

-- 隐藏`小程序字段名配置`，`后台字段名配置`，用户统一使用规则配置进行重命名
DELETE FROM `sys_role_permission` WHERE `permission_id` in ('12211010', '12211020');

-- 删除已有的表单配置（新增），仅保留编辑创建态
DELETE FROM `dfs_form_config` WHERE `full_path_code` in ('workOrder.add','saleOrder.add','productOrder.add','material.add','purchaseRequestOrder.add','purchaseOrder.add','purchaseReceiptOrder.add');
DELETE FROM `dfs_form_field_config` WHERE `full_path_code` in ('workOrder.add','saleOrder.add','productOrder.add','material.add','purchaseRequestOrder.add','purchaseOrder.add','purchaseReceiptOrder.add');
DELETE FROM `dfs_form_field_rule_config` WHERE `full_path_code` in ('workOrder.add','saleOrder.add','productOrder.add','material.add','purchaseRequestOrder.add','purchaseOrder.add','purchaseReceiptOrder.add');
UPDATE `dfs_form_config` SET `name` = '生产工单(状态)' WHERE `name` = '编辑生产工单';
UPDATE `dfs_form_config` SET `name` = '销售订单(状态)' WHERE `name` = '编辑销售订单';
UPDATE `dfs_form_config` SET `name` = '生产订单(状态)' WHERE `name` = '编辑生产订单';
UPDATE `dfs_form_config` SET `name` = '物料(状态)' WHERE `name` = '编辑物料';
UPDATE `dfs_form_config` SET `name` = '采购需求(状态)' WHERE `name` = '编辑采购需求';
UPDATE `dfs_form_config` SET `name` = '采购订单(状态)' WHERE `name` = '编辑采购订单';
UPDATE `dfs_form_config` SET `name` = '采购收料单(状态)' WHERE `name` = '编辑采购收料单';


-- 生产订单下推生产订单用料清单配置
delete from dfs_order_push_down_item  where instance_type  = 'production.productOrderPushDownConfig.productMaterialsList.jumpPage';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'production.productOrderPushDownConfig.productMaterialsList.jumpPage';
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productOrderPushDownConfig.productMaterialsList.pushBatch', '按BOM下推', 'production.productOrderPushDownConfig.productMaterialsList', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.enable', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.originalOrderStates', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.description', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.targetOrderStates', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.bomSplitType', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'api', '/ams/requests/bom/split', 'get', NULL, 'code,name', '["byTierOneBom","byMultiLevelBom"]','\"byMultiLevelBom\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialSorts', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '["purchase","outsourcing"]', '["purchase","outsourcing"]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.appId', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.url', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/order-model/production-materials?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.sourceOrderType', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.targetOrderType', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrderMaterialList\"', NULL);
-- 生产订单下推生产订单用料清单内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productOrderPushDownConfig.productMaterialsList',  '下推生产订单用量清单', 'RULE', 1, 1, 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.enable', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.originalOrderStates', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.description', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.targetOrderStates', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'api', '/ams/material/lists/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.bomSplitType', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select', 'api', '/ams/requests/bom/split', 'get', NULL, 'code,name', '["byTierOneBom","byMultiLevelBom"]','\"byMultiLevelBom\"', NULL);
    INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialSorts', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '["purchase","outsourcing"]', '["purchase","outsourcing"]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.appId', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.url', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/order-model/production-materials?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.sourceOrderType', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.targetOrderType', 'production.productOrderPushDownConfig.productMaterialsList.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrderMaterialList\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.productMaterialsList.pushBatch';

-- 删除之前的销售订单下推生产订单下推配置
DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.workOrder" AND `code` = "saleOrder.pushDownConfig.workOrder.craftRoute";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.workOrder.craftRoute";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.workOrder.craftRoute";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.workOrder.craftRoute";

DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.workOrder" AND `code` = "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder";

DELETE FROM dfs_dict WHERE `type` = "saleOrder.pushDownConfig.workOrder" AND `code` = "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch";
-- 销售订单按工艺路线下推工单配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('saleOrder.pushDownConfig.workOrder.craftPush', '按工艺路线下推', 'saleOrder.pushDownConfig.workOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.workOrder.craftPush.originalOrderStates', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.workOrder.craftPush.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.workOrder.craftPush.description', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'productOrderState', '中间单据状态', 'saleOrder.pushDownConfig.workOrder.craftPush.productOrderStates', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', '2', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.workOrder.craftPush.targetOrderStates', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.workOrder.craftPush.bomSplitTypes', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"notSplit\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialSorts', '过滤物料分类', 'saleOrder.pushDownConfig.workOrder.craftPush.filterMaterialSorts', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialTypes', '过滤物料类型', 'saleOrder.pushDownConfig.workOrder.craftPush.filterMaterialTypes', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'saleOrder.pushDownConfig.workOrder.craftPush.enableAutoFillPlanTime', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isFilter', '是否过滤数量为0的生产订单', 'saleOrder.pushDownConfig.workOrder.craftPush.isFilter', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableMergeOrder', '是否合单', 'saleOrder.pushDownConfig.workOrder.craftPush.enableMergeOrder', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'defaultCraft', '是否支持默认工艺', 'saleOrder.pushDownConfig.workOrder.craftPush.defaultCraft', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'craftSplitType', '工艺拆分方式', 'saleOrder.pushDownConfig.workOrder.craftPush.craftSplitType', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'materialChoose', '工序物料选择', 'saleOrder.pushDownConfig.workOrder.craftPush.materialChoose', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'isShowFinishProductCraft', '绑定成品工艺', 'saleOrder.pushDownConfig.workOrder.craftPush.isShowFinishProductCraft', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.workOrder.craftPush.appId', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.workOrder.craftPush.url', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/order-model/product-order?pushDownOrigin=saleOrder&sort=1\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.targetOrderType', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.workOrder.craftPush.appId-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.workOrder.craftPush.url-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/order-model/production-workorder?pushDownOrigin=productOrder&sort=2\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.targetOrderType-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'saleOrder.pushDownConfig.workOrder',  '按工艺路线下推生产工单', 'RULE', 1, 1, 'saleOrder.pushDownConfig.workOrder.craftPush', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'saleOrder.pushDownConfig.workOrder.craftPush.originalOrderStates', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'saleOrder.pushDownConfig.workOrder.craftPush.isSupportMaterialLineReversePushDown', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'saleOrder.pushDownConfig.workOrder.craftPush.description', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'productOrderState', '中间单据状态', 'saleOrder.pushDownConfig.workOrder.craftPush.productOrderStates', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', '2', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'saleOrder.pushDownConfig.workOrder.craftPush.targetOrderStates', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'saleOrder.pushDownConfig.workOrder.craftPush.bomSplitTypes', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"notSplit\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialSorts', '过滤物料分类', 'saleOrder.pushDownConfig.workOrder.craftPush.filterMaterialSorts', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'filterMaterialTypes', '过滤物料类型', 'saleOrder.pushDownConfig.workOrder.craftPush.filterMaterialTypes', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'saleOrder.pushDownConfig.workOrder.craftPush.enableAutoFillPlanTime', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isFilter', '是否过滤数量为0的生产订单', 'saleOrder.pushDownConfig.workOrder.craftPush.isFilter', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableMergeOrder', '是否合单', 'saleOrder.pushDownConfig.workOrder.craftPush.enableMergeOrder', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'defaultCraft', '是否支持默认工艺', 'saleOrder.pushDownConfig.workOrder.craftPush.defaultCraft', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'craftSplitType', '工艺拆分方式', 'saleOrder.pushDownConfig.workOrder.craftPush.craftSplitType', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'materialChoose', '工序物料选择', 'saleOrder.pushDownConfig.workOrder.craftPush.materialChoose', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'isShowFinishProductCraft', '绑定成品工艺', 'saleOrder.pushDownConfig.workOrder.craftPush.isShowFinishProductCraft', 'saleOrder.pushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.workOrder.craftPush.appId', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.workOrder.craftPush.url', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/order-model/product-order?pushDownOrigin=saleOrder&sort=1\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.targetOrderType', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.workOrder.craftPush.appId-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.workOrder.craftPush.url-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/order-model/production-workorder?pushDownOrigin=productOrder&sort=2\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.sourceOrderType-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.workOrder.craftPush.targetOrderType-2', 'saleOrder.pushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.workOrder.craftPush';

-- 删除之前的销售订单下推生产订单下推配置
DELETE FROM dfs_dict WHERE `type` = "production.productOrderPushDownConfig.workOrder" AND `code` = "production.productOrderPushDownConfig.workOrder.jumpPage";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "production.productOrderPushDownConfig.workOrder.jumpPage";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "production.productOrderPushDownConfig.workOrder.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "production.productOrderPushDownConfig.workOrder.jumpPage";

DELETE FROM dfs_dict WHERE `type` = "production.productOrderPushDownConfig.workOrder" AND `code` = "production.productOrderPushDownConfig.workOrder.craftRoute";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "production.productOrderPushDownConfig.workOrder.craftRoute";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "production.productOrderPushDownConfig.workOrder.craftRoute";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "production.productOrderPushDownConfig.workOrder.craftRoute";

DELETE FROM dfs_dict WHERE `type` = "production.productOrderPushDownConfig.workOrder" AND `code` = "production.productOrderPushDownConfig.workOrder.craftRouteAuto";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "production.productOrderPushDownConfig.workOrder.craftRouteAuto";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "production.productOrderPushDownConfig.workOrder.craftRouteAuto";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "production.productOrderPushDownConfig.workOrder.craftRouteAuto";
-- 生产订单按工艺路线下推工单配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productOrderPushDownConfig.workOrder.craftPush', '按工艺路线下推', 'production.productOrderPushDownConfig.workOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.workOrder.craftPush.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.workOrder.craftPush.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.workOrder.craftPush.description', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.workOrder.craftPush.targetOrderStates', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'production.productOrderPushDownConfig.workOrder.craftPush.enableAutoFillPlanTime', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isFilterWorkOrder', '是否过滤数量为0的生产工单', 'production.productOrderPushDownConfig.workOrder.craftPush.isFilterWorkOrder', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'defaultCraft', '是否支持默认工艺', 'production.productOrderPushDownConfig.workOrder.craftPush.defaultCraft', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'craftSplitType', '工艺拆分方式', 'production.productOrderPushDownConfig.workOrder.craftPush.craftSplitType', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'materialChoose', '工序物料选择', 'production.productOrderPushDownConfig.workOrder.craftPush.materialChoose', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'isShowFinishProductCraft', '绑定成品工艺', 'production.productOrderPushDownConfig.workOrder.craftPush.isShowFinishProductCraft', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.workOrder.craftPush.appId', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.workOrder.craftPush.url', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/dfs/order-model/production-workorder?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftPush.sourceOrderType', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftPush.targetOrderType', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productOrderPushDownConfig.workOrder',  '按工艺路线下推生产工单', 'RULE', 1, 1, 'production.productOrderPushDownConfig.workOrder.craftPush', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.workOrder.craftPush.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2,3]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.workOrder.craftPush.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'description', '描述', 'production.productOrderPushDownConfig.workOrder.craftPush.description', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.workOrder.craftPush.targetOrderStates', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'enableAutoFillPlanTime', '自动填写计划时间', 'production.productOrderPushDownConfig.workOrder.craftPush.enableAutoFillPlanTime', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'isFilterWorkOrder', '是否过滤数量为0的生产工单', 'production.productOrderPushDownConfig.workOrder.craftPush.isFilterWorkOrder', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'defaultCraft', '是否支持默认工艺', 'production.productOrderPushDownConfig.workOrder.craftPush.defaultCraft', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'craftSplitType', '工艺拆分方式', 'production.productOrderPushDownConfig.workOrder.craftPush.craftSplitType', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'materialChoose', '工序物料选择', 'production.productOrderPushDownConfig.workOrder.craftPush.materialChoose', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`, `group_type`)
VALUES (NULL, 'isShowFinishProductCraft', '绑定成品工艺', 'production.productOrderPushDownConfig.workOrder.craftPush.isShowFinishProductCraft', 'production.productOrderPushDownConfig.workOrder.craftPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL, 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.workOrder.craftPush.appId', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.workOrder.craftPush.url', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/dfs/order-model/production-workorder?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftPush.sourceOrderType', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftPush.targetOrderType', 'production.productOrderPushDownConfig.workOrder.craftPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.workOrder.craftPush';


-- 校验工单的制造单元和所属工位一致
INSERT INTO dfs_business_config_value (id, value_code, value_name, value_full_path_code, config_full_path_code, input_type, option_values_type, url, `method`, params, resp_param_field, option_values, value, rules) VALUES(null, 'checkLineAndFac', '是否校验工单的制造单元和所属工位一致', 'production.workOrderVerificationReportConfig.reportGeneralConfig.multipleLineProduction.checkLineAndFac', 'production.workOrderVerificationReportConfig.reportGeneralConfig.multipleLineProduction', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'false', NULL);
update dfs_business_config set description = '是否允许工单在同产线类型下跨产线扫码：为是时，小程序工单列表选择界面可以选择同产线类型（前面产线列表选择的产线）的其他工单。是否校验工单的制造单元和所属工位一致：为是时，过站会检验工单的制造单元和过站选择工位的制造单元必须一致，该配置受工艺的工序控制开关管控' where full_path_code = 'production.workOrderVerificationReportConfig.reportGeneralConfig.multipleLineProduction';

-- 键鼎包装报工标签打印字段
delete from dfs_config_label_info where code in ('factoryWeight', 'finishCount','packageDate', 'barCode', 'customerMaterialName', 'customerSpecification',
'platingDate', 'deliveryDate', 'remarks', 'factoryMaterialName', 'factoryStandard');
delete from dfs_config_label_type_info_relate where label_type_code = 'finished' and label_info_code in ('factoryWeight', 'finishCount','packageDate', 'factoryMaterialName', 'factoryStandard');
delete from dfs_config_label_type_info_relate where label_type_code = 'common' and label_info_code in ('factoryWeight', 'finishCount','packageDate',
'barCode', 'customerMaterialName', 'customerSpecification', 'platingDate', 'deliveryDate', 'remarks');

INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('factoryWeight', '本厂单重', '\$\{factoryWeight\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('finishCount', '数量', '\$\{finishCount\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('packageDate', '包装日期', '\$\{packageDate\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('barCode', '批号', '\$\{barCode\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('customerMaterialName', '客户物料名称', '\$\{customerMaterialName\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('customerSpecification', '客户物料规格', '\$\{customerSpecification\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('platingDate', '电镀日期', '\$\{platingDate\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('deliveryDate', '出货日期', '\$\{deliveryDate\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('remarks', '备注', '\$\{remarks\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('factoryMaterialName', '本厂品名', '\$\{factoryMaterialName\}', 1);
INSERT INTO `dfs_config_label_info`(`code`, `name`, `placeholder`, `is_sys_field`) VALUES ('factoryStandard', '本厂规格', '\$\{factoryMaterialName\}', 1);

INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('finished', 'factoryWeight');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('finished', 'finishCount');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('finished', 'packageDate');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('finished', 'factoryMaterialName');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('finished', 'factoryStandard');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'barCode');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'customerMaterialCode');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'customerMaterialName');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'customerSpecification');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'factoryWeight');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'finishCount');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'packageDate');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'platingDate');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'deliveryDate');
INSERT INTO `dfs_config_label_type_info_relate`(`label_type_code`, `label_info_code`) VALUES ('common', 'remarks');

-- 生产工单下推生产工单用料清单配置
-- DELETE FROM dfs_dict WHERE `type` = "production.workOrderPushDownConfig.materialList" AND `code` = "production.workOrderPushDownConfig.materialList.pushBatch";
-- DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.pushBatch";
-- DELETE FROM dfs_order_push_down_item WHERE instance_type = "production.workOrderPushDownConfig.materialList.pushBatch";
-- DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.pushBatch";
--
-- DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.jumpPage";
-- DELETE FROM dfs_order_push_down_item WHERE instance_type = "production.workOrderPushDownConfig.materialList.jumpPage";
-- DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.jumpPage";
--
-- INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.workOrderPushDownConfig.materialList.bomPush', '按BOM下推', 'production.workOrderPushDownConfig.materialList', 'show');
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.workOrderPushDownConfig.materialList.bomPush.enable', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.originalOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', '[2,3,4,5]', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.workOrderPushDownConfig.materialList.bomPush.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.workOrderPushDownConfig.materialList.bomPush.description', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式', 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"multiLevelBomNotFilter\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'materialChoose', '物料选择', 'production.workOrderPushDownConfig.materialList.bomPush.materialChoose', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/material/choose', 'get', NULL, 'code,name', '[\"workOrderMaterialBom\",\"procedureMaterialUsed\"]', '\"workOrderMaterialBom\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'production.workOrderPushDownConfig.materialList.bomPush.appId', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.workOrderPushDownConfig.materialList.bomPush.url', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/dfs/order-model/workOrder-materials?pushDownOrigin=workOrder\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.sourceOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialList\"', NULL);
-- -- 生产工单下推生产工单用料清单内置配置
-- INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
-- VALUES (NULL, 'production.workOrderPushDownConfig.materialList',  '下推生产工单用量清单', 'RULE', 1, 1, 'production.workOrderPushDownConfig.materialList.bomPush', 'admin', 'admin', NOW(), NOW(), 1);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.workOrderPushDownConfig.materialList.bomPush.enable', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.originalOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', '[2,3,4,5]', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.workOrderPushDownConfig.materialList.bomPush.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.workOrderPushDownConfig.materialList.bomPush.description', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式', 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"multiLevelBomNotFilter\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'materialChoose', '物料选择', 'production.workOrderPushDownConfig.materialList.bomPush.materialChoose', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/material/choose', 'get', NULL, 'code,name', '[\"workOrderMaterialBom\",\"procedureMaterialUsed\"]', '\"workOrderMaterialBom\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'production.workOrderPushDownConfig.materialList.bomPush.appId', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.workOrderPushDownConfig.materialList.bomPush.url', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/genieos/dfs/order-model/workOrder-materials?pushDownOrigin=workOrder\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.sourceOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
-- INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialList\"', NULL);
-- -- 关联绑定itemId
-- UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
-- SET a.`item_id` = b.`id`
-- WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.materialList.bomPush';



-- 更新下推配置组类型
UPDATE `dfs_order_push_down_config_value_dict` SET `group_type` = 'sysConf' WHERE `value_code` in ('appId', 'url', 'sourceOrderType', 'targetOrderType');
UPDATE `dfs_order_push_down_config_value` SET `group_type` = 'sysConf' WHERE `value_code` in ('appId', 'url', 'sourceOrderType', 'targetOrderType');
UPDATE `dfs_order_push_down_config_value_dict` SET `group_type` = 'sourceConf' WHERE `value_code` in ('isSupportMaterialLineReversePushDown', 'originalOrderStates', 'description');
UPDATE `dfs_order_push_down_config_value` SET `group_type` = 'sourceConf' WHERE `value_code` in ('isSupportMaterialLineReversePushDown', 'originalOrderStates', 'description');

-- 修改自定义下推的名称
UPDATE `dfs_order_push_down_config_value_dict` SET `value_name` = '反选物料行', `option_values` = '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]' WHERE `value_name` = '是否支持物料行反选';
UPDATE `dfs_order_push_down_config_value` SET `value_name` = '反选物料行', `option_values` = '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]' WHERE `value_name` = '是否支持物料行反选';

-- 修改路由
UPDATE `sys_permissions` SET `path` = '/facility-maintain/reworkQuality/maintainPlan/project' WHERE `path` = '/facility-maintain/reworkQuality/plan/project';
UPDATE `sys_route` SET `path` = '/facility-maintain/reworkQuality/maintainPlan/project' WHERE `path` = '/facility-maintain/reworkQuality/plan/project';

-- 下推配置的appId统一重命名为applicationId
UPDATE `dfs_order_push_down_config_value` SET `value_code` = 'applicationId', `value_name` = 'applicationId' WHERE `value_code` = 'appId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value_code` = 'applicationId', `value_name` = 'applicationId' WHERE `value_code` = 'appId';
UPDATE `dfs_order_push_down_config_value` SET `value_full_path_code` = REPLACE(`value_full_path_code`, '.appId', '.applicationId');
UPDATE `dfs_order_push_down_config_value_dict` SET `value_full_path_code` = REPLACE(`value_full_path_code`, '.appId', '.applicationId');
