-- <PERSON><PERSON>
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_10min',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_work_order_10min` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');

-- 指标：产线每日
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_line_daily',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_line_daily` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_line_daily',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_line_daily` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');


-- 指标： 生产订单每日
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_daily',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_product_order_daily` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_daily',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_product_order_daily` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_daily',
        'end_unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_product_order_daily` ADD COLUMN `end_unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''成品不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_daily',
        'end_unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_product_order_daily` ADD COLUMN `end_unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''成品不良项记录数量: 流水码不去重''');


-- 指标：生产订单整体
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_product_order` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_product_order` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');


-- 指标：生产订单每月
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'end_unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `end_unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''成品不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_product_order_monthly',
        'end_unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_product_order_monthly` ADD COLUMN `end_unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''成品不良项记录数量: 流水码不去重''');


-- 指标：销售订单-物料每日
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_material_daily',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_sale_order_material_daily` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_material_daily',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_sale_order_material_daily` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');


-- 指标：销售订单-物料整体
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_material',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_sale_order_material` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_material',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_sale_order_material` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');


-- 指标：班组每日
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_team_daily',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_team_daily` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_team_daily',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_team_daily` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');


-- 指标：生产工单每日
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_daily',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_work_order_daily` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');


-- 指标：生产工单整体
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');

-- 指标：生产工单每小时
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_hourly',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order_hourly',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');

-- 指标：产线每月
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_monthly',
        'unqualified_record_quantity',
        'ALTER TABLE `dfs_metrics_line_monthly` MODIFY COLUMN `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_line_monthly',
        'unqualified_record_item_quantity',
        'ALTER TABLE `dfs_metrics_line_monthly` ADD COLUMN `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重''');

-- 指标：生产工单整体
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'direct_access_quantity',
        'ALTER TABLE `dfs_metrics_work_order_hourly` ADD COLUMN `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT ''直通数''');

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_line_material_defect_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(512) NOT NULL COMMENT '唯一标识',
    `line_id` int(11) NOT NULL,
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
    `defect_type_code` varchar(255) DEFAULT NULL COMMENT '不良类型编号',
    `defect_type_name` varchar(255) DEFAULT NULL COMMENT '不良类型名称',
    `defect_id` varchar(255) DEFAULT NULL COMMENT '不良项id',
    `defect_name` varchar(255) DEFAULT NULL COMMENT '不良项名称',
    `unqualified_record_item_quantity` double(11,2) DEFAULT NULL COMMENT '不良项记录数量: 流水码不去重',
    `record_date` datetime NOT NULL COMMENT '记录日期',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`),
    KEY `idx_material` (`material_code`) USING BTREE,
    KEY `idx_record` (`record_date`),
    KEY `idx_defect_type` (`defect_type_code`),
    KEY `idx_defect_id` (`defect_id`),
    KEY `idx_line_id` (`line_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每日：产品不良项统计 (物料，不良类型，不良项)';
-- 不良记录表添加索引
call `proc_add_column_index`('dfs_record_work_order_unqualified','create_date','create_date');
call `proc_add_column_index`('dfs_record_work_order_unqualified','record_date','record_date');

call `proc_add_column`(
        'dfs_report_line',
        'report_end_time',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `report_end_time` datetime NULL COMMENT ''报工结束时间''');

-- 产品指标配置
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('lineMaterialDefectDaily', '产线每日不良项统计', '1', 'baseLine', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('lineMaterialDefectDaily', 'baseLine', '系统统计', 'lineMaterialDefectDaily', 1, NULL);

-- 指标限制配置
CREATE TABLE IF NOT EXISTS `dfs_target_limit_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `type` varchar(255) NOT NULL COMMENT '类型：WORK_ORDER, PRODUCT_ORDER',
    `last_duration_s` bigint(20) NOT NULL COMMENT '未更新时长, 单位: s',
    `cal_frequency_s` bigint(20) DEFAULT NULL COMMENT '计算频率, 单位: s',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`type`,`last_duration_s`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='指标限制配置';
INSERT INTO `dfs_target_limit_config` (`id`, `type`, `last_duration_s`, `cal_frequency_s`) VALUES (1, 'WORK_ORDER', 86400, 3600);
INSERT INTO `dfs_target_limit_config` (`id`, `type`, `last_duration_s`, `cal_frequency_s`) VALUES (2, 'WORK_ORDER', 604800, 86400);
INSERT INTO `dfs_target_limit_config` (`id`, `type`, `last_duration_s`, `cal_frequency_s`) VALUES (3, 'WORK_ORDER', 2592000, NULL);
INSERT INTO `dfs_target_limit_config` (`id`, `type`, `last_duration_s`, `cal_frequency_s`) VALUES (4, 'PRODUCT_ORDER', 86400, 3600);
INSERT INTO `dfs_target_limit_config` (`id`, `type`, `last_duration_s`, `cal_frequency_s`) VALUES (5, 'PRODUCT_ORDER', 604800, 86400);
INSERT INTO `dfs_target_limit_config` (`id`, `type`, `last_duration_s`, `cal_frequency_s`) VALUES (6, 'PRODUCT_ORDER', 2592000, NULL);

-- 部门新增部门编码
call `proc_add_column`(
        'sys_department',
        'department_code',
        'ALTER TABLE `sys_department` ADD COLUMN `department_code` varchar(255) NULL DEFAULT NULL COMMENT ''部门编码'' after `department_id`');

CREATE TABLE IF NOT EXISTS `dfs_material_attribute_type` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribute_type_code` varchar(255) DEFAULT NULL COMMENT '分类编码',
    `full_attribute_type_code` varchar(750) DEFAULT '' COMMENT '分类全路径编码，父级全路径编码.子分类编码',
    `attribute_type_name` varchar(255) DEFAULT NULL COMMENT '分类名称',
    `parent_code` varchar(255) DEFAULT '' COMMENT '父级分类编码',
    `seq` int(11) DEFAULT 1 COMMENT '序号',
    `remark` text COMMENT '备注',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `attribute_type_code` (`attribute_type_code`) USING BTREE,
    KEY `full_attribute_type_code` (`full_attribute_type_code`) USING BTREE,
    KEY `parent_code` (`parent_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='物料属性分类表';

CREATE TABLE IF NOT EXISTS `dfs_material_attribute` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `attribute_code` varchar(255) DEFAULT NULL COMMENT '物料属性编码',
    `attribute_name` varchar(255) DEFAULT NULL COMMENT '物料属性名称',
    `state` int(11) DEFAULT NULL COMMENT '状态（1-创建 2-启用 3-停用 4-废弃）',
    `description` text COMMENT '描述',
    `data_type` varchar(50) DEFAULT 'string' COMMENT '数据类型：number, string, bool',
    `unit` varchar(255) DEFAULT NULL COMMENT '单位',
    `input_type` varchar(50) DEFAULT NULL COMMENT '输入框类型，单选，多选，输入框',
    `value_range` varchar(255) DEFAULT NULL COMMENT '值范围',
    `option_values` varchar(255) DEFAULT NULL COMMENT '可选值',
    `parent_code` varchar(255) DEFAULT '' COMMENT '父级分类编码',
    `remark` text COMMENT '备注',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `attribute_code` (`attribute_code`) USING BTREE,
    KEY `parent_code` (`parent_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='物料属性表';

CREATE TABLE IF NOT EXISTS `dfs_material_type_config_attribute` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `material_type_id` int(11) DEFAULT NULL COMMENT '物料类型id',
    `material_type_name` varchar(255) DEFAULT NULL COMMENT '物料类型',
    `attribute_code` varchar(255) DEFAULT NULL COMMENT '物料属性编码',
    `value_range` varchar(255) DEFAULT NULL COMMENT '值范围',
    `option_values` varchar(255) DEFAULT NULL COMMENT '可选值',
    `value` varchar(255) DEFAULT NULL COMMENT '默认值',
    `remark` text COMMENT '备注',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `material_type_id` (`material_type_id`) USING BTREE,
    KEY `attribute_code` (`attribute_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='物料类型属性配置表';

CREATE TABLE IF NOT EXISTS `dfs_material_attribute_list` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
    `material_type_id` int(11) DEFAULT NULL COMMENT '物料类型id',
    `attribute_code` varchar(255) DEFAULT NULL COMMENT '物料属性编码',
    `value` varchar(255) DEFAULT NULL COMMENT '属性值',
    `remark` text COMMENT '备注',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `material_code` (`material_code`) USING BTREE,
    KEY `attribute_code` (`attribute_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='物料属性清单表';

-- 生产工单用料清单
CREATE TABLE IF NOT EXISTS `dfs_work_order_material_list`
(
    `material_list_id`     int(11)      NOT NULL AUTO_INCREMENT COMMENT '生产工单用料清单id',
    `material_list_code`   varchar(255) NOT NULL COMMENT '生产工单用料清单编号',
    `state`                int(11)       DEFAULT NULL COMMENT '状态（1-创建 2-生效 3-完成 4-关闭 5-取消）',
    `relate_type`          varchar(255)  DEFAULT NULL COMMENT '关联单据类型',
    `relate_number`        varchar(255)  DEFAULT NULL COMMENT '关联单据编号',
    `relate_material_code` varchar(255)  DEFAULT NULL COMMENT '关联单据物料编码',
    `relate_quantity`      double(11, 4) DEFAULT NULL COMMENT '关联单据数量',
    `approval_status`      int(11)       DEFAULT NULL COMMENT '审批状态(0-待审核 1-已审核待批准 2-已批准 3-驳回)',
    `approval_suggestion`  varchar(255)  DEFAULT NULL COMMENT '审批建议',
    `approval_time`        datetime      DEFAULT NULL COMMENT '审批时间',
    `approver`             varchar(255)  DEFAULT NULL COMMENT '指定审批人',
    `actual_approver`      varchar(255)  DEFAULT NULL COMMENT '实际审批人',
    `create_time`          datetime      DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime      DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_by`            varchar(255)  DEFAULT NULL COMMENT '创建人',
    `update_by`            varchar(255)  DEFAULT NULL COMMENT '修改人',
    `relate_sku_id`        int(11)       DEFAULT '0' COMMENT '关联单据特征参数skuId',
    `remark`               varchar(255)  DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`material_list_id`),
    UNIQUE KEY `material_list_code` (`material_list_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='生产工单用料清单';

CREATE TABLE IF NOT EXISTS `dfs_work_order_material_list_material`
(
    `id`                           int(11) NOT NULL AUTO_INCREMENT COMMENT '用料清单关联物料id',
    `material_list_id`             int(11)       DEFAULT NULL COMMENT '生产工单用料清单id',
    `material_list_code`           varchar(255)  DEFAULT NULL COMMENT '生产工单用料清单编号',
    `material_list_plan_quantity`  double(11, 4) DEFAULT NULL COMMENT '生产工单用料清单计划数量',
    `material_code`                varchar(255)  DEFAULT NULL COMMENT '物料编码',
    `bom_numerator`                double(11, 4) DEFAULT NULL COMMENT 'bom分子',
    `bom_denominator`              double(11, 4) DEFAULT NULL COMMENT 'bom分母',
    `plan_quantity`                double(11, 4) DEFAULT NULL COMMENT '计划数量',
    `actual_quantity`              double(11, 4) DEFAULT NULL COMMENT '实际数量',
    `main_material_code`           varchar(255)  DEFAULT NULL COMMENT '主物料编号',
    `sub_type`                     int(11)       DEFAULT NULL COMMENT '子项类型（1001-标准件  1002-替代件）',
    `replace_quantity`             double(11, 2) DEFAULT NULL COMMENT '替代数量',
    `replace_material_id`          varchar(255)  DEFAULT NULL COMMENT '可供选择的替代物id（多个则按逗号分隔）',
    `selected_replace_material_id` varchar(255)  DEFAULT NULL COMMENT '已选择的替代物id（多个则按逗号分隔）',
    `create_time`                  datetime      DEFAULT NULL COMMENT '创建时间',
    `sku_id`                       int(11)       DEFAULT '0' COMMENT '特征参数skuId',
    `reference_quantity`           double(11, 3) DEFAULT NULL COMMENT '参考数量',
    `take_out_quantity`            double(11, 3) DEFAULT NULL COMMENT '领补数量',
    `return_quantity`              double(11, 3) DEFAULT NULL COMMENT '退料数量',
    `bom_raw_material_id`          varchar(255)  DEFAULT NULL COMMENT 'BOM子物料id',
    `bom_raw_material_extend_one`  varchar(255)  DEFAULT NULL COMMENT 'BOM子物料拓展字段',
    PRIMARY KEY (`id`),
    KEY `material_list_id` (`material_list_id`),
    KEY `material_list_code` (`material_list_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4;

-- 由于现网无人使用，故重新构建表
DROP TABLE IF EXISTS `dfs_line_employee`;
CREATE TABLE IF NOT EXISTS `dfs_line_employee`
(
    `id`                   int(11) NOT NULL AUTO_INCREMENT,
    `production_line_code` varchar(255) DEFAULT NULL COMMENT '产线编号',
    `user_name`            varchar(255) DEFAULT NULL COMMENT '员工账号',
    `create_by`            varchar(50)  DEFAULT NULL COMMENT '创建人',
    `create_time`          datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='产线员工关联表';

call proc_add_column(
        'dfs_package_level',
        'material_code',
        'ALTER TABLE `dfs_package_level` ADD COLUMN `material_code`  varchar(255) DEFAULT NULL  COMMENT ''物料code'';');

call proc_add_column(
        'dfs_package_level',
        'material_name',
        'ALTER TABLE `dfs_package_level` ADD COLUMN `material_name`  varchar(255) DEFAULT NULL  COMMENT ''物料name'';');

-- 包装方案物料关联表
CREATE TABLE IF NOT EXISTS `dfs_package_scheme_material`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `scheme_code`   varchar(255) DEFAULT NULL COMMENT '包装方案编码',
    `scheme_name`   varchar(255) DEFAULT NULL COMMENT '包装方案名称',
    `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
    PRIMARY KEY (`id`),
    KEY `scheme_code` (`scheme_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='包装方案物料关联表';

call proc_add_column(
        'dfs_package_scheme',
        'is_common',
        'ALTER TABLE `dfs_package_scheme` ADD COLUMN `is_common`   tinyint(1) DEFAULT 1   COMMENT ''是否通用方案'';');


call proc_add_column(
        'dfs_product_flow_code',
        'material_type',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `material_type`  int(11) DEFAULT NULL  COMMENT ''物料类型'';');
call proc_add_column_index('dfs_product_flow_code','material_type','material_type');
-- 产能表新增 人均产能字段
call proc_add_column(
        'dfs_capacity',
        'capacity_per_person',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `capacity_per_person`  double(11,2) DEFAULT NULL  COMMENT ''人均产能'';');

-- 创建默认产能表
CREATE TABLE IF NOT EXISTS `dfs_default_capacity`
(
    `id`                         int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `work_center_type`           varchar(100)  DEFAULT NULL COMMENT '工作中心类型',
    `production_basic_unit_id`   int(11)       DEFAULT NULL COMMENT '生产基本单元id',
    `production_basic_unit_code` varchar(255)  DEFAULT NULL COMMENT '生产基本单元编号',
    `production_basic_unit_name` varchar(255)  DEFAULT NULL COMMENT '生产基本单元名称',
    `capacity`                   int(11)       DEFAULT NULL COMMENT '产能',
    `theory_staff_num`           int(11)       DEFAULT NULL COMMENT '理论人员数',
    `capacity_per_person`        double(11, 2) DEFAULT NULL COMMENT '人均产能',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = DYNAMIC COMMENT ='默认产能配置表';

-- 创建班组类型定义表，本想使用dict表，但是发现班组类型编号是int类型，而且使用了1和3，放在dict表会导致主键重复，此表初始自增号设置为4，请勿修改
CREATE TABLE IF NOT EXISTS `dfs_team_type_def`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT COMMENT '班组类型序号',
    `type_def_code` varchar(255) DEFAULT NULL COMMENT '班组类型编号',
    `type_def_name` varchar(255) DEFAULT NULL COMMENT '班组类型名称',
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`     varchar(50)  DEFAULT NULL COMMENT '创建人',
    `update_by`     varchar(50)  DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB  AUTO_INCREMENT = 4
  DEFAULT CHARSET = utf8mb4 COMMENT ='班组类型定义表';

-- 订单报工小程序  新增、修改表单字段配置
call proc_add_form_field("orderReportApp.addReport", "reportEndTime", "报工结束时间");
UPDATE `dfs_form_field_config` SET `field_name` = '报工开始时间'
WHERE `full_path_code` = 'orderReportApp.addReport' AND `field_code` = 'reportTime' AND `type_code` = 'sysField';
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, 'yelink.workorder.report', 'orderReportApp.addReport', 'orderReportApp.addReport', 'reportEndTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, '', '', '当前时间', NULL, 0, 0, 0, 1, 1, 0, 0, 1);
-- 质量等级
call `proc_add_column`(
        'dfs_dict',
        'quality_level',
        'ALTER TABLE `dfs_dict` ADD COLUMN `quality_level` varchar(255) NULL DEFAULT NULL COMMENT ''质量等级''');
call `proc_add_column`(
        'dfs_material',
        'quality_level',
        'ALTER TABLE `dfs_material` ADD COLUMN `quality_level` varchar(255) NULL DEFAULT NULL COMMENT ''质量等级''');
call `proc_add_column`(
        'dfs_report_line',
        'quality_level',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `quality_level` varchar(255) NULL DEFAULT NULL COMMENT ''质量等级''');
call proc_add_column(
        'dfs_product_flow_code',
        'relation_type',
        'ALTER TABLE `dfs_product_flow_code` ADD COLUMN `relation_type`  int(11) DEFAULT NULL  COMMENT ''关联单据类型'';');

-- DML

-- 生产工单用料清单新增审批功能
INSERT INTO `dfs_approve_config` ( `code`, `name`, `is_approve`, `update_by`, `update_time`) VALUES ( 'workOrderMaterialList', '生产工单用料清单', 1, 'admin', '2023-04-26 15:58:56');
-- 生产工单用料清单编码规则
call init_number_rules_config_in_new_rule("INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (77, '生产工单-生产工单用料清单');");
-- 新增生产工单用料清单按钮权限、路由
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012', '生产工单用料清单', '/order-model/workOrder-materials', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '100', 1, 1, 0, '/order-model', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012010', '审批', 'workOrder.materials:approval', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012020', '新增', 'workOrder.materials:add', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'POST', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012030', '编辑', 'workOrder.materials:update', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'PUT', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012040', '详情', 'workOrder.materials:select', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012050', '删除', 'workOrder.materials:delete', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'DELETE', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012060', '下推', 'workOrder.materials:pushDown', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'POST', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012070', '列配置_列表', 'workOrder.materials:columnConfigList', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012080', '批量审批', 'workOrder.materials:approvalBatch', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10012090', '保存并生效', 'workOrder.materials:saveAndEffect', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '');
call init_new_role_permission('10012%');

INSERT INTO `sys_route`(`id`, `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES (null, '/order-model/workOrder-materials', '/order-model', 'workOrderMaterials', NULL, '生产工单用料清单', NULL, 'dfs', NULL, NULL, NULL, 5, 1, NULL, '');
-- 调整调用api接口路径
UPDATE `dfs_config_open_api` SET `path` = CONCAT('/openApi/dfs',`path`) WHERE `service` != 'open' AND `service` != 'jingzhi';
-- 新增调用精制的接口
INSERT INTO `dfs_config_open_api`(`service`, `model`, `interface_code`, `interface_name`, `http_method`, `path`) VALUES ('jingzhi', 'role', 'updateJzRole', '修改精制角色信息', 'PUT', '/jingzhi/role/update');
-- 新增班组类型定义权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11307090', '班组类型定义', 'team.manager:typeDef', NULL, NULL, '2023-12-18 03:11:07', NULL, '2023-12-18 06:41:40', 'enable', 'POST', '11307', 2, 1, 1, '/factory-model/team', 1, NULL, '');
call init_new_role_permission('11307090');
-- 新增班组类型定义默认值
INSERT INTO `dfs_team_type_def`(`id`, `type_def_code`, `type_def_name`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (1, 'warehouse', '仓管', '2023-12-19 14:40:32', '2023-12-19 06:40:58', 'admin', 'admin');
INSERT INTO `dfs_team_type_def`(`id`, `type_def_code`, `type_def_name`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (3, 'production', '生产', '2023-12-19 14:40:32', '2023-12-19 06:41:07', 'admin', 'admin');


-- 单品码历史字段刷新
UPDATE dfs_product_flow_code
SET material_type = (
    SELECT type
    FROM dfs_material
    WHERE dfs_product_flow_code.material_code = dfs_material.code
);
-- 客户档案编码规则
call init_number_rules_config_in_new_rule("INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (78, '客户档案-客户编码');");
-- 物料属性
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805', '物料属性', '/material-config/material-property', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '118', 1, 1, 1, '/material-config', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805010', '新增', 'material.property:add', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'POST', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805020', '编辑', 'material.property:update', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'PUT', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805030', '详情', 'material.property:select', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805040', '删除', 'material.property:delete', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'DELETE', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805050', '属性分类', 'material.property.type', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805060', '分类新增', 'material.property.type:add', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'POST', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805070', '分类编辑', 'material.property.type:update', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'PUT', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805080', '分类详情', 'material.property.type:select', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'GET', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805090', '分类删除', 'material.property.type:delete', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'DELETE', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805100', '分类顺序调整', 'material.property.type:seq', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'POST', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11805110', '列配置', 'material.property:columnConfigList', NULL, NULL, NULL, NULL, '2023-12-05 01:20:14', 'enable', 'POST', '11805', 2, 1, 1, '/material-config/material-property', 1, NULL, '');
call init_new_role_permission('11805%');

INSERT INTO `sys_route`(`id`, `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES (null, '/material-config/material-property', '/material-config', 'materialConfig', NULL, '物料属性', NULL, 'dfs', NULL, NULL, NULL, 0, 0, NULL, '');

-- 兼容默认产能历史数据
INSERT INTO `dfs_default_capacity`(`work_center_type`, `production_basic_unit_id`, `production_basic_unit_name`, `capacity`, `capacity_per_person`)
SELECT 'line', `des`, `name`, `code`, `code`
FROM `dfs_dict`
WHERE `type` = 'lineCapacity';

INSERT INTO `dfs_default_capacity`(`work_center_type`, `production_basic_unit_id`, `production_basic_unit_name`, `capacity`, `capacity_per_person`)
SELECT 'device', `des`, `name`, `code`, `code`
FROM `dfs_dict`
WHERE `type` = 'deviceCapacity';

UPDATE `dfs_default_capacity` a , `dfs_production_line` b
SET a.`production_basic_unit_code` = b.`production_line_code`
WHERE a.`work_center_type` = 'line' AND a.`production_basic_unit_id` = b.`production_line_id`;

UPDATE `dfs_default_capacity` a , `dfs_device` b
SET a.`production_basic_unit_code` = b.`device_code`
WHERE a.`work_center_type` = 'device' AND a.`production_basic_unit_id` = b.`device_id`;

-- 删除默认产能相关字典表数据，保留工厂产能和每月产能数据
DELETE FROM `dfs_dict` WHERE `type` = 'lineCapacity' or `type` = 'deviceCapacity';

-- 新增默认产能取值逻辑
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`) VALUES ('logicTwo', '生产基本单元 + 物料默认 > 物料 > 生产基本单元 > 工厂产能', 'defaultCapacityValueLogic', NULL, NULL, 'true', NULL, '2023-11-02 14:38:27', '2023-12-25 10:59:06', 'admin', 'admin', NULL);
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`) VALUES ('logicOne', '生产基本单元 + 物料默认 > 生产基本单元 > 物料 > 工厂产能', 'defaultCapacityValueLogic', NULL, NULL, 'false', NULL, '2023-11-02 14:38:27', '2023-12-25 10:59:06', 'admin', 'admin', NULL);

INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('toPlan', '待计划标识', 'saleOrder.listConfig.stateIdentifier.toPlan', 'saleOrder.listConfig.stateIdentifier', '生产状态-待计划', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');
INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'saleOrder.listConfig.stateIdentifier.toPlan.enable', 'saleOrder.listConfig.stateIdentifier.toPlan', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'saleOrder.listConfig.stateIdentifier.toPlan.colorIdentifier', 'saleOrder.listConfig.stateIdentifier.toPlan', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#FFD000"');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'saleOrder.listConfig.stateIdentifier.toPlan.textValue', 'saleOrder.listConfig.stateIdentifier.toPlan', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"待计划"');

INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('havePlan', '已计划标识', 'saleOrder.listConfig.stateIdentifier.havePlan', 'saleOrder.listConfig.stateIdentifier', '生产状态-已计划', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');
INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'saleOrder.listConfig.stateIdentifier.havePlan.enable', 'saleOrder.listConfig.stateIdentifier.havePlan', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'saleOrder.listConfig.stateIdentifier.havePlan.colorIdentifier', 'saleOrder.listConfig.stateIdentifier.havePlan', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#FFD000"');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'saleOrder.listConfig.stateIdentifier.havePlan.textValue', 'saleOrder.listConfig.stateIdentifier.havePlan', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"已计划"');


INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('scheduledPlan', '已排产标识(生产状态)', 'saleOrder.listConfig.stateIdentifier.scheduledPlan', 'saleOrder.listConfig.stateIdentifier', '生产状态-已排产', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');

INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'saleOrder.listConfig.stateIdentifier.scheduledPlan.enable', 'saleOrder.listConfig.stateIdentifier.scheduledPlan', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'saleOrder.listConfig.stateIdentifier.scheduledPlan.colorIdentifier', 'saleOrder.listConfig.stateIdentifier.scheduledPlan', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#FFD000"');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'saleOrder.listConfig.stateIdentifier.scheduledPlan.textValue', 'saleOrder.listConfig.stateIdentifier.scheduledPlan', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"已排产"');
INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('inProduction', '生产中标识', 'saleOrder.listConfig.stateIdentifier.inProduction', 'saleOrder.listConfig.stateIdentifier', '生产状态-生产中', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');
INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'saleOrder.listConfig.stateIdentifier.inProduction.enable', 'saleOrder.listConfig.stateIdentifier.inProduction', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'saleOrder.listConfig.stateIdentifier.inProduction.colorIdentifier', 'saleOrder.listConfig.stateIdentifier.inProduction', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#FFD000"');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'saleOrder.listConfig.stateIdentifier.inProduction.textValue', 'saleOrder.listConfig.stateIdentifier.inProduction', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"生产中"');

INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('finishProduction', '生产完成标识', 'saleOrder.listConfig.stateIdentifier.finishProduction', 'saleOrder.listConfig.stateIdentifier', '生产状态-生产完成', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');
INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'saleOrder.listConfig.stateIdentifier.finishProduction.enable', 'saleOrder.listConfig.stateIdentifier.finishProduction', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'saleOrder.listConfig.stateIdentifier.finishProduction.colorIdentifier', 'saleOrder.listConfig.stateIdentifier.finishProduction', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#FFD000"');
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'saleOrder.listConfig.stateIdentifier.finishProduction.textValue', 'saleOrder.listConfig.stateIdentifier.finishProduction', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"生产完成"');

update dfs_business_config_value set `option_values` = null where value_full_path_code  = 'general.saveAndBackConfig.selectOrder';

-- 新增采集设备导入导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11602080', '下载默认模板', 'sensor:downTemplate', NULL, NULL, NULL, NULL, '2023-12-18 06:41:40', 'enable', 'GET', '11602', 2, 1, 1, '/equipment/test-equipment', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11602090', '导入数据', 'sensor:import-data', NULL, NULL, NULL, NULL, '2023-12-18 06:41:40', 'enable', 'GET', '11602', 2, 1, 1, '/equipment/test-equipment', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11602100', '查看日志', 'sensor:import-log', NULL, NULL, NULL, NULL, '2023-12-18 06:41:40', 'enable', 'GET', '11602', 2, 1, 1, '/equipment/test-equipment', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11602110', '后台导出列表', 'sensor:exportList', NULL, NULL, NULL, NULL, '2023-12-18 06:41:40', 'enable', 'GET', '11602', 2, 1, 1, '/equipment/test-equipment', 1, NULL, '');
call init_new_role_permission('11602080');
call init_new_role_permission('11602090');
call init_new_role_permission('11602100');
call init_new_role_permission('11602110');

-- 物料标识权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('131', '标识中心', '/identification-center', NULL, NULL, NULL, NULL, '2023-12-22 07:36:20', 'enable', 'GET', '0', 0, 1, 0, NULL, 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101', '物料标识', '/identification-center/material-identification', NULL, NULL, NULL, NULL, '2023-12-21 07:36:20', 'enable', 'GET', '131', 1, 1, 0, '/trace', 1, NULL, '');
INSERT INTO `sys_route`( `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES ( '/identification-center', NULL, '', 0, '标识中心', 'menu_identification_center', NULL, NULL, NULL, NULL, 2, 0, 0, '');
INSERT INTO `sys_route`( `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES ( '/identification-center/material-identification', '/identification-center', 'materialIdentification', NULL, '物料标识', NULL, 'ams', NULL, NULL, NULL, 3, 0, NULL, '');

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101010', '新增', 'material.identification:add', NULL, NULL, '2023-10-25 02:55:28', NULL, '2023-12-21 07:36:20', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '');

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101020', '详情', 'material.identification:select', NULL, NULL, '2023-10-25 02:55:28', NULL, '2023-12-21 07:36:20', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101030', '删除', 'material.identification:delete', NULL, NULL, '2023-10-25 02:55:28', NULL, '2023-12-21 07:36:20', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101040', '导入管理', 'material.identification:import', NULL, NULL, '2023-10-25 02:55:28', NULL, '2023-12-21 07:36:20', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '');


INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101050', '导入管理-下载导入模板', 'material.identification:templateExport', NULL, NULL, '2023-10-25 02:55:28', NULL, '2023-12-21 07:36:20', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101060', '导入管理-导入数据', 'material.identification:importData', NULL, NULL, '2023-10-25 02:55:28', NULL, '2023-12-21 07:36:20', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`)
VALUES ('13101070', '导入管理-查看日志', 'material.identification:logExport', NULL, NULL, '2023-10-25 02:55:28', NULL, '2023-12-21 07:36:20', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '');

call init_new_role_permission('131%');

update dfs_product_flow_code set relation_type = 1 where type = 1;
update dfs_product_flow_code set relation_type = 2 where type = 4;
update dfs_product_flow_code set relation_type = 4 where type = 3;

update dfs_business_config_value set value_name = '是否允许工单在同产线类型下跨产线扫码' where value_full_path_code = 'production.workOrderVerificationReportConfig.reportGeneralConfig.multipleLineProduction.enable';

INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (null, 'takeOutApplication', '生产工单用料清单', 'production.workOrderPushDownConfig.materialList', 'production.workOrderPushDownConfig', NULL, 'yelinkoncall', 'yelinkoncall', '2023-12-18 03:06:35', '2023-12-18 03:06:35');
INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (null, 'jumpPage', '下推生产工单用料清单(新建页签)', 'production.workOrderPushDownConfig.materialList.jumpPage', 'production.workOrderPushDownConfig.materialList', 'workOrder下推materialList配置', 'yelinkoncall', 'yelinkoncall', '2023-12-18 03:06:35', '2023-12-18 03:06:35');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES (null, 'enable', '是否启用(单选)', 'production.workOrderPushDownConfig.materialList.jumpPage.enable', 'production.workOrderPushDownConfig.materialList.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES (null, 'originalOrderStates', '原单状态(多选)', 'production.workOrderPushDownConfig.materialList.jumpPage.originalOrderStates', 'production.workOrderPushDownConfig.materialList.jumpPage', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4]', '[2,3,4]');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES (null, 'openPage', '是否新建页面(单选)', 'production.workOrderPushDownConfig.materialList.jumpPage.openPage', 'production.workOrderPushDownConfig.materialList.jumpPage', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

delete from dfs_business_config_value where value_full_path_code = 'saleOrder.listConfig.stateIdentifier.tableCellProduct.enable';
delete from dfs_business_config_value where value_full_path_code = 'saleOrder.listConfig.stateIdentifier.tableCellProduct.colorIdentifier';
delete from dfs_business_config_value where value_full_path_code = 'saleOrder.listConfig.stateIdentifier.tableCellProduct.textValue';

-- 删除首班时间配置路由及权限
DELETE FROM `sys_route` WHERE `path` = '/production-config/firstShiftTimeConfig';
DELETE FROM `sys_permissions` WHERE `path` = '/production-config/firstShiftTimeConfig' or `path` = 'first.shift.time.config:configTime';

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10101590', '生产工单过站记录列配置', 'production.workorder:passingRecordColumnConfigList', NULL, NULL, NULL, NULL, '2023-12-28 11:56:27', 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10005590', '生产工单过站记录列配置', 'production.workorder:passingRecordColumnConfigList', NULL, NULL, NULL, NULL, '2023-12-28 11:56:27', 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1, NULL, '');
call init_new_role_permission('10101590');
call init_new_role_permission('10005590');

-- 新增工艺参数导入权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10803460', '工序参数数据导入与日志查询', 'procedureParameterImport', NULL, NULL, NULL, NULL, '2023-12-28 11:56:27', 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1, NULL, '');
call init_new_role_permission('10803460');