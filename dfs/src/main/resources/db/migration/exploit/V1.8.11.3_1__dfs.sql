set autocommit = 0;
#DDL

CREATE TABLE IF NOT EXISTS `dfs_config_schedule_reverse_material`
(
    `material_code` varchar(100) DEFAULT NULL COMMENT '物料名称'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工单排程-倒排物料配置表';

CREATE TABLE IF NOT EXISTS `dfs_config_schedule_reverse_material_detail`
(
    `id`                           int(11) NOT NULL AUTO_INCREMENT,
    `column_name`                  varchar(100) DEFAULT NULL COMMENT '列名',
    `material_type_name`           varchar(100) DEFAULT NULL,
    `material_name_match_rule`     varchar(100) DEFAULT NULL COMMENT '物料名称规则',
    `material_name_match_val`      varchar(100) DEFAULT NULL COMMENT '物料名称值',
    `material_standard_match_rule` varchar(100) DEFAULT NULL COMMENT '物料规格规则',
    `material_standard_match_val`  varchar(100) DEFAULT NULL COMMENT '物料规则名称',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 44
  DEFAULT CHARSET = utf8 COMMENT ='工单排程-倒排物料的详情表';


#DML

commit