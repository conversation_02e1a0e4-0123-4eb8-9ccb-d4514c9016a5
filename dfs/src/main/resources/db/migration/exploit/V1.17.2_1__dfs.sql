set autocommit = 0;

-- DDL
-- 物料表增加损耗率字段
call proc_add_column_if_not_exist(
        'dfs_material',
        'lose_rate',
        'ALTER TABLE `dfs_material` ADD COLUMN `lose_rate`  double NULL DEFAULT 0 COMMENT ''损耗率''');


CREATE TABLE IF NOT EXISTS `dfs_weight_record` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `work_order_code` varchar(255) DEFAULT NULL COMMENT '生产工单编号',
                                                   `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
                                                   `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
                                                   `batch_code` varchar(255) DEFAULT NULL COMMENT '物料批次号',
                                                   `weight_type` enum('FEEDING_TYPE','BLANKING_TYPE','WASTE_TYPE') DEFAULT NULL COMMENT '上报类型(上料、下料、废料)',
                                                   `weight_value` double(16,2) DEFAULT '0.00' COMMENT '重量',
                                                   `operator` varchar(255) DEFAULT NULL COMMENT '上报人/操作人',
                                                   `operator_time` datetime DEFAULT NULL COMMENT '上报时间',
                                                   `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                                   `create_time` datetime DEFAULT NULL,
                                                   `update_by` varchar(255) DEFAULT NULL,
                                                   `create_by` varchar(255) DEFAULT NULL,
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='称重记录表';


CREATE TABLE IF NOT EXISTS `dfs_weight_target` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `work_order_code` varchar(255) DEFAULT NULL COMMENT '生产工单编号',
                                                   `feeding_weight_value` double(16,2) DEFAULT NULL COMMENT '上料重量',
                                                   `blank_weight_value` double(16,2) DEFAULT NULL COMMENT '下料重量',
                                                   `waste_weight_value` double(16,2) DEFAULT NULL COMMENT '废料重量',
                                                   `theory_output_rate` double(16,4) DEFAULT NULL COMMENT '理论产出率',
                                                   `actual_output_rate` double(16,4) DEFAULT NULL COMMENT '实际产出率',
                                                   `output_compliance_rate` double(16,4) DEFAULT NULL COMMENT '产出达标率',
                                                   `theory_waste_rate` double(16,4) DEFAULT NULL COMMENT '理论废料率',
                                                   `actual_waste_rate` double(16,4) DEFAULT NULL COMMENT '实际废料率',
                                                   `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                                   `create_time` datetime DEFAULT NULL,
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='称重统计表';



commit;
