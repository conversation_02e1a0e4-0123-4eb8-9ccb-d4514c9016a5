-- 重新创建任务中心表
DROP TABLE IF EXISTS `dfs_task`;
CREATE TABLE IF NOT EXISTS `dfs_task`  (
    `task_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务id',
    `order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据类型',
    `order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据编码',
    `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料',
    `material_line_id` int(11) NULL DEFAULT NULL COMMENT '物料行Id',
    `upstream_order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游单据类型',
    `upstream_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游单据编码',
    `upstream_material_line_id` int(11) NULL DEFAULT NULL COMMENT '上游物料行Id',
    `order_state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据状态编码',
    `order_state_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据状态名称',
    `task_state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务状态',
    `task_progress` decimal(22, 10) NULL DEFAULT NULL COMMENT '任务进度',
    `root_order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '根单据类型',
    `root_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '根单据编号',
    `root_material_line_id` int(11) NULL DEFAULT NULL COMMENT '根物料行Id',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`task_id`) USING BTREE,
    INDEX `order`(`order_category`, `order_number`, `material_line_id`) USING BTREE,
    INDEX `upstream`(`upstream_order_category`, `upstream_order_number`, `upstream_material_line_id`) USING BTREE,
    INDEX `root`(`root_order_category`, `root_order_number`, `root_material_line_id`) USING BTREE
    ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='任务表';

-- 任务--人员关系表
CREATE TABLE IF NOT EXISTS `dfs_task_user_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `task_id` int(11) DEFAULT NULL COMMENT '任务id',
    `user_type` varchar(100) DEFAULT NULL COMMENT '人员类型',
    `username` varchar(255) DEFAULT NULL COMMENT '用户名',
    PRIMARY KEY (`id`),
    KEY `task_id` (`task_id`) USING BTREE,
    KEY `username` (`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='任务--人员关系表';

ALTER TABLE `sys_users` COMMENT = '用户表';
ALTER TABLE `sys_department` COMMENT = '部门表';

-- 物料查询配置：匹配模式
INSERT INTO `dfs_business_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('materialQuery', '物料查询配置', 'material.materialQueryConfig', 'material', '用于单据列表页的物料查询或添加物料的弹窗查询时，模糊匹配的方式');

INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('nameMatchMethod', '名称匹配方式', 'material.materialQueryConfig.nameMatchMethod', 'material.materialQueryConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"LIKE\",\"label\":\"包含查询字段\"},{\"value\":\"LIKE_RIGHT\",\"label\":\"以查询字段开头\"}]', '\"LIKE_RIGHT\"', NULL);
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('codeMatchMethod', '编码匹配方式', 'material.materialQueryConfig.codeMatchMethod', 'material.materialQueryConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"LIKE\",\"label\":\"包含查询字段\"},{\"value\":\"LIKE_RIGHT\",\"label\":\"以查询字段开头\"}]', '\"LIKE_RIGHT\"', NULL);
-- 表单配置-生产订单新增客户订单编号字段
call proc_add_form_field("productOrder.list", "customerOrderNumber", "客户订单编号");
call proc_add_form_field("productOrder.add", "customerOrderNumber", "客户订单编号");
call proc_add_form_field("productOrder.edit", "customerOrderNumber", "客户订单编号");
call proc_add_form_field("productOrder.detail", "customerOrderNumber", "客户订单编号");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.list', 'productOrder.list', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.editByCreate', 'productOrder.edit', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.editByRelease', 'productOrder.edit', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.editByFinish', 'productOrder.edit', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.editByClosed', 'productOrder.edit', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.editByCancel', 'productOrder.edit', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.detail', 'productOrder.detail', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/product-order', 'productOrder.add', 'productOrder.add', 'customerOrderNumber', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

-- 不良代号、维修代号
call init_number_rules(102, '不良定义-不良代号');
call init_number_rules(103, '维修定义-维修代号');
-- 默认规则
delete from dfs_config_number_rules where type in (102, 103);
INSERT INTO dfs_config_number_rules (id, `type`, name, prefix_detail, create_by, update_by, create_time, update_time, auto_increment_configure_type, is_default) VALUES(null, 102, '编码规则', '[{"code":30,"example":"20240813","initValue":1,"name":"不良类型编码","rule":"yyyyMMdd"},{"autoIncrementConfigureType":"noCross","code":4,"example":"001","initValue":1,"name":"自动生成序号","rule":"3","uuid":"86fddcef-d220-11ef-adb8-0242ac120009"}]', 'admin', NULL, '2025-01-15 15:02:56', NULL, 'day', 1);
INSERT INTO dfs_config_number_rules (id, `type`, name, prefix_detail, create_by, update_by, create_time, update_time, auto_increment_configure_type, is_default) VALUES(null, 103, '编码规则', '[{"code":31,"example":"20240813","initValue":1,"name":"维修类型编码","rule":"yyyyMMdd"},{"autoIncrementConfigureType":"noCross","code":4,"example":"001","initValue":1,"name":"自动生成序号","rule":"3","uuid":"870ae0db-d220-11ef-adb8-0242ac120009"}]', 'admin', NULL, '2025-01-15 15:02:39', NULL, 'day', 1);

-- 表单配置中投产状态的工单，基本生产单元需要必显示，且置灰
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'workOrder.editByInvestment' AND `field_code` = 'lineId';

-- 删除历史BOM导出权限，使用新的导出权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('/bom/export/downDefaultExportTemplate', '下载默认导出模板', 'bom:export.downDefaultExportTemplate', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('/bom/export/downUploadTemplate', '上传/下载自定义导出模板', 'bom:export.downUploadTemplate', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('/bom/export/dataLog', '查看日志', 'bom:export.dataLog', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('/bom/export/excel', '导出excel', 'bom:export.excel', NULL, NULL, NULL, NULL, '2024-12-09 15:26:02', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
-- 历史绑定导出权限的角色自动赋值新权限
UPDATE `sys_role_permission` SET `permission_id` = '/bom/export/downDefaultExportTemplate' WHERE `permission_id` in ('10802250', '10802290');
UPDATE `sys_role_permission` SET `permission_id` = '/bom/export/downUploadTemplate' WHERE `permission_id` in ('10802260', '10802300');
UPDATE `sys_role_permission` SET `permission_id` = '/bom/export/dataLog' WHERE `permission_id` in ('10802280', '10802320', '10802340', '10802360');
UPDATE `sys_role_permission` SET `permission_id` = '/bom/export/excel' WHERE `permission_id` in ('10802270', '10802310', '10802330', '10802350');

DELETE FROM `sys_permissions` WHERE `id` in ('10802250','10802260','10802270','10802280','10802290','10802300','10802310','10802320','10802330','10802340','10802350','10802360');
-- 去重更新角色权限后的数据
DELETE t1
FROM `sys_role_permission` t1
JOIN `sys_role_permission` t2
WHERE t1.`role_id` = t2.`role_id`
  AND t1.`permission_id` = t2.`permission_id`
  AND t1.id > t2.id
  AND t1.`permission_id` in ('/bom/export/downDefaultExportTemplate','/bom/export/downUploadTemplate','/bom/export/dataLog','/bom/export/excel');

-- 标签打印：生产批次增加客户订单编号字段
INSERT INTO `dfs_config_label_info` (`code`, `name`, `placeholder`, `is_sys_field`, `module_code`, `form_field_code`)
VALUES ('productOrderEntity.productOrderMaterial.customerOrderNumber', '客户订单编号', "\$\{productOrderEntity.productOrderMaterial.customerOrderNumber\}", '1', 'productOrder', 'customerOrderNumber');

-- 标签打印：采购收料批次增加检验开始时间、检验结束时间
INSERT INTO `dfs_label_module_config` (`type_code`, `module_code`, `module_name`, `have_all_field`) VALUES ('purchase', 'inspectOrder', '来料检验单', '1');
INSERT INTO `dfs_config_label_info` (`code`, `name`, `placeholder`, `is_sys_field`, `module_code`) VALUES ('barCodeEntity.inspectPrint.inspectBeginTime', '检验开始时间', "\$\{barCodeEntity.inspectPrint.inspectBeginTime\}", '1', 'inspectOrder');
INSERT INTO `dfs_config_label_info` (`code`, `name`, `placeholder`, `is_sys_field`, `module_code`) VALUES ('barCodeEntity.inspectPrint.inspectEndTime', '检验结束时间', "\$\{barCodeEntity.inspectPrint.inspectEndTime\}", '1', 'inspectOrder');


-- 任务角色配置
CREATE TABLE IF NOT EXISTS `dfs_task_role_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_category` varchar(255) NOT NULL COMMENT '单据类型',
    `role_id` int(11) NOT NULL COMMENT '角色id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`order_category`,`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务配置表';

-- 委外收货单新增批次表单配置
call proc_add_form_field_module("subcontractReceipt.detail", "barCode", "批次号", "batchField");
call proc_add_form_field_module("subcontractReceipt.detail", "barCount", "批次数量", "batchField");
call proc_add_form_field_module("subcontractReceipt.detail", "receiveStateName", "收货状态", "batchField");
call proc_add_form_field_module("subcontractReceipt.detail", "createByName", "创建人", "batchField");
call proc_add_form_field_module("subcontractReceipt.detail", "createTime", "创建时间", "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "barCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "barCount", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "receiveStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "createByName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_module_config` (`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`) VALUES ('subcontractReceipt.detail', 'subcontractReceipt.detail', 'batchField', '批次字段', '1', '1');

call proc_add_form_field_module("subcontractReceipt.edit", "barCode", "批次号", "batchField");
call proc_add_form_field_module("subcontractReceipt.edit", "barCount", "批次数量", "batchField");
call proc_add_form_field_module("subcontractReceipt.edit", "receiveStateName", "收货状态", "batchField");
call proc_add_form_field_module("subcontractReceipt.edit", "createByName", "创建人", "batchField");
call proc_add_form_field_module("subcontractReceipt.edit", "createTime", "创建时间", "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "barCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "barCount", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "receiveStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "createByName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_module_config` (`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`) VALUES ('subcontractReceipt.editByCreate', 'subcontractReceipt.edit', 'batchField', '批次字段', '1', '1');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "barCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "barCount", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "receiveStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "createByName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_module_config` (`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`) VALUES ('subcontractReceipt.editByRelease', 'subcontractReceipt.edit', 'batchField', '批次字段', '1', '1');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "barCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "barCount", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "receiveStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "createByName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_module_config` (`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`) VALUES ('subcontractReceipt.editByFinish', 'subcontractReceipt.edit', 'batchField', '批次字段', '1', '1');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "barCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "barCount", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "receiveStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "createByName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_module_config` (`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`) VALUES ('subcontractReceipt.editByClosed', 'subcontractReceipt.edit', 'batchField', '批次字段', '1', '1');


INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "barCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "barCount", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "receiveStateName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "createByName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, "batchField");
INSERT INTO `dfs_form_field_module_config` (`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`) VALUES ('subcontractReceipt.editByCancel', 'subcontractReceipt.edit', 'batchField', '批次字段', '1', '1');


-- 消息通知
update dfs_config_notice_level_relation set configurable_placeholder = '{设备编号},{设备名称},{设备负责人},{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人}' WHERE code = 'deviceAlarmNotice';

-- 删除重复的用户类配置，防止页面读取列配置报错
DELETE t1
FROM `dfs_user_column_record` t1
JOIN `dfs_user_column_record` t2
WHERE t1.`user_name` = t2.`user_name`
  AND t1.`modular_type` = t2.`modular_type`
  AND t1.id > t2.id;

-- 调整任务中心的上下游的坐标
TRUNCATE TABLE `dfs_order_category`;
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('saleOrder', '销售订单', 'new.yk.genieos.ams.order-model.salesOrder', '', NULL, 1, 1, -143.2218, -273.4485);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('deliveryApplication', '销售出货单', 'new.yk.genieos.dfs.scc.order-model.shipment_application', '', NULL, 1, 1, 135.6515, -417.7545);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('saleReturnOrder', '销售退货单', 'new.yk.genieos.ams.scc.order-model.sales_returns', '', NULL, 1, 1, -375.7320, -415.6455);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('productOrder', '生产订单', 'new.yk.genieos.ams.order-model.product-order', '', NULL, 1, 1, -145.8119, 78.1222);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('productMaterialsList', '生产订单用料清单', 'new.yk.genieos.ams.order-model.production-materials', '', NULL, 1, 1, 157.1910, 262.0235);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseRequest', '采购需求单', 'new.yk.genieos.ams.scc.procurement-management.purchasing-demand', '', NULL, 1, 1, -379.7639, -127.4884);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseOrder', '采购订单', 'new.yk.genieos.ams.scc.procurement-management.purchasing-list', '', NULL, 1, 1, -383.7155, -269.7062);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseReceipt', '采购收料', 'new.yk.genieos.ams.scc.procurement-management.delivery-order', '', NULL, 1, 1, -595.7519, -119.3452);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('returnOrder', '采购退料', 'new.yk.genieos.ams.scc.procurement-management.return-order', '', NULL, 1, 1, -596.3304, 72.1052);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractOrder', '委外订单', 'new.yk.genieos.ams.scc.osm.outsourcingOrder', '', NULL, 1, 1, -237.9962, -125.5530);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrder', '生产工单', 'new.yk.genieos.dfs.order-model.production-workorder', '', NULL, 1, 1, 147.7726, -108.6067);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrderMaterialList', '生产工单用料清单', 'new.yk.genieos.dfs.order-model.workOrder-materials', '', NULL, 1, 1, 274.4147, -21.4274);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('inspectOrder', '检验单', 'new.yk.genieos.qms3.0.inspection-manage.qpi.productInspection', '', NULL, 1, 1, -779.6518, -120.3568);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('salesIssueDoc', '销售出库单', 'com.luotu.wms.salesIssueReceipt.salesIssueDoc', '', NULL, 1, 1, -157.8219, -584.8682);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('salesReturnReceiptDoc', '销售退货入库单', 'com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc', '', NULL, 1, 1, -35.3190, -426.0588);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrderComplete', '生产入库单', 'com.luotu.wms.productInOrOut.workOrderComplete', '', NULL, 1, 1, -36.9817, -114.5484);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('outputPickingProduct', '生产领料出库单', 'com.luotu.wms.productInOrOut.workOrderTakeOut', '', NULL, 1, 1, 154.5648, 78.1733);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('workOrderSupplement', '生产补料出库单', 'com.luotu.wms.productInOrOut.workOrderSupplement', '', NULL, 1, 1, 452.3808, -104.4949);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('applicationReturn', '生产退料入库单', 'com.luotu.wms.productInOrOut.applicationReturn', '', NULL, 1, 1, 147.3101, -270.5885);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseIn', '采购入库单', 'com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage', '', NULL, 1, 1, -597.0048, -270.5400);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('purchaseReturnOut', '采购退料出库单', 'com.luotu.wms.purchaseOutOfStorage.purchaseReturnOutOfStorage', '', NULL, 1, 1, -599.1779, -406.9217);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractProductOutbound', '委外领料出库单', 'com.luotu.wms.subcontracting.outstoreMaterail', '', NULL, 1, 1, 457.0194, -592.1575);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractSupplementaryFood', '委外补料出库单', 'com.luotu.wms.subcontracting.repairMaterail', '', NULL, 1, 1, 619.7463, -429.9878);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('subcontractReturnReceipt', '委外退料入库单', 'com.luotu.wms.subcontracting.refundMaterail', '', NULL, 1, 1, 619.7154, -589.2482);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('transferOrder', '调拨单', 'com.luotu.wms.cougnyManage.allocation', '', NULL, 1, 1, 618.8331, -109.9185);
INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`) VALUES ('stockOtherAppForm', '其他出库申请单', 'com.luotu.wms.mixReceive.issueRequisition', '', NULL, 1, 1, 617.8385, -271.6371);

-- 删除工单基础字段表单配置中物料的扩展字段
DELETE from dfs_form_field_rule_config
WHERE `field_code` in ('loseRate','nameEnglish','rawMaterial','materialPrice')
AND `route` = '/order-model/production-workorder' and module_code = 'baseField';

-- 脏数据删除，删除replace_material_id为0 的脏数据
DELETE FROM `dfs_replace_bom_material` WHERE `replace_material_id` = 0;

-- 新增任务角色配置表
CREATE TABLE IF NOT EXISTS `dfs_task_role_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_category` varchar(255) NOT NULL COMMENT '单据类型',
    `role_id` int(11) NOT NULL COMMENT '角色id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`order_category`,`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务角色配置表';

INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'workOrder', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');
INSERT INTO dfs_task_role_config (order_category, role_id) SELECT 'workOrderMaterialList', id from sys_roles WHERE name in ('管理员','系统运维','生产','经营');

update dfs_config_notice_level_relation set configurable_placeholder='{设备编号},{设备名称},{设备负责人},{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人},{告警说明}' where code='deviceAlarmNotice';
update dfs_config_notice_level_relation set configurable_placeholder='{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人},{告警说明}' where code='productAlarmNotice';
update dfs_config_notice_level_relation set configurable_placeholder='{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人},{告警说明}' where code='safeAlarmNotice';
update dfs_config_notice_level_relation set configurable_placeholder='{告警名称},{告警ID},{告警级别名称},{告警上报时间},{告警恢复时间},{告警处理人},{告警说明}' where code='otherAlarmNotice';
