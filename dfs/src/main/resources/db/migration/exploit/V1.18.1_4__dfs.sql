set autocommit = 0;

-- dfs_record_device_oee修改长度
call proc_modify_column(
        'dfs_record_device_oee',
        'yield',
        'ALTER TABLE `dfs_record_device_oee`MODIFY COLUMN `yield` double(11, 4) NULL DEFAULT NULL COMMENT ''合格率''');

call proc_modify_column(
        'dfs_record_device_oee',
        'performance',
        'ALTER TABLE `dfs_record_device_oee` MODIFY COLUMN `performance` double(11, 4) NULL DEFAULT NULL COMMENT ''性能开动率''');

call proc_modify_column(
        'dfs_record_device_oee',
        'time_efficiency',
        'ALTER TABLE `dfs_record_device_oee` MODIFY COLUMN `time_efficiency` double(11, 4) NULL DEFAULT NULL COMMENT ''时间开动率''');

call proc_modify_column(
        'dfs_record_device_oee',
        'oee',
        'ALTER TABLE `dfs_record_device_oee` MODIFY COLUMN `oee` double(11, 4) NULL DEFAULT NULL COMMENT ''OEE''');


commit;
