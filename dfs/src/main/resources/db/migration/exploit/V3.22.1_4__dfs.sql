-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================21.2
-- 增加销售合同字段配置和扩展字段配置
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'saleContract', '销售合同', 'saleContract', 'amsSale', NULL, 'admin', 'admin', NOW(), NOW(),NULL,1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleContractList', '销售合同列表页', 'saleContract.list', 'saleContract', NULL, 'admin', 'admin', NOW(), NOW(),'list',1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'saleContractEdit', '销售合同编辑页', 'saleContract.edit', 'saleContract', NULL, 'admin', 'admin', NOW(), NOW(),NULL,1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleContractEditByCreate', '创建态', 'saleContract.editByCreate', 'saleContract.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-create',1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleContractEditByRelease', '生效态', 'saleContract.editByRelease', 'saleContract.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-other',1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleContractEditByCancel', '取消态', 'saleContract.editByCancel', 'saleContract.edit', NULL, 'admin', 'admin', NOW(), NOW(),'edit-other',1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'saleContractDetail', '销售合同详情页', 'saleContract.detail', 'saleContract', NULL, 'admin', 'admin', NOW(), NOW(),'detail',1);

-- 销售合同
-- 列表页
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.list', 'saleContract.list','baseField', '销售合同基本字段', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.list', 'saleContract.list','baseExtendField', '销售合同扩展字段', 'extend', '扩展字段', 1, 0, 2);

call proc_add_form_field_module('saleContract.list', 'contractCode', '合同编号', 'baseField');
call proc_add_form_field_module('saleContract.list', 'contractName', '合同名称', 'baseField');
call proc_add_form_field_module('saleContract.list', 'states', '状态', 'baseField');
call proc_add_form_field_module('saleContract.list', 'customerCode', '客户编号', 'baseField');
call proc_add_form_field_module('saleContract.list', 'customerName', '客户名称', 'baseField');
call proc_add_form_field_module('saleContract.list', 'contractVestDepartmentName', '归属部门', 'baseField');
call proc_add_form_field_module('saleContract.list', 'salespersonName', '业务员名称', 'baseField');
call proc_add_form_field_module('saleContract.list', 'contractReceivableAmount', '合同应收金额', 'baseField');
call proc_add_form_field_module('saleContract.list', 'contractNetReceiptAmount', '合同实收金额', 'baseField');
call proc_add_form_field_module('saleContract.list', 'validBeginTime', '有效起始时间', 'baseField');
call proc_add_form_field_module('saleContract.list', 'validEndTime', '有效截止时间', 'baseField');
call proc_add_form_field_module('saleContract.list', 'contractValidDay', '合同有效期', 'baseField');
call proc_add_form_field_module('saleContract.list', 'contractDescribe', '项目描述', 'baseField');
call proc_add_form_field_module('saleContract.list', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('saleContract.list', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('saleContract.list', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('saleContract.list', 'updateTime', '更新时间', 'baseField');

call proc_add_form_field_module('saleContract.list', 'extendFieldOne', '扩展字段1', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldTwo', '扩展字段2', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldThree', '扩展字段3', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldFour', '扩展字段4', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldFive', '扩展字段5', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldSix', '扩展字段6', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldSeven', '扩展字段7', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldEight', '扩展字段8', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldNine', '扩展字段9', 'baseExtendField');
call proc_add_form_field_module('saleContract.list', 'extendFieldTen', '扩展字段10', 'baseExtendField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'contractCode', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'contractName', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'states', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'contractVestDepartmentName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'salespersonName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'contractReceivableAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'contractNetReceiptAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'validBeginTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'validEndTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'contractValidDay', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'contractDescribe', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.list', 'saleContract.list', 'extendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');

-- 编辑
-- 创建态
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.editByCreate', 'saleContract.editByCreate','baseField', '销售合同基本字段', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.editByCreate', 'saleContract.editByCreate','baseExtendField', '销售合同扩展字段', 'extend', '扩展字段', 1, 0, 2);

call proc_add_form_field_module('saleContract.editByCreate', 'contractCode', '合同编号', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'contractName', '合同名称', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'states', '状态', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'customerCode', '客户编号', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'customerName', '客户名称', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'contractVestDepartmentName', '归属部门', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'salespersonName', '业务员名称', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'contractReceivableAmount', '合同应收金额', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'contractNetReceiptAmount', '合同实收金额', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'validBeginTime', '有效起始时间', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'validEndTime', '有效截止时间', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'contractValidDay', '合同有效期', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'contractDescribe', '项目描述', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('saleContract.editByCreate', 'updateTime', '更新时间', 'baseField');

call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldOne', '扩展字段1', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldTwo', '扩展字段2', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldThree', '扩展字段3', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldFour', '扩展字段4', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldFive', '扩展字段5', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldSix', '扩展字段6', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldSeven', '扩展字段7', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldEight', '扩展字段8', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldNine', '扩展字段9', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCreate', 'extendFieldTen', '扩展字段10', 'baseExtendField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'contractCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'contractName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'states', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'contractVestDepartmentName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'salespersonName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'contractReceivableAmount', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'contractNetReceiptAmount', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'validBeginTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'validEndTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'contractValidDay', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'contractDescribe', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'createByName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'createTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'updateByName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'updateTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCreate', 'saleContract.editByCreate', 'extendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');

-- 生效态
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.editByRelease', 'saleContract.editByRelease','baseField', '销售合同基本字段', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.editByRelease', 'saleContract.editByRelease','baseExtendField', '销售合同扩展字段', 'extend', '扩展字段', 1, 0, 2);

call proc_add_form_field_module('saleContract.editByRelease', 'contractCode', '合同编号', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'contractName', '合同名称', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'states', '状态', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'customerCode', '客户编号', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'customerName', '客户名称', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'contractVestDepartmentName', '归属部门', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'salespersonName', '业务员名称', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'contractReceivableAmount', '合同应收金额', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'contractNetReceiptAmount', '合同实收金额', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'validBeginTime', '有效起始时间', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'validEndTime', '有效截止时间', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'contractValidDay', '合同有效期', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'contractDescribe', '项目描述', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('saleContract.editByRelease', 'updateTime', '更新时间', 'baseField');

call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldOne', '扩展字段1', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldTwo', '扩展字段2', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldThree', '扩展字段3', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldFour', '扩展字段4', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldFive', '扩展字段5', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldSix', '扩展字段6', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldSeven', '扩展字段7', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldEight', '扩展字段8', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldNine', '扩展字段9', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByRelease', 'extendFieldTen', '扩展字段10', 'baseExtendField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'contractCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'contractName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'states', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'contractVestDepartmentName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'salespersonName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'contractReceivableAmount', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'contractNetReceiptAmount', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'validBeginTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'validEndTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'contractValidDay', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'contractDescribe', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'createByName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'createTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'updateByName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'updateTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByRelease', 'saleContract.editByRelease', 'extendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');


-- 取消态
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.editByCancel', 'saleContract.editByCancel','baseField', '销售合同基本字段', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.editByCancel', 'saleContract.editByCancel','baseExtendField', '销售合同扩展字段', 'extend', '扩展字段', 1, 0, 2);

call proc_add_form_field_module('saleContract.editByCancel', 'contractCode', '合同编号', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'contractName', '合同名称', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'states', '状态', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'customerCode', '客户编号', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'customerName', '客户名称', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'contractVestDepartmentName', '归属部门', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'salespersonName', '业务员名称', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'contractReceivableAmount', '合同应收金额', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'contractNetReceiptAmount', '合同实收金额', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'validBeginTime', '有效起始时间', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'validEndTime', '有效截止时间', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'contractValidDay', '合同有效期', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'contractDescribe', '项目描述', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('saleContract.editByCancel', 'updateTime', '更新时间', 'baseField');

call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldOne', '扩展字段1', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldTwo', '扩展字段2', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldThree', '扩展字段3', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldFour', '扩展字段4', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldFive', '扩展字段5', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldSix', '扩展字段6', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldSeven', '扩展字段7', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldEight', '扩展字段8', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldNine', '扩展字段9', 'baseExtendField');
call proc_add_form_field_module('saleContract.editByCancel', 'extendFieldTen', '扩展字段10', 'baseExtendField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'contractCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'contractName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'states', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'customerCode', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'customerName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'contractVestDepartmentName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'salespersonName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'contractReceivableAmount', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'contractNetReceiptAmount', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'validBeginTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'validEndTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'contractValidDay', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'contractDescribe', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'createByName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'createTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'updateByName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'updateTime', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.editByCancel', 'saleContract.editByCancel', 'extendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');

-- 详情页
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.detail', 'saleContract.detail','baseField', '销售合同基本字段', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`)  VALUES('saleContract.detail', 'saleContract.detail','baseExtendField', '销售合同扩展字段', 'extend', '扩展字段', 1, 0, 2);

call proc_add_form_field_module('saleContract.detail', 'contractCode', '合同编号', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'contractName', '合同名称', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'states', '状态', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'customerCode', '客户编号', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'customerName', '客户名称', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'contractVestDepartmentName', '归属部门', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'salespersonName', '业务员名称', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'contractReceivableAmount', '合同应收金额', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'contractNetReceiptAmount', '合同实收金额', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'validBeginTime', '有效起始时间', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'validEndTime', '有效截止时间', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'contractValidDay', '合同有效期', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'contractDescribe', '项目描述', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'createByName', '创建人', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'createTime', '创建时间', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'updateByName', '更新人', 'baseField');
call proc_add_form_field_module('saleContract.detail', 'updateTime', '更新时间', 'baseField');

call proc_add_form_field_module('saleContract.detail', 'extendFieldOne', '扩展字段1', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldTwo', '扩展字段2', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldThree', '扩展字段3', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldFour', '扩展字段4', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldFive', '扩展字段5', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldSix', '扩展字段6', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldSeven', '扩展字段7', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldEight', '扩展字段8', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldNine', '扩展字段9', 'baseExtendField');
call proc_add_form_field_module('saleContract.detail', 'extendFieldTen', '扩展字段10', 'baseExtendField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'contractCode', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'contractName', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'states', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'contractVestDepartmentName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'salespersonName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'contractReceivableAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'contractNetReceiptAmount', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'validBeginTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'validEndTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'contractValidDay', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'contractDescribe', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/order-model/sales_contracts', 'saleContract.detail', 'saleContract.detail', 'extendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1, NULL, NULL, 1,'baseExtendField');



-- 修改字段默认配置
-- 采购订单
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseOrder.editByCreate' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseOrder.editByRelease' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseOrder.editByCancel' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseOrder.editByFinish' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseOrder.editByClosed' and field_code = 'projectDefineId';

-- 采购需求单
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseRequestOrder.editByCreate' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseRequestOrder.editByRelease' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseRequestOrder.editByCancel' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseRequestOrder.editByFinish' and field_code = 'projectDefineId';
update  dfs_form_field_rule_config  set  is_edit = 0,need_gray = 1,edit_gray = 1 where full_path_code = 'purchaseRequestOrder.editByClosed' and field_code = 'projectDefineId';

-- 销售模块更新字段名称
-- 按单展示
UPDATE `dfs_form_field_config` SET `field_name` = '业务员' WHERE `full_path_code` = 'saleOrder.list.order' AND `field_code` = 'salesmanName' AND `type_code` = 'sysField';
-- 按物料展示
UPDATE `dfs_form_field_config` SET `field_name` = '业务员' WHERE `full_path_code` = 'saleOrder.list.material' AND `field_code` = 'salesmanName' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '业务员简称' WHERE `full_path_code` = 'saleOrder.list.material' AND `field_code` = 'salesmanCode' AND `type_code` = 'sysField';
-- 五种态
UPDATE `dfs_form_field_config` SET `field_name` = '业务员' WHERE `full_path_code` = 'saleOrder.edit' AND `field_code` = 'salesmanName' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '业务员简称' WHERE `full_path_code` = 'saleOrder.edit' AND `field_code` = 'salesmanCode' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '业务员联系方式' WHERE `full_path_code` = 'saleOrder.edit' AND `field_code` = 'salesmanMobile' AND `type_code` = 'sysField';

-- 详情页
UPDATE `dfs_form_field_config` SET `field_name` = '业务员' WHERE `full_path_code` = 'saleOrder.detail' AND `field_code` = 'salesmanName' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '业务员简称' WHERE `full_path_code` = 'saleOrder.detail' AND `field_code` = 'salesmanCode' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '业务员联系方式' WHERE `full_path_code` = 'saleOrder.detail' AND `field_code` = 'salesmanMobile' AND `type_code` = 'sysField';