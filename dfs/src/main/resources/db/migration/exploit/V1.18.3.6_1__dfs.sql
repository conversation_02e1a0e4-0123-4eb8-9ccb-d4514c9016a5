set autocommit = 0;

-- <PERSON><PERSON>
call proc_add_column(
        'dfs_work_order',
        'state_change_time',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `state_change_time` datetime NULL COMMENT ''状态变更时间'' AFTER `state`');


CREATE TABLE IF NOT EXISTS `dfs_device_state_duration_day_union` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `device_id` int(11) DEFAULT NULL COMMENT '设备id',
  `running` varchar(255) DEFAULT NULL COMMENT '工位运行时长（分钟）',
  `pause` varchar(255) DEFAULT NULL COMMENT '产线暂停时长（分钟）',
  `stop` varchar(255) DEFAULT NULL COMMENT '产线停止时长（分钟）',
  `time` varchar(255) DEFAULT NULL COMMENT '时间',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id`) USING BTREE,
  KEY `time` (`time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- DML

commit;
