-- 生产订单批量审批权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`,
                              `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`,
                              `is_enable`, `parent_path`)
VALUES ('10003510', '批量审批', 'product:order:batch:approval', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10003', 2, 1,
        0, 1, '/order-model/product-order');

call proc_add_column(
        'dfs_alarm',
        'higher_notification',
        'ALTER TABLE `dfs_alarm` ADD COLUMN `higher_notification` varchar(512) DEFAULT NULL COMMENT ''上级通知人''');

call proc_add_column(
        'dfs_alarm',
        'start_deal_time',
        'ALTER TABLE `dfs_alarm` ADD COLUMN `start_deal_time` datetime DEFAULT NULL COMMENT  ''开始处理时间''');