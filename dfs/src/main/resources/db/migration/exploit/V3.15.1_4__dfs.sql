-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================

-- 演示数据初始化
DROP PROCEDURE if EXISTS `init_dfs_component_data`;
delimiter $$
CREATE PROCEDURE `init_dfs_component_data`()
top:begin
    
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1, 'ocs.gljsc.jiashuju.scr', 'nJQnhpN-c4', '[{"abnormalName":"外观不良","count":"5"},{"abnormalName":"声音不良","count":"3"},{"abnormalName":"碟片不良1","count":"2"},{"abnormalName":"碟片不良2","count":"9"},{"abnormalName":"碟片不良3","count":"10"}]', '0:00:00', 'yelinkoncall', '2023-12-27 16:24:14');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(11, 'ocs.gljsc.jiashuju.scr', 'GmqtcmUQ7g', '{"rawMaterialWarehouseTotal":"999","semiFinishedProductWarehouseTotal":"256","finishedProductWarehouseTotal":"500","inventoryTurnover":null,"inventoryTurnoverName":"88%","avervageTurnoverCycle":null,"avervageTurnoverCycleName":"7.5天"}', '0:00:00', 'yelinkoncall', '2023-12-27 16:24:54');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(20, 'ocs_center-Production-screen', 'aBTIgJYro3', '[{"lineName":"组装A线","stopTime":"4"},{"lineName":"组装B线","stopTime":"8.7"},{"lineName":"组装C线","stopTime":"2"}]', '0', 'yelinkoncall', '2023-12-27 18:26:56');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(24, 'ocs_center-Production-screen', 'sqB_JF23w7', '{"allPlanQuantity":"2000","allStockAmount":"1600","allCompletionRate":"0.90","allUnqualifiedRate":"0.03","todayPlanQuantity":"400","todayStockAmount":"200","todayCompletionRate":"1","todayUnqualifiedRate":"0.02"}', '0', 'yelinkoncall', '2023-12-27 18:34:34');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(45, 'ocs_wisdom-screen-center', '-nKavsZNbo', '[{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""},{"duration":"140.29天","alarmDes":"激光系统初始化失败","dealState":"0","productionLineName":"COG线","map":{"901":"13","902":"0","903":"0"},"dealNickName":"","amagNickName":""}]', '0', 'yelinkoncall', '2023-12-28 09:51:16');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(48, 'ocs_center-Production-screen', 'ZRkdVoFGj6', '[{"planQuantity":"800","lineName":"组装A线","produceQuantity":"800"},{"planQuantity":"177","lineName":"组装B线","produceQuantity":"177"},{"planQuantity":"245","lineName":"组装C线","produceQuantity":"245"}]', '0', 'yelinkoncall', '2023-12-28 10:03:55');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(51, 'ocs_center-Production-screen', 'vqJnpdXoQT', '[{"materialName":"A1478-S","planQuantity":"200","produceQuantity":"200"},{"materialName":"A1465-B","planQuantity":"400","produceQuantity":"400"},{"materialName":"A1478-W","planQuantity":"111","produceQuantity":"111"},{"materialName":"A1477-C","planQuantity":"900","produceQuantity":"900"}]', '0', 'yelinkoncall', '2023-12-28 10:31:53');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(54, 'ocs_center-Production-screen', 'oejHBooyqR', '[{"lineName":"组装A线","stopTime":"4"},{"lineName":"组装B线","stopTime":"8.7"},{"lineName":"组装C线","stopTime":"2"}]', '0', 'yelinkoncall', '2023-12-29 16:56:47');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(64, 'ocs_center-Production-screen', 'ECKd-eA9rM', '[{"stopCount":"1","unqualified":"0.02","lineName":"组装A线","stopTime":"0.1","oee":"0.4"},{"stopCount":"1","unqualified":"0.03","lineName":"组装B线","stopTime":"0.3","oee":"0.57"},{"stopCount":"0","unqualified":"0.02","lineName":"组装C线","stopTime":"0","oee":"0.45"},{"stopCount":"0","unqualified":"0.02","lineName":"组装D线","stopTime":"0","oee":"0.47"},{"stopCount":"0","unqualified":"0.02","lineName":"组装E线","stopTime":"0","oee":"0.49"}]', '0', 'yelinkoncall', '2024-01-02 15:12:07');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(69, 'ocs_center-Production-screen', '5AERnb3EAd', '[{"lineName":"组装A线","completed":"743"},{"lineName":"组装B线","completed":"220"},{"lineName":"组装C线","completed":"580"},{"lineName":"组装D线","completed":"192"}]', '0', 'yelinkoncall', '2024-01-02 15:44:37');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(70, 'ocs_center-Production-screen', 'QP9dsdvaQ3', '[{"stopCount":"1","lineName":"组装A线"},{"stopCount":"10","lineName":"组装B线"},{"stopCount":"3","lineName":"组装C线"}]', '0', 'yelinkoncall', '2024-01-02 15:44:48');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(71, 'ocs_wisdom-screen-center', '1OOrJoezzz', '[{"recordDate":"2023-12-28","throughRate":"0.91","produceQuantity":"509"},{"recordDate":"2023-12-29","throughRate":"0.88","produceQuantity":"423"},{"recordDate":"2023-12-30","throughRate":"0.92","produceQuantity":"444"},{"recordDate":"2023-12-31","throughRate":"0.79","produceQuantity":"659"},{"recordDate":"2024-01-01","throughRate":"0.91","produceQuantity":"150"},{"recordDate":"2024-01-02","throughRate":"0.89","produceQuantity":"904"},{"recordDate":"2024-01-03","throughRate":"0.90","produceQuantity":"777"}]', '0', 'yelinkoncall', '2024-01-03 09:24:06');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(72, 'ocs_wisdom-screen-center', 'E6LaO5QGku', '[{"avgOee":"0.66","time":"2023-12-28","directRate":"0.9"},{"avgOee":"0.74","time":"2023-12-29","directRate":"0.89"},{"avgOee":"0.5","time":"2023-12-30","directRate":"0.99"},{"avgOee":"0.77","time":"2023-12-31","directRate":"0.89"},{"avgOee":"0.68","time":"2024-01-01","directRate":"0.91"},{"avgOee":"0.59","time":"2024-01-02","directRate":"0.97"},{"avgOee":"0.88","time":"2024-01-03","directRate":"0.91"}]', '0', 'yelinkoncall', '2024-01-03 09:24:12');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(73, 'ocs_wisdom-screen-center', '_7PQKmwoOQ', '[{"orderCompleted":"1648","planQuantity":"625","orderPlanQuantity":"1648","completed":"625","time":"2023-12-28","referenceValue":"0.9"},{"orderCompleted":"1066","planQuantity":"501","orderPlanQuantity":"1066","completed":"501","time":"2023-12-29","referenceValue":"0.9"},{"orderCompleted":"1680","planQuantity":"515","orderPlanQuantity":"1680","completed":"515","time":"2023-12-30","referenceValue":"0.9"},{"orderCompleted":"1991","planQuantity":"910","orderPlanQuantity":"1991","completed":"910","time":"2023-12-31","referenceValue":"0.9"},{"orderCompleted":"1869","planQuantity":"804","orderPlanQuantity":"1869","completed":"804","time":"2024-01-01","referenceValue":"0.9"},{"orderCompleted":"1913","planQuantity":"684","orderPlanQuantity":"1913","completed":"684","time":"2024-01-02","referenceValue":"0.9"},{"orderCompleted":"1271","planQuantity":"573","orderPlanQuantity":"1271","completed":"573","time":"2024-01-03","referenceValue":"0.9"}]', '0', 'yelinkoncall', '2024-01-03 09:24:19');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(74, 'ocs_center-Production-screen', 'a0IYxUThU2', '[{"avgOee":"0.66","time":"2023-12-28","directRate":"0.9"},{"avgOee":"0.74","time":"2023-12-29","directRate":"0.89"},{"avgOee":"0.5","time":"2023-12-30","directRate":"0.99"},{"avgOee":"0.77","time":"2023-12-31","directRate":"0.89"},{"avgOee":"0.68","time":"2024-01-01","directRate":"0.91"},{"avgOee":"0.59","time":"2024-01-02","directRate":"0.97"},{"avgOee":"0.88","time":"2024-01-03","directRate":"0.91"}]', '0', 'yelinkoncall', '2024-01-03 09:31:08');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(76, 'ocs_center-Production-screen', 'piWScToZx_', '[{"stopCount":"1","lineName":"组装A线"},{"stopCount":"10","lineName":"组装B线"},{"stopCount":"3","lineName":"组装C线"}]', '0', 'yelinkoncall', '2024-01-04 11:05:08');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(86, 'OCS-2.29-jp', '6vQJLoM1c_', '[{"achievements":"0.2","actualProductStartTime":"","lineName":"SSS","remark":"","capacity":"10","theoryWorkingHour":"2","stateName":"生效","workingPeopleQuantity":"2","theoryStaffNum":"11","planQuantity":"100","productEfficiencyRate":"0.2","materialCode":"BB","throughRate":"0.5","planProductEndTime":"","produceQuantity":"100","materialName":"CC","completeRate":"5","orderFulfillmentRate":"0.3","actualWorkingHour":"2","workOrderNumber":"AA","unqualifiedQuantity":"1"}]', '11:48:04', 'yelinkoncall', '2024-02-02 14:34:59');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(89, 'OCS-2.29-jp', 'R4pGEuSpWz', '[{"actualEndDate":"","endDate":"2024-01-03 00:00:00","planQuantity":"1111","lineName":"点胶线1","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"型材","unqualified":"0","finishRate":"0","stateName":"投产","finishCount":"0","workOrderName":"SCGD20231229001","workOrderNumber":"SCGD20231229001","actualStartDate":"2023-12-29 17:48:47","startDate":"2023-12-29 17:47:55","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-28 22:18:47","planQuantity":"100","lineName":"点胶线2","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"型材","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"SCGD20231228003","workOrderNumber":"SCGD20231228003","actualStartDate":"","startDate":"2023-12-28 22:18:45","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-29 00:00:00","planQuantity":"100","lineName":"点胶线3","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"型材","unqualified":"0","finishRate":"0","stateName":"投产","finishCount":"0","workOrderName":"jjjjjj000001","workOrderNumber":"SCGD20231228002","actualStartDate":"2023-12-28 20:23:12","startDate":"2023-12-29 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-30 00:00:00","planQuantity":"2000","lineName":"点胶线4","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"型材","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"九成010","workOrderNumber":"SCGD20231228001","actualStartDate":"","startDate":"2023-12-28 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线5","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"SCGD20231218004","workOrderNumber":"SCGD20231218004","actualStartDate":"","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线6","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"SCGD20231218001","workOrderNumber":"SCGD20231218001","actualStartDate":"","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线7","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"SCGD20231218002","workOrderNumber":"SCGD20231218002","actualStartDate":"","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""}]', '12:48:04', 'yelinkoncall', '2024-02-02 14:36:10');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(90, 'OCS-2.29-jp', 'R4pGEuSpWz', '[{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线8","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"SCGD20231218003","workOrderNumber":"SCGD20231218003","actualStartDate":"","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-11 00:00:00","planQuantity":"5","lineName":"点胶线9","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0.2","stateName":"投产","finishCount":"1","workOrderName":"SCGD20231206009","workOrderNumber":"SCGD20231206009","actualStartDate":"2023-12-06 20:23:09","startDate":"2023-12-07 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线10","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0.4444","stateName":"投产","finishCount":"4","workOrderName":"SCGD20231206005","workOrderNumber":"SCGD20231206005","actualStartDate":"2023-12-06 17:19:23","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线11","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0","stateName":"投产","finishCount":"0","workOrderName":"SCGD20231206006","workOrderNumber":"SCGD20231206006","actualStartDate":"2023-12-06 17:19:24","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线12","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"SCGD20231206007","workOrderNumber":"SCGD20231206007","actualStartDate":"","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2023-12-12 00:00:00","planQuantity":"9","lineName":"点胶线13","passRate":"1","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"AOI检测设备","unqualified":"0","finishRate":"0","stateName":"生效","finishCount":"0","workOrderName":"SCGD20231206008","workOrderNumber":"SCGD20231206008","actualStartDate":"","startDate":"2023-12-08 00:00:00","finishedOrderQuantity":""}]', '17:48:04', 'yelinkoncall', '2024-02-02 14:36:10');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(92, 'OCS-2.29-jp', 'WiRKY_x2TB', '[{"rate":"0.1","unqualifiedName":"外观不良A"},{"rate":"0.2","unqualifiedName":"外观不良B"},{"rate":"0.3","unqualifiedName":"外观不良C"},{"rate":"0.4","unqualifiedName":"外观不良D"},{"rate":"0.1","unqualifiedName":"外观不良E"}]', '12:48:04', 'yelinkoncall', '2024-02-02 14:43:02');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(93, 'OCS-2.29-jp', 'AZxCNP5Y4U', '[{"num":"5","unqualifiedName":"不良项名称B1"},{"num":"10","unqualifiedName":"不良项名称B2"},{"num":"11","unqualifiedName":"不良项名称B3"},{"num":"12","unqualifiedName":"不良项名称B4"},{"num":"13","unqualifiedName":"不良项名称B5"},{"num":"14","unqualifiedName":"不良项名称B6"},{"num":"15","unqualifiedName":"不良项名称B7"}]', '14:32:33', 'yelinkoncall', '2024-02-02 15:36:16');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(102, 'ocs_sales_order_perspective', 'EIEkz-aaic', '[{"produceQuantity":"1"},{"produceQuantity":"2"},{"produceQuantity":"3"},{"produceQuantity":"4"},{"produceQuantity":"5"},{"produceQuantity":"6"},{"produceQuantity":"7"}]', '10:09:16', 'yelinkoncall', '2024-03-05 10:10:17');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(105, 'ocs-test-yjkqsjys-330', 'WNmhEoe8aS', '[{"produceQuantity":"1"},{"produceQuantity":"2"},{"produceQuantity":"3"},{"produceQuantity":"4"},{"produceQuantity":"5"},{"produceQuantity":"6"},{"produceQuantity":"7"}]', '10:09:16', 'yelinkoncall', '2024-03-05 10:22:16');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(107, 'ocs_sales_order_perspective_jd', 'RVkl5mOkkL', '[{"salesmanName":"","saleOrderNumber":"DD202403290003","saleOrderMaterial":{"materialFields":{"standard":"","code":"1010635147555218","name":"BSO-FM3E H6.35*14.75-5500"},"salesQuantity":"6000","productionQuantity":"16","requireGoodsDate":"2024-03-29","completionRate":"0","plannedProductionQuantity":"1","orderDate":"2024-03-29"},"customerName":"杭州凯贝奈特科技有限公司--工业区"},{"salesmanName":"","saleOrderNumber":"DD202403290003","saleOrderMaterial":{"materialFields":{"standard":"","code":"1050475040055022","name":"SOO-FM3E H4.75*4.0-4300(板厚0.6)"},"salesQuantity":"3000","productionQuantity":"16","requireGoodsDate":"2024-03-29","completionRate":"0","plannedProductionQuantity":"2","orderDate":"2024-03-29"},"customerName":"杭州凯贝奈特科技有限公司--工业区"},{"salesmanName":"","saleOrderNumber":"DD202403280003","saleOrderMaterial":{"materialFields":{"standard":"","code":"10010800275059021","name":"四角柱 S8.0*27.5-M6B"},"salesQuantity":"70","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"3","orderDate":"2024-03-28"},"customerName":"西安维冠精密设备制造有限责任公司"},{"salesmanName":"","saleOrderNumber":"DD202403280029","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680750090055162","name":"TBSO-FM3B ∮7.5*9.0-6600"},"salesQuantity":"2000","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"4","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060800045074073","name":"铆钉 H8.0*4.5-7100C"},"salesQuantity":"1500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"5","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"},{"salesmanName":"","saleOrderNumber":"DD202403280033","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680400020053020","name":"TSOO-SM2PS ∮4.0*2.0-3300"},"salesQuantity":"148000","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"6","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280009","saleOrderMaterial":{"materialFields":{"standard":"","code":"10757011","name":"W FHS-M4-10"},"salesQuantity":"1440","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"7","orderDate":"2024-03-28"},"customerName":"苏州新育达精密机械有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"1020635055055004","name":"SOO-FM3B H6.35*5.5-5500(01)"},"salesQuantity":"6500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"8","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"},{"salesmanName":"","saleOrderNumber":"DD202403280011","saleOrderMaterial":{"materialFields":{"standard":"","code":"14480550098574366","name":"铆钉 H5.5*9.85-4730B"},"salesQuantity":"40","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"9","orderDate":"2024-03-28"},"customerName":"杭州凯贝奈特科技有限公司---清兴路"},{"salesmanName":"","saleOrderNumber":"DD202403280009","saleOrderMaterial":{"materialFields":{"standard":"","code":"1070800029757006","name":"S-SM4-1A ∮8.0*2.97-5500"},"salesQuantity":"360","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"10","orderDate":"2024-03-28"},"customerName":"苏州新育达精密机械有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280025","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680550028055188","name":"TSOO-FM3CN ∮5.5*2.8-4600"},"salesQuantity":"2500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"11","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280019","saleOrderMaterial":{"materialFields":{"standard":"","code":"1070635024755006","name":"S-SM3-1A ∮6.35*2.47-4300EMC"},"salesQuantity":"8000","productionQuantity":"16","requireGoodsDate":"2024-04-01","completionRate":"0","plannedProductionQuantity":"12","orderDate":"2024-03-28"},"customerName":"上海旻融智能科技有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280011","saleOrderMaterial":{"materialFields":{"standard":"","code":"14480950041074974","name":"卡扣钉 ∮9.5*4.1-8574B 首次"},"salesQuantity":"80","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"13","orderDate":"2024-03-28"},"customerName":"杭州凯贝奈特科技有限公司---清兴路"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060640045055074","name":"SO-FM3C H6.35*4.5-5500"},"salesQuantity":"4500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"14","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"},{"salesmanName":"","saleOrderNumber":"DD202403280025","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680700034053011","name":"TSOOPC-FM2B ∮7.0*3.4-6140"},"salesQuantity":"4500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"15","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280007","saleOrderMaterial":{"materialFields":{"standard":"","code":"10262137","name":"W GB/T825-1988  吊环M12*22  镀蓝白锌"},"salesQuantity":"100","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"16","orderDate":"2024-03-28"},"customerName":"上海曦创通讯设备制造有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060700061055075","name":"SOPC-FM3C H7.0*6.1-6645"},"salesQuantity":"2000","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"17","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"}]', '16:47:59', 'yelinkoncall', '2024-04-08 16:33:32');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(108, 'ocs_sales_order_perspective', 'vjXwwej3xg', '[{"salesmanName":"","saleOrderNumber":"DD202403290003","saleOrderMaterial":{"materialFields":{"standard":"","code":"1010635147555218","name":"BSO-FM3E H6.35*14.75-5500"},"salesQuantity":"6000","productionQuantity":"16","requireGoodsDate":"2024-03-29","completionRate":"0","plannedProductionQuantity":"1","orderDate":"2024-03-29"},"customerName":"杭州凯贝奈特科技有限公司--工业区"},{"salesmanName":"","saleOrderNumber":"DD202403290003","saleOrderMaterial":{"materialFields":{"standard":"","code":"1050475040055022","name":"SOO-FM3E H4.75*4.0-4300(板厚0.6)"},"salesQuantity":"3000","productionQuantity":"16","requireGoodsDate":"2024-03-29","completionRate":"0","plannedProductionQuantity":"2","orderDate":"2024-03-29"},"customerName":"杭州凯贝奈特科技有限公司--工业区"},{"salesmanName":"","saleOrderNumber":"DD202403280003","saleOrderMaterial":{"materialFields":{"standard":"","code":"10010800275059021","name":"四角柱 S8.0*27.5-M6B"},"salesQuantity":"70","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"3","orderDate":"2024-03-28"},"customerName":"西安维冠精密设备制造有限责任公司"},{"salesmanName":"","saleOrderNumber":"DD202403280029","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680750090055162","name":"TBSO-FM3B ∮7.5*9.0-6600"},"salesQuantity":"2000","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"4","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060800045074073","name":"铆钉 H8.0*4.5-7100C"},"salesQuantity":"1500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"5","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"},{"salesmanName":"","saleOrderNumber":"DD202403280033","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680400020053020","name":"TSOO-SM2PS ∮4.0*2.0-3300"},"salesQuantity":"148000","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"6","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280009","saleOrderMaterial":{"materialFields":{"standard":"","code":"10757011","name":"W FHS-M4-10"},"salesQuantity":"1440","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"7","orderDate":"2024-03-28"},"customerName":"苏州新育达精密机械有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"1020635055055004","name":"SOO-FM3B H6.35*5.5-5500(01)"},"salesQuantity":"6500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"8","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"},{"salesmanName":"","saleOrderNumber":"DD202403280011","saleOrderMaterial":{"materialFields":{"standard":"","code":"14480550098574366","name":"铆钉 H5.5*9.85-4730B"},"salesQuantity":"40","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"9","orderDate":"2024-03-28"},"customerName":"杭州凯贝奈特科技有限公司---清兴路"},{"salesmanName":"","saleOrderNumber":"DD202403280009","saleOrderMaterial":{"materialFields":{"standard":"","code":"1070800029757006","name":"S-SM4-1A ∮8.0*2.97-5500"},"salesQuantity":"360","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"10","orderDate":"2024-03-28"},"customerName":"苏州新育达精密机械有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280025","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680550028055188","name":"TSOO-FM3CN ∮5.5*2.8-4600"},"salesQuantity":"2500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"11","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280019","saleOrderMaterial":{"materialFields":{"standard":"","code":"1070635024755006","name":"S-SM3-1A ∮6.35*2.47-4300EMC"},"salesQuantity":"8000","productionQuantity":"16","requireGoodsDate":"2024-04-01","completionRate":"0","plannedProductionQuantity":"12","orderDate":"2024-03-28"},"customerName":"上海旻融智能科技有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280011","saleOrderMaterial":{"materialFields":{"standard":"","code":"14480950041074974","name":"卡扣钉 ∮9.5*4.1-8574B 首次"},"salesQuantity":"80","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"13","orderDate":"2024-03-28"},"customerName":"杭州凯贝奈特科技有限公司---清兴路"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060640045055074","name":"SO-FM3C H6.35*4.5-5500"},"salesQuantity":"4500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"14","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"},{"salesmanName":"","saleOrderNumber":"DD202403280025","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680700034053011","name":"TSOOPC-FM2B ∮7.0*3.4-6140"},"salesQuantity":"4500","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"15","orderDate":"2024-03-28"},"customerName":"达运精密工业（厦门）有限公"},{"salesmanName":"","saleOrderNumber":"DD202403280007","saleOrderMaterial":{"materialFields":{"standard":"","code":"10262137","name":"W GB/T825-1988  吊环M12*22  镀蓝白锌"},"salesQuantity":"100","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"16","orderDate":"2024-03-28"},"customerName":"上海曦创通讯设备制造有限公司"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060700061055075","name":"SOPC-FM3C H7.0*6.1-6645"},"salesQuantity":"2000","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","plannedProductionQuantity":"17","orderDate":"2024-03-28"},"customerName":"常州振扬电子有限公司(JD006)"}]', '16:47:59', 'yelinkoncall', '2024-04-08 16:40:56');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(109, 'ocs_sales_order_perspective_jianding', 'HCiD_z47cF', '[{"salesmanName":"","saleOrderNumber":"DD202403290003","saleOrderMaterial":{"materialFields":{"standard":"","code":"1010635147555218","name":"BSO-FM3E H6.35*14.75-5500"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-29","completionRate":"0","orderDate":"2024-03-29"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403290003","saleOrderMaterial":{"materialFields":{"standard":"","code":"1050475040055022","name":"SOO-FM3E H4.75*4.0-4300(板厚0.6)"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-29","completionRate":"0","orderDate":"2024-03-29"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280003","saleOrderMaterial":{"materialFields":{"standard":"","code":"10010800275059021","name":"四角柱 S8.0*27.5-M6B"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280029","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680750090055162","name":"TBSO-FM3B ∮7.5*9.0-6600"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060800045074073","name":"铆钉 H8.0*4.5-7100C"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280033","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680400020053020","name":"TSOO-SM2PS ∮4.0*2.0-3300"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280009","saleOrderMaterial":{"materialFields":{"standard":"","code":"10757011","name":"W FHS-M4-10"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"1020635055055004","name":"SOO-FM3B H6.35*5.5-5500(01)"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280011","saleOrderMaterial":{"materialFields":{"standard":"","code":"14480550098574366","name":"铆钉 H5.5*9.85-4730B"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280009","saleOrderMaterial":{"materialFields":{"standard":"","code":"1070800029757006","name":"S-SM4-1A ∮8.0*2.97-5500"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280025","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680550028055188","name":"TSOO-FM3CN ∮5.5*2.8-4600"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280019","saleOrderMaterial":{"materialFields":{"standard":"","code":"1070635024755006","name":"S-SM3-1A ∮6.35*2.47-4300EMC"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-04-01","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280011","saleOrderMaterial":{"materialFields":{"standard":"","code":"14480950041074974","name":"卡扣钉 ∮9.5*4.1-8574B 首次"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060640045055074","name":"SO-FM3C H6.35*4.5-5500"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280025","saleOrderMaterial":{"materialFields":{"standard":"","code":"10680700034053011","name":"TSOOPC-FM2B ∮7.0*3.4-6140"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280007","saleOrderMaterial":{"materialFields":{"standard":"","code":"10262137","name":"W GB/T825-1988  吊环M12*22  镀蓝白锌"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"},{"salesmanName":"","saleOrderNumber":"DD202403280014","saleOrderMaterial":{"materialFields":{"standard":"","code":"10060700061055075","name":"SOPC-FM3C H7.0*6.1-6645"},"salesQuantity":"#{销售数量}","productionQuantity":"16","requireGoodsDate":"2024-03-28","completionRate":"0","orderDate":"2024-03-28"},"customerName":"#{客户名称}"}]', '16:47:59', 'yelinkoncall', '2024-04-08 17:18:21');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(114, 'ocs_machining_produce_perspective', 'NIfrO_35UP', '[{"orderId":"1","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"20","circulationDuration":0,"procedureName":"切割","sumCirculationDuration":0},{"orderId":"1","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"3","circulationDuration":0,"procedureName":"抛光","sumCirculationDuration":0},{"orderId":"1","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"5","circulationDuration":0,"procedureName":"组装","sumCirculationDuration":0},{"orderId":"2","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"16","circulationDuration":0,"procedureName":"切割","sumCirculationDuration":0},{"orderId":"2","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"7","circulationDuration":0,"procedureName":"抛光","sumCirculationDuration":0},{"orderId":"2","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"3","circulationDuration":0,"procedureName":"组装","sumCirculationDuration":0},{"orderId":"3","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"2","circulationDuration":0,"procedureName":"切割","sumCirculationDuration":0},{"orderId":"3","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"0","circulationDuration":0,"procedureName":"抛光","sumCirculationDuration":0},{"orderId":"3","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"unqualified":"0","circulationDuration":0,"procedureName":"组装","sumCirculationDuration":0}]', '0', 'yelinkoncall', '2024-04-30 12:16:30');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(115, 'ocs_machining_produce_perspective', 'C95sVLDvn_', '[{"orderId":"1","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"300","circulationDuration":0,"procedureName":"切割","sumCirculationDuration":0},{"orderId":"1","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"200","circulationDuration":0,"procedureName":"抛光","sumCirculationDuration":0},{"orderId":"1","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"180","circulationDuration":0,"procedureName":"组装","sumCirculationDuration":0},{"orderId":"2","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"1600","circulationDuration":0,"procedureName":"切割","sumCirculationDuration":0},{"orderId":"2","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"800","circulationDuration":0,"procedureName":"抛光","sumCirculationDuration":0},{"orderId":"2","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"800","circulationDuration":0,"procedureName":"组装","sumCirculationDuration":0},{"orderId":"3","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"40","circulationDuration":0,"procedureName":"切割","sumCirculationDuration":0},{"orderId":"3","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"0","circulationDuration":0,"procedureName":"抛光","sumCirculationDuration":0},{"orderId":"3","conversionFactor":1,"inStockCount":0,"flowTimeoutThresholdConfiguration":0,"sumFinishCount":"0","circulationDuration":0,"procedureName":"组装","sumCirculationDuration":0}]', '0', 'yelinkoncall', '2024-04-30 12:16:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(116, 'ocs_machining_produce_perspective', 'hhjrQWGHCs', '[{"orderNumber":"scdd1011","orderId":"1","actualProductStartTime":"2024/1/12","releasedCount":"3","remark":"","productionCode":"fzcp","planQuantity":"2000","planProductEndTime":"2024/10/20","abnormalCount":"0","procedureProcess":"0.3","completedRate":"0.3","finishCount":"600","productionName":"工程板"},{"orderNumber":"scdd1012","orderId":"2","actualProductStartTime":"2024/1/15","releasedCount":"","remark":"","productionCode":"jdcp","planQuantity":"8000","planProductEndTime":"2024/8/17","abnormalCount":"","procedureProcess":"0.2","completedRate":"0.2","finishCount":"1600","productionName":"钉板"},{"orderNumber":"scdd1013","orderId":"3","actualProductStartTime":"2024/1/13","releasedCount":"","remark":"","productionCode":"zscp","planQuantity":"16000","planProductEndTime":"2024/4/16","abnormalCount":"","procedureProcess":"0.05","completedRate":"0.05","finishCount":"800","productionName":"螺母"}]', '0', 'yelinkoncall', '2024-04-30 12:16:36');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(117, 'ocs_sales_order_perspective', 'RVkl5mOkkL', '[{"salesmanName":"陈军亮","saleOrderNumber":"SC202207012121","saleOrderMaterial":{"materialFields":{"standard":"","code":"123","name":"显示屏"},"salesQuantity":"20000","productionQuantity":"12001","requireGoodsDate":"2024/7/19","completionRate":"0.60005","orderDate":"2024/2/19"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SC20220701A003","saleOrderMaterial":{"materialFields":{"standard":"","code":"A001","name":"订单下推测试"},"salesQuantity":"600000","productionQuantity":"111880","requireGoodsDate":"2024/7/15","completionRate":"0.1864666667","orderDate":"2024/2/15"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SC202207051217","saleOrderMaterial":{"materialFields":{"standard":"","code":"zzz123","name":"亮光灯"},"salesQuantity":"580000","productionQuantity":"389129","requireGoodsDate":"2024/7/7","completionRate":"0.670912069","orderDate":"2024/2/2"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SC202207050013001","saleOrderMaterial":{"materialFields":{"standard":"","code":"DB001","name":"撕膜机"},"salesQuantity":"300000","productionQuantity":"172700","requireGoodsDate":"2024/7/20","completionRate":"0.5756666667","orderDate":"2024/2/20"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SC202207050013003","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-2","name":"组件2"},"salesQuantity":"600000","productionQuantity":"280526","requireGoodsDate":"2024/7/20","completionRate":"0.4675433333","orderDate":"2024/2/20"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220726","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-1","name":"组件1"},"salesQuantity":"220000","productionQuantity":"101292","requireGoodsDate":"2024/7/7","completionRate":"0.4604181818","orderDate":"2024/2/2"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220727001","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-1","name":"组件1"},"salesQuantity":"180000","productionQuantity":"49373","requireGoodsDate":"2024/7/7","completionRate":"0.2742944444","orderDate":"2024/2/2"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220729001","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-1","name":"组件1"},"salesQuantity":"20000","productionQuantity":"1437","requireGoodsDate":"2024/7/7","completionRate":"0.07185","orderDate":"2024/2/2"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220733001","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-1","name":"组件1"},"salesQuantity":"500000","productionQuantity":"430271","requireGoodsDate":"2024/7/18","completionRate":"0.860542","orderDate":"2024/2/18"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220738","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-4","name":"组件4"},"salesQuantity":"400000","productionQuantity":"7380","requireGoodsDate":"2024/7/10","completionRate":"0.01845","orderDate":"2024/2/10"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220742","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-2","name":"组件2"},"salesQuantity":"380000","productionQuantity":"374426","requireGoodsDate":"2024/7/7","completionRate":"0.9853315789","orderDate":"2024/2/2"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220746001","saleOrderMaterial":{"materialFields":{"standard":"","code":"SCX10-4","name":"组件4"},"salesQuantity":"220000","productionQuantity":"52632","requireGoodsDate":"2024/7/12","completionRate":"0.2392363636","orderDate":"2024/2/12"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220751","saleOrderMaterial":{"materialFields":{"standard":"","code":"wuliao023002","name":"显卡"},"salesQuantity":"220000","productionQuantity":"56223","requireGoodsDate":"2024/7/12","completionRate":"0.2555590909","orderDate":"2024/2/12"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220756","saleOrderMaterial":{"materialFields":{"standard":"","code":"023003","name":"水质监测仪器"},"salesQuantity":"520000","productionQuantity":"272136","requireGoodsDate":"2024/9/30","completionRate":"0.5233384615","orderDate":"2024/1/30"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD220778","saleOrderMaterial":{"materialFields":{"standard":"","code":"A-A0002","name":"胶囊测试2"},"salesQuantity":"500000","productionQuantity":"188158","requireGoodsDate":"2024/7/30","completionRate":"0.376316","orderDate":"2024/2/18"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD2207106001","saleOrderMaterial":{"materialFields":{"standard":"","code":"A-A0002","name":"胶囊测试2"},"salesQuantity":"40000","productionQuantity":"20915","requireGoodsDate":"2024/7/30","completionRate":"0.522875","orderDate":"2024/2/18"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD2207124001","saleOrderMaterial":{"materialFields":{"standard":"","code":"LW5003","name":"LW5003主板"},"salesQuantity":"560000","productionQuantity":"289413","requireGoodsDate":"2024/7/30","completionRate":"0.5168089286","orderDate":"2024/2/18"},"customerName":"HW"},{"salesmanName":"陈军亮","saleOrderNumber":"SCDD2207139001","saleOrderMaterial":{"materialFields":{"standard":"","code":"A-A0002","name":"胶囊测试2"},"salesQuantity":"280000","productionQuantity":"233041","requireGoodsDate":"2024/7/27","completionRate":"0.8322892857","orderDate":"2024/2/22"},"customerName":"HW"}]', '0', 'yelinkoncall', '2024-04-30 12:16:51');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(118, 'ocs_wisdom-screen-center', 'uFOi5iM9ir', '{"allPlanQuantity":"2000","allStockAmount":"1600","allCompletionRate":"0.90","allUnqualifiedRate":"0.03","todayPlanQuantity":"400","todayStockAmount":"200","todayCompletionRate":"1","todayUnqualifiedRate":"0.02"}', '0', 'yelinkoncall', '2024-04-30 12:20:16');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(121, 'ocs-product-scr', '42IpX0uQ4j', '[{"actualEndDate":"","endDate":"2024-3-10 0:00","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9045191783","actualOrderQuantity":"14","needOrderQuantity":"15","planOrderQuantity":"13","materialName":"电容1","unqualified":"0","finishRate":"0.048","stateName":"生效","finishCount":"48","workOrderName":"ceeeeee0001","workOrderNumber":"ceeeeee0001","actualStartDate":"","startDate":"2024-01-02 00:00:00","finishedOrderQuantity":"0"},{"actualEndDate":"","endDate":"2024-3-6 15:23","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9776147755","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电容2","unqualified":"0","finishRate":"0.385","stateName":"生效","finishCount":"385","workOrderName":"gd2023122900009","workOrderNumber":"gd2023122900009","actualStartDate":"","startDate":"2023-12-29 15:23:30","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-3-4 15:23","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.8911930329","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电容3","unqualified":"0","finishRate":"0.091","stateName":"生效","finishCount":"91","workOrderName":"gd2023122900007","workOrderNumber":"gd2023122900007","actualStartDate":"","startDate":"2023-12-29 15:23:30","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-3-14 15:23","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9837795328","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电容4","unqualified":"0","finishRate":"0.708","stateName":"生效","finishCount":"708","workOrderName":"gd2023122900008","workOrderNumber":"gd2023122900008","actualStartDate":"","startDate":"2023-12-29 15:23:30","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-3-24 15:12","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.8772048539","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电容5","unqualified":"0","finishRate":"0.478","stateName":"生效","finishCount":"478","workOrderName":"gd2023122900006","workOrderNumber":"gd2023122900006","actualStartDate":"","startDate":"2023-12-29 15:12:08","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-3-17 15:12","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9155415323","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电容6","unqualified":"0","finishRate":"0.03","stateName":"投产","finishCount":"30","workOrderName":"gd2023122900004","workOrderNumber":"gd2023122900004","actualStartDate":"2023-12-29 15:12:07","startDate":"2023-12-29 15:12:07","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-2-3 15:12","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9187643575","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电容7","unqualified":"0","finishRate":"0.809","stateName":"生效","finishCount":"809","workOrderName":"gd2023122900005","workOrderNumber":"gd2023122900005","actualStartDate":"","startDate":"2023-12-29 15:12:08","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-3-24 0:00","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9965195026","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电阻1","unqualified":"0","finishRate":"0.794","stateName":"投产","finishCount":"794","workOrderName":"gd2023122900003","workOrderNumber":"gd2023122900003","actualStartDate":"2023-12-29 00:00:00","startDate":"2023-12-29 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-3-22 0:00","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9187765746","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电阻2","unqualified":"0","finishRate":"0.53","stateName":"生效","finishCount":"530","workOrderName":"gd2023122900002","workOrderNumber":"gd2023122900002","actualStartDate":"","startDate":"2024-01-05 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-2-13 0:00","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.8807509907","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电阻3","unqualified":"0","finishRate":"0.069","stateName":"生效","finishCount":"69","workOrderName":"WO231200010","workOrderNumber":"WO231200010","actualStartDate":"","startDate":"2023-12-11 0:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-3-11 0:00","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.8639650408","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电阻4","unqualified":"0","finishRate":"0.915","stateName":"生效","finishCount":"915","workOrderName":"gd2023122800020","workOrderNumber":"gd2023122800020","actualStartDate":"","startDate":"2024-01-03 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-2-12 0:00","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.9891016997","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电阻5","unqualified":"0","finishRate":"0.855","stateName":"生效","finishCount":"855","workOrderName":"gd2023122800019","workOrderNumber":"gd2023122800019","actualStartDate":"","startDate":"2023-12-19 00:00:00","finishedOrderQuantity":""},{"actualEndDate":"","endDate":"2024-2-24 0:00","planQuantity":"1000","lineName":"电芯到模组产线","passRate":"0.8755267161","actualOrderQuantity":"","needOrderQuantity":"","planOrderQuantity":"","materialName":"电阻6","unqualified":"0","finishRate":"0.362","stateName":"生效","finishCount":"362","workOrderName":"WO231200009","workOrderNumber":"WO231200009","actualStartDate":"","startDate":"2023-12-12 00:00:00","finishedOrderQuantity":""}]', '0', 'yelinkoncall', '2024-04-30 12:22:19');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(122, 'ocs-product-scr', 'tkJ6lulnY_', '{"orderAchieveDTO":{"yieldRate":"0.11","gross":"8888","completion":"1000"},"dayReachDTO":{"yieldRate":"0.68","gross":"88888","completion":"60000"},"yieldDTO":{"yieldRate":"0.99","gross":null,"completion":null}}', '0', 'yelinkoncall', '2024-04-30 12:22:38');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(123, 'ocs-product-scr', 'dPKXDszdj-', '[{"gridName":"","lineBeats":[{"beats":[{"beat":"0.581"},{"beat":"0.506"},{"beat":"0.595"},{"beat":"0.581"},{"beat":"0.524"},{"beat":"0.534"},{"beat":"0.588"}],"lineName":"电芯到模组产线A"}]}]', '0', 'yelinkoncall', '2024-04-30 12:22:45');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(124, 'ocs-product-scr', 'KoMNGKE74Q', '[{"orderCompleted":"95","planQuantity":"80","orderPlanQuantity":"92","completed":"94","referenceValue":"0.9"},{"orderCompleted":"95","planQuantity":"90","orderPlanQuantity":"81","completed":"81","referenceValue":"0.9"},{"orderCompleted":"87","planQuantity":"81","orderPlanQuantity":"84","completed":"94","referenceValue":"0.9"},{"orderCompleted":"97","planQuantity":"93","orderPlanQuantity":"97","completed":"80","referenceValue":"0.9"},{"orderCompleted":"88","planQuantity":"97","orderPlanQuantity":"100","completed":"90","referenceValue":"0.9"},{"orderCompleted":"87","planQuantity":"82","orderPlanQuantity":"96","completed":"80","referenceValue":"0.9"},{"orderCompleted":"95","planQuantity":"95","orderPlanQuantity":"99","completed":"83","referenceValue":"0.9"}]', '0', 'yelinkoncall', '2024-04-30 12:22:51');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(125, 'ocs-product-scr', 'nsqtcJ_kXx', '[{"avgOee":"0.4991681306","directRate":"0.8465953822"},{"avgOee":"0.5259360695","directRate":"0.8692270418"},{"avgOee":"0.651103259","directRate":"0.8358901695"},{"avgOee":"0.4604326462","directRate":"0.9040532264"},{"avgOee":"0.7068894584","directRate":"0.8679217787"},{"avgOee":"0.6423814664","directRate":"0.9358366704"},{"avgOee":"0.886794215","directRate":"0.8879902987"}]', '0', 'yelinkoncall', '2024-04-30 12:23:02');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(126, 'ocs_line_product_plan', '9-JhzGDyMu', '[{"orderNumber":"SC202207012121","orderId":"241","actualProductStartTime":"","releasedCount":"20","remark":"","productionCode":"123","planQuantity":"80","plannedDeliveryDate":"2024/7/19","planProductEndTime":"2022-07-19","finishOrderCount":"5","relatedOrderNum":"","abnormalCount":"0","procedureProcess":"0.773","procedureNameList":["折弯"],"completedRate":"0","finishCount":"0","productionName":"显示屏"},{"orderNumber":"SC20220701A003","orderId":"243","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"A001","planQuantity":"200","plannedDeliveryDate":"2024/7/15","planProductEndTime":"2022-07-15","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.615","procedureNameList":["折弯"],"completedRate":"0","finishCount":"0","productionName":"订单下推测试"},{"orderNumber":"SC202207051217","orderId":"253","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"zzz123","planQuantity":"100","plannedDeliveryDate":"2024/7/7","planProductEndTime":"2022-07-07","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.497","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"亮光灯"},{"orderNumber":"SC202207050013001","orderId":"256","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"DB001","planQuantity":"5","plannedDeliveryDate":"2024/7/20","planProductEndTime":"2022-07-20","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.721","procedureNameList":["铣"],"completedRate":"0.4","finishCount":"2","productionName":"撕膜机"},{"orderNumber":"SC202207050013003","orderId":"258","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"SCX10-2","planQuantity":"20","plannedDeliveryDate":"2024/7/20","planProductEndTime":"2022-07-20","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.836","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"组件2"},{"orderNumber":"SCDD220726","orderId":"268","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"SCX10-1","planQuantity":"100","plannedDeliveryDate":"2024/7/7","planProductEndTime":"2022-07-07","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.005","procedureNameList":["折弯"],"completedRate":"0","finishCount":"0","productionName":"组件1"},{"orderNumber":"SCDD220727001","orderId":"270","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"SCX10-1","planQuantity":"100","plannedDeliveryDate":"2024/7/7","planProductEndTime":"2022-07-07","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.635","procedureNameList":["钻孔"],"completedRate":"0","finishCount":"0","productionName":"组件1"},{"orderNumber":"SCDD220729001","orderId":"271","actualProductStartTime":"2022-07-07","releasedCount":"","remark":"","productionCode":"SCX10-1","planQuantity":"100","plannedDeliveryDate":"2024/7/7","planProductEndTime":"2022-07-07","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.057","procedureNameList":["钻孔"],"completedRate":"0.2","finishCount":"20","productionName":"组件1"},{"orderNumber":"SCDD220733001","orderId":"275","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"SCX10-1","planQuantity":"100","plannedDeliveryDate":"2024/7/18","planProductEndTime":"2022-07-18","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.975","procedureNameList":["钻孔"],"completedRate":"0.2","finishCount":"20","productionName":"组件1"},{"orderNumber":"SCDD220738","orderId":"280","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"SCX10-4","planQuantity":"20","plannedDeliveryDate":"2024/7/10","planProductEndTime":"2022-07-10","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.715","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"组件4"},{"orderNumber":"SCDD220742","orderId":"284","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"SCX10-2","planQuantity":"10","plannedDeliveryDate":"2024/7/7","planProductEndTime":"2022-07-07","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.336","procedureNameList":["折弯"],"completedRate":"0","finishCount":"0","productionName":"组件2"},{"orderNumber":"SCDD220746001","orderId":"287","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"SCX10-4","planQuantity":"10000","plannedDeliveryDate":"2024/7/12","planProductEndTime":"2022-07-12","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.556","procedureNameList":["钻孔"],"completedRate":"0","finishCount":"0","productionName":"组件4"},{"orderNumber":"SCDD220751","orderId":"291","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"wuliao023002","planQuantity":"100","plannedDeliveryDate":"2024/7/12","planProductEndTime":"2022-07-12","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.367","procedureNameList":["折弯"],"completedRate":"0","finishCount":"0","productionName":"显卡"},{"orderNumber":"SCDD220756","orderId":"293","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"023003","planQuantity":"90","plannedDeliveryDate":"2024/9/30","planProductEndTime":"2022-09-30","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.278","procedureNameList":["折弯"],"completedRate":"0.22","finishCount":"20","productionName":"水质监测仪器"},{"orderNumber":"SCDD220778","orderId":"308","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"A-A0002","planQuantity":"60","plannedDeliveryDate":"2024/7/30","planProductEndTime":"2022-07-30","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.459","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"胶囊测试2"},{"orderNumber":"SCDD220789","orderId":"313","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"A-A0002","planQuantity":"200","plannedDeliveryDate":"2024/7/30","planProductEndTime":"2022-07-30","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.322","procedureNameList":["溶胶"],"completedRate":"0","finishCount":"0","productionName":"胶囊测试2"},{"orderNumber":"SCDD2207106001","orderId":"324","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"LW5003","planQuantity":"80","plannedDeliveryDate":"2024/7/30","planProductEndTime":"2022-07-30","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.113","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"LW5003主板"},{"orderNumber":"SCDD2207124001","orderId":"345","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"A-A0002","planQuantity":"2800","plannedDeliveryDate":"2024/7/27","planProductEndTime":"2022-07-27","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.190","procedureNameList":["折弯"],"completedRate":"0","finishCount":"0","productionName":"胶囊测试2"},{"orderNumber":"SCDD2207139001","orderId":"361","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"A-A0002","planQuantity":"2098","plannedDeliveryDate":"2024/7/28","planProductEndTime":"2022-07-28","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.892","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"胶囊测试2"},{"orderNumber":"生产订单2207206001","orderId":"412","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"H20220729-4","planQuantity":"40","plannedDeliveryDate":"2024/7/31","planProductEndTime":"2022-07-31","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.044","procedureNameList":["折弯"],"completedRate":"0","finishCount":"0","productionName":"电脑整机"},{"orderNumber":"生产订单2208230001","orderId":"428","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"LW5003","planQuantity":"500","plannedDeliveryDate":"2024/8/5","planProductEndTime":"2022-08-05","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.725","procedureNameList":["钻孔"],"completedRate":"0","finishCount":"0","productionName":"LW5003主板"},{"orderNumber":"生产订单2208239","orderId":"432","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"LW5003","planQuantity":"1000","plannedDeliveryDate":"2024/8/11","planProductEndTime":"2022-08-11","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.441","procedureNameList":["钻孔"],"completedRate":"0","finishCount":"0","productionName":"LW5003主板"},{"orderNumber":"生产订单2208243","orderId":"435","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"LW5003","planQuantity":"1500","plannedDeliveryDate":"2024/8/11","planProductEndTime":"2022-08-11","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.279","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"LW5003主板"},{"orderNumber":"生产订单2208244001","orderId":"437","actualProductStartTime":"","releasedCount":"","remark":"","productionCode":"LW5003","planQuantity":"12","plannedDeliveryDate":"2024/8/10","planProductEndTime":"2022-08-10","finishOrderCount":"","relatedOrderNum":"","abnormalCount":"","procedureProcess":"0.559","procedureNameList":["铣"],"completedRate":"0","finishCount":"0","productionName":"LW5003主板"}]', '0', 'yelinkoncall', '2024-04-30 12:23:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(127, 'ocs.gljsc.jiashuju.scr', '9OcU20Zql6', '[{"analysisCompareVO":{"growthStatus":"down","name":"同比","value":"1%"},"name":"订单总量","value":"8742"},{"analysisCompareVO":{"growthStatus":"up","name":"同比","value":"2%"},"name":"总交付率","value":"97%"},{"analysisCompareVO":{"growthStatus":"up","name":"同比","value":"14%"},"name":"订单及时交付率","value":"92%"},{"analysisCompareVO":{"growthStatus":"down","name":"同比","value":"1%"},"name":"月均订单量","value":"728.5"},{"analysisCompareVO":{"growthStatus":"up","name":"同比","value":"4%"},"name":"订单批次均量","value":"20800"}]', '0:00:00', 'yelinkoncall', '2024-12-03 08:45:34');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(129, 'ocs.gljsc.jiashuju.scr', '_8VMIFvy0i', '{"thisYear":[{"month":"1月","count":"718"},{"month":"2月","count":"894"},{"month":"3月","count":"708"},{"month":"4月","count":"810"},{"month":"5月","count":"867"},{"month":"6月","count":"766"},{"month":"7月","count":"775"},{"month":"8月","count":"806"},{"month":"9月","count":"1194"},{"month":"10月","count":"1188"},{"month":"11月","count":"902"},{"month":"12月","count":"12"}],"lastYear":[{"month":"1月","count":"672"},{"month":"2月","count":"692"},{"month":"3月","count":"868"},{"month":"4月","count":"606"},{"month":"5月","count":"822"},{"month":"6月","count":"845"},{"month":"7月","count":"628"},{"month":"8月","count":"808"},{"month":"9月","count":"647"},{"month":"10月","count":"725"},{"month":"11月","count":"854"},{"month":"12月","count":"679"}],"outputStandard":"800"}', '0', 'yelinkoncall', '2024-12-03 08:46:16');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(554, 'ocs.gljsc.jiashuju.scr', 'bDa_t_sH8n', '[{"analysisCompareVO":{"growthStatus":"up","name":"同比","value":"9%"},"name":"客户总数","value":"267"},{"analysisCompareVO":{"growthStatus":"up","name":"同比","value":"18%"},"name":"订单客户数","value":"131"},{"analysisCompareVO":{"growthStatus":"up","name":"同比","value":"8%"},"name":"订单客户比列","value":"49%"},{"analysisCompareVO":{"growthStatus":"up","name":"同比","value":"20%"},"name":"客户复单率","value":"70%"}]', '0', 'yelinkoncall', '2024-12-03 09:04:34');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(555, 'ocs.gljsc.jiashuju.scr', 'Mwv6qGPa9q', '{"topFiveCustomerOrderProportion":"70%","pieChartEntityList":[{"code":"KH20241127003","name":"南昌棉柔巾","value":"21"},{"code":"KH20241127002","name":"新余棉柔巾","value":"18"},{"code":"KH20241127001","name":"杭州棉柔巾","value":"15"},{"code":"KH20241127004","name":"宜春棉柔巾","value":"10"},{"code":"KH20241127008","name":"赣州棉柔巾","value":"6"},{"value":"30","name":"其它客户","code":null}]}', '0', 'yelinkoncall', '2024-12-03 09:04:49');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(556, 'ocs.gljsc.jiashuju.scr', 'yx-D_uBXhg', '[{"materialName":"棉柔世家棉柔巾15*20","number":"420","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127085","completionRate":"79.06%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"2000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127084","completionRate":"29.93%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127083","completionRate":"89.25%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"2500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127082","completionRate":"68.82%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"3500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127081","completionRate":"18.54%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"4500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127080","completionRate":"96.73%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"10000","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127079","completionRate":"14.84%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1300","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127078","completionRate":"0.97%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1200","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127077","completionRate":"83.23%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"9500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127076","completionRate":"38.78%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"8500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127075","completionRate":"60.11%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1600","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127074","completionRate":"28.59%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1400","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127073","completionRate":"86.20%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1750","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127072","completionRate":"77.65%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"7500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127071","completionRate":"14.90%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"6500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127070","completionRate":"28.32%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"5500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127069","completionRate":"15.13%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"20000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127068","completionRate":"70.58%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127067","completionRate":"68.03%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"800","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127066","completionRate":"11.44%","customerName":"新余棉柔巾"}]', '0', 'yelinkoncall', '2024-12-03 09:05:12');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(557, 'ocs.gljsc.jiashuju.scr', 'yx-D_uBXhg', '[{"materialName":"棉柔世家棉柔巾15*20","number":"420","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127085","completionRate":"81.26%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"2000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127084","completionRate":"32.30%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127083","completionRate":"92.01%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"2500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127082","completionRate":"71.46%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"3500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127081","completionRate":"19.27%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"4500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127080","completionRate":"97.51%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"10000","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127079","completionRate":"15.60%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1300","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127078","completionRate":"3.06%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1200","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127077","completionRate":"84.70%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"9500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127076","completionRate":"40.91%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"8500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127075","completionRate":"61.96%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1600","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127074","completionRate":"30.50%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1400","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127073","completionRate":"88.42%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1750","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127072","completionRate":"78.63%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"7500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127071","completionRate":"17.41%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"6500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127070","completionRate":"31.30%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"5500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127069","completionRate":"17.38%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"20000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127068","completionRate":"72.00%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127067","completionRate":"70.97%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"800","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127066","completionRate":"12.81%","customerName":"新余棉柔巾"}]', '10:00', 'yelinkoncall', '2024-12-03 09:05:12');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(558, 'ocs.gljsc.jiashuju.scr', 'yx-D_uBXhg', '[{"materialName":"棉柔世家棉柔巾15*20","number":"420","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127085","completionRate":"79.30%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"2000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127084","completionRate":"30.80%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127083","completionRate":"89.88%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"2500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127082","completionRate":"69.48%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"3500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127081","completionRate":"18.63%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"4500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127080","completionRate":"96.78%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"10000","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127079","completionRate":"14.99%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1300","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127078","completionRate":"1.24%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1200","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127077","completionRate":"83.60%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"9500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127076","completionRate":"39.37%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"8500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127075","completionRate":"60.43%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1600","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127074","completionRate":"28.70%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1400","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127073","completionRate":"86.95%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1750","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127072","completionRate":"77.66%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"7500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127071","completionRate":"15.20%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"6500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127070","completionRate":"29.26%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"5500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127069","completionRate":"15.65%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"20000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127068","completionRate":"71.18%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127067","completionRate":"68.49%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"800","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127066","completionRate":"11.60%","customerName":"新余棉柔巾"}]', '8:30', 'yelinkoncall', '2024-12-03 09:05:12');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(559, 'ocs.gljsc.jiashuju.scr', 'yx-D_uBXhg', '[{"materialName":"棉柔世家棉柔巾15*20","number":"420","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127085","completionRate":"79.80%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"2000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127084","completionRate":"31.17%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127083","completionRate":"90.59%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"2500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127082","completionRate":"70.23%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"3500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127081","completionRate":"18.67%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"4500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127080","completionRate":"97.00%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"10000","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127079","completionRate":"15.13%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1300","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127078","completionRate":"1.56%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1200","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127077","completionRate":"83.71%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"9500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127076","completionRate":"39.99%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"8500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127075","completionRate":"61.01%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1600","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127074","completionRate":"28.75%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1400","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127073","completionRate":"87.12%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1750","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127072","completionRate":"78.38%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"7500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127071","completionRate":"16.09%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"6500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127070","completionRate":"30.03%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"5500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127069","completionRate":"15.90%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"20000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127068","completionRate":"71.66%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127067","completionRate":"69.29%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"800","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127066","completionRate":"11.92%","customerName":"新余棉柔巾"}]', '9:00', 'yelinkoncall', '2024-12-03 09:05:12');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(560, 'ocs.gljsc.jiashuju.scr', 'yx-D_uBXhg', '[{"materialName":"棉柔世家棉柔巾15*20","number":"420","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127085","completionRate":"80.54%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"2000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127084","completionRate":"31.31%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127083","completionRate":"91.02%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"2500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127082","completionRate":"70.70%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"3500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127081","completionRate":"18.69%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"4500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127080","completionRate":"97.30%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"10000","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127079","completionRate":"15.58%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1300","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127078","completionRate":"2.31%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1200","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127077","completionRate":"84.52%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"9500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127076","completionRate":"40.19%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"8500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127075","completionRate":"61.07%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"1600","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127074","completionRate":"29.71%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1400","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127073","completionRate":"87.85%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"1750","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127072","completionRate":"78.39%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"7500","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127071","completionRate":"16.88%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家棉柔巾15*20","number":"6500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ01600","orderCode":"MRJ20241127070","completionRate":"30.84%","customerName":"南昌棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"5500","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127069","completionRate":"16.68%","customerName":"新余棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"20000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127068","completionRate":"71.96%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家婴儿手口湿巾","number":"1000","deliverTime":"2024-12-30 00:00:00","materialCode":"SJ00260","orderCode":"MRJ20241127067","completionRate":"70.13%","customerName":"杭州棉柔巾"},{"materialName":"棉柔世家洗脸柔巾","number":"800","deliverTime":"2024-12-30 00:00:00","materialCode":"MRJ00900","orderCode":"MRJ20241127066","completionRate":"12.31%","customerName":"新余棉柔巾"}]', '9:30', 'yelinkoncall', '2024-12-03 09:05:12');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(561, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.3%","passRateGrowth":"0.5%","passRateUpOrDown":"up","qualifiedRate":"98.1%","qualifiedRateGrowth":"0.2%","qualifiedRateUpOrDown":"up"}', '10:00', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(562, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.6%","passRateGrowth":"0.3%","passRateUpOrDown":"up","qualifiedRate":"98.3%","qualifiedRateGrowth":"0.2%","qualifiedRateUpOrDown":"up"}', '10:10', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(563, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.7%","passRateGrowth":"0.1%","passRateUpOrDown":"up","qualifiedRate":"97.9%","qualifiedRateGrowth":"0.4%","qualifiedRateUpOrDown":"down"}', '10:20', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(564, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.9%","passRateGrowth":"0.2%","passRateUpOrDown":"up","qualifiedRate":"97.7%","qualifiedRateGrowth":"0.2%","qualifiedRateUpOrDown":"down"}', '10:30', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(565, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"99.3%","passRateGrowth":"0.4%","passRateUpOrDown":"up","qualifiedRate":"97.4%","qualifiedRateGrowth":"0.3%","qualifiedRateUpOrDown":"down"}', '10:40', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(566, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"99.0%","passRateGrowth":"0.3%","passRateUpOrDown":"down","qualifiedRate":"97.3%","qualifiedRateGrowth":"0.1%","qualifiedRateUpOrDown":"down"}', '10:50', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(567, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"99.5%","passRateGrowth":"0.5%","passRateUpOrDown":"up","qualifiedRate":"97.7%","qualifiedRateGrowth":"0.4%","qualifiedRateUpOrDown":"up"}', '11:00', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(568, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"99.3%","passRateGrowth":"0.2%","passRateUpOrDown":"down","qualifiedRate":"97.7%","qualifiedRateGrowth":"0.0%","qualifiedRateUpOrDown":"down"}', '11:10', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(569, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"99.0%","passRateGrowth":"0.3%","passRateUpOrDown":"down","qualifiedRate":"97.2%","qualifiedRateGrowth":"0.5%","qualifiedRateUpOrDown":"down"}', '11:20', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(570, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.8%","passRateGrowth":"0.2%","passRateUpOrDown":"down","qualifiedRate":"97.6%","qualifiedRateGrowth":"0.4%","qualifiedRateUpOrDown":"up"}', '11:30', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(571, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"99.0%","passRateGrowth":"0.2%","passRateUpOrDown":"up","qualifiedRate":"97.4%","qualifiedRateGrowth":"0.2%","qualifiedRateUpOrDown":"down"}', '11:40', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(572, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.0%","passRateGrowth":"1.0%","passRateUpOrDown":"up","qualifiedRate":"97.0%","qualifiedRateGrowth":"1.0%","qualifiedRateUpOrDown":"down"}', '8:00', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(573, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"97.9%","passRateGrowth":"0.1%","passRateUpOrDown":"down","qualifiedRate":"96.8%","qualifiedRateGrowth":"0.2%","qualifiedRateUpOrDown":"down"}', '8:10', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(574, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.3%","passRateGrowth":"0.4%","passRateUpOrDown":"up","qualifiedRate":"97.1%","qualifiedRateGrowth":"0.3%","qualifiedRateUpOrDown":"up"}', '8:20', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(575, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.1%","passRateGrowth":"0.2%","passRateUpOrDown":"down","qualifiedRate":"97.2%","qualifiedRateGrowth":"0.1%","qualifiedRateUpOrDown":"up"}', '8:30', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(576, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"97.7%","passRateGrowth":"0.4%","passRateUpOrDown":"down","qualifiedRate":"97.6%","qualifiedRateGrowth":"0.4%","qualifiedRateUpOrDown":"up"}', '8:40', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(577, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.2%","passRateGrowth":"0.5%","passRateUpOrDown":"up","qualifiedRate":"97.5%","qualifiedRateGrowth":"0.1%","qualifiedRateUpOrDown":"down"}', '8:50', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(578, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.1%","passRateGrowth":"0.1%","passRateUpOrDown":"down","qualifiedRate":"97.5%","qualifiedRateGrowth":"0.0%","qualifiedRateUpOrDown":"down"}', '9:00', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(579, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"97.7%","passRateGrowth":"0.4%","passRateUpOrDown":"down","qualifiedRate":"97.8%","qualifiedRateGrowth":"0.3%","qualifiedRateUpOrDown":"up"}', '9:10', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(580, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.2%","passRateGrowth":"0.5%","passRateUpOrDown":"up","qualifiedRate":"97.5%","qualifiedRateGrowth":"0.3%","qualifiedRateUpOrDown":"down"}', '9:20', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(581, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"98.1%","passRateGrowth":"0.1%","passRateUpOrDown":"down","qualifiedRate":"97.5%","qualifiedRateGrowth":"0.0%","qualifiedRateUpOrDown":"down"}', '9:30', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(582, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"97.6%","passRateGrowth":"0.5%","passRateUpOrDown":"down","qualifiedRate":"97.6%","qualifiedRateGrowth":"0.1%","qualifiedRateUpOrDown":"up"}', '9:40', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(583, 'ocs.gljsc.jiashuju.scr', 'GOvTFOj5tl', '{"passRate":"97.8%","passRateGrowth":"0.2%","passRateUpOrDown":"up","qualifiedRate":"97.9%","qualifiedRateGrowth":"0.3%","qualifiedRateUpOrDown":"up"}', '9:50', 'yelinkoncall', '2024-12-03 09:05:31');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(796, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"121分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"121分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"121分","stateName":"运行中","deviceName":"干巾三线"}]', '10:00', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(797, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"122分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"122分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"122分","stateName":"运行中","deviceName":"干巾三线"}]', '10:01', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(798, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"123分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"123分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"123分","stateName":"运行中","deviceName":"干巾三线"}]', '10:02', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(799, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"124分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"124分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"124分","stateName":"运行中","deviceName":"干巾三线"}]', '10:03', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(800, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"125分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"125分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"125分","stateName":"运行中","deviceName":"干巾三线"}]', '10:04', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(801, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"126分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"126分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"126分","stateName":"运行中","deviceName":"干巾三线"}]', '10:05', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(802, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"127分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"127分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"127分","stateName":"运行中","deviceName":"干巾三线"}]', '10:06', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(803, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"128分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"128分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"128分","stateName":"运行中","deviceName":"干巾三线"}]', '10:07', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(804, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"129分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"129分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"129分","stateName":"运行中","deviceName":"干巾三线"}]', '10:08', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(805, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"130分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"130分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"130分","stateName":"运行中","deviceName":"干巾三线"}]', '10:09', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(806, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"131分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"131分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"131分","stateName":"运行中","deviceName":"干巾三线"}]', '10:10', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(807, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"132分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"132分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"132分","stateName":"运行中","deviceName":"干巾三线"}]', '10:11', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(808, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"133分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"133分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"133分","stateName":"运行中","deviceName":"干巾三线"}]', '10:12', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(809, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"134分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"134分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"134分","stateName":"运行中","deviceName":"干巾三线"}]', '10:13', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(810, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"135分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"135分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"135分","stateName":"运行中","deviceName":"干巾三线"}]', '10:14', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(811, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"136分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"136分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"136分","stateName":"运行中","deviceName":"干巾三线"}]', '10:15', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(812, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"137分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"137分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"137分","stateName":"运行中","deviceName":"干巾三线"}]', '10:16', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(813, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"138分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"138分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"138分","stateName":"运行中","deviceName":"干巾三线"}]', '10:17', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(814, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"139分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"139分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"139分","stateName":"运行中","deviceName":"干巾三线"}]', '10:18', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(815, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"140分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"140分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"140分","stateName":"运行中","deviceName":"干巾三线"}]', '10:19', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(816, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"141分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"141分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"141分","stateName":"运行中","deviceName":"干巾三线"}]', '10:20', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(817, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"142分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"142分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"142分","stateName":"运行中","deviceName":"干巾三线"}]', '10:21', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(818, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"143分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"143分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"143分","stateName":"运行中","deviceName":"干巾三线"}]', '10:22', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(819, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"144分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"144分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"144分","stateName":"运行中","deviceName":"干巾三线"}]', '10:23', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(820, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"145分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"145分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"145分","stateName":"运行中","deviceName":"干巾三线"}]', '10:24', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(821, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"146分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"146分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"146分","stateName":"运行中","deviceName":"干巾三线"}]', '10:25', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(822, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"147分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"147分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"147分","stateName":"运行中","deviceName":"干巾三线"}]', '10:26', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(823, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"148分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"148分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"148分","stateName":"运行中","deviceName":"干巾三线"}]', '10:27', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(824, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"149分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"149分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"149分","stateName":"运行中","deviceName":"干巾三线"}]', '10:28', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(825, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"150分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"150分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"150分","stateName":"运行中","deviceName":"干巾三线"}]', '10:29', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(826, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"151分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"151分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"151分","stateName":"运行中","deviceName":"干巾三线"}]', '10:30', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(827, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"152分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"152分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"152分","stateName":"运行中","deviceName":"干巾三线"}]', '10:31', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(828, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"153分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"153分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"153分","stateName":"运行中","deviceName":"干巾三线"}]', '10:32', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(829, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"154分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"154分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"154分","stateName":"运行中","deviceName":"干巾三线"}]', '10:33', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(830, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"155分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"155分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"155分","stateName":"运行中","deviceName":"干巾三线"}]', '10:34', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(831, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"156分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"156分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"156分","stateName":"运行中","deviceName":"干巾三线"}]', '10:35', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(832, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"157分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"157分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"157分","stateName":"运行中","deviceName":"干巾三线"}]', '10:36', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(833, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"158分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"158分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"158分","stateName":"运行中","deviceName":"干巾三线"}]', '10:37', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(834, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"159分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"159分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"159分","stateName":"运行中","deviceName":"干巾三线"}]', '10:38', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(835, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"160分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"160分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"160分","stateName":"运行中","deviceName":"干巾三线"}]', '10:39', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(836, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"161分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"161分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"161分","stateName":"运行中","deviceName":"干巾三线"}]', '10:40', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(837, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"162分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"162分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"162分","stateName":"运行中","deviceName":"干巾三线"}]', '10:41', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(838, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"163分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"163分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"163分","stateName":"运行中","deviceName":"干巾三线"}]', '10:42', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(839, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"164分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"164分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"164分","stateName":"运行中","deviceName":"干巾三线"}]', '10:43', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(840, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"165分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"165分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"165分","stateName":"运行中","deviceName":"干巾三线"}]', '10:44', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(841, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"166分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"166分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"166分","stateName":"运行中","deviceName":"干巾三线"}]', '10:45', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(842, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"167分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"167分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"167分","stateName":"运行中","deviceName":"干巾三线"}]', '10:46', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(843, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"168分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"168分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"168分","stateName":"运行中","deviceName":"干巾三线"}]', '10:47', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(844, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"169分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"169分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"169分","stateName":"运行中","deviceName":"干巾三线"}]', '10:48', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(845, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"170分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"170分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"170分","stateName":"运行中","deviceName":"干巾三线"}]', '10:49', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(846, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"171分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"171分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"171分","stateName":"运行中","deviceName":"干巾三线"}]', '10:50', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(847, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"172分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"172分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"172分","stateName":"运行中","deviceName":"干巾三线"}]', '10:51', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(848, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"173分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"173分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"173分","stateName":"运行中","deviceName":"干巾三线"}]', '10:52', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(849, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"174分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"174分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"174分","stateName":"运行中","deviceName":"干巾三线"}]', '10:53', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(850, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"175分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"175分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"175分","stateName":"运行中","deviceName":"干巾三线"}]', '10:54', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(851, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"176分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"176分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"176分","stateName":"运行中","deviceName":"干巾三线"}]', '10:55', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(852, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"177分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"177分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"177分","stateName":"运行中","deviceName":"干巾三线"}]', '10:56', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(853, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"178分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"178分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"178分","stateName":"运行中","deviceName":"干巾三线"}]', '10:57', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(854, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"179分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"179分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"179分","stateName":"运行中","deviceName":"干巾三线"}]', '10:58', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(855, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"180分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"180分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"180分","stateName":"运行中","deviceName":"干巾三线"}]', '10:59', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(856, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"181分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"181分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"181分","stateName":"运行中","deviceName":"干巾三线"}]', '11:00', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(857, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"182分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"182分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"182分","stateName":"运行中","deviceName":"干巾三线"}]', '11:01', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(858, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"183分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"183分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"183分","stateName":"运行中","deviceName":"干巾三线"}]', '11:02', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(859, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"184分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"184分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"184分","stateName":"运行中","deviceName":"干巾三线"}]', '11:03', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(860, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"185分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"185分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"185分","stateName":"运行中","deviceName":"干巾三线"}]', '11:04', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(861, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"186分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"186分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"186分","stateName":"运行中","deviceName":"干巾三线"}]', '11:05', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(862, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"187分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"187分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"187分","stateName":"运行中","deviceName":"干巾三线"}]', '11:06', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(863, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"188分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"188分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"188分","stateName":"运行中","deviceName":"干巾三线"}]', '11:07', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(864, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"189分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"189分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"189分","stateName":"运行中","deviceName":"干巾三线"}]', '11:08', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(865, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"190分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"190分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"190分","stateName":"运行中","deviceName":"干巾三线"}]', '11:09', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(866, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"191分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"191分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"191分","stateName":"运行中","deviceName":"干巾三线"}]', '11:10', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(867, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"192分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"192分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"192分","stateName":"运行中","deviceName":"干巾三线"}]', '11:11', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(868, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"193分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"193分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"193分","stateName":"运行中","deviceName":"干巾三线"}]', '11:12', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(869, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"194分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"194分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"194分","stateName":"运行中","deviceName":"干巾三线"}]', '11:13', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(870, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"195分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"195分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"195分","stateName":"运行中","deviceName":"干巾三线"}]', '11:14', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(871, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"196分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"196分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"196分","stateName":"运行中","deviceName":"干巾三线"}]', '11:15', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(872, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"197分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"197分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"197分","stateName":"运行中","deviceName":"干巾三线"}]', '11:16', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(873, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"198分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"198分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"198分","stateName":"运行中","deviceName":"干巾三线"}]', '11:17', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(874, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"199分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"199分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"199分","stateName":"运行中","deviceName":"干巾三线"}]', '11:18', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(875, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"200分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"200分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"200分","stateName":"运行中","deviceName":"干巾三线"}]', '11:19', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(876, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"201分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"201分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"201分","stateName":"运行中","deviceName":"干巾三线"}]', '11:20', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(877, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"202分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"202分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"202分","stateName":"运行中","deviceName":"干巾三线"}]', '11:21', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(878, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"203分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"203分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"203分","stateName":"运行中","deviceName":"干巾三线"}]', '11:22', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(879, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"204分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"204分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"204分","stateName":"运行中","deviceName":"干巾三线"}]', '11:23', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(880, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"205分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"205分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"205分","stateName":"运行中","deviceName":"干巾三线"}]', '11:24', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(881, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"206分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"206分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"206分","stateName":"运行中","deviceName":"干巾三线"}]', '11:25', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(882, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"207分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"207分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"207分","stateName":"运行中","deviceName":"干巾三线"}]', '11:26', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(883, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"208分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"208分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"208分","stateName":"运行中","deviceName":"干巾三线"}]', '11:27', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(884, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"209分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"209分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"209分","stateName":"运行中","deviceName":"干巾三线"}]', '11:28', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(885, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"210分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"210分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"210分","stateName":"运行中","deviceName":"干巾三线"}]', '11:29', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(886, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"211分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"211分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"211分","stateName":"运行中","deviceName":"干巾三线"}]', '11:30', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(887, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"212分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"212分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"212分","stateName":"运行中","deviceName":"干巾三线"}]', '11:31', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(888, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"1分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"1分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"1分","stateName":"运行中","deviceName":"干巾三线"}]', '8:00', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(889, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"2分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"2分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"2分","stateName":"运行中","deviceName":"干巾三线"}]', '8:01', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(890, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"3分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"3分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"3分","stateName":"运行中","deviceName":"干巾三线"}]', '8:02', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(891, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"4分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"4分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"4分","stateName":"运行中","deviceName":"干巾三线"}]', '8:03', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(892, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"5分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"5分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"5分","stateName":"运行中","deviceName":"干巾三线"}]', '8:04', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(893, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"6分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"6分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"6分","stateName":"运行中","deviceName":"干巾三线"}]', '8:05', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(894, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"7分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"7分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"7分","stateName":"运行中","deviceName":"干巾三线"}]', '8:06', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(895, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"8分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"8分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"8分","stateName":"运行中","deviceName":"干巾三线"}]', '8:07', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(896, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"9分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"9分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"9分","stateName":"运行中","deviceName":"干巾三线"}]', '8:08', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(897, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"10分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"10分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"10分","stateName":"运行中","deviceName":"干巾三线"}]', '8:09', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(898, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"11分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"11分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"11分","stateName":"运行中","deviceName":"干巾三线"}]', '8:10', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(899, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"12分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"12分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"12分","stateName":"运行中","deviceName":"干巾三线"}]', '8:11', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(900, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"13分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"13分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"13分","stateName":"运行中","deviceName":"干巾三线"}]', '8:12', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(901, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"14分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"14分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"14分","stateName":"运行中","deviceName":"干巾三线"}]', '8:13', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(902, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"15分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"15分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"15分","stateName":"运行中","deviceName":"干巾三线"}]', '8:14', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(903, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"16分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"16分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"16分","stateName":"运行中","deviceName":"干巾三线"}]', '8:15', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(904, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"17分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"17分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"17分","stateName":"运行中","deviceName":"干巾三线"}]', '8:16', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(905, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"18分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"18分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"18分","stateName":"运行中","deviceName":"干巾三线"}]', '8:17', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(906, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"19分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"19分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"19分","stateName":"运行中","deviceName":"干巾三线"}]', '8:18', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(907, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"20分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"20分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"20分","stateName":"运行中","deviceName":"干巾三线"}]', '8:19', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(908, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"21分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"21分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"21分","stateName":"运行中","deviceName":"干巾三线"}]', '8:20', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(909, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"22分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"22分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"22分","stateName":"运行中","deviceName":"干巾三线"}]', '8:21', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(910, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"23分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"23分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"23分","stateName":"运行中","deviceName":"干巾三线"}]', '8:22', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(911, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"24分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"24分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"24分","stateName":"运行中","deviceName":"干巾三线"}]', '8:23', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(912, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"25分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"25分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"25分","stateName":"运行中","deviceName":"干巾三线"}]', '8:24', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(913, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"26分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"26分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"26分","stateName":"运行中","deviceName":"干巾三线"}]', '8:25', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(914, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"27分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"27分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"27分","stateName":"运行中","deviceName":"干巾三线"}]', '8:26', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(915, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"28分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"28分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"28分","stateName":"运行中","deviceName":"干巾三线"}]', '8:27', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(916, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"29分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"29分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"29分","stateName":"运行中","deviceName":"干巾三线"}]', '8:28', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(917, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"30分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"30分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"30分","stateName":"运行中","deviceName":"干巾三线"}]', '8:29', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(918, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"31分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"31分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"31分","stateName":"运行中","deviceName":"干巾三线"}]', '8:30', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(919, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"32分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"32分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"32分","stateName":"运行中","deviceName":"干巾三线"}]', '8:31', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(920, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"33分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"33分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"33分","stateName":"运行中","deviceName":"干巾三线"}]', '8:32', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(921, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"34分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"34分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"34分","stateName":"运行中","deviceName":"干巾三线"}]', '8:33', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(922, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"35分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"35分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"35分","stateName":"运行中","deviceName":"干巾三线"}]', '8:34', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(923, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"36分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"36分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"36分","stateName":"运行中","deviceName":"干巾三线"}]', '8:35', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(924, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"37分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"37分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"37分","stateName":"运行中","deviceName":"干巾三线"}]', '8:36', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(925, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"38分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"38分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"38分","stateName":"运行中","deviceName":"干巾三线"}]', '8:37', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(926, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"39分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"39分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"39分","stateName":"运行中","deviceName":"干巾三线"}]', '8:38', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(927, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"40分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"40分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"40分","stateName":"运行中","deviceName":"干巾三线"}]', '8:39', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(928, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"41分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"41分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"41分","stateName":"运行中","deviceName":"干巾三线"}]', '8:40', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(929, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"42分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"42分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"42分","stateName":"运行中","deviceName":"干巾三线"}]', '8:41', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(930, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"43分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"43分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"43分","stateName":"运行中","deviceName":"干巾三线"}]', '8:42', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(931, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"44分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"44分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"44分","stateName":"运行中","deviceName":"干巾三线"}]', '8:43', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(932, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"45分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"45分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"45分","stateName":"运行中","deviceName":"干巾三线"}]', '8:44', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(933, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"46分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"46分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"46分","stateName":"运行中","deviceName":"干巾三线"}]', '8:45', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(934, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"47分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"47分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"47分","stateName":"运行中","deviceName":"干巾三线"}]', '8:46', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(935, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"48分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"48分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"48分","stateName":"运行中","deviceName":"干巾三线"}]', '8:47', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(936, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"49分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"49分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"49分","stateName":"运行中","deviceName":"干巾三线"}]', '8:48', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(937, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"50分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"50分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"50分","stateName":"运行中","deviceName":"干巾三线"}]', '8:49', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(938, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"51分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"51分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"51分","stateName":"运行中","deviceName":"干巾三线"}]', '8:50', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(939, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"52分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"52分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"52分","stateName":"运行中","deviceName":"干巾三线"}]', '8:51', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(940, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"53分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"53分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"53分","stateName":"运行中","deviceName":"干巾三线"}]', '8:52', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(941, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"54分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"54分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"54分","stateName":"运行中","deviceName":"干巾三线"}]', '8:53', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(942, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"55分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"55分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"55分","stateName":"运行中","deviceName":"干巾三线"}]', '8:54', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(943, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"56分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"56分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"56分","stateName":"运行中","deviceName":"干巾三线"}]', '8:55', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(944, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"57分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"57分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"57分","stateName":"运行中","deviceName":"干巾三线"}]', '8:56', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(945, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"58分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"58分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"58分","stateName":"运行中","deviceName":"干巾三线"}]', '8:57', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(946, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"59分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"59分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"59分","stateName":"运行中","deviceName":"干巾三线"}]', '8:58', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(947, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"60分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"60分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"60分","stateName":"运行中","deviceName":"干巾三线"}]', '8:59', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(948, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"61分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"61分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"61分","stateName":"运行中","deviceName":"干巾三线"}]', '9:00', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(949, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"62分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"62分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"62分","stateName":"运行中","deviceName":"干巾三线"}]', '9:01', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(950, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"63分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"63分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"63分","stateName":"运行中","deviceName":"干巾三线"}]', '9:02', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(951, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"64分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"64分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"64分","stateName":"运行中","deviceName":"干巾三线"}]', '9:03', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(952, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"65分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"65分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"65分","stateName":"运行中","deviceName":"干巾三线"}]', '9:04', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(953, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"66分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"66分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"66分","stateName":"运行中","deviceName":"干巾三线"}]', '9:05', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(954, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"67分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"67分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"67分","stateName":"运行中","deviceName":"干巾三线"}]', '9:06', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(955, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"68分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"68分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"68分","stateName":"运行中","deviceName":"干巾三线"}]', '9:07', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(956, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"69分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"69分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"69分","stateName":"运行中","deviceName":"干巾三线"}]', '9:08', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(957, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"70分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"70分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"70分","stateName":"运行中","deviceName":"干巾三线"}]', '9:09', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(958, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"71分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"71分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"71分","stateName":"运行中","deviceName":"干巾三线"}]', '9:10', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(959, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"72分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"72分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"72分","stateName":"运行中","deviceName":"干巾三线"}]', '9:11', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(960, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"73分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"73分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"73分","stateName":"运行中","deviceName":"干巾三线"}]', '9:12', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(961, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"74分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"74分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"74分","stateName":"运行中","deviceName":"干巾三线"}]', '9:13', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(962, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"75分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"75分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"75分","stateName":"运行中","deviceName":"干巾三线"}]', '9:14', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(963, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"76分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"76分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"76分","stateName":"运行中","deviceName":"干巾三线"}]', '9:15', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(964, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"77分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"77分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"77分","stateName":"运行中","deviceName":"干巾三线"}]', '9:16', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(965, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"78分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"78分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"78分","stateName":"运行中","deviceName":"干巾三线"}]', '9:17', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(966, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"79分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"79分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"79分","stateName":"运行中","deviceName":"干巾三线"}]', '9:18', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(967, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"80分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"80分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"80分","stateName":"运行中","deviceName":"干巾三线"}]', '9:19', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(968, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"81分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"81分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"81分","stateName":"运行中","deviceName":"干巾三线"}]', '9:20', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(969, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"82分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"82分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"82分","stateName":"运行中","deviceName":"干巾三线"}]', '9:21', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(970, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"83分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"83分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"83分","stateName":"运行中","deviceName":"干巾三线"}]', '9:22', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(971, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"84分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"84分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"84分","stateName":"运行中","deviceName":"干巾三线"}]', '9:23', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(972, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"85分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"85分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"85分","stateName":"运行中","deviceName":"干巾三线"}]', '9:24', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(973, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"86分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"86分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"86分","stateName":"运行中","deviceName":"干巾三线"}]', '9:25', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(974, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"87分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"87分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"87分","stateName":"运行中","deviceName":"干巾三线"}]', '9:26', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(975, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"88分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"88分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"88分","stateName":"运行中","deviceName":"干巾三线"}]', '9:27', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(976, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"89分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"89分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"89分","stateName":"运行中","deviceName":"干巾三线"}]', '9:28', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(977, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"90分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"90分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"90分","stateName":"运行中","deviceName":"干巾三线"}]', '9:29', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(978, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"91分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"91分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"91分","stateName":"运行中","deviceName":"干巾三线"}]', '9:30', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(979, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"92分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"92分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"92分","stateName":"运行中","deviceName":"干巾三线"}]', '9:31', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(980, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"93分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"93分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"93分","stateName":"运行中","deviceName":"干巾三线"}]', '9:32', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(981, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"94分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"94分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"94分","stateName":"运行中","deviceName":"干巾三线"}]', '9:33', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(982, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"95分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"95分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"95分","stateName":"运行中","deviceName":"干巾三线"}]', '9:34', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(983, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"96分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"96分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"96分","stateName":"运行中","deviceName":"干巾三线"}]', '9:35', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(984, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"97分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"97分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"97分","stateName":"运行中","deviceName":"干巾三线"}]', '9:36', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(985, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"98分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"98分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"98分","stateName":"运行中","deviceName":"干巾三线"}]', '9:37', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(986, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"99分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"99分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"99分","stateName":"运行中","deviceName":"干巾三线"}]', '9:38', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(987, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"100分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"100分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"100分","stateName":"运行中","deviceName":"干巾三线"}]', '9:39', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(988, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"101分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"101分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"101分","stateName":"运行中","deviceName":"干巾三线"}]', '9:40', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(989, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"102分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"102分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"102分","stateName":"运行中","deviceName":"干巾三线"}]', '9:41', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(990, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"103分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"103分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"103分","stateName":"运行中","deviceName":"干巾三线"}]', '9:42', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(991, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"104分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"104分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"104分","stateName":"运行中","deviceName":"干巾三线"}]', '9:43', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(992, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"105分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"105分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"105分","stateName":"运行中","deviceName":"干巾三线"}]', '9:44', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(993, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"106分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"106分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"106分","stateName":"运行中","deviceName":"干巾三线"}]', '9:45', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(994, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"107分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"107分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"107分","stateName":"运行中","deviceName":"干巾三线"}]', '9:46', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(995, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"108分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"108分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"108分","stateName":"运行中","deviceName":"干巾三线"}]', '9:47', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(996, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"109分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"109分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"109分","stateName":"运行中","deviceName":"干巾三线"}]', '9:48', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(997, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"110分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"110分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"110分","stateName":"运行中","deviceName":"干巾三线"}]', '9:49', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(998, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"111分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"111分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"111分","stateName":"运行中","deviceName":"干巾三线"}]', '9:50', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(999, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"112分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"112分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"112分","stateName":"运行中","deviceName":"干巾三线"}]', '9:51', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1000, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"113分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"113分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"113分","stateName":"运行中","deviceName":"干巾三线"}]', '9:52', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1001, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"114分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"114分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"114分","stateName":"运行中","deviceName":"干巾三线"}]', '9:53', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1002, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"115分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"115分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"115分","stateName":"运行中","deviceName":"干巾三线"}]', '9:54', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1003, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"116分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"116分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"116分","stateName":"运行中","deviceName":"干巾三线"}]', '9:55', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1004, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"117分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"117分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"117分","stateName":"运行中","deviceName":"干巾三线"}]', '9:56', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1005, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"118分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"118分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"118分","stateName":"运行中","deviceName":"干巾三线"}]', '9:57', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1006, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"119分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"119分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"119分","stateName":"运行中","deviceName":"干巾三线"}]', '9:58', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1007, 'ocs.gljsc.jiashuju.scr', 'vgHz63Wsc2', '[{"inStateTime":"120分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"120分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"120分","stateName":"运行中","deviceName":"干巾三线"}]', '9:59', 'yelinkoncall', '2024-12-03 09:06:05');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1010, 'ocs.gljsc.jiashuju.scr', 'Bn7zOnptqO', '{"monthAverageCapacityUtilizationName":"68%","monthAverageCapacityUtilization":null,"monthAverageCapacityUtilizationCompareData":null,"monthAverageCapacityUtilizationCompareDataName":"3%","monthAverageCapacityUtilizationCompare":"down","yearAverageCapacityUtilizationName":"70%","yearAverageCapacityUtilization":null,"yearAverageCapacityUtilizationCompareData":null,"yearAverageCapacityUtilizationCompareDataName":"10%","yearAverageCapacityUtilizationCompare":"up","list":[{"month":"1","capacityUtilization":"68"},{"month":"2","capacityUtilization":"70"},{"month":"3","capacityUtilization":"68"},{"month":"4","capacityUtilization":"66"},{"month":"5","capacityUtilization":"72"},{"month":"6","capacityUtilization":"66"},{"month":"7","capacityUtilization":"70"},{"month":"8","capacityUtilization":"67"},{"month":"9","capacityUtilization":"65"},{"month":"10","capacityUtilization":"64"},{"month":"11","capacityUtilization":"69"},{"month":"12","capacityUtilization":"66"}]}', '0', 'yelinkoncall', '2024-12-03 09:11:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1011, 'ocs.gljsc.jiashuju.scr', 'J4EzFHWDwK', '[{"dateStr":"2024-01-01","passRate":"98"},{"dateStr":"2024-02-01","passRate":"98"},{"dateStr":"2024-03-01","passRate":"98"},{"dateStr":"2023-04-01","passRate":"98"},{"dateStr":"2023-05-01","passRate":"96"},{"dateStr":"2023-06-01","passRate":"98"},{"dateStr":"2023-07-01","passRate":"97"},{"dateStr":"2023-08-01","passRate":"97"},{"dateStr":"2023-09-01","passRate":"96"},{"dateStr":"2023-10-01","passRate":"98"},{"dateStr":"2023-11-01","passRate":"96"},{"dateStr":"2023-12-01","passRate":"96"}]', '0:00:00', 'yelinkoncall', '2024-12-03 09:11:47');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1012, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"121分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"121分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"121分","stateName":"运行中","deviceName":"干巾三线"}]', '10:00', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1013, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"122分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"122分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"122分","stateName":"运行中","deviceName":"干巾三线"}]', '10:01', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1014, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"123分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"123分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"123分","stateName":"运行中","deviceName":"干巾三线"}]', '10:02', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1015, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"124分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"124分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"124分","stateName":"运行中","deviceName":"干巾三线"}]', '10:03', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1016, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"125分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"125分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"125分","stateName":"运行中","deviceName":"干巾三线"}]', '10:04', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1017, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"126分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"126分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"126分","stateName":"运行中","deviceName":"干巾三线"}]', '10:05', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1018, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"127分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"127分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"127分","stateName":"运行中","deviceName":"干巾三线"}]', '10:06', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1019, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"128分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"128分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"128分","stateName":"运行中","deviceName":"干巾三线"}]', '10:07', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1020, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"129分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"129分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"129分","stateName":"运行中","deviceName":"干巾三线"}]', '10:08', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1021, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"130分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"130分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"130分","stateName":"运行中","deviceName":"干巾三线"}]', '10:09', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1022, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"131分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"131分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"131分","stateName":"运行中","deviceName":"干巾三线"}]', '10:10', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1023, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"132分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"132分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"132分","stateName":"运行中","deviceName":"干巾三线"}]', '10:11', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1024, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"133分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"133分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"133分","stateName":"运行中","deviceName":"干巾三线"}]', '10:12', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1025, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"134分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"134分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"134分","stateName":"运行中","deviceName":"干巾三线"}]', '10:13', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1026, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"135分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"135分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"135分","stateName":"运行中","deviceName":"干巾三线"}]', '10:14', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1027, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"136分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"136分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"136分","stateName":"运行中","deviceName":"干巾三线"}]', '10:15', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1028, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"137分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"137分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"137分","stateName":"运行中","deviceName":"干巾三线"}]', '10:16', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1029, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"138分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"138分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"138分","stateName":"运行中","deviceName":"干巾三线"}]', '10:17', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1030, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"139分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"139分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"139分","stateName":"运行中","deviceName":"干巾三线"}]', '10:18', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1031, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"140分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"140分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"140分","stateName":"运行中","deviceName":"干巾三线"}]', '10:19', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1032, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"141分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"141分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"141分","stateName":"运行中","deviceName":"干巾三线"}]', '10:20', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1033, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"142分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"142分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"142分","stateName":"运行中","deviceName":"干巾三线"}]', '10:21', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1034, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"143分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"143分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"143分","stateName":"运行中","deviceName":"干巾三线"}]', '10:22', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1035, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"144分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"144分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"144分","stateName":"运行中","deviceName":"干巾三线"}]', '10:23', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1036, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"145分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"145分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"145分","stateName":"运行中","deviceName":"干巾三线"}]', '10:24', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1037, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"146分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"146分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"146分","stateName":"运行中","deviceName":"干巾三线"}]', '10:25', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1038, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"147分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"147分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"147分","stateName":"运行中","deviceName":"干巾三线"}]', '10:26', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1039, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"148分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"148分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"148分","stateName":"运行中","deviceName":"干巾三线"}]', '10:27', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1040, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"149分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"149分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"149分","stateName":"运行中","deviceName":"干巾三线"}]', '10:28', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1041, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"150分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"150分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"150分","stateName":"运行中","deviceName":"干巾三线"}]', '10:29', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1042, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"151分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"151分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"151分","stateName":"运行中","deviceName":"干巾三线"}]', '10:30', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1043, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"152分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"152分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"152分","stateName":"运行中","deviceName":"干巾三线"}]', '10:31', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1044, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"153分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"153分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"153分","stateName":"运行中","deviceName":"干巾三线"}]', '10:32', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1045, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"154分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"154分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"154分","stateName":"运行中","deviceName":"干巾三线"}]', '10:33', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1046, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"155分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"155分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"155分","stateName":"运行中","deviceName":"干巾三线"}]', '10:34', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1047, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"156分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"156分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"156分","stateName":"运行中","deviceName":"干巾三线"}]', '10:35', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1048, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"157分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"157分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"157分","stateName":"运行中","deviceName":"干巾三线"}]', '10:36', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1049, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"158分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"158分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"158分","stateName":"运行中","deviceName":"干巾三线"}]', '10:37', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1050, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"159分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"159分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"159分","stateName":"运行中","deviceName":"干巾三线"}]', '10:38', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1051, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"160分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"160分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"160分","stateName":"运行中","deviceName":"干巾三线"}]', '10:39', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1052, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"161分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"161分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"161分","stateName":"运行中","deviceName":"干巾三线"}]', '10:40', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1053, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"162分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"162分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"162分","stateName":"运行中","deviceName":"干巾三线"}]', '10:41', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1054, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"163分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"163分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"163分","stateName":"运行中","deviceName":"干巾三线"}]', '10:42', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1055, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"164分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"164分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"164分","stateName":"运行中","deviceName":"干巾三线"}]', '10:43', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1056, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"165分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"165分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"165分","stateName":"运行中","deviceName":"干巾三线"}]', '10:44', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1057, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"166分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"166分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"166分","stateName":"运行中","deviceName":"干巾三线"}]', '10:45', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1058, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"167分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"167分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"167分","stateName":"运行中","deviceName":"干巾三线"}]', '10:46', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1059, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"168分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"168分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"168分","stateName":"运行中","deviceName":"干巾三线"}]', '10:47', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1060, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"169分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"169分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"169分","stateName":"运行中","deviceName":"干巾三线"}]', '10:48', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1061, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"170分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"170分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"170分","stateName":"运行中","deviceName":"干巾三线"}]', '10:49', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1062, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"171分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"171分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"171分","stateName":"运行中","deviceName":"干巾三线"}]', '10:50', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1063, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"172分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"172分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"172分","stateName":"运行中","deviceName":"干巾三线"}]', '10:51', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1064, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"173分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"173分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"173分","stateName":"运行中","deviceName":"干巾三线"}]', '10:52', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1065, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"174分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"174分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"174分","stateName":"运行中","deviceName":"干巾三线"}]', '10:53', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1066, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"175分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"175分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"175分","stateName":"运行中","deviceName":"干巾三线"}]', '10:54', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1067, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"176分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"176分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"176分","stateName":"运行中","deviceName":"干巾三线"}]', '10:55', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1068, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"177分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"177分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"177分","stateName":"运行中","deviceName":"干巾三线"}]', '10:56', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1069, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"178分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"178分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"178分","stateName":"运行中","deviceName":"干巾三线"}]', '10:57', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1070, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"179分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"179分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"179分","stateName":"运行中","deviceName":"干巾三线"}]', '10:58', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1071, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"180分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"180分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"180分","stateName":"运行中","deviceName":"干巾三线"}]', '10:59', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1072, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"181分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"181分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"181分","stateName":"运行中","deviceName":"干巾三线"}]', '11:00', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1073, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"182分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"182分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"182分","stateName":"运行中","deviceName":"干巾三线"}]', '11:01', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1074, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"183分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"183分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"183分","stateName":"运行中","deviceName":"干巾三线"}]', '11:02', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1075, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"184分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"184分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"184分","stateName":"运行中","deviceName":"干巾三线"}]', '11:03', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1076, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"185分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"185分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"185分","stateName":"运行中","deviceName":"干巾三线"}]', '11:04', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1077, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"186分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"186分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"186分","stateName":"运行中","deviceName":"干巾三线"}]', '11:05', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1078, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"187分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"187分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"187分","stateName":"运行中","deviceName":"干巾三线"}]', '11:06', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1079, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"188分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"188分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"188分","stateName":"运行中","deviceName":"干巾三线"}]', '11:07', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1080, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"189分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"189分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"189分","stateName":"运行中","deviceName":"干巾三线"}]', '11:08', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1081, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"190分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"190分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"190分","stateName":"运行中","deviceName":"干巾三线"}]', '11:09', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1082, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"191分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"191分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"191分","stateName":"运行中","deviceName":"干巾三线"}]', '11:10', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1083, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"192分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"192分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"192分","stateName":"运行中","deviceName":"干巾三线"}]', '11:11', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1084, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"193分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"193分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"193分","stateName":"运行中","deviceName":"干巾三线"}]', '11:12', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1085, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"194分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"194分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"194分","stateName":"运行中","deviceName":"干巾三线"}]', '11:13', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1086, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"195分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"195分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"195分","stateName":"运行中","deviceName":"干巾三线"}]', '11:14', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1087, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"196分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"196分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"196分","stateName":"运行中","deviceName":"干巾三线"}]', '11:15', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1088, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"197分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"197分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"197分","stateName":"运行中","deviceName":"干巾三线"}]', '11:16', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1089, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"198分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"198分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"198分","stateName":"运行中","deviceName":"干巾三线"}]', '11:17', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1090, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"199分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"199分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"199分","stateName":"运行中","deviceName":"干巾三线"}]', '11:18', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1091, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"200分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"200分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"200分","stateName":"运行中","deviceName":"干巾三线"}]', '11:19', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1092, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"201分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"201分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"201分","stateName":"运行中","deviceName":"干巾三线"}]', '11:20', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1093, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"202分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"202分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"202分","stateName":"运行中","deviceName":"干巾三线"}]', '11:21', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1094, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"203分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"203分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"203分","stateName":"运行中","deviceName":"干巾三线"}]', '11:22', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1095, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"204分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"204分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"204分","stateName":"运行中","deviceName":"干巾三线"}]', '11:23', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1096, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"205分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"205分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"205分","stateName":"运行中","deviceName":"干巾三线"}]', '11:24', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1097, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"206分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"206分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"206分","stateName":"运行中","deviceName":"干巾三线"}]', '11:25', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1098, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"207分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"207分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"207分","stateName":"运行中","deviceName":"干巾三线"}]', '11:26', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1099, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"208分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"208分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"208分","stateName":"运行中","deviceName":"干巾三线"}]', '11:27', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1100, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"209分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"209分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"209分","stateName":"运行中","deviceName":"干巾三线"}]', '11:28', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1101, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"210分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"210分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"210分","stateName":"运行中","deviceName":"干巾三线"}]', '11:29', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1102, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"211分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"211分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"211分","stateName":"运行中","deviceName":"干巾三线"}]', '11:30', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1103, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"212分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"212分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"212分","stateName":"运行中","deviceName":"干巾三线"}]', '11:31', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1104, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"1分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"1分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"1分","stateName":"运行中","deviceName":"干巾三线"}]', '8:00', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1105, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"2分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"2分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"2分","stateName":"运行中","deviceName":"干巾三线"}]', '8:01', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1106, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"3分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"3分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"3分","stateName":"运行中","deviceName":"干巾三线"}]', '8:02', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1107, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"4分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"4分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"4分","stateName":"运行中","deviceName":"干巾三线"}]', '8:03', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1108, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"5分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"5分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"5分","stateName":"运行中","deviceName":"干巾三线"}]', '8:04', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1109, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"6分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"6分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"6分","stateName":"运行中","deviceName":"干巾三线"}]', '8:05', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1110, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"7分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"7分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"7分","stateName":"运行中","deviceName":"干巾三线"}]', '8:06', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1111, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"8分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"8分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"8分","stateName":"运行中","deviceName":"干巾三线"}]', '8:07', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1112, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"9分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"9分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"9分","stateName":"运行中","deviceName":"干巾三线"}]', '8:08', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1113, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"10分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"10分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"10分","stateName":"运行中","deviceName":"干巾三线"}]', '8:09', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1114, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"11分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"11分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"11分","stateName":"运行中","deviceName":"干巾三线"}]', '8:10', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1115, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"12分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"12分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"12分","stateName":"运行中","deviceName":"干巾三线"}]', '8:11', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1116, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"13分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"13分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"13分","stateName":"运行中","deviceName":"干巾三线"}]', '8:12', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1117, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"14分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"14分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"14分","stateName":"运行中","deviceName":"干巾三线"}]', '8:13', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1118, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"15分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"15分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"15分","stateName":"运行中","deviceName":"干巾三线"}]', '8:14', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1119, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"16分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"16分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"16分","stateName":"运行中","deviceName":"干巾三线"}]', '8:15', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1120, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"17分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"17分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"17分","stateName":"运行中","deviceName":"干巾三线"}]', '8:16', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1121, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"18分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"18分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"18分","stateName":"运行中","deviceName":"干巾三线"}]', '8:17', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1122, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"19分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"19分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"19分","stateName":"运行中","deviceName":"干巾三线"}]', '8:18', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1123, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"20分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"20分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"20分","stateName":"运行中","deviceName":"干巾三线"}]', '8:19', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1124, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"21分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"21分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"21分","stateName":"运行中","deviceName":"干巾三线"}]', '8:20', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1125, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"22分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"22分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"22分","stateName":"运行中","deviceName":"干巾三线"}]', '8:21', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1126, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"23分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"23分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"23分","stateName":"运行中","deviceName":"干巾三线"}]', '8:22', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1127, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"24分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"24分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"24分","stateName":"运行中","deviceName":"干巾三线"}]', '8:23', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1128, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"25分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"25分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"25分","stateName":"运行中","deviceName":"干巾三线"}]', '8:24', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1129, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"26分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"26分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"26分","stateName":"运行中","deviceName":"干巾三线"}]', '8:25', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1130, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"27分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"27分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"27分","stateName":"运行中","deviceName":"干巾三线"}]', '8:26', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1131, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"28分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"28分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"28分","stateName":"运行中","deviceName":"干巾三线"}]', '8:27', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1132, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"29分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"29分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"29分","stateName":"运行中","deviceName":"干巾三线"}]', '8:28', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1133, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"30分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"30分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"30分","stateName":"运行中","deviceName":"干巾三线"}]', '8:29', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1134, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"31分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"31分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"31分","stateName":"运行中","deviceName":"干巾三线"}]', '8:30', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1135, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"32分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"32分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"32分","stateName":"运行中","deviceName":"干巾三线"}]', '8:31', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1136, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"33分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"33分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"33分","stateName":"运行中","deviceName":"干巾三线"}]', '8:32', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1137, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"34分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"34分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"34分","stateName":"运行中","deviceName":"干巾三线"}]', '8:33', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1138, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"35分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"35分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"35分","stateName":"运行中","deviceName":"干巾三线"}]', '8:34', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1139, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"36分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"36分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"36分","stateName":"运行中","deviceName":"干巾三线"}]', '8:35', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1140, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"37分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"37分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"37分","stateName":"运行中","deviceName":"干巾三线"}]', '8:36', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1141, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"38分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"38分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"38分","stateName":"运行中","deviceName":"干巾三线"}]', '8:37', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1142, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"39分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"39分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"39分","stateName":"运行中","deviceName":"干巾三线"}]', '8:38', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1143, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"40分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"40分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"40分","stateName":"运行中","deviceName":"干巾三线"}]', '8:39', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1144, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"41分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"41分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"41分","stateName":"运行中","deviceName":"干巾三线"}]', '8:40', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1145, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"42分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"42分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"42分","stateName":"运行中","deviceName":"干巾三线"}]', '8:41', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1146, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"43分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"43分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"43分","stateName":"运行中","deviceName":"干巾三线"}]', '8:42', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1147, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"44分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"44分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"44分","stateName":"运行中","deviceName":"干巾三线"}]', '8:43', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1148, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"45分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"45分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"45分","stateName":"运行中","deviceName":"干巾三线"}]', '8:44', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1149, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"46分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"46分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"46分","stateName":"运行中","deviceName":"干巾三线"}]', '8:45', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1150, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"47分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"47分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"47分","stateName":"运行中","deviceName":"干巾三线"}]', '8:46', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1151, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"48分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"48分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"48分","stateName":"运行中","deviceName":"干巾三线"}]', '8:47', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1152, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"49分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"49分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"49分","stateName":"运行中","deviceName":"干巾三线"}]', '8:48', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1153, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"50分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"50分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"50分","stateName":"运行中","deviceName":"干巾三线"}]', '8:49', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1154, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"51分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"51分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"51分","stateName":"运行中","deviceName":"干巾三线"}]', '8:50', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1155, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"52分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"52分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"52分","stateName":"运行中","deviceName":"干巾三线"}]', '8:51', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1156, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"53分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"53分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"53分","stateName":"运行中","deviceName":"干巾三线"}]', '8:52', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1157, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"54分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"54分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"54分","stateName":"运行中","deviceName":"干巾三线"}]', '8:53', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1158, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"55分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"55分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"55分","stateName":"运行中","deviceName":"干巾三线"}]', '8:54', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1159, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"56分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"56分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"56分","stateName":"运行中","deviceName":"干巾三线"}]', '8:55', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1160, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"57分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"57分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"57分","stateName":"运行中","deviceName":"干巾三线"}]', '8:56', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1161, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"58分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"58分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"58分","stateName":"运行中","deviceName":"干巾三线"}]', '8:57', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1162, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"59分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"59分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"59分","stateName":"运行中","deviceName":"干巾三线"}]', '8:58', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1163, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"60分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"60分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"60分","stateName":"运行中","deviceName":"干巾三线"}]', '8:59', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1164, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"61分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"61分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"61分","stateName":"运行中","deviceName":"干巾三线"}]', '9:00', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1165, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"62分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"62分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"62分","stateName":"运行中","deviceName":"干巾三线"}]', '9:01', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1166, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"63分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"63分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"63分","stateName":"运行中","deviceName":"干巾三线"}]', '9:02', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1167, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"64分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"64分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"64分","stateName":"运行中","deviceName":"干巾三线"}]', '9:03', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1168, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"65分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"65分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"65分","stateName":"运行中","deviceName":"干巾三线"}]', '9:04', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1169, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"66分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"66分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"66分","stateName":"运行中","deviceName":"干巾三线"}]', '9:05', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1170, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"67分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"67分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"67分","stateName":"运行中","deviceName":"干巾三线"}]', '9:06', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1171, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"68分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"68分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"68分","stateName":"运行中","deviceName":"干巾三线"}]', '9:07', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1172, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"69分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"69分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"69分","stateName":"运行中","deviceName":"干巾三线"}]', '9:08', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1173, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"70分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"70分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"70分","stateName":"运行中","deviceName":"干巾三线"}]', '9:09', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1174, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"71分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"71分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"71分","stateName":"运行中","deviceName":"干巾三线"}]', '9:10', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1175, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"72分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"72分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"72分","stateName":"运行中","deviceName":"干巾三线"}]', '9:11', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1176, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"73分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"73分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"73分","stateName":"运行中","deviceName":"干巾三线"}]', '9:12', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1177, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"74分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"74分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"74分","stateName":"运行中","deviceName":"干巾三线"}]', '9:13', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1178, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"75分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"75分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"75分","stateName":"运行中","deviceName":"干巾三线"}]', '9:14', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1179, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"76分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"76分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"76分","stateName":"运行中","deviceName":"干巾三线"}]', '9:15', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1180, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"77分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"77分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"77分","stateName":"运行中","deviceName":"干巾三线"}]', '9:16', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1181, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"78分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"78分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"78分","stateName":"运行中","deviceName":"干巾三线"}]', '9:17', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1182, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"79分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"79分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"79分","stateName":"运行中","deviceName":"干巾三线"}]', '9:18', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1183, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"80分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"80分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"80分","stateName":"运行中","deviceName":"干巾三线"}]', '9:19', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1184, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"81分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"81分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"81分","stateName":"运行中","deviceName":"干巾三线"}]', '9:20', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1185, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"82分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"82分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"82分","stateName":"运行中","deviceName":"干巾三线"}]', '9:21', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1186, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"83分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"83分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"83分","stateName":"运行中","deviceName":"干巾三线"}]', '9:22', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1187, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"84分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"84分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"84分","stateName":"运行中","deviceName":"干巾三线"}]', '9:23', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1188, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"85分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"85分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"85分","stateName":"运行中","deviceName":"干巾三线"}]', '9:24', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1189, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"86分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"86分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"86分","stateName":"运行中","deviceName":"干巾三线"}]', '9:25', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1190, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"87分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"87分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"87分","stateName":"运行中","deviceName":"干巾三线"}]', '9:26', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1191, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"88分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"88分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"88分","stateName":"运行中","deviceName":"干巾三线"}]', '9:27', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1192, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"89分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"89分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"89分","stateName":"运行中","deviceName":"干巾三线"}]', '9:28', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1193, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"90分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"90分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"90分","stateName":"运行中","deviceName":"干巾三线"}]', '9:29', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1194, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"91分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"91分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"91分","stateName":"运行中","deviceName":"干巾三线"}]', '9:30', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1195, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"92分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"92分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"92分","stateName":"运行中","deviceName":"干巾三线"}]', '9:31', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1196, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"93分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"93分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"93分","stateName":"运行中","deviceName":"干巾三线"}]', '9:32', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1197, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"94分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"94分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"94分","stateName":"运行中","deviceName":"干巾三线"}]', '9:33', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1198, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"95分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"95分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"95分","stateName":"运行中","deviceName":"干巾三线"}]', '9:34', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1199, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"96分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"96分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"96分","stateName":"运行中","deviceName":"干巾三线"}]', '9:35', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1200, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"97分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"97分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"97分","stateName":"运行中","deviceName":"干巾三线"}]', '9:36', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1201, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"98分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"98分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"98分","stateName":"运行中","deviceName":"干巾三线"}]', '9:37', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1202, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"99分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"99分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"99分","stateName":"运行中","deviceName":"干巾三线"}]', '9:38', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1203, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"100分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"100分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"100分","stateName":"运行中","deviceName":"干巾三线"}]', '9:39', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1204, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"101分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"101分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"101分","stateName":"运行中","deviceName":"干巾三线"}]', '9:40', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1205, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"102分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"102分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"102分","stateName":"运行中","deviceName":"干巾三线"}]', '9:41', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1206, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"103分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"103分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"103分","stateName":"运行中","deviceName":"干巾三线"}]', '9:42', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1207, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"104分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"104分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"104分","stateName":"运行中","deviceName":"干巾三线"}]', '9:43', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1208, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"105分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"105分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"105分","stateName":"运行中","deviceName":"干巾三线"}]', '9:44', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1209, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"106分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"106分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"106分","stateName":"运行中","deviceName":"干巾三线"}]', '9:45', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1210, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"107分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"107分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"107分","stateName":"运行中","deviceName":"干巾三线"}]', '9:46', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1211, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"108分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"108分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"108分","stateName":"运行中","deviceName":"干巾三线"}]', '9:47', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1212, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"109分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"109分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"109分","stateName":"运行中","deviceName":"干巾三线"}]', '9:48', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1213, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"110分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"110分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"110分","stateName":"运行中","deviceName":"干巾三线"}]', '9:49', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1214, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"111分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"111分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"111分","stateName":"运行中","deviceName":"干巾三线"}]', '9:50', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1215, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"112分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"112分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"112分","stateName":"运行中","deviceName":"干巾三线"}]', '9:51', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1216, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"113分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"113分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"113分","stateName":"运行中","deviceName":"干巾三线"}]', '9:52', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1217, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"114分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"114分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"114分","stateName":"运行中","deviceName":"干巾三线"}]', '9:53', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1218, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"115分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"115分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"115分","stateName":"运行中","deviceName":"干巾三线"}]', '9:54', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1219, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"116分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"116分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"116分","stateName":"运行中","deviceName":"干巾三线"}]', '9:55', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1220, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"117分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"117分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"117分","stateName":"运行中","deviceName":"干巾三线"}]', '9:56', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1221, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"118分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"118分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"118分","stateName":"运行中","deviceName":"干巾三线"}]', '9:57', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1222, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"119分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"119分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"119分","stateName":"运行中","deviceName":"干巾三线"}]', '9:58', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1223, 'ocs_wisdom-screen-center', 'rO50Frm1jl', '[{"inStateTime":"120分","stateName":"运行中","deviceName":"干巾一线"},{"inStateTime":"120分","stateName":"运行中","deviceName":"干巾二线"},{"inStateTime":"120分","stateName":"运行中","deviceName":"干巾三线"}]', '9:59', 'yelinkoncall', '2024-12-03 09:17:27');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1268, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"16","yield":"25","time":"#{时间}","numOfBad":"15","directRate":"1450%"}]', '0:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1269, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"2","yield":"11","time":"#{时间}","numOfBad":"1","directRate":"50%"}]', '10:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1270, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"3","yield":"12","time":"#{时间}","numOfBad":"2","directRate":"150%"}]', '11:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1271, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"4","yield":"13","time":"#{时间}","numOfBad":"3","directRate":"250%"}]', '12:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1272, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"5","yield":"14","time":"#{时间}","numOfBad":"4","directRate":"350%"}]', '13:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1273, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"6","yield":"15","time":"#{时间}","numOfBad":"5","directRate":"450%"}]', '14:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1274, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"7","yield":"16","time":"#{时间}","numOfBad":"6","directRate":"550%"}]', '15:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1275, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"8","yield":"17","time":"#{时间}","numOfBad":"7","directRate":"650%"}]', '16:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1276, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"9","yield":"18","time":"#{时间}","numOfBad":"8","directRate":"750%"}]', '17:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1277, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"10","yield":"19","time":"#{时间}","numOfBad":"9","directRate":"850%"}]', '18:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1278, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"11","yield":"20","time":"#{时间}","numOfBad":"10","directRate":"950%"}]', '19:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1279, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"12","yield":"21","time":"#{时间}","numOfBad":"11","directRate":"1050%"}]', '20:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1280, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"13","yield":"22","time":"#{时间}","numOfBad":"12","directRate":"1150%"}]', '21:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1281, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"14","yield":"23","time":"#{时间}","numOfBad":"13","directRate":"1250%"}]', '22:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1282, 'ocs_center-Production-screen', 'sJuF47Qtrl', '[{"input":"15","yield":"24","time":"#{时间}","numOfBad":"14","directRate":"1350%"}]', '23:32:25', 'yelinkoncall', '2024-12-05 10:37:28');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1283, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00015","materialName":"测试","planQuantity":"125","finishCount":"26","progress":"1411%","passRate":"1466%"}]', '0:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1284, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00001","materialName":"测试","planQuantity":"111","finishCount":"12","progress":"11%","passRate":"66%"}]', '10:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1285, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00002","materialName":"测试","planQuantity":"112","finishCount":"13","progress":"111%","passRate":"166%"}]', '11:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1286, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00003","materialName":"测试","planQuantity":"113","finishCount":"14","progress":"211%","passRate":"266%"}]', '12:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1287, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00004","materialName":"测试","planQuantity":"114","finishCount":"15","progress":"311%","passRate":"366%"}]', '13:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1288, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00005","materialName":"测试","planQuantity":"115","finishCount":"16","progress":"411%","passRate":"466%"}]', '14:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1289, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00006","materialName":"测试","planQuantity":"116","finishCount":"17","progress":"511%","passRate":"566%"}]', '15:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1290, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00007","materialName":"测试","planQuantity":"117","finishCount":"18","progress":"611%","passRate":"666%"}]', '16:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1291, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00008","materialName":"测试","planQuantity":"118","finishCount":"19","progress":"711%","passRate":"766%"}]', '17:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1292, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00009","materialName":"测试","planQuantity":"119","finishCount":"20","progress":"811%","passRate":"866%"}]', '18:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1293, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00010","materialName":"测试","planQuantity":"120","finishCount":"21","progress":"911%","passRate":"966%"}]', '19:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1294, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00016","materialName":"测试","planQuantity":"126","finishCount":"27","progress":"1511%","passRate":"1566%"}]', '1:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1295, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00011","materialName":"测试","planQuantity":"121","finishCount":"22","progress":"1011%","passRate":"1066%"}]', '20:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1296, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00012","materialName":"测试","planQuantity":"122","finishCount":"23","progress":"1111%","passRate":"1166%"}]', '21:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1297, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00013","materialName":"测试","planQuantity":"123","finishCount":"24","progress":"1211%","passRate":"1266%"}]', '22:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1298, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00014","materialName":"测试","planQuantity":"124","finishCount":"25","progress":"1311%","passRate":"1366%"}]', '23:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1299, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00017","materialName":"测试","planQuantity":"127","finishCount":"28","progress":"1611%","passRate":"1666%"}]', '2:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1300, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00018","materialName":"测试","planQuantity":"128","finishCount":"29","progress":"1711%","passRate":"1766%"}]', '3:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1301, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00019","materialName":"测试","planQuantity":"129","finishCount":"30","progress":"1811%","passRate":"1866%"}]', '4:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1302, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00020","materialName":"测试","planQuantity":"130","finishCount":"31","progress":"1911%","passRate":"1966%"}]', '5:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1303, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00021","materialName":"测试","planQuantity":"131","finishCount":"32","progress":"2011%","passRate":"2066%"}]', '6:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1304, 'ocs_center-Production-screen', 'U1jUpaCBKm', '[{"workOrder":"w00022","materialName":"测试","planQuantity":"132","finishCount":"33","progress":"2111%","passRate":"2166%"}]', '7:32:25', 'yelinkoncall', '2024-12-05 10:42:33');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1306, 'ocs_center-Production-screen', 'NRdD60JahY', '[{"produceQuantity":"300"},{"produceQuantity":"300"},{"produceQuantity":"400"},{"produceQuantity":"355"},{"produceQuantity":"300"},{"produceQuantity":"350"},{"produceQuantity":"800"}]', '09:39:47', 'yelinkoncall', '2024-12-09 09:42:09');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1308, 'ocs_center-Production-screen', 'SP9ygpKhhF', '[{"produceQuantity":"1300"},{"produceQuantity":"1400"},{"produceQuantity":"1300"},{"produceQuantity":"1500"},{"produceQuantity":"1600"},{"produceQuantity":"900"},{"produceQuantity":"950"}]', '9:47:58', 'yelinkoncall', '2024-12-09 09:53:15');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1309, 'ocs_center-Production-screen', 'Z9QN8BzNbD', '[{"input":"1","yield":"10","time":"#{时间}","numOfBad":"1","directRate":"50"}]', '09:58:24', 'yelinkoncall', '2024-12-12 17:26:00');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1310, 'ocs_center-Production-screen', 'MM43EucCEX', '[{"workOrder":"GD001","materialName":"假数据工单","planQuantity":"100","finishCount":"50","progress":"50","passRate":"10"}]', '09:58:24', 'yelinkoncall', '2024-12-12 17:28:43');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1311, 'ocs_center-Production-screen', 'v1zAX9wboQ', '[{"input":"0","yield":"6","time":"#{时间}","numOfBad":"96","directRate":""},{"input":"0","yield":"6","time":"#{时间}","numOfBad":"96","directRate":""},{"input":"0","yield":"6","time":"#{时间}","numOfBad":"96","directRate":""},{"input":"0","yield":"6","time":"#{时间}","numOfBad":"96","directRate":""},{"input":"0","yield":"6","time":"#{时间}","numOfBad":"96","directRate":""},{"input":"0","yield":"6","time":"#{时间}","numOfBad":"96","directRate":""},{"input":"0","yield":"6","time":"#{时间}","numOfBad":"97","directRate":""}]', '19:51:09', 'yelinkoncall', '2024-12-12 19:52:21');
    INSERT INTO dfs_component_data (id, app_id, component_id, `data`, `time`, create_by, create_time) VALUES(1312, 'ocs_machining_produce_perspective', 'QAN7wN54ZR', '[{"orderNumber":"scdd1011","orderId":"1","actualProductStartTime":"2024/1/12","releasedCount":"3","remark":"","productionCode":"fzcp","planQuantity":"2000","planProductEndTime":"2024/10/20","abnormalCount":"0","procedureProcess":"0.3","completedRate":"0.3","finishCount":"600","productionName":"工程板"},{"orderNumber":"scdd1012","orderId":"2","actualProductStartTime":"2024/1/15","releasedCount":"","remark":"","productionCode":"jdcp","planQuantity":"8000","planProductEndTime":"2024/8/17","abnormalCount":"","procedureProcess":"0.2","completedRate":"0.2","finishCount":"1600","productionName":"钉板"},{"orderNumber":"scdd1013","orderId":"3","actualProductStartTime":"2024/1/13","releasedCount":"","remark":"","productionCode":"zscp","planQuantity":"16000","planProductEndTime":"2024/4/16","abnormalCount":"","procedureProcess":"0.05","completedRate":"0.05","finishCount":"800","productionName":"螺母"}]', '6:25:40', 'yelinkoncall', '2024-12-17 14:33:18');

	end $$
delimiter ;



-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
-- ================================================此脚本专为演示数据初始化设定，如需写脚本，请移步到3.15.1.1=======================================================
