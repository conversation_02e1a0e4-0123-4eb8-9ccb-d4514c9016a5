-- 该脚本仅用于调整路由权限

-- 备份一下原始表
DROP PROCEDURE if EXISTS route_backup;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_backup`()
begin
	-- 路由表
    CREATE TABLE IF NOT EXISTS `sys_route_backup` like sys_route;
    SET @cnt = (SELECT count(1) from sys_route_backup);
	IF (@cnt = 0) THEN
		INSERT INTO `sys_route_backup` SELECT * from sys_route;
    END IF;
	-- 权限表
    CREATE TABLE IF NOT EXISTS `sys_permissions_backup` like sys_permissions;
    SET @cnt2 = (SELECT count(1) from sys_permissions_backup);
	IF (@cnt2 = 0) THEN
		INSERT INTO `sys_permissions_backup` SELECT * from sys_permissions;
    END IF;
end $$
delimiter;
call route_backup();


-- 添加一个l1节点
DROP PROCEDURE if EXISTS route_add_l1;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_add_l1`(in _path varchar(500), in _name varchar(500))
begin
	SET @cnt = (SELECT count(1) from sys_route WHERE path = _path);
	-- 路由
	IF (@cnt = 0) THEN
		INSERT INTO `sys_route` (`path`, `parent_path`, `is_back_stage`, `title`, `icon`, `module_name`, `sort`, `sec_sort`, `type`)
		VALUES (_path, NULL, 0, _name, _name, NULL, 0, 0, 0);
		-- 权限, 先找999以内的, 没有则直接用path
		set @per_id = (SELECT max(CONVERT(id,SIGNED)) from sys_permissions WHERE parent_id = 0 and CONVERT(id,SIGNED) <= 999);
		if (@per_id < 999) then
			set @per_id = @per_id+1;
        else
			set @per_id = _path;
        end if;
        INSERT INTO `sys_permissions` ( `id`,`name`, `path`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `is_sys_data`)
        VALUES (@per_id, _name, _path, 'enable', 'GET', '0', 0, 1, 0, NULL, 1, 1);
        call init_new_role_permission(@per_id);
    END IF;
end $$
delimiter;

-- 在某一个l1节点下面添加一个l2节点
DROP PROCEDURE if EXISTS route_add_l2;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_add_l2`(in _parent_path varchar(500), in _path varchar(500), in _name varchar(500))
begin
	set @pid = (SELECT n.id from sys_permissions n WHERE n.path = _parent_path and type = 0);
	set @cnt = (SELECT count(1) from sys_route WHERE path = _path);
	IF(@pid is not null and @cnt = 0) THEN
		-- 路由
		INSERT INTO `sys_route` (`path`, `parent_path`, `is_back_stage`, `title`, `icon`, `module_name`, `sort`, `sec_sort`, `type`)
		VALUES (_path, _parent_path, 0, _name, Null, NULL, 0, 0, NULL);
		-- 权限
        INSERT INTO `sys_permissions` ( `id`,`name`, `path`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `is_sys_data`)
        VALUES (_path, _name, _path, 'enable', 'GET', @pid, 1, 1, 0, _parent_path, 1, 1);
        call init_new_role_permission(_path);
    END IF;
end $$
delimiter;

-- 将任意节点移动到某一个l1节点下面，作为l2节点
DROP PROCEDURE if EXISTS route_mv_l2;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_mv_l2`(in _parent_path varchar(500), in _path varchar(500), in new_name varchar(100))
begin
	set @pid = (SELECT n.id from sys_permissions n WHERE n.path = _parent_path and type = 0);
	set @is_back_stage = (SELECT n.is_back_stage from sys_permissions n WHERE n.path = _parent_path and type = 0);
	-- 如需重命名l2, 可传入名称
	if (new_name is null and @pid is not null) then
        update sys_route set parent_path = _parent_path, type = null WHERE path = _path;
        update sys_permissions set parent_path = _parent_path, parent_id = @pid, type = 1, is_back_stage = @is_back_stage WHERE path = _path;
    elseif (@pid is not null) then
        update sys_route set parent_path = _parent_path, type = null, title = new_name WHERE path = _path;
        update sys_permissions set parent_path = _parent_path, parent_id = @pid, type = 1, is_back_stage = @is_back_stage, `name` = new_name WHERE path = _path;
    end if;
end $$
delimiter;

-- 将任意节点移动到l1
DROP PROCEDURE if EXISTS route_mv_l1;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_mv_l1`(in _path varchar(500), in new_name varchar(100))
begin
    -- 如需重命名l1, 可传入名称
	if (new_name is null) then
        update sys_route set parent_path = null, type = 0 WHERE path = _path;
        update sys_permissions set parent_path = null, parent_id = '0', type = 0 WHERE path = _path;
    else
        update sys_route set parent_path = null, type = 0, title = new_name WHERE path = _path;
        update sys_permissions set parent_path = null, parent_id = '0', type = 0, `name` = new_name WHERE path = _path;
    end if;

	set @is_back_stage = (select is_back_stage from sys_route WHERE path = _path);
    if (@is_back_stage is null) THEN
        update sys_route set is_back_stage = 0 WHERE path = _path;
    end if;
end $$
delimiter;


-- 重命名节点
DROP PROCEDURE if EXISTS route_rn;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_rn`( in _path varchar(500), in new_name varchar(100))
begin
    update sys_route set title = new_name WHERE path = _path;
    update sys_permissions set `name` = new_name WHERE path = _path;
end $$
delimiter;


-- 将任意节点删除
DROP PROCEDURE if EXISTS route_rm;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_rm`(in _path varchar(500))
begin
    -- delete from sys_role_permission where permission_id in (select id FROM sys_permissions WHERE path = _path);
    delete from sys_permissions WHERE path = _path;
    delete from sys_route WHERE path = _path;
end $$
delimiter;


-- 节点排序
DROP PROCEDURE if EXISTS route_sort;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `route_sort`(in _path varchar(500), in _sort int)
begin
    update sys_permissions set sort = _sort WHERE path = _path;
    update sys_route set sort = _sort WHERE path = _path;
end $$
delimiter;


-- 移动按钮权限 old菜单 -> new菜单
DROP PROCEDURE if EXISTS permission_mv;
delimiter $$
CREATE DEFINER=`root`@`%` PROCEDURE `permission_mv`(in new_parent_path varchar(500), in old_parent_path varchar(500))
begin
	set @pid = (SELECT n.id from sys_permissions n WHERE n.path = new_parent_path);
	if (@pid is not null) then
        update sys_permissions set parent_path = new_parent_path, parent_id = @pid, is_enable = 1 WHERE parent_path = old_parent_path;
    end if;
end $$
delimiter;



-- 一级
-- 市场销售
call route_add_l1('/market-sale', '市场销售');
-- 二级
-- 销售订单
call route_mv_l2('/market-sale', '/supply-chain-collaboration/order-model/salesOrder', null);
-- 出货申请
call route_mv_l2('/market-sale', '/supply-chain-collaboration/order-model/shipment_application', null);
-- 销售发货
call route_mv_l2('/market-sale', '/supply-chain-collaboration/order-model/deliver_goods', '销售发货');
-- 销售退货
call route_mv_l2('/market-sale', '/supply-chain-collaboration/order-model/sales_returns', null);
-- 销售合同
call route_mv_l2('/market-sale', '/contract-management/sale-contract', null);
-- 条款管理
call route_mv_l2('/market-sale', '/documents-config/clause', null);
-- 客户档案
call route_mv_l2('/market-sale', '/supply-chain-collaboration/order-model/clientFile', null);
-- 客户物料清单
call route_mv_l2('/market-sale', '/supply-chain-collaboration/order-model/customer-material-list', null);
-- 销售看板
call route_mv_l2('/market-sale', '/order-model/saleReport', null);
-- 销售订单透
call route_mv_l2('/market-sale', '/trace/order-transparency', null);


-- 一级
-- 供应链协同
-- 二级
-- 采购订单
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/purchasing-list', null);
-- 采购收料单
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/delivery-order', '采购收料单');
-- 采购退料
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/return-order', '采购退料');
-- 采购需求单
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/purchasing-demand', '采购需求单');
-- 物料需求分析
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/materialAnalysis', null);
-- 采购退料申请
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/purchase-material-return-application', '采购退料申请');
-- 采购合同
call route_mv_l2('/supply-chain-collaboration', '/contract-management/purchase-contract', null);
-- 供应商档案
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/supplier-profile', null);
-- 供应商物料清单
call route_mv_l2('/supply-chain-collaboration', '/supply-chain-collaboration/procurement-management/supplyList', null);
-- 删除项
-- 销售管理
call route_rm('/supply-chain-collaboration/order-model');
-- 采购管理
call route_rm('/supply-chain-collaboration/procurement-management');


-- 一级
-- 生产计划
call route_rn('/order-model', '生产计划');
-- 二级
-- 工单排产
call route_rn('/order-model/work-schedule-production', '智能排产');
-- 删除项
-- 销售订单
call route_rm('/order-model/salesOrder');
-- 生产工单
call route_rm('/order-model/production-workorder');
-- 销售报表
call route_rm('/order-model/reportManage');

-- 一级
-- 委外管理
call route_mv_l1('/supply-chain-collaboration/outsourcingManagement', null);
-- 二级
-- 委外订单
call route_mv_l2('/supply-chain-collaboration/outsourcingManagement', '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', null);
-- 委外订单用料清单
call route_mv_l2('/supply-chain-collaboration/outsourcingManagement', '/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', null);
-- 委外发料单
call route_mv_l2('/supply-chain-collaboration/outsourcingManagement', '/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', null);
-- 委外订单收货单
call route_mv_l2('/supply-chain-collaboration/outsourcingManagement', '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', null);
-- 委外订单退货单
call route_mv_l2('/supply-chain-collaboration/outsourcingManagement', '/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', null);


-- 一级
-- 生产作业
-- 二级
-- 生产工单用料清单
call route_mv_l2('/workorder-model', '/order-model/workOrder-materials', null);
-- 作业工单
call route_mv_l2('/workorder-model', '/assignment-model/home-workorder', null);
-- 包装工单
call route_mv_l2('/workorder-model', '/order-model/package-order', null);
-- 作业工单报工记录
call route_mv_l2('/workorder-model', '/assignment-model/workorder-records', null);
-- 包装记录
call route_mv_l2('/workorder-model', '/trace/package-record', null);
-- 工资计算
call route_mv_l2('/workorder-model', '/trace/valuation-cal', null);
-- 删除项
-- 领料申请
call route_rm('/workorder-model/production-requisition');
-- 生产检查项目
call route_rm('/workorder-model/product-checked/productionCheckProject');
-- 生产检查方案
call route_rm('/workorder-model/product-checked/productionCheckPlan');
-- 生产报表
call route_rm('/workorder-model/reportManage');


-- 一级
-- 生产追溯
-- 二级
-- 物料条码
call route_mv_l2('/trace', '/identification-center/material-identification', null);


-- 一级
-- 产线质检
call route_rn('/facility-quality', '产线质检');
-- 二级
-- 维修定义
call route_mv_l2('/facility-quality', '/facility-maintain/reworkQuality/define', null);
-- 维修方案
call route_mv_l2('/facility-quality', '/facility-maintain/reworkQuality/maintainPlan/project', null);
-- 维修记录
call route_mv_l2('/facility-quality', '/facility-maintain/reworkQuality', null);


-- 一级
-- 现场管理
call route_rn('/smt-management', '现场管理');
-- 二级
-- 上料防错
call route_mv_l2('/smt-management', '/workorder-model/feed-to-prevent-errors', null);
-- 电芯数据管理
call route_mv_l2('/smt-management', '/battery-management/cellDataManagement', null);
-- 产前检查项目
call route_mv_l2('/smt-management', '/smt-management/antenatal-preparation/inspection-items', '产前检查项目');
-- 产前检查方案
call route_mv_l2('/smt-management', '/smt-management/antenatal-preparation/inspection-plan', '产前检查方案');
-- 产前检查记录
call route_mv_l2('/smt-management', '/smt-management/antenatal-preparation/inspection-record', '产前检查记录');
-- 删除项
-- 产前准备
call route_rm('/smt-management/antenatal-preparation');


-- 一级
-- 设备管理
call route_rn('/equipment', '设备管理');
-- 二级
-- 设备台账
call route_rn('/equipment/device', '设备台账');
-- 工装
call route_rn('/equipment/technology-device', '工装');
-- 删除项
-- 视频设备
call route_rm('/equipment/video-device');
-- 视频设备
call route_rm('/equipment/screen-management');


-- 一级
-- 设备维保
call route_rn('/equipment-management', '设备维保');
-- 二级
-- 维修工单
call route_rn('/equipment-management/warranty', '维修工单');
-- 巡检计划
call route_rn('/equipment-management/deviceInspection/index', '巡检计划');
-- 点检计划
call route_rn('/equipment-management/deviceDetection/index', '点检计划');
-- 保养计划
call route_rn('/equipment-management/deviceMaintenance/index', '保养计划');
-- 删除项
-- 生产设备
call route_rm('/equipment-management/device');


-- 一级
-- 告警作业
call route_rn('/alarm', '告警作业');
-- 二级
-- 告警转工单
call route_mv_l2('/alarm', '/equipment-management/alarmToOrder', '告警转工单');


-- 一级
-- 能耗管理
call route_rn('/energy-consumption-management', '能耗管理');


-- 一级
-- 产品定义
-- 二级
-- 单位配置
call route_mv_l2('/product-management', '/material-config/unit-config', null);
-- 物料类型配置
call route_mv_l2('/product-management', '/material-config/material-type-config', null);
-- 物料扩展字段
call route_mv_l2('/product-management', '/material-config/extend-config', '物料扩展字段');
-- 物料附加属性
call route_mv_l2('/product-management', '/material-config/material-property', '物料附加属性');


-- 一级
-- 工艺管理
call route_add_l1('/craft-procedure-management', '工艺管理');
-- 二级
-- 工序定义
call route_mv_l2('/craft-procedure-management', '/product-management/procedure', '工序定义');
-- 工序检验项定义
call route_mv_l2('/craft-procedure-management', '/product-management/process-inspection-item', '工序检验项定义');
-- 工艺
call route_mv_l2('/craft-procedure-management', '/product-management/technology', '工艺');
-- 工艺
call route_mv_l2('/craft-procedure-management', '/product-management/technologyTemplate', '工艺模板');


-- 一级
-- 工作中心
call route_rn('/production-config', '工作中心');
-- 二级
-- 消息通知配置
call route_mv_l2('/production-config', '/system_settings/circulation-notice-config', null);
-- 工位机工位设置
call route_mv_l2('/production-config', '/system_settings/app-permission-config', '工位机工位设置');
-- 岗位管理
call route_mv_l2('/production-config', '/base-info/post-manager', null);


-- 一级
-- 指标中心
call route_rn('/target', '指标中心');
-- 演示数据配置
call route_mv_l2('/target', '/system_settings/demo-data-config', null);


-- 一级
-- 标识中心
call route_rn('/documents-config', '标识中心');
-- 二级
-- 标签管理*
update sys_permissions set path = '/label-manage/labelRules' WHERE path = '/label-manage';
update sys_permissions set parent_path = '/label-manage/labelRules' WHERE parent_path = '/label-manage';
call route_mv_l2('/documents-config', '/label-manage/labelRules', '标签管理');
-- 删除项
-- 出入库类型配置
call route_rm('/documents-config/inbound-outbound-type-confige');
-- 打印配置
call route_rm('/documents-config/print-config');


-- 一级
-- 任务中心
call route_add_l1('/task-center', '任务中心');
-- 二级
-- 单据回退
call route_mv_l2 ('/task-center', '/documents-config/return-order', null);
-- 操作日志*
update sys_permissions set path = '/system/operation' WHERE path = '/system-operation';
update sys_permissions set parent_path = '/system/operation' WHERE parent_path = '/system-operation';
call route_mv_l2 ('/task-center', '/system/operation', null);
-- 单据下推配置
call route_mv_l2 ('/task-center', '/system_settings/orderPushDownConf', null);
-- 审批配置
call route_mv_l2 ('/task-center', '/documents-config/approveConfig', null);
-- 表单配置
call route_mv_l2 ('/task-center', '/system_settings/form-config/appFieldRuleConf', '表单配置');
-- 删除项
-- 后台表单配置
call route_rm('/system_settings/form-config/webFieldRuleConf');


-- 一级
-- 配置中心
call route_rn('/system_settings', '配置中心');
-- 二级
-- 软件参数配置
call route_rn('/system_settings/BusinessConfiguration', '软件参数配置');
-- 模型初始化
call route_rn('/system_settings/init-config', '模型初始化');
-- 删除项
-- 表单配置
call route_rm('/system_settings/form-config');


-- 一级
-- 用户中心
call route_rn('/base-info', '用户中心');
-- 二级
-- 角色业务权限
call route_rn('/base-info/role-permission', '角色业务权限');


-- 一级
-- 开发配置
call route_rn('/api/transform', '开发配置');
-- 二级
-- 页面重定向
call route_mv_l2('/api/transform', '/system_settings/pageRedirect','页面重定向');
-- 菜单配置
call route_mv_l2('/api/transform', '/system_settings/routeConf','菜单配置');
-- API重定向
call route_rn('/api/transform/apiList', 'API重定向');


-- 一级
-- 产品检验
call route_rn('/inspection-manage', '产品检验');
-- 二级
-- 检验单
call route_rn('/inspection-manage/qms-product-inspection/productInspection', '检验单');
-- 缺陷绑定
call route_mv_l2('/inspection-manage', '/inspection-manage/aftersale-inspection/defectBound','缺陷绑定');
-- 删除项
-- 检验项目组
call route_rm('/inspection-manage/qms-product-inspection/projectgroup');
-- 按钮
call permission_mv('/inspection-manage/qms-product-inspection/project', '/inspection-manage/qms-product-inspection/projectgroup');
update sys_permissions set name = CONCAT('项目组',name) WHERE path like 'qms-product-inspection.inspection-manage.projectgroup%' and name not like '项目组%';


-- 一级
-- 工序巡检
call route_add_l1('/procedure-inspect', '工序巡检');
-- 二级
-- 工序巡检计划
call route_mv_l2 ('/procedure-inspect', '/inspection-manage/process/plan', null);
-- 工序巡检项目
call route_mv_l2 ('/procedure-inspect', '/inspection-manage/process/item', null);
-- 工序巡检方案
call route_mv_l2 ('/procedure-inspect', '/inspection-manage/process/scheme', null);
-- 工序巡检记录
call route_mv_l2 ('/procedure-inspect', '/inspection-manage/process/inspect', null);









-- *******************排序******************
-- 1.市场销售

-- 销售订单
call route_sort('/supply-chain-collaboration/order-model/salesOrder', 1);
-- 出货申请
call route_sort('/supply-chain-collaboration/order-model/shipment_application', 2);
-- 销售发货
call route_sort('/supply-chain-collaboration/order-model/deliver_goods', 3);
-- 销售退货单
call route_sort('/supply-chain-collaboration/order-model/sales_returns', 4);
-- 销售合同
call route_sort('/contract-management/sale-contract', 5);
-- 条款管理
call route_sort('/documents-config/clause', 6);
-- 客户档案
call route_sort('/supply-chain-collaboration/order-model/clientFile', 7);
-- 客户物料清单
call route_sort('/supply-chain-collaboration/order-model/customer-material-list', 8);
-- 销售看板
call route_sort('/order-model/saleReport', 9);
-- 销售订单透明
call route_sort('/trace/order-transparency', 10);



-- 2.供应链协同

-- 采购订单
call route_sort('/supply-chain-collaboration/procurement-management/purchasing-list', 1);
-- 采购收料单
call route_sort('/supply-chain-collaboration/procurement-management/delivery-order', 2);
-- 采购退料
call route_sort('/supply-chain-collaboration/procurement-management/return-order', 3);
-- 采购需求单
call route_sort('/supply-chain-collaboration/procurement-management/purchasing-demand', 4);
-- 物料需求分析
call route_sort('/supply-chain-collaboration/procurement-management/materialAnalysis', 5);
-- 采购退料申请
call route_sort('/supply-chain-collaboration/procurement-management/purchase-material-return-application', 6);
-- 采购合同
call route_sort('/contract-management/purchase-contract', 7);
-- 供应商档案
call route_sort('/supply-chain-collaboration/procurement-management/supplier-profile', 8);
-- 供应商物料清单
call route_sort('/supply-chain-collaboration/procurement-management/supplyList', 9);



-- 3.生产计划

-- 生产订单
call route_sort('/order-model/product-order', 1);
-- 生产订单用料清单
call route_sort('/order-model/production-materials', 2);
-- 辅助计划
call route_sort('/order-model/auxiliary_plan', 3);
-- 工单排产
call route_sort('/order-model/work-schedule-production', 4);



-- 4.委外管理

-- 委外订单
call route_sort('/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 1);
-- 委外订单用料清单
call route_sort('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 2);
-- 委外发料单
call route_sort('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 3);
-- 委外订单收货单
call route_sort('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 4);
-- 委外订单退货单
call route_sort('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 5);


-- 5. 生产作业

-- 生产工单
call route_sort('/workorder-model/production-workorder', 1);
-- 生产工单用料清单
call route_sort('/order-model/workOrder-materials', 2);
-- 作业工单
call route_sort('/assignment-model/home-workorder', 3);
-- 包装工单
call route_sort('/order-model/package-order', 4);
-- 生产看板
call route_sort('/workorder-model/product-report/index', 5);
-- 人员计时计件看板
call route_sort('/workorder-model/hourly-piece-board/hourlyPieceBoard', 6);
-- 生产工单报工记录
call route_sort('/workorder-model/production-records', 7);
-- 作业工单报工记录
call route_sort('/assignment-model/workorder-records', 8);
-- 包装记录
call route_sort('/trace/package-record', 9);
-- 生产上工记录
call route_sort('/workorder-model/workorderAttendanceRecords', 10);
-- 工资计算
call route_sort('/trace/valuation-cal', 11);



-- 6.生产追溯

-- 生产订单追溯
call route_sort('/trace/order', 1);
-- 销售订单追溯
call route_sort('/trace/saleOrder-trace', 2);
-- 工单追溯
call route_sort('/trace/work-order', 3);
-- 单品追溯
call route_sort('/trace/single-product', 4);
-- 物料追溯
call route_sort('/trace/feed-record', 5);
-- 设备追溯
call route_sort('/trace/device-trace', 6);
-- 物料条码
call route_sort('/identification-center/material-identification', 7);



-- 7.产线质检

-- 不良定义
call route_sort('/facility-quality/processQuality/qualityDefinition', 1);
-- 质检方案
call route_sort('/facility-quality/quality-inspection/paln', 2);
-- 质检记录
call route_sort('/facility-quality/processQuality/qualityRecord', 3);
-- 维修定义
call route_sort('/facility-maintain/reworkQuality/define', 4);
-- 维修方案
call route_sort('/facility-maintain/reworkQuality/maintainPlan/project', 5);
-- 维修记录
call route_sort('/facility-maintain/reworkQuality', 6);



-- 8.现场管理

-- 物料站位表
call route_sort('/smt-management/material-station', 1);
-- 工单站位表
call route_sort('/smt-management/order-station', 2);
-- Feeder管理
call route_sort('/smt-management/feederManage', 3);
-- 拼码管理
call route_sort('/smt-management/combine-management', 4);
-- 上料防错
call route_sort('/workorder-model/feed-to-prevent-errors', 5);
-- BOSA管理
call route_sort('/smt-management/bosaManagement', 6);
-- log管理
call route_sort('/smt-management/logManagement', 7);
-- 老化管理
call route_sort('/smt-management/aging-management', 8);
-- 废料管理
call route_sort('/smt-management/scrap-material-management', 9);
-- 电芯数据管理
call route_sort('/battery-management/cellDataManagement', 10);
-- 产前检查项目
call route_sort('/smt-management/antenatal-preparation/inspection-items', 11);
-- 产前检查方案
call route_sort('/smt-management/antenatal-preparation/inspection-plan', 12);
-- 产前检查记录
call route_sort('/smt-management/antenatal-preparation/inspection-record', 13);



-- 9.设备管理

-- 设备台账
call route_sort('/equipment/device', 1);
-- 设备类型
call route_sort('/equipment/equipment-model', 2);
-- 工装
call route_sort('/equipment/technology-device', 3);
-- 采集设备
call route_sort('/equipment/test-equipment', 4);
-- 终端设备
call route_sort('/equipment/station-machine', 5);



-- 10.设备维保

-- 维修工单
call route_sort('/equipment-management/warranty', 1);
-- 设备检查项目组
call route_sort('/equipment-management/equipment-checked/team', 2);
-- 设备检查项目
call route_sort('/equipment-management/equip-checked/equipCheckProject', 3);
-- 设备检查方案
call route_sort('/equipment-management/equip-checked/equipCheckPlan', 4);
-- 巡检计划
call route_sort('/equipment-management/deviceInspection/index', 5);
-- 点检计划
call route_sort('/equipment-management/deviceDetection/index', 6);
-- 保养计划
call route_sort('/equipment-management/deviceMaintenance/index', 7);
-- 维保班组
call route_sort('/equipment-management/maintainGroup', 8);



-- 11.告警作业

-- 告警定义
call route_sort('/alarm/alarmIDDefine', 1);
-- 告警设置
call route_sort('/alarm/config', 2);
-- 告警列表
call route_sort('/alarm/list', 3);
-- 历史告警
call route_sort('/alarm/historyAlarm', 4);
-- 告警转工单
call route_sort('/equipment-management/alarmToOrder', 5);
-- 事件定义
call route_sort('/alarm/event-definition', 6);
-- 事件记录
call route_sort('/alarm/event-record', 7);



-- 13.能耗管理

-- 能源信息
call route_sort('/energy-consumption-management/energy-information', 1);
-- 用电时段管理
call route_sort('/energy-consumption-management/time-period-management', 2);



-- 14.产品定义

-- 物料
call route_sort('/product-management/supplies', 1);
-- 物料特征参数
call route_sort('/product-management/auxiliary-attr', 2);
-- 单位配置
call route_sort('/material-config/unit-config', 3);
-- 物料类型配置
call route_sort('/material-config/material-type-config', 4);
-- 物料扩展字段
call route_sort('/material-config/extend-config', 5);
-- 物料附加属性
call route_sort('/material-config/material-property', 6);
-- BOM
call route_sort('/product-management/bom', 7);
-- BOM模板
call route_sort('/product-management/bomTemplate', 8);
-- 替代方案
call route_sort('/product-management/replace-scheme', 9);
-- 包装方案
call route_sort('/packageManagement/scheme', 10);



-- 15.工艺管理

-- 工序定义
call route_sort('/product-management/procedure', 1);
-- 工序检验项定义
call route_sort('/product-management/process-inspection-item', 2);
-- 工艺
call route_sort('/product-management/technology', 3);
-- 工艺模板
call route_sort('/product-management/technologyTemplate', 4);



-- 16.工厂建模

-- 公司信息
call route_sort('/factory-model/companyInfo/index', 1);
-- 工厂模型
call route_sort('/factory-model/product-model/index', 2);
-- 厂区
call route_sort('/factory-model/factory-area', 3);
-- 车间
call route_sort('/factory-model/workshop', 4);
-- 工作中心
call route_sort('/factory-model/work-center', 5);
-- 制造单元
call route_sort('/factory-model/product-line', 6);
-- 工位
call route_sort('/factory-model/station', 7);
-- 班组
call route_sort('/factory-model/team', 8);
-- 业务单元
call route_sort('/factory-model/business-unit', 9);



-- 17.工作中心

-- 工作日历
call route_sort('/production-config/workCalendar/worksCalendar', 1);
-- 班次配置
call route_sort('/production-config/flight-config', 2);
-- 产能列表
call route_sort('/production-config/capacityList', 3);
-- 消息通知配置
call route_sort('/system_settings/circulation-notice-config', 4);
-- 工位机工位设置
call route_sort('/system_settings/app-permission-config', 5);
-- 工资配置
call route_sort('/production-config/valuation-config', 6);
-- 岗位管理
call route_sort('/base-info/post-manager', 7);



-- 18.指标中心

-- 工厂指标
call route_sort('/target/factoryTarget', 1);
-- 演示数据配置
call route_sort('/system_settings/demo-data-config', 2);



-- 19.标识中心

-- 编码规则
call route_sort('/documents-config/num-rule', 1);
-- 扫码规则
call route_sort('/documents-config/scan-rule', 2);
-- 解码规则
call route_sort('/documents-config/decode-rule', 3);
-- 标签管理
call route_sort('/label-manage/labelRules', 4);



-- 20.任务中心

-- 单据回退
call route_sort('/documents-config/return-order', 1);
-- 操作日志
call route_sort('/system/operation', 2);
-- 单据下推配置
call route_sort('/system_settings/orderPushDownConf', 3);
-- 审批配置
call route_sort('/documents-config/approveConfig', 4);
-- 表单配置
call route_sort('/system_settings/form-config/appFieldRuleConf', 5);



-- 21.配置中心

-- 软件参数配置
call route_sort('/system_settings/BusinessConfiguration', 1);
-- 白牌设置
call route_sort('/system_settings/whiteCardSetting', 2);
-- 模型初始化
call route_sort('/system_settings/init-config', 3);



-- 22.用户中心

-- 角色业务权限
call route_sort('/base-info/role-permission', 1);
-- 用户管理
call route_sort('/base-info/user', 2);
-- 组织机构
call route_sort('/base-info/organization', 3);


-- 23.开发配置

-- 页面重定向
call route_sort('/system_settings/pageRedirect', 1);
-- 菜单配置
call route_sort('/system_settings/routeConf', 2);
-- API重定向
call route_sort('/api/transform/apiList', 3);



-- 24.工序巡检

-- 工序巡检计划
call route_sort('/inspection-manage/process/plan', 1);
-- 工序巡检项目
call route_sort('/inspection-manage/process/item', 2);
-- 工序巡检方案
call route_sort('/inspection-manage/process/scheme', 3);
-- 工序巡检记录
call route_sort('/inspection-manage/process/inspect', 4);



-- 25.产品检验

-- 抽样方案
call route_sort('/inspection-manage/qms-product-inspection/samplePlane', 1);
-- 检验项目
call route_sort('/inspection-manage/qms-product-inspection/project', 2);
-- 检验方案
call route_sort('/inspection-manage/qms-product-inspection/scheme', 3);
-- 检验单
call route_sort('/inspection-manage/qms-product-inspection/productInspection', 4);
-- 质量看板
call route_sort('/inspection-manage/qms-product-inspection/productionQualityReport', 5);
-- 缺陷定义
call route_sort('/inspection-manage/qms-product-inspection/defect', 6);
-- 缺陷原因
call route_sort('/inspection-manage/qms-product-inspection/defectReason', 7);
-- 缺陷结果
call route_sort('/inspection-manage/qms-product-inspection/defectResult', 8);
-- 缺陷绑定
call route_sort('/inspection-manage/aftersale-inspection/defectBound', 9);