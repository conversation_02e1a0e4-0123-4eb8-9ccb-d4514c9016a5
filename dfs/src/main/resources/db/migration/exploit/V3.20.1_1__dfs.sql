-- 能力标签相关表
CREATE TABLE IF NOT EXISTS `dfs_capacity_label`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `label_code`  varchar(255) NOT NULL COMMENT '能力标签编码',
    `label_name`  varchar(255) NOT NULL COMMENT '能力标签名称',
    `create_by`   varchar(255) DEFAULT NULL COMMENT '创建人',
    `create_time` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`   varchar(255) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `label_code` (`label_code`) USING BTREE COMMENT '唯一标识'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='能力标签定义表';

CREATE TABLE IF NOT EXISTS `dfs_capacity_label_basic_unit_relation`
(
    `id`                         int(11) NOT NULL AUTO_INCREMENT,
    `label_code`                 varchar(255) NOT NULL COMMENT '所属的能力标签编码',
    `production_basic_unit_type` varchar(255) NOT NULL COMMENT '生产资源类型',
    `production_basic_unit_id`   int(11) NOT NULL COMMENT '生产基本单元id',
    `production_basic_unit_name` varchar(255) DEFAULT NULL COMMENT '生产基本单元名称(冗余)',
    PRIMARY KEY (`id`),
    KEY                          `label_code` (`label_code`) USING BTREE COMMENT '所属的能力标签标识',
    KEY                          `basic_index` (`production_basic_unit_type`,`production_basic_unit_id`) USING BTREE COMMENT '生产基本单元标识'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='能力标签关联的生产基本单元';

CREATE TABLE IF NOT EXISTS `dfs_procedure_def_capacity_label_relation`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT,
    `procedure_def_code` varchar(255) NOT NULL COMMENT '工序定义编码',
    `related_label_code` varchar(255) NOT NULL COMMENT '关联的能力标签编码',
    `priority`           int(11) DEFAULT '1' COMMENT '优先级',
    PRIMARY KEY (`id`),
    KEY                  `procedure_def_code` (`procedure_def_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工序定义和能力标签的关联表';

-- 工序流转条件
call proc_add_column(
        'dfs_procedure_controller_config',
        'standard_circulation_duration_type',
        'ALTER TABLE `dfs_procedure_controller_config` ADD COLUMN `standard_circulation_duration_type` varchar(50) DEFAULT ''time'' COMMENT ''工序流转条件类型''');
call proc_modify_column(
        'dfs_procedure_controller_config',
        'standard_circulation_duration',
        'ALTER TABLE `dfs_procedure_controller_config` MODIFY COLUMN `standard_circulation_duration` double(20,2) DEFAULT ''0.00'' COMMENT ''工序流转条件''');

call proc_add_column(
        'dfs_procedure_def_controller_config',
        'standard_circulation_duration_type',
        'ALTER TABLE `dfs_procedure_def_controller_config` ADD COLUMN `standard_circulation_duration_type` varchar(50) DEFAULT ''time'' COMMENT ''工序流转条件类型''');
call proc_modify_column(
        'dfs_procedure_def_controller_config',
        'standard_circulation_duration',
        'ALTER TABLE `dfs_procedure_def_controller_config` MODIFY COLUMN `standard_circulation_duration` double(20,2) DEFAULT ''0.00'' COMMENT ''工序流转条件''');

-- 下推记录的数量改大点，防止数量过大存不进来
call proc_modify_column(
        'dfs_order_push_down_record',
        'push_down_quantity',
        'ALTER TABLE `dfs_order_push_down_record` MODIFY COLUMN `push_down_quantity` double(18,4) DEFAULT ''0.00'' COMMENT ''源单据下推数量''');
call proc_modify_column(
        'dfs_order_push_down_record',
        'target_order_push_down_quantity',
        'ALTER TABLE `dfs_order_push_down_record` MODIFY COLUMN `target_order_push_down_quantity` double(18,4) DEFAULT ''0.00'' COMMENT ''目标单据下推数量''');
-- 删除无用字段
call proc_drop_column_index('dfs_capacity','line_id');
call proc_modify_column(
    'dfs_capacity',
    'line_id',
    'ALTER TABLE `dfs_capacity` DROP COLUMN `line_id`');
call proc_modify_column(
    'dfs_capacity',
    'line_code',
    'ALTER TABLE `dfs_capacity` DROP COLUMN `line_code`');
call proc_modify_column(
    'dfs_capacity',
    'line_name',
    'ALTER TABLE `dfs_capacity` DROP COLUMN `line_name`');
-- 产能新增厂区、工作中心、生产基本单元类型、物料类型字段
call proc_add_column(
        'dfs_capacity',
        'area_code',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `area_code` varchar(255) DEFAULT NULL COMMENT ''厂区编码''');
call proc_add_column(
        'dfs_capacity',
        'work_center_id',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `work_center_id` int(11) DEFAULT NULL COMMENT ''工作中心id'' AFTER `theory_staff_num`');
call proc_add_column(
        'dfs_capacity',
        'production_basic_unit_type',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `production_basic_unit_type` int(11) DEFAULT NULL COMMENT ''生产基本单元类型'' AFTER `work_center_type`');
call proc_add_column(
        'dfs_capacity',
        'production_basic_unit_type_name',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `production_basic_unit_type_name` varchar(255) DEFAULT NULL COMMENT ''生产基本单元类型名称'' AFTER `production_basic_unit_type`');
call proc_add_column(
        'dfs_capacity',
        'material_type',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `material_type` int(11) DEFAULT NULL COMMENT ''物料类型'' AFTER `capacity_id`');
call proc_add_column(
        'dfs_capacity',
        'capacity_unit',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `capacity_unit` varchar(255) DEFAULT ''perHour'' COMMENT ''产能单位'' AFTER `unit_type`');
-- 工厂产能需要插入同步到产能配置表中
INSERT INTO `dfs_capacity` (`area_code`) SELECT `acode` FROM `dfs_area` WHERE `acode` IS NOT NULL OR `acode` != '';
UPDATE `dfs_capacity` ca, `dfs_dict` d SET ca.`capacity` = d.`code` WHERE d.`type` = 'factoryCapacity' AND ca.`area_code` IS NOT NULL;
-- 将生产基本单元默认产能同步到产能配置表中
INSERT INTO `dfs_capacity` (`work_center_type`, `production_basic_unit_id`, `production_basic_unit_code`, `production_basic_unit_name`, `capacity`, `theory_staff_num`, `capacity_per_person`)
SELECT `work_center_type`, `production_basic_unit_id`, `production_basic_unit_code`, `production_basic_unit_name`, `capacity`, `theory_staff_num`, `capacity_per_person`
FROM `dfs_default_capacity`
WHERE `capacity` IS NOT NULL;
-- 产能配置兼容历史数据
UPDATE `dfs_capacity` c,`dfs_material` m
SET c.`material_type` = m.`type`
WHERE c.`material_code` = m.`code`;

UPDATE `dfs_capacity` c,`dfs_production_line` m
SET c.`production_basic_unit_type` = m.`model_id`, c.`work_center_id` = m.`work_center_id`
WHERE c.`production_basic_unit_id` = m.`production_line_id`
AND c.`work_center_type` = 'line';

UPDATE `dfs_capacity` c,`dfs_device` m
SET c.`production_basic_unit_type` = m.`model_id`
WHERE c.`production_basic_unit_id` = m.`device_id`
  AND c.`work_center_type` = 'device';

UPDATE `dfs_capacity` c,`sys_team` m
SET c.`production_basic_unit_type` = m.`team_type`
WHERE c.`production_basic_unit_id` = m.`id`
  AND c.`work_center_type` = 'team';

UPDATE `dfs_capacity` c,`dfs_model` m
SET c.`production_basic_unit_type_name` = m.`name`
WHERE c.`production_basic_unit_type` = m.`id`
AND c.`work_center_type` in ('device', 'line');

UPDATE `dfs_capacity` c,`dfs_team_type_def` m
SET c.`production_basic_unit_type_name` = m.`type_def_name`
WHERE c.`production_basic_unit_type` = m.`id`
  AND c.`work_center_type` ='team';

UPDATE `dfs_capacity` c,`dfs_work_center_team` m
SET c.`work_center_id` = m.`work_center_id`
WHERE c.`production_basic_unit_id` = m.`team_id`
  AND c.`work_center_type` ='team';

UPDATE `dfs_capacity` c,`dfs_work_center_device` m
SET c.`work_center_id` = m.`work_center_id`
WHERE c.`production_basic_unit_id` = m.`device_id`
  AND c.`work_center_type` ='device';

-- 将字段为空字符串的数据统一update成null
UPDATE dfs_capacity
SET
    material_code = NULLIF(material_code, ''),
    capacity_unit = NULLIF(capacity_unit, ''),
    create_by = NULLIF(create_by, ''),
    update_by = NULLIF(update_by, ''),
    work_center_type = NULLIF(work_center_type, ''),
    production_basic_unit_type_name = NULLIF(production_basic_unit_type_name, ''),
    production_basic_unit_code = NULLIF(production_basic_unit_code, ''),
    production_basic_unit_name = NULLIF(production_basic_unit_name, ''),
    area_code = NULLIF(area_code, '')
WHERE
    material_code = ''
   OR capacity_unit = ''
   OR create_by = ''
   OR update_by = ''
   OR work_center_type = ''
   OR production_basic_unit_type_name = ''
   OR production_basic_unit_code = ''
   OR production_basic_unit_name = ''
   OR area_code = '';

-- 防止默认产能和实例产能重复，这里要去重
DELETE FROM dfs_capacity
WHERE capacity_id NOT IN (
    SELECT max_id FROM (
                           SELECT MAX(capacity_id) AS max_id
                           FROM dfs_capacity
                           GROUP BY
                               material_type,
                               material_id,
                               material_code,
                               work_center_id,
                               work_center_type,
                               production_basic_unit_type,
                               production_basic_unit_id,
                               area_code
                       ) AS tmp
);
-- 产能支持小数
call proc_modify_column(
        'dfs_capacity',
        'capacity',
        'ALTER TABLE `dfs_capacity` MODIFY COLUMN `capacity` double(18,4) DEFAULT ''0.00'' COMMENT ''产能''');

-- 客户编码长度扩大为255
call proc_modify_column(
        'dfs_customer',
        'customer_code',
        'ALTER TABLE `dfs_customer` MODIFY COLUMN `customer_code` varchar(255) NOT NULL COMMENT ''客户编号''');

-- 操作日志增加索引
call proc_add_column_index('dfs_operation_log','username','username');

-- 创建能力标签路由以及对应权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11310', '能力标签定义', '/factory-model/capacity-label', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '113', 1, 1, 1, '/factory-model', 1, 10, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11310010', '新增', 'capacity-label:add', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'POST', '11310', 2, 1, 1, '/factory-model/capacity-label', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11310020', '编辑', 'capacity-label:update', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'PUT', '11310', 2, 1, 1, '/factory-model/capacity-label', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11310040', '删除', 'capacity-label:delete', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'DELETE', '11310', 2, 1, 1, '/factory-model/capacity-label', 1, NULL, '', 1);
call init_new_role_permission('11310%');
INSERT INTO `sys_route`(`id`, `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES (null, '/factory-model/capacity-label', '/factory-model', '能力标签定义', NULL, '能力标签定义', NULL, 'dfs', NULL, NULL, NULL, 0, 1, NULL, '');
-- web权限和小程序关联
INSERT INTO `dfs_app_online`(`id`, `permission_id`, `permission_path`, `application_id`, `name`) VALUES (null, '11310', '/factory-model/capacity-label', 'new.yk.genieos.dfs.factory-model.capacity-label', '能力标签定义');

-- 生产订单订单下推采购需求配置
delete from dfs_order_push_down_item  where instance_type  = 'production.productOrderPushDownConfig.purchaseRequest.jumpPage';
delete from dfs_order_push_down_config_value  where config_full_path_code  = 'production.productOrderPushDownConfig.purchaseRequest.jumpPage';
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.productOrderPushDownConfig.purchaseRequest.pushBatch', '按BOM下推', 'production.productOrderPushDownConfig.purchaseRequest', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.enable', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.originalOrderStates', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.description', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.targetOrderStates', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/state', 'get', NULL, 'code,name', '[1,4]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式(单选)', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.bomSplitType', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/bom/split', 'get', NULL, 'code,name', '["direct","byTierOneBom","byMultiLevelBom"]','\"direct\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeSampleMaterial', '是否合并相同物料', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.mergeSampleMaterial', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeOrder', '是否合单', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.mergeOrder', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.filterMaterialSorts', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.appId', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.url', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-demand?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.sourceOrderType', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.targetOrderType', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseRequest\"', NULL);
-- 销售订单下推采购需求内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.productOrderPushDownConfig.purchaseRequest',  '下推采购需求', 'RULE', 1, 1, 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.enable', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.originalOrderStates', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.isSupportMaterialLineReversePushDown', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.description', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.targetOrderStates', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/state', 'get', NULL, 'code,name', '[1,4]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式(单选)', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.bomSplitType', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'api', '/ams/requests/bom/split', 'get', NULL, 'code,name', '["direct","byTierOneBom","byMultiLevelBom"]','\"direct\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeSampleMaterial', '是否合并相同物料', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.mergeSampleMaterial', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'mergeOrder', '是否合单', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.mergeOrder', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.filterMaterialSorts', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.appId', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.url', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/procurement-management/purchasing-demand?pushDownOrigin=productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.sourceOrderType', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.targetOrderType', 'production.productOrderPushDownConfig.purchaseRequest.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"purchaseRequest\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.purchaseRequest.pushBatch';

-- 工时业务配置修改
UPDATE dfs_business_config_value SET option_values = '[{"value":"s","label":"s"},{"value":"min","label":"min"},{"value":"h","label":"h"}]'
WHERE value_full_path_code IN ('design.craftConfig.craftDefaultConf.craftWorkHourPreparationTimeUnitDefault', 'design.craftConfig.craftDefaultConf.craftWorkHourProcessingHoursUnitDefault','design.craftConfig.craftDefaultConf.craftWorkHourDefaultTimeUnitDefault');
UPDATE dfs_business_config_value SET value_name = '工序标准调试工时单位默认值'
WHERE value_full_path_code ='design.craftConfig.craftDefaultConf.craftWorkHourPreparationTimeUnitDefault';

-- 生产备注
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderRemark', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderRemark', 'workOrderNumber', '工单号', 'work_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderRemark', 'username', '用户账号', 'username', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderRemark', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderRemark', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderRemark', 'updateTime', '更新时间', 'update_time', NULL);

-- 默认合并供应商
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = 'false' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleSupplier';
UPDATE `dfs_order_push_down_config_value` SET `value` = 'false' WHERE `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.mergeSampleSupplier';
-- 生产基本单元投产记录表
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderBasicUnitInputRecord', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderBasicUnitInputRecord', 'workOrderNumber', '工单号', 'work_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderBasicUnitInputRecord', 'username', '操作人账号', 'username', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderBasicUnitInputRecord', 'productionBasicUnitId', '生产基本单元id', 'production_basic_unit_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderBasicUnitInputRecord', 'productionBasicUnitName', '生产基本单元名称', 'production_basic_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderBasicUnitInputRecord', 'startTime', '开始时间', 'start_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrderBasicUnitInputRecord', 'endTime', '结束时间', 'end_time', NULL);

-- 工序
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'procedureId', '工序id', 'procedure_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'name', '工序名称', 'name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'alias', '工序别名', 'alias', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'procedureCode', '工序定义编号', 'procedure_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'state', '工序状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'procedureEntity', 'updateTime', '更新时间', 'update_time', NULL);

-- 用户
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'username', '用户名', 'user_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'mobile', '手机号', 'mobile', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'nickname', '用户昵称', 'nick_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'enabled', '账号是否可用', 'enabled', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'user', 'updateTime', '更新时间', 'update_time', NULL);

-- 车间
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'gid', '车间id', 'gid', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'gcode', '车间编码', 'gcode', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'aid', '厂区id', 'aid', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'gname', '车间名称', 'gname', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'grid', 'updateTime', '更新时间', 'update_time', NULL);

-- 标签模板
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'ruleId', '规则id', 'rule_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'ruleCode', '规则编号', 'rule_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'ruleName', '规则名称', 'rule_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'codeType', '条码类型', 'code_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'isDefault', '该条码是否为默认条码', 'is_default', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'barCodeRule', 'updateTime', '更新时间', 'update_time', NULL);

-- 订单报工ocs
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report', '订单报工', '/ocs-productOrder-report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.investment', '投产', 'ocs.productOrder.report:investment', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.report', '报工', 'ocs.productOrder.report:report', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.hang', '挂起', 'ocs.productOrder.report:product:hang', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.finish', '完成', 'ocs.productOrder.report:finish', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.putInto', '投入', 'ocs.productOrder.report:putInto', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.barCode', '批次', 'ocs.productOrder.report:barCode', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.productionNote', '生产备注', 'ocs.productOrder.report:productionNote', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
-- INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.abnormalReport', '异常上报', 'ocs.productOrder.report:abnormalReport', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.reportRecordEdit', '报工记录修改', 'ocs.productOrder.report:reportRecordEdit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.putIntoRecordEdit', '投入记录修改', 'ocs.productOrder.report:putIntoRecordEdit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.abnormalReportEdit', '异常记录编辑', 'ocs.productOrder.report:abnormalReportEdit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.appendix', '附件', 'ocs.productOrder.report:appendix', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.uploadAppendix', '上传附件', 'ocs.productOrder.report:uploadAppendix', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `is_sys_data`) VALUES ('yelink.ocs.productOrder.report.downloadAppendix', '下载附件', 'ocs.productOrder.report:downloadAppendix', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.ocs.productOrder.report', 2, 0, 1, NULL, 1, NULL, 1);

-- 订单报工OCS配置
INSERT INTO `dfs_business_config` VALUES (NULL, 'orderReportOCSConfig', '订单报工OCS配置', 'production.orderReportOCSConfig', 'production', NULL, 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config` VALUES (NULL,  'investmentConfig', '投产配置', 'production.orderReportOCSConfig.investmentConfig', 'production.orderReportOCSConfig', '点击投产按钮时是否会挂起其他占用所选生产基本单元的投产工单', 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config_value` VALUES (null, 'hangUpOther', '是否挂起其他投产工单', 'production.orderReportOCSConfig.investmentConfig.hangUpOther', 'production.orderReportOCSConfig.investmentConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'true', NULL);
INSERT INTO `dfs_business_config` VALUES (NULL, 'multipleOperatorConfiguration', '操作员多选配置', 'production.orderReportOCSConfig.multipleOperatorConfiguration', 'production.orderReportOCSConfig', '点击报工按钮时是否可以选择多个操作员', 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config_value` VALUES (null, 'multipleOperator', '操作员是否可以多选', 'production.orderReportOCSConfig.multipleOperatorConfiguration.multipleOperator', 'production.orderReportOCSConfig.multipleOperatorConfiguration', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":true,"label":"是"},{"value":false,"label":"否"}]', 'false', NULL);
INSERT INTO `dfs_business_config` VALUES (NULL, 'reportBarCodeType', '报工批次类型', 'production.orderReportOCSConfig.reportBarCodeType', 'production.orderReportOCSConfig', '配置新增和展示的批次类型', 'yelinkoncall', 'yelinkoncall', now(), now(), '');
INSERT INTO `dfs_business_config_value` VALUES (null, 'barCodeType', '支持批次类型', 'production.orderReportOCSConfig.reportBarCodeType.barCodeType', 'production.orderReportOCSConfig.reportBarCodeType', 'select', 'table', NULL, NULL, NULL, NULL, '[{"value":"finished","label":"工单批次"},{"value":"productBarCode","label":"订单批次"}]', '"finished"', NULL);

-- 每月指标 客户数统计
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'average_number_of_order_customer',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `average_number_of_order_customer` double(11,2) NULL COMMENT ''客均订单数'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'average_number_plan_order_customer',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `average_number_plan_order_customer` double(11,2) NULL COMMENT ''客均下单数'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'order_qaq',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `order_qaq` double(11,4) NULL COMMENT ''订单数环比'';');

call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'order_product_qaq',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `order_product_qaq` double(11,4) NULL COMMENT ''订单产品数环比'';');


call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'proportion_of_three_customers',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` ADD COLUMN `proportion_of_three_customers` double(11,4) NULL COMMENT ''前三大客户占比'';');
-- oee
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_oee',
        'plan_quantity',
        'ALTER TABLE `dfs_metrics_device_oee` ADD COLUMN `plan_quantity` double(11,4) NULL COMMENT ''计划数'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_oee',
        'plan_achievement_rate',
        'ALTER TABLE `dfs_metrics_device_oee` ADD COLUMN `plan_achievement_rate` double(11,4) NULL COMMENT ''计划达成率'';');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_oee',
        'theory_working_hour',
        'ALTER TABLE `dfs_metrics_device_oee` ADD COLUMN `theory_working_hour` double(11,4) NULL COMMENT ''理论产出工时'';');

-- 报工表
call proc_add_column(
        'dfs_report_line',
        'resource_device_id',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_device_id` int(11) DEFAULT NULL COMMENT ''关联资源-设备id'';');

call proc_add_column(
        'dfs_report_line',
        'resource_team_id',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_team_id` int(11) DEFAULT NULL COMMENT ''关联资源-班组id'';');

call proc_add_column(
        'dfs_report_line',
        'resource_line_id',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `resource_line_id` int(11) DEFAULT NULL COMMENT ''关联资源-制造单元id'';');
UPDATE dfs_report_line rl
    INNER JOIN dfs_production_line pl on rl.resource_line_code = pl.`production_line_code`
    SET rl.resource_line_id = pl.production_line_id
WHERE rl.resource_line_id is null;

UPDATE dfs_report_line rl
    INNER JOIN dfs_device d on rl.resource_device_code = d.`device_code`
    SET rl.resource_device_id = d.device_id
WHERE rl.resource_device_id is null;

UPDATE dfs_report_line rl
    INNER JOIN sys_team t on rl.resource_team_code = t.`team_code`
    SET rl.resource_team_id = t.id
WHERE rl.resource_team_id is null;


-- 资源每日产量表
call proc_add_column(
        'dfs_work_order_basic_unit_day_count',
        'is_main',
        'ALTER TABLE `dfs_work_order_basic_unit_day_count` ADD COLUMN `is_main` tinyint(4) DEFAULT NULL COMMENT ''是否投产'';');
update dfs_work_order_basic_unit_day_count	set is_main = 1 WHERE is_main is null;

-- 资源产量表
call proc_add_column(
        'dfs_work_order_basic_unit_count',
        'is_main',
        'ALTER TABLE `dfs_work_order_basic_unit_count` ADD COLUMN `is_main` tinyint(4) DEFAULT NULL COMMENT ''是否投产'';');
update dfs_work_order_basic_unit_count	set is_main = 1 WHERE is_main is null;


call proc_add_column(
        'dfs_fac_cal_eui',
        'target_name',
        'ALTER TABLE `dfs_fac_cal_eui` ADD COLUMN `target_name` varchar(255) NULL COMMENT ''指标名''');

-- 生产工单关联的操作员
CREATE TABLE IF NOT EXISTS `dfs_work_order_operator`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) DEFAULT NULL COMMENT '生产工单号',
    `operator_type`     varchar(255) DEFAULT NULL COMMENT '操作类型',
    `operator`          varchar(255) DEFAULT NULL COMMENT '操作员',
    PRIMARY KEY (`id`),
    KEY                 `work_order_number` (`work_order_number`) USING BTREE,
    KEY                 `operator_type` (`operator_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产工单关联的操作员';

-- 更新编码规则存储过程，入参改为字符类型
DELIMITER $$
DROP PROCEDURE IF EXISTS init_number_rules;
CREATE DEFINER=`root`@`%` PROCEDURE `init_number_rules`(in var_id varchar(255), in var_rule_name varchar(255))
top:
BEGIN
    -- 定义变量
    DECLARE type_id varchar(255);
    DECLARE uuid_val CHAR(36);
    DECLARE prefix VARCHAR(500);
    DECLARE suffix VARCHAR(500);
    DECLARE nowTime TIMESTAMP;

    -- 获取当前时间
    SET nowTime = NOW();
    -- 生成UUID
    SELECT UUID() INTO uuid_val;
    -- 设置编码规则JSON模板
    SET prefix = '[{\"code\":\"2\",\"name\":\"当前日期\",\"rule\":\"yyyyMMdd\",\"initValue\":1,\"example\":\"20240813\"},{\"code\":\"4\",\"name\":\"自动生成序号\",\"rule\":\"3\",\"autoIncrementConfigureType\":\"day\",\"uuid\":\"';
    SET suffix = '\",\"initValue\":1,\"example\":\"001\"}]';

    -- 查询是否存在指定的规则类型
    SELECT type INTO type_id FROM dfs_rule_type_config WHERE type = var_id;

    -- 如果存在，则结束存储过程,避免重复刷脚本导致脏数据生成
    IF type_id IS NOT NULL THEN
            LEAVE top;
    END IF;

    -- 1. 插入规则类型配置(没填则默认归属于'其他')
    INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`, `parent_type_code`) VALUES (var_id, var_rule_name, nowTime, nowTime, 'OTHER');

    -- 2. 更新前缀配置的allow_rule_types字段
    UPDATE `dfs_rule_prefix_config` SET `allow_rule_types` = CASE
         WHEN `allow_rule_types` IS NULL THEN var_id
         WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
             THEN CONCAT(`allow_rule_types`, ',', var_id)
         ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN ('1', '2', '4', '5');

    -- 3. 更新自增配置的allow_rule_types字段
    UPDATE `dfs_rule_auto_increment_config` SET `allow_rule_types` = CASE
         WHEN `allow_rule_types` IS NULL THEN var_id
         WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
             THEN CONCAT(`allow_rule_types`, ',', var_id)
         ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN ('day', 'month', 'year', 'noCross');

    -- 4. 插入编码规则实例
    INSERT INTO `dfs_config_number_rules`(`type`, `name`, `prefix_detail`, `create_by`, `update_by`, `create_time`, `update_time`, `is_default`)
    VALUES (var_id, '编码规则', CONCAT(prefix, uuid_val, suffix), 'admin', NULL, nowTime, NULL, 1);

END $$
DELIMITER ;

-- 更新存储过程适应新添加的编码规则，入参改为字符类型
DELIMITER $$
DROP PROCEDURE IF EXISTS init_number_rules_module;
CREATE DEFINER=`root`@`%` PROCEDURE `init_number_rules_module`(in var_id varchar(255), in var_rule_name varchar(255), in var_parent_type_code varchar(255))
top:
BEGIN
    -- 定义变量
    DECLARE type_id varchar(255);
    DECLARE uuid_val CHAR(36);
    DECLARE prefix VARCHAR(500);
    DECLARE suffix VARCHAR(500);
    DECLARE nowTime TIMESTAMP;

    -- 获取当前时间
    SET nowTime = NOW();
    -- 生成UUID
    SELECT UUID() INTO uuid_val;
    -- 设置编码规则JSON模板
    SET prefix = '[{\"code\":\"2\",\"name\":\"当前日期\",\"rule\":\"yyyyMMdd\",\"initValue\":1,\"example\":\"20240813\"},{\"code\":\"4\",\"name\":\"自动生成序号\",\"rule\":\"3\",\"autoIncrementConfigureType\":\"day\",\"uuid\":\"';
    SET suffix = '\",\"initValue\":1,\"example\":\"001\"}]';

    -- 查询是否存在指定的规则类型
    SELECT type INTO type_id FROM dfs_rule_type_config WHERE type = var_id;

    -- 如果存在，则结束存储过程,避免重复刷脚本导致脏数据生成
    IF type_id IS NOT NULL THEN
                LEAVE top;
    END IF;

    -- 1. 插入规则类型配置(没填则默认归属于'其他')
    INSERT INTO `dfs_rule_type_config`(`type`, `module_name`, `create_time`, `update_time`, `parent_type_code`) VALUES (var_id, var_rule_name, nowTime, nowTime, var_parent_type_code);

    -- 2. 更新前缀配置的allow_rule_types字段
    UPDATE `dfs_rule_prefix_config` SET `allow_rule_types` = CASE
        WHEN `allow_rule_types` IS NULL THEN var_id
        WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
            THEN CONCAT(`allow_rule_types`, ',', var_id)
        ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN ('1', '2', '4', '5');

    -- 3. 更新自增配置的allow_rule_types字段
    UPDATE `dfs_rule_auto_increment_config` SET `allow_rule_types` = CASE
         WHEN `allow_rule_types` IS NULL THEN var_id
         WHEN FIND_IN_SET(var_id, `allow_rule_types`) = 0
             THEN CONCAT(`allow_rule_types`, ',', var_id)
         ELSE `allow_rule_types`
    END,
    `update_time` = nowTime WHERE `code` IN ('day', 'month', 'year', 'noCross');

    -- 4. 插入编码规则实例
    INSERT INTO `dfs_config_number_rules`(`type`, `name`, `prefix_detail`, `create_by`, `update_by`, `create_time`, `update_time`, `is_default`)
    VALUES (var_id, '编码规则', CONCAT(prefix, uuid_val, suffix), 'admin', NULL, nowTime, NULL, 1);

END $$
DELIMITER ;

-- 新增能力标签编码规则
call init_number_rules_module('capacityLabel', '能力标签编号', 'BASIC');

-- 由于视图脚本更新但是为重新构建视图，这里需要重新创建离散加工-生产日报的视图
DROP VIEW IF EXISTS dfs_metrics.`v_discreteProcessingProductionSchedule`;
CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`%` SQL SECURITY DEFINER VIEW `v_discreteProcessingProductionSchedule` AS select vdrl.report_date,vdwo.line_name ,vdm.`code`, vdm.`name`, vdm.`standard`,vdm.`drawing_number`,
                                                                                                                           vdrl.`work_order`,vdwo.product_order_number ,vdwo.`sale_order_number`,vdrl.`finish_count`,vdrl.unqualified,
                                                                                                                           vdrl.user_nickname ,vdrl.operator_name,vdrl.shift_type ,vdrl.device_code,vdrl.device_id
                                                                                                                    from dfs_metrics.v_dfs_report_line vdrl,dfs_metrics.v_dfs_work_order vdwo,dfs_metrics.v_dfs_material vdm
                                                                                                                    where vdrl.work_order = vdwo.work_order_number
                                                                                                                      and vdwo.material_code = vdm.code;
-- 产能配置新增删除权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11404080', '删除', 'capacity.list:delete', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'POST', '11404', 2, 1, 1, '/production-config/capacityList', 1, NULL, '', 1);
call init_new_role_permission('11404080');

-- BOM批量删除新增权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('/bom/batch/delete', '批量删除', 'bom:batch.delete', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);
call init_new_role_permission('/bom/batch/delete');

-- 追溯导出
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10205110', '导出_下载默认模板', 'trace.single:export:downDefaultTemplate', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10205120', '导出_上传下载自定义导出模板', 'trace.single:export:downUploadTemplate', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10205', 2, 1, 0, '/trace/single-product', 1, NULL, '', 1);
call init_new_role_permission('10205110');
call init_new_role_permission('10205120');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10208020', '导出_下载默认模板', 'feed.record:export:downDefaultTemplate', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10208', 2, 1, 0, '/trace/feed-record', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10208030', '导出_上传下载自定义导出模板', 'feed.record:export:downUploadTemplate', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10208', 2, 1, 0, '/trace/feed-record', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10208040', '导出_查看日志', 'feed.record:export:log', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '10208', 2, 1, 0, '/trace/feed-record', 1, NULL, '', 1);
call init_new_role_permission('10208020');
call init_new_role_permission('10208030');
call init_new_role_permission('10208040');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('13101100', '导出_下载默认模板', 'material.identification:export:downDefaultTemplate', NULL, NULL, '2023-10-25 02:55:28', NULL, '2025-03-19 09:25:59', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('13101110', '导出_上传下载自定义导出模板', 'material.identification:export:downUploadTemplate', NULL, NULL, '2023-10-25 02:55:28', NULL, '2025-03-19 09:25:59', 'enable', 'GET', '13101', 2, 1, 0, '/identification-center/material-identification', 1, NULL, '', 1);
call init_new_role_permission('13101100');
call init_new_role_permission('13101110');

-- 工单关联生产基本单元表新增冗余的生产基本单元编码字段，方便后续业务处理
call proc_add_column(
        'dfs_work_order_basic_unit_relation',
        'production_basic_unit_code',
        'ALTER TABLE `dfs_work_order_basic_unit_relation` ADD COLUMN `production_basic_unit_code` varchar(255) DEFAULT NULL COMMENT ''生产基本单元编码'' AFTER `production_basic_unit_id`');
-- 兼容历史数据
UPDATE `dfs_work_order_basic_unit_relation` r, `dfs_production_line` l
SET r.`production_basic_unit_code` = l.`production_line_code`
WHERE r.`production_basic_unit_id` = l.`production_line_id`
AND r.`work_center_type` = 'line';

UPDATE `dfs_work_order_basic_unit_relation` r, `dfs_device` d
SET r.`production_basic_unit_code` = d.`device_code`
WHERE r.`production_basic_unit_id` = d.`device_id`
  AND r.`work_center_type` = 'device';

UPDATE `dfs_work_order_basic_unit_relation` r, `sys_team` t
SET r.`production_basic_unit_code` = t.`team_code`
WHERE r.`production_basic_unit_id` = t.`id`
  AND r.`work_center_type` = 'team';
-- 生产订单字段配置
update dfs_form_field_rule_config  set route = '/order-model/product-order'  where route = '/product-management/supplies' and full_path_code = 'productOrder.list';

-- 销售订单字段配置加上是否赠品字段
call proc_add_form_field_module("saleOrder.edit", "isGift", "是否赠品","baseMaterialLineField");
call proc_add_form_field_module("saleOrder.list.material", "isGift", "是否赠品","baseMaterialLineField");
call proc_add_form_field_module("saleOrder.detail", "isGift", "是否赠品","baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/order-model/salesOrder', 'saleOrder.editByCancel', 'saleOrder.edit', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/order-model/salesOrder', 'saleOrder.editByClosed', 'saleOrder.edit', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/order-model/salesOrder', 'saleOrder.editByCreate', 'saleOrder.edit', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/order-model/salesOrder', 'saleOrder.editByFinish', 'saleOrder.edit', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/order-model/salesOrder', 'saleOrder.editByRelease', 'saleOrder.edit', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/order-model/salesOrder', 'saleOrder.list.material', 'saleOrder.list.material', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/order-model/salesOrder', 'saleOrder.detail', 'saleOrder.detail', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");

call proc_add_form_field_module("saleReturn.list.material", "isGift", "是否赠品","baseMaterialLineField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/order-model/sales_returns', 'saleReturn.list.material', 'saleReturn.list.material', 'isGift', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"baseMaterialLineField");

-- 下推wms的下游单据的目标单据状态，都需要支持生效
-- 销售订单下推销售出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="saleOrder.pushDownConfig.saleOut.push" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="saleOrder.pushDownConfig.saleOut.push" AND value_code="targetOrderState";
-- 销售订单下推销售退货入库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="saleOrder.pushDownConfig.salesReturnReceipt.push" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="saleOrder.pushDownConfig.salesReturnReceipt.push" AND value_code="targetOrderState";
-- 销售出货单(出货申请单)下推销售出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="delivery.deliveryApplicationPushDownConfig.saleInAndOut.general" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="delivery.deliveryApplicationPushDownConfig.saleInAndOut.general" AND value_code="targetOrderState";
-- 生产订单下推生产入库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="production.productOrderPushDownConfig.productionIn.push" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="production.productOrderPushDownConfig.productionIn.push" AND value_code="targetOrderState";
-- 生产工单下推生产领料出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.outputPickingProduct.general" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.outputPickingProduct.general" AND value_code="targetOrderState";
-- 生产工单下推生产补料出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.workOrderSupplement.general" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.workOrderSupplement.general" AND value_code="targetOrderState";
-- 生产工单下推生产退料入库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.productionReturnReceipt.general" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.productionReturnReceipt.general" AND value_code="targetOrderState";
-- 生产工单下推生产入库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.productionIn.general" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderPushDownConfig.productionIn.general" AND value_code="targetOrderState";
-- 生产工单用料清单下推领料出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderMaterialsListPushDownConfig.outputPickingProduct.general" AND value_code="targetOrderState";
-- 生产工单用料清单下推补料出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="production.workOrderMaterialsListPushDownConfig.workOrderSupplement.general" AND value_code="targetOrderState";
-- 采购订单下推采购入库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[0,1]' WHERE config_full_path_code="purchase.purchaseOrderPushDownConfig.purchaseIn.push" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[0,1]' WHERE config_full_path_code="purchase.purchaseOrderPushDownConfig.purchaseIn.push" AND value_code="targetOrderState";
-- 委外订单下推委外入库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractOrderPushDownConfig.subcontractIn.pushBatch" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractOrderPushDownConfig.subcontractIn.pushBatch" AND value_code="targetOrderState";
-- 委外收货下推委外入库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch" AND value_code="targetOrderState";
-- 委外订单用料清单下推委外领料出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch" AND value_code="targetOrderState";
-- 委外订单用料清单下推委外补料出库单
UPDATE dfs_order_push_down_config_value_dict SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch" AND value_code="targetOrderState";
UPDATE dfs_order_push_down_config_value SET option_values='[1,2]' WHERE config_full_path_code="subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch" AND value_code="targetOrderState";

