-- 采购需求
UPDATE `dfs_form_field_config` SET `field_name` = '供应商简称'
WHERE `full_path_code` in ('purchaseRequestOrder.list','purchaseRequestOrder.edit','purchaseRequestOrder.detail') AND `field_code` = 'suggestedSupplierShortName' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '供应商地址'
WHERE `full_path_code` in ('purchaseRequestOrder.list','purchaseRequestOrder.edit','purchaseRequestOrder.detail') AND `field_code` = 'suggestedSupplierAddr' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '供应商联系人'
WHERE `full_path_code` in ('purchaseRequestOrder.list','purchaseRequestOrder.edit','purchaseRequestOrder.detail') AND `field_code` = 'suggestedSupplierContacts' AND `type_code` = 'sysField';
UPDATE `dfs_form_field_config` SET `field_name` = '供应商联系方式'
WHERE `full_path_code` in ('purchaseRequestOrder.list','purchaseRequestOrder.edit','purchaseRequestOrder.detail') AND `field_code` = 'suggestedSupplierPhone' AND `type_code` = 'sysField';

-- 采购订单
UPDATE `dfs_form_field_rule_config` SET `is_show` = 0 WHERE `full_path_code` in ('purchaseOrder.list.order','purchaseOrder.list.material','purchaseOrder.detail','purchaseOrder.editByCreate','purchaseOrder.editByRelease','purchaseOrder.editByFinish','purchaseOrder.editByClosed','purchaseOrder.editByCancel') AND `field_code` in ('supplierPhone','supplierAddr');

-- 销售订单
UPDATE `dfs_form_field_config` SET `field_name` = '发货数量'
WHERE `full_path_code` in ('saleOrder.list.material') AND `field_code` = 'appliedQuantity' AND `type_code` = 'sysField';

-- 采购收料单
UPDATE `dfs_form_field_rule_config` SET `is_show` = 0 WHERE `full_path_code` in ('purchaseReceiptOrder.list.order','purchaseReceiptOrder.list.material','purchaseReceiptOrder.detail','purchaseReceiptOrder.editByCreate','purchaseReceiptOrder.editByRelease','purchaseReceiptOrder.editByFinish','purchaseReceiptOrder.editByClosed','purchaseReceiptOrder.editByCancel') AND `field_code` in ('supplierPhone');

-- 委外订单收货单
UPDATE `dfs_form_field_config` SET `field_name` = '供应商联系方式'
WHERE `full_path_code` in ('subcontractReceipt.edit','subcontractReceipt.detail','subcontractReceipt.list') AND `field_code` = 'supplierPhone' AND `type_code` = 'sysField';

-- 生产日汇总-按生产订单
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '对应订单的生产日计划数量求和' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_plan_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单最后一道工序的报工数量or产出工位日过站数量求和' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_produce_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单最后一道工序上报不良or工位机上报不良，且未经维修的数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_unqualified_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '成品日完成数/（成品日完成数+成品日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_qualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应工单的完成数之和' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'produce_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应工单的不良数之和' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'unqualified_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序日完成数/（工序日完成数+工序日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'qualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码去重' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码不去重' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'unqualified_record_item_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码去重' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码不去重' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_unqualified_record_item_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '（工单计划数/生产订单计划数 * 工单单件理论工时）再进行累加' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'single_theory_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '单件理论工时 * 计划数量 再进行累加' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'plan_theory_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '没有被标记为首次不良的成品数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_direct_access_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '成品日不良数/（成品日完成数+成品日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_unqualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '成品日直通数/（成品日完成数+维修报废数）' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_direct_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应工单的直通数之和' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'direct_access_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序日不良数/（工序日完成数+工序日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'unqualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序日直通数/（工序日完成数+维修报废数）' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'direct_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '所有工单的实际工时（投产时长）相加' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'procedure_working_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '所有的工单流转时长相加' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'procedure_circulation_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序流转时长 /(工序流转时长+工序时长) ' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'circulation_hour_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应总的计划数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_plan_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应总的完成数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_produce_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应总的不良数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_unqualified_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码去重' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码不去重' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_unqualified_record_item_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应总的直通数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_direct_access_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应总的合格率' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_qualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应总的不良率' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_unqualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应总的直通率' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_direct_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应工单的计划数量求和' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'plan_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_name` = '成品总不良品记录数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_name` = '成品总不良项记录数量' WHERE `table_name` = 'dfs_metrics_product_order_daily' and `field_code` = 'end_total_unqualified_record_item_quantity';

-- 生产月度汇总-按生产订单
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '（工单计划数/生产订单计划数 * 工单单件理论工时）再进行累加' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'single_theory_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '单件理论工时 * 计划数量 再进行累加' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'plan_theory_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '对应订单的生产日计划数量求和' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_plan_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单最后一道工序的报工数量or产出工位日过站数量求和' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_produce_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单最后一道工序上报不良or工位机上报不良，且未经维修的数量' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_unqualified_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '成品日完成数/（成品日完成数+成品日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_qualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码去重' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码不去重' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_unqualified_record_item_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '没有被标记为首次不良的成品数量' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_direct_access_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '成品日不良数/（成品日完成数+成品日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_unqualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '成品日直通数/（成品日完成数+维修报废数）' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'end_direct_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应工单的完成数之和' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'produce_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应工单的不良数之和' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'unqualified_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序日完成数/（工序日完成数+工序日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'qualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码去重' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码不去重' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'unqualified_record_item_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '订单对应工单的直通数之和' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'direct_access_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序日不良数/（工序日完成数+工序日不良数）' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'unqualified_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序日直通数/（工序日完成数+维修报废数）' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'direct_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '工序流转时长 /(工序流转时长+工序时长) ' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'circulation_hour_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '所有的工单流转时长相加' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'procedure_circulation_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '所有工单的实际工时（投产时长）相加' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'procedure_working_hour';
UPDATE `dfs`.`dfs_table_config` SET `field_name` = '工序不良品记录数量' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_name` = '工序不良项记录数量' WHERE `table_name` = 'dfs_metrics_product_order_monthly' and `field_code` = 'unqualified_record_item_quantity';

-- 销售月度汇总
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '周期内下单的新客户' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'customer_add_count';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '周期内下单的老客户' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'customer_repeat_count';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '周期内下单的客户数' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'customer_active_count';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '新增订单数/活跃客户数' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'average_number_of_order_customer';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '销售物料金额之和' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'all_order_material_amount';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '退货单计数' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'return_count';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '销售产品总数/活跃客户数' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'average_number_plan_order_customer';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '及时交付产品数/（已交付产品数+超期未交产品数）' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'delivered_timely_order_material_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '及时交付订单数/(已交付订单数+延期未交付订单数)' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'delivered_timely_order_rate';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '（新增订单数 - 上个月新增订单数）/ 上个月新增订单数' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'order_qaq';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '（销售产品总数 - 上个月销售产品总数）/ 上个月销售产品总数' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'order_product_qaq';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '前三大客户的物料计划数和/订单产品数' WHERE `table_name` = 'dfs_metrics_sale_order_summary_monthly' and `field_code` = 'proportion_of_three_customers';



-- 生产订单下推采购需求，下推配置增加物料类型过滤
UPDATE `dfs`.`dfs_order_push_down_config_value_dict` SET `option_values` = '[\"purchase\",\"product\",\"outsourcing\"]' WHERE `value_code` = 'filterMaterialSorts' and `value_full_path_code` = 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.filterMaterialSorts';
UPDATE `dfs`.`dfs_order_push_down_config_value` SET `option_values` = '[\"purchase\",\"product\",\"outsourcing\"]' WHERE `value_code` = 'filterMaterialSorts' and `value_full_path_code` = 'production.productOrderPushDownConfig.purchaseRequest.pushBatch.filterMaterialSorts';

-- 采购订单下推采购收料单的配置，应该允许下推出生效单据
UPDATE `dfs`.`dfs_order_push_down_config_value_dict` SET `option_values` = '[1,2]' WHERE `value_code` = 'targetOrderState' and `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.targetOrderState';
UPDATE `dfs`.`dfs_order_push_down_config_value` SET `option_values` = '[1,2]' WHERE `value_code` = 'targetOrderState' and `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.push.targetOrderState';

-- 生产订单不良每日质量
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码去重' WHERE `table_name` = 'dfs_metrics_quality_product_order_defect_daily' and `field_code` = 'unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_remark` = '流水码不去重' WHERE `table_name` = 'dfs_metrics_quality_product_order_defect_daily' and `field_code` = 'unqualified_record_item_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_name` = '不良品记录数量' WHERE `table_name` = 'dfs_metrics_quality_product_order_defect_daily' and `field_code` = 'unqualified_record_quantity';
UPDATE `dfs`.`dfs_table_config` SET `field_name` = '不良项记录数量' WHERE `table_name` = 'dfs_metrics_quality_product_order_defect_daily' and `field_code` = 'unqualified_record_item_quantity';

-- 修改表中文名
update dfs_table_config set table_remark = '生产日汇总-按生产订单' WHERE table_name = 'dfs_metrics_product_order_daily';
update dfs_target_model set target_cnname = '生产日汇总-按生产订单' WHERE target_name = 'productOrderDaily';

update dfs_table_config set table_remark = '生产月度汇总-按生产订单' WHERE table_name = 'dfs_metrics_product_order_monthly';
update dfs_target_model set target_cnname = '生产月度汇总-按生产订单' WHERE target_name = 'productOrderMonthly';

update dfs_table_config set table_remark = '销售月度汇总' WHERE table_name = 'dfs_metrics_sale_order_summary_monthly';
update dfs_target_model set target_cnname = '销售月度汇总' WHERE target_name = 'saleOrderSummaryMonthly';

-- 修改属性指定的指标类型
UPDATE dfs_table_config  SET field_define = 'TARGET' WHERE table_name = 'dfs_metrics_sale_order_summary_monthly' AND field_code in ('average_number_of_order_customer','average_number_plan_order_customer','order_qaq','order_product_qaq','proportion_of_three_customers','all_order_material_amount');