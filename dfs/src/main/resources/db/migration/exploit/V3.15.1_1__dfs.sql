-- DDL
-- 生产工单用料清单新增bom_id、bom即时修订次数
call proc_add_column(
        'dfs_work_order_material_list',
        'bom_id',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `bom_id` int(11) NULL COMMENT ''关联的bomId'';');
call proc_add_column(
        'dfs_work_order_material_list',
        'bom_version_revision',
        'ALTER TABLE `dfs_work_order_material_list` ADD COLUMN `bom_version_revision` int(11) NULL COMMENT ''关联的bom的即时修订次数'';');


-- BOM物料行新增损耗
call proc_add_column(
        'dfs_bom_raw_material',
        'loss_rate',
        'ALTER TABLE `dfs_bom_raw_material` ADD COLUMN `loss_rate` double DEFAULT ''0'' COMMENT ''预估损耗率'' AFTER `fixed_damage`;');

-- 报表订单定时任务表增加接收人
call proc_add_column(
        'dfs_manage_report_task',
        'receiver',
        'ALTER TABLE `dfs_manage_report_task` ADD COLUMN `receiver`  text NULL COMMENT ''接收人，多个逗号分割'' AFTER `data_source`;');

-- 上下游单据关系配置表
CREATE TABLE IF NOT EXISTS `dfs_order_category`(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单据类型',
    `order_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单据名称',
    `application_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序id',
    `detail_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详情路由',
    `request_prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求前缀',
    `is_show` tinyint(1) NULL DEFAULT NULL COMMENT '是否展示',
    `is_sys_data` tinyint(1) NULL DEFAULT NULL COMMENT '是否系统数据',
    `location_x` double(11,4) NULL DEFAULT 1.0 COMMENT 'x轴',
    `location_y` double(11,4) NULL DEFAULT 1.0 COMMENT 'y轴',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `order_category`(`order_category`) USING BTREE,
    INDEX `is_sys_data`(`is_sys_data`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '单据大类表';

CREATE TABLE IF NOT EXISTS `dfs_order_relation`(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `upstream_service` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游所属服务',
    `upstream_order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游单据类型',
    `upstream_order_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游单据名称',
    `downstream_service` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下游所属服务',
    `downstream_order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下游单据类型',
    `downstream_order_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下游单据名称',
    `is_sys_data` tinyint(1) NULL DEFAULT NULL COMMENT '是否系统数据',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `unique`(`upstream_order_category`, `downstream_order_category`) USING BTREE,
    INDEX `upstream_order_category`(`upstream_order_category`) USING BTREE,
    INDEX `downstream_order_category`(`downstream_order_category`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '上下游单据关系配置表';

CREATE TABLE IF NOT EXISTS `dfs_task`  (
    `task_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务id',
    `order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据类型',
    `order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单据编码',
    `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料',
    `material_line_id` int(11) NULL DEFAULT NULL COMMENT '物料行Id',
    `upstream_order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游单据类型',
    `upstream_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上游单据编码',
    `upstream_material_line_id` int(11) NULL DEFAULT NULL COMMENT '上游物料行Id',
    `task_group_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务组类型',
    `task_group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务组',
    `task_superintendent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务人',
    `state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态编码',
    `state_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态名称',
    `target_quantity` decimal(22, 10) NULL DEFAULT NULL COMMENT '目标数量',
    `progress_quantity` decimal(22, 10) NULL DEFAULT NULL COMMENT '进展数量',
    `root_order_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '根单据类型',
    `root_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '根单据编号',
    `root_material_line_id` int(11) NULL DEFAULT NULL COMMENT '根物料行Id',
    `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`task_id`) USING BTREE,
    INDEX `order`(`order_category`, `order_number`, `material_line_id`) USING BTREE,
    INDEX `upstream`(`upstream_order_category`, `upstream_order_number`, `upstream_material_line_id`) USING BTREE,
    INDEX `root`(`root_order_category`, `root_order_number`, `root_material_line_id`) USING BTREE,
    INDEX `group`(`task_group_type`, `task_group`) USING BTREE,
    INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- DML
-- 替代方案的替代策略改为不限制
call proc_modify_column(
        'dfs_replace_scheme',
        'replace_strategy',
        'ALTER TABLE `dfs_replace_scheme` MODIFY COLUMN `replace_strategy` varchar(100)  DEFAULT ''noLimit'' COMMENT ''替代策略（不限制：noLimit）''');

-- 采购收料物料行单价字段列配置
call proc_add_form_field("purchaseReceiptOrder.list.material", "unitPrice", "单价");
call proc_add_form_field("purchaseReceiptOrder.edit", "unitPrice", "单价");
call proc_add_form_field("purchaseReceiptOrder.detail", "unitPrice", "单价");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.list.material', 'purchaseReceiptOrder.list.material', 'unitPrice', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'unitPrice', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'unitPrice', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'unitPrice', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'unitPrice', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'unitPrice', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'unitPrice', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "returnOutQuantity", "已退料出库数");
call proc_add_form_field("purchaseReceiptOrder.detail", "returnOutQuantity", "已退料出库数");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'returnOutQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'returnOutQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'returnOutQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'returnOutQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'returnOutQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'returnOutQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "customerCode", "客户编码");
call proc_add_form_field("purchaseReceiptOrder.detail", "customerCode", "客户编码");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'customerCode', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'customerCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'customerCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'customerCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'customerCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'customerCode', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "customerName", "客户名称");
call proc_add_form_field("purchaseReceiptOrder.detail", "customerName", "客户名称");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'customerName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'customerName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "warehouseStateName", "入库状态");
call proc_add_form_field("purchaseReceiptOrder.detail", "warehouseStateName", "入库状态");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "relateInspectOrder", "关联检验单号");
call proc_add_form_field("purchaseReceiptOrder.detail", "relateInspectOrder", "关联检验单号");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);


call proc_add_form_field("purchaseReceiptOrder.edit", "rejectedQuantity", "判退数量");
call proc_add_form_field("purchaseReceiptOrder.detail", "rejectedQuantity", "判退数量");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'rejectedQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'rejectedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'rejectedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'rejectedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'rejectedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'rejectedQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);


call proc_add_form_field("purchaseReceiptOrder.edit", "concessionAcceptanceQuantity", "让步接收数量");
call proc_add_form_field("purchaseReceiptOrder.detail", "concessionAcceptanceQuantity", "让步接收数量");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "returnQuantity", "退料数量");
call proc_add_form_field("purchaseReceiptOrder.detail", "returnQuantity", "退料数量");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'returnQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'returnQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "warehousingAmount", "入库数量");
call proc_add_form_field("purchaseReceiptOrder.detail", "warehousingAmount", "入库数量");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'warehousingAmount', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "industrialWasteQuantity", "工废数量");
call proc_add_form_field("purchaseReceiptOrder.detail", "industrialWasteQuantity", "工废数量");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "materialWasteQuantity", "料废数量");
call proc_add_form_field("purchaseReceiptOrder.detail", "materialWasteQuantity", "料废数量");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);


call proc_add_form_field("purchaseReceiptOrder.edit", "rejectedQty", "拒收数量");
call proc_add_form_field("purchaseReceiptOrder.detail", "rejectedQty", "拒收数量");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);

call proc_add_form_field("purchaseReceiptOrder.edit", "rejectedReason", "拒收理由");
call proc_add_form_field("purchaseReceiptOrder.detail", "rejectedReason", "拒收理由");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1);


-- 敏感字段权限配置基础数据
INSERT INTO `sys_field_permissions`(`unique_code`, `code`, `default_name`, `custom_name`, `route`, `parent_code`, `is_enable`, `service_name`, `create_by`, `create_time`) VALUES ('purchaseReceipt_unitPrice', 'unitPrice', '单价', NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceipt', 1, 'ams', 'admin', '2024-05-28 11:32:54');
INSERT INTO `sys_field_permissions`(`unique_code`, `code`, `default_name`, `custom_name`, `route`, `parent_code`, `is_enable`, `service_name`, `create_by`, `create_time`) VALUES ('purchaseReceipt', 'purchaseReceipt', '采购收料单', NULL, '/supply-chain-collaboration/procurement-management/delivery-order', null, 1, 'ams', 'admin', '2024-05-28 11:32:54');
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1, `remark` = '该字段和敏感字段配置冲突，请以敏感字段配置为准，此字段置灰，仅允许重命名 (冲突位置：人员信息-->角色权限-->权限分配-->单据敏感字段权限维护)' WHERE `field_code` = 'unitPrice' AND `route` = '/supply-chain-collaboration/procurement-management/delivery-order';

-- 工厂指标的脚本字段改成longtext
call proc_modify_column(
        'dfs_target_model',
        'script',
        'ALTER TABLE `dfs_target_model` MODIFY COLUMN `script` longtext  COMMENT ''执行脚本''');

-- 插入单据关系
INSERT INTO `dfs_order_relation`(`upstream_service`, `upstream_order_category`, `upstream_order_name`, `downstream_service`, `downstream_order_category`, `downstream_order_name`, `is_sys_data`) VALUES
('ams', 'saleOrder', '销售订单', 'ams', 'productOrder', '生产订单', 1),
('ams', 'saleOrder', '销售订单', 'dfs', 'workOrder', '生产工单', 1),
('ams', 'saleOrder', '销售订单', 'ams', 'purchaseRequest', '采购需求单', 1),
('ams', 'saleOrder', '销售订单', 'dfs', 'deliveryApplication', '销售出货单', 1),
('ams', 'saleOrder', '销售订单', 'ams', 'subcontractOrder', '委外订单', 1),
('ams', 'saleOrder', '销售订单', 'ams', 'purchaseOrder', '采购订单', 1),
('ams', 'saleOrder', '销售订单', 'ams', 'saleReturnOrder', '销售退货单', 1),
('ams', 'saleOrder', '销售订单', 'wms', 'salesIssueDoc', '销售出库单', 1),
('ams', 'saleOrder', '销售订单', 'wms', 'salesReturnReceiptDoc', '销售退货入库单', 1),
('dfs', 'deliveryApplication', '销售出货单', 'wms', 'salesIssueDoc', '销售出库单', 1),
('dfs', 'deliveryApplication', '销售出货单', 'wms', 'transferOrder', '调拨单', 1),
('ams', 'productOrder', '生产订单', 'dfs', 'workOrder', '生产工单', 1),
('ams', 'productOrder', '生产订单', 'ams', 'purchaseRequest', '采购需求单', 1),
('ams', 'productOrder', '生产订单', 'ams', 'productMaterialsList', '生产订单用料清单', 1),
('ams', 'productOrder', '生产订单', 'ams', 'subcontractOrder', '委外订单', 1),
('ams', 'productOrder', '生产订单', 'wms', 'workOrderComplete', '生产入库单', 1),
('ams', 'productMaterialsList', '生产订单用料清单', 'wms', 'outputPickingProduct', '生产领料出库单', 1),
('ams', 'productMaterialsList', '生产订单用料清单', 'wms', 'workOrderSupplement', '生产补料出库单', 1),
('ams', 'productMaterialsList', '生产订单用料清单', 'wms', 'transferOrder', '调拨单', 1),
('dfs', 'workOrder', '生产工单', 'dfs', 'workOrderMaterialList', '生产工单用料清单', 1),
('dfs', 'workOrder', '生产工单', 'wms', 'outputPickingProduct', '生产领料出库单', 1),
('dfs', 'workOrder', '生产工单', 'wms', 'workOrderSupplement', '生产补料出库单', 1),
('dfs', 'workOrder', '生产工单', 'wms', 'applicationReturn', '生产退料入库单', 1),
('dfs', 'workOrder', '生产工单', 'wms', 'workOrderComplete', '生产入库单', 1),
('dfs', 'workOrderMaterialList', '生产工单用料清单', 'wms', 'outputPickingProduct', '生产领料出库单', 1),
('dfs', 'workOrderMaterialList', '生产工单用料清单', 'wms', 'workOrderSupplement', '生产补料出库单', 1),
('ams', 'purchaseRequest', '采购需求单', 'ams', 'purchaseOrder', '采购订单', 1),
('ams', 'purchaseOrder', '采购订单', 'ams', 'purchaseReceipt', '采购收料', 1),
('ams', 'purchaseOrder', '采购订单', 'wms', 'purchaseIn', '采购入库单', 1),
('ams', 'purchaseReceipt', '采购收料', 'wms', 'purchaseIn', '采购入库单', 1),
('ams', 'purchaseReceipt', '采购收料', 'wms', 'returnOrder', '采购退料', 1),
('ams', 'purchaseReceipt', '采购收料', 'qms', 'inspectOrder', '检验单', 1),
('wms', 'salesIssueDoc', '销售出库单', 'wms', 'salesReturnReceiptDoc', '销售退货入库单', 1),
('wms', 'purchaseIn', '采购入库单', 'wms', 'purchaseReturnOut', '采购退料出库单', 1),
('wms', 'subcontractProductOutbound', '委外领料出库单', 'wms', 'subcontractReturnReceipt', '委外退料入库单', 1),
('wms', 'subcontractSupplementaryFood', '委外补料出库单', 'wms', 'subcontractReturnReceipt', '委外退料入库单', 1),
('wms', 'stockOtherAppForm', '其他出库申请单', 'wms', 'transferOrder', '调拨单', 1);

INSERT INTO `dfs_order_category`(`order_category`, `order_name`, `application_id`, `detail_path`, is_show, `is_sys_data`, `location_x`, `location_y`) VALUES
('saleOrder', '销售订单', 'new.yk.genieos.ams.order-model.salesOrder', '', 1, 1, 100.0000, 100.0000),
('deliveryApplication', '销售出货单', 'new.yk.genieos.dfs.scc.order-model.shipment_application', '', 1, 1, 200.0000, 300.0000),
('saleReturnOrder', '销售退货单', 'new.yk.genieos.ams.scc.order-model.sales_returns', '', 1, 1, 200.0000, 400.0000),
('productOrder', '生产订单', 'new.yk.genieos.ams.order-model.product-order', '', 1, 1, 200.0000, 100.0000),
('productMaterialsList', '生产订单用料清单', 'new.yk.genieos.ams.order-model.production-materials', '', 1, 1, 300.0000, 0.0000),
('purchaseRequest', '采购需求单', 'new.yk.genieos.ams.scc.procurement-management.purchasing-demand', '', 1, 1, 200.0000, 200.0000),
('purchaseOrder', '采购订单', 'new.yk.genieos.ams.scc.procurement-management.purchasing-list', '', 1, 1, 300.0000, 200.0000),
('purchaseReceipt', '采购收料', 'new.yk.genieos.ams.scc.procurement-management.delivery-order', '', 1, 1, 400.0000, 200.0000),
('returnOrder', '采购退料', 'new.yk.genieos.ams.scc.procurement-management.return-order', '', 1, 1, 500.0000, 200.0000),
('subcontractOrder', '委外订单', 'new.yk.genieos.ams.scc.osm.outsourcingOrder', '', 1, 1, 200.0000, 0.0000),
('workOrder', '生产工单', 'new.yk.genieos.dfs.order-model.production-workorder', '', 1, 1, 300.0000, 100.0000),
('workOrderMaterialList', '生产工单用料清单', 'new.yk.genieos.dfs.order-model.workOrder-materials', '', 1, 1, 400.0000, 100.0000),
('inspectOrder', '检验单', 'new.yk.genieos.qms3.0.inspection-manage.qpi.productInspection', '', 1, 1, 500.0000, 300.0000),
('salesIssueDoc', '销售出库单', 'com.luotu.wms.salesIssueReceipt.salesIssueDoc', '', 1, 1, 100.0000, 300.0000),
('salesReturnReceiptDoc', '销售退货入库单', 'com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc', '', 1, 1, 0.0000, 300.0000),
('workOrderComplete', '生产入库单', 'com.luotu.wms.productInOrOut.workOrderComplete', '', 1, 1, 250.0000, -100.0000),
('outputPickingProduct', '生产领料出库单', 'com.luotu.wms.productInOrOut.workOrderTakeOut', '', 1, 1, 500.0000, 0.0000),
('workOrderSupplement', '生产补料出库单', 'com.luotu.wms.productInOrOut.workOrderSupplement', '', 1, 1, 500.0000, -100.0000),
('applicationReturn', '生产退料入库单', 'com.luotu.wms.productInOrOut.applicationReturn', '', 1, 1, 500.0000, -200.0000),
('purchaseIn', '采购入库单', 'com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage', '', 1, 1, 400.0000, 300.0000),
('purchaseReturnOut', '采购退料出库单', 'com.luotu.wms.purchaseOutOfStorage.purchaseReturnOutOfStorage', '', 1, 1, 400.0000, 400.0000),
('subcontractProductOutbound', '委外领料出库单', 'com.luotu.wms.subcontracting.outstoreMaterail', '', 1, 1, 100.0000, -100.0000),
('subcontractSupplementaryFood', '委外补料出库单', 'com.luotu.wms.subcontracting.repairMaterail', '', 1, 1, 0.0000, 0.0000),
('subcontractReturnReceipt', '委外退料入库单', 'com.luotu.wms.subcontracting.refundMaterail', '', 1, 1, 100.0000, 0.0000),
('transferOrder', '调拨单', 'com.luotu.wms.cougnyManage.allocation', '', 1, 1, 300.0000, 300.0000),
('stockOtherAppForm', '其他出库申请单', 'com.luotu.wms.mixReceive.issueRequisition', '', 1, 1, 300.0000, 400.0000);









-- 委外退料单审批配置
INSERT INTO `dfs_approve_config`( `code`, `name`, `is_approve`, `update_by`, `update_time`) VALUES ( 'subcontractReturnOrder', '委外订单退料单', 0, 'admin', null);
-- 委外订单下推委外收货单配置
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontract', '委外模块', 'subcontract', '', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractOrderPushDownConfig', '委外订单下推配置', 'subcontract.subcontractOrderPushDownConfig', 'subcontract', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractReceiptOrder', '委外收货单', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder', 'subcontract.subcontractOrderPushDownConfig', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder',  '下推委外收货单', 'RULE', 1, 1, 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', '通用下推', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.enable', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.description', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.targetOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.url', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReceiptOrder\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.enable', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.description', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.targetOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.url', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReceiptOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractOrderPushDownConfig.subcontractReceiptOrder.pushBatch';

-- 委外订单下推委外领料出库配置
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractProductOutbound', '委外领料出库单', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound', 'subcontract.subcontractOrderPushDownConfig', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound',  '下推委外领料出库单', 'RULE', 1, 1, 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', '通用下推', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.enable', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.description', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.outstoreMaterail\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.url', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/outstoreMaterail/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductOutbound\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.enable', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.description', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.outstoreMaterail\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.url', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/outstoreMaterail/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductOutbound\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractOrderPushDownConfig.subcontractProductOutbound.pushBatch';

-- 委外收货单下推委外退货配置
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractReceiptOrderPushDownConfig', '委外收货下推配置', 'subcontract.subcontractReceiptOrderPushDownConfig', 'subcontract', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractReturnOrder', '委外退货单', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder', 'subcontract.subcontractReceiptOrderPushDownConfig', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder',  '下推委外退货单', 'RULE', 1, 1, 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', '通用下推', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.enable', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.originalOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select-multiple', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.description', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.targetOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'api', '/ams/subcontract/return/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.pushDownAgain', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingReturnOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.url', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.sourceOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.targetOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReturnOrder\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.enable', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.originalOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select-multiple', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.description', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.targetOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'api', '/ams/subcontract/return/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.pushDownAgain', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingReturnOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.url', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.sourceOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch.targetOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReturnOrder\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractReturnOrder.pushBatch';

-- 委外订单收货单下推委外入库单
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractIn', '委外入库单', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn', 'subcontract.subcontractReceiptOrderPushDownConfig', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn',  '下推委外入库单', 'RULE', 1, 1, 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', '通用下推', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.enable', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.originalOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select-multiple', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.description', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.targetOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select', 'api', '/ams/subcontract/return/state', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.order\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.url', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/order/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.sourceOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.targetOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductReceipt\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.enable', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.originalOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select-multiple', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.description', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.targetOrderStates', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'select', 'api', '/ams/subcontract/return/state', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.applicationId', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.order\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.url', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/order/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.sourceOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractReceiptOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch.targetOrderType', 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductReceipt\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractReceiptOrderPushDownConfig.subcontractIn.pushBatch';


-- 委外订单用料清单下推委外领料出库单配置
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractMaterialsListPushDownConfig', '委外订单用料清单下推配置', 'subcontract.subcontractMaterialsListPushDownConfig', 'subcontract', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractProductOutbound', '委外领料出库单', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound', 'subcontract.subcontractMaterialsListPushDownConfig', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound',  '下推领料出库单', 'RULE', 1, 1, 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', '通用下推', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.enable', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.originalOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select-multiple', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.description', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.outstoreMaterail\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.url', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/outstoreMaterail/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.sourceOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductOutbound\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.enable', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.originalOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select-multiple', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.description', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.outstoreMaterail\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.url', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/outstoreMaterail/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.sourceOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch.targetOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractProductOutbound\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractMaterialsListPushDownConfig.subcontractProductOutbound.pushBatch';

-- 委外订单用料清单下推委外补料出库单配置
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractSupplyOutbound', '委外补料出库单', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound', 'subcontract.subcontractMaterialsListPushDownConfig', NULL, 1, 0);
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound',  '下推委外补料出库单', 'RULE', 1, 1, 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', '通用下推', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.enable', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.originalOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select-multiple', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.description', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.targetOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.repairMaterail\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.url', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/repairMaterail/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.sourceOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.targetOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractSupplyOutbound\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'enable', '是否启用', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.enable', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.originalOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select-multiple', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[2,3]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.isSupportMaterialLineReversePushDown', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.description', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.targetOrderStates', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'select', 'api', '/ams/subcontract/receipts/states', 'get', NULL, 'code,name', '[1]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'applicationId', 'applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.applicationId', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.luotu.wms.subcontracting.repairMaterail\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.url', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/wms/warehousingDistribution/subcontracting/repairMaterail/add\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.sourceOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractMaterialsList\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch.targetOrderType', 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractSupplyOutbound\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractMaterialsListPushDownConfig.subcontractSupplyOutbound.pushBatch';


-- 委外订单下推委外发料单
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractDeliveryOrder', '委外发料单', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder', 'subcontract.subcontractOrderPushDownConfig', NULL, 1, 0);
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', '通用下推', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.description', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.targetOrderState', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select', 'api', '/ams/subcontract/delivery/states', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingIssueMaterial\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.url', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial?pushDownOrigin=subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractDeliveryOrder\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder',  '下推委外发料单', 'RULE', 1, 1, 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.description', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.targetOrderState', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select', 'api', '/ams/subcontract/delivery/states', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingIssueMaterial\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.url', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial?pushDownOrigin=subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractDeliveryOrder\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.push';

-- 委外订单下推委外订单用料清单
INSERT INTO `dfs_order_push_down_config` (`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `is_inner`, `is_show`) VALUES ('subcontractOrderMaterial', '委外订单用料清单', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial', 'subcontract.subcontractOrderPushDownConfig', NULL, 1, 0);
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', '通用下推', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial', 'show');
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('custom', '自定义', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.description', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.targetOrderState', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'materialChoose', '用料类型', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.materialChoose', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"bomMaterial\",\"label\":\"BOM物料\"},{\"value\":\"procedureMaterialUsed\",\"label\":\"工序用料\"}]', '\"bomMaterial\"', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.bomSplitTypes', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"multiLevelBomNotFilter\"', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'filterMaterialSorts', '物料分类过滤', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.filterMaterialSorts', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"product\",\"outsourcing\"]', '[]', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'filterMaterialTypes', '物料类型过滤', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.filterMaterialTypes', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingMaterialOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.url', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder?pushDownOrigin=subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrderMaterial\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial',  '下推委外订单用料清单', 'RULE', 1, 1, 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '源单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '反选物料行', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'description', '描述', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.description', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.targetOrderState', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'api', '/ams/subcontract/materials/states', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'materialChoose', '用料类型', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.materialChoose', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"bomMaterial\",\"label\":\"BOM物料\"},{\"value\":\"procedureMaterialUsed\",\"label\":\"工序用料\"}]', '\"bomMaterial\"', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.bomSplitTypes', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"multiLevelBomNotFilter\"', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'filterMaterialSorts', '物料分类过滤', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.filterMaterialSorts', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"product\",\"outsourcing\"]', '[]', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'filterMaterialTypes', '物料类型过滤', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.filterMaterialTypes', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingMaterialOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.url', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder?pushDownOrigin=subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrderMaterial\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractOrderPushDownConfig.subcontractOrderMaterial.push';

-- 删除内置下推配置"下推销售出库单(新建页签)"
DELETE FROM dfs_order_push_down_item WHERE instance_type = "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut";

-- 委外发料单、委外订单退货单、委外订单用料清单 设置全局扩展字段配置
INSERT INTO `dfs_form_up_down_stream_relationship_config` (`upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES ('ams', 'subcontractOrder', '委外订单', 'ams', 'subcontractDeliveryOrder', '委外发料单');
INSERT INTO `dfs_form_up_down_stream_relationship_config` (`upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES ('ams', 'subcontractOrder', '委外订单', 'ams', 'subcontractReturn', '委外订单退货单');
INSERT INTO `dfs_form_up_down_stream_relationship_config` (`upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES ('ams', 'subcontractOrder', '委外订单', 'ams', 'subcontractOrderMaterial', '委外订单用料清单');
INSERT INTO `dfs_form_up_down_stream_relationship_config` (`upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES ('ams', 'subcontractOrder', '委外订单', 'ams', 'subcontractReceiptOrder', '委外订单收货单');
INSERT INTO `dfs_form_up_down_stream_relationship_config` (`upstream_service`, `upstream_order_type`, `upstream_order_name`, `downstream_service`, `downstream_order_type`, `downstream_order_name`) VALUES ('ams', 'subcontractReceiptOrder', '委外订单收货单', 'ams', 'subcontractReturn', '委外订单退货单');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldOne', '委外发料单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldTwo', '委外发料单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldThree', '委外发料单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldFour', '委外发料单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldFive', '委外发料单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldSix', '委外发料单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldSeven', '委外发料单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldEight', '委外发料单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldNine', '委外发料单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderExtendFieldTen', '委外发料单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldOne', '委外发料单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldTwo', '委外发料单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldThree', '委外发料单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldFour', '委外发料单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldFive', '委外发料单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldSix', '委外发料单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldSeven', '委外发料单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldEight', '委外发料单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldNine', '委外发料单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial', 'subcontractDeliveryOrder', 'orderMaterialExtendFieldTen', '委外发料单物料扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldOne', '委外退货单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldTwo', '委外退货单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldThree', '委外退货单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldFour', '委外退货单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldFive', '委外退货单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldSix', '委外退货单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldSeven', '委外退货单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldEight', '委外退货单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldNine', '委外退货单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderExtendFieldTen', '委外退货单扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldOne', '委外退货单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldTwo', '委外退货单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldThree', '委外退货单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldFour', '委外退货单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldFive', '委外退货单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldSix', '委外退货单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldSeven', '委外退货单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldEight', '委外退货单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldNine', '委外退货单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReturnOrder', 'subcontractReturn', 'orderMaterialExtendFieldTen', '委外退货单物料扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldOne', '单据扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldTwo', '单据扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldThree', '单据扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldFour', '单据扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldFive', '单据扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldSix', '单据扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldSeven', '单据扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldEight', '单据扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldNine', '单据扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderExtendFieldTen', '单据扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldOne', '物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldTwo', '物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldThree', '物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldFour', '物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldFive', '物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldSix', '物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldSeven', '物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldEight', '物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldNine', '物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineExtendFieldTen', '物料扩展字段10');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldOne', '子物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldTwo', '子物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldThree', '子物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldFour', '子物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldFive', '子物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldSix', '子物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldSeven', '子物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldEight', '子物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldNine', '子物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingMaterialOrder', 'subcontractOrderMaterial', 'orderLineMaterialExtendFieldTen', '子物料扩展字段10');

INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldOne', '委外收货单扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldTwo', '委外收货单扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldThree', '委外收货单扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldFour', '委外收货单扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldFive', '委外收货单扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldSix', '委外收货单扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldSeven', '委外收货单扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldEight', '委外收货单扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldNine', '委外收货单扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderExtendFieldTen', '委外收货单扩展字段0');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldOne', '委外收货单物料扩展字段1');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldTwo', '委外收货单物料扩展字段2');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldThree', '委外收货单物料扩展字段3');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldFour', '委外收货单物料扩展字段4');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldFive', '委外收货单物料扩展字段5');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldSix', '委外收货单物料扩展字段6');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldSeven', '委外收货单物料扩展字段7');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldEight', '委外收货单物料扩展字段8');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldNine', '委外收货单物料扩展字段9');
INSERT INTO `dfs_form_overall_allow_field_config` (`route`, `order_type`, `field_code`, `field_name`) VALUES ('/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceiptOrder', 'orderMaterialExtendFieldTen', '委外收货单物料扩展字段0');

-- 全局表单配置字段新增公式字段
call proc_add_column(
        'dfs_form_overall_field_config',
        'formula',
        'ALTER TABLE `dfs_form_overall_field_config` ADD COLUMN `formula` varchar(2000) DEFAULT NULL COMMENT ''取值公式'';');

-- 物料扩展字段新增取值配置对应的字段，并将表单配置中的配置同步到新增字段中
call proc_add_column(
        'dfs_material_config_field',
        'input_type',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `input_type` varchar(255) DEFAULT NULL COMMENT ''输入框类型，单选，多选，输入框，文本域，json框，文本'';');
call proc_add_column(
        'dfs_material_config_field',
        'option_values_type',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `option_values_type` varchar(50) DEFAULT NULL COMMENT ''可选值来源类型'';');
call proc_add_column(
        'dfs_material_config_field',
        'related_api_code',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `related_api_code` varchar(255) DEFAULT NULL COMMENT ''关联API编码'';');
call proc_add_column(
        'dfs_material_config_field',
        'all_option',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `all_option` text COMMENT ''全选值'';');
call proc_add_column(
        'dfs_material_config_field',
        'formula',
        'ALTER TABLE `dfs_material_config_field` ADD COLUMN `formula` varchar(2000) DEFAULT NULL COMMENT ''取值公式'';');

call proc_add_column(
        'dfs_material_type_config_field',
        'input_type',
        'ALTER TABLE `dfs_material_type_config_field` ADD COLUMN `input_type` varchar(255) DEFAULT NULL COMMENT ''输入框类型，单选，多选，输入框，文本域，json框，文本'';');
call proc_add_column(
        'dfs_material_type_config_field',
        'option_values_type',
        'ALTER TABLE `dfs_material_type_config_field` ADD COLUMN `option_values_type` varchar(50) DEFAULT NULL COMMENT ''可选值来源类型'';');
call proc_add_column(
        'dfs_material_type_config_field',
        'related_api_code',
        'ALTER TABLE `dfs_material_type_config_field` ADD COLUMN `related_api_code` varchar(255) DEFAULT NULL COMMENT ''关联API编码'';');
call proc_add_column(
        'dfs_material_type_config_field',
        'all_option',
        'ALTER TABLE `dfs_material_type_config_field` ADD COLUMN `all_option` text COMMENT ''全选值'';');
call proc_add_column(
        'dfs_material_type_config_field',
        'formula',
        'ALTER TABLE `dfs_material_type_config_field` ADD COLUMN `formula` varchar(2000) DEFAULT NULL COMMENT ''取值公式'';');

UPDATE `dfs_material_config_field` mcf, `dfs_form_field_rule_config` fuc
SET mcf.`input_type` = fuc.`input_type`, mcf.`option_values_type` = fuc.`option_values_type`, mcf.`related_api_code` = fuc.`related_api_code`,
    mcf.`all_option` = fuc.`all_option`, mcf.`formula` = fuc.`formula`
WHERE fuc.`full_path_code` = 'material.editByRelease'
AND mcf.`field_code` = fuc.`field_code`;

UPDATE `dfs_material_type_config_field` mcf, `dfs_material_config_field` fuc
SET mcf.`input_type` = fuc.`input_type`, mcf.`option_values_type` = fuc.`option_values_type`, mcf.`related_api_code` = fuc.`related_api_code`,
    mcf.`all_option` = fuc.`all_option`, mcf.`formula` = fuc.`formula`
WHERE mcf.`field_code` = fuc.`field_code`;

-- 删除表单配置中物料的扩展字段，同时新增tab页用来展示物料扩展字段
DELETE FROM  `dfs_form_field_rule_config`
WHERE `field_code` in ('customFieldOne','customFieldTwo','customFieldThree','customFieldFour','customFieldFive','customFieldSix',
                       'customFieldSeven','customFieldEight','customFieldNine','customFieldTen','customFieldEleven','remark','standard',
                       'drawingNumber','materialPrice','loseRate','rawMaterial','nameEnglish','minimumProductionLot','factoryModel',
                       'customFieldOneName','customFieldTwoName','customFieldThreeName','customFieldFourName','customFieldFiveName',
                       'customFieldSixName','customFieldSevenName','customFieldEightName','customFieldNineName','customFieldTenName','customFieldElevenName')
AND `route` = '/product-management/supplies';

DELETE FROM `dfs_form_field_config`
WHERE `field_code` in ('customFieldOne','customFieldTwo','customFieldThree','customFieldFour','customFieldFive','customFieldSix',
                       'customFieldSeven','customFieldEight','customFieldNine','customFieldTen','customFieldEleven','remark','standard',
                       'drawingNumber','materialPrice','loseRate','rawMaterial','nameEnglish','minimumProductionLot','factoryModel',
                       'customFieldOneName','customFieldTwoName','customFieldThreeName','customFieldFourName','customFieldFiveName',
                       'customFieldSixName','customFieldSevenName','customFieldEightName','customFieldNineName','customFieldTenName','customFieldElevenName')
AND `full_path_code` in ('material.list','material.edit', 'material.detail');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`)
VALUES ('material.list', 'material.list', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`)
VALUES ('material.editByCreate', 'material.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`)
VALUES ('material.editByRelease', 'material.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`)
VALUES ('material.editByStopUsing', 'material.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`)
VALUES ('material.editByAbandon', 'material.edit', 'materialExtendField', '物料扩展字段', 0, 0);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `have_save_button`, `have_add_button`)
VALUES ('material.detail', 'material.detail', 'materialExtendField', '物料扩展字段', 0, 0);

-- 删除软参
DELETE FROM `dfs_business_config` WHERE `full_path_code` in ('production.productMaterialsList.listConfig','production.productMaterialsList.listConfig.listDisplayFormat','production.workerMaterialsList.listConfig','production.workerMaterialsList.listConfig.listDisplayFormat');
DELETE FROM `dfs_business_config_value` WHERE `config_full_path_code` in ('production.productMaterialsList.listConfig','production.productMaterialsList.listConfig.listDisplayFormat','production.workerMaterialsList.listConfig','production.workerMaterialsList.listConfig.listDisplayFormat');

-- 软参中的bom清单算法优化
UPDATE `dfs_business_config_value` SET `option_values` = '[{\"value\":\"1*(1+lossRate)\",\"label\":\"1*(1+损耗率%)+固定损耗\"},{\"value\":\"1/(1-lossRate)\",\"label\":\"1/(1-损耗率%)+固定损耗\"}]'
WHERE `value_code` = 'lossRateDealMethod';

call proc_modify_column(
        'dfs_report_line',
        'operator_name',
        'ALTER TABLE `dfs_report_line` MODIFY COLUMN `operator_name` varchar(255)  NULL DEFAULT NULL COMMENT ''操作员姓名''');

-- 表单配置的销售出货单 重命名为 出货申请单
UPDATE `dfs_form_config` SET `name` = '出货申请单' WHERE `full_path_code` = 'deliveryApplication';
UPDATE `dfs_form_config` SET `name` = '出货申请单列表页' WHERE `full_path_code` = 'deliveryApplication.list.material';
UPDATE `dfs_form_config` SET `name` = '出货申请单编辑页' WHERE `full_path_code` = 'deliveryApplication.edit';
UPDATE `dfs_form_config` SET `name` = '出货申请单详情' WHERE `full_path_code` = 'deliveryApplication.detail';

update dfs_form_field_config set `field_name` = REPLACE(`field_name`,'销售出货单扩展字段','出货申请单扩展字段');
update dfs_form_field_config set `field_name` = REPLACE(`field_name`,'销售出货单物料扩展字段','出货申请单物料扩展字段');

-- 物料的扩展字段默认为人工输入
UPDATE `dfs_material_type_config_field` SET `input_type` = 'input' WHERE `input_type` is null;
UPDATE `dfs_material_config_field` SET `input_type` = 'input' WHERE `input_type` is null;

-- API重定向的接口调整
UPDATE `dfs_api_transform` SET `url` = 'http://127.0.0.1:8080/factory/v1/ams/v1/open/sale_orders/simple/order/list', `req_body_script` = 'function transform(){
    return {
    "size": 2,
    "current": 1,
    "states": "2",
    "showType": "order",
    "isShowSimpleInfo" : true
    };
}'
WHERE `name` = '获取销售订单列表';
UPDATE `dfs_api_transform` SET `url` = 'http://127.0.0.1:8080/factory/v1/ams/v1/open/product_orders/list', `req_body_script` = 'function transform(){
    return {
    "size": 10,
    "current": 1,
    "states": "2,3",
    "isShowSimpleInfo": true
    };
}'
WHERE `name` = '获取生产订单列表';
UPDATE `dfs_api_transform` SET `url` = 'http://127.0.0.1:8080/factory/v1/api/v1/open/materials/list', `req_body_script` = 'function transform(){
    return {
    "size": 10,
    "current": 1,
    "states": "2",
    "isShowSimpleInfo" : true
    };
}'
WHERE `name` = '获取物料列表';
