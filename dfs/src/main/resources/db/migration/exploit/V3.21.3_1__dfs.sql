-- BOM新增`生产状态`字段
call proc_add_column(
        'dfs_bom',
        'production_state',
        'ALTER TABLE `dfs_bom` ADD COLUMN `production_state` varchar(255) DEFAULT ''massProduction'' COMMENT ''生产状态(testProduction-试产,massProduction-量产)''');
-- 工艺新增`生产状态`字段
call proc_add_column(
        'dfs_craft',
        'production_state',
        'ALTER TABLE `dfs_craft` ADD COLUMN `production_state` varchar(255) DEFAULT ''massProduction'' COMMENT ''生产状态(testProduction-试产,massProduction-量产)''');

-- 新增V2可查询字段
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('bom', 'productionState', '生产状态', 'production_state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('craft', 'productionState', '生产状态', 'production_state');

-- 生产订单业务类型、单据类型新增 '试产订单' 类型
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('testProductOrder', '试产订单', 'productOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('testProductOrder', 'testProductOrder', '试产订单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');


-- 生产工单业务类型、单据类型新增 '试产工单' 类型
INSERT INTO `dfs_order_type_config`(`code`, `name`, `related_category_code`, `description`, `is_default`, `create_by`, `update_by`, `create_time`, `update_time`, `is_inner`) VALUES ('testWorkOrder', '试产工单', 'workOrder', NULL, 0, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30', 1);
INSERT INTO `dfs_order_type_item`(`business_type_code`, `code`, `name`, `enable`, `sort`, `is_default`, `is_inner`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES ('testWorkOrder', 'testWorkOrder', '试产工单', 1, 1, 1, 1, NULL, 'admin', 'admin', '2024-12-02 11:27:30', '2024-12-02 11:27:30');


-- 表单配置--生产工单用料清单新增 bom编码、bom版本
call proc_add_form_field("workOrderMaterialList.list.material", "bomNum", "BOM编码");
call proc_add_form_field("workOrderMaterialList.list.material", "bomVersion", "BOM版本");
call proc_add_form_field("workOrderMaterialList.list.order", "bomNum", "BOM编码");
call proc_add_form_field("workOrderMaterialList.list.order", "bomVersion", "BOM版本");
call proc_add_form_field("workOrderMaterialList.edit", "bomNum", "BOM编码");
call proc_add_form_field("workOrderMaterialList.edit", "bomVersion", "BOM版本");
call proc_add_form_field("workOrderMaterialList.detail", "bomNum", "BOM编码");
call proc_add_form_field("workOrderMaterialList.detail", "bomVersion", "BOM版本");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.order', 'workOrderMaterialList.list.order', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'bomNum', 1, 0, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCreate', 'workOrderMaterialList.edit', 'bomVersion', 1, 0, 1, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByRelease', 'workOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByFinish', 'workOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByClosed', 'workOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.editByCancel', 'workOrderMaterialList.edit', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'bomNum', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/order-model/workOrder-materials', 'workOrderMaterialList.detail', 'workOrderMaterialList.detail', 'bomVersion', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
