-- DDL
-- 编码规则类型表新增`类型编码`字段。1、用于判断编码规则是否加1使用2、用来解释历史类型编码type所代表的含义
call proc_add_column(
        'dfs_rule_type_config',
        'type_code',
        'ALTER TABLE `dfs_rule_type_config` ADD COLUMN `type_code` varchar(255) DEFAULT NULL COMMENT ''类型编码(1、用于判断编码规则是否加1使用2、用来解释历史类型编码type所代表的含义)''');



UPDATE dfs_rule_type_config SET type_code = 'saleOrderNumber' WHERE `type` = '1';
UPDATE dfs_rule_type_config SET type_code = 'productOrderNumber' WHERE `type` = '10';
UPDATE dfs_rule_type_config SET type_code = 'workOrderNumber' WHERE `type` = '2';
UPDATE dfs_rule_type_config SET type_code = 'productBarCode' WHERE `type` = '5';
UPDATE dfs_rule_type_config SET type_code = 'productFlowCode' WHERE `type` = '12';
UPDATE dfs_rule_type_config SET type_code = 'orderProductCode' WHERE `type` = '57';
UPDATE dfs_rule_type_config SET type_code = 'finishedProductCode' WHERE `type` = '14';
UPDATE dfs_rule_type_config SET type_code = 'packageProductCode' WHERE `type` = '61';
UPDATE dfs_rule_type_config SET type_code = 'packageCode' WHERE `type` = '87';
UPDATE dfs_rule_type_config SET type_code = 'purchaseReceiptBatch' WHERE `type` = '11';
UPDATE dfs_rule_type_config SET type_code = 'purchaseSingleProductCode' WHERE `type` = '22';
UPDATE dfs_rule_type_config SET type_code = 'material' WHERE `type` = '82';
-- UPDATE dfs_rule_type_config SET type_code = 'customerCode' WHERE `type` = '78';
-- UPDATE dfs_rule_type_config SET type_code = 'supplierCode' WHERE `type` = '3';
-- UPDATE dfs_rule_type_config SET type_code = 'packageOrderNumber' WHERE `type` = '86';
UPDATE dfs_rule_type_config SET type_code = 'batteryCode' WHERE `type` = '97';
UPDATE dfs_rule_type_config SET type_code = 'bomVersion' WHERE `type` = '105';
UPDATE dfs_rule_type_config SET type_code = 'craftVersion' WHERE `type` = '106';
-- UPDATE dfs_rule_type_config SET type_code = 'defectCode' WHERE `type` = '102';
-- UPDATE dfs_rule_type_config SET type_code = 'maintainCode' WHERE `type` = '103';
-- UPDATE dfs_rule_type_config SET type_code = 'defectTypeCode' WHERE `type` = '88';
-- UPDATE dfs_rule_type_config SET type_code = 'workOrderMaterialList' WHERE `type` = '77';
-- UPDATE dfs_rule_type_config SET type_code = 'bomCode' WHERE `type` = '4';
-- UPDATE dfs_rule_type_config SET type_code = 'craftCode' WHERE `type` = '26';
-- UPDATE dfs_rule_type_config SET type_code = 'replaceSchemeCode' WHERE `type` = '56';
-- UPDATE dfs_rule_type_config SET type_code = 'auxiliaryAttrCode' WHERE `type` = '58';
-- UPDATE dfs_rule_type_config SET type_code = 'procedureDefCode' WHERE `type` = '98';
-- UPDATE dfs_rule_type_config SET type_code = 'packageSchemeCode' WHERE `type` = '60';
-- UPDATE dfs_rule_type_config SET type_code = 'capacityLabel' WHERE `type` = 'capacityLabel';

call proc_add_column(
        'dfs_target_model',
        'sdk_module',
        'ALTER TABLE `dfs_target_model` ADD COLUMN `sdk_module` varchar(255) NULL COMMENT ''sdk所属模块'';');

-- 默认新增工单发送消息只包含物料
CALL conditional_execute(
    'SELECT COUNT(1) FROM dfs_operation_log',
    'UPDATE dfs_business_config_value SET `value` = ''["material"]'' where value_code = ''addMsgContent'' and value_full_path_code = ''production.workOrderConfig.workOrderEventConfig.addMsgContent'''
);

-- 公司信息表调整字符长度
call proc_modify_column(
        'dfs_company',
        'cname',
        'ALTER TABLE `dfs_company` CHANGE COLUMN `cname` `cname` VARCHAR(255) COMMENT ''公司中文名''');
call proc_modify_column(
        'dfs_company',
        'ctype_number',
        'ALTER TABLE `dfs_company` CHANGE COLUMN `ctype_number` `ctype_number` VARCHAR(255) COMMENT ''公司类型编码''');
call proc_modify_column(
        'dfs_company',
        'cshow',
        'ALTER TABLE `dfs_company` CHANGE COLUMN `cshow` `cshow` VARCHAR(255) COMMENT ''公司英文名''');


-- DML
-- 审批总价信息修改
update dfs_approve_node_field set field_code = 'totalPrice' where full_path_code = 'saleOrder.releasedApprove' and module_code = 'materialField' and field_name = '总价';
-- 审批销售订单单价
INSERT INTO `dfs_approve_node_field`(`id`, `full_path_code`, `module_code`, `field_code`, `field_name`) VALUES (null, 'saleOrder.releasedApprove', 'materialField', 'price', '单价');

-- 新增用户导出按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('dfs12102100', '导出', 'user:export', NULL, NULL, NULL, NULL, '2025-07-01 11:40:23', 'enable', 'GET', '12102', 2, 1, 0, '/base-info/user', 1, NULL, '', 1);
-- 初始化权限
call init_new_role_permission('dfs12102100');

-- 新增部门导出按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('dfs12101060', '导出', 'organization:export', NULL, NULL, NULL, NULL, '2025-07-16 10:18:05', 'enable', 'DELETE', '12101', 2, 1, 1, '/base-info/organization', 1, NULL, '', 1);
-- 初始化权限
call init_new_role_permission('dfs12101060');

-- 单据类型统一sql
update `dfs_order_category` set order_category = 'productOrderMaterialList' WHERE order_category = 'productMaterialsList';
update `dfs_order_category` set order_category = 'subcontractOrderReceiptOrder' WHERE order_category = 'subcontractReceiptOrder';
update `dfs_order_category` set order_category = 'subcontractOrderReturnOrder' WHERE order_category = 'subcontractReturnOrder';
update `dfs_order_category` set order_category = 'subcontractOrderMaterialList' WHERE order_category = 'subcontractMaterialsList';

update `dfs_task` set order_category = 'subcontractOrderMaterialList' WHERE order_category = 'subcontractMaterialsList';
update `dfs_task` set order_category = 'subcontractOrderReceiptOrder' WHERE order_category = 'subcontractReceiptOrder';
update `dfs_task` set order_category = 'subcontractOrderReturnOrder' WHERE order_category = 'subcontractReturnOrder';
update `dfs_task` set order_category = 'subcontractOrderMaterialList' WHERE order_category = 'subcontractMaterialsList';

update `dfs_task` set upstream_order_category = 'subcontractOrderMaterialList' WHERE upstream_order_category = 'subcontractMaterialsList';
update `dfs_task` set upstream_order_category = 'subcontractOrderReceiptOrder' WHERE upstream_order_category = 'subcontractReceiptOrder';
update `dfs_task` set upstream_order_category = 'subcontractOrderReturnOrder' WHERE upstream_order_category = 'subcontractReturnOrder';
update `dfs_task` set upstream_order_category = 'subcontractOrderMaterialList' WHERE upstream_order_category = 'subcontractMaterialsList';

update `dfs_task` set root_order_category = 'subcontractOrderMaterialList' WHERE root_order_category = 'subcontractMaterialsList';
update `dfs_task` set root_order_category = 'subcontractOrderReceiptOrder' WHERE root_order_category = 'subcontractReceiptOrder';
update `dfs_task` set root_order_category = 'subcontractOrderReturnOrder' WHERE root_order_category = 'subcontractReturnOrder';
update `dfs_task` set root_order_category = 'subcontractOrderMaterialList' WHERE root_order_category = 'subcontractMaterialsList';

-- 首件检验记录校验
INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES (null, 'firstItemInspectVerify', '首件检验记录校验', 'production.workOrderVerificationReportConfig.reportGeneralConfig.firstItemInspectVerify', 'production.workOrderVerificationReportConfig.reportGeneralConfig', '报工时若当前报工工单当天没有对应的已合格的首件检验单（当天最新），则无法提交报工记录', 'yelinkoncall', 'yelinkoncall', '2025-07-01 06:15:49', '2025-07-01 15:57:43', '');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (null, 'enable', '是否开启', 'production.workOrderVerificationReportConfig.reportGeneralConfig.firstItemInspectVerify.enable', 'production.workOrderVerificationReportConfig.reportGeneralConfig.firstItemInspectVerify', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);

-- DDL
-- 为 dfs_rule_seq 表添加唯一约束，防止并发插入重复数据
-- 首先清理可能存在的重复数据（保留ID最大的记录）
DELETE t1
FROM dfs_rule_seq AS t1
LEFT JOIN (
    SELECT MAX(id) AS max_id
    FROM dfs_rule_seq
    GROUP BY rule_type, relation_number, relation_type, uuid
) AS t2 ON t1.id = t2.max_id
WHERE t2.max_id IS NULL;
-- 添加唯一约束，防止并发插入重复数据
-- 约束字段：rule_type, relation_number, relation_type, uuid
-- 注意：relation_number 可能为 NULL，MySQL 中 NULL 值不参与唯一性检查
call proc_modify_column(
        'dfs_rule_seq',
        'relation_type',
        'ALTER TABLE `dfs_rule_seq` CHANGE COLUMN `relation_type` `relation_type` VARCHAR(100) COMMENT ''关联的编号类型''');
call proc_modify_column(
        'dfs_rule_seq',
        'uuid',
        'ALTER TABLE `dfs_rule_seq` CHANGE COLUMN `uuid` `uuid` VARCHAR(50) COMMENT ''自动生成序号所需id，用于找到对应的自增序号''');
call proc_add_unique_index('dfs_rule_seq','rule_type,uuid,relation_number,relation_type','uk_rule_seq_unique');

-- 审批控件模块处理
update dfs_approve_template_control c set module_code = (select f.module_code from dfs_approve_node_field f where c.full_path_code = f.full_path_code and c.field_code = f.field_code limit 1) where c.field_code is not null;
-- 添加唯一索引，避免重复执行脚本
-- 1、删除重复数据，保留每组重复记录中的第一条
-- 使用备份表方式处理重复数据，避免临时表的限制
-- 创建备份表保存去重后的数据
CREATE TABLE IF NOT EXISTS dfs_config_open_api_backup (
    service VARCHAR(255),
    model VARCHAR(255),
    interface_code VARCHAR(255),
    interface_name VARCHAR(255),
    http_method VARCHAR(255),
    path VARCHAR(255)
);
INSERT INTO dfs_config_open_api_backup (service, model, interface_code, interface_name, http_method, path)
SELECT DISTINCT service, model, interface_code, interface_name, http_method, path
FROM dfs_config_open_api;
-- 清空原表
TRUNCATE TABLE dfs_config_open_api;
-- 将去重后的数据插入回原表
INSERT INTO dfs_config_open_api (service, model, interface_code, interface_name, http_method, path)
SELECT service, model, interface_code, interface_name, http_method, path
FROM dfs_config_open_api_backup;
-- 删除备份表
DROP TABLE dfs_config_open_api_backup;
-- 2. 添加唯一索引
call proc_add_unique_index('dfs_config_open_api','service,model,http_method,path','uk_unique_key');


-- 为 sys_user_role 表添加唯一约束，防止用户角色关系重复
DELETE FROM `sys_user_role`
WHERE id NOT IN (
    SELECT t.max_id FROM (
                             SELECT MAX(id) as max_id
                             FROM `sys_user_role`
                             GROUP BY `user_id`, `role_id`
                         ) as t
);
-- 添加唯一约束，防止 (user_id, role_id) 组合重复
call proc_add_unique_index('sys_user_role','user_id,role_id','uk_unique_key');

-- 销售发货单
INSERT INTO `dfs_order_category` (`order_category`, `order_name`, `application_id`, `detail_path`, `request_prefix`, `is_show`, `is_sys_data`, `location_x`, `location_y`, `background_color`, `font_color`) VALUES ('stockDelivery', '销售发货单', 'new.yk.genieos.dfs.supply-chain-collaboration.order-model.dg', '', NULL, 1, 1, 509.5061, -643.9753, '#FEDCC3', '#000000');
