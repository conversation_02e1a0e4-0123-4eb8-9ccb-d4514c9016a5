set autocommit = 0;                                                                                                                       set autocommit = 0;
#DDL

call proc_add_column_if_not_exist(
'sys_permissions',
'is_enable',
'ALTER TABLE `sys_permissions` ADD COLUMN `is_enable` int NULL DEFAULT 1 COMMENT ''菜单是否启用0-否 1-是'' AFTER `is_back_stage`');

call proc_modify_column_if_exist(
        'sys_role_permission',
        'permission_id',
        'alter table sys_role_permission modify column permission_id varchar(100) NOT NULL');

#DML

DELETE FROM `dfs_target_dict` WHERE target_name = 'deviceOee';
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`) VALUES ('deviceOee', '设备oee', NULL, 'device', NULL, NULL, NULL, '2021-03-25 10:20:11', '2021-03-25 10:20:11', NULL);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('deviceOee', 'device', '设备自动计算OEE', 'deviceAutoOee', 1, NULL);

#添加系统管理员账号 角色 关联关系 所有菜单权限
INSERT INTO `sys_roles`(`name`, `role_code`, `role_level`, `status`) VALUES ('系统管理员', 'oncall', 0, 'enable');
INSERT INTO `sys_users`(`user_name`, `pass_word`, `header_url`, `mobile`, `id_card`, `email`, `nick_name`, `job_number`, `comp_id`, `bind_range`, `unit_name`, `sex`, `age`, `department_id`, `skill_level`, `wechat`, `post`, `entry_date`, `enabled`, `account_non_expired`, `credentials_non_expired`, `account_non_locked`, `update_by`, `create_by`, `update_time`, `create_time`, `department_name`, `function_code`, `im_user_name`, `keycloak_id`) VALUES ('yelinkoncall', '$2a$10$LabFYfeZ08MEayKMhzbfdOksHeGJTutR8ZFLwOCK5ZYSNiPvFnbcy', '', '***********', NULL, '', '系统管理员', NULL, 1, NULL, NULL, 1, 18, '1', NULL, '', '', NULL, 'enable', 'enable', 'enable', 'enable', NULL, 'admin', '2022-02-24 19:46:58', '2022-02-22 14:24:42', '公司', NULL, NULL, NULL);
INSERT INTO `sys_user_role`(`user_id`, `role_id`) VALUES (
(SELECT id from sys_users where user_name='yelinkoncall'),
(SELECT id from sys_roles where role_code='oncall')
);

INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) ( SELECT ( SELECT id FROM sys_roles WHERE role_code = 'oncall' ) `role_id`, id AS `permission_id` FROM sys_permissions );
INSERT INTO `sys_role_line` ( `role_id`, `line_id` ) ( SELECT ( SELECT id FROM sys_roles WHERE role_code = 'oncall' ) `role_id`, production_line_id AS `line_id` FROM dfs_production_line );

commit
