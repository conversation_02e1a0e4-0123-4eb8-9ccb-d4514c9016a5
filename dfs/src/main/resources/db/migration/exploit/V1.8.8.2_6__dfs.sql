set autocommit = 0;

-- 判断字段不存在则增加该字段
DROP PROCEDURE if EXISTS proc_add_column_if_not_exist;
delimiter $$
CREATE PROCEDURE `proc_add_column_if_not_exist`(in var_table_name varchar(64),in var_column_name varchar(64),in var_sqlstr varchar(1024))
top:begin
	-- 表不存在则直接返回
	set @p_tablenum='';
	set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
	prepare stmt1 from @sqlstr1;
	execute stmt1;
	deallocate prepare stmt1;
	if(@p_tablenum<1)then
		leave top;
	end if;

	-- 字段已存在则直接返回
	set @p_columnnum='';
	set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
	prepare stmt2 from @sqlstr;
	execute stmt2;
	deallocate prepare stmt2;
	if(@p_columnnum>0)then
		leave top;
	end if;

	-- 表存在且字段不存在则创建新字段
	set @sqlcmd=var_sqlstr;
	prepare stmt3 from @sqlcmd;
	execute stmt3;
	deallocate prepare stmt3;
end $$
delimiter;




-- 修改表字段的函数
DROP PROCEDURE if EXISTS proc_modify_column_if_exist;
delimiter $$
CREATE PROCEDURE `proc_modify_column_if_exist`(in var_table_name varchar(64),in var_column_name varchar(64),in var_sqlstr varchar(1024))
top:begin

	-- 表不存在则直接返回
	set @p_tablenum='';
	set @sqlstr1=concat('select count(table_name)into @p_tablenum from information_schema.tables where table_schema=database() and table_name=\'',var_table_name,'\' limit 1;');
	prepare stmt1 from @sqlstr1;
	execute stmt1;
	deallocate prepare stmt1;
	if(@p_tablenum<1)then
		leave top;
	end if;

	-- 字段不存在则直接返回
	set @p_columnnum='';
	set @sqlstr=concat('select count(column_name) into @p_columnnum from information_schema.columns where table_schema=database() and table_name=\'',var_table_name,'\'and column_name =\'',var_column_name,'\';');
	prepare stmt2 from @sqlstr;
	execute stmt2;
	deallocate prepare stmt2;
	if(@p_columnnum<=0)then
		leave top;
	end if;


	-- 表存在且字段存在则修改字段
 	set @sqlcmd=var_sqlstr;
 	prepare stmt3 from @sqlcmd;
 	execute stmt3;
 	deallocate prepare stmt3;

	end $$
delimiter;

#DDL
-- 新增sys_pads表字段
call proc_add_column_if_not_exist(
'sys_pads',
'name',
'ALTER TABLE `sys_pads` ADD COLUMN `name` varchar(100)  null DEFAULT NULL COMMENT ''pad名称'' AFTER `mac`');

call proc_add_column_if_not_exist(
'sys_pads',
'free_login',
'ALTER TABLE `sys_pads` ADD COLUMN `free_login` tinyint null DEFAULT NULL COMMENT ''是否开启免密登录 0否 1是'' AFTER `mac`');

#新增小程序权限信息表
CREATE TABLE IF NOT EXISTS `sys_app_permission_info` (
    `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限id',
    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限名',
    `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '接口路径',
    `description` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明',
    `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    `create_time` datetime DEFAULT NULL,
    `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    `update_time` datetime DEFAULT NULL,
    `status` enum('enable','disable') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'enable' COMMENT '角色状态''ENABLE''开启,''DISABLE''关闭',
    `method` enum('GET','POST','PUT','DELETE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'GET' COMMENT '请求方式',
    `parent_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限等级：1级代表菜单、2级也代菜单，3级代表具体权限',
    `type` int DEFAULT NULL COMMENT '0：一级菜单   1：二级菜单   2：按钮',
    `is_web` int DEFAULT NULL COMMENT '是否web端1-web端,0-pad端',
    `is_back_stage` int DEFAULT NULL COMMENT '是否为后台管理（1-是 0-否）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='小程序权限信息表(全量)';

#新增小程序信息表
CREATE TABLE IF NOT EXISTS `sys_apps_info` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小程序名称',
    `industry` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属行业',
    `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='小程序信息表';

#新增pad角色中间表
CREATE TABLE IF NOT EXISTS `sys_pad_role` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'pad与角色关系id',
  `pad_id` int DEFAULT NULL COMMENT 'pad id',
  `role_id` int DEFAULT NULL COMMENT '角色id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='pad-角色 中间表';

#DML

commit
