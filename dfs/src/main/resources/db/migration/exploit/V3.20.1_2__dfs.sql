-- 修改字段的类型大小
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_quality_product_order_defect_daily',
        'unqualified_product_quantity',
        'ALTER TABLE `dfs_metrics_quality_product_order_defect_daily` CHANGE COLUMN `unqualified_product_quantity` `unqualified_record_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良品记录数量: 流水码去重'';');

-- 修改字段的类型大小
call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_quality_product_order_defect_daily',
        'unqualified_item_quantity',
        'ALTER TABLE `dfs_metrics_quality_product_order_defect_daily` CHANGE COLUMN `unqualified_item_quantity` `unqualified_record_item_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''不良项记录数量: 流水码不去重'';');


UPDATE dfs_table_config SET field_code = 'unqualified_record_quantity'
WHERE table_name = 'dfs_metrics_quality_product_order_defect_daily' and field_code = 'unqualified_product_quantity';

UPDATE dfs_table_config SET field_code = 'unqualified_record_item_quantity'
WHERE table_name = 'dfs_metrics_quality_product_order_defect_daily' and field_code = 'unqualified_item_quantity';


-- 采购订单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'projectContract', '项目合同是否存在', 'project_contract', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'supplierName', '供应商名称', 'supplier_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'supplierCode', '供应商编码', 'supplier_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'supplierPhone', '供应商联系方式', 'supplier_phone', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'supplierAddr', '供应商地址', 'supplier_addr', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'requestCode', '采购需求编号：逗号分隔', 'request_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'relatedType', '关联类型', 'related_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseCode', '采购单号', 'purchase_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'buyer', '采购员', 'buyer', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'buyerPhone', '采购员联系方式', 'buyer_phone', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'planTime', '计划采购时间', 'plan_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'state', '状态（1-创建 2-发放 3-完成 4-关闭 5-取消）', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'realTime', '实际采购时间', 'real_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseType', '单据类型', 'purchase_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'businessType', '业务类型', 'business_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'tradeId', '贸易条件ID', 'trade_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'collectId', '收款条件ID', 'collect_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'payId', '付款条件ID', 'pay_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'updateTime', '更新时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'approver', '审核人', 'approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'remark', '备注', 'remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'actualApprover', '实际审批人', 'actual_approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'approvalStatus', '审批状态', 'approval_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'approvalSuggestion', '审批建议', 'approval_suggestion', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'approvalTime', '审批时间', 'approval_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'projectName', '项目备注', 'project_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'supplierOrderState', '供应商单据状态（待签收、已确认、打包中、打包完成、发货完成）', 'supplier_order_state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'supplierRemark', '供应商备注', 'supplierRemark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'receiptState', '收料状态', 'receipt_state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'warehouseState', '入库状态', 'warehouse_state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldOne', '采购订单扩展字段1', 'purchase_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldTwo', '采购订单扩展字段2', 'purchase_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldThree', '采购订单扩展字段3', 'purchase_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldFour', '采购订单扩展字段4', 'purchase_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldFive', '采购订单扩展字段5', 'purchase_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldSix', '采购订单扩展字段6', 'purchase_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldSeven', '采购订单扩展字段7', 'purchase_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldEight', '采购订单扩展字段8', 'purchase_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldNine', '采购订单扩展字段9', 'purchase_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchase', 'purchaseExtendFieldTen', '采购订单扩展字段10', 'purchase_extend_field_ten', NULL);
-- 采购订单物料
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'lineNumber', '行号', 'line_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseId', '采购订单ID', 'purchase_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseCode', '采购订单单号', 'purchase_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'relateMaterialId', '关联上级单据物料行id', 'relate_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'businessUnitCode', '业务单元编码', 'business_unit_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'businessUnitName', '业务单元名称', 'business_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'materialId', '物料id', 'material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'code', '物料编码', 'code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'isGift', '是否赠品', 'is_gift', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'num', '需求数量', 'num', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'planNum', '计划采购数量', 'plan_num', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'giftQuantity', '赠品收料数', 'gift_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'giveawayQuantity', '赠品数量,用于erp回写', 'giveaway_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'price', '成本单价', 'price', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'materialDesc', '物料描述', 'material_desc', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'materialRemark', '物料备注', 'material_remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'sumprice', '采购总价', 'sum_price', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'inventoryQuantity', '入库数量', 'inventory_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseDate', '要货日期', 'purchase_date', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'warehouseCode', '来源物料默认仓库', 'warehouse_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'returnQuantity', '退料数量', 'return_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'rejectedQty', '拒收数量', 'rejected_qty', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'updateBy', '更新人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'customerCode', '客户编码', 'customer_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'customerName', '客户名称', 'customer_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldOne', '采购订单物料扩展字段1', 'purchase_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldTwo', '采购订单物料扩展字段2', 'purchase_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldThree', '采购订单物料扩展字段3', 'purchase_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldFour', '采购订单物料扩展字段4', 'purchase_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldFive', '采购订单物料扩展字段5', 'purchase_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldSix', '采购订单物料扩展字段6', 'purchase_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldSeven', '采购订单物料扩展字段7', 'purchase_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldEight', '采购订单物料扩展字段8', 'purchase_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldNine', '采购订单物料扩展字段9', 'purchase_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'purchaseMaterial', 'purchaseMaterialExtendFieldTen', '采购订单物料扩展字段10', 'purchase_material_extend_field_ten', NULL);

-- 指标： 生产工单-工单产能
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'capacity',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `capacity` double(11, 2) NULL DEFAULT NULL COMMENT ''工单产能''');

-- 指标： 生产工单-异常工时
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'exception_hour',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `exception_hour` double(11, 2) NULL DEFAULT NULL COMMENT ''异常工时''');
-- 指标： 生产工单-直通数
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'direct_access_quantity',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `direct_access_quantity` double(11, 2) NULL DEFAULT NULL COMMENT ''直通数''');


-- 委外订单
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'subcontractOrderNumber', '委外订单号', 'subcontract_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'saleOrderNumber', '销售订单号', 'sale_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'productOrderNumber', '生产订单号', 'product_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderType', '单据类型：单工序委外订单 singleProcessSubcontractOrder,多工序委外订单 multiProcessSubcontractOrder', 'order_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'businessType', '业务类型: 标准委外 standardSubcontractOrder', 'business_type', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'customer', '客户编码', 'customer', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'customerContact', '客户联系方式', 'customer_contact', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'salesQuantity', '销售订单数量', 'sales_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'state', '状态', 'state', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'approvalStatus', '审批状态', 'approval_status', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'approver', '审批人', 'approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'actualApprover', '实际审批人', 'actual_approver', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'approvalSuggestion', '审批意见', 'approval_suggestion', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'approvalTime', '审批时间', 'approval_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'updateBy', '修改人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'updateTime', '修改时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderRemark', '备注', 'order_remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldOne', '单据扩展字段1', 'order_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldTwo', '单据扩展字段2', 'order_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldThree', '单据扩展字段9', 'order_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldFour', '单据扩展字段4', 'order_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldFive', '单据扩展字段5', 'order_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldSix', '单据扩展字段6', 'order_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldSeven', '单据扩展字段7', 'order_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldEight', '单据扩展字段8', 'order_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldNine', '单据扩展字段9', 'order_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrder', 'orderExtendFieldTen', '单据扩展字段10', 'order_extend_field_ten', NULL);

-- 委外订单物料
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'id', 'id', 'id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderId', '关联委外订单id', 'order_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'subcontractOrderNumber', '委外订单号', 'subcontract_order_number', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'materialCode', '物料编码', 'material_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'relateMaterialRowId', '关联上级单据物料行id', 'relate_material_row_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'skuId', '特征参数skuId', 'sku_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'quantity', '订单数量', 'quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'planStartTime', '计划开始时间', 'plan_start_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'planEndTime', '计划完成时间', 'plan_end_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'priority', '优先级', 'priority', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'supplier', '供应商编码', 'supplier', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'materialRemark', '物料行备注', 'material_remark', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'craftId', '工艺id', 'craft_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'craftProcedureId', '工艺工序ID', 'craft_procedure_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'procedureId', '工序id', 'procedure_id', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'procedureName', '工序名称', 'procedure_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'finishCount', '实际数量（完成数量）', 'finish_count', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'measureUnit', '计量单位', 'measure_unit', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'measureFinishCount', '实际数量', 'measure_finish_count', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'createBy', '创建人', 'create_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'updateBy', '修改人', 'update_by', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'createTime', '创建时间', 'create_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'updateTime', '修改时间', 'update_time', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'pickingQuantity', '已领料数', 'picking_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'inventoryQuantity', '已入库数', 'inventory_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'receiptQuantity', '收货数量', 'receipt_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'deliveryQuantity', '发料数量', 'delivery_quantity', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'businessUnitCode', '业务单元编码', 'business_unit_code', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'businessUnitName', '业务单元名称', 'business_unit_name', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldOne', '单据物料行扩展字段1', 'order_material_extend_field_one', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldTwo', '单据物料行扩展字段2', 'order_material_extend_field_two', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldThree', '单据物料行扩展字段3', 'order_material_extend_field_three', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldFour', '单据物料行扩展字段4', 'order_material_extend_field_four', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldFive', '单据物料行扩展字段5', 'order_material_extend_field_five', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldSix', '单据物料行扩展字段6', 'order_material_extend_field_six', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldSeven', '单据物料行扩展字段7', 'order_material_extend_field_seven', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldEight', '单据物料行扩展字段8', 'order_material_extend_field_eight', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldNine', '单据物料行扩展字段9', 'order_material_extend_field_nine', NULL);
INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'subcontractOrderMaterial', 'orderMaterialExtendFieldTen', '单据物料行扩展字段10', 'order_material_extend_field_ten', NULL);
