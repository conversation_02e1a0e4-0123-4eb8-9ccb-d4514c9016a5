-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_material_daily` (
    `id` int NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一索引',
    `time` datetime DEFAULT NULL COMMENT '最近更新时间',
    `record_time` date NOT NULL COMMENT '记录日期 [yyyy-MM-dd]',
    `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料编码',
    `material_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料名称',
    `material_type_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料类型',
    `material_sort_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料分类',
    `material_standard` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料规格',
    `material_drawing_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料图号',
    `material_raw_material` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料材质',
    `material_sku_attr_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料特征参数名称 [多个按,分隔]',
    `material_custom_field_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段1',
    `material_custom_field_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段2',
    `material_custom_field_three` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段3',
    `material_custom_field_four` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段4',
    `material_custom_field_five` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段5',
    `material_custom_field_six` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段6',
    `material_custom_field_seven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段7',
    `material_custom_field_eight` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段8',
    `material_custom_field_nine` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段9',
    `material_custom_field_ten` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段10',
    `material_custom_field_eleven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段11',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `unqualified_record_quantity` int DEFAULT NULL COMMENT '不良品记录数量 [流水码去重]',
    `unqualified_record_item_quantity` int DEFAULT NULL COMMENT '不良项记录数量 [流水码不去重]',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '维修数',
    `repair_scrap_quantity` double(11,2) DEFAULT NULL COMMENT '维修报废数 [维修中结果报废的数量]',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '维修合格数 [维修中结果正常的数量]',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数 [没有被标记为首次不良的成品数量]',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率 [合格数/(合格数+不良数)]',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率 [直通数/(完成数+维修报废数)]',
    `human_working_hour` double(11,2) DEFAULT NULL COMMENT '人员投入工时 [人员打卡的时间求和]',
    `resource_working_hour` double(11,2) DEFAULT NULL COMMENT '资源投入工时 [生产资源分配时间求和]',
    `product_hour` double(11,2) DEFAULT NULL COMMENT '产出工时 [完成数/产能]',
    `human_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '人员生产效率 [产出工时/人员投入工时]',
    `resource_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '资源生产效率 [产出工时/资源投入工时]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '总处理时长 [告警处理时长求和]',
    `alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '总恢复时长 [告警恢复时长求和]',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_material_code` (`material_code`),
    KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='生产日汇总-按产品';


CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_material_monthly` (
    `id` int NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一索引',
    `time` datetime DEFAULT NULL COMMENT '最近更新时间',
    `record_time` date NOT NULL COMMENT '记录日期 [yyyy-MM-dd]',
    `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物料编码',
    `material_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料名称',
    `material_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料类型',
    `material_sort_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料分类',
    `material_standard` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料规格',
    `material_drawing_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料图号',
    `material_raw_material` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料材质',
    `material_sku_attr_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料特征参数名称 [多个按,分隔]',
    `material_custom_field_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段1',
    `material_custom_field_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段2',
    `material_custom_field_three` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段3',
    `material_custom_field_four` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段4',
    `material_custom_field_five` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段5',
    `material_custom_field_six` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段6',
    `material_custom_field_seven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段7',
    `material_custom_field_eight` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段8',
    `material_custom_field_nine` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段9',
    `material_custom_field_ten` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段10',
    `material_custom_field_eleven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段11',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `unqualified_record_quantity` int DEFAULT NULL COMMENT '不良品记录数量 [流水码去重]',
    `unqualified_record_item_quantity` int DEFAULT NULL COMMENT '不良项记录数量 [流水码不去重]',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '维修数',
    `repair_scrap_quantity` double(11,2) DEFAULT NULL COMMENT '维修报废数 [维修中结果报废的数量]',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '返修合格数 [维修中结果正常的数量]',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数 [没有被标记为首次不良的成品数量]',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率 [合格数/(合格数+不良数)]',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率 [直通数/(完成数+维修报废数)]',
    `human_working_hour` double(11,2) DEFAULT NULL COMMENT '人员投入工时 [人员打卡的时间求和]',
    `resource_working_hour` double(11,2) DEFAULT NULL COMMENT '资源投入工时 [生产资源分配时间求和]',
    `product_hour` double(11,2) DEFAULT NULL COMMENT '产出工时 [完成数/产能]',
    `human_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '人员生产效率 [产出工时/人员投入工时]',
    `resource_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '资源生产效率 [产出工时/资源投入工时]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '总处理时长 [告警处理时长求和]',
    `alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '总恢复时长 [告警恢复时长求和]',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_material_code` (`material_code`),
    KEY `idx_record_time` (`record_time`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='生产每月汇总-按产品';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_basic_unit_daily` (
    `id` int NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一索引',
    `time` datetime DEFAULT NULL COMMENT '最近更新时间',
    `record_time` date NOT NULL COMMENT '记录日期 [yyyy-MM-dd]',
    `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务单元名称',
    `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产资源类型 [工作中心类型]',
    `work_center_id` int DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心名称',
    `production_basic_unit_id` int NOT NULL COMMENT '生产基本单元id',
    `production_basic_unit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元编码',
    `production_basic_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元名称',
    `production_basic_unit_model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元类型 [模型的类型]',
    `gid` int DEFAULT NULL COMMENT '车间id',
    `gname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间名称',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `unqualified_record_quantity` int DEFAULT NULL COMMENT '不良品记录数量 [流水码去重]',
    `unqualified_record_item_quantity` int DEFAULT NULL COMMENT '不良项记录数量 [流水码不去重]',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '维修数',
    `repair_scrap_quantity` double(11,2) DEFAULT NULL COMMENT '维修报废数 [维修中结果报废的数量]',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '维修合格数 [维修中结果正常的数量]',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数 [没有被标记为首次不良的成品数量]',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率 [合格数/(合格数+不良数)]',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率 [直通数/(完成数+维修报废数)]',
    `human_working_hour` double(11,2) DEFAULT NULL COMMENT '人员投入工时 [人员打卡的时间求和]',
    `resource_working_hour` double(11,2) DEFAULT NULL COMMENT '资源投入工时 [生产资源分配时间求和]',
    `product_hour` double(11,2) DEFAULT NULL COMMENT '产出工时 [完成数/产能]',
    `human_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '人员生产效率 [产出工时/人员投入工时]',
    `resource_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '资源生产效率 [产出工时/资源投入工时]',
    `plan_achievement_rate` double(11,4) DEFAULT NULL COMMENT '计划达成率 [实际生产数/计划生产数]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '总处理时长 [告警处理时长求和]',
    `alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '总恢复时长 [告警恢复时长求和]',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_record_time` (`record_time`),
    KEY `idx_work_center_type` (`work_center_type`) USING BTREE,
    KEY `idx_production_basic_unit_id` (`production_basic_unit_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='生产日汇总-按生产基本单元';


CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_basic_unit_monthly` (
    `id` int NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一索引',
    `time` datetime DEFAULT NULL COMMENT '最近更新时间',
    `record_time` date NOT NULL COMMENT '记录日期 [yyyy-MM-dd]',
    `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务单元名称',
    `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产资源类型 [工作中心类型]',
    `work_center_id` int DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心名称',
    `production_basic_unit_id` int NOT NULL COMMENT '生产基本单元id',
    `production_basic_unit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元编码',
    `production_basic_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元名称',
    `production_basic_unit_model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元类型 [模型的类型]',
    `gid` int DEFAULT NULL COMMENT '车间id',
    `gname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间名称',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `unqualified_record_quantity` int DEFAULT NULL COMMENT '不良品记录数量 [流水码去重]',
    `unqualified_record_item_quantity` int DEFAULT NULL COMMENT '不良项记录数量 [流水码不去重]',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '维修数',
    `repair_scrap_quantity` double(11,2) DEFAULT NULL COMMENT '维修报废数 [维修中结果报废的数量]',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '维修合格数 [维修中结果正常的数量]',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数 [没有被标记为首次不良的成品数量]',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率 [合格数/(合格数+不良数)]',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率 [直通数/(完成数+维修报废数)]',
    `human_working_hour` double(11,2) DEFAULT NULL COMMENT '人员投入工时 [人员打卡的时间求和]',
    `resource_working_hour` double(11,2) DEFAULT NULL COMMENT '资源投入工时 [生产资源分配时间求和]',
    `product_hour` double(11,2) DEFAULT NULL COMMENT '产出工时 [完成数/产能]',
    `human_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '人员生产效率 [产出工时/人员投入工时]',
    `resource_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '资源生产效率 [产出工时/资源投入工时]',
    `plan_achievement_rate` double(11,4) DEFAULT NULL COMMENT '计划达成率 [实际生产数/计划生产数]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '总处理时长 [告警处理时长求和]',
    `alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '总恢复时长 [告警恢复时长求和]',
    `add_work_order_count` int DEFAULT NULL COMMENT '新增工单数 [周期内新增的工单计数]',
    `finish_work_order_count` int DEFAULT NULL COMMENT '完成工单数 [周期内完成的工单计数]',
    `timely_work_order_count` int DEFAULT NULL COMMENT '及时完成工单数 [周期内在计划完成时间内完成的工单计数]',
    `delay_work_order_count` int DEFAULT NULL COMMENT '延误完成工单数 [周期内超出计划完成时间完成的工单计数]',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_record_time` (`record_time`),
    KEY `idx_work_center_type` (`work_center_type`) USING BTREE,
    KEY `idx_production_basic_unit_id` (`production_basic_unit_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='生产每月汇总-按生产基本单元';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_work_order_monthly` (
    `id` int NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一索引',
    `time` datetime DEFAULT NULL COMMENT '最近更新时间',
    `record_time` date NOT NULL COMMENT '记录日期 [yyyy-MM-dd]',
    `sale_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '销售订单号',
    `product_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产订单号',
    `work_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产工单号',
    `a_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '厂区名称',
    `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单元名称',
    `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产资源类型 [工作中心类型]',
    `work_center_id` int DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心名称',
    `production_basic_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元名称 [多个按'',''分隔]',
    `production_basic_unit_model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元类型 [模型的类型，多个按'',''分隔]',
    `gid` int DEFAULT NULL COMMENT '车间id',
    `gname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间名称',
    `craft_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工艺名称',
    `craft_procedure_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工序名称 [多个按'',''分隔]',
    `theory_hour` double DEFAULT NULL COMMENT '单件理论工时',
    `plan_theory_hour` double DEFAULT NULL COMMENT '计划理论工时',
    `start_date` datetime DEFAULT NULL COMMENT '计划开始时间',
    `end_date` datetime DEFAULT NULL COMMENT '计划结束时间',
    `actual_start_date` datetime DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_date` datetime DEFAULT NULL COMMENT '实际结束时间',
    `material_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料名称',
    `material_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料类型',
    `material_sort_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料分类',
    `material_standard` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料规格',
    `material_drawing_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料图号',
    `material_raw_material` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料材质',
    `material_sku_attr_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料特征参数名称 [多个按,分隔]',
    `material_custom_field_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段1',
    `material_custom_field_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段2',
    `material_custom_field_three` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段3',
    `material_custom_field_four` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段4',
    `material_custom_field_five` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段5',
    `material_custom_field_six` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段6',
    `material_custom_field_seven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段7',
    `material_custom_field_eight` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段8',
    `material_custom_field_nine` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段9',
    `material_custom_field_ten` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段10',
    `material_custom_field_eleven` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段11',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `unqualified_record_quantity` int DEFAULT NULL COMMENT '不良品记录数量 [流水码去重]',
    `unqualified_record_item_quantity` int DEFAULT NULL COMMENT '不良项记录数量 [流水码不去重]',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '维修数',
    `repair_scrap_quantity` double(11,2) DEFAULT NULL COMMENT '维修报废数 [维修中结果报废的数量]',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '返修合格数 [维修中结果正常的数量]',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数 [没有被标记为首次不良的成品数量]',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率 [合格数/(合格数+不良数)]',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率 [直通数/(完成数+维修报废数)]',
    `human_working_hour` double(11,2) DEFAULT NULL COMMENT '人员投入工时 [人员打卡的时间求和]',
    `resource_working_hour` double(11,2) DEFAULT NULL COMMENT '资源投入工时 [生产资源分配时间求和]',
    `product_hour` double(11,2) DEFAULT NULL COMMENT '产出工时 [完成数/产能]',
    `human_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '人员生产效率 [产出工时/人员投入工时]',
    `resource_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '资源生产效率 [产出工时/资源投入工时]',
    `plan_achievement_rate` double(11,4) DEFAULT NULL COMMENT '计划达成率 [实际生产数/计划生产数]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '总处理时长 [告警处理时长求和]',
    `alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '总恢复时长 [告警恢复时长求和]',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_record_time` (`record_time`),
    KEY `idx_work_order_number` (`work_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='生产每月汇总-按产品';


CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_alarm_basic_unit_monthly` (
    `id` int NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一索引',
    `time` datetime DEFAULT NULL COMMENT '最近更新时间',
    `record_time` date NOT NULL COMMENT '记录日期 [yyyy-MM-dd]',
    `alarm_definition_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '告警定义编码',
    `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '生产资源类型 [工作中心类型]',
    `production_basic_unit_id` int NOT NULL COMMENT '生产基本单元id',
    `alarm_classify_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '告警类型',
    `alarm_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '告警名称',
    `alarm_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '严重程度',
    `business_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单元名称',
    `gname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间名称',
    `work_center_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心名称',
    `production_basic_unit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元编码',
    `production_basic_unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元名称',
    `production_basic_unit_model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元类型 [模型的类型]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `average_alarm_response_duration_h` double(11,4) DEFAULT NULL COMMENT '平均响应时长 [创建 -> 处理中（未处理按当前时间）]',
    `average_alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '平均处理时长 [处理中 -> 恢复]',
    `average_alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '平均恢复时长 [创建 -> 恢复（未恢复按当前时间）]',
    `alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '总处理时长 [告警处理时长求和]',
    `alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '总恢复时长 [告警恢复时长求和]',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_record_time` (`record_time`),
    KEY `idx_work_center_type` (`work_center_type`) USING BTREE,
    KEY `idx_production_basic_unit_id` (`production_basic_unit_id`),
    KEY `idx_alarm_definition_code` (`alarm_definition_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='告警每月汇总-按告警+生产基本单元';


CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_procedure_defect_daily` (
    `id` int NOT NULL AUTO_INCREMENT,
    `uni_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '唯一索引',
    `time` datetime DEFAULT NULL COMMENT '最近更新时间',
    `record_time` date NOT NULL COMMENT '记录日期 [yyyy-MM-dd]',
    `procedure_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工序名称',
    `defect_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '不良编码',
    `defect_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '不良名称',
    `defect_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '不良类型名称',
    `defect_type_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '不良类型编码',
    `defect_count` int DEFAULT NULL COMMENT '不良计数',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`uni_code`) USING BTREE,
    KEY `idx_record_time` (`record_time`),
    KEY `idx_procedure_name` (`procedure_name`),
    KEY `idx_defect_code` (`defect_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='质量每日统计-按工序&不良';


-- ==========工单每日==========
-- 重命名
RENAME TABLE `dfs_metrics`.`dfs_metrics_work_order_daily` TO `dfs_metrics`.`dfs_metrics_work_order_daily_old`;
-- 创建新表
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_work_order_daily` (
    `id` int NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单号',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '当日计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '当日产出数量',
    `inspection_quantity` double(11,2) DEFAULT NULL COMMENT '当日检测数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '当日不良数量',
    `inbound_quantity` double(11,2) DEFAULT NULL COMMENT '当日入库数量',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '当日直通数量',
    `start_work_time` datetime DEFAULT NULL COMMENT '当日开工时间(小程序录入)',
    `working_people_quantity` double(11,2) DEFAULT NULL COMMENT '当日在岗人数',
    `actual_working_hour` double(11,2) DEFAULT NULL COMMENT '实际投入工时',
    `theory_working_hour` double(11,2) DEFAULT NULL COMMENT '理论产出工时',
    `qualified_rate` double(11,2) DEFAULT NULL COMMENT '当日合格率',
    `product_efficiency_rate` double(11,2) DEFAULT NULL COMMENT '生产效率',
    `achievements` double(11,2) DEFAULT NULL COMMENT '绩效',
    `repair_quantity` double(11,2) DEFAULT NULL COMMENT '当日返修数',
    `repair_qualified_quantity` double(11,2) DEFAULT NULL COMMENT '当日返修良品数（维修判定: 不报废）',
    `repair_quantity_rate` double(11,2) DEFAULT NULL COMMENT '当日返修率',
    `record_date` date NOT NULL COMMENT '记录日期',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    `work_center_id` int DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心名称',
    `material_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料名称',
    `unqualified_record_quantity` double(11,2) DEFAULT NULL COMMENT '不良品记录数量: 流水码去重',
    `unqualified_record_item_quantity` double(11,2) DEFAULT NULL COMMENT '不良项记录数量: 流水码不去重',
    `flow_code_report_quantity` double(11,2) DEFAULT NULL COMMENT '流水码标记为产出的数量：用于计算直通率',
    `human_working_hour` double(11,2) DEFAULT NULL COMMENT '人员投入工时',
    `resource_working_hour` double(11,2) DEFAULT NULL COMMENT '资源投入工时',
    `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心类型',
    `production_basic_unit_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元id',
    `production_basic_unit_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元名称',
    `sale_order_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '销售订单号',
    `product_order_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产订单号',
    `business_unit_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单元名称',
    `production_basic_unit_model_type` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元类型 [模型的类型，多个按,分隔]',
    `gid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间id  [附属信息]',
    `gname` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间名称',
    `craft_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工艺编码 [附属信息]',
    `craft_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工艺名称',
    `craft_procedure_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工序名称',
    `theory_hour` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单件理论工时',
    `plan_theory_hour` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划理论工时',
    `start_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划开始时间',
    `end_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划结束时间',
    `actual_start_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实际结束时间',
    `material_type_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料类型',
    `material_sort_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料分类',
    `material_standard` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料规格',
    `material_drawing_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料图号',
    `material_raw_material` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料材质',
    `material_sku_attr_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料特征参数名称 [多个按'',''分隔]',
    `material_custom_field_one` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段1',
    `material_custom_field_two` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段2',
    `material_custom_field_three` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段3',
    `material_custom_field_four` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段4',
    `material_custom_field_five` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段5',
    `material_custom_field_six` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段6',
    `material_custom_field_seven` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段7',
    `material_custom_field_eight` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段8',
    `material_custom_field_nine` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段9',
    `material_custom_field_ten` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段10',
    `material_custom_field_eleven` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段11',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率 [直通数/（完成数+维修报废数）]',
    `human_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '人员生产效率 [产出工时/人员投入工时]',
    `resource_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '资源生产效率 [产出工时/资源投入工时]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `alarm_deal_duration_h` double(11,4) DEFAULT NULL COMMENT '总处理时长 [告警处理时长求和]',
    `alarm_recover_duration_h` double(11,4) DEFAULT NULL COMMENT '总恢复时长 [告警恢复时长求和]',
    `a_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '厂区名称',
    `work_order_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单名称',
    `state` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单状态',
    `state_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单状态名称',
    `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工单备注',
    `plan_achievement_rate` double(11,4) DEFAULT NULL COMMENT '计划达成率 [实际生产数/计划生产数]',
    PRIMARY KEY (`id`),
    KEY `idx_uni` (`work_order_number`,`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单-每日统计';
-- 插入这两天的数据
INSERT INTO `dfs_metrics`.`dfs_metrics_work_order_daily` (
    `id`,`work_order_number`, `plan_quantity`, `produce_quantity`, `inspection_quantity`,
    `unqualified_quantity`, `inbound_quantity`, `direct_access_quantity`, `start_work_time`,
    `working_people_quantity`, `actual_working_hour`, `theory_working_hour`, `qualified_rate`,
    `product_efficiency_rate`, `achievements`, `repair_quantity`, `repair_qualified_quantity`,
    `repair_quantity_rate`, `record_date`, `time`, `work_center_id`, `work_center_name`,
    `material_code`, `material_name`, `unqualified_record_quantity`, `unqualified_record_item_quantity`,
    `flow_code_report_quantity`, `human_working_hour`, `resource_working_hour`, `work_center_type`,
    `production_basic_unit_id`, `production_basic_unit_name`
)
SELECT
    `id`,`work_order_number`, `plan_quantity`, `produce_quantity`, `inspection_quantity`,
    `unqualified_quantity`, `inbound_quantity`, `direct_access_quantity`, `start_work_time`,
    `working_people_quantity`, `actual_working_hour`, `theory_working_hour`, `qualified_rate`,
    `product_efficiency_rate`, `achievements`, `repair_quantity`, `repair_qualified_quantity`,
    `repair_quantity_rate`, `record_date`, `time`, `work_center_id`, `work_center_name`,
    `material_code`, `material_name`, `unqualified_record_quantity`, `unqualified_record_item_quantity`,
    `flow_code_report_quantity`, `human_working_hour`, `resource_working_hour`, `work_center_type`,
    `production_basic_unit_id`, `production_basic_unit_name`
FROM `dfs_metrics`.`dfs_metrics_work_order_daily_old`
WHERE record_date > CURDATE() - INTERVAL 2 DAY;





-- ==========工单10min==========
-- 重命名
RENAME TABLE  `dfs_metrics`.`dfs_metrics_work_order_10min` TO `dfs_metrics`.`dfs_metrics_work_order_10min_old`;
-- 创建新表
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_work_order_10min` (
    `id` int NOT NULL AUTO_INCREMENT,
    `work_order_number` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '工单号',
    `record_time` datetime NOT NULL COMMENT '记录时间(取整10分) ',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不良数量',
    `unqualified_record_quantity` double(11,2) DEFAULT NULL COMMENT '不良记录数量',
    `input_quantity` double(11,2) DEFAULT NULL COMMENT '投入数量',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数',
    `working_people_quantity` double(11,2) DEFAULT NULL COMMENT '打卡人数(上机点)',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    `unqualified_record_item_quantity` double(11,2) DEFAULT NULL COMMENT '不良项记录数量: 流水码不去重',
    `material_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料编码',
    `material_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料名称',
    `work_center_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心类型',
    `production_basic_unit_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元id',
    `production_basic_unit_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元名称',
    `work_center_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心id',
    `work_center_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心名称',
    `sale_order_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '销售订单号',
    `product_order_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产订单号',
    `business_unit_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单元名称',
    `production_basic_unit_model_type` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产基本单元类型 [模型的类型，多个按,分隔]',
    `gid` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间id  [附属信息]',
    `gname` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间名称',
    `craft_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工艺编码 [附属信息]',
    `craft_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工艺名称',
    `craft_procedure_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工序名称',
    `theory_hour` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单件理论工时',
    `plan_theory_hour` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划理论工时',
    `start_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划开始时间',
    `end_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计划结束时间',
    `actual_start_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实际开始时间',
    `actual_end_date` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '实际结束时间',
    `material_type_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料类型',
    `material_sort_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料分类',
    `material_standard` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料规格',
    `material_drawing_number` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料图号',
    `material_raw_material` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料材质',
    `material_sku_attr_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料特征参数名称 [多个按'',''分隔]',
    `material_custom_field_one` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段1',
    `material_custom_field_two` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段2',
    `material_custom_field_three` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段3',
    `material_custom_field_four` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段4',
    `material_custom_field_five` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段5',
    `material_custom_field_six` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段6',
    `material_custom_field_seven` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段7',
    `material_custom_field_eight` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段8',
    `material_custom_field_nine` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段9',
    `material_custom_field_ten` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段10',
    `material_custom_field_eleven` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物料扩展字段11',
    `human_working_hour` double(11,4) DEFAULT NULL COMMENT '人员投入工时',
    `resource_working_hour` double(11,4) DEFAULT NULL COMMENT '资源投入工时',
    `product_hour` double(11,4) DEFAULT NULL COMMENT '产出工时[完成数/产能]',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率 [直通数/（完成数+维修报废数）]',
    `human_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '人员生产效率 [产出工时/人员投入工时]',
    `resource_efficiency_rate` double(11,4) DEFAULT NULL COMMENT '资源生产效率 [产出工时/资源投入工时]',
    `alarm_count` int DEFAULT NULL COMMENT '告警发生次数 [告警次数计数]',
    `a_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '厂区名称',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`work_order_number`,`record_time`) USING BTREE,
    KEY `idx_record_time` (`record_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工单-10分钟统计';
-- 插入今天的数据
INSERT INTO `dfs_metrics`.`dfs_metrics_work_order_10min` (
    `id`,
    `work_order_number`,
    `record_time`,
    `produce_quantity`,
    `unqualified_quantity`,
    `unqualified_record_quantity`,
    `input_quantity`,
    `direct_access_quantity`,
    `working_people_quantity`,
    `time`,
    `unqualified_record_item_quantity`,
    `material_code`,
    `material_name`,
    `work_center_type`,
    `production_basic_unit_id`,
    `production_basic_unit_name`
)
SELECT
    `id`,
    `work_order_number`,
    `record_time`,
    `produce_quantity`,
    `unqualified_quantity`,
    `unqualified_record_quantity`,
    `input_quantity`,
    `direct_access_quantity`,
    `working_people_quantity`,
    `time`,
    `unqualified_record_item_quantity`,
    `material_code`,
    `material_name`,
    `work_center_type`,
    `production_basic_unit_id`,
    `production_basic_unit_name`
FROM `dfs_metrics`.`dfs_metrics_work_order_10min_old`
WHERE `record_time` > CURDATE() - INTERVAL 1 DAY;




-- ==========设备oee==========
-- 重命名
RENAME TABLE  `dfs_metrics`.`dfs_metrics_device_oee` TO `dfs_metrics`.`dfs_metrics_device_oee_old`;
-- 创建新表
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_device_oee` (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '记录id',
    `device_id` int NOT NULL COMMENT '设备id',
    `theoretical_speed` double(11,3) DEFAULT NULL COMMENT '理论生产速度',
    `amount` double(11,3) DEFAULT NULL COMMENT '生产数量',
    `unqualified` double(11,3) DEFAULT NULL COMMENT '不合格量',
    `run_time` double(11,2) DEFAULT NULL COMMENT '开动时长（小时）',
    `load_time` double(11,2) DEFAULT NULL COMMENT '负荷时长（小时）',
    `yield` double(11,4) DEFAULT NULL COMMENT '合格率',
    `performance` double(11,4) DEFAULT NULL COMMENT '性能开动率',
    `time_efficiency` double(11,4) DEFAULT NULL COMMENT '时间开动率',
    `oee` double(11,4) DEFAULT NULL COMMENT 'OEE',
    `record_date` date NOT NULL COMMENT '记录日期',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    `device_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备名称',
    `plan_quantity` double(11,4) DEFAULT NULL COMMENT '计划数',
    `plan_achievement_rate` double(11,4) DEFAULT NULL COMMENT '计划达成率',
    `theory_working_hour` double(11,4) DEFAULT NULL COMMENT '理论产出工时',
    `rated_work_hour` double(11,4) DEFAULT NULL COMMENT '额定工作时长',
    `input_work_order_count` int DEFAULT NULL COMMENT '投产工单数',
    `resource_working_hour` double(11,4) DEFAULT NULL COMMENT '资源投入工时',
    `alarm_count` int DEFAULT NULL COMMENT '告警次数',
    `device_model_type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备类型',
    `device_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备编码 [附属信息]',
    `relate_line_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联产线名称',
    `relate_fac_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联工位名称',
    `gid` int DEFAULT NULL COMMENT '车间id [附属信息]',
    `gname` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '车间名称',
    `work_center_id` int DEFAULT NULL COMMENT '工作中心id [附属信息]',
    `work_center_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工作中心名称',
    `state` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态 [统计时的状态]',
    `state_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态名称 [统计时的状态]',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `create_time` (`create_time`) USING BTREE,
    KEY `id_record_date` (`device_id`,`record_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='设备每天OEE记录表';
-- 插入前两天的数据
INSERT INTO `dfs_metrics`.`dfs_metrics_device_oee` (
    `id`,
    `device_id`,
    `theoretical_speed`,
    `amount`,
    `unqualified`,
    `run_time`,
    `load_time`,
    `yield`,
    `performance`,
    `time_efficiency`,
    `oee`,
    `record_date`,
    `create_time`,
    `update_time`,
    `device_name`,
    `plan_quantity`,
    `plan_achievement_rate`,
    `theory_working_hour`
)
SELECT
    `id`,
    `device_id`,
    `theoretical_speed`,
    `amount`,
    `unqualified`,
    `run_time`,
    `load_time`,
    `yield`,
    `performance`,
    `time_efficiency`,
    `oee`,
    `record_date`,
    `create_time`,
    `update_time`,
    `device_name`,
    `plan_quantity`,
    `plan_achievement_rate`,
    `theory_working_hour`
FROM `dfs_metrics`.`dfs_metrics_device_oee_old`
WHERE `record_date` > CURDATE() - INTERVAL 2 DAY;




-- ==========工单整体==========
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'sale_order_number',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `sale_order_number` varchar(255) DEFAULT NULL COMMENT ''销售订单号''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'product_order_number',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `product_order_number` varchar(255) DEFAULT NULL COMMENT ''生产订单号''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'business_unit_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `business_unit_name` varchar(255) DEFAULT NULL COMMENT ''业务单元名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'business_unit_code',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `business_unit_code` varchar(255) DEFAULT NULL COMMENT ''业务单元编码''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'order_type',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `order_type` varchar(255) DEFAULT NULL COMMENT ''单据类型''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'order_type_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `order_type_name` varchar(255) DEFAULT NULL COMMENT ''单据类型名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'work_center_id',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `work_center_id` int DEFAULT NULL COMMENT ''工作中心id [附属信息]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'work_center_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `work_center_name` varchar(255) DEFAULT NULL COMMENT ''工作中心名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'production_basic_unit_code',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `production_basic_unit_code` varchar(255) DEFAULT NULL COMMENT ''生产基本单元名称 [附属信息]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'production_basic_unit_model_type',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `production_basic_unit_model_type` varchar(512) DEFAULT NULL COMMENT ''生产基本单元类型 [模型的类型，多个按,分隔]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'gid',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `gid` int DEFAULT NULL COMMENT ''车间id  [附属信息]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'gname',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `gname` varchar(255) DEFAULT NULL COMMENT ''车间名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'state',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `state` varchar(255) DEFAULT NULL COMMENT ''状态''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'state_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `state_name` varchar(255) DEFAULT NULL COMMENT ''状态名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'start_date',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `start_date` varchar(255) DEFAULT NULL COMMENT ''计划开始时间''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'end_date',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `end_date` varchar(255) DEFAULT NULL COMMENT ''计划结束时间''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'actual_start_date',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `actual_start_date` varchar(255) DEFAULT NULL COMMENT ''实际开始时间''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'actual_end_date',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `actual_end_date` varchar(255) DEFAULT NULL COMMENT ''实际结束时间''');
-- 物料
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_type_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_type_name` varchar(255) DEFAULT NULL COMMENT ''物料类型''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_sort_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_sort_name` varchar(255) DEFAULT NULL COMMENT ''物料分类''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_standard',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_standard` varchar(255) DEFAULT NULL COMMENT ''物料规格''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_drawing_number',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_drawing_number` varchar(255) DEFAULT NULL COMMENT ''物料图号''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_raw_material',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_raw_material` varchar(255) DEFAULT NULL COMMENT ''物料材质''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_sku_attr_name',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_sku_attr_name` varchar(255) DEFAULT NULL COMMENT ''物料特征参数名称 [多个按,分隔]''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_one',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_one` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段1''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_two',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_two` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段2''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_three',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_three` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段3''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_four',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_four` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段4''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_five',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_five` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段5''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_six',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_six` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段6''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_seven',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_seven` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段7''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_eight',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_eight` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段8''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_nine',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_nine` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段9''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_ten',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_ten` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段10''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_work_order',
        'material_custom_field_eleven',
        'ALTER TABLE `dfs_metrics_work_order` ADD COLUMN `material_custom_field_eleven` varchar(255) DEFAULT NULL COMMENT ''物料扩展字段11''');






-- ==========设备能耗==========
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'device_model_type',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `device_model_type` varchar(255) DEFAULT NULL COMMENT ''设备类型''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'relate_line_name',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `relate_line_name` varchar(255) DEFAULT NULL COMMENT ''关联产线名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'relate_fac_name',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `relate_fac_name` varchar(255) DEFAULT NULL COMMENT ''关联工位名称''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'energy_sharp_consumption',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `energy_sharp_consumption` double(11,4) DEFAULT NULL COMMENT ''波-耗电量''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'energy_peak_consumption',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `energy_peak_consumption` double(11,4) DEFAULT NULL COMMENT ''峰-耗电量''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'energy_shoulder_consumption',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `energy_shoulder_consumption` double(11,4) DEFAULT NULL COMMENT ''平-耗电量''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'energy_off_peak_consumption',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `energy_off_peak_consumption` double(11,4) DEFAULT NULL COMMENT ''谷-耗电量''');
call `dfs_metrics`.`proc_add_column`(
        'dfs_metrics_device_consumption_daily',
        'energy_deep_off_peak_consumption',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` ADD COLUMN `energy_deep_off_peak_consumption` double(11,4) DEFAULT NULL COMMENT ''深谷-耗电量''');



-- 删除脏数据
DELETE from dfs_table_config
WHERE table_name = 'dfs_metrics_quality_product_order_defect_daily' and field_code in ('unqualified_item_quantity', 'unqualified_product_quantity', 'unqualified_record_quantity', 'unqualified_record_item_quantity');

DELETE from dfs_table_config
WHERE table_name = 'dfs_metrics_work_order_daily' and field_code in ('human_working_hour', 'resource_working_hour');

DELETE from dfs_table_config
WHERE table_name = 'dfs_metrics_work_order' and field_code in ('human_working_hour', 'resource_working_hour');

DELETE from dfs_table_config
WHERE table_name = 'dfs_metrics_work_order_basic_unit_daily';

UPDATE dfs_table_config SET `field_name` = '不良品记录数量', `field_remark` = '流水码去重' WHERE `field_name` =  '不良品记录数量: 流水码去重';
UPDATE dfs_table_config SET `field_name` = '不良项记录数量', `field_remark` = '流水码不去重' WHERE `field_name` =  '不良项记录数量: 流水码不去重';

UPDATE dfs_table_config SET `field_name` = '成品不良品记录数量', `field_remark` = '流水码去重' WHERE `field_name` =  '成品不良品记录数量: 流水码去重';
UPDATE dfs_table_config SET `field_name` = '成品不良项记录数量', `field_remark` = '流水码不去重' WHERE`field_name` =  '成品不良项记录数量: 流水码不去重';

UPDATE dfs_table_config SET `field_name` = '不良品记录数量', `field_remark` = '流水码去重' WHERE`field_name` =  '不良品记录数：流水码去重';
UPDATE dfs_table_config SET `field_name` = '不良项记录数量', `field_remark` = '流水码不去重' WHERE`field_name` =  '不良项记录数：不去重';

UPDATE dfs_table_config SET `field_remark` = '日期，数据库实际值为yyyy-MM-dd' WHERE table_name = 'dfs_metrics_work_order_basic_unit_daily' AND `field_code` =  'record_time';
UPDATE dfs_table_config SET `field_remark` = '日期，数据库实际值为yyyy-MM-dd' WHERE table_name = 'dfs_metrics_device_oee' AND `field_code` =  'record_date';

-- 旧表增加维度标识
UPDATE dfs_table_config  SET field_define = 'DIMENSION' WHERE table_name = 'dfs_metrics_work_order_daily' AND field_code in ('material_name', 'work_center_name', 'gname');

UPDATE dfs_table_config  SET field_define = 'DIMENSION' WHERE table_name = 'dfs_metrics_work_order_hourly' AND field_code in ('material_name', 'production_basic_unit_name');

UPDATE dfs_table_config  SET field_define = 'TARGET' WHERE table_name = 'dfs_metrics_device_consumption_daily' AND field_code like'%_consumption';

-- 改表名
update dfs_table_config set table_remark = '生产日汇总-按工单(计划+投产+实际生产)' WHERE table_name = 'dfs_metrics_work_order_daily';
update dfs_target_model set target_cnname = '生产日汇总-按工单(计划+投产+实际生产)' WHERE target_name = 'workOrderDailyProgress';

update dfs_table_config set table_remark = '生产十分钟汇总-按工单' WHERE table_name = 'dfs_metrics_work_order_10min';
update dfs_target_model set target_cnname = '生产十分钟汇总-按工单' WHERE target_name = 'workOrder10min';

update dfs_table_config set table_remark = '每日能耗-按设备' WHERE table_name = 'dfs_metrics_device_consumption_daily';
update dfs_target_model set target_cnname = '每日能耗-按设备' WHERE target_name = 'deviceConsumptionDaily';

update dfs_table_config set table_remark = '设备每天OEE记录表' WHERE table_name = 'dfs_metrics_device_oee';
update dfs_target_model set target_cnname = '设备每天OEE记录表' WHERE target_name = 'deviceOeeView';

update dfs_table_config set table_remark = '质量每日统计-按成品&不良' WHERE table_name = 'dfs_metrics_quality_line_material_defect_daily';
update dfs_target_model set target_cnname = '质量每日统计-按成品&不良' WHERE target_name = 'qualityLineMaterialDefectDaily';

-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================