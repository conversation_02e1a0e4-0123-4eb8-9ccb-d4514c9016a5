-- DDL
-- 出货申请、发货记录、生产工单、生产工单用料清单新增业务单元、业务单元编码字段
call proc_add_column(
        'dfs_delivery_application_material',
        'business_unit_code',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `business_unit_code`  varchar(255) NULL COMMENT ''业务单元编码'';');
call proc_add_column(
        'dfs_delivery_application_material',
        'business_unit_name',
        'ALTER TABLE `dfs_delivery_application_material` ADD COLUMN `business_unit_name`  varchar(255) NULL COMMENT ''业务单元名称'' AFTER `business_unit_code`;');

call proc_add_column(
        'dfs_stock_delivery_material',
        'business_unit_code',
        'ALTER TABLE `dfs_stock_delivery_material` ADD COLUMN `business_unit_code`  varchar(255) NULL COMMENT ''业务单元编码'';');
call proc_add_column(
        'dfs_stock_delivery_material',
        'business_unit_name',
        'ALTER TABLE `dfs_stock_delivery_material` ADD COLUMN `business_unit_name`  varchar(255) NULL COMMENT ''业务单元名称'' AFTER `business_unit_code`;');

call proc_add_column(
        'dfs_work_order',
        'business_unit_code',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `business_unit_code`  varchar(255) NULL COMMENT ''业务单元编码'';');
call proc_add_column(
        'dfs_work_order',
        'business_unit_name',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `business_unit_name`  varchar(255) NULL COMMENT ''业务单元名称'' AFTER `business_unit_code`;');

call proc_add_column(
        'dfs_work_order_material_list_material',
        'business_unit_code',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `business_unit_code`  varchar(255) NULL COMMENT ''业务单元编码'';');
call proc_add_column(
        'dfs_work_order_material_list_material',
        'business_unit_name',
        'ALTER TABLE `dfs_work_order_material_list_material` ADD COLUMN `business_unit_name`  varchar(255) NULL COMMENT ''业务单元名称'' AFTER `business_unit_code`;');



-- 表单字段规则表删除无用字段
call proc_modify_column(
        'dfs_form_field_rule_config',
        'method',
        'ALTER TABLE `dfs_form_field_rule_config` DROP COLUMN `method`');
call proc_modify_column(
        'dfs_form_field_rule_config',
        'params',
        'ALTER TABLE `dfs_form_field_rule_config` DROP COLUMN `params`');
call proc_modify_column(
        'dfs_form_field_rule_config',
        'respParamField',
        'ALTER TABLE `dfs_form_field_rule_config` DROP COLUMN `respParamField`');

-- 新增业务单元表
CREATE TABLE IF NOT EXISTS `dfs_business_unit` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `code` varchar(100) NOT NULL COMMENT '编码',
    `name` varchar(255) NOT NULL COMMENT '名称',
    `state` varchar(32) NOT NULL COMMENT '状态： CREATED-创建 RELEASED-生效 ABANDON-弃用',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `name` (`name`),
    UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='业务单元表';





-- DML
-- 业务配置新增业务单元的全局配置
INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'globalConf', '全局配置', 'globalConf', '', NULL, 'yelinkoncall', 'yelinkoncall', '2024-09-20 12:12:48', '2024-09-20 12:12:48');
INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'businessUnitConf', '业务单元配置', 'globalConf.businessUnitConf', 'globalConf', '该配置控制全部单据内是否展示业务单元字段（包含销售、生产、采购）', 'yelinkoncall', 'yelinkoncall', '2023-10-10 12:12:48', '2024-09-20 14:24:57');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'enable', '是否启用', 'globalConf.businessUnitConf.enable', 'globalConf.businessUnitConf', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);

-- 生产工单下推生产工单用料清单配置
DELETE FROM dfs_dict WHERE `type` = "production.workOrderPushDownConfig.materialList" AND `code` = "production.workOrderPushDownConfig.materialList.pushBatch";
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.pushBatch";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "production.workOrderPushDownConfig.materialList.pushBatch";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.pushBatch";
--
DELETE FROM dfs_order_push_down_config_value_dict WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.jumpPage";
DELETE FROM dfs_order_push_down_item WHERE instance_type = "production.workOrderPushDownConfig.materialList.jumpPage";
DELETE FROM dfs_order_push_down_config_value WHERE config_full_path_code = "production.workOrderPushDownConfig.materialList.jumpPage";
--
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('production.workOrderPushDownConfig.materialList.bomPush', '按BOM下推', 'production.workOrderPushDownConfig.materialList', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.originalOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', '[2,3,4,5]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.workOrderPushDownConfig.materialList.bomPush.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.workOrderPushDownConfig.materialList.bomPush.description', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式', 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"multiLevelBomNotFilter\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'materialChoose', '物料选择', 'production.workOrderPushDownConfig.materialList.bomPush.materialChoose', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/material/choose', 'get', NULL, 'code,name', '[\"workOrderMaterialBom\",\"procedureMaterialUsed\"]', '\"workOrderMaterialBom\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'production.workOrderPushDownConfig.materialList.bomPush.appId', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.workOrderPushDownConfig.materialList.bomPush.url', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/order-model/workOrder-materials?pushDownOrigin=workOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.sourceOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialList\"', NULL);
-- 生产工单下推生产工单用料清单内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'production.workOrderPushDownConfig.materialList',  '下推生产工单用料清单', 'RULE', 1, 1, 'production.workOrderPushDownConfig.materialList.bomPush', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'originalOrderStates', '源单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.originalOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[2,3,4,5]', '[2,3,4,5]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'isSupportMaterialLineReversePushDown', '反选物料行', 'production.workOrderPushDownConfig.materialList.bomPush.isSupportMaterialLineReversePushDown', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceConf', 'description', '描述', 'production.workOrderPushDownConfig.materialList.bomPush.description', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'targetOrderState', '目标单据状态', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderStates', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'bomSplitType', 'BOM拆分方式', 'production.workOrderPushDownConfig.materialList.bomPush.bomSplitType', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"multiLevelBomNotFilter\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'filterMaterialSorts', '物料分类过滤', 'production.workOrderPushDownConfig.materialList.bomPush.filterMaterialSorts', 'production.workOrderPushDownConfig.materialList.bomPush', 'select-multiple', 'api', '/api/materials/sort', 'get', NULL, 'code,name', '[\"purchase\",\"outsourcing\"]', '[\"purchase\",\"outsourcing\"]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetConf', 'materialChoose', '物料选择', 'production.workOrderPushDownConfig.materialList.bomPush.materialChoose', 'production.workOrderPushDownConfig.materialList.bomPush', 'select', 'api', '/api/work_order/material/lists/material/choose', 'get', NULL, 'code,name', '[\"workOrderMaterialBom\",\"procedureMaterialUsed\"]', '\"workOrderMaterialBom\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'appId', 'appId', 'production.workOrderPushDownConfig.materialList.bomPush.appId', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"com.yk.genieos.ams.order-model\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'url', '前端路由', 'production.workOrderPushDownConfig.materialList.bomPush.url', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/dfs/order-model/workOrder-materials?pushDownOrigin=workOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'sourceOrderType', '源单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.sourceOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `group_type`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sysConf', 'targetOrderType', '目标单据类型编码', 'production.workOrderPushDownConfig.materialList.bomPush.targetOrderType', 'production.workOrderPushDownConfig.materialList.bomPush', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrderMaterialList\"', NULL);
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.workOrderPushDownConfig.materialList.bomPush';


-- 业务配置-采购收料小程序配置-自动下推入库单配置
INSERT INTO `dfs_business_config` (`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES (null, 'receiptOrderAppConfig', '采购收料小程序配置', 'purchase.receiptOrderAppConfig', 'purchase', NULL, 'admin', 'admin', now(), now());
INSERT INTO `dfs_business_config` (`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES (null, 'autoPushPurchaseIn', '自动下推入库单配置', 'purchase.receiptOrderAppConfig.autoPushPurchaseIn', 'purchase.receiptOrderAppConfig', '启用时将目标单据下所选物料状态改为已收料并更改单据状态为完成以及下推采购入库单（创建态）', 'admin', 'admin', now(), now());
INSERT INTO `dfs_business_config_value` (`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES (null, 'enable', '是否启用', 'purchase.receiptOrderAppConfig.autoPushPurchaseIn.enable', 'purchase.receiptOrderAppConfig.autoPushPurchaseIn', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);

-- 更新版本号信息
UPDATE `dfs_business_config_value` SET `value` = '[{\"name\":\"SMT版本\",\"imageName\":\"smt-server\"},{\"name\":\"LYSMT版本\",\"imageName\":\"dfs-lysmt\"},{\"name\":\"BATTERY版本\",\"imageName\":\"dfs-battery\"},{\"name\":\"QMS版本\",\"imageName\":\"qms-server\"},{\"name\":\"QMS3.0版本\",\"imageName\":\"qms-product-inspection\"},{\"name\":\"PMS版本\",\"imageName\":\"pms-server\"},{\"name\":\"WMS版本\",\"imageName\":\"wms\"},{\"name\":\"APS版本\",\"imageName\":\"aps-server\"},{\"name\":\"TPM版本\",\"imageName\":\"tpm-server\"}]' WHERE `value_full_path_code` = 'general.version.serviceVersion';

call init_number_rules(98, '工序编号');

-- 生产工单用料清单导入
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10012100', '下载默认转换模板', 'workOrder.materials:export-default-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10012110', '下载自定义转换模板', 'workOrder.materials:export-custom-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10012120', '上传自定义转换模板', 'workOrder.materials:import-custom-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10012130', '下载导入模板', 'workOrder.materials:import-template', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10012140', '导入数据', 'workOrder.materials:import-data', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '', 1);
INSERT INTO sys_permissions (id, name, `path`, description, create_by, create_time, update_by, update_time, status, `method`, parent_id, `type`, is_web, is_back_stage, parent_path, is_enable, sort, service_name, is_sys_data) VALUES('10012150', '查看日志', 'workOrder.materials:import-log', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '10012', 2, 1, 0, '/order-model/workOrder-materials', 1, NULL, '', 1);
call init_new_role_permission('100121%');


-- 业务单元
INSERT INTO `sys_route` (`path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES ('/factory-model/business-unit', '/factory-model', '业务单元', NULL, '业务单元', NULL, 'dfs', NULL, NULL, NULL, 0, 1, NULL, '');
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11309', '业务单元', '/factory-model/business-unit', NULL, NULL, NULL, NULL, '2024-10-11 10:59:51', 'enable', 'GET', '113', 1, 1, 1, NULL, 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11309010', '新增', 'business-unit:add', NULL, NULL, NULL, NULL, '2024-10-11 14:45:58', 'enable', 'POST', '11309', 2, 1, 1, '/factory-model/business-unit', 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11309020', '编辑', 'business-unit:update', NULL, NULL, NULL, NULL, '2024-10-11 14:46:00', 'enable', 'PUT', '11309', 2, 1, 1, '/factory-model/business-unit', 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('11309040', '删除', 'business-unit:delete', NULL, NULL, NULL, NULL, '2024-10-11 14:46:05', 'enable', 'DELETE', '11309', 2, 1, 1, '/factory-model/business-unit', 1, NULL, '', 1);
call init_new_role_permission('11309%');

-- 下推单据的applicationId变更
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.order-model.product-order\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bomPush.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.order-model.product-order\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-list\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-list\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.bom.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.subcontractOrder.bom.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.dfs.order-model.production-workorder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.applicationId-2';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.dfs.order-model.production-workorder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.workOrder.craftPush.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.dfs.scc.order-model.shipment_application\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.deliveryApplication.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.order-model.production-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.order-model.production-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.dfs.order-model.workOrder-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.dfs.order-model.workOrder-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.delivery-order\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.applicationId';

UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.order-model.product-order\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.productOrder.bomPush.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.order-model.product-order\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseRequest.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseRequest.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-demand\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-list\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.purchaseOrder.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.purchasing-list\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.craftRouteBatch.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.subcontractOrder.craftRouteBatch.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.subcontractOrder.bom.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.osm.outsourcingOrder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.subcontractOrder.bom.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.dfs.order-model.production-workorder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.workOrder.craftPush.applicationId-2';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.dfs.order-model.production-workorder\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.workOrder.craftPush.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.dfs.scc.order-model.shipment_application\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.deliveryApplication.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.order-model.production-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.order-model.production-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.dfs.order-model.workOrder-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.pushBatch.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.dfs.order-model.workOrder-materials\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.materialList.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"new.yk.genieos.ams.scc.procurement-management.delivery-order\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.applicationId';

-- 删除`是否启用`的下推配置项
DELETE FROM `dfs_order_push_down_config_value_dict` WHERE `value_name` = '是否启用';
DELETE FROM `dfs_order_push_down_config_value` WHERE `value_name` = '是否启用';

UPDATE `dfs_order_push_down_config_value` SET `value` = null WHERE `value` = 'null';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = null WHERE `value` = 'null';

-- 下推配置的appId统一重命名为applicationId
UPDATE `dfs_order_push_down_config_value` SET `value_code` = 'applicationId', `value_name` = 'applicationId' WHERE `value_code` = 'appId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value_code` = 'applicationId', `value_name` = 'applicationId' WHERE `value_code` = 'appId';
UPDATE `dfs_order_push_down_config_value` SET `value_full_path_code` = REPLACE(`value_full_path_code`, '.appId', '.applicationId');
UPDATE `dfs_order_push_down_config_value_dict` SET `value_full_path_code` = REPLACE(`value_full_path_code`, '.appId', '.applicationId');

-- 调整表单配置字段
UPDATE `dfs_form_field_config` SET `field_code` = 'orderMaterialRemark' WHERE `full_path_code` = 'saleOrder.list.material' AND `field_code` = 'remark';
UPDATE `dfs_form_field_config` SET `field_name` = 'orderMaterialRemark' WHERE `full_path_code` = 'saleOrder.list.material' AND `field_name` = 'remark';

-- 更新下推路由配置
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/ams/supply-chain-collaboration/procurement-management/purchasing-demand?pushDownOrigin=productOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/ams/supply-chain-collaboration/procurement-management/purchasing-list?pushDownOrigin=purchaseRequest\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/ams/supply-chain-collaboration/procurement-management/delivery-order?pushDownOrigin=purchaseOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/dfs/supply-chain-collaboration/order-model/shipment_application?pushDownOrigin=saleOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'saleOrder.pushDownConfig.deliveryApplication.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/ams/supply-chain-collaboration/procurement-management/purchasing-demand?pushDownOrigin=productOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productOrderPushDownConfig.purchaseRequest.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/ams/supply-chain-collaboration/procurement-management/purchasing-list?pushDownOrigin=purchaseRequest\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/ams/supply-chain-collaboration/procurement-management/delivery-order?pushDownOrigin=purchaseOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/dfs/supply-chain-collaboration/order-model/shipment_application?pushDownOrigin=saleOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'saleOrder.pushDownConfig.deliveryApplication.jumpPage.url';

-- 更改仓库的路由和applicationId
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.salesIssueReceipt.salesIssueDoc\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/salesIssueReceipt/salesIssueDoc?pushDownOrigin=saleOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc?pushDownOrigin=saleOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage?pushDownOrigin=purchaseOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.purchaseOutOfStorage.purchaseReturnOutOfStorage\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage?pushDownOrigin=purchaseReturnApplication\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderSupplement\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.productInOrOut.applicationReturn\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/applicationReturn?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderComplete\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderComplete\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productionIn.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete?pushDownOrigin=productOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productionIn.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut?pushDownOrigin=productMaterialsList\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderSupplement\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement?pushDownOrigin=productMaterialsList\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.url';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"com.luotu.wms.cougnyManage.allocation\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value` SET `value` = '\"/wms/warehousingDistribution/cougnyManage/allocation?pushDownOrigin=productMaterialsList\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage.url';

UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.salesIssueReceipt.salesIssueDoc\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/salesIssueReceipt/salesIssueDoc?pushDownOrigin=saleOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.salesIssueReceipt.salesMaterialReturnReceiptDoc\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/salesIssueReceipt/salesMaterialReturnReceiptDoc?pushDownOrigin=saleOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.purchaseOutOfStorage.purchasePutInStorage\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage?pushDownOrigin=purchaseOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.purchaseOutOfStorage.purchaseReturnOutOfStorage\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage?pushDownOrigin=purchaseReturnApplication\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderSupplement\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.productInOrOut.applicationReturn\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/applicationReturn?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderComplete\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete?pushDownOrigin=workOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderComplete\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productionIn.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderComplete?pushDownOrigin=productOrder\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productOrderPushDownConfig.productionIn.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderTakeOut\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderTakeOut?pushDownOrigin=productMaterialsList\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.productInOrOut.workOrderSupplement\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/productInOrOut/workOrderSupplement?pushDownOrigin=productMaterialsList\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.url';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"com.luotu.wms.cougnyManage.allocation\"' WHERE `value_code` = 'applicationId' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage.applicationId';
UPDATE `dfs_order_push_down_config_value_dict` SET `value` = '\"/wms/warehousingDistribution/cougnyManage/allocation?pushDownOrigin=productMaterialsList\"' WHERE `value_code` = 'url' AND `value_full_path_code` = 'production.productMaterialsListPushDownConfig.transferOrder.jumpPage.url';

UPDATE `dfs_order_push_down_config` SET `name` = '采购收料' WHERE `name` = '采购收货';
UPDATE `dfs_order_push_down_item` SET `name` = '下推采购收料单(新建页签)' WHERE `name` = '下推采购收货单(新建页签)';
-- 修改下推菜单名称
UPDATE `dfs_order_push_down_config` SET `code` = 'workOrderMaterialList' WHERE `full_path_code` = 'production.workOrderPushDownConfig.materialList';
UPDATE `dfs_order_push_down_config` SET `code` = 'productOrderMaterialList' WHERE `full_path_code` = 'production.productOrderPushDownConfig.productMaterialsList';

-- 删除下推配置
DELETE FROM `dfs_order_push_down_config` WHERE `full_path_code` = 'production.workOrderPushDownConfig.takeOutApplication';
DELETE FROM `dfs_order_push_down_config_value` WHERE `value_full_path_code` like 'production.workOrderPushDownConfig.takeOutApplication%';
DELETE FROM `dfs_order_push_down_config_value_dict` WHERE `value_full_path_code` like 'production.workOrderPushDownConfig.takeOutApplication%';

-- 更新下推菜单名称
UPDATE `dfs_order_push_down_config` SET `name` = REPLACE (`name`, '(wms2.25版本后支持)', '(当前版本暂不支持)') WHERE `full_path_code` in ('production.productMaterialsListPushDownConfig.outputPickingProduct', 'production.productMaterialsListPushDownConfig.workOrderSupplement', 'production.productMaterialsListPushDownConfig.transferOrder', 'production.takeOutApplicationPushDownConfig.takeOutOutbound', 'purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut');

-- 表单配置：委外订单"供应商名称"支持编辑
UPDATE `dfs_form_field_rule_config` SET `edit_gray` = 0, `is_edit` = 1 WHERE `field_name_full_path_code` = 'subcontractOrder.edit' AND `field_code` = 'supplierName';
