-- 字段配置中，将采购收料单把遗漏的字段配置移动到批次字段类型中
UPDATE `dfs_form_field_rule_config` SET `module_code` = 'batchField' WHERE `route` = '/supply-chain-collaboration/procurement-management/delivery-order' AND `field_code` in ('barCode','count','relateInspectOrder','qualifiedQuantity','unqualifiedQuantity','rejectedQty','warehouseStateName','concessionAcceptanceQuantity','returnAmount','industrialWasteQuantity','materialWasteQuantity','rejectedReason','receiveStateName','receiveTime','inspectStateName','createByNickName','createTime');
-- 误删除规则，现需在采购收料单物料行添加物料行相关字段
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'warehouseStateName', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'relateInspectOrder', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'concessionAcceptanceQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'industrialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'materialWasteQuantity', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'rejectedQty', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCreate', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByRelease', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByFinish', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByClosed', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.editByCancel', 'purchaseReceiptOrder.edit', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'rejectedReason', 1, 0, 0, 0, NULL, NULL, 0, 0, 1, 1, 1, 1, 1, 1, 'baseMaterialLineField');
