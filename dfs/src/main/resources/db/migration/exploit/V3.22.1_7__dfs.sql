-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ============== 变更时间记录格式 ================

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_work_order_summary_monthly',
        'record_time',
        'ALTER TABLE `dfs_metrics_work_order_summary_monthly` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_top_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_top_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_team_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_team_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_staff_work_order_daily',
        'record_time',
        'ALTER TABLE `dfs_metrics_staff_work_order_daily` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_staff_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_staff_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_sale_order_summary_monthly',
        'record_month',
        'ALTER TABLE `dfs_metrics_sale_order_summary_monthly` MODIFY COLUMN `record_month` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_sale_order_summary_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_sale_order_summary_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_sale_order_material_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_sale_order_material_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_quality_product_order_defect_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_quality_product_order_defect_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_quality_line_material_defect_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_quality_line_material_defect_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_quality_line_material_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_quality_line_material_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_quality_defect_distribution_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_quality_defect_distribution_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order_summary_monthly',
        'record_month',
        'ALTER TABLE `dfs_metrics_product_order_summary_monthly` MODIFY COLUMN `record_month` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order_monthly',
        'record_month',
        'ALTER TABLE `dfs_metrics_product_order_monthly` MODIFY COLUMN `record_month` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_product_order_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_product_order_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_material_trace_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_material_trace_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_maintain_team_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_maintain_team_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_maintain_monthly',
        'record_time',
        'ALTER TABLE `dfs_metrics_maintain_monthly` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_maintain_line_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_maintain_line_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_monthly',
        'record_month',
        'ALTER TABLE `dfs_metrics_line_monthly` MODIFY COLUMN `record_month` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_material_defect_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_line_material_defect_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_line_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_line_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_increase_order_weekly',
        'record_time',
        'ALTER TABLE `dfs_metrics_increase_order_weekly` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_increase_base_weekly',
        'record_time',
        'ALTER TABLE `dfs_metrics_increase_base_weekly` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_incoming_inspection_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_incoming_inspection_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_exception_classify_monthly',
        'record_time',
        'ALTER TABLE `dfs_metrics_exception_classify_monthly` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');

call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_exception_classify_daily',
        'record_time',
        'ALTER TABLE `dfs_metrics_exception_classify_daily` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_end_material_daily',
        'record_date',
        'ALTER TABLE `dfs_metrics_end_material_daily` MODIFY COLUMN `record_date` date NOT NULL COMMENT ''记录日期''');


call `dfs_metrics`.`proc_modify_column`(
        'dfs_metrics_device_consumption_daily',
        'record_time',
        'ALTER TABLE `dfs_metrics_device_consumption_daily` MODIFY COLUMN `record_time` date NOT NULL COMMENT ''记录日期''');

-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.22.1.1=======================================================