-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- *该脚本需要在V3_18_1_4__JavaMigration后执行

-- 脏数据
DELETE FROM dfs_table_config WHERE `table_schema` = 'dfs_metrics' AND `table_name` =  'dfs_metrics_sale_order_material_daily' AND `field_code` = 'time`';

UPDATE dfs_table_config SET `field_name` = '不良品记录数量', `field_remark` = '流水码去重' WHERE `field_name` =  '不良品记录数量: 流水码去重';
UPDATE dfs_table_config SET `field_name` = '不良项记录数量', `field_remark` = '流水码不去重' WHERE `field_name` =  '不良项记录数量: 流水码不去重';

UPDATE dfs_table_config SET `field_name` = '成品不良品记录数量', `field_remark` = '流水码去重' WHERE `field_name` =  '成品不良品记录数量: 流水码去重';
UPDATE dfs_table_config SET `field_name` = '成品不良项记录数量', `field_remark` = '流水码不去重' WHERE`field_name` =  '成品不良项记录数量: 流水码不去重';

UPDATE dfs_table_config SET `field_name` = '不良品记录数量', `field_remark` = '流水码去重' WHERE`field_name` =  '不良品记录数：流水码去重';
UPDATE dfs_table_config SET `field_name` = '不良项记录数量', `field_remark` = '流水码不去重' WHERE`field_name` =  '不良项记录数：不去重';

UPDATE dfs_table_config SET `field_name` = '月份', `field_remark` = 'yyyy-MM' WHERE `field_name` =  '月份: yyyy-MM';
UPDATE dfs_table_config SET `field_name` = '返修良品数', `field_remark` = '维修判定: 不报废' WHERE `field_name` =  '返修良品数（维修判定: 不报废）';
UPDATE dfs_table_config SET `field_name` = '当日返修良品数', `field_remark` = '维修判定: 不报废' WHERE `field_name` =  '当日返修良品数（维修判定: 不报废）';
UPDATE dfs_table_config SET `field_name` = '理论数量', `field_remark` = '产成品bom推出来的' WHERE `field_name` =  '理论数量： 产成品bom推出来的';
UPDATE dfs_table_config SET `field_name` = '流水码标记为产出的数量', `field_remark` = '用于计算直通率' WHERE `field_name` =  '流水码标记为产出的数量：用于计算直通率';
UPDATE dfs_table_config SET `field_name` = '所属周的日期', `field_remark` = '按周日为第一天算' WHERE `field_name` =  '所属周的日期（按周日为第一天算）';
UPDATE dfs_table_config SET `field_name` = '唯一标识', `field_remark` = 'category+record_time' WHERE `field_name` =  '唯一标识: category+record_time';
UPDATE dfs_table_config SET `field_name` = '订单计划总数', `field_remark` = '所有工单物料计划生产数量之和' WHERE `field_name` =  '订单计划总数:  所有工单物料计划生产数量之和';
UPDATE dfs_table_config SET `field_name` = '订单已生产总数', `field_remark` = '所有工单物料已生产数量之和' WHERE `field_name` =  '订单已生产总数:  所有工单物料已生产数量之和';
UPDATE dfs_table_config SET `field_name` = '记录时间', `field_remark` = '取整点, 统计包含整个小时内的' WHERE `field_name` =  '记录时间(取整点) 统计包含整个小时内的';
UPDATE dfs_table_config SET `field_name` = '生产时长', `field_remark` = '工单实际工时sum' WHERE `field_name` =  '生产时长：工单实际工时sum';
UPDATE dfs_table_config SET `field_name` = '记录时间', `field_remark` = '每整点' WHERE `field_name` =  '记录时间，每整点';
UPDATE dfs_table_config SET `field_name` = '维修合格数', `field_remark` = '*没有维修结果的均算合格' WHERE `field_name` =  '维修合格数: *没有维修结果的均算合格';
UPDATE dfs_table_config SET `field_name` = '单品不良项数', `field_remark` = '1,2,3,...,N' WHERE `field_name` =  '单品不良项数: 1,2,3,...,N';
UPDATE dfs_table_config SET `field_name` = '对应不良项数的不良品件数', `field_remark` = '出现次数' WHERE `field_name` =  '对应不良项数的不良品件数（出现次数）';
UPDATE dfs_table_config SET `field_name` = '制造单元名称' WHERE `table_name` =  'dfs_metrics_line_daily' AND `table_schema` = 'dfs_metrics' AND `field_code` = 'line_name';
UPDATE dfs_table_config SET `field_name` = '不良数' WHERE `table_name` =  'dfs_metrics_product_order_procedure' AND `table_schema` = 'dfs_metrics' AND `field_code` = 'unqualified_quantity';
UPDATE dfs_table_config SET `field_name` = '直通数' WHERE `table_name` =  'dfs_metrics_product_order_procedure' AND `table_schema` = 'dfs_metrics' AND `field_code` = 'direct_access_quantity';
UPDATE dfs_table_config SET `field_name` = '唯一键' WHERE `table_name` =  'dfs_metrics_line_daily' AND `table_schema` = 'dfs_metrics' AND `field_code` = 'uni_code';
UPDATE dfs_table_config SET `field_remark` = '所有上一道工序过站完成 - 当前工具过站完成 / 所有上一道工序过站完成' WHERE `table_name` =  'dfs_metrics_work_order_procedure' AND `table_schema` = 'dfs_metrics' AND `field_code` = 'procedure_attrition_rate';
UPDATE dfs_table_config SET `field_remark` = '所有上一道工序过站完成 - 当前工具过站完成 / 所有上一道工序过站完成' WHERE `table_name` =  'dfs_metrics_product_order_procedure' AND `table_schema` = 'dfs_metrics' AND `field_code` = 'procedure_attrition_rate';

-- 设备指标表-定义更新
UPDATE `dfs_table_config` SET `field_define` = 'TARGET' WHERE `table_schema` = 'dfs_metrics' AND `table_name` = 'dfs_metrics_device_oee' AND `field_code` in ('theoretical_speed', 'amount', 'unqualified', 'run_time', 'load_time', 'yield', 'performance', 'time_efficiency', 'oee');
UPDATE `dfs_table_config` SET `field_define` = 'TARGET' WHERE `table_schema` = 'dfs_metrics' AND `table_name` = 'dfs_metrics_device_state_daily' AND `field_code` in ('running', 'pause', 'stop');




-- 口径备注
-- 销售订单-物料 整体统计
UPDATE dfs_table_config SET `field_remark` = '原表数据' WHERE `table_name` =  'dfs_metrics_sale_order_material' AND `table_schema` = 'dfs_metrics'
AND `field_code` in ('sales_quantity', 'plan_quantity', 'produce_quantity', 'unqualified_quantity', 'applied_shipment_quantity', 'applied_quantity', 'out_stock_quantity');

UPDATE dfs_table_config SET `field_remark` = '计划时间 < 发货时间时? ($计划数量 - $发货数量) : 0' WHERE `table_name` =  'dfs_metrics_sale_order_material' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delay_quantity';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / ($产出数量 + $不良数量)' WHERE `table_name` =  'dfs_metrics_sale_order_material' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'qualified_rate';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / ($计划数量)' WHERE `table_name` =  'dfs_metrics_sale_order_material' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'finish_rate';



-- 销售订单料-物料 每日统计
UPDATE dfs_table_config SET `field_remark` = 'sum(工单每日计划)' WHERE `table_name` =  'dfs_metrics_sale_order_material_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'plan_quantity';

UPDATE dfs_table_config SET `field_remark` = 'sum(工单每日产出)' WHERE `table_name` =  'dfs_metrics_sale_order_material_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'produce_quantity';

UPDATE dfs_table_config SET `field_remark` = 'sum(工单每日不良)' WHERE `table_name` =  'dfs_metrics_sale_order_material_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'unqualified_quantity';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / ($产出数量 + $不良数量)' WHERE `table_name` =  'dfs_metrics_sale_order_material_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'qualified_rate';

UPDATE dfs_table_config SET `field_remark` = 'count(不良记录)' WHERE `table_name` =  'dfs_metrics_sale_order_material_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'unqualified_record_quantity';

UPDATE dfs_table_config SET `field_remark` = 'count(不良记录流水码去重)' WHERE `table_name` =  'dfs_metrics_sale_order_material_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'unqualified_record_item_quantity';


-- 销售订单 每月汇总
UPDATE dfs_table_config SET `field_remark` = '统计维度：销售订单计数' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'all_order_count';

UPDATE dfs_table_config SET `field_remark` = '统计维度：销售订单计数; 统计条件：状态为已发' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_order_count';

UPDATE dfs_table_config SET `field_remark` = '统计维度：销售订单计数; 统计条件：状态为已发货 && 发货时间 <= 要货时间' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_timely_order_count';

UPDATE dfs_table_config SET `field_remark` = '统计维度：销售订单计数; 统计条件：状态为已发货 && 发货日期 > 要货日期' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_delay_order_count';

UPDATE dfs_table_config SET `field_remark` = '统计维度：销售订单计数; 统计条件：状态为(未/部分)发货 && 当前日期 > 要货日期' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'no_delivered_delay_order_count';

UPDATE dfs_table_config SET `field_remark` = '统计维度：销售的物料数求和' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'all_order_material_quantity';

UPDATE dfs_table_config SET `field_remark` = '统计维度：发货的物料数求和; 统计条件：状态为(已/部分)发货' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_order_material_quantity';

UPDATE dfs_table_config SET `field_remark` = '统计维度：发货的物料数求和; 统计条件：状态为(已/部分)发货 && 发货时间 <= 要货时间' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_timely_order_material_quantity';

UPDATE dfs_table_config SET `field_remark` = '统计维度：未发货的物料数求和; 统计条件：状态为(未/部分)发货' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'no_delivered_order_material_quantity';

UPDATE dfs_table_config SET `field_remark` = '统计维度：未发货的物料数求和; 统计条件：状态为(未/部分)发货 && 当前日期 > 要货日期' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'no_delivered_delay_order_material_quantity';

UPDATE dfs_table_config SET `field_remark` = '$已交付订单数/ $总订单数' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_order_rate';

UPDATE dfs_table_config SET `field_remark` = '$及时交付订单数 / $已交付订单数' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_timely_order_rate';

UPDATE dfs_table_config SET `field_remark` = '$已交付产品总数 / $总订单数' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'finish_order_material_rate';

UPDATE dfs_table_config SET `field_remark` = '$及时交付产品总数 / $已交付产品总数' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_timely_order_material_rate';

UPDATE dfs_table_config SET `field_remark` = '($及时交付订单数／$总订单数) × 0.4 + ($及时交付产品总数/$销售产品总数) × 0.6' WHERE `table_name` =  'dfs_metrics_sale_order_summary_monthly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'delivered_timely_rate';



-- 生产工单整体
UPDATE dfs_table_config SET `field_remark` = '原表数据' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` in ('plan_quantity', 'produce_quantity', 'unqualified_quantity');

UPDATE dfs_table_config SET `field_remark` = '未启用' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` in ('inspection_quantity', 'inbound_quantity');

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / $实际投入工时' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'actual_capacity';

UPDATE dfs_table_config SET `field_remark` = '打卡记录小时(按工作日历)' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'actual_working_hour';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / 标准产能 * 理论生产人数; 未配理论生产人数则取1' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'theory_working_hour';

UPDATE dfs_table_config SET `field_remark` = '先看工艺工序，没有则看产能' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'single_theory_hour';

UPDATE dfs_table_config SET `field_remark` = '停线的告警中, 影响工时字段求和' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'stop_line_hour';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / $计划数量' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'finish_rate';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / $产出数量 + $不良数量)' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'qualified_rate';

UPDATE dfs_table_config SET `field_remark` = '$理论产出工时 / $实际投入工时' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'product_efficiency_rate';

UPDATE dfs_table_config SET `field_remark` = '$生产效率 - 1' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'achievements';

UPDATE dfs_table_config SET `field_remark` = '维修记录中barcode去重计数' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'repair_quantity';

UPDATE dfs_table_config SET `field_remark` = '维修记录中结果不为报废的' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'repair_qualified_quantity';

UPDATE dfs_table_config SET `field_remark` = '$返修数 / ($产出数量 + $返修数 - $返修良品数)' WHERE `table_name` =  'dfs_metrics_work_order' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'repair_quantity_rate';


-- 生产工单每日
UPDATE dfs_table_config SET `field_remark` = '未启用' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` in ('inspection_quantity', 'inbound_quantity', 'start_work_time');

UPDATE dfs_table_config SET `field_remark` = '工单计划表的计划数' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'plan_quantity';

UPDATE dfs_table_config SET `field_remark` = '工单每日表的产出数' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'produce_quantity';

UPDATE dfs_table_config SET `field_remark` = '工单每日表的不良数' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'unqualified_quantity';

UPDATE dfs_table_config SET `field_remark` = '该工单打卡人去重' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'working_people_quantity';

UPDATE dfs_table_config SET `field_remark` = '打卡所有关联的打卡记录' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'actual_working_hour';

UPDATE dfs_table_config SET `field_remark` = '打卡所有关联的打卡记录(按工作日历)' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'actual_working_hour';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / 标准产能 * 理论生产人数' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'theory_working_hour';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / $产出数量 + $不良数量)' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'qualified_rate';

UPDATE dfs_table_config SET `field_remark` = '$理论产出工时 / $实际投入工时' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'product_efficiency_rate';

UPDATE dfs_table_config SET `field_remark` = '$生产效率 - 1' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'achievements';

UPDATE dfs_table_config SET `field_remark` = '维修记录中barcode去重计数' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'repair_quantity';

UPDATE dfs_table_config SET `field_remark` = '维修记录中结果不为报废的' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'repair_qualified_quantity';

UPDATE dfs_table_config SET `field_remark` = '$返修数 / ($产出数量 + $返修数 - $返修良品数)' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'repair_quantity_rate';

UPDATE dfs_table_config SET `field_remark` = '当日参与报工的流水码' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'flow_code_report_quantity';

UPDATE dfs_table_config SET `field_remark` = '当日参与报工的流水码去重 - 此批流水码有维修记录的' WHERE `table_name` =  'dfs_metrics_work_order_daily' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'direct_access_quantity';


-- 生产工单每小时
UPDATE dfs_table_config SET `field_remark` = '工单表的产出数: 全量快照根据时间差相减' WHERE `table_name` =  'dfs_metrics_work_order_hourly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'produce_quantity';

UPDATE dfs_table_config SET `field_remark` = '工单表的不良数: 全量快照根据时间差相减' WHERE `table_name` =  'dfs_metrics_work_order_hourly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'unqualified_quantity';

UPDATE dfs_table_config SET `field_remark` = '工单表的投入数: 全量快照根据时间差相减' WHERE `table_name` =  'dfs_metrics_work_order_hourly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'input_quantity';

UPDATE dfs_table_config SET `field_remark` = '$投入数量 - $产出数量: 全量快照根据时间差相减' WHERE `table_name` =  'dfs_metrics_work_order_hourly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'making_quantity';

UPDATE dfs_table_config SET `field_remark` = '未启用' WHERE `table_name` =  'dfs_metrics_work_order_hourly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'inspection_quantity';

UPDATE dfs_table_config SET `field_remark` = '$产出数量 / $产出数量 + $不良数量)' WHERE `table_name` =  'dfs_metrics_work_order_hourly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'qualified_rate';

UPDATE dfs_table_config SET `field_remark` = '参与报工的流水码去重 - 此批流水码有维修记录的' WHERE `table_name` =  'dfs_metrics_work_order_hourly' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'direct_access_quantity';


-- 生产工单10分钟
UPDATE dfs_table_config SET `field_remark` = '工单表的产出数: 全量快照根据时间差相减' WHERE `table_name` =  'dfs_metrics_work_order_10min' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'produce_quantity';

UPDATE dfs_table_config SET `field_remark` = '工单表的不良数: 全量快照根据时间差相减' WHERE `table_name` =  'dfs_metrics_work_order_10min' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'unqualified_quantity';

UPDATE dfs_table_config SET `field_remark` = '工单表的投入数: 全量快照根据时间差相减' WHERE `table_name` =  'dfs_metrics_work_order_10min' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'input_quantity';

UPDATE dfs_table_config SET `field_remark` = '打卡人员去重' WHERE `table_name` =  'dfs_metrics_work_order_10min' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'working_people_quantity';

UPDATE dfs_table_config SET `field_remark` = '参与报工的流水码去重 - 此批流水码有维修记录的' WHERE `table_name` =  'dfs_metrics_work_order_10min' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'direct_access_quantity';


-- 生产工单工序
UPDATE dfs_table_config SET `field_remark` = '过站记录流水码去重' WHERE `table_name` =  'dfs_metrics_work_order_procedure' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'crossing_quantity';

UPDATE dfs_table_config SET `field_remark` = '过站记录为不良，流水码去重' WHERE `table_name` =  'dfs_metrics_work_order_procedure' AND `table_schema` = 'dfs_metrics'
AND `field_code` = 'unqualified_quantity';



-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.18.1.1=======================================================

