-- DDL
-- 表格字段修改记录
CREATE TABLE IF NOT EXISTS dfs_table_modify_record
(
    `id`                 int(11)      NOT NULL AUTO_INCREMENT,
    `table_name`         varchar(100) NOT NULL COMMENT '表名',
    `column_name`        varchar(100) NOT NULL COMMENT '字段名',
    `operation`          varchar(100) DEFAULT NULL COMMENT '操作类型',
    `record_unique_code` varchar(100) DEFAULT NULL COMMENT '记录唯一标识',
    `old_value`          varchar(100) DEFAULT NULL COMMENT '旧值',
    `new_value`          varchar(100) DEFAULT NULL COMMENT '新值',
    `changed_by`         varchar(100),
    `changed_at`         TIMESTAMP    DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='表格监控记录';

-- 监听flyway checksum修改记录
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS trg_audit_flyway_schema_history_update
    AFTER UPDATE
    ON flyway_schema_history
    FOR EACH ROW
BEGIN
    INSERT INTO dfs_table_modify_record (table_name, column_name, operation, old_value, new_value, record_unique_code,
                                         changed_by)
    VALUES ('flyway_schema_history', 'checksum', 'UPDATE', OLD.checksum, NEW.checksum, OLD.version, CURRENT_USER());
END;
DELIMITER ;

-- DML
-- 生产订单的工单工艺字段改为 工艺配置  字段
UPDATE `dfs_form_field_config` SET `field_name` = '工艺配置' WHERE `field_code` = 'haveCraft' AND `type_code` = 'sysField' AND `full_path_code` in ('productOrder.list', 'productOrder.edit', 'productOrder.detail');


-- 字段配置 - 订单报工小程序-报工记录
INSERT INTO `dfs_form_config` (`type`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`)
VALUES ('app', 'orderReportWorkReportRecord', '报工记录', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.productReportPage', NULL, 'admin', 'admin', NOW(), NOW(), 'detail', '1');

call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "finishCount", "完成数量", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "unqualified", "不良数量", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "relateResources", "关联资源", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "operatorName", "操作员", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "scaleFactor", "单重", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "totalScale", "总重", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "qualityLevel", "质量等级", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "batch", "批次号", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "shiftType", "班次", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "productionBasicUnitName", "生产基本单元", "baseField");
call proc_add_form_field_module("orderReportApp.orderReportWorkReportRecord", "reportEndTime", "报工时间", "baseField");

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'finishCount', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'unqualified', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'relateResources', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'operatorName', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'scaleFactor', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'totalScale', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'qualityLevel', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'batch', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'shiftType', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'productionBasicUnitName', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`) VALUES ('yelink.workorder.report', 'orderReportApp.orderReportWorkReportRecord', 'orderReportApp.orderReportWorkReportRecord', 'reportEndTime', '1', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '1', '1', '1', '1', '1', '1', '1', NULL, NULL, '1', '0', 'baseField');

-- bom子物料的分子默认为1
call `proc_modify_column`(
        'dfs_bom_raw_material',
        'num',
        'ALTER TABLE `dfs_bom_raw_material` MODIFY COLUMN `num` decimal(22,10) DEFAULT ''1.0000000000'' COMMENT ''用量：分子'';');
-- 新增工单投产选基本单元的 软件参数配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('workOrderInvestChooseBasicUnit', '投产选生产基本单元', 'production.workOrderVerificationReportConfig.workOrderInvestChooseBasicUnit', 'production.workOrderVerificationReportConfig.reportGeneralConfig', '适用dfs系统小程序(工单报工/订单报工)，配置可控制工单投产时是否支持多选', 'yelinkoncall', 'yelinkoncall', '2025-06-03 10:52:27', '2025-06-03 10:52:27', '');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES ('line', '制造单元', 'production.workOrderVerificationReportConfig.workOrderInvestChooseBasicUnit.line', 'production.workOrderVerificationReportConfig.workOrderInvestChooseBasicUnit', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"single\",\"label\":\"单选\"}]', '\"single\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES ('device', '设备', 'production.workOrderVerificationReportConfig.workOrderInvestChooseBasicUnit.device', 'production.workOrderVerificationReportConfig.workOrderInvestChooseBasicUnit', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"multiple\",\"label\":\"多选\"},{\"value\":\"single\",\"label\":\"单选\"}]', '\"multiple\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`)
VALUES ('team', '班组', 'production.workOrderVerificationReportConfig.workOrderInvestChooseBasicUnit.team', 'production.workOrderVerificationReportConfig.workOrderInvestChooseBasicUnit', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"multiple\",\"label\":\"多选\"},{\"value\":\"single\",\"label\":\"单选\"}]', '\"single\"', NULL);

-- 生产工单的计划开始时间、计划完成时间字段配置的置灰去掉
UPDATE `dfs_form_field_rule_config` SET `show_gray` = 0, `edit_gray` = 0, `need_gray` = 0 WHERE `full_path_code` = 'workOrder.editByCreate' AND `field_code` in ('startDate', 'endDate');

-- 工单的工单工艺字段重命名为关联工艺字段
UPDATE `dfs_form_field_config` SET `field_name` = '关联工艺' WHERE `field_code` = 'haveCraft' AND `type_code` = 'sysField' AND `full_path_code` in ('workOrder.list', 'workOrder.edit', 'workOrder.detail');

-- 将工单表的销售订单字段改为varchar(255)
call proc_modify_column(
       'dfs_work_order',
       'sale_order_number',
       'ALTER TABLE `dfs_work_order` MODIFY COLUMN `sale_order_number` varchar(255) NULL DEFAULT NULL COMMENT ''销售订单号''');

-- 物料新增`是否上传附件`字段配置（需求背景：用户想在物料表看到未上传的附件，已进行附件上传）
call proc_add_form_field_module('material.list', 'uploadFile', '是否上传附件', 'baseField');
INSERT INTO `dfs`.`dfs_form_field_rule_config`(`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `sub_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `is_overall`, `module_code`, `is_dynamic_add`)
VALUES ('/product-management/supplies', 'material.list', 'material.list', 'uploadFile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'baseField', 0);

-- 自动建单
INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'autoCreateOrder', '自动建单', 'production.orderReportAppConfig.autoCreateOrder', 'production.orderReportAppConfig', '最后一道非委外工序报工时 报工页面点击【确定】按钮——> 弹窗：选择创建单据类型——>跳转进入创建单据的页面', 'yelinkoncall', 'admin', '2024-02-22 06:15:05', '2024-02-22 11:50:13');

INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'enable', '报工时自动创单（生产检验单/生产入库单）', 'production.orderReportAppConfig.autoCreateOrder.enable', 'production.orderReportAppConfig.autoCreateOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"开启\"},{\"value\":false,\"label\":\"关闭\"}]', 'false', NULL);
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'createFrequency', '自动建单频率', 'production.orderReportAppConfig.autoCreateOrder.createFrequency', 'production.orderReportAppConfig.autoCreateOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"everyReport\",\"label\":\"每次报工都创单\"},{\"value\":\"reportFinish\",\"label\":\"仅报工完成时\"}]', '\"reportFinish\"', NULL);
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'defaultSelect', '创建的单据类型（默认选中）', 'production.orderReportAppConfig.autoCreateOrder.defaultSelect', 'production.orderReportAppConfig.autoCreateOrder', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"workOrderComplete\",\"label\":\"生产入库单\"},{\"value\":\"productionInspectOrder\",\"label\":\"生产检验单\"},{\"value\":\"no\",\"label\":\"不创单\"}]', '\"workOrderComplete\"', NULL);

update `dfs_procedure` set state = 5 WHERE is_enable = 0;

-- 默认更新工单发送消息只包含物料
CALL conditional_execute(
    'SELECT COUNT(1) FROM dfs_operation_log',
    'UPDATE dfs_business_config_value SET `value` = ''["material"]''  where value_code = ''updateMsgContent'' and value_full_path_code = ''production.workOrderConfig.workOrderEventConfig.updateMsgContent'''
);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402160', '删除', 'processQuality.record:delete', NULL, NULL, NULL, NULL, '2023-01-31 09:53:19', 'enable', 'GET', 'facility-quality.02', 2, 1, 0, '/facility-quality/processQuality/qualityRecord', 1);
call init_new_role_permission('10402160');

INSERT INTO dfs_config_open_api (service, model, interface_code, interface_name, http_method, path) VALUES ('wms', 'inventoryLedger', 'getList', '物料台账列表', 'POST', '/wms/stocks/inventory/ledger/list');

-- 采购需求供应商字段配置
update dfs_form_field_rule_config set  need_gray = 0,`is_edit` = 1, `edit_gray` = 0 where field_code = 'suggestedSupplier' and  full_path_code = 'purchaseRequestOrder.editByRelease';

