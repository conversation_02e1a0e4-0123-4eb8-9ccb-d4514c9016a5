-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================

-- 委外订单表单配置
-- 将委外订单路由名称由“委外订单(当前版本仅下推有效)”修改成“委外订单”
update dfs_form_config SET `name`="委外订单" where `code`="subcontractOrder" and full_path_code="subcontractOrder";
-- 删除之前添加的多余字段
delete from dfs_form_field_config where full_path_code="subcontractOrder.list.material"
                                    and field_code in ("auxiliaryAttr", "standard", "materialPrice", "nameEnglish","drawingNumber","rawMaterial","factoryModel","minimumProductionLot","loseRate","materialFields_remark","materialFields_customFieldOne","materialFields_customFieldTwo","materialFields_customFieldThree","materialFields_customFieldFour","materialFields_customFieldFive","materialFields_customFieldSix","materialFields_customFieldSeven","materialFields_customFieldEight","materialFields_customFieldNine","materialFields_customFieldTen","materialFields_customFieldEleven");
delete from dfs_form_field_rule_config where full_path_code="subcontractOrder.list.material"
                                         and field_code in ("auxiliaryAttr", "standard", "materialPrice", "nameEnglish","drawingNumber","rawMaterial","factoryModel","minimumProductionLot","loseRate","materialFields_remark","materialFields_customFieldOne","materialFields_customFieldTwo","materialFields_customFieldThree","materialFields_customFieldFour","materialFields_customFieldFive","materialFields_customFieldSix","materialFields_customFieldSeven","materialFields_customFieldEight","materialFields_customFieldNine","materialFields_customFieldTen","materialFields_customFieldEleven");

UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCreate' AND `field_code` = 'subcontractOrderNumber';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCreate' AND `field_code` = 'materialCode';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCreate' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCreate' AND `field_code` = 'typeName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCreate' AND `field_code` = 'supplier';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 1, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCreate' AND `field_code` = 'supplierName';

UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByRelease' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByRelease' AND `field_code` = 'typeName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByRelease' AND `field_code` = 'supplier';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByRelease' AND `field_code` = 'supplierName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByRelease' AND `field_code` = 'quantity';

UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByFinish' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByFinish' AND `field_code` = 'typeName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByFinish' AND `field_code` = 'supplier';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByFinish' AND `field_code` = 'supplierName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByFinish' AND `field_code` = 'quantity';

UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByClosed' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByClosed' AND `field_code` = 'typeName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByClosed' AND `field_code` = 'supplier';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByClosed' AND `field_code` = 'supplierName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByClosed' AND `field_code` = 'quantity';

UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCancel' AND `field_code` = 'name';
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1, `show_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCancel' AND `field_code` = 'typeName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCancel' AND `field_code` = 'supplier';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCancel' AND `field_code` = 'supplierName';
UPDATE `dfs_form_field_rule_config` SET `is_edit` = 0, `edit_gray` = 1 WHERE `full_path_code` = 'subcontractOrder.editByCancel' AND `field_code` = 'quantity';

-- 添加新加的字段
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldOne", "委外订单扩展字段1");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldTwo", "委外订单扩展字段2");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldThree", "委外订单扩展字段3");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldFour", "委外订单扩展字段4");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldFive", "委外订单扩展字段5");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldSix", "委外订单扩展字段6");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldSeven", "委外订单扩展字段7");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldEight", "委外订单扩展字段8");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldNine", "委外订单扩展字段9");
call proc_add_form_field("subcontractOrder.list.material", "orderExtendFieldTen", "委外订单扩展字段10");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldOne", "委外订单物料扩展字段1");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldTwo", "委外订单物料扩展字段2");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldThree", "委外订单物料扩展字段3");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldFour", "委外订单物料扩展字段4");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldFive", "委外订单物料扩展字段5");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldSix", "委外订单物料扩展字段6");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldSeven", "委外订单物料扩展字段7");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldEight", "委外订单物料扩展字段8");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldNine", "委外订单物料扩展字段9");
call proc_add_form_field("subcontractOrder.list.material", "orderMaterialExtendFieldTen", "委外订单物料扩展字段10");

call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldOne", "委外订单扩展字段1");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldTwo", "委外订单扩展字段2");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldThree", "委外订单扩展字段3");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldFour", "委外订单扩展字段4");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldFive", "委外订单扩展字段5");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldSix", "委外订单扩展字段6");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldSeven", "委外订单扩展字段7");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldEight", "委外订单扩展字段8");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldNine", "委外订单扩展字段9");
call proc_add_form_field("subcontractOrder.list.order", "orderExtendFieldTen", "委外订单扩展字段10");

call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldOne", "委外订单扩展字段1");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldTwo", "委外订单扩展字段2");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldThree", "委外订单扩展字段3");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldFour", "委外订单扩展字段4");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldFive", "委外订单扩展字段5");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldSix", "委外订单扩展字段6");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldSeven", "委外订单扩展字段7");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldEight", "委外订单扩展字段8");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldNine", "委外订单扩展字段9");
call proc_add_form_field("subcontractOrder.edit", "orderExtendFieldTen", "委外订单扩展字段10");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldOne", "委外订单物料扩展字段1");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldTwo", "委外订单物料扩展字段2");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldThree", "委外订单物料扩展字段3");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldFour", "委外订单物料扩展字段4");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldFive", "委外订单物料扩展字段5");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldSix", "委外订单物料扩展字段6");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldSeven", "委外订单物料扩展字段7");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldEight", "委外订单物料扩展字段8");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldNine", "委外订单物料扩展字段9");
call proc_add_form_field("subcontractOrder.edit", "orderMaterialExtendFieldTen", "委外订单物料扩展字段10");

call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldOne", "委外订单扩展字段1");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldTwo", "委外订单扩展字段2");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldThree", "委外订单扩展字段3");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldFour", "委外订单扩展字段4");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldFive", "委外订单扩展字段5");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldSix", "委外订单扩展字段6");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldSeven", "委外订单扩展字段7");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldEight", "委外订单扩展字段8");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldNine", "委外订单扩展字段9");
call proc_add_form_field("subcontractOrder.detail", "orderExtendFieldTen", "委外订单扩展字段10");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldOne", "委外订单物料扩展字段1");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldTwo", "委外订单物料扩展字段2");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldThree", "委外订单物料扩展字段3");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldFour", "委外订单物料扩展字段4");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldFive", "委外订单物料扩展字段5");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldSix", "委外订单物料扩展字段6");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldSeven", "委外订单物料扩展字段7");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldEight", "委外订单物料扩展字段8");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldNine", "委外订单物料扩展字段9");
call proc_add_form_field("subcontractOrder.detail", "orderMaterialExtendFieldTen", "委外订单物料扩展字段10");
-- 委外订单列表-按物料
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.material', "subcontractOrder.list.material", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
-- 委外订单列表-按单
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.list.order', "subcontractOrder.list.order", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
-- 编辑
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCreate', 'subcontractOrder.edit', 'orderMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByRelease', 'subcontractOrder.edit', 'orderMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByFinish', 'subcontractOrder.edit', 'orderMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByClosed', 'subcontractOrder.edit', 'orderMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (null, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.editByCancel', 'subcontractOrder.edit', 'orderMaterialExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
-- 详情
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingOrder', 'subcontractOrder.detail', 'subcontractOrder.detail', "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);


-- 委外收货单表单配置
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'subcontractReceipt', '委外订单收货单', 'subcontractReceipt', '', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'subcontractReceiptList', '委外订单收货单列表页', 'subcontractReceipt.list', 'subcontractReceipt', NULL, 'admin', 'admin', NOW(), NOW(), 'list', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'subcontractReceiptEdit', '委外订单收货单编辑页', 'subcontractReceipt.edit', 'subcontractReceipt', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'subcontractReceiptEditByCreate', '创建态', 'subcontractReceipt.editByCreate', 'subcontractReceipt.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-create', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'subcontractReceiptEditByRelease', '生效态', 'subcontractReceipt.editByRelease', 'subcontractReceipt.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'subcontractReceiptEditByFinish', '完成态', 'subcontractReceipt.editByFinish', 'subcontractReceipt.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'subcontractReceiptEditByClosed', '关闭态', 'subcontractReceipt.editByClosed', 'subcontractReceipt.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', 0, 'subcontractReceiptEditByCancel', '取消态', 'subcontractReceipt.editByCancel', 'subcontractReceipt.edit', NULL, 'admin', 'admin', NOW(), NOW(), 'edit-other', 1);
INSERT INTO `dfs_form_config`(`id`, `type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `module`, `is_inner`) VALUES (NULL, 'web', NULL, 'subcontractReceiptDetail', '委外订单收货单详情页', 'subcontractReceipt.detail', 'subcontractReceipt', NULL, 'admin', 'admin', NOW(), NOW(), NULL, 1);
-- 委外收货单相关字段配置
call proc_add_form_field("subcontractReceipt.list", "receiptNumber", "委外订单收货单号");
call proc_add_form_field("subcontractReceipt.list", "state", "状态");
call proc_add_form_field("subcontractReceipt.list", "subcontractOrder", "委外订单");
call proc_add_form_field("subcontractReceipt.list", "priority", "优先级");
call proc_add_form_field("subcontractReceipt.list", "supplier", "供应商编码");
call proc_add_form_field("subcontractReceipt.list", "supplierName", "供应商名称");
call proc_add_form_field("subcontractReceipt.list", "supplierPhone", "供应商电话");
call proc_add_form_field("subcontractReceipt.list", "supplierAddr", "供应商地址");
call proc_add_form_field("subcontractReceipt.list", "code", "物料编码");
call proc_add_form_field("subcontractReceipt.list", "name", "物料名称");
call proc_add_form_field("subcontractReceipt.list", "receiptQuantity", "收货数量");
call proc_add_form_field("subcontractReceipt.list", "inputQuantity", "入库数量");
call proc_add_form_field("subcontractReceipt.list", "approvalStatus", "审批状态");
call proc_add_form_field("subcontractReceipt.list", "approver", "审批人");
call proc_add_form_field("subcontractReceipt.list", "createBy", "创建人");
call proc_add_form_field("subcontractReceipt.list", "createTime", "创建时间");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldOne", "委外收货单扩展字段1");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldTwo", "委外收货单扩展字段2");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldThree", "委外收货单扩展字段3");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldFour", "委外收货单扩展字段4");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldFive", "委外收货单扩展字段5");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldSix", "委外收货单扩展字段6");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldSeven", "委外收货单扩展字段7");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldEight", "委外收货单扩展字段8");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldNine", "委外收货单扩展字段9");
call proc_add_form_field("subcontractReceipt.list", "orderExtendFieldTen", "委外收货单扩展字段10");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldOne", "委外收货单物料扩展字段1");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldTwo", "委外收货单物料扩展字段2");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldThree", "委外收货单物料扩展字段3");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldFour", "委外收货单物料扩展字段4");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldFive", "委外收货单物料扩展字段5");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldSix", "委外收货单物料扩展字段6");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldSeven", "委外收货单物料扩展字段7");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldEight", "委外收货单物料扩展字段8");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldNine", "委外收货单物料扩展字段9");
call proc_add_form_field("subcontractReceipt.list", "orderMaterialExtendFieldTen", "委外收货单物料扩展字段10");

call proc_add_form_field("subcontractReceipt.edit", "receiptNumber", "委外订单收货单号");
call proc_add_form_field("subcontractReceipt.edit", "state", "状态");
call proc_add_form_field("subcontractReceipt.edit", "subcontractOrder", "委外订单");
call proc_add_form_field("subcontractReceipt.edit", "supplier", "供应商编码");
call proc_add_form_field("subcontractReceipt.edit", "supplierName", "供应商名称");
call proc_add_form_field("subcontractReceipt.edit", "supplierPhone", "供应商电话");
call proc_add_form_field("subcontractReceipt.edit", "supplierAddr", "供应商地址");
call proc_add_form_field("subcontractReceipt.edit", "priority", "优先级");
call proc_add_form_field("subcontractReceipt.edit", "receiptTime", "收货时间");
call proc_add_form_field("subcontractReceipt.edit", "approver", "审批人");
call proc_add_form_field("subcontractReceipt.edit", "actualApprover", "实际审批人");
call proc_add_form_field("subcontractReceipt.edit", "createBy", "创建人");
call proc_add_form_field("subcontractReceipt.edit", "createTime", "创建时间");
call proc_add_form_field("subcontractReceipt.edit", "materialCode", "物料编码");
call proc_add_form_field("subcontractReceipt.edit", "name", "物料名称");
call proc_add_form_field("subcontractReceipt.edit", "standard", "物料规格");
call proc_add_form_field("subcontractReceipt.edit", "comp", "物料单位");
call proc_add_form_field("subcontractReceipt.edit", "auxiliaryAttr", "特征参数");
call proc_add_form_field("subcontractReceipt.edit", "receiptQuantity", "收货数量");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldOne", "委外收货单扩展字段1");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldTwo", "委外收货单扩展字段2");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldThree", "委外收货单扩展字段3");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldFour", "委外收货单扩展字段4");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldFive", "委外收货单扩展字段5");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldSix", "委外收货单扩展字段6");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldSeven", "委外收货单扩展字段7");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldEight", "委外收货单扩展字段8");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldNine", "委外收货单扩展字段9");
call proc_add_form_field("subcontractReceipt.edit", "orderExtendFieldTen", "委外收货单扩展字段10");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldOne", "委外收货单物料扩展字段1");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldTwo", "委外收货单物料扩展字段2");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldThree", "委外收货单物料扩展字段3");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldFour", "委外收货单物料扩展字段4");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldFive", "委外收货单物料扩展字段5");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldSix", "委外收货单物料扩展字段6");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldSeven", "委外收货单物料扩展字段7");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldEight", "委外收货单物料扩展字段8");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldNine", "委外收货单物料扩展字段9");
call proc_add_form_field("subcontractReceipt.edit", "orderMaterialExtendFieldTen", "委外收货单物料扩展字段10");

call proc_add_form_field("subcontractReceipt.detail", "receiptNumber", "委外订单收货单号");
call proc_add_form_field("subcontractReceipt.detail", "state", "状态");
call proc_add_form_field("subcontractReceipt.detail", "subcontractOrder", "委外订单");
call proc_add_form_field("subcontractReceipt.detail", "supplier", "供应商编码");
call proc_add_form_field("subcontractReceipt.detail", "supplierName", "供应商名称");
call proc_add_form_field("subcontractReceipt.detail", "supplierPhone", "供应商电话");
call proc_add_form_field("subcontractReceipt.detail", "supplierAddr", "供应商地址");
call proc_add_form_field("subcontractReceipt.detail", "priority", "优先级");
call proc_add_form_field("subcontractReceipt.detail", "receiptTime", "收货时间");
call proc_add_form_field("subcontractReceipt.detail", "approver", "审批人");
call proc_add_form_field("subcontractReceipt.detail", "actualApprover", "实际审批人");
call proc_add_form_field("subcontractReceipt.detail", "createBy", "创建人");
call proc_add_form_field("subcontractReceipt.detail", "createTime", "创建时间");
call proc_add_form_field("subcontractReceipt.detail", "materialCode", "物料编码");
call proc_add_form_field("subcontractReceipt.detail", "name", "物料名称");
call proc_add_form_field("subcontractReceipt.detail", "standard", "物料规格");
call proc_add_form_field("subcontractReceipt.detail", "comp", "物料单位");
call proc_add_form_field("subcontractReceipt.detail", "auxiliaryAttr", "特征参数");
call proc_add_form_field("subcontractReceipt.detail", "receiptQuantity", "收货数量");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldOne", "委外收货单扩展字段1");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldTwo", "委外收货单扩展字段2");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldThree", "委外收货单扩展字段3");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldFour", "委外收货单扩展字段4");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldFive", "委外收货单扩展字段5");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldSix", "委外收货单扩展字段6");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldSeven", "委外收货单扩展字段7");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldEight", "委外收货单扩展字段8");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldNine", "委外收货单扩展字段9");
call proc_add_form_field("subcontractReceipt.detail", "orderExtendFieldTen", "委外收货单扩展字段10");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldOne", "委外收货单物料扩展字段1");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldTwo", "委外收货单物料扩展字段2");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldThree", "委外收货单物料扩展字段3");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldFour", "委外收货单物料扩展字段4");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldFive", "委外收货单物料扩展字段5");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldSix", "委外收货单物料扩展字段6");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldSeven", "委外收货单物料扩展字段7");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldEight", "委外收货单物料扩展字段8");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldNine", "委外收货单物料扩展字段9");
call proc_add_form_field("subcontractReceipt.detail", "orderMaterialExtendFieldTen", "委外收货单物料扩展字段10");

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "receiptNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "state", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "subcontractOrder", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "priority", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "code", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "receiptQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "inputQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "approvalStatus", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.list', "subcontractReceipt.list", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "receiptNumber", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "state", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "subcontractOrder", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "receiptTime", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "approver", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "name", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "standard", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "comp", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "auxiliaryAttr", 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "receiptQuantity", 1, 1, 1, 0, NULL, NULL, 1, 1, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCreate', "subcontractReceipt.edit", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "receiptNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "state", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "subcontractOrder", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "receiptTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "name", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "standard", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "comp", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "auxiliaryAttr", 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "receiptQuantity", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByRelease', "subcontractReceipt.edit", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "receiptNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "state", 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "subcontractOrder", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "receiptTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "name", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "standard", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "comp", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "auxiliaryAttr", 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "receiptQuantity", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByFinish', "subcontractReceipt.edit", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "receiptNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "state", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "subcontractOrder", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "receiptTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "name", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "standard", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "comp", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "auxiliaryAttr", 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "receiptQuantity", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByClosed', "subcontractReceipt.edit", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "receiptNumber", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "state", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "subcontractOrder", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "priority", 1, 0, 1, 0, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "receiptTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "materialCode", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "name", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "standard", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "comp", 1, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "auxiliaryAttr", 1, 0, 1, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "receiptQuantity", 1, 1, 0, 0, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.editByCancel', "subcontractReceipt.edit", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, 1, 1, 1);

INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "receiptNumber", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "state", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "subcontractOrder", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "supplier", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "supplierName", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "supplierPhone", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "supplierAddr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "priority", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "receiptTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "approver", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "actualApprover", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "createBy", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "createTime", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "materialCode", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "name", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "standard", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "comp", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "auxiliaryAttr", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "receiptQuantity", 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldOne", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldTwo", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldThree", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldFour", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldFive", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldSix", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldSeven", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldEight", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldNine", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`) VALUES (NULL, '/supply-chain-collaboration/outsourcingManagement/outsourcingReceiptOrder', 'subcontractReceipt.detail', "subcontractReceipt.detail", "orderMaterialExtendFieldTen", 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 1, 1, 1);

-- 审批字段、特征参数统一增加备注说明
UPDATE `dfs_form_field_rule_config`
SET `remark` = "该字段和审批配置冲突，请以审批配置为准，此字段置灰，仅允许重命名 (冲突位置：配置中心-->单据配置-->审批配置)"
WHERE `field_code` like "%approver%" and full_path_code like "subcontractReceipt%";

UPDATE `dfs_form_field_rule_config`
SET `remark` = "该字段和业务配置冲突，请以业务配置为准，此字段置灰，仅允许重命名 (冲突位置：配置中心-->系统配置-->业务配置配置-->物料特征参数配置)"
WHERE `field_code` = "auxiliaryAttr" and full_path_code like "subcontractReceipt%";


-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
