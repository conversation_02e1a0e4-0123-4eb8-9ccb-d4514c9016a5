set autocommit = 0;
#DDL

#修改工单辅助排程临时表的字段为非必填
ALTER TABLE `dfs_tmp_work_order_schedule`
    MODIFY COLUMN `work_order_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT  NULL COMMENT '生产工单号',
    MODIFY COLUMN  `plan_quantity` double(11,2) NOT NULL DEFAULT '0.00' COMMENT '计划数量',
    MODIFY COLUMN  `order_number` varchar(255) NOT NULL COMMENT '生产订单号';

ALTER TABLE `dfs_tmp_work_order_schedule`
    ADD COLUMN `schedule_status_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT  NULL COMMENT '排程状态',
    ADD COLUMN `grid_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT  NULL COMMENT '车间编码',
    ADD COLUMN `material_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT  NULL COMMENT '物料编码',
    ADD COLUMN `state` int DEFAULT NULL COMMENT '0-创建、1-发放、2-投入、3-挂起、4-完成、5-关闭、6-取消',
    ADD COLUMN `state_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT  NULL COMMENT '状态名称',
    ADD COLUMN `unqualified` double(11,4) DEFAULT NULL COMMENT '不合格数',
    ADD COLUMN `progress` double(11,4) DEFAULT NULL COMMENT '工单进度',
    ADD COLUMN `actual_start_date` datetime DEFAULT NULL COMMENT '实际开始时间',
    ADD COLUMN `actual_end_date` datetime DEFAULT NULL COMMENT '实际结束时间',
    ADD COLUMN `line_id` int DEFAULT NULL COMMENT '产线ID',
    ADD COLUMN `gname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT  NULL COMMENT '车间名称';
#DML

commit