-- DDL

call proc_add_column(
        'dfs_procedure_inspection_config',
        'defect_judge_type',
        'ALTER TABLE `dfs_procedure_inspection_config` ADD COLUMN `defect_judge_type` varchar(50) default ''booleanJudge'' COMMENT ''不良判断类型'';');
call proc_add_column(
        'dfs_procedure_def_inspection_config',
        'defect_judge_type',
        'ALTER TABLE `dfs_procedure_def_inspection_config` ADD COLUMN `defect_judge_type` varchar(50) default ''booleanJudge'' COMMENT ''不良判断类型'';');

CREATE TABLE IF NOT EXISTS `dfs_procedure_def_inspection_config_defect` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `procedure_code` varchar(255) DEFAULT NULL COMMENT '工序定义编号',
  `inspection_config_id` int(11) DEFAULT NULL COMMENT '工序检验配置id',
  `upper_limit` varchar(255) DEFAULT NULL COMMENT '上限值',
  `down_limit` varchar(255) DEFAULT NULL COMMENT '下限值',
  `defect_id` int(11) DEFAULT NULL COMMENT '不良定义id',
  `defect_name` varchar(255) DEFAULT NULL COMMENT '不良定义名称',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inspection_config_id` (`inspection_config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工序定义检验不良配置表';

CREATE TABLE IF NOT EXISTS `dfs_procedure_inspection_config_defect` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `craft_procedure_id` int(11) DEFAULT NULL COMMENT '工艺工序关联表id',
  `inspection_config_id` int(11) DEFAULT NULL COMMENT '工序检验配置id',
  `upper_limit` varchar(255) DEFAULT NULL COMMENT '上限值',
  `down_limit` varchar(255) DEFAULT NULL COMMENT '下限值',
  `defect_id` int(11) DEFAULT NULL COMMENT '不良定义id',
  `defect_name` varchar(255) DEFAULT NULL COMMENT '不良定义名称',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inspection_config_id` (`inspection_config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='工序检验不良配置表';

CREATE TABLE IF NOT EXISTS `dfs_customer_material_list`
(
  `id`                     int(11)      NOT NULL AUTO_INCREMENT,
  `customer_code`          varchar(255) NOT NULL COMMENT '客户编号',
  `state`                  varchar(255) DEFAULT 1 COMMENT '状态(1-创建 2-生效 3-停用 4-废弃)',
  `material_code`          varchar(255) NOT NULL COMMENT '物料编码',
  `customer_material_code` varchar(255) DEFAULT NULL COMMENT '客户物料编码',
  `customer_material_name` varchar(255) DEFAULT NULL COMMENT '客户物料名称',
  `remark`                 varchar(255) DEFAULT NULL COMMENT '备注',
  `update_by`              varchar(50)  DEFAULT NULL COMMENT '更新人',
  `create_by`              varchar(50)  DEFAULT NULL COMMENT '创建人',
  `update_time`            datetime     DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time`            datetime     DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `customer_code` (`customer_code`) USING BTREE,
  KEY `state` (`state`) USING BTREE,
  KEY `material_code` (`material_code`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='客户物料清单';

-- 产能列表新增 工作中心类型、生产基本单元id、生产基本单元编号、生产基本单元名称字段
call proc_add_column(
        'dfs_capacity',
        'work_center_type',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `work_center_type` varchar(100) NULL DEFAULT NULL COMMENT ''工作中心类型''');
call proc_add_column(
        'dfs_capacity',
        'production_basic_unit_id',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `production_basic_unit_id` int(11) NULL DEFAULT NULL COMMENT ''生产基本单元id''');
call proc_add_column(
        'dfs_capacity',
        'production_basic_unit_code',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `production_basic_unit_code` varchar(255) NULL DEFAULT NULL COMMENT ''生产基本单元编号''');
call proc_add_column(
        'dfs_capacity',
        'production_basic_unit_name',
        'ALTER TABLE `dfs_capacity` ADD COLUMN `production_basic_unit_name` varchar(255) NULL DEFAULT NULL COMMENT ''生产基本单元名称''');
-- 指标阈值
call proc_add_column(
        'dfs_target_threshold',
        'remark',
        'ALTER TABLE `dfs_target_threshold` ADD COLUMN `remark` varchar(1024) NULL COMMENT ''附加接口备注''');
--
call proc_add_column(
        'dfs_procedure_inspection_config',
        'result_update',
        'ALTER TABLE `dfs_procedure_inspection_config` ADD COLUMN `result_update`  tinyint(4) default 0 COMMENT ''结果是否可修改'';');
call proc_add_column(
        'dfs_procedure_def_inspection_config',
        'result_update',
        'ALTER TABLE `dfs_procedure_def_inspection_config` ADD COLUMN `result_update`  tinyint(4) default 0 COMMENT ''结果是否可修改'';');
-- 供应商物料清单新增  供应商物料名称 字段
call proc_add_column(
        'dfs_supplier_material',
        'supplier_material_name',
        'ALTER TABLE `dfs_supplier_material`  ADD COLUMN `supplier_material_name` varchar(255) DEFAULT NULL COMMENT ''供应商物料名称'' after `supplier_material_code`');
-- 供应商物料清单删除审批相关字段，重命名状态字段
call proc_modify_column(
        'dfs_supplier_material',
        'approval_status',
        'ALTER TABLE `dfs_supplier_material` DROP COLUMN `approval_status`');
call proc_modify_column(
        'dfs_supplier_material',
        'approval_suggestion',
        'ALTER TABLE `dfs_supplier_material` DROP COLUMN `approval_suggestion`');
call proc_modify_column(
        'dfs_supplier_material',
        'reviewer',
        'ALTER TABLE `dfs_supplier_material` DROP COLUMN `reviewer`');
call proc_modify_column(
        'dfs_supplier_material',
        'approver',
        'ALTER TABLE `dfs_supplier_material` DROP COLUMN `approver`');
call proc_modify_column(
        'dfs_supplier_material',
        'status',
        'ALTER TABLE `dfs_supplier_material` CHANGE COLUMN `status` `state` int(11) DEFAULT 1 COMMENT ''状态（1-创建 2-生效 3-停用 4-废弃）''');

-- 工单新增拓展字段5个
call proc_add_column(
        'dfs_work_order',
        'work_order_extend_field_one',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `work_order_extend_field_one` varchar(255) NULL DEFAULT NULL COMMENT ''工单拓展字段1''');
call proc_add_column(
        'dfs_work_order',
        'work_order_extend_field_two',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `work_order_extend_field_two` varchar(255) NULL DEFAULT NULL COMMENT ''工单拓展字段2''');
call proc_add_column(
        'dfs_work_order',
        'work_order_extend_field_three',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `work_order_extend_field_three` varchar(255) NULL DEFAULT NULL COMMENT ''工单拓展字段3''');
call proc_add_column(
        'dfs_work_order',
        'work_order_extend_field_four',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `work_order_extend_field_four` varchar(255) NULL DEFAULT NULL COMMENT ''工单拓展字段4''');
call proc_add_column(
        'dfs_work_order',
        'work_order_extend_field_five',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `work_order_extend_field_five` varchar(255) NULL DEFAULT NULL COMMENT ''工单拓展字段5''');

call proc_add_column(
        'sys_team',
        'is_scheme',
        'ALTER TABLE `sys_team` ADD COLUMN `is_scheme`  tinyint(4) default 0 COMMENT ''是否支持维修'';');
call proc_add_column(
        'sys_team',
        'scheme_id',
        'ALTER TABLE `sys_team` ADD COLUMN `scheme_id`  int(11) default null COMMENT ''维修方案id'';');

call proc_add_column(
        'dfs_maintain_record',
        'team_id',
        'ALTER TABLE `dfs_maintain_record` ADD COLUMN `team_id`  int(11) default null COMMENT ''上报班组位置'';');

-- 上传记录表添加是否删除字段、删除时间
call proc_add_column(
        'dfs_upload',
        'is_delete',
        'ALTER TABLE `dfs_upload` ADD COLUMN `is_delete` tinyint(1) NULL COMMENT ''是否被删除''');

call proc_add_column(
        'dfs_upload',
        'delete_time',
        'ALTER TABLE `dfs_upload` ADD COLUMN `delete_time` datetime NULL COMMENT ''删除时间''');

call proc_add_column_index('dfs_upload', 'is_delete', 'is_delete');


-- DML
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('11404070', '列配置_列表', 'capacity.list:columnConfigList', NULL, NULL, NULL, NULL, '2023-10-25 06:29:58', 'enable', 'POST', '11404', 2, 1, 1, '/production-config/capacityList', 1, NULL, '');
call init_new_role_permission('11404070');
-- 生产呼叫消息
UPDATE `dfs_config_notice_level_relation` SET `name` = '生产呼叫通知', `configurable_placeholder` = '{产线名称},{工位名称},{工序名称},{物料编号},{物料名称},{生产订单号},{工单号},{操作人名称},{生产呼叫类型},{备注}' WHERE `code` = 'iqcNotice';
update sys_app_permission_info set name = '生产呼叫' where id = 'yelink.ocs.callOutIQC.com';

-- 将产能列表数据copy到资源字段上去
UPDATE dfs_capacity SET `production_basic_unit_code` = `line_code`, `production_basic_unit_name` = `line_name`, `production_basic_unit_id` = `line_id`,`work_center_type` = 'line' WHERE `line_code` is not null;

-- 客户物料清单相关权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105', '客户物料清单', '/supply-chain-collaboration/order-model/customer-material-list', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'GET', '10901', 3, 1, 0, '/supply-chain-collaboration/order-model', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105010', '新增', 'customer.material.list:add', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105020', '删除', 'customer.material.list:delete', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105030', '列配置', 'customer.material.list:columnConfigList', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105040', '批量编辑', 'customer.material.list:batchUpdate', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105050', '下载导入模板', 'customer.material.list:exportTemplate', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105060', '导入数据', 'customer.material.list:importData', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105070', '查看日志', 'customer.material.list:importLog', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090105080', '导出', 'customer.material.list:exportList', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'POST', '1090105', 2, 1, 0, '/supply-chain-collaboration/order-model/customer-material-list', 1, NULL, '');
call init_new_role_permission('1090105%');
-- 供应商物料清单相关权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090202060', '批量编辑', 'supplier.list:batchUpdate', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'GET', '1090202', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplyList', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090202070', '列配置', 'supplier.list:columnConfigList', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'GET', '1090202', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplyList', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090202080', '导出', 'supplier.list:exportList', NULL, NULL, NULL, NULL, '2023-10-31 03:16:57', 'enable', 'GET', '1090202', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplyList', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090202090', '导入管理(下载默认转换模板)', 'supplier.list:downDefaultTemplate', NULL, NULL, NULL, NULL, '2023-11-02 11:31:29', 'enable', 'GET', '1090202', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplyList', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090202100', '导入管理(导入数据)', 'supplier.list:importData', NULL, NULL, NULL, NULL, '2023-11-02 11:31:29', 'enable', 'GET', '1090202', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplyList', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('1090202110', '导入管理(查看日志)', 'supplier.list:log', NULL, NULL, NULL, NULL, '2023-11-02 11:31:29', 'enable', 'GET', '1090202', 2, 1, 0, '/supply-chain-collaboration/procurement-management/supplyList', 1, NULL, '');
call init_new_role_permission('1090202060');
call init_new_role_permission('1090202070');
call init_new_role_permission('1090202080');
call init_new_role_permission('1090202090');
call init_new_role_permission('1090202100');
call init_new_role_permission('1090202110');

-- 添加客户物料清单路由
INSERT INTO `sys_route`(`id`, `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES (null, '/supply-chain-collaboration/order-model/customer-material-list', '/supply-chain-collaboration/order-model', 'customerMaterialList', NULL, '客户物料清单', NULL, 'dfs', NULL, 1, NULL, 1, 1, NULL, '');

-- 默认设置供应商物料状态为生效
UPDATE dfs_supplier_material SET `state` = 2;

-- 班组维修
INSERT INTO  `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`) VALUES ('yelink.team.maintenance.com', '班组维修', '/team-maintenance', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1, NULL);
INSERT INTO  `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`) VALUES ('yelink.team.maintenance.button1', '提交', 'yelink.team.maintenance:submit', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.team.maintenance.com', 2, 0, 1, NULL, 1, NULL);

-- 生产工单新增计划批数、每批计划数、实际批数
call proc_add_column(
        'dfs_work_order',
        'planned_batches',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `planned_batches`  double DEFAULT 0  COMMENT ''计划批数'' AFTER `product_total`;');
call proc_add_column(
        'dfs_work_order',
        'plans_per_batch',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `plans_per_batch`  double DEFAULT 0  COMMENT ''每批计划数'' AFTER `planned_batches`;');
call proc_add_column(
        'dfs_work_order',
        'actual_batches',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `actual_batches`  double DEFAULT 0  COMMENT ''实际批数'' AFTER `plans_per_batch`;');
call proc_add_column(
        'dfs_work_order',
        'customer_code',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `customer_code`  varchar(255) NULL DEFAULT NULL  COMMENT ''客户编码'' AFTER `planned_batches`;');
call proc_add_column(
        'dfs_work_order',
        'customer_name',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `customer_name`  varchar(255) NULL DEFAULT NULL  COMMENT ''客户名称'' AFTER `customer_code`;');

-- 新增业务配置  （完工自动报工配置）
INSERT INTO `dfs_business_config`(`id`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (null, 'stateConfig', '完工自动报工配置', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork', 'production.workOrderVerificationReportConfig.reportGeneralConfig', NULL, 'yelinkoncall', 'yelinkoncall', '2023-09-08 06:15:05', '2023-09-08 06:15:05');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES (null, 'enable', '完工时是否自动报工', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork.enable', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES (null, 'enableWorkCenter', '允许自动报工的工作中心', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork.enableWorkCenter', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork', 'select-multiple', 'pageApi', '/api/work/center/list', 'get', NULL, 'code,name', '', '');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES (null, 'enableProcedure', '允许自动报工的工序', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork.enableProcedure', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork', 'select-multiple', 'pageApi', '/api/procedures/list', 'get', NULL, 'procedureCode,name', '', '');
INSERT INTO `dfs_business_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES (null, 'popUpDisplayEnable', '满足条件时是否弹窗确认', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork.popUpDisplay', 'production.workOrderVerificationReportConfig.autoReportAfterFinishWork', 'select', 'table', '', 'get', NULL, '', '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false');

-- 单据与项目合同关联
CREATE TABLE  IF NOT EXISTS `dfs_receipt_project_contract` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_define_id` int(11) DEFAULT NULL COMMENT '项目ID',
  `project_define_name` varchar(100) DEFAULT NULL COMMENT '项目名称',
  `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
  `contract_name` varchar(100) DEFAULT NULL COMMENT '合同名称',
  `relation_id` int(11) DEFAULT NULL COMMENT '关联主键ID',
  `type` varchar(100) DEFAULT NULL COMMENT '关联类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单据与项目和合同的关系';



INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('stateIdentifier', '状态标识', 'saleOrder.listConfig.stateIdentifier', 'saleOrder.listConfig', '状态标识', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');


INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('tableCellProduct', '已排产标识', 'saleOrder.listConfig.stateIdentifier.tableCellProduct', 'saleOrder.listConfig.stateIdentifier', '已排产，生产数量不够发货。计划生产数量 > 0 ，排产数量 > 0，生产数量 < 销售数量;', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');

INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'saleOrder.listConfig.stateIdentifier.tableCellProduct.enable', 'saleOrder.listConfig.stateIdentifier.tableCellProduct', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'saleOrder.listConfig.stateIdentifier.tableCellProduct.colorIdentifier', 'saleOrder.listConfig.stateIdentifier.tableCellProduct', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#FFD000"');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'saleOrder.listConfig.stateIdentifier.tableCellProduct.textValue', 'saleOrder.listConfig.stateIdentifier.tableCellProduct', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"已排产"');






INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('tableCellAllow', '生产数量满足发货标识', 'saleOrder.listConfig.stateIdentifier.tableCellAllow', 'saleOrder.listConfig.stateIdentifier', '生产数量够，但未发货完。状态为生效，生产数量 >= 销售数量，且 发货状态为未发货或部分发货', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');

INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'saleOrder.listConfig.stateIdentifier.tableCellAllow.enable', 'saleOrder.listConfig.stateIdentifier.tableCellAllow', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'saleOrder.listConfig.stateIdentifier.tableCellAllow.colorIdentifier', 'saleOrder.listConfig.stateIdentifier.tableCellAllow', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#4DFF00"');

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'saleOrder.listConfig.stateIdentifier.tableCellAllow.textValue', 'saleOrder.listConfig.stateIdentifier.tableCellAllow', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"待发货"');





-- 订单

INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('stateIdentifier', '状态标识', 'production.productOrderListConfig.stateIdentifier', 'production.productOrderListConfig', '生产数量够。状态为生效，生产数量 >= 计划生产数量', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');


INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('markMeetDelivery', '已完成标识', 'production.productOrderListConfig.stateIdentifier.markMeetDelivery', 'production.productOrderListConfig.stateIdentifier', '生产数量够。状态为生效，生产数量 >= 计划生产数量', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');

INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'production.productOrderListConfig.stateIdentifier.markMeetDelivery.enable', 'production.productOrderListConfig.stateIdentifier.markMeetDelivery', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'production.productOrderListConfig.stateIdentifier.markMeetDelivery.colorIdentifier', 'production.productOrderListConfig.stateIdentifier.markMeetDelivery', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#4DFF00"');

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'production.productOrderListConfig.stateIdentifier.markMeetDelivery.textValue', 'production.productOrderListConfig.stateIdentifier.markMeetDelivery', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL,  '"已完成"');




INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('markNotMeetDelivery', '生产中标识', 'production.productOrderListConfig.stateIdentifier.markNotMeetDelivery', 'production.productOrderListConfig.stateIdentifier', '生产中，但生产数量不够。状态为生效，生产数量 > 0，生产数量 < 计划生产数量', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');

INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'production.productOrderListConfig.stateIdentifier.markNotMeetDelivery.enable', 'production.productOrderListConfig.stateIdentifier.markNotMeetDelivery', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'production.productOrderListConfig.stateIdentifier.markNotMeetDelivery.colorIdentifier', 'production.productOrderListConfig.stateIdentifier.markNotMeetDelivery', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#FFD000"');

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'production.productOrderListConfig.stateIdentifier.markNotMeetDelivery.textValue', 'production.productOrderListConfig.stateIdentifier.markNotMeetDelivery', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"生产中"');




INSERT INTO `dfs_business_config`( `code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`)
VALUES ('markOutputGtPlanQuantity', '排产数量大于计划数量', 'production.productOrderListConfig.stateIdentifier.markOutputGtPlanQuantity', 'production.productOrderListConfig.stateIdentifier', '排产数量超出。状态为生产，排产数量 >= 计划生产数量', 'yelinkoncall', 'yelinkoncall', '2023-07-06 04:28:17', '2023-07-06 04:28:17');

INSERT INTO  `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ( 'enable', '是否启用(单选)', 'production.productOrderListConfig.stateIdentifier.markOutputGtPlanQuantity.enable', 'production.productOrderListConfig.stateIdentifier.markOutputGtPlanQuantity', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');

INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('colorIdentifier', '颜色标识', 'production.productOrderListConfig.stateIdentifier.markOutputGtPlanQuantity.colorIdentifier', 'production.productOrderListConfig.stateIdentifier.markOutputGtPlanQuantity', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '"#F73302"');

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('textValue', '文本内容(4个汉字8个字母符之内)', 'production.productOrderListConfig.stateIdentifier.markOutputGtPlanQuantity.textValue', 'production.productOrderListConfig.stateIdentifier.markOutputGtPlanQuantity', 'textarea', 'input', NULL, NULL, NULL, NULL, NULL, '"超产"');






-- 质检返工改为维修
update sys_route set title = '维修定义' where path ='/qualities-manage/reworkQuality/define';
update sys_route set title = '维修' where path ='/qualities-manage/reworkQuality';
update sys_route set title = '维修方案' where path ='/qualities-manage/reworkQuality/plan/project';

update sys_permissions set name = '维修定义' where path ='/qualities-manage/reworkQuality/define';
update sys_permissions set name = '维修' where path ='/qualities-manage/reworkQuality';
update sys_permissions set name = '维修方案' where path ='/qualities-manage/reworkQuality/plan/project';
