-- 删除一些脏数据
DELETE FROM `dfs_replace_bom_material` WHERE `replace_material_id` = 0;

-- 补充误删除的表单字段配置
INSERT INTO dfs_form_field_rule_config (id, route, full_path_code, field_name_full_path_code, field_code, is_show, is_need, is_edit, is_scan, input_type, option_values_type, related_api_code, url, resp_param_field, all_option, option_values, default_value, rules, show_gray, need_gray, edit_gray, input_gray, value_gray, default_value_gray, rules_gray, scan_gray, remark, formula, sort, is_overall, module_code, is_dynamic_add) VALUES(NULL, '/supply-chain-collaboration/procurement-management/delivery-order', 'purchaseReceiptOrder.detail', 'purchaseReceiptOrder.detail', 'barCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1, 0, 'batchField', 0);

-- 刷新数据，兼容废弃的全局物料扩展字段配置
UPDATE `dfs_material_config_field` m,`dfs_form_field_rule_config` r
SET m.`is_using` = r.`is_show`, m.`is_not_null` = r.`is_need`, m.`is_change` = r.`is_edit`
WHERE m.`field_code` = r.`field_code`
AND r.`module_code` = 'materialExtendField'
AND r.`route` = '/product-management/supplies';

UPDATE `dfs_material_config_field` m,`dfs_form_field_config` f
SET m.`field_name` = f.`field_name`
WHERE m.`field_code` = f.`field_code`
AND f.`type_code` = 'customField'
AND f.`module_code` = 'materialExtendField'
AND f.`full_path_code` = 'material.detail';

UPDATE `dfs_material_config_field` m,`dfs_form_field_config` f
SET m.`default_field_name` = f.`field_name`
WHERE m.`field_code` = f.`field_code`
AND f.`type_code` = 'sysField'
AND f.`module_code` = 'materialExtendField'
AND f.`full_path_code` = 'material.detail';

