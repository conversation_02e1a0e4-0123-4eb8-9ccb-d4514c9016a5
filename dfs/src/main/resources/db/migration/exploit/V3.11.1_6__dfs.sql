-- 华东电工需要自动下推功能，需要回退此下推配置
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `p_code`, `pid`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`, `is_process_assembly`) VALUES (NULL, 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', '自动按工艺路线下推生产工单', 'production.productOrderPushDownConfig.workOrder', NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'originalOrderStates', '源单状态(多选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'workOrderState', '生产工单状态(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.workOrderState', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'defaultCraft', '是否支持默认工艺(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.defaultCraft', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'craftSplitType', '工艺拆分方式(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.craftSplitType', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'ruleId', '生产订单编码规则(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.ruleId', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/numberRule/getDetailByType?typeCode=2', 'get', NULL, 'id,prefixDetailName', NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'workOrderSplittingRules', '工单拆分规则', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.workOrderSplittingRules', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/config/push/down/work/order/split/rules', 'get', NULL, 'code,name', NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'materialChoose', '工序物料选择', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.materialChoose', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'isShowFinishProductCraft', '是否绑定成品工艺', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.isShowFinishProductCraft', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.enableFilterOutsourcing', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.appId', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.url', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.sourceOrderType', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.targetOrderType', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'originalOrderStates', '源单状态(多选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.originalOrderStates', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'workOrderState', '生产工单状态(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.workOrderState', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/workorders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'defaultCraft', '是否支持默认工艺(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.defaultCraft', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'craftSplitType', '工艺拆分方式(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.craftSplitType', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/config/push/down/craft/split', 'get', NULL, 'code,name', NULL, '\"procedure\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'ruleId', '生产订单编码规则(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.ruleId', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/numberRule/getDetailByType?typeCode=2', 'get', NULL, 'id,prefixDetailName', NULL, '', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'workOrderSplittingRules', '工单拆分规则', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.workOrderSplittingRules', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'api', '/api/config/push/down/work/order/split/rules', 'get', NULL, 'code,name', NULL, 'null', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'materialChoose', '物料选择', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.materialChoose', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"finishProduct\",\"label\":\"成品\"},{\"value\":\"procedureMaterial\",\"label\":\"工序物料\"}]', '\"finishProduct\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'isShowFinishProductCraft', '是否绑定成品工艺', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.isShowFinishProductCraft', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.enableFilterOutsourcing', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'appId', 'appId', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.appId', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'url', '前端路由', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.url', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'sourceOrderType', '源单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.sourceOrderType', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'targetOrderType', '目标单据类型编码', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto.targetOrderType', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"workOrder\"', NULL);

INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'production.productOrderPushDownConfig.workOrder', NULL, '自动按工艺路线下推生产工单', 'RULE', 'production.productOrderPushDownConfig.workOrder.craftRouteAuto', NULL, 1, 1, 0, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'production.productOrderPushDownConfig.workOrder.craftRouteAuto';

INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `p_code`, `pid`, `create_time`, `update_time`, `create_by`, `update_by`, `quality_level`, `is_process_assembly`) VALUES (NULL, 'saleOrder.pushDownConfig.productOrder.bomAuto', '自动按BOM下推生产订单', 'saleOrder.pushDownConfig.productOrder', NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0);

INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'originalOrderStates', '源单状态(多选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.productOrderState', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'bomSplitType', 'BOM拆分方式(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.bomSplitType', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"firstLevelBomNotFilter\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'enableFilterPurchase', '是否过滤采购品(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.enableFilterPurchase', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.enableFilterOutsourcing', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'ruleId', '生产订单编码规则(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.ruleId', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/api/numberRule/getDetailByType?typeCode=10', 'get', NULL, 'id,prefixDetailName', NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.productOrder.bomAuto.appId', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.productOrder.bomAuto.url', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomAuto.sourceOrderType', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomAuto.targetOrderType', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);

INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'originalOrderStates', '源单状态(多选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.originalOrderStates', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select-multiple', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[2]', '[2]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'productOrderState', '生产订单状态(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.productOrderState', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/ams/product/orders/all/state', 'get', NULL, 'code,name', '[1,2]', '1', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'bomSplitType', 'BOM拆分方式(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.bomSplitType', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"notSplit\",\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"notSplit\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'enableFilterPurchase', '是否过滤采购品(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.enableFilterPurchase', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'enableFilterOutsourcing', '是否过滤委外品(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.enableFilterOutsourcing', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'filterMaterialTypes', '物料类型过滤(多选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.filterMaterialTypes', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select-multiple', 'api', '/api/materials/material/type/list', 'get', NULL, 'id,name', NULL, '[]', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'ruleId', '生产订单编码规则(单选)', 'saleOrder.pushDownConfig.productOrder.bomAuto.ruleId', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'select', 'api', '/api/numberRule/getDetailByType?typeCode=10', 'get', NULL, 'id,prefixDetailName', NULL, '', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'appId', 'appId', 'saleOrder.pushDownConfig.productOrder.bomAuto.appId', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'url', '前端路由', 'saleOrder.pushDownConfig.productOrder.bomAuto.url', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'sourceOrderType', '源单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomAuto.sourceOrderType', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"saleOrder\"', NULL);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `item_id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES (NULL, NULL, 'targetOrderType', '目标单据类型编码', 'saleOrder.pushDownConfig.productOrder.bomAuto.targetOrderType', 'saleOrder.pushDownConfig.productOrder.bomAuto', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"productOrder\"', NULL);

INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `code`, `name`, `type`, `instance_type`, `url`, `enable`, `is_inner`, `is_show`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (NULL, 'saleOrder.pushDownConfig.productOrder', NULL, '自动按BOM下推生产订单', 'RULE', 'saleOrder.pushDownConfig.productOrder.bomAuto', NULL, 1, 1, 0, NULL, 'admin', 'admin', '2024-07-04 17:16:48', '2024-07-04 17:16:50');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'saleOrder.pushDownConfig.productOrder.bomAuto';








-- 更新下推配置组类型
UPDATE `dfs_order_push_down_config_value_dict` SET `group_type` = 'sysConf' WHERE `value_code` in ('appId', 'url', 'sourceOrderType', 'targetOrderType');
UPDATE `dfs_order_push_down_config_value` SET `group_type` = 'sysConf' WHERE `value_code` in ('appId', 'url', 'sourceOrderType', 'targetOrderType');
UPDATE `dfs_order_push_down_config_value_dict` SET `group_type` = 'sourceConf' WHERE `value_code` in ('isSupportMaterialLineReversePushDown', 'originalOrderStates', 'description');
UPDATE `dfs_order_push_down_config_value` SET `group_type` = 'sourceConf' WHERE `value_code` in ('isSupportMaterialLineReversePushDown', 'originalOrderStates', 'description');
