INSERT INTO `dfs_query_field_config`(`id`, `prefix`, `field_code`, `field_name`, `table_field_code`, `remark`) VALUES (null, 'workOrder', 'workOrderId', '工单id,工单物料行id', 'work_order_id', NULL);
-- 字段配置的取值方式默认为人工输入
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'input' WHERE `value_gray` = 0 AND `input_type` is null;
UPDATE `dfs_form_field_rule_config` SET `input_type` = 'select' WHERE `value_gray` = 0 AND `option_values_type` = 'table';