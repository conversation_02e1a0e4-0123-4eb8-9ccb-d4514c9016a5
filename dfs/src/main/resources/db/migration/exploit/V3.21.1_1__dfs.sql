-- DDL
call proc_add_column(
        'dfs_order_execute_seq',
        'device_as_resource',
        'ALTER TABLE `dfs_order_execute_seq` ADD COLUMN `device_as_resource` tinyint(1) DEFAULT 0 COMMENT ''设备是否为关联资源'' after line_id');

-- DML
-- 部门编码
update sys_department set department_code = department_name where department_code is null or department_code = '';

delete from dfs_notice_field_info where placeholder in ('{任务编号}','{任务名称}','{项目编号}', '{项目名称}');
delete from dfs_notice_type_placeholder where placeholder in ('{任务编号}','{任务名称}','{项目编号}', '{项目名称}');

UPDATE `dfs_target_method` SET `method_cname` = '取采集器状态（在线/离线）' where `method_name` = 'deviceStateByOnlySensor';

CALL conditional_execute(
    'select count(1) from `dfs_target_method` where `method_name` = ''deviceStateByOutput''',
    'INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES (''deviceState'', ''device'', ''根据产量变化判断'', ''deviceStateByOutput'', 1, NULL)'
);

call proc_add_column(
        'dfs_target_model',
        'enable_limit',
        'ALTER TABLE `dfs_target_model` ADD COLUMN `enable_limit` tinyint(4) DEFAULT ''1'' COMMENT ''是否启用限制器''');

-- 修改工艺表编码和名称长度
call `proc_modify_column`(
        'dfs_craft',
        'craft_code',
        'ALTER TABLE `dfs_craft` MODIFY COLUMN `craft_code` varchar(255) COMMENT ''工艺编号'';');
call `proc_modify_column`(
        'dfs_craft',
        'name',
        'ALTER TABLE `dfs_craft` MODIFY COLUMN `name` varchar(255) COMMENT ''工艺名称'';');

-- 采购退料批次字段配置
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCreate', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCreate', 'purchaseReturn.edit', 'returnAmount', 1, 0, 1, 0, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByRelease', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByRelease', 'purchaseReturn.edit', 'returnAmount', 1, 0, 1, 0, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByFinish', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByFinish', 'purchaseReturn.edit', 'returnAmount', 1, 0, 1, 0, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByClosed', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByClosed', 'purchaseReturn.edit', 'returnAmount', 1, 0, 1, 0, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCancel', 'purchaseReturn.edit', 'batchAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.editByCancel', 'purchaseReturn.edit', 'returnAmount', 1, 0, 1, 0, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'batchAmount', 1, 0, 0, 0, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1,"batchField");
INSERT INTO `dfs_form_field_rule_config`(`id`, `route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `module_code`) VALUES (NULL, '/supply-chain-collaboration/procurement-management/return-order', 'purchaseReturn.detail', 'purchaseReturn.detail', 'returnAmount', 1, 0, 1, 0, NULL, NULL, 0, 1, 0, 1, 1, 1, 1, 1,"batchField");

-- 删除销售订单创建多余的字段配置
delete from  dfs_form_field_rule_config  where full_path_code = 'saleOrder.editByCreate'  and  field_code in ('plannedProductionQuantity','scheduledProductionQuantity','productionQuantity');
-- 生产订单字段配置
update dfs_form_field_rule_config  set  route = '/order-model/product-order'  where  route  = '/product-management/supplies'  and field_name_full_path_code in ('productOrder.edit','productOrder.detail');

-- 字段配置的单据类型统一要显示置灰
UPDATE `dfs_form_field_rule_config` SET `is_show` = 1,`show_gray` = 1 WHERE `field_code` = 'orderType' or `field_code` = 'orderTypeName' or `field_code` = 'purchaseType' or `field_code` = 'receiptTypeName' or `field_code` = 'receiptType' or `field_code` = 'returnTypeName' or `field_code` = 'returnType' or `field_code` = 'saleOrderTypeName' or `field_code` = 'saleOrderType';

-- 修改软件参数配置名称
UPDATE `dfs_business_config_value` SET `value_name` = '默认勾选“挂起该生产基本单元下的其他工单”按钮', `option_values` = '[{\"value\":true,\"label\":\"勾选\"},{\"value\":false,\"label\":\"不勾选\"}]'
WHERE `value_full_path_code` = 'production.workOrderVerificationReportConfig.investmentConfig.hangUpOther';
UPDATE `dfs_business_config_value` SET `value_name` = '是否提示多个工单投产(功能废弃)'
WHERE `value_full_path_code` = 'production.workOrderVerificationReportConfig.investmentConfig.enable';

-- 关闭物料缓存
UPDATE `dfs_dict` SET value = 'false' WHERE type = 'cacheSwitch' AND code = 'MATERIAL_CACHE';