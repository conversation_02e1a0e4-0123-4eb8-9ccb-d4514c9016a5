-- 业务配置

INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('versionMng', '版本管理', 'design.craftConfig.versionMng', 'design.craftConfig', '工艺保存时，根据配置影响到的工艺状态会变为停用', 'yelinkoncall', 'yelinkoncall', '2024-05-09 15:50:34', '2024-05-09 15:50:34', '');
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enableReleaseCount', '生效限制范围下允许生效的个数', 'design.craftConfig.versionMng.enableReleaseCount', 'design.craftConfig.versionMng', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"one\",\"label\":\"仅一个\"},{\"value\":\"more\",\"label\":\"多个\"}]', '\"more\"', NULL);
INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('limitRange', '生效限制范围', 'design.craftConfig.versionMng.limitRange', 'design.craftConfig.versionMng', 'select-multiple', 'table', NULL, NULL, NULL, NULL, '[{\"value\":\"sameName\",\"label\":\"同编码\"},{\"value\":\"sameMaterial\",\"label\":\"同物料\"}]', '[]', NULL);
-- INSERT INTO `dfs_business_config_value` (`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('newVerRecreate', '更新版本是否生成新工艺(旧版本不变)', 'design.craftConfig.versionMng.newVerRecreate', 'design.craftConfig.versionMng', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false', NULL);


-- 工艺表增加字段
call proc_add_column(
        'dfs_craft',
        'origin_id',
        'ALTER TABLE `dfs_craft` ADD COLUMN `origin_id` int(11) DEFAULT NULL COMMENT ''原始id''');
call proc_add_column(
        'dfs_craft',
        'origin_code',
        'ALTER TABLE `dfs_craft` ADD COLUMN `origin_code` varchar(255) DEFAULT NULL COMMENT ''原始编码''');
call proc_add_column(
        'dfs_craft',
        'origin_name',
        'ALTER TABLE `dfs_craft` ADD COLUMN `origin_name` varchar(255) DEFAULT NULL COMMENT ''原始名称''');

update dfs_craft set origin_id = craft_id, origin_code = craft_code, origin_name = `name` WHERE origin_code is null;
call proc_add_column_index('dfs_craft','origin_id','origin_id');
call proc_add_column_index('dfs_craft','origin_code','origin_code');

-- bom表增加字段
call proc_add_column(
        'dfs_bom',
        'origin_id',
        'ALTER TABLE `dfs_bom` ADD COLUMN `origin_id` int(11) DEFAULT NULL COMMENT ''原始id''');
call proc_add_column(
        'dfs_bom',
        'origin_num',
        'ALTER TABLE `dfs_bom` ADD COLUMN `origin_num` varchar(255) DEFAULT NULL COMMENT ''原始编码''');

update dfs_bom set origin_id = id, origin_num = bom_num WHERE origin_num is null;
call proc_add_column_index('dfs_bom','origin_id','origin_id');
call proc_add_column_index('dfs_bom','origin_num','origin_num');


-- 版本变更记录
call proc_modify_column(
        'dfs_version_change_record',
        'link_id',
        'alter table `dfs_version_change_record` MODIFY COLUMN `link_id` text COMMENT ''历史id链: {id1}...,{id2},{idn}''');
update dfs_version_change_record set link_id = relate_id WHERE link_id is null;
update dfs_version_change_record set first_id = relate_id WHERE first_id is null;

-- 权限
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10803490', '版本日志', 'technology:versionLog', NULL, NULL, NULL, NULL, '2025-02-20 18:17:22', 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1, NULL, '', 1);
INSERT INTO `sys_permissions` (`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`) VALUES ('10802250', '版本日志', 'bom:versionLog', NULL, NULL, NULL, NULL, '2024-12-10 12:11:11', 'enable', 'GET', '10802', 2, 1, 0, '/product-management/bom', 1, NULL, '', 1);

call proc_add_column(
        'dfs_rule_type_config',
        'remark',
        'ALTER TABLE `dfs_rule_type_config` ADD COLUMN `remark` varchar(512) DEFAULT NULL COMMENT ''备注''');