-- DDL
call proc_add_column(
        'dfs_circulation_notice_contact',
        'type',
        'ALTER TABLE `dfs_circulation_notice_contact` ADD COLUMN `type` varchar(100) DEFAULT NULL COMMENT ''类型（receive--接收通知方 send--发送通知方）''');
call proc_add_column(
        'dfs_craft',
        'type',
        'ALTER TABLE `dfs_craft` ADD COLUMN `type` varchar(100) DEFAULT "normal" COMMENT ''类型(normal--正常，rework--返工)''');
CREATE TABLE IF NOT EXISTS `dfs_supplier_related_type`
(
    `id`            int(11)                          NOT NULL AUTO_INCREMENT COMMENT 'id',
    `supplier_code` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '供应商编号',
    `type_code`     int(11)                          DEFAULT NULL COMMENT '供应商类型编号',
    `type_name`     varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '供应商类型名称',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin
  ROW_FORMAT = DYNAMIC COMMENT ='供应商关联供应商类型表';

call proc_add_column(
        'sys_route',
        'service_name',
        'ALTER TABLE `sys_route` ADD COLUMN `service_name` varchar(255) DEFAULT '''' COMMENT ''服务名称''');
call proc_add_column(
        'sys_permissions',
        'service_name',
        'ALTER TABLE `sys_permissions` ADD COLUMN `service_name` varchar(255) DEFAULT '''' COMMENT ''服务名称''');
call proc_add_column(
        'dfs_procedure_inspection',
        'ng_controller',
        'ALTER TABLE `dfs_procedure_inspection` ADD COLUMN `ng_controller` tinyint(1) NULL DEFAULT 1 COMMENT ''ng控制项，0否1是''');
call proc_add_column(
        'dfs_procedure_def_inspection_config',
        'ng_controller',
        'ALTER TABLE `dfs_procedure_def_inspection_config` ADD COLUMN `ng_controller` tinyint(1) NULL DEFAULT 1 COMMENT ''ng控制项，0否1是''');
call proc_add_column(
        'dfs_procedure_inspection_config',
        'ng_controller',
        'ALTER TABLE `dfs_procedure_inspection_config` ADD COLUMN `ng_controller` tinyint(1) NULL DEFAULT 1 COMMENT ''ng控制项，0否1是''');
call proc_add_column(
        'dfs_report_line',
        'batch_remark',
        'ALTER TABLE `dfs_report_line` ADD COLUMN `batch_remark` varchar(255) DEFAULT '''' COMMENT ''批次备注''');
call proc_add_column(
        'dfs_report_count',
        'batch_remark',
        'ALTER TABLE `dfs_report_count` ADD COLUMN `batch_remark` varchar(255) DEFAULT '''' COMMENT ''批次备注''');
call proc_add_column(
        'dfs_circulation_notice_config',
        'send_contact_ids',
        'ALTER TABLE `dfs_circulation_notice_config` ADD COLUMN `send_contact_ids` varchar(255) DEFAULT '''' COMMENT ''前端级联选择器回显字段''');

call proc_modify_column(
        'dfs_work_center',
        'first_inspection',
        'ALTER TABLE `dfs_work_center` DROP COLUMN `first_inspection`');

-- 重构生产订单整体统计 + 生产订单日统计
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_product_order`;
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_product_order_daily`;
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_product_order_monthly`;
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_order_id` int(11) NOT NULL COMMENT '销售订单id',
    `product_order_number` varchar(255) NOT NULL COMMENT '销售订单号',
    `end_plan_quantity` double(11,2) DEFAULT NULL COMMENT '成品计划数',
    `end_produce_quantity` double(11,2) DEFAULT NULL COMMENT '成品生产数',
    `end_unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '成品不合格数',
    `order_plan_quantity` double(11,2) DEFAULT NULL COMMENT '订单计划总数:  所有工单物料计划生产数量之和',
    `order_produce_quantity` double(11,2) DEFAULT NULL COMMENT '订单已生产总数:  所有工单物料已生产数量之和',
    `all_order_count` int(11) DEFAULT NULL COMMENT '生产工单计数',
    `invested_order_count` int(11) DEFAULT NULL COMMENT '投产工单计数',
    `finish_order_count` int(11) DEFAULT NULL COMMENT '已完成工单计数',
    `delay_order_count` int(11) DEFAULT NULL COMMENT '延期工单计数',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率',
    `finish_rate` double(11,4) DEFAULT NULL COMMENT '达成率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`product_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单 整体统计';
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_order_id` int(11) NOT NULL COMMENT '销售订单id',
    `product_order_number` varchar(255) NOT NULL COMMENT '销售订单号',
    `record_date` datetime NOT NULL COMMENT '记录日期',
    `end_plan_quantity` double(11,2) DEFAULT NULL COMMENT '成品计划数',
    `end_unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '成品不合格数',
    `end_produce_quantity` double(11,2) DEFAULT NULL COMMENT '成品产出数',
    `end_qualified_rate` double(11,4) DEFAULT NULL COMMENT '成品合格率',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不合格数',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`product_order_number`,`record_date`) USING BTREE,
    KEY `idx_record_date` (`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='生产订单 每日统计';
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `product_order_id` int(11) NOT NULL COMMENT '销售订单id',
    `product_order_number` varchar(255) NOT NULL COMMENT '销售订单号',
    `record_month` varchar(255) NOT NULL COMMENT '月份',
    `end_plan_quantity` double(11,2) DEFAULT NULL COMMENT '成品计划数',
    `end_produce_quantity` double(11,2) DEFAULT NULL COMMENT '成品产出数',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`product_order_number`,`record_month`) USING BTREE,
    KEY `idx_record_month` (`record_month`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COMMENT='生产订单 每月统计';
-- 生产订单汇总 每月统计
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_product_order_summary_monthly` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `record_month` varchar(255) NOT NULL COMMENT '月份',
    `new_count` int(11) DEFAULT NULL COMMENT '本月新增订单数',
    `finish_count` int(11) DEFAULT NULL COMMENT '本月已完成订单数',
    `finish_delay_count` int(11) DEFAULT NULL COMMENT '本月延期完成订单数',
    `no_finish_delay_count` int(11) DEFAULT NULL COMMENT '本月延期未完成订单数',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`record_month`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='生产订单汇总 每月统计';
-- 产成品 指标表
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_end_material` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `material_code` varchar(255) NOT NULL COMMENT '物料编码',
    `sale_quantity` double(11,2) DEFAULT NULL COMMENT '销售数',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '生产数',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不合格数',
    `per_working_hours` double(11,4) DEFAULT NULL COMMENT '单件工时',
    `qualified_rate` double(11,2) DEFAULT NULL COMMENT '合格率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`material_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产成品 整体统计';
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_end_material_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `material_code` varchar(255) NOT NULL COMMENT '物料编码',
    `record_date` datetime NOT NULL COMMENT '记录日期',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '生产数',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不合格数',
    `direct_access_quantity` double(11,2) DEFAULT NULL COMMENT '直通数量',
    `working_hours` double(11,2) DEFAULT NULL COMMENT '当日工时',
    `per_working_hours` double(11,2) DEFAULT NULL COMMENT '单件工时',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率',
    `direct_access_rate` double(11,4) DEFAULT NULL COMMENT '直通率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`material_code`,`record_date`) USING BTREE,
    KEY `idx_record_date` (`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产成品 每日统计';
-- 销售订单物料的指标
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_sale_order_material`;
DROP TABLE if EXISTS `dfs_metrics`.`dfs_metrics_sale_order_material_daily`;
CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_sale_order_material` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `sale_order_material_id` int(11) NOT NULL COMMENT '销售订单物料行id',
    `sale_order_id` int(11) NOT NULL COMMENT '销售订单id',
    `sale_order_number` varchar(255) NOT NULL COMMENT '销售订单号',
    `material_code` varchar(255) NOT NULL COMMENT '物料编码',
    `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
    `sales_quantity` double(11,2) DEFAULT NULL COMMENT '销售数',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不合格数',
    `applied_shipment_quantity` double(11,2) DEFAULT NULL COMMENT '出货申请数量',
    `applied_quantity` double(11,2) DEFAULT NULL COMMENT '发货数量',
    `delay_quantity` double(11,2) DEFAULT NULL COMMENT '延期数量',
    `out_stock_quantity` double(11,2) DEFAULT NULL COMMENT '出库数量',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率',
    `finish_rate` double(11,4) DEFAULT NULL COMMENT '达成率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`sale_order_material_id`) USING BTREE,
    KEY `idx_sale_order` (`sale_order_number`),
    KEY `idx_material` (`material_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单-物料 整体统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_sale_order_material_daily` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `sale_order_material_id` int(11) NOT NULL COMMENT '销售订单物料行id',
    `sale_order_id` int(11) NOT NULL COMMENT '销售订单id',
    `sale_order_number` varchar(255) NOT NULL COMMENT '销售订单号',
    `material_code` varchar(255) NOT NULL COMMENT '物料编码',
    `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
    `record_date` datetime NOT NULL COMMENT '记录日期',
    `plan_quantity` double(11,2) DEFAULT NULL COMMENT '计划数量',
    `produce_quantity` double(11,2) DEFAULT NULL COMMENT '产出数量',
    `unqualified_quantity` double(11,2) DEFAULT NULL COMMENT '不合格数',
    `qualified_rate` double(11,4) DEFAULT NULL COMMENT '合格率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`sale_order_material_id`,`record_date`) USING BTREE,
    KEY `idx_date` (`record_date`),
    KEY `idx_sale_order` (`sale_order_number`) USING BTREE,
    KEY `idx_material` (`material_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单-物料 每日统计';

CREATE TABLE IF NOT EXISTS `dfs_metrics`.`dfs_metrics_sale_order_summary` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `all_order_count` int(11) DEFAULT NULL COMMENT '总订单数',
    `delivered_order_count` int(11) DEFAULT NULL COMMENT '已交付订单数',
    `delivered_timely_order_count` int(11) DEFAULT NULL COMMENT '及时交付订单数',
    `delivered_delay_order_count` int(11) DEFAULT NULL COMMENT '延期交付订单数',
    `no_delivered_delay_order_count` int(11) DEFAULT NULL COMMENT '延期未交付订单数',
    `all_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '销售产品总数',
    `delivered_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '已交付产品总数',
    `delivered_timely_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '及时交付产品总数',
    `no_delivered_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '待交付产品总数',
    `no_delivered_delay_order_material_quantity` double(11,2) DEFAULT NULL COMMENT '超期未交付产品总数',
    `delivered_order_rate` double(11,4) DEFAULT NULL COMMENT '订单交付率',
    `delivered_timely_order_rate` double(11,4) DEFAULT NULL COMMENT '订单及时交付率',
    `finish_order_material_rate` double(11,4) DEFAULT NULL COMMENT '销售订单完成率',
    `delivered_timely_order_material_rate` double(11,4) DEFAULT NULL COMMENT '产品及时交付率',
    `delivered_timely_rate` double(11,4) DEFAULT NULL COMMENT '交货及时率',
    `time` datetime DEFAULT NULL COMMENT '最新统计时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售订单 整体统计';

-- 物料与检验控制方式的关联表
CREATE TABLE IF NOT EXISTS `dfs_material_inspect_method`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `material_code`       varchar(255) DEFAULT NULL COMMENT '物料编码',
    `inspect_type_code`   int(11)      DEFAULT NULL COMMENT '检验类型编号',
    `inspect_type_name`   varchar(100) DEFAULT NULL COMMENT '检验类型名称',
    `inspect_method_code` varchar(255) DEFAULT NULL COMMENT '检验方式编号',
    `inspect_method_name` varchar(255) DEFAULT NULL COMMENT '检验方式名称',
    PRIMARY KEY (`id`),
    KEY `material_code` (`material_code`) USING BTREE COMMENT '物料编码'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='物料与检验控制方式的关联表';
CREATE TABLE IF NOT EXISTS `dfs_procedure_inspection_controller_config`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `craft_id`            int(11) NOT NULL COMMENT '工艺id',
    `craft_procedure_id`  int(11) NOT NULL COMMENT '工艺工序id',
    `inspect_type_code`   int(11)      DEFAULT NULL COMMENT '检验类型编号',
    `inspect_type_name`   varchar(100) DEFAULT NULL COMMENT '检验类型名称',
    `inspect_scheme_name` varchar(255) DEFAULT NULL COMMENT '检验方案名称（按逗号分隔）',
    `inspect_method_code` varchar(255) DEFAULT NULL COMMENT '检验方式编号（按逗号分隔）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `craft_procedure_id` (`craft_procedure_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工艺工序的检验控制表';

-- 模型统计配置
CREATE TABLE IF NOT EXISTS `dfs_model_stat_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `model_id` int(11) NOT NULL COMMENT '模型id',
    `scheme` varchar(255) DEFAULT NULL COMMENT '配置方案',
    `produce_scheme` varchar(255) DEFAULT NULL COMMENT '产出数方案',
    `unqualified_scheme` varchar(255) DEFAULT NULL COMMENT '不良数方案',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uni` (`model_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模型统计的配置';

call proc_add_column(
        'dfs_area',
        'material_check_type',
        'ALTER TABLE `dfs_area` ADD COLUMN `material_check_type` varchar(50) DEFAULT ''material'' COMMENT ''上料防错类型''');
call proc_add_column(
        'dfs_production_line',
        'material_check_type',
        'ALTER TABLE `dfs_production_line` ADD COLUMN `material_check_type` varchar(50) DEFAULT ''material'' COMMENT ''上料防错类型''');
call proc_add_column(
        'dfs_work_order',
        'material_check_type',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `material_check_type` varchar(50) DEFAULT ''material'' COMMENT ''上料防错类型''');
call proc_add_column(
        'dfs_work_order',
        'material_check_replace',
        'ALTER TABLE `dfs_work_order` ADD COLUMN `material_check_replace` tinyint(1) DEFAULT 0 COMMENT ''上料防错是否支持替代料''');
CREATE TABLE IF NOT EXISTS `dfs_work_order_material_check_material` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '上料防错物料id',
  `work_order_number` varchar(255) DEFAULT NULL COMMENT '工单号',
  `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `bom_numerator` double(11,4) DEFAULT NULL COMMENT 'bom分子',
  `bom_denominator` double(11,4) DEFAULT NULL COMMENT 'bom分母',
  `main_material_code` varchar(255) DEFAULT NULL COMMENT '主物料编号',
  `sub_type` int(11) DEFAULT NULL COMMENT '子项类型（1001-标准件  1002-替代件）',
  `seq` int(11) DEFAULT NULL COMMENT '上料顺序',
  `code_prefix` varchar(255) DEFAULT NULL COMMENT '单品码前缀',
  `replace_material_id` varchar(255) DEFAULT NULL COMMENT '可供选择的替代物id（多个则按逗号分隔）',
  `selected_replace_material_id` varchar(255) DEFAULT NULL COMMENT '已选择的替代物id（多个则按逗号分隔）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `sku_id` int(11) DEFAULT '0' COMMENT '特征参数skuId',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `work_order_number` (`work_order_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT ='上料防错物料表';

-- DML
-- 兼容历史数据
UPDATE dfs_circulation_notice_contact SET `type` = 'receive';

-- 供应商关联类型表兼容历史数据
INSERT INTO dfs_supplier_related_type (`supplier_code`, `type_name`, `type_code`)
SELECT s.`code` as `supplier_code`, s.`type` as `type_name`, d.`code` as `type_code`
FROM `dfs_supplier` s
JOIN dfs_dict d ON s.`type` = d.`name`;

UPDATE `dfs_config_notice_level_relation` SET `name` = '生产工单',`configurable_placeholder` = '{工单号},{创建时间},{产线名称},{生产订单号},{物料名称},{物料编号},{工序名称},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'workOrderAddNotice';
UPDATE `dfs_config_notice_level_relation` SET `name` = '生产工单',`configurable_placeholder` = '{工单号},{审批状态},{审批时间},{产线名称},{生产订单号},{物料名称},{物料编号},{工序名称},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'workOrderApprovalNotice';
UPDATE `dfs_config_notice_level_relation` SET `name` = '生产工单',`configurable_placeholder` = '{工单号},{状态},{变更前状态},{产线名称},{生产订单号},{物料名称},{物料编号},{工序名称},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'workOrderStateChangeNotice';
UPDATE `dfs_config_notice_level_relation` SET `configurable_placeholder` = '{产线名称},{工单号},{生产订单号},{物料名称},{物料编号},{工序名称},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'productTimeOut';
UPDATE `dfs_config_notice_level_relation` SET `configurable_placeholder` = '{工序名称},{前工序名称},{产线名称},{工单号},{生产订单号},{生产数量},{物料名称},{物料编号},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'takeMaterial';
UPDATE `dfs_config_notice_level_relation` SET `configurable_placeholder` = '{工序名称},{前工序名称},{产线名称},{工单号},{生产订单号},{物料名称},{物料编号},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'takeMaterialTimeOut';
UPDATE `dfs_config_notice_level_relation` SET `configurable_placeholder` = '{工序名称},{后工序名称},{产线名称},{工单号},{生产订单号},{生产数量},{物料名称},{物料编号},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'sendMaterial';
UPDATE `dfs_config_notice_level_relation` SET `configurable_placeholder` = '{工序名称},{前工序名称},{产线名称},{工单号},{生产订单号},{物料名称},{物料编号},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'sendMaterialTimeOut';
UPDATE `dfs_config_notice_level_relation` SET `configurable_placeholder` = '{产线名称},{变更前状态},{状态},{工序名称},{工单号},{生产订单号},{物料名称},{物料编号},{工作中心名称},{基本生产单元名称}' WHERE `code` = 'locationMachineNotice';

-- 考勤的业务配置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('attendance', '考勤模块', 'attendance', '', NULL);
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('checkInCalConfig', '打卡工时计算配置', 'attendance.checkInCalConfig', 'attendance', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('workCalendarCorrect', '是否按工作日历校正(单选)', 'attendance.checkInCalConfig.workCalendarCorrect', 'attendance.checkInCalConfig', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'false');


-- 生产订单指标配置项
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('productOrder', '生产订单整体', '1', 'productOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('productOrder', 'productOrderTarget', '系统统计', 'productOrder', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('productOrderDaily', '生产订单每日', '1', 'productOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('productOrderDaily', 'productOrderTarget', '系统统计', 'productOrderDaily', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('productOrderMonthly', '生产订单每月', '1', 'productOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('productOrderMonthly', 'productOrderTarget', '系统统计', 'productOrderMonthly', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('productOrderSummaryMonthly', '生产订单每月总结', '1', 'productOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('productOrderSummaryMonthly', 'productOrderTarget', '系统统计', 'productOrderSummaryMonthly', 1, NULL);

-- 产成品
INSERT INTO `dfs`.`dfs_model` (`type`, `name`, `pid`, `theory_efficiency`, `working_hours`) VALUES ('endMaterialTarget', '产成品', -1, NULL, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('endMaterialDaily', '产成品每日', '1', 'endMaterialTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('endMaterialDaily', 'endMaterialTarget', '系统统计', 'endMaterialDaily', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('endMaterial', '产成品整体', '1', 'endMaterialTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('endMaterial', 'endMaterialTarget', '系统统计', 'endMaterial', 1, NULL);

-- 销售订单
INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('saleOrder', '销售订单每日', '1', 'saleOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('saleOrder', 'saleOrderTarget', '系统统计', 'saleOrder', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('saleOrderMaterialDaily', '销售订单物料每日', '1', 'saleOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('saleOrderMaterialDaily', 'saleOrderTarget', '系统统计', 'saleOrderMaterialDaily', 1, NULL);

INSERT INTO `dfs_target_dict`(`target_name`, `target_cnname`, `target_type`, `model_type`, `alarm_config`, `is_threshold_setting`, `unit`, `create_time`, `update_time`, `data_type`, `is_inner_target`) VALUES ('saleOrderMaterial', '销售订单物料', '1', 'saleOrderTarget', NULL, NULL, '1', NULL, NULL, NULL, 1);
INSERT INTO `dfs_target_method`(`target_name`, `model_type`, `method_cname`, `method_name`, `has_frequency`, `multiple_choice`) VALUES ('saleOrderMaterial', 'saleOrderTarget', '系统统计', 'saleOrderMaterial', 1, NULL);

DELETE from dfs_target_method WHERE target_name in ('saleOrderCountPerMonth','saleOrderCountPerYear','saleOrderDeliveredCountPerMonth','saleOrderDeliveredCountPerYear','saleOrderDeliveredTimelyCountPerMonth','saleOrderDeliveredTimelyCountPerYear','saleOrderDeliveredDelayCountPerMonth','saleOrderDeliveredDelayCountPerYear','saleOrderNoDeliveredDelayCountPerMonth','saleOrderNoDeliveredDelayCountPerYear','saleMaterialCountPerMonth','saleMaterialCountPerYear','saleMaterialDeliveredCountPerMonth','saleMaterialDeliveredCountPerYear','saleMaterialDeliveredTimelyCountPerMonth','saleMaterialDeliveredTimelyCountPerYear','saleMaterialNoDeliveredCountPerMonth','saleMaterialNoDeliveredCountPerYear','saleMaterialNoDeliveredDelayCountPerMonth','saleMaterialNoDeliveredDelayCountPerYear','saleOrderDeliveredTimelyRatePerMonth','saleOrderDeliveredTimelyRatePerYear','saleMaterialDeliveredTimelyRatePerMonth','saleMaterialDeliveredTimelyRatePerYear','deliveredTimelyRatePerMonth','deliveredTimelyRatePerYear','saleOrderDeliveredRate');
DELETE from dfs_target_dict WHERE target_name in ('saleOrderCountPerMonth','saleOrderCountPerYear','saleOrderDeliveredCountPerMonth','saleOrderDeliveredCountPerYear','saleOrderDeliveredTimelyCountPerMonth','saleOrderDeliveredTimelyCountPerYear','saleOrderDeliveredDelayCountPerMonth','saleOrderDeliveredDelayCountPerYear','saleOrderNoDeliveredDelayCountPerMonth','saleOrderNoDeliveredDelayCountPerYear','saleMaterialCountPerMonth','saleMaterialCountPerYear','saleMaterialDeliveredCountPerMonth','saleMaterialDeliveredCountPerYear','saleMaterialDeliveredTimelyCountPerMonth','saleMaterialDeliveredTimelyCountPerYear','saleMaterialNoDeliveredCountPerMonth','saleMaterialNoDeliveredCountPerYear','saleMaterialNoDeliveredDelayCountPerMonth','saleMaterialNoDeliveredDelayCountPerYear','saleOrderDeliveredTimelyRatePerMonth','saleOrderDeliveredTimelyRatePerYear','saleMaterialDeliveredTimelyRatePerMonth','saleMaterialDeliveredTimelyRatePerYear','deliveredTimelyRatePerMonth','deliveredTimelyRatePerYear','saleOrderDeliveredRate');
DELETE from dfs_target_model WHERE target_name in ('saleOrderCountPerMonth','saleOrderCountPerYear','saleOrderDeliveredCountPerMonth','saleOrderDeliveredCountPerYear','saleOrderDeliveredTimelyCountPerMonth','saleOrderDeliveredTimelyCountPerYear','saleOrderDeliveredDelayCountPerMonth','saleOrderDeliveredDelayCountPerYear','saleOrderNoDeliveredDelayCountPerMonth','saleOrderNoDeliveredDelayCountPerYear','saleMaterialCountPerMonth','saleMaterialCountPerYear','saleMaterialDeliveredCountPerMonth','saleMaterialDeliveredCountPerYear','saleMaterialDeliveredTimelyCountPerMonth','saleMaterialDeliveredTimelyCountPerYear','saleMaterialNoDeliveredCountPerMonth','saleMaterialNoDeliveredCountPerYear','saleMaterialNoDeliveredDelayCountPerMonth','saleMaterialNoDeliveredDelayCountPerYear','saleOrderDeliveredTimelyRatePerMonth','saleOrderDeliveredTimelyRatePerYear','saleMaterialDeliveredTimelyRatePerMonth','saleMaterialDeliveredTimelyRatePerYear','deliveredTimelyRatePerMonth','deliveredTimelyRatePerYear','saleOrderDeliveredRate');


-- 新增字典，物料检验类型和检验方式的关联关系
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'productOrderByOneOfWorkWorkOrderInvestment', '投产-生产订单下其中一个生产工单需要校验', '首件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'productOrderByOneOfWorkOrdersReport', '报工-生产订单下其中一个生产工单需要校验', '首件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'productOrderByOneOfWorkOrdersFinish', '完工-生产订单下其中一个生产工单需要校验', '首件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderInvestment', '投产-生产工单需要校验', '首件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderReport', '报工-生产工单需要校验', '首件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderFinish', '完工-生产工单需要校验', '首件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);

INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'productOrderByOneOfWorkOrdersFinish', '完工-生产订单下其中一个生产工单需要校验', '末件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderFinish', '完工-生产工单需要校验', '末件检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);

INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'productOrderByOneOfWorkOrdersReport', '报工-生产订单下其中一个生产工单需要校验', '过程检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'productOrderByOneOfWorkOrdersFinish', '完工-生产订单下其中一个生产工单需要校验', '过程检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderReport', '报工-生产工单需要校验', '过程检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderFinish', '完工-生产工单需要校验', '过程检验', NULL, NULL, '', 'materialInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
-- 新增字典，工艺工序检验类型和检验方式的关联关系
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderInvestment', '投产', '首件检验', NULL, NULL, '', 'craftProcedureInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderReport', '报工', '首件检验', NULL, NULL, '', 'craftProcedureInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderFinish', '完工', '首件检验', NULL, NULL, '', 'craftProcedureInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);

INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderReport', '报工', '过程检验', NULL, NULL, '', 'craftProcedureInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);
INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderFinish', '完工', '过程检验', NULL, NULL, '', 'craftProcedureInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);

INSERT INTO `dfs_dict`(`id`, `code`, `name`, `type`, `url`, `unit`, `des`, `value`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (null, 'workOrderFinish', '完工', '末件检验', NULL, NULL, '', 'craftProcedureInspectMethod', '2023-08-29 11:29:42', '2023-08-29 11:29:42', 'yelinkoncall', NULL);

-- 不良和维修关联表 DDL
DROP TABLE IF EXISTS `dfs_defect_maintain`;
CREATE TABLE `dfs_defect_maintain` (
  `defect_maintain_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `defect_id` int(11) DEFAULT NULL COMMENT '不良定义ID',
  `maintain_id` int(11) DEFAULT NULL COMMENT '质检返工ID（维修ID）',
  PRIMARY KEY (`defect_maintain_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='不良定义和维修定义关联关系表';
-- 工位质检不良记录表，添加“维修ID”字段
ALTER TABLE dfs_record_work_order_unqualified add COLUMN state TINYINT COMMENT '状态（1-打开 0-关闭）（打开：未维修 关闭：已维修）' DEFAULT 1 AFTER serial_number;
ALTER TABLE dfs_record_work_order_unqualified add COLUMN maintain_ids VARCHAR(500) COMMENT '维修ID(多个以逗号隔开)' AFTER state;


-- 删除无用权限
DELETE FROM sys_permissions WHERE `path` = 'production.workorder:serialNumber:add';

-- 新增物料检验导入权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10801210', '下载物料检验默认模板', 'material:inspect.export-template', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10801', 2, 1, 0, '/product-management/supplies', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10801220', '导入物料检验数据', 'material:inspect.import-data', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10801', 2, 1, 0, '/product-management/supplies', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10801230', '查看物料检验导入日志', 'material:inspect.import-log', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10801', 2, 1, 0, '/product-management/supplies', 1, NULL, '');
call init_new_role_permission('10801210');
call init_new_role_permission('10801220');
call init_new_role_permission('10801230');

-- 新增工序检验方案导入全西安
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10803440', '工序检验方案控制数据导入与日志查询', 'procedureInspectControllerImport', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'POST', '10803', 2, 1, 0, '/product-management/technology', 1, NULL, '');
call init_new_role_permission('10803440');
-- 电池模型
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('battery', '电池模块', 'battery', '', NULL);
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('batteryCellManagementModel', '电池电芯管理模型', 'battery.batteryCellManagementModel', 'battery', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`) VALUES ('productFlowCodeType', '电池组装工厂电芯适配模型', 'battery.batteryCellManagementModel.productFlowCodeType', 'battery.batteryCellManagementModel', 'select', 'api', '/api/production/code/types', 'get', NULL, 'code,name', '[1,4]', '1');


-- 将旧工位质检的数据，修改成“已维修”
UPDATE dfs_record_work_order_unqualified SET state=0 WHERE state=1;

-- 兼容历史数据，所有工艺工序统一设置默认值
INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       1      inspect_type_code,
       '过程检验' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 1);

INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       2      inspect_type_code,
       '库存检验' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 2);

INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       3      inspect_type_code,
       '首件检验' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 3);

INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       4      inspect_type_code,
       '末件检验' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 4);

INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       5      inspect_type_code,
       '来料检验' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 5);

INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       6      inspect_type_code,
       '退货检验' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 6);

INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       7      inspect_type_code,
       '发货检验' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 7);

INSERT INTO dfs_procedure_inspection_controller_config (`craft_id`, `craft_procedure_id`, `inspect_type_code`,
                                                        `inspect_type_name`)
SELECT craft_id,
       id     craft_procedure_id,
       8      inspect_type_code,
       '生产巡检' inspect_type_name
FROM dfs_craft_procedure
where id NOT IN
      (SELECT craft_procedure_id FROM dfs_procedure_inspection_controller_config where `inspect_type_code` = 8);


-- 质检操作变更工单状态
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('reportConfiguration', '质检操作配置', 'quality.reportConfiguration', 'quality', NULL);
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`) VALUES ('changeWorkOrderStatus', '质检变更工单状态', 'quality.changeWorkOrderStatus', 'quality.commencementConfiguration', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('enable', '是否开启工单状态变更', 'quality.changeWorkOrderStatus.enable', 'quality.changeWorkOrderStatus', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`)
VALUES ('applets', '启用小程序(多选)', 'quality.changeWorkOrderStatus.applets', 'quality.changeWorkOrderStatus', 'select-multiple', 'api', '/api/sys/get/all/applet/code/name', 'get', NULL, 'code,name', NULL, '[]');
--  生产工单列配置方案配置权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`)
VALUES ('10005570', '列配置', 'production.workorder:workerOrderSchemeConfiguration', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10005', 2, 1, 0, '/order-model/production-workorder', 1, NULL);
call init_new_role_permission('10005570');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`)
VALUES ('10101560', '列配置', 'production.workorder:workerOrderSchemeConfiguration', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10101', 2, 1, 0, '/workorder-model/production-workorder', 1, NULL);
call init_new_role_permission('10101560');

-- 消息通知层级关系配置
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('firstEffectPatrolNotice', '首检巡检单生效通知', '{生产订单号},{工单号},{送检单类型},{送检单编码},{工序},{工单投产时间},{检验单生效时间},{物料编号},{物料名称}', 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('processEffectPatrolNotice', '过程巡检单生效通知', '{生产订单号},{工单号},{送检单类型},{送检单编码},{工序},{工单投产时间},{检验单生效时间},{物料编号},{物料名称}', 'qualityInfoNotice');
INSERT INTO `dfs_config_notice_level_relation` ( `code`, `name`, `configurable_placeholder`, `parent_code`) VALUES ('endEffectPatrolNotice', '末检巡检单生效通知', '{生产订单号},{工单号},{送检单类型},{送检单编码},{工序},{工单投产时间},{检验单生效时间},{物料编号},{物料名称}', 'qualityInfoNotice');
-- 消息通知配置
INSERT INTO `dfs_circulation_notice_config` (`config_name`, `notice_type`, `notice_content`, `frequency`, `notice_method`, `state`, `remark`, `create_by`, `update_by`, `create_time`, `update_time`, `contact_ids`, `limit_scope`, `trigger_mode`, `is_notice_repeat`, `max_repeat_notice`) VALUES ('首检单生效推送', 'firstEffectPatrolNotice', '生产订单:{生产订单号}下的工单:{工单号}，有{送检单类型}送检单号:{送检单编码}生效，需进行巡检', NULL, '1', '1', '', 'admin', NULL, '2023-08-25 10:14:58', NULL, '', NULL, 'manual', '0', '0');
INSERT INTO `dfs_circulation_notice_config` (`config_name`, `notice_type`, `notice_content`, `frequency`, `notice_method`, `state`, `remark`, `create_by`, `update_by`, `create_time`, `update_time`, `contact_ids`, `limit_scope`, `trigger_mode`, `is_notice_repeat`, `max_repeat_notice`) VALUES ('过程检单生效推送', 'processEffectPatrolNotice', '生产订单:{生产订单号}下的工单:{工单号}，有{送检单类型}送检单号:{送检单编码}生效，需进行巡检', NULL, '1', '1', '', 'admin', NULL, '2023-08-25 10:14:58', NULL, '', NULL, 'manual', '0', '0');
INSERT INTO `dfs_circulation_notice_config` (`config_name`, `notice_type`, `notice_content`, `frequency`, `notice_method`, `state`, `remark`, `create_by`, `update_by`, `create_time`, `update_time`, `contact_ids`, `limit_scope`, `trigger_mode`, `is_notice_repeat`, `max_repeat_notice`) VALUES ('末检单生效推送', 'endEffectPatrolNotice', '生产订单:{生产订单号}下的工单:{工单号}，有{送检单类型}送检单号:{送检单编码}生效，需进行巡检', NULL, '1', '1', '', 'admin', NULL, '2023-08-25 10:14:58', NULL, '', NULL, 'manual', '0', '0');

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10113', '上料防错', '/workorder-model/feed-to-prevent-errors', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '101', 1, 1, 0, '/workorder-model', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10113010', '厂区设置', 'feedToPreventErrors:factory.setting', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10113', 2, 1, 0, '/workorder-model/feed-to-prevent-errors', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10113020', '制造单元设置', 'feedToPreventErrors:line.setting', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10113', 2, 1, 0, '/workorder-model/feed-to-prevent-errors', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10113030', '批量编辑', 'feedToPreventErrors:batch.edit', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'POST', '10113', 2, 1, 0, '/workorder-model/feed-to-prevent-errors', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10113040', '编辑', 'feedToPreventErrors:edit', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'PUT', '10113', 2, 1, 0, '/workorder-model/feed-to-prevent-errors', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10113050', '详情', 'feedToPreventErrors:select', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10113', 2, 1, 0, '/workorder-model/feed-to-prevent-errors', 1, NULL, '');
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`) VALUES ('10113060', '列配置', 'feedToPreventErrors:columnConfigList', NULL, NULL, NULL, NULL, '2022-12-06 10:27:20', 'enable', 'GET', '10113', 2, 1, 0, '/workorder-model/feed-to-prevent-errors', 1, NULL, '');
call init_new_role_permission('10113%');

INSERT INTO `sys_route`(`id`, `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`) VALUES (null, '/workorder-model/feed-to-prevent-errors', '/workorder-model', 'feedToPreventErrors', NULL, '上料防错', NULL, 'dfs', NULL, NULL, NULL, 0, 1, NULL, '');

-- 生产订单新增复制按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
 VALUES ('10003450', '复制', 'product.order:copyConfiguration', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10003', 2, 1, 0, '/order-model/product-order', 1);
call init_new_role_permission('10003450');

INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.loadMaterialStationMachine.machine', '上料工位机', '/load-material-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);

-- 重命名菜单，将 小程序设置菜单名应改为：工位机设置
UPDATE sys_permissions SET `name` = '工位机设置' WHERE `path` = '/system_settings/app-permission-config';
UPDATE sys_route SET `title` = '工位机设置' WHERE `path` = '/system_settings/app-permission-config';

-- 兼容可能因代码问题导致产生的脏数据，对工单投产后没有实际开始时间进行赋值
UPDATE dfs_work_order wo
    INNER JOIN dfs_order_change_log ocl ON wo.work_order_number = ocl.order_number
set wo.actual_start_date = ocl.create_time
where wo.actual_start_date is null and (ocl.des = '工单状态变更为: 投产' or ocl.des = '由 "生效"调整为 "投产"');

-- 工序定义新增合格检查
call proc_add_column(
        'dfs_procedure',
        'qualified_check',
        'ALTER TABLE `dfs_procedure` ADD COLUMN `qualified_check` tinyint(1) DEFAULT 0 COMMENT ''合格检查 0-否 1-是''');
-- 维修判定加权限
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`) VALUES ('yelink.kangjuren.maintenance.machine.finish', '成品', 'maintenance.station.machine:finish', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.maintenance.machine', 2, 0, 1, NULL, 1, NULL);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`) VALUES ('yelink.kangjuren.maintenance.machine.repair', '返回产线', 'maintenance.station.machine:repair', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.maintenance.machine', 2, 0, 1, NULL, 1, NULL);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`) VALUES ('yelink.kangjuren.maintenance.machine.scrap', '条码作废', 'maintenance.station.machine:scrap', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.maintenance.machine', 2, 0, 1, NULL, 1, NULL);
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`) VALUES ('yelink.kangjuren.maintenance.machine.scrapReuse', '条码复用', 'maintenance.station.machine:scrapReuse', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', 'yelink.kangjuren.maintenance.machine', 2, 0, 1, NULL, 1, NULL);

-- 条码合格状态校验
call proc_add_column(
        'dfs_facilities',
        'code_state_check',
        'ALTER TABLE `dfs_facilities` ADD COLUMN `code_state_check` tinyint(1) DEFAULT 1 COMMENT ''条码合格状态校验''');
-- 维修记录 添加不良信息
ALTER TABLE dfs_maintain_record add COLUMN defect_id INT COMMENT '不良ID' AFTER maintain_result_type;