-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================21.2
-- 委外订单按BOM下推委外发料单
-- 下推配置
INSERT INTO `dfs_dict`(`code`, `name`, `type`, `value`) VALUES ('subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', '按BOM下推委外发料单', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder', 'show');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.targetOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'api', '/ams/subcontract/delivery/states', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.bomSplitTypes', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"firstLevelBomNotFilter\"', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.push.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingIssueMaterial\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.url', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial?pushDownOrigin=subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value_dict`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractDeliveryOrder\"', 'sysConf');
-- 内置配置
INSERT INTO `dfs_order_push_down_item`(`id`, `config_path`, `name`, `type`, `enable`, `is_inner`, `instance_type`, `create_by`, `update_by`, `create_time`, `update_time`, `is_show`)
VALUES (NULL, 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder',  '按BOM下推委外发料单', 'RULE', 1, 1, 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'admin', 'admin', NOW(), NOW(), 1);
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'originalOrderStates', '原单状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.originalOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select-multiple', 'api', '/ams/subcontract/orders/states', 'get', NULL, 'code,name', '[2,3]', '[2,3]', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderState', '目标单据状态', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.targetOrderStates', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'api', '/ams/subcontract/delivery/states', 'get', NULL, 'code,name', '[1,2]', '1', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'isSupportMaterialLineReversePushDown', '是否支持物料行反选', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.isSupportMaterialLineReversePushDown', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', 'sourceConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'bomSplitTypes', 'BOM拆分方式', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.bomSplitTypes', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'api', '/api/config/push/down/bom/split', 'get', NULL, 'code,name', '[\"firstLevelBomNotFilter\",\"multiLevelBomNotFilter\"]', '\"firstLevelBomNotFilter\"', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'pushDownAgain', '再次下推', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.push.pushDownAgain', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"支持\"},{\"value\":false,\"label\":\"不支持\"}]', 'true', 'targetConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'applicationId', 'applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.applicationId', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"new.yk.genieos.ams.scc.osm.outsourcingIssueMaterial\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'url', '前端路由', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.url', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"/ams/supply-chain-collaboration/outsourcingManagement/outsourcingIssueMaterial?pushDownOrigin=subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'sourceOrderType', '源单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.sourceOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractOrder\"', 'sysConf');
INSERT INTO `dfs_order_push_down_config_value`(`id`, `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `group_type`)
VALUES (NULL, 'targetOrderType', '目标单据类型编码', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom.targetOrderType', 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom', 'input', 'input', NULL, NULL, NULL, NULL, NULL, '\"subcontractDeliveryOrder\"', 'sysConf');
-- 关联绑定itemId
UPDATE `dfs_order_push_down_config_value` a,`dfs_order_push_down_item` b
SET a.`item_id` = b.`id`
WHERE a.`config_full_path_code` = b.`instance_type` and a.`item_id` is null and b.`instance_type` = 'subcontract.subcontractOrderPushDownConfig.subcontractDeliveryOrder.bom';

-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================21.2
