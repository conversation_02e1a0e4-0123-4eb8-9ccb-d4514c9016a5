-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================21.2
-- 客户档案
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customer', '客户档案', 'customer', 'dfs', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerList', '客户档案表页', 'customer.list', 'customer', 1, 'list');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerDetail', '客户档案详情页', 'customer.detail', 'customer', 1, 'detail');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerEdit', '客户档案编辑页', 'customer.edit', 'customer', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'customerEditByCreate', '创建态', 'customer.edit.byCreate', 'customer.edit', 1, 'edit-create');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'customerEditByRelease', '生效态', 'customer.edit.byRelease', 'customer.edit', 1, 'edit-other');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'customerEditByDisable', '停用态', 'customer.edit.byDisable', 'customer.edit', 1, 'edit-other');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.list', 'customer.list', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.detail', 'customer.detail', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byCreate', 'customer.edit.byCreate', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byRelease', 'customer.edit.byRelease', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byDisable', 'customer.edit.byDisable', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.list', 'customer.list', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.detail', 'customer.detail', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byRelease', 'customer.edit.byRelease', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byDisable', 'customer.edit.byDisable', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byCreate', 'customer.edit.byCreate', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);


-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerContacts', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerPriority', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'salesman', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'companyType', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'companyScale', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

call proc_add_form_field('customer.list', 'customerCode', '客户编码');
call proc_add_form_field('customer.list', 'customerName', '客户名称');
call proc_add_form_field('customer.list', 'customerContacts', '客户联系人');
call proc_add_form_field('customer.list', 'customerMobile', '客户联系方式');
call proc_add_form_field('customer.list', 'customerPriority', '客户优先级');
call proc_add_form_field('customer.list', 'salesman', '业务员');
call proc_add_form_field('customer.list', 'salesmanNickName', '业务员简称');
call proc_add_form_field('customer.list', 'salesmanMobile', '业务员联系方式');
call proc_add_form_field('customer.list', 'stateName', '状态');
call proc_add_form_field('customer.list', 'companyType', '公司类型');
call proc_add_form_field('customer.list', 'companyScale', '公司规模');
call proc_add_form_field('customer.list', 'remark', '备注');
call proc_add_form_field('customer.list', 'createTime', '创建时间');
call proc_add_form_field('customer.list', 'updateTime', '修改时间');
call proc_add_form_field('customer.list', 'createByName', '创建人');
call proc_add_form_field('customer.list', 'updateByName', '修改人');

call proc_add_form_field_module('customer.list', 'customerExtendFieldOne', '客户扩展字段1', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldTwo', '客户扩展字段2', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldThree', '客户扩展字段3', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldFour', '客户扩展字段4', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldFive', '客户扩展字段5', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldSix', '客户扩展字段6', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldSeven', '客户扩展字段7', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldEight', '客户扩展字段8', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldNine', '客户扩展字段9', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldTen', '客户扩展字段10', 'baseExtendField');

-- 详情
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerContacts', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerPriority', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'salesman', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'companyType', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'companyScale', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

call proc_add_form_field('customer.detail', 'customerCode', '客户编码');
call proc_add_form_field('customer.detail', 'customerName', '客户名称');
call proc_add_form_field('customer.detail', 'customerContacts', '客户联系人');
call proc_add_form_field('customer.detail', 'customerMobile', '客户联系方式');
call proc_add_form_field('customer.detail', 'customerPriority', '客户优先级');
call proc_add_form_field('customer.detail', 'salesman', '业务员');
call proc_add_form_field('customer.detail', 'salesmanNickName', '业务员简称');
call proc_add_form_field('customer.detail', 'salesmanMobile', '业务员联系方式');
call proc_add_form_field('customer.detail', 'stateName', '状态');
call proc_add_form_field('customer.detail', 'companyType', '公司类型');
call proc_add_form_field('customer.detail', 'companyScale', '公司规模');
call proc_add_form_field('customer.detail', 'remark', '备注');
call proc_add_form_field('customer.detail', 'createTime', '创建时间');
call proc_add_form_field('customer.detail', 'updateTime', '修改时间');
call proc_add_form_field('customer.detail', 'createByName', '创建人');
call proc_add_form_field('customer.detail', 'updateByName', '修改人');

call proc_add_form_field_module('customer.detail', 'customerExtendFieldOne', '客户扩展字段1', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldTwo', '客户扩展字段2', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldThree', '客户扩展字段3', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldFour', '客户扩展字段4', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldFive', '客户扩展字段5', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldSix', '客户扩展字段6', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldSeven', '客户扩展字段7', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldEight', '客户扩展字段8', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldNine', '客户扩展字段9', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldTen', '客户扩展字段10', 'baseExtendField');

-- 编辑(创建态)
call proc_add_form_field('customer.edit', 'customerCode', '客户编码');
call proc_add_form_field('customer.edit', 'customerName', '客户名称');
call proc_add_form_field('customer.edit', 'customerContacts', '客户联系人');
call proc_add_form_field('customer.edit', 'customerMobile', '客户联系方式');
call proc_add_form_field('customer.edit', 'customerPriority', '客户优先级');
call proc_add_form_field('customer.edit', 'salesman', '业务员');
call proc_add_form_field('customer.edit', 'salesmanNickName', '业务员简称');
call proc_add_form_field('customer.edit', 'salesmanMobile', '业务员联系方式');
call proc_add_form_field('customer.edit', 'state', '状态');
call proc_add_form_field('customer.edit', 'companyType', '公司类型');
call proc_add_form_field('customer.edit', 'companyScale', '公司规模');
call proc_add_form_field('customer.edit', 'remark', '备注');
call proc_add_form_field('customer.edit', 'createTime', '创建时间');
call proc_add_form_field('customer.edit', 'updateTime', '修改时间');
call proc_add_form_field('customer.edit', 'createBy', '创建人');
call proc_add_form_field('customer.edit', 'updateBy', '修改人');

call proc_add_form_field_module('customer.edit', 'customerExtendFieldOne', '客户扩展字段1', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldTwo', '客户扩展字段2', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldThree', '客户扩展字段3', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldFour', '客户扩展字段4', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldFive', '客户扩展字段5', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldSix', '客户扩展字段6', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldSeven', '客户扩展字段7', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldEight', '客户扩展字段8', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldNine', '客户扩展字段9', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldTen', '客户扩展字段10', 'baseExtendField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerContacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerMobile', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerPriority', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'salesman', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'companyType', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'companyScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'createBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'updateBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

-- 编辑（生效）
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerContacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerMobile', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerPriority', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'salesman', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'companyType', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'companyScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'createBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'updateBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

-- 编辑（停用）
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerContacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerMobile', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerPriority', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'salesman', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'companyType', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'companyScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'createBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'updateBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

-- 新增客户档案二开接口查询脚本
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerId', '客户id', 'customer_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerCode', '客户编号', 'customer_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerName', '客户名称', 'customer_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerMobile', '客户联系方式', 'customer_mobile');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'salesman', '业务员', 'salesman');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'cusState', '客户状态', 'cus_state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'updateBy', '修改人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerContacts', '客户联系人', 'customer_contacts');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'dutyParagraph', '纳税人识别号', 'duty_paragraph');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'bankAccount', '开户行', 'bank_account');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'bankNumber', '账号', 'bank_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyNumber', '电话', 'company_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyAddress', '注册地址', 'company_address');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerPriority', '客户优先级', 'customer_priority');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyType', '公司类型', 'company_type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyScale', '公司规模', 'company_scale');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyFullName', '企业全称', 'company_full_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldOne', '客户档案拓展字段1', 'customer_extend_field_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldTwo', '客户档案拓展字段2', 'customer_extend_field_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldThree', '客户档案拓展字段3', 'customer_extend_field_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldFour', '客户档案拓展字段4', 'customer_extend_field_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldFive', '客户档案拓展字段5', 'customer_extend_field_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldSix', '客户档案拓展字段6', 'customer_extend_field_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldSeven', '客户档案拓展字段7', 'customer_extend_field_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldEight', '客户档案拓展字段8', 'customer_extend_field_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldNine', '客户档案拓展字段9', 'customer_extend_field_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldTen', '客户档案拓展字段10', 'customer_extend_field_ten');


INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerId', '客户档案拓展字段10', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerCode', '客户编号', 'customer_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'state', '客户物料状态', 'state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'materialCode', '物料编码', 'material_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialCode', '客户物料编码', 'customer_material_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialName', '客户物料名称', 'customer_material_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'updateBy', '更新人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialStandard', '客户物料规格', 'customer_material_standard');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'price', '价格', 'price');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldOne', '客户物料扩展字段1', 'customer_material_extend_field_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldTwo', '客户物料扩展字段2', 'customer_material_extend_field_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldThree', '客户物料扩展字段3', 'customer_material_extend_field_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldFour', '客户物料扩展字段4', 'customer_material_extend_field_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldFive', '客户物料扩展字段5', 'customer_material_extend_field_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldSix', '客户物料扩展字段6', 'customer_material_extend_field_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldSeven', '客户物料扩展字段7', 'customer_material_extend_field_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldEight', '客户物料扩展字段8', 'customer_material_extend_field_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldNine', '客户物料扩展字段9', 'customer_material_extend_field_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldTen', '客户物料扩展字段10', 'customer_material_extend_field_ten');

-- 下推标识软参设置
-- 生产工单用料清单
UPDATE `dfs_business_config` SET `name` = '生产工单用料清单配置' WHERE `full_path_code` = 'production.workerMaterialsList.listConfig';
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('pushDownIdentifierConf', '下推标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf', 'production.workerMaterialsList.listConfig', NULL, 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', '');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('outputPickingProduct', '领料出库单', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('workOrderSupplement', '生产补料出库单', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('transferOrder', '调拨单', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.noPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.partPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.allPushDown', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.noPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.noPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.partPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.partPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.allPushDown.enable', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`( `value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.allPushDown.colorIdentifier', 'production.workerMaterialsList.listConfig.pushDownIdentifierConf.transferOrder.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

-- 生产工单
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('pushDownIdentifierConf', '下推标识', 'production.workOrderConfig.pushDownIdentifierConf', 'production.workOrderConfig', NULL, 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', '');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('workOrderMaterialList', '生产工单用料清单', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList', 'production.workOrderConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', '');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.noPushDown', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', '');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.partPushDown', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', '');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.allPushDown', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', '');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('outputPickingProduct', '生产领料出库单', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct', 'production.workOrderConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('workOrderSupplement', '生产补料出库单', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement', 'production.workOrderConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('productionReturnReceipt', '生产退料入库单', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt', 'production.workOrderConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.noPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.partPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.allPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('productionIn', '生产入库单', 'production.workOrderConfig.pushDownIdentifierConf.productionIn', 'production.workOrderConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.noPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionIn', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.partPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionIn', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.allPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionIn', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'wms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('productionInspectOrder', '生产检验单', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder', 'production.workOrderConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.noPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.partPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.allPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('productionProcessOrder', '生产巡检单', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder', 'production.workOrderConfig.pushDownIdentifierConf', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('noPushDown', '未下推', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.noPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('partPushDown', '部分下推', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.partPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('allPushDown', '已下推', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.allPushDown', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder', '用于标记下推状态', 'yelinkoncall', 'yelinkoncall', '2025-07-22 17:34:57', '2025-07-22 17:34:57', 'qms');

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.noPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.noPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.partPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.partPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.allPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.allPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.workOrderMaterialList.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.outputPickingProduct.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.workOrderSupplement.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.noPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.noPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.partPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.partPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.allPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.allPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionReturnReceipt.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.noPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.noPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.partPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.partPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.allPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.allPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionIn.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.noPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.noPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.partPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.partPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.allPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.allPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionInspectOrder.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.noPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.noPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.noPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.noPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.partPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.partPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.partPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.partPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('enable', '是否启用(单选)', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.allPushDown.enable', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.allPushDown', 'select', 'table', NULL, NULL, NULL, NULL, '[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]', 'true', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('colorIdentifier', '颜色标识', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.allPushDown.colorIdentifier', 'production.workOrderConfig.pushDownIdentifierConf.productionProcessOrder.allPushDown', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);

-- 新增下推标识字段
call proc_add_form_field_module('workOrder.list', 'pushDownIdentifier', '下推标识', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'pushDownIdentifier', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

call proc_add_form_field_module('workOrderMaterialList.list.material', 'pushDownIdentifier', '下推标识', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list', 'pushDownIdentifier', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');


-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================21.2
