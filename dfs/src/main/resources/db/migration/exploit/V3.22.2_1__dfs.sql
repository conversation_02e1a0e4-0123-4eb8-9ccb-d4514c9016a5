-- ================================================此脚本专为企业微信审批需求设定=======================================================
UPDATE dfs_approve_node_config t
SET t.can_wechat_approve = 1
WHERE t.full_path_code = 'productOrder.releasedApprove';

INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`)
VALUES ('productOrder.releasedApprove', 'baseField', '基础信息', 1);
INSERT INTO `dfs_approve_node_module`(`full_path_code`, `module_code`, `module_name`, `seq`, `value_source`)
VALUES ('productOrder.releasedApprove', 'materialField', '物料信息', 2, 'productOrderMaterial');

-- baseField字段（ProductOrderEntity中的表字段）
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`)
VALUES ('productOrder.releasedApprove', 'baseField', 'productOrderId', '生产订单id'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderNumber', '生产订单编号'),
       ('productOrder.releasedApprove', 'baseField', 'stateName', '生产订单状态'),
       ('productOrder.releasedApprove', 'baseField', 'customerCode', '客户编码'),
       ('productOrder.releasedApprove', 'baseField', 'customerName', '客户名称'),
       ('productOrder.releasedApprove', 'baseField', 'relatedOrderNum', '关联的上级生产订单号'),
       ('productOrder.releasedApprove', 'baseField', 'relatedRootOrderNum', '关联的根订单'),
       ('productOrder.releasedApprove', 'baseField', 'remark', '生产订单备注'),
       ('productOrder.releasedApprove', 'baseField', 'createTime', '生产订单创建时间'),
       ('productOrder.releasedApprove', 'baseField', 'updateTime', '生产订单修改时间'),
       ('productOrder.releasedApprove', 'baseField', 'importTime', '生产订单导入时间'),
       ('productOrder.releasedApprove', 'baseField', 'createBy', '生产订单创建人'),
       ('productOrder.releasedApprove', 'baseField', 'updateBy', '生产订单修改人'),
       ('productOrder.releasedApprove', 'baseField', 'isPrint', '是否已打印'),
       ('productOrder.releasedApprove', 'baseField', 'type', '业务类型'),
       ('productOrder.releasedApprove', 'baseField', 'orderType', '单据类型'),
       ('productOrder.releasedApprove', 'baseField', 'projectContract', '存在项目合同'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldOneName', '生产订单扩展字段1'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldTwoName', '生产订单扩展字段2'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldThreeName', '生产订单扩展字段3'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldFourName', '生产订单扩展字段4'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldFiveName', '生产订单扩展字段5'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldSixName', '生产订单扩展字段6'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldSevenName', '生产订单扩展字段7'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldEightName', '生产订单扩展字段8'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldNineName', '生产订单扩展字段9'),
       ('productOrder.releasedApprove', 'baseField', 'productOrderExtendFieldTenName', '生产订单扩展字段10');

-- materialField字段（ProductOrderMaterialEntity中的表字段）
INSERT INTO `dfs_approve_node_field`(`full_path_code`, `module_code`, `field_code`, `field_name`)
VALUES ('productOrder.releasedApprove', 'materialField', 'lineNumber', '生产订单物料行号'),
       ('productOrder.releasedApprove', 'materialField', 'relatedSaleOrderMaterialLineNumber',
        '关联的销售订单物料行号'),
       ('productOrder.releasedApprove', 'materialField', 'materialCode', '物料编码'),
       ('productOrder.releasedApprove', 'materialField', 'requireGoodsDate', '要货日期'),
       ('productOrder.releasedApprove', 'materialField', 'salesQuantity', '销售数量'),
       ('productOrder.releasedApprove', 'materialField', 'planQuantity', '计划数量'),
       ('productOrder.releasedApprove', 'materialField', 'scheduledProductionQuantity', '排产数量'),
       ('productOrder.releasedApprove', 'materialField', 'needProduceQuantity', '需生产数量'),
       ('productOrder.releasedApprove', 'materialField', 'finishCount', '完成数量'),
       ('productOrder.releasedApprove', 'materialField', 'unqualifiedCount', '不合格数'),
       ('productOrder.releasedApprove', 'materialField', 'plannedBatches', '计划批数'),
       ('productOrder.releasedApprove', 'materialField', 'plansPerBatch', '每批计划数'),
       ('productOrder.releasedApprove', 'materialField', 'actualBatches', '实际批数'),
       ('productOrder.releasedApprove', 'materialField', 'planProductStartTime', '计划开始生产时间'),
       ('productOrder.releasedApprove', 'materialField', 'planProductEndTime', '计划生产完成时间'),
       ('productOrder.releasedApprove', 'materialField', 'actualProductStartTime', '实际开始生产时间'),
       ('productOrder.releasedApprove', 'materialField', 'actualProductEndTime', '实际生产完成时间'),
       ('productOrder.releasedApprove', 'materialField', 'saleOrderCode', '销售订单编码'),
       ('productOrder.releasedApprove', 'materialField', 'relateSaleOrderMaterialId', '关联销售订单的物料行ID'),
       ('productOrder.releasedApprove', 'materialField', 'schedulingStatus', '排程状态'),
       ('productOrder.releasedApprove', 'materialField', 'scheduleMode', '排程方式'),
       ('productOrder.releasedApprove', 'materialField', 'progress', '进度'),
       ('productOrder.releasedApprove', 'materialField', 'procedureProcess', '生产订单工序进度'),
       ('productOrder.releasedApprove', 'materialField', 'procedureProcessDetail', '生产订单工序进度详情'),
       ('productOrder.releasedApprove', 'materialField', 'mergedState', '合单状态'),
       ('productOrder.releasedApprove', 'materialField', 'mergeOrderNumber', '关联的合单号'),
       ('productOrder.releasedApprove', 'materialField', 'priority', '优先级'),
       ('productOrder.releasedApprove', 'materialField', 'gridCode', '车间编码'),
       ('productOrder.releasedApprove', 'materialField', 'gridName', '车间名称'),
       ('productOrder.releasedApprove', 'materialField', 'bomNumber', 'BOM编码'),
       ('productOrder.releasedApprove', 'materialField', 'bomVersion', 'BOM版本'),
       ('productOrder.releasedApprove', 'materialField', 'bomLayerNum', 'BOM层级数'),
       ('productOrder.releasedApprove', 'materialField', 'bomRatio', 'BOM系数'),
       ('productOrder.releasedApprove', 'materialField', 'materialIsComplete', '物料是否齐套'),
       ('productOrder.releasedApprove', 'materialField', 'skuId', '特征参数skuId'),
       ('productOrder.releasedApprove', 'materialField', 'packageSchemeCode', '包装方案编码'),
       ('productOrder.releasedApprove', 'materialField', 'processStatus', '过程状态'),
       ('productOrder.releasedApprove', 'materialField', 'pickingStateName', '领料状态'),
       ('productOrder.releasedApprove', 'materialField', 'pickingQuantity', '已领料数'),
       ('productOrder.releasedApprove', 'materialField', 'inventoryQuantity', '已入库数'),
       ('productOrder.releasedApprove', 'materialField', 'customerOrderNumber', '客户订单编号'),
       ('productOrder.releasedApprove', 'materialField', 'customerMaterialCode', '客户物料编码'),
       ('productOrder.releasedApprove', 'materialField', 'customerMaterialName', '客户物料名称'),
       ('productOrder.releasedApprove', 'materialField', 'customerSpecification', '客户物料规格'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldOneName',
        '生产订单物料扩展字段1'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldTwoName',
        '生产订单物料扩展字段2'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldThreeName',
        '生产订单物料扩展字段3'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldFourName',
        '生产订单物料扩展字段4'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldFiveName',
        '生产订单物料扩展字段5'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldSixName',
        '生产订单物料扩展字段6'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldSevenName',
        '生产订单物料扩展字段7'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldEightName',
        '生产订单物料扩展字段8'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldNineName',
        '生产订单物料扩展字段9'),
       ('productOrder.releasedApprove', 'materialField', 'productOrderMaterialExtendFieldTenName',
        '生产订单物料扩展字段10'),
       ('productOrder.releasedApprove', 'materialField', 'businessUnitCode', '业务单元编码'),
       ('productOrder.releasedApprove', 'materialField', 'businessUnitName', '业务单元名称');