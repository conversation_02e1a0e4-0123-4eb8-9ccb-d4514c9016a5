set autocommit = 0;


#DDL
#新增pad权限中间表
CREATE TABLE IF NOT EXISTS `sys_pad_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `pad_id` int NOT NULL COMMENT 'Pad id',
  `permission_id` int NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `pad_id` (`pad_id`) USING BTREE,
  KEY `permission_id` (`permission_id`) USING BTREE,
  CONSTRAINT `sys_pad_permission_ibfk_1` FOREIGN KEY (`pad_id`) REFERENCES `sys_pads` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='Pad-权限中间表';

#DML

commit
