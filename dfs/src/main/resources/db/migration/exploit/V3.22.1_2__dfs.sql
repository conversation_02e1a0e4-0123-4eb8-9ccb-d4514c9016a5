-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================21.2

-- 供应商档案新增`简称`字段
call proc_add_column(
        'dfs_supplier',
        'short_name',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `short_name` varchar(255) DEFAULT NULL COMMENT ''简称'' after `name`');
-- 支持字段配置
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.list', 'supplier.list', 'shortName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.detail', 'supplier.detail', 'shortName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byCreate', 'supplier.edit.byCreate', 'shortName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byRelease', 'supplier.edit.byRelease', 'shortName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/supply-chain-collaboration/procurement-management/supplier-profile', 'supplier.edit.byDisable', 'supplier.edit.byDisable', 'shortName', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

call proc_add_form_field_module('supplier.list', 'shortName', '简称', 'baseField');
call proc_add_form_field_module('supplier.detail', 'shortName', '简称', 'baseField');
call proc_add_form_field_module('supplier.edit.byCreate', 'shortName', '简称', 'baseField');
call proc_add_form_field_module('supplier.edit.byRelease', 'shortName', '简称', 'baseField');
call proc_add_form_field_module('supplier.edit.byDisable', 'shortName', '简称', 'baseField');
-- 支持apiV2字段查询
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('supplier', 'shortName', '简称', 'short_name');


-- 客户档案重构
-- 1、删除的字段：公司联系人、传真、公司网址、公司性质、客户地址、客户类别
call proc_modify_column(
        'dfs_customer',
        'company_contact_person',
        'ALTER TABLE `dfs_customer` DROP COLUMN `company_contact_person`');
call proc_modify_column(
        'dfs_customer',
        'email',
        'ALTER TABLE `dfs_customer` DROP COLUMN `email`');
call proc_modify_column(
        'dfs_customer',
        'company_website',
        'ALTER TABLE `dfs_customer` DROP COLUMN `company_website`');
call proc_modify_column(
        'dfs_customer',
        'company_property',
        'ALTER TABLE `dfs_customer` DROP COLUMN `company_property`');
call proc_modify_column(
        'dfs_customer',
        'level',
        'ALTER TABLE `dfs_customer` DROP COLUMN `level`');
call proc_modify_column(
        'dfs_customer',
        'customer_addr',
        'ALTER TABLE `dfs_customer` DROP COLUMN `customer_addr`');
call proc_modify_column(
        'dfs_customer',
        'customer_category',
        'ALTER TABLE `dfs_customer` DROP COLUMN `customer_category`');
-- 2、重命名字段：业务员（== 本厂对接人）、纳税人识别号（==税号）、注册地址（==公司地址）、电话（公司联系方式）、账号（==银行账号）、开户行（==银行账户）
call proc_modify_column(
        'dfs_customer',
        'cus_successor',
        'ALTER TABLE `dfs_customer` CHANGE COLUMN `cus_successor` `salesman` VARCHAR(255) COMMENT ''业务员''');
call proc_modify_column(
        'dfs_customer',
        'duty_paragraph',
        'ALTER TABLE `dfs_customer` CHANGE COLUMN `duty_paragraph` `duty_paragraph` VARCHAR(255) COMMENT ''纳税人识别号''');
call proc_modify_column(
        'dfs_customer',
        'company_address',
        'ALTER TABLE `dfs_customer` CHANGE COLUMN `company_address` `company_address` VARCHAR(255) COMMENT ''注册地址''');
call proc_modify_column(
        'dfs_customer',
        'company_number',
        'ALTER TABLE `dfs_customer` CHANGE COLUMN `company_number` `company_number` VARCHAR(255) COMMENT ''电话''');
call proc_modify_column(
        'dfs_customer',
        'bank_number',
        'ALTER TABLE `dfs_customer` CHANGE COLUMN `bank_number` `bank_number` VARCHAR(255) COMMENT ''账号''');
call proc_modify_column(
        'dfs_customer',
        'bank_account',
        'ALTER TABLE `dfs_customer` CHANGE COLUMN `bank_account` `bank_account` VARCHAR(255) COMMENT ''开户行''');

-- 2、新增字段：企业全称
call proc_add_column(
        'dfs_customer',
        'company_full_name',
        'ALTER TABLE `dfs_customer` ADD COLUMN `company_full_name` varchar(255) DEFAULT NULL COMMENT ''企业全称''');
-- 3、客户地址省市区改为非必填
call proc_modify_column(
        'dfs_customer_address',
        'province_id',
        'ALTER TABLE `dfs_customer_address` CHANGE COLUMN `province_id` `province_id` int(11) DEFAULT NULL COMMENT ''省份id''');
call proc_modify_column(
        'dfs_customer_address',
        'city_id',
        'ALTER TABLE `dfs_customer_address` CHANGE COLUMN `city_id` `city_id` int(11) DEFAULT NULL COMMENT ''城市id''');
call proc_modify_column(
        'dfs_customer_address',
        'area_id',
        'ALTER TABLE `dfs_customer_address` CHANGE COLUMN `area_id` `area_id` int(11) DEFAULT NULL COMMENT ''区域id''');
call proc_modify_column(
        'dfs_customer_address',
        'province_name',
        'ALTER TABLE `dfs_customer_address` CHANGE COLUMN `province_name` `province_name` varchar(255) DEFAULT NULL COMMENT ''省份名称''');
call proc_modify_column(
        'dfs_customer_address',
        'city_name',
        'ALTER TABLE `dfs_customer_address` CHANGE COLUMN `city_name` `city_name` varchar(255) DEFAULT NULL COMMENT ''城市名称''');
call proc_modify_column(
        'dfs_customer_address',
        'area_name',
        'ALTER TABLE `dfs_customer_address` CHANGE COLUMN `area_name` `area_name` varchar(255) DEFAULT NULL COMMENT ''区域名称''');



-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================21.2
