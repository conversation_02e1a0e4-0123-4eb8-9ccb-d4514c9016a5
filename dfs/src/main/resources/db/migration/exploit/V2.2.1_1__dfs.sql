set autocommit = 0;
-- DDL

-- 物料表新增 工厂型号 字段
call proc_add_column(
        'dfs_material',
        'factory_model',
        'ALTER TABLE `dfs_material` ADD COLUMN `factory_model`  varchar(255) NULL COMMENT ''工厂型号''');
-- 出入库单新增`补料原因`字段
call proc_add_column(
        'dfs_stock_in_and_out',
        'replenish_reason',
        'ALTER TABLE `dfs_stock_in_and_out` ADD COLUMN `replenish_reason` varchar(255) NULL COMMENT ''补料原因''');
-- 采购收货单关联表新增`是否已全部入库`字段
call proc_add_column(
        'dfs_purchase_receipt_material',
        'is_warehousing',
        'ALTER TABLE `dfs_purchase_receipt_material` ADD COLUMN `is_warehousing` varchar(255) DEFAULT false COMMENT ''是否已全部入库''');
-- 采购收货单关联表新增`入库数量`字段
call proc_add_column(
        'dfs_purchase_receipt_material',
        'warehousing_amount',
        'ALTER TABLE `dfs_purchase_receipt_material` ADD COLUMN `warehousing_amount`  double(11,4) DEFAULT 0.0 COMMENT ''入库数量'' after `stack_amount`');

-- 流水码表增加索引
call proc_add_column_index(
        'dfs_product_flow_code',
        'product_flow_code',
        'product_flow_code');
call proc_add_column_index(
        'dfs_product_flow_code',
        'seq',
        'seq');
call proc_add_column_index(
        'dfs_product_flow_code',
        'rule_code',
        'rule_code');

-- 新增采购退料申请单 dfs_purchase_return_application
CREATE TABLE IF NOT EXISTS `dfs_purchase_return_application`(
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `purchase_return_number` varchar(255)  DEFAULT NULL COMMENT '采购退料申请单号',
    `supplier_code` varchar(255)  DEFAULT NULL COMMENT '供应商编码',
    `supplier_name` varchar(255)  DEFAULT NULL COMMENT '供应商名称',
    `applicant` varchar(255) DEFAULT NULL COMMENT '申请人',
    `applicant_name` varchar(255) DEFAULT NULL COMMENT '申请人姓名',
    `applicant_time` datetime DEFAULT NULL COMMENT '申请时间',
    `state` int(11) DEFAULT '0' COMMENT '状态',
    `remark` varchar(255)  DEFAULT NULL COMMENT '备注',
    `approval_status` int DEFAULT NULL COMMENT '审批状态',
    `order_return_reason` varchar(255) DEFAULT NULL COMMENT '单据退料原因',
    `approver` varchar(255) DEFAULT NULL COMMENT '审批人',
    `approver_name` varchar(255) DEFAULT NULL COMMENT '审批人姓名',
    `approval_suggestion` varchar(255)  DEFAULT NULL COMMENT '审批建议',
    `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
    `creator_name` varchar(255) DEFAULT NULL COMMENT '创建人姓名',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `purchase_return_number` (`purchase_return_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT ='采购退料申请单';


-- 新增采购退料申请单——物料关联表 dfs_purchase_return_application
CREATE TABLE IF NOT EXISTS `dfs_purchase_return_application_material`(
    `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `purchase_return_number` varchar(255)  DEFAULT NULL COMMENT '采购退料申请单号',
    `material_code` varchar(255)  DEFAULT NULL COMMENT '物料编码',
    `material_id` int(11) DEFAULT NULL COMMENT '物料id',
    `return_count` double(20,2) DEFAULT '0.00' COMMENT '申请退料数量',
    `material_return_reason` varchar(255) DEFAULT NULL COMMENT '物料退料原因',
    PRIMARY KEY (`id`),
     KEY `purchase_return_number` (`purchase_return_number`) USING BTREE,
     KEY `material_code` (`material_code`) USING BTREE,
     KEY `material_id` (`material_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT ='采购退料申请-物料关联表';

-- 工艺加扩展字段
call proc_add_column('dfs_craft','extend','ALTER TABLE `dfs_craft` ADD COLUMN `extend` text NULL COMMENT ''扩展字段''');

-- 小程序与工位关联表字段换test
call proc_modify_column(
        'dfs_app_fac',
        'fac_ids',
        'ALTER TABLE `dfs_app_fac` MODIFY COLUMN `fac_ids` text COMMENT ''工位(小程序下的多个工位)''');

call proc_modify_column(
        'dfs_app_fac',
        'fac_model_ids',
        'ALTER TABLE `dfs_app_fac` MODIFY COLUMN `fac_model_ids` text COMMENT ''工位模型(小程序下的多个工位模型)''');

-- 供应商档案新增本厂对接人
call proc_add_column(
        'dfs_supplier',
        'counterpart',
        'ALTER TABLE `dfs_supplier` ADD COLUMN `counterpart` varchar(255) NULL COMMENT ''本厂对接人''');

-- 供应商档案类型 地址 联系方式 字段改为非必选
call proc_modify_column(
        'dfs_supplier',
        'type',
        'ALTER TABLE `dfs_supplier` MODIFY COLUMN `type` int(11) DEFAULT NULL COMMENT ''类型（合格供应商：1001，导入供应商：1002，临时供应商：1003，非合格供应商：1004）''');

call proc_modify_column(
        'dfs_supplier',
        'addr',
        'ALTER TABLE `dfs_supplier` MODIFY COLUMN `addr` varchar(255) DEFAULT NULL COMMENT ''地址''');

call proc_modify_column(
        'dfs_supplier',
        'phone',
        'ALTER TABLE `dfs_supplier` MODIFY COLUMN `phone` varchar(255) DEFAULT NULL COMMENT ''联系方式''');
-- DML

-- 物料可配置字段
INSERT INTO `dfs_material_config_field`(`field_code`, `field_name`, `field_type`, `is_using`, `is_not_null`, `is_change`, `default_value`, `create_time`, `update_time`, `create_by`, `update_by`)
VALUES ('factoryModel', '工厂型号', 'input', 0, 0, 0, NULL, '2022-10-19 11:11:11', NULL, 'admin', NULL);

-- 仓储配送菜单三级变二级
UPDATE `sys_permissions` SET `name` = '仓储配送', `path` = '/warehousingDistribution', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-20 03:30:03', `status` = 'enable', `method` = 'GET', `parent_id` = '0', `type` = 0, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '10903';
UPDATE `sys_permissions` SET `name` = '物料台账', `path` = '/warehousingDistribution/record-query/inbound-record', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:45:43', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090301';
UPDATE `sys_permissions` SET `name` = '库存初始化', `path` = 'inbound.record:initialize', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:42:31', `status` = 'enable', `method` = 'POST', `parent_id` = '1090301', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/inbound-record', `is_enable` = 1 WHERE `id` = '1090301010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'inbound.record:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:42:31', `status` = 'enable', `method` = 'PUT', `parent_id` = '1090301', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/inbound-record', `is_enable` = 1 WHERE `id` = '1090301020';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'inbound.record:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:42:31', `status` = 'enable', `method` = 'GET', `parent_id` = '1090301', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/inbound-record', `is_enable` = 1 WHERE `id` = '1090301030';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'inbound.record:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:42:31', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090301', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/inbound-record', `is_enable` = 1 WHERE `id` = '1090301040';
UPDATE `sys_permissions` SET `name` = '物料批次', `path` = '/warehousingDistribution/batch', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:50:43', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090302';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'batch:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:09', `status` = 'enable', `method` = 'GET', `parent_id` = '1090302', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/batch', `is_enable` = 1 WHERE `id` = '1090302010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'batch:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:09', `status` = 'enable', `method` = 'GET', `parent_id` = '1090302', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/batch', `is_enable` = 1 WHERE `id` = '1090302020';
UPDATE `sys_permissions` SET `name` = '出库单', `path` = '/warehousingDistribution/record-query/factory-record', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:45:50', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090303';
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'factory.record:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:31', `status` = 'enable', `method` = 'POST', `parent_id` = '1090303', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/factory-record', `is_enable` = 1 WHERE `id` = '1090303010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'factory.record:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:31', `status` = 'enable', `method` = 'PUT', `parent_id` = '1090303', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/factory-record', `is_enable` = 1 WHERE `id` = '1090303020';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'factory.record:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:31', `status` = 'enable', `method` = 'GET', `parent_id` = '1090303', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/factory-record', `is_enable` = 1 WHERE `id` = '1090303030';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'factory.record:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:31', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090303', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/factory-record', `is_enable` = 1 WHERE `id` = '1090303040';
UPDATE `sys_permissions` SET `name` = '审批', `path` = 'factory.record:approval', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:31', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090303', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/factory-record', `is_enable` = 1 WHERE `id` = '1090303050';
UPDATE `sys_permissions` SET `name` = '入库单', `path` = '/warehousingDistribution/record-query/access-record', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:45:56', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090304';
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'access.record:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:51', `status` = 'enable', `method` = 'POST', `parent_id` = '1090304', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/access-record', `is_enable` = 1 WHERE `id` = '1090304010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'access.record:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:51', `status` = 'enable', `method` = 'PUT', `parent_id` = '1090304', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/access-record', `is_enable` = 1 WHERE `id` = '1090304020';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'access.record:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:51', `status` = 'enable', `method` = 'GET', `parent_id` = '1090304', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/access-record', `is_enable` = 1 WHERE `id` = '1090304030';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'access.record:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:51', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090304', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/access-record', `is_enable` = 1 WHERE `id` = '1090304040';
UPDATE `sys_permissions` SET `name` = '审批', `path` = 'access.record:approval', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:51:51', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090304', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/access-record', `is_enable` = 1 WHERE `id` = '1090304050';
UPDATE `sys_permissions` SET `name` = '调拨单', `path` = '/warehousingDistribution/record-query/material-allocation', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:46:02', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090305';
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'material.allocation:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:52:14', `status` = 'enable', `method` = 'POST', `parent_id` = '1090305', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/material-allocation', `is_enable` = 1 WHERE `id` = '1090305010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'material.allocation:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:52:14', `status` = 'enable', `method` = 'PUT', `parent_id` = '1090305', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/material-allocation', `is_enable` = 1 WHERE `id` = '1090305020';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'material.allocation:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:52:14', `status` = 'enable', `method` = 'GET', `parent_id` = '1090305', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/material-allocation', `is_enable` = 1 WHERE `id` = '1090305030';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'material.allocation:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:52:14', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090305', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/material-allocation', `is_enable` = 1 WHERE `id` = '1090305040';
UPDATE `sys_permissions` SET `name` = '审批', `path` = 'material.allocation:approval', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:52:14', `status` = 'enable', `method` = 'GET', `parent_id` = '1090305', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/material-allocation', `is_enable` = 1 WHERE `id` = '1090305050';
UPDATE `sys_permissions` SET `name` = '库存盘点', `path` = '/warehousingDistribution/record-query/inventory', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:46:07', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090306';
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'inventory:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:52:48', `status` = 'enable', `method` = 'POST', `parent_id` = '1090306', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/inventory', `is_enable` = 1 WHERE `id` = '1090306010';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'inventory:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:52:48', `status` = 'enable', `method` = 'GET', `parent_id` = '1090306', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/inventory', `is_enable` = 1 WHERE `id` = '1090306020';
UPDATE `sys_permissions` SET `name` = '仓库定义', `path` = '/warehousingDistribution/record-query/warehouse-manage', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:46:12', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090307';
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'warehouse.manage:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:07', `status` = 'enable', `method` = 'POST', `parent_id` = '1090307', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/warehouse-manage', `is_enable` = 1 WHERE `id` = '1090307010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'warehouse.manage:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:07', `status` = 'enable', `method` = 'PUT', `parent_id` = '1090307', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/warehouse-manage', `is_enable` = 1 WHERE `id` = '1090307020';
UPDATE `sys_permissions` SET `name` = '审批', `path` = 'warehouse.manage:approval', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:07', `status` = 'enable', `method` = 'GET', `parent_id` = '1090307', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/warehouse-manage', `is_enable` = 1 WHERE `id` = '1090307030';
UPDATE `sys_permissions` SET `name` = '库位定义', `path` = '/warehousingDistribution/record-query/location-define', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:46:17', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090308';
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'location.define:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:29', `status` = 'enable', `method` = 'POST', `parent_id` = '1090308', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/location-define', `is_enable` = 1 WHERE `id` = '1090308010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'location.define:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:29', `status` = 'enable', `method` = 'PUT', `parent_id` = '1090308', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/location-define', `is_enable` = 1 WHERE `id` = '1090308020';
UPDATE `sys_permissions` SET `name` = '详情', `path` = 'location.define:select', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:29', `status` = 'enable', `method` = 'GET', `parent_id` = '1090308', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/location-define', `is_enable` = 1 WHERE `id` = '1090308030';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'location.define:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:29', `status` = 'enable', `method` = 'DELETE', `parent_id` = '1090308', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/location-define', `is_enable` = 1 WHERE `id` = '1090308040';
UPDATE `sys_permissions` SET `name` = '导入', `path` = 'location.define:import', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:29', `status` = 'enable', `method` = 'GET', `parent_id` = '1090308', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/location-define', `is_enable` = 1 WHERE `id` = '1090308050';
UPDATE `sys_permissions` SET `name` = '导出', `path` = 'location.define:export', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:29', `status` = 'enable', `method` = 'GET', `parent_id` = '1090308', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/location-define', `is_enable` = 1 WHERE `id` = '1090308060';
UPDATE `sys_permissions` SET `name` = '审批', `path` = 'location.define:approval', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:53:29', `status` = 'enable', `method` = 'GET', `parent_id` = '1090308', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/location-define', `is_enable` = 1 WHERE `id` = '1090308070';
UPDATE `sys_permissions` SET `name` = '仓管组管理', `path` = '/warehousingDistribution/record-query/team', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 01:46:22', `status` = 'enable', `method` = 'GET', `parent_id` = '10903', `type` = 1, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090309';
UPDATE `sys_permissions` SET `name` = '新增', `path` = 'team:add', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 02:03:22', `status` = 'enable', `method` = 'GET', `parent_id` = '1090309', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/team', `is_enable` = 1 WHERE `id` = '1090309010';
UPDATE `sys_permissions` SET `name` = '编辑', `path` = 'team:update', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 02:03:22', `status` = 'enable', `method` = 'GET', `parent_id` = '1090309', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/team', `is_enable` = 1 WHERE `id` = '1090309020';
UPDATE `sys_permissions` SET `name` = '成员维护', `path` = 'team:member', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 02:03:22', `status` = 'enable', `method` = 'GET', `parent_id` = '1090309', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/team', `is_enable` = 1 WHERE `id` = '1090309030';
UPDATE `sys_permissions` SET `name` = '删除', `path` = 'team:delete', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-24 02:03:22', `status` = 'enable', `method` = 'GET', `parent_id` = '1090309', `type` = 2, `is_web` = 1, `is_back_stage` = 0, `parent_path` = '/warehousingDistribution/record-query/team', `is_enable` = 1 WHERE `id` = '1090309040';


-- 新增采购退料申请单菜单权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208', '采购退料申请单', '/supply-chain-collaboration/procurement-management/purchase-material-return-application', NULL, NULL, NULL, NULL, '2022-10-20 07:38:31', 'enable', 'GET', '10902', 3, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208010', '新增', 'return:application:add', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'POST', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208020', '编辑', 'return:application:update', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'PUT', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208030', '删除', 'return:application:delete', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'DELETE', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208040', '详情', 'return:application:select', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208050', '审批', 'return:application:approval', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090208060', '下推', 'return:application:pushDown', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '1090208', 2, 1, 0, '/supply-chain-collaboration/procurement-management/purchase-material-return-application', 1);

-- 新增生产订单用料清单权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006', '生产订单用料清单', '/order-model/production-materials', NULL, NULL, NULL, NULL, '2022-10-12 07:12:40', 'enable', 'GET', '100', 1, 1, 0, '/order-model', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006010', '审批', 'production.materials:approval', NULL, NULL, NULL, NULL, '2022-10-12 07:12:40', 'enable', 'GET', '10006', 2, 1, 0, '/order-model/production-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006020', '新增', 'production.materials:add', NULL, NULL, NULL, NULL, '2022-10-12 07:12:40', 'enable', 'POST', '10006', 2, 1, 0, '/order-model/production-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006030', '编辑', 'production.materials:update', NULL, NULL, NULL, NULL, '2022-10-12 07:12:40', 'enable', 'PUT', '10006', 2, 1, 0, '/order-model/production-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006040', '详情', 'production.materials:select', NULL, NULL, NULL, NULL, '2022-10-12 07:12:40', 'enable', 'GET', '10006', 2, 1, 0, '/order-model/production-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006050', '删除', 'production.materials:delete', NULL, NULL, NULL, NULL, '2022-10-12 07:12:40', 'enable', 'DELETE', '10006', 2, 1, 0, '/order-model/production-materials', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10006060', '下推', 'production.materials:pushDown', NULL, NULL, NULL, NULL, '2022-10-12 07:12:40', 'enable', 'POST', '10006', 2, 1, 0, '/order-model/production-materials', 1);
-- 新增出入库二三级菜单
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090311', '生产出入库', '/warehousingDistribution/productInOrOut', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '10903', 1, 1, 0, NULL, 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101', '生产入库单', '/warehousingDistribution/productInOrOut/workOrderComplete', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '1090311', 3, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101010', '新增', 'workOrderComplete:add', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101020', '编辑', 'workOrderComplete:update', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101030', '删除', 'workOrderComplete:delete', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101040', '详情', 'workOrderComplete:select', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101050', '报表导出', 'workOrderComplete:export', NULL, NULL, NULL, NULL, '2022-10-20 03:48:27', 'enable', 'GET', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031101060', '审批', 'workOrderComplete:approval', NULL, NULL, NULL, NULL, '2022-10-20 03:49:58', 'enable', 'GET', '109031101', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderComplete', 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102', '生产领料出库单', '/warehousingDistribution/productInOrOut/workOrderTakeOut', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '1090311', 3, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102010', '新增', 'workOrderTakeOut:add', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102020', '编辑', 'workOrderTakeOut:update', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102030', '删除', 'workOrderTakeOut:delete', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102040', '详情', 'workOrderTakeOut:select', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102050', '报表导出', 'workOrderTakeOut:export', NULL, NULL, NULL, NULL, '2022-10-20 03:48:27', 'enable', 'GET', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031102060', '审批', 'workOrderTakeOut:approval', NULL, NULL, NULL, NULL, '2022-10-20 03:49:58', 'enable', 'GET', '109031102', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderTakeOut', 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103', '生产补料出库单', '/warehousingDistribution/productInOrOut/workOrderSupplement', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '1090311', 3, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103010', '新增', 'workOrderSupplement:add', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103020', '编辑', 'workOrderSupplement:update', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103030', '删除', 'workOrderSupplement:delete', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103040', '详情', 'workOrderSupplement:select', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103050', '报表导出', 'workOrderSupplement:export', NULL, NULL, NULL, NULL, '2022-10-20 03:48:27', 'enable', 'GET', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031103060', '审批', 'workOrderSupplement:approval', NULL, NULL, NULL, NULL, '2022-10-20 03:49:58', 'enable', 'GET', '109031103', 2, 1, 0, '/warehousingDistribution/productInOrOut/workOrderSupplement', 1);

INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104', '生产退料入库单', '/warehousingDistribution/productInOrOut/applicationReturn', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '1090311', 3, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104010', '新增', 'applicationReturn:add', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104020', '编辑', 'applicationReturn:update', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104030', '删除', 'applicationReturn:delete', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104040', '详情', 'applicationReturn:select', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104050', '报表导出', 'applicationReturn:export', NULL, NULL, NULL, NULL, '2022-10-20 03:48:27', 'enable', 'GET', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031104060', '审批', 'applicationReturn:approval', NULL, NULL, NULL, NULL, '2022-10-20 03:49:58', 'enable', 'GET', '109031104', 2, 1, 0, '/warehousingDistribution/productInOrOut/applicationReturn', 1);


-- 编码规则枚举
truncate table dfs_config_rule;
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (1, '销售订单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (16, '出货申请-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (17, '销售发货-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (10, '生产订单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (34, '生产订单-生产订单用料清单');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (2, '生产工单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (5, '生产工单-生产批次号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (12, '生产工单-生产流水码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (14, '生产工单-生产成品码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (18, '领料申请-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (9, '作业工单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (19, '采购需求-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (20, '采购订单-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (21, '采购订单-采购批次号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (22, '采购订单-采购单品码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (23, '采购收货-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (11, '采购收货-采购批次号');

-- 新增采购出入库菜单权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090310', '采购出入库', '/warehousingDistribution/purchaseOutOfStorage', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '10903', 1, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001', '采购入库单', '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '1090310', 3, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001010', '新增', 'purchasePutInStorage:add', NULL, NULL, NULL, NULL, '2022-10-20 03:40:30', 'enable', 'GET', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001020', '编辑', 'purchasePutInStorage:update', NULL, NULL, NULL, NULL, '2022-10-20 03:40:30', 'enable', 'GET', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001030', '删除', 'purchasePutInStorage:delete', NULL, NULL, NULL, NULL, '2022-10-20 03:40:31', 'enable', 'GET', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001040', '详情', 'purchasePutInStorage:select', NULL, NULL, NULL, NULL, '2022-10-20 03:40:31', 'enable', 'GET', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001050', '报表导出', 'purchasePutInStorage:export', NULL, NULL, NULL, NULL, '2022-10-20 03:47:55', 'enable', 'GET', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031001060', '批量审批', 'purchasePutInStorage:approval', NULL, NULL, NULL, NULL, '2022-10-20 03:49:52', 'enable', 'GET', '109031001', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchasePutInStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002', '采购退料出库单', '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', NULL, NULL, NULL, NULL, '2022-10-20 03:39:51', 'enable', 'GET', '1090310', 3, 1, 0, NULL, 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002010', '新增', 'purchaseReturnMaterialOutOfStorage:add', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002020', '编辑', 'purchaseReturnMaterialOutOfStorage:update', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002030', '删除', 'purchaseReturnMaterialOutOfStorage:delete', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002040', '详情', 'purchaseReturnMaterialOutOfStorage:select', NULL, NULL, NULL, NULL, '2022-10-20 03:40:49', 'enable', 'GET', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002050', '报表导出', 'purchaseReturnMaterialOutOfStorage:export', NULL, NULL, NULL, NULL, '2022-10-20 03:48:27', 'enable', 'GET', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('109031002060', '批量审批', 'purchaseReturnMaterialOutOfStorage:approval', NULL, NULL, NULL, NULL, '2022-10-20 03:49:58', 'enable', 'GET', '109031002', 2, 1, 0, '/warehousingDistribution/purchaseOutOfStorage/purchaseReturnMaterialOutOfStorage', 1);

-- 新增采购退料申请单号编码配置
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (33, '采购退料-申请单号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (24, '来料检验-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (25, '来料检验-采购批次号(拆批)');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (4, 'BOM-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (26, '工艺-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (27, '入库记录-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (35, '入库记录-采购入库');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (36, '入库记录-生产入库');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (37, '入库记录-生产退料');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (6, '出库记录-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (38, '出库记录-采购退料出库');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (39, '出库记录-生产领料出库');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (40, '出库记录-生产补料出库');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (28, '仓库调拨-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (29, '仓库盘点-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (13, '质检报工-质检号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (15, '产品检测-送检单号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (30, '产品检测-质检单号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (31, '产品检测-报告单号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (32, '生产任务-单据编号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (3, '供应商档案-供应商编码');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (7, '炉号信息-电炉炉次号');
INSERT INTO `dfs_config_rule`(`rule_type`, `rule_type_name`) VALUES (8, '炉号信息-精炼炉炉次号');

-- 新增默认的编码规则数据，组成方式为人工输入
call init_number_rules_config();

-- 新增审批配置
INSERT INTO `dfs_approve_config`(`id`, `code`, `name`, `is_approve`, `update_by`, `update_time`) VALUES
(null, 'productOrderMaterialList', '生产订单用料清单', 0, 'admin', '2022-04-25 17:43:05'),
(null, 'purchaseReturnRequisition', '采购退料申请单', 0, 'admin', '2022-04-25 17:43:08'),
(null, 'productReceiptDoc', '生产入库单', 0, 'admin', '2022-04-25 17:43:08'),
(null, 'purchaseReceiptDoc', '采购入库单', 0, 'admin', '2022-04-25 17:43:08'),
(null, 'productMaterialReturnReceipt', '生产退料入库单', 0, 'admin', '2022-04-25 17:43:08'),
(null, 'pickingIssue', '生产领料出库', 0, 'admin', '2022-04-25 17:43:08'),
(null, 'productReplenishDelivery', '生产补料出库', 0, 'admin', '2022-04-25 17:43:08'),
(null, 'purchaseReturnIssueDoc', '采购退料出库单', 0, 'admin', '2022-04-25 17:43:08');

-- 删除冗余审批权限
DELETE FROM sys_permissions WHERE id = '1090308080';

-- 新增工艺工序导入权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`)
VALUES
('10803210', '工艺工序模板下载', 'technology:procedureTemplate', NULL, NULL, NULL, NULL, '2022-10-24 07:34:21', 'enable', 'GET', '10803', 2, 1, 0, 'technology:procedureImport', 1),
('10803220', '工艺工序数据导入', 'technology:procedureImport', NULL, NULL, NULL, NULL, '2022-10-24 07:34:36', 'enable', 'GET', '10803', 2, 1, 0, '/product-management/technology', 1),
('10803230', '工艺工序日志导出', 'technology:procedureLog', NULL, NULL, NULL, NULL, '2022-10-24 07:34:32', 'enable', 'GET', '10803', 2, 1, 0, 'technology:procedureImport', 1);
-- 角色增加权限
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10803210' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10803220' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10803230' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10803210');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10803220');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10803230');

-- 新增工位质检记录/报表导入权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402030', '下载默认模板', 'processQuality.record:exportDefault', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402040', '上传记录模板', 'processQuality.record:uploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'POST', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402050', '导出记录数据', 'processQuality.record:exportData', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402060', '下载默认模板', 'processQuality.statement:exportDefault', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402070', '上传报表模板', 'processQuality.statement:uploadTemplate', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10402080', '导出报表数据', 'processQuality.statement:exportData', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '10402', 2, 1, 0, '/qualities-manage/processQuality', 1);
-- 角色增加权限
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10402030' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10402040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10402050' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10402060' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10402070' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10402080' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10402030');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10402040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10402050');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10402060');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10402070');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10402080');

-- 角色增加权限
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10006' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10006010' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10006020' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10006030' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10006040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10006050' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'10006060' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10006');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10006010');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10006020');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10006030');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10006040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '10006050');

INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090311' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031101' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031101010' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031101020' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031101030' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031101040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031101050' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031101060' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031102' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031102010' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031102020' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031102030' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031102040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031102050' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031102060' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031103' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031103010' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031103020' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031103030' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031103040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031103050' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031103060' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031104' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031104010' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031104020' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031104030' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031104040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031104050' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'109031104060' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090311');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031101');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031101010');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031101020');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031101030');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031101040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031101050');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031101060');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031102');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031102010');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031102020');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031102030');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031102040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031102050');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031102060');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031103');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031103010');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031103020');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031103030');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031103040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031103050');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031103060');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031104');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031104010');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031104020');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031104030');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031104040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031104050');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '109031104060');




-- 字典表新增库房类型定义
INSERT INTO `dfs_dict`( `code`, `name`, `type`, `url`, `unit`, `des`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('rawMaterialWarehouse', '原材料库房', 'warehouseType', NULL, NULL, NULL, '2022-10-24 15:36:16', '2022-10-24 07:36:19', 'admin', 'admin');
INSERT INTO `dfs_dict`( `code`, `name`, `type`, `url`, `unit`, `des`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('semiFinishedProductWarehouse', '半成品库房', 'warehouseType', NULL, NULL, NULL, '2022-10-24 15:36:16', '2022-10-24 07:37:37', 'admin', 'admin');
INSERT INTO `dfs_dict`( `code`, `name`, `type`, `url`, `unit`, `des`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('finishedProductWarehouse', '成品库房', 'warehouseType', NULL, NULL, NULL, '2022-10-24 15:36:16', '2022-10-24 07:37:44', 'admin', 'admin');
INSERT INTO `dfs_dict`( `code`, `name`, `type`, `url`, `unit`, `des`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES ('other', '其他', 'warehouseType', NULL, NULL, NULL, '2022-10-24 15:36:16', '2022-10-24 07:37:44', 'admin', 'admin');

-- 新增库房定义web端按钮权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090307040', '库房类型定义', 'warehouse.manage:define', NULL, NULL, NULL, NULL, '2022-10-24 07:46:15', 'enable', 'GET', '1090307', 2, 1, 0, '/warehousingDistribution/record-query/warehouse-manage', 1);
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090307040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090208');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090208010');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090208020');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090208030');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090208040');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090208050');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` ) VALUES (1, '1090208060');
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090307040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090208' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090208010' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090208020' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090208030' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090208040' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090208050' FROM sys_roles WHERE role_code = 'oncall';
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT id ,'1090208060' FROM sys_roles WHERE role_code = 'oncall';

-- 新增单品码绑工单工位机
INSERT INTO `sys_app_permission_info`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('yelink.kangjuren.singleProductCode-bindingWorkOrder.machine', '单品码绑工单工位机', '/single-productCode-Binding-workOrder-machine', 'locationMachine', NULL, NULL, NULL, NULL, 'enable', 'GET', '0', 0, 0, 1, NULL, 1);

-- 用户导入管理权限码
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('12102040', '导入管理', 'user:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'GET', '12102', 2, 1, 1, '/base-info/user', 1);

-- 工位信息导入管理权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('11306050', '导入管理', 'station:import', NULL, NULL, NULL, NULL, NULL, 'enable', 'DELETE', '11306', 2, 1, 1, '/factory-model/station', 1);

-- 新增质检返工记录导出菜单权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405010', '下载默认模板', 'return:record:downTemplate', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405020', '上传自定义模板', 'return:record:upload', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'POST', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405030', '下载自定义模板', 'return:record:down', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405040', '导出报表', 'return:record:export', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);

-- 新增质检返工报表导出菜单权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405050', '下载默认模板', 'return:statement:downTemplate', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405060', '上传自定义模板', 'return:statement:upload', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'POST', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405070', '下载自定义模板', 'return:statement:down', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('10405080', '导出报表', 'return:statement:export', NULL, NULL, NULL, NULL, '2022-10-20 07:38:42', 'enable', 'GET', '10405', 2, 1, 0, '/qualities-manage/reworkQuality', 1);

-- 物料可配置字段表备注换为物料备注
update dfs_material_config_field set field_name= '物料备注' where field_code='remark';

-- 物料类型可配置字段配置表备注换为物料备注
update dfs_material_type_config_field set field_name= '物料备注' where field_code='remark';

-- 仓储配送 新增二级权限 基础管理
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`) VALUES ('1090312', '基础管理', '/warehousingDistribution/baseManagement', NULL, NULL, NULL, NULL, '2022-10-27 07:41:47', 'enable', 'GET', '10903', 1, 1, 0, NULL, 1);
-- 调整仓库定义 库位定义 仓管组管理权限 为基础管理三级权限
UPDATE `sys_permissions` SET `name` = '仓库定义', `path` = '/warehousingDistribution/baseManagement/warehouse-manage', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-27 08:08:59', `status` = 'enable', `method` = 'GET', `parent_id` = '1090312', `type` = 3, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090307';
UPDATE `sys_permissions` SET `name` = '库位定义', `path` = '/warehousingDistribution/baseManagement/location-define', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-27 08:09:03', `status` = 'enable', `method` = 'GET', `parent_id` = '1090312', `type` = 3, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090308';
UPDATE `sys_permissions` SET `name` = '仓管组管理', `path` = '/warehousingDistribution/baseManagement/team', `description` = NULL, `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-10-27 08:09:07', `status` = 'enable', `method` = 'GET', `parent_id` = '1090312', `type` = 3, `is_web` = 1, `is_back_stage` = 0, `parent_path` = NULL, `is_enable` = 1 WHERE `id` = '1090309';

-- 兼容历史数据
INSERT INTO `sys_role_permission` ( `role_id`, `permission_id` )  SELECT role_id, '1090312' FROM sys_role_permission WHERE permission_id in('1090307','1090308','1090309') GROUP BY role_id;

-- 供应商档案 供应商联系人兼容历史数据
UPDATE dfs_supplier,sys_users
SET dfs_supplier.contacts = sys_users.nick_name
WHERE dfs_supplier.contacts = sys_users.user_name;



commit;
