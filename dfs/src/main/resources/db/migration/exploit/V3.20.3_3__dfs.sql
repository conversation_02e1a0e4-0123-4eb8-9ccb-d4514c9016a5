--
DROP PROCEDURE if EXISTS add_button_permission;

DELIMITER $$
CREATE PROCEDURE `add_button_permission` (
    IN in_path VARCHAR(255),           -- 按钮路径
    IN in_name VARCHAR(255),           -- 按钮名称
    IN in_id VARCHAR(100),             -- 按钮 ID（可选）
    IN in_parent_path VARCHAR(255)     -- 上级菜单路径
)
BEGIN
    proc_end: BEGIN
        DECLARE final_id VARCHAR(100);
        DECLARE path_count INT DEFAULT 0;
        DECLARE id_count INT DEFAULT 0;

        DECLARE parent_id_val VARCHAR(100);
        DECLARE parent_is_web INT DEFAULT NULL;
        DECLARE parent_is_back_stage INT DEFAULT NULL;
        DECLARE parent_service_name VARCHAR(255) DEFAULT NULL;

        -- 1. 设置最终 ID（如果未传则使用 path）
        SET final_id = IFNULL(NULLIF(TRIM(in_id), ''), in_path);

        -- 2. 校验 path 是否存在
SELECT COUNT(*) INTO path_count
FROM sys_permissions
WHERE path = in_path;

IF path_count > 0 THEN
SELECT 'Path already exists, skipping insert.' AS message;
LEAVE proc_end;
END IF;

        -- 3. 校验 id 是否存在
SELECT COUNT(*) INTO id_count
FROM sys_permissions
WHERE id = final_id;

IF id_count > 0 THEN
SELECT 'ID already exists, skipping insert.' AS message;
LEAVE proc_end;
END IF;

        -- 4. 获取上级菜单信息（含 service_name）
SELECT id, is_web, is_back_stage, service_name
INTO parent_id_val, parent_is_web, parent_is_back_stage, parent_service_name
FROM sys_permissions
WHERE path = in_parent_path
    LIMIT 1;

IF parent_id_val IS NULL THEN
SELECT 'Parent path not found, cannot insert button.' AS message;
LEAVE proc_end;
END IF;

        -- 5. 插入按钮权限（type = 2）
INSERT INTO sys_permissions (
    id, name, path, parent_id, type, is_web, is_back_stage, parent_path, service_name
) VALUES (
    final_id, in_name, in_path, parent_id_val, 2, parent_is_web, parent_is_back_stage, in_parent_path, parent_service_name
);
call init_new_role_permission(final_id);
SELECT 'Insert success.' AS message;

END proc_end;
END $$

DELIMITER ;


call add_button_permission(
	'production.records:audit',
	'审核',
	'',
	'/workorder-model/production-records'
);
call add_button_permission(
	'production.records:audit-batch',
	'批量审核',
	'',
	'/workorder-model/production-records'
);
call add_button_permission(
	'production.records:audit-log',
	'日志',
	'',
	'/workorder-model/production-records'
);
