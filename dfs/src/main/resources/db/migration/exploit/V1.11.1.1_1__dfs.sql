set autocommit = 0;

-- 新增附件表
CREATE TABLE IF NOT EXISTS `dfs_appendix`
(
    `id`          int NOT NULL AUTO_INCREMENT,
    `file_name`   varchar(255) DEFAULT NULL COMMENT '附件名称',
    `file_size`   varchar(255) DEFAULT NULL COMMENT '附件大小',
    `file_path`   varchar(255) DEFAULT NULL COMMENT '附件地址',
    `create_user` varchar(255) DEFAULT NULL COMMENT '上传人',
    `create_time` datetime     DEFAULT NULL COMMENT '上传时间',
    `update_user` varchar(255) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime     DEFAULT NULL COMMENT '修改时间',
    `type`        varchar(255) DEFAULT NULL COMMENT '附件类型',
    `relate_id`   varchar(255) DEFAULT NULL COMMENT '关联ID',
    `is_used`     tinyint      DEFAULT NULL COMMENT '是否被使用0：没有被使用',
    `relate_name` varchar(255) DEFAULT NULL COMMENT '关联名称',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='附件表';

commit