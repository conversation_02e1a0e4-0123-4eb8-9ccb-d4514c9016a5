set autocommit = 0;
#DDL

CREATE TABLE `dfs_record_device_input_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `device_id` int(11) NOT NULL COMMENT '设备id',
  `input_rate` double(11,4) NOT NULL COMMENT '设备投入率',
  `batch` varchar(50) DEFAULT NULL COMMENT '批次号',
  `time` datetime NOT NULL COMMENT '记录时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `time` (`time`) USING BTREE,
  KEY `batch` (`batch`) USING BTREE,
  KEY `device_id` (`device_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='设备投入率表';

#DML
commit
