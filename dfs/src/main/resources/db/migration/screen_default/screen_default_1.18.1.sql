set autocommit = 0;

-- 需求原文:DFS随版本提供缺省的中央大屏页面（含管理驾驶舱，中央大屏，生产大屏，制造运营中心，设备大屏），产线组大屏页面，产线大屏页面，并提供机加行业大屏模板

-- 设备大屏
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
( '设备大屏', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'CENTER_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'CENTER_SCREEN', '{\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '设备详情-1', 'DeviceContent-1', '960', '1920', 672, 1344, 0, 0, 0, '{}', @LASTID, NOW(), NOW());


-- 生产大屏
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('生产大屏', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'CENTER_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'CENTER_SCREEN', '{\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '状态总览-1', 'OverviewState-1', '153', '1410', 108, 987, 0, 25, 0, '{}', @LASTID, NOW(), NOW()),
('', '生产计划与进度-1', 'MainContentAll-1', '683.1428571428572', '1410', 479, 987, 127, 25, 0, '{}', @LASTID, NOW(), NOW()),
('', '产量统计-1', 'LineCharts-1', '265', '400', 186, 280, 0, 1041, 0, '{\"grid_ids\":[{\"id\":\"all\",\"name\":\"all\"}]}', @LASTID, NOW(), NOW()),
('', 'OEE&良率-1', 'ProductionOee-1', '265', '400', 186, 280, 208, 1041, 0, '{}', @LASTID, NOW(), NOW()),
('', '实时节拍信息-1', 'Beat-1', '265', '400', 186, 280, 420, 1041, 0, '{}', @LASTID, NOW(), NOW());


-- 制造运营中心
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
( '制造运营中心', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'CENTER_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'CENTER_SCREEN', '{\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '工艺路线-1', 'ProcessMain-1', '762', '1420', 534, 994, 0, 150, 0, '{}', @LASTID, NOW(), NOW()),
('', '告警汇总-1', 'ProcessAlarmSummary-1', '388', '340', 272, 238, 0, 1083, 0, '{}', @LASTID, NOW(), NOW()),
('', '设备当前状态-1', 'StateList-1', '388', '340', 272, 238, 306, 1083, 0, '{}', @LASTID, NOW(), NOW()),
('', '产量统计2-1', 'RightChart-1', '280', '340', 196, 238, 0, 23, 0, '{}', @LASTID, NOW(), NOW()),
('', '工厂OEE&良率-1', 'ProcessFactoryRate-1', '280', '340', 196, 238, 221, 23, 0, '{}', @LASTID, NOW(), NOW()),
('', '计划完成率-1', 'RightAlarmChart-1', '280', '340', 196, 238, 449, 23, 0, '{}', @LASTID, NOW(), NOW());


-- 中央大屏
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('中央大屏', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'CENTER_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'CENTER_SCREEN', '{\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '产量统计1-1', 'CentralOrderOutput-1', '381', '465', 267, 326, 0, 12, 0, '{}', @LASTID, NOW(), NOW()),
('', '本月产成品产出对比图-1', 'CentralProductionOutput-1', '286', '465', 201, 326, 282, 12, 0, '{\"grid_ids\":[{\"id\":\"all\",\"name\":\"all\"}]}', @LASTID, NOW(), NOW()),
('', '本月产线故障时长对比图-1', 'CentralLineTime-1', '286', '465', 201, 326, 494, 12, 0, '{\"grid_ids\":[{\"id\":\"all\",\"name\":\"all\"}]}', @LASTID, NOW(), NOW()),
('', '统计数1-1', 'CentralTotalShow-1', '204', '924', 143, 647, 0, 354, 0, '{}', @LASTID, NOW(), NOW()),
('', '今日总装车间产线产出对比图-1', 'CentralGridOutput-1', '428.28571428571433', '924', 300, 647, 166, 354, 0, '{\"grid_ids\":[{\"id\":\"all\",\"name\":\"all\"}]}', @LASTID, NOW(), NOW()),
('', '统计数2-1', 'CentralOrderList-1', '286', '924', 201, 647, 482, 354, 0, '{\"grid_ids\":[{\"id\":\"all\",\"name\":\"all\"}]}', @LASTID, NOW(), NOW()),
('', '今日不良品项分布图top5-1', 'CentralBadProduction-1', '381', '465', 267, 326, 0, 1015, 0, '{}', @LASTID, NOW(), NOW()),
('', '本月总装车间产出对比图-1', 'CentralAssemblyGrid-1', '286', '465', 201, 326, 281, 1015, 0, '{\"grid_ids\":[{\"id\":\"all\",\"name\":\"all\"}]}', @LASTID, NOW(), NOW()),
('', '本月产线故障次数占比图-1', 'CentralFaultCharts-1', '286', '465', 201, 326, 493, 1015, 0, '{\"grid_ids\":[{\"id\":\"all\",\"name\":\"all\"}]}', @LASTID, NOW(), NOW());


-- 管理驾驶舱
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('管理驾驶舱', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'CENTER_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'CENTER_SCREEN', '{\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '订单统计-1', 'OrderStatistics-1', '293', '518', 206, 363, 0, 8, 0, '{}', @LASTID, NOW(), NOW()),
('', '订单列表-1', 'OrderList-1', '370', '518', 259, 363, 217, 8, 0, '{}', @LASTID, NOW(), NOW()),
('', '库存统计-1', 'InventoryStatistics-1', '281', '518', 197, 363, 494, 8, 0, '{}', @LASTID, NOW(), NOW()),
('', '年度订单走势-1', 'OrderTrend-1', '332', '797', 233, 558, 0, 390, 0, '{}', @LASTID, NOW(), NOW()),
('', '产能分析-1', 'CapacityAnalysis-1', '332', '797', 233, 558, 243, 390, 0, '{}', @LASTID, NOW(), NOW()),
('', '质量稳定性分析-1', 'QualityStabilityAnalysis-1', '279', '797', 196, 558, 494, 390, 0, '{}', @LASTID, NOW(), NOW()),
('', '客户订单分析-1', 'OrderAnalysis-1', '421', '518', 295, 363, 0, 966, 0, '{}', @LASTID, NOW(), NOW()),
('', '质量分析-1', 'QualityAnalysis-1', '245', '518', 172, 363, 304, 966, 0, '{}', @LASTID, NOW(), NOW()),
('', '不良原因分析-1', 'AnalysisOfAdverseCauses-1', '280', '518', 196, 363, 494, 966, 0, '{}', @LASTID, NOW(), NOW());


-- 产线大屏
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('产线大屏', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'PRODUCT_LINE_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'PRODUCT_LINE_SCREEN', '{\"line_ids\":[],\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '状态总览-1', 'AoOverviewState-1', '153', '1410', 108, 987, 0, 11, 0, '{}', @LASTID, NOW(), NOW()),
('', '生产计划与进度-1', 'AoNewProductionContent-1', '307.8571428571429', '1410', 216, 987, 127, 11, 0, '{}', @LASTID, NOW(), NOW()),
('', '告警详情-1', 'AoRightAlarmList-1', '265', '400', 186, 280, 59, 1026, 0, '{}', @LASTID, NOW(), NOW()),
('', '历史工单挂起数-1', 'RightLineTopCount-1', '65', '400', 46, 280, 0, 1026, 0, '{}', @LASTID, NOW(), NOW()),
('', '成品维修-1', 'AoRightFinishedProductMaintenance-1', '195', '400', 137, 280, 259, 1026, 0, '{}', @LASTID, NOW(), NOW()),
('', '半成品维修-1', 'AoRightSemiFinishedProductMaintenance-1', '195', '400', 137, 280, 409, 1026, 0, '{}', @LASTID, NOW(), NOW()),
('', '质量检测-1', 'AoRightSemiFinishedProductTop-1', '195', '400', 137, 280, 557, 1026, 0, '{}', @LASTID, NOW(), NOW()),
('', '产量良率-2', 'AoProductionYield-2', '275', '560', 193, 392, 353, 606, 0, '{}', @LASTID, NOW(), NOW()),
('', '投入产出信息-1', 'AoTodayInputOutputInfo-1', '275', '820', 193, 574, 354, 11, 0, '{}', @LASTID, NOW(), NOW()),
('', '工单备注信息-1', 'AoWorkOrderNote-1', '195', '1410', 137, 987, 557, 11, 0, '{}', @LASTID, NOW(), NOW());


-- 产线大屏
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('产线大屏', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'PRODUCT_LINE_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'PRODUCT_LINE_SCREEN', '{\"line_ids\":[],\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '当前工单-1', 'assemblyCurrentOrder-1', '418', '465', 293, 326, 2, 7, 0, '{}', @LASTID, NOW(), NOW()),
('', '今日数据-1', 'assemblyTodayData-1', '418', '1018', 293, 713, 2, 352, 0, '{}', @LASTID, NOW(), NOW()),
('', '设备故障列表-1', 'EquipmentFailureList-1', '766', '363', 537, 255, 135, 1084, 0, '{}', @LASTID, NOW(), NOW()),
('', '工单列表-1', 'assemblyOrderList-1', '512', '724', 359, 507, 314, 7, 0, '{}', @LASTID, NOW(), NOW()),
('', '今日小时产出数-1', 'assemblyOutputEcharts-1', '512', '724', 359, 507, 313, 558, 0, '{}', @LASTID, NOW(), NOW()),
(NULL, '达标率-1', 'complianceRate-1', '180', '363', 126, 255, 2, 1083, 0, '{\"deviceType\":\"\",\"deviceMetrics\":\"\"}', @LASTID, NOW(), NOW());


-- 产线故障统计分析
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('产线故障统计分析', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'PRODUCT_LINE_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
( @LASTID, 'PRODUCT_LINE_SCREEN', '{\"line_ids\":[],\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '产线故障统计分析大屏-1', 'FailureAnalysis-1', '977', '1920', 684, 1344, 0, 0, 0, '{\"time_ranges\":\"\"}', @LASTID, NOW(), NOW());


-- 机加大屏
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('机加大屏', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'PRODUCT_LINE_GROUP_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'PRODUCT_LINE_GROUP_SCREEN', '{\"grid_ids\":[],\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '机加设备状态总览-1', 'maDeviceStatusOverview-1', '209', '1875', 147, 1313, 0, 7, 0, '{}', @LASTID, NOW(), NOW()),
('', '机加设备工单列表-1', 'maDeviceWorkOrderList-1', '676', '1875', 474, 1313, 158, 7, 0, '{}', @LASTID, NOW(), NOW());


-- 车间大屏
INSERT INTO `dfs_screen`(`name`, `show_home`, `app_id`, `top_sort`, `create_time`, `update_time`, `create_by`) VALUES
('车间大屏', 1, (SELECT app_id FROM dfs_screen_app WHERE app_type = 'PRODUCT_LINE_GROUP_SCREEN'), NULL, NOW(), NOW(), 'admin');
SET @LASTID = LAST_INSERT_ID();
INSERT INTO `dfs_screen_config`( `screen_id`, `app_type`, `config`) VALUES
(@LASTID, 'PRODUCT_LINE_GROUP_SCREEN', '{\"grid_ids\":[],\"lang\":\"CN\",\"keyComponent\":\"\",\"assistComponents\":[],\"transcriptScreenName\":[]}');
INSERT INTO `dfs_screen_component`( `title`, `name`, `components`, `current_height`, `current_width`, `height`, `width`, `com_top`, `com_left`, `z_index`, `config`, `screen_id`, `create_time`, `update_time`) VALUES
('', '今日数据-1', 'TodayDate-1', '418.5714285714286', '1888.7142857142858', 293, 1323, 1, 8, 0, '{}', @LASTID, NOW(), NOW()),
('', '数据列表-1', 'RightDateList-1', '513', '569', 360, 399, 318, 8, 0, '{}', @LASTID, NOW(), NOW()),
('', '产线计划达成率-1', 'LeftproductLinePlan-1', '513', '643', 360, 451, 317, 420, 0, '{}', @LASTID, NOW(), NOW()),
('', '产线OEE/良率对比-1', 'CenterCompare-1', '513', '643', 360, 451, 317, 886, 0, '{}', @LASTID, NOW(), NOW());



commit
