set autocommit = 0;

-- 清空大屏表,大屏组件表
truncate table `dfs_screen_component`;
truncate table `dfs_component`;
-- dfs_screen_component 插入数据
#驾驶舱
INSERT INTO `dfs_screen_component`(`name`, `title`, `color`, `create_time`, `is_show_home`, `is_record_data`, `cavas_size`) 
VALUES ('驾驶舱', '驾驶舱', '', NOW(), 1, 0, '1');
INSERT INTO `dfs_component`(`position`, `name`, `size`, `scale_size`, `type`, `create_time`, `screen_id`, `z_index`)
VALUES ('left:988.2,top:480.5', 'AnalysisOfAdverseCauses', 'width:518,height:280', 'width:362.6,height:196', 'carScreen', NOW(), LAST_INSERT_ID(), 10),
('left:988.2,top:292.5', 'QualityAnalysis', 'width:518,height:245', 'width:362.6,height:171.5', 'carScreen', NOW(), LAST_INSERT_ID(), 9),
('left:409.05,top:480.85', 'QualityStabilityAnalysis', 'width:797,height:279', 'width:557.9,height:195.3', 'carScreen', NOW(), LAST_INSERT_ID(), 8),
('left:988,top:0', 'OrderAnalysis', 'width:518,height:421', 'width:362.6,height:294.7', 'carScreen', NOW(), LAST_INSERT_ID(), 7),
('left:409.05,top:231.6', 'CapacityAnalysis', 'width:797,height:332', 'width:557.9,height:232.4', 'carScreen', NOW(), LAST_INSERT_ID(), 6),
('left:409,top:0', 'OrderTrend', 'width:797,height:332', 'width:557.9,height:232.4', 'carScreen', NOW(), LAST_INSERT_ID(), 5),
('left:10.2,top:480.15', 'InventoryStatistics', 'width:518,height:281', 'width:362.6,height:196.7', 'carScreen', NOW(), LAST_INSERT_ID(), 4),
('left:10.2,top:205', 'OrderList', 'width:518,height:370', 'width:362.6,height:259', 'carScreen', NOW(), LAST_INSERT_ID(), 3),
('left:10,top:0', 'OrderStatistics', 'width:518,height:293', 'width:362.6,height:205.1', 'carScreen', NOW(), LAST_INSERT_ID(), 2);


#运营中心
INSERT INTO `dfs_screen_component`(`name`, `title`, `color`, `create_time`, `is_show_home`, `is_record_data`, `cavas_size`)
VALUES ('制造运营中心', '制造运营中心', '', NOW(), 1, 0, '2');
INSERT INTO `dfs_component`(`position`, `name`, `size`, `scale_size`, `type`, `create_time`, `screen_id`, `z_index`)
VALUES ('left:179,top:543.6', 'MainFooter', 'width:1420,height:82', 'width:994,height:57.4', 'process', NOW(), LAST_INSERT_ID(), 9),
       ('left:166,top:0', 'ProcessMain', 'width:1420,height:762', 'width:994,height:533.4', 'process', NOW(), LAST_INSERT_ID(), 0),
       ('left:1097,top:294', 'StateList', 'width:340,height:388', 'width:238,height:271.6', 'process', NOW(), LAST_INSERT_ID(), 8),
       ('left:1097,top:0', 'ProcessAlarmSummary', 'width:340,height:388', 'width:238,height:271.6', 'process', NOW(), LAST_INSERT_ID(), 7),
       ('left:11,top:405', 'RightAlarmChart', 'width:340,height:280', 'width:238,height:196', 'process', NOW(), LAST_INSERT_ID(), 6),
       ('left:11,top:196', 'ProcessFactoryRate', 'width:340,height:280', 'width:238,height:196', 'process', NOW(), LAST_INSERT_ID(), 5),
       ('left:11,top:0', 'RightChart', 'width:340,height:280', 'width:238,height:196', 'process', NOW(), LAST_INSERT_ID(), 4);


#生产大屏
INSERT INTO `dfs_screen_component`(`name`, `title`, `color`, `create_time`, `is_show_home`, `is_record_data`, `cavas_size`)
VALUES ('生产大屏', '生产大屏', '', NOW(), 1, 0, '2');
INSERT INTO `dfs_component`(`position`, `name`, `size`, `scale_size`, `type`, `create_time`, `screen_id`, `z_index`)
VALUES ('left:1039,top:467', 'Beat', 'width:400,height:265', 'width:280,height:185.5', 'production', NOW(), LAST_INSERT_ID(), 6),
       ('left:1039,top:236', 'ProductionOee', 'width:400,height:265', 'width:280,height:185.5', 'production', NOW(), LAST_INSERT_ID(), 5),
       ('left:1039,top:0', 'LineCharts', 'width:400,height:265', 'width:280,height:185.5', 'production', NOW(), LAST_INSERT_ID(), 4),
       ('left:21,top:121', 'MainContentAll', 'width:1410,height:666', 'width:987,height:466.2', 'production', NOW(), LAST_INSERT_ID(), 3),
       ('left:21,top:0', 'OverviewState', 'width:1410,height:153', 'width:987,height:107.1', 'production', NOW(), LAST_INSERT_ID(), 2);


#设备大屏
INSERT INTO `dfs_screen_component`(`name`, `title`, `color`, `create_time`, `is_show_home`, `is_record_data`, `cavas_size`)
VALUES ('设备大屏', '设备大屏', '', NOW(), 1, 0, '2');
INSERT INTO `dfs_component`(`position`, `name`, `size`, `scale_size`, `type`, `create_time`, `screen_id`, `z_index`)
VALUES ('left:0,top:0', 'DeviceContent', 'width:1920,height:960', 'width:1344,height:672', 'device', NOW(), LAST_INSERT_ID(), 0);


#产线大屏
INSERT INTO `dfs_screen_component`(`name`, `title`, `color`, `create_time`, `is_show_home`, `is_record_data`, `cavas_size`)
VALUES ('产线大屏', '产线大屏', '', NOW(), 1, 0, '1');
INSERT INTO `dfs_component`(`position`, `name`, `size`, `scale_size`, `type`, `create_time`, `screen_id`, `z_index`)
VALUES ('left:1046,top:486.5', 'AoRightBadStationData', 'width:400,height:275', 'width:280,height:192.5', 'ao', NOW(), LAST_INSERT_ID(), 10),
       ('left:1046,top:340', 'AoRightSemiFinishedProductMaintenance', 'width:400,height:195', 'width:280,height:136.5', 'ao', NOW(), LAST_INSERT_ID(), 9),
       ('left:1046,top:186', 'AoRightFinishedProductMaintenance', 'width:400,height:195', 'width:280,height:136.5', 'ao', NOW(), LAST_INSERT_ID(), 8),
       ('left:14,top:532', 'AoWorkOrderNote', 'width:1410,height:195', 'width:987,height:136.5', 'ao', NOW(), LAST_INSERT_ID(), 7),
       ('left:609,top:301.25', 'AoProductionYield', 'width:560,height:275', 'width:392,height:192.5', 'ao', NOW(), LAST_INSERT_ID(), 6),
       ('left:14,top:301', 'AoTodayInputOutputInfo', 'width:820,height:275', 'width:574,height:192.5', 'ao', NOW(), LAST_INSERT_ID(), 5),
       ('left:1046,top:0', 'AoRightAlarmList', 'width:400,height:265', 'width:280,height:185.5', 'ao', NOW(), LAST_INSERT_ID(), 4),
       ('left:14,top:125', 'AoNewProductionContent', 'width:1410,height:200', 'width:987,height:140', 'ao', NOW(), LAST_INSERT_ID(), 3),
       ('left:14,top:0', 'AoOverviewState', 'width:1410,height:153', 'width:987,height:107.1', 'ao', NOW(), LAST_INSERT_ID(), 2);


commit
