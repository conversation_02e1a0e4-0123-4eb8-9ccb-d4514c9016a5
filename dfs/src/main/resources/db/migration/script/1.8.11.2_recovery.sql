# 一键还原数据脚本

-- ----------------------------
-- Records of dfs_alarm_classify
-- ----------------------------
INSERT INTO `dfs_alarm_classify` VALUES (1, '901', '设备告警', '[{\"model\": \"grid\",\"ename\": \"gridName\",\"name\": \"车间名称\"},{ \"model\": \"line\", \"ename\": \"lineName\", \"name\": \"制造单元名称\" },{\"model\": \"device\",\"ename\":\"deviceName\", \"name\": \"设备名称\"},{\"model\":\"abnormalInformation\",\"ename\":\"abnormalInformation\",\"name\":\"异常信息\" }]', 'systemGeneration', NULL, NULL, NULL, NULL);
INSERT INTO `dfs_alarm_classify` VALUES (2, '902', '生产告警', '[{ \"model\": \"line\",\"ename\": \"lineName\", \"name\": \"制造单元名称\"},{\"model\": \"workOrder\",\"ename\": \"workOrderName\",\"name\": \"工单名称\"},{\"model\": \"product\",\"ename\": \"materialName\",\"name\": \"物料名称\"},{\"model\": \"product\",\"ename\": \"materialStandard\",\"name\": \"物料规格\"},{\"model\":\"abnormalInformation\",\"ename\": \"abnormalInformation\",\"name\": \"异常信息\"}]', 'systemGeneration', NULL, NULL, NULL, NULL);
INSERT INTO `dfs_alarm_classify` VALUES (3, '903', '安全告警', '[{\"model\":\"grid\",\"ename\":\"gridName\",\"name\":\"车间名称\"},{\"model\":\"line\",\"ename\":\"lineName\",\"name\":\"制造单元名称\" },{\"model\":\"device\",\"ename\": \"deviceName\",\"name\":\"设备名称\" },{\"model\":\"abnormalInformation\",\"ename\":\"abnormalInformation\",\"name\":\"异常信息\"}]', 'systemGeneration', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Records of dfs_alarm_notice
-- ----------------------------
INSERT INTO `dfs_alarm_notice`(`id`, `alarm_level`, `alarm_level_name`, `type`, `create_by`, `update_by`, `create_time`, `update_time`, `user_id`, `upgrade_duration`) VALUES (1, 777, '轻微告警', '1,2,3,0', '', 'admin', '2021-03-06 17:21:45', '2021-11-08 15:32:54', '32,33', 300);
INSERT INTO `dfs_alarm_notice`(`id`, `alarm_level`, `alarm_level_name`, `type`, `create_by`, `update_by`, `create_time`, `update_time`, `user_id`, `upgrade_duration`) VALUES (2, 888, '一般告警', '0,2,3', NULL, 'admin', NULL, '2021-03-11 15:29:46', '1', 18000);
INSERT INTO `dfs_alarm_notice`(`id`, `alarm_level`, `alarm_level_name`, `type`, `create_by`, `update_by`, `create_time`, `update_time`, `user_id`, `upgrade_duration`) VALUES (3, 999, '严重告警', '0,4,1,3', NULL, 'admin', NULL, '2021-03-09 18:35:23', '1', 300);

-- ----------------------------
-- Records of dfs_approve_config
-- ----------------------------
INSERT INTO `dfs_approve_config` VALUES (1, 'salesOrder', '销售订单', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (2, 'customerProfile', '客户档案', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (3, 'deliveryApplication', '出货申请', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (4, 'delivery', '发货管理', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (5, 'productOrder', '生产订单', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (6, 'workOrder', '生产工单', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (7, 'productionTaking', '生产领料', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (8, 'supplierProfile', '供应商档案', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (9, 'procureDemand', '采购需求', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (10, 'purchaseOrder', '采购订单', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (11, 'receipt', '收货单', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (12, 'material', '物料', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (13, 'bom', 'BOM', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (14, 'craft', '工艺', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (15, 'procedure', '工序', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (16, 'output', '出库记录', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (17, 'input', '入库记录', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (18, 'transfer', '物料调拨', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (19, 'warehouse', '库房管理', 0, NULL, NULL);
INSERT INTO `dfs_approve_config` VALUES (39, 'productInspectionReportProject', '产品检测报告', 0, 'admin', '2021-11-25 14:30:58');
INSERT INTO `dfs_approve_config` VALUES (40, 'productInspectionProject', '产品检测项目', 0, 'admin', '2021-12-22 20:24:13');
INSERT INTO `dfs_approve_config` VALUES (41, 'productInspectionPlan', '产品检测方案', 0, 'admin', '2021-12-22 20:24:46');

-- ----------------------------
-- Records of dfs_config_cleanable_table
-- ----------------------------
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (1, 'dfs_operation_log');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (2, 'dfs_quality');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (3, 'dfs_report_count');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (4, 'dfs_report_fac_state');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (5, 'dfs_sensor_record');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (6, 'dfs_alarm_record');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (7, 'dfs_notice_record');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (18, 'dfs_record_area_state');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (19, 'dfs_record_device_pause');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (20, 'dfs_record_device_run');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (21, 'dfs_record_device_state');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (22, 'dfs_record_device_utilization');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (23, 'dfs_record_facility_count');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (24, 'dfs_record_facility_count_order');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (25, 'dfs_record_facility_day_union');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (26, 'dfs_record_facility_efficiency');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (27, 'dfs_record_facility_oee');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (28, 'dfs_record_facility_pause');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (29, 'dfs_record_facility_performance');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (30, 'dfs_record_facility_run');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (31, 'dfs_record_facility_state');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (32, 'dfs_record_facility_stop');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (33, 'dfs_record_facility_unqualified');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (34, 'dfs_record_facility_unqualified_order');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (35, 'dfs_record_facility_yield');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (36, 'dfs_record_grid_state');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (37, 'dfs_record_line_avg_time_order');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (38, 'dfs_record_line_count');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (39, 'dfs_record_line_count_order');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (40, 'dfs_record_line_day_union');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (41, 'dfs_record_line_efficiency');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (42, 'dfs_record_line_finish_rate');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (43, 'dfs_record_line_oee');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (44, 'dfs_record_line_pause');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (45, 'dfs_record_line_performance');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (46, 'dfs_record_line_run');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (47, 'dfs_record_line_state');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (48, 'dfs_record_line_stop');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (49, 'dfs_record_line_time_efficiency');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (50, 'dfs_record_line_unqualified');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (51, 'dfs_record_line_unqualified_order');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (52, 'dfs_record_line_yield');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (53, 'dfs_record_line_yield_order');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (54, 'dfs_record_work_order_progress');
INSERT INTO `dfs_config_cleanable_table`(`id`, `table_name`) VALUES (55, 'dfs_record_work_order_state');

-- ----------------------------
-- Records of dfs_config_rule
-- ----------------------------
INSERT INTO `dfs_config_rule` VALUES (1, 1, '销售订单-订单编号');
INSERT INTO `dfs_config_rule` VALUES (2, 2, '生产工单-工单编号');
INSERT INTO `dfs_config_rule` VALUES (3, 3, '供应商档案-供应商编码');
INSERT INTO `dfs_config_rule` VALUES (4, 4, 'BOM-BOM编码');
INSERT INTO `dfs_config_rule` VALUES (5, 5, '生产工单-批次号');
INSERT INTO `dfs_config_rule` VALUES (6, 6, '出库记录-出库单号');

-- ----------------------------
-- Records of dfs_dict
-- ----------------------------
INSERT INTO `dfs_dict` VALUES (1, '08:00', '首班最早时间', 'beginTimeOfDay', NULL, NULL, NULL, '2021-11-25 10:05:58', '2022-01-07 17:49:56', 'admin', 'admin');
INSERT INTO `dfs_dict` VALUES (2, 'transfer_direct', '直接调拨单列表', 'synTable', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_dict` VALUES (3, 'faultAlarm', '故障告警', 'alarmCycleSetting', NULL, NULL, '2', '2021-11-12 17:16:41', NULL, 'admin', NULL);
INSERT INTO `dfs_dict` VALUES (4, 'recoveryAlarm', '恢复告警', 'alarmCycleSetting', NULL, NULL, '3', '2021-11-12 17:17:24', NULL, 'admin', NULL);
INSERT INTO `dfs_dict` VALUES (5, NULL, 'Pcs', 'unit', NULL, NULL, '单位：Pcs', '2022-01-06 15:02:19', '2022-01-06 15:16:48', 'admin', 'admin');
INSERT INTO `dfs_dict` VALUES (6, NULL, 'Kg', 'unit', NULL, NULL, '单位：千克', '2022-01-06 15:15:56', '2022-01-06 15:16:30', 'admin', 'admin');
INSERT INTO `dfs_dict` VALUES (7, NULL, '个', 'unit', NULL, NULL, '单位：个', '2022-01-06 15:16:15', '2022-01-06 15:16:15', 'admin', NULL);
INSERT INTO `dfs_dict` VALUES (8, NULL, 'mm', 'unit', NULL, NULL, '单位：毫米', '2022-01-06 15:26:13', '2022-01-06 15:26:13', 'admin', NULL);
INSERT INTO `dfs_dict` VALUES (9, NULL, 'g', 'unit', NULL, NULL, '单位：克', '2022-01-07 14:04:00', '2022-01-07 14:04:00', 'admin', NULL);
INSERT INTO `dfs_dict` VALUES (10, '777', '轻微告警', 'alarmLevel', NULL, NULL, NULL, '2021-02-23 09:33:04', '2021-02-23 09:33:09', NULL, NULL);
INSERT INTO `dfs_dict` VALUES (11, '888', '一般告警', 'alarmLevel', NULL, NULL, NULL, '2021-02-23 09:33:04', '2021-02-23 09:33:04', NULL, NULL);
INSERT INTO `dfs_dict` VALUES (12, '999', '严重告警', 'alarmLevel', NULL, NULL, NULL, '2021-02-23 09:33:04', '2021-02-23 09:33:04', NULL, NULL);

-- ----------------------------
-- Records of dfs_model
-- ----------------------------
INSERT INTO `dfs_model` VALUES (1, NULL, '设备', NULL, NULL, 'devicecode', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `dfs_model` VALUES (2, NULL, '产线', NULL, NULL, 'LineCode', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Records of dfs_rule_seq
-- ----------------------------
INSERT INTO `dfs_rule_seq` VALUES (1, '1', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (2, '2', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (3, '3', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (4, '4', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (5, '5', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (6, '6', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (7, '7', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (8, '8', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (9, '9', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');
INSERT INTO `dfs_rule_seq` VALUES (10, '10', 1, '2021-11-25 11:14:23', '2021-11-25 11:14:23');

-- ----------------------------
-- Records of dfs_sensor_product
-- ----------------------------
INSERT INTO `dfs_sensor_product` VALUES (1, '计数器', NULL, 'newCounter', '2022-01-19 10:20:22', '2022-01-19 10:20:22', 'admin', 'admin');
INSERT INTO `dfs_sensor_product` VALUES (2, '机器手环', NULL, 'wristband', '2022-01-19 10:20:22', '2022-01-19 10:20:22', 'admin', 'admin');
INSERT INTO `dfs_sensor_product` VALUES (3, '温湿度', NULL, 'innerHumiture', '2022-01-19 10:20:22', '2022-01-19 10:20:22', 'admin', 'admin');
INSERT INTO `dfs_sensor_product` VALUES (4, '通用类型', NULL, 'commonCollector', '2022-01-19 10:20:22', '2022-01-19 10:20:22', 'admin', 'admin');

-- ----------------------------
-- Records of dfs_synchronize_record
-- ----------------------------
INSERT INTO `dfs_synchronize_record` VALUES (1, 'transfer_direct', NULL, 'Index: 0, Size: 0', 0, '2022-01-19 23:55:00');
INSERT INTO `dfs_synchronize_record` VALUES (2, 'transfer_direct', NULL, 'Index: 0, Size: 0', 0, '2022-01-20 00:15:00');
INSERT INTO `dfs_synchronize_record` VALUES (3, 'transfer_direct', NULL, 'Index: 0, Size: 0', 0, '2022-01-20 00:35:00');

-- ----------------------------
-- Records of dfs_target_dict
-- ----------------------------
INSERT INTO `dfs_target_dict` VALUES (1, 'facState', '工位工作状态', NULL, 'facility', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (2, 'facRunningDuration', '工位工作时长', NULL, 'facility', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (3, 'facPauseDuration', '工位暂停时长', NULL, 'facility', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (4, 'facStopDuration', '工位停止时长', NULL, 'facility', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (5, 'facCountOrder', '工位按工单的已加工数量', NULL, 'facility', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (6, 'facCount', '工位当天的已加工数量', NULL, 'facility', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (13, 'lineState', '产线工作状态', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (14, 'lineRunningDuration', '产线工作时长', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (15, 'linePauseDuration', '产线暂停时长', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (16, 'lineStopDuration', '产线停止时长', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (17, 'lineCount', '产线当天已加工数量', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (18, 'lineCountByOrder', '产线工单已加工数量', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (19, 'lineUnqualified', '产线不合格量', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (20, 'lineUnqualifiedOrder', '产线工单不合格数量', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (21, 'lineTimeEfficiency', '产线时间利用率', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (22, 'lineEfficiency', '当天工作效率', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (23, 'linePerformance', '产线性能利用率', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (24, 'lineYield', '产线当天良品率', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (25, 'lineYieldOrder', '产线工单良品率', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (26, 'lineOEE', '产线oee', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (27, 'lineOrderAvgTime', '平均加工时长', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (30, 'lineFinishRate', '工单及时交付率', NULL, 'line', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (31, 'lineBeat', '产线节拍', '', 'line', NULL, NULL, NULL, '2021-09-09 14:32:29', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (32, 'workOrderProgress', '工单进度', NULL, 'workOrder', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (33, 'workOrderState', '工单状态', NULL, 'workOrder', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (84, 'deviceState', '设备工作状态', NULL, 'device', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (85, 'deviceRunningDuration', '设备工作时长', NULL, 'device', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (86, 'devicePauseDuration', '设备暂停时长', NULL, 'device', NULL, NULL, NULL, '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (88, 'ua', 'A相电压', '1', 'device', NULL, NULL, 'V', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (89, 'ub', 'B相电压', '1', 'device', NULL, NULL, 'V', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (90, 'uc', 'C相电压', '1', 'device', NULL, NULL, 'V', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (91, 'ia', 'A相电流', '1', 'device', NULL, NULL, 'A', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (92, 'ib', 'B相电流', '1', 'device', NULL, NULL, 'A', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (93, 'ic', 'C相电流', '1', 'device', NULL, NULL, 'A', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (94, 'ept', '有功电能', '1', 'device', NULL, NULL, 'kwh', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (95, 'pt', '总有功功率', NULL, 'device', NULL, NULL, 'KW', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (96, 'qt', '总无功功率', NULL, 'device', NULL, NULL, 'Kvar', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (97, 'temperature', '温度', NULL, 'device', NULL, NULL, '℃', '2022-01-19 09:37:52', NULL, NULL);
INSERT INTO `dfs_target_dict` VALUES (98, 'humidity', '湿度', NULL, 'device', NULL, NULL, '％', '2022-01-19 09:37:52', NULL, NULL);

-- ----------------------------
-- Records of dfs_target_method
-- ----------------------------
INSERT INTO `dfs_target_method` VALUES (2, 'facState', 'facility', '所有采集器综合计算', 'facStateBySelected', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (3, 'facRunningDuration', 'facility', '根据工位状态计算', 'facRunningDurationByState', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (4, 'facPauseDuration', 'facility', '根据工位状态计算', 'facPauseDurationByState', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (5, 'facStopDuration', 'facility', '根据工位状态计算', 'facStopDurationByState', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (9, 'facCount', 'facility', '采集器采集', 'facDayCount', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (10, 'facCountOrder', 'facility', '采集器采集', 'facCountByOrder', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (11, 'lineState', 'line', '所有工位综合计算', 'lineStateBySelected', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (12, 'lineRunningDuration', 'line', '根据产线状态计算', 'lineRunningDurationByState', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (13, 'linePauseDuration', 'line', '根据产线状态计算', 'linePauseDurationByState', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (14, 'lineStopDuration', 'line', '根据产线状态计算', 'lineStopDurationByState', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (15, 'lineUnqualified', 'line', '全天不合格数量累加计算', 'lineUnqualifiedCount', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (16, 'lineUnqualifiedOrder', 'line', '手动输入', 'lineUnqualifiedOrder', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (17, 'lineTimeEfficiency', 'line', '产线运行时长/理论每天产线工作时长', 'lineTimeEfficiency', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (18, 'lineEfficiency', 'line', '已加工数/当天实际产出产品的总时间', 'lineEfficiency', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (19, 'linePerformance', 'line', '当天工作效率/理论工作效率', 'linePerformance', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (20, 'lineYield', 'line', '当天良品之和/当天总加工之和', 'lineYield', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (21, 'lineYieldOrder', 'line', '工单周期内，良品之和/总加工个数', 'lineYieldOrder', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (22, 'lineOEE', 'line', '当天良品个数/产线工作效率', 'lineOEE', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (23, 'lineFinishRate', 'line', '及时交付率=该产线及时完成的工单数量/该产线工单完成数量', 'lineFinishRate', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (24, 'lineCount', 'line', '根据最后一个工位计算', 'lineDayCount', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (27, 'lineCountByOrder', 'line', '根据最后一个工位计算', 'lineCountByOrder', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (28, 'lineOrderAvgTime', 'line', '平均加工时长=工单工作时长/工单加工数量（良品）', 'lineOrderAvgTime', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (29, 'lineBeat', 'line', '产线节拍=时间/数量', 'lineBeat', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (30, 'workOrderProgress', 'workOrder', '工单进度', 'workOrderProgress', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (31, 'workOrderState', 'workOrder', '工单状态', 'workOrderState', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (32, 'temperature', 'device', '温湿度计采集', 'temperatureByInnerSensor', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (33, 'humidity', 'device', '温湿度计采集', 'humidityByInnerSensor', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (34, 'ia', 'device', '通过机器手环采集', 'iaAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (35, 'ib', 'device', '通过机器手环采集', 'ibAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (36, 'ic', 'device', '通过机器手环采集', 'icAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (37, 'ua', 'device', '通过机器手环采集', 'uaAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (38, 'ub', 'device', '通过机器手环采集', 'ubAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (39, 'uc', 'device', '通过机器手环采集', 'ucAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (40, 'pt', 'device', '通过机器手环采集', 'ptAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (41, 'qt', 'device', '通过机器手环采集', 'qtAutoCollect', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (42, 'ept', 'device', '通过机器手环采集', 'eptAutoCollect', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (43, 'deviceState', 'device', '所有采集器综合计算', 'deviceStateBySensors', 1, NULL);
INSERT INTO `dfs_target_method` VALUES (44, 'deviceRunningDuration', 'device', '根据设备状态计算', 'deviceRunningDurationByState', 0, NULL);
INSERT INTO `dfs_target_method` VALUES (45, 'devicePauseDuration', 'device', '根据设备状态计算', 'devicePauseDurationByState', 0, NULL);

-- ----------------------------
-- Records of dfs_target_model
-- ----------------------------
INSERT INTO `dfs_target_model`(`id`, `target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `frequency_unit`, `alarm_definition_code`, `is_count_target`) VALUES (1, 'workOrderProgress', '工单进度', 0, 'workOrderProgress', '产线成品数/工单计划成品数', NULL, 5, '', NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, 's', NULL, NULL);
INSERT INTO `dfs_target_model`(`id`, `target_name`, `target_cnname`, `model_id`, `method_name`, `method_cname`, `multiple_choice`, `frequency`, `unit`, `alarm_level`, `alarm_type`, `alarm_des`, `alarm_advice`, `alarm_config`, `is_threshold_setting`, `show_type`, `create_time`, `update_time`, `frequency_unit`, `alarm_definition_code`, `is_count_target`) VALUES (2, 'workOrderState', '工单状态', 0, 'workOrderState', '工单状态', NULL, 5, '', NULL, NULL, NULL, NULL, '', NULL, NULL, NULL, NULL, 's', NULL, NULL);


-- ----------------------------
-- Records of oauth_client_details
-- ----------------------------
INSERT INTO `oauth_client_details` VALUES ('android-place-client', NULL, '$2a$10$1J3bQoEj0hKL3k/y7hZmDuEaouredhRZQNMkYLPWcbSP6aCLwo4v6', 'all', 'password,refresh_token', NULL, NULL, 86400, 172800, NULL, '1');
INSERT INTO `oauth_client_details` VALUES ('web-admin-client', NULL, '$2a$10$9VwseTAj5pBxygQahOydGeSRWVkny6qJhudr0JmeBZbOIED90OTNC', 'all', 'password,refresh_token', NULL, NULL, 86400, 172800, NULL, '1');
