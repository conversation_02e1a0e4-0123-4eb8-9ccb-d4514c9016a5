server:
  port: 8181
  servlet:
    context-path: /api

spring:
  session:
    timeout: 0
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    dynamic:
      # 设置默认的数据源
      primary: master
      # 严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      strict: false
      datasource:
        master:
          username: root
          password: yelink123
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://${mysql.ip}:3306/${dfs.tableSchema}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&useSSL=false&serverTimezone=Asia/Shanghai&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true
          hikari:
            #连接池名
            pool-name: master
            #最小空闲连接数
            min-idle: 5
            # 连接池最大连接数，默认是10
            max-pool-size: 200
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 300000
            # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
            is-auto-commit: true
            # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 1200000
            # 数据库连接超时时间,默认30秒，即30000
            connection-timeout: 30000
            connection-test-query: SELECT 1
            leak-detection-threshold: 60000
            keep-alive-time: 30000
        metrics:
          username: root
          password: yelink123
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://${mysql.ip}:3306/dfs_metrics?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&useSSL=false&serverTimezone=Asia/Shanghai&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true&createDatabaseIfNotExist=true
          hikari:
            #连接池名
            pool-name: metrics
            #最小空闲连接数
            min-idle: 5
            # 连接池最大连接数，默认是10
            max-pool-size: 200
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 300000
            # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
            is-auto-commit: true
            # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 1200000
            # 数据库连接超时时间,默认30秒，即30000
            connection-timeout: 30000
            connection-test-query: SELECT 1
            leak-detection-threshold: 60000
            keep-alive-time: 30000
#    hikari:
#      #连接池名
#      pool-name: ${spring.application.name}
#      #最小空闲连接数
#      minimum-idle: 5
#      # 连接池最大连接数，默认是10
#      maximum-pool-size: 200
#      # 空闲连接存活最大时间，默认600000（10分钟）
#      idle-timeout: 300000
#      # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
#      auto-commit: true
#      # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
#      max-lifetime: 1200000
#      # 数据库连接超时时间,默认30秒，即30000
#      connection-timeout: 30000
#      connection-test-query: SELECT 1
#      keep-alive-time: 30000
  data:
    mongodb:
      auto-index-creation: true
      uri: mongodb://root:Yelink%40123@${mongodb.ip}/dfs?authSource=admin&authMechanism=SCRAM-SHA-1${mongodb.replicaset}
  redis:
    database: 2
    host: ${redis.ip}
    port: 6379
    password: null
    jedis:
      pool:
        max-wait: 8
        max-active: 1000
        max-idle: 100
        min-idle: 1
  servlet:
    multipart:
      max-request-size: 500MB
      max-file-size: 500MB
  kafka:
    bootstrap-servers: ${kafka.ip}
    consumer:
      group-id: consumer-group-dfs
      auto-offset-reset: latest
      enable-auto-commit: true
      auto-commit-interval: 100
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 100
      properties:
        session:
          timeout.ms: 120000
        max:
          poll:
            interval:
              ms: 1200000
    listener:
      type: batch
      concurrency: ${concurrency}
  flyway:
    # 是否启用flyway
    enabled: true
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，默认db/migration
    locations: classpath:db/migration/exploit,db/migration/maintain,com/yelink/dfs/migration
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # flyway 的 clean 命令会删除指定 schema 下的所有 table, 生产务必禁掉。这个默认值是 false 理论上作为默认配置是不科学的。
    clean-disabled: true
    # 是否允许不按顺序迁移 由于有分支版本，需要置为true
    out-of-order: true
  application:
    name: dfs
  cloud:
    nacos:
      discovery:
        server-addr: ${local.vip}:8001
        #自身服务ip和端口
        ip: ${location.ip}
        port: ${server.port}
        metadata:
          version: ${server.version}
          # 系统重置需要清空的数据库
          reset-data: dfs
          # 系统重置需要重启docker容器的名称
          reset-server: dfs
  main: #解决@FeignClient值重复问题,解決重复bean
    allow-bean-definition-overriding: true
    #excel记录保存的数据库连接配置
  excel:
    datasource:
      username: ${spring.datasource.dynamic.datasource.master.username}
      password: ${spring.datasource.dynamic.datasource.master.password}
      driver-class-name: ${spring.datasource.dynamic.datasource.master.driver-class-name}
      url: ${spring.datasource.dynamic.datasource.master.url}

mybatis-plus:
  mapper-locations: classpath:mapper/*/*Mapper.xml
  typeAliasesPackage: com.yelink.dfs.entity
  typeEnumsPackage: com.yelink.dfs
  global-config:
    db-config:
      #主键类型  0:数据库ID自增   1:用户输入id  2:全局唯一id(IdWorker)  3:全局唯一ID(uuid)
      table-underline: true
      id-type: auto
  configuration:
    #这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    call-setters-on-nulls: true
#    map-underscore-to-camel-case: true
#    default-enum-type-handler: com.yelink.dfs.mapper.user.handler.MyEnumTypeHandler

management:
  endpoints:
    web:
      exposure:
        include: "health,metrics,prometheus"
  endpoint:
    prometheus:
      enabled: true
  metrics:
    enable:
      all: true

#数据库库名
dfs:
  tableSchema: dfs

#kafka 订阅topic
kafka:
  topics: property_post_exchange,event_post_exchange
  websocket:
    consumer: websocket-${location.ip}

#内部计数器应用id，用于区分传感器
inner:
  kafka:
    #灯检机
    lampExaminers: y3b16tt5k1s
    #洗瓶机ACX160
    bottleWashingMachineACX160: 2k67uh4lpjf
    #东富龙灌装机
    fillingMachineDongFuLong: adsonbmpcsq
    #空调系统
    airConditioningSystem: phs12l28ryx

#传感器最大暂停时间【毫秒值】
maxCounterInterval: 300000
#工位/设施最长暂停时间【毫秒值】
maxFaciInterval: 300000

#fdfs:
#  so-timeout: 1500
#  connect-timeout: 600
#  tracker-list: ${fdfs.ip}
header:
  #工单
  workOrder: 200
  #入库单
  stockInput: 300
  #出库单
  stockOutput: 400
  #发货单
  stockDelivery: 600
  #bom
  bom: 700
  #生产领料单
  takeOutApplication: 110
  #出货申请单
  DeliveryApplication: 120
  #故障维修记录单
  maintainRecordOrder: 130
  #故障维修单
  maintainOrder: 140
  #炉号
  furnace: 150
  #告警
  alarm: 180
  #事件
  event: 190

priority: "{1:'正常', 2:'优先',3:'加急',4:'特急'}"

factory: yelink

linux:
  DiskUsage: 0.85

workOrder:
  #自动切换工单
  autoShift: false
  #是否需要审批
  examine: false
  #工单名称自动生成
  autoGenNameSuffix: true
  #是否需要选择计算产量工位
  selectFac: false

fakeData:
  autoExecution: false
iot:
  serviceUrl: ${http.method:http}://${local.vip}:8301
  commandUrl: ${http.method:http}://${local.vip}:8301
  grantType: password
  username: iot-user
  password: 1234qwer
  clientId: msc
  clientSecret: 1234qwer

#关闭hystrix默认熔断
feign:
  hystrix:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
  httpclient:
    disable-ssl-validation: true

ribbon:
  ReadTimeout: 120000
  ConnectTimeout: 30000

#同步系统名字
syn:
  system:
    name: kingdee
  #同步失败重试次数和重试间隔时间
  restartNumber: 3
  restartTime: 1200000
  maxDelay: 3600000
  multiplier: 1
#日志级别
logging:
  level:
    com.yelink: INFO
    com.yelink.dfs.mapper: INFO
    com.yelink.dfs.service.target: INFO

#动态配置redis同步锁过期时间
expirationTime: 2

downloadPath:
  #采购订单模板路径
  purchaseModelPath: /opt/dfs/config/LCM.xlsx
  alarmDefinitionModelPath: /opt/dfs/config/告警定义模板.xlsx
  productionStatementPath: /opt/dfs/config/productionStatement
  inspectionItemModelPath:
  inspectNumsPath: /opt/dfs/config/质检导出报告.xlsx
  craftProcedureModelPath: /opt/dfs/config/CraftProdureceExcel.xlsx

#大屏参考线配置（标准线）standardValue下级表示是那个图
standardValue:
  planAchievementRate: 0.9

#接入单点登入
keycloak:
  auth-server-url: http://${local.vip}:8411/auth/
  resource: dfs
  realm: edge
  credentials:
    secret: 8b8af56a-a2a5-411f-a402-2d9a07aa9a8f
  principal-attribute: preferred_username
  use-resource-role-mappings: false  #注意：下面截图中的值为true,实际应为false
  ssl-required: none

kc:
  master-realm-user-name: admin
  master-realm-user-password: Pa55w0rd
  target-realm: master
  master-resource: admin-cli
oauth:
  tokenUrl: http://${local.vip}:8411/auth/realms/edge/protocol/openid-connect/token
  kcClientId: dfs
  kcClientSecret: 8b8af56a-a2a5-411f-a402-2d9a07aa9a8f


#认证模式（keycloak或者springSecurity）
oauthModel:
  type: keycloak

# 调用精制客户端
jingzhi:
  tokenUrl: ${iot.serviceUrl}/jingzhi/auth/oauth/token
  grantType: client_credentials
  clientId: jingzhi
  clientSecret: 50995e0d-39c3-47dc-929d-aa4240d004c4
# 调用精制标志
jzmark: jzMark

#工单生产完成时如果完成数量小于计划数量，则是否可以新建工单
isNewOrder: false

#对接渊联视频
video:
  accessKey: 87erwerasdas4514858415
  accessKeySecret: sadasdasd1222222
  requestHost: ${local.vip}

#是否开启作业工单特性
isOpenOperationOrder: false

#ECM平台设备告警处理小程序的appID
alarmNotice:
  appId: yelink-alarmHandle

isPushProcedureMsgNotice: false


# 调用云端平台
pushProxy:
  ipPort: http://apigateway.yelinksaas.com:8301/push
  grantType: password
  clientId: jingzhi
  username: jingzhi_admin
  password: Yelink@1234
  clientSecret: 6df47d11-9ff9-47b5-a6fd-c597c04cf06e

ccu:
  requestHost: ${local.vip}:9700

# dfs调用其他微服务时使用到的aksk配置
openApi:
  # ak
  accessKeyId: 79761O2LU1LFMWBEOBQ2
  # sk
  accessKeySecret: AhLlD3CyOTpVId0eXPxkqMOMvjGYBHL1bypsx4cC
  # 本地开发测试使用 或者 在未部署IOT的服务器中使用
#  localGateway: http://127.0.0.1:8000

#cube.js
cubeJs:
  requestHost: ${http.method:http}://${local.vip}:8080/factory/v1/cube

#扫码信息缓存时间
scanner:
  recordTime: 30

#swagger配置
yt:
  doc:
    enable: true

#wechat:
#  client:
#    corp-id: ${wechat.corpId:-}
#    agent-secret: ${wechat.agentSecret:-}
#  server:
#    receive-id: ${wechat.client.corp-id}
#    s-token: wechatToken
#    s-encoding-aes-key: 5QVSyFkf5IvZrlcV5WVxhLGMWZGMzj5SDmsWDQqYGBL


document:
  service:
    languages: zh:Chinese (Simplified),zh-TW:Chinese (Traditional),en:English
    #文件服务器内网访问地址
    command-url: ${http.method:http}://${location.ip}:8080/
    #文件服务器前缀url
    url: /onlyoffice/
    #文件服务器密钥
    secret: 22JQlVYQrIcwxq8d4V0DRjGKxVGiHMMv
    #文件存储的地址
    file-server-url: http://${location.ip}:28301
    # 本服务地址
    local-url: http://${location.ip}:${server.port}/api/

minio:
  endpoint:  http://${local.vip}:28301
  accessKey: root
  secretKey: yelink@123
  bucketName: group1


metrics-sdk:
  module: dfs
  entity-package: com.yelink.dfs.metrics.entity