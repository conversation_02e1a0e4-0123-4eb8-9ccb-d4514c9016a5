package com.yelink.dfs.inner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.device.DeviceTypeEnum;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.maintain.MaintainSchemeTypeEnum;
import com.yelink.dfs.constant.target.TargetConst;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.dto.IndicatorEntityDTO;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.maintain.MaintainSchemeEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderLineDTO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.screen.dto.GridTargetDTO;
import com.yelink.dfs.entity.screen.dto.ProductionAlarmDTO;
import com.yelink.dfs.entity.screen.dto.ScreenDeviceStateDTO;
import com.yelink.dfs.entity.screen.dto.StateOverviewDTO;
import com.yelink.dfs.entity.screen.dto.UnqualifiedFinishDTO;
import com.yelink.dfs.entity.screen.dto.WorkOrderListDTO;
import com.yelink.dfs.entity.screen.vo.LineDetailVO;
import com.yelink.dfs.entity.screen.vo.MaintenanceHistogramVO;
import com.yelink.dfs.entity.screen.vo.PoorStationVO;
import com.yelink.dfs.entity.screen.vo.TodayInputAndOutputVO;
import com.yelink.dfs.entity.target.TargetDictEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.dto.RecordManualCollectionDTO;
import com.yelink.dfs.entity.target.record.RecordLineBeatEntity;
import com.yelink.dfs.entity.target.record.RecordLineInputOutputOrderHourEntity;
import com.yelink.dfs.entity.target.record.RecordManualCollectionEntity;
import com.yelink.dfs.entity.target.record.dto.BeatDTO;
import com.yelink.dfs.entity.target.record.dto.GridBeatDTO;
import com.yelink.dfs.entity.target.record.dto.GridOeeDTO;
import com.yelink.dfs.entity.target.record.dto.LineBeatDTO;
import com.yelink.dfs.entity.target.record.dto.LineOeeDTO;
import com.yelink.dfs.mapper.maintain.MaintainRecordMapper;
import com.yelink.dfs.mapper.manufacture.ProductionLineMapper;
import com.yelink.dfs.mapper.screen.ConfigYzjScreenMapper;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.event.EventRecordService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.maintain.MaintainSchemeService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.CompanyService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.screen.ProductionService;
import com.yelink.dfs.service.target.TargetDictService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.target.TargetThresholdService;
import com.yelink.dfs.service.target.record.RecordLineBeatService;
import com.yelink.dfs.service.target.record.RecordLineInputOutputOrderHourService;
import com.yelink.dfs.service.target.record.RecordLineInputOutputOrderService;
import com.yelink.dfs.service.target.record.RecordManualCollectionService;
import com.yelink.dfs.service.target.record.RecordWorkOrderStateService;
import com.yelink.dfs.utils.EnumUtil;
import com.yelink.dfscommon.api.dfs.HomeScreenInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.screen.WorkOrderRemarkScrDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yelink.dfscommon.utils.ColumnUtil.getField;

/**
 * <AUTHOR>
 * @description: 首页大屏统一对外部接口
 * @time 2021/7/13 10:35
 */
@Slf4j
@AllArgsConstructor
@RestController
public class HomeScreenController implements HomeScreenInterface {

    private WorkOrderService workOrderService;
    private MaterialService materialService;
    private AlarmService alarmService;
    private ModelService modelService;
    private GridService gridService;
    private ProductionLineService lineService;
    private FacilitiesService facilitiesService;
    private DeviceService deviceService;
    private ConfigYzjScreenMapper configYzjScreenMapper;
    private RecordLineBeatService beatService;
    private TargetModelService targetModelService;
    private RecordManualCollectionService recordManualCollectionService;
    private TargetDictService targetDictService;
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    private CommonService commonService;
    private DictService dictService;
    private ProductionLineMapper productionLineMapper;
    private ProductionLineService productionLineService;
    private RecordLineInputOutputOrderService inputOutputOrderService;
    private ProductionService productionService;
    private MaintainRecordMapper maintainRecordMapper;
    private MaintainSchemeService maintainSchemeService;
    private TargetThresholdService targetThresholdService;
    private MaintainRecordService maintainRecordService;
    private RecordWorkOrderStateService recordWorkOrderStateService;
    private EventRecordService eventRecordService;
    private CompanyService companyService;
    private WorkPropertise workPropertise;
    private WorkCenterService workCenterService;
    private RedisTemplate redisTemplate;
    private RecordLineInputOutputOrderHourService recordLineInputOutputOrderHourService;
    private RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService;

    /**
     * 根据状态获取非父工单
     *
     * @param state
     * @return
     * <AUTHOR>
    @Override
    @Cacheable(cacheNames = "screen_work_order_by_state", key = "'state=' + #state")
    public ResponseData listWorkOrderByState(Integer state) {
        LambdaQueryWrapper<WorkOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode());
        qw.and(wrapper -> wrapper.eq(WorkOrderEntity::getIsPid, Constant.START).or().isNull(WorkOrderEntity::getIsPid));
        List<WorkOrderEntity> list = workOrderService.list(qw);
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(list));
        return ResponseData.success(jsonArray);
    }

    /**
     * 根据物料编码拿到物料数据
     *
     * @param code
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_material_by_code", key = "'code=' + #code")
    public ResponseData getMaterialBycode(String code) {
        LambdaQueryWrapper<MaterialEntity> materialWrapper = new LambdaQueryWrapper<>();
        materialWrapper.eq(MaterialEntity::getCode, code);
        MaterialEntity one = materialService.getOne(materialWrapper);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(one));
        return ResponseData.success(jsonObject);
    }

    /**
     * 获取deviceId不为空的告警信息
     *
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_alarm_device_not_null")
    public ResponseData listAlarmDeviceNotNUll() {
        LambdaQueryWrapper<AlarmEntity> qw = new LambdaQueryWrapper<>();
        qw.isNotNull(AlarmEntity::getDeviceId);
        List<AlarmEntity> list = alarmService.list(qw);
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(list));
        return ResponseData.success(jsonArray);
    }

    @Override
    @Cacheable(cacheNames = "screen_device_by_code", key = "'code=' + #code")
    public ResponseData getDeviceByCode(String code) {
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getDeviceCode, code);
        DeviceEntity deviceEntity = deviceService.getOne(deviceWrapper);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(deviceEntity));
        return ResponseData.success(jsonObject);
    }

    @Override
    @Cacheable(cacheNames = "screen_work_order_by_num", key = "'workOrderNum=' + #workOrderNum")
    public ResponseData getWorkOrederByNum(String workOrderNum) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNum);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(workOrderEntity));
        return ResponseData.success(jsonObject);
    }

    @Override
    @Cacheable(cacheNames = "screen_finish_count_in_this_year")
    public ResponseData getFinishCountInThisYear(List<Integer> instanceIdList) {
        List<RecordManualCollectionEntity> list = configYzjScreenMapper.getFinishCountInThisYear(instanceIdList, new Date());
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(list));
        return ResponseData.success(jsonArray);
    }

    @Override
    @Cacheable(cacheNames = "screen_device_list")
    public ResponseData deviceList() {
        LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
        //只要生产设备和关键设备
        wrapper.in(DeviceEntity::getTypeCode, DeviceTypeEnum.KEY.getCode(), DeviceTypeEnum.PRODUCTION.getCode());
        List<DeviceEntity> list = deviceService.list(wrapper);
        List<ScreenDeviceStateDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return ResponseData.success();
        }
        Date nowTime = new Date();
        list.forEach(deviceEntity -> {
            //拿到设备状态持续时间
            Date date = deviceEntity.getStateUpdateTime();
            ScreenDeviceStateDTO dto = ScreenDeviceStateDTO.builder()
                    .deviceName(deviceEntity.getDeviceName())
                    .inStateTime(date == null ? null : DateUtil.getTimeMinute(date, nowTime))
                    .state(deviceEntity.getState())
                    .stateName(DevicesStateEnum.getNameByCode(deviceEntity.getState()))
                    .build();
            dtos.add(dto);
        });
        return ResponseData.success(dtos);
    }

    @Override
    @Cacheable(cacheNames = "screen_manual_collection_on_day", key = "'dateString=' + #dateString")
    public ResponseData getManualCollectionOnDay(String dateString, List<Integer> instanceIdList) {
        Date date = DateUtil.parse(dateString, DateUtil.DATETIME_FORMAT_MIN);
        List<RecordManualCollectionEntity> manualCollectionOnDay = configYzjScreenMapper.getManualCollectionOnDay(date, instanceIdList);
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(manualCollectionOnDay));
        return ResponseData.success(jsonArray);
    }

    @Override
    @Cacheable(cacheNames = "screen_model_by_name", key = "'modelName=' + #modelName")
    public ResponseData getModleByName(String modelName) {
        LambdaQueryWrapper<ModelEntity> modelWrapper = new LambdaQueryWrapper<>();
        modelWrapper.eq(ModelEntity::getName, modelName);
        ModelEntity modelEntity = modelService.getOne(modelWrapper);
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(modelEntity));
        return ResponseData.success(jsonObject);
    }

    @Override
    @Cacheable(cacheNames = "screen_line_by_model_id", key = "'modelId=' + #modelId")
    public ResponseData listLineByModelId(Integer modelId) {
        LambdaQueryWrapper<ProductionLineEntity> lineWrapper = new LambdaQueryWrapper<>();
        lineWrapper.eq(ProductionLineEntity::getModelId, modelId);
        List<ProductionLineEntity> lineList = lineService.list(lineWrapper);
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(lineList));
        return ResponseData.success(jsonArray);
    }

    @Override
    @Cacheable(cacheNames = "screen_device_by_line_id", key = "'productionLineId=' + #productionLineId")
    public ResponseData deviceByLineId(Integer productionLineId) {
        List<DeviceEntity> deviceList = deviceService.deviceByLineId(productionLineId);
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(deviceList));
        return ResponseData.success(jsonArray);

    }

    @Override
    @Cacheable(cacheNames = "screen_work_order_list")
    public ResponseData parentWorkOrderList() {
        return ResponseData.success(workOrderService.parentWorkOrderList());
    }

    @Override
    @Cacheable(cacheNames = "screen_parent_work_order_on_day", key = "'dateString=' + #dateString")
    public ResponseData getParentWorkOrderOnDay(String dateString, List<Integer> instanceIdList) {
        List<String> nums = new ArrayList<>();
        Date date = DateUtil.parse(dateString, DateUtil.DATETIME_FORMAT);
        // 通过实例ID获取实例单号
        instanceIdList.forEach(id -> {
            WorkOrderEntity workOrderEntity = workOrderService.getById(id);
            if (Objects.nonNull(workOrderEntity)) {
                nums.add(workOrderEntity.getWorkOrderNumber());
            }
        });
        List<RecordWorkOrderDayCountEntity> parentWorkOrderOnDay = configYzjScreenMapper.getParentWorkOrderOnDay(date, nums);
        JSONArray jsonArray = JSONArray.parseArray(JSONObject.toJSONString(parentWorkOrderOnDay));
        return ResponseData.success(jsonArray);
    }

    @Override
    @Cacheable(cacheNames = "screen_grid_by_device_id", key = "'deviceId=' + #deviceId")
    public ResponseData getGridByDeviceId(Integer deviceId) {
        // 通过设备获取所属车间
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getDeviceId, deviceId);
        DeviceEntity deviceEntity = deviceService.getOne(deviceWrapper);

        LambdaQueryWrapper<FacilitiesEntity> facWrapper = new LambdaQueryWrapper<>();
        facWrapper.eq(FacilitiesEntity::getFid, deviceEntity.getFid());
        FacilitiesEntity facilitiesEntity = facilitiesService.getOne(facWrapper);

        JSONObject jsonObject = getGridJsonObject(facilitiesEntity.getGid());
        return ResponseData.success(jsonObject);
    }

    private JSONObject getGridJsonObject(Integer gid) {
        LambdaQueryWrapper<GridEntity> gridWrapper = new LambdaQueryWrapper<>();
        gridWrapper.eq(GridEntity::getGid, gid);
        GridEntity gridEntity = gridService.getOne(gridWrapper);
        return JSONObject.parseObject(JSON.toJSONString(gridEntity));
    }

    @Override
    @Cacheable(cacheNames = "screen_grid_by_work_order_num", key = "'workOrderNum=' + #workOrderNum")
    public ResponseData getGridByWorkOrderNum(String workOrderNum) {
        // 通过工单编号获取所属车间
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNum);

        LambdaQueryWrapper<ProductionLineEntity> lineWrapper = new LambdaQueryWrapper<>();
        lineWrapper.eq(ProductionLineEntity::getProductionLineId, workOrderEntity.getLineId());
        ProductionLineEntity lineEntity = lineService.getOne(lineWrapper);

        JSONObject jsonObject = getGridJsonObject(lineEntity.getGid());
        return ResponseData.success(jsonObject);
    }

    @Override
    @Cacheable(cacheNames = "screen_grid_oee")
    public ResponseData getGridOee() {
        //找到所有车间
        List<GridEntity> list = gridService.list();
        if (CollectionUtils.isEmpty(list)) {
            return ResponseData.success();
        }
        ArrayList<GridOeeDTO> gridOeeDTOS = new ArrayList<>();
        for (GridEntity gridEntity : list) {
            //找到所有产线，求出OEE
            List<ProductionLineEntity> lineEntities = lineService.getLineList(gridEntity.getGcode());
            if (CollectionUtils.isEmpty(lineEntities)) {
                continue;
            }
            List<LineOeeDTO> lineOeeDTOList = commonService.getLineOee(lineEntities, new Date());

            GridOeeDTO gridOeeDTO = GridOeeDTO.builder().gid(gridEntity.getGid()).gridName(gridEntity.getGname()).oee(lineOeeDTOList).build();
            gridOeeDTOS.add(gridOeeDTO);
        }
        return ResponseData.success(gridOeeDTOS);
    }

    @Override
    @Cacheable(cacheNames = "screen_grid_beat", key = "'timeType=' + #timeType")
    public ResponseData getGridBeat(String timeType) {
//        找出七个时间点，再去获取数据
        List<String> timeList = new ArrayList<>();
        if ("fiveMin".equals(timeType)) {
            timeList = getTimeList(5);
        }
        if ("tenMin".equals(timeType)) {
            timeList = getTimeList(10);
        }
        if ("oneHour".equals(timeType)) {
            timeList = getTimeList(60);
        }
        LambdaQueryWrapper<RecordLineBeatEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RecordLineBeatEntity::getTime, timeList).orderByAsc(RecordLineBeatEntity::getTime);
        List<RecordLineBeatEntity> list = beatService.list(wrapper);
        HashMap<String, Map<Integer, String>> gridNames;
        HashMap<Integer, List<BeatDTO>> lineBeatMap = new HashMap<>();
        List<BeatDTO> blankBeats = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            //<车间id,<产线id,产线名称>>
            gridNames = getGridNames(null);
            for (String s : timeList) {
                blankBeats.add(BeatDTO.builder().date(s).build());
            }
            blankBeats.sort(Comparator.comparing(BeatDTO::getDate));
        } else {
            SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
            Map<Integer, List<RecordLineBeatEntity>> map = list.stream().collect(Collectors.groupingBy(RecordLineBeatEntity::getLineId));
            for (Map.Entry<Integer, List<RecordLineBeatEntity>> entry : map.entrySet()) {
                List<RecordLineBeatEntity> value = entry.getValue();
                List<BeatDTO> beats = new ArrayList<>();
                for (RecordLineBeatEntity entity : value) {
                    BeatDTO build = BeatDTO.builder().date(format.format(entity.getTime())).build();
                    if ("fiveMin".equals(timeType)) {
                        build.setBeat(entity.getBeat5min());
                    }
                    if ("tenMin".equals(timeType)) {
                        build.setBeat(entity.getBeat10min());
                    }
                    if ("oneHour".equals(timeType)) {
                        build.setBeat(entity.getBeat60min());
                    }
                    beats.add(build);
                }
                lineBeatMap.put(entry.getKey(), beats);
            }
            Set<Integer> lineSet = list.stream().map(RecordLineBeatEntity::getLineId).collect(Collectors.toSet());
            gridNames = getGridNames(lineSet);
        }
        List<GridBeatDTO> gridBeats = new ArrayList<>();
        for (Map.Entry<String, Map<Integer, String>> entry : gridNames.entrySet()) {
            Map<Integer, String> value = entry.getValue();
            List<LineBeatDTO> lineBeats = new ArrayList<>();
            for (Map.Entry<Integer, String> lineEntry : value.entrySet()) {
                Integer lineId = lineEntry.getKey();
                LineBeatDTO build = LineBeatDTO.builder()
                        .lineId(lineId)
                        .lineName(lineEntry.getValue()).build();
                build.setBeats(lineBeatMap.getOrDefault(lineId, blankBeats));
                lineBeats.add(build);
            }
            gridBeats.add(GridBeatDTO.builder().gridName(entry.getKey()).lineBeats(lineBeats).build());
        }
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(gridBeats, SerializerFeature.WriteMapNullValue));
        return ResponseData.success(jsonArray);
    }

    private HashMap<String, Map<Integer, String>> getGridNames(Set<Integer> lineSet) {
        //获取产线名称
        List<ProductionLineEntity> lineEntities;
        if (CollectionUtils.isEmpty(lineSet)) {
            lineEntities = lineService.list();
        } else {
            lineEntities = lineService.listByIds(lineSet);
        }
        Map<Integer, Map<Integer, String>> collect = lineEntities.stream().collect(Collectors
                .groupingBy(ProductionLineEntity::getGid,
                        Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)));
        List<GridEntity> gridEntities = gridService.listByIds(collect.keySet());
        HashMap<String, Map<Integer, String>> map = new HashMap<>();
        for (GridEntity entity : gridEntities) {
            Integer gid = entity.getGid();
            if (collect.containsKey(gid)) {
                map.put(entity.getGname(), collect.get(gid));
            }
        }
        return map;
    }

    private List<String> getTimeList(int interval) {
        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime minutes = localDateTime.minusMinutes(localDateTime.getMinute() % interval);
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            String format = minutes.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT_MIN));
            minutes = minutes.minusMinutes(interval);
            list.add(format);
        }
        return list;
    }

    @Override
    @Cacheable(cacheNames = "screen_grid_target_data")
    public ResponseData getGridTargetData() {
        Map<String, List<GridTargetDTO>> map = new HashMap<>();
        // 获取车间列表
        Page<GridEntity> page = gridService.list(null, null, null, null, null, null, null);
        for (GridEntity gridEntity : page.getRecords()) {
            List<GridTargetDTO> list = new ArrayList<>();
            // 获取绑定到该车间的生产设备
            LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
            deviceWrapper.eq(DeviceEntity::getGid, gridEntity.getGid())
                    .eq(DeviceEntity::getTypeCode, DeviceTypeEnum.ENVIRONMENT.getCode());
            List<DeviceEntity> deviceEntities = deviceService.list(deviceWrapper);
            for (DeviceEntity deviceEntity : deviceEntities) {
                // 通过生产设备获取自动指标值
                getAutomaticIndicatorValue(list, deviceEntity);
                // 通过生产设备获取手动指标值
                getManualIndicatorValue(list, deviceEntity);
            }
            map.put(gridEntity.getGname(), list);
        }
        return ResponseData.success(map);
    }

    /**
     * 通过生产设备获取手动指标值
     */
    private void getManualIndicatorValue(List<GridTargetDTO> list, DeviceEntity deviceEntity) {
        LambdaQueryWrapper<RecordManualCollectionEntity> tbWrapper = new LambdaQueryWrapper<>();
        tbWrapper.eq(RecordManualCollectionEntity::getDeviceId, deviceEntity.getDeviceId())
                .orderByDesc(RecordManualCollectionEntity::getReportTime)
                .orderByDesc(RecordManualCollectionEntity::getId).last("limit 1");
        RecordManualCollectionEntity recordManualCollectionEntity = recordManualCollectionService.getOne(tbWrapper);
        if (recordManualCollectionEntity != null) {
            List<RecordManualCollectionDTO> recordManualCollectionDTOS = JSONArray.parseArray(recordManualCollectionEntity.getReportContent(),
                    RecordManualCollectionDTO.class);
            recordManualCollectionDTOS.forEach(recordManualCollectionDTO -> {
                GridTargetDTO build = GridTargetDTO.builder()
                        .targetCname(recordManualCollectionDTO.getName())
                        .targetName(recordManualCollectionDTO.getEname())
                        .dataVal(recordManualCollectionDTO.getValue().toString())
                        .unit(recordManualCollectionDTO.getUnit())
                        .build();
                list.add(build);
            });
        }
    }

    /**
     * 通过生产设备获取自动指标值
     */
    private void getAutomaticIndicatorValue(List<GridTargetDTO> list, DeviceEntity deviceEntity) {
        LambdaQueryWrapper<TargetModelEntity> tmWrapper = new LambdaQueryWrapper<>();
        tmWrapper.eq(TargetModelEntity::getModelId, deviceEntity.getModelId());
        List<TargetModelEntity> targetModelEntities = targetModelService.list(tmWrapper);
        targetModelEntities.forEach(targetModelEntity -> {
            IndicatorEntityDTO dto = commonService.getLatestTargetValue(deviceEntity.getDeviceId(), targetModelEntity.getTargetName());
            String dataVal = dto == null ? null : dto.getDataVal();
            // 获取单位
            LambdaQueryWrapper<TargetDictEntity> dictWrapper = new LambdaQueryWrapper<>();
            dictWrapper.eq(TargetDictEntity::getTargetName, targetModelEntity.getTargetName()).last("limit 1");
            TargetDictEntity dictEntity = targetDictService.getOne(dictWrapper);
            GridTargetDTO build = GridTargetDTO.builder()
                    .targetCname(targetModelEntity.getTargetCnname())
                    .targetName(targetModelEntity.getTargetName())
                    .dataVal(dataVal)
                    .unit(dictEntity == null ? null : dictEntity.getUnit())
                    .build();
            list.add(build);
        });
    }

    @Override
    @Cacheable(cacheNames = "screen_grid_day_count_by_line", key = "'dateStr=' + #dateStr")
    public ResponseData getGridDayCountByLine(String dateStr) {
        List<UnqualifiedFinishDTO> list = new ArrayList<>();
        // 获取车间列表
        Page<GridEntity> gridPage = gridService.list(null, null, null, null, null, null, null);
        for (GridEntity gridEntity : gridPage.getRecords()) {
            // 计算车间的不合格数、合格数（产线总和）
            double qualifyCount = 0;
            double unqualifiedCount = 0;
            // 获取产线列表
            Page<ProductionLineEntity> linePage = lineService.lineByGid(gridEntity.getGid(), 1, Integer.MAX_VALUE);
            if (linePage == null) {
                UnqualifiedFinishDTO finishDTO = UnqualifiedFinishDTO.builder()
                        .finishCount(null)
                        .unqualified(null)
                        .qualified(null)
                        .gridName(gridEntity.getGname())
                        .build();
                list.add(finishDTO);
            } else {
                List<Integer> lineIds = linePage.getRecords().stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
                for (Integer lineId : lineIds) {
                    // 每条产线,一个工单只有一条记录
                    List<RecordWorkOrderLineDayCountEntity> dayCountEntities;
                    if (workCenterService.isOperationByLineId(lineId)) {
                        //作业工单需要查询作业工单模块
                        dayCountEntities = commonService.getWorkOrderDayCountListByOperation(DateUtil.parse(dateStr, DateUtil.DATE_FORMAT), lineId);

                    } else {
                        LambdaQueryWrapper<RecordWorkOrderLineDayCountEntity> qw = new LambdaQueryWrapper<>();
                        qw.eq(RecordWorkOrderLineDayCountEntity::getLineId, lineId)
                                .eq(RecordWorkOrderLineDayCountEntity::getTime, dateStr);
                        dayCountEntities = recordWorkOrderLineDayCountService.list(qw);
                    }

                    if (!CollectionUtils.isEmpty(dayCountEntities)) {
                        double unqualified = dayCountEntities.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
                        double qualified = dayCountEntities.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
                        qualifyCount += qualified;
                        unqualifiedCount += unqualified;
                    }
                    //今天的数量需要加上计数器的数量
                    /*if (dateStr.equals(nowDateStr)) {
                        qualifyCount += commonService.getAutoCountByLineAndRecord(productionLineMapper.selectById(lineId));
                    }*/
                }
                // 总数
                double count = qualifyCount + unqualifiedCount;
                UnqualifiedFinishDTO finishDTO = UnqualifiedFinishDTO.builder()
                        .finishCount(count)
                        .unqualified(unqualifiedCount)
                        .qualified(qualifyCount)
                        .gridName(gridEntity.getGname())
                        .build();
                list.add(finishDTO);
            }
        }
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(list));
        return ResponseData.success(jsonArray);
    }


    @Override
    @Cacheable(cacheNames = "screen_input_output_today", key = "'lineIds=' + #lineIds")
    public ResponseData getInputOutputToday(String lineIds) {
        Date now = new Date();
        //当天工厂开工时间到现在经过的小时集合
        Date start = DateUtil.formatToDate(dictService.getDayOutputBeginTime(now), DateUtil.DATETIME_FORMAT_HOUR);
        Date startHour = DateUtil.formatToDate(start, DateUtil.DATETIME_FORMAT_HOUR);
        Date endHour = DateUtil.formatToDate(now, DateUtil.DATETIME_FORMAT_HOUR);
        List<String> betweenHours = DateUtil.getBetweenHours(startHour, now);

        List<Integer> lineIdList = StringUtils.isEmpty(lineIds) ? null : Arrays.stream(lineIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        //获取当天的投入量、产出量
        List<RecordLineInputOutputOrderHourEntity> inputOutputList = recordLineInputOutputOrderHourService.getInputOutput(null, lineIdList, startHour, endHour);
        List<TodayInputAndOutputVO> result = getTodayInputAndOutputByList(lineIdList, betweenHours, inputOutputList);

        return ResponseData.success(result);
    }

    private List<TodayInputAndOutputVO> getTodayInputAndOutputByList(List<Integer> lineIds, List<String> betweenHours, List<RecordLineInputOutputOrderHourEntity> inputOutputList) {
        Map<String, TodayInputAndOutputVO> vosMap = new HashMap<>(32);

        if (!CollectionUtils.isEmpty(inputOutputList)) {
            //按时间分组
            Map<String, List<RecordLineInputOutputOrderHourEntity>> collect = inputOutputList.stream().collect(Collectors.groupingBy(o -> DateUtil.format(o.getTime(), DateUtil.DATETIME_FORMAT_HOUR)));
            for (String hour : betweenHours) {
                List<RecordLineInputOutputOrderHourEntity> recordLineInputOutputOrderHourEntities = collect.get(hour);
                double inputTotal = 0.0;
                double outputTotal = 0.0;
                if (!CollectionUtils.isEmpty(recordLineInputOutputOrderHourEntities)) {
                    inputTotal = recordLineInputOutputOrderHourEntities.stream().mapToDouble(RecordLineInputOutputOrderHourEntity::getInput).sum();
                    outputTotal = recordLineInputOutputOrderHourEntities.stream().mapToDouble(RecordLineInputOutputOrderHourEntity::getOutput).sum();
                }

                TodayInputAndOutputVO build = TodayInputAndOutputVO.builder().time(hour).input(inputTotal).output(outputTotal).build();
                vosMap.put(hour, build);
            }
        } else {
            //如果当天没有数据 则显示0
            for (int i = 1; i < betweenHours.size(); i++) {
                vosMap.put(betweenHours.get(i), TodayInputAndOutputVO.builder().time(betweenHours.get(i)).input(0.0).output(0.0).build());
            }
        }
        List<TodayInputAndOutputVO> result = vosMap.values().stream().sorted(Comparator.comparing(o -> DateUtil.parse(o.getTime(), DateUtil.DATETIME_FORMAT_HOUR))).collect(Collectors.toList());

        double referenceValue = targetThresholdService.getLineReferenceValue(lineIds, TargetConst.LINE_INPUT);
        double inProductionQuantity = 0.0;
        for (TodayInputAndOutputVO todayInputAndOutputVO : result) {
            //计算每小时的在制数(在制数= 累加的投入量减去产量的差值)
            inProductionQuantity += (todayInputAndOutputVO.getInput() - todayInputAndOutputVO.getOutput());
            todayInputAndOutputVO.setInProductionQuantity(inProductionQuantity);
            todayInputAndOutputVO.setStandardValue(referenceValue);
            todayInputAndOutputVO.setTime(DateUtil.parseToString(todayInputAndOutputVO.getTime(), DateUtil.DATETIME_FORMAT_HOUR, DateUtil.HOUR_ZERO_FORMAT));
        }
        return result;
    }

    /*private RecordLineInputOutputOrderEntity getTempLineInputOutput(Integer lineId, String workOrderNumber, WorkOrderEntity one, String hour, Date parseHour) {
        //先从缓存取值
        String key = RedisKeyPrefix.getScreenInputOutput(lineId, parseHour.getTime(), workOrderNumber);
        RecordLineInputOutputOrderEntity tempEntity = JSONObject.parseObject(JSON.toJSONString(redisTemplate.opsForValue().get(key)), RecordLineInputOutputOrderEntity.class);
        if (tempEntity == null) {
            tempEntity = inputOutputOrderService.lambdaQuery()
                    .eq(RecordLineInputOutputOrderEntity::getWorkOrder, workOrderNumber)
                    .between(RecordLineInputOutputOrderEntity::getTime, one.getActualStartDate(), parseHour)
                    .orderByDesc(RecordLineInputOutputOrderEntity::getTime).last("limit 1").one();
            tempEntity = tempEntity != null ? tempEntity : RecordLineInputOutputOrderEntity.builder().input(0.0).output(0.0).build();
            redisTemplate.opsForValue().set(key, tempEntity, 24, TimeUnit.HOURS);
        }
        return tempEntity;
    }*/

    @Override
    @Cacheable(cacheNames = "screen_work_order_remarks", key = "'lineId=' + #lineId")
    public ResponseData getWorkOrderRemarks(Integer lineId, Integer current, Integer size) {
        List<Integer> lineIds = Stream.of(lineId).collect(Collectors.toList());
        LambdaQueryWrapper<WorkOrderEntity> wrapper = productionService.getQueryWrapper(lineIds, null);
        wrapper.isNotNull(WorkOrderEntity::getRemark);
        List<WorkOrderEntity> collect = workOrderService.list(wrapper);
        //产线看板里工单备注信息增加一列展示工单事件。取【事件记录】里有记录该工单的最新的“工单事件”的”备注”信息（有就展示，没有就空着）
        //“工单事件”是用户定义的名称,不一定会有,现逻辑改成事件明细中包含工单号，且还有备注的，就取备注的值。
        //王祥的需求,1.8.12版本 --2022年2月10日
        List<String> orders = collect.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        Map<String, String> remarkMap = eventRecordService.getRemarksByWorkOrders(orders);
        List<String> materialCodes = collect.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toList());
        Map<String, String> materialNameMap = materialService.getMaterialCodeNameMap(materialCodes);
        // 简化出参
        List<WorkOrderRemarkScrDTO> list = collect.stream().map(o -> WorkOrderRemarkScrDTO.builder()
                .workOrderEvent(remarkMap.get(o.getWorkOrderNumber()))
                .materialName(materialNameMap.get(o.getMaterialCode()))
                .workOrderNumber(o.getWorkOrderNumber())
                .remark(o.getRemark())
                .build()).collect(Collectors.toList());
        return ResponseData.success(list);
    }

    /**
     * 维修项目top5
     * 通过产线下的工位查询当天的不良——》通过产线下的工单去查询当天的不良
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_product_maintenance", key = "'lineId=' + #lineId + ',type=' +  #type")
    public ResponseData getProductMaintenance(Integer lineId, String type) {
        boolean include = EnumUtil.isInclude(MaintainSchemeTypeEnum.class, type);
        if (include) {
            //得到相对应的维修方案类型（成品、半成品）
            List<MaintainRecordEntity> maintainRecordEntities = delMaintainRecord(lineId, type);
            //统计维修项目
            Map<String, Long> maintainNameMap = maintainRecordEntities.stream()
                    .collect(Collectors.groupingBy(MaintainRecordEntity::getMaintainName, Collectors.counting()));
            //转换并排序
            List<MaintenanceHistogramVO> list = maintainNameMap.entrySet().stream()
                    .limit(5)
                    .map(e -> new MaintenanceHistogramVO(e.getKey(), e.getValue()))
                    .sorted(Comparator.comparing(MaintenanceHistogramVO::getNum))
                    .collect(Collectors.toList());
            return ResponseData.success(list);
        }
        return ResponseData.success(null);
    }

    /**
     * 获取改产线下所有成品半成品的维修记录并分组
     *
     * @param lineId
     * @return
     */
    private List<MaintainRecordEntity> delMaintainRecord(Integer lineId, String type) {
        //获取当天产线下的维修记录
        List<MaintainRecordEntity> recordEntities = maintainRecordService.getTodayMaintainByLine(lineId);
        if (CollectionUtils.isEmpty(recordEntities)) {
            return new ArrayList<>();
        }
        List<MaintainRecordEntity> list = recordEntities.stream().filter(entity -> {
            //通过维修方案找到该维修记录的维修方案类型
            Integer schemeId = entity.getSchemeId();
            if (schemeId == null) {
                return false;
            }
            MaintainSchemeEntity maintainSchemeEntity = maintainSchemeService.getById(schemeId);
            //得到相对应的维修方案类型
            if (maintainSchemeEntity == null) {
                return false;
            }
            String schemeType = maintainSchemeEntity.getSchemeType();
            return schemeType.equals(type);
        }).collect(Collectors.toList());
        //需根据条码号、维修项目去重
        return list.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                        tc -> tc.getMaintainId() + ";" + tc.getBarCode()))), ArrayList::new));
    }


    /**
     * 工位不良数据
     * 需根据工位、条码去重
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_poor_station_data", key = "'lineId=' + #lineId")
    public ResponseData getPoorStationData(Integer lineId) {
        List<PoorStationVO> voList = new ArrayList<>();
        //获取当天产线下的维修记录
        Date now = new Date();
        Date startTime = dictService.getDayOutputBeginTime(now);
        List<Map<String, Object>> maps = maintainRecordMapper.getPoorStationData(lineId, startTime, now);
        for (Map<String, Object> map : maps) {
            int fid = Integer.parseInt(map.get(getField(PoorStationVO::getFid)).toString());
            int num = Integer.parseInt(map.get(getField(PoorStationVO::getNum)).toString());
            FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
            if (Objects.isNull(facilitiesEntity)) {
                continue;
            }
            voList.add(PoorStationVO.builder().fid(fid).fname(facilitiesEntity.getFname()).num(num).build());
        }
        return ResponseData.success(voList);
    }

    /**
     * 近一周产量&产率
     *
     * @return 算法：
     * 产量：该产线下工单每天上报的产量（默认需要近一周的数据）
     * 直通率 = （产量-（当前产线下所有质检工位上报的不良品数量））/产量
     */
    @Override
    public ResponseData getYieldRecord(Integer lineId, Integer showDay) {
        // 默认显示7天的数据
        showDay = Objects.isNull(showDay) ? 7 : showDay;
        return ResponseData.success(productionService.getYieldRecord(lineId, new Date(), showDay));
    }

    /**
     * 状态总览
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_order_information", key = "'lineId=' + #lineId")
    public ResponseData workOrderInformation(Integer lineId) {
        StateOverviewDTO stateOverviewDTO = productionService.workOrderInformation(String.valueOf(lineId), null);
        return ResponseData.success(stateOverviewDTO);
    }

    /**
     * 生产计划与进度（产线大屏）
     *
     * @param lineId
     * @return
     */
    @Override
    @Cacheable(cacheNames = "screen_work_order", key = "'lineId=' + #lineId")
    public ResponseData listWorkOrder(Integer lineId, Integer current, Integer size) {
        Date time = new Date();
        Date date = dictService.getDayOutputBeginTime(time);
        List<Integer> lineIds = Stream.of(lineId).collect(Collectors.toList());
        Page<WorkOrderEntity> page = productionService.getWorkOrderInWork(lineIds, null, current, size);
        // 生产中数量（投产）、已完成数量（完成）、待生产数量（生效、挂起）
        LambdaQueryWrapper<WorkOrderEntity> investWrapper = productionService.getQueryWrapper(lineIds, time);
        investWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode());
        long inProduction = workOrderService.count(investWrapper);

        LambdaQueryWrapper<WorkOrderEntity> finishWrapper = productionService.getQueryWrapper(lineIds, time);
        finishWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode());
        long completed = workOrderService.count(finishWrapper);

        LambdaQueryWrapper<WorkOrderEntity> toBeProcedureWrapper = productionService.getQueryWrapper(lineIds, time);
        toBeProcedureWrapper.in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
        long toBeProduced = workOrderService.count(toBeProcedureWrapper);

        List<WorkOrderLineDTO> rets = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : page.getRecords()) {
//            // 统计状态数量
//            if (WorkOrderStateEnum.INVESTMENT.getCode().equals(workOrderEntity.getState())) {
//                inProduction++;
//            } else if (WorkOrderStateEnum.FINISHED.getCode().equals(workOrderEntity.getState())) {
//                completed++;
//            } else {
//                toBeProduced++;
//            }
            //之前完成的不要统计
            if (workOrderEntity.getActualEndDate() != null && workOrderEntity.getActualEndDate().compareTo(date) < 0) {
                continue;
            }
            recordWorkOrderStateService.setProduceRange(workOrderEntity);
            rets.add(productionService.convertWoreOrderEntity(workOrderEntity));
        }
        rets.sort(Comparator.comparing(WorkOrderLineDTO::getLineId));

        WorkOrderListDTO build = WorkOrderListDTO.builder()
                .workOrderForLineScreen(rets)
                .completed(completed)
                .toBeProduced(toBeProduced)
                .inProduction(inProduction)
                .current(page.getCurrent())
                .size(page.getSize())
                .total(page.getTotal())
                .build();

        return ResponseData.success(build);
    }

    /**
     * 告警汇总
     */
    @Override
    @Cacheable(cacheNames = "screen_alarm_list", key = "'lineIds=' + #lineIds + ',alarmTypes=' +  #alarmTypes")
    public ResponseData getAlarmList(String lineIds, String alarmTypes) {
        //找到该产线下各个告警类型的数量
        ProductionAlarmDTO productionAlarmDTO = productionService.getAlarmCount(lineIds, alarmTypes);
        return ResponseData.success(productionAlarmDTO);
    }

    @Override
    @Cacheable(cacheNames = "screen_line_detail", key = "'lineId=' + #lineId")
    public ResponseData getLineDetail(Integer lineId) {
        //获取产线名称
        ProductionLineEntity lineEntity = lineService.getById(lineId);
        if (lineEntity != null) {
            // 计算该产线当天的出勤人数
            //int num = statisticsService.getNumberOfWorkers(lineId);
            LineDetailVO build = LineDetailVO.builder()
                    .lineId(lineId)
                    .lineName(lineEntity.getName())
                    .attendance(lineEntity.getAttendance() != null ? lineEntity.getAttendance() : 0)
                    .build();
            return ResponseData.success(build);
        }
        return ResponseData.success(null);
    }

    @Override
    @Cacheable(cacheNames = "screen_line_list")
    public ResponseData getLineList() {
        return ResponseData.success(productionLineMapper.selectList(null));
    }

    @Override
    @Cacheable(cacheNames = "screen_grid_list")
    public ResponseData getGridList() {
        return ResponseData.success(gridService.list());
    }

    @Override
    @Cacheable(cacheNames = "screen_layout")
    public ResponseData getLayoutUrl() {
        return ResponseData.success(companyService.detail().getLayoutUrl());
    }

    @Override
    @Cacheable(cacheNames = "screen_input_output_today_by_lines", key = "'lineIds=' + #lineIds + ',gId=' +  #gId")
    public ResponseData getInputOutputTodayByLines(String lineIds, Integer gId) {
        List<ProductionLineEntity> productionLineEntities;
        //如果产线为空则使用车间下所有产线
        if (StringUtils.isEmpty(lineIds)) {
            productionLineEntities = productionLineService.listLineByGid(gId);
            if (CollectionUtils.isEmpty(productionLineEntities)) {
                throw new ResponseException("所选车间产线不能为空");
            }
            List<Integer> collect1 = productionLineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            lineIds = Joiner.on(Constant.SEP).join(collect1);
        }
        List<Integer> linesId = Arrays.stream(lineIds.split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        Date now = new Date();
        //当天工厂开工时间到现在经过的小时集合
        Date start = DateUtil.formatToDate(dictService.getDayOutputBeginTime(now), DateUtil.DATETIME_FORMAT_HOUR);
        Date startHour = DateUtil.formatToDate(start, DateUtil.DATETIME_FORMAT_HOUR);
        Date endHour = DateUtil.formatToDate(now, DateUtil.DATETIME_FORMAT_HOUR);
        List<String> betweenHours = DateUtil.getBetweenHours(startHour, endHour);

        //获取当天的投入量、产出量
        List<RecordLineInputOutputOrderHourEntity> inputOutputList = recordLineInputOutputOrderHourService.getInputOutput(null, linesId, startHour, endHour);

        List<TodayInputAndOutputVO> result = getTodayInputAndOutputByList(linesId, betweenHours, inputOutputList);

        return ResponseData.success(result);
    }


}
