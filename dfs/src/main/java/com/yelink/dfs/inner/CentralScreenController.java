package com.yelink.dfs.inner;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.constant.order.WorkOrderCirculationStateEnum;
import com.yelink.dfs.constant.product.TimeoutThresholdTypeEnum;
import com.yelink.dfs.constant.screen.TimeRangeEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.attendance.AttendanceEntity;
import com.yelink.dfs.entity.capacity.CapacityEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.defect.DefectDefineEntity;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.ShowExtraNameDTO;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureControllerConfigEntity;
import com.yelink.dfs.entity.screen.dto.CapacityDTO;
import com.yelink.dfs.entity.screen.dto.MaterialOutputDTO;
import com.yelink.dfs.entity.screen.dto.StateOverviewDTO;
import com.yelink.dfs.entity.screen.vo.AlarmDeviceVO;
import com.yelink.dfs.entity.screen.vo.AlarmFacilityVO;
import com.yelink.dfs.entity.screen.vo.AlarmLineVO;
import com.yelink.dfs.entity.screen.vo.FacProductDetailVO;
import com.yelink.dfs.entity.screen.vo.InspectSheetDayPassVO;
import com.yelink.dfs.entity.screen.vo.InspectSheetDefectVO;
import com.yelink.dfs.entity.screen.vo.InspectSheetPassVO;
import com.yelink.dfs.entity.screen.vo.PlanProgressCraftDetailVO;
import com.yelink.dfs.entity.screen.vo.PlanProgressOrderVO;
import com.yelink.dfs.entity.screen.vo.PlanProgressWorkOrderVO;
import com.yelink.dfs.entity.screen.vo.ProductionEfficiencyVO;
import com.yelink.dfs.entity.screen.vo.ScheduleCompletionRateVO;
import com.yelink.dfs.entity.screen.vo.TodayUnqualifiedTimeVO;
import com.yelink.dfs.entity.screen.vo.TodayWokOrderPassRateVO;
import com.yelink.dfs.entity.screen.vo.UnqualifiedTimeVO;
import com.yelink.dfs.entity.screen.vo.UnqualifiedVO;
import com.yelink.dfs.entity.screen.vo.WorkOrderProcedureCount2VO;
import com.yelink.dfs.entity.screen.vo.WorkOrderProcedureCountVO;
import com.yelink.dfs.entity.screen.vo.WorkOrderStatisticsVO;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderEntity;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderProcedureEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.attendance.AttendanceService;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.datax.DataxProductOrderService;
import com.yelink.dfs.service.defect.DefectDefineService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureControllerConfigService;
import com.yelink.dfs.service.screen.ProductionService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderProcedureService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfscommon.api.ams.SubcontractOrderInterface;
import com.yelink.dfscommon.api.dfs.CentralScreenInterface;
import com.yelink.dfscommon.api.qms.InspectionSheetDefectRecordInterface;
import com.yelink.dfscommon.api.qms.InspectionSheetInterface;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.OrderShowTypeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.ams.SubcontractOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.constant.wms.StockInputOrOutputTypeEnum;
import com.yelink.dfscommon.constant.wms.StockOrderStateEnum;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.subcontract.SubcontractOrderSelectDTO;
import com.yelink.dfscommon.dto.qms.InspectionSheetScreenDTO;
import com.yelink.dfscommon.dto.screen.WorkOrderProcedureCountDTO;
import com.yelink.dfscommon.dto.wms.StockInAndOutSelectDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.SubcontractOrderDetailEntity;
import com.yelink.dfscommon.entity.ams.SubcontractOrderEntity;
import com.yelink.dfscommon.entity.qms.InspectionSheetDefectRecordEntity;
import com.yelink.dfscommon.entity.qms.InspectionSheetEntity;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.dfscommon.utils.PageData;
import com.yelink.dfscommon.utils.PageUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.time.LocalDate.now;

/**
 * 中央大屏调用统一接口
 *
 * <AUTHOR>
 * @Date 2022/5/26 19:00
 */
@AllArgsConstructor
@RestController
@Slf4j
public class CentralScreenController implements CentralScreenInterface {
    private final DictService dictService;
    private final ProductionLineService lineService;
    private final WorkOrderService workOrderService;
    private final WorkOrderPlanService workOrderPlanService;
    private final RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    private final StockInAndOutInterface stockInAndOutInterface;
    private final ProductionService productionService;
    private final OrderWorkOrderService orderWorkOrderService;
    private final MaterialService materialService;
    private final CraftProcedureService craftProcedureService;
    private final WorkOrderProcedureRelationService workOrderProcedureRelationService;
    private final ProcedureControllerConfigService procedureControllerConfigService;
    private final MaintainRecordService maintainRecordService;
    private final InspectionSheetInterface inspectionSheetInterface;
    private final DefectDefineService defectDefineService;
    private final InspectionSheetDefectRecordInterface inspectionSheetDefectRecordInterface;
    private final FacilitiesService facilitiesService;
    private final ProductFlowCodeRecordService productFlowCodeRecordService;
    private final ExtProductOrderInterface extProductOrderInterface;
    private final AttendanceService attendanceService;
    private final CapacityService capacityService;
    private final RecordWorkOrderUnqualifiedService recordWorkOrderUnqualifiedService;
    private final ReportLineService reportLineService;
    private final MetricsWorkOrderService metricsWorkOrderService;
    private final AlarmService alarmService;
    private final RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService;
    private final ProductionLineService productionLineService;
    private final SubcontractOrderInterface subcontractOrderInterface;

    private final DataxProductOrderService dataxProductOrderService;

    private final MetricsWorkOrderProcedureService metricsWorkOrderProcedureService;


    @Override
    public ResponseData workOrderScheduleCompletionRate(Integer gridId) {
        ScheduleCompletionRateVO build = this.workOrderScheduleCompletion(gridId);
        return ResponseData.success(build);
    }

    /**
     * 订单计划数、订单入库数、计划完成率、累计不良率
     * 今日计划数、今日入库数、今日完成率、今日不良率
     * 计划完成率，在指定的时间内把指定对象所有工单的实际产出之和除以所有工单的计划产出之和（不含关闭的工单）
     * 累计不良率，不含关闭的工单，取工单算数平均值
     * 订单计划数，不含关闭的工单
     * 订单入库数，不含关闭的工单
     */
    @Override
    public ResponseData scheduleCompletionRate() {
        ScheduleCompletionRateVO build = this.workOrderScheduleCompletion(null);

        // 订单入库数
        // 查询 工单成品入库 的单
        int allStockAmount = 0, todayStockAmount = 0;
        List<StockInAndOutEntity> stockInAndOuts = JacksonUtil.getResponseArray(stockInAndOutInterface.getInputList(StockInAndOutSelectDTO
                .builder()
                .inOrOutType(StockInputOrOutputTypeEnum.INPUT_WORK_ORDER_COMPLETE.getTypeCode())
                .showType(OrderShowTypeEnum.ORDER.getType())
                .state(String.valueOf(StockOrderStateEnum.FINISHED.getCode()))
                .build(), null), StockInAndOutEntity.class);

        List<String> stockRecordOrderNumbers = stockInAndOuts.stream().map(StockInAndOutEntity::getOrderNumber).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(stockRecordOrderNumbers)) {
            List<StockMaterialDetailEntity> stockMaterialDetails = JacksonUtil.getResponseArray(stockInAndOutInterface.getRelatedMaterialList(stockRecordOrderNumbers), StockMaterialDetailEntity.class);
            allStockAmount = (int) stockMaterialDetails.stream().filter(res -> Objects.nonNull(res.getActualAmount())).mapToDouble(StockMaterialDetailEntity::getActualAmount).sum();
            // 今日入库数
            // yyyyMMdd
            String nowDateFormat = DateUtil.format(new Date(), DateUtil.DATE_FORMAT_SHORT);
            // 过滤出今天插入的记录
            List<StockMaterialDetailEntity> todayStockMaterialDetails = stockMaterialDetails.stream().filter(record -> nowDateFormat.equals(DateUtil.format(record.getCreateTime(), DateUtil.DATE_FORMAT_SHORT))).collect(Collectors.toList());
            todayStockAmount = (int) todayStockMaterialDetails.stream().filter(res -> Objects.nonNull(res.getActualAmount())).mapToDouble(StockMaterialDetailEntity::getActualAmount).sum();
        }
        build.setAllStockAmount(allStockAmount);
        build.setTodayStockAmount(todayStockAmount);

        return ResponseData.success(build);
    }

    /**
     * 计划与完成率
     * 订单计划数、订单完成数、计划完成率、累计不良率
     * 今日计划数、今日完成数、今日完成率、今日不良率
     */
    private ScheduleCompletionRateVO workOrderScheduleCompletion(Integer gridId) {
        // 所有产线
        List<Integer> lineIds = null;
        if (Objects.nonNull(gridId)) {
            lineIds = lineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId)
                    .eq(ProductionLineEntity::getGid, gridId)
                    .list().stream()
                    .map(ProductionLineEntity::getProductionLineId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lineIds)) {
                return ScheduleCompletionRateVO.builder().allPlanQuantity(0.0).allCompleted(0).allCompletionRate(0.0).allUnqualified(0).allUnqualifiedRate(0.0).todayPlanQuantity(0.0).todayCompleted(0).todayCompletionRate(0.0).todayUnqualified(0).todayUnqualifiedRate(0.0).build();
            }
        }

        // 首班时间 yyyy-MM-dd 00:00:00
        Date date = dictService.getRecordDate(new Date());
        // 1.完成数(不含关闭的工单)
        int allCompleted = 0, totalUnqualified = 0, todayCompleted = 0, todayUnqualified = 0;
        // 其中包含的 未关闭
        List<String> tempWorkOrderNumbers = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber)
                .notIn(WorkOrderEntity::getState, Stream.of(WorkOrderStateEnum.CLOSED.getCode(), WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.CANCELED.getCode()).collect(Collectors.toList()))
                .list()
                .stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tempWorkOrderNumbers)) {
            List<RecordWorkOrderLineDayCountEntity> tempAllRecords = recordWorkOrderLineDayCountService.lambdaQuery()
                    .in(RecordWorkOrderLineDayCountEntity::getWorkOrderNumber, tempWorkOrderNumbers)
                    .in(!CollectionUtils.isEmpty(lineIds), RecordWorkOrderLineDayCountEntity::getLineId, lineIds)
                    .list();
            allCompleted = (int) tempAllRecords.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            totalUnqualified = (int) tempAllRecords.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();

            // 今日订单完成数
            List<RecordWorkOrderLineDayCountEntity> tempTodayRecords = tempAllRecords.stream().filter(record -> record.getTime().equals(date)).collect(Collectors.toList());
            todayCompleted = (int) tempTodayRecords.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            todayUnqualified = (int) tempTodayRecords.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
        }

        // 2.订单计划数(不含关闭、创建、取消的工单)：总的订单计划数
        double allPlanQuantity = getTotalPlanQuantity(lineIds);

        // 今日订单计划数(不含关闭、创建、取消的工单)
        double todayPlanQuantity = getTodayPlanQuantity(lineIds, date);

        // 3.计划完成率(不含关闭的工单)：总的计划完成率 = 完成数 / 计划数
        double allCompletionRate = allPlanQuantity == 0 ? 1.0 : MathUtil.divideDouble(allCompleted, allPlanQuantity, 4);
        double todayCompletionRate = todayPlanQuantity == 0 ? 1.0 : MathUtil.divideDouble(todayCompleted, todayPlanQuantity, 4);

        // 3.累计不良率(不含关闭的工单，取工单算数平均值)：不良率 = 不良数 / (完成数 + 不良数)
        double allUnqualifiedRate = allCompleted == 0 ? 0 : MathUtil.divideDouble(totalUnqualified, allCompleted + totalUnqualified, 4);
        double todayUnqualifiedRate = todayCompleted == 0 ? 0 : MathUtil.divideDouble(todayUnqualified, todayCompleted + todayUnqualified, 4);

        return ScheduleCompletionRateVO.builder()
                .allPlanQuantity(allPlanQuantity)
                .allCompleted(allCompleted)
                .allCompletionRate(allCompletionRate)
                .allUnqualified(totalUnqualified)
                .allUnqualifiedRate(allUnqualifiedRate)
                .todayPlanQuantity(todayPlanQuantity)
                .todayCompleted(todayCompleted)
                .todayCompletionRate(todayCompletionRate)
                .todayUnqualified(todayUnqualified)
                .todayUnqualifiedRate(todayUnqualifiedRate)
                .build();
    }

    @Override
    public ResponseData statusOverview() {
        // 1.订单完成率（生产订单）
        // 完成订单数：独立订单 + 父订单
        long orderCompletedCount = countOrderByState(OrderStateEnum.FINISHED.getCode());
        // 所有生产订单数（不含关闭，创建状态订单,即生效 + 完成）：独立订单 + 父订单 :
        long orderCount = countOrderByState(OrderStateEnum.RELEASED.getCode()) + orderCompletedCount;

        // 订单完成率
        CapacityDTO orderCompletedRate = CapacityDTO.builder()
                .gross((int) orderCount)
                .completion((int) orderCompletedCount)
                .yieldRate(orderCount == 0 ? 1.0 : MathUtil.divideDouble(orderCompletedCount, orderCount, 4))
                .build();

        // 2.今日产量完成率 计划生产数量，已生产数量
        StateOverviewDTO stateOverviewDTO = productionService.workOrderInformation(null, null);
        CapacityDTO dayReachDTO = stateOverviewDTO.getDayReachDTO();

        // 3.今日合格率
        CapacityDTO yieldDTO = stateOverviewDTO.getYieldDTO();

        // 4.今日产出数  今日完成的所有工单的良品数
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT);
        String start = LocalDateTime.of(now, LocalTime.MIN).format(formatter);
        String end = LocalDateTime.of(now, LocalTime.MAX).format(formatter);
        List<RecordWorkOrderDayCountEntity> countByDays = recordWorkOrderDayCountService.getDayCountByDays(start, end);
        double dayOutputCount = 0;
        for (RecordWorkOrderDayCountEntity countByDay : countByDays) {
            dayOutputCount = MathUtil.add(dayOutputCount, countByDay.getCount());
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderCompletedRate", orderCompletedRate);
        jsonObject.put("yieldCompletionRate", dayReachDTO);
        jsonObject.put("passRate", yieldDTO);
        jsonObject.put("dayOutputCount", dayOutputCount);
        return ResponseData.success(jsonObject);
    }

    @Override
    public ResponseData listProductionOrder(Integer current, Integer size, String gridCode, Boolean filterErr) {
        List<String> filterGridCodes = StringUtils.isNotEmpty(gridCode) ? Arrays.stream(gridCode.split(Constants.SEP)).collect(Collectors.toList()) : null;
        // 查询生效、完成的生产订单
        List<ProductOrderEntity> records = dataxProductOrderService.getNecessaryList(Arrays.asList(OrderStateEnum.RELEASED.getCode(), OrderStateEnum.FINISHED.getCode()));
        if (CollectionUtils.isEmpty(records)) {
            // 空数据返回结构不一致导致前端报错
            PageData<PlanProgressOrderVO> pageData = new PageData<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("releasedCount", 0);
            jsonObject.put("finishCount", 0);
            jsonObject.put("pageData", pageData);
            jsonObject.put("abnormalCount", 0);
            return ResponseData.success(jsonObject);
        }
        // 查询生产订单关联的生效、完成委外订单
        List<String> productOrderNumbers = records.stream().map(ProductOrderEntity::getProductOrderNumber).collect(Collectors.toList());
        SubcontractOrderSelectDTO subcontractOrderSelect = SubcontractOrderSelectDTO.builder()
                .showType(ShowTypeEnum.MATERIAL.getType())
                .isShowMaterialFields(false)
                .productOrderNumbers(productOrderNumbers)
                .states(SubcontractOrderStateEnum.RELEASED.getCode() + Constant.SEP + SubcontractOrderStateEnum.FINISHED.getCode())
                .build();
        subcontractOrderSelect.setCurrent(1);
        subcontractOrderSelect.setSize(Integer.MAX_VALUE);
        List<SubcontractOrderEntity> subcontractOrders = JacksonUtil.getResponsePage(subcontractOrderInterface.getList(subcontractOrderSelect), SubcontractOrderEntity.class).getRecords();
        Map<String, SubcontractOrderEntity> subcontractOrderEntityMap = subcontractOrders.stream().collect(Collectors.toMap(SubcontractOrderEntity::getSubcontractOrderNumber, v -> v, (n, o) -> n));
        Map<String, List<SubcontractOrderEntity>> orderNumberSubcontractOrdersMap = subcontractOrders.stream().collect(Collectors.groupingBy(SubcontractOrderEntity::getProductOrderNumber));

        Date now = new Date();
        //当天工厂开工时间到现在经过的小时集合
        Date start = DateUtil.formatToDate(now, DateUtil.DATETIME_FORMAT_ZERO);

        // 循环生产订单列表，组装对象返回
        //饶艳生需求，生效数和完成数同列表展示一致
        int releasedCount = 0;
        int finishCount = 0;
        int abnormalCount = 0;
        List<ProductOrderEntity> filterOrders = new ArrayList<>();
        for (ProductOrderEntity productOrder : records) {
            //过滤今天之前完成的订单 2.13饶燕生（状态是完成，但没有实际完成时间）
            if (productOrder.getState() == OrderStateEnum.FINISHED.getCode()) {
                if (productOrder.getProductOrderMaterial().getActualProductEndTime() == null || productOrder.getProductOrderMaterial().getActualProductEndTime().before(start)) {
                    continue;
                }
            }
            // 不显示计划数量为0的生产订单
            Double planQuantity = productOrder.getProductOrderMaterial().getPlanQuantity();
            if (planQuantity == null || planQuantity.equals(0.0)) {
                continue;
            }
            // 不显示排产数量为0的生产订单
            Double scheduledProductionQuantity = productOrder.getProductOrderMaterial().getScheduledProductionQuantity();
            if (scheduledProductionQuantity == null || scheduledProductionQuantity.equals(0.0000)) {
                continue;
            }
            // 过滤车间
            if (!CollectionUtils.isEmpty(filterGridCodes)) {
                String productGridCode = productOrder.getProductOrderMaterial().getGridCode();
                boolean flag = false;
                for (String filterGridCode : filterGridCodes) {
                    // "noGrid": 表示查没有车间的
                    if (filterGridCode.equals(productGridCode) || ("noGrid".equals(filterGridCode) && StringUtils.isEmpty(productGridCode))) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    continue;
                }
            }
            if (productOrder.getState() == OrderStateEnum.FINISHED.getCode()) {
                finishCount++;
            }
            if (productOrder.getState() == OrderStateEnum.RELEASED.getCode()) {
                releasedCount++;
            }
            //计划完成时间在今天之前统计为异常;filterErr表示是否展示异常订单，TURE是展示，false不展示
            Date planProductEndTime = productOrder.getProductOrderMaterial().getPlanProductEndTime();
            if (filterErr) {
                filterOrders.add(productOrder);
                if (planProductEndTime != null && start.after(planProductEndTime)) {
                    abnormalCount++;
                }
            } else {
                if (!(planProductEndTime != null && start.after(planProductEndTime))) {
                    filterOrders.add(productOrder);
                }
            }

            // 委外订单当成生产订单特殊处理
            List<SubcontractOrderEntity> relateSubcontractOrders = orderNumberSubcontractOrdersMap.getOrDefault(productOrder.getProductOrderNumber(), new ArrayList<>());
            for (SubcontractOrderEntity relateSubcontractOrder : relateSubcontractOrders) {
                if (SubcontractOrderStateEnum.FINISHED.getCode().equals(relateSubcontractOrder.getState())) {
                    finishCount++;
                }
                if (SubcontractOrderStateEnum.RELEASED.getCode().equals(relateSubcontractOrder.getState())) {
                    releasedCount++;
                }
                filterOrders.add(ProductOrderEntity.builder()
                        .productOrderNumber(relateSubcontractOrder.getSubcontractOrderNumber())
                        .isSubcontractOrder(true)
                        .relatedOrderNum(productOrder.getRelatedOrderNum())
                        .build());
            }
        }
        //分页
        List<ProductOrderEntity> builds = PageUtil.startPage(filterOrders, current, size);
        //没有复核条件的数据返回
        if (CollectionUtils.isEmpty(builds)) {
            // 空数据返回结构不一致导致前端报错
            PageData<PlanProgressOrderVO> pageData = new PageData<>();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("releasedCount", 0);
            jsonObject.put("finishCount", 0);
            jsonObject.put("pageData", pageData);
            jsonObject.put("abnormalCount", 0);
            return ResponseData.success(jsonObject);
        }
        // 之前性能太差，把只需要查询的抽出来就行
        List<OrderWorkOrderEntity> orderWorkOrders = orderWorkOrderService.listOrderWorkOrderRelations(builds.stream().map(ProductOrderEntity::getProductOrderId).collect(Collectors.toList()), OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        Map<Integer, List<Integer>> productOrderIdMap = orderWorkOrders.stream().collect(Collectors.groupingBy(OrderWorkOrderEntity::getOrderId, Collectors.mapping(OrderWorkOrderEntity::getWorkOrderId, Collectors.toList())));

        List<PlanProgressOrderVO> vos = builds.stream().map(productOrder -> {
            if (productOrder.getIsSubcontractOrder()) {
                // 是委外订单当成生产订单特殊处理
                SubcontractOrderEntity subcontractOrder = subcontractOrderEntityMap.get(productOrder.getProductOrderNumber());
                SubcontractOrderDetailEntity detailEntity = Objects.nonNull(subcontractOrder.getDetailEntity()) ? subcontractOrder.getDetailEntity() : new SubcontractOrderDetailEntity();
                Double progress = MathUtil.divideDouble(detailEntity.getFinishCount(), detailEntity.getQuantity(), 4);
                return PlanProgressOrderVO.builder()
                        .isSubcontractOrder(true)
                        .orderNumber(subcontractOrder.getSubcontractOrderNumber())
                        .state(subcontractOrder.getState())
                        .productionCode(detailEntity.getMaterialCode())
                        .plannedDeliveryDate(detailEntity.getPlanEndTime())
                        .planQuantity(detailEntity.getQuantity())
                        .procedureProcess(progress)
                        .procedureNameList(Stream.of(detailEntity.getProcedureName()).collect(Collectors.toList()))
                        .relatedOrderNum(productOrder.getRelatedOrderNum())
                        .remark(detailEntity.getMaterialRemark())
                        .finishCount(detailEntity.getFinishCount())
                        .planProductStartTime(detailEntity.getPlanStartTime())
                        .planProductEndTime(detailEntity.getPlanEndTime())
                        .completedRate(progress)
                        .build();
            }
            // 查询当前生产订单下的工单
            List<Integer> relateWorkOrderIds = productOrderIdMap.getOrDefault(productOrder.getProductOrderId(), new ArrayList<>());
            List<WorkOrderEntity> workOrders = CollectionUtils.isEmpty(relateWorkOrderIds) ? new ArrayList<>() : workOrderService.listByIds(relateWorkOrderIds);
            return convertToPlanProgressOrderVO(productOrder, workOrders);
        }).collect(Collectors.toList());
        //处理物料名称
        preHandleData(vos);
        PageData<PlanProgressOrderVO> pageData = new PageData<>(current, size, filterOrders.size(), vos);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("releasedCount", releasedCount);
        jsonObject.put("finishCount", finishCount);
        jsonObject.put("pageData", pageData);
        jsonObject.put("abnormalCount", abnormalCount);
        return ResponseData.success(jsonObject);
    }


    private void preHandleData(List<PlanProgressOrderVO> records) {
        Set<String> materialCodes = records.stream()
                .map(PlanProgressOrderVO::getProductionCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<String, MaterialEntity> materialCodeNameMap = materialService.listByCodes(materialCodes, MaterialEntity::getCode, MaterialEntity::getName, MaterialEntity::getDrawingNumber, MaterialEntity::getStandard);
        for (PlanProgressOrderVO record : records) {
            MaterialEntity materialEntity = materialCodeNameMap.get(record.getProductionCode());
            record.setProductionName(materialEntity.getName());
            record.setDrawingNumber(materialEntity.getDrawingNumber());
            record.setStandard(materialEntity.getStandard());
        }
    }

    /**
     * 获取当天指定状态的生产订单的个数
     */
    private Long countDayOrderByState(int state, Date start, Date end) {
        PageResult<ProductOrderEntity> page = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().state(state).actualProductStartTime(DateUtil.dateTimeStr(start))
                .actualProductEndTime(DateUtil.dateTimeStr(end)).isShowSimpleInfo(true).build());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return 0L;
        }
        return page.getTotal();
    }

    /**
     * 封装成生产计划与进度列表页面的PlanProgressOrderVO对象
     * <p>
     * 生产订单号、状态、交期、产品名称、计划数量、当前工序、流转状态、工序进度
     */
    private PlanProgressOrderVO convertToPlanProgressOrderVO(ProductOrderEntity productOrder, List<WorkOrderEntity> workOrders) {
        ProductOrderMaterialEntity productOrderMaterial = !Objects.isNull(productOrder.getProductOrderMaterial()) ?
                productOrder.getProductOrderMaterial() : productOrder.getProductOrderMaterials().get(0);

        //Integer flowStatus = null;
        List<String> procedureNameList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(workOrders)) {
            // 3、获取生产工单的当前工序名称列表
            Map<Integer, String> procedureMap = getProcedureMap(productOrderMaterial.getProductOrderId(), workOrders);
            procedureNameList = new ArrayList<>(procedureMap.values());
            ////4、获取当前工序的流转状态
            //flowStatus = getFlowStatusOfCurrentProcess(productOrder.getProductOrderNumber(), new ArrayList<>(procedureMap.keySet()));
        }

        return PlanProgressOrderVO.builder()
                .isSubcontractOrder(false)
                .orderId(productOrder.getProductOrderId())
                .orderNumber(productOrder.getProductOrderNumber())
                .state(productOrder.getState())
                .productionCode(productOrderMaterial.getMaterialCode())
                //.productionName(productOrderMaterial.getMaterialName())
                .plannedDeliveryDate(productOrderMaterial.getRequireGoodsDate())
                .planQuantity(productOrderMaterial.getPlanQuantity())
                .procedureProcess(productOrderMaterial.getProcedureProcess())
                //.flowStatus(flowStatus)
                .procedureNameList(procedureNameList)
                .relatedOrderNum(productOrder.getRelatedOrderNum())
                .remark(productOrder.getRemark())
                .finishCount(productOrderMaterial.getFinishCount())
                .planProductStartTime(productOrderMaterial.getPlanProductStartTime())
                .planProductEndTime(productOrderMaterial.getPlanProductEndTime())
                .actualProductStartTime(productOrderMaterial.getActualProductStartTime())
                .actualProductEndTime(productOrderMaterial.getActualProductEndTime())
                .completedRate(MathUtil.divideDouble(productOrderMaterial.getFinishCount(), productOrderMaterial.getPlanQuantity(), 4))
                .build();
    }

    /**
     * 获取当前工序的流转状态
     *
     * @param craftProcedureIds 当前工序id
     * @return 流转状态
     * <AUTHOR>
     */
    private Integer getFlowStatusOfCurrentProcess(String productOrderNumber, List<Integer> craftProcedureIds) {
        if (CollectionUtils.isEmpty(craftProcedureIds)) {
            return WorkOrderCirculationStateEnum.UNABLE.getCode();
        }
        // 找到当前工序的多个工单
        Set<Integer> workOrderIds = workOrderProcedureRelationService.lambdaQuery()
                .in(WorkOrderProcedureRelationEntity::getCraftProcedureId, craftProcedureIds)
                .list()
                .stream().map(WorkOrderProcedureRelationEntity::getWorkOrderId).collect(Collectors.toSet());
        List<WorkOrderEntity> workOrderList = null;
        if (!CollectionUtils.isEmpty(workOrderIds)) {
            workOrderList = workOrderService.lambdaQuery()
                    .in(WorkOrderEntity::getWorkOrderId, workOrderIds)
                    .list();
        }
        if (CollectionUtils.isEmpty(workOrderList)) {
            return WorkOrderCirculationStateEnum.UNABLE.getCode();
        }
        // 找出对应订单的工单
        List<WorkOrderEntity> collect1 = workOrderList.stream()
                .filter(obj -> obj != null && productOrderNumber.equals(obj.getProductOrderNumber()))
                .collect(Collectors.toList());
        // 无工单，取“不可流转”
        if (CollectionUtils.isEmpty(collect1)) {
            return WorkOrderCirculationStateEnum.UNABLE.getCode();
        }
        // 有工单，调用getFlowStatus()方法
        return getFlowStatus(collect1);
    }

    /**
     * 获取某工序的流转状态
     * 工序流转状态：工序流转状态：不可流转、可流转、流转超时、流转完成
     * 判断条件：`circulation_state` int(11) DEFAULT '0' COMMENT '流转状态(0-不可流转,1-可流转,2-流转超时,3-已流转)'
     * 步骤
     * 取此工序中的工单的流转状态，有多个工单时，展示规则顺序：流转完成（工单状态有效大于 生效） >
     * 流转超时（上一个工序完成开始计时，工序中存在标准流转时长，如果没有配置标准流转时长，标准流转时长显示--，显示可流转） >
     * 可流转（最小批量达到或者最小批量为0时上一个工序完成） >
     * 不可流转（未达到最小批量，或者最小批量为0时，上一个工序未完成）
     *
     * @param list 多个生产工单
     * @return
     */
    private Integer getFlowStatus(List<WorkOrderEntity> list) {
        boolean flagUnable = false, flagEnable = false, flagOver = false, flagPlaned = false;
        for (WorkOrderEntity entity : list) {
            if (WorkOrderCirculationStateEnum.UNABLE.getCode().equals(entity.getCirculationState())) {
                flagUnable = true;
            } else if (WorkOrderCirculationStateEnum.ENABE.getCode().equals(entity.getCirculationState())) {
                flagEnable = true;
            } else if (WorkOrderCirculationStateEnum.OVER.getCode().equals(entity.getCirculationState())) {
                flagOver = true;
            } else if (WorkOrderCirculationStateEnum.PLANED.getCode().equals(entity.getCirculationState())) {
                flagPlaned = true;
                break;
            } else {
                // 不处理
            }
        }
        if (flagPlaned) {
            return WorkOrderCirculationStateEnum.PLANED.getCode();
        }
        if (flagOver) {
            return WorkOrderCirculationStateEnum.OVER.getCode();
        }
        if (flagEnable) {
            return WorkOrderCirculationStateEnum.ENABE.getCode();
        }
        if (flagUnable) {
            return WorkOrderCirculationStateEnum.UNABLE.getCode();
        }
        return null;
    }

    /**
     * 获取生产订单的当前工序名称列表
     * 1、如果生产工单的状态没有投产、挂起、完成、关闭，则取工序路径图的第一个工序
     * 2、有投产、挂起的，取所有的投产、挂起的工序
     * 3、没有投产、挂起，但有完成或关闭的，取完成或关闭中，最靠后的工序。其下一个工序
     */
    private Map<Integer, String> getProcedureMap(Integer productOrderId, List<WorkOrderEntity> workOrderEntityList) {
        Map<Integer, String> map = new HashMap<>(8);
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            return map;
        }
        // 查询工单关联的工艺工序
        List<WorkOrderProcedureRelationEntity> allRelations = workOrderProcedureRelationService.lambdaQuery()
                .in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderEntityList.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList()))
                .list();
        if (CollectionUtils.isEmpty(allRelations)) {
            return map;
        }
        Map<String, List<WorkOrderProcedureRelationEntity>> workOrderNumberRelationsMap = allRelations.stream()
                .collect(Collectors.groupingBy(WorkOrderProcedureRelationEntity::getWorkOrderNumber));
        // 查所有的工序 -> 获取别名
        List<Integer> procedureIds = allRelations.stream().map(WorkOrderProcedureRelationEntity::getProcedureId).distinct().collect(Collectors.toList());
        Map<Integer, CraftProcedureEntity> procedureIdsMap = craftProcedureService.lambdaQuery().in(CraftProcedureEntity::getProcedureId, procedureIds).list()
                .stream().collect(Collectors.toMap(CraftProcedureEntity::getId, Function.identity()));

        // 1、有投产、挂起的，取所有的投产、挂起的工单
        List<WorkOrderEntity> investmentHangupCollect = workOrderEntityList.stream()
                .filter(obj -> WorkOrderStateEnum.INVESTMENT.getCode().equals(obj.getState()) || WorkOrderStateEnum.HANG_UP.getCode().equals(obj.getState()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(investmentHangupCollect)) {
            for (WorkOrderEntity workOrderEntity : investmentHangupCollect) {
                // 通过工单找到对应的工序列表
                List<WorkOrderProcedureRelationEntity> list = workOrderNumberRelationsMap.getOrDefault(workOrderEntity.getWorkOrderNumber(), new ArrayList<>());
                // 工序列表转为map<工艺工序id，工艺工序名称>
                List<CraftProcedureEntity> craftProcedures = list.stream().map(e -> procedureIdsMap.get(e.getCraftProcedureId())).filter(Objects::nonNull).collect(Collectors.toList());
                // 统一放到返回的map
                Map<Integer, String> innerMap = craftProcedures.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, buildProcedureName()));
                map.putAll(innerMap);
            }
            return map;
        }
        // 2、有完成或关闭的，取完成或关闭中，最靠后的工序。其下一个工序
        List<WorkOrderEntity> finishStateCollect = workOrderEntityList.stream()
                .filter(obj -> WorkOrderStateEnum.FINISHED.getCode().equals(obj.getState()) || WorkOrderStateEnum.CLOSED.getCode().equals(obj.getState()))
                .filter(res -> Objects.nonNull(res.getActualEndDate()))
                .sorted(Comparator.comparing(WorkOrderEntity::getActualEndDate))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(finishStateCollect)) {
            // 1、获取最晚完成的生产工单 对应的工序列表
            WorkOrderEntity workOrderEntity = finishStateCollect.get(finishStateCollect.size() - 1);
            List<WorkOrderProcedureRelationEntity> relationEntitiesList = workOrderNumberRelationsMap.get(workOrderEntity.getWorkOrderNumber());

            // 查询最晚完成工单下的工序对应的下一级工序
            List<CraftProcedureEntity> nextCraftList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(relationEntitiesList)) {
                List<CraftProcedureEntity> craftProcedures = craftProcedureService.listByCraftId(relationEntitiesList.get(0).getCraftId());
                // 工艺工序如果是一条直线,保证一条直线顺序后返回
                craftProcedures = craftProcedureService.sortCraftProceduresLine(craftProcedures);

                int lastIndex = -1;
                for (WorkOrderProcedureRelationEntity workOrderProcedureRelation : relationEntitiesList) {
                    for (int i = 0; i < craftProcedures.size(); i++) {
                        if (workOrderProcedureRelation.getCraftProcedureId().equals(craftProcedures.get(i).getId()) && i + 1 > lastIndex) {
                            lastIndex = i + 1;
                            break;
                        }
                    }
                }
                //如果没有找到下级工序，就取当前工序
                if (lastIndex < 0 || lastIndex >= craftProcedures.size()) {
                    nextCraftList.add(craftProcedures.get(craftProcedures.size() - 1));
                } else {
                    nextCraftList.add(craftProcedures.get(lastIndex));
                }
            }
            // 返回
            return nextCraftList.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, buildProcedureName()));
        }
        // 3、如果生产工单的状态没有投产、挂起、完成，则取工序路径图的第一个工序
        List<CraftProcedureEntity> procedureList = new ArrayList<>();
        // 工单可以选择工艺,不能直接通过物料查最新工艺
        List<CraftProcedureEntity> craftProcedures = workOrderService.listCraftProcedureByProductOrderId(productOrderId);
        if (!CollectionUtils.isEmpty(craftProcedures)) {
            procedureList.add(craftProcedures.get(0));
        }
        return procedureList.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, buildProcedureName()));
    }

    private static Function<CraftProcedureEntity, String> buildProcedureName() {
        return e -> StringUtils.isNotEmpty(e.getAlias()) ? e.getAlias() : e.getProcedureName();
    }


    @Override
    public ResponseData productionOrderDetail(Integer orderId) {
        ProductOrderEntity productOrder = extProductOrderInterface.selectProductOrderById(orderId);
        if (productOrder == null) {
            return ResponseData.fail("没有对应的生产订单");
        }
        // 查询当前生产订单下的工单
        List<WorkOrderEntity> workOrderEntities = orderWorkOrderService.listWorkOrderByOrderId(productOrder.getProductOrderId(), OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        // 订单详情VO封装
        PlanProgressOrderVO orderVO = convertToPlanProgressOrderVO(productOrder, workOrderEntities);
        //处理物料名称
        preHandleData(Collections.singletonList(orderVO));

        // 查询生产订单的工序
        List<CraftProcedureEntity> craftProcedures = listCraftProcedures(productOrder.getProductOrderId(), productOrder.getProductOrderNumber());

        // 计划总工时, 生产总工时, 完成率，合格率
        double totalPlannedHours = 0.0, totalProductionHours = 0.0, passRate = 0.0;
        Date productionStartTime = null;
        if (!CollectionUtils.isEmpty(workOrderEntities)) {
            totalPlannedHours = workOrderEntities.stream().filter(res -> !Objects.isNull(res.getPlannedWorkingHours())).mapToDouble(WorkOrderEntity::getPlannedWorkingHours).sum();
            totalProductionHours = workOrderEntities.stream().filter(res -> !Objects.isNull(res.getActualWorkingHours())).mapToDouble(WorkOrderEntity::getActualWorkingHours).sum();
            // 最早的生产日期开始时间
            List<Date> dates = workOrderEntities.stream()
                    .map(WorkOrderEntity::getActualStartDate).filter(Objects::nonNull).collect(Collectors.toList());
            productionStartTime = !CollectionUtils.isEmpty(dates) ? Collections.min(dates) : null;

            List<String> workOrderNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            List<RecordWorkOrderDayCountEntity> workOrderDayCounts = recordWorkOrderDayCountService.lambdaQuery()
                    .in(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumbers)
                    .list();
            // 完成数
            double completedCount = workOrderDayCounts.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
            // 不合格数
            double unqualified = workOrderDayCounts.stream().mapToDouble(RecordWorkOrderDayCountEntity::getUnqualified).sum();
            double total = completedCount + unqualified;
            // 合格率
            passRate = total == 0 ? 1.0 : MathUtil.divideDouble(completedCount, total, 4);

        }
        // 计划开始日期: 生产订单中，所有生产工单(作业工单)的最早的实际生产开始时间
        orderVO.setProductionStartTime(productionStartTime);
        // 计划总工时
        orderVO.setTotalPlannedHours(MathUtil.round(totalPlannedHours, 2));
        // 生产总工时
        orderVO.setTotalProductionHours(MathUtil.round(totalProductionHours, 2));
        // 合格率
        orderVO.setPassRate(passRate);

        // 工序进度VO封装
        List<PlanProgressCraftDetailVO> planProgressCraftDetails = new ArrayList<>();

        // 根据
        for (CraftProcedureEntity craftProcedure : craftProcedures) {
            double unqualified = 0, inStockCount = 0, minCirculationDuration = 0;
            // 工序的工单列表
            List<WorkOrderEntity> workOrders = craftProcedure.getWorkOrderEntities();
            //Integer flowStatus = 0;
            String productStatus = "生效";
            double sumFinishCount = 0, productionTotalTime = 0, productionPlanTotalTime = 0, sumCirculationDuration = 0, flowTimeoutThresholdConfiguration = 0;
            if (!CollectionUtils.isEmpty(workOrders)) {
                // 工序流转状态、工序生产状态
                //flowStatus = getFlowStatus(workOrders);
                productStatus = getProductStatus(workOrders);
                // 工序完成数量数 （前端判断：工序完成时，完成数量低于销售订单数量，数量标红）
                sumFinishCount = workOrders.stream().mapToDouble(WorkOrderEntity::getFinishCount).sum();
                // 生产时长(求和)，计划工时（求和）
                productionTotalTime = workOrders.stream().filter(workOrderEntity -> workOrderEntity.getActualWorkingHours() != null).mapToDouble(WorkOrderEntity::getActualWorkingHours).sum();
                productionPlanTotalTime = workOrders.stream().filter(workOrderEntity -> workOrderEntity.getPlanTheoryHour() != null).mapToDouble(WorkOrderEntity::getPlanTheoryHour).sum();
                // 统计工单列表的流转时长之和 , 标准流转时长+流转超时阈值配置_固定值(小时)
                sumCirculationDuration = workOrders.stream().mapToDouble(WorkOrderEntity::getCirculationDuration).sum();
                // 不良数
                unqualified = workOrders.stream().filter(workOrder -> workOrder.getUnqualified() != null).mapToDouble(WorkOrderEntity::getUnqualified).sum();
                // 流转数
                inStockCount = workOrders.stream().filter(workOrder -> workOrder.getInStockCount() != null).mapToDouble(WorkOrderEntity::getInStockCount).sum();
                // 流转时长, 取最小的
                minCirculationDuration = workOrders.stream().filter(workOrder -> workOrder.getCirculationDuration() != null).mapToDouble(WorkOrderEntity::getCirculationDuration).min().orElse(0);
            }
            ProcedureControllerConfigEntity configEntity = procedureControllerConfigService.getEntityByCraftProcedureId(craftProcedure.getId());
            if (Objects.nonNull(configEntity)) {
                //固定值
                if (TimeoutThresholdTypeEnum.FIXED_VALUE.getCode().equals(configEntity.getFlowTimeoutThresholdConfigurationUnitType())) {
                    flowTimeoutThresholdConfiguration = MathUtil.add(configEntity.getStandardCirculationDuration(), MathUtil.divideDouble(configEntity.getFlowTimeoutThresholdConfiguration(), 60, 2));
                } else {
                    flowTimeoutThresholdConfiguration = MathUtil.add(MathUtil.divideDouble(configEntity.getFlowTimeoutThresholdConfiguration(), 100, 2), 1.0);
                }
            }
            double finishCountSum = craftProcedure.getFinishCountSum() != null ? craftProcedure.getFinishCountSum() : 0;
            double planQuantitySum = craftProcedure.getPlanQuantitySum() != null ? craftProcedure.getPlanQuantitySum() : 0;

            planProgressCraftDetails.add(PlanProgressCraftDetailVO.builder()
                    // 3.1版本： 有别名用别名，否则用原名
                    .procedureName(buildProcedureName().apply(craftProcedure))
                    .finishCountSum(finishCountSum)
                    .planQuantitySum(planQuantitySum)
                    .actualWorkingHour(craftProcedure.getActualWorkingHourSum())
                    .unqualified(unqualified)
                    .inStockCount(inStockCount)
                    .circulationDuration(minCirculationDuration)
                    // 工序进度  完成数/计划数
                    .procedureProcess(planQuantitySum == 0 ? 0.0 : MathUtil.divideDouble(finishCountSum, planQuantitySum, 4))
                    .sumFinishCount(sumFinishCount)
                    //.flowStatus(flowStatus)
                    .productStatus(productStatus)
                    .productionTotalTime(MathUtil.round(productionTotalTime, 2))
                    .productionPlanTotalTime(MathUtil.round(productionPlanTotalTime, 2))
                    .sumCirculationDuration(sumCirculationDuration)
                    .flowTimeoutThresholdConfiguration(flowTimeoutThresholdConfiguration)
                    .conversionFactor(Objects.nonNull(configEntity) ? configEntity.getConversionFactor() : 1)
                    .build());
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderDetail", orderVO);
        jsonObject.put("progressCrafts", planProgressCraftDetails);
        return ResponseData.success(jsonObject);
    }


    /**
     * 工单统计数
     *
     * @param gridIds       车间id,多个逗号分割
     * @param maintainTypes 维修类型
     * @return
     */
    @Override
    public ResponseData workOrderStatistics(String gridIds, String maintainTypes) {
        // yyyy-MM-dd
        SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 本月开始, 要转成 yyyy-MM-dd 00:00:00
        Date monthStart = DateUtil.getDayBegin(calendar.getTime());
        // 本月末
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date monthEnd = DateUtil.getEndTimeOfCurrentDay(calendar.getTime());

        Date today = new Date();
        // yyyy-MM-dd 00:00:00
        String todayStr = formatter.format(today);

        // （工单状态：投产，完成，生效，挂起，关闭）
        List<Integer> workOrderStates = Stream.of(WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.FINISHED.getCode(),
                        WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.HANG_UP.getCode(), WorkOrderStateEnum.CLOSED.getCode())
                .collect(Collectors.toList());

        List<Integer> lineIds = null;
        if (!StringUtils.isEmpty(gridIds)) {
            List<Integer> gridIdList = Arrays.stream(gridIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            // 查询这些车间对应的产线
            lineIds = listLineIdByGridIds(gridIdList);
        }

        double monthPlan = 0, monthComplete = 0, monthCompletionRate = 0, monthPassRate = 0, todayPlan = 0, todayComplete = 0, todayCompletionRate = 0, todayPassRate = 0;
        // 计划数查整个月的
        // 1.当月工单计划数：工单目前有分日计划，是详细到每一天的，此处就要取所有生产工单的分日计划为当月之和（工单状态：投产，完成，生效，挂起，关闭）
        // 今日工单计划数：工单时间为今日的分日计划之和（工单状态：投产，完成，生效，挂起，关闭）
        List<WorkOrderPlanEntity> workOrderPlans = workOrderPlanService.lambdaQuery()
                .between(WorkOrderPlanEntity::getTime, monthStart, monthEnd)
                .list();
        // 过滤掉不符合条件的工单
        if (!CollectionUtils.isEmpty(workOrderPlans)) {
            Set<String> workOrderNumbers = workOrderPlans.stream().map(WorkOrderPlanEntity::getWorkOrderNumber).collect(Collectors.toSet());
            // 符合条件的工单
            List<String> tempWorkOrderNumbers = listWorkOrderNumbers(workOrderNumbers, workOrderStates, lineIds);
            List<WorkOrderPlanEntity> tempWorkOrderPlans = workOrderPlans.stream().filter(res -> tempWorkOrderNumbers.contains(res.getWorkOrderNumber())).collect(Collectors.toList());

            monthPlan = tempWorkOrderPlans.stream().mapToDouble(WorkOrderPlanEntity::getPlanQuantity).sum();
            todayPlan = tempWorkOrderPlans.stream().filter(res -> todayStr.equals(formatter.format(res.getTime()))).mapToDouble(WorkOrderPlanEntity::getPlanQuantity).sum();
        }

        // 2.当月工单完成数：对应当月所有日期的日计划中，每日实际完成的数量之和（工单状态：投产，完成，生效，挂起，关闭）
        // 今日工单完成数：每日实际完成的数量之和（工单状态：投产，完成，生效，挂起，关闭）
        List<RecordWorkOrderDayCountEntity> workOrderDayCounts = recordWorkOrderDayCountService.lambdaQuery()
                .between(RecordWorkOrderDayCountEntity::getTime, monthStart, todayStr)
                .list();
        // 过滤掉不符合条件的工单
        if (!CollectionUtils.isEmpty(workOrderDayCounts)) {
            Set<String> workOrderNumbers = workOrderDayCounts.stream().map(RecordWorkOrderDayCountEntity::getWorkOrderNumber).collect(Collectors.toSet());
            // 符合条件的工单
            List<String> tempWorkOrderNumbers = listWorkOrderNumbers(workOrderNumbers, workOrderStates, lineIds);
            List<RecordWorkOrderDayCountEntity> tempWorkOrderDayCounts = workOrderDayCounts.stream().filter(res -> tempWorkOrderNumbers.contains(res.getWorkOrderNumber())).collect(Collectors.toList());

            monthComplete = tempWorkOrderDayCounts.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
            todayComplete = tempWorkOrderDayCounts.stream().filter(res -> todayStr.equals(formatter.format(res.getTime()))).mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
        }

        // 3.计划完成率：当月工单完成数 除以 当月工单计划数
        // 今日完成率：今日工单完成数 除以 今日工单计划数
        monthCompletionRate = monthPlan == 0 ? 0 : MathUtil.divideDouble(monthComplete, monthPlan, 4);
        todayCompletionRate = todayPlan == 0 ? 0 : MathUtil.divideDouble(todayComplete, todayPlan, 4);

        // 4.当月一次通过率：当月生产的所有产品，减去质检返工记录中维修时间为当月，且流水号去重后的数据为分子，当月工单完成数为分母
        // 今日一次通过率：当日生产的所有产品，减去质检返工记录中当日的流水号去重数据为分子，当日工单完成数为分母
        //指定维修类型
        List<String> types = new ArrayList<>();
        if (StringUtils.isNotBlank(maintainTypes)) {
            types = Stream.of(maintainTypes.split(Constants.SEP)).collect(Collectors.toList());
        }
        List<MaintainRecordEntity> maintainRecords = maintainRecordService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(types), MaintainRecordEntity::getMaintainType, types)
                .between(MaintainRecordEntity::getCreateTime, monthStart, today)
                .list();
        // 过滤掉不符合条件的工单
        double monthMaintainCount = 0, todayMaintainCount = 0;
        if (!CollectionUtils.isEmpty(maintainRecords)) {
            Set<String> workOrderNumbers = maintainRecords.stream().map(MaintainRecordEntity::getWorkOrder).collect(Collectors.toSet());
            // 符合条件的工单
            List<String> tempWorkOrderNumbers = listWorkOrderNumbers(workOrderNumbers, workOrderStates, lineIds);
            List<MaintainRecordEntity> tempMaintainRecords = maintainRecords.stream().filter(res -> tempWorkOrderNumbers.contains(res.getWorkOrder())).collect(Collectors.toList());
            List<MaintainRecordEntity> todayMaintainRecords = tempMaintainRecords.stream().filter(res -> todayStr.equals(formatter.format(res.getCreateTime()))).collect(Collectors.toList());

            monthMaintainCount = tempMaintainRecords.stream().map(MaintainRecordEntity::getBarCode).filter(Objects::nonNull).distinct().count();
            todayMaintainCount = todayMaintainRecords.stream().map(MaintainRecordEntity::getBarCode).filter(Objects::nonNull).distinct().count();
        }
        monthPassRate = monthComplete == 0 ? 0 : MathUtil.divideDouble(monthComplete - monthMaintainCount, monthComplete, 4);
        todayPassRate = todayComplete == 0 ? 0 : MathUtil.divideDouble(todayComplete - todayMaintainCount, todayComplete, 4);

        WorkOrderStatisticsVO build = WorkOrderStatisticsVO.builder()
                .monthPlan(monthPlan)
                .todayPlan(todayPlan)
                .monthComplete(monthComplete)
                .todayComplete(todayComplete)
                .monthCompletionRate(monthCompletionRate)
                .todayCompletionRate(todayCompletionRate)
                .monthPassRate(monthPassRate)
                .todayPassRate(todayPassRate)
                .build();
        return ResponseData.success(build);
    }

    /**
     * 查询车间下对应的产线id
     */
    private List<Integer> listLineIdByGridIds(List<Integer> gridIds) {
        List<Integer> lineIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(gridIds)) {
            // 查询这些车间对应的产线
            lineIds = lineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getGid)
                    .in(ProductionLineEntity::getGid, gridIds)
                    .list()
                    .stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        }
        return lineIds;
    }

    /**
     * 查询符合这些条件的工单号
     */
    private List<String> listWorkOrderNumbers(Collection<String> workOrderNumbers, List<Integer> workOrderStates, List<Integer> lineIds) {
        return workOrderService.lambdaQuery()
                .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                .in(WorkOrderEntity::getState, workOrderStates)
                .in(!CollectionUtils.isEmpty(lineIds), WorkOrderEntity::getLineId, lineIds)
                .list().stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
    }

    /**
     * 取送检完成时间为当天，送检类型为首件/末件/过程检验，不良描述列，按条形图展示(不良项目数量，从大到小排)
     * 取送检完成时间为本周，送检类型为首件/末件/过程检验，不良描述列，按条形图展示(不良项目数量，从大到小排)
     */
    @Override
    public ResponseData inspectSheetDefectTop5(String timeRange, Integer inspectionSheetType, String gridIds) {
        Date now = new Date();

        Date finishStartTime, finishEndTime;
        if (TimeRangeEnum.DAY.getCode().equals(timeRange)) {
            // 当天
            finishStartTime = dictService.getDayOutputBeginTime(new Date());
            finishEndTime = now;
        } else if (TimeRangeEnum.WEEK.getCode().equals(timeRange)) {
            // 本周
            Calendar calendar = Calendar.getInstance();
            // 周日是第一天,周六是最后一天
            calendar.add(Calendar.DAY_OF_WEEK, -(calendar.get(Calendar.DAY_OF_WEEK) - 2));
            // 本周开始
            Date weekStart = calendar.getTime();
            // 本周开始首班时间
            String outputBeginTime = dictService.getBeginTimeOfDay();
            finishStartTime = DateUtil.parse(DateUtil.format(weekStart, DateUtil.DATE_FORMAT) + " " + outputBeginTime, DateUtil.DATETIME_FORMAT);
            finishEndTime = now;
        } else {
            log.error("只支持查询当天和本周的检验不良top5, timeRange:{}", timeRange);
            throw new ResponseException("只支持查询当天和本周的检验不良top5");
        }

        List<InspectSheetDefectVO> vos = new ArrayList<>();
        List<Integer> lineIds = null;
        if (!StringUtils.isEmpty(gridIds)) {
            List<Integer> gridIdList = Arrays.stream(gridIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            // 查询这些车间对应的产线
            lineIds = listLineIdByGridIds(gridIdList);
            if (CollectionUtils.isEmpty(lineIds)) {
                return ResponseData.success(vos);
            }
        }

        // 查询指定时间段内完成的送检单
        InspectionSheetScreenDTO dto = InspectionSheetScreenDTO.builder()
                .sheetTypes(String.valueOf(inspectionSheetType))
                .start(finishStartTime)
                .end(finishEndTime)
                .build();
        List<InspectionSheetEntity> inspectionSheets = JacksonUtil.getResponseArray(inspectionSheetInterface.selectSheetListForScreen(dto), InspectionSheetEntity.class);
        if (CollectionUtils.isEmpty(inspectionSheets)) {
            return ResponseData.success(vos);
        }

        // 工单
        Set<String> inspectionSheetWorkOrderNumbers = inspectionSheets.stream().map(InspectionSheetEntity::getWorkOrderNumber).collect(Collectors.toSet());
        Set<String> workOrderNumberSet = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderId, WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getMaterialCode)
                .in(WorkOrderEntity::getWorkOrderNumber, inspectionSheetWorkOrderNumbers)
                .in(!StringUtils.isEmpty(gridIds) && !CollectionUtils.isEmpty(lineIds), WorkOrderEntity::getLineId, lineIds)
                .list()
                .stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toSet());

        // 查询送检单关联的不良
        Map<Integer, String> idWorkOrderNumberMap = inspectionSheets.stream().collect(Collectors.toMap(InspectionSheetEntity::getInspectionSheetId, InspectionSheetEntity::getWorkOrderNumber));
        List<InspectionSheetDefectRecordEntity> sheetDefects = JacksonUtil.getResponseArray(inspectionSheetDefectRecordInterface.listInspectionSheetDefectRecord(new ArrayList<>(idWorkOrderNumberMap.keySet())), InspectionSheetDefectRecordEntity.class);
        if (!CollectionUtils.isEmpty(sheetDefects)) {
            // 查询不良
            Set<Integer> defineIds = sheetDefects.stream().map(InspectionSheetDefectRecordEntity::getDefectDefineId).collect(Collectors.toSet());
            List<DefectDefineEntity> defectList = defectDefineService.lambdaQuery()
                    .in(DefectDefineEntity::getDefectId, defineIds)
                    .list();
            Map<Integer, String> defineIdNameMap = defectList.stream().collect(Collectors.toMap(DefectDefineEntity::getDefectId, DefectDefineEntity::getDefectName));

            for (Map.Entry<Integer, String> entry : defineIdNameMap.entrySet()) {
                Integer defectId = entry.getKey();
                String defectName = entry.getValue();

                List<InspectionSheetDefectRecordEntity> tempSheetDefects = sheetDefects;
                if (!StringUtils.isEmpty(gridIds)) {
                    tempSheetDefects = sheetDefects.stream()
                            .filter(res -> workOrderNumberSet.contains(idWorkOrderNumberMap.get(res.getInspectionSheetId())))
                            .collect(Collectors.toList());
                }
                // 过滤符合条件的 送检单数量
                long count = tempSheetDefects.stream()
                        .filter(res -> defectId.equals(res.getDefectDefineId()))
                        .map(InspectionSheetDefectRecordEntity::getInspectionSheetId)
                        .collect(Collectors.toSet())
                        .size();
                vos.add(InspectSheetDefectVO.builder()
                        .defectId(defectId)
                        .defectName(defectName)
                        .inspectSheetCount(count)
                        .build());
            }
        }
        // top5
        List<InspectSheetDefectVO> rets = vos.stream()
                .sorted((t1, t2) -> t2.getInspectSheetCount().compareTo(t1.getInspectSheetCount())).limit(5).collect(Collectors.toList());
        return ResponseData.success(rets);
    }

    /**
     * 获取工序生产状态：从多个生产工单的生产状态去判断
     * 备注：完成/关闭都算是完成状态
     *
     * @param list 多个生产工单
     * @return 工序生产状态
     * <AUTHOR>
     */
    private String getProductStatus(List<WorkOrderEntity> list) {
        Integer code = list.stream()
                .filter(entity -> WorkOrderStateEnum.INVESTMENT.getCode().equals(entity.getState()) ||
                        WorkOrderStateEnum.HANG_UP.getCode().equals(entity.getState()) ||
                        WorkOrderStateEnum.FINISHED.getCode().equals(entity.getState()) ||
                        WorkOrderStateEnum.CLOSED.getCode().equals(entity.getState()))
                .map(WorkOrderEntity::getState)
                .distinct()
                .min(Comparator.comparing(Integer::intValue)).orElse(WorkOrderStateEnum.RELEASED.getCode());

        if (code.equals(WorkOrderStateEnum.CLOSED.getCode())) {
            return WorkOrderStateEnum.getNameByCode(WorkOrderStateEnum.FINISHED.getCode());
        } else {
            // 默认是生效
            return WorkOrderStateEnum.getNameByCode(code);
        }
    }

    /**
     * 七天送检单合格率
     *
     * @param sheetTypes
     * @param gridId
     * @return
     */
    @Override
    public ResponseData inspectSheetPassRate(String sheetTypes, Integer gridId) {
        InspectSheetPassVO passVO = InspectSheetPassVO.builder().build();
        if (StringUtils.isBlank(sheetTypes)) {
            return ResponseData.success(passVO);
        }
        Date beginTime = dictService.getDayOutputBeginTime(new Date());
        //最晚时间
        Calendar instance = Calendar.getInstance();
        instance.setTime(beginTime);
        instance.add(Calendar.DATE, 1);
        instance.add(Calendar.SECOND, -1);
        Date endTime = instance.getTime();
        //七天前
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginTime);
        calendar.add(Calendar.DATE, -6);
        Date startTime = calendar.getTime();
        //车间id
        List<Integer> lineIds = new ArrayList<>();
        if (gridId != null) {
            List<ProductionLineEntity> lineEntities = lineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId)
                    .eq(ProductionLineEntity::getGid, gridId)
                    .list();
            if (CollectionUtils.isEmpty(lineEntities)) {
                return ResponseData.success(passVO);
            }
            lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        }
        //查询数据
        List<String> types = Arrays.asList(sheetTypes.split(Constant.SEP));
        //查询送检单
        List<InspectionSheetEntity> sheetEntities = new ArrayList<>();
        try {
            ResponseData responseData = inspectionSheetInterface.selectSheetListForScreen(InspectionSheetScreenDTO.builder()
                    .lineIds(lineIds).sheetTypes(sheetTypes).start(startTime).end(endTime).build());
            sheetEntities = JacksonUtil.getResponseArray(responseData, InspectionSheetEntity.class);
        } catch (Exception e) {
            log.error("访问qms模块错误", e);
        }
        //计算首班时间距离0点的长度
        Date dayBegin = DateUtil.getDayBegin(beginTime);
        long interval = beginTime.getTime() - dayBegin.getTime();
        Date loopStart = new Date(startTime.getTime() - interval);
        Date loopEnd = new Date(endTime.getTime() - interval);
        //过程检验
        if (types.contains("1")) {
            //检验完成时间为当天，送检类型为首件检验，(送检结论为合格的送检单数量/送检单的数量)
            Integer type = 1;
            List<InspectSheetDayPassVO> vos = getInspectSheetDayPassVos(type, sheetEntities, interval, loopStart, loopEnd);
            passVO.setProcess(vos);
        }
        //首件检验
        if (types.contains("3")) {
            Integer type = 3;
            List<InspectSheetDayPassVO> vos = getInspectSheetDayPassVos(type, sheetEntities, interval, loopStart, loopEnd);
            passVO.setFirstProduct(vos);
        }
        //末件检验
        if (types.contains("4")) {
            Integer type = 4;
            List<InspectSheetDayPassVO> vos = getInspectSheetDayPassVos(type, sheetEntities, interval, loopStart, loopEnd);
            passVO.setLastProduct(vos);
        }
        return ResponseData.success(passVO);


    }

    /**
     * 处理送检单合格率数据
     * (送检结论为合格的送检单数量/送检单的数量)
     *
     * @param sheetEntities
     * @param interval
     * @param loopStart
     * @param loopEnd
     * @param type
     * @return
     */
    private List<InspectSheetDayPassVO> getInspectSheetDayPassVos(Integer type, List<InspectionSheetEntity> sheetEntities, long interval, Date loopStart, Date loopEnd) {
        SimpleDateFormat monthAndDayFormat = new SimpleDateFormat(DateUtil.MONTH_DAY_FORMAT_SLASH);
        //分组
        Map<String, List<InspectionSheetEntity>> listMap = sheetEntities.stream()
                .filter(o -> type.equals(o.getInspectionSheetType()))
                .collect(Collectors.groupingBy(o -> {
                    long l = o.getFinishedTime().getTime() - interval;
                    return monthAndDayFormat.format(new Date(l));
                }));
        List<InspectSheetDayPassVO> vos = new ArrayList<>();
        //遍历每一天
        while (loopStart.before(loopEnd)) {
            String dateStr = monthAndDayFormat.format(loopStart);
            Double passRate = null;
            if (listMap.containsKey(dateStr)) {
                List<InspectionSheetEntity> sheetList = listMap.get(dateStr);
                long count = sheetList.stream().filter(o -> "qualified".equals(o.getConclusion()))
                        .count();
                passRate = MathUtil.divideDouble(count, sheetList.size(), 4, RoundingMode.HALF_UP);
            }
            vos.add(InspectSheetDayPassVO.builder()
                    .dateSrt(dateStr)
                    .passRate(passRate)
                    .build());
            loopStart = DateUtil.addDate(loopStart, 1);
        }
        return vos;
    }

    /**
     * 查询生产订单的工序
     */
    private List<CraftProcedureEntity> listCraftProcedures(Integer productionOrderId, String productionOrderNumber) {
        // 工单可以选择工艺而不是使用当前物料的最新工艺
        List<CraftProcedureEntity> craftProcedures = workOrderService.listCraftProcedureByProductOrderId(productionOrderId);

        if (CollectionUtils.isEmpty(craftProcedures)) {
            return craftProcedures;
        }
        List<WorkOrderEntity> allWorkOrders = new ArrayList<>();
        for (CraftProcedureEntity craftProcedure : craftProcedures) {
            // 查询工艺工序关联的工单
            List<WorkOrderProcedureRelationEntity> relationList = workOrderProcedureRelationService.lambdaQuery()
                    .eq(WorkOrderProcedureRelationEntity::getCraftProcedureId, craftProcedure.getId())
                    .list();
            if (!CollectionUtils.isEmpty(relationList)) {
                List<String> workOrderNumbers = relationList.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
                List<WorkOrderEntity> workOrders = workOrderService.lambdaQuery()
                        .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                        .in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode(),
                                WorkOrderStateEnum.HANG_UP.getCode(), WorkOrderStateEnum.FINISHED.getCode())
                        .eq(WorkOrderEntity::getProductOrderNumber, productionOrderNumber)
                        .list();
                craftProcedure.setWorkOrderEntities(workOrders);
                allWorkOrders.addAll(workOrders);
                double finishSum = workOrders.stream()
                        .filter(o -> o.getFinishCount() != null)
                        .mapToDouble(WorkOrderEntity::getFinishCount)
                        .sum();
                double plannedWorkingHourSum = workOrders.stream()
                        .filter(o -> o.getPlannedWorkingHours() != null)
                        .mapToDouble(WorkOrderEntity::getPlannedWorkingHours)
                        .sum();
                double actualWorkingHourSum = workOrders.stream()
                        .filter(o -> o.getActualWorkingHours() != null)
                        .mapToDouble(WorkOrderEntity::getActualWorkingHours)
                        .sum();
                double planQuantitySum = workOrders.stream()
                        .filter(o -> o.getPlanQuantity() != null)
                        .mapToDouble(WorkOrderEntity::getPlanQuantity)
                        .sum();
                craftProcedure.setFinishCountSum(finishSum);
                craftProcedure.setActualWorkingHourSum(actualWorkingHourSum);
                craftProcedure.setPlannedWorkingHourSum(plannedWorkingHourSum);
                craftProcedure.setPlanQuantitySum(planQuantitySum);
            }
        }
        workOrderService.setSimpleWorkOrderTheoryHour(allWorkOrders);
        return craftProcedures;
    }

    /**
     * 获取指定状态的生产订单的个数
     */
    private Long countOrderByState(Integer state) {
        PageResult<ProductOrderEntity> page = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().state(state).isShowSimpleInfo(true).build());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return 0L;
        }
        return page.getTotal();
    }

    /**
     * 查询全部计划数
     */
    private double getTotalPlanQuantity(List<Integer> lineIds) {
        // 查询所有未关闭的工单
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(!CollectionUtils.isEmpty(lineIds), WorkOrderEntity::getLineId, lineIds)
                .notIn(WorkOrderEntity::getState, Stream.of(WorkOrderStateEnum.CLOSED.getCode(), WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.CANCELED.getCode()).collect(Collectors.toList()));
        queryWrapper.select("IFNULL(sum(plan_quantity),0) AS plan_quantity");

        WorkOrderEntity workOrder = workOrderService.getOne(queryWrapper);
        if (Objects.isNull(workOrder)) {
            return 0;
        }
        return workOrder.getPlanQuantity();
    }

    /**
     * 今日订单计划数(不含关闭、创建、取消的工单)
     */
    private double getTodayPlanQuantity(List<Integer> lineIds, Date date) {
        // 今日订单计划数
        double todayPlanQuantity = 0;
        // 查询今日的所有计划数
        List<WorkOrderPlanEntity> todayWorkOrderPlans = workOrderPlanService.lambdaQuery()
                .select(WorkOrderPlanEntity::getWorkOrderNumber, WorkOrderPlanEntity::getPlanQuantity, WorkOrderPlanEntity::getTime)
                .eq(WorkOrderPlanEntity::getTime, date)
                .list();
        List<String> planWorkOrderNumbers = todayWorkOrderPlans.stream().map(WorkOrderPlanEntity::getWorkOrderNumber).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(planWorkOrderNumbers)) {
            // 在对应产线上的工单
            List<String> workOrderNumbers = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .in(WorkOrderEntity::getWorkOrderNumber, planWorkOrderNumbers)
                    .in(!CollectionUtils.isEmpty(lineIds), WorkOrderEntity::getLineId, lineIds)
                    .in(WorkOrderEntity::getState, Stream.of(WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode(), WorkOrderStateEnum.FINISHED.getCode()).collect(Collectors.toList()))
                    .list().stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            for (WorkOrderPlanEntity workOrderPlan : todayWorkOrderPlans) {
                if (workOrderNumbers.contains(workOrderPlan.getWorkOrderNumber())) {
                    todayPlanQuantity = MathUtil.add(todayPlanQuantity, workOrderPlan.getPlanQuantity());
                }
            }
        }
        return todayPlanQuantity;
    }

    @Override
    public ResponseData workOrderPlanAndProgress(Integer current, Integer size, String gridIds) {
        //本大屏根据生产计划与进度修改而来，用于展示对应产品在工单维度的计划与生产进度
        //1.产品名称：生产的产品名称
        //2.状态：产品生产工单的状态，待生产状态：生效；已完成状态：完成；生产中状态：投产，挂起
        //3.工单编号：生产工单编号
        //4.计划数量：工单计划数量
        //5.合格率：对应工单目前在质检返工中产生的流水号去重剩余的工单取和，除以工单的完成数量
        //6.投产率：工单首个工序完成数 除以 计划数量

        List<Integer> lineIds = null;
        if (!StringUtils.isEmpty(gridIds)) {
            List<Integer> gridIdList = Arrays.stream(gridIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            // 查询这些车间对应的产线
            lineIds = listLineIdByGridIds(gridIdList);
        }
        // （工单状态：生效，完成，投产，挂起）
        List<Integer> workOrderStates = Stream.of(WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.FINISHED.getCode(),
                        WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode())
                .collect(Collectors.toList());

        Page<WorkOrderEntity> page = workOrderService.lambdaQuery()
                .in(WorkOrderEntity::getState, workOrderStates)
                .in(!CollectionUtils.isEmpty(lineIds), WorkOrderEntity::getLineId, lineIds)
                .orderByDesc(WorkOrderEntity::getCreateDate)
                .orderByDesc(WorkOrderEntity::getWorkOrderId)
                .page(new Page<>(current, size));
        List<WorkOrderEntity> records = page.getRecords();

        // 查询产品名称
        Map<String, String> codeMaterialNameMap = new HashMap<>(16);
        // 工单质检返工数量
        Map<String, Long> workOrderNumberMaintainCountMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(records)) {
            Set<String> codes = records.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
            codeMaterialNameMap = getMaterialCodeNameMap(codes);

            // 查询质检返工记录
            List<String> workOrderNumbers = records.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            List<MaintainRecordEntity> maintainRecords = maintainRecordService.lambdaQuery()
                    .in(MaintainRecordEntity::getWorkOrder, workOrderNumbers)
                    .list();
            Map<String, List<MaintainRecordEntity>> numberMaintainRecordsMap = maintainRecords.stream().collect(Collectors.groupingBy(MaintainRecordEntity::getWorkOrder));
            for (Map.Entry<String, List<MaintainRecordEntity>> entry : numberMaintainRecordsMap.entrySet()) {
                String workOrderNumber = entry.getKey();
                long maintainCount = entry.getValue().stream().map(MaintainRecordEntity::getBarCode).filter(Objects::nonNull).distinct().count();
                workOrderNumberMaintainCountMap.put(workOrderNumber, maintainCount);
            }
        }
        List<PlanProgressWorkOrderVO> vos = new ArrayList<>();
        for (WorkOrderEntity record : records) {
            String workOrderNumber = record.getWorkOrderNumber();
            //合格率：对应工单目前在质检返工中产生的流水号去重，剩余的工单取和，除以工单的完成数量
            long maintainCount = workOrderNumberMaintainCountMap.getOrDefault(workOrderNumber, 0L);
            double passRate = record.getFinishCount() == 0 ? 0 : MathUtil.divideDouble(record.getFinishCount() - maintainCount, record.getFinishCount(), 4);

            // 查询该工单的关联的工序
            List<Integer> relationProcedureIds = workOrderProcedureRelationService.lambdaQuery()
                    .eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumber)
                    .list().stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toList());
            long firstProcedureFinishCount = 0;
            if (!CollectionUtils.isEmpty(relationProcedureIds)) {
                // 首个工序：上级工序ID为null的工序
                List<CraftProcedureEntity> craftProcedures = craftProcedureService.lambdaQuery()
                        .select(CraftProcedureEntity::getId, CraftProcedureEntity::getProcedureId, CraftProcedureEntity::getProcedureName, CraftProcedureEntity::getSupProcedureId)
                        .in(CraftProcedureEntity::getId, relationProcedureIds)
                        .list().stream()
                        .filter(res -> StringUtils.isEmpty(res.getSupProcedureId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(craftProcedures)) {
                    CraftProcedureEntity firstCraftProcedure = craftProcedures.get(0);
                    // 查询扫码记录, 按生产流水码去重,得到工单首个工序的完成数量
                    firstProcedureFinishCount = new HashSet<>(productFlowCodeRecordService.lambdaQuery()
                            .select(ProductFlowCodeRecordEntity::getProductFlowCode)
                            .eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber)
                            .eq(ProductFlowCodeRecordEntity::getProdureId, firstCraftProcedure.getId())
                            .list()).size();
                }
            }
            //投产率：工单首个工序完成数 除以 计划数量
            double productionRate = record.getPlanQuantity() == 0 ? 0 : MathUtil.divideDouble(firstProcedureFinishCount, record.getPlanQuantity(), 4);

            vos.add(PlanProgressWorkOrderVO.builder()
                    .productName(codeMaterialNameMap.get(record.getMaterialCode()))
                    .state(record.getState())
                    .workOrderNumber(workOrderNumber)
                    .planQuantity(record.getPlanQuantity())
                    .passRate(passRate)
                    .productionRate(productionRate)
                    .build());
        }
        PageData<PlanProgressWorkOrderVO> pageData = new PageData<>(current, size, Long.valueOf(page.getTotal()).intValue(), vos);
        return ResponseData.success(pageData);
    }

    /**
     * 查询物料名称
     */
    private Map<String, String> getMaterialCodeNameMap(Collection<String> codes) {
        return materialService.lambdaQuery()
                .select(MaterialEntity::getCode, MaterialEntity::getName)
                .in(MaterialEntity::getCode, codes)
                .list().stream().collect(Collectors.toMap(MaterialEntity::getCode, MaterialEntity::getName));
    }

    @Override
    public ResponseData facProductionDetail(String gridIds) {
        //产线工位生产情况：
        //组装产线各个工位当日的产品完成情况；通过堆积柱形图展示
        //
        //X轴为对应工位，工位名称展示5个汉字，多余的用...填充页面最多展示7个工位，若工位超过7个，15s自动切换页面展示
        //Y轴为工位上的过站数量，需要去重，需要自适应，Y轴展示最大值为工位过站数量的总数最大值
        //产线上当日存在生产多个工单的情况时，右上角标记工单编号与产品名称
        //若有多个工单，不同颜色进行区分，同时堆叠柱状图展示；

        Date today = new Date();
        // yyyy-MM-dd 00:00:00
        Date todayZero = DateUtil.formatToDate(today, DateUtil.DATETIME_FORMAT_ZERO);

        List<Integer> gridIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(gridIds)) {
            gridIdList = Arrays.stream(gridIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        }
        // 查询车间对应的全部工位
        List<FacilitiesEntity> facList = facilitiesService.lambdaQuery()
                .select(FacilitiesEntity::getFid, FacilitiesEntity::getFname, FacilitiesEntity::getSeq)
                .in(!CollectionUtils.isEmpty(gridIdList), FacilitiesEntity::getGid, gridIdList)
                .list();
        facList.sort(Comparator.comparing(FacilitiesEntity::getSeq, Comparator.nullsLast(Integer::compareTo)));

        // 查询今日全部扫码记录，类型是流水码
        List<ProductFlowCodeRecordEntity> allFlowCodeRecords = productFlowCodeRecordService.lambdaQuery()
                .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                .between(ProductFlowCodeRecordEntity::getReportTime, todayZero, today)
                .list();
        // 按工位分组
        Map<Integer, List<ProductFlowCodeRecordEntity>> facIdRecordsMap = allFlowCodeRecords.stream()
                .filter(res -> Objects.nonNull(res.getFacId()))
                .collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getFacId));

        // 查询产品名称
        Map<String, String> codeMaterialNameMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(allFlowCodeRecords)) {
            Set<String> codes = allFlowCodeRecords.stream().map(ProductFlowCodeRecordEntity::getMaterialCode).collect(Collectors.toSet());
            codeMaterialNameMap = getMaterialCodeNameMap(codes);
        }

        List<FacProductDetailVO> vos = new ArrayList<>();
        for (FacilitiesEntity facilitiesEntity : facList) {
            Integer facId = facilitiesEntity.getFid();
            String facName = facilitiesEntity.getFname();

            // 该工位对应的扫码记录, 按生产流水码去重,得到工单今日的完成数量
            List<ProductFlowCodeRecordEntity> productFlowCodeRecords = facIdRecordsMap.getOrDefault(facId, new ArrayList<>());
            // 按工单分组
            Map<String, List<ProductFlowCodeRecordEntity>> workOrderNumberRecordsMap = productFlowCodeRecords.stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getRelationNumber));

            List<FacProductDetailVO.ProductDetail> list = new ArrayList<>();
            for (Map.Entry<String, List<ProductFlowCodeRecordEntity>> entry : workOrderNumberRecordsMap.entrySet()) {
                String workOrderNumber = entry.getKey();
                long count = entry.getValue().stream().map(ProductFlowCodeRecordEntity::getProductFlowCode).distinct().count();
                list.add(FacProductDetailVO.ProductDetail.builder()
                        .workOrderNumber(workOrderNumber)
                        .materialCode(entry.getValue().get(0).getMaterialCode())
                        .materialName(codeMaterialNameMap.get(entry.getValue().get(0).getMaterialCode()))
                        .completeQuantity(count)
                        .build());
            }
            //过滤掉过站记录为空德
            if (!CollectionUtils.isEmpty(list)) {
                vos.add(FacProductDetailVO.builder()
                        .facId(facId)
                        .facName(facName)
                        .productDetails(list)
                        .build());
            }
        }
        return ResponseData.success(vos);
    }


    @Override
    public ResponseData getAttendanceInfo(String lineIds, String lineCodes) {
        List<Integer> lineIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(lineCodes)) {
            List<ProductionLineEntity> productionLineEntities = productionLineService.lambdaQuery().in(ProductionLineEntity::getProductionLineCode, Arrays.asList(lineCodes.split(Constant.SEP))).list();
            if (!CollectionUtils.isEmpty(productionLineEntities)) {
                lineIdList = productionLineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            }
        }
        if (StringUtils.isNotBlank(lineIds)) {
            lineIdList.addAll(Arrays.stream(lineIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList()));
        }
        Date nowDate = new Date();
        Date recordDate = dictService.getRecordDate(nowDate);
        // 员工打卡记录
        List<AttendanceEntity> list = attendanceService.lambdaQuery()
                .eq(!CollectionUtils.isEmpty(lineIdList), AttendanceEntity::getWorkCenterType, WorkCenterTypeEnum.LINE.getCode())
                .in(!CollectionUtils.isEmpty(lineIdList), AttendanceEntity::getRelateId, lineIdList)
                .eq(AttendanceEntity::getRecordDate, recordDate)
                .list();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("attendanceNum", getAttendanceNum(list));
        return ResponseData.success(jsonObject);
    }

    /**
     * 产出工时：当天工单的产出工时
     * 在岗人数：当天打卡人数去重
     */
    @Override
    public ResponseData personnelOutputChangeTrend(String lineIds) {
        // 七天
        // yyyy-MM-dd
        Date endDate = DateUtil.formatToDate(new Date(), DateUtil.DATE_FORMAT);
        Date startDate = DateUtil.addDay(endDate, -6);

        // 这段时间内的打卡记录
        List<AttendanceEntity> attendanceList = attendanceService.lambdaQuery()
                .ge(AttendanceEntity::getRecordDate, startDate)
                .le(AttendanceEntity::getRecordDate, endDate)
                .list();
        //过滤出产线内的
        if (StringUtils.isNotBlank(lineIds) && !CollectionUtils.isEmpty(attendanceList)) {
            List<String> lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
            List<String> workOrders = attendanceList.stream().map(AttendanceEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
            List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .in(WorkOrderEntity::getWorkOrderNumber, workOrders)
                    .in(WorkOrderEntity::getLineId, lineIdList)
                    .list();
            List<String> workOrderNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            attendanceList = attendanceList.stream().filter(o -> workOrderNumbers.contains(o.getWorkOrderNumber())).collect(Collectors.toList());
        }
        // 按天分组
        Map<Date, List<AttendanceEntity>> recordDateMap = attendanceList.stream().collect(Collectors.groupingBy(AttendanceEntity::getRecordDate));

        List<RecordWorkOrderDayCountEntity> workOrderDayCounts = recordWorkOrderDayCountService.lambdaQuery()
                .ge(RecordWorkOrderDayCountEntity::getTime, startDate)
                .le(RecordWorkOrderDayCountEntity::getTime, endDate)
                .list();
        // 按天分组
        Map<Date, List<RecordWorkOrderDayCountEntity>> recordDateWorkOrderDayCountsMap = workOrderDayCounts.stream().collect(Collectors.groupingBy(RecordWorkOrderDayCountEntity::getTime));

        List<ProductionEfficiencyVO.PersonnelOutputChange> list = new ArrayList<>();
        Date tempDate = startDate;
        while (tempDate.compareTo(endDate) <= 0) {
            // 当天员工打卡记录
            List<AttendanceEntity> recordDateAttendances = recordDateMap.getOrDefault(tempDate, new ArrayList<>());

            // 产出工时：当天每个工单产出工时之和
            double outputHours = 0.0;
            List<RecordWorkOrderDayCountEntity> recordDateWorkOrderDayCounts = recordDateWorkOrderDayCountsMap.getOrDefault(tempDate, new ArrayList<>());
            if (!CollectionUtils.isEmpty(recordDateWorkOrderDayCounts)) {
                Map<String, List<RecordWorkOrderDayCountEntity>> workOrderNumberDayCountMap = recordDateWorkOrderDayCounts.stream().collect(Collectors.groupingBy(RecordWorkOrderDayCountEntity::getWorkOrderNumber));

                List<WorkOrderEntity> workOrderEntityList = workOrderService.lambdaQuery()
                        .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumberDayCountMap.keySet())
                        .list();
                // 查询工单物料在各产线上的产能
                List<CapacityEntity> materialCodeCapacityList = !CollectionUtils.isEmpty(workOrderEntityList) ? capacityService.lambdaQuery()
                        .in(CapacityEntity::getMaterialCode, workOrderEntityList.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet()))
                        .list() : new ArrayList<>();
                for (WorkOrderEntity workOrderEntity : workOrderEntityList) {
                    List<RecordWorkOrderDayCountEntity> dayCounts = workOrderNumberDayCountMap.getOrDefault(workOrderEntity.getWorkOrderNumber(), new ArrayList<>());

                    List<CapacityEntity> capacityList = materialCodeCapacityList.stream()
                            .filter(res -> Objects.nonNull(workOrderEntity.getLineId())
                                    && workOrderEntity.getMaterialCode().equals(res.getMaterialCode())
                                    && workOrderEntity.getLineCode().equals(res.getProductionBasicUnitCode()))
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(capacityList)) {
                        // 一个物料在一条产线上的产能只有一个
                        CapacityEntity capacityEntity = capacityList.get(0);
                        Double uph = Objects.isNull(capacityEntity.getCapacity()) ? null : capacityEntity.getCapacity();
                        Double hc = Objects.isNull(capacityEntity.getTheoryStaffNum()) ? null : Double.valueOf(capacityEntity.getTheoryStaffNum());
                        if (Objects.nonNull(uph) && Objects.nonNull(hc)) {
                            double count = dayCounts.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
                            double div = uph.equals(0.0) ? 0.0 : MathUtil.divideDouble(count, uph, 4);
                            double workOrderOutputHours = MathUtil.round(MathUtil.mul(div, hc), 1);
                            outputHours = MathUtil.add(outputHours, workOrderOutputHours);
                        }
                    }
                }
            }

            list.add(ProductionEfficiencyVO.PersonnelOutputChange.builder()
                    .recordDate(tempDate)
                    .attendanceNum(getAttendanceNum(recordDateAttendances))
                    .outputHours(outputHours)
                    .build());
            //循环条件
            tempDate = DateUtil.addDay(tempDate, 1);
        }
        return ResponseData.success(list);
    }

    /**
     * 获取打卡人数
     */
    private Long getAttendanceNum(List<AttendanceEntity> recordAttendances) {
        if (CollectionUtils.isEmpty(recordAttendances)) {
            return 0L;
        }
        // 员工打卡人数（去重）
        long userCount = recordAttendances.stream().map(AttendanceEntity::getUserId).filter(Objects::nonNull).distinct().count();
        // 临时工打卡人数（去重）
        long tempUserCount = recordAttendances.stream().map(AttendanceEntity::getTempUserName).filter(Objects::nonNull).distinct().count();
        return userCount + tempUserCount;
    }


    /**
     * 工单-生产效率组件
     * UPH：标准产能，用户配置
     * H/C：标准人数，用户配置
     * 投入人数：当天对应工单的打卡人数
     * 计划数量：工单的计划数量
     * 生产数量：工单的完成数量
     * 完成率：完成数量 / 计划数量
     * 合格率：总的完成数量/(总的完成数量+总的不合格数量)
     * 当日计划数：工单今日计划数
     * 当日生产数：工单今日完成数
     * 产出工时：生产数量/UPH每小时产能*HC标准人数
     * 投入工时：工单的累计工时求和
     * 生产效率：(产出工时/投入工时 - 1)*100%
     */
    @Override
    public ResponseData listWorkProductionEfficiency(String lineIds, String workOrderStates) {
        Date dateNow = new Date();
        // 2022-06-16 00:00:00
        Date recordDate = dictService.getRecordDate(dateNow);
        // 明天 2022-06-17 00:00:00
        Date tomorrowDate = DateUtil.addDay(recordDate, 1);
        // 投产+今日完成的工单
        List<String> lineIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(lineIds)) {
            lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
        }
        List<String> workOrderStateList = new ArrayList<>();
        if (StringUtils.isNotBlank(workOrderStates)) {
            workOrderStateList = Arrays.asList(workOrderStates.split(Constant.SEP));
        }
        List<WorkOrderEntity> workOrderEntityList = workOrderService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(lineIdList), WorkOrderEntity::getLineId, lineIdList)
                .in(!CollectionUtils.isEmpty(workOrderStateList), WorkOrderEntity::getState, workOrderStateList)
                //未选择状态时默认投产
                .in(StringUtils.isBlank(workOrderStates), WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode(),
                        WorkOrderStateEnum.FINISHED.getCode())
                .list();
        if (StringUtils.isBlank(workOrderStates)) {
            // 过滤不在今日完成的
            workOrderEntityList = workOrderEntityList.stream()
                    .filter(workOrder -> Objects.nonNull(workOrder.getStateChangeTime()))
                    .filter(workOrder -> WorkOrderStateEnum.INVESTMENT.getCode().equals(workOrder.getState())
                            || (WorkOrderStateEnum.FINISHED.getCode().equals(workOrder.getState())
                            && workOrder.getStateChangeTime().compareTo(recordDate) > 0
                            && workOrder.getStateChangeTime().compareTo(tomorrowDate) < 0))
                    .collect(Collectors.toList());
        }

        // 员工今日打卡记录
        List<AttendanceEntity> attendanceList = attendanceService.lambdaQuery()
                .eq(AttendanceEntity::getRecordDate, recordDate)
                .list();
        // 按工单分组
        Map<String, List<AttendanceEntity>> workOrderAttendancesMap = attendanceList.stream().collect(Collectors.groupingBy(AttendanceEntity::getWorkOrderNumber));

        // 今日生产
        List<RecordWorkOrderDayCountEntity> workOrderDayCounts = recordWorkOrderDayCountService.getByDateAndLineId(recordDate, null);
        // 按工单分组
        Map<String, List<RecordWorkOrderDayCountEntity>> workOrderNumberDayCountsMap = workOrderDayCounts.stream().collect(Collectors.groupingBy(RecordWorkOrderDayCountEntity::getWorkOrderNumber));

        // 今日计划
        List<WorkOrderPlanEntity> workOrderPlans = workOrderPlanService.lambdaQuery()
                .eq(WorkOrderPlanEntity::getTime, recordDate)
                .list();
        // 按工单分组
        Map<String, WorkOrderPlanEntity> workOrderNumberPlanMap = workOrderPlans.stream().collect(Collectors.toMap(WorkOrderPlanEntity::getWorkOrderNumber, v -> v, (oldValue, newValue) -> oldValue));

        // 查询工单物料在各产线上的产能
        List<CapacityEntity> materialCodeCapacityList = new ArrayList<>();
        Map<String, MetricsWorkOrderEntity> workOrderNumberRecordMap = new HashMap<>(workOrderEntityList.size());
        if (!CollectionUtils.isEmpty(workOrderEntityList)) {
            Set<String> codes = workOrderEntityList.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
            materialCodeCapacityList = capacityService.lambdaQuery()
                    .in(CapacityEntity::getMaterialCode, codes)
                    .list();
            List<String> workOrderNumbers = workOrderEntityList.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            // 该工单的累计投入, 停线时长 dfs_record_work_order_statistics
            workOrderNumberRecordMap = metricsWorkOrderService.lambdaQuery()
                    .in(MetricsWorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                    .list().stream().collect(Collectors.toMap(MetricsWorkOrderEntity::getWorkOrderNumber, v -> v));
        }
        //物料名称
        List<String> materialCodes = workOrderEntityList.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toList());
        List<MaterialEntity> materialEntities = materialService.lambdaQuery()
                .select(MaterialEntity::getCode, MaterialEntity::getName)
                .in(!CollectionUtils.isEmpty(materialCodes), MaterialEntity::getCode, materialCodes)
                .list();
        Map<String, String> materialMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, MaterialEntity::getName));
        List<ProductionEfficiencyVO.WorkProductionEfficiencyVO> vos = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : workOrderEntityList) {
            // 工单当日打卡
            List<AttendanceEntity> workOrderAttendances = workOrderAttendancesMap.getOrDefault(workOrderEntity.getWorkOrderNumber(), new ArrayList<>());
            // 该工单的累计投入, 停线时长
            MetricsWorkOrderEntity workOrderStatistics = workOrderNumberRecordMap.get(workOrderEntity.getWorkOrderNumber());
            Double inputHours = Objects.isNull(workOrderStatistics) || Objects.isNull(workOrderStatistics.getActualWorkingHour()) ? 0.0 : workOrderStatistics.getActualWorkingHour();
            Double stopDuration = Objects.isNull(workOrderStatistics) || Objects.isNull(workOrderStatistics.getStopLineHour()) ? 0.0 : workOrderStatistics.getStopLineHour();

            double finishCount = workOrderEntity.getFinishCount(), unqualified = workOrderEntity.getUnqualified();
            double passRate = MathUtil.divideDouble(finishCount, finishCount + unqualified, 4);

            double todayPlanQuantity = 0.0;
            //今天计划数
            WorkOrderPlanEntity workOrderPlan = workOrderNumberPlanMap.get(workOrderEntity.getWorkOrderNumber());
            if (Objects.nonNull(workOrderPlan)) {
                todayPlanQuantity = workOrderPlan.getPlanQuantity();
            } else {
                if (workOrderEntity.getStartDate().compareTo(DateUtil.addDate(recordDate, 1)) > 0
                        && !CollectionUtils.isEmpty(workOrderNumberDayCountsMap.get(workOrderEntity.getWorkOrderNumber()))) {
                    //提前计划数：计划开始时间大于今天，且今天有产量的工单，将这类型的工单所有计划产量进行累加
                    todayPlanQuantity = workOrderEntity.getPlanQuantity();
                } else if (workOrderEntity.getEndDate().compareTo(recordDate) < 0
                        && (Objects.isNull(workOrderEntity.getActualEndDate()) || recordDate.compareTo(DateUtil.formatToDate(workOrderEntity.getActualEndDate(), DateUtil.DATE_FORMAT)) == 0)) {
                    //延迟完成计划数：计划完成时间已过，还没结束或者今天结束的工单
                    //今天没结束，总数-今天之前完成的
                    List<RecordWorkOrderDayCountEntity> beforeTodayDayCounts = recordWorkOrderDayCountService.lambdaQuery()
                            .eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                            .lt(RecordWorkOrderDayCountEntity::getTime, recordDate)
                            .list();
                    double beforeTodayCount = beforeTodayDayCounts.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();
                    todayPlanQuantity = workOrderEntity.getPlanQuantity() - beforeTodayCount;
                    todayPlanQuantity = todayPlanQuantity >= 0 ? todayPlanQuantity : 0;
                }
            }

            List<RecordWorkOrderDayCountEntity> todayWorkOrderDayCounts = workOrderNumberDayCountsMap.getOrDefault(workOrderEntity.getWorkOrderNumber(), new ArrayList<>());
            double todayFinishCount = todayWorkOrderDayCounts.stream().mapToDouble(RecordWorkOrderDayCountEntity::getCount).sum();

            Double uph = null, hc = null, outputHours = null, productionEfficiency = null;
            List<CapacityEntity> capacityList = materialCodeCapacityList.stream()
                    .filter(res -> Objects.nonNull(workOrderEntity.getLineId())
                            && workOrderEntity.getMaterialCode().equals(res.getMaterialCode())
                            && workOrderEntity.getLineCode().equals(res.getProductionBasicUnitCode()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(capacityList)) {
                // 一个物料在一条产线上的产能只有一个
                CapacityEntity capacityEntity = capacityList.get(0);
                uph = Objects.isNull(capacityEntity.getCapacity()) ? null : capacityEntity.getCapacity();
                hc = Objects.isNull(capacityEntity.getTheoryStaffNum()) ? null : Double.valueOf(capacityEntity.getTheoryStaffNum());
                if (Objects.nonNull(uph) && Objects.nonNull(hc)) {
                    double div = uph.equals(0.0) ? 0.0 : MathUtil.divideDouble(finishCount, uph, 4);
                    outputHours = MathUtil.round(MathUtil.mul(div, hc), 1);
                    productionEfficiency = Double.valueOf(0.0).equals(inputHours) ? null : MathUtil.sub(MathUtil.divideDouble(outputHours, inputHours, 4), 1);
                }
            }

            vos.add(ProductionEfficiencyVO.WorkProductionEfficiencyVO.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                    .workOrderName(workOrderEntity.getWorkOrderName())
                    .lineName(workOrderEntity.getLineName())
                    .materialName(materialMap.get(workOrderEntity.getMaterialCode()))
                    .uph(uph)
                    .hc(hc)
                    .attendanceNum(getAttendanceNum(workOrderAttendances))
                    .planQuantity(workOrderEntity.getPlanQuantity())
                    .finishCount(finishCount)
                    .completionRate(MathUtil.divideDouble(finishCount, workOrderEntity.getPlanQuantity(), 4))
                    .passRate(passRate)
                    .todayPlanQuantity(todayPlanQuantity)
                    .todayFinishCount(todayFinishCount)
                    .outputHours(outputHours)
                    .inputHours(inputHours)
                    .productionEfficiency(productionEfficiency)
                    .stopDuration(stopDuration)
                    .build());
        }
        return ResponseData.success(vos);
    }

    @Override
    public ResponseData todayUnqualifiedTimes(String workOrderNumber) {
        //统计每小时报工不良次数
        LocalDateTime now = LocalDateTime.now();
        //获取前24小时的格式字符
        List<String> hourList = getHourList(now);
        //获取第23小时前字符
        String dateStr = now.minusHours(23).format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT_HOUR));
        List<RecordWorkOrderUnqualifiedEntity> list = recordWorkOrderUnqualifiedService.lambdaQuery()
                .ge(RecordWorkOrderUnqualifiedEntity::getCreateDate, dateStr)
                .eq(StringUtils.isNotBlank(workOrderNumber), RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, workOrderNumber)
                .list();
        SimpleDateFormat format = new SimpleDateFormat("HH");
        //不良报工次数
        Map<String, Long> timesMap = list.stream().collect(Collectors.groupingBy(o -> format.format(o.getCreateDate()), Collectors.counting()));
        //统计每小时合格率,报工表有手动报工与扫码报工
        //扫码报工
        List<ProductFlowCodeRecordEntity> recordEntities = productFlowCodeRecordService.lambdaQuery()
                .ne(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.DISABLE.getCode())
                .eq(ProductFlowCodeRecordEntity::getIsReport, true)
                .ge(ProductFlowCodeRecordEntity::getReportTime, dateStr)
                .eq(StringUtils.isNotBlank(workOrderNumber), ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber)
                .list();
        //按小时分组
        Map<String, List<ProductFlowCodeRecordEntity>> recordMap = recordEntities.stream().collect(Collectors.groupingBy(o -> format.format(o.getReportTime())));
        //计算合格率
        HashMap<String, Double> totalFinish = new HashMap<>();
        HashMap<String, Double> totalUnqualified = new HashMap<>();
        Set<Map.Entry<String, List<ProductFlowCodeRecordEntity>>> entries = recordMap.entrySet();
        for (Map.Entry<String, List<ProductFlowCodeRecordEntity>> entry : entries) {
            String key = entry.getKey();
            List<ProductFlowCodeRecordEntity> value = entry.getValue();
            long finish = value.stream().filter(o -> !o.getIsUnqualified()).map(ProductFlowCodeRecordEntity::getProductFlowCode).distinct().count();
            long unqualified = value.stream().filter(ProductFlowCodeRecordEntity::getIsUnqualified).map(ProductFlowCodeRecordEntity::getProductFlowCode).distinct().count();
            totalFinish.put(key, (double) finish);
            totalUnqualified.put(key, (double) unqualified);
        }
        //手动报工
        List<ReportLineEntity> reportLineEntities = reportLineService.lambdaQuery()
                .ge(ReportLineEntity::getReportTime, dateStr)
                .list();
        //按小时分组
        Map<String, List<ReportLineEntity>> reportLineMap = reportLineEntities.stream().collect(Collectors.groupingBy(o -> format.format(o.getReportTime())));
        Set<Map.Entry<String, List<ReportLineEntity>>> reportLineEntrySet = reportLineMap.entrySet();
        for (Map.Entry<String, List<ReportLineEntity>> entry : reportLineEntrySet) {
            String key = entry.getKey();
            List<ReportLineEntity> value = entry.getValue();
            double finish = value.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
            double unqualified = value.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
            totalFinish.merge(key, finish, Double::sum);
            totalUnqualified.merge(key, unqualified, Double::sum);
        }
        //遍历hourList
        ArrayList<TodayUnqualifiedTimeVO> timeVOS = new ArrayList<>();
        for (String hourStr : hourList) {
            TodayUnqualifiedTimeVO build = TodayUnqualifiedTimeVO.builder()
                    .hour(hourStr)
                    .build();
            if (timesMap.containsKey(hourStr)) {
                Long aLong = timesMap.get(hourStr);
                build.setTimes(aLong.intValue());
            }
            if (totalFinish.containsKey(hourStr)) {
                Double finish = totalFinish.get(hourStr);
                Double unqualified = totalUnqualified.get(hourStr);
                unqualified = unqualified == null ? 0 : unqualified;
                Double passRate = finish + unqualified == 0 ? 0.0 :
                        MathUtil.divideDouble(finish, finish + unqualified, 4, RoundingMode.HALF_UP);
                build.setPassRate(passRate);
            }
            timeVOS.add(build);
        }
        return ResponseData.success(timeVOS);
    }

    private List<String> getHourList(LocalDateTime time) {
        List<String> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH");
        for (int i = 0; i < 24; i++) {
            LocalDateTime hour = time.minusHours(i);
            list.add(hour.format(formatter));
        }
        Collections.reverse(list);
        return list;
    }


    @Override
    public ResponseData todayUnqualifiedTypes(String lineIds) {
        //获取首班时间
        Date beginTime = dictService.getDayOutputBeginTime(new Date());
        List<Integer> fids = new ArrayList<>();
        if (StringUtils.isNotBlank(lineIds)) {
            List<String> lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
            List<FacilitiesEntity> facilitiesEntities = facilitiesService.lambdaQuery()
                    .select(FacilitiesEntity::getFid)
                    .in(FacilitiesEntity::getProductionLineId, lineIdList)
                    .list();
            fids = facilitiesEntities.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());
        }
        List<RecordWorkOrderUnqualifiedEntity> list = recordWorkOrderUnqualifiedService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(fids), RecordWorkOrderUnqualifiedEntity::getFid, fids)
                .ge(RecordWorkOrderUnqualifiedEntity::getCreateDate, beginTime)
                .list();
        Map<String, List<RecordWorkOrderUnqualifiedEntity>> countMap = list.stream().collect(Collectors.groupingBy(RecordWorkOrderUnqualifiedEntity::getAbnormalName));
        List<UnqualifiedVO> vos = new ArrayList<>();
        Set<Map.Entry<String, List<RecordWorkOrderUnqualifiedEntity>>> entries = countMap.entrySet();
        for (Map.Entry<String, List<RecordWorkOrderUnqualifiedEntity>> entry : entries) {
            String unqualifiedName = entry.getKey();
            List<RecordWorkOrderUnqualifiedEntity> value = entry.getValue();
            //同一个不良类型对流水号去重
            long count = value.stream().map(RecordWorkOrderUnqualifiedEntity::getSequenceId).distinct().count();
            vos.add(UnqualifiedVO.builder().unqualifiedName(unqualifiedName).num(count).build());
        }
        //排序
        vos.sort(Comparator.comparing(UnqualifiedVO::getNum).reversed());
        if (vos.size() > 5) {
            List<UnqualifiedVO> top5 = vos.stream().limit(5).collect(Collectors.toList());
            vos.removeAll(top5);
            long sum = vos.stream().mapToLong(UnqualifiedVO::getNum).sum();
            top5.add(UnqualifiedVO.builder().unqualifiedName("其他").num(sum).build());
            return ResponseData.success(top5);
        }
        return ResponseData.success(vos);
    }

    @Override
    public ResponseData todayUnqualifiedMaterials(String lineIds) {
        //获取首班时间
        Date beginTime = dictService.getDayOutputBeginTime(new Date());
        List<Integer> fids = new ArrayList<>();
        if (StringUtils.isNotBlank(lineIds)) {
            List<String> lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
            List<FacilitiesEntity> facilitiesEntities = facilitiesService.lambdaQuery()
                    .select(FacilitiesEntity::getFid)
                    .in(FacilitiesEntity::getProductionLineId, lineIdList)
                    .list();
            fids = facilitiesEntities.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());
        }
        List<RecordWorkOrderUnqualifiedEntity> list = recordWorkOrderUnqualifiedService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(fids), RecordWorkOrderUnqualifiedEntity::getFid, fids)
                .ge(RecordWorkOrderUnqualifiedEntity::getCreateDate, beginTime)
                .list();
        Map<String, List<RecordWorkOrderUnqualifiedEntity>> countMap = list.stream().collect(Collectors.groupingBy(RecordWorkOrderUnqualifiedEntity::getMaterialCode));
        List<MaterialOutputDTO> vos = new ArrayList<>();
        Set<Map.Entry<String, List<RecordWorkOrderUnqualifiedEntity>>> entries = countMap.entrySet();
        for (Map.Entry<String, List<RecordWorkOrderUnqualifiedEntity>> entry : entries) {
            String materialCode = entry.getKey();
            List<RecordWorkOrderUnqualifiedEntity> value = entry.getValue();
            //同一个物料对流水号去重
            long count = value.stream().map(RecordWorkOrderUnqualifiedEntity::getSequenceId).distinct().count();
            vos.add(MaterialOutputDTO.builder().materialCode(materialCode).count((double) count).build());
        }
        //排序
        List<MaterialOutputDTO> top5 = vos.stream()
                .sorted(Comparator.comparing(MaterialOutputDTO::getCount).reversed())
                .limit(5).collect(Collectors.toList());
        //处理物料名称
        List<String> materialCodes = top5.stream().map(MaterialOutputDTO::getMaterialCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(materialCodes)) {
            List<MaterialEntity> materialEntities = materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list();
            Map<String, String> map = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, MaterialEntity::getName));
            for (MaterialOutputDTO dto : top5) {
                dto.setMaterialName(map.get(dto.getMaterialCode()));
            }
        }
        return ResponseData.success(top5);
    }

    @Override
    public ResponseData todayPerHourPassRate(String lineIds, String workOrderNumbers) {
        //获取0点字符
        String dateStr = LocalDateTime.of(now(), LocalTime.MIN).format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT_HOUR));
        // 获取投产/当天完成的工单
        List<String> lineIdList = null;
        if (StringUtils.isNotBlank(lineIds)) {
            lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
        }
        List<String> workOrderNumberList = null;
        if (StringUtils.isNotBlank(workOrderNumbers)) {
            workOrderNumberList = Arrays.asList(workOrderNumbers.split(Constant.SEP));
        }
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getWorkOrderName, WorkOrderEntity::getState)
                .in(!CollectionUtils.isEmpty(lineIdList), WorkOrderEntity::getLineId, lineIdList)
                .in(!CollectionUtils.isEmpty(workOrderNumberList), WorkOrderEntity::getWorkOrderNumber, workOrderNumberList)
                .and(wp -> wp.eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                        .or(w -> w.eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode())
                                .ge(WorkOrderEntity::getActualEndDate, dateStr)))
                .list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return ResponseData.success(new ArrayList<>());
        }
        workOrderNumberList = workOrderEntities.stream()
                .filter(workOrderEntity -> workOrderEntity.getState().equals(WorkOrderStateEnum.INVESTMENT.getCode()))
                .map(WorkOrderEntity::getWorkOrderNumber)
                .collect(Collectors.toList());
        //统计每小时合格率,报工表有手动报工与扫码报工
        //扫码报工
        List<ProductFlowCodeRecordEntity> recordEntities = productFlowCodeRecordService.lambdaQuery()
                .ne(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.DISABLE.getCode())
                .ge(ProductFlowCodeRecordEntity::getReportTime, dateStr)
                .in(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumberList)
                .and(wp -> wp.eq(ProductFlowCodeRecordEntity::getIsReport, true)
                        .or().eq(ProductFlowCodeRecordEntity::getIsUnqualified, true))
                .list();
        //按工单分组
        Map<String, List<ProductFlowCodeRecordEntity>> recordMap = recordEntities.stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getRelationNumber));
        //手动报工
        List<ReportLineEntity> reportLineEntities = reportLineService.lambdaQuery()
                .ge(ReportLineEntity::getReportTime, dateStr)
                .in(ReportLineEntity::getWorkOrder, workOrderNumberList)
                .list();
        //按工单分组
        Map<String, List<ReportLineEntity>> reportLineMap = reportLineEntities.stream().collect(Collectors.groupingBy(ReportLineEntity::getWorkOrder));
        List<TodayWokOrderPassRateVO> rateVOS = getWorkOrderPassRateList(workOrderEntities, recordMap, reportLineMap);
        return ResponseData.success(rateVOS);
    }

    private List<TodayWokOrderPassRateVO> getWorkOrderPassRateList(List<WorkOrderEntity> workOrderEntities, Map<String, List<ProductFlowCodeRecordEntity>> recordMap, Map<String, List<ReportLineEntity>> reportLineMap) {
        SimpleDateFormat format = new SimpleDateFormat("HH");
        List<String> hourList = get24HourList();
        List<TodayWokOrderPassRateVO> rateVOS = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            String workOrder = workOrderEntity.getWorkOrderNumber();
            HashMap<String, Double> totalFinish = new HashMap<>();
            HashMap<String, Double> totalUnqualified = new HashMap<>();
            //扫码报工
            if (recordMap.containsKey(workOrder)) {
                List<ProductFlowCodeRecordEntity> codeRecordEntities = recordMap.get(workOrder);
                //按小时分组
                Map<String, List<ProductFlowCodeRecordEntity>> recordHourMap = codeRecordEntities.stream().collect(Collectors.groupingBy(o -> format.format(o.getReportTime())));
                Set<Map.Entry<String, List<ProductFlowCodeRecordEntity>>> entries = recordHourMap.entrySet();
                for (Map.Entry<String, List<ProductFlowCodeRecordEntity>> entry : entries) {
                    String key = entry.getKey();
                    List<ProductFlowCodeRecordEntity> value = entry.getValue();
                    //按小时分组
                    long finish = value.stream().filter(ProductFlowCodeRecordEntity::getIsReport).map(ProductFlowCodeRecordEntity::getProductFlowCode).distinct().count();
                    long unqualified = value.stream().filter(ProductFlowCodeRecordEntity::getIsUnqualified).map(ProductFlowCodeRecordEntity::getProductFlowCode).distinct().count();
                    totalFinish.put(key, (double) finish);
                    totalUnqualified.put(key, (double) unqualified);
                }
            }
            //手动报工
            if (reportLineMap.containsKey(workOrder)) {
                List<ReportLineEntity> lineEntities = reportLineMap.get(workOrder);
                Map<String, List<ReportLineEntity>> reportLineHourMap = lineEntities.stream().collect(Collectors.groupingBy(o -> format.format(o.getCreateTime())));
                Set<Map.Entry<String, List<ReportLineEntity>>> reportLineEntrySet = reportLineHourMap.entrySet();
                for (Map.Entry<String, List<ReportLineEntity>> entry : reportLineEntrySet) {
                    String key = entry.getKey();
                    List<ReportLineEntity> value = entry.getValue();
                    double finish = value.stream().filter(o -> o.getFinishCount() != null).mapToDouble(ReportLineEntity::getFinishCount).sum();
                    double unqualified = value.stream().filter(o -> o.getUnqualified() != null).mapToDouble(ReportLineEntity::getUnqualified).sum();
                    totalFinish.merge(key, finish, Double::sum);
                    totalUnqualified.merge(key, unqualified, Double::sum);
                }
            }

            //遍历hourList
            ArrayList<TodayUnqualifiedTimeVO> timeVOS = new ArrayList<>();
            if (workOrderEntity.getState().equals(WorkOrderStateEnum.FINISHED.getCode())) {
                rateVOS.add(TodayWokOrderPassRateVO.builder().workOrderNumber(workOrder).workOrderName(workOrderEntity.getWorkOrderName())
                        .vos(timeVOS).build());
                continue;
            }
            for (String hourStr : hourList) {
                TodayUnqualifiedTimeVO build = TodayUnqualifiedTimeVO.builder()
                        .hour(hourStr)
                        .build();
                //计算合格率
                if (totalFinish.containsKey(hourStr)) {
                    Double finish = totalFinish.get(hourStr);
                    Double unqualified = totalUnqualified.get(hourStr);
                    unqualified = unqualified == null ? 0 : unqualified;
                    Double passRate = finish + unqualified == 0 ? null :
                            MathUtil.divideDouble(finish, finish + unqualified, 4, RoundingMode.HALF_UP);
                    build.setPassRate(passRate);
                }
                timeVOS.add(build);
            }
            rateVOS.add(TodayWokOrderPassRateVO.builder().workOrderNumber(workOrder).workOrderName(workOrderEntity.getWorkOrderName())
                    .vos(timeVOS).build());
        }
        return rateVOS;
    }

    private List<String> get24HourList() {
        LocalDateTime min = LocalDateTime.of(now(), LocalTime.MIN);
        List<String> list = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH");
        LocalTime nowTime = LocalTime.now();
        for (int i = 0; i < nowTime.getHour() + 1; i++) {
            LocalDateTime hour = min.plusHours(i);
            list.add(hour.format(formatter));
        }
        return list;
    }

    @Override
    public ResponseData getUnqualifiedPerDay(String lineIds) {
        List<String> lineIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(lineIds)) {
            lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
        }
        Date date = DateUtil.initDateByDay();
        Date startDate = DateUtil.addDay(date, -30);
        //获取最近30天数据
        List<RecordWorkOrderLineDayCountEntity> countByDays = recordWorkOrderLineDayCountService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(lineIdList), RecordWorkOrderLineDayCountEntity::getLineId, lineIdList)
                .between(RecordWorkOrderLineDayCountEntity::getTime, startDate, date)
                .list();
        List<UnqualifiedTimeVO> dtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(countByDays)) {
            return ResponseData.success(dtos);
        }
        SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATE_FORMAT_SLASH);
        Map<String, Double> countMap = countByDays.stream().collect(Collectors.groupingBy(o -> format.format(o.getTime()),
                Collectors.summingDouble(RecordWorkOrderLineDayCountEntity::getCount)));
        Map<String, Double> unqualifiedMap = countByDays.stream().collect(Collectors.groupingBy(o -> format.format(o.getTime()),
                Collectors.summingDouble(RecordWorkOrderLineDayCountEntity::getUnqualified)));
        while (startDate.before(date)) {
            startDate = DateUtil.addDay(startDate, 1);
            String format1 = DateUtil.format(startDate, DateUtil.DATE_FORMAT_SLASH);
            Integer count = countMap.containsKey(format1) ? countMap.get(format1).intValue() : 0;
            Integer unqualified = unqualifiedMap.containsKey(format1) ? unqualifiedMap.get(format1).intValue() : 0;
            Double passRate = count + unqualified == 0 ? null :
                    MathUtil.divideDouble(count, count + unqualified, 4, RoundingMode.HALF_UP);
            dtos.add(UnqualifiedTimeVO.builder().dateStr(format1).unqualified(unqualified).passRate(passRate).build());
        }
        dtos.sort(Comparator.comparing(UnqualifiedTimeVO::getDateStr));
        return ResponseData.success(dtos);
    }

    @Override
    public ResponseData monthUnqualifiedTypes(String lineIds) {
        List<String> lineIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(lineIds)) {
            lineIdList = Arrays.asList(lineIds.split(Constant.SEP));
        }
        Date date = DateUtil.initDateByDay();
        // 获取前三十天
        Date startDate = DateUtil.addDate(date, -29);
        List<FacilitiesEntity> facilities = facilitiesService.lambdaQuery().in(!CollectionUtils.isEmpty(lineIdList), FacilitiesEntity::getProductionLineId, lineIdList).list();
        List<Integer> fids = facilities.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fids)) {
            return ResponseData.success(new ArrayList<>());
        }
        List<RecordWorkOrderUnqualifiedEntity> list = recordWorkOrderUnqualifiedService.lambdaQuery()
                .in(RecordWorkOrderUnqualifiedEntity::getFid, fids)
                .between(RecordWorkOrderUnqualifiedEntity::getRecordDate, DateUtil.dateSub(startDate), DateUtil.dateSub(date))
                .list();
        Map<String, List<RecordWorkOrderUnqualifiedEntity>> countMap = list.stream().collect(Collectors.groupingBy(RecordWorkOrderUnqualifiedEntity::getAbnormalName));
        List<UnqualifiedVO> vos = new ArrayList<>();
        Set<Map.Entry<String, List<RecordWorkOrderUnqualifiedEntity>>> entries = countMap.entrySet();
        int totalCount = 0;
        for (Map.Entry<String, List<RecordWorkOrderUnqualifiedEntity>> entry : entries) {
            String unqualifiedName = entry.getKey();
            List<RecordWorkOrderUnqualifiedEntity> value = entry.getValue();
            //同一个不良类型对流水号去重
            long count = value.stream().map(RecordWorkOrderUnqualifiedEntity::getSequenceId).distinct().count();
            totalCount += count;
            vos.add(UnqualifiedVO.builder().unqualifiedName(unqualifiedName).num(count).build());
        }
        //排序
        vos.sort(Comparator.comparing(UnqualifiedVO::getNum).reversed());
        int currentTotalCount = 0;
        if (totalCount == 0) {
            return ResponseData.success(vos);
        }
        // 比率 (当前维修项目+排在当前维修项目之前的不良数之和)/本月所有不良产品的次数
        for (UnqualifiedVO o : vos) {
            currentTotalCount += o.getNum();
            Double rate = MathUtil.divideDouble(currentTotalCount, totalCount, 4, RoundingMode.HALF_UP);
            o.setRate(rate);
        }
        return ResponseData.success(vos);
    }

    @Override
    public ResponseData monthAlarmDeviceStatistics(String lineIds) {
        List<Integer> lineIdList = getLineIds(lineIds);
        // 获取当前的自然月
        Date now = new Date();
        Date startTime = DateUtil.getFirstDayDateOfMonth(now);
        List<AlarmEntity> allAlarmEntities = alarmService.lambdaQuery()
                .le(AlarmEntity::getAlarmReportTime, now)
                .ge(AlarmEntity::getAlarmReportTime, startTime)
                .isNotNull(AlarmEntity::getDeviceId)
                .in(!CollectionUtils.isEmpty(lineIdList), AlarmEntity::getProductionLineId, lineIdList)
                .list();
        // 按设备分组
        Map<Integer, List<AlarmEntity>> deviceAlarmMap = allAlarmEntities.stream().collect(Collectors.groupingBy(AlarmEntity::getDeviceId));

        // 设备告警时长 - 列表
        List<AlarmDeviceVO> result = deviceAlarmMap.keySet().stream().map(deviceId -> {
            List<AlarmEntity> alarmEntities = deviceAlarmMap.get(deviceId);
            long durationMs = getDurationMs(now, alarmEntities);
            return AlarmDeviceVO.builder()
                    .deviceId(deviceId)
                    .deviceName(alarmEntities.stream().findFirst().map(AlarmEntity::getDeviceName).orElse(null))
                    .durationMs(durationMs)
                    .durationMin(durationMs / 1000 / 60)
                    .build();
            // 按照时长倒序
        }).sorted(Comparator.comparingLong(AlarmDeviceVO::getDurationMs).reversed()).collect(Collectors.toList());

        // 求 '柏拉图分布'
        long total = result.stream().mapToLong(AlarmDeviceVO::getDurationMs).sum();
        long sum = 0;
        for (AlarmDeviceVO vo : result) {
            long cur = vo.getDurationMs();
            sum += cur;
            vo.setRate(NullableDouble.of((double) sum).div((double) total).cal());
        }
        return ResponseData.success(result);
    }

    @Override
    public ResponseData monthAlarmFacilityStatistics(String lineIds) {
        List<Integer> lineIdList = getLineIds(lineIds);
        // 获取当前的自然月
        Date now = new Date();
        Date startTime = DateUtil.getFirstDayDateOfMonth(now);
        List<AlarmEntity> allAlarmEntities = alarmService.lambdaQuery()
                .le(AlarmEntity::getAlarmReportTime, now)
                .ge(AlarmEntity::getAlarmReportTime, startTime)
                .in(!CollectionUtils.isEmpty(lineIdList), AlarmEntity::getProductionLineId, lineIdList)
                .isNotNull(AlarmEntity::getFid)
                .list();

        // 按工位分组
        Map<Integer, List<AlarmEntity>> deviceAlarmMap = allAlarmEntities.stream().collect(Collectors.groupingBy(AlarmEntity::getFid));

        List<AlarmFacilityVO> result = deviceAlarmMap.keySet().stream().map(fid -> {
            List<AlarmEntity> alarmEntities = deviceAlarmMap.get(fid);
            long durationMs = getDurationMs(now, alarmEntities);
            return AlarmFacilityVO.builder()
                    .fid(fid)
                    .fname(alarmEntities.stream().findFirst().map(AlarmEntity::getFname).orElse(null))
                    .durationMs(durationMs)
                    .durationMin(durationMs / 1000 / 60)
                    .alarmCount(alarmEntities.size())
                    .build();
        }).collect(Collectors.toList());

        return ResponseData.success(result);
    }


    @Override
    public ResponseData weekAlarmLineStatistics(String lineIds) {
        List<Integer> lineIdList = getLineIds(lineIds);
        Date now = new Date();

        // 最近7天
        int deltaDay = 7;
        // 今日的记录时间
        Date todayRecord = dictService.getRecordDate(now);
        // 起始时间：最早日期的首班开始时间
        Date startTime = dictService.getDayOutputBeginTime(DateUtil.addDay(now, -deltaDay));
        List<AlarmEntity> allAlarmEntities = alarmService.lambdaQuery()
                .le(AlarmEntity::getAlarmReportTime, now)
                .ge(AlarmEntity::getAlarmReportTime, startTime)
                .in(!CollectionUtils.isEmpty(lineIdList), AlarmEntity::getProductionLineId, lineIdList)
                .isNotNull(AlarmEntity::getDeviceId)
                .list();
        // 根据日期分组
        Map<Date, List<AlarmEntity>> dateAlarmMap = allAlarmEntities.stream().collect(Collectors.groupingBy(alarm -> dictService.getRecordDate(alarm.getAlarmReportTime())));
        // 产量
        List<RecordWorkOrderLineDayCountEntity> allCounts = recordWorkOrderLineDayCountService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(lineIdList), RecordWorkOrderLineDayCountEntity::getLineId, lineIdList)
                .le(RecordWorkOrderLineDayCountEntity::getTime, now)
                .ge(RecordWorkOrderLineDayCountEntity::getTime, startTime)
                .list();
        Map<Date, List<RecordWorkOrderLineDayCountEntity>> dayCountsMap = allCounts.stream().collect(Collectors.groupingBy(RecordWorkOrderLineDayCountEntity::getTime));
        List<AlarmLineVO> result = IntStream.range(0, deltaDay).mapToObj(i -> {
            // 某一天的记录日期
            Date curRecord = DateUtil.addDay(todayRecord, -i);
            List<AlarmEntity> alarmEntities = dateAlarmMap.getOrDefault(curRecord, Collections.emptyList());
            // 某一天的结束统计时间
            Date curEndTime;
            if (i == 0) {
                // 如果是今天
                curEndTime = now;
            } else {
                // 某一天的末班时间
                curEndTime = DateUtil.addDate(dictService.getBeginTimeByReportDate(curRecord), 1);
            }
            long durationMs = getDurationMs(curEndTime, alarmEntities);
            // 产量统计
            List<RecordWorkOrderLineDayCountEntity> counts = dayCountsMap.getOrDefault(curRecord, Collections.emptyList());
            double productCount = counts.stream().mapToDouble(e -> Optional.ofNullable(e.getCount()).orElse(0d)).sum();
            return AlarmLineVO.builder()
                    .durationMs(durationMs)
                    .durationMin(durationMs / 1000 / 60)
                    .productCount(productCount)
                    .recordDate(curRecord)
                    .build();
        }).collect(Collectors.toList());
        // 反转
        Collections.reverse(result);
        return ResponseData.success(result);
    }

    private List<Integer> getLineIds(String lineIds) {
        if (StringUtils.isNotBlank(lineIds)) {
            try {
                return Arrays.stream(lineIds.split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toList());
            } catch (Exception e) {
                throw new ResponseException("入参: " + lineIds + " 格式异常");
            }
        } else {
            return Collections.emptyList();
        }
    }

    private long getDurationMs(Date now, List<AlarmEntity> alarmEntities) {
        if (CollectionUtils.isEmpty(alarmEntities)) {
            return 0;
        }
        return alarmEntities.stream().filter(alarm -> alarm.getAlarmReportTime() != null).map(alarm -> {
            Date t1 = alarm.getAlarmReportTime();
            Date t2 = Optional.ofNullable(alarm.getAlarmRecoveryTime()).orElse(now);
            return t2.getTime() - t1.getTime();
        }).reduce(Long::sum).orElse(0L);
    }

    /**
     * 工单工序过站统计
     *
     * @param gridIds
     * @return
     */
    @Override
    public ResponseData workOrderProcedureCount(String gridIds) {
        List<WorkOrderProcedureCountVO> vos = getWorkOrderProcedureCount(gridIds, null);
        return ResponseData.success(vos);
    }

    private List<WorkOrderProcedureCountVO> getWorkOrderProcedureCount(String gridIds, Date date) {
        List<Integer> gridIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(gridIds)) {
            gridIdList = Arrays.stream(gridIds.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        }
        // 查询车间对应的全部产线
        List<ProductionLineEntity> lineEntities = lineService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(gridIdList), ProductionLineEntity::getGid, gridIdList)
                .list();
        if (CollectionUtils.isEmpty(lineEntities)) {
            return new ArrayList<>();
        }
        //查询产线的投产工单
        List<Integer> lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .in(WorkOrderEntity::getLineId, lineIds)
                .orderByDesc(WorkOrderEntity::getWorkOrderId)
                .list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return new ArrayList<>();
        }
        return getWorkOrderProcedureCountList(workOrderEntities, date);
    }


    private List<WorkOrderProcedureCountVO> getWorkOrderProcedureCountList(List<WorkOrderEntity> workOrderEntities, Date date) {
        List<WorkOrderProcedureCountVO> vos = new ArrayList<>();
        // 获取当天的开始时间，如果date为null，则dayBegin为null
        Date dayBegin = date == null ? null : DateUtil.getDayBegin(new Date());

        // 获取所有工单的物料编码集合
        Set<String> materialCodes = workOrderEntities.stream()
                .map(WorkOrderEntity::getMaterialCode)
                .collect(Collectors.toSet());

        // 查询物料编码对应的物料名称，并存入Map中
        Map<String, String> materialMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list()
                .stream()
                .collect(Collectors.toMap(MaterialEntity::getCode, MaterialEntity::getName));

        // 遍历每个工单
        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            // 创建工单统计对象
            WorkOrderProcedureCountVO countVO = WorkOrderProcedureCountVO.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                    .materialName(materialMap.getOrDefault(workOrderEntity.getMaterialCode(), null))
                    .build();
            //获取工单关联工艺路线
//            List<WorkOrderProcedureRelationEntity> workOrderProcedureRelationEntities = workOrderProcedureRelationService.getWorkOrderProcedureRelationEntities(countVO.getWorkOrderNumber());
//            if(CollectionUtils.isEmpty(workOrderProcedureRelationEntities)){
//                continue;
//            }
//
//            List<WorkOrderProcedureCountVO.FacProductDetail> facProductDetails = workOrderProcedureRelationEntities.stream().map(workOrderProcedureRelationEntity -> {
//                List<MetricsWorkOrderProcedureEntity> list = metricsWorkOrderProcedureService.lambdaQuery()
//                        .eq(MetricsWorkOrderProcedureEntity::getWorkOrderNumber, countVO.getWorkOrderNumber())
//                        .eq(MetricsWorkOrderProcedureEntity::getCraftProcedureId, workOrderProcedureRelationEntity.getCraftProcedureId())
//                        .list();
//                if (CollectionUtils.isEmpty(list)) {
//                    return WorkOrderProcedureCountVO.FacProductDetail.builder()
//                            .facId(workOrderProcedureRelationEntity.getCraftProcedureId())
//                            .facName(workOrderProcedureRelationEntity.getCraftProcedureName())
//                            .build();
//                }
//                MetricsWorkOrderProcedureEntity metricsWorkOrderProcedureEntity = list.get(0);
//                return WorkOrderProcedureCountVO.FacProductDetail.builder()
//                        .facId(workOrderProcedureRelationEntity.getCraftProcedureId())
//                        .facName(workOrderProcedureRelationEntity.getCraftProcedureName())
//                        .completeCount(metricsWorkOrderProcedureEntity.getCrossingQuantity().longValue())
//                        .passRate(MathUtil.divideDouble(metricsWorkOrderProcedureEntity.getCrossingQuantity().longValue() -
//                                metricsWorkOrderProcedureEntity.getUnqualifiedQuantity().longValue(), metricsWorkOrderProcedureEntity.getCrossingQuantity().longValue(), 4, RoundingMode.HALF_UP))
//                        .build();
//            }).collect(Collectors.toList());
//            //计算一下 在制数
//
//            countVO.setFacProductDetails(facProductDetails);


            // 获取并排序工单对应产线的所有工位
            List<FacilitiesEntity> facilitiesEntities = getSortedFacilities(workOrderEntity.getLineId());

            // 获取工单的过站记录
            List<ProductFlowCodeRecordEntity> recordEntities = getRecordEntities(workOrderEntity.getWorkOrderNumber(), dayBegin);

            // 如果没有过站记录，直接创建空的工位产品详情并添加到结果列表中
            if (CollectionUtils.isEmpty(recordEntities)) {
                countVO.setFacProductDetails(createEmptyFacProductDetails(facilitiesEntities));
                vos.add(countVO);
                continue;
            }

            // 创建工位产品详情列表
            List<WorkOrderProcedureCountVO.FacProductDetail> details = createFacProductDetails(facilitiesEntities, recordEntities, workOrderEntity);
            // 设置工位产品详情并添加到结果列表中
            countVO.setFacProductDetails(details);

            // 计算直通率并设置到工单统计对象中
            countVO.setThroughRate(calculateThroughRate(recordEntities, workOrderEntity, date));


            vos.add(countVO);
        }
        return vos;
    }

    // 创建工单统计对象
    private WorkOrderProcedureCountVO createCountVO(WorkOrderEntity workOrderEntity, Map<String, String> materialMap) {
        return WorkOrderProcedureCountVO.builder()
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .materialName(materialMap.getOrDefault(workOrderEntity.getMaterialCode(), null))
                .build();
    }

    // 获取并按顺序排序工单对应产线的所有工位
    private List<FacilitiesEntity> getSortedFacilities(Integer lineId) {
        return facilitiesService.lambdaQuery()
                .eq(FacilitiesEntity::getProductionLineId, lineId)
                .list()
                .stream()
                .sorted(Comparator.comparing(FacilitiesEntity::getSeq, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    // 获取工单的过站记录
    private List<ProductFlowCodeRecordEntity> getRecordEntities(String workOrderNumber, Date dayBegin) {
        return productFlowCodeRecordService.lambdaQuery()
                .select(ProductFlowCodeRecordEntity::getProductFlowCode, ProductFlowCodeRecordEntity::getRelationNumber,
                        ProductFlowCodeRecordEntity::getFacId, ProductFlowCodeRecordEntity::getProdureId,
                        ProductFlowCodeRecordEntity::getMaterialCode, ProductFlowCodeRecordEntity::getReportTime
                        , ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordEntity::getIsReport
                        , ProductFlowCodeRecordEntity::getIsUnqualified, ProductFlowCodeRecordEntity::getIsMaintain
                )
                .eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber)
                .ge(dayBegin != null, ProductFlowCodeRecordEntity::getReportTime, dayBegin)
                .list();
    }

    // 创建空的工位产品详情列表
    private List<WorkOrderProcedureCountVO.FacProductDetail> createEmptyFacProductDetails(List<FacilitiesEntity> facilitiesEntities) {
        return facilitiesEntities.stream()
                .map(fac -> WorkOrderProcedureCountVO.FacProductDetail.builder()
                        .facId(fac.getFid())
                        .facName(fac.getFname())
                        .build())
                .collect(Collectors.toList());
    }

    // 创建工位产品详情列表
    private List<WorkOrderProcedureCountVO.FacProductDetail> createFacProductDetails(List<FacilitiesEntity> facilitiesEntities,
                                                                                     List<ProductFlowCodeRecordEntity> recordEntities,
                                                                                     WorkOrderEntity workOrderEntity) {
        // 将过站记录按工位ID分组
        Map<Integer, List<ProductFlowCodeRecordEntity>> facMap = recordEntities.stream()
                .filter(productFlowCodeRecordEntity -> productFlowCodeRecordEntity.getFacId() != null)
                .collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getFacId));

        return facilitiesEntities.stream().map(fac -> {
            Integer fid = fac.getFid();
            WorkOrderProcedureCountVO.FacProductDetail detail = WorkOrderProcedureCountVO.FacProductDetail.builder()
                    .facId(fid)
                    .facName(fac.getFname())
                    .build();

            // 如果没有该工位的过站记录，计算在制数
            if (!facMap.containsKey(fid)) {
                detail.setCreatingCount(calculateCreatingCount(fac, recordEntities, workOrderEntity));
            } else {
                // 如果有过站记录，计算完成数、在制数和良率
                List<ProductFlowCodeRecordEntity> flowCodeRecordEntities = facMap.get(fid);
                detail.setCompleteCount(calculateCompleteCount(flowCodeRecordEntities));
                detail.setCreatingCount(calculateCreatingCountForCriticalProcedure(flowCodeRecordEntities, recordEntities));
                detail.setPassRate(calculatePassRate(flowCodeRecordEntities, fid));
            }
            return detail;
        }).collect(Collectors.toList());
    }

    // 计算在制数（没有过站记录的情况下）
    private Long calculateCreatingCount(FacilitiesEntity fac, List<ProductFlowCodeRecordEntity> recordEntities, WorkOrderEntity workOrderEntity) {
        List<Integer> craftProcedureIds = craftProcedureService.getCraftProcedureByFacModel(workOrderEntity.getCraftId(), fac.getModelId());
        List<Integer> criticalProcedures = craftProcedureService.getCriticalProcedures(craftProcedureIds);

        if (CollectionUtils.isEmpty(criticalProcedures)) {
            return null;
        }

        List<Integer> criticalProcedureIds = craftProcedureService.getSupCriticalProcedures(craftProcedureIds);
        if (!CollectionUtils.isEmpty(criticalProcedureIds)) {
            return recordEntities.stream()
                    .filter(o -> criticalProcedureIds.contains(o.getProdureId()))
                    .map(ProductFlowCodeRecordEntity::getProductFlowCode)
                    .distinct()
                    .count();
        }
        return null;
    }

    // 计算完成数
    private Long calculateCompleteCount(List<ProductFlowCodeRecordEntity> flowCodeRecordEntities) {
        return flowCodeRecordEntities.stream()
                .map(ProductFlowCodeRecordEntity::getProductFlowCode)
                .distinct()
                .count();
    }

    // 计算在制数（有过站记录的情况下）
    private Long calculateCreatingCountForCriticalProcedure(List<ProductFlowCodeRecordEntity> flowCodeRecordEntities, List<ProductFlowCodeRecordEntity> recordEntities) {
        List<Integer> procedureIds = flowCodeRecordEntities.stream()
                .map(ProductFlowCodeRecordEntity::getProdureId)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> criticalProcedures = craftProcedureService.getCriticalProcedures(procedureIds);
        if (!CollectionUtils.isEmpty(criticalProcedures)) {
            List<Integer> criticalProcedureIds = craftProcedureService.getSupCriticalProcedures(procedureIds);
            if (!CollectionUtils.isEmpty(criticalProcedureIds)) {
                long supProcedureCompleteCount = recordEntities.stream()
                        .filter(o -> criticalProcedureIds.contains(o.getProdureId()))
                        .map(ProductFlowCodeRecordEntity::getProductFlowCode)
                        .distinct()
                        .count();
                return supProcedureCompleteCount - calculateCompleteCount(flowCodeRecordEntities);
            }
        }
        return null;
    }

    // 计算良率
    private Double calculatePassRate(List<ProductFlowCodeRecordEntity> flowCodeRecordEntities, Integer fid) {
        List<String> flowCodes = flowCodeRecordEntities.stream()
                .map(ProductFlowCodeRecordEntity::getProductFlowCode)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(flowCodes)) {
            return 0.0;
        }

        Long faultFacCount = maintainRecordService.getFaultFacCount(flowCodes, fid);
        int size = flowCodes.size();
        faultFacCount = faultFacCount == null ? 0 : faultFacCount;

        return MathUtil.divideDouble(size - faultFacCount, size, 4, RoundingMode.HALF_UP);
    }

    // 计算直通率
    private double calculateThroughRate(List<ProductFlowCodeRecordEntity> recordEntities, WorkOrderEntity workOrderEntity, Date date) {
        long maintainCount = recordEntities.stream()
                .filter(ProductFlowCodeRecordEntity::getIsMaintain)
                .map(ProductFlowCodeRecordEntity::getProductFlowCode)
                .distinct()
                .count();

        double totalCount;
        if (date != null) {
            long finishCount = recordEntities.stream()
                    .filter(ProductFlowCodeRecordEntity::getIsReport)
                    .map(ProductFlowCodeRecordEntity::getProductFlowCode)
                    .distinct()
                    .count();
            long unqualified = recordEntities.stream()
                    .filter(ProductFlowCodeRecordEntity::getIsUnqualified)
                    .map(ProductFlowCodeRecordEntity::getProductFlowCode)
                    .distinct()
                    .count();
            totalCount = finishCount + unqualified;
        } else {
            totalCount = workOrderEntity.getFinishCount() + workOrderEntity.getUnqualified();
        }

        return totalCount == 0 ? 0 : MathUtil.divideDouble(totalCount - maintainCount, totalCount, 4, RoundingMode.HALF_UP);
    }


//    public String getOrSetValue(String key, String value,long timeout,TimeUnit unit) {
//        // 使用 setIfAbsent 方法进行原子操作
//        String existingValue = redisTemplate.opsForValue().get(key);
//        if (existingValue != null) {
//            // 如果存在，返回现有值
//            return existingValue;
//        } else {
//            // 如果不存在，尝试设置新值
//            Boolean isSet = redisTemplate.opsForValue().setIfAbsent(key, value,timeout,unit);
//            if (isSet != null && isSet) {
//                // 如果成功设置，返回新值
//                return value;
//            }
//            return redisTemplate.opsForValue().get(key);
//        }
//    }


    @Override
    public ResponseData workOrderProcedureCountToday(String gridIds) {
        List<WorkOrderProcedureCountVO> vos = getWorkOrderProcedureCount(gridIds, new Date());
        return ResponseData.success(vos);
    }

    @Override
    public ResponseData workOrderProcedureCountToday2(WorkOrderProcedureCountDTO dto) {
        List<Integer> states = CollectionUtils.isEmpty(dto.getStates()) ?
                Collections.singletonList(WorkOrderStateEnum.INVESTMENT.getCode()) : dto.getStates();
        LambdaQueryChainWrapper<WorkOrderEntity> wrapper = workOrderService.lambdaQuery().in(WorkOrderEntity::getState, states);
        // 是否需要查今天已完成的
        if (Boolean.TRUE.equals(dto.getTodayFinish())) {
            Date now = new Date();
            Date startTime = dictService.getDayOutputBeginTime(now);
            Date endTime = DateUtil.addDate(startTime, 1);
            wrapper.or(w -> w
                    .eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode())
                    .ge(WorkOrderEntity::getStateChangeTime, startTime)
                    .lt(WorkOrderEntity::getStateChangeTime, endTime)
            );
        }
        // 查工单
        List<WorkOrderEntity> workOrders = wrapper.list();
        Map<String, WorkOrderEntity> workOrderNumberMap = workOrders.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, Function.identity()));
        workOrderService.showWorkOrdersExtraName(workOrders, ShowExtraNameDTO.builder().procedure(true).build());
        // 查物料
        List<String> materialCodes = workOrders.stream().map(WorkOrderEntity::getMaterialCode).distinct().collect(Collectors.toList());
        Map<String, String> materialCodeNameMap = materialService.getMaterialCodeNameMap(materialCodes);

        // 查指标
        List<MetricsWorkOrderProcedureEntity> workOrderProcedures = metricsWorkOrderProcedureService.lambdaQuery()
                .in(MetricsWorkOrderProcedureEntity::getWorkOrderNumber, workOrderNumberMap.keySet()).list();
        Map<String, List<MetricsWorkOrderProcedureEntity>> orderGroup = workOrderProcedures.stream().collect(Collectors.groupingBy(MetricsWorkOrderProcedureEntity::getWorkOrderNumber));

        List<WorkOrderProcedureCount2VO> result = workOrders.stream().map(workOrder -> {
            List<CraftProcedureEntity> craftProcedures = workOrder.getCraftProcedureEntities();
            if (craftProcedures == null) {
                craftProcedures = Collections.emptyList();
            }
            // 先按 id 排一次
            craftProcedures.sort(Comparator.comparing(CraftProcedureEntity::getId));
            // 排成一条直线
            craftProcedures = craftProcedureService.sortCraftProceduresLine(craftProcedures);
            // 指标计算的数据
            List<MetricsWorkOrderProcedureEntity> cals = orderGroup.getOrDefault(workOrder.getWorkOrderNumber(), Collections.emptyList());
            Map<Integer, MetricsWorkOrderProcedureEntity> calGroup = cals.stream().collect(Collectors.toMap(MetricsWorkOrderProcedureEntity::getCraftProcedureId, Function.identity()));
            // 把工单所有工序都展示出来
            List<WorkOrderProcedureCount2VO.ProductDetail> productDetails = craftProcedures.stream().map(e -> {
                MetricsWorkOrderProcedureEntity cal = calGroup.get(e.getId());
                return WorkOrderProcedureCount2VO.ProductDetail.builder()
                        .craftProcedureId(e.getId())
                        .craftProcedureName(e.getProcedureName())
                        .crossingQuantity(Optional.ofNullable(cal).map(MetricsWorkOrderProcedureEntity::getCrossingQuantity).orElse(0d))
                        .unqualifiedQuantity(Optional.ofNullable(cal).map(MetricsWorkOrderProcedureEntity::getUnqualifiedQuantity).orElse(0d))
                        .build();
            }).collect(Collectors.toList());
            return WorkOrderProcedureCount2VO.builder()
                    .workOrderNumber(workOrder.getWorkOrderNumber())
                    .materialCode(workOrder.getMaterialCode())
                    .materialName(materialCodeNameMap.get(workOrder.getMaterialCode()))
                    .productDetails(productDetails)
                    .build();
        }).collect(Collectors.toList());
//        List<WorkOrderProcedureCount2VO> result = orderGroup.entrySet().stream().map(entry -> {
//            String workOrderNumber = entry.getKey();
//            List<WorkOrderProcedureCount2VO.ProductDetail> productDetails = entry.getValue().stream().map(e -> WorkOrderProcedureCount2VO.ProductDetail.builder()
//                    .craftProcedureId(e.getCraftProcedureId())
//                    .craftProcedureName(e.getCraftProcedureName())
//                    .crossingQuantity(e.getCrossingQuantity())
//                    .unqualifiedQuantity(e.getUnqualifiedQuantity())
//                    .seq(e.getSeq())
//                    .build()
//            ).sorted(Comparator.comparing(WorkOrderProcedureCount2VO.ProductDetail::getSeq)).collect(Collectors.toList());
//            WorkOrderEntity workOrder = workOrderNumberMap.get(workOrderNumber);
//            return WorkOrderProcedureCount2VO.builder()
//                    .workOrderNumber(workOrderNumber)
//                    .materialCode(workOrder.getMaterialCode())
//                    .materialName(materialCodeNameMap.get(workOrder.getMaterialCode()))
//                    .productDetails(productDetails)
//                    .build();
//        }).collect(Collectors.toList());
        return ResponseData.success(result);
    }
}
