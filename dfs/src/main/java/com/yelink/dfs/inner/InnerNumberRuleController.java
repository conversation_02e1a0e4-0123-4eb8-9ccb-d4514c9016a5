package com.yelink.dfs.inner;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfscommon.dto.dfs.GenerateCodeDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfscommon.api.dfs.NumberRuleInterface;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.dto.dfs.GenerateRuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqUpdateDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/31 19:57
 */
@Slf4j
@AllArgsConstructor
@RestController
public class InnerNumberRuleController implements NumberRuleInterface {
    private final NumberRuleService numberRuleService;
    private final RuleSeqService ruleSeqService;

    @Override
    public ResponseData getNumberByRuleType(Integer ruleType) {
        String numberByRuleType = numberRuleService.getNumberByRuleType(ruleType);
        ResponseData success = ResponseData.success();
        success.setData(numberByRuleType);
        return success;
    }

    @Override
    public ResponseData listRuleByType(String ruleType) {
        Page<NumberRulesConfigEntity> all = numberRuleService.getAll(null, null, ruleType);
        return ResponseData.success(all);
    }

    @Override
    public ResponseData generateRules(GenerateRuleDetailDTO build) {
        NumberCodeDTO numberCodeDTO = numberRuleService.generateRules(build.getNumberRulesId(), build.getRuleDetailDTOs(), build.getRelatedMap());
        return ResponseData.success(numberCodeDTO.getCode());
    }

    @Override
    public ResponseData generateCodeByType(GenerateCodeDTO dto) {
        return ResponseData.success(numberRuleService.generateCodeByType(dto));
    }

    @Override
    public ResponseData generateCodeByTypeAndAdd(GenerateCodeDTO dto) {
        return ResponseData.success(numberRuleService.generateCodeByTypeAndAdd(dto));
    }

    @Override
    public ResponseData getById(Integer numberRuleId) {
        NumberRulesConfigEntity numberRulesConfigEntity = numberRuleService.getById(numberRuleId);
        return ResponseData.success(numberRulesConfigEntity);
    }

    @Override
    public ResponseData updateSeqEntityAddOne(RuleSeqUpdateDTO ruleSeqUpdateDTO) {
        // 编码规则有自动生成序号的，seq才加1
        ruleSeqService.updateSeqEntity(ruleSeqUpdateDTO.getRelatedMap(), ruleSeqUpdateDTO.getNumberRulesId(), ruleSeqUpdateDTO.getIsCheckTimesNeedAddOne());
        return ResponseData.success();
    }

    @Override
    public ResponseData getSeqById(RuleSeqDTO ruleSeqDTO) {
        NumberCodeDTO numberCodeDTO = numberRuleService.getSeqById(ruleSeqDTO);
        return ResponseData.success(numberCodeDTO);
    }

    @Override
    public ResponseData getByType(String typeCode) {
        List<NumberRulesConfigEntity> list = numberRuleService.getByTypeCode(typeCode);
        return ResponseData.success(list);
    }

    @Override
    public ResponseData generateRulesDto(GenerateRuleDetailDTO build) {
        NumberCodeDTO numberCodeDTO = numberRuleService.generateRules(build.getNumberRulesId(), build.getRuleDetailDTOs(), build.getRelatedMap());
        return ResponseData.success(numberCodeDTO);
    }

}
