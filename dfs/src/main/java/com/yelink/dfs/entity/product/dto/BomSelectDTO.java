package com.yelink.dfs.entity.product.dto;

import com.yelink.dfs.constant.product.ProductionStateEnum;
import com.yelink.dfscommon.dto.CommSelectPageDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuSelectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/4 19:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class BomSelectDTO extends CommSelectPageDTO {
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String state;
    /**
     * 模糊查询的物料编码
     */
    @ApiModelProperty("模糊查询的物料编码")
    private String code;
    /**
     * 精确查询的物料编码
     */
    @ApiModelProperty("精确查询的物料编码")
    private String fullMaterialCode;

    /**
     *审批人
     */
    @ApiModelProperty("审批人")
    private String approver;
    /**
     * BOM编号
     */
    @ApiModelProperty("BOM编号")
    private String bomNum;
    @ApiModelProperty("原始工艺编号")
    private String originNum;
    /**
     * BOM名称
     */
    @ApiModelProperty("BOM名称")
    private String bomName;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    private String version;

    /**
     * 精准查询
     */
    @ApiModelProperty("版本(精准查询)")
    private String fullVersion;

    /**
     * 是否是模板，默认否
     */
    @ApiModelProperty("是否是模板，默认否")
    private Boolean isTemplate;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    private MaterialEntitySelectDTO materialFields;

    /**
     * 物料特征参数
     */
    @ApiModelProperty("物料特征参数")
    private List<MaterialSkuSelectDTO> materialSkus;

    /**
     * skuId
     */
    @ApiModelProperty("skuId")
    private Integer skuId;

    /**
     * 导出模板的id
     */
    private Integer templateId;

    /**
     * 导出模板要填充数据的sheetName
     */
    private String templateSheetName;

    /**
     * 生产工单号
     */
    private String workOrderNumber;

    /**
     * bom id列表
     */
    private List<Integer> bomIds;

    /**
     * bomCodes
     */
    @ApiModelProperty("BOM编号集合")
    private List<String> bomCodes;

    /**
     * 是否导出多级bom
     */
    @ApiModelProperty("是否导出多级bom")
    private Boolean exportMultiLevelBom;

    /**
     * 是否导出替代关系
     */
    @ApiModelProperty("是否导出替代关系")
    private Boolean exportReplaceMaterial = false;

    /**
     * 是否查询BOM子物料信息
     */
    @ApiModelProperty("是否查询BOM子物料信息")
    private Boolean isShowBomRawMaterialInfo;

    /**
     * bom物料行id
     */
    @ApiModelProperty("bom物料行id")
    private List<Integer> bomRawMaterialIds;

    /**
     * 生产状态
     */
    @ApiModelProperty("生产状态(testProduction-试产,massProduction-量产)")
    private String productionState;

    /**
     * 是否为最父级的BOM
     */
    @ApiModelProperty("是否为最父级的BOM。为是的BOM查询原则：试产的生产状态查询试产的BOM,量产的生产状态查询量产的BOM。" +
            "为否的BOM查询原则：试产的生产状态查询试产/量产的BOM,量产的生产状态查询量产的BOM。\n" +
            "不填默认为是")
    private Boolean isTopBom;

    /**
     * 获取isTopBom值，如果为null则返回默认值true
     */
    public Boolean getIsTopBom() {
        return isTopBom != null ? isTopBom : true;
    }
//
//    /**
//     * 获取productionState值，如果为null则返回默认值量产
//     */
//    public String getProductionState() {
//        return productionState != null ? productionState : ProductionStateEnum.MASS_PRODUCTION.getCode();
//    }
}
