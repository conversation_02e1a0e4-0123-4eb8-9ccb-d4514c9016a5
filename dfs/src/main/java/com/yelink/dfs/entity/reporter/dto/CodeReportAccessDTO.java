package com.yelink.dfs.entity.reporter.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CodeReportAccessDTO {

    /**
     * 关联单据号
     */
    private Collection<String> relationNumbers;

    /**
     * 报工时间下限
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTimeDown;

    /**
     * 报工时间上限
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTimeUp;

    /**
     * 首次不良， 直通数中这个需要为false
     */
    private Boolean isFirstUnqualified;

    /**
     * 是否有工序
     */
    private Boolean hasProcedure;
}

