package com.yelink.dfs.migration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.yelink.dfs.constant.sys.TableFieldTypeEnum;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.open.v1.metrics.dto.TableViewCreateDTO;
import com.yelink.dfs.open.v1.metrics.dto.TargetGroupViewInsertDTO;
import com.yelink.dfs.open.v1.metrics.vo.TableFieldCreateVO;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueDictEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 维表视图
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_20_1_100__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        ModelService modelService = SpringUtil.getBean(ModelService.class);
        ModelEntity factoryModel = modelService.lambdaQuery().eq(ModelEntity::getType, "baseTargetGroupObject").eq(ModelEntity::getCode, "areaTarget").one();
        ModelEntity workOrderModel = modelService.lambdaQuery().eq(ModelEntity::getType, "baseTargetGroupObject").eq(ModelEntity::getCode, "workOrderTarget").one();
        TargetModelService targetModelService = SpringUtil.getBean(TargetModelService.class);
        List<TargetGroupViewInsertDTO> addViews = new ArrayList<>();
        // 1
        if(factoryModel == null) {
            log.warn("model:工厂不存在");
        }else {
            boolean existsModelFactory = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, "v_dimension_factory_model").exists();
            if(existsModelFactory) {
                log.warn("工厂模型维表已存在");
            }else {
                addViews.add(TargetGroupViewInsertDTO.builder()
                        .modelId(factoryModel.getId())
                        .targetName("v_dimension_factory_model")
                        .targetCnname("工厂模型维表")
                        .script("SELECT\n" +
                                "\ta.aid,\n" +
                                "\ta.aname,\n" +
                                "\tg.gid,\n" +
                                "\tg.gname,\n" +
                                "\twc.id AS work_center_id,\n" +
                                "\twc.NAME AS work_center_name,\n" +
                                "\twc.type AS work_center_type,\n" +
                                "\tbs.production_basic_unit_id,\n" +
                                "\tbs.production_basic_unit_name,\n" +
                                "\tbs.production_basic_unit_type_id,\n" +
                                "\tbs.production_basic_unit_type,\n" +
                                "\tf.fid,\n" +
                                "\tf.fname\n" +
                                "\t\n" +
                                "FROM\n" +
                                "\tdfs_work_center wc\n" +
                                "\tLEFT JOIN dfs_area a ON a.aid = wc.aid\n" +
                                "\tLEFT JOIN (\n" +
                                "\t\t(\n" +
                                "\t\t\tSELECT \n" +
                                "\t\t\t\t'line' AS work_center_type,\n" +
                                "\t\t\t\tpl.production_line_id AS production_basic_unit_id, \n" +
                                "\t\t\t\tpl.name AS production_basic_unit_name, \n" +
                                "        m.id AS production_basic_unit_type_id,\n" +
                                "\t\t\t\tm.name AS production_basic_unit_type, \n" +
                                "\t\t\t\tpl.work_center_id AS work_center_id,\n" +
                                "\t\t\t\tpl.gid AS gid\n" +
                                "\t\t\tFROM dfs_production_line pl\n" +
                                "\t\t\tLEFT JOIN dfs_model m ON m.id = pl.model_id\n" +
                                "\t\t)\n" +
                                "\t\tUNION ALL\n" +
                                "\t\t(\n" +
                                "\t\t\tSELECT\n" +
                                "\t\t\t'device' AS work_center_type,\t\n" +
                                "\t\t\td.device_id AS production_basic_unit_id, \n" +
                                "\t\t\td.device_name AS production_basic_unit_name,\n" +
                                "      m.id AS production_basic_unit_type_id,\n" +
                                "\t\t\tm.name AS production_basic_unit_type, \n" +
                                "\t\t\tcd.work_center_id AS work_center_id,\n" +
                                "\t\t\tnull AS gid\n" +
                                "\t\t\tFROM dfs_device d\n" +
                                "\t\t\tLEFT JOIN dfs_work_center_device cd ON d.device_id = cd.device_id\n" +
                                "\t\t\tLEFT JOIN dfs_model m ON m.id = d.model_id\n" +
                                "\t\t)\n" +
                                "\t\tUNION ALL\n" +
                                "\t\t(\n" +
                                "\t\t\tSELECT\n" +
                                "\t\t\t'team' AS work_center_type,\t\t\n" +
                                "\t\t\tt.id AS production_basic_unit_id, \n" +
                                "\t\t\tt.team_name AS production_basic_unit_name,\n" +
                                "      tt.id AS production_basic_unit_type_id,\n" +
                                "\t\t\ttt.type_def_name AS production_basic_unit_type, \n" +
                                "\t\t\tct.work_center_id AS work_center_id,\n" +
                                "\t\t\tnull AS gid\n" +
                                "\t\t\tFROM sys_team t\n" +
                                "\t\t\tLEFT JOIN dfs_work_center_team ct ON t.id = ct.team_id\n" +
                                "\t\t\tLEFT JOIN dfs_team_type_def tt ON tt.id = t.team_type\t\n" +
                                "\t\t)\t\n" +
                                "\t)bs ON bs.work_center_id = wc.id\n" +
                                "\tLEFT JOIN dfs_grid g on g.gid = bs.gid and bs.work_center_type = 'line'\n" +
                                "\tLEFT JOIN dfs_facilities f on f.production_line_id = bs.production_basic_unit_id and bs.work_center_type = 'line'")
                        .tableDTO(TableViewCreateDTO.builder()
                                .tableName("v_dimension_factory_model")
                                .fields(Stream.of(
                                        TableFieldCreateVO.builder().fieldCode("aid").fieldName("厂区id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("aname").fieldName("厂区名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("gid").fieldName("车间id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("gname").fieldName("车间名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_id").fieldName("工作中心id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_name").fieldName("工作中心名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_type").fieldName("工作中心类型").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("production_basic_unit_id").fieldName("生产基本单元id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("production_basic_unit_name").fieldName("生产基本单元名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("production_basic_unit_type_id").fieldName("生产基本单元类型id").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("production_basic_unit_type").fieldName("生产基本单元类型").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("fid").fieldName("工位id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("fname").fieldName("工位名称").fieldType(TableFieldTypeEnum.VARCHAR).build()
                                ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                                .build()
                        )
                        .build()
                );
            }

            // 2
            boolean existsMaterial = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, "v_dimension_material").exists();
            if(existsMaterial) {
                log.warn("物料维表");
            }else {
                addViews.add(TargetGroupViewInsertDTO.builder()
                        .modelId(factoryModel.getId())
                        .targetName("v_dimension_material")
                        .targetCnname("物料维表")
                        .script("SELECT \n\tm.`code`,\n\tm.`name`,\n\tm.name_english,\n\tm.standard,\t\n  m.state,\t\n\tCOALESCE(\n\t\t\tCASE m.state\n\t\t\t\t\tWHEN 1 THEN '创建'\n\t\t\t\t\tWHEN 2 THEN '生效'\n\t\t\t\t\tWHEN 3 THEN '停用'\n\t\t\t\t\tWHEN 4 THEN '废弃'\t\t\t\t\t\n\t\t\t\t\tEND,  \n\t\t\tNULL -- 添加兜底值\n\t) AS state_name,\t\t\n\tm.type,\n\tm.type_name,\t\n\tm.sort,\n\tCOALESCE(\n\t\t\tCASE m.sort\n\t\t\t\t\tWHEN 'purchase' THEN '采购品'\n\t\t\t\t\tWHEN 'product' THEN '采购品'\n\t\t\t\t\tWHEN 'outsourcing' THEN '委外品'\n\t\t\t\t\tEND,  \n\t\t\tNULL -- 添加兜底值\n\t) AS sort_name,\t\n  m.comp AS unit1,\t\n\tm.unit AS unit2, \n\tm.version,\n\tm.`level`,\n\tm.drawing_number,\n\tm.unit_numerator,\t\n\tm.unit_denominator,\t\t\n\tm.material_price,\n\tm.lose_rate,\n\tm.raw_material,\n\tm.minimum_production_lot,\n\tm.factory_model,\n\tm.create_by,\n\tu1.nick_name AS create_by_name,\t\n\tm.update_by,\n\tu2.nick_name AS update_by_name,\t\t\n\tm.editor,\n\tu3.nick_name AS editor_name,\t\n\tm.create_time,\n\tm.update_time,\n\tm.release_time,\t\t\n\tm.approval_status,\n\tCOALESCE(\n\t\t\tCASE m.approval_status\n\t\t\t\t\tWHEN '0' THEN '待提交'\n\t\t\t\t\tWHEN '1' THEN '待审批'\n\t\t\t\t\tWHEN '2' THEN '已通过'\n\t\t\t\t\tWHEN '3' THEN '已驳回'\n\t\t\t\t\tWHEN '4' THEN '已撤销'\t\t\t\n\t\t\t\t\tWHEN '5' THEN '审批中'\t\t\t\n\t\t\t\t\tEND,  \n\t\t\tNULL -- 添加兜底值\n\t) AS approval_status_name,\t\t\n\tm.approver,\n\tu4.nick_name AS approver_name,\t\n\tm.approval_time,\t\t\n\tm.approval_suggestion,\t\n\tm.remark,\n\tm.is_auxiliary_material,\n\tm.is_batch_mag,\n\tm.is_support_code_manage,\n\tm.have_bom,\n\tm.have_craft,\n\tm.custom_field_one,\n\tm.custom_field_two,\t\t\n\tm.custom_field_three,\n\tm.custom_field_four,\t\n\tm.custom_field_five,\n\tm.custom_field_six,\t\n\tm.custom_field_seven,\n\tm.custom_field_eight,\t\t\t\n\tm.custom_field_nine,\n\tm.custom_field_ten,\t\t\n\tm.custom_field_eleven\t\t\n\t\t\nFROM dfs_material m \nLEFT JOIN sys_users u1 on u1.user_name = m.create_by\nLEFT JOIN sys_users u2 on u2.user_name = m.update_by\nLEFT JOIN sys_users u3 on u3.user_name = m.editor\nLEFT JOIN sys_users u4 on u4.user_name = m.approver\n")
                        .tableDTO(TableViewCreateDTO.builder()
                                .tableName("v_dimension_material")
                                .fields(Stream.of(
                                        TableFieldCreateVO.builder().fieldCode("code").fieldName("物料编码").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("name").fieldName("物料名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("name_english").fieldName("物料英文名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("standard").fieldName("物料规格").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("state").fieldName("状态").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("state_name").fieldName("状态名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("type").fieldName("物料类型").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("type_name").fieldName("物料类型名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("sort").fieldName("分类").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("sort_name").fieldName("分类名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("unit1").fieldName("单位1").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("unit2").fieldName("单位2").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("version").fieldName("版本").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("level").fieldName("物料等级").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("drawing_number").fieldName("图号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("unit_numerator").fieldName("计量系数分子").fieldType(TableFieldTypeEnum.DOUBLE).fieldDefaultValue("1.0000").build(),
                                        TableFieldCreateVO.builder().fieldCode("unit_denominator").fieldName("计量系数分母").fieldType(TableFieldTypeEnum.DOUBLE).fieldDefaultValue("1.0000").build(),
                                        TableFieldCreateVO.builder().fieldCode("material_price").fieldName("该物料单价").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("lose_rate").fieldName("损耗率").fieldType(TableFieldTypeEnum.DOUBLE).fieldDefaultValue("0").build(),
                                        TableFieldCreateVO.builder().fieldCode("raw_material").fieldName("材质").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("minimum_production_lot").fieldName("最小生产批量").fieldType(TableFieldTypeEnum.DOUBLE).fieldDefaultValue("0.00").build(),
                                        TableFieldCreateVO.builder().fieldCode("factory_model").fieldName("工厂型号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("create_by").fieldName("创建人").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("create_by_name").fieldName("创建人昵称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("update_by").fieldName("更新人").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("update_by_name").fieldName("更新人昵称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("editor").fieldName("编制人").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("editor_name").fieldName("编制人昵称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("create_time").fieldName("创建时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("update_time").fieldName("更新时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("release_time").fieldName("生效时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("approval_status").fieldName("审批状态").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("approval_status_name").fieldName("审批状态名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("approver").fieldName("审批人").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("approver_name").fieldName("审批人昵称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("approval_time").fieldName("审批时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("approval_suggestion").fieldName("审批建议").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("remark").fieldName("备注").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("is_auxiliary_material").fieldName("是否为辅料(1-是  0-否)").fieldType(TableFieldTypeEnum.INT).fieldDefaultValue("0").build(),
                                        TableFieldCreateVO.builder().fieldCode("is_batch_mag").fieldName("是否按批量管理(1-是  0-否)").fieldType(TableFieldTypeEnum.INT).fieldDefaultValue("0").build(),
                                        TableFieldCreateVO.builder().fieldCode("is_support_code_manage").fieldName("是否支持生产流水码管理(1-是 0-否)").fieldType(TableFieldTypeEnum.INT).fieldDefaultValue("0").build(),
                                        TableFieldCreateVO.builder().fieldCode("have_bom").fieldName("有无bom(1-是  0-否)").fieldType(TableFieldTypeEnum.INT).fieldDefaultValue("0").build(),
                                        TableFieldCreateVO.builder().fieldCode("have_craft").fieldName("有无工艺(1-是  0-否)").fieldType(TableFieldTypeEnum.INT).fieldDefaultValue("0").build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_one").fieldName("扩展字段1").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_two").fieldName("扩展字段2").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_three").fieldName("扩展字段3").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_four").fieldName("扩展字段4").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_five").fieldName("扩展字段5").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_six").fieldName("扩展字段6").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_seven").fieldName("扩展字段7").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_eight").fieldName("扩展字段8").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_nine").fieldName("扩展字段9").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_ten").fieldName("扩展字段10").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("custom_field_eleven").fieldName("扩展字段11").fieldType(TableFieldTypeEnum.VARCHAR).build()
                                ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                                .build()
                        )
                        .build()
                );
            }
        }

        // 2
        if(workOrderModel == null) {
            log.warn("model:工单不存在");
        }else {
            boolean existsWorkOrder = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, "v_dimension_work_order").exists();
            if(existsWorkOrder) {
                log.warn("生产工单维表已存在");
            }else {
                addViews.add(TargetGroupViewInsertDTO.builder()
                        .modelId(workOrderModel.getId())
                        .targetName("v_dimension_work_order")
                        .targetCnname("生产工单维表")
                        .script("SELECT \n" +
                                "\two.sale_order_number,\n" +
                                "\two.product_order_number,\n" +
                                "\two.work_order_number,\n" +
                                "\two.state,\n" +
                                "\tCOALESCE(\n" +
                                "\t\t\tCASE wo.state\n" +
                                "\t\t\t\t\tWHEN 1 THEN '创建'\n" +
                                "\t\t\t\t\tWHEN 2 THEN '生效'\n" +
                                "\t\t\t\t\tWHEN 3 THEN '投产'\n" +
                                "\t\t\t\t\tWHEN 4 THEN '挂起'\t\t\n" +
                                "\t\t\t\t\tWHEN 5 THEN '完成'\n" +
                                "\t\t\t\t\tWHEN 6 THEN '关闭'\n" +
                                "\t\t\t\t\tWHEN 7 THEN '取消'\t\t\t\t\t\t\t\t\t\t\n" +
                                "\t\t\t\t\tEND,  \n" +
                                "\t\t\tNULL -- 添加兜底值\n" +
                                "\t) AS state_name,\t\t\n" +
                                "\two.material_code,\n" +
                                "\tm.`name` AS material_name,\n" +
                                "\two.work_center_type,\n" +
                                "\tbu.production_basic_unit_name,\n" +
                                "\two.create_date AS create_time,\n" +
                                "\two.start_date AS plan_start_time,\n" +
                                "\two.actual_start_date AS actual_start_time,\n" +
                                "\two.end_date AS plan_end_time,\n" +
                                "\two.actual_end_date AS actual_end_time,\n" +
                                "\two.plan_quantity,\n" +
                                "\tmwo.capacity, -- 工单产能\n" +
                                "\tmwo.theory_working_hour, -- 单件理论工时\n" +
                                "\tmwo.human_working_hour + mwo.resource_working_hour AS input_hour, -- 投入工时\n" +
                                "\tmwo.achievements, -- 生产绩效\n" +
                                "\two.finish_count AS produce_quantity, -- 计划数量\n" +
                                "\two.unqualified AS unqualified_quantity,  -- 不良数量\n" +
                                "\tmwo.direct_access_quantity, -- 直通数\n" +
                                "\tmwo.unqualified_record_quantity, -- 不良品记录数量\n" +
                                "\tmwo.unqualified_record_item_quantity,  -- 不良项记录数量\n" +
                                "\tmwo.repair_quantity,  -- 维修数量\n" +
                                "\tmwo.repair_quantity - mwo.repair_qualified_quantity AS repair_scrap_quantity, -- 维修报废数\n" +
                                "\two.actual_working_hours, -- 生产工时(时间点到第一次【投产】时间点，并剔除挂起的时间)\n" +
                                "\tmwo.exception_hour, -- 异常工时\n" +
                                "\tmwo.stop_line_hour, -- 停机工时(异常工时中停线的)\n" +
                                "\tCOALESCE(mwo.exception_hour, 0) + COALESCE(wo.actual_working_hours, 0) AS total_hour, -- 总工时 = 异常工时+生产工时\n" +
                                "\toti.code AS order_type, -- 单据类型\n" +
                                "\toti.name AS order_type_name, -- 单据类型名称\n" +
                                "\two.business_unit_code, -- 业务单元编码\n" +
                                "\two.business_unit_name, -- 业务单元名称\n" +
                                "\tsku.sku_name, -- 特征参数\n" +
                                "\tCASE \n" +
                                "\t\t\tWHEN wo.actual_end_date IS NULL THEN NULL\n" +
                                "\t\t\tWHEN wo.actual_end_date < wo.end_date THEN ROUND(TIMESTAMPDIFF(SECOND, wo.actual_end_date, wo.end_date) / 86400.0, 2)\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS end_advance_days, -- 结束提前天数\n" +
                                "\tCASE \n" +
                                "\t\t\tWHEN wo.actual_end_date IS NULL THEN NULL\n" +
                                "\t\t\tWHEN wo.actual_end_date > wo.end_date THEN ROUND(TIMESTAMPDIFF(SECOND, wo.end_date, wo.actual_end_date) / 86400.0, 2)\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS end_delay_days, -- 结束延迟天数\n" +
                                "\ttop.all_defects AS top5_defects -- 前五不良问题\n" +
                                "\n" +
                                "FROM `dfs`.`dfs_work_order` wo\n" +
                                "LEFT JOIN `dfs`.`dfs_material` m ON wo.material_code = m.`code`\n" +
                                "LEFT JOIN `dfs_metrics`.`dfs_metrics_work_order` mwo ON wo.work_order_number = mwo.work_order_number\n" +
                                "LEFT JOIN `dfs`.`dfs_order_type_item` oti ON wo.order_type = oti.code and  wo.business_type = oti.business_type_code\n" +
                                "LEFT JOIN `dfs`.`dfs_sku` sku ON wo.sku_id = sku.sku_id\n" +
                                "LEFT JOIN (\n" +
                                "\tSELECT\n" +
                                "\t\twork_order_number,\n" +
                                "\t\tGROUP_CONCAT(production_basic_unit_name SEPARATOR ',') AS production_basic_unit_name\n" +
                                "\tFROM `dfs`.`dfs_work_order_basic_unit_relation`\n" +
                                "\tGROUP BY work_order_number\n" +
                                ") AS bu on wo.work_order_number = bu.work_order_number \n" +
                                "LEFT JOIN (\n" +
                                "\tSELECT\n" +
                                "\t\twork_order_number,\n" +
                                "\t\tGROUP_CONCAT(defect_name ORDER BY `rank` SEPARATOR ',') AS all_defects\n" +
                                "\tFROM `dfs_metrics`.`dfs_metrics_top_work_order`\n" +
                                "\tWHERE defect_name IS NOT NULL AND defect_name <> ''\n" +
                                "\tGROUP BY work_order_number \n" +
                                ") AS top ON top.work_order_number = wo.work_order_number;")
                        .tableDTO(TableViewCreateDTO.builder()
                                .tableName("v_dimension_work_order")
                                .fields(Stream.of(
                                        TableFieldCreateVO.builder().fieldCode("sale_order_number").fieldName("销售订单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("product_order_number").fieldName("生产订单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_order_number").fieldName("生产工单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("state").fieldName("工单状态").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("state_name").fieldName("工单状态名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("material_code").fieldName("物料编码").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("material_name").fieldName("物料名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_type").fieldName("工作中心类型").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("production_basic_unit_name").fieldName("生产基本单元").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("create_time").fieldName("创建时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("plan_start_time").fieldName("计划开始时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("actual_start_time").fieldName("实际开始时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("plan_end_time").fieldName("计划结束时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("actual_end_time").fieldName("实际结束时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("plan_quantity").fieldName("计划数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("capacity").fieldName("工单产能").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("theory_working_hour").fieldName("单件理论工时").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("input_hour").fieldName("投入工时").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("achievements").fieldName("生产绩效").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("produce_quantity").fieldName("计划数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("unqualified_quantity").fieldName("不良数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("direct_access_quantity").fieldName("直通数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("unqualified_record_quantity").fieldName("不良品记录数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("unqualified_record_item_quantity").fieldName("不良项记录数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("repair_quantity").fieldName("维修数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("repair_scrap_quantity").fieldName("维修报废数").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("actual_working_hours").fieldName("生产工时").fieldType(TableFieldTypeEnum.DOUBLE).fieldRemark("时间点到第一次【投产】时间点，并剔除挂起的时间").build(),
                                        TableFieldCreateVO.builder().fieldCode("exception_hour").fieldName("异常工时").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("stop_line_hour").fieldName("停机工时").fieldType(TableFieldTypeEnum.DOUBLE).fieldRemark("异常工时中停线的").build(),
                                        TableFieldCreateVO.builder().fieldCode("total_hour").fieldName("总工时").fieldType(TableFieldTypeEnum.DOUBLE).fieldRemark("异常工时+生产工时").build(),
                                        TableFieldCreateVO.builder().fieldCode("order_type").fieldName("单据类型").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("order_type_name").fieldName("单据类型名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("business_unit_code").fieldName("业务单元编码").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("business_unit_name").fieldName("业务单元名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("sku_name").fieldName("特征参数").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("end_advance_days").fieldName("结束提前天数").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("end_delay_days").fieldName("结束延迟天数").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("top5_defects").fieldName("前五不良问题").fieldType(TableFieldTypeEnum.VARCHAR).build()
                                ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                                .build()
                        )
                        .build()
                );
            }

            boolean existsReportLine = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, "v_dimension_report_line").exists();
            if(existsReportLine) {
                log.warn("工单报工维表已存在");
            }else {
                addViews.add(TargetGroupViewInsertDTO.builder()
                        .modelId(workOrderModel.getId())
                        .targetName("v_dimension_report_line")
                        .targetCnname("工单报工维表")
                        .script("SELECT \n" +
                                "\two.sale_order_number,\n" +
                                "\two.product_order_number,\n" +
                                "\trl.work_order AS work_order_number,\n" +
                                "\two.material_code,\n" +
                                "\tm.`name` AS material_name,\n" +
                                "\ta.aid,\n" +
                                "\ta.aname,\n" +
                                "\tg.gid,\n" +
                                "\tg.gname,\n" +
                                "\two.work_center_id,\n" +
                                "\two.work_center_name,\n" +
                                "\tCASE\n" +
                                "\t\t\tWHEN rl.device_id IS NOT NULL THEN 'device'\n" +
                                "\t\t\tWHEN rl.team_id IS NOT NULL THEN 'team'\n" +
                                "\t\t\tWHEN rl.line_id IS NOT NULL THEN 'line'\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS work_center_type,   -- 中作中心类型\n" +
                                "\tCASE\n" +
                                "\t\t\tWHEN rl.device_id IS NOT NULL THEN rl.device_id\n" +
                                "\t\t\tWHEN rl.team_id IS NOT NULL THEN rl.team_id\n" +
                                "\t\t\tWHEN rl.line_id IS NOT NULL THEN rl.line_id\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS product_basic_unit_id,\t  -- 生产基本单元id\n" +
                                "\tCASE\n" +
                                "\t\t\tWHEN rl.device_name IS NOT NULL THEN rl.device_name\n" +
                                "\t\t\tWHEN rl.team_name IS NOT NULL THEN rl.team_name\n" +
                                "\t\t\tWHEN rl.line_name IS NOT NULL THEN rl.line_name\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS product_basic_unit_name,   -- 生产基本单元名称\n" +
                                "\tCASE\n" +
                                "\t\t\tWHEN rl.resource_device_id IS NOT NULL THEN 'device'\n" +
                                "\t\t\tWHEN rl.resource_team_id IS NOT NULL THEN 'team'\n" +
                                "\t\t\tWHEN rl.resource_line_id IS NOT NULL THEN 'line'\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS relevance_type,  -- 关联资源类型\n" +
                                "\tCASE\n" +
                                "\t\t\tWHEN rl.resource_device_id IS NOT NULL THEN rl.resource_device_id\n" +
                                "\t\t\tWHEN rl.resource_team_id IS NOT NULL THEN rl.resource_team_id\n" +
                                "\t\t\tWHEN rl.resource_line_id IS NOT NULL THEN rl.resource_line_id\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS relevance_unit_id,\t -- 关联资源id\n" +
                                "\tCASE\n" +
                                "\t\t\tWHEN rl.resource_device_name IS NOT NULL THEN rl.resource_device_name\n" +
                                "\t\t\tWHEN rl.resource_team_name IS NOT NULL THEN rl.resource_team_name\n" +
                                "\t\t\tWHEN rl.resource_line_name IS NOT NULL THEN rl.resource_line_name\n" +
                                "\t\t\tELSE NULL\n" +
                                "\tEND AS relevance_unit_name, -- 关联资源名称\n" +
                                "\trl.shift_type, -- 报工批次\n" +
                                "\trl.user_name, -- 上报人账号\n" +
                                "\trl.user_nickname,  -- 上报人昵称\n" +
                                "\trl.operator, -- 操作人账号\n" +
                                "\trl.operator_name,  -- 操作人昵称\n" +
                                "\tCOALESCE(\n" +
                                "\t\t\tCASE rl.type\n" +
                                "\t\t\t\t\tWHEN 'auto' THEN '设备采集'\n" +
                                "\t\t\t\t\tWHEN 'report' THEN '手动报工'\t\t\t\t\t\t\t\t\n" +
                                "\t\t\t\t\tEND,  \n" +
                                "\t\t\tNULL \n" +
                                "\t) AS report_type, -- 报工类型\t\t\n" +
                                "\trl.finish_count, -- 手动上报完成数\t\n" +
                                "\trl.unqualified, -- 手动上报不良数\n" +
                                "\tROUND(rl.finish_count/(rl.finish_count + IFNULL(rl.unqualified, 0)),4) AS qualified_rate, -- 手动上报合格率\t\t\t\n" +
                                "\trl.auto_count, -- 数采完成数\t\n" +
                                "\trl.auto_unqualified, -- 数采不良数\t\n" +
                                "\tROUND(rl.auto_count/(rl.auto_count + IFNULL(rl.auto_unqualified, 0)),4) AS auto_qualified_rate, -- 手动上报合格率\t\t\n" +
                                "\trl.create_time, -- 上报时间\n" +
                                "\trl.report_time AS report_start_time, -- 报工开始时间\n" +
                                "\trl.report_end_time,\t-- 报工结束时间\n" +
                                "\two.business_unit_code, -- 业务单元编码\n" +
                                "\two.business_unit_name, -- 业务单元名称\n" +
                                "\tsku.sku_name -- 特征参数\n" +
                                "FROM `dfs`.`dfs_report_line` rl\n" +
                                "LEFT JOIN `dfs`.`dfs_work_order` wo ON rl.work_order = wo.work_order_number\n" +
                                "LEFT JOIN `dfs`.`dfs_material` m ON wo.material_code = m.`code`\n" +
                                "LEFT JOIN `dfs`.`dfs_production_line` pl ON wo.line_id = pl.production_line_id\n" +
                                "LEFT JOIN `dfs`.`dfs_grid` g ON g.gid = pl.gid\n" +
                                "LEFT JOIN `dfs`.`dfs_sku` sku ON wo.sku_id = sku.sku_id\n" +
                                "LEFT JOIN `dfs`.`dfs_work_center` wc ON wc.id = wo.work_center_id\n" +
                                "LEFT JOIN `dfs`.`dfs_area` a ON a.aid = wc.aid")
                        .tableDTO(TableViewCreateDTO.builder()
                                .tableName("v_dimension_report_line")
                                .fields(Stream.of(
                                        TableFieldCreateVO.builder().fieldCode("sale_order_number").fieldName("销售订单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("product_order_number").fieldName("生产订单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_order_number").fieldName("生产工单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("material_code").fieldName("物料编码").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("material_name").fieldName("物料名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("aid").fieldName("厂区id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("aname").fieldName("厂区名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("gid").fieldName("车间id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("gname").fieldName("车间名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_id").fieldName("工作中心id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_name").fieldName("工作中心名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_type").fieldName("工作中心类型").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("product_basic_unit_id").fieldName("生产基本单元id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("product_basic_unit_name").fieldName("生产基本单元名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("relevance_type").fieldName("关联资源类型").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("relevance_unit_id").fieldName("关联资源id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("relevance_unit_name").fieldName("关联资源名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("shift_type").fieldName("班次名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("user_name").fieldName("账号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("user_nickname").fieldName("上报人姓名").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("operator").fieldName("操作员").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("operator_name").fieldName("操作员姓名").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("report_type").fieldName("报工类型").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("finish_count").fieldName("手动上报完成数").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("unqualified").fieldName("手动上报不良数").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("qualified_rate").fieldName("手动上报合格率").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("auto_count").fieldName("采集器采集数量").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("auto_unqualified").fieldName("采集器采集不良数").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("auto_qualified_rate").fieldName("采集器采集合格率").fieldType(TableFieldTypeEnum.DOUBLE).build(),
                                        TableFieldCreateVO.builder().fieldCode("create_time").fieldName("创建时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("report_start_time").fieldName("报工开始时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("report_end_time").fieldName("报工结束时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("business_unit_code").fieldName("业务单元编码").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("business_unit_name").fieldName("业务单元名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("sku_name").fieldName("特征参数").fieldType(TableFieldTypeEnum.VARCHAR).build()
                                ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                                .build()
                        )
                        .build()
                );
            }
        }

        if(CollUtil.isNotEmpty(addViews)) {
            targetModelService.batchAddTargetGroupView(addViews);
        }

        addHistoryPushDownConf();
        addHistoryPushDownDictConf();
    }

    /**
     * 添加采购订单下推采购收料单据是否携带附件配置
     */
    private void addHistoryPushDownConf() {
        String configFullPath = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.push";
        OrderPushDownConfigValueService valueService = com.yelink.dfs.utils.SpringUtil.getBean(OrderPushDownConfigValueService.class);

        List<OrderPushDownConfigValueEntity> orderPushDownConfigValueEntities = valueService.lambdaQuery().eq(OrderPushDownConfigValueEntity::getConfigFullPathCode, configFullPath)
                .select(OrderPushDownConfigValueEntity::getItemId)
                .list().stream().distinct().collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> valueEntities = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderPushDownConfigValueEntities)) {
            for (OrderPushDownConfigValueEntity orderPushDownConfigValueEntity : orderPushDownConfigValueEntities) {
                valueEntities.add(OrderPushDownConfigValueEntity.builder().
                        itemId(orderPushDownConfigValueEntity.getItemId()).
                        valueCode("carryFile").
                        valueName("携带源单据附件").
                        inputType("select").
                        optionValuesType("table").
                        valueFullPathCode(configFullPath + ".carryFile").
                        configFullPathCode(configFullPath).
                        value("true").
                        optionValues("[{\"value\":true,\"label\":\"携带\"},{\"value\":false,\"label\":\"不携带\"}]").
                        build());
            }
            valueService.saveBatch(valueEntities);
        }

    }

    private void addHistoryPushDownDictConf() {
        String configFullPath = "purchase.purchaseOrderPushDownConfig.purchaseReceipt.push";
        OrderPushDownConfigValueDictService valueService = com.yelink.dfs.utils.SpringUtil.getBean(OrderPushDownConfigValueDictService.class);

        List<OrderPushDownConfigValueDictEntity> orderPushDownConfigValueEntities = valueService.lambdaQuery().eq(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, configFullPath)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list().stream().distinct().collect(Collectors.toList());
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderPushDownConfigValueEntities)) {
            orderPushDownConfigValueEntities.forEach(orderPushDownConfigValueDictEntity -> {
                dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                        .valueCode("carryFile")
                        .valueName("携带源单据附件")
                        .inputType("select")
                        .optionValuesType("table")
                        .valueFullPathCode(orderPushDownConfigValueDictEntity.getConfigFullPathCode() + ".carryFile")
                        .configFullPathCode(orderPushDownConfigValueDictEntity.getConfigFullPathCode())
                        .value("true")
                        .optionValues("[{\"value\":true,\"label\":\"携带\"},{\"value\":false,\"label\":\"不携带\"}]")
                        .build());
            });
            valueService.saveBatch(dictlist);
        }
    }
}

