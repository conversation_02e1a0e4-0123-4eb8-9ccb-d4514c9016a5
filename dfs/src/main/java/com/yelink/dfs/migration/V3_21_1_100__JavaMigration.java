package com.yelink.dfs.migration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.yelink.dfs.constant.sys.TableFieldTypeEnum;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.open.v1.metrics.dto.TableViewCreateDTO;
import com.yelink.dfs.open.v1.metrics.dto.TargetGroupViewInsertDTO;
import com.yelink.dfs.open.v1.metrics.vo.TableFieldCreateVO;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.target.TargetModelService;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 维表视图
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_21_1_100__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        ModelService modelService = SpringUtil.getBean(ModelService.class);
        ModelEntity deviceModel = modelService.lambdaQuery().eq(ModelEntity::getType, "baseTargetGroupObject").eq(ModelEntity::getCode, "deviceTarget").one();
        ModelEntity workOrderModel = modelService.lambdaQuery().eq(ModelEntity::getType, "baseTargetGroupObject").eq(ModelEntity::getCode, "workOrderTarget").one();
        TargetModelService targetModelService = SpringUtil.getBean(TargetModelService.class);
        List<TargetGroupViewInsertDTO> addViews = new ArrayList<>();
        // 1
        if(deviceModel == null) {
            log.warn("model:设备指标模型不存在");
        }else {
            boolean existsDimensionDevice = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, "v_dimension_device").exists();
            if (existsDimensionDevice) {
                log.warn("设备维表已存在");
            } else {
                addViews.add(TargetGroupViewInsertDTO.builder()
                        .modelId(deviceModel.getId())
                        .targetName("v_dimension_device")
                        .targetCnname("设备维表")
                        .script("SELECT \n" +
                                "\td.device_id,\n" +
                                "\td.device_name,\n" +
                                "\td.device_code,\n" +
                                "\td.aid,\n" +
                                "\ta.aname,\n" +
                                "\td.gid,\n" +
                                "\tg.gname,\n" +
                                "\td.fid,\n" +
                                "\tf.fname,\n" +
                                "\td.production_line_id as line_id,\n" +
                                "\tl.name as line_name,\n" +
                                "\td.model_id,\n" +
                                "\tm.name as model_name,\n" +
                                "\twc.`id` as work_center_id,\n" +
                                "\twc.`name` as work_center_name,\n" +
                                "\td.state,\n" +
                                "\tCOALESCE(\n" +
                                "\t\tCASE d.state\n" +
                                "\t\t\t\tWHEN '0' THEN '停机'\n" +
                                "\t\t\t\tWHEN '1' THEN '运行中'\n" +
                                "\t\t\t\tWHEN '2' THEN '暂停'\n" +
                                "\t\t\t\tWHEN '3' THEN '故障'\t\t\n" +
                                "\t\t\t\tEND,  \n" +
                                "\t\t\tNULL -- 添加兜底值\n" +
                                "\t) AS state_name,\t\t\n" +
                                "\td.place,\n" +
                                "\td.model_img,\n" +
                                "\td.simulation_img,\n" +
                                "\td.mag_name,\n" +
                                "\tum.nick_name as mag_nickname,\n" +
                                "\td.remark,\n" +
                                "\td.state_update_time,\n" +
                                "\td.use_state,\n" +
                                "\tCOALESCE(\n" +
                                "\t\tCASE d.state\n" +
                                "\t\t\t\tWHEN '0' THEN '在用'\n" +
                                "\t\t\t\tWHEN '1' THEN '闲置'\n" +
                                "\t\t\t\tWHEN '2' THEN '维修'\n" +
                                "\t\t\t\tWHEN '3' THEN '移交'\t\t\n" +
                                "\t\t\t\tWHEN '4' THEN '报废'\t\n" +
                                "\t\t\t\tWHEN '5' THEN '借出'\t\t\t\t\t\t\t\n" +
                                "\t\t\t\tEND,  \n" +
                                "\t\t\tNULL -- 添加兜底值\n" +
                                "\t) AS use_state_name,\t\t\n" +
                                "\td.brand_model,\n" +
                                "\td.supplier,\n" +
                                "\td.supplier_contact_way,\n" +
                                "\td.purchase_date,\n" +
                                "\td.maintainer,\n" +
                                "\tum2.nick_name as maintainer_nick,\n" +
                                "\td.binding_date,\n" +
                                "\td.device_extend_one,\n" +
                                "\td.device_extend_two,\n" +
                                "\td.device_extend_three,\n" +
                                "\td.device_extend_four,\n" +
                                "\td.device_extend_five,\n" +
                                "\td.device_extend_six,\n" +
                                "\td.device_extend_seven,\n" +
                                "\td.device_extend_eight,\n" +
                                "\td.device_extend_nine,\n" +
                                "\td.device_extend_ten\n" +
                                "FROM `dfs_device` d\n" +
                                "LEFT JOIN `dfs_area` a on a.aid = d.aid\n" +
                                "LEFT JOIN `dfs_grid` g on g.gid = d.gid\n" +
                                "LEFT JOIN `dfs_facilities` f on f.fid = d.fid\n" +
                                "LEFT JOIN `dfs_production_line` l on l.production_line_id = d.production_line_id\n" +
                                "LEFT JOIN `dfs_model` m on m.id = d.model_id\n" +
                                "LEFT JOIN `sys_users` um on um.user_name = d.mag_name\n" +
                                "LEFT JOIN `sys_users` um2 on um2.user_name = d.maintainer\n" +
                                "LEFT JOIN `dfs_work_center_device` wcd on wcd.device_id = d.device_id\n" +
                                "LEFT JOIN `dfs_work_center` wc on wc.id = wcd.work_center_id;")
                        .tableDTO(TableViewCreateDTO.builder()
                                .tableName("v_dimension_device")
                                .fields(Stream.of(
                                        TableFieldCreateVO.builder().fieldCode("device_id").fieldName("设备id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_name").fieldName("设备名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_code").fieldName("设备编号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("aid").fieldName("厂区id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("aname").fieldName("厂区名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("gid").fieldName("车间id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("gname").fieldName("车间名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("fid").fieldName("工位id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("fname").fieldName("设施名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("line_id").fieldName("生产线id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("line_name").fieldName("产线名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("model_id").fieldName("模型id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("model_name").fieldName("模型名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_id").fieldName("工作中心id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("work_center_name").fieldName("工作中心名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("state").fieldName("设备状态").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("state_name").fieldName("设备状态名称").fieldType(TableFieldTypeEnum.VARCHAR).fieldRemark("0-停机 1-运行中 2-暂停 3-故障").build(),
                                        TableFieldCreateVO.builder().fieldCode("place").fieldName("设备地址").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("model_img").fieldName("模型图片").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("simulation_img").fieldName("仿真图").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("mag_name").fieldName("管理人账号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("mag_nickname").fieldName("用户真实姓名").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("remark").fieldName("备注信息").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("state_update_time").fieldName("状态更新时间").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("use_state").fieldName("设备使用状态").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("use_state_name").fieldName("设备使用状态名称").fieldType(TableFieldTypeEnum.VARCHAR).fieldRemark("0在用、1闲置、2维修、3移交、4报废、5借出").build(),
                                        TableFieldCreateVO.builder().fieldCode("brand_model").fieldName("品牌型号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("supplier").fieldName("供应商").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("supplier_contact_way").fieldName("供应商联系方式").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("purchase_date").fieldName("购置日期").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("maintainer").fieldName("设备维护人").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("maintainer_nick").fieldName("用户真实姓名").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("binding_date").fieldName("绑定日期").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_one").fieldName("扩展字段1").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_two").fieldName("扩展字段2").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_three").fieldName("扩展字段3").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_four").fieldName("扩展字段4").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_five").fieldName("扩展字段5").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_six").fieldName("扩展字段6").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_seven").fieldName("扩展字段7").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_eight").fieldName("扩展字段8").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_nine").fieldName("扩展字段9").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_extend_ten").fieldName("扩展字段10").fieldType(TableFieldTypeEnum.VARCHAR).build()
                                ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                                .build()
                        )
                        .build()
                );
            }
            boolean existsDeviceConsumptionPeriod = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, "v_dimension_device_consumption_period_daily").exists();
            if (existsDeviceConsumptionPeriod) {
                log.warn("设备能耗时段每日维表");
            } else {
                addViews.add(TargetGroupViewInsertDTO.builder()
                        .modelId(deviceModel.getId())
                        .targetName("v_dimension_device_consumption_period_daily")
                        .targetCnname("设备能耗时段每日维表")
                        .script(
                                "SELECT\n" +
                                "\tec.device_id,\n" +
                                "\td.device_name,\n" +
                                "\tec.device_code,\n" +
                                "\tec.record_date,\n" +
                                "\tec.eui,\n" +
                                "\tec.consumption,\n" +
                                "\tec.name_type as type,\n" +
                                "\tCOALESCE(\n" +
                                "\t\tCASE ec.name_type\n" +
                                "\t\t\t\tWHEN '0' THEN '尖'\n" +
                                "\t\t\t\tWHEN '1' THEN '峰'\n" +
                                "\t\t\t\tWHEN '2' THEN '平'\n" +
                                "\t\t\t\tWHEN '3' THEN '谷'\t\t\n" +
                                "\t\t\t\tEND,  \n" +
                                "\t\t\tNULL -- 添加兜底值\n" +
                                "\t) AS type_name\t\n" +
                                "from dfs_device_time_period_energy_consumption ec\n" +
                                "LEFT JOIN dfs.dfs_device d on d.device_id = ec.device_id;"
                        )
                        .tableDTO(TableViewCreateDTO.builder()
                                .tableName("v_dimension_device_consumption_period_daily")
                                .fields(Stream.of(
                                        TableFieldCreateVO.builder().fieldCode("device_id").fieldName("设备id").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_name").fieldName("设备名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("device_code").fieldName("设备编号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("record_date").fieldName("记录日期").fieldType(TableFieldTypeEnum.DATETIME).build(),
                                        TableFieldCreateVO.builder().fieldCode("eui").fieldName("传感器eui").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("consumption").fieldName("能耗").fieldType(TableFieldTypeEnum.DOUBLE).fieldRemark("单位：kwh").build(),
                                        TableFieldCreateVO.builder().fieldCode("type").fieldName("时段类型").fieldType(TableFieldTypeEnum.INT).build(),
                                        TableFieldCreateVO.builder().fieldCode("type_name").fieldName("时段类型名称").fieldType(TableFieldTypeEnum.VARCHAR).fieldRemark("尖0、峰1、平2、谷3").build()
                                ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                                .build()
                        )
                        .build()
                );
            }
        }


        // 工单

        // 2
        if(workOrderModel == null) {
            log.warn("model:工单指标模型不存在");
        }else {
            boolean existsWorkOrderTask = targetModelService.lambdaQuery().eq(TargetModelEntity::getTargetName, "v_dimension_work_order_task_today").exists();
            if(existsWorkOrderTask) {
                log.warn("工单今日任务维表已存在");
            }else {
                addViews.add(TargetGroupViewInsertDTO.builder()
                        .modelId(workOrderModel.getId())
                        .targetName("v_dimension_work_order_task_today")
                        .targetCnname("工单今日任务维表")
                        .script("SELECT\n" +
                                "\tt.source_type,\n" +
                                "\tt.work_order_number,\n" +
                                "\td.product_order_number,\n" +
                                "\td.sale_order_number,\n" +
                                "\td.material_code,\n" +
                                "\tm.name as material_name,\n" +
                                "\tm.standard as material_standard\n" +
                                "/* 今日出现过的工单 & 来源标记 */\n" +
                                "from (\n" +
                                "\tSELECT  wp.work_order_number,\n" +
                                "\t\t\t\t\t'计划'            AS source_type          -- ① 计划表里出现过的，直接标记“计划”\n" +
                                "\tFROM    `dfs`.`dfs_work_order_plan` wp\n" +
                                "\tWHERE   wp.time >= CURDATE()\n" +
                                "\t\tAND   wp.time < CURDATE() + INTERVAL 1 DAY      -- 今日 00:00:00 ≤ time < 明日 00:00:00\n" +
                                "\n" +
                                "\tUNION ALL\n" +
                                "\n" +
                                "\tSELECT  rc.work_order_number,\n" +
                                "\t\t\t\t\t'生产'            AS source_type          -- ② 只在生产记录出现的，标记“生产”\n" +
                                "\tFROM    `dfs`.`dfs_record_work_order_day_count` rc\n" +
                                "\tWHERE   rc.time >= CURDATE()\n" +
                                "\t\tAND   rc.time < CURDATE() + INTERVAL 1 DAY\n" +
                                "\t\tAND NOT EXISTS (                                -- 保证同一天计划表里没有\n" +
                                "\t\t\t\t\tSELECT 1\n" +
                                "\t\t\t\t\tFROM   `dfs`.`dfs_work_order_plan` p\n" +
                                "\t\t\t\t\tWHERE  p.work_order_number = rc.work_order_number\n" +
                                "\t\t\t\t\t\tAND  p.time >= CURDATE()\n" +
                                "\t\t\t\t\t\tAND  p.time < CURDATE() + INTERVAL 1 DAY\n" +
                                "\t\t\t\t)\n" +
                                ")\tt \n" +
                                "LEFT JOIN `dfs`.`dfs_work_order` d ON d.work_order_number = t.work_order_number\n" +
                                "LEFT JOIN `dfs`.`dfs_material` m ON m.`code` = d.material_code;")
                        .tableDTO(TableViewCreateDTO.builder()
                                .tableName("v_dimension_work_order_task_today")
                                .fields(Stream.of(
                                        TableFieldCreateVO.builder().fieldCode("source_type").fieldName("任务来源").fieldType(TableFieldTypeEnum.VARCHAR).fieldRemark("当日存在计划时为：'计划'; 当日存在产出时为：'生产';都存在时为：'计划'").build(),
                                        TableFieldCreateVO.builder().fieldCode("work_order_number").fieldName("工单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("product_order_number").fieldName("订单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("sale_order_number").fieldName("销售订单号").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("material_code").fieldName("物料编码").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("material_name").fieldName("物料名称").fieldType(TableFieldTypeEnum.VARCHAR).build(),
                                        TableFieldCreateVO.builder().fieldCode("material_standard").fieldName("物料规格").fieldType(TableFieldTypeEnum.VARCHAR).build()
                                ).map(TableFieldCreateVO::covert).collect(Collectors.toList()))
                                .build()
                        )
                        .build()
                );
            }

        }

        if(CollUtil.isNotEmpty(addViews)) {
            targetModelService.batchAddTargetGroupView(addViews);
        }

    }

}

