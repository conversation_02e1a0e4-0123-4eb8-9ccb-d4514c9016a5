package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueDictEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 用电时段表 添加 深谷项
 *
 * <AUTHOR>
 */
public class V3_10_3__JavaMigration extends BaseJavaMigration {


    @Override
    public void migrate(Context context) {
        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        // 上版本新增的下推配置数据存在脏数据，虽然不影响上版本的业务，但是这里统一删除重新跑下脚本
        List<String> removeInfos = Stream.of("saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.appId", "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.url", "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.sourceOrderType", "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder.targetOrderType", "saleOrder.pushDownConfig.productOrder.bom.appId", "saleOrder.pushDownConfig.productOrder.bom.url", "saleOrder.pushDownConfig.productOrder.bom.sourceOrderType", "saleOrder.pushDownConfig.productOrder.bom.targetOrderType", "saleOrder.pushDownConfig.productOrder.mergeOrder.appId", "saleOrder.pushDownConfig.productOrder.mergeOrder.url", "saleOrder.pushDownConfig.productOrder.mergeOrder.sourceOrderType", "saleOrder.pushDownConfig.productOrder.mergeOrder.targetOrderType", "saleOrder.pushDownConfig.productOrder.jumpPage.appId", "saleOrder.pushDownConfig.productOrder.jumpPage.url", "saleOrder.pushDownConfig.productOrder.jumpPage.sourceOrderType", "saleOrder.pushDownConfig.productOrder.jumpPage.targetOrderType", "saleOrder.pushDownConfig.productOrder.bomMergeOrder.appId", "saleOrder.pushDownConfig.productOrder.bomMergeOrder.url", "saleOrder.pushDownConfig.productOrder.bomMergeOrder.sourceOrderType", "saleOrder.pushDownConfig.productOrder.bomMergeOrder.targetOrderType", "saleOrder.pushDownConfig.workOrder.craftRoute.appId", "saleOrder.pushDownConfig.workOrder.craftRoute.url", "saleOrder.pushDownConfig.workOrder.craftRoute.sourceOrderType", "saleOrder.pushDownConfig.workOrder.craftRoute.targetOrderType", "saleOrder.pushDownConfig.productOrder.bomAuto.appId", "saleOrder.pushDownConfig.productOrder.bomAuto.url", "saleOrder.pushDownConfig.productOrder.bomAuto.sourceOrderType", "saleOrder.pushDownConfig.productOrder.bomAuto.targetOrderType", "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch.appId", "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch.url", "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch.sourceOrderType", "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch.targetOrderType", "saleOrder.pushDownConfig.purchaseRequest.jumpPage.appId", "saleOrder.pushDownConfig.purchaseRequest.jumpPage.url", "saleOrder.pushDownConfig.purchaseRequest.jumpPage.sourceOrderType", "saleOrder.pushDownConfig.purchaseRequest.jumpPage.targetOrderType", "saleOrder.pushDownConfig.deliveryApplication.jumpPage.appId", "saleOrder.pushDownConfig.deliveryApplication.jumpPage.url", "saleOrder.pushDownConfig.deliveryApplication.jumpPage.sourceOrderType", "saleOrder.pushDownConfig.deliveryApplication.jumpPage.targetOrderType", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.appId", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.url", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.sourceOrderType", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt.targetOrderType", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.appId", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.url", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.sourceOrderType", "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut.targetOrderType", "saleOrder.pushDownConfig.subcontractOrder.jumpPage.appId", "saleOrder.pushDownConfig.subcontractOrder.jumpPage.url", "saleOrder.pushDownConfig.subcontractOrder.jumpPage.sourceOrderType", "saleOrder.pushDownConfig.subcontractOrder.jumpPage.targetOrderType", "production.productOrderPushDownConfig.workOrder.jumpPage.appId", "production.productOrderPushDownConfig.workOrder.jumpPage.url", "production.productOrderPushDownConfig.workOrder.jumpPage.sourceOrderType", "production.productOrderPushDownConfig.workOrder.jumpPage.targetOrderType", "production.productOrderPushDownConfig.workOrder.craftRouteAuto.appId", "production.productOrderPushDownConfig.workOrder.craftRouteAuto.url", "production.productOrderPushDownConfig.workOrder.craftRouteAuto.sourceOrderType", "production.productOrderPushDownConfig.workOrder.craftRouteAuto.targetOrderType", "production.productOrderPushDownConfig.workOrder.craftRoute.appId", "production.productOrderPushDownConfig.workOrder.craftRoute.url", "production.productOrderPushDownConfig.workOrder.craftRoute.sourceOrderType", "production.productOrderPushDownConfig.workOrder.craftRoute.targetOrderType", "production.productOrderPushDownConfig.purchaseRequest.jumpPage.appId", "production.productOrderPushDownConfig.purchaseRequest.jumpPage.url", "production.productOrderPushDownConfig.purchaseRequest.jumpPage.sourceOrderType", "production.productOrderPushDownConfig.purchaseRequest.jumpPage.targetOrderType", "production.productOrderPushDownConfig.productMaterialsList.jumpPage.appId", "production.productOrderPushDownConfig.productMaterialsList.jumpPage.url", "production.productOrderPushDownConfig.productMaterialsList.jumpPage.sourceOrderType", "production.productOrderPushDownConfig.productMaterialsList.jumpPage.targetOrderType", "production.productOrderPushDownConfig.productionIn.jumpPage.appId", "production.productOrderPushDownConfig.productionIn.jumpPage.url", "production.productOrderPushDownConfig.productionIn.jumpPage.sourceOrderType", "production.productOrderPushDownConfig.productionIn.jumpPage.targetOrderType", "production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.appId", "production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.url", "production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.sourceOrderType", "production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage.targetOrderType", "production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.appId", "production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.url", "production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.sourceOrderType", "production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage.targetOrderType", "production.productMaterialsListPushDownConfig.transferOrder.jumpPage.appId", "production.productMaterialsListPushDownConfig.transferOrder.jumpPage.url", "production.productMaterialsListPushDownConfig.transferOrder.jumpPage.sourceOrderType", "production.productMaterialsListPushDownConfig.transferOrder.jumpPage.targetOrderType", "production.workOrderPushDownConfig.takeOutApplication.jumpPage.appId", "production.workOrderPushDownConfig.takeOutApplication.jumpPage.url", "production.workOrderPushDownConfig.takeOutApplication.jumpPage.sourceOrderType", "production.workOrderPushDownConfig.takeOutApplication.jumpPage.targetOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.appId", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.url", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.sourceOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt.targetOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.appId", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.url", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.sourceOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn.targetOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.appId", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.url", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.sourceOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement.targetOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.appId", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.url", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.sourceOrderType", "production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct.targetOrderType", "production.workOrderPushDownConfig.materialList.pushBatch.appId", "production.workOrderPushDownConfig.materialList.pushBatch.url", "production.workOrderPushDownConfig.materialList.pushBatch.sourceOrderType", "production.workOrderPushDownConfig.materialList.pushBatch.targetOrderType", "production.workOrderPushDownConfig.materialList.jumpPage.appId", "production.workOrderPushDownConfig.materialList.jumpPage.url", "production.workOrderPushDownConfig.materialList.jumpPage.sourceOrderType", "production.workOrderPushDownConfig.materialList.jumpPage.targetOrderType", "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.appId", "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.url", "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.sourceOrderType", "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage.targetOrderType", "purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.appId", "purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.url", "purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.sourceOrderType", "purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage.targetOrderType", "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.appId", "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.url", "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.sourceOrderType", "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage.targetOrderType", "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.appId", "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.url", "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.sourceOrderType", "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage.targetOrderType", "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn.appId", "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn.url", "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn.sourceOrderType", "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn.targetOrderType", "purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder.appId", "purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder.url", "purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder.sourceOrderType", "purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder.targetOrderType", "purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.appId", "purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.url", "purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.sourceOrderType", "purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage.targetOrderType", "purchase.purchaseReceiptPushDownConfig.inspectOrder.batchPush.appId", "purchase.purchaseReceiptPushDownConfig.inspectOrder.batchPush.url", "purchase.purchaseReceiptPushDownConfig.inspectOrder.batchPush.sourceOrderType", "purchase.purchaseReceiptPushDownConfig.inspectOrder.batchPush.targetOrderType", "purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.appId", "purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.url", "purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.sourceOrderType", "purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage.targetOrderType").collect(Collectors.toList());
        valueService.lambdaUpdate().in(OrderPushDownConfigValueEntity::getValueFullPathCode, removeInfos).remove();
        valueDictService.lambdaUpdate().in(OrderPushDownConfigValueDictEntity::getValueFullPathCode, removeInfos).remove();
        // 生产订单按工艺路线下推工单，需要增加“是否过滤委外品”的配置
        addPushDownConf();
        dealHistorySaleOrderPushDownProductOrderData();
        dealHistorySaleOrderPushDownPurchaseRequestData();
        dealHistorySaleOrderPushDownDeliveryApplicationData();
        dealHistorySaleOrderPushDownSaleInAndOutData();
        dealHistorySaleOrderPushDownSubcontractOrderData();
        dealHistoryProductOrderPushDownWorkOrderData();
        dealHistoryProductOrderPushDownPurchaseRequestData();
        dealHistoryProductOrderPushDownProductMaterialsListData();
        dealHistoryProductOrderPushDownProductionInData();
        dealHistoryProductMaterialsListPushDownOutputPickingProductData();
        dealHistoryProductMaterialsListPushDownWorkOrderSupplementData();
        dealHistoryProductMaterialsListPushDownTransferOrderData();
        dealHistoryWorkOrderPushDownTakeOutApplicationData();
        dealHistoryWorkOrderPushDownProductStockInAndOutData();
        dealHistoryWorkOrderPushDownWorkOrderMaterialListData();
        dealHistoryPurchaseRequestPushDownPurchaseOrderData();
        dealHistoryPurchaseOrderPushDownSupplierBomData();
        dealHistoryPurchaseOrderPushDownPurchaseReceiptData();
        dealHistoryPurchaseOrderPushDownPurchaseInData();
        dealHistoryPurchaseReceiptPushDownRequestStockInAndOutData();
        dealHistoryPurchaseReceiptPushDownReturnOrderData();
        dealHistoryPurchaseReceiptPushDownIncomingInspectionData();
        dealHistoryPurchaseReceiptPushDownInspectOrderData();
        dealHistoryPurchaseReturnApplicationPushDownPurchaseReturnOutData();
    }

    private static void addPushDownConf() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.workOrder.craftRoute",
                "production.productOrderPushDownConfig.workOrder.craftRouteAuto"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("enableFilterOutsourcing")
                    .valueName("是否过滤委外品(单选)")
                    .inputType("select")
                    .optionValuesType("table")
                    .valueFullPathCode(configFullPathCode + ".enableFilterOutsourcing")
                    .configFullPathCode(configFullPathCode)
                    .optionValues("[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]")
                    .value("true")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("enableFilterOutsourcing")
                    .valueName("是否过滤委外品(单选)")
                    .inputType("select")
                    .optionValuesType("table")
                    .valueFullPathCode(configFullPathCode + ".enableFilterOutsourcing")
                    .configFullPathCode(configFullPathCode)
                    .optionValues("[{\"value\":true,\"label\":\"是\"},{\"value\":false,\"label\":\"否\"}]")
                    .value("true")
                    .build());
        }
        valueService.saveBatch(list);
    }


    private void dealHistorySaleOrderPushDownProductOrderData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.productOrder.jumpPage",
                "saleOrder.pushDownConfig.productOrder.bom",
                "saleOrder.pushDownConfig.productOrder.mergeOrder",
                "saleOrder.pushDownConfig.productOrder.bomMergeOrder",
                "saleOrder.pushDownConfig.productOrder.bomAuto",
                "saleOrder.pushDownConfig.workOrder.craftRoute",
                "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder",
                "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownPurchaseRequestData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.purchaseRequest.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownDeliveryApplicationData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.deliveryApplication.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"deliveryApplication\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"deliveryApplication\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownSaleInAndOutData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut",
                "saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleInAndOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleInAndOut\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownSubcontractOrderData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.subcontractOrder.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"subcontractOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"subcontractOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownWorkOrderData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.workOrder.jumpPage",
                "production.productOrderPushDownConfig.workOrder.craftRoute",
                "production.productOrderPushDownConfig.workOrder.craftRouteAuto"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownPurchaseRequestData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.purchaseRequest.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownProductMaterialsListData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.productMaterialsList.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownProductionInData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.productionIn.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productionIn\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productionIn\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductMaterialsListPushDownOutputPickingProductData() {
        List<String> collect = Stream.of(
                "production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"outputPickingProduct\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"outputPickingProduct\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductMaterialsListPushDownWorkOrderSupplementData() {
        List<String> collect = Stream.of(
                "production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderSupplement\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderSupplement\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductMaterialsListPushDownTransferOrderData() {
        List<String> collect = Stream.of(
                "production.productMaterialsListPushDownConfig.transferOrder.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"transferOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"transferOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryWorkOrderPushDownTakeOutApplicationData() {
        List<String> collect = Stream.of(
                "production.workOrderPushDownConfig.takeOutApplication.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"takeOutApplication\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"takeOutApplication\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryWorkOrderPushDownProductStockInAndOutData() {
        List<String> collect = Stream.of(
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct",
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement",
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt",
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productStockInAndOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productStockInAndOut\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryWorkOrderPushDownWorkOrderMaterialListData() {
        List<String> collect = Stream.of(
                "production.workOrderPushDownConfig.materialList.jumpPage",
                "production.workOrderPushDownConfig.materialList.pushBatch"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderMaterialList\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderMaterialList\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseRequestPushDownPurchaseOrderData() {
        List<String> collect = Stream.of(
                "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseOrderPushDownSupplierBomData() {
        List<String> collect = Stream.of(
                "purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"supplierBom\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"supplierBom\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseOrderPushDownPurchaseReceiptData() {
        List<String> collect = Stream.of(
                "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseOrderPushDownPurchaseInData() {
        List<String> collect = Stream.of(
                "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseIn\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseIn\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownRequestStockInAndOutData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"requestStockInAndOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"requestStockInAndOut\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownReturnOrderData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"returnOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"returnOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownIncomingInspectionData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"incomingInspection\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"incomingInspection\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownInspectOrderData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.inspectOrder.batchPush"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"inspectOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"inspectOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReturnApplicationPushDownPurchaseReturnOutData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnApplication\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnApplication\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnOut\"")
                    .build());
        }
        valueService.saveBatch(list);
    }


}

