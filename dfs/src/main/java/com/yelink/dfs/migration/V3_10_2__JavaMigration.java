package com.yelink.dfs.migration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yelink.dfs.entity.statement.ManageReportEntity;
import com.yelink.dfs.mapper.statement.ManageReportMapper;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueDictService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.service.statement.ManageReportService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueDictEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.apache.commons.lang.StringUtils;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 处理历史数据
 */
public class V3_10_2__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 报表管理：配置页面修改，需要处理历史数据
        dealHistoryManageReport();
        // 历史下推需要配置需要统一加上源单据和目标单据字段配置
        dealHistorySaleOrderPushDownProductOrderData();
        dealHistorySaleOrderPushDownPurchaseRequestData();
        dealHistorySaleOrderPushDownDeliveryApplicationData();
        dealHistorySaleOrderPushDownSaleInAndOutData();
        dealHistorySaleOrderPushDownSubcontractOrderData();
        dealHistoryProductOrderPushDownWorkOrderData();
        dealHistoryProductOrderPushDownPurchaseRequestData();
        dealHistoryProductOrderPushDownProductMaterialsListData();
        dealHistoryProductOrderPushDownProductionInData();
        dealHistoryProductMaterialsListPushDownOutputPickingProductData();
        dealHistoryProductMaterialsListPushDownWorkOrderSupplementData();
        dealHistoryProductMaterialsListPushDownTransferOrderData();
        dealHistoryWorkOrderPushDownTakeOutApplicationData();
        dealHistoryWorkOrderPushDownProductStockInAndOutData();
        dealHistoryWorkOrderPushDownWorkOrderMaterialListData();
        dealHistoryPurchaseRequestPushDownPurchaseOrderData();
        dealHistoryPurchaseOrderPushDownSupplierBomData();
        dealHistoryPurchaseOrderPushDownPurchaseReceiptData();
        dealHistoryPurchaseOrderPushDownPurchaseInData();
        dealHistoryPurchaseReceiptPushDownRequestStockInAndOutData();
        dealHistoryPurchaseReceiptPushDownReturnOrderData();
        dealHistoryPurchaseReceiptPushDownIncomingInspectionData();
        dealHistoryPurchaseReceiptPushDownInspectOrderData();
        dealHistoryPurchaseReturnApplicationPushDownPurchaseReturnOutData();
    }


    private void dealHistorySaleOrderPushDownProductOrderData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.productOrder.jumpPage",
                "saleOrder.pushDownConfig.productOrder.bom",
                "saleOrder.pushDownConfig.productOrder.mergeOrder",
                "saleOrder.pushDownConfig.productOrder.bomMergeOrder",
                "saleOrder.pushDownConfig.productOrder.bomAuto",
                "saleOrder.pushDownConfig.workOrder.craftRoute",
                "saleOrder.pushDownConfig.workOrder.craftRouteMergeOrder",
                "saleOrder.pushDownConfig.workOrderSubcontractOrder.craftRouteBatch"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownPurchaseRequestData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.purchaseRequest.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownDeliveryApplicationData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.deliveryApplication.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"deliveryApplication\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"deliveryApplication\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownSaleInAndOutData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.saleInAndOut.jumpPageSaleOut",
                "saleOrder.pushDownConfig.saleInAndOut.jumpPageSalesReturnReceipt"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleInAndOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleInAndOut\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistorySaleOrderPushDownSubcontractOrderData() {
        List<String> collect = Stream.of(
                "saleOrder.pushDownConfig.subcontractOrder.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"subcontractOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"saleOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"subcontractOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownWorkOrderData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.workOrder.jumpPage",
                "production.productOrderPushDownConfig.workOrder.craftRoute",
                "production.productOrderPushDownConfig.workOrder.craftRouteAuto"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownPurchaseRequestData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.purchaseRequest.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownProductMaterialsListData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.productMaterialsList.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductOrderPushDownProductionInData() {
        List<String> collect = Stream.of(
                "production.productOrderPushDownConfig.productionIn.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productionIn\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productionIn\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductMaterialsListPushDownOutputPickingProductData() {
        List<String> collect = Stream.of(
                "production.productMaterialsListPushDownConfig.outputPickingProduct.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"outputPickingProduct\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"outputPickingProduct\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductMaterialsListPushDownWorkOrderSupplementData() {
        List<String> collect = Stream.of(
                "production.productMaterialsListPushDownConfig.workOrderSupplement.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderSupplement\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderSupplement\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryProductMaterialsListPushDownTransferOrderData() {
        List<String> collect = Stream.of(
                "production.productMaterialsListPushDownConfig.transferOrder.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"transferOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productMaterialsList\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"transferOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryWorkOrderPushDownTakeOutApplicationData() {
        List<String> collect = Stream.of(
                "production.workOrderPushDownConfig.takeOutApplication.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"takeOutApplication\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"takeOutApplication\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryWorkOrderPushDownProductStockInAndOutData() {
        List<String> collect = Stream.of(
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageOutputPickingProduct",
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageWorkOrderSupplement",
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionReturnReceipt",
                "production.workOrderPushDownConfig.productStockInAndOut.jumpPageProductionIn"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productStockInAndOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"productStockInAndOut\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryWorkOrderPushDownWorkOrderMaterialListData() {
        List<String> collect = Stream.of(
                "production.workOrderPushDownConfig.materialList.jumpPage",
                "production.workOrderPushDownConfig.materialList.pushBatch"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderMaterialList\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"workOrderMaterialList\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseRequestPushDownPurchaseOrderData() {
        List<String> collect = Stream.of(
                "purchase.purchaseRequestPushDownConfig.purchaseOrder.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseRequest\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseOrderPushDownSupplierBomData() {
        List<String> collect = Stream.of(
                "purchase.purchaseOrderPushDownConfig.supplierBom.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"supplierBom\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"supplierBom\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseOrderPushDownPurchaseReceiptData() {
        List<String> collect = Stream.of(
                "purchase.purchaseOrderPushDownConfig.purchaseReceipt.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseOrderPushDownPurchaseInData() {
        List<String> collect = Stream.of(
                "purchase.purchaseOrderPushDownConfig.purchaseIn.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseIn\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseOrder\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseIn\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownRequestStockInAndOutData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.requestStockInAndOut.jumpPagePurchaseIn"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"requestStockInAndOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"requestStockInAndOut\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownReturnOrderData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.returnOrder.jumpPageReturnOrder"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"returnOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"returnOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownIncomingInspectionData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.incomingInspection.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"incomingInspection\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"incomingInspection\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReceiptPushDownInspectOrderData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReceiptPushDownConfig.inspectOrder.batchPush"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"inspectOrder\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReceipt\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"inspectOrder\"")
                    .build());
        }
        valueService.saveBatch(list);

    }

    private void dealHistoryPurchaseReturnApplicationPushDownPurchaseReturnOutData() {
        List<String> collect = Stream.of(
                "purchase.purchaseReturnApplicationPushDownConfig.purchaseReturnOut.jumpPage"
        ).collect(Collectors.toList());
        List<OrderPushDownConfigValueEntity> list = new ArrayList<>();
        List<OrderPushDownConfigValueDictEntity> dictlist = new ArrayList<>();
        OrderPushDownConfigValueDictService valueDictService = SpringUtil.getBean(OrderPushDownConfigValueDictService.class);
        List<OrderPushDownConfigValueDictEntity> dictValueEntities = valueDictService.lambdaQuery()
                .in(OrderPushDownConfigValueDictEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueDictEntity::getConfigFullPathCode)
                .list();
        Map<String, List<OrderPushDownConfigValueDictEntity>> dictMap = dictValueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueDictEntity::getConfigFullPathCode));
        for (Map.Entry<String, List<OrderPushDownConfigValueDictEntity>> listEntry : dictMap.entrySet()) {
            OrderPushDownConfigValueDictEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnApplication\"")
                    .build());
            dictlist.add(OrderPushDownConfigValueDictEntity.builder()
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnOut\"")
                    .build());
        }
        valueDictService.saveBatch(dictlist);

        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        // 根据itemId分组，对每个下推工艺路线的配置项新增两配置
        List<OrderPushDownConfigValueEntity> valueEntities = valueService.lambdaQuery()
                .in(OrderPushDownConfigValueEntity::getConfigFullPathCode, collect)
                .select(OrderPushDownConfigValueEntity::getConfigFullPathCode, OrderPushDownConfigValueEntity::getItemId)
                .list();
        Map<Integer, List<OrderPushDownConfigValueEntity>> map = valueEntities.stream().collect(Collectors.groupingBy(OrderPushDownConfigValueEntity::getItemId));
        for (Map.Entry<Integer, List<OrderPushDownConfigValueEntity>> listEntry : map.entrySet()) {
            Integer itemId = listEntry.getKey();
            OrderPushDownConfigValueEntity valueEntity = listEntry.getValue().get(0);
            String configFullPathCode = valueEntity.getConfigFullPathCode();
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("appId")
                    .valueName("appId")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".appId")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("url")
                    .valueName("前端路由")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".url")
                    .configFullPathCode(configFullPathCode)
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("sourceOrderType")
                    .valueName("源单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".sourceOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnApplication\"")
                    .build());
            list.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(itemId)
                    .valueCode("targetOrderType")
                    .valueName("目标单据类型编码")
                    .inputType("input")
                    .optionValuesType("input")
                    .valueFullPathCode(configFullPathCode + ".targetOrderType")
                    .configFullPathCode(configFullPathCode)
                    .value("\"purchaseReturnOut\"")
                    .build());
        }
        valueService.saveBatch(list);

    }


    private void dealHistoryManageReport() {
        ManageReportService manageReportService = SpringUtil.getBean(ManageReportService.class);
        // 所有历史报表
        List<ManageReportEntity> list = manageReportService.lambdaQuery()
                .select(ManageReportEntity::getReportId, ManageReportEntity::getReportName, ManageReportEntity::getSubhead, ManageReportEntity::getState, ManageReportEntity::getDefaultInner, ManageReportEntity::getModel, ManageReportEntity::getDataSources, ManageReportEntity::getDataSourceConfig, ManageReportEntity::getTemplateId, ManageReportEntity::getCreateTime, ManageReportEntity::getCreateBy)
                .list();
        for (ManageReportEntity manageReport : list) {
            String dataSources = manageReport.getDataSources();
            List<JsonNode> addSourceNodes = new ArrayList<>();
            if (StringUtils.isNotBlank(dataSources)) {
                List<String> dataSourceList = Arrays.asList(dataSources.split(Constants.SEP));
                JsonNode allDataSourceNode = JacksonUtil.stringToNode(manageReport.getDataSourceConfig());
                for (JsonNode jsonNode1 : allDataSourceNode) {
                    JsonNode childrenNode = jsonNode1.get("children");
                    if (Objects.nonNull(childrenNode)) {
                        for (int j = 0; j < childrenNode.size(); j++) {
                            JsonNode jsonNode = childrenNode.get(j);
                            String dataSourceCode = jsonNode.get("code").textValue();
                            if (dataSourceList.contains(dataSourceCode)) {
                                ObjectNode objectNode = new ObjectNode(JsonNodeFactory.instance);
                                objectNode.put("code", jsonNode.get("code"));
                                objectNode.put("name", jsonNode.get("name"));
                                objectNode.put("fieldConfigs", jsonNode.get("fieldConfigs"));
                                addSourceNodes.add(objectNode);
                            }
                        }
                    }
                }
            }
            manageReport.setDataSourceConfig(JacksonUtil.toJSONString(addSourceNodes));
            ((ManageReportMapper) manageReportService.getBaseMapper()).updateManageReportById(manageReport);
        }
    }


}

