package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.OrderPushDownConfigValueService;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigValueEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemEntity;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;


/**
 * 内置角色权限
 *
 * <AUTHOR>
 */
public class V3_13_1_10__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        // 处理生产订单下推生产订单用料清单增加按物料类型过滤下推配置项
        dealHistoryProductOrderPushDownProductOrderMaterialData();
        // 处理生产工单下推生产工单用料清单增加按物料类型过滤下推配置项
        dealHistoryWorkOrderPushDownProductOrderMaterialData();
    }

    private void dealHistoryWorkOrderPushDownProductOrderMaterialData() {
        List<OrderPushDownConfigValueEntity> orderPushDownConfigValueEntities = new ArrayList<>();
        OrderPushDownItemService itemService = SpringUtil.getBean(OrderPushDownItemService.class);
        List<OrderPushDownItemEntity> itemEntities = itemService.lambdaQuery()
                .eq(OrderPushDownItemEntity::getInstanceType, "production.workOrderPushDownConfig.materialList.bomPush")
                .select(OrderPushDownItemEntity::getId)
                .list();
        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        itemEntities.forEach(orderPushDownItemEntity -> {
            orderPushDownConfigValueEntities.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(orderPushDownItemEntity.getId())
                    .groupType("targetConf")
                    .valueCode("filterMaterialTypes")
                    .valueName("物料类型过滤")
                    .valueFullPathCode("production.workOrderPushDownConfig.materialList.bomPush.filterMaterialTypes")
                    .configFullPathCode("production.workOrderPushDownConfig.materialList.bomPush")
                    .inputType("select-multiple")
                    .optionValuesType("api")
                    .url("/api/materials/material/type/list")
                    .method("get")
                    .params(null)
                    .respParamField("id,name")
                    .optionValues(null)
                    .value("[]")
                    .rules(null)
                    .build());
        });
        valueService.saveBatch(orderPushDownConfigValueEntities);
    }

    private void dealHistoryProductOrderPushDownProductOrderMaterialData() {
        List<OrderPushDownConfigValueEntity> orderPushDownConfigValueEntities = new ArrayList<>();
        OrderPushDownItemService itemService = SpringUtil.getBean(OrderPushDownItemService.class);
        List<OrderPushDownItemEntity> itemEntities = itemService.lambdaQuery()
                .eq(OrderPushDownItemEntity::getInstanceType, "production.productOrderPushDownConfig.productMaterialsList.pushBatch")
                .select(OrderPushDownItemEntity::getId)
                .list();
        OrderPushDownConfigValueService valueService = SpringUtil.getBean(OrderPushDownConfigValueService.class);
        itemEntities.forEach(orderPushDownItemEntity -> {
            orderPushDownConfigValueEntities.add(OrderPushDownConfigValueEntity.builder()
                    .itemId(orderPushDownItemEntity.getId())
                    .groupType("targetConf")
                    .valueCode("filterMaterialTypes")
                    .valueName("物料类型过滤")
                    .valueFullPathCode("production.productOrderPushDownConfig.productMaterialsList.pushBatch.filterMaterialTypes")
                    .configFullPathCode("production.productOrderPushDownConfig.productMaterialsList.pushBatch")
                    .inputType("select-multiple")
                    .optionValuesType("api")
                    .url("/api/materials/material/type/list")
                    .method("get")
                    .params(null)
                    .respParamField("id,name")
                    .optionValues(null)
                    .value("[]")
                    .rules(null)
                    .build());
        });
        valueService.saveBatch(orderPushDownConfigValueEntities);
    }

}

