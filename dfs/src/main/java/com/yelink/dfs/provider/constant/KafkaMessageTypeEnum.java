package com.yelink.dfs.provider.constant;

import com.yelink.dfscommon.provider.constant.KafkaMessageTypeInterface;

/**
 * @description: kafka推送消息类型
 * @author: shuang
 * @time: 2022/2/16
 */
public enum KafkaMessageTypeEnum implements KafkaMessageTypeInterface {

    /**
     * 销售订单相关事件
     */
    SALE_ORDER_STATUS_CHANGE_MESSAGE("saleOrder", "statusChange", "销售订单状态改变消息"),
    SALE_ORDER_UPDATE_MESSAGE("saleOrder", "update", "销售订单状态改变消息"),

    /**
     * 客户资料相关事件
     */
    CUSTOMER_UPDATE_MESSAGE("customer", "update", "客户资料编辑消息"),
    CUSTOMER_ADD_MESSAGE("customer", "add", "客户资料新增消息"),
    CUSTOMER_DELETE_MESSAGE("customer", "delete", "客户资料删除消息"),
    CUSTOMER_STATUS_CHANGE_MESSAGE("customer", "statusChange", "客户资料状态变更消息"),
    CUSTOMER_MATERIAL_DELETE_MESSAGE("customerMaterial", "delete", "客户物料删除消息"),
    CUSTOMER_MATERIAL_ADD_MESSAGE("customerMaterial", "add", "客户物料添加消息"),

    /**
     * 出货申请相关事件
     */
    DELIVERY_APPLICATION_UPDATE_MESSAGE("deliveryApplication", "update", "出货申请编辑消息"),
    DELIVERY_APPLICATION_ADD_MESSAGE("deliveryApplication", "add", "出货申请新增消息"),
    DELIVERY_APPLICATION_DELETE_MESSAGE("deliveryApplication", "delete", "出货申请删除消息"),
    DELIVERY_APPLICATION_STATUS_CHANGE_MESSAGE("deliveryApplication", "statusChange", "出货申请状态变更消息"),

    /**
     * 物料信息相关事件
     */
    MATERIAL_UPDATE_MESSAGE("material", "update", "物料编辑消息"),
    MATERIAL_ADD_MESSAGE("material", "add", "物料新增消息"),
    MATERIAL_DELETE_MESSAGE("material", "delete", "物料删除消息"),

    /**
     * 物料类型
     */
    MATERIAL_TYPE_ADD_MESSAGE("materialType", "add", "物料类型新增消息"),
    MATERIAL_TYPE_UPDATE_MESSAGE("materialType", "update", "物料类型编辑消息"),
    MATERIAL_TYPE_DELETE_MESSAGE("materialType", "delete", "物料类型删除消息"),

    /**
     * 物料单位
     */
    MATERIAL_UNIT_ADD_MESSAGE("materialUnit", "add", "物料单位新增消息"),
    MATERIAL_UNIT_UPDATE_MESSAGE("materialUnit", "update", "物料单位编辑消息"),
    MATERIAL_UNIT_DELETE_MESSAGE("materialUnit", "delete", "物料单位删除消息"),

    /**
     * 供应商相关事件
     */
    SUPPLIER_STATUS_CHANGE_MESSAGE("supplier", "statusChange", "供应商状态改变消息"),
    SUPPLIER_UPDATE_MESSAGE("supplier", "update", "供应商编辑消息"),
    SUPPLIER_ADD_MESSAGE("supplier", "add", "供应商新增消息"),
    SUPPLIER_DELETE_MESSAGE("supplier", "delete", "供应商删除消息"),
    SUPPLIER_MATERIAL_DELETE_MESSAGE("supplierMaterial", "delete", "供应商物料删除消息"),
    SUPPLIER_MATERIAL_ADD_MESSAGE("supplierMaterial", "add", "供应商物料添加消息"),
    SUPPLIER_MATERIAL_BATCH_ADD_MESSAGE("supplierMaterial", "addBatch", "供应商物料批量添加消息"),
    SUPPLIER_MATERIAL_UPDATE_MESSAGE("supplierMaterial", "update", "供应商物料更新消息"),

    /**
     * 模块审批相关事件
     */

    /**
     * 工作日历相关事件
     */
    WORK_CALENDAR_ADD_MESSAGE("workCalendar", "add", "工作日历新增消息"),
    WORK_CALENDAR_UPDATE_MESSAGE("workCalendar", "update", "工作日历编辑消息"),
    WORK_CALENDAR_DELETE_MESSAGE("workCalendar", "delete", "工作日历删除消息"),
    WORK_CALENDAR_DOWN_ADD_MESSAGE("workCalendarDown", "add", "工作日历计划停产新增消息"),
    WORK_CALENDAR_DOWN_UPDATE_MESSAGE("workCalendarDown", "update", "工作日历计划停产编辑消息"),
    WORK_CALENDAR_DOWN_DELETE_MESSAGE("workCalendarDown", "delete", "工作日历计划停产删除消息"),
    /**
     * 班次相关
     */
    SHIFT_ADD_MESSAGE("shift", "add", "班次新增消息"),
    SHIFT_UPDATE_MESSAGE("shift", "update", "班次编辑消息"),
    SHIFT_DELETE_MESSAGE("shift", "delete", "班次删除消息"),

    /**
     * 生产工单相关事件
     */
    WORK_ORDER_STATUS_CHANGE_MESSAGE("workOrder", "statusChange", "工单状态改变消息"),
    WORK_ORDER_UPDATE_MESSAGE("workOrder", "update", "工单编辑消息"),
    WORK_ORDER_ADD_MESSAGE("workOrder", "add", "工单新增消息"),
    WORK_ORDER_DELETE_MESSAGE("workOrder", "delete", "工单删除消息"),
    WORK_ORDER_RETURN_MESSAGE("workOrder", "return", "工单回退消息"),
    WORK_ORDER_COUNT_CHANGE_MESSAGE("workOrder", "countChange", "工单数量修改消息"),
    /**
     * 生产工单用料清单相关事件
     */
    WORK_ORDER_MATERIAL_LIST_ADD_MESSAGE("workOrderMaterialList", "add", "生产工单用料清单新增消息"),
    WORK_ORDER_MATERIAL_LIST_UPDATE_MESSAGE("workOrderMaterialList", "update", "生产工单用料清单更新消息"),
    WORK_ORDER_MATERIAL_LIST_DELETE_MESSAGE("workOrderMaterialList", "delete", "生产工单用料清单删除消息"),
    WORK_ORDER_MATERIAL_LIST_STATUS_CHANGE_MESSAGE("workOrderMaterialList", "statusChange", "生产工单用料清单状态变更消息"),

    /**
     * 包装工单相关事件
     */
    PACKAGE_ORDER_STATUS_CHANGE_MESSAGE("packageOrder", "statusChange", "包装工单状态改变消息"),
    PACKAGE_ORDER_UPDATE_MESSAGE("packageOrder", "update", "包装工单编辑消息"),
    PACKAGE_ORDER_ADD_MESSAGE("packageOrder", "add", "包装工单新增消息"),
    PACKAGE_ORDER_DELETE_MESSAGE("packageOrder", "delete", "包装工单删除消息"),
    PACKAGE_ORDER_RETURN_MESSAGE("packageOrder", "return", "包装工单回退消息"),

    /**
     * 产能相关事件
     */
    CAPACITY_ADD_MESSAGE("capacity", "add", "产能新增消息"),
    CAPACITY_UPDATE_MESSAGE("capacity", "update", "产能编辑消息"),
    CAPACITY_DELETE_MESSAGE("capacity", "delete", "产能删除消息"),
    CAPACITY_UTILIZATION_ANYLYSE_MESSAGE("capacity", "analyse", "产能利用率消息"),
    CAPACITY_STANDARD_ADD_MESSAGE("capacity", "addStandard", "产能标准新增消息"),
    CAPACITY_STANDARD_UPDATE_MESSAGE("capacity", "updateStandard", "产能标准修改消息"),

    WORK_ORDER_PRODUCT_MESSAGE("product", "statistics", "生产状态总览信息"),
    /**
     * 库存相关事件
     */
    STOCK_INVENTORY_INPUT_CHANGE_MESSAGE("stockInput", "statusChange", "入库状态事件改变信息"),
    STOCK_INVENTORY_OUTPUT_CHANGE_MESSAGE("stockOutput", "statusChange", "出库状态改变信息"),
    STOCK_INVENTORY_SEND_CHANGE_MESSAGE("stockDelivery", "statusChange", "发货状态事件改变信息"),
    STOCK_INVENTORY_TRANSFER_CHANGE_MESSAGE("stockTransfer", "statusChange", "物料调拨状态事件改变信息"),
    STOCK_INVENTORY_INIT_MESSAGE("stockInit", "init", "库存初始化数据信息"),
    STOCK_INVENTORY_CHECK_MESSAGE("stockCheck", "check", "库存盘点数据信息"),
    STOCK_LOCATION_STATUS_CHANGE_MESSAGE("stockLocation", "statusChange", "库位定义状态改变信息"),

    STOCK_INVENTORY_SEND_CHANGE_TO_DOC_MESSAGE("stockDelivery", "statusChangeToDoc", "发货状态事件改变信息"),

    /**
     * 告警相关事件
     */
    ALARM_ORDER_ADD_MESSAGE("alarm", "add", "告警新增信息"),
    ALARM_ORDER_UPDATE_MESSAGE("alarm", "update", "告警更新信息"),
    METRICS_GROUP_MESSAGE("metricsGroup", "threshold", "指标组超出阈值"),
    METRICS_DEVICE_MESSAGE("metricsDevice", "threshold", "设备指标超出阈值"),

    WORK_ORDER_UNQUALIFIED_ADD_LIST_MESSAGE("workOrderUnqualified", "addList", "工单不良品数据新增消息"),

    /**
     * 采购订单相关事件
     */
    PURCHASE_ORDER_NODE_UPDATE_MESSAGE("purchaseOrderNodeConfig", "update", "采购订单改变消息"),
    SALE_ORDER_NODE_REPORT_MESSAGE("saleOrderNodeReport", "add", "销售订单节点上报消息"),
    WORK_ORDER_UPDATE_NODE_MESSAGE("workOrderNodeConfig", "update", "生产工单节点单据上报消息"),

    /**
     * 发货单相关事件
     */
    STOCK_DELIVERY_STATUS_CHANGE_MESSAGE("stockDelivery", "statusChange", "发货单状态改变消息"),
    STOCK_DELIVERY_UPDATE_MESSAGE("stockDelivery", "update", "发货单编辑消息"),
    STOCK_DELIVERY_ADD_MESSAGE("stockDelivery", "add", "发货单新增消息"),
    STOCK_DELIVERY_DELETE_MESSAGE("stockDelivery", "delete", "发货单删除消息"),

    /**
     * 报工事件
     */
    REPORT_LINE_ADD("reportLine", "add", "新增报工事件"),
    REPORT_LINE_UPDATE("reportLine", "update", "更新报工事件"),

    /**
     * 质量管理相关
     */
    DEFECT_DEFINE_ADD_MESSAGE("defectDefine", "add", "新增不良定义消息"),
    DEFECT_DEFINE_UPDATE_MESSAGE("defectDefine", "update", "修改不良定义消息"),
    DEFECT_DEFINE_DELETE_MESSAGE("defectDefine", "delete", "删除不良定义消息"),
    DEFECT_SCHEME_ADD_MESSAGE("defectScheme", "add", "新增质检方案消息"),
    DEFECT_SCHEME_UPDATE_MESSAGE("defectScheme", "update", "修改质检方案消息"),
    DEFECT_SCHEME_DELETE_MESSAGE("defectScheme", "delete", "修改质检方案消息"),
    /**
     * 质检返工
     */
    MAINTAIN_DEFINE_ADD_MESSAGE("maintainDefine", "add", "新增质检返工定义消息"),
    MAINTAIN_DEFINE_UPDATE_MESSAGE("maintainDefine", "update", "修改质检返工定义消息"),
    MAINTAIN_DEFINE_DELETE_MESSAGE("maintainDefine", "delete", "删除质检返工定义消息"),
    MAINTAIN_SCHEME_ADD_MESSAGE("maintainScheme", "add", "新增质检返工方案消息"),
    MAINTAIN_SCHEME_UPDATE_MESSAGE("maintainScheme", "update", "修改质检返工方案消息"),
    MAINTAIN_SCHEME_DELETE_MESSAGE("maintainScheme", "delete", "修改质检返工方案消息"),

    MAINTAIN_FEED_UPDATE_MESSAGE("maintainFeed", "update", "新增质检返工方案消息"),

    /**
     * 生产设备
     */
    PRODUCTION_EQUIPMENT_ADD_MESSAGE("productionEquipment", "add", "新增生产设备消息"),
    PRODUCTION_EQUIPMENT_UPDATE_MESSAGE("productionEquipment", "update", "修改生产设备消息"),
    PRODUCTION_EQUIPMENT_DELETE_MESSAGE("productionEquipment", "delete", "删除生产设备消息"),
    PRODUCTION_EQUIPMENT_STATUS_CHANGE_MESSAGE("productionEquipment", "statusChange", "设备状态变更消息"),
    /**
     * 采集设备
     */
    SENSOR_EQUIPMENT_ADD_MESSAGE("sensorEquipment", "add", "新增采集设备消息"),
    SENSOR_EQUIPMENT_UPDATE_MESSAGE("sensorEquipment", "update", "修改采集设备消息"),
    SENSOR_EQUIPMENT_DELETE_MESSAGE("sensorEquipment", "delete", "删除采集设备消息"),

    /**
     * 终端设备
     */
    TERMINAL_EQUIPMENT_ADD_MESSAGE("terminalEquipment", "add", "新增终端设备消息"),
    TERMINAL_EQUIPMENT_UPDATE_MESSAGE("terminalEquipment", "update", "修改终端设备消息"),
    TERMINAL_EQUIPMENT_DELETE_MESSAGE("terminalEquipment", "delete", "删除终端设备消息"),

    /**
     * 视频设备
     */
    VIDEO_EQUIPMENT_ADD_MESSAGE("videoEquipment", "add", "新增视频设备消息"),
    VIDEO_EQUIPMENT_UPDATE_MESSAGE("videoEquipment", "update", "修改视频设备消息"),
    VIDEO_EQUIPMENT_DELETE_MESSAGE("videoEquipment", "delete", "删除视频设备消息"),

    /**
     * 设备模型
     */
    EQUIPMENT_MODEL_ADD_MESSAGE("equipmentModel", "add", "新增设备模型消息"),
    EQUIPMENT_MODEL_UPDATE_MESSAGE("equipmentModel", "update", "修改设备模型消息"),
    EQUIPMENT_MODEL_DELETE_MESSAGE("equipmentModel", "delete", "删除设备模型消息"),

    /**
     * 设备维修
     */
    EQUIPMENT_MAINTAIN_ADD_MESSAGE("equipmentMaintain", "add", "新增设备维修消息"),
    EQUIPMENT_MAINTAIN_UPDATE_MESSAGE("equipmentMaintain", "update", "修改设备维修消息"),
    EQUIPMENT_MAINTAIN_DELETE_MESSAGE("equipmentMaintain", "delete", "删除设备维修消息"),

    /**
     * 产品检测
     */
    INSPECTION_ITEM_ADD_MESSAGE("InspectionItem", "add", "新增产品检测项目消息"),
    INSPECTION_ITEM_UPDATE_MESSAGE("InspectionItem", "update", "修改产品检测项目消息"),
    INSPECTION_ITEM_DELETE_MESSAGE("InspectionItem", "delete", "删除产品检测项目消息"),

    INSPECTION_ITEM_GROUP_ADD_MESSAGE("InspectionItemGroup", "add", "新增产品检测项目消息"),
    INSPECTION_ITEM_GROUP_UPDATE_MESSAGE("InspectionItemGroup", "update", "修改产品检测项目消息"),
    INSPECTION_ITEM_GROUP_DELETE_MESSAGE("InspectionItemGroup", "delete", "删除产品检测项目消息"),

    INSPECTION_SCHEME_ADD_MESSAGE("InspectionScheme", "add", "新增产品检测方案消息"),
    INSPECTION_SCHEME_UPDATE_MESSAGE("InspectionScheme", "update", "修改产品检测方案消息"),
    INSPECTION_SCHEME_DELETE_MESSAGE("InspectionScheme", "delete", "删除产品检测方案消息"),

    INSPECTION_SHEET_ADD_MESSAGE("InspectionSheet", "add", "新增产品送检单消息"),
    INSPECTION_SHEET_UPDATE_MESSAGE("InspectionSheet", "update", "修改送检单消息"),
    INSPECTION_SHEET_DELETE_MESSAGE("InspectionSheet", "delete", "删除送检单消息"),

    INSPECTION_RESULT_UPDATE_MESSAGE("InspectionResult", "update", "修改产品检测报告消息"),
    INSPECTION_RESULT_DELETE_MESSAGE("InspectionResult", "delete", "删除产品检测报告消息"),


    INSPECTION_REPORT_UPDATE_MESSAGE("InspectionReport", "update", "修改产品检测结果消息"),

    /**
     * 视频设备
     */
    VIDEO_UPDATE_NAME("video", "updateName", "视频设备编辑修改名称消息"),
    VIDEO_DELETE("video", "delete", "视频设备删除消息"),

    /**
     * 采购订单
     */
    PURCHASE_ADD_MESSAGE("purchase", "add", "新增采购订单消息"),
    PURCHASE_UPDATE_MESSAGE("purchase", "update", "修改采购订单消息"),
    PURCHASE_DELETE_MESSAGE("purchase", "delete", "删除采购订单消息"),
    PURCHASE_STATUS_CHANGE_MESSAGE("purchase", "statusChange", "采购订单状态变更消息"),
    PURCHASE_APPROVAL_STATUS_CHANGE_MESSAGE("purchase", "approvalStatusChange", "采购订单审批消息"),

    /**
     * 批次
     */
    BARCODE_ADD_MESSAGE("barCode", "add", "新增批次消息"),
    BARCODE_UPDATE_MESSAGE("barCode", "update", "修改批次消息"),
    BARCODE_DELETE_MESSAGE("barCode", "delete", "删除批次消息"),
    WORK_ORDER_BARCODE_DELETE_MESSAGE("workOrder.barCode", "add", "新增生产工单批次消息"),
    PRODUCT_ORDER_BARCODE_DELETE_MESSAGE("productOrder.barCode", "add", "新增生产订单批次消息"),

    /**
     * 流水码
     */
    WORK_ORDER_FLOW_CODE_ADD_MESSAGE("workOrder.flowCode", "add", "新增生产工单流水码消息"),
    PRODUCT_ORDER_FLOW_CODE_ADD_MESSAGE("productOrder.flowCode", "add", "新增生产订单流水码消息"),

    /**
     * 标签
     */
    LABEL_ADD_OR_UPDATE("label", "addOrUpdate", "新增或更新标签信息"),

    /**
     * 出入库单
     */
    STOCK_IN_OUT_ADD_MESSAGE("stockInAndOut", "add", "新增出入库单消息"),
    STOCK_IN_OUT_UPDATE_MESSAGE("stockInAndOut", "update", "修改出入库单消息"),
    STOCK_IN_OUT_DELETE_MESSAGE("stockInAndOut", "delete", "删除出入库单消息"),
    STOCK_IN_OUT_APPROVAL_STATUS_CHANGE_MESSAGE("stockInAndOut", "approvalStatusChange", "出入库单审批消息"),

    /**
     * 仓库
     */
    WAREHOUSE_TYPE_ADD_MESSAGE("warehouseType", "add", "仓库类型新增消息"),
    STOCK_WAREHOUSE_ADD_MESSAGE("warehouse", "add", "新增仓库消息"),
    STOCK_WAREHOUSE_UPDATE_MESSAGE("warehouse", "update", "修改仓库消息"),
    STOCK_WAREHOUSE_DELETE_MESSAGE("warehouse", "delete", "删除仓库消息"),
    STOCK_WAREHOUSE_APPROVAL_STATUS_CHANGE_MESSAGE("warehouse", "approvalStatusChange", "仓库审批消息"),

    /**
     * 调拨单
     */
    STOCK_TRANSFER_ADD_MESSAGE("stockTransfer", "add", "新增调拨单消息"),
    STOCK_TRANSFER_UPDATE_MESSAGE("stockTransfer", "update", "修改调拨单消息"),
    STOCK_TRANSFER_DELETE_MESSAGE("stockTransfer", "delete", "删除调拨单消息"),
    STOCK_TRANSFER_APPROVAL_STATUS_CHANGE_MESSAGE("stockTransfer", "approvalStatusChange", "调拨单审批消息"),

    /**
     * 物料库存
     */
    STOCK_INVENTORY_DETAIL_ADD_MESSAGE("stockInventoryDetail", "add", "新增物料库存消息"),
    STOCK_INVENTORY_DETAIL_UPDATE_MESSAGE("stockInventoryDetail", "update", "修改物料库存消息"),
    STOCK_INVENTORY_DETAIL_DELETE_MESSAGE("stockInventoryDetail", "delete", "删除物料库存消息"),

    /**
     * 工作中心
     */
    WORK_CENTER_ADD_MESSAGE("workCenter", "add", "新增工作中心消息"),
    WORK_CENTER_UPDATE_MESSAGE("workCenter", "update", "修改工作中心消息"),
    WORK_CENTER_DELETE_MESSAGE("workCenter", "delete", "删除工作中心消息"),

    /**
     * 盘点记录
     */
    STOCK_CHECK_ADD_MESSAGE("stockCheck", "add", "新增盘点记录消息"),

    /**
     * 库位定义
     */
    STOCK_LOCATION_ADD_MESSAGE("stockLocation", "add", "新增库位定义消息"),
    STOCK_LOCATION_UPDATE_MESSAGE("stockLocation", "update", "修改库位定义消息"),
    STOCK_LOCATION_DELETE_MESSAGE("stockLocation", "delete", "删除库位定义消息"),
    STOCK_LOCATION_APPROVAL_STATUS_CHANGE_MESSAGE("stockLocation", "approvalStatusChange", "库位定义审批消息"),

    /**
     * 公司
     */
    COMPANY_UPDATE_MESSAGE("company", "update", "修改公司消息"),

    /**
     * 工厂模型
     */
    MODEL_ADD_MESSAGE("model", "add", "新增模型消息"),
    MODEL_UPDATE_MESSAGE("model", "update", "修改模型消息"),
    MODEL_DELETE_MESSAGE("model", "delete", "删除模型消息"),

    /**
     * 厂区
     */
    AREA_ADD_MESSAGE("area", "add", "新增厂区消息"),
    AREA_UPDATE_MESSAGE("area", "update", "修改厂区消息"),
    AREA_DELETE_MESSAGE("area", "delete", "删除厂区消息"),

    /**
     * 产线
     */
    PRODUCTION_LINE_ADD_MESSAGE("productionLine", "add", "新增产线消息"),
    PRODUCTION_LINE_UPDATE_MESSAGE("productionLine", "update", "修改产线消息"),
    PRODUCTION_LINE_DELETE_MESSAGE("productionLine", "delete", "删除产线消息"),

    /**
     * 工位
     */
    FACILITIES_ADD_MESSAGE("facilities", "add", "新增工位消息"),
    FACILITIES_UPDATE_MESSAGE("facilities", "update", "修改工位消息"),
    FACILITIES_DELETE_MESSAGE("facilities", "delete", "删除工位消息"),

    /**
     * 采购需求
     */
    PURCHASE_REQUEST_ADD_MESSAGE("purchaseRequest", "add", "新增采购需求单消息"),
    PURCHASE_REQUEST_UPDATE_MESSAGE("purchaseRequest", "update", "修改采购需求单消息"),
    PURCHASE_REQUEST_DELETE_MESSAGE("purchaseRequest", "delete", "删除采购需求单消息"),
    PURCHASE_REQUEST_STATUS_CHANGE_MESSAGE("purchaseRequest", "statusChange", "采购需求单状态变更消息"),
    PURCHASE_REQUEST_APPROVAL_STATUS_CHANGE_MESSAGE("purchaseRequest", "approvalStatusChange", "采购需求单审批消息"),

    /**
     * 采购收货单
     */
    PURCHASE_RECEIPT_ADD_MESSAGE("purchaseReceipt", "add", "新增采购收货单消息"),
    PURCHASE_RECEIPT_UPDATE_MESSAGE("purchaseReceipt", "update", "修改采购收货单消息"),
    PURCHASE_RECEIPT_DELETE_MESSAGE("purchaseReceipt", "delete", "删除采购收货单消息"),
    PURCHASE_RECEIPT_STATUS_CHANGE_MESSAGE("purchaseReceipt", "statusChange", "采购收货单状态变更消息"),
    PURCHASE_RECEIPT_APPROVAL_STATUS_CHANGE_MESSAGE("purchaseReceipt", "approvalStatusChange", "采购收货单审批消息"),

    /**
     * 岗位
     */
    POST_ADD_MESSAGE("post", "add", "新增岗位消息"),
    POST_UPDATE_MESSAGE("post", "update", "修改岗位消息"),
    POST_DELETE_MESSAGE("post", "delete", "删除岗位消息"),

    /**
     * 工艺
     */
    CRAFT_ADD_MESSAGE("craft", "add", "新增工艺消息"),
    CRAFT_UPDATE_MESSAGE("craft", "update", "修改工艺消息"),
    CRAFT_DELETE_MESSAGE("craft", "delete", "删除工艺消息"),
    CRAFT_APPROVAL_STATUS_CHANGE_MESSAGE("craft", "approvalStatusChange", "工艺审批消息"),

    /**
     * 工艺模板
     */
    CRAFT_TEMPLATE_ADD_MESSAGE("craftTemplate", "add", "新增工艺模板消息"),
    CRAFT_TEMPLATE_UPDATE_MESSAGE("craftTemplate", "update", "修改工艺模板消息"),
    CRAFT_TEMPLATE_DELETE_MESSAGE("craftTemplate", "delete", "删除工艺模板消息"),
    CRAFT_TEMPLATE_APPROVAL_STATUS_CHANGE_MESSAGE("craftTemplate", "approvalStatusChange", "工艺模板审批消息"),

    CRAFT_PROCEDURE_ADD_MESSAGE("craftProcedure", "add", "新增工艺工序消息"),
    CRAFT_PROCEDURE_UPDATE_MESSAGE("craftProcedure", "update", "修改工艺工序消息"),
    CRAFT_PROCEDURE_DELETE_MESSAGE("craftProcedure", "delete", "删除工艺工序消息"),

    CRAFT_PROCEDURE_MATERIAL_ADD_MESSAGE("craftProcedureMaterial", "add", "新增工艺工序物料消息"),
    CRAFT_PROCEDURE_MATERIAL_UPDATE_MESSAGE("craftProcedureMaterial", "update", "修改工艺工序物料消息"),
    CRAFT_PROCEDURE_MATERIAL_DELETE_MESSAGE("craftProcedureMaterial", "delete", "删除工艺工序物料消息"),

    /**
     * 工序定义
     */
    PROCEDURE_ADD_MESSAGE("procedure", "add", "新增工序定义消息"),
    PROCEDURE_UPDATE_MESSAGE("procedure", "update", "修改工序定义消息"),
    PROCEDURE_DELETE_MESSAGE("procedure", "delete", "删除工序定义消息"),

    /**
     * 工序检验项定义
     */
    PROCEDURE_INSPECTION_ADD_MESSAGE("procedureInspection", "add", "新增工序检验项定义消息"),
    PROCEDURE_INSPECTION_UPDATE_MESSAGE("procedureInspection", "update", "修改工序检验项定义消息"),
    PROCEDURE_INSPECTION_DELETE_MESSAGE("procedureInspection", "delete", "删除工序检验项定义消息"),

    /**
     * 工艺工序检验项定义
     */
    PROCEDURE_INSPECTION_CONFIG_ADD_MESSAGE("procedureInspectionConfig", "add", "新增工艺工序检验项消息"),
    PROCEDURE_INSPECTION_CONFIG_UPDATE_MESSAGE("procedureInspectionConfig", "update", "修改工艺工序检验项消息"),
    PROCEDURE_INSPECTION_CONFIG_DELETE_MESSAGE("procedureInspectionConfig", "delete", "删除工艺工序检验项消息"),

    /**
     * 基础配置
     */
    DICT_ADD_MESSAGE("dict", "add", "基础配置新增消息"),
    DICT_UPDATE_MESSAGE("dict", "update", "基础配置编辑消息"),
    DICT_DELETE_MESSAGE("dict", "delete", "基础配置删除消息"),

    /**
     * bom
     */
    BOM_ADD_MESSAGE("bom", "add", "新增bom消息"),
    BOM_UPDATE_MESSAGE("bom", "update", "修改bom消息"),
    BOM_DELETE_MESSAGE("bom", "delete", "删除bom消息"),
    BOM_APPROVAL_STATUS_CHANGE_MESSAGE("bom", "approvalStatusChange", "bom审批消息"),

    /**
     * bom模板
     */
    BOM_TEMPLATE_ADD_MESSAGE("bomTemplate", "add", "新增bom模板消息"),
    BOM_TEMPLATE_UPDATE_MESSAGE("bomTemplate", "update", "修改bom模板消息"),
    BOM_TEMPLATE_DELETE_MESSAGE("bomTemplate", "delete", "删除bom模板消息"),
    BOM_TEMPLATE_APPROVAL_STATUS_CHANGE_MESSAGE("bomTemplate", "approvalStatusChange", "bom模板审批消息"),

    /**
     * 权限菜单
     */
    PERMISSION_ADD_MESSAGE("permission", "add", "新增权限菜单消息"),
    PERMISSION_UPDATE_MESSAGE("permission", "update", "修改权限菜单消息"),
    PERMISSION_DELETE_MESSAGE("permission", "delete", "删除权限菜单消息"),

    /**
     * 审批
     */
    APPROVE_UPDATE_MESSAGE("approve", "update", "更新审批配置消息"),

    /**
     * 条款管理
     */
    TERM_ADD_MESSAGE("term", "add", "新增条款消息"),
    TERM_UPDATE_MESSAGE("term", "update", "修改条款消息"),
    TERM_DELETE_MESSAGE("term", "delete", "删除条款消息"),

    /**
     * 采购退料
     */
    PURCHASE_RETURN_APPLICATION_ADD_MESSAGE("purchaseReturnApplication", "add", "新增采购退料单消息"),
    PURCHASE_RETURN_APPLICATION_UPDATE_MESSAGE("purchaseReturnApplication", "update", "修改采购退料单消息"),
    PURCHASE_RETURN_APPLICATION_DELETE_MESSAGE("purchaseReturnApplication", "delete", "删除采购退料单消息"),
    PURCHASE_RETURN_APPLICATION_STATUS_CHANGE_MESSAGE("purchaseReturnApplication", "statusChange", "采购退料单状态变更消息"),
    PURCHASE_RETURN_APPLICATION_APPROVAL_STATUS_CHANGE_MESSAGE("purchaseReturnApplication", "approvalStatusChange", "采购退料单审批消息"),

    /**
     * 车间
     */
    GRID_ADD_MESSAGE("grid", "add", "新增车间消息"),
    GRID_UPDATE_MESSAGE("grid", "update", "修改车间消息"),
    GRID_DELETE_MESSAGE("grid", "delete", "删除车间消息"),

    /**
     * 班组
     */
    TEAM_ADD_MESSAGE("team", "add", "新增班组消息"),
    TEAM_UPDATE_MESSAGE("team", "update", "修改班组消息"),
    TEAM_DELETE_MESSAGE("team", "delete", "删除班组消息"),

    ;
    private String modelCode;

    private String typeCode;

    private String des;

    KafkaMessageTypeEnum(String modelCode, String typeCode, String des) {
        this.modelCode = modelCode;
        this.typeCode = typeCode;
        this.des = des;
    }

    @Override
    public String getModelCode() {
        return modelCode;
    }

    @Override
    public String getTypeCode() {
        return typeCode;
    }

    @Override
    public String getDes() {
        return des;
    }
}
