package com.yelink.dfs.constant.product;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * @Description: 工序状态枚举
 * @Author: zhuangwq
 * @Date: 2021/05/24
 */
public enum ProcedureStateEnum {

    /**
     * 工序状态
     * 1-创建 2-已审核 3-已批准 4-生效
     */
    CREATE(1, "创建"),
//    REVIEWED(2, "已审核"),
//    APPROVED(3, "已批准"),
    RELEASED(4, "启用"),
    STOP_USING(5, "停用"),
    //ABANDONED(6, "废弃")
    ;

    @EnumValue
    private int code;
    private String name;

    ProcedureStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProcedureStateEnum stateEnum : ProcedureStateEnum.values()) {
            if (stateEnum.code == code) {
                return stateEnum.name;
            }
        }
        return null;
    }
    
    public static Integer getCodeByName(String name) {
        for (ProcedureStateEnum stateEnum : ProcedureStateEnum.values()) {
            if (stateEnum.name.equals(name)) {
                return stateEnum.code;
            }
        }
        return null;
    }

}
