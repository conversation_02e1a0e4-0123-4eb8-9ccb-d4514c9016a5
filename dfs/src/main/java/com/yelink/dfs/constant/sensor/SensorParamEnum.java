package com.yelink.dfs.constant.sensor;


/**
 * @description:
 * @author: z<PERSON><PERSON>
 * @create: 2021-02-21 09:54
 **/
public enum SensorParamEnum {

    /**
     * 设施编码及描述
     * 生产设施类型
     */
    COUNTER_COUNT("计数", "count", "个"),
    COUNTER_STATE("开关状态", "state", ""),

    WRISTBAND_UA("A相电压", "UA", "V"),
    WRISTBAND_UB("B相电压", "UB", "V"),
    WRISTBAND_UC("C相电压", "UC", "V"),
    WRISTBAND_UAB("AB线电压", "Uab", "V"),
    WRISTBAND_UBC("BC线电压", "Ubc", "V"),
    WRISTBAND_UAC("CA线电压", "Uac", "V"),
    WRISTBAND_IA("A相电流", "IA", "A"),
    WRISTBAND_IB("B相电流", "IB", "A"),
    WRISTBAND_IC("C相电流", "IC", "A"),
    WRISTBAND_PT("总有功功率", "PT", "KW"),
    WRISTBAND_QT("总无功功率", "QT", "Kvar"),
    WRISTBAND_COS("平均功率因数", "COS", ""),
    WRISTBAND_EP("正向有功电度总", "Ep", "kWH"),
    WRISTBAND_EPT("有功电能总和", "EPT", "kWH"),
    WRISTBAND_VPER("不平衡电压百分比", "VPer", ""),
    WRISTBAND_LPER("不平衡电流百分比", "LPer", ""),
    WRISTBAND_CHAT("A相总电流谐波畸变率", "CHAT", "%"),
    WRISTBAND_CHBT("B相总电流谐波畸变率", "CHBT", "%"),
    WRISTBAND_CHCT("C相总电流谐波畸变率", "CHCT", "%"),
    WRISTBAND_VHAT("A相总电压谐波畸变率", "VHAT", "%"),
    WRISTBAND_VHBT("B相总电压谐波畸变率", "VHBT", "%"),
    WRISTBAND_VHCT("C相总电压谐波畸变率", "VHCT", "%"),
    WRISTBAND_ENOW("当前有功实时需量", "Enow", "KW"),
    WRISTBAND_DMD("当月正向有功最大需量", "DMD", "KW"),
    WRISTBAND_DMDT("当月正向有功最大需量发生时间", "DMDT", "ms"),
    WRISTBAND_EQ("无功电能总和", "Eq", "KWH"),
    WRISTBAND_EN("反向有功电度总", "En", "KWH"),
    WRISTBAND_PTA("A相有功功率", "Pta", "KW"),
    WRISTBAND_PTB("B相有功功率", "Ptb", "KW"),
    WRISTBAND_PTC("C相有功功率", "Ptc", "KW"),
    WRISTBAND_STATUS("设备状态", "Status", ""),
    SWITCHING_MACHINE_INFORMATION("开关机信息", "switchingMachineInformation", ""),
    MACHINE_SWITCHING_INFORMATION("本机开关机信息", "machineSwitchingInformation", ""),
    EQUIPMENT_CURRENT_BROKEN_STATE("设备电流通断状态", "equipmentCurrentBrokenState", ""),

    INNER_TEMPERATURE("温度", "temperature", "℃"),

    INNER_HUMITURE_TEMPERATURE("温度", "temperature", "℃"),
    INNER_HUMITURE_HUMIDITY("湿度", "humidity", "%"),
    /*########################扬子江设备##########################*/

    DEVICE_STATE("设备状态", "deviceState", ""),
    RUNNING_STATE("设备运行", "runningState", ""),
    PAUSE_STATE("设备暂停", "pauseState", ""),
    STOP_STATE("设备停止", "stopState", ""),


    //洗瓶机
    BOTTLE_WASHER_SPEED("洗瓶机速度", "bottleWasherSpeed", ""),
    CIRCULATING_WATER_PRESSURE("循环水压力", "circulatingWaterPressure", ""),
    INJECTION_WATER_PRESSURE("注射用水压力", "injectionWaterPressure", ""),
    COMPRESSED_AIR_PRESSURE("压缩空气压力", "compressedAirPressure", ""),
    FREEZING_WATER_PRESSURE("冷冻水压力", "freezingWaterPressure", ""),
    COOLING_WATER_PRESSURE("冷却水压力", "coolingWaterPressure", ""),
    CIRCULATING_WATER_TEMPERATURE("循环水温度", "circulatingWaterTemperature", ""),
    BOTTLE_WASHER_PRODUCTION_NUMBER("洗瓶机生产数量", "bottleWasherProductionNumber", ""),

    //洗瓶机ACX160
    ACX_BOTTLE_WASHER_SPEED("生产速度", "acxBottleWasherSpeed", ""),
    ACX_INJECTION_WATER_PRESSURE("注射水清洗压力", "acxInjectionWaterPressure", ""),
    CLEAN_AIR_BLOWING_PRESSURE("洁净空气气吹压力", "cleanAirBlowingPressure", ""),
    ACX_CIRCULATING_WATER_PRESSURE("循环水清洗压力", "acxCirculatingWaterPressure", ""),
    INJECTION_WATER_TEMPERATURE("注射水温度", "injectionWaterTemperature", ""),

    //灌装机
    DIFFERENTIAL_PRESSURE_A("A级层流压差", "differentialPressureA", ""),
    WIND_SPEED_A("A级层流风速", "windSpeedA", ""),
    SAMPLING_POINT_FLOW("采样点流量", "samplingPointFlow", ""),
    speed("设备速度", "speed", ""),
    //该指标实际为生产总量万以下的数量
    INTO_BOTTLE_NUMBER("灌装进瓶数量", "intoBottleNumber", ""),
    //该指标获取的是生产总量万以上的数值，原指标名为出瓶数量
    OUT_BOTTLE_NUMBER("生产产量", "outBottleNumber", ""),
    UNQUALIFIED_NUMBER("不合格品数量", "unqualifiedNumber", ""),
    ONLINE_PARTICLE_NUMBER("在线粒子数量", "onlineParticleNumber", ""),
    ABANDONED_NUMBER("剔废数量", "abandonedNumber", ""),

    //烘箱
    COOLING_DIFFERENTIAL_PRESSURE("冷却段对洗烘压差", "coolingDifferentialPressure", ""),
    PREHEATING_TEMPERATURE("预热段温度", "preheatingTemperature", ""),
    HEATING_TEMPERATURE("加热段温度", "heatingTemperature", ""),
    HEATING_DIFFERENTIAL_PRESSURE("加热段对洗烘压差", "heatingDifferentialPressure", ""),
    FILLING_DIFFERENTIAL_PRESSURE("灌装间对洗烘间压差", "fillingDifferentialPressure", ""),
    COOLING_SECTION_TEMPERATURE("冷却段温度", "coolingSectionTemperature", ""),
    PREHEATING_DIFFERENTIAL_PRESSURE("预热段对洗烘压差", "preheatingDifferentialPressure", ""),
    BELT_SPEED("传送带速度", "beltSpeed", ""),
    HEATING_TEMPERATURE_ONE("加热段温度一", "heatingTemperatureOne", ""),
    HEATING_TEMPERATURE_TWO("加热段温度二", "heatingTemperatureTwo", ""),
    HEATING_TEMPERATURE_THREE("加热段温度三", "heatingTemperatureThree", ""),

    //配液系统
//    HEATING_TIME("升温时间", "heatingTime", ""),
//    COOLING_TEMPERATURE("冷却温度", "coolingTemperature", ""),
    INDUSTRIAL_STEAM_PRESSURE("工业蒸汽压力", "industrialSteamPressure", ""),
    //    CHILLED_WATER_PRESSURE("冷冻水压力", "chilledWaterPressure", ""),
//    VACUUM_PRESSURE("真空压力", "vacuumPressure", ""),
//    REAL_TIME_WEIGHT("实时重量", "realTimeWeight", ""),
//    FILTER_DIFFERENTIAL_PRESSURE("称量罩初中高效过滤器压差", "filterDifferentialPressure", ""),
//    WIND_SPEED("风速", "windSpeed", ""),
//    FAN_FREQUENCY("风机频率", "fanFrequency", ""),
//    IMPELLER_MOTOR_CURRENT("搅拌桨电机电流", "impellerMotorCurrent", ""),
    //配液系统新枚举
    RESPIRATOR_STERILIZATION_TEMPERATURE("呼吸器灭菌温度", "respiratorSterilizationTemperature", ""),
    TANK_TEMPERATURE("罐温度", "tankTemperature", ""),
    TANK_LOW_DRAIN_TEMPERATURE("罐低点疏水排放温度", "tankLowDrainTemperature", ""),
    FILTER_LOW_DRAIN_TEMPERATURE("过滤器低点疏水排放温度", "filterLowDrainTemperature", ""),
    INLET_PIPE_PRESSURE("进水管压力", "inletPipePressure", ""),
    TANK_PRESSURE("罐压力", "tankPressure", ""),
    BUFFER_TANK_LEVEL("缓冲罐液位", "bufferTankLevel", ""),
    BUFFER_TANK_DISCHARGE_TEMPERATURE_ONE("缓冲罐排放温度1", "bufferTankDischargeTemperatureOne", ""),
    BUFFER_TANK_DISCHARGE_TEMPERATURE_TWO("缓冲罐排放温度2", "bufferTankDischargeTemperatureTwo", ""),
    SURGE_TANK_DISCHARGE_CONDUCTIVITY("缓冲罐排放电导", "surgeTankDischargeConductivity", ""),
    DEEP_FILTER_PRESSURE("深层过滤器压力", "deepFilterPressure", ""),
    INLET_PRESSURE_OF_CYSTIC_FILTER("囊氏过滤器入口压力", "inletPressureOfCysticFilter", ""),
    OUTLET_PRESSURE_OF_CYSTIC_FILTER("囊氏过滤器出口压力", "outletPressureOfCysticFilter", ""),
    BUFFER_TANK_DISCHARGE_TEMPERATURE_THREE("缓冲罐排放温度3", "bufferTankDischargeTemperatureThree", ""),
    CIP_CONDUCTIVITY("CIP电导", "cipConductivity", ""),

    //水浴灭菌柜
    CHAMBER_PRESSURE_ONE("内室压力1", "chamberPressureOne", ""),
    CHAMBER_PRESSURE_TWO("内室压力2", "chamberPressureTwo", ""),
    STEAM_PRESSURE("蒸汽压力", "steamPressure", ""),
    DOOR_SEALING_PRESSURE("门密封压力", "doorSealingPressure", ""),
    COOL_WATER_PRESSURE("冷却水压力", "coolWaterPressure", ""),
    PURIFIED_WATER_PRESSURE("纯化水压力", "purifiedWaterPressure", ""),
    COLOR_WATER_PRESSURE("色水检测压力", "colorWaterPressure", ""),
    HEAT_EXCHANGE_INLET("换热器入口温度", "heatExchangeInlet", ""),
    HEAT_EXCHANGE_OUT("换热器出口温度", "heatExchangeOut", ""),
    T_ONE_PRODUCT("T1产品温度", "tOneProduct", ""),
    T_TWO_PRODUCT("T2产品温度", "tTwoProduct", ""),
    T_THREE_PRODUCT("T3产品温度", "tThreeProduct", ""),
    T_FOUR_PRODUCT("T4产品温度", "tFourProduct", ""),
    CIRCULAT_PUMP_A_CURRENTT("循环泵A相电流", "circulatPumpACurrentt", ""),
    CIRCULAT_PUMP_B_CURRENTT("循环泵B相电流", "circulatPumpBCurrentt", ""),
    CIRCULAT_PUMP_C_CURRENTT("循环泵C相电流", "circulatPumpCCurrentt", ""),


    //通风灭菌柜
    FILTER_PRESSURE("过滤器压力", "filterPressure", ""),
    CHAMBER_PRESSURE("内室压力回差", "chamberPressure", ""),
    T1("T1", "t1", ""),
    T2("T2", "t2", ""),
    T3("T3", "t3", ""),
    T6("T6", "t6", ""),
    PT4("PT4", "pt4", ""),
    PT5("PT5", "pt5", ""),
    P1("P1", "p1", ""),
    P2("P2", "p2", ""),
    P3("P3", "p3", ""),
    P4("P4", "p4", ""),
    P5("P5", "p5", ""),
    P6("P6", "p6", ""),
    P7("P7", "p7", ""),
    P8("P8", "p8", ""),
    P9("P9", "p9", ""),
    P10("P10", "p10", ""),
    P11("P11", "p11", ""),
    P12("P12", "p12", ""),
    /*INDOOR_AIR_SUPPLY_PRESSURE("室内气源压力", "indoorAirSupplyPressure", ""),
    COIL_PRESSURE("盘管压力", "coilPressure", ""),
    FRONT_DOOR_SEALING_PRESSURE("前门密封压力", "frontDoorSealingPressure", ""),
    BACK_DOOR_SEALING_PRESSURE("后门密封压力", "backDoorSealingPressure", ""),
    MECHANICAL_SEAL_ONE_PRESSURE("机械密封压力1", "mechanicalSealOnePressure", ""),
    MECHANICAL_SEAL_TWO_PRESSURE("机械密封压力2", "mechanicalSealTwoPressure", ""),
    CHAMBER_TEMPERATURE("内室温度", "chamberTemperature", ""),
    FILTER_TEMPERATURE("过滤器温度", "filterTemperature", ""),
    MECHANICAL_SEAL_WATER_TANK("机械密封水箱温度", "mechanicalSealWaterTank", ""),
    CONTROL_TEMPERATURE("控制温度", "controlTemperature", ""),
    ACTIVITY_TEMPERATURE("活动温度", "activityTemperature", ""),
    DRAIN_TANK_TEMPERATURE("排水箱温度", "drainTankTemperature", ""),*/


    //灯检机
    EQUIPMENT_SPEED("设备速度", "equipmentSpeed", ""),
    LAMP_INSPECTION_TOTAL("一次灯检总数", "lampInspectionTotal", ""),
    LAMP_INSPECTION_QUALIFIED_NUMBER("一次灯检合格数", "lampInspectionQualifiedNumber", ""),
    LAMP_INSPECTION_PEND_NUMBER("一次灯检待处理品数", "lampInspectionPendNumber", ""),
    LAMP_INSPECTION_PASS_RATE("一次灯检合格率", "lampInspectionPassRate", ""),
    TOTAL_NUMBER_OF_LEAKSL("捡漏总数", "totalNumberOfLeaks", ""),
    UNQUALIFIED_NUMBER_OF_LEAKS("捡漏不合格数", "unqualifiedNumberOfLeaks", ""),
    QUALIFIED_NUMBER_OF_LEAKS("捡漏合格数", "qualifiedNumberOfLeaks", ""),
    DAMAGE_RATE_OF_LEAKS("捡漏破损率", "damageRateOfLeaks", ""),
    UNQUALIFIED_INSPECTION_NUMBER("不合格品检测工位检测数", "unqualifiedInspectionNumber", ""),
    TV_ONE_SCRAP_NUMBER("TV1剔废数", "tvOneScrapNumber", ""),
    TV_TWO_SCRAP_NUMBER("TV2剔废数", "tvTwoScrapNumber", ""),
    TV_THREE_SCRAP_NUMBER("TV3剔废数", "tvThreeScrapNumber", ""),
    TV_FOUR_SCRAP_NUMBER("TV4剔废数", "tvFourScrapNumber", ""),
    TV_FIVE_SCRAP_NUMBER("TV5剔废数", "tvFiveScrapNumber", ""),
    TV_SIX_SCRAP_NUMBER("TV6剔废数", "tvSixScrapNumber", ""),
    TV_SEVEN_SCRAP_NUMBER("TV7剔废数", "tvSevenScrapNumber", ""),
    TV_EIGHT_SCRAP_NUMBER("TV8剔废数", "tvEightScrapNumber", ""),
    TV_NINE_SCRAP_NUMBER("TV9剔废数", "tvNineScrapNumber", ""),
    BATCH_ID("批次号", "batchId", ""),

    TV_ONE_SCRAP_PERCENT("TV1剔废占比", "tvOneScrapPercent", ""),
    TV_TWO_SCRAP_PERCENT("TV2剔废占比", "tvTwoScrapPercent", ""),
    TV_THREE_SCRAP_PERCENT("TV3剔废占比", "tvThreeScrapPercent", ""),
    TV_FOUR_SCRAP_PERCENT("TV4剔废占比", "tvFourScrapPercent", ""),
    TV_FIVE_SCRAP_PERCENT("TV5剔废占比", "tvFiveScrapPercent", ""),
    TV_SIX_SCRAP_PERCENT("TV6剔废占比", "tvSixScrapPercent", ""),
    TV_SEVEN_SCRAP_PERCENT("TV7剔废占比", "tvSevenScrapPercent", ""),
    TV_EIGHT_SCRAP_PERCENT("TV8剔废占比", "tvEightScrapPercent", ""),
    TV_NINE_SCRAP_PERCENT("TV9剔废占比", "tvNineScrapPercent", ""),
    TOTAL_LOADED_CONTAINERS_NUMBER("容器总装载量", "totalLoadedContainersNumber", ""),
    TOTAL_LOADED_CONTAINERS_PERCENT("容器总装载占比", "totalLoadedContainersPercent", ""),
    REJECT_TRAY_ONE_NUMBER("一号托盘不合格量", "rejectTrayOneNumber", ""),
    REJECT_TRAY_ONE_PERCENT("一号托盘不合格占比", "rejectTrayOnePercent", ""),
    REJECT_TRAY_TWO_NUMBER("二号托盘不合格量", "rejectTrayTwoNumber", ""),
    REJECT_TRAY_TWO_PERCENT("二号托盘不合格占比", "rejectTrayTwoPercent", ""),
    ACCEPTED_TRAY_NUMBER("托盘合格量", "acceptedTrayNumber", ""),
    ACCEPTED_TRAY_PERCENT("托盘合格占比", "acceptedTrayPercent", ""),
    REJECT_CHANNEL_ONE_NUMBER("一号管道不合格量", "rejectChannelOneNumber", ""),
    REJECT_CHANNEL_ONE_PERCENT("一号管道不合格占比", "rejectChannelOnePercent", ""),
    REJECT_CHANNEL_TWO_NUMBER("二号管道不合格量", "rejectChannelTwoNumber", ""),
    REJECT_CHANNEL_TWO_PERCENT("二号管道不合格占比", "rejectChannelTwoPercent", ""),
    LAMP_REJECT_NUMBER("灯检不合格量", "lampRejectNumber", ""),
    LAMP_REJECT_PERCENT("灯检不合格占比", "lampRejectPercent", ""),


    //捡漏机
    SUPPLY_COUNT("供应计数", "supplyCount", ""),
    QUALIFIED_NUMBER("合格品数量", "qualifiedNumber", ""),


    //贴签机
    TOTAL_NUMBER_OF_LABELS("贴标总数", "totalNumberOfLabels", ""),
    QUALIFIED_NUMBER_OF_LABELING("贴标合格数", "qualifiedNumberOfLabeling", ""),
    UNQUALIFIED_NUMBER_OF_LABELING("贴标不合格数", "unqualifiedNumberOfLabeling", ""),
    SYSTEM_SPEED("系统速度", "systemSpeed", ""),

    //泡罩包装机
    HEAT_SEALING_TEMPERATURE("热合温度", "heatSealingTemperature", ""),
    //COMPRESSED_AIR_PRESSURE("压缩空气压力", "compressedAirPressure", ""),
    HEATING_PLATE_TEMPERATURE("加热板温度", "heatingPlateTemperature", ""),
    BLANKING_SPEED("冲裁速度", "blankingSpeed", ""),
    //EQUIPMENT_SPEED("设备速度", "equipmentSpeed", ""),
    QUANTITY_OF_QUALIFIED_PRODUCTS("合格品数量", "quantityOfQualifiedProducts", ""),
    SCRAP_QUANTITY("剔废数量", "scrapQuantity", ""),

    //电子监管码
    TOTAL_PRODUCT_NUM("总产品数", "totalProductNum", ""),
    SYSTEM_COUNT_NUM("系统计数", "systemCountNum", ""),
    MOTOR_SPEED("电机速度", "motorSpeed", ""),

    //配液系统1500
    TIC101("TIC101", "tic101", ""),
    TIC102("TIC102", "tic102", ""),
    TIC103("TIC103", "tic103", ""),
    TIC104("TIC104", "tic104", ""),
    TIC105("TIC105", "tic105", ""),
    TIC106("TIC106", "tic106", ""),
    TIC107("TIC107", "tic107", ""),
    PIC101("PIC101", "pic101", ""),
    PIC102("PIC102", "pic102", ""),
    PIC103("PIC103", "pic103", ""),
    WT101("WT101", "wt101", ""),
    SIC101("SIC101", "sic101", ""),
    PUMP101("PUMP101", "pump101", ""),
    QIC601("QIC601", "qic601", ""),
    PT901("PT901", "pt901", ""),
    PT902("PT902", "pt902", ""),
    QIC908("QIC908", "qic908", ""),
    LT910("LT910", "lt910", ""),
    HV1("HV1", "hv1", ""),
    HV2("HV2", "hv2", ""),
    HV3("HV3", "hv3", ""),
    HV4("HV4", "hv4", ""),

    // 捡漏机

    // 东富龙灌装机
    RECORD_TIME("记录时间", "recordTime", ""),
    NUMBER_SUM("进瓶计数", "numberSum", ""),
    PT004_PARTICLE_ACT_R("0.5UM尘埃粒子", "pt004ParticleActR", ""),
    PT003_PARTICLE_ACT_R("5UM尘埃粒子", "pt003ParticleActR", ""),
    NUMBER_GOOD("出瓶计数", "numberGood", ""),
    PRODUCT_NAME("产品名称", "productName", ""),
    INFEED_BELT("进料网带速度系数", "infeedBelt", ""),
    REJECT_BELT("剔废网带速度系数", "rejectBelt", ""),
    //DIAMETER("灌装泵直径", "diameter", ""),
    OUTFEED_BELT("出料网带速度系数", "outfeedBelt", ""),
    //TURN_TABLE("转盘速度系数", "turnTable", ""),
    PRODUCT_SPEED("生产速度", "productSpeed", ""),
    //DOSE_REVOLUME("柱塞泵回吸距离", "doseRevolume", ""),
    //LIQUID_DENSITY("灌装药液密度", "liquidDensity", ""),
    DOSE_VOLUME("灌装量", "doseVolume", ""),

    // 空调系统（温湿度）
    // 空调机组1JK21
    DEW_POINT_TEMPERATURE121("露点温度121", "dewPointTemperature121", "摄氏度"),
    FRESH_AIR_TEMPERATURE121("新风温度121", "freshAirTemperature121", "摄氏度"),
    AIR_SUPPLY_HUMIDITY121("送风湿度121", "airSupplyHumidity121", "摄氏度"),
    AIR_SUPPLY_TEMPERATURE121("送风温度121", "airSupplyTemperature121", "摄氏度"),
    RETURN_AIR_HUMIDITY121("回风湿度121", "returnAirHumidity121", "摄氏度"),
    RETURN_AIR_TEMPERATURE121("回风温度121", "returnAirTemperature121", "摄氏度"),
    SURFACE_COOLING_TEMPERATURE121("表冷温度121", "surfaceCoolingTemperature121", "摄氏度"),
    // 空调机组1K11
    DEW_POINT_TEMPERATURE11("露点温度11", "dewPointTemperature11", "摄氏度"),
    FRESH_AIR_TEMPERATURE11("新风温度11", "freshAirTemperature11", "摄氏度"),
    AIR_SUPPLY_TEMPERATURE11("送风温度11", "airSupplyTemperature11", "摄氏度"),
    RETURN_AIR_HUMIDITY11("回风湿度11", "returnAirHumidity11", "摄氏度"),
    RETURN_AIR_TEMPERATURE11("回风温度11", "returnAirTemperature11", "摄氏度"),
    // 小容量注射剂1号车间洁净区温湿度
    CARBON_ADJUSTING_ROOM_HUMIDITY("调炭间湿度", "carbonAdjustingRoomHumidity", "摄氏度"),
    CARBON_ADJUSTING_ROOM_TEMPERATURE("调炭间温度", "carbonAdjustingRoomTemperature", "摄氏度"),
    RAW_MATERIALS_ROOM_HUMIDITY("原辅料暂存间湿度", "rawMaterialsRoomHumidity", "摄氏度"),
    RAW_MATERIALS_ROOM_TEMPERATURE("原辅料暂存间温度", "rawMaterialsRoomTemperature", "摄氏度"),
    WEIGHING_ROOM_HUMIDITY("称量间湿度", "weighingRoomHumidity", "摄氏度"),
    WEIGHING_ROOM_TEMPERATURE("称量间温度", "weighingRoomTemperature", "摄氏度"),
    CONFIG_ROOM_TWO_HUMIDITY("配置间二湿度", "configRoomTwoHumidity", "摄氏度"),
    CONFIG_ROOM_TWO_TEMPERATURE("配置间二温度", "configRoomTwoTemperature", "摄氏度"),
    CONFIG_ROOM_ONE_HUMIDITY("配置间一湿度", "configRoomOneHumidity", "摄氏度"),
    CONFIG_ROOM_ONE_TEMPERATURE("配置间一温度", "configRoomOneTemperature", "摄氏度"),
    CANNING_ROOM_ONE_HUMIDITY("罐装间一湿度", "canningRoomOneHumidity", "摄氏度"),
    CANNING_ROOM_ONE_TEMPERATURE("罐装间一温度", "canningRoomOneTemperature", "摄氏度"),
    CANNING_ROOM_TWO_HUMIDITY("罐装间二湿度", "canningRoomTwoHumidity", "摄氏度"),
    CANNING_ROOM_TWO_TEMPERATURE("罐装间二温度", "canningRoomTwoTemperature", "摄氏度"),
    WASHING_DRYING_ROOM_ONE_HUMIDITY("洗烘间一湿度", "washingDryingRoomOneHumidity", "摄氏度"),
    WASHING_DRYING_ROOM_ONE_TEMPERATURE("洗烘间一温度", "washingDryingRoomOneTemperature", "摄氏度"),
    WASHING_DRYING_ROOM_TWO_HUMIDITY("洗烘间二湿度", "washingDryingRoomTwoHumidity", "摄氏度"),
    WASHING_DRYING_ROOM_TWO_TEMPERATURE("洗烘间二湿度", "washingDryingRoomTwoTemperature", "摄氏度"),
    DEHUMIDIFICATION_ROOM_HUMIDITY("除湿间湿度", "dehumidificationRoomHumidity", "摄氏度"),
    DEHUMIDIFICATION_ROOM_TEMPERATURE("除湿间温度", "dehumidificationRoomTemperature", "摄氏度"),
    STERILIZATION_ROOM_HUMIDITY("灭菌后存放间湿度", "sterilizationRoomHumidity", "摄氏度"),
    STERILIZATION_ROOM_TEMPERATURE("灭菌后存放间温度", "sterilizationRoomTemperature", "摄氏度"),
    LABEL_ROOM_HUMIDITY("贴签后暂存间湿度", "labelRoomHumidity", "摄氏度"),
    LABEL_ROOM_TEMPERATURE("贴签后暂存间温度", "labelRoomTemperature", "摄氏度"),
    DEVICE_ROOM_HUMIDITY("设备间湿度", "deviceRoomHumidity", "摄氏度"),
    DEVICE_ROOM_TEMPERATURE("设备间温度", "deviceRoomTemperature", "摄氏度"),
    FINISHED_PRODUCT_HUMIDITY("成品暂存间湿度", "finishedProductHumidity", "摄氏度"),
    FINISHED_PRODUCT_TEMPERATURE("成品暂存间湿度", "finishedProductTemperature", "摄氏度"),
    PRIVATE_ROOM_HUMIDITY("外包间湿度", "privateRoomHumidity", "摄氏度"),
    PRIVATE_ROOM_TEMPERATURE("外包间温度", "privateRoomTemperature", "摄氏度"),


    //装盒
    //EQUIPMENT_SPEED("设备速度", "equipmentSpeed", ""),
    //QUANTITY_OF_QUALIFIED_PRODUCTS("合格品数量", "quantityOfQualifiedProducts", ""),
    //SCRAP_QUANTITY("剔废数量", "scrapQuantity", ""),

    //捆包
    //EQUIPMENT_SPEED("设备速度", "equipmentSpeed", ""),
    //QUANTITY_OF_QUALIFIED_PRODUCTS("合格品数量", "quantityOfQualifiedProducts", ""),
    //SCRAP_QUANTITY("剔废数量", "scrapQuantity", ""),

    //装箱
    //EQUIPMENT_SPEED("设备速度", "equipmentSpeed", ""),
    //QUANTITY_OF_QUALIFIED_PRODUCTS("合格品数量", "quantityOfQualifiedProducts", ""),
    //SCRAP_QUANTITY("剔废数量", "scrapQuantity", ""),
    MACHINE_SPEED("编组机速度", "machineSpeed", ""),
    TOPPING_TIMES("顶料次数", "toppingTimes", ""),
    PUSH_NUMBER("推料次数", "pushNumber", ""),
    ON_DUTY_NUMBER("当班产量", "onDutyNumber", ""),
    STATION_ONE_STEP("工位1步序", "stationOneStep", ""),
    STATION_TWO_STEP("工位2步序", "stationTwoStep", ""),


    /*#####################LCM设备###################*/
    //自动上料机
    PRODUCTION_SPEED("生产速度", "productionSpeed", ""),
    NUMBER("数量", "number", ""),

    //端子清洗机
    //PRODUCTION_SPEED("生产速度", "productionSpeed", ""),
    //NUMBER("数量", "number", ""),
    ALCOHOL_FLOW("酒精流量", "alcoholFlow", ""),
    PLASMA_ENERGY_VALUE("等离子能量值", "plasmaEnergyValue", ""),

    //IC绑定机
    //PRODUCTION_SPEED("生产速度", "productionSpeed", ""),
    //NUMBER("数量", "number", ""),
    ACF_ATTACHMENT_TEMPERATURE("ACF贴附温度", "acfAttachmentTemperature", ""),
    ACF_ATTACHMENT_TIME("ACF贴附时间", "acfAttachmentTime", ""),
    ACF_ATTACHMENT_PRESSURE("ACF贴附压力", "acfAttachmentPressure", ""),
    PRE_PRESSURE_TEMPERATURE("预压温度", "prePressureTemperature", ""),
    PRELOADING_TIME("预压时间", "preloadingTime", ""),
    PRELOADING_PRESSURE("预压压力", "preloadingPressure", ""),
    LOCAL_PRESSURE_TEMPERATURE_ONE("本压时间1", "localPressureTemperatureOne", ""),
    INTRINSIC_PRESSURE_TIME_ONE("本压温度1", "intrinsicPressureTimeOne", ""),
    LOCAL_PRESSURE_ONE("本压压力1", "localPressureOne", ""),
    LOCAL_PRESSURE_TEMPERATURE_TWO("本压温度2", "localPressureTemperatureTwo", ""),
    INTRINSIC_PRESSURE_TIME_TWO("本压时间2", "intrinsicPressureTimeTwo", ""),
    LOCAL_PRESSURE_TWO("本压压力2", "localPressureTwo", ""),
    LOCAL_PRESSURE_TEMPERATURE_THREE("本压温度3", "localPressureTemperatureThree", ""),
    INTRINSIC_PRESSURE_TIME_THREE("本压时间3", "intrinsicPressureTimeThree", ""),
    LOCAL_PRESSURE_THREE("本压压力3", "localPressureThree", ""),


    //FOG热压机
    //PRODUCTION_SPEED("生产速度", "productionSpeed", ""),
    //NUMBER("数量", "number", ""),
    //ACF_ATTACHMENT_TEMPERATURE("ACF贴附温度", "acfAttachmentTemperature", ""),
    //ACF_ATTACHMENT_TIME("ACF贴附时间", "acfAttachmentTime", ""),
    //ACF_ATTACHMENT_PRESSURE("ACF贴附压力", "acfAttachmentPressure", ""),
    //PRE_PRESSURE_TEMPERATURE("预压温度", "prePressureTemperature", ""),
    //PRELOADING_TIME("预压时间", "preloadingTime", ""),
    //PRELOADING_PRESSURE("预压压力", "preloadingPressure", ""),
    //LOCAL_PRESSURE_TEMPERATURE_ONE("本压时间1", "localPressureTemperatureOne", ""),
    //INTRINSIC_PRESSURE_TIME_ONE("本压温度1", "intrinsicPressureTimeOne", ""),
    //LOCAL_PRESSURE_ONE("本压压力1", "localPressureOne", ""),
    //LOCAL_PRESSURE_TEMPERATURE_TWO("本压温度2", "localPressureTemperatureTwo", ""),
    //INTRINSIC_PRESSURE_TIME_TWO("本压时间2", "intrinsicPressureTimeTwo", ""),
    //LOCAL_PRESSURE_TWO("本压压力2", "localPressureTwo", ""),
    //LOCAL_PRESSURE_TEMPERATURE_THREE("本压温度3", "localPressureTemperatureThree", ""),
    //INTRINSIC_PRESSURE_TIME_THREE("本压时间3", "intrinsicPressureTimeThree", ""),
    //LOCAL_PRESSURE_THREE("本压压力3", "localPressureThree", ""),
    LOCAL_PRESSURE_TEMPERATURE_FOUR("本压压力4", "localPressureTemperatureFour", ""),
    INTRINSIC_PRESSURE_TIME_FOUR("本压压力4", "intrinsicPressureTimeFour", ""),
    LOCAL_PRESSURE_FOUR("本压压力4", "localPressureFour", ""),

    //点胶机
    LEFT_UP_PROCESS_TIME("左上加工时间", "leftUpProcessTime", ""),
    LEFT_DOWN_PROCESS_TIME("左下加工时间", "leftDownProcessTime", ""),
    RIGHT_UP_PROCESS_TIME("右上加工时间", "rightUpProcessTime", ""),
    RIGHT_DOWN_PROCESS_TIME("右下加工时间", "rightDownProcessTime", ""),
    LEFT_PROCESS_COUNT("左加工总数", "leftProcessCount", ""),
    RIGHT_PROCESS_COUNT("右加工总数", "rightProcessCount", ""),
    LEFT_STARTUP_STATUS("左启动状态", "leftStartupStatus", ""),
    RIGHT_STARTUP_STATUS("右启动状态", "rightStartupStatus", ""),
    AUTO_STARTUP_STATUS("自动启动状态", "autoStartupStatus", ""),
    STOP_STATUS("停止状态", "stopStatus", ""),


    //晾干机
    //PRODUCTION_SPEED("生产速度", "productionSpeed", ""),

    /*#####################粤深钢设备###################*/

    // 电子秤
    ALLOY_ADDITION_WEIGHT("重量", "alloyAdditionWeight", ""),

    // 平行送料小车
    TROLLEY_BACK_IN_PLACE("小车后退到位", "trolleyBackInPlace", ""),
    TROLLEY_ADVANCE_IN_PLACE("小车前进到位", "trolleyAdvanceInPlace", ""),
    TROLLEY_FORWARD("小车前进开", "trolleyForward", ""),
    TROLLEY_BACKWARD("小车后退开", "trolleyBackward", ""),
    TROLLEY_FEEDING_OPENING("小车送料开", "trolleyFeedingOpening", ""),

    // 钢水测温仪
    MOLTEN_STEEL_TEMPERATURE("钢水温度", "moltenSteelTemperature", "摄氏度"),
    //吹氧喷粉
    OXYGEN_BLOWING_SWITCH("开/关吹氧", "oxygenBlowingSwitch", ""),
    OXYGEN_CONSUMPTION("氧气消耗", "oxygenConsumption", ""),
    OXYGEN_FLOW("氧气流量", "oxygenFlow", "摄氏度"),
    CARBON_INJECTION_ACTION_ONE("喷碳动作1", "carbonInjectionActionOne", ""),
    CARBON_INJECTION_ACTION_TWO("喷碳动作2", "carbonInjectionActionTwo", ""),
    //光谱仪
    ELEMENT_FE("Fe", "elementFe", "%"),
    ELEMENT_C("C", "elementC", "%"),
    ELEMENT_SI("Si", "elementSi", "%"),
    ELEMENT_MN("Mn", "elementMn", "%"),
    ELEMENT_P("P", "elementP", "%"),
    ELEMENT_S("S", "elementS", "%"),
    ELEMENT_CR("Cr", "elementCr", "%"),
    ELEMENT_MO("Mo", "elementMo", "%"),
    ELEMENT_NI("Ni", "elementNi", "%"),
    ELEMENT_AI("Al", "elementAl", "%"),
    ELEMENT_CU("Cu", "elementCu", "%"),
    ELEMENT_TI("Ti", "elementTi", "%"),
    ELEMENT_V("V", "elementV", "%"),
    ELEMENT_NB("Nb", "elementNb", "%"),
    ELEMENT_AS("As", "elementAs", "%"),

    //电炉
    ELECTRIC_QUANTITY("本炉电度", "electricQuantity", ""),
    DOLOMITE_WEIGHT("白云石实际重量", "dolomiteWeight", ""),
    LIME_WEIGHT("石灰实际重量", "limeWeight", ""),
    WEIGHT_INSTRUMENT_VAL("称重仪表实际重量", "weightInstrumentVal", ""),
    FURNACE_COVER_RETURN_WATER_TEMP("炉盖回水温度", "furnaceCoverReturnWaterTemp", ""),
    MOLTEN_STEEL_WEIGHT("钢水重量", "moltenSteelWeight", ""),
    PHASE_CURRENT_A("A相-电流", "phaseCurrentA", ""),
    VOLTAGE_A_B("A-B-电压", "voltageAB", ""),
    VOLTAGE_A_C("A-C-电压", "voltageAC", ""),
    PHASE_CURRENT_B("B相-电流", "phaseCurrentB", ""),
    VOLTAGE_B_C("B-C-电压", "voltageBC", ""),
    PHASE_CURRENT_C("C相-电流", "phaseCurrentC", ""),
    THREE_PHASE_ACTIVE_POWER_P("三相有功功率-P ", "threePhaseActivePowerP", ""),
    THREE_PHASE_ACTIVE_POWER_Q("三相无功功率-Q ", "threePhaseActivePowerQ", ""),
    THREE_PHASE_ACTIVE_POWER_S("三相视在功率-S", "threePhaseActivePowerS", ""),
    //电炉-液压
    HYDRAULIC_SYSTEM_PRESSURE("液压系统压力", "hydraulicSystemPressure", ""),
    //电炉-水冷
    RTN_WATER_TEMP_A_ONE("A相水冷电缆A1回水温度", "rtnWaterTempAOne", ""),

    RTN_WATER_TEMP_B_ONE("B相水冷电缆B1回水温度", "rtnWaterTempBOne", ""),

    RTN_WATER_TEMP_C_ONE("C相水冷电缆C1回水温度", "rtnWaterTempCOne", ""),

    FURNACE_BODY_RETURN_WATER_TEMP("炉体回水温度", "furnaceBodyReturnWaterTemp", ""),

    INLETATER_TEMP_OF_FURNACE_COVER_BODY("炉盖及炉体进水温度", "inletaterTempOfFurnaceCoverBody", ""),

    WATER_INLET_TEMP_OF_EQUIPMENT("设备进水温度", "waterInletTempOfEquipment", ""),

    //wifi空气监测仪
    WIFI_AIR_MONITORING_NOISE("噪音", "noise", "分贝"),

    WIFI_AIR_MONITORING_PM25("粉尘PM2.5", "pm25", ""),

    WIFI_AIR_MONITORING_PM10("粉尘PM10", "pm10", ""),

    WIFI_AIR_MONITORING_TEMP("温度", "temperature", ""),

    WIFI_AIR_MONITORING_HUMIDITY("湿度", "humidity", ""),


    //奥德计数器
    VACANCY_RATE("设备空置率", "vacancyRate", ""),

    //奥德机器手环
    STANDBY_RATE("设备待机率", "standbyRate", ""),

    UTILIZATION_RATE("设备使用率", "utilizationRate", ""),

    //奥德485采集器
    PRODUCTION_TOTAL("生产总数", "productionTotal", ""),

    SET_TOTAL("设定总数", "setTotal", ""),

    SET_OUTPUT("设定产量", "setOutput", ""),

    PRESSURE_DISPLAY("压力显示", "pressureDisplay", ""),

    UPPER_PRESSURE_LIMIT("压力上限", "upperPressureLimit", ""),

    OIL_ROLLING_MOTOR_RUNING_TIME("滚油电机运行时间", "oilRollingMotorRuningTime", ""),

    OIL_ROLL_COUNT("滚油计数", "oilRollCount", ""),

    REFUELING_PRODUCT_SETTING("加油产品设定", "refuelingProductSetting", ""),

    LOWER_GASKET_WARNING_LAMP("下垫片报警灯", "lowerGasketWarningLamp", ""),

    TubeAlarmLamp(" 50管报警灯", "50TubeAlarmLamp", ""),

    SNAP_RING_ALARM_LAMP("卡簧报警灯", "snapRingAlarmLamp", ""),

    DISCHARGE_ALARM_LAMP("卸料报警灯", "dischargeAlarmLamp", ""),

    SET_OF_ALARM_LAMP("按套报警灯", "setOfAlarmLamp", ""),

    PLASTIC_BEARING_ALARM_LAMP("塑料轴承报警灯", "plasticBearingAlarmLamp", ""),

    HIGH_PRESSURE("高压", "highPressure", ""),

    GOOD_PRODUCT("良品", "goodProduct", ""),

    LOW_PRESSURE("低压", "lowPressure", ""),

    CURRENT_PRESSURE_OF_STATION_ONE("一工位当前压力", "currentPressureOfStationOne", ""),

    STATION_RESULT_PRESSURE_ONE("一工位结果压力", "stationResultPressureOne", ""),

    CURRENT_PRESSURE_OF_STATION_TWO("二工位当前压力", "currentPressureOfStationTwo", ""),

    STATION_RESULT_PRESSURE_TWO("二工位结果压力", "stationResultPressureTwo", ""),

    CURRENT_PRESSURE_OF_STATION_THREE("三工位当前压力", "currentPressureOfStationThree", ""),

    STATION_RESULT_PRESSURE_THREE("三工位结果压力", "stationResultPressureThree", ""),

    PRESSURE_MEASUREMENT_TIME("测压时间", "pressureMeasurementTime", ""),

    /**
     * ###############奥维斯###################
     */
    WEIGHT("重量", "weight", ""),
    QR_CODE("条形码", "QRcode", ""),
    WORK_ORDER_NUMBER("工单号", "workOrderNumber", ""),
    /**
     * ###############中山动力###################
     */
    DETECTION_RESULT("检测状态", "detectionResult", ""),
    DETECTION_RESULT_VALUE("检测结果值", "detectionResultValue", ""),
    DETECTION_TARGET("检测目标", "detectionTarget", ""),
    DETECTION_UP("检测上差值", "detectionUp", ""),
    DETECTION_DOWN("检测下差值", "detectionDown", ""),
    /**
     * ####联诚发
     */
    PRESSER("压力", "presser", "");


    private String name;
    private String ename;
    private String unit;


    SensorParamEnum(String name, String ename, String unit) {
        this.name = name;
        this.ename = ename;
        this.unit = unit;
    }

    public String getName() {
        return name;
    }

    public String getEname() {
        return ename;
    }

    public String getUnit() {
        return unit;
    }

    public static SensorParamEnum getByEname(String ename) {
        for (SensorParamEnum sensorParamEnum : SensorParamEnum.values()) {
            if (sensorParamEnum.getEname().equals(ename)) {
                return sensorParamEnum;
            }
        }
        return null;
    }

    public static String getCodeByName(String code) {
        if (code == null) {
            return null;
        }
        for (SensorParamEnum sensorParamEnum : SensorParamEnum.values()) {
            if (sensorParamEnum.name.equals(code)) {
                return sensorParamEnum.ename;
            }
        }
        return null;
    }
}






