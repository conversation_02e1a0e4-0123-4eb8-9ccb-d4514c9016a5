package com.yelink.dfs.constant;

/**
 * @description: 常量类
 * @author: zengfu
 * @create: 2020-12-04 15:22
 **/
public class RedisKeyPrefix {
    /**
     * dfs定时任务前缀
     */
    public static final String DFS_SCHEDULE_TASK = "dfs_schedule_task_";
    public static final String WORK_ORDER_BATCH_EDIT_TASK = "work_order_batch_edit_task_";
    public static final String ALARM_DEFINITION_BATCH_EDIT_TASK = "alarm_definition_batch_edit_task_";
    public static final String WORK_ORDER_BATCH_INVEST_CHECK_TASK = "work_order_batch_invest_check_task_";
    public static final String PACKAGE_ORDER_BATCH_EDIT_TASK = "package_order_batch_edit_task_";

    /**
     * 字典表
     */
    public static final String DICT = "DICT";


    public static final String DICT_BEGIN_DAY = "DICT_BEGIN_DAY:";
    public static final String REPLACE_SCHEME_KEY = "REPLACE_SCHEME_KEY:";

    /**
     * 状态指标中缀
     */
    public static final String _STATE_ = "_STATE:";

    /**
     * 状态更改时间中缀
     */
    public static final String _STATE_CHANGE_ = "_STATE_CHANGE_";

    /**
     * 数量指标中缀
     */
    public static final String _COUNT_ = "_COUNT_";


    /**
     * 指标记录前缀
     */
    public static final String TARGET_METHOD_ = "TARGET_METHOD:";
    /**
     * 指标记录
     */
    public static final String TARGET_METHOD_LIST = "TARGET_METHOD_LIST";

    /**
     * 类型设备列表记录
     */
    public static final String MODEL_DEVCIE_LIST = "MODEL_DEVICE_LIST:";

    /**
     * 类型设备列表记录
     */
    public static final String RECORD_DATE = "RECORD_DATE";

    /**
     * 单个指标记录
     */
    public static final String TARGET_METHOD_ENTITY = "TARGET_METHOD_ENTITY:";

    /**
     * 编号自增前缀
     */
    public static final String CODE_INCR_ = "CODE_INCR_";

    /**
     * 工艺路线的工序自增前缀
     */
    public static final String CRAFT_PROCEDURE_CODE_INCR_ = "CRAFT_PROCEDURE_CODE_INCR_";

    /**
     * 数据库清理前缀
     */
    public static final String TABLE_DATA_CLEAN_ = "TABLE_DATA_CLEAN_";

    /**
     * 产能单位名称
     */
    public static final String WORK_ORDER_CAPACITY_UNIT_NAME_ = "WORK_ORDER_CAPACITY_UNIT_NAME_";

    /**
     * device指标记录
     */
    public static final String TARGET_DEVICE_LIST = "TARGET_DEVICE_LIST";


    /**
     * 设备采集器指标key
     */
    public static final String DEVICE_SENSOR_VALUE = "DEVICE_SENSOR_VALUE:";

    /**
     * 工单指标任务
     */
    public static final String WORK_ORDER_TARGET_TASK = "WORK_ORDER_TARGET_TASK:";

    /**
     * 工单告警任务
     */
    public static final String WORK_ORDER_ALARM_TASK = "WORK_ORDER_ALARM_TASK:";

    /**
     * 产能记录
     */
    public static final String RECORD_OUT_PUT = "RECORD_OUT_PUT";

    /**
     *
     */
    public static final String WORK_ORDER_FAKE_DATA = "WORK_ORDER_FAKE_DATA";


    /**
     * 计算工单排程--物料齐备性的进度的key
     */
    public static final String SCHEDULE_MATERIAL_COMPLETENESS_PROGRESS = "SCHEDULE_MATERIAL_COMPLETENESS_PROGRESS";

    /**
     * 新增工单批次进度的key
     */
    public static final String WORK_ORDER_BATCH_PROGRESS = "WORK_ORDER_BATCH_PROGRESS";
    /**
     * 新增订单批次进度的key
     */
    public static final String PRODUCT_ORDER_BATCH_PROGRESS = "PRODUCT_ORDER_BATCH_PROGRESS";

    /**
     * 新增采购收货批次进度的key
     */
    public static final String PURCHASE_BATCH_PROGRESS = "PURCHASE_BATCH_PROGRESS";
    /**
     * 新增委外收货批次进度的key
     */
    public static final String SUBCONTRACTING_RECEIPT_BATCH_PROGRESS = "SUBCONTRACTING_RECEIPT_BATCH_PROGRESS";

    /**
     * 新增销售退货批次进度的key
     */
    public static final String SALE_RETURN_BATCH_PROGRESS = "SALE_RETURN_BATCH_PROGRESS";

    /**
     * 新增其他批次进度的key
     */
    public static final String OTHER_BATCH_PROGRESS = "OTHER_BATCH_PROGRESS";
    /**
     * 新增生产流水码进度的key
     */
    public static final String PRODUCTION_SEQUENCE_CODE_PROGRESS = "PRODUCTION_SEQUENCE_CODE_PROGRESS";
    /**
     * 新增生产成品码进度的key
     */
    public static final String FINISHED_PRODUCT_CODE_PROGRESS = "FINISHED_PRODUCT_CODE_PROGRESS";
    /**
     * 新增采购单品码进度的key
     */
    public static final String PURCHASE_PRODUCT_CODE_PROGRESS = "PURCHASE_PRODUCT_CODE_PROGRESS";

    /**
     * 新增订单流水码进度的key
     */
    public static final String ORDER_PRODUCT_CODE_PROGRESS = "ORDER_PRODUCT_CODE_PROGRESS";

    /**
     * 包装码新增锁进度的key
     */
    public static final String PACKAGE_CODE_PROGRESS = "PACKAGE_CODE_PROGRESS";

    /**
     * 工单批次新增锁的key
     */
    public static final String WORK_ORDER_BATCH_PROGRESS_LOCK = "WORK_ORDER_BATCH_PROGRESS_LOCK";

    /**
     * 订单批次新增锁的key
     */
    public static final String PRODUCT_ORDER_BATCH_PROGRESS_LOCK = "PRODUCT_ORDER_BATCH_PROGRESS_LOCK";

    /**
     * 采购收货批次新增锁的key
     */
    public static final String PURCHASE_BATCH_PROGRESS_LOCK = "PURCHASE_BATCH_PROGRESS_LOCK";

    /**
     * 委外收货批次新增锁的key
     */
    public static final String SUBCONTRACTING_RECEIPT_BATCH_LOCK = "SUBCONTRACTING_RECEIPT_BATCH_LOCK";

    /**
     * 销售退货批次新增锁的key
     */
    public static final String SALE_RETURN_BATCH_LOCK = "SALE_RETURN_BATCH_LOCK";
    /**
     * 无类型的批次新增锁的key
     */
    public static final String OTHER_BATCH_LOCK = "OTHER_BATCH_LOCK";

    /**
     * 生产流水码新增锁的key
     */
    public static final String PRODUCTION_SEQUENCE_CODE_PROGRESS_LOCK = "PRODUCTION_SEQUENCE_CODE_PROGRESS_LOCK";

    /**
     * 生产成品码新增锁的key
     */
    public static final String FINISHED_PRODUCT_CODE_PROGRESS_LOCK = "FINISHED_PRODUCT_CODE_PROGRESS_LOCK";

    /**
     * 采购单品码新增锁的key
     */
    public static final String PURCHASE_PRODUCT_CODE_PROGRESS_LOCK = "PURCHASE_PRODUCT_CODE_PROGRESS_LOCK";

    /**
     * 订单流水码新增锁的key
     */
    public static final String ORDER_PRODUCT_CODE_PROGRESS_LOCK = "ORDER_PRODUCT_CODE_PROGRESS_LOCK";

    /**
     * 包装码新增锁的key
     */
    public static final String PACKAGE_CODE_PROGRESS_LOCK = "PACKAGE_CODE_PROGRESS_LOCK";

    /**
     * 计算工单排程倒排配置进度的key
     */
    public static final String SCHEDULE_MATERIAL_REVERSE_PROGRESS = "SCHEDULE_MATERIAL_REVERSE_PROGRESS";
    /**
     * 导出日报报表进度的key
     */
    public static final String PRODUCTION_DAILY_PROGRESS = "productionDailyProgress";

    /**
     * 工单排程--物料齐备性检查锁的key
     */
    public static final String SCHEDULE_MATERIAL_LOCK = "SCHEDULE_MATERIAL_LOCK";
    /**
     * 工单排程--物料倒排锁的key
     */
    public static final String SCHEDULE_REVERSE_MATERIAL_LOCK = "SCHEDULE_REVERSE_MATERIAL_LOCK";

    /**
     * iot---productKey的key
     */
    public static final String PRODUCT_KEY = "PRODUCT_KEY";
    /**
     * 条形码不良品-防止重复的key
     */
    public static final String BAR_CODE_KEY = "NUMBER_KEY";


    public static final String ALARM = "ALARM_";

    public static final String WORK_ORDER_STATE = "WORK_ORDER_STATE_";

    /**
     * 计数设备
     */
    public static final String COUNT_SENSOR = "COUNT_SENSOR:";

    /**
     * 插入产能记录加锁
     */
    public static final String INERT_RECORD_OUT_PUT = "INERT_RECORD_OUT_PUT";

    /**
     * 向前遍历到存在日历那天所减的天数
     */
    public static final String CALENDAR_EXIST_DATE_MINUS_NUM = "CALENDAR_EXIST_DATE_MINUS_NUM_";


    /**
     * 基础数据一键导入锁
     */
    public static final String BASE_DATA_BATCH_IMPORT_LOCK = "BASE_DATA_BATCH_IMPORT_LOCK";
    /**
     * 基础数据一键导入进度key
     */
    public static final String BASE_DATA_BATCH_IMPORT_PROGRESS = "BASE_DATA_BATCH_IMPORT_PROGRESS";

    /**
     * bom导入进度的key
     */
    public static final String BOM_IMPORT_PROGRESS = "BOM_IMPORT_PROGRESS";
    /**
     * 物料单位导入进度的key
     */
    public static final String MATERIAL_UNIT_PROGRESS = "MATERIAL_UNIT_PROGRESS";
    /**
     * 物料类型导入进度的key
     */
    public static final String MATERIAL_TYPE_PROGRESS = "MATERIAL_TYPE_PROGRESS";
    /**
     * 物料导入进度的key
     */
    public static final String MATERIAL_IMPORT_PROGRESS = "MATERIAL_IMPORT_PROGRESS";
    /**
     * 客户档案导入进度的key
     */
    public static final String CUSTOMER_IMPORT_PROGRESS = "CUSTOMER_IMPORT_PROGRESS";
    /**
     * 产能导入进度的key
     */
    public static final String CAPACITY_IMPORT_PROGRESS = "CAPACITY_IMPORT_PROGRESS";
    /**
     * 工单每日计划导入进度的key
     */
    public static final String WORK_ORDER_PLAN_IMPORT_PROGRESS = "WORK_ORDER_PLAN_IMPORT_PROGRESS";
    /**
     * 物料单位导入锁的key
     */
    public static final String MATERIAL_UNIT_LOCK = "MATERIAL_UNIT_LOCK";
    /**
     * 物料类型导入锁的key
     */
    public static final String MATERIAL_TYPE_LOCK = "MATERIAL_TYPE_LOCK";
    /**
     * 物料导入锁的key
     */
    public static final String MATERIAL_IMPORT_LOCK = "MATERIAL_IMPORT_LOCK";
    /**
     * 产能导入锁的key
     */
    public static final String CAPACITY_IMPORT_LOCK = "CAPACITY_IMPORT_LOCK";
    /**
     * 工单每日计划导入锁的key
     */
    public static final String WORK_ORDER_PLAN_IMPORT_LOCK = "WORK_ORDER_PLAN_IMPORT_LOCK";

    /**
     * 工序导入进度的key
     */
    public static final String PROCEDURE_IMPORT_PROGRESS = "PROCEDURE_IMPORT_PROGRESS";

    /**
     * 工艺导入进度的key
     */
    public static final String CRAFT_IMPORT_PROGRESS = "CRAFT_IMPORT_PROGRESS";

    /**
     * 物料检验导入锁的key
     */
    public static final String MATERIAL_INSPECT_LOCK = "MATERIAL_INSPECT_LOCK";
    /**
     * 物料检验导入进度的key
     */
    public static final String MATERIAL_INSPECT_IMPORT_PROGRESS = "MATERIAL_INSPECT_IMPORT_PROGRESS";

    /**
     * 工单导入进度的key
     */
    public static final String WORK_ORDER_IMPORT_PROGRESS = "WORK_ORDER_IMPORT_PROGRESS";
    /**
     * BOM导入锁的key
     */
    public static final String BOM_IMPORT_LOCK = "BOM_IMPORT_LOCK";

    /**
     * 制造单元导入进度的key
     */
    public static final String PRODUCTION_LINE_IMPORT_PROGRESS = "PRODUCTION_LINE_IMPORT_PROGRESS";
    /**
     * 制造单元导入锁的key
     */
    public static final String PRODUCTION_LINE_IMPORT_LOCK = "PRODUCTION_LINE_IMPORT_LOCK";

    /**
     * 班组导入进度的key
     */
    public static final String TEAM_IMPORT_PROGRESS = "TEAM_IMPORT_PROGRESS";
    /**
     * 班组导入锁的key
     */
    public static final String TEAM_IMPORT_LOCK = "TEAM_IMPORT_LOCK";

    /**
     * 不良定义导入进度的key
     */
    public static final String DEFECT_DEFINE_IMPORT_PROGRESS = "DEFECT_DEFINE_IMPORT_PROGRESS";
    /**
     * 不良定义导入锁的key
     */
    public static final String DEFECT_DEFINE_IMPORT_LOCK = "DEFECT_DEFINE_IMPORT_LOCK";

    /**
     * 工序导入锁的key
     */
    public static final String PROCEDURE_IMPORT_LOCK = "PROCEDURE_IMPORT_LOCK";

    /**
     * 工单导入锁的key
     */
    public static final String WORK_ORDER_IMPORT_LOCK = "WORK_ORDER_IMPORT_LOCK";

    /**
     * 客户档案导入锁的key
     */
    public static final String CUSTOMER_IMPORT_LOCK = "CUSTOMER_IMPORT_LOCK";

    /**
     * 物料批量更新检验要求的锁key
     */
    public static final String MATERIAL_BATCH_UPDATE_INSPECT_LOCK = "MATERIAL_BATCH_UPDATE_INSPECT_LOCK";

    /**
     * 物料批量更新检验要求的进度key
     */
    public static final String MATERIAL_BATCH_UPDATE_INSPECT_PROGRESS = "MATERIAL_BATCH_UPDATE_INSPECT_PROGRESS";

    /**
     * 条码标识导入锁的key
     */
    public static final String CODE_IMPORT_LOCK = "CODE_IMPORT_LOCK";

    /**
     * 条码标识导入进度的key
     */
    public static final String CODE_IMPORT_PROGRESS = "CODE_IMPORT_PROGRESS";

    /**
     * 计数器计算周期
     */
    public static final String CAL_INTERVAL = "CAL_INTERVAL";

    /**
     * 定时任务每天推送产线oee锁
     */
    public static final String TASK_LOCK_PUSH_OEE_DAY = "TASK_LOCK_PUSH_OEE_DAY";
    /**
     * 定时任务锁
     */
    public static final String TASK_LOCK_PUSH_PRODUCT_STATUS_DATA = "TASK_LOCK_PUSH_PRODUCT_STATUS_DATA";

    /**
     * 生产流水表
     */
    public static final String DFS_PRODUCT_FLOW_CODE = "dfs_product_flow_code";

    /**
     * 生产流水记录表导出
     */
    public static final String DFS_PRODUCT_FLOW_CODE_RECORD_EXCEL_EXPORT = "dfs_product_flow_code_record_excel_export";

    /**
     * 上料记录表导出
     */
    public static final String DFS_FEED_RECORD_EXCEL_EXPORT = "dfs_feed_record_excel_export";

    /**
     * 报工标识
     */
    public static final String REPORT_LINE_MCA = "reportLineMca";

    /**
     * 补全完成时间标识
     */
    public static final String COMPLEMENT_END_TIME = "complementEndTime";

    /**
     * 投入数标识
     */
    public static final String INPUT_LINE = "inputLine";

    /**
     * 不良数标识
     */
    public static final String UNQUALIFIED_LINE = "unqualifiedLine";

    /**
     * 刷新条码时间标识
     */
    public static final String REFRESH_REPORT_TIME = "refreshReportTime";

    /**
     * 工单每日完成数报工标识
     */
    public static final String SAVE_OR_UPDATE_WORK_ORDER_DAY_COUNT = "saveOrUpdateWorkOrderDayCount";

    /**
     * 工单每日完成数报工标识
     */
    public static final String SAVE_OR_UPDATE_WORK_ORDER_DAY_INPUT = "saveOrUpdateWorkOrderDayInput";

    /**
     * 工单每日完成数报工标识
     */
    public static final String SAVE_OR_UPDATE_WORK_ORDER_DAY_UNQUALIFIED = "saveOrUpdateWorkOrderDayUnqualified";

    /**
     * 工序控制配置表
     */
    public static final String DFS_PROCEDURE_CONTROLLER_CONFIG = "dfs_procedure_controller_config";
    /**
     * 炉次号生成同步锁
     */
    public static final String FURNACE_SYC_LOCK = "FURNACE_SYC_LOCK";
    /**
     * 告警升级同步锁
     */
    public static final String ALARM_UPGRADE_LOCK = "ALARM_UPGRADE_LOCK";

    /**
     * 生产设备导入锁
     */
    public static final String DEVICE_IMPORT_LOCK = "DEVICE_IMPORT_LOCK";
    /**
     * 采集设备导入锁
     */
    public static final String SENSOR_IMPORT_LOCK = "SENSOR_IMPORT_LOCK";
    /**
     * 生产设备导入进度
     */
    public static final String DEVICE_IMPORT_PROGRESS = "DEVICE_IMPORT_PROGRESS";
    /**
     * 采集设备导入进度
     */
    public static final String SENSOR_IMPORT_PROGRESS = "SENSOR_IMPORT_PROGRESS";
    public static final String MODEL_LIST = "MODEL_LIST";

    /**
     * 物料缓存
     */
    public static final String MATERIAL_CACHE = "MATERIAL_CACHE";
    public static final String MATERIAL_CACHE_SWITCH = "MATERIAL_CACHE_SWITCH";


    public static final String APP_ID = "APP_ID:";


    /**
     * 设备采集器指标key
     */
    public static String getDeviceTargetKey(Integer deviceId, String targetName) {
        return "DEVICE_TARGET:" + deviceId + "_" + targetName;
    }

    public static String getDeviceTargetQueueKey(Integer deviceId, String targetName) {
        return "DEVICE_TARGET_QUEUE:" + deviceId + "_" + targetName;
    }

    public static String getLastRecordKey(String eui, String targetName) {
        return "LAST_RECORD:" + eui + Constant.UNDERLINE + targetName;
    }

    public static String getAllowReport(String workOrderNumber) {
        return "ALLOW_REPORT:" + workOrderNumber;
    }

    public static String getCalTmpList(String eui, String targetName) {
        return "CAL_TMP_LIST:" + eui + Constant.UNDERLINE + targetName;
    }

    /**
     * 内存计算相关key
     */
    public static final String DEAL_MEMORY_LIST = "DEAL_MEMORY_LIST:";
    public static final String MEMORY_CAL_REPORT_WORK_ORDER = "MEMORY_CAL_REPORT_WORK_ORDER:";
    public static final String MEMORY_CAL_UNQUALIFIED_WORK_ORDER = "MEMORY_CAL_UNQUALIFIED_WORK_ORDER:";
    public static final String MEMORY_CAL_FAC_INPUT_WORK_ORDER = "MEMORY_CAL_FAC_INPUT_WORK_ORDER_";
    public static final String MEMORY_CAL_INPUT_WORK_ORDER = "MEMORY_CAL_INPUT_WORK_ORDER_";
    public static final String MEMORY_CAL_TASK_LOCK_ = "MEMORY_CAL_TASK_LOCK:";
    public static final String MEMORY_CAL_TASK_ITEM = "MEMORY_CAL_TASK_ITEM:";

    public static final String WORK_ORDER_BASIC_UNIT_COUNT_REPORT = "WORK_ORDER_BASIC_UNIT_COUNT_REPORT:";

    /**
     * 组合指标相关key
     */
    public static final String COMBINE_TARGET_ITEM = "COMBINE_TARGET_ITEM_";
    public static final String COMBINE_TARGET_VALUE = "COMBINE_TARGET_VALUE:";
    public static final String COMBINE_TARGET_LOCK = "COMBINE_TARGET_LOCK_";

    public static String getPopDataKey(String key) {
        return "POP_DATA_" + key;
    }

    public static String getDeviceYesterdayLastTargetKey(Integer deviceId, String fieldName, String dateStr) {
        return "DEVICE_YESTERDAY_LAST_TARGET:" + deviceId + "_" + fieldName + "_" + dateStr;
    }

    public static String getDeviceTodayRunningDuration(Integer deviceId, String dateStr) {
        return "DEVICE_RUNNING_DURATION:" + deviceId + "_" + dateStr;
    }

    public static String getDeviceTodaySuspendDuration(Integer deviceId, String dateStr) {
        return "DEVICE_SUSPEND_DURATION:" + deviceId + "_" + dateStr;
    }

    public static String getDeviceTodayStopDuration(Integer deviceId, String dateStr) {
        return "DEVICE_STOP_DURATION:" + deviceId + "_" + dateStr;
    }

    public static String getDeviceTodayMaintainDuration(Integer deviceId, String dateStr) {
        return "DEVICE_MAINTAIN_DURATION:" + deviceId + "_" + dateStr;
    }

    public static String getDeviceTodayStopOnWorkDuration(Integer deviceId, String dateStr) {
        return "DEVICE_STOP_ON_WORK_DURATION:" + deviceId + "_" + dateStr;
    }

    public static String getDeviceStateKey(Integer deviceId) {
        return "DEVICE_STATE:" + deviceId;
    }

    public static String getAlarmDefinitionCodeKey(String alarmDefinitionCode) {
        return "ALARM_DEFINITION:" + alarmDefinitionCode;
    }

    public static String getTargetModelExistKey(String methodName) {
        return "TARGET_MODEL_EXIST:" + methodName;
    }

    public static String getTargetModelEntity(String targetName) {
        return "TARGET_MODEL_ENTITY:" + targetName;
    }

    public static String getUpdateEuiMarkKey(String eui) {
        return "UPDATE_EUI_MARK:" + eui;
    }

    public static final String INSTANCE_TREE = "INSTANCE_TREE:";

    public static String getInstanceTreeKey(String modelType) {
        return INSTANCE_TREE + modelType;
    }

    public static final String TODAY_WORK_TIME = "TODAY_WORK_TIME:";

    public static String getTodayWorkTime(String recordDateStr, Integer instanceId, String modelType) {
        return TODAY_WORK_TIME + recordDateStr + "_" + modelType + "_" + instanceId;
    }

    public static final String UPDATE_SENSOR_LOCK = "UPDATE_SENSOR_LOCK:";

    public static String getProcedureLineModel(Integer orderId) {
        return "PROCEDURE_LINE_MODEL:" + orderId;
    }

    public static String getLatestSensorRecordKey(String eui) {
        return "LATEST_SENSOR_RECORD:" + eui;
    }

    public static String getDefaultCapacityKey() {
        return "DEFAULT_CAPACITY";
    }

    public static String getCapacityKey(String resourceCode, String materialCode) {
        return "CAPACITY:" + resourceCode + "_" + materialCode;
    }

    public static String getTargetAggregationKey(String type, Integer deviceId, String targetName, String timeStr) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("TARGET_AGGREGATION:");
        stringBuilder.append(type);
        stringBuilder.append("_");
        stringBuilder.append(deviceId);
        stringBuilder.append("_");
        stringBuilder.append(targetName);
        stringBuilder.append("_");
        stringBuilder.append(timeStr);
        return stringBuilder.toString();
    }

    public static String getShiftTimeEnergyConsumptionKey(String str) {
        return "SHIFT_TIME_ENERGY_CONSUMPTION:" + str;
    }

    public static String getShiftTimeGasConsumptionKey(String str) {
        return "SHIFT_TIME_GAS_CONSUMPTION:" + str;
    }

    public static String getDeviceStateDurationDayUnion(Integer deviceId) {
        return "DEVICE_STATE_DURATION_DAY_UNION:" + deviceId;
    }

    public static String getDeviceStateDurationDayUnionIntoDb(Integer deviceId) {
        return "DEVICE_STATE_DURATION_DAY_UNION_INTO_DB:" + deviceId;
    }

    public static String getDeviceRunEntity(Integer deviceId, String recordDateStr) {
        return "DEVICE_RUN_ENTITY:" + deviceId + "_" + recordDateStr;
    }

    public static String getDevicePauseEntity(Integer deviceId, String recordDateStr) {
        return "DEVICE_PAUSE_ENTITY:" + deviceId + "_" + recordDateStr;
    }

    public static String getDeviceStopEntity(Integer deviceId, String recordDateStr) {
        return "DEVICE_STOP_ENTITY:" + deviceId + "_" + recordDateStr;
    }

    public static String getDeviceMaintainEntity(Integer deviceId, String recordDateStr) {
        return "DEVICE_MAINTAIN_ENTITY:" + deviceId + "_" + recordDateStr;
    }

    public static String getDeviceRunDurationIntoDb(Integer deviceId) {
        return "DEVICE_RUN_DURATION_INTO_DB:" + deviceId;
    }

    public static String getDevicePauseDurationIntoDb(Integer deviceId) {
        return "DEVICE_PAUSE_DURATION_INTO_DB:" + deviceId;
    }

    public static String getDeviceStopDurationIntoDb(Integer deviceId) {
        return "DEVICE_STOP_DURATION_INTO_DB:" + deviceId;
    }

    public static String getDeviceMaintainDurationIntoDb(Integer deviceId) {
        return "DEVICE_MAINTAIN_DURATION_INTO_DB:" + deviceId;
    }

    public static final String EXPORT_SENSOR_LOCK = "EXPORT_SENSOR_LOCK:";

    public static final String UPDATE_WORK_ORDER_DAY_COUNT_BY_COUNTER = "UPDATE_WORK_ORDER_DAY_COUNT_BY_COUNTER:";

    public static final String UPDATE_PRODUCT_ORDER_BY_COUNTER = "UPDATE_PRODUCT_ORDER_BY_COUNTER:";

    public static final String SENSOR_RECORD_QUERY_LOCK = "SENSOR_RECORD_QUERY_LOCK";

    public static final String WORK_ORDER_DAY_COUNT_CALL_BACK_LOCK = "WORK_ORDER_DAY_COUNT_CALL_BACK_LOCK:";

    public static String getScreenInputOutput(Integer lineId, long time, String workOrderNumber) {
        return "SCREEN_INPUT_OUTPUT:" + lineId + "_" + time + "_" + workOrderNumber;
    }

    public static String getUpdateDeviceDayCountKey(Integer deviceId, Long recordDate) {
        return "UPDATE_DEVICE_DAY_COUNT:" + deviceId + "_" + recordDate;
    }

    public static String getSensorDeviceKey(String eui) {
        return "SENSOR_DEVICE_LIST:" + eui;
    }

    public static String getDeviceSensorKey(String deviceId) {
        return "DEVICE_SENSOR_LIST:" + deviceId;
    }

    public static String getSensorEntityKey(String eui) {
        return "SENSOR_ENTITY:" + eui;
    }

    public static String getDeviceEntityKey(Integer deviceId) {
        return "DEVICE_ENTITY:" + deviceId;
    }

    public static String getIsCalConcurrently(Integer fid) {
        return "IS_CAL_CONCURRENTLY:" + fid;
    }

    public static String getDeviceEnergyConfigKey(Integer deviceModelId) {
        return "DEVICE_ENERGY_CONFIG:" + deviceModelId;
    }

    public static String getDeviceConsumptionConfigKey(Integer deviceModelId, String type) {
        return "DEVICE_" + type.toUpperCase() + "_CONFIG:" + deviceModelId;
    }

    public static String getDeviceGasConfigKey(Integer deviceModelId) {
        return "DEVICE_GAS_CONFIG:" + deviceModelId;
    }

    public static String getShitTimeKey(String recordDateStr) {
        return "SHIT_TIME:" + recordDateStr;
    }

    public static String getStopTimeKey(String startDate, String endDate) {
        return "SHIT_TIME:STOP" + startDate + "-" + endDate;
    }

    public static String getWorkOrderHourInputRecord(String workOrderNumber, String time) {
        return "WORK_ORDER_HOUR_INPUT:" + workOrderNumber + time;
    }

    public static String getBusinessConfigKey(String valueCode) {
        return "BUSINESS_CONFIG:" + valueCode;
    }

    /**
     * RAW表迁移锁
     */
    public static final String SENSOR_RECORD_MIGRATION_LOCK = "SENSOR_RECORD_MIGRATION_LOCK";

    /**
     * 指标表迁移锁
     */
    public static final String METRICS_TABLE_MIGRATION_LOCK = "METRICS_TABLE_MIGRATION_LOCK";

    /**
     * 指标表迁移锁
     */
    public static final String ORDER_EXECUTE_SEQ_LIST = "ORDER_EXECUTE_SEQ_LIST";

    /**
     * 扫码相关业务标识
     */
    /**
     * 流水码
     */
    public static final String SCANNER_PRODUCT_FLOW_CODE = "scanner_product_flow_code_";
    /**
     * 工位
     */
    public static final String SCANNER_FACILITIES = "scanner_facilities_";

    public static final String FACILITIES_ENTITY = "FACILITIES_ENTITY:";
    /**
     * 工艺工序
     */
    public static final String SCANNER_CRAFT_PROCEDURE = "scanner_craft_procedure_";
    /**
     * 工艺工序控制
     */
    public static final String SCANNER_PROCEDURE_CONTROLLER_CONFIG = "scanner_procedure_controller_config_";
    /**
     * 关联条码
     */
    public static final String SCANNER_RELEVANCE_CODES = "scanner_relevance_codes_";
    /**
     * 工单信息
     */
    public static final String SCANNER_WORK_ORDER = "scanner_work_order_";

    /**
     * 工序后的所有工序
     */
    public static final String SCANNER_LAST_PROCEDURES = "scanner_last_procedures_";
    /**
     * 工序前的所有关键工序
     */
    public static final String SCANNER_KEY_PROCEDURE = "scanner_key_procedure_";

    /**
     * 生产工单号
     */
    public static final String WORK_ORDER_NUMBER = "work_order_number_";

    /**
     * 工艺装备导入进度的key
     */
    public static final String PROCESS_ASSEMBLY_IMPORT_PROGRESS = "PROCESS_ASSEMBLY_IMPORT_PROGRESS";
    /**
     * 工艺装备导入锁的key
     */
    public static final String PROCESS_ASSEMBLY_IMPORT_LOCK = "PROCESS_ASSEMBLY_IMPORT_LOCK";

    /**
     * 工艺装备附件导入进度的key
     */
    public static final String PROCESS_ASSEMBLY_APPENDIX_IMPORT_PROGRESS = "PROCESS_ASSEMBLY_APPENDIX_IMPORT_PROGRESS";
    /**
     * 工艺装备附件导入锁的key
     */
    public static final String PROCESS_ASSEMBLY_APPENDIX_IMPORT_LOCK = "PROCESS_ASSEMBLY_APPENDIX_IMPORT_LOCK";

    /**
     * 工资配置导入
     */
    public static final String VALUATION_CONFIG_IMPORT_LOCK = "VALUATION_CONFIG_IMPORT_LOCK";
    public static final String VALUATION_CONFIG_IMPORT_PROGRESS = "VALUATION_CONFIG_IMPORT_PROGRESS";

    /**
     * 清除工单排序数据
     */
    public static final String ORDER_EXECUTE_SEQ_CHECK_ORDER_STATE = "ORDER_EXECUTE_SEQ_CHECK_ORDER_STATE";

    /**
     * 属性分类导入进度的key
     */
    public static final String MATERIAL_ATTRIBUTE_TYPE_IMPORT_PROGRESS = "MATERIAL_ATTRIBUTE_TYPE_IMPORT_PROGRESS";
    /**
     * 属性分类导入锁的key
     */
    public static final String MATERIAL_ATTRIBUTE_TYPE_IMPORT_LOCK = "MATERIAL_ATTRIBUTE_TYPE_IMPORT_LOCK";

    /**
     * 物料属性导入进度的key
     */
    public static final String MATERIAL_ATTRIBUTE_IMPORT_PROGRESS = "MATERIAL_ATTRIBUTE_IMPORT_PROGRESS";
    /**
     * 物料属性导入锁的key
     */
    public static final String MATERIAL_ATTRIBUTE_IMPORT_LOCK = "MATERIAL_ATTRIBUTE_IMPORT_LOCK";

    /**
     * 配置迁移导入进度的key
     */
    public static final String CONFIG_MIGRATE_IMPORT_PROGRESS = "CONFIG_MIGRATE_IMPORT_PROGRESS";
    /**
     * 配置迁移导入锁的key
     */
    public static final String CONFIG_MIGRATE_IMPORT_LOCK = "CONFIG_MIGRATE_IMPORT_LOCK";

    /**
     * 设备模型缓存key
     *
     * @param deviceId
     * @return
     */
    public static String getDeviceModelKey(Integer deviceId) {
        return "DEVICE_MODEL:" + deviceId;
    }

    public static String getModelThresholdKey(Integer modelId, String targetName, String instanceId) {
        return "MODEL_THRESHOLD:" + modelId + ":" + targetName + ":" + instanceId;
    }

    public static final String TASK_PROCESS_LOCK = "TASK_PROCESS_LOCK";


    public static final String WORK_ORDER_PROCEDURE_FID = "WORK_ORDER_PROCEDURE_FID:";

    public static String getWorkOrderProcedureByFid(String workOrderNumber, Integer fid) {
        return WORK_ORDER_PROCEDURE_FID + workOrderNumber + ":" + fid;
    }

    public static final String BOM_COPY_IMPORT_LOCK = "BOM_COPY_IMPORT_LOCK";
    public static final String BOM_COPY_IMPORT_PROGRESS = "BOM_COPY_IMPORT_PROGRESS";

    public static String getWorkCenterDetail(Integer id) {
        return "WORK_CENTER_DETAIL:" + id;
    }

    public static String getWorkOrderRelevanceDevice(Integer workOrderId) {
        return "WORK_ORDER_RELEVANCE_DEVICE:" + workOrderId;
    }

    public static String getDeviceDayCountRefreshKey(Integer deviceId) {
        return "DEVICE_DAY_COUNT_REFRESH:" + deviceId;
    }

    public static String getMetricsLock(String targetName) {
        return "METRICS_SDK_LOCK:" + targetName;
    }
}
