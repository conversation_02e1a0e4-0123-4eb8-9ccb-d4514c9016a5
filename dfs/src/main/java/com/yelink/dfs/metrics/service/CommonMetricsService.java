package com.yelink.dfs.metrics.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.HashBasedTable;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.metrics.entity.dto.BasicUnitDTO;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfs.service.user.TeamTypeDefService;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.TeamTypeDefEntity;
import com.yelink.metrics.api.dto.common.BaseMaterialEntity;
import com.yelink.metrics.api.dto.common.CommonMaterialEntity;
import com.yelink.metrics.api.dto.common.MaterialType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 */
@Component
public class CommonMetricsService {

    @Resource
    private MaterialService materialService;
    @Resource
    private MaterialAuxiliaryAttrService materialAuxiliaryAttrService;
    @Resource
    private ProductionLineService lineService;
    @Resource
    private SysTeamService teamService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private ModelService modelService;
    @Resource
    private TeamTypeDefService teamTypeDefService;


    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> {
            Object key = keyExtractor.apply(t);
            // 为null键分配特殊标记，避免业务歧义
            key = (key == null) ? Optional.empty() : key;
            return seen.add(key);
        };
    }

    public void setMaterialAttr(Collection<? extends BaseMaterialEntity> vos) {
        List<String> materialCodes = vos.stream().map(BaseMaterialEntity::getMaterialCode).distinct().collect(Collectors.toList());
        if(CollUtil.isEmpty(materialCodes)) {
            return;
        }
        List<MaterialEntity> materials = materialService.getMaterialsByCodes(materialCodes);
        Map<String, MaterialEntity> materialCodeMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));
        // 特征参数
        List<MaterialAuxiliaryAttrEntity> allAuxiliaryAttrs = materialAuxiliaryAttrService.lambdaQuery().in(MaterialAuxiliaryAttrEntity::getMaterialCode, materialCodes).list();
        Map<String, List<MaterialAuxiliaryAttrEntity>> materialCodeAuxiliaryGroup = allAuxiliaryAttrs.stream().collect(Collectors.groupingBy(MaterialAuxiliaryAttrEntity::getMaterialCode));
        for (BaseMaterialEntity vo : vos) {
            MaterialEntity material = materialCodeMap.get(vo.getMaterialCode());
            if(material == null) {
                continue;
            }
            vo.setMaterialName(material.getName());
            vo.setMaterialTypeName(material.getTypeName());
            vo.setMaterialSortName(material.getSortName());
            vo.setMaterialStandard(material.getStandard());
            vo.setMaterialDrawingNumber(material.getDrawingNumber());
            vo.setMaterialRawMaterial(material.getRawMaterial());
            vo.setMaterialCustomFieldOne(material.getCustomFieldOne());
            vo.setMaterialCustomFieldTwo(material.getCustomFieldTwo());
            vo.setMaterialCustomFieldThree(material.getCustomFieldThree());
            vo.setMaterialCustomFieldFour(material.getCustomFieldFour());
            vo.setMaterialCustomFieldFive(material.getCustomFieldFive());
            vo.setMaterialCustomFieldSix(material.getCustomFieldSix());
            vo.setMaterialCustomFieldSeven(material.getCustomFieldSeven());
            vo.setMaterialCustomFieldEight(material.getCustomFieldEight());
            vo.setMaterialCustomFieldNine(material.getCustomFieldNine());
            vo.setMaterialCustomFieldTen(material.getCustomFieldTen());
            vo.setMaterialCustomFieldEleven(material.getCustomFieldEleven());
            List<MaterialAuxiliaryAttrEntity> auxiliaryAttrs = materialCodeAuxiliaryGroup.get(vo.getMaterialCode());
            if(CollUtil.isNotEmpty(auxiliaryAttrs)) {
                String materialSkuAttrName = auxiliaryAttrs.stream().sorted(Comparator.comparing(MaterialAuxiliaryAttrEntity::getId)).map(MaterialAuxiliaryAttrEntity::getAuxiliaryAttrName).collect(Collectors.joining(Constant.SEP));
                vo.setMaterialSkuAttrName(materialSkuAttrName);
            }
        }
    }
    public void setMaterialAttrForce(Collection<? extends MaterialType> vos) {
        List<CommonMaterialEntity> commonMaterials = vos.stream()
                .map(e -> CommonMaterialEntity.builder().materialCode(e.getMaterialCode()).build())
                .filter(distinctByKey(MaterialType::getMaterialCode)).collect(Collectors.toList());
        setMaterialAttr(commonMaterials);
        Map<String, CommonMaterialEntity> materialCodeMap = commonMaterials.stream().collect(Collectors.toMap(CommonMaterialEntity::getMaterialCode, Function.identity()));
        for (MaterialType vo : vos) {
            CommonMaterialEntity common = materialCodeMap.get(vo.getMaterialCode());
            BeanUtil.copyProperties(common, vo, "id", "time", "uniCode");
        }
    }

    public Collection<CommonMaterialEntity> getCommonMaterialEntitys(List<String> materialCodes) {
        if (CollUtil.isEmpty(materialCodes)) {
            return Collections.emptyList();
        }
        List<CommonMaterialEntity> commonMaterialEntities = materialCodes.stream().map(materialCode -> CommonMaterialEntity.builder().materialCode(materialCode).build()).collect(Collectors.toList());
        setMaterialAttr(commonMaterialEntities);
        return commonMaterialEntities;
    }
    public HashBasedTable<String, Integer, String> getModelType(List<BasicUnitDTO> basicUnits) {
        List<Integer> lineIds = basicUnits.stream().filter(e -> WorkCenterTypeEnum.LINE.getCode().equals(e.getWorkCenterType())).map(BasicUnitDTO::getProductionBasicUnitId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> deviceIds = basicUnits.stream().filter(e -> WorkCenterTypeEnum.DEVICE.getCode().equals(e.getWorkCenterType())).map(BasicUnitDTO::getProductionBasicUnitId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> teamIds = basicUnits.stream().filter(e -> WorkCenterTypeEnum.TEAM.getCode().equals(e.getWorkCenterType())).map(BasicUnitDTO::getProductionBasicUnitId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 产线
        List<ProductionLineEntity> lines = CollUtil.isEmpty(lineIds)? Collections.emptyList() : lineService.listByIds(lineIds);
        Map<Integer, Integer> lineIdModelIdMap = lines.stream().filter(e -> e.getModelId() != null).collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getModelId));
        // 设备
        List<DeviceEntity> device = CollUtil.isEmpty(deviceIds)? Collections.emptyList() : deviceService.listByIds(deviceIds);
        Map<Integer, Integer> deviceIdModelIdMap = device.stream().filter(e -> e.getModelId() != null).collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getModelId));
        // 班组
        List<SysTeamEntity> teams = CollUtil.isEmpty(teamIds)? Collections.emptyList() : teamService.listByIds(teamIds);
        Map<Integer, Integer> teamIdTypeIdMap = teams.stream().filter(e -> e.getTeamType() != null).collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamType));


        // 模型
        List<Integer> modelIds = Stream.concat(
                lineIdModelIdMap.values().stream(),
                deviceIdModelIdMap.values().stream()
        ).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        List<ModelEntity> models = CollUtil.isEmpty(modelIds)? Collections.emptyList() : modelService.listByIds(modelIds);
        Map<Integer, String> modelIdNameMap = models.stream().filter(e -> e.getName() != null).collect(Collectors.toMap(ModelEntity::getId, ModelEntity::getName));
        // 班组类型
        Collection<Integer> teamTypeIds = teamIdTypeIdMap.values();
        List<TeamTypeDefEntity> teamTypes = CollUtil.isEmpty(teamTypeIds)? Collections.emptyList() : teamTypeDefService.listByIds(teamTypeIds);
        Map<Integer, String> teamTypeIdNameMap = teamTypes.stream().filter(e -> e.getTypeDefName() != null).collect(Collectors.toMap(TeamTypeDefEntity::getId, TeamTypeDefEntity::getTypeDefName));

        HashBasedTable<String, Integer, String> results = HashBasedTable.create();
        for (Map.Entry<Integer, Integer> entry : lineIdModelIdMap.entrySet()) {
            String modelName = modelIdNameMap.get(entry.getValue());
            if(modelName != null) {
                results.put(WorkCenterTypeEnum.LINE.getCode(), entry.getKey(), modelName);
            }
        }
        for (Map.Entry<Integer, Integer> entry : deviceIdModelIdMap.entrySet()) {
            String modelName = modelIdNameMap.get(entry.getValue());
            if(modelName != null) {
                results.put(WorkCenterTypeEnum.DEVICE.getCode(), entry.getKey(), modelName);
            }
        }
        for (Map.Entry<Integer, Integer> entry : teamIdTypeIdMap.entrySet()) {
            String modelName = teamTypeIdNameMap.get(entry.getValue());
            if(modelName != null) {
                results.put(WorkCenterTypeEnum.TEAM.getCode(), entry.getKey(), modelName);
            }
        }
        return results;
    }

}
