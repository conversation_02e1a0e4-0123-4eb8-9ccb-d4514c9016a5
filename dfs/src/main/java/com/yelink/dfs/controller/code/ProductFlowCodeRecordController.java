package com.yelink.dfs.controller.code;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.code.ScannerQualityStateEnum;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.code.dto.CodeRecordSelectDTO;
import com.yelink.dfs.entity.code.dto.DetectionSubmitDTO;
import com.yelink.dfs.entity.code.dto.ProductFlowCodeRecordImportDTO;
import com.yelink.dfs.entity.code.dto.ScannerDTO;
import com.yelink.dfs.entity.code.dto.TraceReportDTO;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.screen.dto.FacilitiesDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.code.CodeTargetRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.code.ScannerService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfs.service.impl.code.ProductFlowCodeRecordExportHandler;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.dto.ExportBillBatchDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.barcode.RecordDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeTargetRecordEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.FileUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description 生产流水码记录
 * @Date 2022/3/31 14:36
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/code/record")
public class ProductFlowCodeRecordController extends BaseController {

    private final ProductFlowCodeRecordService productFlowCodeRecordService;
    private final CodeTargetRecordService codeTargetRecordService;
    private final FeedRecordService feedRecordService;
    private final ScannerService scannerService;
    private final ModelUploadFileService modelUploadFileService;
    private final FastDfsClientService fastDfsClientService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ExcelService excelService;


    /**
     * 查询生产流水码记录列表（单品追溯）
     *
     * @param codeRecordSelectDTO
     * @return
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody CodeRecordSelectDTO codeRecordSelectDTO) {
        Page<ProductFlowCodeRecordEntity> list = productFlowCodeRecordService.list(codeRecordSelectDTO);
        return success(list);
    }

    /**
     * 工位类型
     *
     * @return
     */
    @GetMapping("/fac/list")
    public ResponseData getFacList() {
        List<FacilitiesDTO> facilitiesDTOList = productFlowCodeRecordService.getFacList();
        return success(facilitiesDTOList);
    }

    /**
     * 通过id获取产品流水码记录详情
     *
     * @return
     */
    @GetMapping("/detail")
    public ResponseData getDetailById(@RequestParam(value = "id") Integer id) {
        ProductFlowCodeRecordEntity entity = productFlowCodeRecordService.getDetailById(id);
        return success(entity);
    }

    /**
     * 通过id获取产品流水码记录详情-简单版
     *
     * @return
     */
    @GetMapping("/simple/detail")
    public ResponseData getSimpleDetailById(@RequestParam(value = "id") Integer id) {
        ProductFlowCodeRecordEntity entity = productFlowCodeRecordService.getSimpleDetailById(id);
        return success(entity);
    }


    /**
     * 获取指标上报详情
     *
     * @return
     */
    @GetMapping("/target/list")
    public ResponseData getTargetList(@RequestParam(value = "id") Integer id) {
        LambdaQueryWrapper<CodeTargetRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CodeTargetRecordEntity::getCodeRecordId, id);
        List<CodeTargetRecordEntity> codeTargetRecordEntities = codeTargetRecordService.list(lambdaQueryWrapper);
        return success(codeTargetRecordEntities);
    }


    /**
     * 追溯报告列表
     */
    @PostMapping("/trace/report/list")
    public ResponseData traceReportList(@RequestBody TraceReportDTO traceReportDTO) {
        return success(productFlowCodeRecordService.traceReportList(traceReportDTO));
    }

    /**
     * 追溯页面信息
     */
    @PostMapping("/trace/report/detail")
    public ResponseData traceReportList(@RequestBody ProductFlowCodeRecordEntity productFlowCodeRecordEntity) {
        return success(productFlowCodeRecordService.getCourse(productFlowCodeRecordEntity.getProductFlowCode()));
    }


    /**
     * 获取生产过程信息详情
     */
    @GetMapping("/trace/course/detail")
    public ResponseData courseDetail(@RequestParam Integer codeRecordId) {
        return success(productFlowCodeRecordService.courseDetail(codeRecordId));
    }

    /**
     * 维修记录选择材料时拿到上料列表
     */
    @PostMapping("/feed/record/by/code")
    public ResponseData courseDetail(@RequestBody ProductFlowCodeRecordEntity productFlowCodeRecordEntity) {
        if (StringUtils.isBlank(productFlowCodeRecordEntity.getProductFlowCode())) {
            throw new ResponseException("请输入条码号");
        }
        return success(feedRecordService.getFeedRecordByCode(productFlowCodeRecordEntity.getProductFlowCode()));
    }

    /**
     * 君兰
     * RF检测工位机、电声检测工位机-确认并提交
     */
    @PostMapping("/confirm/submit")
    public ResponseData confirmAndSubmits(@RequestBody List<DetectionSubmitDTO> submits) {
        String username = getUsername();
        // 保存数据:1.添加扫码记录 2.保存指标
        scannerService.confirmAndSubmit(submits, username);
        return ResponseData.success(true);
    }

    /**
     * 上传自定义导出单据模板
     */
    @PostMapping("/export/template/upload")
    public ResponseData uploadPurchaseTemplate(MultipartFile file) {
        productFlowCodeRecordService.uploadCustomExportTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 下载导出默认模板
     *
     * @param response
     * @throws Exception
     */
    @RequestMapping("/default/export/template/download")
    public void downloadDefaultExportTemplate(HttpServletResponse response) throws Exception {
        byte[] bytes = productFlowCodeRecordService.downloadDefaultExportTemplate("classpath:template/flowCodeRecordDefaultTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "电子工序流程卡默认单据导出模板" + Constant.XLSX);
    }


    /**
     * 获取上传单据模板列表
     *
     * @return
     */
    @GetMapping("/export/template/list")
    public ResponseData list() {
        return success(modelUploadFileService.listByType(ModelUploadFileEnum.FLOW_CODE_RECORD.getCode()));
    }


    /**
     * 下载指定上传单据模板
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/export/template/download")
    public void downloadTemplate(@RequestParam Integer id, HttpServletResponse response) throws Exception {
        ModelUploadFileEntity modelUploadFileEntity = modelUploadFileService.getById(id);
        if (Objects.isNull(modelUploadFileEntity)) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        byte[] bytes = fastDfsClientService.getFileStream(modelUploadFileEntity.getFileAddress());
        ExcelTemplateImportUtil.responseToClient(response, bytes, modelUploadFileEntity.getFileName());
    }

    /**
     * 删除上传的单据模板
     *
     * @param id
     * @return
     */
    @DeleteMapping("/export/template/delete")
    public ResponseData deleteTemplate(@RequestParam Integer id) {
        modelUploadFileService.removeFileById(id);
        return success();
    }

    /**
     * 导出单据为pdf
     *
     * @param batchDTO
     * @param response
     * @throws Exception
     */
    @PostMapping("/export/pdf")
    public void printDataToPdf(@RequestBody ExportBillBatchDTO batchDTO, HttpServletResponse response) throws Exception {
        File pdfFile = productFlowCodeRecordService.asposePrintDataToPdf(batchDTO);
        try {
            FileUtil.exportPdfFile(response, pdfFile, batchDTO.getFileName());
        } finally {
            FileUtils.deleteQuietly(pdfFile);
        }
    }

    /**
     * 导出单据为EXCEL
     *
     * @param batchDTO
     * @param response
     * @throws Exception
     */
    @PostMapping("/export/excel")
    public void exportDataToExcel(@RequestBody ExportBillBatchDTO batchDTO, HttpServletResponse response) throws Exception {
        File pdfFile = productFlowCodeRecordService.asposePrintDataToExcel(batchDTO);
        try {
            InputStream inputStream = new FileInputStream(pdfFile);
            ExcelTemplateImportUtil.responseToClient(response, inputStream, batchDTO.getFileName());
        } finally {
            FileUtils.deleteQuietly(pdfFile);
        }
    }


    /**
     * 按照条件进行导出
     *
     * @param relationNumber  关联单号
     * @param startTime       开始时间
     * @param endTime         结束时间
     * @param productFlowCode 生产流水码
     * @param facName         工位名称
     * @param response
     * @deprecated 2.17.1版本废弃  由于导出大量数据会造成oom
     */
    @Deprecated
    @GetMapping("/excel/export")
    public void exportExcel(@RequestParam(value = "relationNumber", required = false) String relationNumber,
                            @RequestParam(value = "startTime", required = false) String startTime,
                            @RequestParam(value = "endTime", required = false) String endTime,
                            @RequestParam(value = "productFlowCode", required = false) String productFlowCode,
                            @RequestParam(value = "facName", required = false) Integer facName,
                            HttpServletResponse response) throws IOException {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE_RECORD_EXCEL_EXPORT, new Date(), 3, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("并发占用，稍后再试");
        }
        try {
            productFlowCodeRecordService.exportExcel(relationNumber, startTime, endTime, productFlowCode, facName, response);
        } finally {
            redisTemplate.delete(RedisKeyPrefix.DFS_PRODUCT_FLOW_CODE_RECORD_EXCEL_EXPORT);
        }
    }

    /**
     * 默认导出模板
     */
    @GetMapping("/default/export/template")
    public void downloadSaleOrderDefaultExportTemplate(HttpServletResponse response) throws IOException {
        CodeRecordSelectDTO selectDTO = new CodeRecordSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        IPage<ProductFlowCodeRecordEntity> page = productFlowCodeRecordService.pageList(null,10, selectDTO);
        List<ProductFlowCodeRecordImportDTO> list = productFlowCodeRecordService.convertToExportDTO(page.getRecords());
        EasyExcelUtil.export(response, "过站记录默认导出模板", "数据源", list, ProductFlowCodeRecordImportDTO.class);
    }

    /**
     * 最多导出满足条件的100万行数据、超过数据不处理
     * 异步执行  条件导出流水码记录数据
     *
     * @param codeRecordSelectDTO
     * @return
     */
    @PostMapping("/syc/export")
    public ResponseData exportExcel(@RequestBody CodeRecordSelectDTO codeRecordSelectDTO) {
        DataExportParam<CodeRecordSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.PRODUCT_FLOW_CODE_RECORD.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_CODE_RECORD.name());
        dataExportParam.setCreateUserCode(getUsername());
        codeRecordSelectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>(8);
        parameters.put(CodeRecordSelectDTO.class.getName(), codeRecordSelectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        Long result = excelService.doExport(dataExportParam, ProductFlowCodeRecordExportHandler.class);
        return success(result);
    }


    /**
     * 分页查询当前导出任务列表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData exportExcel(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                    @RequestParam(required = false, defaultValue = "1") Integer currentPage
    ) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_CODE_RECORD.name());
        return success(excelService.listPage(excelTask, currentPage, pageSize));
    }


    /**
     * 查询 流水码导入进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_CODE_RECORD.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.info("任务id错误");
            return fail();
        }
        return success(records.get(0));
    }


    /**
     * 对扫码记录进行失效处理
     * \
     *
     * @param disableRecordDTO
     * @return
     */
    @PostMapping("/disable/record")
    public ResponseData disableRecordByFid(@RequestBody RecordDTO disableRecordDTO) {
        productFlowCodeRecordService.disableRecordByFid(disableRecordDTO);
        return success();
    }

    /**
     * 获取质量状态枚举列表
     *
     * @return
     */
    @GetMapping("/scanner/quality/state/enum/list")
    public ResponseData getEnumList() {
        List<CommonType> list = new ArrayList<>();
        ScannerQualityStateEnum[] values = ScannerQualityStateEnum.values();
        for (ScannerQualityStateEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 修改生产流水码记录
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody ProductFlowCodeRecordEntity entity) {
        productFlowCodeRecordService.updateEntityById(entity);
        return success();
    }

    /**
     * 统计初次不良和待维修数量
     *
     * @param scannerDTO
     * @return
     */
    @PostMapping("/first/unqualified/maintain/count")
    public ResponseData selectFirstUnqualifiedAndMaintainCount(@RequestBody ScannerDTO scannerDTO) {
        if (StringUtils.isBlank(scannerDTO.getRelationNumber())) {
            throw new ResponseException("请输入工单号");
        }
        return success(productFlowCodeRecordService.selectFirstUnqualifiedAndMaintainCount(scannerDTO.getRelationNumber()))
                ;
    }

    /**
     * 统计工位、工序相关过站数量
     *
     * @param workOrderNumber
     * @param facId
     * @param craftProcedureId
     * @return
     */
    @GetMapping("/select/record/count/fid")
    public ResponseData selectFirstUnqualifiedAndMaintainCount(@RequestParam(value = "workOrderNumber") String workOrderNumber,
                                                               @RequestParam(value = "facId") Integer facId,
                                                               @RequestParam(value = "craftProcedureId", required = false) Integer craftProcedureId) {
        //拿到工单该工序过站数
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> productFlowCodeRecordWrapper = new LambdaQueryWrapper<>();
        productFlowCodeRecordWrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber);
        //不传工序找工位过站数
        WrapperUtil.eq(productFlowCodeRecordWrapper, ProductFlowCodeRecordEntity::getProdureId, craftProcedureId);
        productFlowCodeRecordWrapper.eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode());
        Long count = productFlowCodeRecordService.count(productFlowCodeRecordWrapper);
        //维修数量
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber).eq(ProductFlowCodeRecordEntity::getFacId, facId)
                .eq(ProductFlowCodeRecordEntity::getIsMaintain, true);
        Long maintainCount = productFlowCodeRecordService.count(lambdaQueryWrapper);
        Map<String, Long> map = new HashMap<>(2);
        map.put("passCount", count);
        map.put("maintainCount", maintainCount);
        return success(map);
    }

    /**
     * 工序进度详情
     *
     * @param workOrderNumber
     * @return
     */
    @GetMapping("/procedure/process")
    public ResponseData procedureProcess(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        return success(productFlowCodeRecordService.procedureProcess(workOrderNumber));
    }

    /**
     * 批次工序进度详情
     *
     * @param productFlowCode
     * @return
     */
    @GetMapping("/batch/procedure/process")
    public ResponseData batchProcedureProcess(@RequestParam(value = "productFlowCode") String productFlowCode) {
        return success(productFlowCodeRecordService.batchProcedureProcess(productFlowCode));
    }

    /**
     * 生产订单工序过站进度详情
     *
     * @param productOrderNumber
     * @return
     */
    @GetMapping("/product_order/procedure/process")
    public ResponseData productOrderProcedureProcess(@RequestParam(value = "productOrderNumber") String productOrderNumber) {
        return success(productFlowCodeRecordService.productOrderProcedureProcess(productOrderNumber));
    }

}
