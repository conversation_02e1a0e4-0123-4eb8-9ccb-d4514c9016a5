package com.yelink.dfs.controller.order;


import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.order.CounterpartTypeEnum;
import com.yelink.dfs.constant.user.CustomerMaterialListStateEnum;
import com.yelink.dfs.entity.order.CounterpartEntity;
import com.yelink.dfs.entity.order.CustomerAddressEntity;
import com.yelink.dfs.entity.order.CustomerEntity;
import com.yelink.dfs.entity.order.CustomerMaterialListEntity;
import com.yelink.dfs.entity.order.dto.CounterpartDTO;
import com.yelink.dfs.entity.order.dto.CustomerExportDTO;
import com.yelink.dfs.entity.order.dto.CustomerMaterialBatchUpdateDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumbersDTO;
import com.yelink.dfscommon.dto.dfs.CustomerMaterialByMaterialDTO;
import com.yelink.dfs.open.v1.production.dto.CustomerAddressDTO;
import com.yelink.dfs.open.v1.production.dto.CustomerDeleteDTO;
import com.yelink.dfs.open.v1.production.dto.CustomerMaterialListSelectDTO;
import com.yelink.dfs.open.v1.production.dto.CustomerSelectDTO;
import com.yelink.dfs.service.order.CustomerAddressService;
import com.yelink.dfs.service.order.CustomerMaterialListService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @Date 2021/4/6 21:33
 */
@Slf4j
@RestController
@RequestMapping("/customers")
public class CustomerController extends BaseController {
    @Resource
    private CustomerService customerService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private CustomerMaterialListService customerMaterialListService;
    @Resource
    private CustomerAddressService customerAddressService;

    /**
     * 分页模糊、条件查询客户列表
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody CustomerSelectDTO selectDTO) {
        return success(customerService.list(selectDTO));
    }

    /**
     * 新增客户信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "订单管理", type = OperationType.ADD, desc = "新增了客户编号为#{customerCode}的客户")
    public ResponseData add(@RequestBody @Validated({CustomerEntity.Insert.class}) CustomerEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        CustomerEntity customerEntity = customerService.addCustomer(entity);
        return success(customerEntity);
    }

    /**
     * 新增客户信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert/released")
    @OperLog(module = "订单管理", type = OperationType.ADD, desc = "新增了客户编号为#{customerCode}的客户")
    public ResponseData addReleasedCustomer(@RequestBody @Validated({CustomerEntity.Insert.class}) CustomerEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        CustomerEntity customerEntity = customerService.addReleasedCustomer(entity);
        return success(customerEntity);
    }

    /**
     * 修改客户信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "订单管理", type = OperationType.UPDATE, desc = "修改了客户编号为#{customerCode}的客户")
    public ResponseData update(@RequestBody @Validated({CustomerEntity.Update.class}) CustomerEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        if (customerService.updateCustomer(entity)) {
            return success(entity);
        }
        return fail();
    }

    /**
     * 通过Id删除客户信息
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "订单管理", type = OperationType.DELETE, desc = "删除了客户编号为#{customerCode}的客户")
    public ResponseData delete(@PathVariable Integer id) {
        CustomerEntity customerEntity = customerService.deleteCustomerById(id);
        return success(customerEntity);
    }

    /**
     * 通过Id获取客户信息
     *
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable Integer id) {
        CustomerEntity customerEntity = customerService.detail(id);
        return success(customerEntity);
    }

    /**
     * 获取客户状态以及对应的code
     *
     * @return
     */
    @GetMapping("/all/state")
    public ResponseData getAllState() {
        return success(customerService.getAllState());
    }

    /**
     * 审批
     *
     * @param approvalStatus
     * @param approvalSuggestion
     * @param id
     * @return
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        customerService.approve(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        customerService.approveBatch(dto);
        return success();
    }

    /**
     * 新增客户对接人
     *
     * @return
     */
    @PostMapping("/insert/counterpart")
    public ResponseData insertCounterpart(@RequestBody CounterpartDTO counterpartDTO) {
        String type = CounterpartTypeEnum.CUSTOMER_TYPE.getType();
        customerService.insertCounterpart(counterpartDTO, type);
        return success();
    }

    /**
     * 查询客户对接人
     * isEnable   有值时只查询对接人配置信息
     *
     * @return
     */
    @GetMapping("/get/counterpart")
    public ResponseData getCounterparts(@RequestParam(value = "isEnable", required = false) String isEnable) {
        String type = CounterpartTypeEnum.CUSTOMER_TYPE.getType();
        List<CounterpartEntity> customerEntities = customerService.getCounterparts(type, isEnable);
        return success(customerEntities);
    }

    /**
     * 查询客户档案中客户对接人
     *
     * @return
     */
    @GetMapping("/get/exist/counterpart")
    public ResponseData getExistCounterparts() {
        List<CounterpartEntity> dist = customerService.getExistCounterparts();
        return success(dist);
    }

    /**
     * 客户按地址维度查询
     */
    @PostMapping("/list/customer/address")
    public ResponseData listCustomerAddress(@RequestBody CustomerSelectDTO selectDTO) {
        Assert.notNull(selectDTO.getCurrent(), RespCodeEnum.PARAM_CURRENT_EXCEPTION.getMsgDes());
        Assert.notNull(selectDTO.getSize(), RespCodeEnum.PARAM_SIZE_EXCEPTION.getMsgDes());
        Page<CustomerAddressDTO> pageData = customerService.listCustomerAddress(selectDTO);
        return success(pageData);
    }

    /**
     * 设置默认地址
     */
    @PutMapping("/address/set-default/{customerId}/{addressId}")
    @OperLog(module = "订单管理", type = OperationType.UPDATE, desc = "设置了客户#{customerId}的默认地址")
    public ResponseData setDefaultAddress(@PathVariable Integer customerId, @PathVariable Integer addressId) {
        customerAddressService.setDefaultAddress(customerId, addressId);
        return success();
    }
//
//    /**
//     * 下载客户档案模板
//     */
//    @GetMapping("/export/template")
//    public void exportTemplate(HttpServletResponse response) {
//        customerService.exportTemplate(response);
//    }

    /**
     * 客户档案信息导出
     *
     * @return
     */
    @PostMapping("/export/customer")
    public void exportCustomerList(@RequestBody CustomerSelectDTO selectDTO,
                                   HttpServletResponse response) throws IOException {
        selectDTO.setCurrent(1);
        selectDTO.setSize(Integer.MAX_VALUE);
        Page<CustomerEntity> page = customerService.list(selectDTO);
        List<CustomerEntity> records = page.getRecords();

        List<CustomerExportDTO> exports = new ArrayList<>();
        for (CustomerEntity customer : records) {
            List<CustomerAddressEntity> addresses = customer.getCustomerAddressList();
            if (CollectionUtils.isEmpty(addresses)) {
                // 如果客户没有地址，导出一条记录，地址信息为空
                exports.add(CustomerExportDTO.convertToExportWithAddress(customer, null));
            } else {
                // 如果客户有地址，为每个地址导出一条记录
                for (CustomerAddressEntity address : addresses) {
                    exports.add(CustomerExportDTO.convertToExportWithAddress(customer, address));
                }
            }
        }

        EasyExcelUtil.export(response, "客户档案数据导出", "客户档案", exports, CustomerExportDTO.class);
    }

//    /**
//     * 客户档案信息导入
//     *
//     * @return
//     */
//    @PostMapping("/upload/customer")
//    public ResponseData uploadCustomerList(MultipartFile file) {
//        //1、判断导入文件的合法性
//        ExcelUtil.checkImportExcelFile(file);
//        //2、初始化处理进度
//        ImportProgressDTO build = ImportProgressDTO.builder()
//                .progress(Double.valueOf(Constant.ZERO_VALUE))
//                .executionDescription("正在处理中，请耐心等候...")
//                .executionStatus(false)
//                .build();
//        redisTemplate.opsForValue().set(RedisKeyPrefix.CUSTOMER_IMPORT_PROGRESS, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
//        //4、加锁
//        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.CUSTOMER_IMPORT_LOCK, new Date(), 1, TimeUnit.HOURS);
//        if (lockStatus == null || !lockStatus) {
//            log.info("获取redis同步锁失败");
//            return fail(RespCodeEnum.IMPORT_OPREATION_NOT_RE);
//        }
//        String username = getUsername();
//        customerService.uploadCustomerList(file, username);
//        return ResponseData.success();
//    }

//    /**
//     * 查询导入进度
//     *
//     * @return
//     */
//    @GetMapping("/import/progress")
//    public ResponseData getImportProgress() {
//        Object obj = redisTemplate.opsForValue().get(RedisKeyPrefix.CUSTOMER_IMPORT_PROGRESS);
//        return success(obj);
//    }

    /**
     * 分页模糊、条件查询客户物料清单列表
     *
     * @return
     */
    @PostMapping("/materials/list")
    public ResponseData getMaterialList(@RequestBody CustomerMaterialListSelectDTO selectDTO) {
        return success(customerMaterialListService.list(selectDTO));
    }

    /**
     * 以客户维度 刷新客户物料清单
     *
     * @param
     * @return
     */
    @PostMapping("/materials/insert")
    @OperLog(module = "销售管理", type = OperationType.UPDATE, desc = "刷新了客户编码为#{code}的客户物料清单")
    public ResponseData refreshCustomerMaterialsByCustomer(@RequestBody CustomerEntity entity) {
        entity.setCreateBy(getUsername());
        customerService.saveCustomerMaterials(entity);
        return success(entity);
    }

    /**
     * 以物料维度 刷新客户物料清单
     *
     * @param
     * @return
     */
    @PostMapping("/materials/insert/by_material")
    @OperLog(module = "销售管理", type = OperationType.UPDATE, desc = "刷新了物料编码为#{materialCode}的客户物料清单")
    public ResponseData refreshCustomerMaterialsByMaterial(@RequestBody CustomerMaterialByMaterialDTO entity) {
        entity.setCreateBy(getUsername());
        customerService.refreshCustomerMaterialsByMaterial(entity);
        return success(entity);
    }

    /**
     * 删除供应商物料清单
     *
     * @param
     * @return
     */
    @DeleteMapping("/materials/delete")
    @OperLog(module = "销售管理", type = OperationType.DELETE, desc = "删除了客户编号为#{customerCode}的客户，物料编码为#{materialCode}的物料")
    public ResponseData deleteRawMaterial(@RequestParam(value = "customerCode") String customerCode,
                                          @RequestParam(value = "materialCode") String materialCode) {
        CustomerMaterialListEntity entity = customerMaterialListService.removeEntityById(materialCode, customerCode);
        return success(entity);
    }

    /**
     * 通过客户编号获取客户物料清单信息
     *
     * @param customerCode
     * @return
     */
    @GetMapping("/materials/detail")
    public ResponseData getMaterialDetail(@RequestParam(value = "customerCode") String customerCode) {
        CustomerEntity customerEntity = customerService.getMaterials(customerCode);
        return success(customerEntity);
    }

    /**
     * 获取客户物料清单状态以及对应的code
     * {@link CustomerMaterialListStateEnum}
     *
     * @return
     */
    @GetMapping("/materials/all/state")
    public ResponseData getMaterialListAllState() {
        return success(customerMaterialListService.getAllState());
    }


    /**
     * 批量更新客户物料清单的物料状态
     *
     * @param
     * @return
     */
    @PostMapping("/materials/batch/update")
    @OperLog(module = "采购管理", type = OperationType.UPDATE, desc = "批量更新了客户物料清单")
    public ResponseData batchUpdateSupplierMaterials(@RequestBody CustomerMaterialBatchUpdateDTO batchUpdateDTO) {
        customerMaterialListService.batchUpdateSupplierMaterials(batchUpdateDTO);
        return success();
    }

    /**
     * 下载客户物料清单模板
     */
    @GetMapping("/materials/export/template")
    public void exportMaterialListTemplate(HttpServletResponse response) throws IOException {
        customerMaterialListService.exportTemplate(response);
    }

    /**
     * 客户物料清单导出
     *
     * @return
     */
    @PostMapping("/materials/export")
    public void exportCustomerMaterialListList(@RequestBody CustomerMaterialListSelectDTO selectDTO,
                                               HttpServletResponse response) throws IOException {
        selectDTO.setCurrent(1);
        selectDTO.setSize(Integer.MAX_VALUE);
        Page<CustomerMaterialListEntity> page = customerMaterialListService.list(selectDTO);
        EasyExcelUtil.export(response, "客户物料清单", "客户物料清单", page.getRecords(), CustomerMaterialListEntity.class);
    }

    /**
     * 客户物料清单信息导入
     *
     * @return
     */
    @PostMapping("/materials/upload")
    public ResponseData uploadCustomerMaterialList(MultipartFile file) {
        String username = getUsername();
        customerMaterialListService.uploadCustomerMaterialList(file, username);
        return ResponseData.success();
    }

    /**
     * 查询客户物料清单导入进度
     *
     * @return
     */
    @GetMapping("/materials/import/progress")
    public ResponseData getCustomerMaterialListImportProgress() {
        Object obj = redisTemplate.opsForValue().get(RedisKeyPrefix.CUSTOMER_MATERIAL_LIST_IMPORT_PROGRESS);
        return success(obj);
    }

    /**
     * 下载客户档案导入默认模板
     */
    @GetMapping("/export/default/template")
    public void exportExcelDefaultTemplate(HttpServletResponse response) throws Exception {
        customerService.downloadDefaultTemplate("classpath:template/customerTemplate.xlsx", response, "客户档案默认模板" + Constant.XLSX);
    }

    /**
     * 导入 自定义客户档案导入模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入客户档案自定义模板")
    public ResponseData importExcelTemplate(MultipartFile file) {
        customerService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 用户导入的 自定义客户档案导入模板 下载
     */
    @GetMapping("/export/template")
    public void exportExcelTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = customerService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "客户档案数据导入模板" + Constant.XLSX);
    }

    /**
     * 导入管理：下载自定义导入模板
     *
     * @param response
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        InputStream inputStream = customerService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "客户档案自定义数据导入模板" + Constant.XLSX);
    }

    /**
     * 导入excel
     */
    @PostMapping("/import")
    public ResponseData importCustomerExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.CUSTOMER_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        customerService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 获取客户档案导入的进度
     */
    @GetMapping("/import/progress")
    public ResponseData getCustomerCompletenessProgress(@RequestParam(value = "key") String key) {
        Object obj = redisTemplate.opsForValue().get(key);
        return success(obj);
    }

}
