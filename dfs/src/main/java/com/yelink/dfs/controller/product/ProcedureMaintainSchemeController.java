package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.product.ProcedureMaintainSchemeEntity;
import com.yelink.dfs.service.product.ProcedureMaintainSchemeService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * @Description: 工序维修管理
 * @Author: shenzm
 * @Date: 2022/9/20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/procedure/maintain/scheme")
public class ProcedureMaintainSchemeController extends BaseController {

    private ProcedureMaintainSchemeService procedureMaintainSchemeService;

    /**
     * 根据工序id获取工序维修信息
     * @return
     */
    @GetMapping("/getListById/{procedureId}")
    public ResponseData getListById(@PathVariable(value = "procedureId") Integer id) {
        List<ProcedureMaintainSchemeEntity> entities = procedureMaintainSchemeService.getListById(id);
        return success(entities);
    }

    /**
     * 根据工序id获取工序维修信息
     *
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData getDetail(@PathVariable(value = "id") Integer id) {
        ProcedureMaintainSchemeEntity entity = procedureMaintainSchemeService.getById(id);
        return success(entity);
    }

    /**
     * 新增工序维修信息
     *
     * @return
     */
  /*  @PostMapping("/insert")
    public ResponseData insert(@RequestBody ProcedureMaintainSchemeEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        boolean save = procedureMaintainSchemeService.save(entity);
        if (!save) {
            return fail();
        }
        return success();
    }*/

    /**
     * 更新工序工序维修信息
     *
     * @return
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody ProcedureMaintainSchemeEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateTime(new Date());
        boolean b = procedureMaintainSchemeService.updateById(entity);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 根据工序id删除对应工序维修信息
     *
     * @return
     */
    @DeleteMapping("/deleteByProcedureId/{procedureId}")
    public ResponseData deleteByProcedureId(@PathVariable(value = "procedureId") Integer id) {
        boolean b = procedureMaintainSchemeService.deleteByProcedureId(id);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 根据工序质检id删除工序维修信息
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData delete(@PathVariable(value = "id") Integer id) {
        boolean b = procedureMaintainSchemeService.removeById(id);
        if (!b) {
            return fail();
        }
        return success();
    }

    /**
     * 新增、修改、删除工序维修信息
     *
     * @return
     */
    @PostMapping("/insertOrDelete")
    public ResponseData insertOrDelete(@RequestBody ProcedureMaintainSchemeEntity entity) {
        entity.setCreateBy(getUsername());
        entity.setUpdateBy(getUsername());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        boolean b = procedureMaintainSchemeService.insertOrDelete(entity);
        if (!b) {
            return fail();
        }
        return success();
    }


}
