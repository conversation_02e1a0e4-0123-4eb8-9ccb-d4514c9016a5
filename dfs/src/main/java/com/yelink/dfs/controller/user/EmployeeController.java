package com.yelink.dfs.controller.user;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.model.EmployeeEntity;
import com.yelink.dfs.service.model.EmployeeService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Date 2021/3/2 12:30
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/employees")
public class EmployeeController extends BaseController {

    private EmployeeService employeeService;

    /**
     * 分页模糊查询所有员工
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "jobNumber", required = false) String jobNumber,
            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
            @RequestParam(defaultValue = "1", value = "currentPage") int current) {
        return success(employeeService.getList(name, jobNumber, current, pageSize));
    }


    /**
     * 添加员工信息
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody EmployeeEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        boolean save = employeeService.add(entity);
        if (save) {
            return success();
        } else {
            return fail(RespCodeEnum.INSERT_EMPLOYEE_FAIL);
        }
    }

    /**
     * 修改员工信息
     *
     * @param entity
     * @return
     */
    @PostMapping("/update")
    public ResponseData update(@RequestBody EmployeeEntity entity) {
        boolean update = employeeService.upById(entity);
        if (update) {
            return success();
        } else {
            return fail(RespCodeEnum.EXECUTE_FAIL2UP);
        }
    }

    /**
     * 通过Id删除员工
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteById(@PathVariable Integer id) {
        if (!employeeService.empdIsExist(id)) {
            return fail(RespCodeEnum.EMPLOYEE_NOEXIST);
        } else {
            employeeService.removeById(id);
            return success();
        }

    }

    /**
     * 通过ID查询员工信息
     *
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public ResponseData selectById(@PathVariable Integer id) {
        if (!employeeService.empdIsExist(id)) {
            return fail(RespCodeEnum.ROLE_NOEXIST);
        } else {
            EmployeeEntity entity = employeeService.getEmpById(id);
            return success(entity);
        }
    }

}
