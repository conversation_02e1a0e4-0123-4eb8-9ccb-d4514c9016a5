package com.yelink.dfs.controller.screen;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.dfs.entity.screen.RecordComponentDataEntity;
import com.yelink.dfs.entity.screen.dto.ProductionAlarmDTO;
import com.yelink.dfs.entity.screen.dto.StateOverviewDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.screen.ProductionService;
import com.yelink.dfs.service.screen.RecordComponentDataService;
import com.yelink.dfscommon.api.dfs.HomeScreenInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.dto.ScreenOutputDTO;
import com.yelink.dfscommon.entity.RecordOutputEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 生产大屏接口
 * @time 2021/7/19 18:46
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/screens/production")
public class ProductionController {
    private ProductionService productionService;
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    private DictService dictService;
    private RecordComponentDataService recordComponentDataService;
    private HomeScreenInterface homeScreenInterface;

    /**
     * 拿到组件历史数据
     *
     * @param componentName
     * @param dateIn
     * @return
     */
    private Object getHistoryData(String componentName, String dateIn) {
        Date date = DateUtil.formatToDate(DateUtil.parse(dateIn, DateUtil.DATETIME_FORMAT), DateUtil.DATETIME_FORMAT_ZERO);
        LambdaQueryWrapper<RecordComponentDataEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RecordComponentDataEntity::getTime, date).eq(RecordComponentDataEntity::getName, componentName);
        RecordComponentDataEntity recordComponentDataEntity = recordComponentDataService.getOne(lambdaQueryWrapper);
        return recordComponentDataEntity == null ? null : recordComponentDataEntity.getData();
    }

    /**
     * 判断是否是历史数据请求
     *
     * @param componentName
     * @param dateIn
     * @return
     */
    private Boolean isHistory(String componentName, String dateIn) {
        if (StringUtils.isBlank(dateIn) || StringUtils.isBlank(componentName)) {
            return false;
        }
        Date startDate = dictService.getRecordDate(new Date());
        Date date = DateUtil.parse(dateIn, DateUtil.DATETIME_FORMAT);
        return date.compareTo(startDate) < 0;
    }

    private Date getDate(String dateIn) {
        Date date;
        if (StringUtils.isBlank(dateIn)) {
            date = new Date();
        } else {
            date = DateUtil.parse(dateIn, DateUtil.DATETIME_FORMAT);
        }
        return date;
    }

    /**
     * 生产大屏当天状态总览
     * 此处lineId有可能为多个产线ID拼接，2.16.2版本需求要将状态总览改为查询多个产线，为了不影响现网已使用此接口的环境，依旧使用lineId字段
     *
     * @return
     */
    @GetMapping("/information")
    public ResponseData workOrderInformation(@RequestParam(value = "date", required = false) String dateIn,
                                             @RequestParam(value = "lineId", required = false) String lineIds) {
        Date date = DateUtil.parse(dateIn, DateUtil.DATETIME_FORMAT);
        return ResponseData.success(productionService.workOrderInformation(lineIds, date));
    }

    /**
     * 生产大屏生产计划与进度
     *
     * @return
     */
    @GetMapping("/workOrder/list")
    public ResponseData workOrderList(@RequestParam(required = false) Integer lineId,
                                      @RequestParam(required = false) String gridIds,
                                      @RequestParam(value = "current", required = false) Integer current,
                                      @RequestParam(value = "size", required = false) Integer size) {
        if (lineId != null) {
            return homeScreenInterface.listWorkOrder(lineId, current, size);
        }
        return ResponseData.success(productionService.listWorkOrder(gridIds, current, size));
    }

    /**
     * 生产大屏获取历史(昨天之前)挂起工单数量
     *
     * @return
     */
    @GetMapping("/hand/up/workOrder/count")
    @Cacheable(cacheNames = "dfs_screen_hand_up_work_order_count", key = "'lineId=' + #lineId + ',componentName=' + #componentName + ',dateIn=' + #dateIn")
    public ResponseData handUpWorkOrderCount(@RequestParam(value = "componentName", required = false) String componentName,
                                             @RequestParam(value = "date", required = false) String dateIn,
                                             @RequestParam(value = "lineId", required = false) Integer lineId) {
        if (isHistory(componentName, dateIn)) {
            return ResponseData.success(this.getHistoryData(componentName, dateIn));
        }
        Date date = getDate(dateIn);
        return ResponseData.success(productionService.handUpWorkOrderCount(lineId, date));
    }


    /**
     * 计划完成率
     *
     * @return
     */
    @GetMapping("/plan/rate")
    @Cacheable(cacheNames = "dfs_screen_plan_rate", key = "'lineId=' + #lineId + ',componentName=' + #componentName + ',dateIn=' + #dateIn")
    public ResponseData getPlanRate(@RequestParam(value = "componentName", required = false) String componentName,
                                    @RequestParam(value = "date", required = false) String dateIn,
                                    @RequestParam(value = "lineId", required = false) Integer lineId) {
        if (isHistory(componentName, dateIn)) {
            return ResponseData.success(this.getHistoryData(componentName, dateIn));
        }
        Date date = getDate(dateIn);
        List<RecordOutputEntity> list = productionService.getPlanRate(date, lineId);
        return ResponseData.success(list);
    }

    /**
     * 工单达成率
     * 需求：增加新组件“工单达成率”，按照现有逻辑只展示工单的达成率统计折线图
     *
     * @return
     */
    @GetMapping("/order/achieve")
    @Cacheable(cacheNames = "dfs_screen_work_order_achieve", key = "'lineId=' + #lineId + ',componentName=' + #componentName + ',dateIn=' + #dateIn")
    public ResponseData getOrderReach(@RequestParam(value = "componentName", required = false) String componentName,
                                      @RequestParam(value = "date", required = false) String dateIn,
                                      @RequestParam(value = "lineId", required = false) Integer lineId) {
        if (isHistory(componentName, dateIn)) {
            return ResponseData.success(this.getHistoryData(componentName, dateIn));
        }
        Date date = getDate(dateIn);
        List<RecordOutputEntity> list = productionService.getOrderAchieveRate(date, lineId);
        return ResponseData.success(list);
    }

    /**
     * 产量统计
     *
     * @return
     */
    @GetMapping("/output/list")
    public ResponseData getRecordOutput(@RequestParam(required = false) Integer lineId,
                                        @RequestParam(required = false) Integer gridId) {
        //每日产量，需要按每个产品的双单位进行换算，取第一个产品的单位展示
        ScreenOutputDTO result = recordWorkOrderDayCountService.getRecordOutput(lineId, gridId);
        return ResponseData.success(result);
    }

    /**
     * 告警汇总
     *
     * @return
     */
    @GetMapping("/alarm/list")
    public ResponseData getAlarm(@RequestParam(required = false) Integer lineId) {
        String lineIds = Objects.isNull(lineId) ? null : String.valueOf(lineId);
        ProductionAlarmDTO productionAlarmDTO = productionService.getAlarmCount(lineIds, null);
        return ResponseData.success(productionAlarmDTO);
    }


    /**
     * 每个车间对应的良率&oee
     *
     * @return
     */
    @GetMapping("/grid/yield/oee")
    public ResponseData getYieldAndOee(@RequestParam(value = "componentName", required = false) String componentName,
                                       @RequestParam(value = "date", required = false) String dateIn,
                                       @RequestParam(value = "gridId", required = false) Integer gridId) {
        if (isHistory(componentName, dateIn)) {
            return ResponseData.success(this.getHistoryData(componentName, dateIn));
        }
        Date date = getDate(dateIn);
        return ResponseData.success(productionService.getYieldAndOee(date, gridId));
    }


    /**
     * 车间节拍
     *
     * @return
     */
    @GetMapping("/grid/beat")
    public ResponseData getGridBeat(@RequestParam(value = "componentName", required = false) String componentName,
                                    @RequestParam(value = "gridId", required = false) Integer gridId,
                                    @RequestParam(value = "date", required = false) String dateIn,
                                    @RequestParam(value = "timeType") String timeType) {
        Date date = getDate(dateIn);
        return ResponseData.success(productionService.getGridBeat(timeType, date, gridId));
    }

    /**
     * 获取近七天生产的不良数和完成量(yml可配置天数)-产量统计
     *
     * @param
     * @return
     */
    @GetMapping("/unqualified/finish/count")
    public ResponseData getUnqualifiedAndFinishCountOnDay(@RequestParam(value = "componentName", required = false) String componentName,
                                                          @RequestParam(value = "date", required = false) String dateIn) {
        if (isHistory(componentName, dateIn)) {
            return ResponseData.success(this.getHistoryData(componentName, dateIn));
        }
        return ResponseData.success(productionService.getUnqualifiedAndFinishCountOnDay(dateIn));
    }


    /**
     * 获取维修项目top5
     *
     * @param lineId 多个产线逗号分割
     * @param topNum 显示top数量
     */
    @GetMapping("/product/maintenance")
    public ResponseData getProductMaintenance(@RequestParam(required = false) String lineId,
                                              @RequestParam(required = false) Integer topNum) {
        List<Integer> lineIdList = StringUtils.isNotBlank(lineId) ? Arrays.stream(lineId.split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList()) : null;
        return ResponseData.success(productionService.getProductUnqualified(lineIdList, topNum));
    }


    /**
     * 康居人产线大屏
     */

    /**
     * 获取今日数据
     */
    @GetMapping("/get/today/data")
    public ResponseData getTodayData(@RequestParam(value = "lineId", required = false) Integer lineId) {
        return ResponseData.success(productionService.getTodayData(lineId));
    }

    /**
     * 获取工序节拍列表
     */
    @GetMapping("/list/fac/beat")
    public ResponseData listFacBeat(@RequestParam(value = "lineId", required = false) Integer lineId) {
        return ResponseData.success(productionService.listFacilitiesBeat(lineId));
    }

    /**
     * 获取正在生产工单
     *
     * @param lineId
     * @return
     */
    @GetMapping("/get/work/one/in/work")
    public ResponseData getWorkOrderOneInWork(@RequestParam(value = "lineId", required = false) Integer lineId) {
        return ResponseData.success(productionService.getWorkOrderOneInWork(lineId));
    }

    /**
     * 获取产量等级列表
     *
     * @param lineId
     * @return
     */
    @GetMapping("/list/report/grade")
    public ResponseData listReportGrade(@RequestParam(value = "lineId", required = false) Integer lineId) {
        return ResponseData.success(productionService.listReportGrade(lineId));
    }


    /**
     * 获取指定日期每天的产出
     *
     * @param gridId
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("/days/output")
    public ResponseData getDaysOutputByDate(@RequestParam(value = "gridId", required = false) Integer gridId,
                                            @RequestParam(value = "startDate") String startDate,
                                            @RequestParam(value = "endDate") String endDate) {
        Date start = DateUtil.parse(startDate, DateUtil.DATE_FORMAT);
        Date end = DateUtil.parse(endDate, DateUtil.DATE_FORMAT);
        return ResponseData.success(productionService.getDaysOutputByDate(gridId, start, end));
    }

    /**
     * 能耗总览组件
     *
     * @param gridId
     * @return
     */
    @GetMapping("/overview/energy/consumption")
    public ResponseData overviewOfEnergyConsumption(@RequestParam(value = "gridId", required = false) Integer gridId) {
        return ResponseData.success(productionService.overviewOfEnergyConsumption(gridId));
    }


    /**
     * 车间生产大屏当天状态总览
     *
     * @return
     */
    @GetMapping("/grid/information")
    @Cacheable(cacheNames = "dfs_screen_information", key = "'gridId=' + #gridId + ',componentName=' + #componentName + ',date=' +  #date")
    public ResponseData gridInformation(@RequestParam(value = "componentName", required = false) String componentName,
                                        @RequestParam(value = "date", required = false) String date,
                                        @RequestParam(value = "gridId") Integer gridId) {
        if (isHistory(componentName, date)) {
            return ResponseData.success(this.getHistoryData(componentName, date));
        }
        StateOverviewDTO dto = productionService.gridWorkOrderInformation(gridId, date);
        return ResponseData.success(dto);
    }

    /**
     * 生产大屏获取历史(昨天之前)挂起工单数量
     *
     * @return
     */
    @GetMapping("/grid/hand/up/workOrder/count")
    @Cacheable(cacheNames = "dfs_screen_hand_up_work_order_count", key = "'gridId=' + #gridId + ',componentName=' + #componentName + ',date=' +  #date")
    public ResponseData gridHandUpWorkOrderCount(@RequestParam(value = "componentName", required = false) String componentName,
                                             @RequestParam(value = "date", required = false) String date,
                                             @RequestParam(value = "gridId") Integer gridId) {
        if (isHistory(componentName, date)) {
            return ResponseData.success(this.getHistoryData(componentName, date));
        }
        return ResponseData.success(productionService.gridHandUpOrderCount(gridId, date));
    }

}
