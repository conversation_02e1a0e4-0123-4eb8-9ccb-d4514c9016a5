package com.yelink.dfs.controller.statement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.ReportType;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.dto.DebugMachineRecordEntityDTO;
import com.yelink.dfs.entity.event.EventRecordEntity;
import com.yelink.dfs.entity.feed.FeedRecordEntity;
import com.yelink.dfs.entity.maintain.dto.MaintainStatementDTO;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.order.CustomerEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.StaffPieceWorkTimeEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.dto.OperationOrderDTO;
import com.yelink.dfs.entity.order.dto.OperationOrderReportLineDTO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.statement.dto.MachineSpeedRecordEntityDTO;
import com.yelink.dfs.entity.statement.dto.ProductionDailyDTO;
import com.yelink.dfs.entity.statement.dto.ProductionDailyResultDTO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.target.metrics.MetricsDeviceOeeEntity;
import com.yelink.dfs.entity.target.metrics.MetricsDeviceStateDailyEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.work.calendar.WorkCalendarEntity;
import com.yelink.dfs.entity.work.calendar.dto.TimeRangeDTO;
import com.yelink.dfs.mapper.energy.manage.DeviceTimePeriodEnergyConsumptionMapper;
import com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.open.v1.supplier.dto.SupplierSelectDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.energy.manage.DeviceTimePeriodEnergyConsumptionService;
import com.yelink.dfs.service.event.EventRecordService;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.StaffPieceWorkTimeService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.target.metrics.MetricsDeviceOeeService;
import com.yelink.dfs.service.target.metrics.MetricsDeviceStateDailyService;
import com.yelink.dfs.service.target.record.RecordLineDayUnionService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfs.utils.MD5Util;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.api.packaging.PackagingInterface;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.api.wms.StockRelateOrderInterface;
import com.yelink.dfscommon.api.wms.StockWarehouseInterface;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.OrderShowTypeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.StoreStateEnum;
import com.yelink.dfscommon.constant.dfs.code.FeedStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.wms.StockInputOrOutputTypeEnum;
import com.yelink.dfscommon.dto.DateRangeReq;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.wms.StockInAndOutSelectDTO;
import com.yelink.dfscommon.dto.wms.StockWarehouseSelectDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.entity.wms.StockRelateOrderEntity;
import com.yelink.dfscommon.entity.wms.WarehouseEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 多线程-日报导出管理
 *
 * <AUTHOR>
 * @Date 2022/6/22 16:22
 * @insert liangyurong : 产线产量导出，车间用电导出，设备运行信息导出
 */
@Slf4j
@Component
public class DailyExportThreadManager {


    @Resource
    private WorkCalendarService workCalendarService;
    @Resource
    private ProductionLineService productionLineService;
    @Resource
    private ModelService modelService;
    @Resource
    private GridService gridService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OrderWorkOrderService orderWorkOrderRelationService;
    @Resource
    private ReportLineService reportLineService;
    @Resource
    private MaterialService materialService;
    @Resource
    private AlarmService alarmService;
    @Resource
    private PackagingInterface packagingInterface;
    @Resource
    private EventRecordService eventRecordService;
    @Resource
    private AssignmentInterface assignmentInterface;
    @Resource
    private StockWarehouseInterface warehouseInterface;
    @Resource
    private StockInAndOutInterface stockInAndOutInterface;
    @Resource
    private StockRelateOrderInterface stockRelateOrderInterface;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    @Resource
    private DeviceTimePeriodEnergyConsumptionService deviceTimePeriodEnergyConsumptionService;
    @Resource
    private MetricsDeviceOeeService metricsDeviceOeeService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private FeedRecordService feedRecordService;
    @Resource
    private StaffPieceWorkTimeService staffPieceWorkTimeService;
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private CustomerService customerService;
    @Resource
    private RecordLineDayUnionService lineDayUnionService;
    @Resource
    private MetricsDeviceStateDailyService metricsDeviceStateDailyService;
    @Resource
    private MaintainRecordService maintainRecordService;
    @Resource
    private RecordWorkOrderCountService recordWorkOrderCountService;
    @Resource
    private ProductFlowCodeRecordService productFlowCodeRecordService;
    @Resource
    private WorkOrderPlanService workOrderPlanService;
    @Resource
    private ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    private SupplierService supplierService;

    private final SimpleDateFormat dateTimeFormatter = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
    private final SimpleDateFormat dateFormatter = new SimpleDateFormat(DateUtil.DATE_FORMAT);
    /**
     * 临时文件存储路径
     */
    private String temFilePath;

    private Double totalProcess;
    private Workbook workbook;

    private FutureTask<String> lineFutureTask;
    private Thread lineThread;

    private Map<String, FutureTask<String>> codeFutureTaskMap;

    @PostConstruct
    private void init() {
        codeFutureTaskMap = new HashMap<>(8);
    }

    /**
     * 导出产线统计指标
     */
    @Deprecated
    public void exportLine(String yearAndMonth, String fileUrl, HttpServletResponse response) {
        lineFutureTask = new FutureTask<>(() -> {
            File templateFile = null;

            try (ServletOutputStream responseOutputStream = response.getOutputStream();) {
                // 临时转储到服务器用于操作
                String temFilePath = dumpDailyTemplate(fileUrl);
                templateFile = new File(temFilePath);

                Workbook lineWorkbook = lineDayUnionService.exportListFile(yearAndMonth, templateFile);

                // 打开工作簿时应用程序是否应执行完全重新计算。
                lineWorkbook.setForceFormulaRecalculation(true);
                // 强行刷新单元格公式 计算公式保存结果，但不改变公式
                XSSFFormulaEvaluator.evaluateAllFormulaCells(lineWorkbook);

                String fileName = URLEncoder.encode(yearAndMonth + templateFile.getName(), StandardCharsets.UTF_8.name());
                response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"; filename*=utf-8'zh_cn'" + fileName);
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                lineWorkbook.write(responseOutputStream);
                responseOutputStream.flush();
            } catch (Exception e) {
                log.error("导出失败, ", e);
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.getWriter().write(JSONObject.toJSONString(ResponseData.fail()));
            } finally {
                // 最后需要将临时文件删除
                if (templateFile != null) {
                    FileUtils.forceDelete(templateFile);
                }
            }
            return "";
        });
        lineThread = new Thread(lineFutureTask);
        lineThread.start();
    }

    @Deprecated
    public void lineExportJoin() {
        try {
            lineThread.join();
        } catch (InterruptedException e) {
            log.error("join error, ", e);
        }
    }
    @Deprecated
    public void lineExportStop() {
        if (lineFutureTask == null || lineFutureTask.isDone()) {
            log.info("未进行导出操作或不在本机器执行,无法停止");
            return;
        }
        lineFutureTask.cancel(true);
    }


    /**
     * 导出日报
     */
    @Deprecated
    public void exportDaily(ProductionDailyDTO productionDailyDTO, String temFilePath) {
        this.temFilePath = temFilePath;
        FutureTask<String> dailyFutureTask = new FutureTask<>(() -> {
            // 临时存储的文件路径
            File file = new File(temFilePath);
            // 要统计的表单数, 用于统计
            int total = countFormsNum(productionDailyDTO);
            try {
                workbook = new XSSFWorkbook(new FileInputStream(file));
                // 需要写入的Excel文件
                workbook.setForceFormulaRecalculation(true);

                if (total == 0) {
                    log.info("未选择对应需要导出的表单");
                    throw new ResponseException("未选择对应需要导出的表单");
                }
                // 每一项占的比例
                double eachProcess = MathUtil.divideDouble(1, total, 2);
                this.totalProcess = 0.0;

                // 使用说明, 指定日期段
                setUseInstructions(productionDailyDTO.getGlobalConfigTime());

                if (productionDailyDTO.getSaleOrderReq() != null) {
                    // 销售订单
                    saleOrderExport(productionDailyDTO.getSaleOrderReq(), eachProcess);
                }
                if (productionDailyDTO.getProductionOrderReq() != null) {
                    // 生产订单
                    productionOrderExport(productionDailyDTO.getProductionOrderReq(), eachProcess);
                }
                // 生产工单
                if (productionDailyDTO.getProductionWorkOrderReq() != null) {
                    productionWorkOrderExport(productionDailyDTO.getProductionWorkOrderReq(), eachProcess);
                }
                // 生产工单报工
                if (productionDailyDTO.getProductionWorkOrderReportReq() != null) {
                    productionWorkOrderReportExport(productionDailyDTO.getProductionWorkOrderReportReq(), eachProcess);
                }
                // 生产工单异常 - 告警
                if (productionDailyDTO.getProductionWorkOrderErrorReq() != null) {
                    productionWorkOrderErrorExport(productionDailyDTO.getProductionWorkOrderErrorReq(), eachProcess);
                }
                // 生产工单调机
                if (productionDailyDTO.getProductionWorkMonotonerReq() != null) {
                    productionWorkMonotonerExport(productionDailyDTO.getProductionWorkMonotonerReq(), eachProcess);
                }
                // 生产工单特殊参数, 至美日报需求
                if (productionDailyDTO.getProductionWorkSpecialReq() != null) {
                    productionWorkSpecialExport(productionDailyDTO.getProductionWorkSpecialReq(), eachProcess);
                }

                // 作业工单
                if (productionDailyDTO.getOperationOrderReq() != null) {
                    operationOrderExport(productionDailyDTO.getOperationOrderReq(), eachProcess);
                }
                // 作业工单报工
                if (productionDailyDTO.getOperationOrderReportReq() != null) {
                    operationOrderReportExport(productionDailyDTO.getOperationOrderReportReq(), eachProcess);
                }
                // 班次
                if (productionDailyDTO.getShiftReq() != null) {
                    shiftExport(productionDailyDTO.getShiftReq(), eachProcess);
                }
                // 工单出入库
                if (productionDailyDTO.getWorkOrderInOutReq() != null) {
                    workOrderInOutExport(productionDailyDTO.getWorkOrderInOutReq(), eachProcess);
                }
                // 生产工单上料记录
                if (productionDailyDTO.getProductionWorkFeedReq() != null) {
                    productionWorkFeedExport(productionDailyDTO.getProductionWorkFeedReq(), eachProcess);
                }
                // 人员计件计时
                if (productionDailyDTO.getStaffPieceWorkTimeReq() != null) {
                    staffPieceWorkTimeExport(productionDailyDTO.getStaffPieceWorkTimeReq(), eachProcess);
                }
                // 产线产量
                if (productionDailyDTO.getProductionLineOutputReq() != null) {
                    productionLineOutputExport(productionDailyDTO.getProductionLineOutputReq(), eachProcess);
                }
                // 车间用电
                if (productionDailyDTO.getDeviceUseElectricityReq() != null) {
                    deviceUseElectricityExport(productionDailyDTO.getDeviceUseElectricityReq(), eachProcess);
                }
                // 设备运行信息
                if (productionDailyDTO.getDeviceRunReq() != null) {
                    deviceRunExport(productionDailyDTO.getDeviceRunReq(), eachProcess);
                }
                // 工单每日生产表
                if (productionDailyDTO.getWorkOrderDailyProductReq() != null) {
                    workOrderDailyProductExport(productionDailyDTO.getWorkOrderDailyProductReq(), eachProcess);
                }
                // 员工每日生产表
                if (productionDailyDTO.getEmployeeDailyProductReq() != null) {
                    employeeDailyProductExport(productionDailyDTO.getEmployeeDailyProductReq(), eachProcess);
                }
                // 工位质检-检测报表
                if (productionDailyDTO.getFacDetectionReq() != null) {
                    facDetectionExport(productionDailyDTO.getFacDetectionReq(), eachProcess);
                }
                // 质检返工-返工报表
                if (productionDailyDTO.getInspectReworkReq() != null) {
                    inspectReworkExport(productionDailyDTO.getInspectReworkReq(), eachProcess);
                }

                // 打开工作簿时应用程序是否应执行完全重新计算。
                workbook.setForceFormulaRecalculation(true);
                // 强行刷新单元格公式 计算公式保存结果，但不改变公式
                XSSFFormulaEvaluator.evaluateAllFormulaCells(workbook);

                // 操作完后的Excel 保存到临时文件中
                FileOutputStream fileOutputStream = new FileOutputStream(file);
                workbook.write(fileOutputStream);
                fileOutputStream.flush();
                fileOutputStream.close();
                // 因为最后这个导出成功的临时文件是存储在本地服务器,如果存在双机环境可能获取不到,
                // 所以将这个文件传到 fast dfs服务器,下一个接口获取的时候从fast dfs获取
                MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(), "text/plain", new FileInputStream(temFilePath));
                fastDfsClientService.uploadFile(multipartFile, null, temFilePath);
                // 执行完成
                cacheProgress(1, true);
            } catch (Exception e) {
                log.error("导出失败,", e);
                // 执行失败
                ImportProgressDTO exportProgress = ImportProgressDTO.builder()
                        .executionDescription("导出失败")
                        .executionStatus(true)
                        .build();
                redisTemplate.opsForValue().set(RedisKeyPrefix.PRODUCTION_DAILY_PROGRESS + ":" + temFilePath, exportProgress, 1, TimeUnit.HOURS);
            } finally {
                if (workbook != null) {
                    workbook.close();
                }
                // 最后需要将本服务器临时文件删除
                if (file.exists()) {
                    FileUtils.forceDelete(file);
                }
                codeFutureTaskMap.remove(MD5Util.getMD5(temFilePath));
            }
            return null;
        });
        new Thread(dailyFutureTask).start();
        codeFutureTaskMap.put(MD5Util.getMD5(temFilePath), dailyFutureTask);
    }

    @Deprecated
    private void inspectReworkExport(ProductionDailyDTO.InspectReworkReq inspectReworkReq, double eachProcess) throws NoSuchFieldException, IllegalAccessException {
        // 查询结果
        List<ProductionDailyResultDTO.InspectReworkRecord> records = new ArrayList<>();

        String reportStartTime = Objects.isNull(inspectReworkReq.getReportTime()) ? null : DateUtil.format(inspectReworkReq.getReportTime().getStartTime(), DateUtil.DATETIME_FORMAT);
        String reportEndTime = Objects.isNull(inspectReworkReq.getReportTime()) ? null : DateUtil.format(inspectReworkReq.getReportTime().getEndTime(), DateUtil.DATETIME_FORMAT);

        Page<MaintainStatementDTO> page = maintainRecordService.getStatement(null, null, null, null, reportStartTime, reportEndTime, 1, Integer.MAX_VALUE);
        for (MaintainStatementDTO record : page.getRecords()) {
            records.add(ProductionDailyResultDTO.InspectReworkRecord.builder()
                    .workOrderNumber(record.getWorkOrder())
                    .workOrderName(record.getWorkOrderName())
                    .saleOrderNumber(record.getSaleOrderNumber())
                    .productOrderNumber(record.getProductOrderNumber())
                    .materialCode(record.getMaterialCode())
                    .materialName(record.getMaterialName())
                    .standard(record.getStandard())
                    .factoryModel(record.getFactoryModel())
                    .lineName(record.getLineName())
                    .facName(record.getFname())
                    .finishCount(record.getFinishCount())
                    .maintainCount(record.getMaintainCount())
                    .reportDate(record.getDate())
                    .build());
        }

        // 填充excel表头顺序的属性字段名称
        final String[] sortAttributeFields = new String[]{
                "workOrderNumber", "workOrderName", "productOrderNumber", "saleOrderNumber", "materialCode",
                "materialName", "standard", "factoryModel", "lineName", "facName",
                "finishCount", "maintainCount", "reportDate"
        };
        // 填充Excel内容
        setExcelContent("返工报表-原始数据", eachProcess, records, ProductionDailyResultDTO.InspectReworkRecord.class, sortAttributeFields);
    }

    @Deprecated
    private void facDetectionExport(ProductionDailyDTO.FacDetectionReq facDefectReq, double eachProcess) throws NoSuchFieldException, IllegalAccessException {
        // 查询结果
        List<ProductionDailyResultDTO.FacDetectionRecord> records = new ArrayList<>();

        String startTime = Objects.isNull(facDefectReq.getProductTime()) ? null : DateUtil.format(facDefectReq.getProductTime().getStartTime(), DateUtil.DATETIME_FORMAT);
        String endTime = Objects.isNull(facDefectReq.getProductTime()) ? null : DateUtil.format(facDefectReq.getProductTime().getEndTime(), DateUtil.DATETIME_FORMAT);

        Page<RecordWorkOrderCountEntity> page = recordWorkOrderCountService.getQualityAnalysis(null, null, null, null, startTime, endTime, 1, Integer.MAX_VALUE);
        for (RecordWorkOrderCountEntity record : page.getRecords()) {
            MaterialEntity material = Objects.nonNull(record.getMaterialFields()) ? record.getMaterialFields() : new MaterialEntity();

            records.add(ProductionDailyResultDTO.FacDetectionRecord.builder()
                    .workOrderNumber(record.getWorkOrderNumber())
                    .workOrderName(record.getWorkOrderName())
                    .saleOrderNumber(record.getSaleOrderNumber())
                    .productOrderNumber(record.getProductOrderNumber())
                    .materialCode(record.getMaterialCode())
                    .materialName(material.getName())
                    .standard(material.getStandard())
                    .factoryModel(material.getFactoryModel())
                    .lineName(record.getLineName())
                    .facName(record.getFname())
                    .shiftType(record.getShiftType())
                    .checkedQuantity(record.getCheckedQuantity())
                    .qualifiedNumber(record.getQualifiedNumber())
                    .directRate(record.getDirectRate())
                    .defectiveQuantity(record.getDefectiveQuantity())
                    .defectiveRate(record.getDefectiveRate())
                    .repairQualifiedNumber(record.getRepairQualifiedNumber())
                    .finalQualifiedNumber(record.getFinalQualifiedNumber())
                    .finalQualifiedRate(record.getFinalQualifiedRate())
                    .recordDate(record.getRecordDate())
                    .build());
        }

        // 填充excel表头顺序的属性字段名称
        final String[] sortAttributeFields = new String[]{
                "workOrderNumber", "workOrderName", "productOrderNumber", "saleOrderNumber", "materialCode",
                "materialName", "standard", "factoryModel", "lineName", "facName",
                "shiftType", "checkedQuantity", "qualifiedNumber", "directRate", "defectiveQuantity",
                "defectiveRate", "repairQualifiedNumber", "finalQualifiedNumber", "finalQualifiedRate",
                "recordDate"
        };
        // 填充Excel内容
        setExcelContent("检测报表-原始数据", eachProcess, records, ProductionDailyResultDTO.FacDetectionRecord.class, sortAttributeFields);
    }

    @Deprecated
    private void employeeDailyProductExport(ProductionDailyDTO.EmployeeDailyProductReq req, double eachProcess) throws NoSuchFieldException, IllegalAccessException {
        // 查询结果
        List<ProductionDailyResultDTO.EmployeeDailyProductRecord> records = new ArrayList<>();

        Date startTime = Objects.isNull(req.getProductTime()) ? null : req.getProductTime().getStartTime();
        // yyyy-MM-dd 59:59:59
        Date endTime = Objects.isNull(req.getProductTime()) ? null : DateUtil.getEndTimeOfCurrentDay(req.getProductTime().getEndTime());

        boolean condition = Objects.nonNull(startTime) && Objects.nonNull(endTime);

        // yyyy-MM-dd
        SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.DATE_FORMAT);

        // 建议数据从过站记录中获取，统一查询对应时间内的生产工单，然后通过人员工位进行分组汇
        List<ProductFlowCodeRecordEntity> allFlowCodeRecords = productFlowCodeRecordService.lambdaQuery()
                .between(condition, ProductFlowCodeRecordEntity::getReportTime, startTime, endTime)
                .list();
        // 按天分组
        Map<String, List<ProductFlowCodeRecordEntity>> dateFlowCodeRecordsMap = allFlowCodeRecords.stream().collect(Collectors.groupingBy(res -> formatter.format(res.getReportTime())));

        // 工单分日计划
        List<WorkOrderPlanEntity> allWorkOrderPlans = workOrderPlanService.lambdaQuery()
                .between(condition, WorkOrderPlanEntity::getTime, startTime, endTime)
                .list();
        // 按天分组
        Map<String, List<WorkOrderPlanEntity>> dateWorkOrderPlansMap = allWorkOrderPlans.stream().collect(Collectors.groupingBy(res -> formatter.format(res.getTime())));

        // 查询产品名称
        Map<Integer, String> facIdNameMap = new HashMap<>(16), modelIdNameMap = new HashMap<>(16);
        Map<String, String> userNicknameMap = new HashMap<>(16);
        Map<Integer, ProductionLineEntity> lineIdMap = new HashMap<>(16);
        Map<String, MaterialEntity> codeMaterialMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(allFlowCodeRecords)) {
            codeMaterialMap = materialService.lambdaQuery()
                    .in(MaterialEntity::getCode, allFlowCodeRecords.stream().map(ProductFlowCodeRecordEntity::getMaterialCode).collect(Collectors.toSet()))
                    .list()
                    .stream()
                    .collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
            List<ProductionLineEntity> list = productionLineService.lambdaQuery()
                    .in(ProductionLineEntity::getProductionLineId, allFlowCodeRecords.stream().map(ProductFlowCodeRecordEntity::getLineId).collect(Collectors.toSet()))
                    .list();
            Set<Integer> modelIds = list.stream().map(ProductionLineEntity::getModelId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(modelIds)) {
                modelIdNameMap = modelService.lambdaQuery()
                        .select(ModelEntity::getId, ModelEntity::getName)
                        .in(ModelEntity::getId, modelIds)
                        .list()
                        .stream()
                        .collect(Collectors.toMap(ModelEntity::getId, ModelEntity::getName));
            }
            lineIdMap = list.stream()
                    .collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, v -> v));
            facIdNameMap = facilitiesService.lambdaQuery()
                    .select(FacilitiesEntity::getFid, FacilitiesEntity::getFname)
                    .in(FacilitiesEntity::getFid, allFlowCodeRecords.stream().map(ProductFlowCodeRecordEntity::getFacId).collect(Collectors.toSet()))
                    .list()
                    .stream()
                    .collect(Collectors.toMap(FacilitiesEntity::getFid, FacilitiesEntity::getFname));

            Set<String> userNames = allFlowCodeRecords.stream().map(ProductFlowCodeRecordEntity::getReportBy).collect(Collectors.toSet());
            userNicknameMap = sysUserService.getListByUserNames(new ArrayList<>(userNames))
                    .stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        }

        // 最新到最晚排
        Date cur = DateUtil.formatToDate(endTime, DateUtil.DATE_FORMAT);
        while (cur.compareTo(startTime) >= 0) {
            // yyyy-MM-dd
            String formatCur = formatter.format(cur);

            // 分日计划按工单分组, 应该每个工单每天只有一条记录,但明显没校验
            Map<String, List<WorkOrderPlanEntity>> workOrderNumberPlansMap = dateWorkOrderPlansMap.getOrDefault(formatCur, new ArrayList<>()).stream().collect(Collectors.groupingBy(WorkOrderPlanEntity::getWorkOrderNumber));
            // 按工位分组
            Map<Integer, List<ProductFlowCodeRecordEntity>> facIdRecordsMap = dateFlowCodeRecordsMap.getOrDefault(formatCur, new ArrayList<>()).stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getFacId));

            for (Map.Entry<Integer, List<ProductFlowCodeRecordEntity>> facIdRecordsEntry : facIdRecordsMap.entrySet()) {
                List<ProductFlowCodeRecordEntity> tempProductFlowCodeRecords = facIdRecordsEntry.getValue();

                // 又按工单分组
                Map<String, List<ProductFlowCodeRecordEntity>> workOrderNumberRecordsMap = tempProductFlowCodeRecords.stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getRelationNumber));
                for (Map.Entry<String, List<ProductFlowCodeRecordEntity>> entry : workOrderNumberRecordsMap.entrySet()) {
                    String workOrderNumber = entry.getKey();
                    ProductFlowCodeRecordEntity recordEntity = CollectionUtils.isEmpty(entry.getValue()) ? new ProductFlowCodeRecordEntity() : entry.getValue().get(0);
                    ProductionLineEntity line = lineIdMap.getOrDefault(recordEntity.getLineId(), new ProductionLineEntity());
                    MaterialEntity material = codeMaterialMap.getOrDefault(recordEntity.getMaterialCode(), new MaterialEntity());

                    // 按上报人进行分组
                    Map<String, List<ProductFlowCodeRecordEntity>> reportByListMap = entry.getValue().stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getReportBy));

                    for (Map.Entry<String, List<ProductFlowCodeRecordEntity>> reportByListEntry : reportByListMap.entrySet()) {
                        long count = reportByListEntry.getValue().stream().map(ProductFlowCodeRecordEntity::getProductFlowCode).distinct().count();

                        records.add(ProductionDailyResultDTO.EmployeeDailyProductRecord.builder()
                                .productTime(cur)
                                .employeeName(userNicknameMap.get(reportByListEntry.getKey()))
                                .workOrderNumber(workOrderNumber)
                                .materialCode(recordEntity.getMaterialCode())
                                .workOrderPlan(CollectionUtils.isEmpty(workOrderNumberPlansMap.get(workOrderNumber)) ? 0 : workOrderNumberPlansMap.get(workOrderNumber).get(0).getPlanQuantity())
                                .modelTypeName(modelIdNameMap.get(line.getModelId()))
                                .lineName(line.getName())
                                .facName(facIdNameMap.get(recordEntity.getFacId()))
                                .finishCount((double) count)
                                .comp(material.getComp())
                                .build());
                    }
                }
            }
            cur = DateUtil.addDate(cur, -1);
        }

        // 填充excel表头顺序的属性字段名称
        final String[] sortAttributeFields = new String[]{
                "productTime", "employeeName", "workOrderNumber", "materialCode", "workOrderPlan",
                "modelTypeName", "lineName", "facName", "finishCount", "comp"
        };

        // 填充Excel内容
        setExcelContent("员工每日生产表-原始数据", eachProcess, records, ProductionDailyResultDTO.EmployeeDailyProductRecord.class, sortAttributeFields);
    }

    @Deprecated
    private void workOrderDailyProductExport(ProductionDailyDTO.WorkOrderDailyProductReq req, double eachProcess) throws NoSuchFieldException, IllegalAccessException {
        // 查询结果
        List<ProductionDailyResultDTO.WorkOrderDailyProductRecord> records = new ArrayList<>();

        Date startTime = Objects.isNull(req.getProductTime()) ? null : req.getProductTime().getStartTime();
        // yyyy-MM-dd 59:59:59
        Date endTime = Objects.isNull(req.getProductTime()) ? null : DateUtil.getEndTimeOfCurrentDay(req.getProductTime().getEndTime());

        boolean condition = Objects.nonNull(startTime) && Objects.nonNull(endTime);

        // yyyy-MM-dd
        SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.DATE_FORMAT);

        // 工单分日计划
        List<WorkOrderPlanEntity> allWorkOrderPlans = workOrderPlanService.lambdaQuery()
                .between(condition, WorkOrderPlanEntity::getTime, startTime, endTime)
                .list();
        // 按天分组
        Map<String, List<WorkOrderPlanEntity>> dateWorkOrderPlansMap = allWorkOrderPlans.stream().collect(Collectors.groupingBy(res -> formatter.format(res.getTime())));

        // 工单每日产量记录表
        List<RecordWorkOrderDayCountEntity> workOrderDayCounts = recordWorkOrderDayCountService.lambdaQuery()
                .between(RecordWorkOrderDayCountEntity::getTime, startTime, endTime)
                .list();
        // 按天分组
        Map<String, List<RecordWorkOrderDayCountEntity>> dateWorkOrderDayCountsMap = workOrderDayCounts.stream().collect(Collectors.groupingBy(res -> formatter.format(res.getTime())));

        // 工单计数记录 维修数量
        List<RecordWorkOrderCountEntity> workOrderCounts = recordWorkOrderCountService.lambdaQuery()
                .between(RecordWorkOrderCountEntity::getRecordDate, startTime, endTime)
                .list();
        // 按天分组
        Map<String, List<RecordWorkOrderCountEntity>> dateWorkOrderCountsMap = workOrderCounts.stream().collect(Collectors.groupingBy(res -> formatter.format(res.getRecordDate())));


        // 查询产品名称
        Map<Integer, ProductionLineEntity> lineIdMap = new HashMap<>(16);
        Map<String, MaterialEntity> codeMaterialMap = new HashMap<>(16);
        Map<String, WorkOrderEntity> workOrderNumberMap = new HashMap<>(16);
        Map<String, CustomerEntity> workOrderNumberCustomerMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(allWorkOrderPlans)) {
            List<WorkOrderEntity> workOrders = workOrderService.lambdaQuery()
                    .in(WorkOrderEntity::getWorkOrderNumber, allWorkOrderPlans.stream().map(WorkOrderPlanEntity::getWorkOrderNumber).collect(Collectors.toSet()))
                    .list();
            workOrderNumberMap = workOrders.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, v -> v));

            // 物料
            codeMaterialMap = materialService.lambdaQuery()
                    .in(MaterialEntity::getCode, workOrders.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet()))
                    .list()
                    .stream()
                    .collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
            List<ProductionLineEntity> list = productionLineService.lambdaQuery()
                    .in(ProductionLineEntity::getProductionLineId, workOrders.stream().map(WorkOrderEntity::getLineId).collect(Collectors.toSet()))
                    .list();
            lineIdMap = list.stream()
                    .collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, v -> v));
        }

        // 最新到最晚排
        Date cur = DateUtil.formatToDate(endTime, DateUtil.DATE_FORMAT);
        while (cur.compareTo(startTime) >= 0) {
            // yyyy-MM-dd
            String formatCur = formatter.format(cur);

            // 工单每日产量记录按工单分组
            Map<String, RecordWorkOrderDayCountEntity> workOrderNumberDayCountMap = dateWorkOrderDayCountsMap.getOrDefault(formatCur, new ArrayList<>()).stream().collect(Collectors.toMap(RecordWorkOrderDayCountEntity::getWorkOrderNumber, v -> v, (n, o) -> n));
            // 分日计划按工单分组, 应该每个工单每天只有一条记录,但明显没校验
            Map<String, List<WorkOrderPlanEntity>> workOrderNumberPlansMap = dateWorkOrderPlansMap.getOrDefault(formatCur, new ArrayList<>()).stream().collect(Collectors.groupingBy(WorkOrderPlanEntity::getWorkOrderNumber));
            // 按工单分组
            Map<String, List<RecordWorkOrderCountEntity>> workOrderNumberWorkOrderCountsMap = dateWorkOrderCountsMap.getOrDefault(formatCur, new ArrayList<>()).stream().collect(Collectors.groupingBy(RecordWorkOrderCountEntity::getWorkOrderNumber));

            for (Map.Entry<String, RecordWorkOrderDayCountEntity> workOrderDayCount : workOrderNumberDayCountMap.entrySet()) {
                String workOrderNumber = workOrderDayCount.getKey();
                WorkOrderEntity workOrderEntity = workOrderNumberMap.get(workOrderNumber);
                if (Objects.isNull(workOrderEntity)) {
                    continue;
                }
                // 查询工单关联的生产订单或销售订单, 用于查询客户信息
                if (Objects.isNull(workOrderNumberCustomerMap.get(workOrderNumber))) {
                    String customerCode = null;
                    List<OrderWorkOrderEntity> orderWorkOrderRelations = orderWorkOrderRelationService.lambdaQuery()
                            .in(OrderWorkOrderEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
                            .list();
                    List<OrderWorkOrderEntity> relateSaleOrders = orderWorkOrderRelations.stream().filter(res -> OrderNumTypeEnum.SALE_ORDER.getTypeCode().equals(res.getOrderType())).collect(Collectors.toList());
                    List<OrderWorkOrderEntity> relateProductOrders = orderWorkOrderRelations.stream().filter(res -> OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode().equals(res.getOrderType())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(relateSaleOrders)) {
                        // 关联销售订单
                        Integer saleOrderId = relateSaleOrders.get(0).getOrderId();
                        SaleOrderEntity saleOrder = extSaleOrderInterface.selectSaleOrderById(saleOrderId);
                        customerCode = Objects.isNull(saleOrder) ? null : saleOrder.getCustomerCode();
                    } else if (!CollectionUtils.isEmpty(relateProductOrders)) {
                        // 推送至kafka，获取生产订单数据
                        ProductOrderEntity productOrder = extProductOrderInterface.selectProductOrderById(relateProductOrders.get(0).getOrderId());
                        customerCode = Objects.isNull(productOrder) ? null : productOrder.getCustomerCode();
                    }
                    if (Objects.nonNull(customerCode)) {
                        CustomerEntity customer = customerService.lambdaQuery()
                                .eq(CustomerEntity::getCustomerCode, customerCode)
                                .one();
                        if (Objects.nonNull(customer)) {
                            workOrderNumberCustomerMap.put(workOrderNumber, customer);
                        }
                    }
                }

                ProductionLineEntity line = lineIdMap.getOrDefault(workOrderEntity.getLineId(), new ProductionLineEntity());
                MaterialEntity material = codeMaterialMap.getOrDefault(workOrderEntity.getMaterialCode(), new MaterialEntity());
                RecordWorkOrderDayCountEntity dayCount = workOrderNumberDayCountMap.getOrDefault(workOrderNumber, new RecordWorkOrderDayCountEntity());

                double workOrderPlan = CollectionUtils.isEmpty(workOrderNumberPlansMap.get(workOrderNumber)) ? 0 : workOrderNumberPlansMap.get(workOrderNumber).get(0).getPlanQuantity();
                double maintainCount = workOrderNumberWorkOrderCountsMap.getOrDefault(workOrderNumber, new ArrayList<>()).stream().mapToDouble(RecordWorkOrderCountEntity::getMaintainQuantity).sum();

                records.add(ProductionDailyResultDTO.WorkOrderDailyProductRecord.builder()
                        .productTime(cur)
                        .lineName(line.getName())
                        .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                        .materialCode(workOrderEntity.getMaterialCode())
                        .materialName(material.getName())
                        .workOrderPlan(workOrderPlan)
                        .finishCount(dayCount.getCount())
                        .unqualifiedCount(dayCount.getUnqualified())
                        .maintainCount(maintainCount)
                        .comp(material.getComp())
                        .customerName(workOrderNumberCustomerMap.getOrDefault(workOrderNumber, new CustomerEntity()).getCustomerName())
//                        .customerAddress(workOrderNumberCustomerMap.getOrDefault(workOrderNumber, new CustomerEntity()).getCustomerAddr())
                        .build());

            }

            cur = DateUtil.addDate(cur, -1);
        }

        // 填充excel表头顺序的属性字段名称
        final String[] sortAttributeFields = new String[]{
                "productTime", "lineName", "workOrderNumber", "materialCode", "materialName",
                "workOrderPlan", "finishCount", "unqualifiedCount", "maintainCount", "comp",
                "customerName", "customerAddress"
        };
        // 填充Excel内容
        setExcelContent("工单每日生产表-原始数据", eachProcess, records, ProductionDailyResultDTO.WorkOrderDailyProductRecord.class, sortAttributeFields);
    }

    @Deprecated
    public void stopExport(String code) {
        FutureTask<String> dailyFutureTask = codeFutureTaskMap.get(code);
        if (dailyFutureTask == null || dailyFutureTask.isDone()) {
            log.info("未进行导出操作或不在本机器执行,无法停止, code:{}, codeFutureTaskMap:{}", code, codeFutureTaskMap);
            return;
        }
        dailyFutureTask.cancel(true);

        // 最后需要将临时文件删除
        File file = new File(temFilePath);
        if (file.exists()) {
            boolean delete = file.delete();
            log.info("导出日报-终止导出, 删除本地临时文件");
        }
        String fileUrl = fastDfsClientService.getUrlByFileCode(temFilePath);
        if (!StringUtils.isEmpty(fileUrl)) {
            fastDfsClientService.deleteFile(fileUrl);
            log.info("导出日报-终止导出, 删除fast dfs临时文件");
        }
    }


    /**
     * 转储fast dfs文件到服务器上
     * 转储日报模板到服务器
     *
     * @param fileUrl fast dfs文件url
     * @return 临时文件存储路径
     */
    @Deprecated
    public String dumpDailyTemplate(String fileUrl) {
        try {
            // 临时转储到服务器用于操作
            byte[] fileBuffer = fastDfsClientService.getFileStream(fileUrl);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(fileBuffer);
            String dirPath = PathUtils.getAbsolutePath(this.getClass()) + File.separator + "temp";
            File dirFile = new File(dirPath);
            if (!dirFile.exists()) {
                // 不存在目录
                boolean isMkdir = dirFile.mkdirs();
            }
            String temFilePath = dirPath + File.separator + "daily_" + UUID.randomUUID() + Constant.XLSX;
            File newFile = new File(temFilePath);
            if (!newFile.exists()) {
                // 不存在文件
                boolean isFileMkdir = newFile.createNewFile();
            }
            FileUtils.copyInputStreamToFile(byteArrayInputStream, newFile);
            return temFilePath;
        } catch (IOException e) {
            log.error("临时转储模板到服务器失败");
            throw new ResponseException("临时转储模板到服务器失败");
        }
    }

    @Deprecated
    public void deleteDumpDailyTemplate() {
        // TODO 特殊情况下，可能存在临时文件未删除情况


    }
    @Deprecated
    private void saleOrderExport(ProductionDailyDTO.SaleOrderReq req, Double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.SaleOrderRecord> saleOrderRecords = new ArrayList<>();

        // 不传时间段,查全部
        Date plannedDeliveryStartTime = req.getPlannedDeliveryTime().getStartTime();
        Date plannedDeliveryEndTime = req.getPlannedDeliveryTime().getEndTime();

        SaleOrderSelectOpenDTO build = SaleOrderSelectOpenDTO.builder().showType(OrderShowTypeEnum.MATERIAL.getType()).build();
        if (plannedDeliveryStartTime != null && plannedDeliveryEndTime != null) {
            build.setPlanDeliveryStartTime(DateUtil.dateTimeStr(plannedDeliveryStartTime));
            build.setPlanDeliveryEndTime(DateUtil.dateTimeStr(plannedDeliveryEndTime));
        }
        PageResult<SaleOrderVO> responsePage = extSaleOrderInterface.getPage(build);
        if (CollectionUtils.isEmpty(responsePage.getRecords())) {
            return;
        }
        List<SaleOrderVO> saleOrderEntities = responsePage.getRecords();

        for (SaleOrderVO entity : saleOrderEntities) {
            saleOrderRecords.add(ProductionDailyResultDTO.SaleOrderRecord
                    .builder()
                    .orderId(entity.getSaleOrderId())
                    .orderNumber(entity.getSaleOrderNumber())
                    .state(entity.getState())
                    .materialCode(entity.getSaleOrderMaterial().getMaterialCode())
                    .plannedDeliveryTime(entity.getSaleOrderMaterial().getRequireGoodsDate())
                    .customerCode(entity.getCustomerCode())
                    .remark(entity.getRemark())
                    .build());
        }

        // 填充Excel内容
        setSaleOrderExcelContent(saleOrderRecords, eachProcess);
    }

    @Deprecated
    private void setSaleOrderExcelContent(List<ProductionDailyResultDTO.SaleOrderRecord> saleOrderRecords, double eachProcess) {
        int totalRecordSize = saleOrderRecords.size();
        // 更新到Excel中
        Sheet sheet = getSheetByName(workbook, "销售订单-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }
        HashMap<Integer, Integer> saleOrderCountMap = new HashMap<>(8);
        for (int i = 0; i < saleOrderRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.SaleOrderRecord record = saleOrderRecords.get(i);
            // 销售订单
            row.createCell(0, CellType.STRING).setCellValue(record.getOrderNumber());
            // 订单状态
            row.createCell(1, CellType.STRING).setCellValue(OrderStateEnum.getNameByCode(record.getState()));
            // 子项id
            saleOrderCountMap.put(record.getOrderId(), saleOrderCountMap.getOrDefault(record.getOrderId(), 0) + 1);
            row.createCell(2, CellType.STRING).setCellValue(saleOrderCountMap.get(record.getOrderId()));
            // 物料编码
            row.createCell(3, CellType.STRING).setCellValue(record.getMaterialCode());
            // 计划交货日期
            row.createCell(4, CellType.STRING).setCellValue(formatDate(record.getPlannedDeliveryTime()));
            // 客户编号
            row.createCell(5, CellType.STRING).setCellValue(record.getCustomerCode());
            // 	备注
            row.createCell(6, CellType.STRING).setCellValue(record.getRemark());
            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void productionOrderExport(ProductionDailyDTO.ProductionOrderReq req, Double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionOrderRecord> productionOrderRecords = new ArrayList<>();
        // 不传时间段,查全部
        ProductionDailyDTO.TimeRange planCompletionTime = req.getPlanCompletionTime();

        ProductOrderSelectOpenDTO build = ProductOrderSelectOpenDTO.builder()
                .plannedCompletionStartTime(planCompletionTime != null ? DateUtil.dateTimeStr(planCompletionTime.getStartTime()) : null)
                .plannedCompletionEndTime(planCompletionTime != null ? DateUtil.dateTimeStr(planCompletionTime.getEndTime()) : null)
                .build();
        PageResult<ProductOrderEntity> page = extProductOrderInterface.getPage(build);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return;
        }
        for (ProductOrderEntity productionOrder : page.getRecords()) {
            // 推送至kafka，获取订单物料数据
            ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderById(productionOrder.getProductOrderId());
            productionOrderRecords.add(ProductionDailyResultDTO.ProductionOrderRecord.builder()
                    .orderNumber(productionOrder.getProductOrderNumber())
                    .state(OrderStateEnum.getNameByCode(productionOrder.getState()))
                    .planCompletionTime(productOrderEntity == null ? null : productOrderEntity.getProductOrderMaterial().getPlanProductEndTime())
                    .gridCode(productOrderEntity == null ? null : productOrderEntity.getProductOrderMaterial().getGridCode())
                    .saleOrderNumber(productionOrder.getSaleOrderCode())
                    .customerCode(productionOrder.getCustomerCode())
                    .remark(productionOrder.getRemark())
                    .build());
        }

        // 填充Excel内容
        setProductionOrderExcelContent(productionOrderRecords, eachProcess);
    }
    @Deprecated
    private void setProductionOrderExcelContent(List<ProductionDailyResultDTO.ProductionOrderRecord> productionOrderRecords, double eachProcess) {
        int totalRecordSize = productionOrderRecords.size();
        // 更新到Excel中
        Sheet sheet = getSheetByName(workbook, "生产订单-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < productionOrderRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.ProductionOrderRecord record = productionOrderRecords.get(i);
            // 生产订单
            row.createCell(0, CellType.STRING).setCellValue(record.getOrderNumber());
            // 生产订单状态
            row.createCell(1, CellType.STRING).setCellValue(record.getState());
            // 	计划完成时间
            row.createCell(2, CellType.STRING).setCellValue(formatDate(record.getPlanCompletionTime()));
            // 	产线组
            row.createCell(3, CellType.STRING).setCellValue(record.getGridCode());
            // 销售订单
            row.createCell(4, CellType.STRING).setCellValue(record.getSaleOrderNumber());
            // 客户编号
            row.createCell(5, CellType.STRING).setCellValue(record.getCustomerCode());
            // 	备注
            row.createCell(6, CellType.STRING).setCellValue(record.getRemark());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void workOrderInOutExport(ProductionDailyDTO.WorkOrderInOutReq req, Double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.WorkOrderInOutRecord> workOrderInOutRecords = new ArrayList<>();

        // 不传时间段,查全部
        // 入仓开始时间、结束时间
        ProductionDailyDTO.TimeRange warehousingTime = req.getWarehousingTime();

        // 生产工单 仓库名称 入仓日期 入仓数量
        // 查询指定时间内的的 入库记录
        List<StockInAndOutEntity> stockInAndOuts = JacksonUtil.getResponseArray(stockInAndOutInterface.getInputList(StockInAndOutSelectDTO
                .builder()
                .inOrOutType(StockInputOrOutputTypeEnum.INPUT_WORK_ORDER_COMPLETE.getTypeCode())
                .showType(OrderShowTypeEnum.ORDER.getType())
                .actualStartTime(warehousingTime != null ? DateUtil.format(warehousingTime.getStartTime(), DateUtil.DATETIME_FORMAT) : null)
                .actualEndTime(warehousingTime != null ? DateUtil.format(warehousingTime.getEndTime(), DateUtil.DATETIME_FORMAT) : null)
                .build(), null), StockInAndOutEntity.class);

        if (!CollectionUtils.isEmpty(stockInAndOuts)) {
            // 出入单号
            Map<Integer, StockInAndOutEntity> inventoryOrderIdRecordMap = stockInAndOuts.stream().collect(Collectors.toMap(StockInAndOutEntity::getId, v -> v));
            List<String> inventoryOrderNumbers = stockInAndOuts.stream().map(StockInAndOutEntity::getOrderNumber).collect(Collectors.toList());
            // 查询出入库单号对应的工单号
            String join = StringUtils.join(inventoryOrderNumbers, Constant.SEP);
            List<StockRelateOrderEntity> stockRelateOrders = JacksonUtil.getResponseArray(stockRelateOrderInterface.getListByRelateNumber(join, null), StockRelateOrderEntity.class);

            Map<String, List<StockRelateOrderEntity>> workOrderNumbersRelatesMap = stockRelateOrders.stream().collect(Collectors.groupingBy(StockRelateOrderEntity::getOrderNumber));

            // 查询各入库单,入库物料数量
            List<StockMaterialDetailEntity> stockMaterialDetails = JacksonUtil.getResponseArray(stockInAndOutInterface.getRelatedMaterialList(inventoryOrderNumbers), StockMaterialDetailEntity.class);
            Map<Integer, List<StockMaterialDetailEntity>> stockIdMaterialDetailsMap = stockMaterialDetails.stream().collect(Collectors.groupingBy(StockMaterialDetailEntity::getInventoryOrderId));
            // 查询仓库名称
            List<String> warehouseCodes = stockInAndOuts.stream().map(StockInAndOutEntity::getWarehouseCode).collect(Collectors.toList());
            List<WarehouseEntity> warehouseEntities = JacksonUtil.getResponseArray(warehouseInterface.getList(StockWarehouseSelectDTO.builder().warehouseCodes(warehouseCodes).build()), WarehouseEntity.class);
            Map<Integer, String> warehouseIdNameMap = warehouseEntities.stream().collect(Collectors.toMap(WarehouseEntity::getWarehouseId, WarehouseEntity::getWarehouseName));

            for (Map.Entry<String, List<StockRelateOrderEntity>> entry : workOrderNumbersRelatesMap.entrySet()) {
                String workOrderNumber = entry.getKey();
                for (StockRelateOrderEntity relate : entry.getValue()) {
                    Integer inventoryOrderId = relate.getInventoryOrderId();
                    for (StockMaterialDetailEntity temStockMaterialDetail : stockIdMaterialDetailsMap.get(inventoryOrderId)) {
                        workOrderInOutRecords.add(ProductionDailyResultDTO.WorkOrderInOutRecord.builder()
                                .workOrderNumber(workOrderNumber)
                                .productCode(temStockMaterialDetail.getProductCode())
                                .warehouseName(warehouseIdNameMap.get(temStockMaterialDetail.getWarehouseId()))
                                .warehousingTime(inventoryOrderIdRecordMap.get(inventoryOrderId).getActualTime())
                                .actualAmount(temStockMaterialDetail.getActualAmount()).build()
                        );
                    }
                }
            }
        }

        // 填充Excel内容
        setWorkOrderInOutExcelContent(workOrderInOutRecords, eachProcess);
    }
    @Deprecated
    // 产线产量导出
    private void productionLineOutputExport(ProductionDailyDTO.ProductionLineOutputReq req, double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionLineOutputRecord> productionLineOutputRecords;
        // 不传时间段,查全部
        Date productionLineOutputStartTime = req.getTime().getStartTime();
        Date productionLineOutputEndTime = req.getTime().getEndTime();
        // 查询产线产量相关信息
        productionLineOutputRecords = ((RecordWorkOrderDayCountMapper) recordWorkOrderDayCountService.getBaseMapper())
                .selectProductionLineOutputInfo(productionLineOutputStartTime, productionLineOutputEndTime);
        // 填充Excel内容
        setProductionLineOutputExcelContent(productionLineOutputRecords, eachProcess);
    }
    @Deprecated
    // “车间用电”导出
    private void deviceUseElectricityExport(ProductionDailyDTO.DeviceUseElectricityReq req, double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.DeviceUseElectricityRecord> deviceUseElectricityRecords;
        // 不传时间段,查全部
        Date startTime = req.getTime().getStartTime();
        Date endTime = req.getTime().getEndTime();
        // 查询“车间用电”相关信息
        deviceUseElectricityRecords = ((DeviceTimePeriodEnergyConsumptionMapper) deviceTimePeriodEnergyConsumptionService.getBaseMapper())
                .selectDeviceTimePeriodEnergyConsumptionInfo(startTime, endTime);
        // 填充Excel内容
        setDeviceUseElectricityExcelContent(deviceUseElectricityRecords, eachProcess);
    }
    @Deprecated
    // "设备运行情况"导出
    private void deviceRunExport(ProductionDailyDTO.DeviceRunReq req, double eachProcess) {
        // 查询结果
        LinkedList<ProductionDailyResultDTO.DeviceRunRecord> deviceRunRecords = new LinkedList<>();
        // 时间
        Date startTime = req.getTime().getStartTime();
        Date endTime = req.getTime().getEndTime();
        // 获取所有设备
        List<DeviceEntity> deviceEntityList = deviceService.list();
        // 获取时间范围内的所有RecordDeviceOeeEntity
        LambdaQueryWrapper<MetricsDeviceOeeEntity> oeeWrapper = new LambdaQueryWrapper<>();
        oeeWrapper.between(MetricsDeviceOeeEntity::getRecordDate, startTime, endTime);
        List<MetricsDeviceOeeEntity> oeeList = metricsDeviceOeeService.list(oeeWrapper);
        // 获取所有设备的某时间段的所有DeviceStateDurationDayUnionEntity
        Map<Integer, Map<String, MetricsDeviceStateDailyEntity>> dsdduMap = getDsdduMap(deviceEntityList, startTime, endTime);
        // oeeMap
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        Map<Integer, Map<String, MetricsDeviceOeeEntity>> oeeMap = oeeList.stream().collect(
                Collectors.groupingBy(MetricsDeviceOeeEntity::getDeviceId,
                        Collectors.toMap(o -> sdf.format(o.getRecordDate()), r -> r)));
        // 填充DeviceRunRecord
        for (DeviceEntity deviceEntity : deviceEntityList) {
            Integer deviceId = deviceEntity.getDeviceId();
            // 设备名称
            String deviceName = deviceEntity.getDeviceName();
            Integer productionLineId = deviceEntity.getProductionLineId();
            Integer gid = deviceEntity.getGid();
            Date beginTime = startTime;
            Date nextDay = DateUtil.addDate(startTime, 1);
            // 产线名称
            ProductionLineEntity lineEntity = productionLineService.getById(productionLineId);
            String lineName = lineEntity == null ? null : lineEntity.getName();
            // 车间名称
            GridEntity gridEntity = gridService.getById(gid);
            String gname = null == gridEntity ? null : gridEntity.getGname();

            Map<String, MetricsDeviceOeeEntity> oeeEntityMap = oeeMap.get(deviceId);
            Map<String, MetricsDeviceStateDailyEntity> dsdduDeviceMap = dsdduMap.get(deviceId);
            while (!beginTime.after(endTime)) {
                ProductionDailyResultDTO.DeviceRunRecord record = new ProductionDailyResultDTO.DeviceRunRecord();
                // 时间，设备名称，产线名称,车间名称
                record.setRecordDate(beginTime);
                record.setDeviceName(deviceName);
                record.setLineName(lineName);
                record.setGName(gname);
                String beginTimeStr = sdf.format(beginTime);

                // 运行时长，待机时长，停机时长
                if (!CollectionUtils.isEmpty(dsdduDeviceMap) && dsdduDeviceMap.containsKey(beginTimeStr)) {
                    MetricsDeviceStateDailyEntity en = dsdduDeviceMap.get(beginTimeStr);
                    Integer runTime = en == null ? 0 : (en.getRunning() == null ? 0 : en.getRunning());
                    Integer pauseTime = en == null ? 0 : (en.getPause() == null ? 0 : en.getPause());
                    Integer stopTime = en == null ? 0 : (en.getStop() == null ? 0 : en.getStop());
                    record.setRunning(runTime);
                    record.setPause(pauseTime);
                    record.setStop(stopTime);
                }
                // oee,合格率，性能效率，时间效率
                if (!CollectionUtils.isEmpty(oeeEntityMap) && oeeEntityMap.containsKey(beginTimeStr)) {
                    MetricsDeviceOeeEntity oeeEntity = oeeEntityMap.get(beginTimeStr);
                    record.setOee(oeeEntity.getOee() == null ? 0 : oeeEntity.getOee());
                    record.setYield(oeeEntity.getYield() == null ? 0 : oeeEntity.getYield());
                    record.setPerformance(oeeEntity.getPerformance() == null ? 0 : oeeEntity.getPerformance());
                    record.setTimeEfficiency(oeeEntity.getTimeEfficiency() == null ? 0 : oeeEntity.getTimeEfficiency());
                }
                // 加入deviceRunRecords集合；addFirst保证时间倒序排序
                deviceRunRecords.addFirst(record);
                // 时间推移
                beginTime = nextDay;
                nextDay = DateUtil.addDate(nextDay, 1);
            }
        }
        // 填充Excel内容
        setDeviceRunExcelContent(deviceRunRecords, eachProcess);
    }

    /**
     * 获取某时间段的所有DeviceStateDurationDayUnionEntity，里面包含运行时长，停机时长，待机时长
     *
     * @param entityList 所有设备
     * @param startTime  开始时间
     * @param endTime    结束时间
     *                   说明： Map<设备id，<时间，DeviceStateDurationDayUnionEntity>>
     *                   说明：首先将所有的记录加入内存，防止和数据库进行多次IO交互，导致运行过慢
     * @return
     */
    @Deprecated
    private Map<Integer, Map<String, MetricsDeviceStateDailyEntity>> getDsdduMap(List<DeviceEntity> entityList, Date startTime, Date endTime) {
        Map<Integer, Map<String, MetricsDeviceStateDailyEntity>> resultMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        for (DeviceEntity entity : entityList) {
            Map<String, MetricsDeviceStateDailyEntity> map = new HashMap<>();
            Integer deviceId = entity.getDeviceId();
            Date beginTime = startTime;
            Date nextDay = DateUtil.addDate(startTime, 1);
            while (!beginTime.after(endTime)) {
                LambdaQueryWrapper<MetricsDeviceStateDailyEntity> dsdduWrapper = new LambdaQueryWrapper<>();
                dsdduWrapper.ge(MetricsDeviceStateDailyEntity::getRecordDate, DateUtil.getDayBegin(beginTime))
                        .le(MetricsDeviceStateDailyEntity::getRecordDate, DateUtil.getEndTimeOfCurrentDay(beginTime))
                        .eq(MetricsDeviceStateDailyEntity::getDeviceId, deviceId);
                MetricsDeviceStateDailyEntity en = metricsDeviceStateDailyService.getOne(dsdduWrapper);
                if (en != null) {
                    map.put(sdf.format(beginTime), en);
                }
                // 时间推移
                beginTime = nextDay;
                nextDay = DateUtil.addDate(nextDay, 1);
            }
            resultMap.put(deviceId, map);
        }
        return resultMap;
    }

    @Deprecated
    private void setProductionLineOutputExcelContent(List<ProductionDailyResultDTO.ProductionLineOutputRecord> productionLineOutputRecords, Double eachProcess) {
        int totalRecordSize = productionLineOutputRecords.size();
        // 更新到Excel中
        Sheet sheet = getSheetByName(workbook, "产线产量-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < productionLineOutputRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.ProductionLineOutputRecord record = productionLineOutputRecords.get(i);
            // 生产日期
            row.createCell(0, CellType.STRING).setCellValue(DateUtil.dateStr(record.getTime()));
            // 车间名称
            row.createCell(1, CellType.STRING).setCellValue(record.getGName());
            // 产线名称
            row.createCell(2, CellType.STRING).setCellValue(record.getName());
            // 产量
            row.createCell(3, CellType.STRING).setCellValue(record.getCount());
            // 不合格数
            row.createCell(4, CellType.STRING).setCellValue(record.getUnqualified());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }

    }

    // 车间用电
    @Deprecated
    private void setDeviceUseElectricityExcelContent(List<ProductionDailyResultDTO.DeviceUseElectricityRecord> deviceUseElectricityRecords, Double eachProcess) {
        int totalRecordSize = deviceUseElectricityRecords.size();
        // 更新到Excel中
        Sheet sheet = getSheetByName(workbook, "车间用电-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }
        for (int i = 0; i < deviceUseElectricityRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.DeviceUseElectricityRecord record = deviceUseElectricityRecords.get(i);
            // 日期
            row.createCell(0, CellType.STRING).setCellValue(DateUtil.dateStr(record.getRecordDate()));
            // 车间名称
            row.createCell(1, CellType.STRING).setCellValue(record.getGName());
            // 用电总计
            row.createCell(2, CellType.STRING).setCellValue(record.getConsumption().toString());
            // 尖
            row.createCell(3, CellType.STRING).setCellValue(record.getJian().toString());
            // 峰
            row.createCell(4, CellType.STRING).setCellValue(record.getFeng().toString());
            // 平
            row.createCell(5, CellType.STRING).setCellValue(record.getPing().toString());
            // 谷
            row.createCell(6, CellType.STRING).setCellValue(record.getGu().toString());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }

    // 设备运行
    @Deprecated
    private void setDeviceRunExcelContent(List<ProductionDailyResultDTO.DeviceRunRecord> deviceRunRecords, Double eachProcess) {
        int totalRecordSize = deviceRunRecords.size();
        // 更新到Excel中
        Sheet sheet = getSheetByName(workbook, "设备运行-原始数据");
        if (CollectionUtils.isEmpty(deviceRunRecords)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }
        // 小数转为百分比，保留一位小数
        NumberFormat nf = NumberFormat.getPercentInstance();
        nf.setMinimumFractionDigits(1);
        for (int i = 0; i < deviceRunRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.DeviceRunRecord record = deviceRunRecords.get(i);
            // 日期
            row.createCell(0, CellType.STRING).setCellValue(DateUtil.dateStr(record.getRecordDate()));
            // 车间名称
            row.createCell(1, CellType.STRING).setCellValue(record.getGName());
            // 产线名称
            row.createCell(2, CellType.STRING).setCellValue(record.getLineName());
            // 设备名称
            row.createCell(3, CellType.STRING).setCellValue(record.getDeviceName());
            // 待机时长
            row.createCell(4, CellType.STRING).setCellValue((record.getPause() == null || record.getPause() == 0) ? 0.0 : BigDecimal.valueOf(record.getPause()).divide(BigDecimal.valueOf(60), 1, RoundingMode.HALF_UP).doubleValue());
            // 停机时长
            row.createCell(5, CellType.STRING).setCellValue((record.getStop() == null || record.getStop() == 0) ? 0.0 : BigDecimal.valueOf(record.getStop()).divide(BigDecimal.valueOf(60), 1, RoundingMode.HALF_UP).doubleValue());
            // 运行时长
            row.createCell(6, CellType.STRING).setCellValue((record.getRunning() == null || record.getRunning() == 0) ? 0.0 : BigDecimal.valueOf(record.getRunning()).divide(BigDecimal.valueOf(60), 1, RoundingMode.HALF_UP).doubleValue());
            // oee
            row.createCell(7, CellType.STRING).setCellValue(nf.format(record.getOee()));
            // 合格率
            row.createCell(8, CellType.STRING).setCellValue(nf.format(record.getYield()));
            // 性能效率
            row.createCell(9, CellType.STRING).setCellValue(nf.format(record.getPerformance()));
            // 时间效率
            row.createCell(10, CellType.STRING).setCellValue(nf.format(record.getTimeEfficiency()));

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }

    @Deprecated
    private void setWorkOrderInOutExcelContent(List<ProductionDailyResultDTO.WorkOrderInOutRecord> workOrderInOutRecords, Double eachProcess) {
        int totalRecordSize = workOrderInOutRecords.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet("工单出入库-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < workOrderInOutRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.WorkOrderInOutRecord record = workOrderInOutRecords.get(i);
            // 生产工单
            row.createCell(0, CellType.STRING).setCellValue(record.getWorkOrderNumber());
            // 仓库名称
            row.createCell(1, CellType.STRING).setCellValue(record.getWarehouseName());
            // 物料编码
            row.createCell(2, CellType.STRING).setCellValue(record.getProductCode());
            // 入仓时间
            row.createCell(3, CellType.STRING).setCellValue(formatDate(record.getWarehousingTime()));
            // 入仓数量
            row.createCell(4, CellType.STRING).setCellValue(record.getActualAmount());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void shiftExport(ProductionDailyDTO.ShiftReq req, Double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.ShiftRecord> shiftRecords = new ArrayList<>();

        // 班次，查全部
        List<WorkCalendarEntity> workCalendars = workCalendarService.lambdaQuery()
                .select(WorkCalendarEntity::getName, WorkCalendarEntity::getWorkCalendarTime)
                .list();
        for (WorkCalendarEntity workCalendar : workCalendars) {
            //班次时间
            workCalendarService.getWorkCalendarTimes(workCalendar);
            List<TimeRangeDTO> workCalendarTimes = workCalendar.getWorkCalendarTimes();
            for (TimeRangeDTO workCalendarTime : workCalendarTimes) {
                shiftRecords.add(ProductionDailyResultDTO.ShiftRecord
                        .builder()
                        .name(workCalendar.getName())
                        .start(workCalendarTime.getStart())
                        .end(workCalendarTime.getEnd())
                        .build());
            }
        }

        // 填充Excel内容
        setShiftExcelContent(shiftRecords, eachProcess);
    }
    @Deprecated
    private void setShiftExcelContent(List<ProductionDailyResultDTO.ShiftRecord> shiftRecords, Double eachProcess) {
        int totalRecordSize = shiftRecords.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet("班次-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < shiftRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.ShiftRecord record = shiftRecords.get(i);
            // 班次
            row.createCell(0, CellType.STRING).setCellValue(record.getName());
            // 开始时间
            row.createCell(1, CellType.STRING).setCellValue(record.getStart());
            // 结束时间
            row.createCell(2, CellType.STRING).setCellValue(record.getEnd());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }

    /**
     * 作业工单报工
     */
    @Deprecated
    private void operationOrderReportExport(ProductionDailyDTO.OperationOrderReportReq req, Double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.OperationOrderReportRecord> operationOrderReportRecords = new ArrayList<>();
        // 不传时间段,查全部
        ProductionDailyDTO.TimeRange reportTime = req.getReportTime();
        ProductionDailyDTO.TimeRange updateTime = req.getUpdateTime();
        Date start1 = reportTime != null ? reportTime.getStartTime() : null;
        Date end1 = reportTime != null ? reportTime.getEndTime() : null;
        Date start2 = updateTime != null ? updateTime.getStartTime() : null;
        Date end2 = updateTime != null ? updateTime.getEndTime() : null;
        DateRangeReq dateRangeReq = new DateRangeReq(start1, end1, start2, end2);
        // 查询指定时间段内作业报工工单
        ResponseData responseData = assignmentInterface.listOperationOrderReport(dateRangeReq);
        if (!ResponseData.SUCCESS_CODE.equals(responseData.getCode())) {
            // 远程调用未成功
            log.error("远程调用未成功,message:{}", responseData.getMessage());
            throw new ResponseException("远程调用未成功,message:" + responseData.getMessage());
        }
        List<OperationOrderReportLineDTO> operationOrderReports = JacksonUtil.getResponseArray(responseData, OperationOrderReportLineDTO.class);
        Map<Integer, String> lineIdNameMap = new HashMap<>(8);
        if (!CollectionUtils.isEmpty(operationOrderReports)) {
            lineIdNameMap = selectNameByLineIds(operationOrderReports.stream().map(OperationOrderReportLineDTO::getLineId).collect(Collectors.toSet()));
        }

        for (OperationOrderReportLineDTO operationOrderReport : operationOrderReports) {
            operationOrderReportRecords.add(ProductionDailyResultDTO.OperationOrderReportRecord.builder()
                    .reportType(ReportType.getNameByCode(operationOrderReport.getType()))
                    .lineId(operationOrderReport.getLineId())
                    .lineName(lineIdNameMap.get(operationOrderReport.getLineId()))
                    .operationNumber(operationOrderReport.getOperationNumber())
                    .finishCount(operationOrderReport.getFinishCount())
                    .unqualified(operationOrderReport.getUnqualified())
                    .reportUserName(operationOrderReport.getUserNickname())
                    .batch(operationOrderReport.getBatch())
                    .shiftId(operationOrderReport.getShiftId())
                    .shiftName(operationOrderReport.getShiftType())
                    .effectiveHours(operationOrderReport.getEffectiveHours())
                    .reportDate(operationOrderReport.getReportDate())
                    .updateTime(operationOrderReport.getUpdateTime())
                    .operator(operationOrderReport.getOperator())
                    .defectDesc(operationOrderReport.getDefectDesc())
                    .build());
        }

        // 填充Excel内容
        setOperationOrderReportExcelContent(operationOrderReportRecords, eachProcess);
    }
    @Deprecated
    private void setOperationOrderReportExcelContent(List<ProductionDailyResultDTO.OperationOrderReportRecord> operationOrderReportRecords, Double eachProcess) {
        int totalRecordSize = operationOrderReportRecords.size();
        // 更新到Excel中
        Sheet sheet = getSheetByName(workbook, "作业工单报工-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < operationOrderReportRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.OperationOrderReportRecord record = operationOrderReportRecords.get(i);
            // 报工方式
            row.createCell(0, CellType.STRING).setCellValue(record.getReportType());
            // 产线id
            row.createCell(1, CellType.STRING).setCellValue(record.getLineId());
            // 产线名称
            row.createCell(2, CellType.STRING).setCellValue(record.getLineName());
            // 作业工单号
            row.createCell(3, CellType.STRING).setCellValue(record.getOperationNumber());
            // 完成数量
            row.createCell(4, CellType.STRING).setCellValue(record.getFinishCount() != null ? record.getFinishCount() : 0.0);
            // 不合格数量
            row.createCell(5, CellType.STRING).setCellValue(record.getUnqualified() != null ? record.getUnqualified() : 0.0);
            // 上报人姓名
            row.createCell(6, CellType.STRING).setCellValue(record.getReportUserName());
            // 批次
            row.createCell(7, CellType.STRING).setCellValue(record.getBatch());
            // 班次id
            row.createCell(8, CellType.STRING).setCellValue(record.getShiftId() != null ? record.getShiftId().toString() : "");
            // 班次名称
            row.createCell(9, CellType.STRING).setCellValue(record.getShiftName());
            // 有效工时
            row.createCell(10, CellType.STRING).setCellValue(record.getEffectiveHours() != null ? record.getEffectiveHours() : 0.0);
            // 上报时间
            row.createCell(11, CellType.STRING).setCellValue(formatDate(record.getReportDate()));
            // 更新时间
            row.createCell(12, CellType.STRING).setCellValue(formatDate(record.getUpdateTime()));
            // 操作员（生产人员）
            row.createCell(13, CellType.STRING).setCellValue(record.getOperator());
            // 不良描述
            row.createCell(14, CellType.STRING).setCellValue(record.getDefectDesc());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void operationOrderExport(ProductionDailyDTO.OperationOrderReq req, Double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.OperationOrderRecord> operationOrderRecords = new ArrayList<>();
        // 不传时间段,查全部
        ProductionDailyDTO.TimeRange actualStartTime = req.getActualStartTime();
        ProductionDailyDTO.TimeRange actualCompletionTime = req.getActualCompletionTime();
        Date start1 = actualStartTime != null ? actualStartTime.getStartTime() : null;
        Date end1 = actualStartTime != null ? actualStartTime.getEndTime() : null;
        Date start2 = actualCompletionTime != null ? actualCompletionTime.getStartTime() : null;
        Date end2 = actualCompletionTime != null ? actualCompletionTime.getEndTime() : null;

        DateRangeReq dateRangeReq = new DateRangeReq(start1, end1, start2, end2);
        // 查询指定时间段内的作业工单
        ResponseData responseData = assignmentInterface.listOperationOrder(dateRangeReq);
        if (!ResponseData.SUCCESS_CODE.equals(responseData.getCode())) {
            // 远程调用未成功
            log.error("远程调用未成功,message:{}", responseData.getMessage());
            throw new ResponseException("远程调用未成功,message:" + responseData.getMessage());
        }
        List<OperationOrderDTO> operationOrders = JacksonUtil.getResponseArray(responseData, OperationOrderDTO.class);


        Map<String, MaterialEntity> codeMaterialMap = new HashMap<>(8);
        Map<Integer, String> lineIdGridNameMap = new HashMap<>(8);
        if (!CollectionUtils.isEmpty(operationOrders)) {
            // 查询对应产品的规格
            Set<String> codes = operationOrders.stream().map(OperationOrderDTO::getMaterialCode).collect(Collectors.toSet());
            List<MaterialEntity> materials = materialService.getMaterialsByCodes(codes);
            codeMaterialMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));

            // 查询查询对应产线组
            Set<Integer> lineIds = operationOrders.stream().map(OperationOrderDTO::getLineId).filter(Objects::nonNull).collect(Collectors.toSet());
            lineIdGridNameMap = selectGridNameByLineIds(lineIds);
        }
        for (OperationOrderDTO operationOrder : operationOrders) {
            MaterialEntity material = codeMaterialMap.getOrDefault(operationOrder.getMaterialCode(), MaterialEntity.builder().build());
            operationOrderRecords.add(ProductionDailyResultDTO.OperationOrderRecord.builder()
                    .operationNumber(operationOrder.getOperationNumber())
                    .state(operationOrder.getStateName())
                    .workOrderNumber(operationOrder.getWorkOrderNum())
                    .actualStartTime(operationOrder.getActualStartDate())
                    .actualCompletionTime(operationOrder.getActualEndDate())
                    .gName(lineIdGridNameMap.get(operationOrder.getLineId()))
                    .lineName(operationOrder.getLineName())
                    .shiftName(operationOrder.getShiftType())
                    .productionCode(operationOrder.getMaterialCode())
                    .productionName(operationOrder.getMaterialName())
                    .productionStandard(material.getStandard())
                    .unit(material.getComp())
                    .unit2(material.getUnit())
                    .planQuantity(operationOrder.getProductCount())
                    .actualProductionQuantity(operationOrder.getFinishCount())
                    .unqualified(operationOrder.getUnqualified())
                    .remark(operationOrder.getRemark())
                    .build());
        }
        // 填充Excel内容
        setOperationOrderExcelContent(operationOrderRecords, eachProcess);
    }
    @Deprecated
    private void setOperationOrderExcelContent(List<ProductionDailyResultDTO.OperationOrderRecord> operationOrderRecords, Double eachProcess) {
        int totalRecordSize = operationOrderRecords.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet("作业工单-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < operationOrderRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.OperationOrderRecord record = operationOrderRecords.get(i);
            // 作业工单
            row.createCell(0, CellType.STRING).setCellValue(record.getOperationNumber());
            // 作业工单状态
            row.createCell(1, CellType.STRING).setCellValue(record.getState());
            // 生产工单
            row.createCell(2, CellType.STRING).setCellValue(record.getWorkOrderNumber());
            // 实际开始时间
            row.createCell(3, CellType.STRING).setCellValue(formatDate(record.getActualStartTime()));
            // 实际完成时间
            row.createCell(4, CellType.STRING).setCellValue(formatDate(record.getActualCompletionTime()));
            // 产线组
            row.createCell(5, CellType.STRING).setCellValue(record.getGName());
            // 产线
            row.createCell(6, CellType.STRING).setCellValue(record.getLineName());
            // 班次
            row.createCell(7, CellType.STRING).setCellValue(record.getShiftName());
            // 产品编码
            row.createCell(8, CellType.STRING).setCellValue(record.getProductionCode());
            // 产品名称
            row.createCell(9, CellType.STRING).setCellValue(record.getProductionName());
            // 产品规格
            row.createCell(10, CellType.STRING).setCellValue(record.getProductionStandard());
            // 主单位
            row.createCell(11, CellType.STRING).setCellValue(record.getUnit());
            // 副单位
            row.createCell(12, CellType.STRING).setCellValue(record.getUnit2());
            // 单位比值（主/副）
            row.createCell(13, CellType.STRING).setCellValue(record.getUnit() + "/" + record.getUnit2());
            // 计划生产数量
            row.createCell(14, CellType.STRING).setCellValue(record.getPlanQuantity());
            // 实际生产数量
            row.createCell(15, CellType.STRING).setCellValue(record.getActualProductionQuantity());
            // 不良数量
            row.createCell(16, CellType.STRING).setCellValue(record.getUnqualified());
            // 备注
            row.createCell(17, CellType.STRING).setCellValue(record.getRemark());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void productionWorkMonotonerExport(ProductionDailyDTO.ProductionWorkMonotonerReq req, Double eachProcess) throws IllegalAccessException {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionWorkMonotonerRecord> productionWorkOrderErrorRecords = new ArrayList<>();

        // 不传时间段,查全部
        // 调机开始
        ProductionDailyDTO.TimeRange startTime = req.getStartTime();
        // 调机结束
        ProductionDailyDTO.TimeRange endTime = req.getEndTime();
        Date start1 = startTime != null ? startTime.getStartTime() : null;
        Date end1 = startTime != null ? startTime.getEndTime() : null;
        Date start2 = endTime != null ? endTime.getStartTime() : null;
        Date end2 = endTime != null ? endTime.getEndTime() : null;
        DateRangeReq dateRangeReq = new DateRangeReq(start1, end1, start2, end2);
        // 查询指定时间段内的调机记录
        ResponseData responseData = packagingInterface.listDebugMachineRecord(dateRangeReq);
        if (!ResponseData.SUCCESS_CODE.equals(responseData.getCode())) {
            // 远程调用未成功
            log.error("远程调用未成功,message:{}", responseData.getMessage());
            throw new ResponseException("远程调用未成功,message:" + responseData.getMessage());
        }
        List<DebugMachineRecordEntityDTO> dtos = JacksonUtil.getResponseArray(responseData, DebugMachineRecordEntityDTO.class);

        Map<String, DebugMachineRecordEntityDTO> map = dtos.stream()
                .collect(Collectors.toMap(DebugMachineRecordEntityDTO::getWorkOrderNumber, o -> o, (o1, o2) -> o2));
        for (Map.Entry<String, DebugMachineRecordEntityDTO> entry : map.entrySet()) {
            String workOrderNumber = entry.getKey();
            DebugMachineRecordEntityDTO debugMachineRecordEntity = entry.getValue();

            List<EventRecordEntity> events = eventRecordService.getEventsByWorkOrder(workOrderNumber);
            if (CollectionUtils.isEmpty(events)) {
                productionWorkOrderErrorRecords.add(ProductionDailyResultDTO.ProductionWorkMonotonerRecord.builder()
                        .workOrderNumber(workOrderNumber)
                        .startTime(debugMachineRecordEntity.getStartTime())
                        .endTime(debugMachineRecordEntity.getEndTime())
                        .build());
            } else {
                for (EventRecordEntity event : events) {
                    productionWorkOrderErrorRecords.add(ProductionDailyResultDTO.ProductionWorkMonotonerRecord.builder()
                            .workOrderNumber(workOrderNumber)
                            .eventDefinitionName(event.getEventDefinitionName())
                            .startTime(debugMachineRecordEntity.getStartTime())
                            .endTime(debugMachineRecordEntity.getEndTime())
                            .build());
                }
            }
        }

        // 填充excel表头顺序的属性字段名称
        final String[] sortAttributeFields = new String[]{
                "workOrderNumber", "eventDefinitionName", "startTime", "endTime"
        };
        // 填充Excel内容
        setExcelContent("生产工单调机-原始数据", eachProcess, productionWorkOrderErrorRecords, ProductionDailyResultDTO.ProductionWorkMonotonerRecord.class, sortAttributeFields);
    }
    @Deprecated
    private void productionWorkOrderErrorExport(ProductionDailyDTO.ProductionWorkOrderErrorReq req, Double eachProcess) throws IllegalAccessException {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionWorkOrderErrorRecord> productionWorkOrderErrorRecords = new ArrayList<>();

        // 不传时间段,查全部
        ProductionDailyDTO.TimeRange reportTime = req.getReportTime();
        ProductionDailyDTO.TimeRange recoveryTime = req.getRecoveryTime();

        List<AlarmEntity> alarms = alarmService.lambdaQuery()
                .select(AlarmEntity::getWorkOrderNumber, AlarmEntity::getAlarmDes, AlarmEntity::getAlarmRecoveryTime, AlarmEntity::getAlarmReportTime)
                // 上报时间
                .between(!Objects.isNull(reportTime), AlarmEntity::getAlarmReportTime, reportTime != null ? reportTime.getStartTime() : null, reportTime != null ? reportTime.getEndTime() : null)
                // 更新时间
                .between(!Objects.isNull(recoveryTime), AlarmEntity::getAlarmRecoveryTime, recoveryTime != null ? recoveryTime.getStartTime() : null, recoveryTime != null ? recoveryTime.getEndTime() : null)
                .list();
        for (AlarmEntity alarm : alarms) {
            productionWorkOrderErrorRecords.add(ProductionDailyResultDTO.ProductionWorkOrderErrorRecord.builder()
                    .workOrderNumber(alarm.getWorkOrderNumber())
                    .alarmDes(alarm.getAlarmDes())
                    .recoveryTime(alarm.getAlarmRecoveryTime())
                    .reportDate(alarm.getAlarmReportTime())
                    .build());
        }

        // 填充excel表头顺序的属性字段名称
        final String[] sortAttributeFields = new String[]{
                "workOrderNumber", "alarmDes", "reportDate", "recoveryTime"
        };

        // 填充Excel内容
        setExcelContent("生产工单异常-原始数据", eachProcess, productionWorkOrderErrorRecords, ProductionDailyResultDTO.ProductionWorkOrderErrorRecord.class, sortAttributeFields);
    }
    @Deprecated
    private void productionWorkOrderReportExport(ProductionDailyDTO.ProductionWorkOrderReportReq req, Double eachProcess) throws IllegalAccessException {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionWorkOrderReportRecord> productionWorkOrderReportRecords = new ArrayList<>();

        // 不传时间段,查全部
        ProductionDailyDTO.TimeRange reportTime = req.getReportTime();
        Assert.notNull(reportTime, "reportTime不能为null");

        List<ReportLineEntity> reportLines = reportLineService.lambdaQuery()
                // 上报时间
                .between(ReportLineEntity::getReportDate, reportTime.getStartTime(), reportTime.getEndTime())
                .list();
        List<RecordWorkOrderDayCountEntity> workOrderDayCounts = recordWorkOrderDayCountService.lambdaQuery()
                .between(RecordWorkOrderDayCountEntity::getTime, reportTime.getStartTime(), reportTime.getEndTime())
                .list();
        Map<String, List<RecordWorkOrderDayCountEntity>> workOrderDayCountsMap = workOrderDayCounts.stream().collect(Collectors.groupingBy(RecordWorkOrderDayCountEntity::getWorkOrderNumber));

        Map<Integer, String> lineIdNameMap = new HashMap<>(8);
        if (!CollectionUtils.isEmpty(reportLines)) {
            lineIdNameMap = selectNameByLineIds(reportLines.stream().map(ReportLineEntity::getLineId).collect(Collectors.toSet()));
        }

        Map<String, Boolean> workOrderDateMap = new HashMap<>(32);
        SimpleDateFormat formatter = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        for (ReportLineEntity reportLine : reportLines) {
            String dateStr = formatter.format(reportLine.getReportDate());

            List<RecordWorkOrderDayCountEntity> tempWorkOrderDayCounts = workOrderDayCountsMap.getOrDefault(reportLine.getWorkOrder(), new ArrayList<>());
            List<RecordWorkOrderDayCountEntity> recordWorkOrderDayCounts = tempWorkOrderDayCounts.stream()
                    .filter(res -> dateStr.equals(formatter.format(res.getTime())))
                    .collect(Collectors.toList());
            // 工单+日期 Map, 每个工单每天只要一条
            if (CollectionUtils.isEmpty(recordWorkOrderDayCounts) || workOrderDateMap.get(reportLine.getWorkOrder() + dateStr) != null) {
                continue;
            }
            RecordWorkOrderDayCountEntity dayCount = recordWorkOrderDayCounts.get(0);
            productionWorkOrderReportRecords.add(ProductionDailyResultDTO.ProductionWorkOrderReportRecord.builder()
                    .reportType(ReportType.getNameByCode(reportLine.getType()))
                    .lineId(reportLine.getLineId())
                    .lineName(lineIdNameMap.get(reportLine.getLineId()))
                    .workOrderNumber(reportLine.getWorkOrder())
                    .finishCount(dayCount.getCount())
                    .unqualified(dayCount.getUnqualified())
                    .reportUserName(reportLine.getUserNickname())
                    .batch(reportLine.getBatch())
                    .shiftId(reportLine.getShiftId())
                    .shiftName(reportLine.getShiftType())
                    .effectiveHours(reportLine.getEffectiveHours())
                    .reportDate(reportLine.getReportDate())
                    .updateTime(reportLine.getUpdateTime())
                    .operatorName(reportLine.getOperatorName())
                    .storeState(StoreStateEnum.getNameByCode(reportLine.getStoreState()))
                    .vehicleCode(reportLine.getVehicleCode())
                    .build());
            workOrderDateMap.put(reportLine.getWorkOrder() + dateStr, true);
        }
        // 填充excel表头顺序的属性字段名称
        final String[] sortAttributeFields = new String[]{
                "reportType", "lineId", "lineName", "workOrderNumber", "finishCount",
                "unqualified", "reportUserName", "batch", "shiftId", "shiftName",
                "effectiveHours", "reportDate", "updateTime", "operatorName", "storeState",
                "deviceCode", "vehicleCode",
        };

        // 填充Excel内容
        setExcelContent("生产工单报工-原始数据", eachProcess, productionWorkOrderReportRecords, ProductionDailyResultDTO.ProductionWorkOrderReportRecord.class, sortAttributeFields);
    }
    @Deprecated
    private void productionWorkOrderExport(ProductionDailyDTO.ProductionWorkOrderReq req, Double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionWorkOrderRecord> productionWorkOrderRecords = new ArrayList<>();

        // 不传时间段,查全部
        ProductionDailyDTO.TimeRange actualStartTime = req.getActualStartTime();
        ProductionDailyDTO.TimeRange actualCompletionTime = req.getActualCompletionTime();

        List<WorkOrderEntity> productionWorkOrders = workOrderService.lambdaQuery()
                // 实际开始
                .between(!Objects.isNull(actualStartTime), WorkOrderEntity::getActualStartDate, actualStartTime != null ? actualStartTime.getStartTime() : null, actualStartTime != null ? actualStartTime.getEndTime() : null)
                // 实际完成
                .between(!Objects.isNull(actualCompletionTime), WorkOrderEntity::getActualEndDate, actualCompletionTime != null ? actualCompletionTime.getStartTime() : null, actualCompletionTime != null ? actualCompletionTime.getEndTime() : null)
                .list();
        Map<Integer, String> lineIdGridNameMap = selectGridNameByLineIds(productionWorkOrders.stream().map(WorkOrderEntity::getLineId).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String, MaterialEntity> codeMaterialMap = new HashMap<>(16);
        if (!CollectionUtils.isEmpty(productionWorkOrders)) {
            codeMaterialMap = materialService.lambdaQuery()
                    .in(MaterialEntity::getCode, productionWorkOrders.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet()))
                    .list()
                    .stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        }

        for (WorkOrderEntity productionWorkOrder : productionWorkOrders) {
            MaterialEntity materialEntity = codeMaterialMap.getOrDefault(productionWorkOrder.getMaterialCode(), new MaterialEntity());

            productionWorkOrderRecords.add(ProductionDailyResultDTO.ProductionWorkOrderRecord.builder()
                    .workOrderNumber(productionWorkOrder.getWorkOrderNumber())
                    .productionCode(productionWorkOrder.getMaterialCode())
                    .productionName(materialEntity.getName())
                    .actualProductionQuantity(productionWorkOrder.getFinishCount())
                    .actualStartTime(productionWorkOrder.getActualStartDate())
                    .actualCompletionTime(productionWorkOrder.getActualEndDate())
                    .planQuantity(productionWorkOrder.getPlanQuantity())
                    .unqualified(productionWorkOrder.getUnqualified())
                    .remark(productionWorkOrder.getRemark())
                    .state(WorkOrderStateEnum.getNameByCode(productionWorkOrder.getState()))
                    .remark(productionWorkOrder.getRemark())
                    .unit(materialEntity.getComp())
                    .unit2(materialEntity.getUnit())
                    .planCompletionTime(productionWorkOrder.getEndDate())
                    .gName(lineIdGridNameMap.get(productionWorkOrder.getLineId()))
                    .lineName(productionWorkOrder.getLineName())
                    .productionOrderNumber(productionWorkOrder.getProductOrderNumber())
                    .saleOrderNumber(productionWorkOrder.getSaleOrderNumber())
                    .build());
        }

        // 填充Excel内容
        setProductionWorkOrderExcelContent(productionWorkOrderRecords, eachProcess);
    }
    @Deprecated
    private void setProductionWorkOrderExcelContent(List<ProductionDailyResultDTO.ProductionWorkOrderRecord> productionWorkOrderRecords, Double eachProcess) {
        int totalRecordSize = productionWorkOrderRecords.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet("生产工单-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < productionWorkOrderRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.ProductionWorkOrderRecord record = productionWorkOrderRecords.get(i);
            // 生产工单
            row.createCell(0, CellType.STRING).setCellValue(record.getWorkOrderNumber());
            // 产品编码
            row.createCell(1, CellType.STRING).setCellValue(record.getProductionCode());
            // 实际生产数量
            row.createCell(2, CellType.STRING).setCellValue(record.getActualProductionQuantity());
            // 实际开始时间
            row.createCell(3, CellType.STRING).setCellValue(formatDate(record.getActualStartTime()));
            // 实际完成时间
            row.createCell(4, CellType.STRING).setCellValue(formatDate(record.getActualCompletionTime()));
            // 计划生产数量
            row.createCell(5, CellType.STRING).setCellValue(record.getPlanQuantity());
            // 	不良数量
            row.createCell(6, CellType.STRING).setCellValue(record.getUnqualified());
            // 备注
            row.createCell(7, CellType.STRING).setCellValue(record.getRemark());
            // 生产工单状态
            row.createCell(8, CellType.STRING).setCellValue(record.getState());
            // 产品名称
            row.createCell(9, CellType.STRING).setCellValue(record.getProductionName());
            // 主单位
            row.createCell(10, CellType.STRING).setCellValue(record.getUnit());
            // 副单位
            row.createCell(11, CellType.STRING).setCellValue(record.getUnit2());
            // 单位比值（主/副）
            row.createCell(12, CellType.STRING).setCellValue(record.getUnit() + "/" + record.getUnit2());
            // 计划完成时间
            row.createCell(13, CellType.STRING).setCellValue(formatDate(record.getPlanCompletionTime()));
            // 产线组
            row.createCell(14, CellType.STRING).setCellValue(record.getGName());
            // 产线
            row.createCell(15, CellType.STRING).setCellValue(record.getLineName());
            // 生产订单
            row.createCell(16, CellType.STRING).setCellValue(record.getProductionOrderNumber());
            // 销售订单
            row.createCell(17, CellType.STRING).setCellValue(record.getSaleOrderNumber());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void productionWorkSpecialExport(ProductionDailyDTO.ProductionWorkSpecialReq productionWorkSpecialReq, double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionWorkSpecialRecord> productionWorkSpecialRecords = new ArrayList<>();

        ResponseData responseData = packagingInterface.listMachineSpeedRecord();
        if (!ResponseData.SUCCESS_CODE.equals(responseData.getCode())) {
            // 远程调用未成功
            log.error("远程调用未成功,message:{}", responseData.getMessage());
            throw new ResponseException("远程调用未成功,message:" + responseData.getMessage());
        }
        List<MachineSpeedRecordEntityDTO> machineSpeedRecords = JacksonUtil.getResponseArray(responseData, MachineSpeedRecordEntityDTO.class);

        Map<Integer, String> lineIdNameMap = new HashMap<>(8);
        if (!CollectionUtils.isEmpty(machineSpeedRecords)) {
            lineIdNameMap = selectNameByLineIds(machineSpeedRecords.stream().map(MachineSpeedRecordEntityDTO::getLineId).collect(Collectors.toSet()));
        }

        for (MachineSpeedRecordEntityDTO machineSpeedRecord : machineSpeedRecords) {
            productionWorkSpecialRecords.add(ProductionDailyResultDTO.ProductionWorkSpecialRecord.builder()
                    .workOrderNumber(machineSpeedRecord.getWorkOrderNumber())
                    .lineName(lineIdNameMap.get(machineSpeedRecord.getLineId()))
                    .speed(machineSpeedRecord.getSpeed())
                    .build());
        }

        // 填充Excel内容
        setProductionWorkSpecialExcelContent(productionWorkSpecialRecords, eachProcess);
    }
    @Deprecated
    private void setProductionWorkSpecialExcelContent(List<ProductionDailyResultDTO.ProductionWorkSpecialRecord> productionWorkSpecialRecords, Double eachProcess) {
        int totalRecordSize = productionWorkSpecialRecords.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet("生产工单特殊参数-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < productionWorkSpecialRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.ProductionWorkSpecialRecord record = productionWorkSpecialRecords.get(i);
            // 生产工单	产线	调机车速
            row.createCell(0, CellType.STRING).setCellValue(record.getWorkOrderNumber());
            // 产线
            row.createCell(1, CellType.STRING).setCellValue(record.getLineName());
            // 调机车速
            row.createCell(2, CellType.STRING).setCellValue(record.getSpeed() != null ? record.getSpeed() : 0.0);

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void productionWorkFeedExport(ProductionDailyDTO.ProductionWorkFeedReq productionWorkFeedReq, double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.ProductionWorkFeedRecord> productionWorkFeedRecords = new ArrayList<>();

        ProductionDailyDTO.TimeRange feedTime = productionWorkFeedReq.getFeedTime();
        List<FeedRecordEntity> feedRecords = feedRecordService.lambdaQuery()
                .eq(FeedRecordEntity::getState, FeedStateEnum.RELEASED.getCode())
                // 上料时间
                .between(!Objects.isNull(feedTime), FeedRecordEntity::getFeedTime, feedTime != null ? feedTime.getStartTime() : null, feedTime != null ? feedTime.getEndTime() : null)
                .list();

        Map<Integer, String> lineIdNameMap = new HashMap<>(8), facIdNameMap = new HashMap<>(8);
        Map<String, String> userNicknameMap = new HashMap<>(8);
        Map<String, SupplierEntity> supplierMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(feedRecords)) {
            lineIdNameMap = productionLineService.selectNameByLineIds(feedRecords.stream().map(FeedRecordEntity::getLineId).collect(Collectors.toSet()));

            facIdNameMap = facilitiesService.lambdaQuery()
                    .select(FacilitiesEntity::getFid, FacilitiesEntity::getFname)
                    .in(FacilitiesEntity::getFid, feedRecords.stream().map(FeedRecordEntity::getFacId).collect(Collectors.toSet()))
                    .list()
                    .stream()
                    .collect(Collectors.toMap(FacilitiesEntity::getFid, FacilitiesEntity::getFname));

            Set<String> userNames = feedRecords.stream().map(FeedRecordEntity::getFeedBy).collect(Collectors.toSet());
            List<SysUserEntity> sysUserEntities = sysUserService.getListByUserNames(new ArrayList<>(userNames));
            userNicknameMap = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
            // 查询供应商相关信息
            List<String> supplierCodes = feedRecords.stream().map(FeedRecordEntity::getSupplierCode).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(supplierCodes)) {
                Page<SupplierEntity> page = supplierService.supplierOpenPage(SupplierSelectDTO.builder().fullCodes(supplierCodes).build());
                if (!CollectionUtils.isEmpty(page.getRecords())) {
                    supplierMap = page.getRecords().stream().collect(Collectors.toMap(SupplierEntity::getCode, o -> o));
                }
            }
        }

        for (FeedRecordEntity record : feedRecords) {
            // 设置供应商相关信息
            SupplierEntity supplierEntity = supplierMap.get(record.getSupplierCode());
            productionWorkFeedRecords.add(ProductionDailyResultDTO.ProductionWorkFeedRecord.builder()
                    .workOrderNumber(record.getWorkOrderNum())
                    .lineName(lineIdNameMap.get(record.getLineId()))
                    .facName(facIdNameMap.get(record.getFacId()))
                    .materialCode(record.getMaterialCode())
                    .materialName(record.getMaterialName())
                    .count(record.getMaterialNum())
                    .batch(record.getBatchNumber())
                    .supplier(Objects.isNull(supplierEntity) ? null : supplierEntity.getName())
                    .feedTime(record.getFeedTime())
                    .operator(userNicknameMap.getOrDefault(record.getFeedBy(), ""))
                    .feedTime(record.getFeedTime())
                    .build());
        }

        // 填充Excel内容
        setProductionWorkFeedExcelContent(productionWorkFeedRecords, eachProcess);
    }
    @Deprecated
    private void setProductionWorkFeedExcelContent(List<ProductionDailyResultDTO.ProductionWorkFeedRecord> productionWorkFeedRecords, Double eachProcess) {
        int totalRecordSize = productionWorkFeedRecords.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet("生产工单上料记录-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < productionWorkFeedRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.ProductionWorkFeedRecord record = productionWorkFeedRecords.get(i);
            // 生产工单编号
            row.createCell(0, CellType.STRING).setCellValue(record.getWorkOrderNumber());
            // 产线
            row.createCell(1, CellType.STRING).setCellValue(record.getLineName());
            // 工位名称
            row.createCell(2, CellType.STRING).setCellValue(record.getFacName());
            // 物料编码
            row.createCell(3, CellType.STRING).setCellValue(record.getMaterialCode());
            // 物料名称
            row.createCell(4, CellType.STRING).setCellValue(record.getMaterialName());
            // 数量
            row.createCell(5, CellType.STRING).setCellValue(record.getCount());
            // 批次
            row.createCell(6, CellType.STRING).setCellValue(record.getBatch());
            // 供应商
            row.createCell(7, CellType.STRING).setCellValue(record.getSupplier());
            // 上料时间
            row.createCell(8, CellType.STRING).setCellValue(formatDate(record.getFeedTime()));
            // 上料员
            row.createCell(9, CellType.STRING).setCellValue(record.getOperator());

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }
    @Deprecated
    private void staffPieceWorkTimeExport(ProductionDailyDTO.StaffPieceWorkTimeReq staffPieceWorkTimeReq, double eachProcess) {
        // 查询结果
        List<ProductionDailyResultDTO.StaffPieceWorkTimeRecord> staffPieceWorkTimeRecords = new ArrayList<>();

        ProductionDailyDTO.TimeRange productionTime = staffPieceWorkTimeReq.getProductionTime();

        List<StaffPieceWorkTimeEntity> records = staffPieceWorkTimeService.lambdaQuery()
                .between(!Objects.isNull(productionTime), StaffPieceWorkTimeEntity::getProductRecord, productionTime != null ? productionTime.getStartTime() : null, productionTime != null ? productionTime.getEndTime() : null)
                .list();

        for (StaffPieceWorkTimeEntity record : records) {
            staffPieceWorkTimeRecords.add(ProductionDailyResultDTO.StaffPieceWorkTimeRecord.builder()
                    .employeeCode(record.getStaffCode())
                    .employeeName(record.getStaffName())
                    .productionTime(record.getProductRecord())
                    .shift(record.getShift())
                    .workOrderNumber(record.getWorkOrderNumber())
                    .operationNumber(record.getAssignmentNumber())
                    .productionCount(record.getProductNum())
                    .workingHours(record.getHours())
                    .reportTime(record.getReportTime())
                    .build());
        }

        // 填充Excel内容
        setStaffPieceWorkTimeExcelContent(staffPieceWorkTimeRecords, eachProcess);
    }
    @Deprecated
    private void setStaffPieceWorkTimeExcelContent(List<ProductionDailyResultDTO.StaffPieceWorkTimeRecord> staffPieceWorkTimeRecords, Double eachProcess) {
        int totalRecordSize = staffPieceWorkTimeRecords.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet("人员计件计时-原始数据");
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        for (int i = 0; i < staffPieceWorkTimeRecords.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            ProductionDailyResultDTO.StaffPieceWorkTimeRecord record = staffPieceWorkTimeRecords.get(i);
            // 员工账号	员工名称	生产日期	班次	生产工单	作业工单	生产数量	工时	上报时间
            row.createCell(0, CellType.STRING).setCellValue(record.getEmployeeCode());
            row.createCell(1, CellType.STRING).setCellValue(record.getEmployeeName());
            row.createCell(2, CellType.STRING).setCellValue(formatDate(record.getProductionTime()));
            row.createCell(3, CellType.STRING).setCellValue(record.getShift());
            row.createCell(4, CellType.STRING).setCellValue(record.getWorkOrderNumber());
            row.createCell(5, CellType.STRING).setCellValue(record.getOperationNumber());
            row.createCell(6, CellType.STRING).setCellValue(record.getProductionCount());
            row.createCell(7, CellType.STRING).setCellValue(Objects.isNull(record.getWorkingHours()) ? "" : String.valueOf(record.getWorkingHours()));
            row.createCell(8, CellType.STRING).setCellValue(formatDate(record.getReportTime()));

            // 缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }


    /**
     * 设置使用说明
     */
    @Deprecated
    private void setUseInstructions(ProductionDailyDTO.TimeRange globalConfigTime) {
        if (Objects.isNull(globalConfigTime)) {
            return;
        }
        Sheet sheet = getSheetByName(workbook, "使用说明");
        Row row = sheet.getRow(0);
        if (!Objects.isNull(row) && !Objects.isNull(globalConfigTime.getStartTime()) && !Objects.isNull(globalConfigTime.getEndTime())) {
            setRow(row, 1, globalConfigTime.getStartTime());
            setRow(row, 2, globalConfigTime.getEndTime());
        }
    }

    /**
     * 要统计的表单数
     * 前端传什么参数,说明要填充什么sheet
     */
    @Deprecated
    private Integer countFormsNum(ProductionDailyDTO productionDailyDTO) throws IllegalAccessException {
        int total = 0;

        final String serialVersion = "serialVersionUID";
        final String globalConfigTime = "globalConfigTime";
        Field[] declaredFields = ProductionDailyDTO.class.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            // 忽略类中的 serialVersionUID 属性、忽略类中的 globalConfigTime 属性
            if (serialVersion.equals(declaredField.getName()) || globalConfigTime.equals(declaredField.getName())) {
                continue;
            }
            Object fieldValue = declaredField.get(productionDailyDTO);
            if (Objects.nonNull(fieldValue)) {
                total++;
            }
        }
        return total;
    }

    /**
     * 获取Excel中指定的sheet表格
     */
    @Deprecated
    private Sheet getSheetByName(Workbook workbook, String sheetName) {
        Sheet sheet = workbook.getSheet(sheetName);
        if (sheet == null) {
            log.error("模板Excel不存在该sheetName:{}", sheetName);
        }
        return sheet;
    }
    @Deprecated
    private Map<Integer, String> selectNameByLineIds(Collection<Integer> lineIds) {
        lineIds = lineIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lineIds)) {
            return new HashMap<>(4);
        }
        return productionLineService.lambdaQuery()
                .in(ProductionLineEntity::getProductionLineId, lineIds)
                .list()
                .stream()
                .collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
    }
    @Deprecated
    private Map<Integer, String> selectGridNameByLineIds(Collection<Integer> lineIds) {
        Map<Integer, String> lineIdGridNameMap = new HashMap<>(8);
        // 查询各产线对应车间名称
        if (!CollectionUtils.isEmpty(lineIds)) {
            Map<Integer, Integer> lineIdGidMap = productionLineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getGid)
                    .in(ProductionLineEntity::getProductionLineId, lineIds)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getGid));
            Map<Integer, String> gidNameMap = gridService.lambdaQuery()
                    .select(GridEntity::getGid, GridEntity::getGname)
                    .in(GridEntity::getGid, new HashSet<>(lineIdGidMap.values()))
                    .list()
                    .stream().collect(Collectors.toMap(GridEntity::getGid, GridEntity::getGname));

            for (Integer lineId : lineIds) {
                lineIdGridNameMap.put(lineId, gidNameMap.get(lineIdGidMap.get(lineId)));
            }
        }
        return lineIdGridNameMap;
    }

    /**
     * 缓存当前进度
     */
    @Deprecated
    private void cacheProgress(double progress, boolean isExecutionStatus) {
        ImportProgressDTO exportProgress = ImportProgressDTO.builder()
                .progress(progress)
                .executionStatus(isExecutionStatus)
                .build();
        redisTemplate.opsForValue().set(RedisKeyPrefix.PRODUCTION_DAILY_PROGRESS + ":" + temFilePath, exportProgress, 1, TimeUnit.HOURS);
    }

    @Deprecated
    public ImportProgressDTO getCurrentProgress(String temFilePath) {
        Object obj = redisTemplate.opsForValue().get(RedisKeyPrefix.PRODUCTION_DAILY_PROGRESS + ":" + temFilePath);
        if (obj != null) {
            return JSONObject.parseObject(JSON.toJSONString(obj), ImportProgressDTO.class);
        }
        return null;
    }
    @Deprecated
    private String formatDate(Date date) {
        if (Objects.isNull(date)) {
            return "";
        }
        String format = dateTimeFormatter.format(date);
        if (format.endsWith(DateUtil.TIME_OF_ZERO)) {
            return dateFormatter.format(date);
        }
        return format;
    }
    @Deprecated
    private void setRow(Row row, int i, String value) {
        Cell cell = Objects.isNull(row.getCell(i)) ? row.createCell(i, CellType.STRING) : row.getCell(i);
        String strValue = StringUtils.isBlank(value) ? "" : value;
        cell.setCellValue(strValue);
    }
    @Deprecated
    private void setRow(Row row, int i, Date value) {
        Cell cell = Objects.isNull(row.getCell(i)) ? row.createCell(i, CellType.STRING) : row.getCell(i);
        cell.setCellValue(formatDate(value));
    }
    @Deprecated
    private void setRow(Row row, int i, Double value) {
        Cell cell = Objects.isNull(row.getCell(i)) ? row.createCell(i, CellType.STRING) : row.getCell(i);
        String strValue = Objects.isNull(value) ? "" : value.toString();
        cell.setCellValue(strValue);
    }
    @Deprecated
    private void setRow(Row row, int i, Integer value) {
        Cell cell = Objects.isNull(row.getCell(i)) ? row.createCell(i, CellType.STRING) : row.getCell(i);
        String strValue = Objects.isNull(value) ? "" : value.toString();
        cell.setCellValue(strValue);
    }
    @Deprecated
    private <T> void setExcelContent(String sheetName, double eachProcess, List<T> records, Class<T> clazz, String... sortAttributeFields) throws IllegalAccessException {
        int totalRecordSize = records.size();
        // 更新到Excel中
        Sheet sheet = workbook.getSheet(sheetName);
        if (totalRecordSize == 0 || Objects.isNull(sheet)) {
            // 没有查到数据,这一表单进度直接完成
            totalProcess += eachProcess;
            cacheProgress(totalProcess, false);
            return;
        }

        Map<String, Field> nameFieldMap = new HashMap<>(sortAttributeFields.length);
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            nameFieldMap.put(field.getName(), field);
        }

        for (int i = 0; i < records.size(); i++) {
            // 从第二行开始
            Row row = sheet.createRow(i + 1);
            T record = records.get(i);

            // 填充一行数据
            for (int index = 0; index < sortAttributeFields.length; index++) {
                Field field = nameFieldMap.get(sortAttributeFields[index]);
                if (Objects.isNull(field)) {
                    continue;
                }
                Object value = field.get(record);
                if (field.getType() == String.class) {
                    setRow(row, index, (String) value);
                } else if (field.getType() == Double.class) {
                    setRow(row, index, (Double) value);
                } else if (field.getType() == Integer.class) {
                    setRow(row, index, (Integer) value);
                } else if (field.getType() == Date.class) {
                    setRow(row, index, (Date) value);
                } else {
                    setRow(row, index, value.toString());
                }
            }

            // 填充每一行就缓存进度
            Double value = MathUtil.divideDouble(eachProcess, totalRecordSize, 8);
            totalProcess += value;
            cacheProgress(totalProcess, false);
        }
    }

}
