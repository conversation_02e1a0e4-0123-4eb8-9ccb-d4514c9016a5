package com.yelink.dfs.controller.capacity;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.capacity.CapacityEntity;
import com.yelink.dfs.entity.capacity.DefaultCapacityEntity;
import com.yelink.dfs.entity.capacity.dto.CapacitySelectDTO;
import com.yelink.dfs.entity.capacity.dto.DefaultCapacityDTO;
import com.yelink.dfs.entity.capacity.dto.DefaultCapacityUpdateDTO;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.constant.CapacityUnitEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/18 16:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/capacities")
public class CapacityController extends BaseController {

    private CapacityService capacityService;
    private final RedisTemplate redisTemplate;

    /**
     * 产能配置列表
     *
     * @return
     */
    @PostMapping("/list")
    public ResponseData getList(@RequestBody CapacitySelectDTO selectDTO) {
        Page<CapacityEntity> page = capacityService.getList(selectDTO);
        return ResponseData.success(page);
    }


    /**
     * 新增
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "基础信息", type = OperationType.ADD, desc = "新增了产能id为#{capacityId}的产能信息")
    public ResponseData insert(@RequestBody @Validated({CapacityEntity.Insert.class}) CapacityEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        return ResponseData.success(capacityService.addCapacity(entity));
    }

    /**
     * 修改
     *
     * @param entity
     * @param bindingResult
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "基础信息", type = OperationType.UPDATE, desc = "更新了产能id为#{capacityId}的产能信息")
    public ResponseData editRepairRecords(@RequestBody @Validated({CapacityEntity.Update.class}) CapacityEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateTime(new Date());
        capacityService.editCapacity(entity);
        return ResponseData.success(capacityService.getById(entity.getCapacityId()));
    }

    /**
     * 详情
     *
     * @param capacityId
     * @return
     */
    @GetMapping("/detail/{capacityId}")
    public ResponseData getDetail(@PathVariable Integer capacityId) {
        CapacityEntity entity = capacityService.getDetail(capacityId);
        return success(entity);
    }

    /**
     * 删除
     *
     * @return
     */
    @DeleteMapping("/delete")
    public ResponseData delete(@RequestParam(value = "capacityId") String capacityId) {
        capacityService.delete(capacityId);
        return ResponseData.success();
    }

//    /**
//     * 下载默认导入模板
//     *
//     * @param
//     * @return
//     */
//    @GetMapping("/download/default/import/template")
//    public void downloadExcelImportTemplate(HttpServletResponse response) throws IOException {
//        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
//        Resource resource = resolver.getResource("classpath:template/capacityImportTemplate.xlsx");
//        ExcelTemplateImportUtil.responseToClient(response, resource.getInputStream(), "产能导入模板" + Constant.XLSX);
//    }
//
//    /**
//     * 导入excel
//     *
//     * @param file
//     * @return
//     */
//    @PostMapping("/excel/import")
//    public ResponseData previewBeforeImport(MultipartFile file) {
//        String username = getUsername();
//        ImportDTO dto = capacityService.importExcel(file, username);
//        return success(dto);
//    }

    /**
     * 下载产能导入默认转换模板
     */
    @GetMapping("/export/default/template")
    public void exportExcelDefaultTemplate(HttpServletResponse response) throws Exception {
        capacityService.downloadDefaultTemplate("classpath:template/capacityImportTemplate.xlsx", response, "产能默认模板" + Constant.XLSX);
    }

    /**
     * 上传自定义转换模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入产能自定义模板")
    public ResponseData importExcelTemplate(MultipartFile file) {
        capacityService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 下载自定义转换模板
     *
     * @param response
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导出的模板
        InputStream inputStream = capacityService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "产能自定义数据导入模板" + Constant.XLSX);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/export/template")
    public void exportExcelTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = capacityService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "产能数据导入模板" + Constant.XLSX);
    }

    /**
     * 导入excel
     */
    @PostMapping("/import")
    public ResponseData importMaterialExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.CAPACITY_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        capacityService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 查询导入进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress(@RequestParam(value = "key") String key) {
        Object obj = redisTemplate.opsForValue().get(key);
        return success(obj);
    }

    /**
     * 保存导入的列表
     *
     * @param list
     * @return
     */
    @PostMapping("/save/import")
    public ResponseData saveImport(@RequestBody List<CapacityEntity> list) {
        capacityService.saveImport(list);
        return success();
    }

    /**
     * 导出
     *
     * @param response
     */
    @PostMapping("/excel/export")
    public void exportExcel(@RequestBody CapacitySelectDTO selectDTO,
                            HttpServletResponse response) throws IOException {
        capacityService.exportExcel(selectDTO, response);
    }

    /**
     * 查询工厂产能、每月标准产能、默认产能取值逻辑
     *
     * @return
     */
    @GetMapping("/standard/capacity")
    public ResponseData getDefaultStandardCapacity() {
        List<DictEntity> dictEntities = capacityService.getDefaultStandardCapacity();
        return success(dictEntities);
    }

    /**
     * 查询默认产能取值逻辑列表
     *
     * @return
     */
    @GetMapping("/default/capacity/value_logic")
    public ResponseData getDefaultCapacityValueLogic() {
        List<DictEntity> dictEntities = capacityService.getDefaultCapacityValueLogic();
        return success(dictEntities);
    }

    /**
     * 修改默认产能配置
     *
     * @return
     */
    @PutMapping("/default/update")
    public ResponseData updateDefaultCapacity(@RequestBody DefaultCapacityUpdateDTO updateDTO) {
        capacityService.updateDefaultCapacity(updateDTO);
        return ResponseData.success();
    }

    /**
     * 查询某基本单元类型下的默认产能
     *
     * @param
     * @return
     */
    @PostMapping("/default")
    public ResponseData getDefaultCapacityByIds(@RequestBody DefaultCapacityDTO defaultCapacityDTO) {
        List<DefaultCapacityEntity> entity = capacityService.getDefaultCapacityByIds(defaultCapacityDTO);
        return success(entity);
    }

    /**
     * 获取产能单位枚举
     */
    @GetMapping("/unit")
    public ResponseData getCapacityUnit() {
        CapacityUnitEnum[] values = CapacityUnitEnum.values();
        List<CommonType> states = new ArrayList<>();
        for (CapacityUnitEnum unitEnum : values) {
            states.add(CommonType.builder().code(unitEnum.getType()).name(unitEnum.getTypeName()).build());
        }
        return ResponseData.success(states);
    }


}
