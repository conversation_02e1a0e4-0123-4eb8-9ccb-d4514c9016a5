package com.yelink.dfs.controller.product;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.product.dto.ProcedureOneKeyDefaultDTO;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.ProcedureDefControllerConfigService;
import com.yelink.dfs.service.product.ProcedureDefDefectSchemeService;
import com.yelink.dfs.service.product.ProcedureDefDeviceTypeService;
import com.yelink.dfs.service.product.ProcedureDefFileService;
import com.yelink.dfs.service.product.ProcedureDefInspectControllerService;
import com.yelink.dfs.service.product.ProcedureDefMaintainSchemeService;
import com.yelink.dfs.service.product.ProcedureDefMaterialUsedService;
import com.yelink.dfs.service.product.ProcedureDefPostService;
import com.yelink.dfs.service.product.ProcedureDefProcessAssemblyService;
import com.yelink.dfs.service.product.ProcedureDefRelationWorkHoursService;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@RequestMapping("/processDefExtend")
public class ProcessDefExtendController extends BaseController {

    private ProcedureDefMaterialUsedService procedureDefMaterialService;
    private ProcedureDefDeviceTypeService procedureDefDeviceTypeService;
    private ProcedureDefRelationWorkHoursService procedureDefRelationWorkHoursService;
    private ProcedureDefFileService procedureDefFileService;
    private ProcedureDefInspectControllerService procedureDefInspectControllerService;
    private ProcedureDefControllerConfigService procedureDefControllerConfigService;
    private ProcedureDefProcessAssemblyService procedureDefProcessAssemblyService;
    private ProcedureDefDefectSchemeService procedureDefDefectSchemeService;
    private ProcedureDefMaintainSchemeService procedureDefMaintainSchemeService;
    private ProcedureDefPostService procedureDefPostService;
    private CraftService craftService;


    /**
     * 工序用料
     */
    @GetMapping("/materialUsedList")
    public ResponseData getDetail(@RequestParam String procedureCode) {
        return success(procedureDefMaterialService.listByProcedure(procedureCode));
    }
    /**
     * 设备类型
     */
    @GetMapping("/deviceTypeList")
    public ResponseData deviceType(@RequestParam String procedureCode) {
        return success(procedureDefDeviceTypeService.listByProcedure(procedureCode));
    }
    /**
     * 工序工时
     */
    @GetMapping("/relationWorkHours")
    public ResponseData relationWorkHours(@RequestParam String procedureCode) {
        return success(procedureDefRelationWorkHoursService.oneByProcedure(procedureCode));
    }
    /**
     * 工序附件
     */
    @GetMapping("/fileList")
    public ResponseData file(@RequestParam String procedureCode) {
        return success(procedureDefFileService.listByProcedure(procedureCode));
    }
    /**
     * 工序检验方案
     */
    @GetMapping("/inspectControllerList")
    public ResponseData inspectController(@RequestParam String procedureCode) {
        return success(procedureDefInspectControllerService.listByProcedure(procedureCode));
    }
    /**
     * 工序控制
     */
    @GetMapping("/controllerConfig")
    public ResponseData controllerConfig(@RequestParam String procedureCode) {
        return success(procedureDefControllerConfigService.oneByProcedure(procedureCode));
    }
    /**
     * 工序工装
     */
    @GetMapping("/processAssemblyList")
    public ResponseData processAssembly(@RequestParam String procedureCode) {
        return success(procedureDefProcessAssemblyService.listByProcedure(procedureCode));
    }
    /**
     * 工序质检
     */
    @GetMapping("/defectScheme")
    public ResponseData defectScheme(@RequestParam String procedureCode) {
        return success(procedureDefDefectSchemeService.oneByProcedure(procedureCode));
    }
    /**
     * 工序维修
     */
    @GetMapping("/maintainScheme")
    public ResponseData maintainScheme(@RequestParam String procedureCode) {
        return success(procedureDefMaintainSchemeService.oneByProcedure(procedureCode));
    }
    /**
     * 工序人力
     */
    @GetMapping("/postList")
    public ResponseData post(@RequestParam String procedureCode) {
        return success(procedureDefPostService.listByProcedure(procedureCode));
    }
    @PostMapping("/oneKeyDefault")
    public ResponseData oneKeyDefault(@RequestBody @Validated ProcedureOneKeyDefaultDTO dto) {
        dto.setRecordChange(true);
        craftService.oneKeyDefault(dto);
        return success();
    }

}
