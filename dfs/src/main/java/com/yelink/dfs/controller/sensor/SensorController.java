package com.yelink.dfs.controller.sensor;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.sensor.SensorTypeCodeEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.dto.SensorSelectDTO;
import com.yelink.dfs.entity.device.vo.DeviceExcelVO;
import com.yelink.dfs.entity.device.vo.SensorExcelVO;
import com.yelink.dfs.entity.sensor.SensorAlarmConfigEntity;
import com.yelink.dfs.entity.sensor.SensorEntity;
import com.yelink.dfs.entity.sensor.SensorProductEntity;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.sensor.SensorAlarmConfigService;
import com.yelink.dfs.service.sensor.SensorProductService;
import com.yelink.dfs.service.sensor.SensorRecordService;
import com.yelink.dfs.service.sensor.SensorService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.inner.data.access.SensorIotSetInfo;
import com.yelink.inner.listen.CommandHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: 传感器添加-设施+传感器
 * @author: shuang
 * @time: 2020/12/10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sensors")
public class SensorController extends BaseController {

    private SensorService sensorService;
    private SensorAlarmConfigService sensorAlarmConfigService;
    private SensorRecordService sensorRecordService;
    private SensorProductService sensorProductService;
    private RedisTemplate redisTemplate;
    private CommandHandler commandHandler;
    private ImportDataRecordService importDataRecordService;

    /**
     * 添加采集设备
     *
     * @param sensorEntity
     * @return
     */
    @PostMapping("/add")
    @OperLog(module = "设备管理", type = OperationType.ADD, desc = "新增了eui为#{eui}的采集设备")
    public ResponseData add(@RequestBody SensorEntity sensorEntity) {
        sensorEntity.setCid(getUserInfo().getCompId());
        sensorEntity.setCreateTime(new Date());
        sensorEntity.setUpdateTime(sensorEntity.getCreateTime());
        sensorEntity.setUpdateBy(getUsername());
        sensorEntity.setCreateBy(getUsername());
        sensorEntity.setSensorTypeName(SensorTypeCodeEnum.getNameByCode(sensorEntity.getSensorType()));
        sensorService.addEntity(sensorEntity);
        return success(sensorEntity);
    }

    /**
     * 更新传感器信息
     *
     * @param sensorEntity
     * @return
     */
    @PostMapping("/update")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "修改了eui为#{eui}的采集设备")
    public ResponseData update(@RequestBody SensorEntity sensorEntity) {
        sensorEntity.setCid(getUserInfo().getCompId());
        sensorEntity.setUpdateBy(getUsername());
        sensorEntity.setUpdateTime(DateUtil.formatToDate(new Date(), DateUtil.DATETIME_FORMAT));
        sensorService.updateSensor(sensorEntity);
        return success(sensorEntity);
    }


    /**
     * 删除设备（删除设备与用户的绑定关系）
     * 如果设备绑定了默认的设施，那么设施一起删除
     * 如果设备绑定的不是默认的设施，只删除设备
     */
    @DeleteMapping("/delete/{eui}")
    @OperLog(module = "设备管理", type = OperationType.DELETE, desc = "删除了eui为#{eui}的采集设备")
    public ResponseData delete(@PathVariable String eui) {
        sensorService.deleteByEui(eui);
        return success(SensorEntity.builder().eui(eui).build());
    }

    /**
     * 页条件查询设备列表（默认创建了设施的列表）,设备的详细信息也包含其中
     * <p>
     * range 0-全部 1-除了工位看板、产线看板的其他采集设备 2-工位看板、产线看板
     *
     * @return
     */
    @GetMapping("/list")
    public ResponseData list(@RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                             @RequestParam(defaultValue = "1", value = "currentPage") int currentPage,
                             @RequestParam(value = "eui", required = false) String eui,
                             @RequestParam(value = "name", required = false) String name,
                             @RequestParam(value = "sensorType", required = false) String sensorType) {
        return success(sensorService.listByPage(pageSize, currentPage, eui, name, sensorType));
    }


    /**
     * 页条件查询设备列表（默认创建了设施的列表）,设备的详细信息也包含其中
     *
     * @return
     */
    @GetMapping("/select/{eui}")
    public ResponseData detail(@PathVariable("eui") String eui) {
        QueryWrapper<SensorEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SensorEntity::getEui, eui);
        SensorEntity entity = sensorService.getOne(queryWrapper);
        sensorService.showName(Stream.of(entity).collect(Collectors.toList()));
        return success(entity);
    }

    /**
     * 通过eui查询设备上报记录
     * 数据量过大，默认查询当天数据
     *
     * @return
     */
    @GetMapping("/record/{eui}")
    public ResponseData record(@PathVariable(value = "eui") String eui,
                               @RequestParam(value = "startDate", required = false) String startDate,
                               @RequestParam(value = "endDate", required = false) String endDate,
                               @RequestParam(defaultValue = "1", value = "currentPage") int currentPage,
                               @RequestParam(defaultValue = "10", value = "pageSize") int pageSize) {
        return success(sensorRecordService.getRecordList(eui, startDate, endDate, currentPage, pageSize));
    }


    /**
     * 查询所有记录
     *
     * @return
     */
    @GetMapping("/record/all")
    public String allRecord(@RequestParam("currentPage") Integer currentPage, @RequestParam("pageSize") Integer pageSize) {
        Page page = sensorRecordService.page(new Page(currentPage, pageSize), null);
        return JSON.toJSONString(page.getRecords());
    }

    /**
     * 设备大屏
     *
     * @return
     */
    @GetMapping("/screen")
    public ResponseData screenList(@RequestParam(defaultValue = "10", value = "pageSize") int pageSize, @RequestParam(defaultValue = "1", value = "currentPage") int currentPage) {
        return success(sensorService.screenList(pageSize, currentPage));
    }

    /**
     * 关键设备状态监控
     *
     * @return
     */
    @GetMapping("/safety/list")
    public ResponseData saftyList(@RequestParam(defaultValue = "10", value = "pageSize") int pageSize, @RequestParam(defaultValue = "1", value = "currentPage") int currentPage) {
        return success(sensorService.saftyList(pageSize, currentPage));
    }

    /**
     * 设置传感器告警阈值
     *
     * @return
     */
    @PostMapping("/update/alarm/config")
    public ResponseData setSensorAlarmConfig(@RequestBody SensorAlarmConfigEntity entity) {
        QueryWrapper<SensorAlarmConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SensorAlarmConfigEntity::getEui, entity.getEui());
        sensorAlarmConfigService.saveOrUpdate(entity, queryWrapper);
        return success();
    }

    /**
     * 获取传感器告警阈值
     *
     * @return
     */
    @GetMapping("/alarm/config/{eui}")
    public ResponseData getSensorAlarmConfig(@PathVariable("eui") String eui) {
        QueryWrapper<SensorAlarmConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SensorAlarmConfigEntity::getEui, eui);
        return success(sensorAlarmConfigService.getOne(queryWrapper));
    }

    /**
     * 获取类型为产线看板的传感器
     *
     * @param
     * @return
     */
    @GetMapping("/board/list")
    public ResponseData getBoardList() {
        QueryWrapper<SensorEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SensorEntity::getSensorType, SensorTypeCodeEnum.LINE_BOARD.getCode());
        List<SensorEntity> list = sensorService.list(queryWrapper);
        return ResponseData.success(list);
    }

    /**
     * 获取采集设备枚举列表
     *
     * @return
     */
    @GetMapping("/enum/list")
    public ResponseData getEnumList() {
        List<CommonState> list = new ArrayList<>();
        SensorTypeCodeEnum[] values = SensorTypeCodeEnum.getCommonEnum();
        for (SensorTypeCodeEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    @GetMapping("/product/key/list")
    public ResponseData productKeyList() {
        return success(sensorProductService.lambdaQuery().last("order by IF(ISNULL(`sort`),1,0),`sort` asc").list());
    }


    @PutMapping("/update/product/key")
    @OperLog(module = "设备管理", type = OperationType.UPDATE, desc = "修改了ProductKey为#{productKey}的采集设备")
    public ResponseData updateProductKey(@RequestBody List<SensorProductEntity> list) {
        String username = getUsername();
        for (SensorProductEntity sensorProductEntity : list) {
            sensorProductEntity.setUpdateTime(new Date());
            sensorProductEntity.setUpdateBy(username);
        }
        redisTemplate.delete(RedisKeyPrefix.PRODUCT_KEY);
        return success(sensorProductService.updateBatchById(list));
    }

    @PostMapping("/l")
    public ResponseData getList(@RequestBody List<SensorProductEntity> list) {
        String username = getUsername();
        for (SensorProductEntity sensorProductEntity : list) {
            sensorProductEntity.setUpdateTime(new Date());
            sensorProductEntity.setUpdateBy(username);
        }
        redisTemplate.delete(RedisKeyPrefix.PRODUCT_KEY);
        return success(sensorProductService.updateBatchById(list));
    }

    /**
     * 采集设备的指标导出
     *
     * @param
     * @return
     */
    @GetMapping("/export/target")
    public void exportSensorTarget(@RequestParam(value = "startDate", required = false) String startDate,
                                   @RequestParam(value = "endDate", required = false) String endDate,
                                   @RequestParam(value = "eui") String eui,
                                   HttpServletResponse response) {
        sensorRecordService.exportSensorTarget(eui, startDate, endDate, response);
    }

    /**
     * 获取所有的sensor
     *
     * @return
     */
    @GetMapping("/all")
    public ResponseData getAllSensorList() {
        return success(sensorService.list());
    }

    /**
     * 设备参数设置,命令下发
     *
     * @param eui
     * @param setData
     * @return
     */
    @PostMapping("/command/{eui}")
    public ResponseData command(@PathVariable("eui") String eui, @RequestBody String setData) {
        SensorIotSetInfo sensorIotSetInfo = selectSensorSetInfoByEui(eui);
        if (sensorIotSetInfo == null) {
            return fail("设备不存在");
        }
        sensorIotSetInfo.setEui(eui);
        sensorIotSetInfo.setSetData(setData);
        sensorIotSetInfo.setOperator(getUsername());
        return commandHandler.commandMessage(sensorIotSetInfo);
    }

    private SensorIotSetInfo selectSensorSetInfoByEui(String eui) {
        //select model_name workCenterName, product_id productId, device_id deviceId from ifp_sensor where eui = #{eui}
        QueryWrapper<SensorEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SensorEntity::getEui, eui);
        SensorEntity one = sensorService.getOne(queryWrapper);
        return SensorIotSetInfo.builder().modelName(one.getModelName()).productId(one.getProductId()).deviceId(one.getDeviceId()).build();
    }


    /**
     * 产线上报记录
     * 数据量过大，仅查询近三个月数据
     *
     * @return
     */
    @GetMapping("/record/by/operation/order")
    public ResponseData recordByOperationOrder(@RequestParam(value = "lineId", required = false) Integer lineId,
                                               @RequestParam(value = "startDate", required = false) String startDate,
                                               @RequestParam(value = "endDate", required = false) String endDate,
                                               @RequestParam(defaultValue = "1", value = "currentPage") int currentPage,
                                               @RequestParam(defaultValue = "10", value = "pageSize") int pageSize) {
        return success(sensorRecordService.getRecordListByOperationOrder(lineId, startDate, endDate, currentPage, pageSize));
    }


    /**
     * 导出产线上报记录
     *
     * @param lineId
     * @param startDate
     * @param endDate
     * @param operationNumber
     * @param response
     * @throws IOException
     */
    @GetMapping("/excel/export")
    public void exportExcel(@RequestParam(value = "lineId") Integer lineId,
                            @RequestParam(value = "startDate", required = false) String startDate,
                            @RequestParam(value = "endDate", required = false) String endDate,
                            @RequestParam(value = "operationNumber", required = false) String operationNumber,
                            @RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                            @RequestParam(value = "materialCode", required = false) String materialCode,
                            @RequestParam(value = "materialName", required = false) String materialName,
                            HttpServletResponse response) throws IOException {
        sensorRecordService.exportSensorRecord(lineId, startDate, endDate, response, operationNumber, workOrderNumber, materialCode, materialName);

    }

    /**
     * 根据采集设备类型获取采集设备
     *
     * @param sensorType
     * @return
     */
    @GetMapping("get/sensors/by/type")
    public ResponseData getListBySensorType(@RequestParam(value = "sensorType", required = false) String sensorType) {
        List<SensorEntity> sensorEntities = sensorService.getListBySensorType(sensorType);
        return ResponseData.success(sensorEntities);
    }

    /**
     * 根据sensorType查询IOT可绑定的productKey列表
     *
     * @return
     */
    @GetMapping("iot/product/key/list")
    public ResponseData getIotProductKeys(@RequestParam String sensorType) {
        return success(sensorProductService.getIotProductKeys(sensorType));
    }

    /**
     * 获取表里已存在的采集设备类型列表
     *
     * @return
     */
    @GetMapping("/table/exist/enum/list")
    public ResponseData getTableExistEnumList() {
        List<CommonState> list = new ArrayList<>();
        List<SensorEntity> sensorEntities = sensorService.getTableExistEnumList();
        for (SensorEntity entity : sensorEntities) {
            list.add(CommonState.builder().code(entity.getSensorType()).name(entity.getSensorTypeName()).build());
        }
        return success(list);
    }

    /**
     * 下载采集设备默认导入模板
     *
     * @param
     * @return
     */
    @GetMapping("/excel/import-template")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/sensorTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "采集设备导入模板" + Constant.XLSX);
    }

    /**
     * 采集设备excel导入
     *
     * @param
     * @return
     */
    @PostMapping("/excel/import")
    public ResponseData excelImport(MultipartFile file) {
        sensorService.excelImport(file, getUsername());
        return success();
    }

    /**
     * 采集设备excel导入进度
     *
     * @param
     * @return
     */
    @GetMapping("/excel/import-progress")
    public ResponseData getImportExcelProgress() {
        Object o = redisTemplate.opsForValue().get(RedisKeyPrefix.SENSOR_IMPORT_PROGRESS);
        if (o != null) {
            return success(o);
        }
        return fail();
    }

    /**
     * 采集设备-查询导入记录列表
     */
    @RequestMapping("/import/record")
    public ResponseData recordList(@RequestParam(required = false) String fileName,
                                   @RequestParam(required = false) String startTime,
                                   @RequestParam(required = false) String endTime,
                                   @RequestParam(required = false, defaultValue = "1") Integer current,
                                   @RequestParam(required = false, defaultValue = "10") Integer size) {
        return success(importDataRecordService.getList(ImportTypeEnum.SENSOR_IMPORT.getType(), fileName, startTime, endTime, current, size));
    }

    /**
     * 采集设备excel导出
     *
     * @param
     * @return
     */
    @PostMapping("/excel/export")
    public void export(@RequestBody SensorSelectDTO dto, HttpServletResponse response) throws IOException {
        List<SensorEntity> records = sensorService.listByPage(dto.getPageSize(), dto.getCurrentPage(), dto.getEui(), dto.getName(), dto.getSensorType()).getRecords();
        List<SensorExcelVO> exportList = JSON.parseArray(JSON.toJSONString(records), SensorExcelVO.class);
        EasyExcelUtil.export(response, "SensorFile", "SensorSheet", exportList, SensorExcelVO.class);
    }

}
