package com.yelink.dfs.controller.defect;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.defect.DefectStatementShowTypeEnum;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.defect.statement.DefectStatementLineDefectExportDTO;
import com.yelink.dfs.entity.defect.statement.DefectStatementMaterialExportDTO;
import com.yelink.dfs.entity.defect.statement.DefectStatementMaterialLineDefectExportDTO;
import com.yelink.dfs.entity.defect.statement.DefectStatementMaterialLineExportDTO;
import com.yelink.dfs.entity.defect.statement.DefectStatementSelectDTO;
import com.yelink.dfs.entity.defect.vo.DefectDistributionVO;
import com.yelink.dfs.entity.target.metrics.MetricsQualityLineMaterialDefectDailyEntity;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.defect.DefectStatementService;
import com.yelink.dfs.service.statement.impl.DefectDistributionExportHandler;
import com.yelink.dfs.utils.ExcelTemplateExportUtils;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Author: SQ
 * @Date: 2022/3/17 18:24
 * @Version:1.0
 */
@Data
@RestController
@RequiredArgsConstructor
@RequestMapping("/defect/statement")
public class DefectStatementController extends BaseController {
    private final DefectStatementService defectStatementService;
    private final FastDfsClientService fastDfsClientService;
    private final ModelUploadFileService modelUploadFileService;
    private final ExcelService excelService;

    /**
     * 质量报表按 产线、产品、不良等不同组合维度查询数据报表
     * @param selectDTO
     * @return
     */
    @PostMapping("/list/by/type")
    public ResponseData listByType(@RequestBody DefectStatementSelectDTO selectDTO) {
        if (StringUtils.isNotBlank(selectDTO.getShowType())) {
            Page<MetricsQualityLineMaterialDefectDailyEntity> data = defectStatementService.listByType(selectDTO);
            return ResponseData.success(data);
        }
        return success();
    }

    /**
     * 默认导出模板
     */
    @GetMapping("/default/export/template")
    public void defaultExportTemplate(@RequestParam(value = "showType") String showType, HttpServletResponse response) {

        if (DefectStatementShowTypeEnum.MATERIAL.getShowType().equals(showType)) {
            String fileName = "按产品汇总默认导出模板" + Constant.XLSX;
            ExcelTemplateExportUtils.downloadDefaultExportTemplate(response, fileName, DefectStatementMaterialExportDTO.class);
        }
        if (DefectStatementShowTypeEnum.MATERIAL_LINE.getShowType().equals(showType)) {
            String fileName = "按产品产线汇总默认导出模板" + Constant.XLSX;
            ExcelTemplateExportUtils.downloadDefaultExportTemplate(response, fileName, DefectStatementMaterialLineExportDTO.class);
        }
        if (DefectStatementShowTypeEnum.LINE_DEFECT.getShowType().equals(showType)) {
            String fileName = "按产线不良项汇总默认导出模板" + Constant.XLSX;
            ExcelTemplateExportUtils.downloadDefaultExportTemplate(response, fileName, DefectStatementLineDefectExportDTO.class);
        }
        if (DefectStatementShowTypeEnum.MATERIAL_LINE_DEFECT.getShowType().equals(showType)) {
            String fileName = "按产品产线不良项汇总默认导出模板" + Constant.XLSX;
            ExcelTemplateExportUtils.downloadDefaultExportTemplate(response, fileName, DefectStatementMaterialLineDefectExportDTO.class);
        }
    }

    /**
     * 列表导出
     */
    @PostMapping("/excel/exports")
    public void export(@RequestParam(required = false) Integer templateId,
                       @RequestBody DefectStatementSelectDTO selectDTO, HttpServletResponse response) {
        String showType = selectDTO.getShowType();
        if (StringUtils.isBlank(showType)) {
            return;
        }
        Page<MetricsQualityLineMaterialDefectDailyEntity> page = defectStatementService.listByType(selectDTO);
        List<MetricsQualityLineMaterialDefectDailyEntity> entities = page.getRecords();
        if (DefectStatementShowTypeEnum.MATERIAL.getShowType().equals(showType)) {
            convertExcel(templateId, "按产品汇总默认导出模板" + Constant.XLSX, DefectStatementMaterialExportDTO.class, entities, response);
        }
        if (DefectStatementShowTypeEnum.MATERIAL_LINE.getShowType().equals(showType)) {
            convertExcel(templateId, "按产品产线汇总默认导出模板" + Constant.XLSX, DefectStatementMaterialLineExportDTO.class, entities, response);
        }
        if (DefectStatementShowTypeEnum.LINE_DEFECT.getShowType().equals(showType)) {
            convertExcel(templateId, "按产线不良项汇总默认导出模板" + Constant.XLSX, DefectStatementLineDefectExportDTO.class, entities, response);
        }
        if (DefectStatementShowTypeEnum.MATERIAL_LINE_DEFECT.getShowType().equals(showType)) {
            convertExcel(templateId, "按产品产线不良项汇总默认导出模板" + Constant.XLSX, DefectStatementMaterialLineDefectExportDTO.class, entities, response);
        }
    }

    private void convertExcel(Integer templateId, String fileName, Class clazz, List<MetricsQualityLineMaterialDefectDailyEntity> list, HttpServletResponse response) {
        List data = JacksonUtil.convertArray(list, clazz);
        if (Objects.isNull(templateId)) {
            // 按默认模板下载
            ExcelTemplateExportUtils.downloadDefaultExportData(response, fileName, clazz, data);
        } else {
            // 按模板下载
            ModelUploadFileEntity uploadFile = modelUploadFileService.getById(templateId);
            byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
            ExcelTemplateExportUtils.downloadExportData(response, new ByteArrayInputStream(bytes), uploadFile.getFileName(), clazz, data);
        }
    }


    /**
     * 质量看板-不良项分布
     */
    @PostMapping("/defect/distribution")
    public ResponseData defectDistribution(@RequestBody DefectStatementSelectDTO selectDTO) {
        Assert.notNull(selectDTO.getCurrent(), RespCodeEnum.PARAM_CURRENT_EXCEPTION.getMsgDes());
        Assert.notNull(selectDTO.getSize(), RespCodeEnum.PARAM_SIZE_EXCEPTION.getMsgDes());
        Page<DefectDistributionVO> pageData = defectStatementService.listDefectDistribution(selectDTO);
        return success(pageData);
    }
    /**
     * 质量看板-不良项分布导出：默认导出模板
     */
    @GetMapping("/defect/distribution/default/export/template")
    public void defectDistributionDefaultExportTemplate(HttpServletResponse response) throws IOException {
        DefectStatementSelectDTO selectDTO = new DefectStatementSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        Page<DefectDistributionVO> pageData = defectStatementService.listDefectDistribution(selectDTO);
        EasyExcelUtil.export(response, "不良项分布默认导出模板", "数据源", pageData.getRecords(), DefectDistributionVO.class);
    }
    /**
     * 质量看板-不良项分布导出：导出
     */
    @PostMapping("/defect/distribution/excel/export")
    public ResponseData defectDistributionExport(@RequestParam(required = false) Integer templateId, @RequestBody DefectStatementSelectDTO selectDTO) {
        DataExportParam<DefectStatementSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.DEFECT_DISTRIBUTION.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.DEFECT_DISTRIBUTION.name());
        dataExportParam.setCreateUserCode(getUsername());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(DefectStatementSelectDTO.class.getName(), selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        parameters.put("templateId", templateId);
        parameters.put("defectDistribution", "数据源");
        dataExportParam.setParameters(parameters);
        return ResponseData.success(excelService.doExport(dataExportParam, DefectDistributionExportHandler.class));
    }
    /**
     * 质量看板-不良项分布导出:查看日志,分页查询当前导出任务列表
     */
    @GetMapping("/defect/distribution/list/task/page")
    public ResponseData defectDistributionTaskPage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                                   @RequestParam(required = false, defaultValue = "1") Integer currentPage) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.DEFECT_DISTRIBUTION.name());
        return success(excelService.listPage(excelTask, currentPage, pageSize));
    }
}
