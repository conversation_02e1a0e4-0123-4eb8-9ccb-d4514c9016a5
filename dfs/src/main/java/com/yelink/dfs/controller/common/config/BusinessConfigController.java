package com.yelink.dfs.controller.common.config;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.config.dto.BusinessConfigRespEnum;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeSelectDTO;
import com.yelink.dfscommon.entity.dfs.BusinessConfigEntity;
import com.yelink.dfscommon.entity.dfs.BusinessConfigValueEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1 15:01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/business/config")
public class BusinessConfigController extends BaseController {

    private final BusinessConfigService businessConfigService;
    private final BusinessConfigValueService businessConfigValueService;

    /**
     * 查询某节点以下的配置树
     *
     * @param dto 某节点的全路径编码
     * @return List<BusinessConfigEntity> 配置树结构
     */
    @PostMapping("/tree")
    public ResponseData getTreeByFullPathCode(@RequestBody FullPathCodeDTO dto) {
        List<BusinessConfigEntity> options = businessConfigService.getTreeByFullPathCode(dto.getFullPathCode(), false);
        return success(options);
    }

    /**
     * 查询某节点以下的配置树
     *
     * @param dto 某节点的全路径编码
     * @return List<BusinessConfigEntity> 配置树结构
     */
    @PostMapping("/value/tree")
    public ResponseData getValueTreeByFullPathCode(@RequestBody FullPathCodeDTO dto) {
        List<BusinessConfigEntity> options = businessConfigService.getTreeByFullPathCode(dto.getFullPathCode(), true);
        return success(options);
    }

    /**
     * 根据ID查询配置详情
     *
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable(value = "id") Integer id) {
        return success(businessConfigService.getEntityById(id));
    }

    /**
     * 根据编码查询配置详情
     *
     * @return
     */
    @PostMapping("/detail")
    public ResponseData detailByFullPathCode(@RequestBody FullPathCodeDTO dto) {
        BusinessConfigEntity one = businessConfigService.lambdaQuery().select(BusinessConfigEntity::getId)
                .eq(BusinessConfigEntity::getFullPathCode, dto.getFullPathCode()).one();
        if (one != null) {
            return success(businessConfigService.getEntityById(one.getId()));
        }
        return success();
    }

    /**
     * 保存配置值
     */
    @PutMapping("/update")
    public ResponseData update(@RequestBody BusinessConfigEntity configEntity) {
        configEntity.setUpdateTime(new Date());
        configEntity.setUpdateBy(getUsername());
        businessConfigService.updateEntityById(configEntity);
        return success(configEntity);
    }

    /**
     * 保存配置值
     */
    @PostMapping("/insert")
    public ResponseData add(@RequestBody BusinessConfigEntity configEntity) {
        configEntity.setCreateTime(new Date());
        configEntity.setUpdateTime(new Date());
        configEntity.setCreateBy(getUsername());
        configEntity.setUpdateBy(getUsername());
        businessConfigService.saveEntity(configEntity);
        return success(configEntity);
    }

    /**
     * 根据ID查询配置详情
     *
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public ResponseData deleteById(@PathVariable(value = "id") Integer id) {
        businessConfigService.deleteById(id);
        return success();
    }

    /**
     * 查询某节点的配置值，并包装成map返回，value是json格式数据
     *
     * @return
     */
    @PostMapping("/value/map")
    public ResponseData getValueMap(@RequestBody FullPathCodeDTO dto) {
        return success(businessConfigService.getValueMap(dto));
    }

    /**
     * 查询某节点及节点下所有子节点的配置值，并包装成实体类返回
     *
     * @return
     */
    @PostMapping("/value/dto")
    public ResponseData getValueDto(@RequestBody FullPathCodeSelectDTO dto) {
        return success(businessConfigService.getValueDto(dto, getRespClass(dto)));
    }

    private Class getRespClass(FullPathCodeSelectDTO dto){
        Class clazz = BusinessConfigRespEnum.getClassPathByClassName(dto.getClassName());
        if (clazz == null) {
            throw new ResponseException(RespCodeEnum.CLASS_NOT_FOUND);
        }
        return clazz;
    }

    /**
     * 查询某节点及节点下所有子节点的配置值，并包装成实体类列表返回
     * 响应结果实体需要继承BusinessConfigDTO
     *
     * @return
     */
    @PostMapping("/value/list")
    public ResponseData getValueList(@RequestBody FullPathCodeSelectDTO dto) {
        return success(businessConfigService.getValueDtoList(dto, getRespClass(dto)));
    }

    /**
     * 查询某节点及节点下所有子节点的配置值，并包装成实体类列表返回
     * 响应结果实体需要继承BusinessConfigDTO
     *
     * @return
     */
    @GetMapping("/get/value")
    public ResponseData getValue(@RequestParam String valueFullPathCode) {
        return success(businessConfigValueService.lambdaQuery().eq(BusinessConfigValueEntity::getValueFullPathCode, valueFullPathCode).one());
    }

}
