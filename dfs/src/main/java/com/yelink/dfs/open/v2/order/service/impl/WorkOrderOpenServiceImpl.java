package com.yelink.dfs.open.v2.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.common.QueryDTO;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.order.CustomerEntity;
import com.yelink.dfs.entity.order.CustomerMaterialListEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderInvestCheckResultEntity;
import com.yelink.dfs.entity.order.WorkOrderLineRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderTeamEntity;
import com.yelink.dfs.entity.order.WorkOrderTeamRelevanceEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderSmartUpdateDTO;
import com.yelink.dfs.entity.pack.PackageSchemeEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.entity.receipt.ReceiptProjectContractEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderUpdateInsertDTO;
import com.yelink.dfs.open.v2.common.dto.AppendixDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderCraftProcedureRelationUpdateDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderDetailQueryDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderListDetailDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderListQueryDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderQuerySqlDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderRelevanceUpdateDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderStateChangeDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderUpdateDTO;
import com.yelink.dfs.open.v2.order.service.WorkOrderOpenService;
import com.yelink.dfs.open.v2.order.vo.OrderApprovalVO;
import com.yelink.dfs.open.v2.order.vo.ProductBasicUnitInvestRecordVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderBasicUnitRelationVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderBatchVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderCraftProcedureRelationVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderCustomerMaterialVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderCustomerVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderDetailVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderDirectAccessVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderExtendFieldVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderHoursVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderInvestCheckResultVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderMagVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderMainVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderPackageSchemeRelationVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderPlanVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderProjectRelationVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderRelatedWorkCenterVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderRelevanceVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderSchedulingVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderTeamMemberVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderWarehouseRelationVO;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.CustomerMaterialListService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitInputRecordService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderInvestCheckResultService;
import com.yelink.dfs.service.order.WorkOrderLineRelevanceService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.order.WorkOrderTeamRelevanceService;
import com.yelink.dfs.service.pack.PackageSchemeService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.receipt.ReceiptProjectContractService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.user.TeamTypeDefService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.constant.pms.ReceiptTypePMSEnum;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderDetailDTO;
import com.yelink.dfscommon.dto.dfs.ReportDTO;
import com.yelink.dfscommon.dto.pms.ProjectOrderSelectDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderMaterialEntity;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
public class WorkOrderOpenServiceImpl implements WorkOrderOpenService {

    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private WorkOrderMapper workOrderMapper;
    @Resource
    private WorkOrderBasicUnitRelationService basicUnitRelationService;
    @Resource
    private WorkOrderBasicUnitInputRecordService workOrderBasicUnitInputRecordService;
    @Resource
    private SkuService skuService;
    @Resource
    private ModelService modelService;
    @Resource
    private ProductionLineService lineService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private SysTeamService teamService;
    @Resource
    private TeamTypeDefService teamTypeDefService;
    @Resource
    protected SysUserService userService;
    @Resource
    protected CustomerService customerService;
    @Resource
    protected ExtProductOrderInterface extProductOrderInterface;
    @Resource
    protected ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    protected CraftService craftService;
    @Resource
    protected WorkCenterService workCenterService;
    @Autowired
    protected ProcedureService procedureService;
    @Resource
    private WorkOrderDeviceRelevanceService workOrderDeviceRelevanceService;
    @Resource
    private WorkOrderTeamRelevanceService workOrderTeamRelevanceService;
    @Resource
    private WorkOrderLineRelevanceService workOrderLineRelevanceService;
    @Resource
    private PackageSchemeService packageSchemeService;
    @Resource
    protected WorkOrderPlanService workOrderPlanService;
    @Resource
    private BarCodeService barCodeService;
    @Resource
    private WorkOrderInvestCheckResultService investCheckResultService;
    @Resource
    private ReceiptProjectContractService receiptProjectContractService;
    @Resource
    private MaintainRecordService maintainRecordService;
    @Resource
    protected RedisTemplate<String, Object> redisTemplate;

    @Override
    public WorkOrderDetailVO getWorkOrderDetailInfo(WorkOrderDetailQueryDTO dto) {
        // 组装查询条件dto
        WorkOrderDetailDTO detailDTO = WorkOrderDetailDTO.builder()
                .workOrderNumber(dto.getWorkOrderNumber())
                .isShowFileInfo(dto.getQueryAddition().contains("file"))
                .isShowMaterial(dto.getQueryAddition().contains("materialFields"))
                .isShowWarehouseInfo(dto.getQueryAddition().contains("warehouse"))
                .isShowCraftProcedureInfo(dto.getQueryAddition().contains("craftProcedure"))
                .isShowWorkCenterAndProductBasicUnitInfo(dto.getQueryAddition().contains("workCenter"))
                .isShowAssociatedResourcesInfo(dto.getQueryAddition().contains("relatedResources"))
                .isShowPackageSchemeInfo(dto.getQueryAddition().contains("packageScheme"))
                .isShowPlanTheoryHourInfo(dto.getQueryAddition().contains("workOrderHours"))
                .isShowProjectContractInfo(dto.getQueryAddition().contains("projectContractInfo"))
                .isShowMaintainInfo(dto.getQueryAddition().contains("maintain"))
                .isShowBatchInfo(dto.getQueryAddition().contains("batch"))
                .isShowWorkOrderPlanInfo(dto.getQueryAddition().contains("workOrderPlan"))
                .isShowDirectAccessInfo(dto.getQueryAddition().contains("directAccess"))
                .isShowWorkOrderInvestCheckResultInfo(dto.getQueryAddition().contains("workOrderInvestCheckResult"))
                .isShowProductBasicUnitInvestRecordInfo(dto.getQueryAddition().contains("productBasicUnitInvestRecord"))
                .isShowRelateOrder(false)
                .build();
        WorkOrderEntity workOrderEntity = workOrderService.getWorkOrderById(detailDTO);
        return getDetailVO(workOrderEntity);
    }

    /**
     * 转成DetailVO
     *
     * @param workOrderEntity
     * @return
     */
    @Override
    public WorkOrderDetailVO getDetailVO(WorkOrderEntity workOrderEntity) {
        WorkOrderDetailVO workOrderDetailVO = JacksonUtil.convertObject(workOrderEntity, WorkOrderDetailVO.class);
        // 组装工作中心
        WorkOrderRelatedWorkCenterVO relatedWorkCenterVO = WorkOrderRelatedWorkCenterVO.builder()
                .workCenterId(workOrderEntity.getWorkCenterId())
                .workCenterType(workOrderEntity.getWorkCenterType())
                .workCenterCode(workOrderEntity.getWorkCenterCode())
                .workCenterName(workOrderEntity.getWorkCenterName())
                .build();
        // 组装生产基本单元
        List<WorkOrderBasicUnitRelationVO> basicUnitRelationVOS = CollectionUtils.isEmpty(workOrderEntity.getProductBasicUnits()) ? new ArrayList<>() :
                workOrderEntity.getProductBasicUnits()
                        .stream().map(o -> {
                            // 获取生产基本单元类型id和对应的名称
                            String modelCode = null;
                            Integer modelId = null;
                            if (o.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode())) {
                                modelId = lineService.lambdaQuery().select(ProductionLineEntity::getModelId)
                                        .eq(ProductionLineEntity::getProductionLineId, o.getProductionBasicUnitId())
                                        .one().getModelId();
                                modelCode = modelService.getById(modelId).getCode();
                            } else if (o.getWorkCenterType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
                                modelId = teamService.lambdaQuery().select(SysTeamEntity::getTeamType)
                                        .eq(SysTeamEntity::getId, o.getProductionBasicUnitId())
                                        .one().getTeamType();
                                modelCode = teamTypeDefService.getById(modelId).getTypeDefCode();
                            } else if (o.getWorkCenterType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
                                modelId = deviceService.lambdaQuery().select(DeviceEntity::getModelId)
                                        .eq(DeviceEntity::getDeviceId, o.getProductionBasicUnitId())
                                        .one().getModelId();
                                modelCode = modelService.getById(modelId).getCode();
                            }

                            // 查询该生产基本单元正在被占用的工单
                            String occupiedWorkOrderNumber = basicUnitRelationService.getOccupiedWorkOrderNumber(o.getWorkCenterId(), o.getProductionBasicUnitId());
                            return WorkOrderBasicUnitRelationVO.builder()
                                    .productionBasicUnitTypeId(modelId)
                                    .productionBasicUnitTypeCode(modelCode)
                                    .productionBasicUnitId(o.getProductionBasicUnitId())
                                    .productionBasicUnitCode(o.getProductionBasicUnitCode())
                                    .productionBasicUnitName(o.getProductionBasicUnitName())
                                    .isProducing(o.getIsProducing())
                                    .occupiedWorkOrderNumber(occupiedWorkOrderNumber)
                                    .build();
                        })
                        .collect(Collectors.toList());
        // 组装班组成员
        List<WorkOrderTeamMemberVO> teamMemberVOS = CollectionUtils.isEmpty(workOrderEntity.getWorkOrderTeamEntities()) ? new ArrayList<>() :
                workOrderEntity.getWorkOrderTeamEntities().stream().map(
                        o -> WorkOrderTeamMemberVO.builder()
                                .teamRoleId(o.getTeamRole())
                                .memberUserName(o.getMemberName())
                                .build()
                ).collect(Collectors.toList());
        // 组装审批字段
        OrderApprovalVO approvalVO = OrderApprovalVO.builder()
                .approver(workOrderEntity.getApprover())
                .approverName(workOrderEntity.getApproverName())
                .actualApprover(workOrderEntity.getActualApprover())
                .actualApproverSignatureUrl(workOrderEntity.getActualApproverSignatureUrl())
                .actualApproverName(workOrderEntity.getActualApproverName())
                .approvalStatus(workOrderEntity.getApprovalStatus())
                .approvalStatusName(workOrderEntity.getApprovalStatusName())
                .approvalSuggestion(workOrderEntity.getApprovalSuggestion())
                .approvalTime(workOrderEntity.getApprovalTime())
                .build();
        // 组装工艺工序
        List<WorkOrderCraftProcedureRelationVO> craftProcedureRelationVOS = CollectionUtils.isEmpty(workOrderEntity.getCraftProcedureEntities()) ? new ArrayList<>() :
                workOrderEntity.getCraftProcedureEntities().stream().map(
                        o -> WorkOrderCraftProcedureRelationVO.builder()
                                .craftProcedureId(o.getId())
                                .procedureId(o.getProcedureId())
                                .procedureName(o.getProcedureName())
                                .workCenterType(o.getType())
                                .workCenterIds(o.getWorkCenterIds())
                                .workCenterNames(o.getWorkCenterNames())
                                .facType(o.getFacType())
                                .build()
                ).collect(Collectors.toList());
        // 组装包装方案
        List<WorkOrderPackageSchemeRelationVO> packageSchemeRelationVOS = CollectionUtils.isEmpty(workOrderEntity.getPackageSchemeEntities()) ? new ArrayList<>() :
                workOrderEntity.getPackageSchemeEntities().stream().map(
                        o -> WorkOrderPackageSchemeRelationVO.builder()
                                .schemeCode(o.getSchemeCode())
                                .schemeName(o.getSchemeName())
                                .build()
                ).collect(Collectors.toList());
        // 组装工单计划列表
        List<WorkOrderPlanVO> planVOS = CollectionUtils.isEmpty(workOrderEntity.getWorkOrderPlanList()) ? new ArrayList<>() :
                workOrderEntity.getWorkOrderPlanList().stream().map(
                        o -> WorkOrderPlanVO.builder()
                                .time(o.getTime())
                                .planQuantity(o.getPlanQuantity())
                                .productionBasicUnitType(o.getProductionBasicUnitType())
                                .productionBasicUnitId(o.getProductionBasicUnitId())
                                .productionBasicUnitName(o.getProductionBasicUnitName())
                                .isMain(o.getIsMain())
                                .finishCount(o.getFinishCount())
                                .build()
                ).collect(Collectors.toList());
        // 组装生产基本单元投产记录列表
        List<ProductBasicUnitInvestRecordVO> basicUnitInvestRecordVOS = CollectionUtils.isEmpty(workOrderEntity.getProductBasicUnitInputRecords()) ? new ArrayList<>() :
                workOrderEntity.getProductBasicUnitInputRecords().stream().map(
                        o -> ProductBasicUnitInvestRecordVO.builder()
                                .workOrderNumber(o.getWorkOrderNumber())
                                .workCenterId(o.getWorkCenterId())
                                .productionBasicUnitType(o.getWorkCenterType())
                                .productionBasicUnitId(o.getProductionBasicUnitId())
                                .productionBasicUnitName(o.getProductionBasicUnitName())
                                .startTime(o.getStartTime())
                                .endTime(o.getEndTime())
                                .username(o.getUsername())
                                .build()
                ).collect(Collectors.toList());
        // 组装合同对象
        WorkOrderProjectRelationVO projectRelationVO = WorkOrderProjectRelationVO.builder()
                .contractId(workOrderEntity.getContractId())
                .contractName(workOrderEntity.getContractName())
                .projectDefineId(workOrderEntity.getProjectDefineId())
                .projectDefineName(workOrderEntity.getProjectDefineName())
                .projectContract(workOrderEntity.getProjectContract())
                .build();
        // 组装仓库相关字段
        WorkOrderWarehouseRelationVO warehouseRelationVO = WorkOrderWarehouseRelationVO.builder()
                .currentInventory(workOrderEntity.getCurrentInventory())
                .pickingQuantity(workOrderEntity.getPickingQuantity())
                .inventoryQuantity(workOrderEntity.getInventoryQuantity())
                .build();
        // 客户相关字段
        workOrderDetailVO.setWorkOrderCustomerVO(WorkOrderCustomerVO.builder()
                .customerCode(workOrderEntity.getCustomerCode())
                .customerName(workOrderEntity.getCustomerName()).build());
        // 管理员相关字段
        workOrderDetailVO.setWorkOrderMagVO(WorkOrderMagVO.builder()
                .magName(workOrderEntity.getMagName())
                .magNickname(workOrderEntity.getMagNickname())
                .magPhone(workOrderEntity.getMagPhone()).build());
        // 客户物料相关字段
        workOrderDetailVO.setWorkOrderCustomerMaterialVO(WorkOrderCustomerMaterialVO.builder()
                .customerMaterialCode(workOrderEntity.getCustomerMaterialCode())
                .customerMaterialName(workOrderEntity.getCustomerMaterialName())
                .customerSpecification(workOrderEntity.getCustomerSpecification()).build());
        // 投产检查结果相关字段
        workOrderDetailVO.setWorkOrderInvestCheckResult(WorkOrderInvestCheckResultVO.builder()
                .investCheckResult(workOrderEntity.getInvestCheckResult())
                .investCheckResultName(workOrderEntity.getInvestCheckResultName())
                .investCheckResultDetail(workOrderEntity.getInvestCheckResultDetail())
                .build());
        // 排产相关字段
        workOrderDetailVO.setSchedulingVO(WorkOrderSchedulingVO.builder()
                .schedulingCount(workOrderEntity.getSchedulingCount())
                .schedulingSequence(workOrderEntity.getSchedulingSequence())
                .schedulingStateName(workOrderEntity.getSchedulingStateName())
                .schedulingState(workOrderEntity.getSchedulingState())
                .build());
        // 工单相关工时信息
        workOrderDetailVO.setWorkOrderHours(
                WorkOrderHoursVO.builder()
                        .actualWorkingHours(workOrderEntity.getActualWorkingHours())
                        .plannedWorkingHours(workOrderEntity.getPlannedWorkingHours())
                        .effectiveHours(workOrderEntity.getEffectiveHours())
                        .effectiveWorkingHour(workOrderEntity.getEffectiveWorkingHour())
                        .planTheoryHour(workOrderEntity.getPlanTheoryHour())
                        .produceTheoryHour(workOrderEntity.getProduceTheoryHour())
                        .theoryHour(workOrderEntity.getTheoryHour())
                        .build());
        // 批次相关信息
        workOrderDetailVO.setBatch(WorkOrderBatchVO.builder()
                .plannedBatches(workOrderEntity.getPlannedBatches())
                .plansPerBatch(workOrderEntity.getPlansPerBatch())
                .actualBatches(workOrderEntity.getActualBatches())
                .relatedBarCodePlanSum(workOrderEntity.getRelatedBarCodePlanSum())
                .build());
        // 直通信息
        workOrderDetailVO.setDirectAccess(WorkOrderDirectAccessVO.builder()
                .directAccessQuantity(workOrderEntity.getDirectAccessQuantity())
                .directAccessRate(workOrderEntity.getDirectAccessRate())
                .build());
        // 关联的维修信息
        workOrderDetailVO.setMaintain(WorkOrderMainVO.builder().maintainCount(workOrderEntity.getMaintainCount()).build());
        // 工单扩展字段
        workOrderDetailVO.setWorkOrderExtendFieldVO(WorkOrderExtendFieldVO.builder()
                .workOrderExtendFieldOne(workOrderEntity.getWorkOrderExtendFieldOne())
                .workOrderExtendFieldTwo(workOrderEntity.getWorkOrderExtendFieldTwo())
                .workOrderExtendFieldThree(workOrderEntity.getWorkOrderExtendFieldThree())
                .workOrderExtendFieldFour(workOrderEntity.getWorkOrderExtendFieldFour())
                .workOrderExtendFieldFive(workOrderEntity.getWorkOrderExtendFieldFive())
                .workOrderExtendFieldSix(workOrderEntity.getWorkOrderExtendFieldSix())
                .workOrderExtendFieldSeven(workOrderEntity.getWorkOrderExtendFieldSeven())
                .workOrderExtendFieldEight(workOrderEntity.getWorkOrderExtendFieldEight())
                .workOrderExtendFieldNine(workOrderEntity.getWorkOrderExtendFieldNine())
                .workOrderExtendFieldTen(workOrderEntity.getWorkOrderExtendFieldTen())
                .workOrderMaterialExtendFieldOne(workOrderEntity.getWorkOrderMaterialExtendFieldOne())
                .workOrderMaterialExtendFieldTwo(workOrderEntity.getWorkOrderMaterialExtendFieldTwo())
                .workOrderMaterialExtendFieldThree(workOrderEntity.getWorkOrderMaterialExtendFieldThree())
                .workOrderMaterialExtendFieldFour(workOrderEntity.getWorkOrderMaterialExtendFieldFour())
                .workOrderMaterialExtendFieldFive(workOrderEntity.getWorkOrderMaterialExtendFieldFive())
                .workOrderMaterialExtendFieldSix(workOrderEntity.getWorkOrderMaterialExtendFieldSix())
                .workOrderMaterialExtendFieldSeven(workOrderEntity.getWorkOrderMaterialExtendFieldSeven())
                .workOrderMaterialExtendFieldEight(workOrderEntity.getWorkOrderMaterialExtendFieldEight())
                .workOrderMaterialExtendFieldNine(workOrderEntity.getWorkOrderMaterialExtendFieldNine())
                .workOrderMaterialExtendFieldTen(workOrderEntity.getWorkOrderMaterialExtendFieldTen())
                .build());
        // 生产基本单元投产记录
        workOrderDetailVO.setProductBasicUnitInvestRecord(basicUnitInvestRecordVOS);
        // 工单计划
        workOrderDetailVO.setWorkOrderPlan(planVOS);
        // 审批相关
        workOrderDetailVO.setOrderApprovalVO(approvalVO);
        // 工单附件相关
        List<AppendixDTO> fileVOS = CollectionUtils.isEmpty(workOrderEntity.getAppendixEntities()) ? new ArrayList<>() :
                JacksonUtil.convertArray(workOrderEntity.getAppendixEntities(), AppendixDTO.class);
        workOrderDetailVO.setFile(fileVOS);
        // 工艺工序相关
        workOrderDetailVO.setCraftProcedure(craftProcedureRelationVOS);
        // 生产资源相关
        workOrderDetailVO.setWorkCenter(relatedWorkCenterVO);
        // 仓库相关
        workOrderDetailVO.setWarehouse(warehouseRelationVO);
        // 包装方案相关
        workOrderDetailVO.setPackageScheme(packageSchemeRelationVOS);
        // 合同相关
        workOrderDetailVO.setProjectContractInfo(projectRelationVO);
        // 生产基本单元
        workOrderDetailVO.setProductBasicUnits(basicUnitRelationVOS);
        // 关联资源
        workOrderDetailVO.setRelatedResources(workOrderEntity.getWorkOrderRelevanceVOS());
        // 班组成员
        workOrderDetailVO.setWorkOrderTeamEntities(teamMemberVOS);
        return workOrderDetailVO;
    }

    @Override
    public Page<WorkOrderDetailVO> getList(WorkOrderListQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
//        Map<String, QueryDTO<WorkOrderListQueryDTO.CustomerQueryCriteriaDTO>> dtos = SqlUtil.split(dto, "workOrder");
//        // 字段过滤
//        QueryDTO<WorkOrderListQueryDTO.CustomerQueryCriteriaDTO> workOrderDTO = dtos.get("workOrder");
//        QueryDTO<WorkOrderListQueryDTO.CustomerQueryCriteriaDTO> workOrderBasicUnitRelationDTO = dtos.get("workOrderBasicUnitRelation");
//        QueryDTO<WorkOrderListQueryDTO.CustomerQueryCriteriaDTO> workOrderProcedureRelationDTO = dtos.get("workOrderProcedureRelation");
//        QueryDTO<WorkOrderListQueryDTO.CustomerQueryCriteriaDTO> materialDTO = dtos.get("material");
//        // 重新组转成sql
//        WorkOrderQuerySqlDTO workOrderQuerySqlDTO = WorkOrderQuerySqlDTO.builder()
//                .workOrderSql(SqlUtil.getSelectSql(workOrderDTO, false))
//                .workOrderBasicUnitRelationSql(SqlUtil.getSelectSql(workOrderBasicUnitRelationDTO, false))
//                .workOrderProcedureRelationSql(SqlUtil.getSelectSql(workOrderProcedureRelationDTO, false))
//                .materialSql(SqlUtil.getSelectSql(materialDTO, false))
//                .orderBySql(
//                        Stream.of(SqlUtil.getSortSql(workOrderDTO, false))
//                                .filter(StringUtils::isNotBlank).collect(Collectors.joining( Constant.SEP))
//                )
//                .build();
//        Page<WorkOrderEntity> workOrderPage = workOrderMapper.getList(workOrderQuerySqlDTO, dto.getPage());
        Page<WorkOrderEntity> workOrderPage = workOrderMapper.getList(sql, dto.getPage());
        if (CollectionUtils.isEmpty(workOrderPage.getRecords())) {
            return new Page<>();
        }
        // 组装查询条件dto
        WorkOrderListDetailDTO detailDTO = WorkOrderListDetailDTO.builder()
                .workOrderEntities(workOrderPage.getRecords())
                .isShowFileInfo(dto.getQueryAddition().contains("file"))
                .isShowMaterial(dto.getQueryAddition().contains("materialFields"))
                .isShowWarehouseInfo(dto.getQueryAddition().contains("warehouse"))
                .isShowCraftProcedureInfo(dto.getQueryAddition().contains("craftProcedure"))
                .isShowWorkCenterAndProductBasicUnitInfo(dto.getQueryAddition().contains("workCenter"))
                .isShowAssociatedResourcesInfo(dto.getQueryAddition().contains("relatedResources"))
                .isShowPackageSchemeInfo(dto.getQueryAddition().contains("packageScheme"))
                .isShowPlanTheoryHourInfo(dto.getQueryAddition().contains("workOrderHours"))
                .isShowProjectContractInfo(dto.getQueryAddition().contains("projectContractInfo"))
                .isShowMaintainInfo(dto.getQueryAddition().contains("maintain"))
                .isShowBatchInfo(dto.getQueryAddition().contains("batch"))
                .isShowWorkOrderPlanInfo(dto.getQueryAddition().contains("workOrderPlan"))
                .isShowDirectAccessInfo(dto.getQueryAddition().contains("directAccess"))
                .isShowWorkOrderInvestCheckResultInfo(dto.getQueryAddition().contains("workOrderInvestCheckResult"))
                .isShowProductBasicUnitInvestRecordInfo(dto.getQueryAddition().contains("productBasicUnitInvestRecord"))
                .isShowRelateOrder(false)
                .build();
        // 转换成VO
        List<WorkOrderDetailVO> detailVOS = showDetailInfo(detailDTO);
        Page<WorkOrderDetailVO> page = new Page<>(dto.getCurrent(), dto.getSize());
        page.setRecords(detailVOS);
        page.setTotal(workOrderPage.getTotal());
        return page;
    }

    private List<WorkOrderDetailVO> showDetailInfo(WorkOrderListDetailDTO dto) {
        List<WorkOrderDetailVO> detailVOs = new ArrayList<>();
        List<WorkOrderEntity> workOrderEntities = dto.getWorkOrderEntities();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return detailVOs;
        }
        List<String> workOrderNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        List<Integer> workOrderIds = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        // 参数定义集合
        Map<String, List<AppendixEntity>> fileMap = new HashMap<>();
        Map<String, com.yelink.dfs.entity.product.MaterialEntity> materialMap = new HashMap<>();
        Map<String, List<CraftProcedureEntity>> craftProcedureMap = new HashMap<>();
        Map<Integer, WorkCenterEntity> workCenterMap = new HashMap<>();
        Map<Integer, List<WorkOrderDeviceRelevanceEntity>> relevanceDeviceMap = new HashMap<>();
        Map<Integer, List<WorkOrderTeamRelevanceEntity>> relevanceTeamMap = new HashMap<>();
        Map<Integer, List<WorkOrderLineRelevanceEntity>> relevanceLineMap = new HashMap<>();
        Map<Integer, String> deviceMap = new HashMap<>();
        Map<Integer, String> teamMap = new HashMap<>();
        Map<Integer, String> lineMap = new HashMap<>();
        Map<String, List<WorkOrderBasicUnitInputRecordEntity>> basicUnitInputRecordMap = new HashMap<>();
        Map<String, List<PackageSchemeEntity>> packageSchemeMap = new HashMap<>();
        Map<String, List<WorkOrderPlanEntity>> workOrderPlanMap = new HashMap<>();
        Map<String, List<BarCodeEntity>> barCodeMap = new HashMap<>();
        Map<String, WorkOrderInvestCheckResultEntity> investCheckResultMap = new HashMap<>();
        Map<String, ReceiptProjectContractEntity> receiptMap = new HashMap<>();
        Map<String, List<MaintainRecordEntity>> maintainRecordMap = new HashMap<>();
        Map<String, Double> workOrderTheoryHourMap = new HashMap<>();
        // 展示必要信息
        workOrderService.showStateAndName(workOrderEntities);
        // 展示附加信息
        // 工单附件
        if (Objects.nonNull(dto.getIsShowFileInfo()) && dto.getIsShowFileInfo()) {
            AppendixService appendixService = SpringUtil.getBean(AppendixService.class);
            fileMap = appendixService.lambdaQuery()
                    .eq(AppendixEntity::getType, AppendixTypeEnum.WORKORDER_APPENDIX.getCode())
                    .in(AppendixEntity::getRelateId, workOrderIds).list().stream().collect(Collectors.groupingBy(AppendixEntity::getRelateId));
        }
        // 工单物料
        if (Objects.nonNull(dto.getIsShowMaterial()) && dto.getIsShowMaterial()) {
            MaterialService materialService = SpringUtil.getBean(MaterialService.class);
            List<MaterialCodeAndSkuIdSelectDTO> materials = workOrderEntities.stream()
                    .map(entity -> MaterialCodeAndSkuIdSelectDTO.builder()
                            .materialCode(entity.getMaterialCode())
                            .skuId(entity.getSkuId()).build())
                    .collect(Collectors.toList());
            materialMap = materialService.getMaterialEntity(materials);
        }
        // 查询工艺工序
        if (Objects.nonNull(dto.getIsShowCraftProcedureInfo()) && dto.getIsShowCraftProcedureInfo()) {
            CraftProcedureService craftProcedureService = SpringUtil.getBean(CraftProcedureService.class);
            WorkOrderProcedureRelationService workOrderProcedureRelationService = SpringUtil.getBean(WorkOrderProcedureRelationService.class);
            Map<String, List<WorkOrderProcedureRelationEntity>> map = workOrderProcedureRelationService.lambdaQuery().in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.groupingBy(WorkOrderProcedureRelationEntity::getWorkOrderNumber));
            for (Map.Entry<String, List<WorkOrderProcedureRelationEntity>> entry : map.entrySet()) {
                List<Integer> craftProcedureIds = entry.getValue().stream()
                        .map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toList());
                List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.listByIds(craftProcedureIds);
                craftProcedureMap.put(entry.getKey(), craftProcedureEntities);
            }
        }
        // 查询关联的工作中心及生产基本单元信息
        if (Objects.nonNull(dto.getIsShowWorkCenterAndProductBasicUnitInfo()) && dto.getIsShowWorkCenterAndProductBasicUnitInfo()) {
            WorkCenterService workCenterService = SpringUtil.getBean(WorkCenterService.class);
            List<Integer> workCenterIds = workOrderEntities.stream().map(WorkOrderEntity::getWorkCenterId).distinct().collect(Collectors.toList());
            workCenterMap = workCenterService.listByIds(workCenterIds).stream().collect(Collectors.toMap(WorkCenterEntity::getId, o -> o));
        }
        // 查询关联资源
        if (Objects.nonNull(dto.getIsShowAssociatedResourcesInfo()) && dto.getIsShowAssociatedResourcesInfo()) {
            List<WorkOrderDeviceRelevanceEntity> relevanceDevices = workOrderDeviceRelevanceService.lambdaQuery().in(WorkOrderDeviceRelevanceEntity::getWorkOrderId, workOrderIds).list();
            if (CollectionUtils.isNotEmpty(relevanceDevices)) {
                relevanceDeviceMap = relevanceDevices.stream().collect(Collectors.groupingBy(WorkOrderDeviceRelevanceEntity::getWorkOrderId));
                List<Integer> deviceIds = relevanceDevices.stream().map(WorkOrderDeviceRelevanceEntity::getDeviceId).distinct().collect(Collectors.toList());
                List<DeviceEntity> deviceEntities = deviceService.listByIds(deviceIds);
                deviceMap = deviceEntities.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
            }
            List<WorkOrderTeamRelevanceEntity> relevanceTeams = workOrderTeamRelevanceService.lambdaQuery().in(WorkOrderTeamRelevanceEntity::getWorkOrderId, workOrderIds).list();
            if (CollectionUtils.isNotEmpty(relevanceTeams)) {
                relevanceTeamMap = relevanceTeams.stream().collect(Collectors.groupingBy(WorkOrderTeamRelevanceEntity::getWorkOrderId));
                List<Integer> teamIds = relevanceTeams.stream().map(WorkOrderTeamRelevanceEntity::getTeamId).distinct().collect(Collectors.toList());
                List<SysTeamEntity> teamEntities = teamService.listByIds(teamIds);
                teamMap = teamEntities.stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamName));
            }
            List<WorkOrderLineRelevanceEntity> relevanceLines = workOrderLineRelevanceService.lambdaQuery().in(WorkOrderLineRelevanceEntity::getWorkOrderId, workOrderIds).list();
            if (CollectionUtils.isNotEmpty(relevanceLines)) {
                relevanceLineMap = relevanceLines.stream().collect(Collectors.groupingBy(WorkOrderLineRelevanceEntity::getWorkOrderId));
                List<Integer> lineIds = relevanceLines.stream().map(WorkOrderLineRelevanceEntity::getLineId).distinct().collect(Collectors.toList());
                List<ProductionLineEntity> lineEntities = lineService.listByIds(lineIds);
                lineMap = lineEntities.stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
            }
        }
        // 查询生产基本单元投产记录列表
        if (Objects.nonNull(dto.getIsShowProductBasicUnitInvestRecordInfo()) && dto.getIsShowProductBasicUnitInvestRecordInfo()) {
            basicUnitInputRecordMap = workOrderBasicUnitInputRecordService.lambdaQuery().in(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.groupingBy(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber));
        }
        // 获取包装方案
        if (Objects.nonNull(dto.getIsShowPackageSchemeInfo()) && dto.getIsShowPackageSchemeInfo()) {
            List<String> packagesCodes = workOrderEntities.stream().map(WorkOrderEntity::getPackageSchemeCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(packagesCodes)) {
                packageSchemeMap = packageSchemeService.lambdaQuery().in(PackageSchemeEntity::getSchemeCode, packagesCodes)
                        .list().stream().collect(Collectors.groupingBy(PackageSchemeEntity::getSchemeCode));
            }
        }
        // 获取工单计划
        if (Objects.nonNull(dto.getIsShowWorkOrderPlanInfo()) && dto.getIsShowWorkOrderPlanInfo()) {
            workOrderPlanMap = workOrderPlanService.getByWorkOrderNumbers(workOrderNumbers, true);
        }
        // 设置批次计划总数
        if (Objects.nonNull(dto.getIsShowBatchInfo()) && dto.getIsShowBatchInfo()) {
            barCodeMap = barCodeService.lambdaQuery().eq(BarCodeEntity::getRuleType, BarCodeTypeEnum.FINISHED.getCode())
                    .in(BarCodeEntity::getRelateNumber, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.groupingBy(BarCodeEntity::getRelateNumber));
        }
        // 展示投产结果明细
        if (Objects.nonNull(dto.getIsShowWorkOrderInvestCheckResultInfo()) && dto.getIsShowWorkOrderInvestCheckResultInfo()) {
            investCheckResultMap = investCheckResultService.lambdaQuery().in(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.toMap(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, o -> o));
        }
        // 查询项目合同
        if (Objects.nonNull(dto.getIsShowProjectContractInfo()) && dto.getIsShowProjectContractInfo()) {
            ProjectOrderSelectDTO projectOrderSelectDTO = ProjectOrderSelectDTO.builder()
                    .relationType(ReceiptTypePMSEnum.PRUDUCT_WORK_ORDER.getCode())
                    .relationNumbers(workOrderNumbers).build();
            List<ReceiptProjectContractEntity> receiptList = receiptProjectContractService.getListByOrder(projectOrderSelectDTO);
            receiptMap = receiptList.stream().collect(Collectors.toMap(ReceiptProjectContractEntity::getRelationNumber, Function.identity(), (k1, k2) -> k1));
        }
        // 获取维修数量
        if (Objects.nonNull(dto.getIsShowMaintainInfo()) && dto.getIsShowMaintainInfo()) {
            maintainRecordMap = maintainRecordService.lambdaQuery().in(MaintainRecordEntity::getWorkOrder, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.groupingBy(MaintainRecordEntity::getWorkOrder));
        }
        // 查询理论工时
        if (Objects.nonNull(dto.getIsShowPlanTheoryHourInfo()) && dto.getIsShowPlanTheoryHourInfo()) {
            workOrderTheoryHourMap = workOrderService.workOrderTheoryHourMap(workOrderEntities);
        }
        // 查询直通信息
        if (Objects.nonNull(dto.getIsShowDirectAccessInfo()) && dto.getIsShowDirectAccessInfo()) {
            maintainRecordMap = maintainRecordService.lambdaQuery().in(MaintainRecordEntity::getWorkOrder, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.groupingBy(MaintainRecordEntity::getWorkOrder));
        }
        // skuId
        List<Integer> skuIds = workOrderEntities.stream()
                .map(entity -> {
                    Integer skuId = entity.getSkuId();
                    return skuId == null ? Constants.SKU_ID_DEFAULT_VAL : skuId;
                })
                .collect(Collectors.toList());
        Map<Integer, SkuEntity> skuEntityMap = skuService.listByIds(skuIds).stream().collect(Collectors.toMap(SkuEntity::getSkuId, Function.identity()));

        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            workOrderEntity.setAppendixEntities(fileMap.get(String.valueOf(workOrderEntity.getWorkOrderId())));
            MaterialEntity materialEntity = materialMap.get(ColumnUtil.getMaterialSku(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId()));
            if (materialEntity != null) {
                workOrderEntity.setSkuId(workOrderEntity.getSkuId() == null ? Constants.SKU_ID_DEFAULT_VAL : workOrderEntity.getSkuId());
                materialEntity.setSkuEntity(skuEntityMap.get(workOrderEntity.getSkuId()));
            }
            workOrderEntity.setMaterialFields(materialEntity);
            workOrderEntity.setCraftProcedureEntities(craftProcedureMap.get(workOrderEntity.getWorkOrderNumber()));
            WorkCenterEntity workCenterEntity = workCenterMap.get(workOrderEntity.getWorkCenterId());
            if (Objects.nonNull(workCenterEntity)) {
                workOrderEntity.setWorkCenterType(workCenterEntity.getType());
                workOrderEntity.setWorkCenterRelevanceType(workCenterEntity.getRelevanceType());
                workOrderEntity.setWorkCenterCode(workCenterEntity.getCode());
            }
            // 关联资源
            getResourceVos(workOrderEntity, relevanceDeviceMap, deviceMap, relevanceTeamMap, teamMap, relevanceLineMap, lineMap);
            workOrderEntity.setProductBasicUnitInputRecords(basicUnitInputRecordMap.get(workOrderEntity.getWorkOrderNumber()));
            workOrderEntity.setPackageSchemeEntities(packageSchemeMap.get(workOrderEntity.getPackageSchemeCode()));
            workOrderEntity.setWorkOrderPlanList(workOrderPlanMap.get(workOrderEntity.getWorkOrderNumber()));
            // 批次计划总数
            List<BarCodeEntity> barCodeEntities = barCodeMap.get(workOrderEntity.getWorkOrderNumber());
            if (CollectionUtils.isNotEmpty(barCodeEntities)) {
                BigDecimal sum = barCodeEntities.stream().map(BarCodeEntity::getCount).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add);
                workOrderEntity.setRelatedBarCodePlanSum(sum.doubleValue());
            }
            WorkOrderInvestCheckResultEntity investCheckResultEntity = investCheckResultMap.get(workOrderEntity.getWorkOrderNumber());
            workOrderEntity.setInvestCheckResultDetail(Objects.isNull(investCheckResultEntity) ? null : investCheckResultEntity.getInvestCheckResultDetail());
            // 项目合同
            ReceiptProjectContractEntity projectContractEntity = receiptMap.get(workOrderEntity.getWorkOrderNumber());
            if (Objects.nonNull(projectContractEntity)) {
                workOrderEntity.setProjectDefineId(ObjectUtil.isEmpty(projectContractEntity.getProjectDefineId()) ? "" : projectContractEntity.getProjectDefineId() + "");
                workOrderEntity.setContractId(ObjectUtil.isEmpty(projectContractEntity.getContractId()) ? "" : projectContractEntity.getContractId() + "");
                workOrderEntity.setProjectDefineName(projectContractEntity.getProjectDefineName());
                workOrderEntity.setContractName(projectContractEntity.getContractName());
                workOrderEntity.setProjectNodeIds(projectContractEntity.getProjectNodeIds());
                workOrderEntity.setProjectNodeName(projectContractEntity.getProjectNodeName());
            }
            workOrderEntity.setMaintainCount(maintainRecordMap.getOrDefault(workOrderEntity.getWorkOrderNumber(), Collections.emptyList()).size());
            // 工时
            Double theoryHour = workOrderTheoryHourMap.get(workOrderEntity.getWorkOrderNumber());
            workOrderEntity.setTheoryHour(theoryHour);
            workOrderEntity.setProduceTheoryHour(NullableDouble.of(theoryHour).mul(workOrderEntity.getFinishCount()).scale(2).cal());
            workOrderEntity.setPlanTheoryHour(NullableDouble.of(theoryHour).mul(workOrderEntity.getPlanQuantity()).scale(2).cal());
            workOrderEntity.setCapacityUnitName((String) redisTemplate.opsForValue().get(RedisKeyPrefix.WORK_ORDER_CAPACITY_UNIT_NAME_ + workOrderEntity.getWorkOrderNumber()));
            // 直通率
            long maintainCount = maintainRecordMap.getOrDefault(workOrderEntity.getWorkOrderNumber(), Collections.emptyList()).stream().map(MaintainRecordEntity::getBarCode).distinct().count();
            Double finishCount = workOrderEntity.getFinishCount();
            // 直通数 =  完成数 - 有维修过的（流水码去重）
            Double directAccessQuantity = NullableDouble.of(finishCount).sub(maintainCount).scale(2).cal();
            workOrderEntity.setDirectAccessQuantity(directAccessQuantity);
            workOrderEntity.setDirectAccessRate(NullableDouble.of(directAccessQuantity).div(finishCount).scale(2).cal());
            detailVOs.add(getDetailVO(workOrderEntity));
        }
        return detailVOs;
    }

    private static void getResourceVos(WorkOrderEntity workOrderEntity, Map<Integer, List<WorkOrderDeviceRelevanceEntity>> relevanceDeviceMap, Map<Integer, String> deviceMap, Map<Integer, List<WorkOrderTeamRelevanceEntity>> relevanceTeamMap, Map<Integer, String> teamMap, Map<Integer, List<WorkOrderLineRelevanceEntity>> relevanceLineMap, Map<Integer, String> lineMap) {
        List<WorkOrderDeviceRelevanceEntity> relevanceDevices = relevanceDeviceMap.get(workOrderEntity.getWorkOrderId());
        if (CollectionUtils.isNotEmpty(relevanceDevices)) {
            workOrderEntity.setWorkOrderRelevanceVOS(relevanceDevices.stream().map(o -> WorkOrderRelevanceVO.builder()
                    .relevanceType(workOrderEntity.getWorkCenterRelevanceType())
                    .relevanceId(o.getDeviceId())
                    .relevanceName(deviceMap.get(o.getDeviceId()))
                    .build()).collect(Collectors.toList()));
            return;
        }
        List<WorkOrderTeamRelevanceEntity> relevanceTeams = relevanceTeamMap.get(workOrderEntity.getWorkOrderId());
        if (CollectionUtils.isNotEmpty(relevanceTeams)) {
            workOrderEntity.setWorkOrderRelevanceVOS(relevanceTeams.stream().map(o -> WorkOrderRelevanceVO.builder()
                    .relevanceType(workOrderEntity.getWorkCenterRelevanceType())
                    .relevanceId(o.getTeamId())
                    .relevanceName(teamMap.get(o.getTeamId()))
                    .build()).collect(Collectors.toList()));
            return;
        }
        List<WorkOrderLineRelevanceEntity> relevanceLines = relevanceLineMap.get(workOrderEntity.getWorkOrderId());
        if (CollectionUtils.isNotEmpty(relevanceLines)) {
            workOrderEntity.setWorkOrderRelevanceVOS(relevanceLines.stream().map(o -> WorkOrderRelevanceVO.builder()
                    .relevanceType(workOrderEntity.getWorkCenterRelevanceType())
                    .relevanceId(o.getLineId())
                    .relevanceName(lineMap.get(o.getLineId()))
                    .build()).collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeWorkOrderState(List<WorkOrderStateChangeDTO> list) {
        for (WorkOrderStateChangeDTO stateChangeDTO : list) {
            ReportDTO reportDTO = ReportDTO.builder()
                    .workOrder(stateChangeDTO.getWorkOrderNumber())
                    .action(getActionStateByWorkOrderState(stateChangeDTO.getState()))
                    .time(stateChangeDTO.getInvestTime())
                    .completeType(stateChangeDTO.getCompleteType())
                    .isAutoReport(stateChangeDTO.getIsAutoReport())
                    .username(stateChangeDTO.getUsername())
                    .productBasicUnitIds(stateChangeDTO.getProductBasicUnitIds())
                    .build();
            lineService.reportAction(reportDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<WorkOrderUpdateDTO> updateList) {
        List<String> updateWorkOrderNumbers = updateList.stream().map(WorkOrderUpdateDTO::getWorkOrderNumber).collect(Collectors.toList());
        List<WorkOrderEntity> list = workOrderService.lambdaQuery()
                .in(WorkOrderEntity::getWorkOrderNumber, updateWorkOrderNumbers)
                .list();
        if (list.size() != updateWorkOrderNumbers.size()) {
            throw new ResponseException(RespCodeEnum.DFS_NOT_EXIST_WORK_ORDER);
        }
        for (WorkOrderUpdateDTO updateDTO : updateList) {
            updateWorkOrder(updateDTO);
        }
    }

    private void updateWorkOrder(WorkOrderUpdateDTO updateDTO) {
        WorkOrderEntity workOrderEntity = convertToEntity(updateDTO);
        WorkOrderEntity one = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, updateDTO.getWorkOrderNumber()).one();
        // 将数据库值赋予入参，避免调用原更新接口时数据被置空
        FieldUtil.copyValueTo(one, workOrderEntity);
        // 调用原接口更新工单
        updateDTO.setUpdateDate(new Date());
        WorkOrderSmartUpdateDTO build = WorkOrderSmartUpdateDTO.builder()
                .isCalPlannedWorkHours(false)
                .isUpdateProjectContract(false)
                .isUpdateMaterialPlan(false)
                .isUpdateFile(false)
                .isUpdateProductBasicUnitRelation(CollectionUtils.isNotEmpty(updateDTO.getProductBasicUnits()))
                .isUpdateWorkOrderTeam(CollectionUtils.isNotEmpty(updateDTO.getWorkOrderTeamEntities()))
                .isUpdateRelateOrder(StringUtils.isNotBlank(updateDTO.getProductOrderNumber()) || StringUtils.isNotBlank(updateDTO.getSaleOrderNumber()))
                .isUpdateProcedureRelation(StringUtils.isNotBlank(updateDTO.getCraftCode()) && CollectionUtils.isNotEmpty(updateDTO.getCraftProcedure()))
                .isUpdateRelevanceResource(CollectionUtils.isNotEmpty(updateDTO.getRelatedResources()))
                .workOrder(workOrderEntity).username(workOrderEntity.getUpdateBy()).build();
        workOrderService.updateByWorkId(build);
    }

    /**
     * 将WorkOrderDetailVO转换为WorkOrderEntity
     *
     * @param updateDTO 工单更新VO
     * @return WorkOrderEntity
     */
    public WorkOrderEntity convertToEntity(WorkOrderUpdateDTO updateDTO) {
        // 入参字段转换
        WorkOrderEntity entity = JacksonUtil.convertObject(updateDTO, WorkOrderEntity.class);
        if (StringUtils.isNotBlank(updateDTO.getCraftCode())) {
            CraftEntity craft = craftService.getCraftByCode(entity.getCraftCode());
            if (craft == null) {
                throw ResponseException.buildByFields(RespCodeEnum.CRAFT_CODE_NOT_FOUNT, WorkOrderUpdateInsertDTO::getCraftCode);
            }
            entity.setCraftCode(updateDTO.getCraftCode());
            entity.setCraftId(craft.getCraftId());
        }
        // 关联单据相关字段
        if (StringUtils.isNotBlank(updateDTO.getProductOrderNumber())) {
            ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(entity.getProductOrderNumber()).build());
            if (productOrderEntity == null) {
                throw ResponseException.buildByFields(RespCodeEnum.PRODUCT_ORDER_NOT_FOUND, WorkOrderUpdateInsertDTO::getProductOrderNumber);
            }
            entity.setProductOrderNumber(updateDTO.getProductOrderNumber());
            entity.setProductOrderList(Collections.singletonList(productOrderEntity));
        }
        if (StringUtils.isNotBlank(updateDTO.getSaleOrderNumber())) {
            SaleOrderEntity saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(entity.getSaleOrderNumber()).build());
            if (saleOrderEntity == null) {
                throw ResponseException.buildByFields(RespCodeEnum.SALE_ORDER_NOT_FOUND, WorkOrderUpdateInsertDTO::getSaleOrderNumber);
            }
            entity.setSaleOrderNumber(updateDTO.getSaleOrderNumber());
            entity.setSaleOrderList(Collections.singletonList(saleOrderEntity));
            List<SaleOrderMaterialEntity> saleOrderMaterials = saleOrderEntity.getSaleOrderMaterials().stream().filter(o -> o.getLineNumber().equals(updateDTO.getRelatedSaleOrderMaterialLineNumber())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(saleOrderMaterials)) {
                //设置销售订单物料行id
                entity.setRelateOrderMaterialId(saleOrderMaterials.get(0).getId());
            }
        }
        // 客户相关字段
        if (updateDTO.getWorkOrderCustomerVO() != null) {
            entity.setCustomerCode(updateDTO.getWorkOrderCustomerVO().getCustomerCode());
            CustomerEntity customer = customerService.lambdaQuery().eq(CustomerEntity::getCustomerCode, updateDTO.getWorkOrderCustomerVO().getCustomerCode()).one();
            if (customer == null) {
                throw ResponseException.buildByFields(RespCodeEnum.CUSTOMER_IS_NOT_EXIST, WorkOrderUpdateInsertDTO::getCustomerCode);
            }
            entity.setCustomerName(customer.getCustomerName());
        }
        // 管理员相关字段
        if (updateDTO.getWorkOrderMagVO() != null) {
            entity.setMagName(updateDTO.getWorkOrderMagVO().getMagName());
            SysUserEntity user = userService.getUserByUsername(entity.getMagName());
            if (user == null) {
                throw ResponseException.buildByFields(RespCodeEnum.USER_NOT_EXIST, WorkOrderUpdateInsertDTO::getMagName);
            }
            entity.setMagNickname(user.getNickname());
            entity.setMagPhone(user.getMobile());
        }
        // 客户物料相关字段
        if (updateDTO.getWorkOrderCustomerMaterialVO() != null) {
            CustomerMaterialListService customerMaterialListService = SpringUtil.getBean(CustomerMaterialListService.class);
            CustomerMaterialListEntity customerMaterial = customerMaterialListService.lambdaQuery()
                    .eq(CustomerMaterialListEntity::getCustomerMaterialCode, updateDTO.getWorkOrderCustomerMaterialVO().getCustomerMaterialCode())
                    .eq(CustomerMaterialListEntity::getCustomerCode, updateDTO.getWorkOrderCustomerVO().getCustomerCode())
                    .one();
            if (customerMaterial == null) {
                throw ResponseException.buildByFields(RespCodeEnum.CUSTOMER_MATERIAL_NOT_FOUND, WorkOrderUpdateInsertDTO::getCustomerMaterialCode);
            }
            entity.setCustomerMaterialCode(updateDTO.getWorkOrderCustomerMaterialVO().getCustomerMaterialCode());
            entity.setCustomerMaterialName(customerMaterial.getCustomerMaterialName());
            entity.setCustomerSpecification(customerMaterial.getCustomerMaterialStandard());
        }
        // 投产检查结果相关字段
        if (updateDTO.getWorkOrderInvestCheckResult() != null) {
            entity.setInvestCheckResult(updateDTO.getWorkOrderInvestCheckResult().getInvestCheckResult());
        }
        // 排产相关字段
        if (updateDTO.getSchedulingVO() != null) {
            entity.setSchedulingCount(updateDTO.getSchedulingVO().getSchedulingCount());
            entity.setSchedulingSequence(updateDTO.getSchedulingVO().getSchedulingSequence());
            entity.setSchedulingState(updateDTO.getSchedulingVO().getSchedulingState());
        }
        // 工单相关工时信息
        if (updateDTO.getWorkOrderHours() != null) {
            entity.setActualWorkingHours(updateDTO.getWorkOrderHours().getActualWorkingHours());
            entity.setPlannedWorkingHours(updateDTO.getWorkOrderHours().getPlannedWorkingHours());
            entity.setEffectiveHours(updateDTO.getWorkOrderHours().getEffectiveHours());
            entity.setEffectiveWorkingHour(updateDTO.getWorkOrderHours().getEffectiveWorkingHour());
            entity.setPlanTheoryHour(updateDTO.getWorkOrderHours().getPlanTheoryHour());
            entity.setProduceTheoryHour(updateDTO.getWorkOrderHours().getProduceTheoryHour());
            entity.setTheoryHour(updateDTO.getWorkOrderHours().getTheoryHour());
        }
        // 批次相关信息
        if (updateDTO.getBatch() != null) {
            entity.setPlannedBatches(updateDTO.getBatch().getPlannedBatches());
            entity.setPlansPerBatch(updateDTO.getBatch().getPlansPerBatch());
            entity.setActualBatches(updateDTO.getBatch().getActualBatches());
        }
        // 工单扩展字段
        if (updateDTO.getWorkOrderExtendFieldVO() != null) {
            WorkOrderExtendFieldVO extendField = updateDTO.getWorkOrderExtendFieldVO();
            entity.setWorkOrderExtendFieldOne(extendField.getWorkOrderExtendFieldOne());
            entity.setWorkOrderExtendFieldTwo(extendField.getWorkOrderExtendFieldTwo());
            entity.setWorkOrderExtendFieldThree(extendField.getWorkOrderExtendFieldThree());
            entity.setWorkOrderExtendFieldFour(extendField.getWorkOrderExtendFieldFour());
            entity.setWorkOrderExtendFieldFive(extendField.getWorkOrderExtendFieldFive());
            entity.setWorkOrderExtendFieldSix(extendField.getWorkOrderExtendFieldSix());
            entity.setWorkOrderExtendFieldSeven(extendField.getWorkOrderExtendFieldSeven());
            entity.setWorkOrderExtendFieldEight(extendField.getWorkOrderExtendFieldEight());
            entity.setWorkOrderExtendFieldNine(extendField.getWorkOrderExtendFieldNine());
            entity.setWorkOrderExtendFieldTen(extendField.getWorkOrderExtendFieldTen());
            entity.setWorkOrderMaterialExtendFieldOne(extendField.getWorkOrderMaterialExtendFieldOne());
            entity.setWorkOrderMaterialExtendFieldTwo(extendField.getWorkOrderMaterialExtendFieldTwo());
            entity.setWorkOrderMaterialExtendFieldThree(extendField.getWorkOrderMaterialExtendFieldThree());
            entity.setWorkOrderMaterialExtendFieldFour(extendField.getWorkOrderMaterialExtendFieldFour());
            entity.setWorkOrderMaterialExtendFieldFive(extendField.getWorkOrderMaterialExtendFieldFive());
            entity.setWorkOrderMaterialExtendFieldSix(extendField.getWorkOrderMaterialExtendFieldSix());
            entity.setWorkOrderMaterialExtendFieldSeven(extendField.getWorkOrderMaterialExtendFieldSeven());
            entity.setWorkOrderMaterialExtendFieldEight(extendField.getWorkOrderMaterialExtendFieldEight());
            entity.setWorkOrderMaterialExtendFieldNine(extendField.getWorkOrderMaterialExtendFieldNine());
            entity.setWorkOrderMaterialExtendFieldTen(extendField.getWorkOrderMaterialExtendFieldTen());
        }
        // 工作中心相关
        if (updateDTO.getWorkCenter() != null) {
            WorkCenterEntity center = workCenterService.getCenterByCode(entity.getWorkCenterCode());
            if (center == null) {
                throw ResponseException.buildByFields(RespCodeEnum.WORK_CENTER_IS_NOT_FOUND, WorkOrderUpdateInsertDTO::getWorkCenterCode);
            }
            entity.setWorkCenterId(center.getId());
            entity.setWorkCenterName(center.getName());
            entity.setWorkCenterType(center.getType());
        }
        // 仓库相关
        if (updateDTO.getWarehouse() != null) {
            entity.setCurrentInventory(updateDTO.getWarehouse().getCurrentInventory());
            entity.setPickingQuantity(updateDTO.getWarehouse().getPickingQuantity());
            entity.setInventoryQuantity(updateDTO.getWarehouse().getInventoryQuantity());
        }
        // 项目合同相关
        if (updateDTO.getProjectContractInfo() != null) {
            entity.setProjectContract(updateDTO.getProjectContractInfo().getProjectContract());
        }
        // 审批相关
        if (updateDTO.getOrderApprovalVO() != null) {
            entity.setApprover(updateDTO.getOrderApprovalVO().getApprover());
            entity.setActualApprover(updateDTO.getOrderApprovalVO().getActualApprover());
            entity.setApprovalStatus(updateDTO.getOrderApprovalVO().getApprovalStatus());
            entity.setApprovalSuggestion(updateDTO.getOrderApprovalVO().getApprovalSuggestion());
            entity.setApprovalTime(updateDTO.getOrderApprovalVO().getApprovalTime());
        }
        // 生产基本单元
        if (CollectionUtils.isNotEmpty(updateDTO.getProductBasicUnits())) {
            List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = updateDTO.getProductBasicUnits()
                    .stream().map(o -> WorkOrderBasicUnitRelationEntity.builder()
                            .workCenterId(o.getWorkCenterId())
                            .productionBasicUnitId(o.getProductionBasicUnitId())
                            .build())
                    .collect(Collectors.toList());
            entity.setProductBasicUnits(basicUnitRelationEntities);
        }
        // 工单班组成员
        if (CollectionUtils.isNotEmpty(updateDTO.getWorkOrderTeamEntities())) {
            List<WorkOrderTeamEntity> workOrderTeamEntities = updateDTO.getWorkOrderTeamEntities().stream().map(o -> WorkOrderTeamEntity.builder()
                            .workOrderNumber(updateDTO.getWorkOrderNumber())
                            .teamRole(o.getTeamRoleId())
                            .memberName(o.getMemberUserName())
                            .build())
                    .collect(Collectors.toList());
            entity.setWorkOrderTeamEntities(workOrderTeamEntities);
        }
        //设置工艺工序
        List<WorkOrderCraftProcedureRelationUpdateDTO> craftProcedureEntityDTO = updateDTO.getCraftProcedure();
        if (StringUtils.isNotBlank(updateDTO.getCraftCode()) && CollectionUtils.isNotEmpty(craftProcedureEntityDTO)) {
            CraftEntity craft = craftService.getCraftByCode(updateDTO.getCraftCode());
            if (craft == null) {
                throw ResponseException.buildByFields(RespCodeEnum.CRAFT_CODE_NOT_FOUNT, WorkOrderUpdateInsertDTO::getCraftProcedureEntities);
            }
            List<CraftProcedureEntity> craftProcedureEntities = new ArrayList<>();
            for (WorkOrderCraftProcedureRelationUpdateDTO procedureEntityDTO : craftProcedureEntityDTO) {
                ProcedureEntity procedure = procedureService.getProcedureByCode(procedureEntityDTO.getProcedureCode());
                if (procedure == null) {
                    throw ResponseException.buildByFields(RespCodeEnum.PROCEDURE_CODE_NOT_FOUNT, WorkOrderUpdateInsertDTO::getCraftProcedureEntities);
                }
                CraftProcedureService craftProcedureService = SpringUtil.getBean(CraftProcedureService.class);
                CraftProcedureEntity craftProcedure = craftProcedureService.lambdaQuery().
                        eq(CraftProcedureEntity::getCraftId, craft.getCraftId())
                        .eq(CraftProcedureEntity::getProcedureId, procedure.getProcedureId())
                        .eq(CraftProcedureEntity::getProcedureName, procedureEntityDTO.getProcedureName()).one();
                if (craftProcedure == null) {
                    throw ResponseException.buildByFields(RespCodeEnum.CRAFT_PROCEDURE_CODE_NOT_FOUNT, WorkOrderUpdateInsertDTO::getCraftProcedureEntities);
                }
                craftProcedureEntities.add(craftProcedure);
            }
            entity.setCraftProcedureEntities(craftProcedureEntities);
        }
        // 关联资源
        if (CollectionUtils.isNotEmpty(updateDTO.getRelatedResources())) {
            String relevanceType = updateDTO.getRelatedResources().get(0).getRelevanceType();
            List<Integer> relevanceIds = updateDTO.getRelatedResources().stream().map(WorkOrderRelevanceUpdateDTO::getRelevanceId).collect(Collectors.toList());
            if (WorkCenterTypeEnum.LINE.getCode().equals(relevanceType)) {
                entity.setRelevanceLineIds(relevanceIds);
            } else if (WorkCenterTypeEnum.TEAM.getCode().equals(relevanceType)) {
                entity.setRelevanceTeamIds(relevanceIds);
            } else {
                entity.setRelevanceDeviceIds(relevanceIds);
            }
        }
        return entity;
    }

    private Integer getActionStateByWorkOrderState(Integer workOrderState) {
        Integer actionState = null;
        if (workOrderState.equals(WorkOrderStateEnum.INVESTMENT.getCode())) {
            actionState = Constant.START;
        }
        if (workOrderState.equals(WorkOrderStateEnum.FINISHED.getCode())) {
            actionState = Constant.END;
        }
        if (workOrderState.equals(WorkOrderStateEnum.HANG_UP.getCode())) {
            actionState = Constant.PAUSE;
        }
        return actionState;
    }
}
