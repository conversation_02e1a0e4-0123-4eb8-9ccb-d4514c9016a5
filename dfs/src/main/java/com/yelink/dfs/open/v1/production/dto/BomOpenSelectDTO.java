package com.yelink.dfs.open.v1.production.dto;

import com.yelink.dfscommon.dto.CommSelectPageDTO;
import com.yelink.dfscommon.dto.MaterialEntityOpenSelectDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuSelectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/8/4 19:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class BomOpenSelectDTO extends CommSelectPageDTO {
    /**
     * 状态
     */
    @ApiModelProperty("状态(1-创建 2-生效 3-停用 4-废弃)")
    private String state;

    /**
     * 生产状态
     */
    @ApiModelProperty("生产状态(testProduction-试产,massProduction-量产)")
    private String productionState;
    /**
     * 物料code
     */
    @ApiModelProperty("物料code")
    private String code;

    /**
     *审批人
     */
    @ApiModelProperty("审批人")
    private String approver;
    /**
     * BOM编号
     */
    @ApiModelProperty("BOM编号")
    private String bomNum;
    /**
     * BOM名称
     */
    @ApiModelProperty("BOM名称")
    private String bomName;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    private String version;

    /**
     * 精准查询
     */
    @ApiModelProperty("版本(精准查询)")
    private String fullVersion;

    /**
     * 精确查询的物料编码
     */
    @ApiModelProperty("精确查询的物料编码")
    private String fullMaterialCode;

    /**
     * bomId
     */
    @ApiModelProperty("bomId")
    private List<Integer> bomIds;

    /**
     * bom物料行id
     */
    @ApiModelProperty("bom物料行id")
    private List<Integer> bomRawMaterialIds;

    /**
     * skuId
     */
    @ApiModelProperty("skuId")
    private Integer skuId;

    /**
     * bomCodes
     */
    @ApiModelProperty("BOM编号集合")
    private List<String> bomCodes;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    private MaterialEntityOpenSelectDTO materialFields;

    /**
     * 物料特征参数
     */
    @ApiModelProperty("物料特征参数")
    private List<MaterialSkuSelectDTO> materialSkus;
}
