package com.yelink.dfs.service.rule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.constant.rule.RulePrefixDTO;
import com.yelink.dfs.constant.rule.RulePrefixEnum;
import com.yelink.dfs.entity.order.RuleSeqEntity;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfs.open.v2.rule.dto.BatchGenerateRuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.BatchRuleSeqDTO;
import com.yelink.dfscommon.dto.dfs.GenerateCodeDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.pojo.ResponseData;

import java.util.List;
import java.util.Map;

/**
 * 编码生成规则
 *
 * <AUTHOR> @Date 2021-09-24
 */
public interface NumberRuleService extends IService<NumberRulesConfigEntity> {
    /**
     * 查询类型
     *
     * @return
     */
    List<String> getType();

    /**
     * 分页查询规则配置
     *
     * @param current
     * @param size
     * @return
     */
    Page<NumberRulesConfigEntity> getAll(Integer current, Integer size, String ruleType);

    /**
     * 根据编码规则实例生成编号
     *
     * @param dto id --编码规则id
     *            <p>
     *            materialCode --物料编码
     *            <p>
     *            relatedMap --归一方式查询的关联单据对象
     *            key为关联单号类型
     *            value为关联单号（可以是生产工单号、销售订单号、制造单元类型编号、制造单元编号。。。多种单号）
     *            <p>
     *            numberMap --前端传递的编码（可以是生产工单号、销售订单号、制造单元类型编号、制造单元编号。。。多种单号），用于组成编码信息
     *            key为编码规则类型
     *            value为编码号
     *            numberMap 相关枚举 {@link RulePrefixEnum}
     *            relatedMap 相关枚举{@link AutoIncrementConfigureTypeEnum }
     * @return
     */
    NumberCodeDTO getSeqById(RuleSeqDTO dto);

    /**
     * 按类型查询编码规则编码规则
     *
     * @param typeCode
     * @return
     */
    List<NumberRulesConfigEntity> getByTypeCode(String typeCode);

    /**
     * 根据编码规则类型查询所有对应的组成信息
     * 不传，则传所有的组成信息
     *
     * @param ruleType 编码规则类型
     */
    List<RulePrefixDTO> getRulesEnums(String ruleType);

    /**
     * 查询编码规则生成详情
     *
     * @param id
     * @return
     */
    NumberRulesConfigEntity get(Integer id);

    /**
     * 批次生成生成编码
     *
     * @param ruleDetailDTOs :配置集合
     * @return
     */
    NumberCodeDTO generateRules(Integer numberRulesId, List<RuleDetailDTO> ruleDetailDTOs,
                                Map<String, String> relatedMap);
    /**
     * 批次生成生成编码
     */
    NumberCodeDTO generateRules(NumberRulesConfigEntity numberRulesConfigEntity, List<RuleDetailDTO> ruleDetailDTOs, Map<String, String> relatedMap);

    /**
     * 根据规则类型获取
     *
     * @param
     * @return
     */
    String getNumberByRuleType(Integer ruleType);

    /**
     * 返回8位数不良品条码编号
     *
     * @return
     */
    ResponseData getBarCodeLoke(QueryWrapper<RuleSeqEntity> queryWrapper, Integer num, List<String> numList);

    /**
     * 批量新增该编码规则类型下的规则信息
     * （按xx归一：组成信息中有此字段才可按该字段归一，例如按供应商归一，需要有供应商的组成信息，否则会编号重复）
     *
     * @param numberRuleList 编码规则实例
     * @param username       用户名
     * @return
     */
    void batchAddOrUpdate(List<NumberRulesConfigEntity> numberRuleList, String username);

    /**
     * 根据编码规则实例批量生成 编号的组成信息集合
     *
     * @param
     * @return 批量数据
     */
    List<NumberCodeDTO> getBatchSeqById(BatchRuleSeqDTO dto);

    /**
     * 生成对应类型的编码, 取默认的(自增)
     *
     * @return 出参
     */
    String generateCodeByType(GenerateCodeDTO dto);
    String generateCodeByTypeAndAdd(GenerateCodeDTO dto);
    String generateCodeByTypeWithNewTra(GenerateCodeDTO dto);

    /**
     * 批量生成编号 (支持人工输入)
     *
     * @param
     * @return
     */
    List<NumberCodeDTO> batchGenerateRules(BatchGenerateRuleDetailDTO dto);
}
