package com.yelink.dfs.service.impl.order;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.mapper.order.OrderWorkOrderMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.product.CraftProcedureMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2021/4/6 22:26
 */
@Service
public class OrderWorkOrderServiceImpl extends ServiceImpl<OrderWorkOrderMapper, OrderWorkOrderEntity> implements OrderWorkOrderService {

    @Resource
    private WorkOrderMapper workOrderMapper;
    @Resource
    @Lazy
    private ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    @Lazy
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    @Lazy
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    @Lazy
    private FacilitiesService facilitiesService;
    @Resource
    @Lazy
    private ModelService modelService;
    @Resource
    @Lazy
    private CraftProcedureMapper craftProcedureMapper;
    @Resource
    @Lazy
    private RedisTemplate redisTemplate;
    @Resource
    @Lazy
    private WorkPropertise workPropertise;
    /**
     * 循环批量插入工单订单关联信息
     *
     * @param workOrderIds
     * @param orderId
     */
    @Override
    public void add(Integer orderId, String[] workOrderIds, String type) {
        List<OrderWorkOrderEntity> list = Arrays.stream(workOrderIds)
                .filter(workOrder -> !StringUtils.isBlank(workOrder))
                .map(workOrder -> OrderWorkOrderEntity.builder()
                        .orderId(orderId)
                        .workOrderId(Integer.parseInt(workOrder))
                        .build()).collect(Collectors.toList());
        saveBatch(list);
    }

    @Override
    public List<OrderWorkOrderEntity> listOrderWorkOrderRelation(Integer orderId, String orderType) {
        return this.lambdaQuery()
                .eq(OrderWorkOrderEntity::getOrderId, orderId)
                .eq(OrderWorkOrderEntity::getOrderType, orderType)
                .list();
    }

    @Override
    public List<OrderWorkOrderEntity> listOrderWorkOrderRelations(List<Integer> orderIds, String orderType) {
        return this.lambdaQuery()
                .in(OrderWorkOrderEntity::getOrderId, orderIds)
                .eq(OrderWorkOrderEntity::getOrderType, orderType)
                .list();
    }

    @Override
    public List<OrderWorkOrderEntity> getByWorkOrderIdAnOrderType(Integer workOrderId, String orderType) {
        LambdaQueryWrapper<OrderWorkOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                .eq(OrderWorkOrderEntity::getOrderType, orderType);
        return list(qw);
    }

    @Override
    public List<OrderWorkOrderEntity> listByOrderId(Integer orderId) {
        QueryWrapper<OrderWorkOrderEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(OrderWorkOrderEntity::getOrderId, orderId);
        return list(qw);
    }

    @Override
    public List<OrderWorkOrderEntity> getByWorkOrderId(Integer workOrderId, String orderType) {
        QueryWrapper<OrderWorkOrderEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                .eq(OrderWorkOrderEntity::getOrderType, orderType);
        return list(qw);
    }

    @Override
    public List<ProductOrderEntity> productOrderListByWorkId(WorkOrderEntity workOrderEntity) {
        List<OrderWorkOrderEntity> list = this.lambdaQuery().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode()).list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 查询关联的生产订单物料行号
        workOrderEntity.setRelatedProductOrderMaterialLineNumber(list.get(0).getRelatedMaterialLineNumber());
        List<Integer> productOrderIds = list.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productOrderIds)) {
            return new ArrayList<>();
        }
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().productOrderIds(productOrderIds).build());

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new ArrayList<>();
        }
        return pageResult.getRecords();
    }

    @Override
    public List<SaleOrderEntity> saleOrderListByWorkId(WorkOrderEntity workOrderEntity) {
        List<OrderWorkOrderEntity> list = this.lambdaQuery().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode()).list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        // 查询关联的销售订单物料行号
        workOrderEntity.setRelatedSaleOrderMaterialLineNumber(list.get(0).getRelatedMaterialLineNumber());
        List<Integer> saleOrderIds = list.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleOrderIds)) {
            return new ArrayList<>();
        }
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().saleOrderIds(saleOrderIds).build());
        return JacksonUtil.convertArray(pageResult.getRecords(), SaleOrderEntity.class);
    }

    @Override
    public List<WorkOrderEntity> listWorkOrderByOrderId(Integer orderId, String orderType) {
        List<OrderWorkOrderEntity> orderWorkOrderEntities = this.lambdaQuery()
                .eq(OrderWorkOrderEntity::getOrderId, orderId).eq(OrderWorkOrderEntity::getOrderType, orderType).list();
        if (CollectionUtils.isEmpty(orderWorkOrderEntities)) {
            return new ArrayList<>();
        }
        List<Integer> workOrderIds = orderWorkOrderEntities.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
        return workOrderMapper.selectList(workOrderWrapper);
    }

    /**
     * 订单流水码产线挑选工单
     * @param orderNumber
     * @param lineId
     * @param orderType OrderNumTypeEnum
     * @param state
     * @param workOrderNumber
     * @param fid
     * @return
     */
    @Override
    public WorkOrderEntity getWorkOrderByOrderIdAndLineId(String orderNumber, Integer lineId, String orderType, Integer state, String workOrderNumber,Integer fid) {
        //有缓存拿缓存信息
        String workOrderEntityKey = RedisKeyPrefix.SCANNER_WORK_ORDER + orderNumber + Constant.CROSSBAR + lineId + Constant.CROSSBAR + orderType + Constant.CROSSBAR + state + Constant.CROSSBAR + workOrderNumber + Constant.CROSSBAR + fid;
        Object workOrderEntityObject = redisTemplate.opsForValue().get(workOrderEntityKey);
        WorkOrderEntity workOrderEntity;
        if (workOrderEntityObject != null) {
            workOrderEntity = JSONObject.parseObject((String) workOrderEntityObject, WorkOrderEntity.class);
        }else {
            workOrderEntity = getWorkOrderInLine(orderNumber, lineId, orderType, state, workOrderNumber, fid);
        }
        if (workOrderEntity != null) {
            redisTemplate.opsForValue().set(workOrderEntityKey, JSONObject.toJSONString(workOrderEntity), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
        }
        return workOrderEntity;
    }

    /**
     * 订单流水码产线挑选工单
     * @param orderNumber
     * @param lineId
     * @param orderType OrderNumTypeEnum
     * @param state
     * @param workOrderNumber
     * @param fid
     * @return
     */
    private WorkOrderEntity getWorkOrderInLine(String orderNumber, Integer lineId, String orderType, Integer state, String workOrderNumber, Integer fid) {
        ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(orderNumber).build());
        if (orderEntity == null) {
            throw new ResponseException("条码对应生产订单不存在");
        }
        Integer orderId=orderEntity.getProductOrderId();
        List<OrderWorkOrderEntity> orderWorkOrderEntities = this.lambdaQuery()
                .eq(OrderWorkOrderEntity::getOrderId, orderId).eq(OrderWorkOrderEntity::getOrderType, orderType).list();
        if (CollectionUtils.isEmpty(orderWorkOrderEntities)) {
            return null;
        }
        List<Integer> workOrderIds = orderWorkOrderEntities.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds)
                .eq(lineId != null, WorkOrderEntity::getLineId, lineId)
                .eq(StringUtils.isNotBlank(workOrderNumber), WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                .in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode());
        WrapperUtil.eq(workOrderWrapper, WorkOrderEntity::getState, state);
        List<WorkOrderEntity> workOrderEntityList = workOrderMapper.selectList(workOrderWrapper);
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            return null;
        }
        if (workOrderEntityList.size() == 1) {
            return workOrderEntityList.get(0);
        }
        // 工单关联的工序
        List<WorkOrderProcedureRelationEntity> procedureRelationEntities = workOrderProcedureRelationService.lambdaQuery()
                .in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderEntityList.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList()))
                .list();
        // 工位类型名称
        if (fid != null) {
            FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
            String facType = modelService.getModelNameById(facilitiesEntity.getModelId());
            Map<Integer, WorkOrderEntity> workOrderEntityMap = workOrderEntityList.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderId, workOrderEntity -> workOrderEntity));
            List<WorkOrderEntity> procedureWorkOrderList = new ArrayList<>();
            for (WorkOrderProcedureRelationEntity workOrderProcedureRelationEntity : procedureRelationEntities) {
                CraftProcedureEntity craftProcedureEntity = craftProcedureMapper.selectById(workOrderProcedureRelationEntity.getCraftProcedureId());
                if (craftProcedureEntity != null && facType.equals(craftProcedureEntity.getFacType())) {
                    procedureWorkOrderList.add(workOrderEntityMap.get(workOrderProcedureRelationEntity.getWorkOrderId()));
                }
            }
            procedureWorkOrderList = procedureWorkOrderList.stream().distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(procedureWorkOrderList)) {
                if (procedureWorkOrderList.size() == 1) {
                    return procedureWorkOrderList.get(0);
                }
                workOrderEntityList = procedureWorkOrderList;
            }
        }
        //优先取投产工单
        List<WorkOrderEntity> investmentList = workOrderEntityList.stream().filter(o -> o.getState().equals(WorkOrderStateEnum.INVESTMENT.getCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(investmentList) && investmentList.size() == 1) {
            return investmentList.get(0);
        }
        //筛选不到工单则报错
        throw new ResponseException("存在多个符合条件工单");
    }

    @Override
    public List<WorkOrderEntity> listWorkOrdersByIdAndType(Integer orderId, String orderType) {
        List<OrderWorkOrderEntity> list = this.lambdaQuery()
                .select(OrderWorkOrderEntity::getWorkOrderId)
                .eq(OrderWorkOrderEntity::getOrderId, orderId)
                .eq(OrderWorkOrderEntity::getOrderType, orderType).list();
        List<Integer> workOrderIdList = list.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIdList);
        return workOrderMapper.selectList(wrapper);
    }

    @Override
    public List<WorkOrderEntity> listWorkOrderByOrderNumber(String orderNumber) {
        ProductOrderSelectOpenDTO build = ProductOrderSelectOpenDTO.builder().productOrderNumbers(Stream.of(orderNumber).collect(Collectors.toList())).isShowSimpleInfo(true).build();
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(build);

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new ArrayList<>();
        }
        List<Integer> productOrderIds = pageResult.getRecords().stream().map(ProductOrderEntity::getProductOrderId).collect(Collectors.toList());
        return listWorkOrderByOrderIds(productOrderIds, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
    }

    /**
     * 通过工单id列表获取所有关联订单
     *
     * @param workOrderIdList
     * @return
     */
    @Override
    public Map<Integer, SaleOrderVO> listOrderByWorkIdList(List<Integer> workOrderIdList) {
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return new HashMap<>(8);
        }
        List<OrderWorkOrderEntity> list = this.lambdaQuery()
                .in(OrderWorkOrderEntity::getWorkOrderId, workOrderIdList)
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                .list();
        List<Integer> saleOrderIds = list.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleOrderIds)) {
            return new HashMap<>(8);
        }
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().saleOrderIds(saleOrderIds).build());
        Map<Integer, SaleOrderVO> saleOrderMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            saleOrderMap = pageResult.getRecords().stream().collect(Collectors.toMap(SaleOrderVO::getSaleOrderId, o -> o));
        }
        HashMap<Integer, SaleOrderVO> result = new HashMap<>(16);
        for (OrderWorkOrderEntity entity : list) {
            SaleOrderVO saleOrderVO = saleOrderMap.get(entity.getOrderId());
            if (saleOrderVO != null) {
                result.put(entity.getWorkOrderId(), saleOrderVO);
            }
        }

        return result;
    }

    @Override
    public Map<Integer, SaleOrderVO> orderMaterialListByWorkOrders(List<WorkOrderEntity> workOrderEntities) {
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return new HashMap<>();
        }
        List<Integer> workOrderIdList = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        List<OrderWorkOrderEntity> list = this.lambdaQuery()
                .in(OrderWorkOrderEntity::getWorkOrderId, workOrderIdList)
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                .list();
        List<Integer> saleOrderIds = list.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleOrderIds)) {
            return new HashMap<>();
        }
        // 关联销售订单物料行id
        List<Integer> relateOrderMaterialIds = workOrderEntities.stream().map(WorkOrderEntity::getRelateOrderMaterialId).collect(Collectors.toList());
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder()
                .saleOrderIds(saleOrderIds).saleOrderMaterialIds(relateOrderMaterialIds).showType(ShowTypeEnum.MATERIAL.getType()).build());

        Map<Integer, SaleOrderVO> saleOrderMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            saleOrderMap = pageResult.getRecords().stream()
                    .filter(o -> Objects.nonNull(o.getSaleOrderMaterial()))
                    .peek(o -> o.setSaleOrderMaterialId(o.getSaleOrderMaterial().getId()))
                    .collect(Collectors.toMap(SaleOrderVO::getSaleOrderMaterialId, o -> o));
        }
        HashMap<Integer, SaleOrderVO> result = new HashMap<>();
        for (WorkOrderEntity entity : workOrderEntities) {
            SaleOrderVO saleOrderVO = saleOrderMap.get(entity.getRelateOrderMaterialId());
            if (saleOrderVO != null) {
                result.put(entity.getWorkOrderId(), saleOrderVO);
            }
        }
        return result;
    }


    @Override
    public Map<Integer, ProductOrderEntity> listProductOrderByWorkIdList(List<Integer> workOrderIdList) {
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return new HashMap<>(8);
        }
        List<OrderWorkOrderEntity> list = this.lambdaQuery()
                .in(OrderWorkOrderEntity::getWorkOrderId, workOrderIdList)
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                .list();

        List<Integer> productOrderIds = list.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productOrderIds)) {
            return new HashMap<>(8);
        }
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().productOrderIds(productOrderIds).build());
        Map<Integer, ProductOrderEntity> productOrderMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            productOrderMap = pageResult.getRecords().stream().collect(Collectors.toMap(ProductOrderEntity::getProductOrderId, o -> o));
        }
        HashMap<Integer, ProductOrderEntity> result = new HashMap<>(16);
        for (OrderWorkOrderEntity entity : list) {
            ProductOrderEntity productOrderEntity = productOrderMap.get(entity.getOrderId());
            if (productOrderEntity != null) {
                result.put(entity.getWorkOrderId(), productOrderEntity);
            }
        }

        return result;
    }


    @Override
    public List<OrderWorkOrderEntity> getByWorkOrderIdsAndOrderType(List<Integer> workOrderIds, String orderType) {
        QueryWrapper<OrderWorkOrderEntity> qw = new QueryWrapper<>();
        qw.lambda().in(OrderWorkOrderEntity::getWorkOrderId, workOrderIds)
                .eq(StringUtils.isNotEmpty(orderType), OrderWorkOrderEntity::getOrderType, orderType);
        if (CollectionUtils.isEmpty(list(qw))) {
            return new ArrayList<>();
        }
        return list(qw);
    }

    /**
     * 通过订单号和类型获取工单
     *
     * @param orderNumber
     * @param orderType
     * @return
     */
    @Override
    public List<WorkOrderEntity> listWorkOrderByOrderAndType(String orderNumber, String orderType) {
        if (StringUtils.isAnyBlank(orderNumber, orderType)) {
            return new ArrayList<>();
        }
        Integer orderId = null;
        if (OrderNumTypeEnum.SALE_ORDER.getTypeCode().equals(orderType)) {
            // 销售订单
            SaleOrderEntity saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(orderNumber).build());
            orderId = saleOrderEntity == null ? null : saleOrderEntity.getSaleOrderId();
        } else if (OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode().equals(orderType)) {
            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(orderNumber).build());
            orderId = orderEntity != null ? orderEntity.getProductOrderId() : null;
        }
        if (orderId == null) {
            return new ArrayList<>();
        }
        List<OrderWorkOrderEntity> orderWorkOrderEntities = this.listOrderWorkOrderRelation(orderId, orderType);
        if (CollectionUtils.isEmpty(orderWorkOrderEntities)) {
            return new ArrayList<>();
        }
        List<Integer> workOrderIds = orderWorkOrderEntities.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
        return workOrderMapper.selectList(workOrderWrapper);
    }


    @Override
    public List<OrderWorkOrderEntity> getByOrderIdsAndOrderType(Integer orderId, String orderType) {
        QueryWrapper<OrderWorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(OrderWorkOrderEntity::getOrderId, orderId)
                .eq(OrderWorkOrderEntity::getOrderType, orderType);
        return this.list(wrapper);
    }

    @Override
    public List<WorkOrderEntity> listWorkOrderOfProductOrderBySaleOrder(String saleOrderNumber) {
        if (StringUtils.isBlank(saleOrderNumber)) {
            return new ArrayList<>();
        }
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().saleOrderNumber(saleOrderNumber).build());

        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new ArrayList<>();
        }
        List<Integer> orderIds = pageResult.getRecords().stream().map(ProductOrderEntity::getProductOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<OrderWorkOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                .in(OrderWorkOrderEntity::getOrderId, orderIds);
        List<OrderWorkOrderEntity> orderWorkOrderEntities = this.list(qw);
        if (CollectionUtils.isEmpty(orderWorkOrderEntities)) {
            return new ArrayList<>();
        }
        List<Integer> workOrderIds = orderWorkOrderEntities.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
        return workOrderMapper.selectList(workOrderWrapper);


    }

    @Override
    public List<SaleOrderEntity> listSaleOrderByWorkIdOfProductOrder(Integer workOrderId) {
        QueryWrapper<OrderWorkOrderEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        List<OrderWorkOrderEntity> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Integer> productOrderIds = list.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productOrderIds)) {
            return new ArrayList<>();
        }
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().productOrderIds(productOrderIds).build());
        return JacksonUtil.convertArray(pageResult.getRecords(), SaleOrderEntity.class);
    }

    @Override
    public List<WorkOrderEntity> listWorkOrderByOrderIds(List<Integer> orderIds, String typeCode) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        List<OrderWorkOrderEntity> orderWorkOrderEntities = this.lambdaQuery()
                .in(OrderWorkOrderEntity::getOrderId, orderIds)
                .eq(OrderWorkOrderEntity::getOrderType, typeCode).list();
        if (CollectionUtils.isEmpty(orderWorkOrderEntities)) {
            return new ArrayList<>();
        }
        List<Integer> workOrderIds = orderWorkOrderEntities.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        workOrderWrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
        return workOrderMapper.selectList(workOrderWrapper);

    }

}
