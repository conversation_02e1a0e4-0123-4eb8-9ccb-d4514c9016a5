package com.yelink.dfs.service.product;

import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.dto.CraftBindMaterialDTO;
import com.yelink.dfs.entity.product.dto.CraftCopyDTO;
import com.yelink.dfs.entity.product.dto.CraftProcedureByWorkCenterDTO;
import com.yelink.dfs.entity.product.dto.CraftSelectDTO;
import com.yelink.dfs.entity.product.dto.ImpactAnalysisDTO;
import com.yelink.dfs.entity.product.dto.ImpactAnalysisSelectDTO;
import com.yelink.dfs.entity.product.dto.ProcedureOneKeyDefaultDTO;
import com.yelink.dfs.entity.product.dto.SimpleCraftExportDTO;
import com.yelink.dfs.entity.product.vo.ProcedureCapacityLabelVO;
import com.yelink.dfs.open.v1.craft.dto.CraftCopyOpenDTO;
import com.yelink.dfs.open.v1.craft.dto.DefaultCraftMaterialDTO;
import com.yelink.dfs.open.v1.craft.vo.DefaultCraftVO;
import com.yelink.dfs.open.v2.craft.dto.CraftDetailQueryDTO;
import com.yelink.dfs.open.v2.craft.dto.CraftQueryDTO;
import com.yelink.dfs.open.v2.craft.vo.CraftVO;
import com.yelink.dfs.open.v2.material.vo.MaterialFieldVO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureExportDTO;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.DataDelSchema;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-08 09:30
 */
public interface CraftService extends IService<CraftEntity> {

    /**
     * 按条件查询工艺
     *
     * @param selectDTO
     * @return
     */
    Page<CraftEntity> getList(CraftSelectDTO selectDTO);

    /**
     * 通过工艺编号获取工艺
     *
     * @param craftCode
     */
    CraftEntity getCraftByCode(String craftCode);

    /**
     * 工艺模板导出(1.6版本没使用到)
     *
     * @param response
     * @throws IOException
     */
    void export(HttpServletResponse response) throws IOException;

    /**
     * 工艺批量导入
     *
     * @param file
     * @param username
     */
    void batchImport(MultipartFile file, String username) throws IOException;

    /**
     * 删除工艺，同时删除关联的工序
     *
     * @param id
     * @return
     */
    CraftEntity removeEntityById(Integer id);

    /**
     * 获取通过物料编号获取最新工艺
     *
     * @param MaterialCode
     */
    CraftEntity getCraftByMaterialCode(String MaterialCode);

    /**
     * 获取默认工艺
     *
     * @param materialCode
     * @return
     */
    List<CraftEntity> getDefaultCraftListByMaterialCode(String materialCode);

    List<DefaultCraftVO> getDefaultCraftList(DefaultCraftMaterialDTO materialDTO);

    /**
     * 新增工艺
     *
     * @param
     * @return
     */
    CraftEntity saveEntity(CraftEntity entity);

    /**
     * 创建工艺时, 初始化原始信息
     * @param entity 工艺实例
     */
    void initOriginInfo(CraftEntity entity);
    /**
     * 新增生效工艺
     *
     * @param entity
     */
    void saveReleasedEntity(CraftEntity entity);

    /**
     * 获取工艺详情
     *
     * @return
     */
    CraftEntity getEntityById(Integer id);

    /**
     * 根据物料编码获取工艺详情
     *
     * @return
     */
    List<CraftEntity> getEntitiesByCraftId(Integer craftId);

    /**
     * 修改工艺
     *
     * @param entity
     * @return
     */
    boolean updateEntityById(CraftEntity entity);

    /**
     * 工艺路线里绑定的工序需要绑定工序组
     *
     * @param craftId 工艺ID
     * @return
     */
    void bindCraftProcedureGroup(Integer craftId);

    /**
     * 审批
     */
    void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username);

    /**
     * 批量审批
     *
     * @param dto
     */
    void approveBatch(ApproveBatchDTO dto);

    /**
     * 通过工艺编号查询工艺信息
     *
     * @param craftCode
     * @return
     */
    CraftEntity getCraftEntityByCraftCode(String craftCode);

    /**
     * 根据物料查询工艺列表
     *
     * @param materialCode 物料编码
     * @param businessType 工单的业务类型
     * @return
     */
    List<CraftEntity> getListByMaterialCode(String materialCode, String businessType);


    List<CraftEntity> listByCode(String materialCode, String businessType);

    List<CraftEntity> listByCode2(String materialCode, Boolean isTemplate);

    /**
     * 复制工艺配置
     *
     * @param craftCopyDTO
     * @param name
     * @return
     */
    Integer copyCraft(CraftCopyDTO craftCopyDTO, String name);


    /**
     * 获取导出实体类
     * 按条件导出, craftIds 与 selectDTO 只满足一个
     *
     * @param craftIds
     * @param selectDTO
     * @return
     */
    List<CraftProcedureExportDTO> listExportDTO(String craftIds, CraftSelectDTO selectDTO);

    /**
     * 影响分析 -- 获取工艺关联的
     * 生产订单(生效)、
     * 生产工单(生效、投产、挂起、完成)、
     * 作业工单（生效、投产、挂起、完成）
     *
     * @param selectDTO 查询条件
     * @return
     */
    Page<ImpactAnalysisDTO> impactAnalysis(ImpactAnalysisSelectDTO selectDTO);

    void impactAnalysisExport(ImpactAnalysisSelectDTO selectDTO, HttpServletResponse response) throws IOException;

    CraftEntity copyCraftForOpenApi(CraftCopyOpenDTO craftCopyOpenDTO);

    Map<Integer, List<CraftEntity>> listDefaultCraftByType(Collection<Integer> types);

    /**
     * 工艺批量编辑
     */
    Boolean batchUpdateState(BatchChangeStateDTO batchApprovalDTO, String username);

    /**
     * 工艺基本信息列表导出 - 下载默认模板
     * 查询10条BOM基本信息数据导出数据源excel
     *
     * @throws IOException
     */
    List<SimpleCraftExportDTO> convertToSimpleCraftExportDTO(List<CraftEntity> records);

    /**
     * 工艺基本信息列表导出 - 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    void uploadSimpleListExportTemplate(Boolean isTemplate, MultipartFile file, String username) throws IOException;

    /**
     * 工艺基本信息导出  :异步
     *
     * @param selectDTO
     * @return
     * @throws IOException
     */
    Long simpleListExportTask(CraftSelectDTO selectDTO, String username);

    /**
     * 分页查询当前导出任务列表
     *
     * @param size
     * @param current
     * @return
     */
    IPage<ExcelTask> simpleTaskPage(Integer current, Integer size, Boolean isTemplate);

    /**
     * 查询 工艺基本信息导入进度
     *
     * @return
     */
    ExcelTask simpleListTaskById(Long taskId, Boolean isTemplate);

    /**
     * 查询工艺关联的关联标
     * @param
     * @return
     */
    List<DataDelSchema> getCheckSchemas();

    /**
     * 校验是否能废弃
     * @param craftId 工艺id
     * @return 进度条key
     */
    String deleteCheck(Integer craftId);

    /**
     * 工艺删除前的数据预处理
     */
    void preHandleData(CraftEntity craft, List<DataDelSchema> dataDelSchemas);

    /**
     * 废弃校验进度
     * @param key redisKey
     * @return 进度
     */
    Object deleteCheckProgress(String key);

    CraftEntity detailPreview(Integer id);

    Map<String, CraftEntity> getCodeMap(List<String> codes);

    /**
     * 根据物料编码获取工序物料
     * @param materialCode
     * @param skuId
     * @return
     */
    BomEntity getProcedureMaterialByMaterialCode(String materialCode, Integer skuId, String productionState);

    void oneKeyDefault(ProcedureOneKeyDefaultDTO dto);

    /**
     * 刷新生效的工单
     * 3.13.3版本，德彰要求：同一个物料 或 同名工艺 能同时生效一个
     * @param craftIds 变更过的工艺id集合 （之所以用集合，考虑到批量导入的情况）
     */
    void refreshReleasedCraft(List<Integer> craftIds);

    /**
     * 通过物料查询工艺工序列表
     *
     * @return
     */
    List<CraftEntity> getCrafts(String materialCode);
    /**
     * 通过工艺ID和生产订单计划类型返回工序列表
     *
     * @return
     */
    List<CraftProcedureByWorkCenterDTO> getCraftProcedureByCraftId(Integer craftId);

    /**
     * 获取工艺关联的工艺资源列表（即工序定义关联的能力标签列表）
     *
     * @return
     */
    List<ProcedureCapacityLabelVO> getCapacityLabelList(Integer craftId);

    /**
     * 查询工艺信息
     * @param craftCode
     * @param isTemplate
     * @return
     */
    CraftEntity getCraft(String craftCode, Boolean isTemplate);

    /**
     * 工艺模板绑定物料
     *
     * @return
     */
    void bindMaterial(CraftBindMaterialDTO dto);

    /**
     * 查询工艺模板绑定的物料列表
     *
     * @return
     */
    List<MaterialFieldVO> getMaterialList(String craftCode);

    CraftVO detailV2(CraftDetailQueryDTO dto);

    Page<CraftVO> pageV2(CraftQueryDTO dto);

}
