package com.yelink.dfs.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderPlanScheduleWriteBackDTO;
import com.yelink.dfscommon.common.service.ImportDataExtendService;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-07-20 19:08
 */
public interface WorkOrderPlanService extends IService<WorkOrderPlanEntity>, ImportDataExtendService {

    /**
     * 创建工单计划
     *
     * @param workOrderEntity
     */
    void createWorkOrderPlan(WorkOrderEntity workOrderEntity);

    /**
     * 创建工单计划
     *
     * @param workOrderEntity
     */
    void saveWorkOrderPlan(WorkOrderEntity workOrderEntity);

    /**
     * 更新工单计划
     *
     * @param workOrderEntity
     */
    void updateWorkOrderPlan(WorkOrderEntity workOrderEntity);

    /**
     * 通过工单获取指定日期的计划量
     *
     * @param workOrders
     * @param date
     * @return
     */
    Double getPlanQuantityByOrders(List<String> workOrders, Date date);

    /**
     * 通过工单获取指定日期的计划量
     *
     * @param workOrder
     * @param date
     * @return
     */
    Double getPlanQuantityByOrder(String workOrder, Date date);

    /**
     * 通过工单查询日计划
     *
     * @param workOrderNumber
     * @param achievementRate 算达成率
     * @return
     */
    List<WorkOrderPlanEntity> getByWorkOrderNumber(String workOrderNumber, boolean achievementRate);

    /**
     * 通过工单批量查询日计划
     * @param workOrderNumbers 工单列表
     * @param achievementRate 算达成率
     * @return Map
     */

    Map<String, List<WorkOrderPlanEntity>> getByWorkOrderNumbers(Collection<String> workOrderNumbers, boolean achievementRate);

    /**
     * 排程回写
     * @param dto 入参
     * @return 结果
     */
    List<String> scheduleWriteBack(WorkOrderPlanScheduleWriteBackDTO dto);
}
