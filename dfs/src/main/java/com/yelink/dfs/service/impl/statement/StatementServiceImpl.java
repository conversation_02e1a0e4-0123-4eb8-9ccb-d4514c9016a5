package com.yelink.dfs.service.impl.statement;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ReportFormConstant;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.constant.order.CompletionRateEnum;
import com.yelink.dfs.constant.order.DeliveryApplicationStateEnum;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.controller.statement.SaleOrderDetailsExportHandler;
import com.yelink.dfs.controller.statement.SaleOrderProgressExportHandler;
import com.yelink.dfs.controller.statement.SaleOrderQualityTraceExportHandler;
import com.yelink.dfs.controller.statement.SaleOrderStatisticsToatlByCustomerExportHandler;
import com.yelink.dfs.controller.statement.SaleOrderStatisticsToatlByMaterialExportHandler;
import com.yelink.dfs.controller.statement.SaleOrderStatisticsToatlBySalesmanCustomerExportHandler;
import com.yelink.dfs.controller.statement.WorkOrderStatisticsToatlByCenterMaterialExportHandler;
import com.yelink.dfs.controller.statement.WorkOrderStatisticsToatlByMaterialExportHandler;
import com.yelink.dfs.controller.statement.WorkOrderStatisticsToatlByUnitMaterialExportHandler;
import com.yelink.dfs.entity.capacity.CapacityEntity;
import com.yelink.dfs.entity.capacity.dto.CapacityGetDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionResourceDTO;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.code.dto.CodeRecordSelectDTO;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.CustomerEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderLineRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.WorkOrderTeamRelevanceEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.MaterialEntitySelectDTO;
import com.yelink.dfs.entity.reporter.dto.ReporterRecordDTO;
import com.yelink.dfscommon.dto.dfs.ReporterRecordVO;
import com.yelink.dfs.entity.statement.dto.ProductFlowCodeRecordDTO;
import com.yelink.dfs.entity.statement.dto.ProductionDailyDTO;
import com.yelink.dfs.entity.statement.dto.SaleOrderTotalStatisticsSelectDTO;
import com.yelink.dfs.entity.statement.dto.StatementProductionDTO;
import com.yelink.dfs.entity.statement.dto.WorkOrderOverviewDTO;
import com.yelink.dfs.entity.statement.dto.WorkOrderTodayPlanSelectDTO;
import com.yelink.dfs.entity.statement.dto.WorkOrderTotalStatisticsSelectDTO;
import com.yelink.dfs.entity.statement.vo.DailyExportFormVO;
import com.yelink.dfs.entity.statement.vo.SaleOrderTotalStatisticsByCustomerVO;
import com.yelink.dfs.entity.statement.vo.SaleOrderTotalStatisticsByMaterialVO;
import com.yelink.dfs.entity.statement.vo.SaleOrderTotalStatisticsBySalesmanCustomerVO;
import com.yelink.dfs.entity.statement.vo.WorkOrderDayPlanVO;
import com.yelink.dfs.entity.statement.vo.WorkOrderTotalStatisticsByCenterMaterialVO;
import com.yelink.dfs.entity.statement.vo.WorkOrderTotalStatisticsByMaterialVO;
import com.yelink.dfs.entity.statement.vo.WorkOrderTotalStatisticsByUnitMaterialVO;
import com.yelink.dfs.entity.stock.DeliveryApplicationEntity;
import com.yelink.dfs.entity.stock.DeliveryApplicationMaterialEntity;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderDailyEntity;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderEntity;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderHourlyEntity;
import com.yelink.dfs.entity.target.metrics.dto.MetricsWorkOrderSelectDTO;
import com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseRequestInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.FdfsClientWrapper;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.impl.target.metrics.MetricsWorkOrderExtendService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.AreaService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitDayCountService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderLineRelevanceService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.order.WorkOrderTeamRelevanceService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.statement.StatementService;
import com.yelink.dfs.service.statement.impl.ReporterRecordExportHandler;
import com.yelink.dfs.service.stock.DeliveryApplicationExtendService;
import com.yelink.dfs.service.stock.DeliveryApplicationMaterialService;
import com.yelink.dfs.service.target.metrics.MetricsSaleOrderMaterialDailyService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderDailyService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderHourlyService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.common.sort.SortDTO;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.SynTableConstant;
import com.yelink.dfscommon.constant.ams.RequestStateEnum;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseRequestOpenSelectDTO;
import com.yelink.dfscommon.dto.dfs.DeliveryApplicationSelectDTO;
import com.yelink.dfscommon.entity.ams.PurchaseRequestEntity;
import com.yelink.dfscommon.entity.ams.PurchaseRequestMaterialEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderSelectDTO;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yelink.dfscommon.constant.Constant.SEP;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-05-21 14:14
 */
@Slf4j
@Service
public class StatementServiceImpl implements StatementService {
    /**
     * 日报的导出配置
     */
    private final static String DAILY_EXPORT_CONFIG_CODE = "DAILY_EXPORT_CONFIG";

    /**
     * 当前系统所属工厂
     */
    @Value("${factory}")
    private String factory;
    @Resource
    private DictService dictService;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private WorkOrderPlanService workOrderPlanService;
    @Resource
    private OrderWorkOrderService orderWorkOrderService;
    @Resource
    private MaterialService materialService;
    @Resource
    private ProductionLineService productionLineService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private SysTeamService sysTeamService;
    @Resource
    private GridService gridService;
    @Resource
    private WorkCenterService workCenterService;
    @Resource
    private AreaService areaService;
    @Resource
    private CustomerService customerService;
    @Resource
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    @Resource
    private ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    private ExtPurchaseRequestInterface extPurchaseRequestInterface;
    @Resource
    private StockInventoryDetailInterface stockInventoryDetailInterface;
    @Resource
    private CapacityService capacityService;
    @Resource
    private WorkOrderExtendService workOrderExtendService;
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;
    @Resource
    private DeliveryApplicationExtendService deliveryApplicationExtendService;
    @Resource
    private DeliveryApplicationMaterialService applicationMaterialService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private FdfsClientWrapper fdfsClientWrapper;
    @Resource
    private MetricsWorkOrderDailyService metricsWorkOrderDailyService;
    @Resource
    private MetricsWorkOrderHourlyService metricsWorkOrderHourlyService;
    @Resource
    private ReportLineService reportLineService;
    @Resource
    private ProductFlowCodeRecordService productFlowCodeRecordService;
    @Resource
    private ExcelService excelService;
    @Resource
    private MetricsSaleOrderMaterialDailyService metricsSaleOrderMaterialDailyService;
    @Resource
    private MetricsWorkOrderExtendService metricsWorkOrderExtendService;
    @Resource
    private WorkOrderDeviceRelevanceService workOrderDeviceRelevanceService;
    @Resource
    private WorkOrderTeamRelevanceService workOrderTeamRelevanceService;
    @Resource
    private WorkOrderLineRelevanceService workOrderLineRelevanceService;
    @Resource
    private WorkOrderBasicUnitDayCountService workOrderBasicUnitDayCountService;

    @Override
    public Page<WorkOrderOverviewDTO> statementListWorkOrder(MetricsWorkOrderSelectDTO selectDTO) {
        // 条件筛选
        if (StringUtils.isNotBlank(selectDTO.getStates()) || StringUtils.isNotBlank(selectDTO.getProductOrderNumber()) || StringUtils.isNotBlank(selectDTO.getSaleOrderNumber()) || StringUtils.isNotBlank(selectDTO.getMaterialCode())
                || StringUtils.isNotBlank(selectDTO.getMaterialName()) || StringUtils.isNotBlank(selectDTO.getGridIds()) || StringUtils.isNotBlank(selectDTO.getLineIds()) || StringUtils.isNotBlank(selectDTO.getTeamIds())
                || StringUtils.isNotBlank(selectDTO.getDeviceCodes()) || StringUtils.isNotBlank(selectDTO.getRelevanceName())) {
            List<String> workOrderNumberList = conditionStatementListWorkOrder(selectDTO);
            if (CollectionUtils.isEmpty(workOrderNumberList)) {
                return new Page<>();
            }
            selectDTO.setWorkOrderNumberList(workOrderNumberList);
        }

        Page<MetricsWorkOrderEntity> pageData = metricsWorkOrderExtendService.pageList(selectDTO);
        List<MetricsWorkOrderEntity> records = pageData.getRecords();

        List<WorkOrderOverviewDTO> workOrderOverviews = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            List<String> workOrderNumbers = records.stream().map(MetricsWorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            List<WorkOrderEntity> workOrderList = workOrderService.lambdaQuery()
                    .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                    .list();
            Map<String, WorkOrderEntity> workOrderNumberEntityMap = workOrderList.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, v -> v));

            // 物料
            Set<String> codes = workOrderList.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
            List<MaterialEntity> materials = materialService.getMaterialsByCodes(codes);
            Map<String, MaterialEntity> codeMaterialMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v, (oldValue, newValue) -> oldValue));

            // 产线、设备、班组、关联资源
            List<Integer> workOrderIds = workOrderList.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
            List<WorkOrderLineRelevanceEntity> lineRelevanceList = workOrderLineRelevanceService.lambdaQuery()
                    .in(WorkOrderLineRelevanceEntity::getWorkOrderId, workOrderIds)
                    .list();
            Map<Integer, List<WorkOrderLineRelevanceEntity>> idLineRelevancesMap = lineRelevanceList.stream().collect(Collectors.groupingBy(WorkOrderLineRelevanceEntity::getWorkOrderId));

            List<WorkOrderDeviceRelevanceEntity> deviceRelevanceList = workOrderDeviceRelevanceService.lambdaQuery()
                    .in(WorkOrderDeviceRelevanceEntity::getWorkOrderId, workOrderIds)
                    .list();
            Map<Integer, List<WorkOrderDeviceRelevanceEntity>> idDeviceRelevancesMap = deviceRelevanceList.stream().collect(Collectors.groupingBy(WorkOrderDeviceRelevanceEntity::getWorkOrderId));

            List<WorkOrderTeamRelevanceEntity> teamRelevanceList = workOrderTeamRelevanceService.lambdaQuery()
                    .in(WorkOrderTeamRelevanceEntity::getWorkOrderId, workOrderIds)
                    .list();
            Map<Integer, List<WorkOrderTeamRelevanceEntity>> idTeamRelevancesMap = teamRelevanceList.stream().collect(Collectors.groupingBy(WorkOrderTeamRelevanceEntity::getWorkOrderId));
            // 生产基本单元: 设备、班组
            Map<String, List<WorkOrderBasicUnitRelationEntity>> basicUnitGroup = workOrderBasicUnitRelationService.groupByWorkOrderNumbers(workOrderNumbers);


            Map<Integer, String> lineIdNameMap = new HashMap<>(16), lineIdGridNameMap = new HashMap<>(16), deviceIdNameMap = new HashMap<>(16), teamIdNameMap = new HashMap<>(16);
            Set<Integer> lineIds = workOrderList.stream().map(WorkOrderEntity::getLineId).filter(Objects::nonNull).collect(Collectors.toSet());
            lineIds.addAll(lineRelevanceList.stream().map(WorkOrderLineRelevanceEntity::getLineId).collect(Collectors.toSet()));
            if (!CollectionUtils.isEmpty(lineIds)) {
                List<ProductionLineEntity> lines = productionLineService.listByIds(lineIds);
                lineIdNameMap = lines.stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
                Map<Integer, Integer> lineIdGridIdMap = lines.stream().filter(e -> e.getGid() != null).collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getGid));

                Map<Integer, String> gridIdNameMap = gridService.list().stream().collect(Collectors.toMap(GridEntity::getGid, GridEntity::getGname));
                for (Map.Entry<Integer, Integer> lineIdGridIdEntry : lineIdGridIdMap.entrySet()) {
                    lineIdGridNameMap.put(lineIdGridIdEntry.getKey(), gridIdNameMap.get(lineIdGridIdEntry.getValue()));
                }
            }
            Set<Integer> deviceIds = deviceRelevanceList.stream().map(WorkOrderDeviceRelevanceEntity::getDeviceId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(deviceIds)) {
                List<DeviceEntity> devices = deviceService.listByIds(deviceIds);
                deviceIdNameMap = devices.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
            }
            Set<Integer> teamIds = teamRelevanceList.stream().map(WorkOrderTeamRelevanceEntity::getTeamId).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(teamIds)) {
                teamIdNameMap = sysTeamService.listByIds(teamIds).stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamName));
            }

            // 单件理论工时
            Map<String, Double> theoryHourMap = workOrderService.workOrderNumberTheoryHourMap(workOrderNumbers);

            for (MetricsWorkOrderEntity record : records) {
                WorkOrderEntity workOrder = workOrderNumberEntityMap.get(record.getWorkOrderNumber());
                MaterialEntity material = codeMaterialMap.getOrDefault(workOrder.getMaterialCode(), new MaterialEntity());

                // 关联资源
                String relevanceName;
                Map<Integer, String> finalLineIdNameMap = lineIdNameMap;
                relevanceName = idLineRelevancesMap.getOrDefault(workOrder.getWorkOrderId(), new ArrayList<>()).stream().map(res -> finalLineIdNameMap.get(res.getLineId())).collect(Collectors.joining(Constant.SEP));
                if (StringUtils.isEmpty(relevanceName)) {
                    Map<Integer, String> finalDeviceIdNameMap = deviceIdNameMap;
                    relevanceName = idDeviceRelevancesMap.getOrDefault(workOrder.getWorkOrderId(), new ArrayList<>()).stream().map(res -> finalDeviceIdNameMap.get(res.getDeviceId())).collect(Collectors.joining(Constant.SEP));
                    if (StringUtils.isEmpty(relevanceName)) {
                        Map<Integer, String> finalTeamIdNameMap = teamIdNameMap;
                        relevanceName = idTeamRelevancesMap.getOrDefault(workOrder.getWorkOrderId(), new ArrayList<>()).stream().map(res -> finalTeamIdNameMap.get(res.getTeamId())).collect(Collectors.joining(Constant.SEP));
                    }
                }
                Double theoryHour = theoryHourMap.getOrDefault(workOrder.getWorkOrderNumber(), 0.0);
                // 生产基本单元
                List<WorkOrderBasicUnitRelationEntity> basicUnits = basicUnitGroup.getOrDefault(record.getWorkOrderNumber(), Collections.emptyList());
                String basicUnitNames = basicUnits.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).filter(Objects::nonNull).collect(Collectors.joining(Constant.SEP));

                workOrderOverviews.add(WorkOrderOverviewDTO.builder()
                        .workOrderNumber(record.getWorkOrderNumber())
                        .state(workOrder.getState())
                        .stateName(WorkOrderStateEnum.getNameByCode(workOrder.getState()))
                        .saleOrderNumber(workOrder.getSaleOrderNumber())
                        .productOrderNumber(workOrder.getProductOrderNumber())
                        .materialCode(workOrder.getMaterialCode())
                        .materialName(material.getName())
                        .gridName(Objects.nonNull(workOrder.getLineId()) ? lineIdGridNameMap.get(workOrder.getLineId()) : null)
                        .lineId(workOrder.getLineId())
                        .lineName(lineIdNameMap.get(workOrder.getLineId()))
                        .teamId(workOrder.getTeamId())
                        .teamName(WorkCenterTypeEnum.TEAM.getCode().equals(workOrder.getWorkCenterType())? basicUnitNames : null)
                        .deviceName(WorkCenterTypeEnum.DEVICE.getCode().equals(workOrder.getWorkCenterType())? basicUnitNames : null)
                        .relevanceName(relevanceName)
                        .planQuantity(workOrder.getPlanQuantity())
                        .planStartDate(workOrder.getStartDate())
                        .planEndDate(workOrder.getEndDate())
                        .actualStartDate(workOrder.getActualStartDate())
                        .actualEndDate(workOrder.getActualEndDate())
                        .inputTotal(workOrder.getInputTotal())
                        .produceQuantity(record.getProduceQuantity())
                        .unqualifiedQuantity(record.getUnqualifiedQuantity())
                        .unqualifiedRecordItemQuantity(record.getUnqualifiedRecordItemQuantity())
                        .unqualifiedRecordQuantity(record.getUnqualifiedRecordQuantity())
                        .repairQuantity(record.getRepairQuantity())
                        .theoryHour(theoryHour)
                        .produceTheoryHour(NullableDouble.of(theoryHour).mul(workOrder.getFinishCount()).scale(2).cal())
                        .qualifiedRate(record.getQualifiedRate())
                        .finishRate(record.getFinishRate())
                        .build());
            }
        }
        Page<WorkOrderOverviewDTO> page = new Page<>(pageData.getCurrent(), pageData.getSize(), pageData.getTotal());
        page.setRecords(workOrderOverviews);
        return page;
    }

    private List<String> conditionStatementListWorkOrder(MetricsWorkOrderSelectDTO selectDTO) {
        if (StringUtils.isNotBlank(selectDTO.getGridIds())) {
            List<Integer> lineIds = productionLineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId)
                    .in(ProductionLineEntity::getGid, Arrays.stream(selectDTO.getGridIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList()))
                    .list().stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            if (StringUtils.isNotBlank(selectDTO.getLineIds())) {
                // 取交集
                List<Integer> lineIdsFilter = Arrays.stream(selectDTO.getLineIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
                lineIds.retainAll(lineIdsFilter);
            }
            if (CollectionUtils.isEmpty(lineIds)) {
                return new ArrayList<>();
            } else {
                selectDTO.setLineIds(lineIds.stream().map(String::valueOf).collect(Collectors.joining(Constants.SEP)));
            }
        }
        List<Integer> relevanceWorkOrderIds = new ArrayList<>();
        if (StringUtils.isNotBlank(selectDTO.getRelevanceName())) {
            List<Integer> lineIds = productionLineService.lambdaQuery()
                    .like(ProductionLineEntity::getName, selectDTO.getRelevanceName())
                    .list().stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());

            List<Integer> deviceIds = deviceService.lambdaQuery()
                    .like(DeviceEntity::getDeviceName, selectDTO.getRelevanceName())
                    .list().stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());

            List<Integer> teamIds = sysTeamService.lambdaQuery()
                    .like(SysTeamEntity::getTeamName, selectDTO.getRelevanceName())
                    .list().stream().map(SysTeamEntity::getId).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(lineIds)) {
                List<Integer> lineWorkOrderIds = workOrderLineRelevanceService.lambdaQuery()
                        .in(WorkOrderLineRelevanceEntity::getLineId, lineIds)
                        .list().stream().map(WorkOrderLineRelevanceEntity::getWorkOrderId).collect(Collectors.toList());
                relevanceWorkOrderIds.addAll(lineWorkOrderIds);
            }
            if (!CollectionUtils.isEmpty(deviceIds)) {
                List<Integer> deviceWorkOrderIds = workOrderDeviceRelevanceService.lambdaQuery()
                        .in(WorkOrderDeviceRelevanceEntity::getDeviceId, deviceIds)
                        .list().stream().map(WorkOrderDeviceRelevanceEntity::getWorkOrderId).collect(Collectors.toList());
                relevanceWorkOrderIds.addAll(deviceWorkOrderIds);
            }
            if (!CollectionUtils.isEmpty(teamIds)) {
                List<Integer> teamWorkOrderIds = workOrderTeamRelevanceService.lambdaQuery()
                        .in(WorkOrderTeamRelevanceEntity::getTeamId, teamIds)
                        .list().stream().map(WorkOrderTeamRelevanceEntity::getWorkOrderId).collect(Collectors.toList());
                relevanceWorkOrderIds.addAll(teamWorkOrderIds);
            }
            if (CollectionUtils.isEmpty(relevanceWorkOrderIds)) {
                return new ArrayList<>();
            }
        }

        MaterialEntitySelectDTO materialSelect = new MaterialEntitySelectDTO();
        materialSelect.setCode(selectDTO.getMaterialCode());
        materialSelect.setName(selectDTO.getMaterialName());

        WorkOrderSelectDTO workOrderSelect = WorkOrderSelectDTO.builder()
                .isShowSimpleInfo(true)
                .states(selectDTO.getStates())
                .saleOrderNumber(selectDTO.getSaleOrderNumber())
                .productOrderNumber(selectDTO.getProductOrderNumber())
                .lineId(selectDTO.getLineIds())
                .teamIds(selectDTO.getTeamIds())
                .deviceCodes(selectDTO.getDeviceCodes())
                .materialFields(materialSelect)
                .workOrderIds(relevanceWorkOrderIds)
                .current(1)
                .size(Integer.MAX_VALUE)
                .build();
        Page<WorkOrderEntity> filterWorkOrderPage = workOrderService.getWorkOrderEntityPage(workOrderSelect, null);

        return filterWorkOrderPage.getRecords().stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
    }

    @Override
    public Page<ReporterRecordVO> statementListReporterRecord(ReporterRecordDTO dto) {
        Page<ReporterRecordVO> page = reportLineService.getReporterRecord(dto);
        // 设置客户编号、名称
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<Integer> workOrderIds = page.getRecords().stream().map(ReporterRecordVO::getWorkOrderId).collect(Collectors.toList());
            Map<Integer, SaleOrderVO> map = orderWorkOrderService.listOrderByWorkIdList(workOrderIds);
            for (ReporterRecordVO entity : page.getRecords()) {
                SaleOrderVO orderEntity = map.getOrDefault(entity.getWorkOrderId(), new SaleOrderVO());
                entity.setCustomer(orderEntity.getCustomerName());
                entity.setCustomerCode(orderEntity.getCustomerCode());
            }
        }
        return page;
    }

    @Override
    public Page<ProductFlowCodeRecordDTO> statementListCodeRecord(CodeRecordSelectDTO codeRecordSelectDTO) {
        Page<ProductFlowCodeRecordEntity> page = productFlowCodeRecordService.list(codeRecordSelectDTO);
        List<ProductFlowCodeRecordDTO> recordList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            Set<String> workOrderNumberSet = page.getRecords().stream().map(ProductFlowCodeRecordEntity::getRelationNumber).collect(Collectors.toSet());
            Map<String, Integer> workOrderNumberIdMap = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderId, WorkOrderEntity::getWorkOrderNumber)
                    .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumberSet)
                    .list().stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getWorkOrderId));
            // 设置客户编号、名称
            Map<Integer, SaleOrderVO> map = orderWorkOrderService.listOrderByWorkIdList(new ArrayList<>(workOrderNumberIdMap.values()));

            for (ProductFlowCodeRecordEntity entity : page.getRecords()) {
                ProductFlowCodeRecordDTO record = new ProductFlowCodeRecordDTO();
                // 复制属性
                BeanUtils.copyProperties(entity, record);
                Integer workOrderId = workOrderNumberIdMap.get(entity.getRelationNumber());
                if (Objects.nonNull(workOrderId)) {
                    SaleOrderVO orderEntity = map.getOrDefault(workOrderId, new SaleOrderVO());
                    record.setCustomer(orderEntity.getCustomerName());
                    record.setCustomerCode(orderEntity.getCustomerCode());
                    record.setSaleOrderNumber(orderEntity.getSaleOrderNumber());
                }
                recordList.add(record);
            }
        }
        Page<ProductFlowCodeRecordDTO> pageData = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        pageData.setRecords(recordList);
        return pageData;
    }

    @Override
    public Boolean saveConfig(String config) {
        boolean ret;
        DictEntity dictEntity = dictService.lambdaQuery()
                .eq(DictEntity::getCode, DAILY_EXPORT_CONFIG_CODE)
                .one();
        if (dictEntity != null) {
            dictEntity.setDes(config);
            ret = dictService.updateById(dictEntity);
        } else {
            DictEntity dict = DictEntity.builder()
                    .code(DAILY_EXPORT_CONFIG_CODE)
                    .name("日报导出配置")
                    .des(config)
                    .build();
            ret = dictService.save(dict);
        }
        return ret;
    }

    @Override
    public String getConfig() {
        DictEntity dictEntity = dictService.lambdaQuery()
                .eq(DictEntity::getCode, DAILY_EXPORT_CONFIG_CODE)
                .one();
        return dictEntity != null ? dictEntity.getDes() : null;
    }

    @Override
    public void exportTemplate(Boolean isDefault, Integer type, HttpServletResponse response) throws IOException {
        String fileName, templateName;
        byte[] fileByteBuffer;
        if (isDefault) {
            // 默认生产日报模板文件名、默认的生产统计指标模板
            fileName = type == 1 ? ReportFormConstant.DEFAULT_PRODUCTION_DAILY_TEMPLATE + Constant.XLSX : ReportFormConstant.DEFAULT_PRODUCTION_TEMPLATE + Constant.XLSX;
            // 至美导出默认的模板和通用厂有差异性
            if (SynTableConstant.ZHIMEI.equals(factory)) {
                fileName = ReportFormConstant.ZHIMEI_DEFAULT_PRODUCTION_DAILY_TEMPLATE + Constant.XLSX;
            }
            // 下载默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template" + File.separator + fileName);
            if (!resource.exists()) {
                throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
            }
            try (InputStream inputStream = resource.getInputStream();) {
                // 最后返回
                templateName = fileName;
                fileByteBuffer = IOUtils.toByteArray(inputStream);
            } catch (IOException e) {
                log.error("文件获取失败", e);
                throw new ResponseException(RespCodeEnum.FILE_DOWNLOAD_FAILED);
            }
        } else {
            // 用户自己上传的模板文件名、用户自己上传的生产统计指标模板
            fileName = type == 1 ? ReportFormConstant.PRODUCTION_DAILY_TEMPLATE : ReportFormConstant.PRODUCTION_STATISTICS_INDICATOR_TEMPLATE;
            String fileUrl = fastDfsClientService.getUrlByFileCode(fileName);
            if (StringUtils.isBlank(fileUrl)) {
                throw new ResponseException(RespCodeEnum.TEMPLATE_IS_NOT_EXIST);
            }
            templateName = fileName + fileUrl.substring(fileUrl.lastIndexOf("."));
            fileByteBuffer = fastDfsClientService.getFileStream(fileUrl);
        }
        ExcelTemplateImportUtil.responseToClient(response, fileByteBuffer, templateName);
    }

    @Override
    public void uploadTemplate(Integer type, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ResponseException(RespCodeEnum.IMPORT_FILE_IS_NULL);
        }
        //文件后缀判断
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename) ) {
            throw new ResponseException(RespCodeEnum.PARSING_DATA_IS_EMPTY);
        }
        // 保存文件到fast-dfs
        String fileName = type == 1 ? ReportFormConstant.PRODUCTION_DAILY_TEMPLATE : ReportFormConstant.PRODUCTION_STATISTICS_INDICATOR_TEMPLATE;
        fastDfsClientService.uploadFile(file, null, fileName);
    }

    @Override
    @Deprecated
    public List<DailyExportFormVO> listExportForm() {
        // 至美
        if (SynTableConstant.ZHIMEI.equals(factory)) {
            return DailyExportFormVO.createZhiMeiForms();
        }
        return DailyExportFormVO.createCommonForms();
    }

    @Override
    public Page<StatementProductionDTO.StatementProductionVO> listDailyProductionStatement(StatementProductionDTO.SelectDTO selectDTO) {
        // 细化查询条件
        selectDTO = getSelectDTO(selectDTO);
        if (Objects.isNull(selectDTO)) {
            return new Page<>();
        }
        RecordWorkOrderDayCountMapper dayCountMapper = (RecordWorkOrderDayCountMapper) recordWorkOrderDayCountService.getBaseMapper();
        Page<StatementProductionDTO.StatementProductionVO> pageData = dayCountMapper.listProductionStatement(selectDTO, new Page<>(selectDTO.getCurrent(), selectDTO.getSize()));
        // 设置一些值
        setProductionStatement(pageData.getRecords());
        // 设置每条数据查的时间段, 日
        for (StatementProductionDTO.StatementProductionVO record : pageData.getRecords()) {
            ProductionDailyDTO.TimeRange calcTimeRange = ProductionDailyDTO.TimeRange.builder()
                    .startTime(record.getDateTime())
                    .endTime(DateUtil.addDay(record.getDateTime(), 1))
                    .build();
            record.setTimeRange(calcTimeRange);
        }
        return pageData;
    }

    @Override
    public Page<StatementProductionDTO.StatementProductionVO> listWeekProductionStatement(StatementProductionDTO.SelectDTO selectDTO) {
        // 细化查询条件
        selectDTO = getSelectDTO(selectDTO);
        if (Objects.isNull(selectDTO)) {
            return new Page<>();
        }

        RecordWorkOrderDayCountMapper dayCountMapper = (RecordWorkOrderDayCountMapper) recordWorkOrderDayCountService.getBaseMapper();
        Page<StatementProductionDTO.StatementProductionVO> pageData = dayCountMapper.listWeekProductionStatement(selectDTO, new Page<>(selectDTO.getCurrent(), selectDTO.getSize()));
        if (CollectionUtils.isEmpty(pageData.getRecords())) {
            return new Page<>(selectDTO.getCurrent(), selectDTO.getSize());
        }
        // 设置一些值
        setProductionStatement(pageData.getRecords());
        // 设置每条数据查的时间段, 周
        for (StatementProductionDTO.StatementProductionVO record : pageData.getRecords()) {
            ProductionDailyDTO.TimeRange calcTimeRange = ProductionDailyDTO.TimeRange.builder()
                    .startTime(DateUtil.getMondayOfWeek(record.getDateTime()))
                    .endTime(DateUtil.getSundayOfWeek(record.getDateTime()))
                    .build();
            record.setTimeRange(calcTimeRange);
        }
        return pageData;
    }

    @Override
    public Page<StatementProductionDTO.StatementProductionVO> listMonthProductionStatement(StatementProductionDTO.SelectDTO selectDTO) {
        // 细化查询条件
        selectDTO = getSelectDTO(selectDTO);
        if (Objects.isNull(selectDTO)) {
            return new Page<>();
        }

        RecordWorkOrderDayCountMapper dayCountMapper = (RecordWorkOrderDayCountMapper) recordWorkOrderDayCountService.getBaseMapper();
        Page<StatementProductionDTO.StatementProductionVO> pageData = dayCountMapper.listMonthProductionStatement(selectDTO, new Page<>(selectDTO.getCurrent(), selectDTO.getSize()));
        if (CollectionUtils.isEmpty(pageData.getRecords())) {
            return new Page<>(selectDTO.getCurrent(), selectDTO.getSize());
        }
        // 设置一些值
        setProductionStatement(pageData.getRecords());
        // 设置每条数据查的时间段, 月
        for (StatementProductionDTO.StatementProductionVO record : pageData.getRecords()) {
            ProductionDailyDTO.TimeRange calcTimeRange = ProductionDailyDTO.TimeRange.builder()
                    .startTime(DateUtil.getFirstDayDateOfMonth(record.getDateTime()))
                    .endTime(DateUtil.getLastDayDateOfMonth(record.getDateTime()))
                    .build();
            record.setTimeRange(calcTimeRange);
        }
        return pageData;
    }

    /**
     * 细化查询条件
     *
     * @param selectDTO
     * @return
     */
    private StatementProductionDTO.SelectDTO getSelectDTO(StatementProductionDTO.SelectDTO selectDTO) {
        // 客户编码、客户名称
        if (StringUtils.isNotBlank(selectDTO.getCustomerCode()) || StringUtils.isNotBlank(selectDTO.getCustomerName())) {
            List<String> customerCodes = customerService.lambdaQuery()
                    .like(StringUtils.isNotBlank(selectDTO.getCustomerCode()), CustomerEntity::getCustomerCode, selectDTO.getCustomerCode())
                    .like(StringUtils.isNotBlank(selectDTO.getCustomerName()), CustomerEntity::getCustomerName, selectDTO.getCustomerName())
                    .list().stream().map(CustomerEntity::getCustomerCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(customerCodes)) {
                return null;
            }
            // 查询销售订单
            SaleOrderSelectOpenDTO saleOrderSelectDTO = new SaleOrderSelectOpenDTO();
            saleOrderSelectDTO.setCurrent(1);
            saleOrderSelectDTO.setSize(Integer.MAX_VALUE);
            saleOrderSelectDTO.setShowType(ShowTypeEnum.ORDER.getType());
            saleOrderSelectDTO.setCustomerCodes(customerCodes);
            List<String> saleOrderNumbers = extSaleOrderInterface.getPage(saleOrderSelectDTO).getRecords().stream().map(SaleOrderVO::getSaleOrderNumber).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(saleOrderNumbers)) {
                return null;
            }
            selectDTO.setSaleOrderNumbers(saleOrderNumbers);
        }
        // 物料筛选
        if (StringUtils.isNotBlank(selectDTO.getMaterialCode()) || StringUtils.isNotBlank(selectDTO.getMaterialName()) || StringUtils.isNotBlank(selectDTO.getMaterialStandard())) {
            MaterialEntitySelectDTO materialEntitySelect = new MaterialEntitySelectDTO();
            materialEntitySelect.setCode(selectDTO.getMaterialCode());
            materialEntitySelect.setName(selectDTO.getMaterialName());
            materialEntitySelect.setStandard(selectDTO.getMaterialStandard());
            List<String> materialCodes = materialService.materialCodesByMaterialFields(materialEntitySelect);
            if (CollectionUtils.isEmpty(materialCodes)) {
                return null;
            }
            selectDTO.setMaterialCodes(materialCodes);
        }
        // 车间名称筛选、工作中心
        if (StringUtils.isNotBlank(selectDTO.getGridName())) {
            List<Integer> gIds = gridService.lambdaQuery()
                    .select(GridEntity::getGid)
                    .like(GridEntity::getGname, selectDTO.getGridName())
                    .list().stream().map(GridEntity::getGid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(gIds)) {
                return null;
            }
            List<Integer> lineIds = productionLineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId)
                    .in(ProductionLineEntity::getGid, gIds)
                    .like(StringUtils.isNotBlank(selectDTO.getMagName()), ProductionLineEntity::getMagNickname, selectDTO.getMagName())
                    .list().stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lineIds)) {
                return null;
            }
            selectDTO.setLineIds(lineIds);
        }
        // 工作中心筛选
        if (StringUtils.isNotBlank(selectDTO.getWorkCenterName())) {
            List<Integer> workCenterIds = workCenterService.lambdaQuery()
                    .select(WorkCenterEntity::getId)
                    .like(WorkCenterEntity::getName, selectDTO.getWorkCenterName())
                    .list().stream().map(WorkCenterEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workCenterIds)) {
                return null;
            }
            selectDTO.setWorkCenterIds(workCenterIds);
        }
        // 基本生产单元: 制造单元、班组、设备
        if (StringUtils.isNotBlank(selectDTO.getProductionBasicUnit())) {
            List<Integer> tempLineIds = productionLineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId)
                    .like(ProductionLineEntity::getName, selectDTO.getProductionBasicUnit())
                    .like(StringUtils.isNotBlank(selectDTO.getMagName()), ProductionLineEntity::getMagNickname, selectDTO.getMagName())
                    .list().stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            List<Integer> teamIds = sysTeamService.lambdaQuery()
                    .select(SysTeamEntity::getId)
                    .like(SysTeamEntity::getTeamName, selectDTO.getProductionBasicUnit())
                    .list().stream().map(SysTeamEntity::getId).collect(Collectors.toList());
            List<Integer> deviceIds = deviceService.lambdaQuery()
                    .select(DeviceEntity::getDeviceId)
                    .like(DeviceEntity::getDeviceName, selectDTO.getProductionBasicUnit())
                    .list().stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tempLineIds) && CollectionUtils.isEmpty(teamIds) && CollectionUtils.isEmpty(deviceIds)) {
                return null;
            }
            List<Integer> workOrderIds = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderId)
                    .in(!CollectionUtils.isEmpty(tempLineIds), WorkOrderEntity::getLineId, tempLineIds)
                    .or()
                    .in(!CollectionUtils.isEmpty(teamIds), WorkOrderEntity::getTeamId, teamIds)
                    .or()
                    .in(!CollectionUtils.isEmpty(deviceIds), WorkOrderEntity::getDeviceId, deviceIds)
                    .list().stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderIds)) {
                return null;
            }
            selectDTO.setWorkOrderIds(workOrderIds);
        }
        return selectDTO;
    }

    /**
     * 设置一些值
     */
    private void setProductionStatement(List<StatementProductionDTO.StatementProductionVO> productionRecords) {
        if (CollectionUtils.isEmpty(productionRecords)) {
            return;
        }
        Set<String> materialSet = productionRecords.stream().map(StatementProductionDTO.StatementProductionVO::getMaterialCode).collect(Collectors.toSet());
        Map<String, MaterialEntity> materialCodeEntityMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialSet)
                .list().stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));

        Map<Integer, Integer> lineIdGidMap = new HashMap<>(16);
        Map<Integer, String> centerIdNameMap = new HashMap<>(16), lineIdNameMap = new HashMap<>(16),
                deviceIdNameMap = new HashMap<>(16), teamIdNameMap = new HashMap<>(16);
        Map<Integer, String> lineIdMagNameMap = new HashMap<>(16), deviceIdMagNameMap = new HashMap<>(16);
        Set<Integer> workCenterIds = productionRecords.stream().map(StatementProductionDTO.StatementProductionVO::getWorkCenterId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(workCenterIds)) {
            centerIdNameMap = workCenterService.listByIds(workCenterIds)
                    .stream().collect(Collectors.toMap(WorkCenterEntity::getId, WorkCenterEntity::getName));
        }
        Set<Integer> lineIds = productionRecords.stream().map(StatementProductionDTO.StatementProductionVO::getLineId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(lineIds)) {
            List<ProductionLineEntity> lines = productionLineService.listByIds(lineIds);
            lineIdGidMap = lines.stream().filter(res -> Objects.nonNull(res.getGid())).collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getGid));
            lineIdNameMap = lines.stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
            lineIdMagNameMap = lines.stream().filter(res -> Objects.nonNull(res.getMagNickname())).collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getMagNickname));
        }
        Set<Integer> deviceIds = productionRecords.stream().map(StatementProductionDTO.StatementProductionVO::getDeviceId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(deviceIds)) {
            List<DeviceEntity> devices = deviceService.listByIds(deviceIds);
            deviceIdNameMap = devices.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
            deviceIdMagNameMap = devices.stream().filter(res -> Objects.nonNull(res.getMagNickname())).collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getMagNickname));
        }
        Set<Integer> teamIds = productionRecords.stream().map(StatementProductionDTO.StatementProductionVO::getTeamId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(teamIds)) {
            teamIdNameMap = sysTeamService.listByIds(teamIds).stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamName));
        }
        Map<Integer, String> gidNameMap = gridService.list()
                .stream().collect(Collectors.toMap(GridEntity::getGid, GridEntity::getGname));

        Set<String> saleOrderNumbers = productionRecords.stream().map(StatementProductionDTO.StatementProductionVO::getSaleOrderNumber)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        SaleOrderSelectOpenDTO saleOrderSelectDTO = new SaleOrderSelectOpenDTO();
        saleOrderSelectDTO.setCurrent(1);
        saleOrderSelectDTO.setSize(Integer.MAX_VALUE);
        saleOrderSelectDTO.setShowType(ShowTypeEnum.ORDER.getType());
        saleOrderSelectDTO.setSaleOrderNumbers(new ArrayList<>(saleOrderNumbers));
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(saleOrderSelectDTO);
        Map<String, SaleOrderVO> saleOrderNumberMap = Objects.nonNull(pageResult.getRecords()) ? pageResult.getRecords().stream().collect(Collectors.toMap(SaleOrderVO::getSaleOrderNumber, v -> v)) : new HashMap<>();

        for (StatementProductionDTO.StatementProductionVO productionVO : productionRecords) {
            MaterialEntity tempMaterial = materialCodeEntityMap.getOrDefault(productionVO.getMaterialCode(), new MaterialEntity());
            // 基本生产单元：产线、设备或班组
            String productionBasicUnit = Objects.nonNull(productionVO.getLineId()) ? lineIdNameMap.get(productionVO.getLineId()) :
                    Objects.nonNull(productionVO.getDeviceId()) ? deviceIdNameMap.get(productionVO.getDeviceId()) :
                            Objects.nonNull(productionVO.getTeamId()) ? teamIdNameMap.get(productionVO.getTeamId()) : "";
            // 班组没有负责人
            String magName = Objects.nonNull(productionVO.getLineId()) ? lineIdMagNameMap.get(productionVO.getLineId()) :
                    Objects.nonNull(productionVO.getDeviceId()) ? deviceIdMagNameMap.get(productionVO.getDeviceId()) : "";

            SaleOrderVO tempSaleOrder = saleOrderNumberMap.getOrDefault(productionVO.getSaleOrderNumber(), new SaleOrderVO());
            productionVO.setMaterialName(tempMaterial.getName());
            productionVO.setMaterialStandard(tempMaterial.getStandard());
            productionVO.setUnit(tempMaterial.getComp());
            productionVO.setPassRate(MathUtil.divideDouble(productionVO.getProductQuantity(), MathUtil.add(productionVO.getProductQuantity(), productionVO.getUnqualified()), 4));
            productionVO.setGridName(Objects.nonNull(productionVO.getLineId()) ? gidNameMap.get(lineIdGidMap.get(productionVO.getLineId())) : "");
            productionVO.setWorkCenterName(centerIdNameMap.get(productionVO.getWorkCenterId()));
            productionVO.setProductionBasicUnit(productionBasicUnit);
            productionVO.setMagName(magName);
            productionVO.setWorkOrderProgress(MathUtil.divideDouble(productionVO.getWorkOrderFinishQuantity(), productionVO.getWorkOrderPlanQuantity(), 4));
            productionVO.setWorkOrderPassRate(MathUtil.divideDouble(productionVO.getWorkOrderFinishQuantity(), MathUtil.add(productionVO.getWorkOrderFinishQuantity(), productionVO.getWorkOrderUnqualified()), 4));
            productionVO.setCustomerCode(tempSaleOrder.getCustomerCode());
            productionVO.setCustomerName(tempSaleOrder.getCustomerName());
            productionVO.setCustomerAddress(tempSaleOrder.getCustomerAddr());
        }
    }

    @Override
    public Page<StatementProductionDTO.WorkOrderEfficiencyVO> pageWorkOrderEfficiency(StatementProductionDTO.WorkOrderEfficiencySelectDTO selectDTO) {
        // yyyy-MM-dd 00:00:00
        Date startTime = null, endTime = null;
        if (Objects.nonNull(selectDTO.getTimeRange()) && Objects.nonNull(selectDTO.getTimeRange().getStartTime()) && Objects.nonNull(selectDTO.getTimeRange().getEndTime())) {
            selectDTO.setTimeRange(ProductionDailyDTO.TimeRange.builder()
                    .startTime(DateUtil.formatToDate(selectDTO.getTimeRange().getStartTime(), DateUtil.DATETIME_FORMAT_ZERO))
                    .endTime(DateUtil.formatToDate(selectDTO.getTimeRange().getEndTime(), DateUtil.DATETIME_FORMAT_ZERO)).build());
            startTime = selectDTO.getTimeRange().getStartTime();
            endTime = selectDTO.getTimeRange().getEndTime();
        }
        // 细化查询条件
        selectDTO = getSelectDTO(selectDTO);
        if (Objects.isNull(selectDTO)) {
            return new Page<>();
        }
        // 默认查：生效+投产+挂起+完成
        String defaultStates = Stream.of(WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode(), WorkOrderStateEnum.FINISHED.getCode())
                .map(String::valueOf)
                .collect(Collectors.joining(Constant.SEP));
        if (StringUtils.isEmpty(selectDTO.getStates())) {
            selectDTO.setStates(defaultStates);
        }
        List<String> workOrderNumbers = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber)
                .in(WorkOrderEntity::getState, Arrays.asList(selectDTO.getStates().split(Constants.SEP)))
                .like(StringUtils.isNotBlank(selectDTO.getWorkOrderNumber()), WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber())
                .like(StringUtils.isNotBlank(selectDTO.getWorkOrderName()), WorkOrderEntity::getWorkOrderName, selectDTO.getWorkOrderName())
                .like(StringUtils.isNotBlank(selectDTO.getWorkCenterName()), WorkOrderEntity::getWorkCenterName, selectDTO.getWorkCenterName())
                .in(StringUtils.isNotBlank(selectDTO.getWorkCenterIds()), WorkOrderEntity::getWorkCenterId, StringUtils.isNotBlank(selectDTO.getWorkCenterIds()) ? Arrays.asList(selectDTO.getWorkCenterIds().split(Constants.SEP)) : null)
                .in(!CollectionUtils.isEmpty(selectDTO.getLineIds()), WorkOrderEntity::getLineId, selectDTO.getLineIds())
                .in(!CollectionUtils.isEmpty(selectDTO.getDeviceIds()), WorkOrderEntity::getDeviceId, selectDTO.getDeviceIds())
                .in(!CollectionUtils.isEmpty(selectDTO.getTeamIds()), WorkOrderEntity::getTeamId, selectDTO.getTeamIds())
                .in(!CollectionUtils.isEmpty(selectDTO.getMaterialCodes()), WorkOrderEntity::getMaterialCode, selectDTO.getMaterialCodes())
                .list().stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return new Page<>();
        }

        LambdaQueryWrapper<MetricsWorkOrderDailyEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MetricsWorkOrderDailyEntity::getWorkOrderNumber, workOrderNumbers)
                .between(Objects.nonNull(startTime), MetricsWorkOrderDailyEntity::getRecordDate, startTime, endTime)
                .and(o -> o.ne(MetricsWorkOrderDailyEntity::getProduceQuantity, 0).or().isNotNull(MetricsWorkOrderDailyEntity::getAchievements));
        // 合格率过滤
        rateFilter(selectDTO.getPassRate(), wrapper, MetricsWorkOrderDailyEntity::getQualifiedRate);
        // 绩效过滤
        rateFilter(selectDTO.getProductionEfficiency(), wrapper, MetricsWorkOrderDailyEntity::getAchievements);
        if (!CollectionUtils.isEmpty(selectDTO.getSortList())) {
            for (SortDTO sortDTO : selectDTO.getSortList()) {
                // 合格率 排序
                wrapper.orderBy("passRate".equals(sortDTO.getColumn()), !sortDTO.getDesc(), MetricsWorkOrderDailyEntity::getQualifiedRate);
                // 绩效  排序
                wrapper.orderBy("productionEfficiency".equals(sortDTO.getColumn()), !sortDTO.getDesc(), MetricsWorkOrderDailyEntity::getAchievements);
            }
        }

        wrapper.orderByDesc(MetricsWorkOrderDailyEntity::getRecordDate)
                .orderByDesc(MetricsWorkOrderDailyEntity::getId);

        Page<MetricsWorkOrderDailyEntity> pageData = metricsWorkOrderDailyService.page(new Page<>(selectDTO.getCurrent(), selectDTO.getSize()), wrapper);
        List<MetricsWorkOrderDailyEntity> records = pageData.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>();
        }
        List<String> existWorkOrderNumbers = records.stream().map(MetricsWorkOrderDailyEntity::getWorkOrderNumber).collect(Collectors.toList());
        List<WorkOrderEntity> workOrders = workOrderService.lambdaQuery()
                .in(WorkOrderEntity::getWorkOrderNumber, existWorkOrderNumbers)
                .list();
        Map<String, WorkOrderEntity> workOrderNumberEntityMap = workOrders.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, v -> v));

        Set<String> materialCodeSet = workOrders.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
        Map<String, MaterialEntity> materialCodeEntityMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodeSet)
                .list().stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));

        Map<String, List<WorkOrderBasicUnitRelationEntity>> orderBasicUnitGroup = workOrderBasicUnitRelationService.groupByWorkOrderNumbers(existWorkOrderNumbers);
        List<StatementProductionDTO.WorkOrderEfficiencyVO> rets = new ArrayList<>();
        for (MetricsWorkOrderDailyEntity workOrderDailyProgress : records) {
            WorkOrderEntity workOrder = workOrderNumberEntityMap.get(workOrderDailyProgress.getWorkOrderNumber());

            // 今日投入工时、理论产出工时、绩效、在岗人数
            double inputHours = Objects.isNull(workOrderDailyProgress.getActualWorkingHour()) ? 0.0 : workOrderDailyProgress.getActualWorkingHour();
            double outputHours = Objects.isNull(workOrderDailyProgress.getTheoryWorkingHour()) ? 0.0 : workOrderDailyProgress.getTheoryWorkingHour();
            double productionEfficiency = Objects.isNull(workOrderDailyProgress.getAchievements()) ? 0.0 : workOrderDailyProgress.getAchievements();
            double attendanceNum = Objects.isNull(workOrderDailyProgress.getWorkingPeopleQuantity()) ? 0.0 : workOrderDailyProgress.getWorkingPeopleQuantity();
            double qualifiedRate = Objects.isNull(workOrderDailyProgress.getQualifiedRate()) ? 0.0 : workOrderDailyProgress.getQualifiedRate();
            double unqualifiedRecordQuantity =Objects.isNull(workOrderDailyProgress.getUnqualifiedRecordQuantity()) ? 0.0 : workOrderDailyProgress.getUnqualifiedRecordQuantity();
            double unqualifiedRecordItemQuantity = Objects.isNull(workOrderDailyProgress.getUnqualifiedRecordItemQuantity()) ? 0.0 : workOrderDailyProgress.getUnqualifiedRecordItemQuantity();

            // 生产基本单元
            List<WorkOrderBasicUnitRelationEntity> basicUnits = orderBasicUnitGroup.getOrDefault(workOrderDailyProgress.getWorkOrderNumber(), Collections.emptyList());

            List<ProductionResourceDTO> mainResourceDTOS = basicUnits.stream().map(
                                    entity -> ProductionResourceDTO.builder()
                                            .workCenterType(entity.getWorkCenterType())
                                            .productionBasicUnitId(entity.getProductionBasicUnitId())
                                            .build())
                    .collect(Collectors.toList());
            // 查询工单关联的关联资源
            Map<Integer, List<ProductionResourceDTO>> relatedResourceMap = capacityService.getRelatedResourceByWorkOrder(workOrderNumbers);
            List<Integer> basicUnitIds = basicUnits.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
            String basicUnitIdsStr = basicUnitIds.stream().map(String::valueOf).collect(Collectors.joining(Constant.SEP));
            String basicUnitNames = basicUnits.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).filter(Objects::nonNull).collect(Collectors.joining(Constant.SEP));

            Double uph = null, hc = null;
            CapacityGetDTO capacityGetDTO = CapacityGetDTO.builder()
                    .materialCode(workOrder.getMaterialCode())
                    .workCenterId(workOrder.getWorkCenterId())
                    .mainResourceDTOS(mainResourceDTOS)
                    .relatedResourceDTOS(relatedResourceMap.get(workOrder.getWorkOrderId()))
                    .build();
            CapacityEntity capacityEntity = capacityService.getCapacity(capacityGetDTO);
            if (Objects.nonNull(capacityEntity)) {
                // 产能
                uph = Objects.isNull(capacityEntity.getCapacity()) ? null : capacityEntity.getCapacity();
                // 理论人员数 没配置的话,默认为1
                hc = Objects.isNull(capacityEntity.getTheoryStaffNum()) ? 1 : Double.valueOf(capacityEntity.getTheoryStaffNum());
            }
            MaterialEntity tempMaterial = materialCodeEntityMap.getOrDefault(workOrder.getMaterialCode(), new MaterialEntity());

            StatementProductionDTO.WorkOrderEfficiencyVO record = StatementProductionDTO.WorkOrderEfficiencyVO.builder()
                    .dateTime(workOrderDailyProgress.getRecordDate())
                    .workOrderNumber(workOrderDailyProgress.getWorkOrderNumber())
                    .workOrderName(workOrder.getWorkOrderName())
                    .state(workOrder.getState())
                    .materialCode(workOrder.getMaterialCode())
                    .workCenterId(workOrder.getWorkCenterId())
                    .workCenterName(workOrder.getWorkCenterName())
                    .lineId(workOrder.getLineId())
                    .lineName(workOrder.getLineName())
                    .deviceId(WorkCenterTypeEnum.DEVICE.getCode().equals(workOrder.getWorkCenterType())? basicUnitIdsStr: null)
                    .deviceName(WorkCenterTypeEnum.DEVICE.getCode().equals(workOrder.getWorkCenterType())? basicUnitNames: null)
                    .teamId(WorkCenterTypeEnum.TEAM.getCode().equals(workOrder.getWorkCenterType())? basicUnitIdsStr: null)
                    .teamName(WorkCenterTypeEnum.TEAM.getCode().equals(workOrder.getWorkCenterType())? basicUnitNames: null)
                    .planQuantity(workOrder.getPlanQuantity())
                    .finishCount(workOrder.getFinishCount())
                    .unqualified(workOrder.getUnqualified())
                    .todayPlanQuantity(workOrderDailyProgress.getPlanQuantity())
                    .todayFinishCount(workOrderDailyProgress.getProduceQuantity())
                    .build();
            Double finishCount = workOrder.getFinishCount();
            record.setStateName(WorkOrderStateEnum.getNameByCode(workOrder.getState()));
            record.setMaterialName(tempMaterial.getName());
            record.setMaterialStandard(tempMaterial.getStandard());
            record.setUph(uph);
            record.setHc(hc);
            record.setAttendanceNum((long) attendanceNum);
            record.setCompletionRate(MathUtil.divideDouble(finishCount, record.getPlanQuantity(), 4));
            record.setPassRate(qualifiedRate);
            record.setOutputHours(outputHours);
            record.setInputHours(inputHours);
            record.setProductionEfficiency(productionEfficiency);
            record.setUnqualifiedRecordQuantity(unqualifiedRecordQuantity);
            record.setUnqualifiedRecordItemQuantity(unqualifiedRecordItemQuantity);
            rets.add(record);
        }
        Page<StatementProductionDTO.WorkOrderEfficiencyVO> retPage = new Page<>(pageData.getCurrent(), pageData.getSize(), pageData.getTotal());
        retPage.setRecords(rets);
        return retPage;
    }

    private StatementProductionDTO.WorkOrderEfficiencySelectDTO getSelectDTO(StatementProductionDTO.WorkOrderEfficiencySelectDTO selectDTO) {
        // 物料筛选
        if (StringUtils.isNotBlank(selectDTO.getMaterialCode()) || StringUtils.isNotBlank(selectDTO.getMaterialName()) || StringUtils.isNotBlank(selectDTO.getMaterialStandard())) {
            MaterialEntitySelectDTO materialEntitySelect = new MaterialEntitySelectDTO();
            materialEntitySelect.setCode(selectDTO.getMaterialCode());
            materialEntitySelect.setName(selectDTO.getMaterialName());
            materialEntitySelect.setStandard(selectDTO.getMaterialStandard());
            List<String> materialCodes = materialService.materialCodesByMaterialFields(materialEntitySelect);
            if (CollectionUtils.isEmpty(materialCodes)) {
                return null;
            }
            selectDTO.setMaterialCodes(materialCodes);
        }
        // 基本生产单元: 制造单元、班组、设备
        if (StringUtils.isNotBlank(selectDTO.getLineName())) {
            List<Integer> lineIds = productionLineService.lambdaQuery()
                    .select(ProductionLineEntity::getProductionLineId)
                    .like(ProductionLineEntity::getName, selectDTO.getLineName())
                    .list().stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lineIds)) {
                return null;
            }
            selectDTO.setLineIds(lineIds);
        }
        if (StringUtils.isNotBlank(selectDTO.getDeviceName())) {
            List<Integer> deviceIds = deviceService.lambdaQuery()
                    .select(DeviceEntity::getDeviceId)
                    .like(DeviceEntity::getDeviceName, selectDTO.getDeviceName())
                    .list().stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deviceIds)) {
                return null;
            }
            selectDTO.setDeviceIds(deviceIds);
        }
        if (StringUtils.isNotBlank(selectDTO.getTeamName())) {
            List<Integer> teamIds = sysTeamService.lambdaQuery()
                    .select(SysTeamEntity::getId)
                    .like(SysTeamEntity::getTeamName, selectDTO.getTeamName())
                    .list().stream().map(SysTeamEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(teamIds)) {
                return null;
            }
            selectDTO.setTeamIds(teamIds);
        }
        return selectDTO;
    }

    /**
     * 合格率过滤
     */
    private void rateFilter(String rate, LambdaQueryWrapper<MetricsWorkOrderDailyEntity> wrapper, SFunction<MetricsWorkOrderDailyEntity, ?> column) {
        if (StringUtils.isNotEmpty(rate) && Objects.nonNull(CompletionRateEnum.getByType(rate))) {
            switch (CompletionRateEnum.getByType(rate)) {
                // (0%,50%]
                case BETWEEN_ZERO_AND_FIFTY_PERCENT:
                    wrapper.gt(column, 0)
                            .le(column, 0.5);
                    break;
                // (50%,100)
                case BETWEEN_FIFTY_AND_ONE_HUNDRED_PERCENT:
                    wrapper.gt(column, 0.5)
                            .lt(column, 1);
                    break;
                // >=100%
                case GREATER_THAN_OR_EQUAL_TO_ONE_HUNDRED_PERCENT:
                    wrapper.ge(column, 1);
                    break;
                // <=0%或者完成率为空的
                default:
                    wrapper.and(o -> o.isNull(column)
                            .or().le(column, 0));
            }
        }
    }

    @Override
    public Page<SaleOrderTotalStatisticsByMaterialVO> listSaleOrderStatisticsByMaterial(SaleOrderTotalStatisticsSelectDTO selectDTO) {
        return metricsSaleOrderMaterialDailyService.listSaleOrderStatisticsByMaterial(selectDTO);
    }

    @Override
    public Page<SaleOrderTotalStatisticsBySalesmanCustomerVO> listSaleOrderStatisticsBySalesmanCustomer(SaleOrderTotalStatisticsSelectDTO selectDTO) {
        return metricsSaleOrderMaterialDailyService.listSaleOrderStatisticsBySalesmanCustomer(selectDTO);
    }

    @Override
    public Page<SaleOrderTotalStatisticsByCustomerVO> listSaleOrderStatisticsByCustomer(SaleOrderTotalStatisticsSelectDTO selectDTO) {
        return metricsSaleOrderMaterialDailyService.listSaleOrderStatisticsByCustomer(selectDTO);
    }

    @Override
    public Page<WorkOrderTotalStatisticsByUnitMaterialVO> listGroupUnitMaterial(WorkOrderTotalStatisticsSelectDTO params) {
        return metricsWorkOrderDailyService.listGroupUnitMaterial(params);
    }

    @Override
    public Page<WorkOrderTotalStatisticsByMaterialVO> listGroupMaterial(WorkOrderTotalStatisticsSelectDTO params) {
        return metricsWorkOrderDailyService.listGroupMaterial(params);
    }

    @Override
    public Page<WorkOrderTotalStatisticsByCenterMaterialVO> listGroupCenterMaterial(WorkOrderTotalStatisticsSelectDTO params) {
        return metricsWorkOrderDailyService.listGroupCenterMaterial(params);
    }

    @Override
    public Long listSaleOrderStatisticsByMaterialExport(SaleOrderTotalStatisticsSelectDTO selectDTO, String username) {
        DataExportParam<SaleOrderTotalStatisticsSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.SALE_ORDER_STATISTICS_TOTAL_MATERIAL.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.SALE_ORDER_STATISTICS_TOTAL_MATERIAL.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SaleOrderTotalStatisticsSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, SaleOrderStatisticsToatlByMaterialExportHandler.class);
    }

    @Override
    public Long listSaleOrderStatisticsBySalesmanCustomerExport(SaleOrderTotalStatisticsSelectDTO selectDTO, String username) {
        DataExportParam<SaleOrderTotalStatisticsSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.SALE_ORDER_STATISTICS_TOTAL_SALESMEN_CUSTOMER.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.SALE_ORDER_STATISTICS_TOTAL_SALESMEN_CUSTOMER.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SaleOrderTotalStatisticsSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, SaleOrderStatisticsToatlBySalesmanCustomerExportHandler.class);
    }

    @Override
    public Long listSaleOrderStatisticsByCustomerExport(SaleOrderTotalStatisticsSelectDTO selectDTO, String username) {
        DataExportParam<SaleOrderTotalStatisticsSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.SALE_ORDER_STATISTICS_TOTAL_CUSTOMER.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.SALE_ORDER_STATISTICS_TOTAL_CUSTOMER.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SaleOrderTotalStatisticsSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, SaleOrderStatisticsToatlByCustomerExportHandler.class);
    }

    @Override
    public Long listGroupUnitMaterialExport(WorkOrderTotalStatisticsSelectDTO selectDTO, String username) {
        DataExportParam<WorkOrderTotalStatisticsSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.WORK_ORDER_STATISTICS_TOTAL_UNIT_MATERIAL.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.WORK_ORDER_STATISTICS_TOTAL_UNIT_MATERIAL.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(WorkOrderTotalStatisticsSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, WorkOrderStatisticsToatlByUnitMaterialExportHandler.class);
    }

    @Override
    public Long listGroupMaterialExport(WorkOrderTotalStatisticsSelectDTO selectDTO, String username) {
        DataExportParam<WorkOrderTotalStatisticsSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.WORK_ORDER_STATISTICS_TOTAL_MATERIAL.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.WORK_ORDER_STATISTICS_TOTAL_MATERIAL.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(WorkOrderTotalStatisticsSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, WorkOrderStatisticsToatlByMaterialExportHandler.class);
    }

    @Override
    public Long listGroupCenterMaterialExport(WorkOrderTotalStatisticsSelectDTO selectDTO, String username) {
        DataExportParam<WorkOrderTotalStatisticsSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.WORK_ORDER_STATISTICS_TOTAL_CENTER_MATERIAL.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.WORK_ORDER_STATISTICS_TOTAL_CENTER_MATERIAL.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(WorkOrderTotalStatisticsSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, WorkOrderStatisticsToatlByCenterMaterialExportHandler.class);
    }

    @Override
    public Page<StatementProductionDTO.SaleOrderDetailVO> listSaleOrderDetail(StatementProductionDTO.SaleOrderDetailSelectDTO selectDTO) {
        SaleOrderSelectOpenDTO saleOrderSelectDTO = new SaleOrderSelectOpenDTO();
        saleOrderSelectDTO.setCurrent(selectDTO.getCurrent());
        saleOrderSelectDTO.setSize(selectDTO.getSize());
        saleOrderSelectDTO.setShowType(ShowTypeEnum.MATERIAL.getType());
        saleOrderSelectDTO.setSaleOrderNumber(selectDTO.getSaleOrderNumber());
        if (StringUtils.isNotBlank(selectDTO.getSalesmanCode())) {
            saleOrderSelectDTO.setSalesmanCodes(Arrays.stream(selectDTO.getSalesmanCode().split(Constants.SEP)).collect(Collectors.toList()));
        }
        saleOrderSelectDTO.setSalesmanName(selectDTO.getSalesmanName());
        // 生效+当日完成
        saleOrderSelectDTO.setStates(OrderStateEnum.RELEASED.getCode() + SEP + OrderStateEnum.FINISHED.getCode());
        saleOrderSelectDTO.setStates(selectDTO.getStates());
        if (StringUtils.isNotBlank(selectDTO.getCustomerName())) {
            List<String> customerCodes = customerService.lambdaQuery()
                    .like(CustomerEntity::getCustomerName, selectDTO.getCustomerName())
                    .list().stream().map(CustomerEntity::getCustomerCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(customerCodes)) {
                return new Page<>();
            }
            saleOrderSelectDTO.setCustomerCodes(customerCodes);
        }
        if (StringUtils.isNotBlank(selectDTO.getMaterialCode()) || StringUtils.isNotBlank(selectDTO.getMaterialName())) {
            MaterialEntitySelectDTO materialEntitySelect = new MaterialEntitySelectDTO();
            materialEntitySelect.setCode(selectDTO.getMaterialCode());
            materialEntitySelect.setName(selectDTO.getMaterialName());
            List<String> materialCodes = materialService.materialCodesByMaterialFields(materialEntitySelect);
            if (CollectionUtils.isEmpty(materialCodes)) {
                return new Page<>();
            }
            saleOrderSelectDTO.setMaterialCodes(materialCodes);
        }
        // 查询销售订单
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(saleOrderSelectDTO);
        List<SaleOrderVO> saleOrderList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(saleOrderList)) {
            return new Page<>();
        }
        // 查询物料当前库存数量
        List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
        saleOrderList.forEach(o -> materialDetails.add(StockMaterialDetailEntity.builder().productCode(o.getSaleOrderMaterial().getMaterialCode()).skuId(o.getSaleOrderMaterial().getSkuId()).build()));
        Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(stockInventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new com.alibaba.fastjson.TypeReference<Map<String, BigDecimal>>() {
        },null);

        // 销售订单关联的采购需求单（非创建取消状态）
        Set<Integer> saleOrderIdSet = saleOrderList.stream().map(SaleOrderVO::getSaleOrderId).collect(Collectors.toSet());
        Set<String> saleOrderNumberSet = saleOrderList.stream().map(SaleOrderVO::getSaleOrderNumber).collect(Collectors.toSet());
        PurchaseRequestOpenSelectDTO requestOpenSelectDTO = PurchaseRequestOpenSelectDTO.builder()
                .currentPage(1)
                .size(Integer.MAX_VALUE)
                .states(Stream.of(RequestStateEnum.RELEASED.getCode(), RequestStateEnum.FINISHED.getCode(), RequestStateEnum.CLOSED.getCode()).collect(Collectors.toList()))
                .saleOrderIds(new ArrayList<>(saleOrderIdSet))
                .build();
        PageResult<PurchaseRequestEntity> requestInterfacePage = extPurchaseRequestInterface.getPage(requestOpenSelectDTO);
        Map<String, List<PurchaseRequestEntity>> saleOrderNumberPurchaseRequestsMap = new HashMap<>(16);
        for (String saleOrderNum : saleOrderNumberSet) {
            List<PurchaseRequestEntity> list = requestInterfacePage.getRecords().stream()
                    .filter(res -> StringUtils.isNotBlank(res.getOrderNum()) && Arrays.asList(res.getOrderNum().split(Constant.SEP)).contains(saleOrderNum))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)) {
                saleOrderNumberPurchaseRequestsMap.put(saleOrderNum, list);
            }
        }

        // 销售订单物料数量-销售订单关联的出货申请物料数量（出货申请非创建和取消态，且出货时间小于销售订单的要货时间）
        DeliveryApplicationSelectDTO build = DeliveryApplicationSelectDTO.builder()
                .states(Stream.of(DeliveryApplicationStateEnum.RELEASED.getCode(), DeliveryApplicationStateEnum.FINISHED.getCode(), DeliveryApplicationStateEnum.CLOSED.getCode()).map(String::valueOf).collect(Collectors.joining(Constant.SEP)))
                .saleOrderNumbers(new ArrayList<>(saleOrderNumberSet))
                .build();
        Page<DeliveryApplicationEntity> deliveryApplicationPage = deliveryApplicationExtendService.getSimpleList(build);
        if (!CollectionUtils.isEmpty(deliveryApplicationPage.getRecords())) {
            List<Integer> ids = deliveryApplicationPage.getRecords().stream().map(DeliveryApplicationEntity::getId).collect(Collectors.toList());
            Map<Integer, List<DeliveryApplicationMaterialEntity>> idMaterialsMap = applicationMaterialService.lambdaQuery()
                    .in(DeliveryApplicationMaterialEntity::getDeliveryApplicationId, ids)
                    .list().stream().collect(Collectors.groupingBy(DeliveryApplicationMaterialEntity::getDeliveryApplicationId));
            deliveryApplicationPage.getRecords().forEach(res -> res.setMaterialEntities(idMaterialsMap.getOrDefault(res.getId(), new ArrayList<>())));
        }
        Map<String, List<DeliveryApplicationEntity>> saleOrderNumberDeliveryApplicationsMap = deliveryApplicationPage.getRecords().stream().collect(Collectors.groupingBy(DeliveryApplicationEntity::getSaleOrderNumber));

        Date nowDate = new Date();
        Page<StatementProductionDTO.SaleOrderDetailVO> page = new Page<>();
        page.setTotal(pageResult.getTotal());
        page.setCurrent(pageResult.getCurrent());
        page.setSize(pageResult.getSize());
        List<StatementProductionDTO.SaleOrderDetailVO> list = new ArrayList<>();
        for (SaleOrderVO saleOrderVO : saleOrderList) {
            SaleOrderMaterialEntity saleOrderMaterial = saleOrderVO.getSaleOrderMaterial();

            // 采购数量 ：销售订单关联的采购需求单（非创建取消状态）
            List<PurchaseRequestEntity> purchaseRequests = saleOrderNumberPurchaseRequestsMap.getOrDefault(saleOrderVO.getSaleOrderNumber(), new ArrayList<>());
            double purchaseQuantity = 0.0;
            for (PurchaseRequestEntity purchaseRequest : purchaseRequests) {
                double sum = purchaseRequest.getRequestMaterialEntities().stream()
                        .filter(res -> ColumnUtil.getMaterialSku(saleOrderMaterial.getMaterialCode(), saleOrderMaterial.getSkuId()).equals(ColumnUtil.getMaterialSku(res.getCode(), res.getSkuId())))
                        .mapToDouble(PurchaseRequestMaterialEntity::getNum)
                        .sum();
                purchaseQuantity = MathUtil.add(purchaseQuantity, sum);
            }

            // 延期数量: 销售订单物料数量-销售订单关联的出货申请物料数量（出货申请非创建和取消态，且出货时间小于销售订单的要货时间）
            List<DeliveryApplicationEntity> deliveryApplications = saleOrderNumberDeliveryApplicationsMap.getOrDefault(saleOrderVO.getSaleOrderNumber(), new ArrayList<>());
            deliveryApplications = deliveryApplications.stream().filter(res -> res.getPlanDeliveryTime().compareTo(saleOrderMaterial.getRequireGoodsDate()) <= 0).collect(Collectors.toList());
            double deliveryApplicationQuantity = 0.0;
            for (DeliveryApplicationEntity deliveryApplication : deliveryApplications) {
                double sum = deliveryApplication.getMaterialEntities().stream()
                        .filter(res -> ColumnUtil.getMaterialSku(saleOrderMaterial.getMaterialCode(), saleOrderMaterial.getSkuId()).equals(ColumnUtil.getMaterialSku(res.getMaterialCode(), res.getSkuId())))
                        .mapToDouble(DeliveryApplicationMaterialEntity::getAmount)
                        .sum();
                deliveryApplicationQuantity = MathUtil.add(deliveryApplicationQuantity, sum);
            }
            double deferredQuantity = MathUtil.sub(saleOrderMaterial.getSalesQuantity(), deliveryApplicationQuantity);
            if (Objects.nonNull(saleOrderMaterial.getRequireGoodsDate()) && nowDate.compareTo(saleOrderMaterial.getRequireGoodsDate()) < 0) {
                // 如果要当前时间小于要货日期,说明还没延期,直接设置为0
                deferredQuantity = 0;
            }

            // 库存数量
            BigDecimal stockQuantity = Objects.isNull(materialCodeStockMap) ? BigDecimal.ZERO : materialCodeStockMap.getOrDefault(ColumnUtil.getMaterialSku(saleOrderMaterial.getMaterialCode(), saleOrderMaterial.getSkuId()), BigDecimal.ZERO);

            list.add(StatementProductionDTO.SaleOrderDetailVO.builder()
                    .customerName(saleOrderVO.getCustomerName())
                    .saleOrderNumber(saleOrderVO.getSaleOrderNumber())
                    .state(saleOrderVO.getState())
                    .stateName(OrderStateEnum.getNameByCode(saleOrderVO.getState()))
                    .salesmanName(saleOrderVO.getSalesmanName())
                    .materialCode(saleOrderMaterial.getMaterialCode())
                    .materialName(Objects.nonNull(saleOrderMaterial.getMaterialFields()) ? saleOrderMaterial.getMaterialFields().getName() : null)
                    .materialStandard(Objects.nonNull(saleOrderMaterial.getMaterialFields()) ? saleOrderMaterial.getMaterialFields().getStandard() : null)
                    .saleQuantity(saleOrderMaterial.getSalesQuantity())
                    .orderDate(saleOrderMaterial.getOrderDate())
                    .stockQuantity(stockQuantity.doubleValue())
                    .purchaseQuantity(purchaseQuantity)
                    .planProductionQuantity(saleOrderMaterial.getProductionQuantity())
                    .scheduledProductionQuantity(saleOrderMaterial.getScheduledProductionQuantity())
                    .productionQuantity(saleOrderMaterial.getProductionQuantity())
                    .unqualifiedQuantity(saleOrderMaterial.getUnqualifiedQuantity())
                    .appliedShipmentQuantity(saleOrderMaterial.getAppliedShipmentQuantity())
                    .appliedQuantity(saleOrderMaterial.getAppliedQuantity())
                    .notDeliveredQuantity(saleOrderMaterial.getNotDeliveredQuantity())
                    .deferredQuantity(deferredQuantity)
                    .build());
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public Page<StatementProductionDTO.SaleOrderProgressVO> listSaleOrderProgress(StatementProductionDTO.SaleOrderProgressSelectDTO selectDTO) {
        SaleOrderSelectOpenDTO saleOrderSelectDTO = new SaleOrderSelectOpenDTO();
        saleOrderSelectDTO.setCurrent(selectDTO.getCurrent());
        saleOrderSelectDTO.setSize(selectDTO.getSize());
        saleOrderSelectDTO.setShowType(ShowTypeEnum.MATERIAL.getType());

        if (StringUtils.isNotBlank(selectDTO.getSaleOrderNumber())) {
            saleOrderSelectDTO.setSaleOrderNumber(selectDTO.getSaleOrderNumber());
        }
        // 生效+当日完成
        saleOrderSelectDTO.setStates(OrderStateEnum.RELEASED.getCode() + SEP + OrderStateEnum.FINISHED.getCode());
        if (StringUtils.isNotBlank(selectDTO.getStates())) {
            saleOrderSelectDTO.setStates(selectDTO.getStates());
        }
        if (StringUtils.isNotBlank(selectDTO.getCustomerName())) {
            List<String> customerCodes = customerService.lambdaQuery()
                    .like(CustomerEntity::getCustomerName, selectDTO.getCustomerName())
                    .list().stream().map(CustomerEntity::getCustomerCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(customerCodes)) {
                return new Page<>();
            }
            saleOrderSelectDTO.setCustomerCodes(customerCodes);
        }
        if (StringUtils.isNotBlank(selectDTO.getSalesmanCode())) {
            saleOrderSelectDTO.setSalesmanCodes(Arrays.stream(selectDTO.getSalesmanCode().split(Constants.SEP)).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(selectDTO.getSalesmanName())) {
            saleOrderSelectDTO.setSalesmanName(selectDTO.getSalesmanName());
        }
        List<Integer> onlyIncludeWorkOrderIds = null;
        if (StringUtils.isNotBlank(selectDTO.getWorkOrderNumber())) {
            onlyIncludeWorkOrderIds = workOrderService.lambdaQuery()
                    .like(WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber())
                    .list().stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
            Set<Integer> saleOrderIds = null;
            if (!CollectionUtils.isEmpty(onlyIncludeWorkOrderIds)) {
                saleOrderIds = orderWorkOrderService.lambdaQuery()
                        .in(OrderWorkOrderEntity::getWorkOrderId, onlyIncludeWorkOrderIds)
                        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                        .list().stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toSet());
            }
            if (CollectionUtils.isEmpty(saleOrderIds)) {
                return new Page<>();
            }
            saleOrderSelectDTO.setSaleOrderIds(new ArrayList<>(saleOrderIds));
        }
        if (StringUtils.isNotBlank(selectDTO.getMaterialCode())) {
            MaterialEntitySelectDTO materialEntitySelect = new MaterialEntitySelectDTO();
            materialEntitySelect.setCode(selectDTO.getMaterialCode());
            List<String> materialCodes = materialService.materialCodesByMaterialFields(materialEntitySelect);
            if (CollectionUtils.isEmpty(materialCodes)) {
                return new Page<>();
            }
            saleOrderSelectDTO.setMaterialCodes(materialCodes);
        }
        // 查询销售订单
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(saleOrderSelectDTO);
        List<SaleOrderVO> saleOrderList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(saleOrderList)) {
            return new Page<>();
        }

        // 销售订单关联的工单
        Set<Integer> saleOrderIdSet = saleOrderList.stream().map(SaleOrderVO::getSaleOrderId).collect(Collectors.toSet());
        Map<Integer, List<WorkOrderEntity>> saleOrderIdRelateWorkOrderMap = getSaleOrderRelateWorkOrders(saleOrderIdSet, onlyIncludeWorkOrderIds);

        Page<StatementProductionDTO.SaleOrderProgressVO> page = new Page<>();
        page.setTotal(pageResult.getTotal());
        page.setCurrent(pageResult.getCurrent());
        page.setSize(pageResult.getSize());
        List<StatementProductionDTO.SaleOrderProgressVO> list = new ArrayList<>();
        for (SaleOrderVO saleOrderVO : saleOrderList) {
            SaleOrderMaterialEntity saleOrderMaterial = saleOrderVO.getSaleOrderMaterial();

            List<WorkOrderEntity> workOrders = saleOrderIdRelateWorkOrderMap.getOrDefault(saleOrderVO.getSaleOrderId(), new ArrayList<>())
                    .stream().filter(res -> ColumnUtil.getMaterialSku(saleOrderMaterial.getMaterialCode(), saleOrderMaterial.getSkuId()).equals(ColumnUtil.getMaterialSku(res.getMaterialCode(), res.getSkuId())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrders)) {
                // 没有工单的时候销售订单也要展示
                list.add(StatementProductionDTO.SaleOrderProgressVO.builder()
                        .customerName(saleOrderVO.getCustomerName())
                        .saleOrderNumber(saleOrderVO.getSaleOrderNumber())
                        .state(saleOrderVO.getState())
                        .stateName(OrderStateEnum.getNameByCode(saleOrderVO.getState()))
                        .salesmanName(saleOrderVO.getSalesmanName())
                        .materialCode(saleOrderMaterial.getMaterialCode())
                        .build());
            } else {
                for (WorkOrderEntity workOrder : workOrders) {
                    list.add(StatementProductionDTO.SaleOrderProgressVO.builder()
                            .customerName(saleOrderVO.getCustomerName())
                            .saleOrderNumber(saleOrderVO.getSaleOrderNumber())
                            .state(saleOrderVO.getState())
                            .stateName(OrderStateEnum.getNameByCode(saleOrderVO.getState()))
                            .salesmanName(saleOrderVO.getSalesmanName())
                            .materialCode(saleOrderMaterial.getMaterialCode())
                            .workOrderNumber(workOrder.getWorkOrderNumber())
                            .planProductionQuantity(workOrder.getPlanQuantity())
                            .productionQuantity(workOrder.getFinishCount())
                            .unqualifiedQuantity(workOrder.getUnqualified())
                            .inputCount(workOrderService.getInventoryQuantity(workOrder.getWorkOrderNumber(), workOrder.getMaterialCode()))
                            .completionRate(MathUtil.divideDouble(workOrder.getFinishCount(), workOrder.getPlanQuantity(), 4))
                            .passRate(MathUtil.divideDouble(workOrder.getFinishCount(), workOrder.getFinishCount() + workOrder.getUnqualified(), 4))
                            .build());
                }
            }
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public Page<StatementProductionDTO.SaleOrderQualityTraceVO> listSaleOrderQualityTrace(StatementProductionDTO.SaleOrderQualityTraceSelectDTO selectDTO) {
        ProductionDailyDTO.TimeRange timeRange = selectDTO.getTimeRange();

        SaleOrderSelectOpenDTO saleOrderSelectDTO = new SaleOrderSelectOpenDTO();
        saleOrderSelectDTO.setCurrent(selectDTO.getCurrent());
        saleOrderSelectDTO.setSize(selectDTO.getSize());
        saleOrderSelectDTO.setShowType(ShowTypeEnum.MATERIAL.getType());
        saleOrderSelectDTO.setSaleOrderNumber(StringUtils.isNotBlank(selectDTO.getSaleOrderNumber()) ? selectDTO.getSaleOrderNumber() : null);
        // 默认生效+当日完成
        saleOrderSelectDTO.setStates(StringUtils.isNotBlank(selectDTO.getStates()) ? selectDTO.getStates() : OrderStateEnum.RELEASED.getCode() + SEP + OrderStateEnum.FINISHED.getCode());
        List<Integer> onlyIncludeWorkOrderIds = null;
        if (StringUtils.isNotBlank(selectDTO.getWorkOrderNumber())
                || StringUtils.isNotBlank(selectDTO.getWorkCenterName())
                || StringUtils.isNotBlank(selectDTO.getWorkCenterId())
                || StringUtils.isNotBlank(selectDTO.getLineName())) {
            onlyIncludeWorkOrderIds = workOrderService.lambdaQuery()
                    .in(StringUtils.isNotBlank(selectDTO.getWorkCenterId()), WorkOrderEntity::getWorkCenterId, StringUtils.isNotBlank(selectDTO.getWorkCenterId()) ? Arrays.stream(selectDTO.getWorkCenterId().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList()) : null)
                    .like(StringUtils.isNotBlank(selectDTO.getWorkOrderNumber()), WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber())
                    .like(StringUtils.isNotBlank(selectDTO.getWorkCenterName()), WorkOrderEntity::getWorkCenterName, selectDTO.getWorkCenterName())
                    .like(StringUtils.isNotBlank(selectDTO.getLineName()), WorkOrderEntity::getLineName, selectDTO.getLineName())
                    .list().stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
            Set<Integer> saleOrderIds = null;
            if (!CollectionUtils.isEmpty(onlyIncludeWorkOrderIds)) {
                saleOrderIds = orderWorkOrderService.lambdaQuery()
                        .in(OrderWorkOrderEntity::getWorkOrderId, onlyIncludeWorkOrderIds)
                        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                        .list().stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toSet());
            }
            if (CollectionUtils.isEmpty(saleOrderIds)) {
                return new Page<>();
            }
            saleOrderSelectDTO.setSaleOrderIds(new ArrayList<>(saleOrderIds));
        }
        if (StringUtils.isNotBlank(selectDTO.getMaterialCode())) {
            MaterialEntitySelectDTO materialEntitySelect = new MaterialEntitySelectDTO();
            materialEntitySelect.setCode(selectDTO.getMaterialCode());
            List<String> materialCodes = materialService.materialCodesByMaterialFields(materialEntitySelect);
            if (CollectionUtils.isEmpty(materialCodes)) {
                return new Page<>();
            }
            saleOrderSelectDTO.setMaterialCodes(materialCodes);
        }
        // 查询销售订单
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(saleOrderSelectDTO);
        List<SaleOrderVO> saleOrderList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(saleOrderList)) {
            return new Page<>();
        }
        // 销售订单关联的工单
        Set<Integer> saleOrderIdSet = saleOrderList.stream().map(SaleOrderVO::getSaleOrderId).collect(Collectors.toSet());
        Map<Integer, List<WorkOrderEntity>> saleOrderIdRelateWorkOrderMap = getSaleOrderRelateWorkOrders(saleOrderIdSet, onlyIncludeWorkOrderIds);

        Set<String> workOrderNumberSet = new HashSet<>(32);
        saleOrderIdRelateWorkOrderMap.values().forEach(workOrders -> workOrders.forEach(res -> workOrderNumberSet.add(res.getWorkOrderNumber())));
        Map<Date, List<MetricsWorkOrderDailyEntity>> recordDateDailyProgressMap = new HashMap<>(8);
        Map<Date, List<MetricsWorkOrderHourlyEntity>> recordDateHourlyMonitorMap = new HashMap<>(8);
        if (!CollectionUtils.isEmpty(workOrderNumberSet)) {
            // 获取产线开工时间
            recordDateDailyProgressMap = metricsWorkOrderDailyService.lambdaQuery()
                    .in(MetricsWorkOrderDailyEntity::getWorkOrderNumber, workOrderNumberSet)
                    .between(MetricsWorkOrderDailyEntity::getRecordDate, timeRange.getStartTime(), timeRange.getEndTime())
                    .list().stream().collect(Collectors.groupingBy(MetricsWorkOrderDailyEntity::getRecordDate));

            // 获取每个小时的合格率 记录时间(取整点)是小时,所以要筛选出来需要取到yyyy-MM-dd 59:59:59
            recordDateHourlyMonitorMap = metricsWorkOrderHourlyService.lambdaQuery()
                    .in(MetricsWorkOrderHourlyEntity::getWorkOrderNumber, workOrderNumberSet)
                    .between(MetricsWorkOrderHourlyEntity::getRecordTime, timeRange.getStartTime(), DateUtil.addDay(timeRange.getEndTime(), 1))
                    .list().stream().collect(Collectors.groupingBy(res -> DateUtil.formatToDate(res.getRecordTime(), DateUtil.DATETIME_FORMAT_ZERO)));
        }
        // 获取当前首班时间 08:00:00
        String beginTimeStr = dictService.getBeginTimeOfDay();
        Page<StatementProductionDTO.SaleOrderQualityTraceVO> page = new Page<>();
        page.setTotal(pageResult.getTotal());
        page.setCurrent(pageResult.getCurrent());
        page.setSize(page.getSize());
        List<StatementProductionDTO.SaleOrderQualityTraceVO> list = new ArrayList<>();
        // 倒序排
        Date endTime = timeRange.getEndTime();
        double[] passRates = new double[12];
        while (endTime.compareTo(timeRange.getStartTime()) >= 0) {
            Date tempEndTime = endTime;
            endTime = DateUtil.addDay(endTime, -1);

            // 销售订单在这个时间都没创建,所以不用展示每天的
            List<SaleOrderVO> saleOrderRecords = saleOrderList.stream().filter(res -> res.getCreateTime().compareTo(tempEndTime) <= 0).collect(Collectors.toList());
            for (SaleOrderVO saleOrderVO : saleOrderRecords) {
                SaleOrderMaterialEntity saleOrderMaterial = saleOrderVO.getSaleOrderMaterial();


                List<WorkOrderEntity> workOrders = saleOrderIdRelateWorkOrderMap.getOrDefault(saleOrderVO.getSaleOrderId(), new ArrayList<>())
                        .stream().filter(res -> ColumnUtil.getMaterialSku(saleOrderMaterial.getMaterialCode(), saleOrderMaterial.getSkuId()).equals(ColumnUtil.getMaterialSku(res.getMaterialCode(), res.getSkuId())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(workOrders)) {
                    // 没有工单的时候销售订单也要展示
                    list.add(StatementProductionDTO.SaleOrderQualityTraceVO.builder()
                            .dateTime(tempEndTime)
                            .saleOrderNumber(saleOrderVO.getSaleOrderNumber())
                            .state(saleOrderVO.getState())
                            .stateName(OrderStateEnum.getNameByCode(saleOrderVO.getState()))
                            .materialCode(saleOrderMaterial.getMaterialCode())
                            .materialStandard(Objects.nonNull(saleOrderMaterial.getMaterialFields()) ? saleOrderMaterial.getMaterialFields().getStandard() : null)
                            .build());
                } else {
                    // 按工单分组
                    List<MetricsWorkOrderDailyEntity> recordWorkOrderDailyProgress = recordDateDailyProgressMap.getOrDefault(tempEndTime, new ArrayList<>());
                    Map<String, MetricsWorkOrderDailyEntity> workOrderDailyProgressMap = recordWorkOrderDailyProgress.stream().collect(Collectors.toMap(MetricsWorkOrderDailyEntity::getWorkOrderNumber, v -> v, (oldValue, newValue) -> newValue));

                    List<MetricsWorkOrderHourlyEntity> recordWorkOrderHourlyMonitors = recordDateHourlyMonitorMap.getOrDefault(tempEndTime, new ArrayList<>());
                    Map<String, List<MetricsWorkOrderHourlyEntity>> workOrderHourlyMonitorsMap = recordWorkOrderHourlyMonitors.stream().collect(Collectors.groupingBy(MetricsWorkOrderHourlyEntity::getWorkOrderNumber));
                    // 当天首班时间 2023-04-26 08:00:00
                    Date dayBeginDateTime = DateUtil.parse(DateUtil.format(tempEndTime, DateUtil.DATE_FORMAT) + " " + beginTimeStr, DateUtil.DATETIME_FORMAT);

                    for (WorkOrderEntity workOrder : workOrders) {
                        MetricsWorkOrderDailyEntity workOrderDailyProgress = workOrderDailyProgressMap.get(workOrder.getWorkOrderNumber());

                        List<MetricsWorkOrderHourlyEntity> workOrderHourlyMonitors = workOrderHourlyMonitorsMap.getOrDefault(workOrder.getWorkOrderNumber(), new ArrayList<>());
                        // 第一个小时
                        for (int i = 0; i < passRates.length; i++) {
                            Date tempDate = DateUtil.addHour(dayBeginDateTime, i);
                            List<MetricsWorkOrderHourlyEntity> temps = workOrderHourlyMonitors.stream().filter(res -> res.getRecordTime().equals(tempDate)).collect(Collectors.toList());
                            passRates[i] = CollectionUtils.isEmpty(temps) || Objects.isNull(temps.get(0).getQualifiedRate()) ? 0.0 : temps.get(0).getQualifiedRate();
                        }

                        list.add(StatementProductionDTO.SaleOrderQualityTraceVO.builder()
                                .dateTime(tempEndTime)
                                .saleOrderNumber(saleOrderVO.getSaleOrderNumber())
                                .state(saleOrderVO.getState())
                                .stateName(OrderStateEnum.getNameByCode(saleOrderVO.getState()))
                                .workCenterName(workOrder.getWorkCenterName())
                                .lineName(workOrder.getLineName())
                                .materialCode(saleOrderMaterial.getMaterialCode())
                                .materialStandard(Objects.nonNull(saleOrderMaterial.getMaterialFields()) ? saleOrderMaterial.getMaterialFields().getStandard() : null)
                                .workOrderNumber(workOrder.getWorkOrderNumber())
                                .checkNumber(Objects.isNull(workOrderDailyProgress) || Objects.isNull(workOrderDailyProgress.getInspectionQuantity()) ? 0.0 : workOrderDailyProgress.getInspectionQuantity())
                                .outputCount(Objects.isNull(workOrderDailyProgress) || Objects.isNull(workOrderDailyProgress.getProduceQuantity()) ? 0.0 : workOrderDailyProgress.getProduceQuantity())
                                .lineProductionStartTime(Objects.isNull(workOrderDailyProgress) ? null : workOrderDailyProgress.getStartWorkTime())
                                .unqualifiedRecordQuantity(Objects.isNull(workOrderDailyProgress) ? 0 : workOrderDailyProgress.getUnqualifiedRecordQuantity())
                                .unqualifiedRecordItemQuantity(Objects.isNull(workOrderDailyProgress) ? 0 : workOrderDailyProgress.getUnqualifiedRecordItemQuantity())
                                .oneHourPassRate(passRates[0])
                                .twoHourPassRate(passRates[1])
                                .threeHourPassRate(passRates[2])
                                .fourHourPassRate(passRates[3])
                                .fiveHourPassRate(passRates[4])
                                .sixHourPassRate(passRates[5])
                                .sevenHourPassRate(passRates[6])
                                .eightHourPassRate(passRates[7])
                                .nineHourPassRate(passRates[8])
                                .tenHourPassRate(passRates[9])
                                .elevenHourPassRate(passRates[10])
                                .twelveHourPassRate(passRates[11])
                                .build());
                    }
                }
            }
        }
        page.setRecords(list);
        return page;
    }

    /**
     * 查询时间段内工单的每日产量记录（过滤什么数量都没有上报的记录）
     */
    private List<RecordWorkOrderDayCountEntity> listWorkDayCount(Collection<String> workOrderNumbers, Date startTime, Date endTime) {
        // 工单每日产出
        List<RecordWorkOrderDayCountEntity> reportDayCounts = recordWorkOrderDayCountService.lambdaQuery()
                .in(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumbers)
                .between(RecordWorkOrderDayCountEntity::getTime, startTime, endTime)
                .list();
        // 过滤什么都没有上报记录
        reportDayCounts = reportDayCounts.stream()
                .filter(res -> !(res.getCount().equals(0.0) && res.getUnqualified().equals(0.0) && res.getInput().equals(0.0) && res.getRepairQualifiedNumber().equals(0.0)))
                .collect(Collectors.toList());
        return reportDayCounts;
    }

    /**
     * 查询销售订单关联的工单
     *
     * @param onlyIncludeWorkOrderIds 不为空, 则只包含其中的工单
     */
    private Map<Integer, List<WorkOrderEntity>> getSaleOrderRelateWorkOrders(Collection<Integer> saleOrderIds, List<Integer> onlyIncludeWorkOrderIds) {
        // 销售订单关联的工单
        List<OrderWorkOrderEntity> saleOrderRelateWorkOrders = orderWorkOrderService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(onlyIncludeWorkOrderIds), OrderWorkOrderEntity::getWorkOrderId, onlyIncludeWorkOrderIds)
                .in(OrderWorkOrderEntity::getOrderId, saleOrderIds)
                .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                .list();
        Map<Integer, List<WorkOrderEntity>> saleOrderIdRelateWorkOrderMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(saleOrderRelateWorkOrders)) {
            List<Integer> workOrderIds = saleOrderRelateWorkOrders.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
            Map<Integer, WorkOrderEntity> idWorkOrderEntityMap = workOrderService.listByIds(workOrderIds).stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderId, v -> v));
            saleOrderRelateWorkOrders.stream().collect(Collectors.groupingBy(OrderWorkOrderEntity::getOrderId))
                    .forEach((saleOrderId, orderWorkOrders) -> {
                        List<WorkOrderEntity> workOrders = orderWorkOrders.stream().map(res -> idWorkOrderEntityMap.get(res.getWorkOrderId())).filter(Objects::nonNull).collect(Collectors.toList());
                        saleOrderIdRelateWorkOrderMap.put(saleOrderId, workOrders);
                    });
        }
        return saleOrderIdRelateWorkOrderMap;
    }

    @Override
    public void uploadCustomExportTemplate(MultipartFile file, String username) {

    }

    @Override
    public byte[] downloadCustomExportTemplate(Integer templateId) {
        ModelUploadFileEntity uploadFile = modelUploadFileService.getById(templateId);
        return fdfsClientWrapper.download(uploadFile.getFileAddress());
    }


    @Override
    public byte[] getExportTemplate(Integer templateId, Integer code) throws IOException {
        if (null != templateId && templateId != com.yelink.dfscommon.constant.Constant.DEFAULT_TEMPLATE_ID) {
            byte[] bytes = downloadCustomExportTemplate(templateId);
            if (bytes != null) {
                return bytes;
            }
        }
        if (code.equals(ModelUploadFileEnum.DAILY_PRODUCTION_REPORT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/dailyProductionReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.WEEK_PRODUCTION_REPORT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/weekProductionReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.MONTH_PRODUCTION_REPORT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/monthProductionReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.MANAGE_REPORT_PRODUCT_STATISTICS_TOTAL_UNIT_MATERIAL_STATEMENT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/productTotalByUnitMaterialReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.MANAGE_REPORT_PRODUCT_STATISTICS_TOTAL_MATERIAL_STATEMENT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/productTotalByMaterialReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.MANAGE_REPORT_PRODUCT_STATISTICS_TOTAL_CENTER_MATERIAL_STATEMENT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/productTotalByCenterMaterialReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.MANAGE_REPORT_SALE_STATISTICS_TOTAL_MATERIAL_STATEMENT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/saleTotalByMaterialReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.MANAGE_REPORT_SALE_STATISTICS_TOTAL_SALESMAN_CUSTOMER_STATEMENT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/saleTotalBySalesmanCustomerReportDefaultTemplate.xlsx");
        }
        if (code.equals(ModelUploadFileEnum.MANAGE_REPORT_SALE_STATISTICS_TOTAL_CUSTOMER_STATEMENT.getCode())) {
            return downloadDefaultExportTemplate("classpath:template/saleTotalByCustomerReportDefaultTemplate.xlsx");
        }
        return null;
    }

    @Override
    public Page<WorkOrderDayPlanVO> workOrderDayPlan(WorkOrderTodayPlanSelectDTO selectDTO) {
        // 查询在这个时间段内有日计划的工单_生产基本单元信息
        selectDTO.format();
        Page<WorkOrderDayPlanVO> pageData = ((WorkOrderMapper) workOrderService.getBaseMapper()).workOrderDayPlan(selectDTO, new Page<>(selectDTO.getCurrent(), selectDTO.getSize()));
        if (CollectionUtils.isEmpty(pageData.getRecords())) {
            return pageData;
        }
        List<WorkOrderDayPlanVO> vos = pageData.getRecords();
        List<String> workOrderNumbers = vos.stream().map(WorkOrderDayPlanVO::getWorkOrderNumber).collect(Collectors.toList());
        // 查询在这个时间段内的工单计划
        List<WorkOrderPlanEntity> workOrderPlans = workOrderPlanService.lambdaQuery()
                .in(WorkOrderPlanEntity::getWorkOrderNumber, workOrderNumbers)
                .between(WorkOrderPlanEntity::getTime, selectDTO.getTimeRange().getStartTime(), selectDTO.getTimeRange().getEndTime())
                .list();
        // 找出在最小的时间和最大的时间
        Date minDate = workOrderPlans.stream().map(WorkOrderPlanEntity::getTime).min(Date::compareTo).orElseGet(Date::new);
        Date maxDate = workOrderPlans.stream().map(WorkOrderPlanEntity::getTime).max(Date::compareTo).orElseGet(Date::new);

        Map<String, List<WorkOrderPlanEntity>> basicCodePlanMap = workOrderPlans.stream().collect(Collectors.groupingBy(WorkOrderPlanEntity::buildBasicCode));
        // 在这个时间段内工单_基本单元每日生产
        Map<String, WorkOrderBasicUnitDayCountEntity> dayCountMap = workOrderBasicUnitDayCountService.lambdaQuery()
                .in(WorkOrderBasicUnitDayCountEntity::getRecordDate, selectDTO.getTimeRange().getStartTime(), selectDTO.getTimeRange().getEndTime())
                .in(WorkOrderBasicUnitDayCountEntity::getWorkOrderNumber, workOrderNumbers)
                .list()
                .stream().collect(Collectors.toMap(WorkOrderBasicUnitDayCountEntity::getUniCode, Function.identity()));

        for (WorkOrderDayPlanVO vo : vos) {
            vo.setStateName(WorkOrderStateEnum.getNameByCode(vo.getState()));
            List<WorkOrderPlanEntity> plans = basicCodePlanMap.getOrDefault(vo.buildBasicCode(), new ArrayList<>());
            List<WorkOrderDayPlanVO.WorkOrderPlanDTO> workOrderPlanDTOs = plans.stream().map(plan -> {
                WorkOrderBasicUnitDayCountEntity dayCount = dayCountMap.get(plan.buildUniCode());
                return WorkOrderDayPlanVO.WorkOrderPlanDTO.builder()
                        .workOrderNumber(plan.getWorkOrderNumber())
                        .time(plan.getTime())
                        .planQuantity(plan.getPlanQuantity())
                        .finishQuantity(Optional.ofNullable(dayCount).map(WorkOrderBasicUnitDayCountEntity::getCount).orElse(0d))
                        .build();
            }).collect(Collectors.toList());
            vo.setWorkOrderPlans(workOrderPlanDTOs);
        }
        // 排序, 每个工单第一个计划最早的排前面
        vos.sort(Comparator.comparing(o -> o.getWorkOrderPlans().get(0).getTime()));
        // 空的日期填充
        vos.forEach(vo -> {
            Map<Date, WorkOrderDayPlanVO.WorkOrderPlanDTO> dateTimePlanMap = vo.getWorkOrderPlans().stream().collect(Collectors.toMap(WorkOrderDayPlanVO.WorkOrderPlanDTO::getTime, v -> v));

            List<WorkOrderDayPlanVO.WorkOrderPlanDTO> workOrderPlanDTOs = new ArrayList<>();
            for (Date temDate = minDate; temDate.compareTo(maxDate) <= 0; temDate = DateUtil.addDay(temDate, 1)) {
                WorkOrderDayPlanVO.WorkOrderPlanDTO workOrderPlanDTO = dateTimePlanMap.get(temDate);
                if (Objects.isNull(workOrderPlanDTO)) {
                    workOrderPlanDTOs.add(WorkOrderDayPlanVO.WorkOrderPlanDTO.builder().time(temDate).build());
                } else {
                    workOrderPlanDTOs.add(workOrderPlanDTO);
                }
            }
            vo.setWorkOrderPlans(workOrderPlanDTOs);
        });
        return pageData;
    }

    @Override
    public Long exportDailyTask(StatementProductionDTO.SelectDTO selectDTO, String username) {
        DataExportParam<SaleOrderSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.DAILY_PRODUCTION_REPORT.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.DAILY_PRODUCTION_REPORT.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(SaleOrderSelectDTO.class.getName(), selectDTO);
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, ProductionDailyExportHandler.class);
    }

    @Override
    public ExcelTask taskById(BusinessCodeEnum businessCodeEnum,Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(businessCodeEnum.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        return records.get(0);
    }

    @Override
    public IPage<ExcelTask> taskPage(BusinessCodeEnum businessCodeEnum,Integer current, Integer size) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(businessCodeEnum.name());
        return excelService.listPage(excelTask, current, size);
    }

    @Override
    public void uploadListExportTemplate(ModelUploadFileEnum modelUploadFileEnum,MultipartFile file, String username) throws IOException {
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(
                file.getName(),
                file.getOriginalFilename(),
                file.getContentType(),
                EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile, modelUploadFileEnum.getCode(), username);
    }

    @Override
    public Long listSaleOrderDetailExport(StatementProductionDTO.SaleOrderDetailSelectDTO selectDTO, String username) {
        DataExportParam<StatementProductionDTO.SaleOrderDetailSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.SALE_ORDER_DETAIL.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.SALE_ORDER_DETAIL.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(StatementProductionDTO.SaleOrderDetailSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, SaleOrderDetailsExportHandler.class);
    }

    @Override
    public Long listSaleOrderProgressExport(StatementProductionDTO.SaleOrderProgressSelectDTO selectDTO, String username) {
        DataExportParam<StatementProductionDTO.SaleOrderProgressSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.MANAGE_REPORT_SALE_ORDER_PROGRESS.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.MANAGE_REPORT_SALE_ORDER_PROGRESS.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(StatementProductionDTO.SaleOrderProgressSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, SaleOrderProgressExportHandler.class);
    }

    @Override
    public Long listSaleOrderQualityTraceExport(StatementProductionDTO.SaleOrderQualityTraceSelectDTO selectDTO, String username) {
        DataExportParam<StatementProductionDTO.SaleOrderQualityTraceSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.MANAGE_REPORT_SALE_ORDER_QUALITY_TRACE.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.MANAGE_REPORT_SALE_ORDER_QUALITY_TRACE.name());
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(StatementProductionDTO.SaleOrderQualityTraceSelectDTO.class.getName(),selectDTO);
        parameters.put("cleanSheetNames", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, SaleOrderQualityTraceExportHandler.class);
    }

    @Override
    public Long exportTask(ReporterRecordDTO dto, String username) {
        DataExportParam<ReporterRecordDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.REPORTER_RECORD.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.REPORTER_RECORD.name());
        dataExportParam.setCreateUserCode(username);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(ReporterRecordDTO.class.getName(), dto);
        parameters.put("cleanSheetNames", "数据源");
        parameters.put("templateId", dto.getTemplateId());
        parameters.put("reporterRecord", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, ReporterRecordExportHandler.class);
    }

}
