package com.yelink.dfs.service.impl.order;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.supplier.SupplierStateEnum;
import com.yelink.dfs.constant.supplier.SupplierTypeEnum;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderSubcontractEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderSubcontractCountDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSubcontractInsertDTO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.mapper.order.WorkOrderSubcontractMapper;
import com.yelink.dfs.open.v1.supplier.dto.SupplierSelectDTO;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.order.WorkOrderSubcontractService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfscommon.constant.ResponseException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class WorkOrderSubcontractServiceImpl extends ServiceImpl<WorkOrderSubcontractMapper, WorkOrderSubcontractEntity> implements WorkOrderSubcontractService {

    @Resource
    private UserAuthenService userAuthenService;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private SupplierService supplierService;

    @Override
    public void add(WorkOrderSubcontractInsertDTO dto) {
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(dto.getWorkOrderNumber());
        if(workOrder == null) {
            throw new ResponseException("找不到工单号：" + dto.getWorkOrderNumber());
        }
        SupplierEntity supplier = supplierService.getEntityByCode(dto.getSupplierCode());
        if(supplier == null) {
            throw new ResponseException("找不到供应商：" + dto.getSupplierCode());
        }
        if(!Objects.equals(supplier.getState(), SupplierStateEnum.TAKE_EFFECT.getCode())) {
            throw new ResponseException("供应商未生效");
        }
        if(supplier.getType() == null || !supplier.getType().contains(SupplierTypeEnum.OUTSOURCING_SUPPLIER.getName())) {
            throw new ResponseException("供应商不是委外供应商");
        }
        Date now = new Date();
        WorkOrderSubcontractEntity old = lambdaQuery()
                .eq(WorkOrderSubcontractEntity::getWorkOrderNumber, dto.getWorkOrderNumber())
                .eq(WorkOrderSubcontractEntity::getSupplierCode, dto.getSupplierCode())
                .one();
        if(old != null) {
            old.setQuantity(old.getQuantity() + dto.getQuantity());
            old.setUpdateBy(userAuthenService.getUsername());
            old.setUpdateTime(now);
            this.updateById(old);
        }else {
            WorkOrderSubcontractEntity add = WorkOrderSubcontractEntity.builder()
                    .workOrderNumber(workOrder.getWorkOrderNumber())
                    .supplierCode(supplier.getCode())
//                    .supplierName(supplier.getName())
                    .quantity(dto.getQuantity())
                    .createBy(userAuthenService.getUsername())
                    .createTime(now)
                    .build();
            this.save(add);
        }
    }

    @Override
    public void showSubcontract(List<WorkOrderEntity> workOrders) {
        List<String> workOrderNumbers = workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
        if(CollUtil.isEmpty(workOrderNumbers)) {
            return;
        }
        List<WorkOrderSubcontractEntity> allWorkOrderSubcontracts = this.lambdaQuery().in(WorkOrderSubcontractEntity::getWorkOrderNumber, workOrderNumbers).list();
        Map<String, List<WorkOrderSubcontractEntity>> workOrderNumberSubcontractGroup = allWorkOrderSubcontracts.stream().collect(Collectors.groupingBy(WorkOrderSubcontractEntity::getWorkOrderNumber));
        for (WorkOrderEntity workOrder : workOrders) {
            List<WorkOrderSubcontractEntity> workOrderSubcontracts = workOrderNumberSubcontractGroup.get(workOrder.getWorkOrderNumber());
            // 判断工单是否有委外
            workOrder.setIsSubcontract(CollUtil.isNotEmpty(workOrderSubcontracts));
            workOrder.setWorkOrderSubcontracts(workOrderSubcontracts);
        }
    }

    @Override
    public Integer orderCount(WorkOrderSubcontractCountDTO dto) {
        return this.baseMapper.orderCount(dto);
    }

    @Override
    public List<WorkOrderSubcontractEntity> getSupplierByWorkOrderNumber(String workOrderNumber) {
        List<WorkOrderSubcontractEntity> subcontractEntities = this.lambdaQuery().eq(WorkOrderSubcontractEntity::getWorkOrderNumber, workOrderNumber).list();
        Map<String, SupplierEntity> supplierMap = new HashMap<>();
        // 查询供应商相关信息
        List<String> supplierCodes = subcontractEntities.stream().map(WorkOrderSubcontractEntity::getSupplierCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supplierCodes)) {
            Page<SupplierEntity> page = supplierService.supplierOpenPage(SupplierSelectDTO.builder().fullCodes(supplierCodes).build());
            if (!CollectionUtils.isEmpty(page.getRecords())) {
                supplierMap = page.getRecords().stream().collect(Collectors.toMap(SupplierEntity::getCode, o -> o));
            }
        }
        for (WorkOrderSubcontractEntity entity : subcontractEntities) {
            // 设置供应商相关信息
            SupplierEntity supplierEntity = supplierMap.get(entity.getSupplierCode());
            entity.setSupplierName(Objects.isNull(supplierEntity) ? null : supplierEntity.getName());
        }
        return subcontractEntities;
    }
}
