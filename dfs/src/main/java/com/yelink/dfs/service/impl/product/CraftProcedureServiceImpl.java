package com.yelink.dfs.service.impl.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Maps;
import com.yelink.dfs.common.CodeFactory;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.ReportFormConstant;
import com.yelink.dfs.constant.defect.DefectSchemeStatusEnum;
import com.yelink.dfs.constant.maintain.MaintainSchemeStatusEnum;
import com.yelink.dfs.constant.model.TrueFalseEnum;
import com.yelink.dfs.constant.model.VersionModelIdEnum;
import com.yelink.dfs.constant.product.ControlItemEnum;
import com.yelink.dfs.constant.product.CraftProcedureInspectMethodEnum;
import com.yelink.dfs.constant.product.CraftProcedureMaterialDataSourceEnum;
import com.yelink.dfs.constant.product.CraftStateEnum;
import com.yelink.dfs.constant.product.CraftTypeEnum;
import com.yelink.dfs.constant.product.CraftUpdateTypeEnum;
import com.yelink.dfs.constant.product.DataTypeEnum;
import com.yelink.dfs.constant.product.InspectComparatorEnum;
import com.yelink.dfs.constant.product.InspectDefectJudgeEnum;
import com.yelink.dfs.constant.product.InspectItemTypeEnum;
import com.yelink.dfs.constant.product.InspectTriggerConditionEnum;
import com.yelink.dfs.constant.product.ProcedureFlowTypeEnum;
import com.yelink.dfs.constant.product.ProcedureStateEnum;
import com.yelink.dfs.constant.product.ProcessParameterInputModeEnum;
import com.yelink.dfs.constant.product.ProductionStateEnum;
import com.yelink.dfs.constant.product.TimeoutThresholdTypeEnum;
import com.yelink.dfs.constant.supplier.SupplierTypeEnum;
import com.yelink.dfs.entity.ExcelTemplateSetEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.entity.common.VersionChangeCraftProcedureDTO;
import com.yelink.dfs.entity.common.VersionChangeDTO;
import com.yelink.dfs.entity.common.config.dto.CraftDefaultConfigDTO;
import com.yelink.dfs.entity.common.config.dto.CraftRequiredConfDTO;
import com.yelink.dfs.entity.defect.DefectSchemeEntity;
import com.yelink.dfs.entity.device.ProcessAssemblyEntity;
import com.yelink.dfs.entity.maintain.MaintainSchemeEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.dto.WorkCenterSelectDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureInspectResultEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftMaterialRelationEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.CraftProcedureInspectControllerEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureControllerConfigEntity;
import com.yelink.dfs.entity.product.ProcedureDefInspectionConfigEntity;
import com.yelink.dfs.entity.product.ProcedureDefectSchemeEntity;
import com.yelink.dfs.entity.product.ProcedureDeviceTargetReferenceEntity;
import com.yelink.dfs.entity.product.ProcedureDeviceTypeEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.entity.product.ProcedureFileEntity;
import com.yelink.dfs.entity.product.ProcedureInspectionEntity;
import com.yelink.dfs.entity.product.ProcedureMaintainSchemeEntity;
import com.yelink.dfs.entity.product.ProcedureMaterialEntity;
import com.yelink.dfs.entity.product.ProcedureMaterialUsedEntity;
import com.yelink.dfs.entity.product.ProcedurePostEntity;
import com.yelink.dfs.entity.product.ProcedureProcessAssemblyEntity;
import com.yelink.dfs.entity.product.ProcedureProcessParameterEntity;
import com.yelink.dfs.entity.product.ProcedureRelationWorkHoursEntity;
import com.yelink.dfs.entity.product.ProcessParameterConfigEntity;
import com.yelink.dfs.entity.product.dto.CraftBindMaterialDTO;
import com.yelink.dfs.entity.product.dto.CraftFileDTO;
import com.yelink.dfs.entity.product.dto.CraftFileNewDTO;
import com.yelink.dfs.entity.product.dto.CraftImportDTO;
import com.yelink.dfs.entity.product.dto.CraftImportProcedureDTO;
import com.yelink.dfs.entity.product.dto.CraftProcedureControlImportDTO;
import com.yelink.dfs.entity.product.dto.CraftProcedureDeviceParameterImportDTO;
import com.yelink.dfs.entity.product.dto.CraftProcedureFileImportDTO;
import com.yelink.dfs.entity.product.dto.CraftProcedureWorkCenterGroupDTO;
import com.yelink.dfs.entity.product.dto.CraftProcedureWorkOrderDTO;
import com.yelink.dfs.entity.product.dto.CraftRouteDTO;
import com.yelink.dfs.entity.product.dto.ProcedureOneKeyDefaultDTO;
import com.yelink.dfs.entity.product.vo.CraftRouteVO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.supplier.SupplierRelatedTypeEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.event.MaterialHaveReleasedStateCraftEvent;
import com.yelink.dfs.mapper.product.CraftProcedureMapper;
import com.yelink.dfs.open.v1.craft.dto.CraftMaterialDTO;
import com.yelink.dfs.open.v1.craft.dto.CraftProcedureDTO;
import com.yelink.dfs.open.v1.craft.dto.CraftProcedureSelectDTO;
import com.yelink.dfs.open.v1.defect.dto.DefectSchemeSelectDTO;
import com.yelink.dfs.open.v2.craft.dto.CraftProcedureQueryDTO;
import com.yelink.dfs.open.v2.craft.vo.CraftProcedureVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.defect.DefectSchemeService;
import com.yelink.dfs.service.device.ProcessAssemblyService;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureAssembleExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureControlExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureDeviceExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureHumanResourcesExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureInspectControllerExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureInspectExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureMaterialExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureMaterialUsedExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureParameterExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureTempMaterialExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureWorkDurationExcelDTO;
import com.yelink.dfs.service.maintain.MaintainSchemeService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.WorkOrderProcedureInspectResultService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.CraftMaterialRelationService;
import com.yelink.dfs.service.product.CraftProcedureInspectControllerService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureControllerConfigService;
import com.yelink.dfs.service.product.ProcedureDefInspectionConfigService;
import com.yelink.dfs.service.product.ProcedureDefectSchemeService;
import com.yelink.dfs.service.product.ProcedureDeviceTargetReferenceService;
import com.yelink.dfs.service.product.ProcedureDeviceTypeService;
import com.yelink.dfs.service.product.ProcedureFileService;
import com.yelink.dfs.service.product.ProcedureInspectionConfigService;
import com.yelink.dfs.service.product.ProcedureInspectionService;
import com.yelink.dfs.service.product.ProcedureMaintainSchemeService;
import com.yelink.dfs.service.product.ProcedureMaterialService;
import com.yelink.dfs.service.product.ProcedureMaterialUsedService;
import com.yelink.dfs.service.product.ProcedurePostService;
import com.yelink.dfs.service.product.ProcedureProcessAssemblyService;
import com.yelink.dfs.service.product.ProcedureProcessParameterService;
import com.yelink.dfs.service.product.ProcedureRelationWorkHoursService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfs.service.product.ProcessParameterConfigService;
import com.yelink.dfs.service.supplier.SupplierRelatedTypeService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.user.SysPostService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.api.qms.InspectionSchemeInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.OutsourcingProcedureEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.constant.qms.InspectionsSchemeTypeEnum;
import com.yelink.dfscommon.dto.EasyExcelDataDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.StateEnumDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.common.FileModel;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.SysPostEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigEntity;
import com.yelink.dfscommon.entity.qms.QualityInspectionSchemeEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PathUtils;
import com.yelink.inner.service.KafkaWebSocketPublisher;
import com.yelink.notice.constant.TopicEnum;
import com.yelink.notice.entity.MessageContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.formula.BaseFormulaEvaluator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbookType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-05-25 21:17
 */
@Service
@Slf4j
public class CraftProcedureServiceImpl extends ServiceImpl<CraftProcedureMapper, CraftProcedureEntity> implements CraftProcedureService {

    @Autowired
    private ProcedureFileService procedureFileService;
    @Autowired
    private ProcedureMaterialUsedService procedureMaterialUsedService;
    @javax.annotation.Resource
    private ProcedureMaterialService procedureMaterialService;
    @Autowired
    private ProcedureDeviceTypeService procedureDeviceTypeService;
    @Autowired
    private ProcedureService procedureService;
    @Autowired
    private CodeFactory codeFactory;
    @Autowired
    private UploadService uploadService;
    @Lazy
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private CraftService craftService;
    @Lazy
    @javax.annotation.Resource
    private MaterialService materialService;
    @Lazy
    @javax.annotation.Resource
    private ModelService modelService;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private SupplierRelatedTypeService supplierRelatedTypeService;
    @Autowired
    private AppendixService appendixService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private ImportDataRecordService importDataRecordService;
    @Autowired
    private ApproveConfigService approveConfigService;
    @Lazy
    @javax.annotation.Resource
    private ProcedureInspectionConfigService procedureInspectionConfigService;
    @Autowired
    private ProcedureInspectionService procedureInspectionService;
    @Autowired
    private ProcedureControllerConfigService procedureControllerConfigService;
    @Autowired
    private ProductFlowCodeService productFlowCodeService;
    @Autowired
    private FastDfsClientService fastDfsClientService;
    @Autowired
    @Lazy
    private ProductionLineService productionLineService;
    @javax.annotation.Resource
    private ApplicationContext applicationContext;
    @Autowired
    protected ProcedureRelationWorkHoursService procedureRelationWorkHoursService;
    @Autowired
    private ProcedurePostService procedurePostService;
    @Autowired
    private ProcessParameterConfigService processParameterConfigService;
    @Autowired
    private ProcedureProcessParameterService procedureProcessParameterService;
    @Autowired
    private ProcedureProcessAssemblyService procedureProcessAssemblyService;
    @Autowired
    private ProcedureMaintainSchemeService procedureMaintainSchemeService;
    @javax.annotation.Resource
    @Lazy
    private WorkOrderProcedureInspectResultService workOrderProcedureInspectResultService;
    @Autowired
    private ProcedureDefectSchemeService procedureDefectSchemeService;
    @Autowired
    private ProcedureDeviceTargetReferenceService procedureDeviceTargetReferenceService;
    @Autowired
    private SysPostService sysPostService;
    @Autowired
    private WorkCenterService workCenterService;
    @Autowired
    private MessagePushToKafkaService messagePushToKafkaService;
    @Autowired
    private ProcessAssemblyService processAssemblyService;
    @Autowired
    private ProcedureDefInspectionConfigService procedureDefInspectionConfigService;
    @Autowired
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Autowired
    protected KafkaWebSocketPublisher kafkaWebSocketPublisher;
    @Autowired
    private InspectionSchemeInterface inspectionSchemeInterface;
    @Autowired
    private CraftProcedureInspectControllerService craftProcedureInspectControllerService;
    @Autowired
    private DictService dictService;
    @Autowired
    private ImportProgressService importProgressService;
    @Autowired
    private BusinessConfigService businessConfigService;
    @Autowired
    @Lazy
    private DefectSchemeService defectSchemeService;
    @Autowired
    @Lazy
    private MaintainSchemeService maintainSchemeService;
    @Autowired
    private VersionChangeRecordService versionChangeRecordService;
    @Autowired
    private CraftMaterialRelationService craftMaterialRelationService;
    /**
     * 导入文件中工艺工序的唯一标识对应的行：解决同名工艺工序导入时，子项无法匹配的问题
     */
    private static final ThreadLocal<Map<String, CraftProcedureExcelDTO>> IMPORT_UNICODE_MAP = new ThreadLocal<>();
    @Override
    public void updateEntityById(CraftProcedureEntity craftProcedureEntity) {
        //修改前判断是否成环
        if (isLoop(craftProcedureEntity)) {
            throw new ResponseException(RespCodeEnum.CRAFT_PROCEDURE_IS_LOOP);
        }
        fixNodeEdit(craftProcedureEntity);
        //修改部分判空后的字段
        addCraftProcedureField(craftProcedureEntity);
        LambdaUpdateWrapper<CraftProcedureEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CraftProcedureEntity::getId, craftProcedureEntity.getId())
                .set(CraftProcedureEntity::getOutSourcingSupplierCode, craftProcedureEntity.getOutSourcingSupplierCode())
                .set(CraftProcedureEntity::getOutSourcingSupplier, craftProcedureEntity.getOutSourcingSupplier())
                .set(CraftProcedureEntity::getLineModelId, craftProcedureEntity.getLineModelId())
                .set(CraftProcedureEntity::getLineModelName, craftProcedureEntity.getLineModelName());
        this.update(updateWrapper);
        // 删除
        List<ProcedureFileEntity> list = procedureFileService.getEntityByProcedureIdAndCraftId(craftProcedureEntity.getId());
        // 删除fastdfs的文件
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(procedureFileEntity -> {
                //标记上传文件
                uploadService.markUploadFile(procedureFileEntity.getFileUrl(), null, procedureFileEntity);
            });
            // 删除工序关联的附件
            procedureFileService.removeByIds(list.stream().map(ProcedureFileEntity::getId).collect(Collectors.toList()));
        }
        // 新增工序关联的附件信息
        if (!CollectionUtils.isEmpty(craftProcedureEntity.getFileList())) {
            craftProcedureEntity.getFileList().forEach(procedureFileEntity -> {
                procedureFileEntity.setId(null);
                procedureFileEntity.setProcedureId(craftProcedureEntity.getId());
                procedureFileEntity.setCraftId(craftProcedureEntity.getCraftId());
                procedureFileEntity.setCreateBy(craftProcedureEntity.getUpdateBy());
                if (StringUtils.isBlank(procedureFileEntity.getEditor())) {
                    procedureFileEntity.setEditor(craftProcedureEntity.getUpdateBy());
                    procedureFileEntity.setEditorName(sysUserService.getNicknameByUsername(craftProcedureEntity.getUpdateBy()));
                }
                procedureFileEntity.setCreateTime(craftProcedureEntity.getUpdateTime());
                procedureFileService.save(procedureFileEntity);
                //标记上传文件
                uploadService.markUploadFile(procedureFileEntity.getFileUrl(), procedureFileEntity);
            });
        }
        //工序附件的修改
        LambdaUpdateWrapper<AppendixEntity> wrapper1 = new LambdaUpdateWrapper<>();
        wrapper1.eq(AppendixEntity::getRelateId, craftProcedureEntity.getId());
        List<AppendixEntity> appendixEntityList = appendixService.list(wrapper1);
        // 删除fastdfs的文件
        if (!CollectionUtils.isEmpty(appendixEntityList)) {
            appendixEntityList.forEach(appendixEntity -> {
                //标记上传文件
                uploadService.markUploadFile(appendixEntity.getFilePath(), null, appendixEntity);
            });
            // 删除工序关联的附件
            appendixService.remove(wrapper1);
        }
        // 新增工序关联的附件信息
        if (!CollectionUtils.isEmpty(craftProcedureEntity.getAppendixEntities())) {
            craftProcedureEntity.getAppendixEntities().forEach(appendixEntity -> {
                appendixEntity.setRelateId(craftProcedureEntity.getId().toString());
                appendixEntity.setIsUsed(true);
                appendixEntity.setCreateUser(craftProcedureEntity.getCreateBy());
                appendixEntity.setCreateTime(new Date());
                appendixEntity.setRelateName(craftProcedureEntity.getProcedureName());
                appendixEntity.setType(AppendixTypeEnum.PROCEDURE_APPENDIX.getCode());
                if (StringUtils.isBlank(appendixEntity.getEditor())) {
                    appendixEntity.setEditor(craftProcedureEntity.getCreateBy());
                    appendixEntity.setEditorName(sysUserService.getNicknameByUsername(craftProcedureEntity.getCreateBy()));
                }
                // 附件上传标记地址
                uploadService.markUploadFile(appendixEntity.getFilePath(), appendixEntity);
            });
        }
        setWorkCenter(craftProcedureEntity);
        // 更新工序
        this.updateById(craftProcedureEntity);
        this.deleteScannerRedis(craftProcedureEntity);
        //发送kafka消息
        messagePushToKafkaService.pushNewMessage(craftProcedureEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CRAFT_PROCEDURE_UPDATE_MESSAGE);
        kafkaWebSocketPublisher.sendMessage(TopicEnum.CRAFT_PROCEDURE_TOPIC.getTopic(), MessageContent.builder()
                .time(new Date())
                .message(String.format("工艺工序更新: craftId:%s - procedureName:%s, 请知悉", craftProcedureEntity.getCraftId(), craftProcedureEntity.getProcedureName()))
                .build());
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("路线 - 更新")
                .craftProcedureId(craftProcedureEntity.getId())
                .build()
        );
    }


    /**
     * 校验是否形成闭环
     * 查询该工艺下所有工序，遍历工序，从该工序往前检查是否循环引用
     *
     * @param craftProcedureEntity
     * @return
     */
    private boolean isLoop(CraftProcedureEntity craftProcedureEntity) {
        //查询该工艺id下所有的工序
        List<CraftProcedureEntity> craftProcedureEntities = this.lambdaQuery()
                .select(CraftProcedureEntity::getId, CraftProcedureEntity::getSupProcedureId)
                .eq(CraftProcedureEntity::getCraftId, craftProcedureEntity.getCraftId())
                .list();
        //用修改后的工序覆盖数据库原有的工序后再做判断(放最后，转map的时候替换原来的)
        craftProcedureEntities.add(craftProcedureEntity);
        return checkLoop(craftProcedureEntities);
    }

    private boolean checkLoop(List<CraftProcedureEntity> procedureList) {
        if (procedureList == null || procedureList.isEmpty()) {
            return false;
        }
        // 收集所有存在的ID
        Set<Integer> existingIds = procedureList.stream()
                .map(CraftProcedureEntity::getId)
                .collect(Collectors.toSet());

        // 构建邻接表和访问状态表
        Map<Integer, List<Integer>> graph = new HashMap<>();
        Map<Integer, Integer> visited = new HashMap<>(); // 0:未访问, 1:访问中, 2:已访问

        for (CraftProcedureEntity entity : procedureList) {
            Integer id = entity.getId();
            String supIds = entity.getSupProcedureId() != null ? entity.getSupProcedureId() : "";
            List<Integer> supList = Arrays.stream(supIds.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(s -> {
                        try {
                            return Integer.parseInt(s);
                        } catch (NumberFormatException e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .filter(existingIds::contains)
                    .collect(Collectors.toList());
            graph.put(id, supList);
            visited.put(id, 0); // 初始状态为未访问
        }

        // 对每个未访问的节点进行DFS
        for (Integer node : graph.keySet()) {
            if (visited.get(node) == 0) {
                if (dfs(node, graph, visited)) {
                    return true;
                }
            }
        }
        return false;
    }
    private static boolean dfs(Integer node, Map<Integer, List<Integer>> graph, Map<Integer, Integer> visited) {
        visited.put(node, 1); // 标记为访问中
        for (Integer neighbor : graph.get(node)) {
            if (visited.get(neighbor) == 1) {
                // 发现环
                return true;
            } else if (visited.get(neighbor) == 0) {
                if (dfs(neighbor, graph, visited)) {
                    return true;
                }
            }
        }
        visited.put(node, 2); // 标记为已访问
        return false;
    }
    @Override
    public List<CraftProcedureEntity> listByCraftId(Integer craftId) {
        return this.lambdaQuery()
                .eq(CraftProcedureEntity::getCraftId, craftId)
                .list();
    }

    /**
     * 如果类型为line则自动补全工作中心
     *
     * @param craftProcedureEntity
     */
    private void setWorkCenter(CraftProcedureEntity craftProcedureEntity) {
        //如果类型为line则自动补全工作中心
        if (WorkCenterTypeEnum.LINE.getCode().equals(craftProcedureEntity.getType())) {
            WorkCenterSelectDTO selectDTO = WorkCenterSelectDTO.builder().type(WorkCenterTypeEnum.LINE.getCode())
                    .lineModelId(String.valueOf(craftProcedureEntity.getLineModelId())).build();
            List<WorkCenterEntity> workCenterEntityList = workCenterService.list(selectDTO).getRecords();
            //如果工作中心为空则报错
            if (CollectionUtils.isEmpty(workCenterEntityList)) {
                throw new ResponseException(craftProcedureEntity.getLineModelName() + "制造单元类型暂未绑定工作中心，无法添加");
            }
            // 不能覆盖原有的数据
            if (StringUtils.isBlank(craftProcedureEntity.getWorkCenterIds())) {
                craftProcedureEntity.setWorkCenterIds(workCenterEntityList.stream().map(WorkCenterEntity::getId).map(String::valueOf).collect(Collectors.joining(Constant.SEP)));
            }
            if (StringUtils.isBlank(craftProcedureEntity.getWorkCenterNames())) {
                craftProcedureEntity.setWorkCenterNames(workCenterEntityList.stream().map(WorkCenterEntity::getName).collect(Collectors.joining(Constant.SEP)));
            }
        }
    }

    /**
     * 当前工序前驱工序被删除，将删除的工序的后驱删除
     *
     * @param s
     * @param craftProcedureEntity
     */
    private void deleteNextProcedureEntity(String s, CraftProcedureEntity craftProcedureEntity) {
        CraftProcedureEntity one = this.getById(s);
        if (Objects.nonNull(one)) {
            String nextProcedureId = one.getNextProcedureId();
            String nextProcedureName = one.getNextProcedureName();
            List<String> nextProcedureNames = new ArrayList<>(Arrays.asList(nextProcedureName.split(",")));
            List<String> nextProcedureIds = new ArrayList<>(Arrays.asList(nextProcedureId.split(",")));
            nextProcedureIds.remove(String.valueOf(craftProcedureEntity.getId()));
            nextProcedureNames.remove(craftProcedureEntity.getProcedureName());
            String procedureId = StringUtils.join(nextProcedureIds, ",");
            String procedureName = StringUtils.join(nextProcedureNames, ",");
            one.setNextProcedureName(procedureName);
            one.setNextProcedureId(procedureId);
            this.updateById(one);
        }

    }

    /**
     * 当前工序新增前驱工序，将前驱工序的后续加上当前工序
     *
     * @param s
     * @param craftProcedureEntity
     */
    private void addNextProcedureEntity(String s, CraftProcedureEntity craftProcedureEntity) {
        CraftProcedureEntity one = this.getById(s);
        if (one != null) {
            String nextProcedureId = one.getNextProcedureId();
            String nextProcedureName = one.getNextProcedureName();
            if (!(nextProcedureId == null) && nextProcedureId.length() != 0) {
                //进行批量操作时，避免重复插入直接工序后继
                String[] split = nextProcedureId.split(",");
                if (!Arrays.asList(split).contains(String.valueOf(craftProcedureEntity.getId()))) {
                    nextProcedureId = nextProcedureId + ',' + craftProcedureEntity.getId();
                    nextProcedureName = nextProcedureName + "," + craftProcedureEntity.getProcedureName();
                    one.setNextProcedureId(nextProcedureId);
                    one.setNextProcedureName(nextProcedureName);
                }
            } else {
                one.setNextProcedureId(String.valueOf(craftProcedureEntity.getId()));
                one.setNextProcedureName(craftProcedureEntity.getProcedureName());
            }
            this.updateById(one);
            //发送kafka消息
            messagePushToKafkaService.pushNewMessage(one, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CRAFT_PROCEDURE_UPDATE_MESSAGE);
        }

    }

    /**
     * 比较两个工艺路线
     *
     * @param oldCraftId
     * @param newCraftId
     * @return
     */
    @Override
    public CraftRouteDTO compareCraftRoute(Integer oldCraftId, Integer newCraftId) {
        Collection<CraftRouteVO> odlCraftRoutes = getCraftRoute(oldCraftId);
        Collection<CraftRouteVO> newCraftRoutes = getCraftRoute(newCraftId);
        //存储旧流程工序id
        ArrayList<Integer> oldList = new ArrayList<>();
        //存储新流程工序id
        ArrayList<Integer> newList = new ArrayList<>();
        //存放新增的节点ID
        ArrayList<Integer> addList = new ArrayList<>();
        //存放删除数据id
        ArrayList<Integer> deleteList = new ArrayList<>();
        //存放变更的数据
        ArrayList<Integer> updateList = new ArrayList<>();
        odlCraftRoutes.forEach(odlCraftRoute -> {
            oldList.add(odlCraftRoute.getCraftId());
        });
        newCraftRoutes.forEach(newCraftRoute -> {
            newList.add(newCraftRoute.getCraftId());
        });
        for (Integer integer : newList) {
            if (!oldList.contains(integer)) {
                addList.add(integer);
            }
        }
        for (Integer integer : oldList) {
            if (!newList.contains(integer)) {
                deleteList.add(integer);
            }
        }
        //删除新工艺新增的节点，删除旧工艺中新工艺删除的节点
        oldList.removeAll(deleteList);
        newList.removeAll(addList);
        //遍历查询流程工序顺序是否改变
        for (int i = 0; i < oldList.size(); i++) {
            if (!oldList.get(i).equals(addList.get(i))) {
                updateList.add(oldList.get(i));
            }
        }
        //改变标识状态删除
        for (Integer integer : deleteList) {
            for (CraftRouteVO craftRouteVO : odlCraftRoutes) {
                if (integer.equals(craftRouteVO.getCraftId())) {
                    craftRouteVO.setUpdateState(2);
                }
            }
        }
        //新增
        for (Integer integer : addList) {
            for (CraftRouteVO craftRouteVO : newCraftRoutes) {
                if (integer.equals(craftRouteVO.getCraftId())) {
                    craftRouteVO.setUpdateState(1);
                }
            }
        }
        //修改
        for (Integer integer : updateList) {
            for (CraftRouteVO craftRouteVO : newCraftRoutes) {
                if (integer.equals(craftRouteVO.getCraftId())) {
                    craftRouteVO.setUpdateState(3);
                }
            }
        }
        List<CraftProcedureEntity> oldCraftRouteTree = getCraftTree(oldCraftId);
        List<CraftProcedureEntity> newCraftRouteTree = getCraftTree(newCraftId);
        return CraftRouteDTO.builder().data(odlCraftRoutes).tree(oldCraftRouteTree).newData(newCraftRoutes).newTree(newCraftRouteTree).build();
    }

    @Override
    public void sortNextProcedure(Integer craftId) {
        if (craftId == 0) {
            List<CraftProcedureEntity> lists = this.list();
            HashSet<Integer> hashSets = new HashSet<>();
            lists.forEach(list -> {
                hashSets.add(list.getCraftId());
            });
            hashSets.forEach(hashSet -> {
                sortSingleNextProcedure(hashSet);
            });
        } else {
            sortSingleNextProcedure(craftId);
        }


    }

    /**
     * 复制工艺工序数据
     *
     * @param craftProcedureEntity
     */
    @Override
    public CraftProcedureEntity copyCraftProcedure(Integer newCraftId, CraftProcedureEntity craftProcedureEntity) {
        //工艺ID+工序ID+工序名称确定唯一值
        CraftProcedureEntity newEntity = this.lambdaQuery().eq(CraftProcedureEntity::getCraftId, newCraftId).eq(CraftProcedureEntity::getProcedureId, craftProcedureEntity.getProcedureId())
                .eq(CraftProcedureEntity::getProcedureName, craftProcedureEntity.getProcedureName()).one();
        String supProcedureName = newEntity.getSupProcedureName();
        ArrayList<String> supProcedureList = new ArrayList<>();
        //上级工序ID特殊处理
        if (StringUtils.isNotBlank(supProcedureName)) {
            String[] split = supProcedureName.split(Constants.SEP);
            StringBuilder sb = new StringBuilder();
            for (String supName : split) {
                LambdaQueryWrapper<CraftProcedureEntity> cp = new LambdaQueryWrapper<>();
                cp.eq(CraftProcedureEntity::getCraftId, newCraftId);
                cp.eq(CraftProcedureEntity::getProcedureName, supName);
                CraftProcedureEntity one = this.getOne(cp);
                sb.append(one.getId());
                sb.append(Constants.SEP);
                supProcedureList.add(String.valueOf(one.getId()));
            }
            // 去除最后的“,”
            String newSupProcedureId = sb.substring(0, sb.length() - 1);
            newEntity.setSupProcedureId(newSupProcedureId);
        }
        this.updateById(newEntity);
        //发送kafka消息
        messagePushToKafkaService.pushNewMessage(newEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CRAFT_PROCEDURE_ADD_MESSAGE);
        //新增直接后继插入
        for (String s : supProcedureList) {
            addNextProcedureEntity(s, newEntity);
        }
        return newEntity;
    }

    @Override
    public List<CraftProcedureEntity> sortCraftProceduresLine(List<CraftProcedureEntity> craftProcedures) {
        // 工艺工序如果是一条直线,保证一条线顺序正确
        if (judgeCraftProceduresIsBeelineProcess(craftProcedures)) {
            craftProcedures = sortCraftProcedures(craftProcedures);
        }
        return craftProcedures;
    }

    @Override
    public List<CraftProcedureEntity> listCraftProcedureByMaterialCode(String materialCode) {
        // 获取该物料的工艺
        CraftEntity craftEntity = craftService.getCraftByMaterialCode(materialCode);
        List<CraftProcedureEntity> craftProcedures = new ArrayList<>();
        if (craftEntity != null) {
            craftProcedures = this.listByCraftId(craftEntity.getCraftId());
        }
        craftProcedures = sortCraftProceduresLine(craftProcedures);
        return craftProcedures;
    }

    /**
     * 判断工艺工序是否是一条直线连续,不存分支
     */
    private boolean judgeCraftProceduresIsBeelineProcess(List<CraftProcedureEntity> craftProcedureList) {
        if (CollectionUtils.isEmpty(craftProcedureList)) {
            return false;
        }
        // 判断工艺工序是否以 一个工序开头、一个工序结尾
        // 第一个工序
        List<CraftProcedureEntity> firstCraftProcedures = craftProcedureList.stream().filter(res -> StringUtils.isEmpty(res.getSupProcedureId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(firstCraftProcedures) || firstCraftProcedures.size() > 1) {
            // 工艺工序的第一个工序有多个
            return false;
        }
        // 最后那个工序
        List<CraftProcedureEntity> lastCraftProcedures = craftProcedureList.stream().filter(res -> StringUtils.isEmpty(res.getNextProcedureId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lastCraftProcedures) || lastCraftProcedures.size() > 1) {
            // 工艺工序的最后一个工序有多个
            return false;
        }

        // 如果存在下一个工序有多个的情况,则说明有分支
        for (CraftProcedureEntity craftProcedure : craftProcedureList) {
            if (StringUtils.isNotEmpty(craftProcedure.getNextProcedureId())) {
                String[] procedureIds = craftProcedure.getNextProcedureId().split(Constant.SEP);
                if (procedureIds.length > 1) {
                    // 如果存在下一个工序有多个的情况,则说明有分支
                    return false;
                }
            }
        }
        // 工艺工序排成一条直线
        List<CraftProcedureEntity> sortCraftProcedures = sortCraftProcedures(craftProcedureList);
        // 如果从头到尾排序后,还有多余的工序,则说明有直线以外的节点
        return sortCraftProcedures.size() == craftProcedureList.size();
    }

    /**
     * 工艺工序排成一条直线
     * 调用这个方法之前请：判断工艺工序是否是一条直线连续,不存分支
     */
    private List<CraftProcedureEntity> sortCraftProcedures(List<CraftProcedureEntity> craftProcedureList) {
        List<CraftProcedureEntity> firstCraftProcedures = craftProcedureList.stream().filter(res -> StringUtils.isEmpty(res.getSupProcedureId())).collect(Collectors.toList());
        // 第一个工序
        CraftProcedureEntity firstCraftProcedure = firstCraftProcedures.get(0);

        // 如果从头到尾排序后,还有多余的工序,则说明有直线以外的节点
        Map<Integer, CraftProcedureEntity> idCraftProcedureMap = craftProcedureList.stream()
                .collect(Collectors.toMap(CraftProcedureEntity::getId, v -> v));
        // 排序工序,得到一个连续的工艺工序
        List<CraftProcedureEntity> sortCraftProcedures = new ArrayList<>();
        sortCraftProcedures.add(firstCraftProcedure);
        CraftProcedureEntity temp = firstCraftProcedure;
        while (temp != null && StringUtils.isNotEmpty(temp.getNextProcedureId())) {
            CraftProcedureEntity craftProcedureEntity = idCraftProcedureMap.get(Integer.valueOf(temp.getNextProcedureId()));
            if (Objects.nonNull(craftProcedureEntity)) {
                sortCraftProcedures.add(craftProcedureEntity);
            }
            temp = craftProcedureEntity;
        }
        return sortCraftProcedures;
    }

    /**
     * 根据id修改该工艺对呀工序路线的直接后继
     *
     * @param craftId
     */
    private void sortSingleNextProcedure(Integer craftId) {
        List<CraftProcedureEntity> lists = this.listByCraftId(craftId);
        lists.forEach(list -> {
            String supProcedureId = list.getSupProcedureId();
            if (!(supProcedureId == null) && supProcedureId.length() != 0) {
                String[] split = supProcedureId.split(",");
                for (String s : split) {
                    addNextProcedureEntity(s, list);
                }
            }
        });
    }

    @Override
    public List<CraftProcedureEntity> getProcedureList(Integer craftId) {
        // 默认按id顺序排序
        List<CraftProcedureEntity> list = this.lambdaQuery()
                .eq(CraftProcedureEntity::getCraftId, craftId)
                .orderByAsc(CraftProcedureEntity::getId)
                .list();
        if (!CollectionUtils.isEmpty(list)) {
            showName(list);
            return CraftProcedureEntity.sortNearProcedures(list);
        }
        return list;
    }

    @Override
    public void showName(List<CraftProcedureEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //添加部分字段，做过判空
        list.forEach(this::addCraftProcedureField);
        // 设置关联工序标识
        List<Integer> craftProcedureIds = list.stream().map(CraftProcedureEntity::getId).collect(Collectors.toList());
        Map<Integer, ProcedureControllerConfigEntity> configMap = procedureControllerConfigService.getMapByCraftProcedureIds(craftProcedureIds);
        for (CraftProcedureEntity craftProcedureEntity : list) {
            ProcedureControllerConfigEntity configEntity = configMap.get(craftProcedureEntity.getId());
            craftProcedureEntity.setJumpStationCheck(false);
            if (configEntity != null) {
                craftProcedureEntity.setJumpStationCheck(configEntity.getJumpStationCheck());
                // 添加换算系数
                craftProcedureEntity.setConversionFactor(configEntity.getConversionFactor());
            }
        }
        // 展示工序编码
        showProcedureCode(list);
    }

    /**
     * 展示工序编码
     *
     * @param list
     */
    private void showProcedureCode(List<CraftProcedureEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<Integer> procedureIds = list.stream().map(CraftProcedureEntity::getProcedureId).collect(Collectors.toSet());
        Map<Integer, String> procedureCodeMap = procedureService.lambdaQuery().in(ProcedureEntity::getProcedureId, procedureIds).list()
                .stream().collect(Collectors.toMap(ProcedureEntity::getProcedureId, ProcedureEntity::getProcedureCode));
        for (CraftProcedureEntity craftProcedureEntity : list) {
            craftProcedureEntity.setProcedureCode(procedureCodeMap.get(craftProcedureEntity.getProcedureId()));
        }
    }


    @Override
    public List<CraftProcedureWorkCenterGroupDTO> getProceduresGroupByWorkCenter(String craftCode) {
        if (StringUtils.isBlank(craftCode)) {
            return new ArrayList<>();
        }
        CraftEntity craftEntity = craftService.lambdaQuery()
                // 注释原因，工单编辑会通过工艺编码获取工艺，如果关联的工艺不是生效态，就会找不到报空指针
//                .eq(CraftEntity::getState, CraftStateEnum.RELEASED.getCode())
                .eq(CraftEntity::getCraftCode, craftCode).one();

        List<CraftProcedureEntity> craftProcedureEntities = this.lambdaQuery().eq(CraftProcedureEntity::getCraftId, craftEntity.getCraftId()).list();
        //排序
        craftProcedureEntities = sortCraftProceduresLine(craftProcedureEntities);
        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
            return new ArrayList<>();
        }
        // 相同工作中心为一组，和是不是相邻的无关
        List<CraftProcedureWorkCenterGroupDTO> dtos = new ArrayList<>();
        Map<String, List<CraftProcedureEntity>> map = craftProcedureEntities.stream().collect(Collectors.groupingBy(CraftProcedureEntity::getWorkCenterIds));
        for (Map.Entry<String, List<CraftProcedureEntity>> entry : map.entrySet()) {
            List<CraftProcedureEntity> groups = entry.getValue();
            CraftProcedureWorkCenterGroupDTO tempDto = CraftProcedureWorkCenterGroupDTO.builder()
                    .workCenterNames(groups.get(0).getWorkCenterNames())
                    .workCenterIds(groups.get(0).getWorkCenterIds())
                    .groups(groups)
                    .build();
            dtos.add(tempDto);
        }
        return dtos;
    }

    @Override
    public CraftProcedureEntity getProcedureConfig(Integer craftProcedureId) {
        // 获取工艺路线-工序的详细信息
        CraftProcedureEntity craftProcedureEntity = this.lambdaQuery().eq(CraftProcedureEntity::getId, craftProcedureId).one();
        if (craftProcedureEntity != null) {
            craftProcedureEntity.setCraftCode(craftService.getById(craftProcedureEntity.getCraftId()).getCraftCode());
            // 获取用料信息
            List<ProcedureMaterialUsedEntity> procedureMaterialUsedEntities = procedureMaterialUsedService.lambdaQuery().eq(ProcedureMaterialUsedEntity::getProcedureId, craftProcedureId).list();
            craftProcedureEntity.setProcedureMaterialUsedList(procedureMaterialUsedEntities);
            // 获取工序物料信息
            List<ProcedureMaterialEntity> procedureMaterialEntities = procedureMaterialService.lambdaQuery().eq(ProcedureMaterialEntity::getProcedureId, craftProcedureId).list();
            craftProcedureEntity.setProcedureMaterialList(procedureMaterialEntities);
            // 获取设备类型、工序工时信息
            List<ProcedureDeviceTypeEntity> procedureDeviceTypeEntities = procedureDeviceTypeService.lambdaQuery().eq(ProcedureDeviceTypeEntity::getProcedureId, craftProcedureId).list();
            craftProcedureEntity.setDeviceTypeList(procedureDeviceTypeEntities);
            //获取工序工时信息(标准调试工时等3个)
            List<ProcedureRelationWorkHoursEntity> procedureRelationWorkHoursEntities = procedureRelationWorkHoursService.lambdaQuery().eq(ProcedureRelationWorkHoursEntity::getProcedureId, craftProcedureId).list();
            procedureRelationWorkHoursEntities.forEach(o -> o.setProcedureName(craftProcedureEntity.getProcedureName()));
            craftProcedureEntity.setProcedureWorkHoursList(procedureRelationWorkHoursEntities);
            // 获取附件信息
            List<ProcedureFileEntity> procedureFileEntities = procedureFileService.getEntityByProcedureIdAndCraftId(craftProcedureId);
            procedureFileEntities.forEach(entity -> entity.setCreateUserName(sysUserService.getNicknameByUsername(entity.getCreateBy())));
            craftProcedureEntity.setFileList(procedureFileEntities);
            //获取附件信息
            List<AppendixEntity> listAppendix = appendixService.lambdaQuery().eq(AppendixEntity::getRelateId, craftProcedureId).list();
            listAppendix.forEach(appendixEntity -> appendixEntity.setCreateUserName(StringUtils.isNotBlank(appendixEntity.getCreateUser()) ? sysUserService.getNicknameByUsername(appendixEntity.getCreateUser()) : null));
            craftProcedureEntity.setAppendixEntities(listAppendix);
            //获取工序检验配置
            List<ProcedureInspectionConfigEntity> list = procedureInspectionConfigService.getProcedureInspectionConfigEntities(craftProcedureId);
            craftProcedureEntity.setProcedureInspectionConfigEntityList(list);
            //获取工序装配数据列表
            List<ProcedureProcessAssemblyEntity> procedureProcessAssemblyEntities = procedureProcessAssemblyService.getListById(craftProcedureId);
            craftProcedureEntity.setProcedureProcessAssemblyEntities(procedureProcessAssemblyEntities);
            //获取工序人力数据列表
            List<ProcedurePostEntity> procedurePostEntities = procedurePostService.getListById(craftProcedureId);
            List<ProcedurePostEntity> postEntityList = new ArrayList<>();
            procedurePostEntities.forEach(procedurePostEntity -> {
                SysPostEntity sysPostEntity = sysPostService.getById(procedurePostEntity.getPostId());
                if (sysPostEntity == null) {
                    return;
                }
                ProcedurePostEntity procedure = ProcedurePostEntity.builder()
                        .id(procedurePostEntity.getId())
                        .procedureId(craftProcedureId)
                        .postId(procedurePostEntity.getPostId())
                        .postCode(sysPostEntity.getPostCode())
                        .postName(sysPostEntity.getPostName())
                        .postLevel(sysPostEntity.getPostLevel())
                        .number(procedurePostEntity.getNumber())
                        .build();
                postEntityList.add(procedure);
            });
            craftProcedureEntity.setProcedurePostEntities(postEntityList);
            //获取工序质检数据列表
            List<ProcedureDefectSchemeEntity> procedureDefectSchemeEntities = procedureDefectSchemeService.getListById(craftProcedureId);
            craftProcedureEntity.setProcedureDefectSchemeEntities(procedureDefectSchemeEntities);
            //获取工序维修列表
            List<ProcedureMaintainSchemeEntity> procedureMaintainSchemeEntities = procedureMaintainSchemeService.getListById(craftProcedureId);
            craftProcedureEntity.setProcedureMaintainSchemeEntity(procedureMaintainSchemeEntities);
            //工序控制数据列表
            List<ProcedureDeviceTargetReferenceEntity> procedureDeviceTargetReferenceEntities = procedureDeviceTargetReferenceService.lambdaQuery().eq(ProcedureDeviceTargetReferenceEntity::getProcedureId, craftProcedureId).list();
            craftProcedureEntity.setProcedureDeviceTargetReferenceEntities(procedureDeviceTargetReferenceEntities);
            //工序控制配置表
            ProcedureControllerConfigEntity procedureControllerConfigEntity = procedureControllerConfigService.getDetail(craftProcedureId);
            craftProcedureEntity.setProcedureControllerConfigEntity(procedureControllerConfigEntity);
            // 工艺参数
            List<ProcessParameterConfigEntity> procedureParameterConfigEntities = processParameterConfigService.lambdaQuery().eq(ProcessParameterConfigEntity::getCraftProcedureId, craftProcedureId).orderByAsc(ProcessParameterConfigEntity::getSort).list();
            craftProcedureEntity.setProcedureParameterConfigEntities(procedureParameterConfigEntities);
            // 工序检验方案
            List<CraftProcedureInspectControllerEntity> procedureInspectControllerEntities = craftProcedureInspectControllerService.lambdaQuery().eq(CraftProcedureInspectControllerEntity::getCraftProcedureId, craftProcedureId).list();
            craftProcedureEntity.setProcedureInspectControllerEntities(procedureInspectControllerEntities);
        }
        return craftProcedureEntity;
    }


    @Override
    public Collection<CraftRouteVO> getCraftRoute(Integer craftId) {
        // 获取所属该工艺的所有工序列表
        List<CraftProcedureEntity> allProcedureList = this.listByCraftId(craftId);

        Map<Integer, CraftRouteVO> map = new HashMap<>(16);
        List<Integer> procedureIds = allProcedureList.stream().map(CraftProcedureEntity::getProcedureId).collect(Collectors.toList());
        Map<Integer, String> procedureIdCodeMap = getProcedureIdCodeMap(procedureIds);

        List<Integer> craftProcedureIds = allProcedureList.stream().map(CraftProcedureEntity::getId).collect(Collectors.toList());
        Map<Integer, ProcedureControllerConfigEntity> configMap = procedureControllerConfigService.getMapByCraftProcedureIds(craftProcedureIds);

        // 设置下级工序到target属性中
        for (CraftProcedureEntity craftProcedureEntity : allProcedureList) {
            List<Integer> nextProcedureIds = null;
            if (StringUtils.isNotBlank(craftProcedureEntity.getNextProcedureId())) {
                nextProcedureIds = Stream.of(craftProcedureEntity.getNextProcedureId().split(Constants.SEP))
                        .map(Integer::parseInt).collect(Collectors.toList());
            }
            Boolean jumpStationCheck = false;
            ProcedureControllerConfigEntity configEntity = configMap.get(craftProcedureEntity.getId());
            if (configEntity != null) {
                jumpStationCheck = configEntity.getJumpStationCheck();
            }
            CraftRouteVO routeDTO = CraftRouteVO.builder()
                    .craftId(craftProcedureEntity.getCraftId())
                    .procedureId(craftProcedureEntity.getId())
                    .procedureName(craftProcedureEntity.getProcedureName())
                    .procedureCode(procedureIdCodeMap.get(craftProcedureEntity.getProcedureId()))
                    .type(craftProcedureEntity.getType())
                    .jumpStationCheck(jumpStationCheck)
                    .alias(craftProcedureEntity.getAlias())
                    .isSubContractingOperation(craftProcedureEntity.getIsSubContractingOperation())
                    .isInspectionOperation(craftProcedureEntity.getIsInspectionOperation())
                    .isQualityProcess(craftProcedureEntity.getIsQualityProcess())
                    .isMaintenanceProcedure(craftProcedureEntity.getIsMaintenanceProcedure())
                    .target(nextProcedureIds).build();
            map.put(craftProcedureEntity.getId(), routeDTO);
        }
        return map.values().stream().sorted(Comparator.comparing(CraftRouteVO::getProcedureId)).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, String> getProcedureIdCodeMap(List<Integer> procedureIds) {
        //查询工序编码集合
        Map<Integer, String> procedureIdCodeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(procedureIds)) {
            procedureIdCodeMap = procedureService.lambdaQuery()
                    .select(ProcedureEntity::getProcedureId, ProcedureEntity::getProcedureCode)
                    .in(ProcedureEntity::getProcedureId, procedureIds).list()
                    .stream().collect(Collectors.toMap(ProcedureEntity::getProcedureId, ProcedureEntity::getProcedureCode));
        }
        return procedureIdCodeMap;
    }

    @Override
    public Map<Integer, CraftProcedureEntity> getCraftProcedureIdMap(List<Integer> craftProcedureIds) {
        if (CollectionUtils.isEmpty(craftProcedureIds)) {
            return Maps.newHashMap();
        }
        return this.listByIds(craftProcedureIds)
                .stream().collect(Collectors.toMap(CraftProcedureEntity::getId, Function.identity()));
    }

    @Override
    public void importFile(Integer craftId, MultipartFile file) {
        if (ExcelUtil.isExcelFile(file.getOriginalFilename())) {
            List<List<String>> lists = ExcelUtil.excelImport(file);

            for (int i = 1; i < lists.size(); i++) {
                List<String> strings = lists.get(i);
                String procedureName = strings.get(0);
                String supProcedureName = strings.get(1);
                String isLastProcedureName = strings.get(2);
                String facTypeName = strings.get(3);
                //委外工序
                String isSubContractingOperation = strings.get(4);
                //制造单元模型
                String lineModelName = strings.get(5);
                //委外供应商
                String outSourcingSupplier = strings.get(6);
                //检验工序
                String isInspectionOperation = strings.get(7);
                //维修工序
                String isMaintenanceProcedure = strings.get(8);
                //质检工序
                String isQualityProcess = strings.get(9);
                //获取是与否的枚举类对象中的名称列表
                List<String> TrueFalseNameList = getTrueFalseNameList();
//              委外供应商类型的供应商对象
                List<SupplierEntity> supplierNameList = procedureService.getSupplierName();
                HashMap<String, String> map = new HashMap<>();
                for (SupplierEntity supplierEntity : supplierNameList) {
                    map.put(supplierEntity.getName(), supplierEntity.getCode());
                }
                //1. 判断 委外工序如果为 "是" 委外供应商不能为空
                Integer code = OutsourcingProcedureEnum.getCodeByName(isSubContractingOperation);
                if (Objects.isNull(code)) {
                    throw new ResponseException("导入的文件中委外工序只能是“否、完全、部分”");
                }
                //判断委外工序为是
                if (OutsourcingProcedureEnum.YES.getCode() == code) {
                    //委外供应商不能为空
                    if (StringUtils.isBlank(outSourcingSupplier)) {
                        //提示原因---委外供应商不能为空
                        throw new ResponseException(RespCodeEnum.IMPORT_FILE_OUT_SOURCING_SUPPLIER_NOT_NULL);
                    }
                } else {
                    if (StringUtils.isNotBlank(outSourcingSupplier)) {
                        //判断委外供应商的名字 是否在 供应商列表中,是委外供应商类型，对应的名称列表中
                        if (!map.containsKey(outSourcingSupplier)) {
                            //提示原因---委外供应商的名字不在供应商列表中
                            throw new ResponseException(RespCodeEnum.IMPORT_FILE_OUT_SOURCING_SUPPLIER_NOT_EXIST_LIST);
                        }
                    }
                }
                //若不为空，得到 是否为委外工序、委外供应商 字段


                // 获取移除工序名后面的序号
                int count = 0;
                String newProcedureName = "";
                for (int j = procedureName.length() - 1; j > 0; j--) {
                    char ch = procedureName.charAt(j);
                    if (MathUtil.isNumeric(String.valueOf(ch))) {
                        count += 1;
                    } else {
                        newProcedureName = procedureName.substring(0, procedureName.length() - count);
                        break;
                    }
                }
                ProcedureEntity procedureEntity = procedureService.getProcedureByName(newProcedureName);
                // 判断是否存在该工序
                if (procedureEntity == null) {
                    throw new ResponseException(RespCodeEnum.IMPORT_FILE_INCLUDE_NON_EXIST_PROCEDURE);
                }
                //2. 判断制造单元模型是否是工序对应的制造单元模型
                ProcedureEntity procedureEntity1 = procedureService.getEntityById(procedureEntity.getProcedureId());
                List<ModelEntity> manufacturingUnitList = procedureEntity1.getLineModelList();
                List<String> collect = manufacturingUnitList.stream().map(ModelEntity::getName).collect(Collectors.toList());
                if (StringUtils.isNotBlank(lineModelName)) {
                    if (!collect.contains(lineModelName)) {
                        //提示原因，不包含制造单元模型
                        throw new ResponseException(RespCodeEnum.IMPORT_FILE_INCLUDE_NON_EXIST_UNIT_TYPE);
                    }
                }
                //查询工序对应的工位类型(关闭作业工单特性才走这个)
                List<ModelEntity> facModel = procedureEntity1.getFacModel();
                List<String> facModelNameList = facModel.stream().map(ModelEntity::getName).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(facModelNameList)) {
                    if (!facModelNameList.contains(facTypeName)) {
                        //提示原因，检验工序 为 是 或 否
                        throw new ResponseException(RespCodeEnum.IMPORT_FILE_FAC_TYPE_NAME_NOT_LIST);
                    }
                }
                //若包含制造单元模型，拿到 制造单元模型,获取制造单元模型的id
                LambdaQueryWrapper<ModelEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ModelEntity::getName, lineModelName)
                        .eq(ModelEntity::getType, ModelEnum.LINE.getType());
                ModelEntity modelEntity1 = modelService.getOne(queryWrapper);
                //3. 判断是否为检验工序
                if (!TrueFalseNameList.contains(isInspectionOperation)) {
                    //提示原因，检验工序 为 是 或 否
                    throw new ResponseException(RespCodeEnum.IMPORT_FILE_CONTRACTING_OPERATION_IS_YES_OR_NO);
                }
                //3. 判断是否为维修工序
                if (!TrueFalseNameList.contains(isMaintenanceProcedure)) {
                    //提示原因，维修工序 为 是 或 否
                    throw new ResponseException(RespCodeEnum.IMPORT_FILE_CRAFT_PROCEDURE_IS_MAINTENANCE_PROCEDURE);
                }
                //3. 判断是否为质检工序
                if (!TrueFalseNameList.contains(isQualityProcess)) {
                    //提示原因，质检工序 为 是 或 否
                    throw new ResponseException(RespCodeEnum.IMPORT_FILE_CRAFT_PROCEDURE_IS_QUAlITY_PROCESS);
                }
                //若包含 是 或 否，拿到 检验工序
                if (StringUtils.isEmpty(supProcedureName)) {
                    CraftProcedureEntity entity = CraftProcedureEntity.builder()
                            .craftId(craftId)
                            .procedureId(procedureEntity.getProcedureId())
                            .procedureName(procedureName)
                            .facType(facTypeName)
                            .supProcedureId(null)
                            .supProcedureName(null)
                            .whetherImport(true)
                            .isLastProcedure(String.valueOf(TrueFalseEnum.getCodeByName(isLastProcedureName)))
                            .createTime(new Date())
                            .isInspectionOperation("是".equals(isInspectionOperation))
                            .isSubContractingOperation(Objects.nonNull(OutsourcingProcedureEnum.getCodeByName(isSubContractingOperation)) ? OutsourcingProcedureEnum.getCodeByName(isSubContractingOperation) : OutsourcingProcedureEnum.NO.getCode())
                            .isMaintenanceProcedure("是".equals(isMaintenanceProcedure))
                            .isQualityProcess("是".equals(isQualityProcess))
                            .outSourcingSupplier(outSourcingSupplier)
                            .lineModelName(lineModelName)
                            .lineModelId(modelEntity1.getId())
                            .outSourcingSupplierCode(map.get(outSourcingSupplier))
                            .build();
                    this.saveCraftProcedure(entity);
                } else {
                    String[] split = supProcedureName.split(",");
                    StringBuilder sb = new StringBuilder();
                    ArrayList<String> supProcedureList = new ArrayList<>();
                    for (String supName : split) {
                        LambdaQueryWrapper<CraftProcedureEntity> cp = new LambdaQueryWrapper<>();
                        cp.eq(CraftProcedureEntity::getProcedureName, supName);
                        cp.eq(CraftProcedureEntity::getWhetherImport, true);
                        cp.orderByDesc(CraftProcedureEntity::getCreateTime)
                                .orderByDesc(CraftProcedureEntity::getId);
                        cp.last("limit 1");
                        CraftProcedureEntity one = this.getOne(cp);
                        sb.append(one.getId());
                        sb.append(",");
                        supProcedureList.add(String.valueOf(one.getId()));
                    }
                    sb.deleteCharAt(sb.length() - 1);
                    CraftProcedureEntity entity = CraftProcedureEntity.builder()
                            .craftId(craftId)
                            .procedureId(procedureEntity.getProcedureId())
                            .procedureName(procedureName)
                            .facType(facTypeName)
                            .supProcedureId(sb.toString())
                            .supProcedureName(supProcedureName)
                            .whetherImport(true)
                            .isLastProcedure(String.valueOf(TrueFalseEnum.getCodeByName(isLastProcedureName)))
                            .createTime(new Date())
                            .isInspectionOperation("是".equals(isInspectionOperation))
                            .isSubContractingOperation(Objects.nonNull(OutsourcingProcedureEnum.getCodeByName(isSubContractingOperation)) ? OutsourcingProcedureEnum.getCodeByName(isSubContractingOperation) : OutsourcingProcedureEnum.NO.getCode())
                            .isMaintenanceProcedure("是".equals(isMaintenanceProcedure))
                            .isQualityProcess("是".equals(isQualityProcess))
                            .outSourcingSupplier(outSourcingSupplier)
                            .lineModelName(lineModelName)
                            .lineModelId(modelEntity1.getId())
                            .outSourcingSupplierCode(map.get(outSourcingSupplier))
                            .build();
                    this.saveCraftProcedure(entity);
                    //新增直接后继插入
                    for (String s : supProcedureList) {
                        addNextProcedureEntity(s, entity);
                    }
                }
            }
            if (!lists.isEmpty()) {
                versionChangeRecordService.changeVersion(VersionChangeDTO.builder()
                        .relateType(VersionModelIdEnum.CRAFT)
                        .relateId(craftId)
                        .description("工艺工序路线 - 导入")
                        .build()
                );
            }
        } else {
            throw new ResponseException(RespCodeEnum.PARSING_DATA_IS_EMPTY);
        }
    }

    /**
     * 获取是与否的枚举类对象中的名称列表
     *
     * @return
     */
    private List<String> getTrueFalseNameList() {
        List<StateEnumDTO> TrueFalseList = Stream.of(TrueFalseEnum.values())
                .map(stateEnum -> StateEnumDTO.builder().code(stateEnum.getCode()).name(stateEnum.getName()).build())
                .collect(Collectors.toList());
        return TrueFalseList.stream().map(StateEnumDTO::getName).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeEntityById(Integer deleteId, Boolean autoFixNode) {
        CraftProcedureEntity deleter = this.getById(deleteId);
        fixNodeDelete(deleter, autoFixNode);
        // 删除工序
        this.removeById(deleteId);
        //发送kafka消息
        messagePushToKafkaService.pushNewMessage(this.getById(deleteId), Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CRAFT_PROCEDURE_DELETE_MESSAGE);
        // 删除工序关联的配置表
        // 删除工序用料表
        procedureMaterialUsedService.lambdaUpdate().eq(ProcedureMaterialUsedEntity::getCraftId, deleter.getCraftId()).eq(ProcedureMaterialUsedEntity::getProcedureId, deleteId).remove();
        //发送kafka消息
        messagePushToKafkaService.pushNewMessage(this.getById(deleteId), Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CRAFT_PROCEDURE_MATERIAL_DELETE_MESSAGE);
        // 删除设备类型表
        procedureDeviceTypeService.lambdaUpdate().eq(ProcedureDeviceTypeEntity::getCraftId, deleter.getCraftId()).eq(ProcedureDeviceTypeEntity::getProcedureId, deleteId).remove();
        //删除工序设备指标参考表
        procedureDeviceTargetReferenceService.lambdaUpdate().eq(ProcedureDeviceTargetReferenceEntity::getCraftId, deleter.getCraftId()).eq(ProcedureDeviceTargetReferenceEntity::getProcedureId, deleteId).remove();
        // 删除附件表
        List<CraftProcedureEntity> craftProcedureEntityList = this.listByCraftId(deleter.getCraftId());
        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntityList) {
            procedureFileService.lambdaUpdate().eq(ProcedureFileEntity::getCraftId, deleter.getCraftId()).eq(ProcedureFileEntity::getProcedureId, craftProcedureEntity.getProcedureId()).remove();
            //删除附件表
            appendixService.lambdaUpdate().eq(AppendixEntity::getRelateId, craftProcedureEntity.getId()).remove();
        }
        //删除工序控制配置
        procedureControllerConfigService.lambdaUpdate().eq(ProcedureControllerConfigEntity::getCraftProcedureId, deleteId).remove();
        //删除工序工时关联表(标准调试工时等3个)
        procedureRelationWorkHoursService.lambdaUpdate().eq(ProcedureRelationWorkHoursEntity::getCraftId, deleter.getCraftId()).eq(ProcedureRelationWorkHoursEntity::getProcedureId, deleteId).remove();
        //删除工序装备
        procedureProcessAssemblyService.lambdaUpdate().eq(ProcedureProcessAssemblyEntity::getCraftId, deleter.getCraftId()).eq(ProcedureProcessAssemblyEntity::getProcedureId, deleteId).remove();
        //删除工序人力
        procedurePostService.lambdaUpdate().eq(ProcedurePostEntity::getCraftId, deleter.getCraftId()).eq(ProcedurePostEntity::getProcedureId, deleteId).remove();
        //删除工序质检
        procedureMaintainSchemeService.lambdaUpdate().eq(ProcedureMaintainSchemeEntity::getCraftId, deleter.getCraftId()).eq(ProcedureMaintainSchemeEntity::getProcedureId, deleteId).remove();
        //删除工序维修
        procedureDefectSchemeService.lambdaUpdate().eq(ProcedureDefectSchemeEntity::getCraftId, deleter.getCraftId()).eq(ProcedureDefectSchemeEntity::getProcedureId, deleteId).remove();
        //删除工序检验配置表
        procedureInspectionConfigService.lambdaUpdate().eq(ProcedureInspectionConfigEntity::getCraftId, deleter.getCraftId()).eq(ProcedureInspectionConfigEntity::getCraftProcedureId, deleteId).remove();
        // 删除工单工序关联表
        workOrderProcedureRelationService.lambdaUpdate().eq(WorkOrderProcedureRelationEntity::getCraftProcedureId, deleteId).remove();
        // 删除工序质检方案
        procedureDefectSchemeService.lambdaUpdate().eq(ProcedureDefectSchemeEntity::getProcedureId, deleteId).remove();
        // 删除维修质检方案
        procedureMaintainSchemeService.lambdaUpdate().eq(ProcedureMaintainSchemeEntity::getProcedureId, deleteId).remove();
        // 删除工单工序检查项
        workOrderProcedureInspectResultService.lambdaUpdate().eq(WorkOrderProcedureInspectResultEntity::getCraftProcedureId, deleteId).remove();
        //删除工艺工序缓存
        deleteScannerRedis(deleter);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("路线 - 删除")
                .craftProcedureId(deleter.getId())
                .build()
        );
    }

    private void fixNodeDelete(CraftProcedureEntity deleter, boolean autoFixNode) {
        // 自动续接
        if(autoFixNode) {
            checkAutoFixNode(deleter, null);
        }
        Integer deleteId = deleter.getId();
        String deleteIdStr = String.valueOf(deleteId);
        String deleteName = deleter.getProcedureName();
        // 如上级工序包含删除的本工序，去除对应的上级工序
        List<CraftProcedureEntity> craftProcedures = this.listByCraftId(deleter.getCraftId()).stream().filter(e -> !e.getId().equals(deleteId)).collect(Collectors.toList());
        Set<CraftProcedureEntity> updates = new HashSet<>();
        for (CraftProcedureEntity craftProcedure : craftProcedures) {
            Set<String> supIdSet = Arrays.stream(Optional.ofNullable(craftProcedure.getSupProcedureId()).orElse("").split(Constant.SEP)).collect(Collectors.toSet());
            Set<String> nextIdSet = Arrays.stream(Optional.ofNullable(craftProcedure.getNextProcedureId()).orElse("").split(Constant.SEP)).collect(Collectors.toSet());
            // 名称也要改
            Set<String> supNameSet = Arrays.stream(Optional.ofNullable(craftProcedure.getSupProcedureName()).orElse("").split(Constant.SEP)).collect(Collectors.toSet());
            Set<String> nextNameSet = Arrays.stream(Optional.ofNullable(craftProcedure.getNextProcedureName()).orElse("").split(Constant.SEP)).collect(Collectors.toSet());
            // 当前节点的上级为删除的节点(作为下游)
            if (supIdSet.contains(deleteIdStr)) {
                supIdSet.remove(deleteIdStr);
                supNameSet.remove(deleteName);
                // 自动续接-前后只有一个
                if (autoFixNode) {
                    supIdSet.add(deleter.getSupProcedureId());
                    supNameSet.add(deleter.getSupProcedureName());
                }
                String supProcedureIds = String.join(Constant.SEP, supIdSet);
                String supProcedureNames = String.join(Constant.SEP, supNameSet);
                craftProcedure.setSupProcedureId(supProcedureIds);
                craftProcedure.setSupProcedureName(supProcedureNames);
                updates.add(craftProcedure);
            }
            // 当前节点的下级为删除的节点(作为上游)
            if (nextIdSet.contains(deleteIdStr)) {
                nextIdSet.remove(deleteIdStr);
                nextNameSet.remove(deleteName);
                // 自动续接-前后只有一个
                if (autoFixNode) {
                    nextIdSet.add(deleter.getNextProcedureId());
                    nextNameSet.add(deleter.getNextProcedureName());
                }
                String nextProcedureIds = String.join(Constant.SEP, nextIdSet);
                String nextProcedureNames = String.join(Constant.SEP, nextNameSet);
                craftProcedure.setNextProcedureId(nextProcedureIds);
                craftProcedure.setNextProcedureName(nextProcedureNames);
                updates.add(craftProcedure);
            }
        }
        this.updateBatchById(updates);
        for (CraftProcedureEntity update : updates) {
            messagePushToKafkaService.pushNewMessage(update, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CRAFT_PROCEDURE_UPDATE_MESSAGE);
        }
    }

    private void fixNodeEdit(CraftProcedureEntity editor) {
        boolean autoFixNode = editor.getAutoFixNode();
        CraftProcedureEntity oldEditor = this.getById(editor.getId());
        if(oldEditor == null) {
            throw new ResponseException("找不到该节点id：" + editor.getId());
        }
        // 校验自动续接
        if(autoFixNode) {
            checkAutoFixNode(editor, oldEditor);
        }
        // 将前端传入节点的下游置空避免更新了脏数据。这里只需改变上游即可
        editor.setNextProcedureId(null);
        editor.setNextProcedureName(null);

        // 校验前驱节点是否改变
        String oldSupProcedureId = oldEditor.getSupProcedureId();
        String newSupProcedureId = editor.getSupProcedureId();
        Set<String> oldSupIdSet = Arrays.stream(Optional.ofNullable(oldSupProcedureId).orElse("").split(Constant.SEP)).collect(Collectors.toSet());
        Set<String> newSupIdSet = Arrays.stream(Optional.ofNullable(newSupProcedureId).orElse("").split(Constant.SEP)).collect(Collectors.toSet());
        if(oldSupIdSet.containsAll(newSupIdSet) && newSupIdSet.containsAll(oldSupIdSet)) {
            return;
        }

        // 自动续接-前后只有一个
        if(autoFixNode) {
            String newSupId = editor.getSupProcedureId();
            String oldSupId = oldEditor.getSupProcedureId();
            String nextId = oldEditor.getNextProcedureId();
            CraftProcedureEntity oldSupNode = this.getById(oldSupId);
            CraftProcedureEntity nextNode = this.getById(nextId);
            // 维护newSupNode
            addNextProcedureEntity(newSupId, editor);
            // 维护oldSupNode
            oldSupNode.setNextProcedureId(nextId);
            oldSupNode.setNextProcedureName(nextNode.getProcedureName());
            this.updateById(oldSupNode);
            // 维护nextNode
            nextNode.setSupProcedureId(oldSupId);
            nextNode.setSupProcedureName(oldSupNode.getProcedureName());
            this.updateById(nextNode);
            // 维护自己
            editor.setNextProcedureId("");
            editor.setNextProcedureName("");
            this.updateById(editor);
        }else {
            // 这里仅需更新上一工序的下游工序即可
            // 记录修改后删除的前驱节点
            Collection<String> deletes = CollUtil.subtract(oldSupIdSet, newSupIdSet);
            // 记录修改后新增的前驱节点
            Collection<String> adds = CollUtil.subtract(newSupIdSet, oldSupIdSet);
            // deletes: 非空删除前驱需要删除该节点的后继
            if (!deletes.isEmpty()) {
                for (String curId : deletes) {
                    deleteNextProcedureEntity(curId, editor);
                }
            }
            // adds: 非空有新增前驱需要新增该节点的后继
            if (!adds.isEmpty()) {
                for (String curId : adds) {
                    addNextProcedureEntity(curId, editor);
                }
            }
        }
    }

    /**
     * 需要自动续接时，判断节点的是否满足条件逻辑优化
     * a. 修改时: 旧值：上下游是否有且只有一个, 新值上游最多只有一个节点
     * b. 删除时: 旧值：上下游是否有且只有一个
     * @param target 操作节点
     */
    private void checkAutoFixNode(CraftProcedureEntity target, CraftProcedureEntity old) {
        // 上游
        if(old != null) {
            if(StringUtils.isBlank(old.getSupProcedureId())) {
                throw new ResponseException("修改前该节点不存在上游, 无法自动续接");
            }
            if(old.getSupProcedureId().split(Constant.SEP).length > 1) {
                throw new ResponseException("修改前该节点存在多个上游, 无法自动续接");
            }
            if(StringUtils.isNotBlank(target.getSupProcedureId()) && old.getSupProcedureId().split(Constant.SEP).length > 1) {
                throw new ResponseException("修改后该节点存在多个上游, 无法自动续接");
            }
        }else {
            if(StringUtils.isBlank(target.getSupProcedureId())) {
                throw new ResponseException("该节点不存在上游, 无法自动续接");
            }
            if(target.getSupProcedureId().split(Constant.SEP).length > 1) {
                throw new ResponseException("该节点存在多个上游, 无法自动续接");
            }
        }
        // 下游: 传入的值均不变动，直接判断即可
        if(StringUtils.isBlank(target.getNextProcedureId())) {
            throw new ResponseException("该节点不存在下游, 无法自动续接");
        }
        if(target.getNextProcedureId().split(Constant.SEP).length > 1) {
            throw new ResponseException("该节点存在多个下游, 无法自动续接");
        }
    }

    @Override
    public void saveEntity(CraftProcedureEntity craftProcedureEntity, boolean oneKeyDefault) {
        //添加部分判空后的字段
        addCraftProcedureField(craftProcedureEntity);
        craftProcedureEntity.setWhetherImport(false);
        // 判断工序名是否相同，相同则加序号区分
        CraftProcedureEntity one = this.lambdaQuery()
                .eq(CraftProcedureEntity::getCraftId, craftProcedureEntity.getCraftId())
                .eq(CraftProcedureEntity::getProcedureName, craftProcedureEntity.getProcedureName())
                .one();
        if (one != null) {
            craftProcedureEntity.setProcedureName(codeFactory.getProcedureNumber(craftProcedureEntity.getCraftId(), craftProcedureEntity.getProcedureName()));
        }
        //如果类型为line则自动补全工作中心
        setWorkCenter(craftProcedureEntity);
        this.saveCraftProcedure(craftProcedureEntity);
        //当有选择上一工序时才进行修改操作
        if (!StringUtils.isBlank(craftProcedureEntity.getSupProcedureId())) {
            String[] split = craftProcedureEntity.getSupProcedureId().split(",");
            for (String s : split) {
                addNextProcedureEntity(s, craftProcedureEntity);
            }
        }


        //删除工艺工序缓存
        deleteScannerRedis(craftProcedureEntity);

        // 增加工艺工序默认的相关配置（从工序定义中读取）
        if (oneKeyDefault) {
            craftService.oneKeyDefault(ProcedureOneKeyDefaultDTO.builder().craftProcedureId(craftProcedureEntity.getId()).recordChange(true).build());
        }
        if (!craftProcedureEntity.getInnerOperate()) {
            versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                    .description("路线 - 新增")
                    .craftProcedureId(craftProcedureEntity.getId())
                    .build()
            );
        }
    }

    /**
     * 删除工艺工序的扫码过站相关缓存
     *
     * @param craftProcedureEntity
     */
    @Override
    public void deleteScannerRedis(CraftProcedureEntity craftProcedureEntity) {
        redisTemplate.delete(com.yelink.dfs.constant.RedisKeyPrefix.SCANNER_PROCEDURE_CONTROLLER_CONFIG + craftProcedureEntity.getId());
        List<CraftProcedureEntity> craftProcedureEntityList = this.listByCraftId(craftProcedureEntity.getCraftId());
        for (CraftProcedureEntity craftProcedureEntityTemp : craftProcedureEntityList) {
            redisTemplate.delete(com.yelink.dfs.constant.RedisKeyPrefix.SCANNER_CRAFT_PROCEDURE + craftProcedureEntityTemp.getId());
            redisTemplate.delete(com.yelink.dfs.constant.RedisKeyPrefix.SCANNER_LAST_PROCEDURES + craftProcedureEntityTemp.getId());
            redisTemplate.delete(com.yelink.dfs.constant.RedisKeyPrefix.SCANNER_KEY_PROCEDURE + craftProcedureEntityTemp.getId());
        }
    }

    /**
     * 新增的工序默认添加该工序的检验项
     */
    private void addDefaultInspectionConfig(CraftProcedureEntity craftProcedureEntity) {
        ProcedureEntity procedureEntity = procedureService.getById(craftProcedureEntity.getProcedureId());
        List<ProcedureDefInspectionConfigEntity> defInspectionConfigEntities = procedureDefInspectionConfigService.lambdaQuery().eq(ProcedureDefInspectionConfigEntity::getProcedureCode, procedureEntity.getProcedureCode()).list();
        if (CollectionUtils.isEmpty(defInspectionConfigEntities)) {
            return;
        }
        List<ProcedureInspectionConfigEntity> procedureInspectionConfigEntities = JSON.parseArray(JSON.toJSONString(defInspectionConfigEntities), ProcedureInspectionConfigEntity.class);
        for (ProcedureInspectionConfigEntity inspectionConfigEntity : procedureInspectionConfigEntities) {
            inspectionConfigEntity.setId(null);
            inspectionConfigEntity.setCreateTime(craftProcedureEntity.getCreateTime());
            inspectionConfigEntity.setUpdateTime(craftProcedureEntity.getUpdateTime());
            inspectionConfigEntity.setCreateBy(craftProcedureEntity.getCreateBy());
            inspectionConfigEntity.setUpdateBy(craftProcedureEntity.getUpdateBy());
            inspectionConfigEntity.setCraftId(craftProcedureEntity.getCraftId());
            inspectionConfigEntity.setCraftProcedureId(craftProcedureEntity.getId());
        }
        procedureInspectionConfigService.saveBatch(procedureInspectionConfigEntities);
    }

    /**
     * 添加部分字段(做过判断)
     *
     * @param craftProcedureEntity
     */
    private void addCraftProcedureField(CraftProcedureEntity craftProcedureEntity) {
        if (StringUtils.isNotBlank(craftProcedureEntity.getOutSourcingSupplierCode())) {
            List<String> outSourcingSupplierCodes = Arrays.asList(craftProcedureEntity.getOutSourcingSupplierCode().split(Constant.SEP));
            Map<String, String> supplierCodeName = supplierService.lambdaQuery()
                    .select(SupplierEntity::getCode, SupplierEntity::getName)
                    .in(SupplierEntity::getCode, outSourcingSupplierCodes)
                    .list().stream().collect(Collectors.toMap(SupplierEntity::getCode, SupplierEntity::getName));
            if (!CollectionUtils.isEmpty(supplierCodeName)) {
                String outSourcingSupplierNames = outSourcingSupplierCodes.stream().map(supplierCodeName::get).collect(Collectors.joining(Constant.SEP));
                craftProcedureEntity.setOutSourcingSupplier(outSourcingSupplierNames);
            }
        }
    }

    /**
     * 通过物料编号获取工艺工序路线
     *
     * @param materialCode
     * @return
     */
    @Override
    public List<CraftProcedureEntity> getCraftProcedureListByMaterialCode(String materialCode) {
        if (StringUtils.isBlank(materialCode)) {
            return new ArrayList<>();
        }
        CraftEntity craftEntity = craftService.getCraftByMaterialCode(materialCode);
        if (craftEntity == null) {
            return new ArrayList<>();
        }
        return this.listByCraftId(craftEntity.getCraftId());
    }

    @Override
    public List<CraftProcedureEntity> getCraftTree(Integer craftId) {
        List<CraftProcedureEntity> list = new ArrayList<>();
        LambdaQueryWrapper<CraftProcedureEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(CraftProcedureEntity::getCraftId, craftId);
        qw.and(wrapper -> wrapper.isNull(CraftProcedureEntity::getSupProcedureId).or().eq(CraftProcedureEntity::getSupProcedureId, ""));
        List<CraftProcedureEntity> cpList = this.list(qw);
        List<Integer> procedureIds = cpList.stream().map(CraftProcedureEntity::getProcedureId).collect(Collectors.toList());
        Map<Integer, String> procedureIdCodeMap = getProcedureIdCodeMap(procedureIds);

        for (CraftProcedureEntity craftProcedureEntity : cpList) {
            craftProcedureEntity.setProcedureCode(procedureIdCodeMap.get(craftProcedureEntity.getProcedureId()));
            list.add(craftProcedureEntity);
            getChildrenNode(craftProcedureEntity, CraftMaterialDTO.builder().craftId(craftId).build(), new HashMap<>());
        }
        return list;
    }

    @Override
    public List<CraftProcedureEntity> getCraftTree(CraftMaterialDTO craftMaterialDTO) {
        List<CraftProcedureEntity> list = new ArrayList<>();
        LambdaQueryWrapper<CraftProcedureEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(CraftProcedureEntity::getCraftId, craftMaterialDTO.getCraftId());
        qw.and(wrapper -> wrapper.isNull(CraftProcedureEntity::getSupProcedureId).or().eq(CraftProcedureEntity::getSupProcedureId, ""));
        List<CraftProcedureEntity> cpList = this.list(qw);
        List<Integer> craftProcedureIds = cpList.stream().map(CraftProcedureEntity::getProcedureId).collect(Collectors.toList());
        Map<Integer, String> procedureIdCodeMap = getProcedureIdCodeMap(craftProcedureIds);
        Map<Integer, ProcedureControllerConfigEntity> configMap = procedureControllerConfigService.getMapByCraftProcedureIds(craftProcedureIds);

        for (CraftProcedureEntity craftProcedureEntity : cpList) {
            craftProcedureEntity.setProcedureCode(procedureIdCodeMap.get(craftProcedureEntity.getProcedureId()));
            ProcedureControllerConfigEntity configEntity = configMap.get(craftProcedureEntity.getId());
            // 添加换算系数
            craftProcedureEntity.setConversionFactor(configEntity != null ? configEntity.getConversionFactor() : 1);
            // 设置工艺工序颜色(逻辑：该工艺工序下所有工单，完成数和计划数之和相比较)
            if (judgeIsSetCraftProcedureColor(craftMaterialDTO)) {
                setCraftProcedureColor(craftProcedureEntity, craftMaterialDTO.getColorDto());
            }
            list.add(craftProcedureEntity);
            getChildrenNode(craftProcedureEntity, craftMaterialDTO, configMap);
        }
        return list;
    }

    /**
     * 判断是否需要设置工艺工序颜色
     */
    private boolean judgeIsSetCraftProcedureColor(CraftMaterialDTO craftMaterialDTO) {
        return Objects.nonNull(craftMaterialDTO.getColorDto()) && Objects.nonNull(craftMaterialDTO.getColorDto().getIsShowColorInfo()) && craftMaterialDTO.getColorDto().getIsShowColorInfo();
    }

    /**
     * 设置工艺工序颜色(逻辑：该工艺工序下，同一个生产订单下所有的工单，完成数和计划数之和相比较)
     */
    private void setCraftProcedureColor(CraftProcedureEntity craftProcedureEntity, CraftMaterialDTO.ColorDto colorDto) {
        List<WorkOrderProcedureRelationEntity> workOrderProcedureRelations = workOrderProcedureRelationService.lambdaQuery()
                .eq(WorkOrderProcedureRelationEntity::getCraftProcedureId, craftProcedureEntity.getId())
                .list();
        List<String> workOrderNumbers = workOrderProcedureRelations.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                .eq(WorkOrderEntity::getProductOrderNumber, colorDto.getProductOrderNumber()).list();
        if (!CollectionUtils.isEmpty(workOrderEntities)) {
//            double planQuantitySum = workOrderEntities.stream().filter(o -> o.getPlanQuantity() != null).mapToDouble(WorkOrderEntity::getPlanQuantity).sum();
            double planQuantitySum = MathUtil.mulToKeepTwoDecimalPlaces(colorDto.getProductOrderPlanQuantity(), craftProcedureEntity.getConversionFactor());
            double finishCountSum = workOrderEntities.stream().filter(o -> o.getFinishCount() != null).mapToDouble(WorkOrderEntity::getFinishCount).sum();
            boolean inProduction = workOrderEntities.stream().anyMatch(o -> o.getState().equals(WorkOrderStateEnum.INVESTMENT.getCode()));
            if (finishCountSum == 0.0) {
                craftProcedureEntity.setColorState(0);
            } else if (finishCountSum < planQuantitySum) {
                craftProcedureEntity.setColorState(1);
            } else {
                craftProcedureEntity.setColorState(2);
            }
            craftProcedureEntity.setInProduction(inProduction);
        }
    }

    /**
     * 获取子节点
     *
     * @param
     * @return
     */
    private void getChildrenNode(CraftProcedureEntity craftProcedureEntity, CraftMaterialDTO craftMaterialDTO, Map<Integer, ProcedureControllerConfigEntity> configMap) {
        LambdaQueryWrapper<CraftProcedureEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(CraftProcedureEntity::getCraftId, craftMaterialDTO.getCraftId());
        qw.isNotNull(CraftProcedureEntity::getSupProcedureId);
        List<CraftProcedureEntity> listAll = this.list(qw);
        listAll.forEach(craftProcedureEntity1 -> {
            // 设置工艺工序颜色(逻辑：该工艺工序下所有工单，完成数和计划数之和相比较)
//            if (Objects.nonNull(craftMaterialDTO.getColorDto().getIsShowColorInfo()) && craftMaterialDTO.getColorDto().getIsShowColorInfo()) {
//                ProcedureControllerConfigEntity configEntity = configMap.get(craftProcedureEntity.getId());
//                // 添加换算系数
//                craftProcedureEntity.setConversionFactor(configEntity != null ? configEntity.getConversionFactor() : 1);
//                setCraftProcedureColor(craftProcedureEntity, craftMaterialDTO.getColorDto());
//            }
            String[] split = craftProcedureEntity1.getSupProcedureId().split(Constant.SEP);
            List<String> collect = Arrays.stream(split).collect(Collectors.toList());
            if (collect.contains(String.valueOf(craftProcedureEntity.getId()))) {
                if (craftProcedureEntity.getChildren() == null) {
                    List<CraftProcedureEntity> cpList = new ArrayList<>();
                    cpList.add(craftProcedureEntity1);
                    craftProcedureEntity.setChildren(cpList);
                    getChildrenNode(craftProcedureEntity1, craftMaterialDTO, configMap);
                } else {
                    craftProcedureEntity.getChildren().add(craftProcedureEntity1);
                    getChildrenNode(craftProcedureEntity1, craftMaterialDTO, configMap);
                }
            }
        });

    }


    @Override
    public CraftProcedureEntity getLastProcedure(Integer craftId) {
        QueryWrapper<CraftProcedureEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(CraftProcedureEntity::getCraftId, craftId)
                .eq(CraftProcedureEntity::getIsLastProcedure, Constant.YES);
        return this.getOne(wrapper);
    }

    @Override
    public List<CraftFileDTO> selectCraftFiles(Integer fid, String workOrderNumber, String name) {
        List<CraftFileDTO> craftFileDTOList;
        if (StringUtils.isBlank(name)) {
            craftFileDTOList = selectCraftFile(fid, workOrderNumber);
        } else {
            //模糊查询
            craftFileDTOList = search(name, selectCraftFile(fid, workOrderNumber));
        }
        return craftFileDTOList;
    }

    @Override
    public List<CraftFileNewDTO> selectAllCraftFiles(Integer fid, String workOrderNumber, String name) {
        List<CraftFileNewDTO> craftFileDTOList;
        if (StringUtils.isBlank(name)) {
            craftFileDTOList = selectAllCraftFile(fid, workOrderNumber);
        } else {
            //模糊查询
            craftFileDTOList = searchNew(name, selectAllCraftFile(fid, workOrderNumber));
        }
        return craftFileDTOList;

    }

    /**
     * 根据文件名称进行模糊查询
     *
     * @param name
     * @param list
     * @return
     */
    private List<CraftFileNewDTO> searchNew(String name, List<CraftFileNewDTO> list) {
        List<CraftFileNewDTO> results = new ArrayList<>();
        Pattern pattern = Pattern.compile(name);
        for (CraftFileNewDTO craftFileNewDTO : list) {
            Matcher matcher = pattern.matcher(craftFileNewDTO.getName());
            //可以进行模糊查询
            if (matcher.find()) {
                results.add(craftFileNewDTO);
            }
            Matcher mat = pattern.matcher(craftFileNewDTO.getFileName());
            if (mat.find()) {
                results.add(craftFileNewDTO);
            }
        }
        return results;
    }


    private List<CraftFileNewDTO> selectAllCraftFile(Integer fid, String workOrderNumber) {
        List<CraftFileNewDTO> craftFileDTOList = new ArrayList<>();
        //查询工位对应的工序、工序的附件(之前的条件)
        //条件1：根据工位查询对应的工位类型，再根据工单查询工艺，根据工艺查询工位类型，若工位类型匹配，查找对应的工序附件
        List<ProcedureFileEntity> procedureFileEntityList = getProcedureFileUrl(fid, workOrderNumber);
        // 查询工序名称
        if (!CollectionUtils.isEmpty(procedureFileEntityList)) {
            procedureFileEntityList.forEach(procedureFileEntity -> {
                ProcedureEntity procedureEntity = procedureService.getById(procedureFileEntity.getProcedureId());
                if (procedureEntity != null) {
                    procedureFileEntity.setProcedureName(procedureEntity.getName());
                }
            });
        }
        //条件2：根据工单号查询对应的工单，根据工单查询对应的成品物料，查询成品物料下/原材料对应的附件
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        Set<MaterialEntity> entitySet = materialService.getSemiProducts(workOrderEntity.getMaterialCode());
        //条件1查询
        if (!CollectionUtils.isEmpty(procedureFileEntityList)) {
            for (ProcedureFileEntity fileEntity : procedureFileEntityList) {
                CraftFileNewDTO craftFileNewDTO = CraftFileNewDTO.builder()
                        .name(fileEntity.getProcedureName())
                        .fileName(fileEntity.getName())
                        .typeName(AppendixTypeEnum.PROCEDURE_APPENDIX.getName())
                        .type(AppendixTypeEnum.PROCEDURE_APPENDIX.getCode())
                        .filePath(fileEntity.getFileUrl()).build();
                craftFileDTOList.add(craftFileNewDTO);
            }
        }
        List<String> ids = new ArrayList<>();
        // 查询bom下原拆料、成品
        if (!CollectionUtils.isEmpty(entitySet)) {
            entitySet.forEach(materialRawMaterialEntity -> {
                QueryWrapper<MaterialEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(MaterialEntity::getCode, materialRawMaterialEntity.getCode());
                MaterialEntity material = materialService.getOne(queryWrapper);
                ids.add(material.getId().toString());
            });

        }
        List<AppendixEntity> appendixEntities = appendixService.getFiles(AppendixTypeEnum.MATERIAL_APPENDIX.getCode(), ids);
        //条件2查询
        if (!CollectionUtils.isEmpty(appendixEntities)) {
            for (AppendixEntity fileEntity : appendixEntities) {
                CraftFileNewDTO craftFileDTO = CraftFileNewDTO.builder()
                        .name(fileEntity.getRelateName())
                        .filePath(fileEntity.getFilePath())
                        .typeName(AppendixTypeEnum.MATERIAL_APPENDIX.getName())
                        .type(AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                        .fileName(fileEntity.getFileName()).build();
                craftFileDTOList.add(craftFileDTO);
            }
        }
        return craftFileDTOList;
    }


    @Override
    public byte[] download() {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/craftProcedure.xlsx");
        if (!resource.exists()) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        InputStream bis = null;
        try {
            bis = new BufferedInputStream(resource.getInputStream());
            byte[] buffer = new byte[bis.available()];
            bis.read(buffer);
            return buffer;
        } catch (IOException ex) {
            log.error("文件获取失败", ex);
        } finally {
            IOUtils.closeQuietly(bis);
        }
        throw new ResponseException(RespCodeEnum.OPERATION_FAIL);
    }


    /**
     * (按照条件)查询工艺文件
     *
     * @param fid
     * @param workOrderNumber
     * @return
     */
    private List<CraftFileDTO> selectCraftFile(Integer fid, String workOrderNumber) {
        List<CraftFileDTO> craftFileDTOList = new ArrayList<>();
        //查询工位对应的工序、工序的附件(之前的条件)
        //条件1：根据工位查询对应的工位类型，再根据工单查询工艺，根据工艺查询工位类型，若工位类型匹配，查找对应的工序附件
        List<ProcedureFileEntity> procedureFileEntityList = getProcedureFileUrl(fid, workOrderNumber);
        //条件2：根据工单号查询对应的工单，根据工单查询对应的成品物料，查询成品物料下对应的附件
        String materialFileUrl = getMaterialFileUrl(workOrderNumber);
        //条件1查询
        if (!CollectionUtils.isEmpty(procedureFileEntityList)) {
            for (ProcedureFileEntity fileEntity : procedureFileEntityList) {
                CraftFileDTO craftFileDTO = CraftFileDTO.builder().name(fileEntity.getName()).url(fileEntity.getFileUrl()).build();
                craftFileDTOList.add(craftFileDTO);
            }
        } else {
            //条件2查询
            if (StringUtils.isNotBlank(materialFileUrl)) {
                CraftFileDTO craftFileDTO = CraftFileDTO.builder().name(materialFileUrl).url(materialFileUrl).build();
                craftFileDTOList.add(craftFileDTO);
            }
        }
        return craftFileDTOList;
    }

    /**
     * 根据文件名称进行模糊查询
     *
     * @param name
     * @param list
     * @return
     */
    private List<CraftFileDTO> search(String name, List<CraftFileDTO> list) {
        List<CraftFileDTO> results = new ArrayList<>();
        Pattern pattern = Pattern.compile(name);
        for (int i = 0; i < list.size(); i++) {
            Matcher matcher = pattern.matcher((list.get(i)).getName());
            //可以进行模糊查询
            if (matcher.find()) {
                results.add(list.get(i));
            }
        }
        return results;
    }


    /**
     * 条件2：根据工单号查询对应的工单，根据工单查询对应的物料，物料查询对应的附件
     *
     * @param workOrderNumber
     * @return
     */
    private String getMaterialFileUrl(String workOrderNumber) {
        //1.根据工单号查询对应的工单
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity != null) {
            //根据物料编码查询对应的成品物料
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(workOrderEntity.getMaterialCode());
            if (materialEntity != null) {
                //物料对应的附件
                return materialEntity.getFileUrl();
            }
        }
        return null;
    }

    /**
     * 条件1：根据工位查询对应的工位类型，再根据工单查询工艺，根据工艺查询工位类型，若工位类型匹配，查找对应的工序附件
     *
     * @param fid
     * @return
     */
    private List<ProcedureFileEntity> getProcedureFileUrl(Integer fid, String workOrderNumber) {
        //根据工位id查询对应的工位类型名称
        ModelEntity modelEntity = modelService.getModelEntityByFid(fid);
        String name = modelEntity.getName();
        //根据工单查询工艺,根据工艺查询工序附件
        //1.根据工单号查询对应的工单
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            throw new ResponseException(RespCodeEnum.WORK_CODE_IS_NOT_EMPTY);
        }
        Integer craftId = workOrderEntity.getCraftId();
        //工艺文件的列表集合
        List<ProcedureFileEntity> procedureFileEntityList = new ArrayList<>();
        List<CraftProcedureEntity> craftProcedureEntityList;
        if (Objects.isNull(craftId)) {
            return procedureFileEntityList;
        }
        craftProcedureEntityList = this.listByCraftId(craftId);
        craftProcedureEntityList = craftProcedureEntityList.stream()
                .filter(craftProcedureEntity -> craftProcedureEntity.getFacType().equals(name)).collect(Collectors.toList());
        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntityList) {
            // 获取附件信息
            List<ProcedureFileEntity> procedureFileEntities = procedureFileService.getEntityByProcedureIdAndCraftId(craftProcedureEntity.getId());
            if (!CollectionUtils.isEmpty(procedureFileEntities)) {
                procedureFileEntityList.addAll(procedureFileEntities);
            }
        }
        return procedureFileEntityList;
    }

    @Override
    public File getTransferTemplateFile() {
        FileOutputStream fos = null;
        try {
            //下载模板
            String url = fastDfsClientService.getUrlByFileCode(ReportFormConstant.PROCEDURE_TEMPLATE_NAME);
            byte[] bytes = fastDfsClientService.getFileStream(url);
            File file = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!file.getParentFile().exists()) {
                //文件夹不存在 生成
                file.getParentFile().mkdirs();
            }
            fos = new FileOutputStream(file);
            fos.write(bytes);
            return file;
        } catch (Exception e) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_DOWNLOAD_EXIST);
        } finally {
            IOUtils.closeQuietly(fos);
        }
    }

    @Override
    public InputStream exportCraftTemplate() throws Exception {
        String url = fastDfsClientService.getUrlByFileCode(ReportFormConstant.PROCEDURE_TEMPLATE_NAME);
        if (StringUtils.isNotBlank(url)) {
            byte[] fileStream = fastDfsClientService.getFileStream(url);
            //只需要导出导入数据的sheet且为xlsx格式
            OPCPackage open = OPCPackage.open(new ByteArrayInputStream(fileStream));
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(open);
            xssfWorkbook.removeSheetAt(0);  //删除配置说明
            xssfWorkbook.removeSheetAt(0);//删除目标数据
            xssfWorkbook.setWorkbookType(XSSFWorkbookType.XLSX);
            try (ByteArrayOutputStream dstStream = new ByteArrayOutputStream()) {
                xssfWorkbook.write(dstStream);
                byte[] bytes = dstStream.toByteArray();
                return new ByteArrayInputStream(bytes);
            }
        }
        //返回默认导入模板：没有转换公式的直接导入模板
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/craftDefaultTemplate.xlsx");
        return new BufferedInputStream(resource.getInputStream());
    }


    @Override
    public void importTemplateExcel(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName != null) {
            if (fileName.lastIndexOf(ExcelUtil.XLSX) >= 0) {
                fastDfsClientService.uploadFile(file, null, ReportFormConstant.PROCEDURE_TEMPLATE_NAME);
                return;
            }
            throw new ResponseException(RespCodeEnum.TEMPLATE_IS_ERROR_FORMAT);
        }
        throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
    }


    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(InputStream inputStream, String username, String fileName) {
        final String lockKey = RedisKeyPrefix.CRAFT_IMPORT_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        XSSFWorkbook templateWorkbook = null;
        Workbook importWorkbook = null;
        File transferFile = null;
        try {
            // 模板转换文件
            transferFile = this.getTransferTemplateFile();

            templateWorkbook = new XSSFWorkbook(transferFile);
            //1、读取配置数据
            ExcelTemplateSetEntity excelTemplateSetEntity = dealSetSheet(templateWorkbook);
            if (!excelTemplateSetEntity.getExcelType().equals(ReportFormConstant.LIST_MODE)) {
                throw new ResponseException("工艺导入" + excelTemplateSetEntity.getExcelType() + "方式暂未实现");
            }
            importProgressService.updateProgress(importProgressKey, "读取工艺工序数据中...", 0.2, false, null);
            List<CraftProcedureExcelDTO> craftProcedures = new ArrayList<>();
            int totalRows = 0;
            //2、加载导入数据
            importWorkbook = ExcelUtil.getWorkbook(inputStream);
            Sheet importSheet = importWorkbook.getSheetAt(0);
            int rowTotal = importSheet.getPhysicalNumberOfRows() - 1;
            for (int i = 2; i <= rowTotal; i++) {
                Row row = importSheet.getRow(i);
                if (Objects.isNull(row)) {
                    continue;
                }
                totalRows++;
                // 读取数据
                List<CraftProcedureExcelDTO> list = executeDataToTarget(importSheet.getRow(i), templateWorkbook, excelTemplateSetEntity);
                craftProcedures.addAll(list);
            }
            // 校验保存
            importProgressService.updateProgress(importProgressKey, "校验工艺工序数据中...", 0.4, false, null);
            List<CraftProcedureExcelDTO> canImportRecords = verifyFormatCraftProcedure(craftProcedures, false);
            importProgressService.updateProgress(importProgressKey, "保存工艺工序数据中...", 0.6, false, null);
            this.saveCraftProcedureImportDTO(canImportRecords, username, false);

            long successNumber = canImportRecords.stream().map(CraftProcedureExcelDTO::getCraftCode).distinct().count();

            // 验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(craftProcedures, CraftProcedureExcelDTO.class, username);
            //4、保存导入记录
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_IMPORT.getTypeName())
                    .createBy(username)
                    .createTime(new Date())
                    .failNumber((int) (totalRows - successNumber))
                    .successNumber((int) successNumber)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(username).build());
            importProgressService.updateProgress(importProgressKey, "成功" + successNumber + "条，" + "失败" + (totalRows - successNumber) + "条。", 1.0, true, importUrl);
        } catch (Exception e) {
            importProgressService.importProgressException(RedisKeyPrefix.CRAFT_IMPORT_PROGRESS, e);
            log.warn("excel导入数据错误", e);
        } finally {
            ExcelUtil.closeWorkBook(importWorkbook);
            ExcelUtil.closeWorkBook(templateWorkbook);
            FileUtils.deleteQuietly(transferFile);
            // 释放锁
            importProgressService.releaseLock(lockKey);
            IMPORT_UNICODE_MAP.remove();
        }
    }

    /**
     * 处理工艺列表模式导入数据
     */
    private List<CraftProcedureExcelDTO> executeDataToTarget(Row row, XSSFWorkbook templateWorkbook, ExcelTemplateSetEntity excelTemplateSetEntity) {
        //模板的原始数据sheet
        Sheet templateResourceSheet = templateWorkbook.getSheetAt(2);
        //第三行开始读取
        ExcelUtil.clearSheet(templateResourceSheet, 3, excelTemplateSetEntity.getColumnNum());
        Row templateResourceSheetRow = templateResourceSheet.getRow(2);
        if (templateResourceSheetRow == null) {
            templateResourceSheetRow = templateResourceSheet.createRow(2);
        }
        for (int k = 0; k < excelTemplateSetEntity.getColumnNum(); k++) {
            Cell cell = row.getCell(k);
            if (cell != null) {
                //复制单元格至模板
                ExcelUtil.copyCell(cell, templateResourceSheetRow.createCell(k));
            }
        }
        // 打开工作簿时应用程序是否应执行完全重新计算。
        templateWorkbook.setForceFormulaRecalculation(true);
        // 强行刷新单元格公式 计算公式保存结果，但不改变公式
        BaseFormulaEvaluator.evaluateAllFormulaCells(templateWorkbook);

        FormulaEvaluator formulaEvaluator = new XSSFFormulaEvaluator(templateWorkbook);
        XSSFRow xssfRow = templateWorkbook.getSheetAt(1).getRow(2);
        String craftCode = ExcelUtil.getStringCellValue(xssfRow.getCell(0), formulaEvaluator);
        String craftName = ExcelUtil.getStringCellValue(xssfRow.getCell(1), formulaEvaluator);
        String version = ExcelUtil.getStringCellValue(xssfRow.getCell(2), formulaEvaluator);
        String stateName = ExcelUtil.getStringCellValue(xssfRow.getCell(3), formulaEvaluator);
        String craftDes = ExcelUtil.getStringCellValue(xssfRow.getCell(4), formulaEvaluator);
        String typeName = ExcelUtil.getStringCellValue(xssfRow.getCell(5), formulaEvaluator);
        String controlStr = ExcelUtil.getStringCellValue(xssfRow.getCell(6), formulaEvaluator);
        String editorName = ExcelUtil.getStringCellValue(xssfRow.getCell(7), formulaEvaluator);
        String materialCode = ExcelUtil.getStringCellValue(xssfRow.getCell(8), formulaEvaluator);
        String materialName = ExcelUtil.getStringCellValue(xssfRow.getCell(9), formulaEvaluator);
        String updateTypeName = ExcelUtil.getStringCellValue(xssfRow.getCell(10), formulaEvaluator);
        String createTime = ExcelUtil.getStringCellValue(xssfRow.getCell(11), formulaEvaluator);

        List<CraftImportProcedureDTO> craftImportProcedureDTOS = new ArrayList<>();
        //boolean canImport = true;
        for (int i = 12; ; i = i + 7) {
            String procedureName = ExcelUtil.getStringCellValue(xssfRow.getCell(i), formulaEvaluator);
            String lineModelName = ExcelUtil.getStringCellValue(xssfRow.getCell(i + 1), formulaEvaluator);
            String facModelName = ExcelUtil.getStringCellValue(xssfRow.getCell(i + 2), formulaEvaluator);
            String workCentNames = ExcelUtil.getStringCellValue(xssfRow.getCell(i + 3), formulaEvaluator);
            String procedureAlias = ExcelUtil.getStringCellValue(xssfRow.getCell(i + 4), formulaEvaluator);
            String isOutsourcingStr = ExcelUtil.getStringCellValue(xssfRow.getCell(i + 5), formulaEvaluator);
            String outsourcingSupplierNames = ExcelUtil.getStringCellValue(xssfRow.getCell(i + 6), formulaEvaluator);
            if (StringUtils.isBlank(procedureName)) {
                break;
            }
            CraftImportProcedureDTO craftImportProcedureDTO = CraftImportProcedureDTO.builder()
                    .procedureName(procedureName)
                    .lineModelName(lineModelName)
                    .facModelName(facModelName)
                    .workCentNames(workCentNames)
                    .procedureAlias(procedureAlias)
                    .isOutsourcingStr(isOutsourcingStr)
                    .outsourcingSupplierNames(outsourcingSupplierNames)
                    .build();
            craftImportProcedureDTOS.add(craftImportProcedureDTO);
        }
        // 装换成能够处理的
        List<CraftProcedureExcelDTO> craftProcedures = new ArrayList<>();
        for (CraftImportProcedureDTO importProcedure : craftImportProcedureDTOS) {
            craftProcedures.add(CraftProcedureExcelDTO.builder()
                    .craftCode(craftCode)
                    .craftName(craftName)
                    .version(version)
                    .stateName(stateName)
                    .craftDesc(craftDes)
                    .typeName(typeName)
                    .procedureControllerCheckStr(controlStr)
                    .editorName(editorName)
                    .materialCode(materialCode)
                    .materialName(materialName)
                    .updateTypeName(updateTypeName)
                    .procedureName(importProcedure.getProcedureName())
                    .procedureAlias(importProcedure.getProcedureAlias())
                    .lineModelName(importProcedure.getLineModelName())
                    .facModelName(importProcedure.getFacModelName())
                    .workCenterNames(importProcedure.getWorkCentNames())
                    .isOutsourcingStr(importProcedure.getIsOutsourcingStr())
                    .outsourcingSupplierNames(importProcedure.getOutsourcingSupplierNames())
                    .createTime(Objects.nonNull(createTime) ? DateUtil.parse(createTime, DateUtil.DATETIME_FORMAT) : new Date())
                    .build());
        }
        return craftProcedures;
    }

    /**
     * 获取模板第一个sheet的配置数据
     */
    private ExcelTemplateSetEntity dealSetSheet(XSSFWorkbook templateWorkbook) {
        try {
            XSSFSheet xssfSheet = templateWorkbook.getSheetAt(0);
            //获取 BOM子树行数
            return ExcelTemplateSetEntity.builder()
                    .version(xssfSheet.getRow(0).getCell(1).getStringCellValue())
                    .excelType(xssfSheet.getRow(1).getCell(1).getStringCellValue())
                    .rowNum(((Double) xssfSheet.getRow(2).getCell(1).getNumericCellValue()).intValue())
                    .columnNum(((Double) xssfSheet.getRow(3).getCell(1).getNumericCellValue()).intValue())
                    .build();
        } catch (Exception e) {
            log.warn("配置说明获取错误", e);
            throw new ResponseException("配置说明获取错误");
        }
    }


    @Override
    public List<CraftProcedureEntity> getCraftProcedureListByLineModelId(Integer modelId) {
        LambdaQueryWrapper<CraftProcedureEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(CraftProcedureEntity::getLineModelId, modelId);
        return list(qw);
    }


    @Override
    public CraftProcedureEntity getEntityByCraftIdAndProcedureName(Integer craftId, String procedureName) {
        LambdaQueryWrapper<CraftProcedureEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CraftProcedureEntity::getCraftId, craftId)
                .eq(CraftProcedureEntity::getProcedureName, procedureName);
        return this.getOne(wrapper);
    }

    /**
     * 拼装日志
     *
     * @param filename
     * @param list
     */
    private String getLogString(String filename, List<CraftImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        String separator = "--";
        return list.stream().map(o -> (StringUtils.isBlank(o.getReason()) ? "OK" : "NOK")
                        + separator + filename
                        + separator + o.getSheetName()
                        + separator + (o.getRowIndex() + 1)
                        + separator + o.getCraftCode()
                        + (StringUtils.isBlank(o.getReason()) ? "" : separator + o.getReason()))
                .collect(Collectors.joining("\n"));
    }

    /**
     * 保存数据
     *
     * @param list
     * @param username
     */
    private void saveExcelList(List<CraftImportDTO> list, String username) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 获取工序定义id
        List<String> craftProcedureNames = list.stream().map(CraftImportDTO::getProcedureName).distinct().collect(Collectors.toList());
        Map<String, Integer> craftProcedureMap = procedureService.lambdaQuery().in(ProcedureEntity::getName, craftProcedureNames)
                .list().stream()
                .collect(Collectors.toMap(ProcedureEntity::getName, ProcedureEntity::getProcedureId));
        // 分组工艺工序
        Map<String, List<CraftImportDTO>> map = list.stream().collect(Collectors.groupingBy(CraftImportDTO::getCraftCode));
        LambdaQueryWrapper<CraftEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(CraftEntity::getCraftId, CraftEntity::getCraftCode, CraftEntity::getState)
                .in(CraftEntity::getCraftCode, map.keySet()).eq(CraftEntity::getIsTemplate, Constants.FALSE);
        List<CraftEntity> entities = craftService.list(wrapper);
        Map<String, CraftEntity> exists = entities.stream().collect(Collectors.toMap(CraftEntity::getCraftCode, o -> o));
        //过滤出已存在的
        List<String> existCodes = list.stream().filter(o -> exists.containsKey(o.getCraftCode()))
                .map(CraftImportDTO::getCraftCode).distinct().collect(Collectors.toList());
        Date now = new Date();
        for (String existCode : existCodes) {
            CraftEntity entity = exists.get(existCode);
            List<CraftImportDTO> dtos = map.get(existCode);
            if (CraftStateEnum.RELEASED.getCode() == entity.getState()) {
                dtos.forEach(o -> o.setReason("系统已存在该工艺且为生效状态,不可修改"));
                map.remove(existCode);
                continue;
            }
            CraftImportDTO dto = dtos.get(0);
            CraftEntity updateCraftEntity = CraftEntity.builder()
                    .craftId(entity.getCraftId())
                    .name(dto.getCraftName())
                    .state(CraftStateEnum.getCodeByName(dto.getStateName()))
                    .materialId(dto.getMaterialId())
                    .materialCode(dto.getMaterialCode())
                    .craftDesc(dto.getDescribe())
                    .updateBy(username)
                    .updateTime(now)
                    .craftCode(dto.getCraftCode())
                    .build();
            if (approveConfigService.getConfigByCode(ApproveModuleEnum.CRAFT.getCode())) {
                updateCraftEntity.setApprover(username);
                if (CraftStateEnum.CREATE.getName().equals(dto.getStateName())) {
                    updateCraftEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                } else {
                    updateCraftEntity.setApprovalStatus(ApprovalStatusEnum.APPROVED.getCode());
                    updateCraftEntity.setActualApprover(username);
                    updateCraftEntity.setApprovalTime(now);
                }
            }
            boolean update = craftService.updateById(updateCraftEntity);
            if (update) {
                // 物料有无生效状态的工艺 事件
                applicationContext.publishEvent(new MaterialHaveReleasedStateCraftEvent(this, updateCraftEntity));
                this.lambdaUpdate().eq(CraftProcedureEntity::getCraftId, entity.getCraftId()).remove();
                saveCraftProcedure(entity.getCraftId(), dtos, username, craftProcedureMap);
                // 刷新上下级工序
                sortNextProcedure(entity.getCraftId());
                // 工艺路线里绑定的工序需要绑定工序组
                craftService.bindCraftProcedureGroup(entity.getCraftId());
            }
            map.remove(existCode);
        }
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        Set<Map.Entry<String, List<CraftImportDTO>>> entrySet = map.entrySet();
        for (Map.Entry<String, List<CraftImportDTO>> entry : entrySet) {
            String craftCode = entry.getKey();
            List<CraftImportDTO> dtos = entry.getValue();
            CraftImportDTO dto = dtos.get(0);
            List<String> collect = dtos.stream().map(CraftImportDTO::getStateName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            Integer state = collect.contains(CraftStateEnum.CREATE.getName()) ? CraftStateEnum.CREATE.getCode() : CraftStateEnum.getCodeByName(dto.getStateName());
            CraftEntity build = CraftEntity.builder()
                    .craftCode(craftCode)
                    .name(dto.getCraftName())
                    .craftVersion(dto.getVersion())
                    .materialId(dto.getMaterialId())
                    .materialCode(dto.getMaterialCode())
                    .state(state)
                    .craftDesc(dto.getDescribe())
                    .createBy(username)
                    .updateBy(username)
                    .createTime(now)
                    .updateTime(now)
                    .isTemplate(Constants.FALSE)
                    .build();
            if (approveConfigService.getConfigByCode(ApproveModuleEnum.CRAFT.getCode())) {
                build.setApprover(username);
                if (CraftStateEnum.CREATE.getCode() == build.getState()) {
                    build.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                } else {
                    build.setApprovalStatus(ApprovalStatusEnum.APPROVED.getCode());
                    build.setActualApprover(username);
                    build.setApprovalTime(now);
                }
            }
            boolean save = craftService.save(build);
            // 物料有无生效状态的工艺 事件
            applicationContext.publishEvent(new MaterialHaveReleasedStateCraftEvent(this, build));
            if (save) {
                saveCraftProcedure(build.getCraftId(), dtos, username, craftProcedureMap);
                // 刷新上下级工序
                sortNextProcedure(build.getCraftId());
                // 工艺路线里绑定的工序需要绑定工序组
                craftService.bindCraftProcedureGroup(build.getCraftId());
            }
        }
    }

    /**
     * 保存关联关系
     *
     * @param craftId
     * @param dtos
     * @param username
     */
    private void saveCraftProcedure(Integer craftId, List<CraftImportDTO> dtos, String username, Map<String, Integer> craftProcedureMap) {
        dtos.sort(Comparator.comparing(CraftImportDTO::getSerialNumber, Comparator.nullsFirst(String::compareTo)));
        int size = dtos.size();
        Date now = new Date();
        ArrayList<CraftProcedureEntity> arrayList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            CraftImportDTO importDTO = dtos.get(i);
            CraftProcedureEntity build = CraftProcedureEntity.builder()
                    .craftId(craftId)
                    .procedureId(craftProcedureMap.get(importDTO.getProcedureName()))
                    .procedureName(importDTO.getProcedureName())
                    .facType(importDTO.getFacModelName())
                    .lineModelId(importDTO.getLineModelId())
                    .lineModelName(importDTO.getLineModelName())
                    .isLastProcedure(Constant.NO)
                    .whetherImport(true)
                    // 导入时为制造单元类型
                    .type(WorkCenterTypeEnum.LINE.getCode())
                    .createBy(username)
                    .updateBy(username)
                    .createTime(now)
                    .updateTime(now)
                    .build();
            if (i > 0) {
                CraftProcedureEntity entity = arrayList.get(i - 1);
                build.setSupProcedureId(entity.getId().toString());
                build.setSupProcedureName(entity.getProcedureName());
            }
            if (i == size - 1) {
                build.setIsLastProcedure(Constant.YES);
            }
            this.saveCraftProcedure(build);
            arrayList.add(build);
        }
    }

    /**
     * 校验数据
     *
     * @param list
     * @return
     */
    private List<CraftImportDTO> checkExcelList(List<CraftImportDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        //状态
        List<String> states = Arrays.asList(CraftStateEnum.CREATE.getName(), CraftStateEnum.RELEASED.getName());
        List<CraftImportDTO> collect = list.stream().filter(o -> {
            StringBuilder builder = new StringBuilder();
            boolean tag = true;
            String craftCode = o.getCraftCode();
            if (StringUtils.isBlank(craftCode)) {
                builder.append("工艺编号为空;");
                tag = false;
            } else if (!craftCode.matches("^[\\w.-]+$")) {
                builder.append("编号只能包含字母,数字,下划线_,短划线-,点.;");
                tag = false;
            }
            if (StringUtils.isBlank(o.getCraftName())) {
                builder.append("工艺名称为空;");
                tag = false;
            }
            if (StringUtils.isBlank(o.getMaterialCode())) {
                builder.append("物料编号为空;");
                tag = false;
            }
            if (StringUtils.isBlank(o.getMaterialName())) {
                builder.append("物料名称为空;");
                tag = false;
            }
            if (StringUtils.isBlank(o.getProcedureCode())) {
                builder.append("工序编号为空;");
                tag = false;
            }
            if (StringUtils.isBlank(o.getProcedureName())) {
                builder.append("工序名称为空;");
                tag = false;
            }
            if (StringUtils.isBlank(o.getLineModelName())) {
                builder.append("制造单元模型名称为空;");
                tag = false;
            }
            if (StringUtils.isBlank(o.getStateName())) {
                builder.append("状态名称为空;");
                tag = false;
            } else if (!states.contains(o.getStateName())) {
                builder.append("该状态名称有误;");
                tag = false;
            }
            o.setReason(builder.toString());
            return tag;
        }).collect(Collectors.toList());
        Map<String, List<CraftImportDTO>> listMap = collect.stream().collect(Collectors.groupingBy(CraftImportDTO::getCraftCode));
        Set<Map.Entry<String, List<CraftImportDTO>>> entrySet = listMap.entrySet();
        for (Map.Entry<String, List<CraftImportDTO>> entry : entrySet) {
            List<CraftImportDTO> dtos = entry.getValue();
            List<String> materialCodes = dtos.stream().map(CraftImportDTO::getMaterialCode).distinct().collect(Collectors.toList());
            if (materialCodes.size() > 1) {
                dtos.forEach(o -> o.setReason("该工艺对应了多个物料编号;"));
                continue;
            }
            List<String> materialNames = dtos.stream().map(CraftImportDTO::getMaterialName).distinct().collect(Collectors.toList());
            if (materialNames.size() > 1) {
                dtos.forEach(o -> o.setReason("该工艺对应了多个物料名称;"));
                continue;
            }
            MaterialEntity material = materialService.getSimpleMaterialByCode(materialCodes.get(0));
            if (material == null) {
                dtos.forEach(o -> o.setReason("物料不存在;"));
                continue;
            }
            if (!material.getName().equals(materialNames.get(0))) {
                dtos.forEach(o -> o.setReason("物料编号与物料名称不匹配;"));
                continue;
            }
            //工艺允许有多个相同的工序,但名称后缀以“xxx01”样式递增
            boolean notBreak = true;
            for (CraftImportDTO dto : dtos) {
                String procedureCode = dto.getProcedureCode();
                ProcedureEntity procedureEntity = procedureService.getProcedureByCode(procedureCode);
                if (procedureEntity == null || ProcedureStateEnum.RELEASED.getCode() != procedureEntity.getState()) {
                    dtos.forEach(o -> o.setReason("该工艺包含不存在或未生效的工序;"));
                    dto.setReason("该工序不存在或未生效;");
                    notBreak = false;
                    break;
                }
                if (!dto.getProcedureName().equals(procedureEntity.getName())) {
                    dtos.forEach(o -> o.setReason("该工艺存在工序编号与工序名称不匹配;"));
                    dto.setReason("该工序编号与工序名称不匹配;");
                    notBreak = false;
                    break;
                }
                //制造单元
                List<ModelEntity> lineModelList = procedureEntity.getLineModelList();
                if (!CollectionUtils.isEmpty(lineModelList)) {
                    Map<String, Integer> lineModelMap = lineModelList.stream().collect(Collectors.toMap(ModelEntity::getName, ModelEntity::getId, (o1, o2) -> o2));
                    if (lineModelMap.containsKey(dto.getLineModelName())) {
                        dto.setLineModelId(lineModelMap.get(dto.getLineModelName()));
                    } else {
                        dtos.forEach(o -> o.setReason("该工艺存在工序未关联对应产线模型;"));
                        dto.setReason("该工序未关联对应产线模型;");
                        notBreak = false;
                        break;
                    }
                }
                //工位
                List<ModelEntity> facModel = procedureEntity.getFacModel();
                if (StringUtils.isNotBlank(dto.getFacModelName()) && !CollectionUtils.isEmpty(facModel)) {
                    List<String> facNames = facModel.stream().map(ModelEntity::getName).collect(Collectors.toList());
                    if (!facNames.contains(dto.getFacModelName())) {
                        dtos.forEach(o -> o.setReason("该工艺存在工序未关联对应工位模型;"));
                        dto.setReason("该工序未关联对应工位模型;");
                        notBreak = false;
                        break;
                    }
                }
                dto.setMaterialId(material.getId());
                dto.setProcedureId(procedureEntity.getProcedureId());
            }
            if (!notBreak) {
                continue;
            }
            //处理名称
            Map<String, List<CraftImportDTO>> nameMap = dtos.stream().collect(Collectors.groupingBy(CraftImportDTO::getProcedureName));
            for (Map.Entry<String, List<CraftImportDTO>> listEntry : nameMap.entrySet()) {
                List<CraftImportDTO> value = listEntry.getValue();
                if (value.size() > 1) {
                    String procedureName = value.get(0).getProcedureName();
                    for (int i = 1; i < value.size(); i++) {
                        String str = String.format("%02d", i);
                        CraftImportDTO dto = value.get(i);
                        dto.setProcedureName(procedureName + str);
                    }
                }
            }
        }
        return collect.stream().filter(o -> StringUtils.isBlank(o.getReason())).collect(Collectors.toList());
    }


    /**
     * 将excel转换成dto列表
     *
     * @param sheet
     * @return
     */
    private List<CraftImportDTO> convertToDtos(Sheet sheet) {
        String sheetName = sheet.getSheetName();
        int lastRowNum = sheet.getLastRowNum();
        ArrayList<CraftImportDTO> list = new ArrayList<>();
        if (lastRowNum <= 0) {
            return list;
        }
        for (int i = 1; i < lastRowNum + 1; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                list.add(CraftImportDTO.builder().sheetName(sheetName).rowIndex(i).build());
                continue;
            }
            String craftCode = ExcelUtil.getCellStringValue(row.getCell(0));
            String craftName = ExcelUtil.getCellStringValue(row.getCell(1));
            String describe = ExcelUtil.getCellStringValue(row.getCell(2));
            String materialCode = ExcelUtil.getCellStringValue(row.getCell(3));
            String materialName = ExcelUtil.getCellStringValue(row.getCell(4));
            String version = ExcelUtil.getCellStringValue(row.getCell(5));
            String stateName = ExcelUtil.getCellStringValue(row.getCell(6));
            String typeName = ExcelUtil.getCellStringValue(row.getCell(7));
//            String procedureCode = ExcelUtil.getCellStringValue(row.getCell(5));
            String procedureName = ExcelUtil.getCellStringValue(row.getCell(8));
            String lineModelName = ExcelUtil.getCellStringValue(row.getCell(9));
            String facModelName = ExcelUtil.getCellStringValue(row.getCell(10));
            String isOutsourcing = ExcelUtil.getCellStringValue(row.getCell(11));
            String outsourcingSupplierName = ExcelUtil.getCellStringValue(row.getCell(12));
            // 通过工序名称获取工序编号
            ProcedureEntity procedureEntity = procedureService.lambdaQuery().eq(ProcedureEntity::getName, procedureName).one();
            list.add(CraftImportDTO.builder()
                    .craftCode(craftCode)
                    .craftName(craftName)
                    .materialCode(materialCode)
                    .materialName(materialName)
//                    .serialNumber(serialNumber)
                    .procedureCode(Objects.nonNull(procedureEntity) ? procedureEntity.getProcedureCode() : null)
                    .procedureName(procedureName)
                    .lineModelName(lineModelName)
                    .facModelName(facModelName)
                    .describe(describe)
                    .version(version)
                    .stateName(stateName)
                    .sheetName(sheetName)
                    .type(StringUtils.isNotBlank(typeName) ? CraftTypeEnum.getCodeByName(typeName) : CraftTypeEnum.NORMAL.getCode())
                    .isOutsourcing(isOutsourcing)
                    .outsourcingSupplierName(outsourcingSupplierName)
                    .rowIndex(i)
                    .build());
        }
        return list;
    }

    /**
     * 保存工艺工序
     */
    private void saveCraftProcedure(CraftProcedureEntity craftProcedureEntity) {
        this.save(craftProcedureEntity);
        //发送kafka消息
        messagePushToKafkaService.pushNewMessage(craftProcedureEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.CRAFT_PROCEDURE_ADD_MESSAGE);
        //添加工序控制配置
        procedureControllerConfigService.saveConfig(craftProcedureEntity.getId());
        // 添加工序检验方案控制配置
        craftProcedureInspectControllerService.saveConfig(craftProcedureEntity.getId());
    }

    /**
     * 获取工位下的工艺工序列表
     *
     * @param fid
     * @return
     */
    @Override
    public List<CraftProcedureEntity> getCraftProcedureListByFid(Integer fid, String productFlowCode, String relationNumber, Integer type) {
        WorkOrderEntity workOrderEntity;
        if (StringUtils.isNotBlank(relationNumber)) {
            if (type != null && ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == type) {
                workOrderEntity = productFlowCodeService.getWorkOderByOrderNumberAndFid(relationNumber, fid);
            } else {
                return getCraftProcedureEntitiesByMaterialFid(fid, relationNumber);
            }
        } else {
            workOrderEntity = productFlowCodeService.getByProductFlowCodeAndFid(productFlowCode, fid);
        }
        if (workOrderEntity == null) {
            throw new ResponseException("无对应生产工单");
        }
        return getCraftProcedureEntitiesByMaterialFid(fid, workOrderEntity.getWorkOrderNumber());
    }

    /**
     * 获取工位下工单可选的工艺工序列表
     *
     * @param fid
     * @return
     */
    @Override
    public List<CraftProcedureEntity> getCraftProcedureListByFidAndWorkOrder(Integer fid, String workOrderNumber) {
        return getCraftProcedureEntitiesByMaterialFid(fid, workOrderNumber);
    }

    @Override
    public List<CraftProcedureEntity> getCraftProcedureEntitiesByMaterialFid(Integer fid, String workOrderNumber) {
        CraftEntity craftEntity = craftService.getById(workOrderService.getCraftIdByWorkOrderNumber(workOrderNumber));
        if (craftEntity == null) {
            log.error("条码物料解析不到对应工艺");
            return new ArrayList<>();
        }
        ModelEntity modelEntity = modelService.getModelEntityByFid(fid);
        // 根据工位id查询对应产线
        ProductionLineEntity productionLineEntity = productionLineService.getLineEntityByFid(fid);
        if (productionLineEntity == null) {
            log.error("解析不到工位对应制造单元");
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CraftProcedureEntity> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper1.eq(CraftProcedureEntity::getCraftId, craftEntity.getCraftId())
                .eq(CraftProcedureEntity::getFacType, modelEntity.getName())
                .eq(CraftProcedureEntity::getLineModelId, productionLineEntity.getModelId());
        List<CraftProcedureEntity> list = this.list(lambdaQueryWrapper1);
        // 展示工序编码
        showProcedureCode(list);
        return list;
    }

    @Override
    public List<CraftProcedureEntity> getEntityByCraftIdAndFacType(Integer craftId, String facTypeName) {
        LambdaQueryWrapper<CraftProcedureEntity> craftProcedureWrapper = new LambdaQueryWrapper<>();
        craftProcedureWrapper.eq(CraftProcedureEntity::getCraftId, craftId);
        craftProcedureWrapper.eq(CraftProcedureEntity::getFacType, facTypeName);
        return this.list(craftProcedureWrapper);
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importMaterialUsedExcel(String fileName, InputStream inputStream, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_IMPORT_MATERIAL_USED_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_MATERIAL_USED_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureMaterialUsedExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, CraftProcedureMaterialUsedExcelDTO.class, 1, 2);
            //2、校验数据
            List<CraftProcedureMaterialUsedExcelDTO> canImportRecords = verifyFormatMaterialUsed(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureMaterialUsedExcelDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureMaterialUsedExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_MATERIAL_USED.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_MATERIAL_USED.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            for (CraftProcedureMaterialUsedExcelDTO insertImport : canImportRecords) {
                saveCpMaterialUseds(insertImport);
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureMaterialUsedExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("用料 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    private List<CraftProcedureMaterialUsedExcelDTO> verifyFormatMaterialUsed(List<CraftProcedureMaterialUsedExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureMaterialUsedExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureMaterialUsedExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureMaterialUsedExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }
            // 物料编码、物料名称
            if (StringUtils.isBlank(importDTO.getMaterialCode())) {
                importResult.append("物料编码不能为空；");
                canImport = false;
            } else {
                // 物料逻辑
                MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(importDTO.getMaterialCode());
                if (Objects.isNull(materialEntity)) {
                    importResult.append("物料编码对应的在系统中不存在；");
                    canImport = false;
                } else {
                    if (StringUtils.isNotBlank(importDTO.getMaterialName()) && !materialEntity.getName().equals(importDTO.getMaterialName())) {
                        importResult.append("物料名称与物料编号不对应；");
                        canImport = false;
                    } else {
                        importDTO.setMaterialName(materialEntity.getName());
                    }
                    importDTO.setUnit(materialEntity.getComp());
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private List<CraftProcedureMaterialExcelDTO> verifyFormatMaterial(List<CraftProcedureMaterialExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureMaterialExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }
        Map<String, List<MaterialEntity>> materialCodeBomMaterialsMap = new HashMap<>();
        Map<String, MaterialEntity> materialMap = new HashMap<>();

        List<CraftProcedureMaterialExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureMaterialExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;
            // 工艺的物料编码
            String craftMaterialCode = null;
            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    craftMaterialCode = craftProcedureExcel.getMaterialCode();
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                        //工艺关联的物料编码
                        craftMaterialCode = crafts.get(0).getMaterialCode();
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }
            // 物料编码、物料名称
            if (StringUtils.isBlank(importDTO.getMaterialCode())) {
                importResult.append("物料编码不能为空；");
                canImport = false;
            } else {
                // bom物料逻辑
                if (StringUtils.isNotBlank(craftMaterialCode)) {
                    List<MaterialEntity> bomMaterials = materialCodeBomMaterialsMap.getOrDefault(craftMaterialCode, Collections.emptyList());
                    if (CollectionUtils.isEmpty(bomMaterials)) {
                        CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                        // 查询多级BOM(包含工艺物料本身)
                        BomEntity bomEntity;
                        if (Objects.nonNull(craftProcedureExcel)) {
                            CraftEntity craftEntity = craftService.lambdaQuery().eq(CraftEntity::getCraftCode, craftProcedureExcel.getCraftCode()).one();
                            bomEntity = craftService.getProcedureMaterialByMaterialCode(craftMaterialCode, 0, craftEntity.getProductionState());
                        } else {
                            bomEntity = craftService.getProcedureMaterialByMaterialCode(craftMaterialCode, 0, ProductionStateEnum.MASS_PRODUCTION.getCode());
                        }
                        if (Objects.nonNull(bomEntity)) {
                            List<String> codes = bomEntity.getBomRawMaterialEntities().stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
                            Map<String, MaterialEntity> map = materialService.lambdaQuery()
                                    .select(MaterialEntity::getCode, MaterialEntity::getName, MaterialEntity::getComp)
                                    .in(MaterialEntity::getCode, codes)
                                    .list().stream()
                                    .collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
                            materialMap.putAll(map);
                            bomMaterials = bomEntity.getBomRawMaterialEntities().stream().map(o ->
                                            MaterialEntity.builder()
                                                    .code(o.getCode())
                                                    .name(materialMap.get(o.getCode()).getName())
                                                    .comp(materialMap.get(o.getCode()).getComp())
                                                    .build())
                                    .collect(Collectors.toList());
                            materialCodeBomMaterialsMap.put(craftMaterialCode, bomMaterials);
                        }
                    }
                    Map<String, MaterialEntity> codeMaterialMap = bomMaterials.stream().collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
                    MaterialEntity materialEntity = codeMaterialMap.get(importDTO.getMaterialCode());
                    if (Objects.isNull(materialEntity)) {
                        importResult.append("物料编码必须是生效状态下工艺编码关联的多级BOM中的物料；");
                        canImport = false;
                    } else {
                        if (StringUtils.isNotBlank(importDTO.getMaterialName()) && !materialEntity.getName().equals(importDTO.getMaterialName())) {
                            importResult.append("物料名称与物料编号不对应；");
                            canImport = false;
                        } else {
                            importDTO.setMaterialName(materialEntity.getName());
                        }
                    }
                    //校验物料是否来自对应工艺关联的产品的BOM的物料
                    if (!codeMaterialMap.containsKey(importDTO.getMaterialCode())) {
                        importResult.append("物料编码必须是生效状态下工艺编码关联的多级BOM中的物料；");
                        canImport = false;
                    }
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private void saveCpMaterialUseds(CraftProcedureMaterialUsedExcelDTO insertImport) {
        ProcedureMaterialUsedEntity build = ProcedureMaterialUsedEntity.builder()
                .craftId(insertImport.getCraftId())
                .procedureId(insertImport.getCraftProcedureId())
                .materialCode(insertImport.getMaterialCode())
                .materialName(insertImport.getMaterialName())
                .number(insertImport.getNumber())
                .unit(insertImport.getUnit())
                .dataSource(CraftProcedureMaterialDataSourceEnum.MATERIAL.getCode())
                .build();
        ProcedureMaterialUsedEntity procedureMaterial = procedureMaterialUsedService.lambdaQuery()
                .eq(ProcedureMaterialUsedEntity::getProcedureId, insertImport.getCraftProcedureId())
                .eq(ProcedureMaterialUsedEntity::getMaterialCode, insertImport.getMaterialCode())
                .last("limit 1").one();
        if (Objects.isNull(procedureMaterial)) {
            procedureMaterialUsedService.save(build);
        } else {
            build.setId(procedureMaterial.getId());
            procedureMaterialUsedService.updateById(build);
        }
    }

    private void saveCpMaterials(CraftProcedureMaterialExcelDTO insertImport) {
        ProcedureMaterialEntity build = ProcedureMaterialEntity.builder()
                .craftId(insertImport.getCraftId())
                .procedureId(insertImport.getCraftProcedureId())
                .materialCode(insertImport.getMaterialCode())
                .materialName(insertImport.getMaterialName())
                .build();
        ProcedureMaterialEntity procedureMaterial = procedureMaterialService.lambdaQuery()
                .eq(ProcedureMaterialEntity::getProcedureId, insertImport.getCraftProcedureId())
                .eq(ProcedureMaterialEntity::getMaterialCode, insertImport.getMaterialCode())
                .last("limit 1").one();
        if (Objects.isNull(procedureMaterial)) {
            procedureMaterialService.save(build);
        } else {
            build.setId(procedureMaterial.getId());
            procedureMaterialService.updateById(build);
        }
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFileExcel(String fileName, List<FileModel> unzipFileList, FileModel excelFile, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_PROCEDURE_FILE_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_FILE_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);

        try (InputStream excelInputStream = excelFile.getFile().getInputStream();) {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureFileImportDTO> totalImportRecords = EasyExcelUtil.read(excelInputStream, CraftProcedureFileImportDTO.class, 1, 2);
            //2、校验数据
            List<CraftProcedureFileImportDTO> canImportRecords = verifyFormatFile(totalImportRecords, unzipFileList);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureFileImportDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureFileImportDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_FILE.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_FILE.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            // 新增
            for (CraftProcedureFileImportDTO insertImport : canImportRecords) {
                saveImportProcedureFile(insertImport, operationUsername);
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureFileImportDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("附件 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            importProgressService.releaseLock(lockKey);
            kafkaWebSocketPublisher.sendMessage(TopicEnum.CRAFT_PROCEDURE_TOPIC.getTopic(), MessageContent.builder()
                    .time(new Date())
                    .message("工艺工序附件批量导入, 请知悉")
                    .build());
        }
    }

    private List<CraftProcedureFileImportDTO> verifyFormatFile(List<CraftProcedureFileImportDTO> imports, List<FileModel> unzipFileList) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureFileImportDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
            if (!CollectionUtils.isEmpty(craftEntities)) {
                List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                craftIdProceduresMap = this.lambdaQuery()
                        .in(CraftProcedureEntity::getCraftId, craftIds)
                        .list()
                        .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
            }
        }

        List<CraftProcedureFileImportDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureFileImportDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 工艺编码
            if (StringUtils.isBlank(importDTO.getCraftCode())) {
                importResult.append("工艺编码不能为空；");
                canImport = false;
            } else {
                List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(crafts)) {
                    importResult.append("工艺编码必须在系统中存在；");
                    canImport = false;
                } else {
                    importDTO.setCraftId(crafts.get(0).getCraftId());
                }
            }
            // 工序名称
            if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                importResult.append("工序名称不能为空；");
                canImport = false;
            } else {
                List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                        .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(craftProcedures)) {
                    importResult.append("工序必须在工艺编码对应的工艺工序中；");
                    canImport = false;
                } else {
                    if (craftProcedures.size() > 1) {
                        importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                        canImport = false;
                    } else {
                        importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                    }
                }
            }
            // 编制人
            if (StringUtils.isNotBlank(importDTO.getEditorName())) {
                SysUserEntity user = sysUserService.lambdaQuery()
                        .eq(SysUserEntity::getNickname, importDTO.getEditorName())
                        .last("limit 1").one();
                if (Objects.isNull(user)) {
                    importResult.append("编制人必须在系统中存在；");
                    canImport = false;
                } else {
                    importDTO.setEditor(user.getUsername());
                }
            }

            // 校验文件是否在zip文件中存在
            if (StringUtils.isBlank(importDTO.getFileName())) {
                importResult.append("附件名称不能为空；");
                canImport = false;
            } else {
                List<FileModel> filterFiles = unzipFileList.stream().filter(res -> res.getFileName().equals(importDTO.getFileName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterFiles)) {
                    importResult.append("附件名称对应的文件必须在zip文件中存在；");
                    canImport = false;
                } else {
                    importDTO.setFileModel(filterFiles.get(0));
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private void saveImportProcedureFile(CraftProcedureFileImportDTO insertImport, String username) {
        FileModel fileModel = insertImport.getFileModel();
        //上传文件
        UploadEntity upload = fastDfsClientService.uploadFile(fileModel.getFile(), username);
        Date nowDate = new Date();
        // 新增工序关联的附件信息
        ProcedureFileEntity build = ProcedureFileEntity.builder()
                .fileUrl(upload.getUrl())
                .name(fileModel.getFileName())
                .procedureId(insertImport.getCraftProcedureId())
                .craftId(insertImport.getCraftId())
                .editor(StringUtils.isBlank(insertImport.getEditor()) ? username : insertImport.getEditor())
                .createBy(username)
                .createTime(Objects.nonNull(insertImport.getCreateTime()) ? insertImport.getCreateTime() : new Date())
                .build();
        // 如果同一个工序存在上传相同文件名的文件,则覆盖
        ProcedureFileEntity oldProcedureFile = procedureFileService.lambdaQuery()
                .eq(ProcedureFileEntity::getProcedureId, insertImport.getCraftProcedureId())
                .eq(ProcedureFileEntity::getName, fileModel.getFileName())
                .last("limit 1").one();
        if (Objects.isNull(oldProcedureFile)) {
            procedureFileService.save(build);
            //标记上传文件
            uploadService.markUploadFile(build.getFileUrl(), build);
        } else {
            build.setId(oldProcedureFile.getId());
            build.setUpdateBy(username);
            build.setUpdateTime(nowDate);
            procedureFileService.updateById(build);
            // 标记上传文件，并标记原来的文件失效
            uploadService.markUploadFile(oldProcedureFile.getFileUrl(), build.getFileUrl(), build);
        }
    }


    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importInspectExcel(String fileName, InputStream inputStream, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_PROCEDURE_INSPECT_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_INSPECT_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureInspectExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, CraftProcedureInspectExcelDTO.class, 1, 2);
            //2、校验数据
            List<CraftProcedureInspectExcelDTO> canImportRecords = verifyFormatInspect(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureInspectExcelDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureInspectExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_INSPECT.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_INSPECT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            // 新增
            for (CraftProcedureInspectExcelDTO insertImport : canImportRecords) {
                saveProcedureInspects(insertImport, operationUsername);

                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureInspectExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("检验项 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    private List<CraftProcedureInspectExcelDTO> verifyFormatInspect(List<CraftProcedureInspectExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureInspectExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }
        // 查询检验项
        List<String> inspectCodes = imports.stream().map(CraftProcedureInspectExcelDTO::getInspectCode).collect(Collectors.toList());
        List<ProcedureInspectionEntity> inspections = new ArrayList<>();
        if (!CollectionUtils.isEmpty(inspectCodes)) {
            inspections = procedureInspectionService.lambdaQuery()
                    .in(ProcedureInspectionEntity::getInspectionCode, inspectCodes)
                    .list();
        }

        List<CraftProcedureInspectExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        // 系统中所有单位
        List<String> allUnitList = dictService.lambdaQuery().eq(DictEntity::getType, DictTypeEnum.UNIT.getType()).list()
                .stream().map(DictEntity::getName).collect(Collectors.toList());
        for (CraftProcedureInspectExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }

            // 比较符
            List<String> inspectNames = InspectComparatorEnum.getNames();
            if (!inspectNames.contains(importDTO.getComparatorName())) {
                importResult.append("比较符输入错误，只能是（大于/小于/大于等于/小于等于/范围内/范围外/相等/不等/包含在内/不包含在内/等于）；");
                canImport = false;
            }
            // 上限不能小于下限
            if (NumberUtil.isNumber(importDTO.getUpperLimit()) && NumberUtil.isNumber(importDTO.getDownLimit()) && Double.parseDouble(importDTO.getUpperLimit()) < Double.parseDouble(importDTO.getDownLimit())) {
                importResult.append("上限不能小于下限；");
                canImport = false;
            }
            // 检验类型
            if (StringUtils.isNotBlank(importDTO.getInspectTypeNames())) {
                List<String> inspectTypeNames = Arrays.stream(importDTO.getInspectTypeNames().split(Constants.SEP)).distinct().collect(Collectors.toList());
                List<InspectItemTypeEnum> itemTypeEnums = Arrays.stream(InspectItemTypeEnum.values()).filter(res -> inspectTypeNames.contains(res.getName())).collect(Collectors.toList());
                if (itemTypeEnums.size() != inspectTypeNames.size()) {
                    importResult.append("检验类型填写有误；");
                    canImport = false;
                } else {
                    importDTO.setInspectTypes(itemTypeEnums.stream().map(InspectItemTypeEnum::getCode).collect(Collectors.joining(Constants.SEP)));
                }
            }
            // NG控制项校验
            importDTO.setNgController(StringUtils.isNotBlank(importDTO.getNgControllerStr()) && Constant.TRUE.equals(importDTO.getNgControllerStr()));
            // 单位校验
            if (StringUtils.isNotBlank(importDTO.getUnit()) && !allUnitList.contains(importDTO.getUnit())) {
                importResult.append("单位必须在系统中存在；");
                canImport = false;
            }
            // 是否关联不良
            importDTO.setIsDefect(StringUtils.isNotBlank(importDTO.getIsDefectStr()) && Constant.TRUE.equals(importDTO.getIsDefectStr()));
            // 校验检验项目，检验代号
            if (StringUtils.isBlank(importDTO.getInspectCode())) {
                importResult.append("检验代号不能为空；");
                canImport = false;
            } else if (StringUtils.isBlank(importDTO.getIsCheckByProcedureInspectDefine()) || importDTO.getIsCheckByProcedureInspectDefine().equals(Constant.TRUE)) {
                List<ProcedureInspectionEntity> list = inspections.stream().filter(res -> res.getInspectionCode().equals(importDTO.getInspectCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    importResult.append("检验代号必须在工序检验项定义中存在；");
                    canImport = false;
                } else {
                    if (StringUtils.isNotBlank(importDTO.getInspectionName()) && !importDTO.getInspectionName().equals(list.get(0).getInspectionName())) {
                        importResult.append("检验项目名称与检验代号对应项目名称不一致；");
                        canImport = false;
                    }
                    // 补充信息
                    importDTO.setInspectId(list.get(0).getId());
                    importDTO.setInspectionStandard(list.get(0).getInspectionStandard());
                    importDTO.setInspectionInstrument(list.get(0).getInspectionInstrument());
                    importDTO.setDataType(list.get(0).getDataType());
                }
            }
            // 仅在不进行工序检验项定义校验时有效，且该`数据字段`字段需要必填
            if (StringUtils.isNotBlank(importDTO.getIsCheckByProcedureInspectDefine()) && importDTO.getIsCheckByProcedureInspectDefine().equals(Constant.FALSE)) {
                if (StringUtils.isBlank(importDTO.getDataType())) {
                    importResult.append("当不进行工序检验项定义校验时，数据类型需要必填；");
                    canImport = false;
                }
                String code = DataTypeEnum.getCodeByName(importDTO.getDataType());
                if (StringUtils.isBlank(code)) {
                    importResult.append("数据类型只能是数值型/字符型/布尔型；");
                    canImport = false;
                }
                importDTO.setDataType(code);
            }
            // 不良判断类型
            if (StringUtils.isNotBlank(importDTO.getDefectJudgeTypeStr())) {
                String code = InspectDefectJudgeEnum.getCodeByName(importDTO.getDefectJudgeTypeStr());
                if (StringUtils.isBlank(code)) {
                    importResult.append("不良判断类型填写有误，只能是布尔判断或者范围判断；");
                    canImport = false;
                }
                // 除了数值型存在布尔类型和范围类型，其他只有范围类型
                if (!importDTO.getDataType().equals(DataTypeEnum.NUMERICAL_TYPE.getFieldEname())) {
                    importDTO.setDefectJudgeType(null);
                } else {
                    importDTO.setDefectJudgeType(InspectDefectJudgeEnum.getCodeByName(importDTO.getDefectJudgeTypeStr()));
                }
            }
            // 判断规则
            importDTO.setResultUpdate(StringUtils.isNotBlank(importDTO.getResultUpdateStr()) && Constant.ARTIFICIAL_JUDGE.equals(importDTO.getResultUpdateStr()));
            // 工序间互检
            importDTO.setValueCheck(StringUtils.isNotBlank(importDTO.getValueCheckStr()) && Constant.TRUE.equals(importDTO.getValueCheckStr()));

            if (StringUtils.isBlank(importDTO.getStandardValue())) {
                importResult.append("标准值不能为空；");
                canImport = false;
            }
            if (StringUtils.isBlank(importDTO.getInspectionName())) {
                importResult.append("检验名称不能为空；");
                canImport = false;
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importControlExcel(String fileName, InputStream inputStream, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_PROCEDURE_CONTROL_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_CONTROL_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureControlExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, CraftProcedureControlExcelDTO.class, 1, 2);
            //2、校验数据
            List<CraftProcedureControlExcelDTO> canImportRecords = verifyFormatControl(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureControlExcelDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureControlExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_CONTROL.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_CONTROL.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            // 新增
            for (CraftProcedureControlExcelDTO insertImport : canImportRecords) {
                saveProcedureControls(insertImport);

                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureControlExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("控制 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    private List<CraftProcedureTempMaterialExcelDTO> verifyFormatCraftTempMaterial(List<CraftProcedureTempMaterialExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureTempMaterialExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureTempMaterialExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureTempMaterialExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 工艺编码
            if (StringUtils.isBlank(importDTO.getCraftCode())) {
                importResult.append("工艺编码不能为空；");
                canImport = false;
            } else {
                List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(crafts)) {
                    importResult.append("工艺编码必须在系统中存在；");
                    canImport = false;
                }
            }
            // 物料编码、物料名称
            if (StringUtils.isBlank(importDTO.getMaterialCode())) {
                importResult.append("物料编码不能为空；");
                canImport = false;
            } else {
                // 物料逻辑
                MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(importDTO.getMaterialCode());
                if (Objects.isNull(materialEntity)) {
                    importResult.append("物料编码对应的在系统中不存在；");
                    canImport = false;
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private List<CraftProcedureControlExcelDTO> verifyFormatControl(List<CraftProcedureControlExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureControlExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureControlExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureControlExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;
            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                    // 如果为最后一道工序，换算系数只能为1
                    if (Constant.YES.equals(craftProcedureExcel.getIsLastProcedure())
                            && Objects.nonNull(importDTO.getConversionFactor())
                            && !importDTO.getConversionFactor().equals(1.0)) {
                        importResult.append("最后一道工艺的换算系数必须为1；");
                        canImport = false;
                    }
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                        // 如果为最后一道工序，换算系数只能为1
                        if (Constant.YES.equals(craftProcedures.get(0).getIsLastProcedure())
                                && Objects.nonNull(importDTO.getConversionFactor())
                                && !importDTO.getConversionFactor().equals(1.0)) {
                            importResult.append("最后一道工艺的换算系数必须为1；");
                            canImport = false;
                        }
                    }
                }
            }

            // 工序流转条件类型
            if (StringUtils.isNotEmpty(importDTO.getStandardCirculationDurationTypeName())
                    && Objects.isNull(ProcedureFlowTypeEnum.getCodeByName(importDTO.getStandardCirculationDurationTypeName()))) {
                importResult.append("工序流转条件类型填写有误；");
                canImport = false;
            } else {
                importDTO.setStandardCirculationDurationType(StringUtils.isBlank(importDTO.getStandardCirculationDurationTypeName()) ? ProcedureFlowTypeEnum.TIME.getCode() : ProcedureFlowTypeEnum.getCodeByName(importDTO.getStandardCirculationDurationTypeName()));
            }
            // 工序检查项
            if (StringUtils.isNotBlank(importDTO.getProcedureInspect())) {
                List<String> procedureInspects = Arrays.stream(importDTO.getProcedureInspect().split(Constants.SEP)).distinct().collect(Collectors.toList());
                List<ControlItemEnum> list = Arrays.stream(ControlItemEnum.values()).filter(res -> procedureInspects.contains(res.getName())).collect(Collectors.toList());
                if (list.size() != procedureInspects.size()) {
                    importResult.append("工序检查项填写有误；");
                    canImport = false;
                } else {
                    importDTO.setJumpStationCheck(procedureInspects.contains(ControlItemEnum.JUMP_STATION_CHECK.getName()));
                    importDTO.setReformCheck(procedureInspects.contains(ControlItemEnum.REFORM_CHECK.getName()));
                    importDTO.setMaterialCheck(procedureInspects.contains(ControlItemEnum.MATERIAL_CHECK.getName()));
                    importDTO.setProductionCheck(procedureInspects.contains(ControlItemEnum.PRODUCTION_CHECK.getName()));
                    importDTO.setLastValueCheck(procedureInspects.contains(ControlItemEnum.LAST_VALUE_CHECK.getName()));
                }
            }
            // 生产超时阈值配置单位类型、流转超时阈值配置单位类型
            if (StringUtils.isNotEmpty(importDTO.getProductTimeoutThresholdConfUnitTypeName())
                    && Objects.isNull(TimeoutThresholdTypeEnum.getCodeByName(importDTO.getProductTimeoutThresholdConfUnitTypeName()))) {
                importResult.append("生产超时阈值配置单位类型填写有误；");
                canImport = false;
            } else {
                importDTO.setProductTimeoutThresholdConfUnitTypeCode(StringUtils.isBlank(importDTO.getProductTimeoutThresholdConfUnitTypeName()) ? TimeoutThresholdTypeEnum.FIXED_VALUE.getCode() : TimeoutThresholdTypeEnum.getCodeByName(importDTO.getProductTimeoutThresholdConfUnitTypeName()));
            }
            if (StringUtils.isNotEmpty(importDTO.getFlowTimeoutThresholdConfUnitTypeName())
                    && Objects.isNull(TimeoutThresholdTypeEnum.getCodeByName(importDTO.getFlowTimeoutThresholdConfUnitTypeName()))) {
                importResult.append("流转超时阈值配置单位类型填写有误；");
                canImport = false;
            } else {
                importDTO.setFlowTimeoutThresholdConfUnitTypeCode(StringUtils.isBlank(importDTO.getFlowTimeoutThresholdConfUnitTypeName()) ? TimeoutThresholdTypeEnum.FIXED_VALUE.getCode() : TimeoutThresholdTypeEnum.getCodeByName(importDTO.getFlowTimeoutThresholdConfUnitTypeName()));
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    /**
     * 校验工艺工序控制数据
     *
     * @param dto
     * @param index
     * @param stringBuilder
     * @param
     * @return
     */
    private boolean verifyCartProcedureControlData(CraftProcedureControlImportDTO dto, int index, StringBuilder stringBuilder) {
        /*
        stringBuilder.append(String.format("导入第%s条目标数据;\n\t", index)).append("校验工艺工序控制字段结果：");
        boolean canImport = true;
        if (dto == null) {
            stringBuilder.append("\n");
            stringBuilder.append("导入结果：").append("NOK；").append("\n");
            return false;
        }
        if (StringUtils.isBlank(dto.getCraftCode())) {
            stringBuilder.append("工艺编号字段为空；");
            canImport = false;
        }
        CraftEntity craftEntity = craftService.getCraftEntityByCraftCode(dto.getCraftCode());
        if (craftEntity == null) {
            stringBuilder.append("工艺编号在系统中未找到；");
            canImport = false;
        }
        if (StringUtils.isBlank(dto.getProcedureName())) {
            stringBuilder.append("工序名称字段为空；");
            canImport = false;
        }
        ProcedureEntity procedureEntity = procedureService.getProcedureByName(dto.getProcedureName());
        if (procedureEntity == null) {
            stringBuilder.append("工序名称在系统中未找到；");
            canImport = false;
        }
        if (craftEntity != null && procedureEntity != null) {
            CraftProcedureEntity craftProcedureEntity = getEntityByCraftIdAndProcedureName(craftEntity.getCraftId(), procedureEntity.getName());
            if (craftProcedureEntity == null) {
                stringBuilder.append("工艺编号与该工艺名称未绑定；");
                canImport = false;
            }
        }
        if (dto.getIncreaseProductionVolume() == null) {
            stringBuilder.append("生产提前量字段不合法；");
            canImport = false;
        }
        if (StringUtils.isBlank(dto.getJumpStationCheck())) {
            stringBuilder.append("跳站检查字段为空；");
            canImport = false;
        }
        if (!(Constant.Y.equals(dto.getJumpStationCheck()) || Constant.N.equals(dto.getJumpStationCheck()))) {
            stringBuilder.append("跳站检查字段不合法；");
            canImport = false;
        }
        if (StringUtils.isBlank(dto.getReformCheck())) {
            stringBuilder.append("重做检查字段为空；");
            canImport = false;
        }
        if (!(Constant.Y.equals(dto.getReformCheck()) || Constant.N.equals(dto.getReformCheck()))) {
            stringBuilder.append("重做检查字段不合法；");
            canImport = false;
        }
        if (StringUtils.isBlank(dto.getMaterialCheck())) {
            stringBuilder.append("物料检查字段为空；");
            canImport = false;
        }
        if (!(Constant.Y.equals(dto.getMaterialCheck()) || Constant.N.equals(dto.getMaterialCheck()))) {
            stringBuilder.append("物料检查字段不合法；");
            canImport = false;
        }
        if (StringUtils.isBlank(dto.getProductionCheck())) {
            stringBuilder.append("投产检查字段为空；");
            canImport = false;
        }
        if (!(Constant.Y.equals(dto.getProductionCheck()) || Constant.N.equals(dto.getProductionCheck()))) {
            stringBuilder.append("投产检查字段不合法；");
            canImport = false;
        }
        if (StringUtils.isBlank(dto.getCraftProcedureCheck())) {
            stringBuilder.append("工艺工序检查字段为空；");
            canImport = false;
        }
        if (!(Constant.Y.equals(dto.getCraftProcedureCheck()) || Constant.N.equals(dto.getCraftProcedureCheck()))) {
            stringBuilder.append("工艺工序检查字段不合法；");
            canImport = false;
        }
        stringBuilder.append("\n");
        stringBuilder.append("导入结果：").append(canImport ? "OK；" : "NOK；").append("\n");
        return canImport;
         */
        return true;
    }

    /**
     * 保存工艺工序控制数据
     *
     * @param dto
     * @param username
     */
    private void saveCartProcedureControlList(CraftProcedureControlImportDTO dto, String username) {
        Date now = new Date();
        CraftEntity craftEntity = craftService.getCraftEntityByCraftCode(dto.getCraftCode());
        CraftProcedureEntity craftProcedureEntity = getEntityByCraftIdAndProcedureName(craftEntity.getCraftId(), dto.getProcedureName());
        ProcedureControllerConfigEntity configEntity = procedureControllerConfigService.getEntityByCraftProcedureId(craftProcedureEntity.getId());
        if (configEntity != null) {
            procedureControllerConfigService.removeById(configEntity);
        }
        ProcedureControllerConfigEntity entity = ProcedureControllerConfigEntity.builder()
                .craftProcedureId(craftProcedureEntity.getId())
                .increaseProductionVolume(dto.getIncreaseProductionVolume())
                .jumpStationCheck(Constant.Y.equals(dto.getJumpStationCheck()))
                .productionCheck(Constant.Y.equals(dto.getProductionCheck()))
                .materialCheck(Constant.Y.equals(dto.getMaterialCheck()))
                .reformCheck(Constant.Y.equals(dto.getReformCheck()))
                .conversionFactor(dto.getConversionFactor())
                .createBy(username)
                .createTime(now)
                .build();
        procedureControllerConfigService.save(entity);
    }

    /**
     * 解析工艺工序控制数据
     *
     * @param row
     * @return
     */
    private CraftProcedureControlImportDTO getCartProcedureControlList(Row row) {
        if (row != null) {
            String craftCode = ExcelUtil.getCellStringValue(row.getCell(0));
            String procedureName = ExcelUtil.getCellStringValue(row.getCell(1));
            String increaseProductionVolume = ExcelUtil.getCellStringValue(row.getCell(2));
            String jumpStationCheck = ExcelUtil.getCellStringValue(row.getCell(3));
            String reformCheck = ExcelUtil.getCellStringValue(row.getCell(4));
            String materialCheck = ExcelUtil.getCellStringValue(row.getCell(5));
            String productionCheck = ExcelUtil.getCellStringValue(row.getCell(6));
            String craftProcedureCheck = ExcelUtil.getCellStringValue(row.getCell(7));
            String conversionFactorCheck = ExcelUtil.getCellStringValue(row.getCell(8));
            return CraftProcedureControlImportDTO.builder()
                    .craftCode(craftCode)
                    .procedureName(procedureName)
                    .increaseProductionVolume(StringUtils.isNotBlank(increaseProductionVolume) ? Double.valueOf(increaseProductionVolume) : null)
                    .jumpStationCheck(jumpStationCheck)
                    .reformCheck(reformCheck)
                    .materialCheck(materialCheck)
                    .productionCheck(productionCheck)
                    .craftProcedureCheck(craftProcedureCheck)
                    .conversionFactor(Double.valueOf(conversionFactorCheck))
                    .build();
        }
        return null;
    }

    /**
     * 通过工单号获取工序组
     *
     * @param workOrderNumbers
     * @return
     */
    @Override
    public List<CraftProcedureWorkOrderDTO> getCraftProcedureGroupByWorkOrder(List<String> workOrderNumbers) {
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return new ArrayList<>();
        }
        return this.baseMapper.getCraftProcedureGroupByWorkOrder(workOrderNumbers);
    }

    /**
     * 获取关键工序id
     *
     * @param procedureIds
     * @return
     */
    @Override
    public List<Integer> getSupCriticalProcedures(List<Integer> procedureIds) {
        if (CollectionUtils.isEmpty(procedureIds)) {
            return new ArrayList<>();
        }
        //获取上级工序
        List<CraftProcedureEntity> craftProcedureEntities = this.listByIds(procedureIds);
        List<Integer> supProcedureIds = craftProcedureEntities.stream().map(CraftProcedureEntity::getSupProcedureId)
                .filter(StringUtils::isNumeric).distinct().map(Integer::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supProcedureIds)) {
            return new ArrayList<>();
        }
        List<ProcedureControllerConfigEntity> list = procedureControllerConfigService.lambdaQuery()
                .eq(ProcedureControllerConfigEntity::getJumpStationCheck, true)
                .in(ProcedureControllerConfigEntity::getCraftProcedureId, supProcedureIds)
                .list();
        //如果上级工序没有关键工序，再找上上级
        if (CollectionUtils.isEmpty(list)) {
            return getSupCriticalProcedures(supProcedureIds);
        }
        return list.stream().map(ProcedureControllerConfigEntity::getCraftProcedureId).distinct().collect(Collectors.toList());
    }

    /**
     * 获取工位对应的工序
     *
     * @param craftId
     * @param modelId
     * @return
     */
    @Override
    public List<Integer> getCraftProcedureByFacModel(Integer craftId, Integer modelId) {
        ModelEntity modelEntity = modelService.getById(modelId);
        if (modelEntity != null && craftId != null) {
            List<CraftProcedureEntity> craftProcedureEntities = this.lambdaQuery()
                    .eq(CraftProcedureEntity::getCraftId, craftId)
                    .eq(CraftProcedureEntity::getFacType, modelEntity.getName())
                    .list();
            return craftProcedureEntities.stream().map(CraftProcedureEntity::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<Integer> getCriticalProcedures(List<Integer> craftProcedureIds) {
        if (CollectionUtils.isEmpty(craftProcedureIds)) {
            return new ArrayList<>();
        }
        List<ProcedureControllerConfigEntity> list = procedureControllerConfigService.lambdaQuery()
                .eq(ProcedureControllerConfigEntity::getJumpStationCheck, true)
                .in(ProcedureControllerConfigEntity::getCraftProcedureId, craftProcedureIds)
                .list();
        return list.stream().map(ProcedureControllerConfigEntity::getCraftProcedureId).distinct().collect(Collectors.toList());
    }

    @Override
    public byte[] exportDeviceParameterTemplate() {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/craftDeviceParameterTemplate.xlsx");
        if (!resource.exists()) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        InputStream bis = null;
        try {
            bis = new BufferedInputStream(resource.getInputStream());
            byte[] buffer = new byte[bis.available()];
            bis.read(buffer);
            return buffer;
        } catch (IOException ex) {
            log.error("文件获取失败", ex);
        } finally {
            IOUtils.closeQuietly(bis);
        }
        throw new ResponseException(RespCodeEnum.OPERATION_FAIL);
    }

    @Override
    public void importDeviceParameterExcel(String filename, InputStream inputStream, String username) {
        Workbook workbook = null;
        try {
            int successNumber = 0;
            StringBuilder messageBuilder = new StringBuilder();
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            int total = sheet.getPhysicalNumberOfRows() - 1;
            List<CraftProcedureDeviceParameterImportDTO> list = new ArrayList<>();
            for (int i = 1; i <= total; i++) {
                //读取数据并校验数据
                CraftProcedureDeviceParameterImportDTO dto = getCartProcedureDeviceParameterList(sheet.getRow(i));
                list.add(dto);
            }

            // 以工艺编号、工序名称、设备类型、用量、配方号分组
            Map<String, List<CraftProcedureDeviceParameterImportDTO>> map = list.stream().collect(Collectors.groupingBy(this::fetchGroupKey));
            Set<Integer> changeProcedureCraftIdSet = new HashSet<>();

            int index = 0;
            for (Map.Entry<String, List<CraftProcedureDeviceParameterImportDTO>> stringListEntry : map.entrySet()) {
                index++;
                //4、校验导入数据、写入校验结果
                List<CraftProcedureDeviceParameterImportDTO> value = stringListEntry.getValue();
//                List<String> nameList = value.stream().map(CraftProcedureDeviceParameterImportDTO::getParameterName).distinct().collect(Collectors.toList());
//                if (value.size() != nameList.size()) {
//                    throw new ResponseException("参数名称重复");
//                }
//                List<String> codeList = value.stream().map(CraftProcedureDeviceParameterImportDTO::getParameterCode).distinct().collect(Collectors.toList());
//                if (value.size() != codeList.size()) {
//                    throw new ResponseException("参数编码重复");
//                }
                boolean canImport = verifyCartProcedureDeviceParameterData(value, messageBuilder, index);
                //6、保存导入数据
                if (canImport) {
                    successNumber++;
                    if (!CollectionUtils.isEmpty(value)) {
                        //移除之前的关联关系
                        CraftProcedureDeviceParameterImportDTO craftProcedureDeviceParameterImportDTO = value.get(0);
                        CraftEntity craftEntity = craftService.getCraftEntityByCraftCode(value.get(0).getCraftCode());
                        ModelEntity modelEntity = modelService.getDeviceModelByName(value.get(0).getDeviceModel());
                        CraftProcedureEntity craftProcedureEntity = getEntityByCraftIdAndProcedureName(craftEntity.getCraftId(), value.get(0).getProcedureName());
//                        removeDeviceTargetReference(stringListEntry.getKey(), craftEntity, modelEntity, craftProcedureEntity);
                        // 保存工序设备
                        saveProcedureDeviceTypeList(craftProcedureDeviceParameterImportDTO, craftEntity, modelEntity, craftProcedureEntity);
//                        for (CraftProcedureDeviceParameterImportDTO procedureDeviceParameterImportDTO : value) {
//                            // 保存工序设备 配方参数
//                            saveCartProcedureDeviceParameterList(procedureDeviceParameterImportDTO, craftEntity, modelEntity, craftProcedureEntity, username);
//                        }
                        changeProcedureCraftIdSet.add(craftProcedureEntity.getId());
                    }
                }
                Double processPercent = MathUtil.divideDouble(successNumber, map.size(), 2);
                ImportProgressDTO build = ImportProgressDTO.builder().progress(processPercent)
                        .executionDescription(String.format("正在处理中，已完成%s组；", successNumber))
                        .executionStatus(processPercent.compareTo(1.0) == 0)
                        .build();
                redisTemplate.opsForValue().set(RedisKeyPrefix.CRAFT_PROCEDURE_DEVICE_PARAMETER_IMPORT_PROGRESS, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            }
            //保存日志文件
            String url = importDataRecordService.writeLogData(messageBuilder.toString(), username,
                    filename, ImportTypeEnum.CRAFT_PROCEDURE_DEVICE_IMPORT.getType(), successNumber, Math.max(map.size() - successNumber, 0));
            ImportProgressDTO build = ImportProgressDTO.builder()
                    .progress(1.0)
                    .executionDescription(String.format("导入完成：成功%s组；失败%s组", successNumber, Math.max(map.size() - successNumber, 0)))
                    .executionStatus(true)
                    .importUrl(url)
                    .build();
            redisTemplate.opsForValue().set(RedisKeyPrefix.CRAFT_PROCEDURE_DEVICE_PARAMETER_IMPORT_PROGRESS, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            for (Integer changeCraftProcedureId : changeProcedureCraftIdSet) {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("设备 - 导入")
                        .craftProcedureId(changeCraftProcedureId)
                        .build()
                );
            }
        } catch (Exception e) {
            log.error("工艺工序用料导入excel失败", e);
            Object importObj = redisTemplate.opsForValue().get(RedisKeyPrefix.CRAFT_PROCEDURE_DEVICE_PARAMETER_IMPORT_PROGRESS);
            ImportProgressDTO build;
            if (importObj != null) {
                build = JSONObject.parseObject((String) importObj, ImportProgressDTO.class);
            } else {
                build = ImportProgressDTO.builder()
                        .progress(0.0).build();
            }
            build.setExecutionStatus(true);
            if (e instanceof ResponseException) {
                build.setExecutionDescription(e.getMessage());
            } else {
                build.setExecutionDescription("导入过程程序发生错误，请联系开发人员解决");
            }
            redisTemplate.opsForValue().set(RedisKeyPrefix.CRAFT_PROCEDURE_DEVICE_PARAMETER_IMPORT_PROGRESS, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        } finally {
            // 释放锁
            redisTemplate.delete(RedisKeyPrefix.CRAFT_IMPORT_DEVICE_PARAMETER_LOCK);
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(workbook);
        }
    }

    @Async
    @Override
    public void importWorkHourExcel(String fileName, InputStream inputStream, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_PROCEDURE_WORK_HOUR_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_WORK_HOUR_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureWorkDurationExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, CraftProcedureWorkDurationExcelDTO.class, 1, 2);
            //2、校验数据
            List<CraftProcedureWorkDurationExcelDTO> canImportRecords = verifyFormatDuration(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureWorkDurationExcelDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureWorkDurationExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_WORK_HOUR.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_WORK_HOUR.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            // 新增
            for (CraftProcedureWorkDurationExcelDTO insertImport : canImportRecords) {
                saveWorkDurations(insertImport);
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureWorkDurationExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("工时 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    private List<CraftProcedureWorkDurationExcelDTO> verifyFormatDuration(List<CraftProcedureWorkDurationExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureWorkDurationExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureWorkDurationExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureWorkDurationExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }

            // 是否按工时计算
            importDTO.setIsCalculate(StringUtils.isNotBlank(importDTO.getIsCalculateStr()) && Constant.TRUE.equals(importDTO.getIsCalculateStr()));

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    @Override
    public CraftProcedureEntity getLastCraftProcedureEntity(List<CraftProcedureEntity> craftProcedureEntities) {
        Map<Integer, CraftProcedureEntity> craftProcedureMap = craftProcedureEntities.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, o -> o));
        return craftProcedureEntities.stream().filter(o -> {
            if (StringUtils.isBlank(o.getNextProcedureId())) {
                return true;
            }
            List<Integer> ids = Arrays.stream(o.getNextProcedureId().split(com.yelink.dfscommon.constant.Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            for (Integer id : ids) {
                if (craftProcedureMap.containsKey(id)) {
                    return false;
                }
            }
            return true;
        }).findFirst().get();
    }

    @Override
    public Boolean checkCraftProcedureBeforeDelete(Integer craftProcedureId) {
        // 判断是否存在投入、挂起的工单
        List<String> workOrderNumbers = workOrderProcedureRelationService.lambdaQuery()
                .eq(WorkOrderProcedureRelationEntity::getCraftProcedureId, craftProcedureId)
                .list().stream()
                .map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(workOrderNumbers)) {
            List<Integer> workOrderStates = Arrays.asList(WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
            Long existWorkOrderCount = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                    .in(WorkOrderEntity::getState, workOrderStates)
                    .count();
            if (existWorkOrderCount > 0) {
                return true;
            }
        }
        // 判断是否存在过站记录
        ProductFlowCodeRecordService productFlowCodeRecordService = SpringUtil.getBean(ProductFlowCodeRecordService.class);
        Long existProductFlowCodeRecordCount = productFlowCodeRecordService.lambdaQuery().eq(ProductFlowCodeRecordEntity::getProdureId, craftProcedureId).count();
        if (existProductFlowCodeRecordCount > 0) {
            return true;
        }
        return false;
    }

    @Async
    @Override
    public void importInspectControllerExcel(String fileName, InputStream inputStream, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_PROCEDURE_INSPECT_CONTROLLER_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_INSPECT_CONTROLLER_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureInspectControllerExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, CraftProcedureInspectControllerExcelDTO.class, 1, 2);
            //2、校验数据
            List<CraftProcedureInspectControllerExcelDTO> canImportRecords = verifyFormatInspectController(totalImportRecords);
            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureInspectControllerExcelDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureInspectControllerExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_INSPECT_CONTROLLER.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_INSPECT_CONTROLLER.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            // 新增
            for (CraftProcedureInspectControllerExcelDTO insertImport : canImportRecords) {
                saveProcedureControllerInspects(insertImport);

                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureInspectControllerExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("检验方案 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    @Override
    public void importParameterExcel(String fileName, InputStream inputStream, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_PROCEDURE_PARAMETER_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_PARAMETER_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureParameterExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, CraftProcedureParameterExcelDTO.class, 0, 2);
            //2、校验数据
            List<CraftProcedureParameterExcelDTO> canImportRecords = verifyFormatParameter(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureParameterExcelDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureParameterExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_PARAMETER.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_PARAMETER.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            // 新增
            for (CraftProcedureParameterExcelDTO insertImport : canImportRecords) {
                saveParameterDevices(insertImport);
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureParameterExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("参数 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importMaterialExcel(String fileName, InputStream inputStream, String operationUsername) {
        final String lockKey = RedisKeyPrefix.CRAFT_IMPORT_MATERIAL_LOCK, importProgressKey = RedisKeyPrefix.CRAFT_PROCEDURE_MATERIAL_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureMaterialExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, CraftProcedureMaterialExcelDTO.class, 1, 2);
            //2、校验数据
            List<CraftProcedureMaterialExcelDTO> canImportRecords = verifyFormatMaterial(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, CraftProcedureMaterialExcelDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(CraftProcedureMaterialExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.CRAFT_PROCEDURE_MATERIAL.getType())
                    .importTypeName(ImportTypeEnum.CRAFT_PROCEDURE_MATERIAL.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = 0, failCount = totalImportRecords.size() - canImportRecords.size();
            for (CraftProcedureMaterialExcelDTO insertImport : canImportRecords) {
                saveCpMaterials(insertImport);
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount, fileName);
            }
            canImportRecords.stream().map(CraftProcedureMaterialExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("物料 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .build()
                );
            });
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    @Override
    public List<CraftProcedureDTO> getProcedureList(CraftProcedureSelectDTO selectDTO) {
        // 默认按id顺序排序
        List<CraftProcedureEntity> list = this.lambdaQuery()
                .in(!CollectionUtils.isEmpty(selectDTO.getCraftIds()), CraftProcedureEntity::getCraftId, selectDTO.getCraftIds())
                .in(!CollectionUtils.isEmpty(selectDTO.getCraftProcedureIds()), CraftProcedureEntity::getId, selectDTO.getCraftProcedureIds())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        if (Objects.nonNull(selectDTO.getIsShowSimpleInfo()) && selectDTO.getIsShowSimpleInfo()) {
            return JacksonUtil.convertArray(list, CraftProcedureDTO.class);
        }
        showName(list);
        // 获取工序定义id，拿到工序定义编码
        List<Integer> procedureIds = list.stream().map(CraftProcedureEntity::getProcedureId).collect(Collectors.toList());
        Map<Integer, String> procedureIdCodeMap = this.getProcedureIdCodeMap(procedureIds);
        // 获取工艺工序的关联数据
        List<CraftProcedureEntity> detailList = new ArrayList<>();
        for (CraftProcedureEntity craftProcedureEntity : list) {
            CraftProcedureEntity procedureEntity = getProcedureConfig(craftProcedureEntity.getId());
            procedureEntity.setProcedureCode(procedureIdCodeMap.get(procedureEntity.getProcedureId()));
            detailList.add(procedureEntity);
        }
        // 转换为DTO
        return JacksonUtil.convertArray(detailList, CraftProcedureDTO.class);
    }

    private List<CraftProcedureInspectControllerExcelDTO> verifyFormatInspectController(List<CraftProcedureInspectControllerExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureInspectControllerExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureInspectControllerExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureInspectControllerExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;
            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }

            // 检验类型
            if (StringUtils.isBlank(importDTO.getInspectTypeName())) {
                importResult.append("检验类型不能为空；");
                canImport = false;
            } else {
                Integer inspectType = InspectionsSchemeTypeEnum.getCodeByName(importDTO.getInspectTypeName());
                if (Objects.isNull(inspectType)) {
                    importResult.append("检验类型不存在，必须单选且值为（过程检验、库存检验、首件检验、末件检验、来料检验、退货检验、发货检验、生产巡检、生产退料检验）；");
                    canImport = false;
                } else {
                    importDTO.setInspectType(inspectType);
                }
            }

            // 检验方案
            if (StringUtils.isNotBlank(importDTO.getInspectSchemeNames())) {
                List<String> importInspectSchemeNames = Arrays.stream(importDTO.getInspectSchemeNames().split(Constant.SEP)).distinct().collect(Collectors.toList());
                List<QualityInspectionSchemeEntity> inspectionSchemeEntities = JacksonUtil.getResponseArray(inspectionSchemeInterface.getReleasedAndCreateSchemeList(String.join(Constant.SEP, importInspectSchemeNames)), QualityInspectionSchemeEntity.class);
                if (importInspectSchemeNames.size() != inspectionSchemeEntities.size()) {
                    importResult.append("导入检验方案在系统中不存在；");
                    canImport = false;
                } else {
                    // 可能填写重复的值,所以去重后再赋值
                    importDTO.setInspectSchemeNames(String.join(Constant.SEP, importInspectSchemeNames));
                }
                // 检验方案需要和检验类型匹配
                for (QualityInspectionSchemeEntity inspectionSchemeEntity : inspectionSchemeEntities) {
                    if (!inspectionSchemeEntity.getTypeName().equals(importDTO.getInspectTypeName())) {
                        importResult.append("检验方案:").append(inspectionSchemeEntity.getQualityInspectionSchemeName()).append("需要和检验类型匹配；");
                        canImport = false;
                    }
                }
            }

            // 控制方式
            if (StringUtils.isNotBlank(importDTO.getInspectMethodNames())) {
                List<String> inspectMethodNames = Arrays.stream(importDTO.getInspectMethodNames().split(Constant.SEP)).distinct().collect(Collectors.toList());
                List<CraftProcedureInspectMethodEnum> includeEnums = Arrays.stream(CraftProcedureInspectMethodEnum.values()).filter(res -> inspectMethodNames.contains(res.getName())).collect(Collectors.toList());
                if (includeEnums.size() != inspectMethodNames.size()) {
                    importResult.append("控制方式填写有误，只能为投产、报工、完工；");
                    canImport = false;
                } else {
                    importDTO.setInspectMethodCodes(includeEnums.stream().map(CraftProcedureInspectMethodEnum::getCode).collect(Collectors.joining(Constant.SEP)));
                }
                // 如果不为空，则检验方案必须要有值
                if (StringUtils.isBlank(importDTO.getInspectSchemeNames())) {
                    importResult.append("控制方式方式不为空，检验方案必填；");
                    canImport = false;
                }
                // 检验方式需要和检验类型匹配
                for (String inspectMethodName : inspectMethodNames) {
                    boolean exists = dictService.lambdaQuery().eq(DictEntity::getName, inspectMethodName).eq(DictEntity::getType, importDTO.getInspectTypeName()).exists();
                    if (!exists) {
                        importResult.append("检验方式:").append(inspectMethodName).append("需要和检验类型匹配；");
                        canImport = false;
                    }
                }
            }
            // 触发条件
            if (StringUtils.isNotBlank(importDTO.getInspectTriggerConditionNames())) {
                List<String> inspectTriggerConditionNames = Arrays.stream(importDTO.getInspectTriggerConditionNames().split(Constant.SEP)).distinct().collect(Collectors.toList());
                List<InspectTriggerConditionEnum> includeEnums = Arrays.stream(InspectTriggerConditionEnum.values()).filter(res -> inspectTriggerConditionNames.contains(res.getName())).collect(Collectors.toList());
                if (includeEnums.size() != inspectTriggerConditionNames.size()) {
                    importResult.append("触发条件填写有误；");
                    canImport = false;
                } else {
                    importDTO.setInspectTriggerConditionCodes(includeEnums.stream().map(InspectTriggerConditionEnum::getCode).collect(Collectors.joining(Constant.SEP)));
                }
                // 触发条件需要和检验类型匹配
                for (String inspectTriggerConditionName : inspectTriggerConditionNames) {
                    boolean exists = dictService.lambdaQuery().eq(DictEntity::getName, inspectTriggerConditionName).eq(DictEntity::getType, importDTO.getInspectTypeName()).exists();
                    if (!exists) {
                        importResult.append("触发条件:").append(inspectTriggerConditionName).append("需要和检验类型匹配；");
                        canImport = false;
                    }
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private void saveProcedureDeviceTypeList(CraftProcedureDeviceParameterImportDTO dto, CraftEntity craftEntity,
                                             ModelEntity modelEntity, CraftProcedureEntity craftProcedureEntity) {
        ProcedureDeviceTypeEntity build = ProcedureDeviceTypeEntity.builder()
                .craftId(craftEntity.getCraftId())
                .procedureId(craftProcedureEntity.getId())
                .craftCode(craftEntity.getCraftCode())
                .deviceTypeName(modelEntity.getName())
                .deviceModelId(modelEntity.getId())
//                .number(dto.getDosage())
//                .formulaNum(dto.getFormulaNum())
                .build();
        procedureDeviceTypeService.save(build);
    }

//    /**
//     * 删除工序设备指标关联关系
//     *
//     * @param key
//     * @param craftEntity
//     * @param modelEntity
//     * @param craftProcedureEntity
//     */
//    private void removeDeviceTargetReference(String key, CraftEntity craftEntity, ModelEntity modelEntity, CraftProcedureEntity craftProcedureEntity) {
//        String formulaNum = key.split(Constant.UNDERLINE)[3];
//        String number = key.split(Constant.UNDERLINE)[4];
//        LambdaQueryWrapper<ProcedureDeviceTargetReferenceEntity> targetReferenceQW = new LambdaQueryWrapper<>();
//        targetReferenceQW.eq(ProcedureDeviceTargetReferenceEntity::getCraftId, craftEntity.getCraftId())
//                .eq(ProcedureDeviceTargetReferenceEntity::getDeviceModelId, modelEntity.getId())
//                .eq(ProcedureDeviceTargetReferenceEntity::getFormulaNum, formulaNum)
//                .eq(ProcedureDeviceTargetReferenceEntity::getNumber, number)
//                .eq(ProcedureDeviceTargetReferenceEntity::getProcedureId, craftProcedureEntity.getId());
//        procedureDeviceTargetReferenceService.remove(targetReferenceQW);
//
//        LambdaQueryWrapper<ProcedureDeviceTypeEntity> procedureDeviceTypeQW = new LambdaQueryWrapper<>();
//        procedureDeviceTypeQW.eq(ProcedureDeviceTypeEntity::getCraftId, craftEntity.getCraftId())
//                .eq(ProcedureDeviceTypeEntity::getDeviceModelId, modelEntity.getId())
//                .eq(ProcedureDeviceTypeEntity::getNumber, number)
//                .eq(ProcedureDeviceTypeEntity::getFormulaNum, formulaNum)
//                .eq(ProcedureDeviceTypeEntity::getProcedureId, craftProcedureEntity.getId());
//        procedureDeviceTypeService.remove(procedureDeviceTypeQW);
//    }

    private String fetchGroupKey(CraftProcedureDeviceParameterImportDTO dto) {
        return dto.getCraftCode() + Constant.UNDERLINE + dto.getProcedureName() + Constant.UNDERLINE + dto.getDeviceModel();
//                + Constant.UNDERLINE + dto.getFormulaNum() + Constant.UNDERLINE + dto.getDosage();
    }

    private void saveCartProcedureDeviceParameterList(CraftProcedureDeviceParameterImportDTO dto, CraftEntity craftEntity,
                                                      ModelEntity modelEntity, CraftProcedureEntity craftProcedureEntity, String username) {
        ProcedureDeviceTargetReferenceEntity build = ProcedureDeviceTargetReferenceEntity.builder()
                .craftId(craftEntity.getCraftId())
                .procedureId(craftProcedureEntity.getId())
                .deviceModelId(modelEntity.getId())
//                .targetName(dto.getParameterCode())
//                .targetCnname(dto.getParameterName())
//                .referenceVal(dto.getReferenceValue())
//                .referenceUpper(dto.getUpperLimitValue())
//                .referenceLower(dto.getLowerLimitValue())
//                .number(dto.getDosage())
//                .formulaNum(dto.getFormulaNum())
                .type("true")
                .createTime(new Date())
                .createBy(username)
                .build();
        procedureDeviceTargetReferenceService.save(build);

    }

    private boolean verifyCartProcedureDeviceParameterData(List<CraftProcedureDeviceParameterImportDTO> list, StringBuilder messageBuilder, int index) {
        if (CollectionUtils.isEmpty(list)) {
            messageBuilder.append("校验该工艺").append(list.get(0).getCraftCode()).append("数据为空；");
            messageBuilder.append("\n");
            messageBuilder.append("导入结果：").append("NOK；").append("\n");
            return false;
        }
        boolean canImport = false;
        messageBuilder.append(String.format("导入第%s组目标数据;\n\t", index));
        for (int i = 0; i < list.size(); i++) {
            CraftProcedureDeviceParameterImportDTO dto = list.get(i);
            if (StringUtils.isBlank(dto.getCraftCode())) {
                messageBuilder.append("校验工艺工序设备参数字段结果：").append("工艺编号字段为空；");
                canImport = false;
                break;
            }
            CraftEntity craftEntity = craftService.getCraftEntityByCraftCode(dto.getCraftCode());
            if (craftEntity == null) {
                messageBuilder.append("校验工艺工序设备参数字段结果：").append("工艺编号在系统中未找到；");
                canImport = false;
                break;
            }
            if (StringUtils.isBlank(dto.getProcedureName())) {
                messageBuilder.append("校验工艺工序设备参数字段结果：").append("工序名称字段为空；");
                canImport = false;
                break;
            }
            CraftProcedureEntity craftProcedureEntity = getEntityByCraftIdAndProcedureName(craftEntity.getCraftId(), dto.getProcedureName());
            if (craftProcedureEntity == null) {
                messageBuilder.append("校验工艺工序设备参数字段结果：").append("未找到该工艺下的工序名称：").append(dto.getProcedureName());
                canImport = false;
                break;
            }
            if (StringUtils.isBlank(dto.getDeviceModel()) || modelService.getDeviceModelByName(dto.getDeviceModel()) == null) {
                messageBuilder.append("校验工艺工序设备参数字段结果：").append("设备类型字段不合法；");
                canImport = false;
                break;
            }
//            if (dto.getDosage() == null) {
//                messageBuilder.append("校验工艺工序设备参数字段结果：").append("用量字段不合法；");
//                canImport = false;
//                break;
//            }
//            if (StringUtils.isBlank(dto.getFormulaNum())) {
//                messageBuilder.append("校验工艺工序设备参数字段结果：").append("用量字段不合法；");
//                canImport = false;
//                break;
//            }
            canImport = true;
        }
        messageBuilder.append("导入结果：").append(canImport ? "OK；" : "NOK；").append("\n");
        return canImport;
    }

    /**
     * 读取数据并校验数据
     *
     * @param row
     * @return
     */
    private CraftProcedureDeviceParameterImportDTO getCartProcedureDeviceParameterList(Row row) {
        if (row != null) {
            String craftCode = ExcelUtil.getCellStringValue(row.getCell(0));
            String procedureName = ExcelUtil.getCellStringValue(row.getCell(1));
            String deviceModel = ExcelUtil.getCellStringValue(row.getCell(2));
//            String dosage = ExcelUtil.getCellStringValue(row.getCell(3));
//            String formulaNum = ExcelUtil.getCellStringValue(row.getCell(4));
//            String parameterName = ExcelUtil.getCellStringValue(row.getCell(5));
//            String parameterCode = ExcelUtil.getCellStringValue(row.getCell(6));
//            String upperLimitValue = ExcelUtil.getCellStringValue(row.getCell(7));
//            String lowerLimitValue = ExcelUtil.getCellStringValue(row.getCell(8));
//            String referenceValue = ExcelUtil.getCellStringValue(row.getCell(9));
            return CraftProcedureDeviceParameterImportDTO.builder().craftCode(craftCode).procedureName(procedureName)
                    .deviceModel(deviceModel)
//                    .dosage(Integer.valueOf(dosage))
//                    .formulaNum(formulaNum)
//                    .parameterName(parameterName)
//                    .parameterCode(parameterCode)
//                    .upperLimitValue(upperLimitValue)
//                    .lowerLimitValue(lowerLimitValue)
//                    .referenceValue(referenceValue)
                    .build();
        }
        return null;
    }

    /**
     * 工艺工序导入
     */
    @Async
    @Override
    @Transactional
    public void importCraftProcedureExcel(String fileName, InputStream inputStream, String username, Boolean isTemplate) {
        if (isTemplate == null) {
            isTemplate = Constants.FALSE;
        }
        String importProgressKey = RedisKeyPrefix.getCraftOrTemplateKey(RedisKeyPrefix.CRAFT_PROCEDURE_IMPORT_PROGRESS, isTemplate);
        String lockKey = RedisKeyPrefix.getCraftOrTemplateKey(RedisKeyPrefix.CRAFT_PROCEDURE_IMPORT_LOCK, isTemplate);
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);

        File templateFile = null;
        try {
            // 将上传的文件保存到临时文件,方便后面多次读取
            templateFile = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!templateFile.getParentFile().exists()) {
                templateFile.getParentFile().mkdirs();
            }
            FileOutputStream fileOutputStream = new FileOutputStream(templateFile);
            IOUtils.copy(inputStream, fileOutputStream);
            fileOutputStream.close();
            inputStream.close();

            // 列表sheet的顺序不能更改,工艺模板没有工序用料sheet,导入是按顺序的
            int sheetNo = 1;
            // 工艺工序，读取excel第二个sheet,从第3行开始读的数据
            List<CraftProcedureExcelDTO> craftProcedures = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureExcelDTO.class, sheetNo++, 2);
            // 工序物料配置，读取excel第3个sheet,从第3行开始读的数据
            List<CraftProcedureMaterialExcelDTO> procedureMaterials = new ArrayList<>();
            // 工艺模板没有工序物料
            if (!isTemplate) {
                procedureMaterials = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureMaterialExcelDTO.class, sheetNo++, 2);
            }
            // 工序用料
            List<CraftProcedureMaterialUsedExcelDTO> procedureMaterialUseds = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureMaterialUsedExcelDTO.class, sheetNo++, 2);
            // 工序工装配置，读取excel第4个sheet,从第3行开始读的数据
            List<CraftProcedureAssembleExcelDTO> procedureAssembles = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureAssembleExcelDTO.class, sheetNo++, 2);
            // 工序设备配置，读取excel第5个sheet,从第3行开始读的数据
            List<CraftProcedureDeviceExcelDTO> procedureDevices = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureDeviceExcelDTO.class, sheetNo++, 2);
            // 工艺参数，读取excel第6个sheet,从第3行开始读的数据
            List<CraftProcedureParameterExcelDTO> procedureParameters = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureParameterExcelDTO.class, sheetNo++, 2);
            // 工序人力配置，读取excel第7个sheet,从第3行开始读的数据
            List<CraftProcedureHumanResourcesExcelDTO> humanResources = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureHumanResourcesExcelDTO.class, sheetNo++, 2);
            // 工序工时配置，读取excel第8个sheet,从第3行开始读的数据
            List<CraftProcedureWorkDurationExcelDTO> procedureWorkDurations = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureWorkDurationExcelDTO.class, sheetNo++, 2);
            // 工序检验项配置，读取excel第9个sheet,从第3行开始读的数据
            List<CraftProcedureInspectExcelDTO> procedureInspects = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureInspectExcelDTO.class, sheetNo++, 2);
            // 工序检验控制配置，读取excel第10个sheet,从第3行开始读的数据
            List<CraftProcedureInspectControllerExcelDTO> procedureInspectControllers = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureInspectControllerExcelDTO.class, sheetNo++, 2);
            // 工序控制配置，读取excel第11个sheet,从第3行开始读的数据
            List<CraftProcedureControlExcelDTO> procedureControls = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureControlExcelDTO.class, sheetNo++, 2);
            // 工艺模板关联物料配置
            List<CraftProcedureTempMaterialExcelDTO> craftTempMaterials = new ArrayList<>();
            if (isTemplate) {
                craftTempMaterials = EasyExcelUtil.read(new FileInputStream(templateFile), CraftProcedureTempMaterialExcelDTO.class, sheetNo++, 2);
            }

            importProgressService.updateProgress(importProgressKey, "校验工艺工序数据中...", 0.1, false, null);
            //校验工艺工序
            List<CraftProcedureExcelDTO> canImportRecords = verifyFormatCraftProcedure(craftProcedures, isTemplate);
            // 这里应该先对工艺工序进行保存，后续的数据都依赖工艺工序的id
            int craftCount = this.saveCraftProcedureImportDTO(canImportRecords, username, isTemplate);

            List<CraftProcedureMaterialExcelDTO> canImportProcedureMaterials = new ArrayList<>();
            if (!isTemplate) {
                importProgressService.updateProgress(importProgressKey, "校验工序物料数据中...", 0.2, false, null);
                // 校验工序用料
                canImportProcedureMaterials = verifyFormatMaterial(procedureMaterials);
                // 保存工序用料数据
                canImportProcedureMaterials.forEach(this::saveCpMaterials);
                canImportProcedureMaterials.stream().map(CraftProcedureMaterialExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                    versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                            .description("物料 - 导入")
                            .craftProcedureId(craftProcedureId)
                            .editor(username)
                            .build()
                    );
                });
            }
            // 工序检验项
            boolean isProcedureInspect;
            // 工艺参数
            boolean isProcedureParameter;
            // 工序用料
            boolean isProcedureMaterialUsed;
            // 工序设备
            boolean isProcedureDevice;
            // 工序工时
            boolean isProcedureWorkHour;
            // 工序检验方案
            boolean isProcedureInspectScheme;
            // 工序控制
            boolean isProcedureController;
            // 工序工装
            boolean isProcedureAssemble;
            // 工序人力
            boolean isProcedurePost;
            // 工序质检
            boolean isProcedureDefect;
            // 工序维修
            boolean isProcedureMaintain;

            // 工序用料
            importProgressService.updateProgress(importProgressKey, "校验工序用料数据中...", 0.25, false, null);
            List<CraftProcedureMaterialUsedExcelDTO> canImportProcedureMaterialUseds = verifyFormatMaterialUsed(procedureMaterialUseds);
            canImportProcedureMaterialUseds.forEach(this::saveCpMaterialUseds);
            canImportProcedureMaterialUseds.stream().map(CraftProcedureMaterialUsedExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("用料 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureMaterialUsed = CollectionUtils.isEmpty(canImportProcedureMaterialUseds);

            // 工序工装配置
            importProgressService.updateProgress(importProgressKey, "校验工序工装数据中...", 0.3, false, null);
            List<CraftProcedureAssembleExcelDTO> canImportProcedureAssembles = verifyFormatAssemble(procedureAssembles);
            saveCpAssembles(canImportProcedureAssembles);
            canImportProcedureAssembles.stream().map(CraftProcedureAssembleExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("工装 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureAssemble = CollectionUtils.isEmpty(canImportProcedureAssembles);

            // 工序设备配置
            importProgressService.updateProgress(importProgressKey, "校验工序设备数据中...", 0.4, false, null);
            List<CraftProcedureDeviceExcelDTO> canImportProcedureDevices = verifyFormatDevice(procedureDevices);
            saveProcedureDevices(canImportProcedureDevices);
            canImportProcedureDevices.stream().map(CraftProcedureDeviceExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("设备 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureDevice = CollectionUtils.isEmpty(canImportProcedureDevices);

            // 工艺参数
            importProgressService.updateProgress(importProgressKey, "校验工艺参数数据中...", 0.5, false, null);
            List<CraftProcedureParameterExcelDTO> canImportProcedureParameters = verifyFormatParameter(procedureParameters);
            canImportProcedureParameters.forEach(this::saveParameterDevices);
            canImportProcedureParameters.stream().map(CraftProcedureParameterExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("参数 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureParameter = CollectionUtils.isEmpty(canImportProcedureParameters);

            // 工序人力配置
            importProgressService.updateProgress(importProgressKey, "校验工序人力数据中...", 0.6, false, null);
            List<CraftProcedureHumanResourcesExcelDTO> canImportHumanResources = verifyFormatHumanResource(humanResources);
            saveHumanResources(canImportHumanResources);
            canImportHumanResources.stream().map(CraftProcedureHumanResourcesExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("人力 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedurePost = CollectionUtils.isEmpty(canImportHumanResources);

            // 工序工时配置
            importProgressService.updateProgress(importProgressKey, "校验工序工时数据中...", 0.7, false, null);
            List<CraftProcedureWorkDurationExcelDTO> canImportProcedureWorkDurations = verifyFormatDuration(procedureWorkDurations);
            canImportProcedureWorkDurations.forEach(this::saveWorkDurations);
            canImportProcedureWorkDurations.stream().map(CraftProcedureWorkDurationExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("工时 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureWorkHour = CollectionUtils.isEmpty(canImportProcedureWorkDurations);

            // 工序检验配置
            importProgressService.updateProgress(importProgressKey, "校验工序检验数据中...", 0.8, false, null);
            List<CraftProcedureInspectExcelDTO> canImportProcedureInspects = verifyFormatInspect(procedureInspects);
            canImportProcedureInspects.forEach(res -> saveProcedureInspects(res, username));
            canImportProcedureInspects.stream().map(CraftProcedureInspectExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("检验项 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureInspect = CollectionUtils.isEmpty(canImportProcedureInspects);

            // 工序方案控制配置
            List<CraftProcedureInspectControllerExcelDTO> canImportProcedureInspectControllers = verifyFormatInspectController(procedureInspectControllers);
            canImportProcedureInspectControllers.forEach(this::saveProcedureControllerInspects);
            canImportProcedureInspectControllers.stream().map(CraftProcedureInspectControllerExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("检验方案 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureInspectScheme = CollectionUtils.isEmpty(canImportProcedureInspectControllers);

            // 校验工序控制配置
            importProgressService.updateProgress(importProgressKey, "校验工序控制数据中...", 0.9, false, null);
            List<CraftProcedureControlExcelDTO> canImportProcedureControls = verifyFormatControl(procedureControls);
            canImportProcedureControls.forEach(this::saveProcedureControls);
            canImportProcedureControls.stream().map(CraftProcedureControlExcelDTO::getCraftProcedureId).distinct().forEach(craftProcedureId -> {
                versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                        .description("控制 - 导入")
                        .craftProcedureId(craftProcedureId)
                        .editor(username)
                        .build()
                );
            });
            isProcedureController = CollectionUtils.isEmpty(canImportProcedureControls);
            // 校验工序模板物料配置
            importProgressService.updateProgress(importProgressKey, "校验工艺模板关联物料数据中...", 0.95, false, null);
            List<CraftProcedureTempMaterialExcelDTO> canImportCraftTempMaterials = verifyFormatCraftTempMaterial(craftTempMaterials);
            saveCraftTempMaterials(canImportCraftTempMaterials);

            // 工序质检
            isProcedureDefect = this.saveProcedureDefectScheme(canImportRecords, username);
            // 工序维修
            isProcedureMaintain = this.saveProcedureMaintainScheme(canImportRecords, username);

            // 处理日志
            ImportTypeEnum importType = ImportTypeEnum.CRAFT_PROCEDURE_IMPORT;
            if (isTemplate) {
                importType = ImportTypeEnum.CRAFT_TEMPLATE_PROCEDURE_IMPORT;
            }
            //验证结果上传文件服务器
            List<EasyExcelDataDTO> excelDatas = new ArrayList<>();
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(craftProcedures).headerClass(CraftProcedureExcelDTO.class).sheetName("工艺工序").build());
            if (!isTemplate) {
                excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureMaterials).headerClass(CraftProcedureMaterialExcelDTO.class).sheetName("工序物料配置").build());
            }
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureMaterialUseds).headerClass(CraftProcedureMaterialUsedExcelDTO.class).sheetName("工序用料配置").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureAssembles).headerClass(CraftProcedureAssembleExcelDTO.class).sheetName("工序工装配置").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureDevices).headerClass(CraftProcedureDeviceExcelDTO.class).sheetName("工序设备配置").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureParameters).headerClass(CraftProcedureParameterExcelDTO.class).sheetName("工艺参数").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(humanResources).headerClass(CraftProcedureHumanResourcesExcelDTO.class).sheetName("工序人力配置").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureWorkDurations).headerClass(CraftProcedureWorkDurationExcelDTO.class).sheetName("工序工时配置").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureInspects).headerClass(CraftProcedureInspectExcelDTO.class).sheetName("工序检验项配置").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureInspectControllers).headerClass(CraftProcedureInspectControllerExcelDTO.class).sheetName("工序检验控制配置").build());
            excelDatas.add(EasyExcelDataDTO.builder().excelDataList(procedureControls).headerClass(CraftProcedureControlExcelDTO.class).sheetName("工序控制配置").build());
            if (isTemplate) {
                excelDatas.add(EasyExcelDataDTO.builder().excelDataList(craftTempMaterials).headerClass(CraftProcedureTempMaterialExcelDTO.class).sheetName("工艺关联物料配置").build());
            }
            String importUrl = importProgressService.verifyResultToExcelUploadTo(excelDatas, username);

            //4、保存导入记录
            int totalCount = craftProcedures.size() + procedureMaterials.size() + procedureMaterialUseds.size() + procedureAssembles.size() +
                    procedureDevices.size() + procedureParameters.size() + humanResources.size() + procedureWorkDurations.size() +
                    procedureInspects.size() + procedureInspectControllers.size() + procedureControls.size() + craftTempMaterials.size();
            int successCount = canImportRecords.size() + canImportProcedureMaterials.size() + canImportProcedureMaterialUseds.size() + canImportProcedureAssembles.size() +
                    canImportProcedureDevices.size() + canImportProcedureParameters.size() + canImportHumanResources.size() + canImportProcedureWorkDurations.size() +
                    canImportProcedureInspects.size() + canImportProcedureInspectControllers.size() + canImportProcedureControls.size() + canImportCraftTempMaterials.size();
            int failCount = totalCount - successCount;
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(importType.getType())
                    .importTypeName(importType.getTypeName())
                    .createBy(username)
                    .createTime(new Date())
                    .failNumber(failCount)
                    .successNumber(successCount)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(username).build());

            importProgressService.updateProgress(importProgressKey, "导入工艺" + craftCount +"条；数据行成功" + successCount + "条，" + "失败" + failCount + "条。", 1.0, true, importUrl);

            // 5 工序默认
            List<Integer> craftProcedureIds = canImportRecords.stream().map(CraftProcedureExcelDTO::getCraftProcedureId).distinct().collect(Collectors.toList());
            for (Integer craftProcedureId : craftProcedureIds) {
                try {
                    ProcedureOneKeyDefaultDTO oneKeyDefaultDTO = ProcedureOneKeyDefaultDTO.builder()
                            .craftProcedureId(craftProcedureId)
                            .isProcedureInspect(isProcedureInspect)
                            .isProcedureParameter(isProcedureParameter)
                            .isProcedureMaterialUsed(isProcedureMaterialUsed)
                            .isProcedureDevice(isProcedureDevice)
                            .isProcedureWorkHour(isProcedureWorkHour)
                            .isProcedureInspectScheme(isProcedureInspectScheme)
                            .isProcedureController(isProcedureController)
                            .isProcedureAssemble(isProcedureAssemble)
                            .isProcedureDefect(isProcedureDefect)
                            .isProcedureMaintain(isProcedureMaintain)
                            .isProcedurePost(isProcedurePost)
                            .build();
                    craftService.oneKeyDefault(oneKeyDefaultDTO);
                } catch (Exception e) {
                    log.error("保存默认工艺工序配置失败, craftProcedureId:{}, msg:{}", craftProcedureId, e.getMessage());
                }
            }

        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            FileUtils.deleteQuietly(templateFile);
            importProgressService.releaseLock(lockKey);
            IMPORT_UNICODE_MAP.remove();
        }
    }

    private boolean saveProcedureMaintainScheme(List<CraftProcedureExcelDTO> canImportRecords, String username) {
        Date now = new Date();
        List<ProcedureMaintainSchemeEntity> saver = canImportRecords.stream()
                .filter(e -> e.getCraftId() != null && e.getProcedureId() != null && e.getMaintainSchemeId() != null)
                .map(e ->
                        ProcedureMaintainSchemeEntity.builder()
                                .craftId(e.getCraftId())
                                .procedureId(e.getCraftProcedureId())
                                .maintainId(e.getMaintainSchemeId())
                                .maintainName(e.getMaintainSchemeName())
                                .build()
                ).collect(Collectors.toList());
        for (ProcedureMaintainSchemeEntity scheme : saver) {
            ProcedureMaintainSchemeEntity old = procedureMaintainSchemeService.lambdaQuery()
                    .eq(ProcedureMaintainSchemeEntity::getProcedureId, scheme.getProcedureId())
                    .last("limit 1").one();
            if (old != null) {
                scheme.setId(old.getId());
                scheme.setUpdateBy(username);
                scheme.setUpdateTime(now);
            } else {
                scheme.setCreateBy(username);
                scheme.setCreateTime(now);
            }
        }
        procedureMaintainSchemeService.saveOrUpdateBatch(saver);
        return CollectionUtils.isEmpty(saver);
    }

    private boolean saveProcedureDefectScheme(List<CraftProcedureExcelDTO> canImportRecords, String username) {
        Date now = new Date();
        List<ProcedureDefectSchemeEntity> saver = canImportRecords.stream()
                .filter(e -> e.getCraftId() != null && e.getProcedureId() != null && e.getDefectSchemeId() != null)
                .map(e ->
                        ProcedureDefectSchemeEntity.builder()
                                .craftId(e.getCraftId())
                                .procedureId(e.getCraftProcedureId())
                                .schemeId(e.getDefectSchemeId())
                                .schemeName(e.getDefectSchemeName())
                                .build()
                ).collect(Collectors.toList());
        for (ProcedureDefectSchemeEntity scheme : saver) {
            ProcedureDefectSchemeEntity old = procedureDefectSchemeService.lambdaQuery()
                    .eq(ProcedureDefectSchemeEntity::getProcedureId, scheme.getProcedureId())
                    .last("limit 1").one();
            if (old != null) {
                scheme.setId(old.getId());
                scheme.setUpdateBy(username);
                scheme.setUpdateTime(now);
            } else {
                scheme.setCreateBy(username);
                scheme.setCreateTime(now);
            }
        }
        procedureDefectSchemeService.saveOrUpdateBatch(saver);
        return CollectionUtils.isEmpty(saver);
    }

    private List<CraftProcedureAssembleExcelDTO> verifyFormatAssemble(List<CraftProcedureAssembleExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureAssembleExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureAssembleExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureAssembleExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }

            // 工装编号
            //dfs_process_assembly工艺装配表
            ProcessAssemblyEntity assemblyEntity = processAssemblyService.lambdaQuery()
                    .eq(ProcessAssemblyEntity::getCode, importDTO.getAssembleCode())
                    .last("limit 1").one();
            if (assemblyEntity == null) {
                importResult.append("系统中不存在对应工装编号；");
                canImport = false;
            } else {
                // 工装名称/类型，单位
                importDTO.setName(assemblyEntity.getName());
                importDTO.setType(assemblyEntity.getType());
                importDTO.setUnit(assemblyEntity.getUnit());
            }


            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private List<CraftProcedureDeviceExcelDTO> verifyFormatDevice(List<CraftProcedureDeviceExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureDeviceExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }
        // 设备模型
        List<String> deviceTypeNames = imports.stream().map(CraftProcedureDeviceExcelDTO::getDeviceTypeName).distinct().collect(Collectors.toList());
        Map<String, Integer> modelNameIdMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(deviceTypeNames)) {
            modelNameIdMap = modelService.lambdaQuery()
                    .eq(ModelEntity::getType, ModelEnum.DEVICE.getType())
                    .in(ModelEntity::getName, deviceTypeNames)
                    .list().stream().collect(Collectors.toMap(ModelEntity::getName, ModelEntity::getId));
        }

        List<CraftProcedureDeviceExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureDeviceExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }

            // 设备类型
            if (!modelNameIdMap.containsKey(importDTO.getDeviceTypeName())) {
                importResult.append("系统中不存在该设备类型；");
                canImport = false;
            } else {
                //设备类型Id
                importDTO.setModelId(modelNameIdMap.get(importDTO.getDeviceTypeName()));
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private List<CraftProcedureParameterExcelDTO> verifyFormatParameter(List<CraftProcedureParameterExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureParameterExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureParameterExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureParameterExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;
            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }

            // 参数编码
            if (StringUtils.isBlank(importDTO.getParameterCoding())) {
                importResult.append("参数编码不能为空；");
                canImport = false;
            } else if (StringUtils.isBlank(importDTO.getIsCheckByProcedureDefine()) || importDTO.getIsCheckByProcedureDefine().equals(Constant.TRUE)) {

                // 工艺参数校验
                // 1、必须是关联的工序定义中已存在的数据
                // 2、如果输入方式为下拉，参数值只能是工序定义里面定义的参数值里面获取
                CraftProcedureEntity craftProcedureEntity = this.getById(importDTO.getCraftProcedureId());
                if (craftProcedureEntity != null) {
                    ProcedureEntity procedureEntity = procedureService.getById(craftProcedureEntity.getProcedureId());
                    ProcedureProcessParameterEntity processParameter = procedureProcessParameterService.lambdaQuery()
                            .eq(ProcedureProcessParameterEntity::getProcedureCode, procedureEntity.getProcedureCode())
                            .eq(ProcedureProcessParameterEntity::getParameterCoding, importDTO.getParameterCoding())
                            .last("limit 1").one();
                    if (Objects.isNull(processParameter) || !processParameter.getParameterName().equals(importDTO.getParameterName())) {
                        importResult.append("工序定义的工艺参数在系统中不存在；");
                        canImport = false;
                    }

                    if (StringUtils.isNotBlank(importDTO.getOptionalValue()) && Objects.nonNull(processParameter)) {
                        List<String> values = Arrays.stream(importDTO.getOptionalValue().split(Constant.SEP)).collect(Collectors.toList());
                        String optionalValues = processParameter.getOptionalValue();
                        if (processParameter.getInputMode().equals(ProcessParameterInputModeEnum.DROP_DOWN_AN_OPTION.getCode())) {
                            if (values.size() > 1) {
                                importResult.append("关联的工序定义，工艺参数的输入方式为单选，可选值只能为1个；");
                                canImport = false;
                            }
                            if (StringUtils.isBlank(optionalValues) || !optionalValues.contains(values.get(0))) {
                                importResult.append("输入的可选值与工序定义中工艺参数的可选值不匹配；");
                                canImport = false;
                            }
                        } else if (processParameter.getInputMode().equals(ProcessParameterInputModeEnum.DROP_DOWN_MULTIPLE_SELECTION.getCode())) {
                            if (StringUtils.isBlank(optionalValues)) {
                                importResult.append("输入的可选值与工序定义中工艺参数的可选值不匹配；");
                                canImport = false;
                            }
                            List<String> optionValues = Arrays.stream(optionalValues.split(Constant.SEP)).collect(Collectors.toList());
                            for (String optionValue : optionValues) {
                                if (!optionValues.contains(optionValue)) {
                                    importResult.append("输入的可选值与工序定义中工艺参数的可选值不匹配；");
                                    canImport = false;
                                }
                            }
                        }
                    }
                }
            }
            // 单位必须是系统中的单位
            if (StringUtils.isNotBlank(importDTO.getUnit())) {
                boolean unitExists = dictService.lambdaQuery().eq(DictEntity::getName, importDTO.getUnit()).exists();
                if (!unitExists) {
                    importResult.append("单位在系统中不存在；");
                    canImport = false;
                }
            }
            // 参数名称
            if (StringUtils.isBlank(importDTO.getParameterName())) {
                importResult.append("参数名称不能为空；");
                canImport = false;
            }
            // 根据业务配置判断参数值是否必填
            FullPathCodeDTO dto = FullPathCodeDTO.builder()
                    .fullPathCode(ConfigConstant.CRAFT_REQUIRED_CONF).build();
            CraftRequiredConfDTO config = businessConfigService.getValueDto(dto, CraftRequiredConfDTO.class);
            if (config.getCraftProcessParameterValueRequired() && StringUtils.isBlank(importDTO.getOptionalValue())) {
                importResult.append("参数值不能为空；");
                canImport = false;
            }
            if (StringUtils.isNotBlank(importDTO.getReferenceUpperStr())) {
                if (!StringUtils.isNumeric(importDTO.getReferenceUpperStr())) {
                    importResult.append("参考值上限需为数值型");
                    canImport = false;
                } else {
                    importDTO.setReferenceUpper(Double.parseDouble(importDTO.getReferenceUpperStr()));
                }
            }
            if (StringUtils.isNotBlank(importDTO.getReferenceLowerStr())) {
                if (!StringUtils.isNumeric(importDTO.getReferenceLowerStr())) {
                    importResult.append("参考值下限需为数值型");
                    canImport = false;
                } else {
                    importDTO.setReferenceLower(Double.parseDouble(importDTO.getReferenceLowerStr()));
                }
            }
            if (StringUtils.isNotBlank(importDTO.getDeviceModelName())) {
                ModelEntity deviceModel = modelService.getDeviceModelByName(importDTO.getDeviceModelName());
                if (deviceModel == null) {
                    importResult.append(String.format("设备模型名称: [%s]不存在", importDTO.getDeviceModelName()));
                    canImport = false;
                } else {
                    importDTO.setDeviceModelId(deviceModel.getId());
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    private List<CraftProcedureHumanResourcesExcelDTO> verifyFormatHumanResource(List<CraftProcedureHumanResourcesExcelDTO> imports) {
        List<CraftEntity> craftEntities = new ArrayList<>();
        Map<Integer, List<CraftProcedureEntity>> craftIdProceduresMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(imports)) {
            List<String> craftCodes = imports.stream().map(CraftProcedureHumanResourcesExcelDTO::getCraftCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(craftCodes)) {
                craftEntities = craftService.lambdaQuery().in(CraftEntity::getCraftCode, craftCodes).list();
                if (!CollectionUtils.isEmpty(craftEntities)) {
                    List<Integer> craftIds = craftEntities.stream().map(CraftEntity::getCraftId).collect(Collectors.toList());
                    craftIdProceduresMap = this.lambdaQuery()
                            .in(CraftProcedureEntity::getCraftId, craftIds)
                            .list()
                            .stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
                }
            }
        }

        List<CraftProcedureHumanResourcesExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (CraftProcedureHumanResourcesExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 如存在唯一标识，则无视工艺编码，工序名称
            if(StringUtils.isNotBlank(importDTO.getUniCode()) && IMPORT_UNICODE_MAP.get() != null) {
                CraftProcedureExcelDTO craftProcedureExcel = IMPORT_UNICODE_MAP.get().get(importDTO.getUniCode());
                if(craftProcedureExcel == null) {
                    importResult.append("唯一标识符未匹配到工艺工序的标识；");
                    canImport = false;
                }else {
                    importDTO.setCraftCode(craftProcedureExcel.getCraftCode());
                    importDTO.setCraftId(craftProcedureExcel.getCraftId());
                    importDTO.setCraftProcedureName(craftProcedureExcel.getProcedureName());
                    importDTO.setCraftProcedureId(craftProcedureExcel.getCraftProcedureId());
                }
            }else {
                // 工艺编码
                if (StringUtils.isBlank(importDTO.getCraftCode())) {
                    importResult.append("工艺编码不能为空；");
                    canImport = false;
                } else {
                    List<CraftEntity> crafts = craftEntities.stream().filter(res -> res.getCraftCode().equals(importDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(crafts)) {
                        importResult.append("工艺编码必须在系统中存在；");
                        canImport = false;
                    } else {
                        importDTO.setCraftId(crafts.get(0).getCraftId());
                    }
                }
                // 工序名称
                if (StringUtils.isBlank(importDTO.getCraftProcedureName())) {
                    importResult.append("工序名称不能为空；");
                    canImport = false;
                } else {
                    List<CraftProcedureEntity> craftProcedures = craftIdProceduresMap.getOrDefault(importDTO.getCraftId(), new ArrayList<>())
                            .stream().filter(res -> res.getProcedureName().equals(importDTO.getCraftProcedureName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftProcedures)) {
                        importResult.append("工序必须在工艺编码对应的工艺工序中；");
                        canImport = false;
                    } else {
                        if (craftProcedures.size() > 1) {
                            importResult.append("工艺中存在多个与该工序名称相同的工艺工序；");
                            canImport = false;
                        } else {
                            importDTO.setCraftProcedureId(craftProcedures.get(0).getId());
                        }
                    }
                }
            }

            //校验岗位编码
            if (StringUtils.isBlank(importDTO.getPositionCode())) {
                importResult.append("岗位编码不能为空；");
                canImport = false;
            } else {
                SysPostEntity postEntity = sysPostService.lambdaQuery()
                        .eq(SysPostEntity::getPostCode, importDTO.getPositionCode())
                        .last("limit 1")
                        .one();
                if (Objects.isNull(postEntity)) {
                    importResult.append("系统中不存在该岗位编码；");
                    canImport = false;
                } else {
                    // 岗位id,名称
                    importDTO.setPositionId(postEntity.getId());
                    importDTO.setPositionLevel(postEntity.getPostLevel());
                    importDTO.setPositionName(postEntity.getPostName());
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    /**
     * 保存工艺参数，存在则更新，否则新增
     */
    private void saveParameterDevices(CraftProcedureParameterExcelDTO dto) {
        ProcessParameterConfigEntity build = ProcessParameterConfigEntity.builder()
                .craftId(dto.getCraftId())
                .craftProcedureId(dto.getCraftProcedureId())
                .parameterCoding(dto.getParameterCoding())
                .parameterName(dto.getParameterName())
                .parameterValue(dto.getOptionalValue())
                .unit(dto.getUnit())
                .remark(dto.getRemark())
                .sort(dto.getSort())
                .deviceModelId(dto.getDeviceModelId())
                .referenceUpper(dto.getReferenceUpper())
                .referenceLower(dto.getReferenceLower())
                .build();
        ProcessParameterConfigEntity parameterConfigEntity = processParameterConfigService.lambdaQuery().eq(ProcessParameterConfigEntity::getCraftProcedureId, String.valueOf(dto.getCraftProcedureId()))
                .eq(ProcessParameterConfigEntity::getParameterCoding, dto.getParameterCoding())
                .one();
        if (Objects.isNull(parameterConfigEntity)) {
            //新增
            processParameterConfigService.save(build);
        } else {
            build.setId(parameterConfigEntity.getId());
            processParameterConfigService.lambdaUpdate().eq(ProcessParameterConfigEntity::getCraftProcedureId, String.valueOf(dto.getCraftProcedureId()))
                    .eq(ProcessParameterConfigEntity::getParameterCoding, dto.getParameterCoding())
                    .set(ProcessParameterConfigEntity::getUnit, dto.getUnit())
                    .set(ProcessParameterConfigEntity::getRemark, dto.getRemark())
                    .set(ProcessParameterConfigEntity::getSort, dto.getSort())
                    .set(ProcessParameterConfigEntity::getParameterValue, dto.getOptionalValue())
                    .update();
        }
    }

    /**
     * 保存工序控制
     */
    private void saveProcedureControls(CraftProcedureControlExcelDTO insertImport) {
        ProcedureControllerConfigEntity build = ProcedureControllerConfigEntity.builder()
                .craftProcedureId(insertImport.getCraftProcedureId())
                .jumpStationCheck(insertImport.getJumpStationCheck())
                .reformCheck(insertImport.getReformCheck())
                .materialCheck(insertImport.getMaterialCheck())
                .productionCheck(insertImport.getProductionCheck())
                .lastValueCheck(insertImport.getLastValueCheck())
                .standardCirculationDurationType(insertImport.getStandardCirculationDurationType())
                .standardCirculationDuration(Objects.isNull(insertImport.getStandardCirculationDuration()) ? 0 : insertImport.getStandardCirculationDuration())
                .flowTimeoutThresholdConfiguration(Objects.isNull(insertImport.getFlowTimeoutThresholdConf()) ? 0 : insertImport.getFlowTimeoutThresholdConf())
                .productionTimeoutThresholdConfiguration(Objects.isNull(insertImport.getProductTimeoutThresholdConf()) ? 0 : insertImport.getProductTimeoutThresholdConf())
                .flowTimeoutThresholdConfigurationUnitType(insertImport.getFlowTimeoutThresholdConfUnitTypeCode())
                .productionTimeoutThresholdConfigurationUnit(insertImport.getProductTimeoutThresholdConfUnitTypeCode())
                .maxFailCount(insertImport.getMaxFailCount())
                .conversionFactor(Objects.isNull(insertImport.getConversionFactor()) ? 1 : insertImport.getConversionFactor())
                .createTime(Objects.nonNull(insertImport.getCreateTime()) ? insertImport.getCreateTime() : new Date())
                .build();
        ProcedureControllerConfigEntity controllerConfigEntity = procedureControllerConfigService.getEntityByCraftProcedureId(insertImport.getCraftProcedureId());
        if (Objects.isNull(controllerConfigEntity)) {
            //新增
            procedureControllerConfigService.save(build);
        } else {
            build.setId(controllerConfigEntity.getId());
            procedureControllerConfigService.edit(build);
        }
    }

    /**
     * 保存工序控制
     */
    private void saveCraftTempMaterials(List<CraftProcedureTempMaterialExcelDTO> insertImports) {
        Map<String, List<CraftProcedureTempMaterialExcelDTO>> map = insertImports.stream().collect(Collectors.groupingBy(CraftProcedureTempMaterialExcelDTO::getCraftCode));
        for (Map.Entry<String, List<CraftProcedureTempMaterialExcelDTO>> entry : map.entrySet()) {
            craftService.bindMaterial(CraftBindMaterialDTO.builder()
                    .craftCode(entry.getKey())
                    .materialCodes(entry.getValue().stream().map(CraftProcedureTempMaterialExcelDTO::getMaterialCode)
                            .collect(Collectors.toList()))
                    .build());
        }

    }

    /**
     * 保存工序检验
     */
    private void saveProcedureInspects(CraftProcedureInspectExcelDTO insertImport, String username) {
        Date nowDate = new Date();
        ProcedureInspectionConfigEntity build = ProcedureInspectionConfigEntity.builder()
                .craftProcedureId(insertImport.getCraftProcedureId())
                .craftId(insertImport.getCraftId())
                .procedureInspectionId(insertImport.getInspectId())
                .inspectionCode(insertImport.getInspectCode())
                .inspectionName(insertImport.getInspectionName())
                .inspectionStandard(insertImport.getInspectionStandard())
                .inspectionInstrument(insertImport.getInspectionInstrument())
                .dataType(insertImport.getDataType())
                .inspectType(insertImport.getInspectTypes())
                .description(insertImport.getDescription())
                .comparator(InspectComparatorEnum.getCodeByName(insertImport.getComparatorName()))
                .standardValue(insertImport.getStandardValue())
                .upperLimit(insertImport.getUpperLimit())
                .downLimit(insertImport.getDownLimit())
                .defaultValue(insertImport.getDefaultValue())
                .ngController(insertImport.getNgController())
                .unit(insertImport.getUnit())
                .valueCheck(insertImport.getValueCheck())
                .defectJudgeType(insertImport.getDefectJudgeType())
                .isDefect(insertImport.getIsDefect())
                .resultUpdate(insertImport.getResultUpdate())
                .updateBy(username)
                .updateTime(nowDate)
                .createTime(Objects.nonNull(insertImport.getCreateTime()) ? insertImport.getCreateTime() : new Date())
                .build();
        ProcedureInspectionConfigEntity inspectionConfig = procedureInspectionConfigService.lambdaQuery()
                .eq(ProcedureInspectionConfigEntity::getCraftProcedureId, insertImport.getCraftProcedureId())
                .eq(ProcedureInspectionConfigEntity::getInspectionCode, insertImport.getInspectCode())
                .last("limit 1").one();
        if (Objects.isNull(inspectionConfig)) {
            build.setCreateBy(username);
            build.setUpdateTime(nowDate);
            procedureInspectionConfigService.save(build);
        } else {
            build.setId(inspectionConfig.getId());
            procedureInspectionConfigService.updateById(build);
        }
    }

    private void saveProcedureControllerInspects(CraftProcedureInspectControllerExcelDTO insertImport) {
        CraftProcedureInspectControllerEntity build = CraftProcedureInspectControllerEntity.builder()
                .craftId(insertImport.getCraftId())
                .craftProcedureId(insertImport.getCraftProcedureId())
                .inspectTypeCode(insertImport.getInspectType())
                .inspectTypeName(insertImport.getInspectTypeName())
                .inspectMethodCode(insertImport.getInspectMethodCodes())
                .inspectSchemeName(insertImport.getInspectSchemeNames())
                .inspectTriggerConditionCode(insertImport.getInspectTriggerConditionCodes())
                .build();
        CraftProcedureInspectControllerEntity inspectController = craftProcedureInspectControllerService.lambdaQuery()
                .eq(CraftProcedureInspectControllerEntity::getCraftProcedureId, insertImport.getCraftProcedureId())
                .eq(CraftProcedureInspectControllerEntity::getInspectTypeCode, insertImport.getInspectType())
                .last("limit 1").one();
        if (Objects.isNull(inspectController)) {
            craftProcedureInspectControllerService.save(build);
        } else {
            // 存在就更新
            build.setId(inspectController.getId());
            craftProcedureInspectControllerService.updateById(build);
        }
    }

    /**
     * 保存工序工时
     */
    private void saveWorkDurations(CraftProcedureWorkDurationExcelDTO insertImport) {
        ProcedureRelationWorkHoursEntity build = ProcedureRelationWorkHoursEntity.builder()
                .craftId(insertImport.getCraftId())
                .procedureId(insertImport.getCraftProcedureId())
                .preparationTime(insertImport.getPreparationTime())
                .preparationTimeUnit(insertImport.getPreparationTimeUnit())
                .processingHours(insertImport.getProcessingHours())
                .processingHoursUnit(insertImport.getProcessingHoursUnit())
                .defaultTime(insertImport.getDefaultTime())
                .defaultTimeUnit(insertImport.getDefaultTimeUnit())
                .degreeOfDifficulty(insertImport.getDegreeOfDifficulty())
                .isByWork(insertImport.getIsCalculate())
                .inputNum(insertImport.getInputNum())
                .theoryHoursUnit(insertImport.getProcessingHoursUnit())
                .build();
        if (build.getProcessingHours() != null && build.getInputNum() != null) {
            double theoryHours = MathUtil.mul(build.getProcessingHours(), build.getInputNum());
            build.setTheoryHours(theoryHours);
        }
        // 新增或更新
        procedureRelationWorkHoursService.updateEntityById(build);
    }

    /**
     * 保存工序人力
     */
    private void saveHumanResources(List<CraftProcedureHumanResourcesExcelDTO> resourcesExcels) {
        ArrayList<ProcedurePostEntity> entities = new ArrayList<>();
        for (CraftProcedureHumanResourcesExcelDTO dto : resourcesExcels) {
            ProcedurePostEntity build = ProcedurePostEntity.builder()
                    .craftId(dto.getCraftId())
                    .procedureId(dto.getCraftProcedureId())
                    .postId(dto.getPositionId())
                    .postCode(dto.getPositionCode())
                    .postName(dto.getPositionName())
                    .postLevel(dto.getPositionLevel())
                    .number(Optional.ofNullable(dto.getCount()).map(Double::intValue).orElse(1))
                    .build();
            entities.add(build);
            procedurePostService.lambdaUpdate()
                    .eq(ProcedurePostEntity::getCraftId, dto.getCraftId())
                    .eq(ProcedurePostEntity::getProcedureId, dto.getCraftProcedureId())
                    .remove();
        }
        if (!CollectionUtils.isEmpty(entities)) {
            procedurePostService.saveBatch(entities);
        }
    }

    /**
     * 保存工序设备数据
     */
    private void saveProcedureDevices(List<CraftProcedureDeviceExcelDTO> deviceExcels) {
        List<ProcedureDeviceTypeEntity> entities = new ArrayList<>();
        for (CraftProcedureDeviceExcelDTO dto : deviceExcels) {
            ProcedureDeviceTypeEntity build = ProcedureDeviceTypeEntity.builder()
                    .craftId(dto.getCraftId())
                    .procedureId(dto.getCraftProcedureId())
                    .deviceModelId(dto.getModelId())
                    .deviceTypeName(dto.getDeviceTypeName())
//                    .number(dto.getCount().intValue())
                    .build();
            entities.add(build);
            procedureDeviceTypeService.lambdaUpdate()
                    .eq(ProcedureDeviceTypeEntity::getCraftId, dto.getCraftId())
                    .eq(ProcedureDeviceTypeEntity::getProcedureId, dto.getCraftProcedureId())
                    .remove();
        }
        if (!CollectionUtils.isEmpty(entities)) {
            procedureDeviceTypeService.saveBatch(entities);
        }
    }

    /**
     * 保存工序工装
     */
    private void saveCpAssembles(List<CraftProcedureAssembleExcelDTO> assembleExcels) {
        Date date = new Date();
        List<ProcedureProcessAssemblyEntity> entities = new ArrayList();
        for (CraftProcedureAssembleExcelDTO dto : assembleExcels) {
            ProcedureProcessAssemblyEntity build = ProcedureProcessAssemblyEntity.builder()
                    .craftId(dto.getCraftId())
                    .procedureId(dto.getCraftProcedureId())
                    .processAssemblyCode(dto.getAssembleCode())
                    .processAssemblyName(dto.getName())
                    .processAssemblyType(dto.getType())
                    .number(dto.getCount())
                    .unit(dto.getUnit())
                    .createTime(date)
                    .updateTime(date)
                    .build();
            entities.add(build);
            procedureProcessAssemblyService.lambdaUpdate()
                    .eq(ProcedureProcessAssemblyEntity::getCraftId, dto.getCraftId())
                    .eq(ProcedureProcessAssemblyEntity::getProcedureId, dto.getCraftProcedureId())
                    .remove();
        }
        if (!CollectionUtils.isEmpty(entities)) {
            procedureProcessAssemblyService.saveBatch(entities);
        }
    }

    /**
     * 保存导入的数据
     */
    private int saveCraftProcedureImportDTO(List<CraftProcedureExcelDTO> dtos, String username, Boolean isTemplate) {
        List<String> lineModelNames = dtos.stream().map(CraftProcedureExcelDTO::getLineModelName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, Integer> lineModelMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(lineModelNames)) {
            List<ModelEntity> modelEntities = modelService.lambdaQuery()
                    .eq(ModelEntity::getType, ModelEnum.LINE.getType())
                    .in(ModelEntity::getName, lineModelNames).list();
            lineModelMap = modelEntities.stream().collect(Collectors.toMap(ModelEntity::getName, ModelEntity::getId, (o1, o2) -> o2));
        }
        //按工艺分组
        Map<String, List<CraftProcedureExcelDTO>> map = dtos.stream().collect(Collectors.groupingBy(CraftProcedureExcelDTO::getCraftCode));
        Date date = new Date();
        Map<String, CraftProcedureExcelDTO> importUnicodeMap = new HashMap<>();
        for (Map.Entry<String, List<CraftProcedureExcelDTO>> entry : map.entrySet()) {
            // 先保存工艺
            List<CraftProcedureExcelDTO> craftProcedures = entry.getValue();
            CraftProcedureExcelDTO excelDTO = craftProcedures.get(0);
            // 保存表中的工艺
            Integer craftId = saveOrUpdateExcelCraft(excelDTO, username, isTemplate);
            List<CraftProcedureEntity> insertCraftProcedures = new ArrayList();
            if (craftId != null) {
                // 再保存工艺工序
                for (CraftProcedureExcelDTO dto : craftProcedures) {
                    CraftProcedureEntity craftProcedureEntity = setExcelCraftProcedureEntity(craftId, dto, username, lineModelMap, date);
                    this.saveOrUpdate(craftProcedureEntity);
                    // 将工艺id,工艺工序id处理到dto中去,返回给其他表使用,便可将序列号,工艺工序id等封装到一起
                    dto.setCraftId(craftId);
                    dto.setCraftProcedureId(craftProcedureEntity.getId());
                    dto.setProcedureName(craftProcedureEntity.getProcedureName());
                    dto.setIsLastProcedure(craftProcedureEntity.getIsLastProcedure());
                    insertCraftProcedures.add(craftProcedureEntity);
                    // 唯一标识
                    if(StringUtils.isNotBlank(dto.getUniCode())) {
                        importUnicodeMap.put(dto.getUniCode(), dto);
                    }
                }
            }

            // 处理上下级工序关系
            Map<Integer, CraftProcedureEntity> craftProcedureIdEntityMap = insertCraftProcedures.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, v -> v));
            for (CraftProcedureExcelDTO craftProcedure : craftProcedures) {
                List<Integer> nextCraftProcedureIds = new ArrayList<>(), supCraftProcedureIds = new ArrayList<>();
                List<String> nextProcedureNames = new ArrayList<>(), subProcedureNames = new ArrayList<>();
                if (StringUtils.isNotEmpty(craftProcedure.getNextIndexs())) {
                    List<String> nextIndexs = Arrays.stream(craftProcedure.getNextIndexs().split(Constant.SEP)).collect(Collectors.toList());
                    for (CraftProcedureExcelDTO procedure : craftProcedures) {
                        if (nextIndexs.contains(procedure.getIndex())) {
                            nextCraftProcedureIds.add(procedure.getCraftProcedureId());
                            nextProcedureNames.add(procedure.getProcedureName());
                        }
                    }
                }
                if (StringUtils.isNotEmpty(craftProcedure.getSupIndexs())) {
                    List<String> supIndexs = Arrays.stream(craftProcedure.getSupIndexs().split(Constant.SEP)).collect(Collectors.toList());
                    for (CraftProcedureExcelDTO procedure : craftProcedures) {
                        if (supIndexs.contains(procedure.getIndex())) {
                            supCraftProcedureIds.add(procedure.getCraftProcedureId());
                            subProcedureNames.add(procedure.getProcedureName());
                        }
                    }
                }

                CraftProcedureEntity craftProcedureEntity = craftProcedureIdEntityMap.get(craftProcedure.getCraftProcedureId());
                craftProcedureEntity.setSupProcedureId(supCraftProcedureIds.stream().map(String::valueOf).collect(Collectors.joining(Constant.SEP)));
                craftProcedureEntity.setSupProcedureName(String.join(Constant.SEP, subProcedureNames));
                //下级工序
                if (!CollectionUtils.isEmpty(nextCraftProcedureIds)) {
                    craftProcedureEntity.setNextProcedureId(nextCraftProcedureIds.stream().map(String::valueOf).collect(Collectors.joining(Constant.SEP)));
                    craftProcedureEntity.setNextProcedureName(String.join(Constant.SEP, nextProcedureNames));
                    craftProcedureEntity.setIsLastProcedure(Constant.NO);
                } else {
                    craftProcedureEntity.setIsLastProcedure(Constant.YES);
                }
                this.updateById(craftProcedureEntity);

                // 添加默认数据，以防用户不填写导致一些业务上的报错
                // 工序控制
                boolean existsControllerConfig = procedureControllerConfigService.lambdaQuery().eq(ProcedureControllerConfigEntity::getCraftProcedureId, craftProcedureEntity.getId()).exists();
                if (!existsControllerConfig) {
                    procedureControllerConfigService.save(ProcedureControllerConfigEntity.builder()
                            .craftProcedureId(craftProcedureEntity.getId())
                            .build());
                }
                // 工序工时
                boolean existsWorkHours = procedureRelationWorkHoursService.lambdaQuery()
                        .eq(ProcedureRelationWorkHoursEntity::getCraftId, craftProcedureEntity.getCraftId())
                        .eq(ProcedureRelationWorkHoursEntity::getProcedureId, craftProcedureEntity.getId())
                        .exists();
                if (!existsWorkHours) {
                    FullPathCodeDTO dto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.CRAFT_DEFAULT_CONF).build();
                    CraftDefaultConfigDTO configDTO = businessConfigService.getValueDto(dto, CraftDefaultConfigDTO.class);
                    procedureRelationWorkHoursService.save(
                            ProcedureRelationWorkHoursEntity.builder()
                                    .craftId(craftProcedureEntity.getCraftId())
                                    .procedureId(craftProcedureEntity.getId())
                                    .defaultTimeUnit(configDTO.getCraftWorkHourDefaultTimeUnitDefault())
                                    .preparationTimeUnit(configDTO.getCraftWorkHourPreparationTimeUnitDefault())
                                    .processingHoursUnit(configDTO.getCraftWorkHourProcessingHoursUnitDefault())
                                    .build());
                }
                // 工序检验控制, 不存在的才保存
                List<CraftProcedureInspectControllerEntity> list = craftProcedureInspectControllerService.lambdaQuery()
                        .eq(CraftProcedureInspectControllerEntity::getCraftId, craftProcedureEntity.getCraftId())
                        .eq(CraftProcedureInspectControllerEntity::getCraftProcedureId, craftProcedureEntity.getId())
                        .list();
                Set<Integer> existInspectTypeCode = list.stream().map(CraftProcedureInspectControllerEntity::getInspectTypeCode).collect(Collectors.toSet());
                List<CraftProcedureInspectControllerEntity> noExistInspectControllers = Arrays.stream(InspectionsSchemeTypeEnum.values())
                        .filter(e -> !existInspectTypeCode.contains(e.getCode())).map(e -> CraftProcedureInspectControllerEntity.builder()
                                .craftId(craftProcedureEntity.getCraftId())
                                .craftProcedureId(craftProcedureEntity.getId())
                                .inspectTypeCode(e.getCode())
                                .inspectTypeName(e.getName())
                                .build()
                        ).collect(Collectors.toList());
                craftProcedureInspectControllerService.saveBatch(noExistInspectControllers);
            }
        }
        IMPORT_UNICODE_MAP.remove();
        IMPORT_UNICODE_MAP.set(importUnicodeMap);
        return map.size();
    }

    /**
     * 保存表中的工艺工序
     */
    private CraftProcedureEntity setExcelCraftProcedureEntity(Integer craftId, CraftProcedureExcelDTO dto, String username, Map<String, Integer> lineModelMap, Date date) {
        CraftProcedureEntity craftProcedureEntity = CraftProcedureEntity.builder()
                .craftId(craftId)
                .procedureId(dto.getProcedureId())
                .alias(StringUtils.isNotBlank(dto.getProcedureAlias()) ? dto.getProcedureAlias() : dto.getAlias())
                .facType(dto.getFacModelName())
                .whetherImport(true)
                // 生产基本单元类型
                .type(dto.getBasicUnitType())
                .isSubContractingOperation(dto.getIsOutsourcing())
                .isInspectionOperation(dto.getIsInspectionOperation())
                .isQualityProcess(dto.getIsQualityProcess())
                .isMaintenanceProcedure(dto.getIsMaintenanceProcedure())
                .outSourcingSupplierCode(dto.getOutsourcingSupplierCodes())
                .outSourcingSupplier(dto.getOutsourcingSupplierNames())
                .remark(dto.getRemark())
                .build();
        // 如果存在别名，用别名进行区分，找到唯一值
        CraftProcedureEntity old = this.lambdaQuery().eq(CraftProcedureEntity::getCraftId, craftId)
                .eq(StringUtils.isBlank(dto.getProcedureAlias()), CraftProcedureEntity::getProcedureName, dto.getProcedureName())
                .eq(StringUtils.isNotBlank(dto.getProcedureAlias()), CraftProcedureEntity::getAlias, dto.getProcedureAlias())
                .last("limit 1").one();
        // 是否更新
        if (dto.getIsUpdate()) {
            craftProcedureEntity.setProcedureName(dto.getProcedureName());
            if (old != null) {
                craftProcedureEntity.setId(old.getId());
                craftProcedureEntity.setUpdateTime(dto.getUpdateTime());
                craftProcedureEntity.setUpdateBy(username);
            } else {
                craftProcedureEntity.setCreateTime(dto.getUpdateTime());
                craftProcedureEntity.setCreateBy(username);
            }
        } else {
            craftProcedureEntity.setCreateTime(dto.getUpdateTime());
            craftProcedureEntity.setCreateBy(username);
            // 存在相同工序的，加上01，02做区分
            craftProcedureEntity.setProcedureName(old == null ? dto.getProcedureName() : codeFactory.getProcedureNumber(craftId, dto.getProcedureName()));
        }
        if (WorkCenterTypeEnum.LINE.getCode().equals(craftProcedureEntity.getType())) {
            // 产线模型id
            Integer lineModelId = lineModelMap.get(dto.getLineModelName());
            craftProcedureEntity.setLineModelId(lineModelId);
            craftProcedureEntity.setLineModelName(dto.getLineModelName());
            //工作中心ID/名称
            if (lineModelId != null) {
                List<WorkCenterEntity> centerEntities = workCenterService.lambdaQuery()
                        .select(WorkCenterEntity::getId, WorkCenterEntity::getLineModelId, WorkCenterEntity::getName).list();
                List<WorkCenterEntity> includeCenterEntities = new ArrayList<>();
                for (WorkCenterEntity centerEntity : centerEntities) {
                    if (StringUtils.isBlank(centerEntity.getLineModelId())) {
                        continue;
                    }
                    List<String> lineModelIds = Arrays.asList(centerEntity.getLineModelId().split(Constants.SEP));
                    if (lineModelIds.contains(lineModelId.toString())) {
                        includeCenterEntities.add(centerEntity);
                    }
                }
                String ids = includeCenterEntities.stream().map(o -> o.getId().toString()).collect(Collectors.joining(Constant.SEP));
                String names = includeCenterEntities.stream().map(WorkCenterEntity::getName).collect(Collectors.joining(Constant.SEP));
                craftProcedureEntity.setWorkCenterIds(ids);
                craftProcedureEntity.setWorkCenterNames(names);
            }
        }
        if (WorkCenterTypeEnum.TEAM.getCode().equals(craftProcedureEntity.getType())
                || WorkCenterTypeEnum.DEVICE.getCode().equals(craftProcedureEntity.getType())) {
            // 工作中心ID
            String workCenterName = dto.getWorkCenterNames();
            craftProcedureEntity.setWorkCenterNames(workCenterName);
            if (StringUtils.isNotBlank(workCenterName)) {
                List<WorkCenterEntity> centerEntities = workCenterService.lambdaQuery()
                        .select(WorkCenterEntity::getId)
                        .in(WorkCenterEntity::getName, Arrays.asList(workCenterName.split(Constant.SEP))).list();
                String ids = centerEntities.stream().map(o -> o.getId().toString()).collect(Collectors.joining(Constant.SEP));
                craftProcedureEntity.setWorkCenterIds(ids);
            }
        }
        return craftProcedureEntity;
    }

    /**
     * 保存表中的工艺
     *
     * @param username
     * @param excelDTO
     * @return
     */
    private Integer saveOrUpdateExcelCraft(CraftProcedureExcelDTO excelDTO, String username, Boolean isTemplate) {
        CraftEntity craftEntity = CraftEntity.builder()
                .craftCode(excelDTO.getCraftCode())
                .name(excelDTO.getCraftName())
                .craftVersion(excelDTO.getVersion())
                // 类型不填则默认为“正常”
                .type(excelDTO.getType())
                .materialId(excelDTO.getMaterialId())
                .materialCode(excelDTO.getMaterialCode())
                .state(excelDTO.getState())
                .craftDesc(excelDTO.getCraftDesc())
                .isTemplate(isTemplate)
                .procedureControllerCheck(excelDTO.getProcedureControllerCheck())
                .createTime(excelDTO.getCreateTime())
                .build();

        String importType;
        // 校验时，第2次分组过滤, 会赋予是否更新
        if (excelDTO.getIsUpdate()) {
            craftEntity.setCraftId(excelDTO.getCraftId());
            craftEntity.setUpdateTime(excelDTO.getUpdateTime());
            craftEntity.setUpdateBy(username);
            // 替代时，删除旧数据
            if (CraftUpdateTypeEnum.REPLACE.getCode().equals(excelDTO.getUpdateType())) {
                craftService.removeEntityById((excelDTO.getCraftId()));
                importType = "替代";
            } else {
                importType = "更新";
            }
            craftService.saveOrUpdate(craftEntity);
            craftService.initOriginInfo(craftEntity);
        } else {
            craftEntity.setCreateBy(username);
            if (approveConfigService.getConfigByCode(ApproveModuleEnum.CRAFT.getCode())) {
                craftEntity.setApprover(username);
                if (CraftStateEnum.CREATE.getCode() == craftEntity.getState()) {
                    craftEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                } else {
                    craftEntity.setApprovalStatus(ApprovalStatusEnum.APPROVED.getCode());
                    craftEntity.setActualApprover(username);
                    craftEntity.setApprovalTime(new Date());
                }
            }
            importType = "新增";
            // 校验时，第2次分组过滤, 如果存在会赋予craftId
            craftService.save(craftEntity);
            craftService.initOriginInfo(craftEntity);

        }
        // 工艺路线里绑定的工序需要绑定工序组
        craftService.bindCraftProcedureGroup(craftEntity.getCraftId());
        // 物料有无生效状态的工艺 事件
        applicationContext.publishEvent(new MaterialHaveReleasedStateCraftEvent(this, craftEntity));
        // 版本变更记录
        craftService.refreshReleasedCraft(Collections.singletonList(craftEntity.getCraftId()));
        versionChangeRecordService.changeVersion(VersionChangeDTO.builder()
                .relateType(VersionModelIdEnum.CRAFT)
                .description("工艺导入 - " + importType)
                .relateId(craftEntity.getCraftId())
                .versionCurrent(craftEntity.getCraftVersion())
                .editor(username)
                .build()
        );
        // 生效的工艺，关联的物料需要取消关联工艺模板
        if (StringUtils.isNotBlank(craftEntity.getMaterialCode()) && CraftStateEnum.RELEASED.getCode() == craftEntity.getState()) {
            craftMaterialRelationService.lambdaUpdate().eq(CraftMaterialRelationEntity::getMaterialCode, craftEntity.getMaterialCode()).remove();
        }
        //如果保存成功，主键会有值
        return craftEntity.getCraftId();
    }


    private List<CraftProcedureExcelDTO> verifyFormatCraftProcedure(List<CraftProcedureExcelDTO> allImports, Boolean isTemplate) {
        //所有委外供商code
        Map<String, String> existCodeSupplierNameMap = new HashMap<>();
        List<String> existOutSourcingCodes = supplierRelatedTypeService.lambdaQuery()
                .eq(SupplierRelatedTypeEntity::getTypeCode, SupplierTypeEnum.OUTSOURCING_SUPPLIER.getCode())
                .list().stream().map(SupplierRelatedTypeEntity::getSupplierCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(existOutSourcingCodes)) {
            // 获取委外供应商名称
            existCodeSupplierNameMap = supplierService.lambdaQuery()
                    .in(SupplierEntity::getCode, existOutSourcingCodes)
                    .list().stream()
                    .collect(Collectors.toMap(SupplierEntity::getCode, SupplierEntity::getName));
        }
        Map<String, MaterialEntity> materialCodeMaterialMap = new HashMap<>();

        // 1. 第1次过滤：先把没有工艺的过滤过来，避免后面分组有空指针
        List<CraftProcedureExcelDTO> imports1 = allImports.stream().peek(e -> {
            if (StringUtils.isBlank(e.getCraftCode())) {
                e.setVerifyPass(false);
                e.setImportResult("工艺编码不能为空;");
            }
        }).filter(e -> !Boolean.FALSE.equals(e.getVerifyPass())).collect(Collectors.toList());

        // 2. 第2次过滤：（按工艺分组.处理updateTime + 状态）: 只要工艺组上有一个存在updateTime, 则默认所有的都有, 并且以第一个出现的为准
        Map<String, List<CraftProcedureExcelDTO>> craftGroup = imports1.stream().collect(Collectors.groupingBy(
                CraftProcedureExcelDTO::getCraftCode,
                LinkedHashMap::new,
                Collectors.toList()
        ));
        Date date = new Date();
        for (Map.Entry<String, List<CraftProcedureExcelDTO>> entry : craftGroup.entrySet()) {
            List<CraftProcedureExcelDTO> procedures = entry.getValue();
            String updateTypeName = procedures.stream().map(CraftProcedureExcelDTO::getUpdateTypeName).filter(Objects::nonNull).findFirst().orElse(null);
            String stateName = procedures.stream().map(CraftProcedureExcelDTO::getStateName).filter(Objects::nonNull).findFirst().orElse(null);
            Date createTime = procedures.stream().map(CraftProcedureExcelDTO::getCreateTime).filter(Objects::nonNull).findFirst().orElse(null);
            CraftEntity oldCraft = craftService.lambdaQuery()
                    .eq(CraftEntity::getCraftCode, entry.getKey())
                    .last("limit 1").one();
            boolean isUpdate = false;
            boolean canImport = true;
            String updateType = null;
            Boolean oldStateReleased = null;
            StringBuilder importResult = new StringBuilder();
            CraftStateEnum stateEnum = null;
            Integer craftId = null;
            if (oldCraft == null) {
                // 之前不存在, 则直接插入
                isUpdate = false;
            } else {
                // 之前存在
                // 默认为更新
                if (StringUtils.isBlank(updateTypeName) || CraftUpdateTypeEnum.UPDATE.getName().equals(updateTypeName)) {
                    isUpdate = true;
                    updateType = CraftUpdateTypeEnum.UPDATE.getCode();
                } else if (CraftUpdateTypeEnum.REPLACE.getName().equals(updateTypeName)) {
                    isUpdate = true;
                    updateType = CraftUpdateTypeEnum.REPLACE.getCode();
                } else {
                    canImport = false;
                    importResult.append("更新方式不合法;");
                }
                craftId = oldCraft.getCraftId();
            }
            // 状态
            if (StringUtils.isBlank(stateName)) {
                canImport = false;
                importResult.append("状态不能为空;");
            } else {
                stateEnum = CraftStateEnum.getByName(stateName);
                if (!(CraftStateEnum.CREATE == stateEnum || CraftStateEnum.RELEASED == stateEnum)) {
                    importResult.append("工艺状态只能是创建或者生效;");
                    canImport = false;
                } else {
                    // 更新的场景
                    if (isUpdate) {
                        // 旧状态为生效
                        if (Objects.equals(CraftStateEnum.RELEASED.getCode(), oldCraft.getState())) {
                            oldStateReleased = true;
                            if (CraftStateEnum.CREATE == stateEnum) {
                                importResult.append("工艺状态不能从生效->创建;");
                                canImport = false;
                            }
                        }
                    }
                }
            }
            for (CraftProcedureExcelDTO e : procedures) {
                e.setIsUpdate(isUpdate);
                e.setVerifyPass(canImport);
                e.setImportResult(importResult.toString());
                e.setCraftId(craftId);
                e.setOldStateReleased(oldStateReleased);
                e.setState(Optional.ofNullable(stateEnum).map(CraftStateEnum::getCode).orElse(null));
                e.setUpdateType(updateType);
                e.setUpdateTime(date);
                e.setCreateTime(Objects.nonNull(createTime) ? createTime : new Date());
            }
        }
        List<CraftProcedureExcelDTO> imports2 = imports1.stream().filter(CraftProcedureExcelDTO::getVerifyPass).collect(Collectors.toList());

        // 找出有问题的uniCode： 出现次数>1的
        Set<String> errUniCode = allImports.stream().filter(e -> StringUtils.isNotBlank(e.getUniCode()))
                .collect(Collectors.groupingBy(CraftProcedureExcelDTO::getUniCode, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey).collect(Collectors.toSet());
        // 3. 第3次过滤：以前的逻辑
        for (CraftProcedureExcelDTO importDTO : imports2) {
            boolean canImport = true;
            StringBuilder importResult = new StringBuilder();
            // 导入数据中同一个工艺编码对应的数据
            List<CraftProcedureExcelDTO> craftProcedures = imports2.stream().filter(temp -> importDTO.getCraftCode().equals(temp.getCraftCode())).collect(Collectors.toList());
            // 校验工艺编号是否与工艺名称一一对应
            List<String> strings = craftProcedures.stream().map(CraftProcedureExcelDTO::getCraftName).distinct().collect(Collectors.toList());
            //一个编码存在多个名称
            if (strings.size() > 1) {
                importResult.append("工艺编码与工艺名称不对应,存在多个工艺名称;");
                canImport = false;
            }
            // 校验工艺版本
            List<String> versions = craftProcedures.stream().map(CraftProcedureExcelDTO::getVersion).distinct().collect(Collectors.toList());
            if (versions.size() > 1) {
                importResult.append("工艺编码与版本号不对应,存在多个版本号;");
                canImport = false;
            }
            // 检验工艺状态是否合法
            List<String> stateNameList = craftProcedures.stream().map(CraftProcedureExcelDTO::getStateName).distinct().collect(Collectors.toList());
            if (stateNameList.size() > 1) {
                importResult.append("工艺编码与状态不对应,存在多个状态;");
                canImport = false;
            }
            List<String> materialCodeList = craftProcedures.stream().map(CraftProcedureExcelDTO::getMaterialCode).distinct().collect(Collectors.toList());
            if (materialCodeList.size() > 1) {
                importResult.append("工艺编码与物料编码不对应,存在多个物料编码;");
                canImport = false;
            }
            //校验序列号是否重复
            List<String> serialNumbers = craftProcedures.stream().map(CraftProcedureExcelDTO::getSerialNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            HashSet<String> serialNumberSet = new HashSet<>(serialNumbers);
            if (serialNumberSet.size() < serialNumbers.size()) {
                importResult.append("同一个工艺中序列代号编码不可重复;");
                canImport = false;
            }

            if (StringUtils.isBlank(importDTO.getCraftName())) {
                importResult.append("工艺名称不能为空;");
                canImport = false;
            }
            if (StringUtils.isBlank(importDTO.getVersion())) {
                importResult.append("工艺版本号不能为空;");
                canImport = false;
            }
            // 检验工艺类型是否合法
            List<String> craftTypeNames = Arrays.stream(CraftTypeEnum.values()).map(CraftTypeEnum::getName).collect(Collectors.toList());
            if (StringUtils.isNotBlank(importDTO.getTypeName()) && !craftTypeNames.contains(importDTO.getTypeName())) {
                importResult.append("工艺类型允许不填，如果填写只能是正常或者返工;");
                canImport = false;
            } else {
                importDTO.setType(CraftTypeEnum.getCodeByName(importDTO.getTypeName()));
            }

            //物料编码
            if (!isTemplate) {
                if (StringUtils.isBlank(importDTO.getMaterialCode())) {
                    importResult.append("物料编码不能为空;");
                    canImport = false;
                } else {
                    MaterialEntity material = materialCodeMaterialMap.get(importDTO.getMaterialCode());
                    if (Objects.isNull(material)) {
                        material = materialService.lambdaQuery().eq(MaterialEntity::getCode, importDTO.getMaterialCode()).one();
//                        material = materialService.getSimpleMaterialByCode(importDTO.getMaterialCode());
                        materialCodeMaterialMap.put(importDTO.getMaterialCode(), material);
                    }
                    if (Objects.isNull(material)) {
                        importResult.append("系统中未找到该生效的物料;");
                        canImport = false;
                    } else {
                        importDTO.setMaterialId(material.getId());
                    }
                    if (StringUtils.isNotBlank(importDTO.getMaterialName()) && Objects.nonNull(material) &&
                            !importDTO.getMaterialName().equals(material.getName())) {
                        importResult.append("物料名称与物料编码对应物料不一致;");
                        canImport = false;
                    }
                }
            }

            // 校验工序编号,系统中或excel中存在
            List<ProcedureEntity> procedureList = procedureService.lambdaQuery().eq(ProcedureEntity::getName, importDTO.getProcedureName()).list();
            if (CollectionUtils.isEmpty(procedureList)) {
                importResult.append("该工序名称在系统中不存在; ");
                canImport = false;
            } else {
                ProcedureEntity procedureEntity = procedureList.get(0);
                if (!ProcedureStateEnum.RELEASED.getCode().equals(procedureEntity.getState())) {
                    importResult.append("该工序不为生效态;");
                    canImport = false;
                }
                if (StringUtils.isNotBlank(importDTO.getProcedureCode()) && !procedureEntity.getProcedureCode().equals(importDTO.getProcedureCode())) {
                    importResult.append("该工艺中存在工序编号与名称不对应的情况; ");
                    canImport = false;
                } else {
                    importDTO.setProcedureId(procedureEntity.getProcedureId());
                    importDTO.setBasicUnitType(procedureEntity.getType());
                    importDTO.setAlias(procedureEntity.getAlias());
                }
                // 制造单元类型/工位类型、工作中心应该是是从工序当中获取
                String procedureLineModelNames = procedureEntity.getLineModelNames();
                if (StringUtils.isNoneBlank(procedureLineModelNames, importDTO.getLineModelName()) && !procedureLineModelNames.contains(importDTO.getLineModelName())) {
                    importResult.append("制造单元类型没有在工序中找到;");
                    canImport = false;
                }
                List<ModelEntity> facModelList = procedureService.getFacModelList(procedureEntity.getProcedureId());
                List<String> facNames = facModelList.stream().map(ModelEntity::getName).collect(Collectors.toList());
                if (StringUtils.isNotBlank(importDTO.getFacModelName()) && !facNames.contains(importDTO.getFacModelName())) {
                    importResult.append("工位类型没有在工序中找到;");
                    canImport = false;
                }
                // 只填工位类型，没填制造单元类型, 自动填充
                if (StringUtils.isNotEmpty(importDTO.getFacModelName()) && StringUtils.isEmpty(importDTO.getLineModelName())) {
                    List<ModelEntity> tempFacModels = facModelList.stream().filter(res -> res.getName().equals(importDTO.getFacModelName())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(tempFacModels)) {
                        // 工位类型关联的制造单元类型
                        List<ModelEntity> lineModels = modelService.getModelListByIds(String.valueOf(tempFacModels.get(0).getPid()));
                        importDTO.setLineModelName(CollectionUtils.isEmpty(lineModels) ? null : lineModels.get(0).getName());
                        importDTO.setLineModelId(CollectionUtils.isEmpty(lineModels) ? null : String.valueOf(lineModels.get(0).getId()));
                    }
                }
                // 制造单元类型与工位类型不匹配
                if (canImport && StringUtils.isNotBlank(importDTO.getLineModelName()) && StringUtils.isNotBlank(importDTO.getFacModelName())) {
                    List<ModelEntity> lineModels = modelService.getModelListByIds(procedureEntity.getLineModelIds());
                    List<ModelEntity> filterLineModels = lineModels.stream().filter(res -> importDTO.getLineModelName().equals(res.getName())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filterLineModels)) {
                        importResult.append("制造单元类型在系统中不存在;");
                        canImport = false;
                    } else {
                        // 工位类型
                        List<ModelEntity> filterFacModels = facModelList.stream().filter(res -> importDTO.getFacModelName().equals(res.getName())).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(filterFacModels)) {
                            importResult.append("工位类型在系统中不存在;");
                            canImport = false;
                        } else {
                            if (!filterFacModels.get(0).getPid().equals(filterLineModels.get(0).getId())) {
                                importResult.append("制造单元类型与工位类型不匹配;");
                                canImport = false;
                            }
                        }
                    }
                }
                String procedureWorkCenterNames = procedureEntity.getWorkCenterNames();
                if (StringUtils.isNoneBlank(procedureWorkCenterNames, importDTO.getWorkCenterNames())) {
                    List<String> procedureWorkCenterNameList = Arrays.asList(procedureWorkCenterNames.split(Constant.SEP));
                    List<String> importWorkCenterNameList = Arrays.asList(importDTO.getWorkCenterNames().split(Constant.SEP));
                    if (!new HashSet<>(procedureWorkCenterNameList).containsAll(importWorkCenterNameList)) {
                        importResult.append("工作中心没有在工序中找到;");
                        canImport = false;
                    }
                }
                // 委外供应商校验
                if (StringUtils.isNotBlank(importDTO.getOutsourcingSupplierNames())) {
                    List<String> importSupplierNames = Arrays.stream(importDTO.getOutsourcingSupplierNames().split(Constant.SEP)).distinct().collect(Collectors.toList());
                    List<String> supplierNames = existCodeSupplierNameMap.values().stream().filter(importSupplierNames::contains).collect(Collectors.toList());
                    if (supplierNames.size() != importSupplierNames.size()) {
                        importResult.append("委外供应商不存在该系统中;");
                        canImport = false;
                    } else {
                        String outsourcingSupplierCodes = existCodeSupplierNameMap.entrySet().stream()
                                .filter(entry -> importSupplierNames.contains(entry.getValue())).map(Map.Entry::getKey)
                                .collect(Collectors.joining(Constant.SEP));
                        importDTO.setOutsourcingSupplierCodes(outsourcingSupplierCodes);
                    }
                } else {
                    importDTO.setOutsourcingSupplierNames(procedureEntity.getOutSourcingSupplier());
                    importDTO.setOutsourcingSupplierCodes(procedureEntity.getOutSourcingSupplierCode());
                }
                // 导入没填,从工序定义中获取默认值：工作中心/类型、委外工序（是/否）、检验工序（是/否）、质检工序（是/否）、维修工序（是/否）。如果工序中没有默认值，需要校验。
                if (StringUtils.isNotBlank(importDTO.getIsOutsourcingStr())) {
                    Integer code = OutsourcingProcedureEnum.getCodeByName(importDTO.getIsOutsourcingStr());
                    if (Objects.isNull(code)) {
                        importResult.append("是否为委外工序填写有误,只能是“否、完全、部分”;");
                        canImport = false;
                    } else {
                        importDTO.setIsOutsourcing(code);
                    }
                } else {
                    //导入没填,从工序定义中获取默认值
                    importDTO.setIsOutsourcing(procedureEntity.getIsSubContractingOperation());
                }
                importDTO.setIsInspectionOperation(StringUtils.isNotBlank(importDTO.getIsInspectionOperationStr()) ? Constant.TRUE.equals(importDTO.getIsInspectionOperationStr()) : procedureEntity.getIsInspectionOperation());
                importDTO.setIsQualityProcess(StringUtils.isNotBlank(importDTO.getIsQualityProcessStr()) ? Constant.TRUE.equals(importDTO.getIsQualityProcessStr()) : procedureEntity.getIsQualityProcess());
                if (importDTO.getIsQualityProcess() && StringUtils.isNotBlank(importDTO.getDefectSchemeName())) {
                    // 查询质检方案
                    DefectSchemeSelectDTO selectDTO = DefectSchemeSelectDTO.builder()
                            .status(String.valueOf(DefectSchemeStatusEnum.RELEASED.getCode()))
                            .schemeAllName(importDTO.getDefectSchemeName())
                            .type(procedureEntity.getType())
                            .build();
                    Page<DefectSchemeEntity> schemeList = defectSchemeService.getList(selectDTO);
                    if (CollectionUtils.isEmpty(schemeList.getRecords())) {
                        importResult.append("质检方案不存在或未生效或质检方案类型不匹配;");
                        canImport = false;
                    } else {
                        importDTO.setDefectSchemeId(schemeList.getRecords().get(0).getSchemeId());
                    }
                }
                importDTO.setIsMaintenanceProcedure(StringUtils.isNotBlank(importDTO.getIsMaintenanceProcedureStr()) ? Constant.TRUE.equals(importDTO.getIsMaintenanceProcedureStr()) : procedureEntity.getIsMaintenanceProcedure());
                if (importDTO.getIsMaintenanceProcedure() && StringUtils.isNotBlank(importDTO.getMaintainSchemeName())) {
                    // 检查质检方案名称
                    MaintainSchemeEntity maintainScheme = maintainSchemeService.lambdaQuery()
                            .eq(MaintainSchemeEntity::getSchemeName, importDTO.getMaintainSchemeName())
                            .eq(MaintainSchemeEntity::getStatus, MaintainSchemeStatusEnum.RELEASED.getCode())
                            .last("limit 1").one();
                    if (maintainScheme == null) {
                        importResult.append("维修方案不存在或未生效;");
                        canImport = false;
                    } else {
                        importDTO.setMaintainSchemeId(maintainScheme.getSchemeId());
                    }
                }

                if (WorkCenterTypeEnum.LINE.getCode().equals(procedureEntity.getType())) {
                    // 工序关联生产基本单元是制造单元,工位类型不能为空
                    // [["23","31"],["23","32"],["36","44"],["36","45"]]
                    JSONArray tempList = StringUtils.isNotEmpty(procedureEntity.getCascaderJsonStr()) ? JSON.parseArray(procedureEntity.getCascaderJsonStr()) : null;
                    if (StringUtils.isBlank(importDTO.getFacModelName()) && tempList != null && tempList.size() == 1) {
                        // 如果关联的工序只有一个工位类型,直接设置为默认值
                        JSONArray tempArray = JSON.parseArray(JSON.toJSONString(tempList.get(0)));
                        importDTO.setLineModelName(procedureEntity.getLineModelNames());
                        importDTO.setLineModelId(String.valueOf(tempArray.get(0)));

                        Integer facModelId = Integer.parseInt(String.valueOf(tempArray.get(1)));
                        List<ModelEntity> facModel = facModelList.stream().filter(res -> facModelId.equals(res.getId())).collect(Collectors.toList());
                        importDTO.setFacModelName(facModel.get(0).getName());
                        importDTO.setFacModelId(String.valueOf(facModelId));
                    }
                    // 上面没有设置默认值且Excel中没填写工位类型
                    if (StringUtils.isBlank(importDTO.getFacModelName())) {
                        importResult.append("工序关联生产基本单元是制造单元,且工序关联工位类型有多个,工位类型不能为空;");
                        canImport = false;
                    }
                } else if (WorkCenterTypeEnum.TEAM.getCode().equals(procedureEntity.getType()) || WorkCenterTypeEnum.DEVICE.getCode().equals(procedureEntity.getType())) {
                    // 工序关联生产基本单元是班组或设备,工作中心不能为空
                    String[] tempProcedureWorkCenterNames = StringUtils.isNotBlank(procedureEntity.getWorkCenterNames()) ? procedureEntity.getWorkCenterNames().split(Constant.SEP) : new String[0];
                    if (StringUtils.isBlank(importDTO.getWorkCenterNames()) && tempProcedureWorkCenterNames.length == 1) {
                        // 如果关联的工序只有一个工作中心,直接设置为默认值
                        importDTO.setWorkCenterNames(procedureEntity.getWorkCenterNames());
                        importDTO.setWorkCenterIds(procedureEntity.getWorkCenterIds());
                    }
                    // 上面没有设置默认值且Excel中没填写工作中心
                    if (StringUtils.isBlank(importDTO.getWorkCenterNames())) {
                        importResult.append("工序关联生产基本单元是班组或设备,且工序关联工作中心有多个,工作中心不能为空;");
                        canImport = false;
                    }
                }
            }
            // 校验上级序列代号在该组工序数据中是否存在
            if (StringUtils.isNotBlank(importDTO.getSuperSerialNumber())) {
                // 上级工序为“*”,表示这个工序为首工序, 所以要排除“*”
                List<String> supers = Arrays.stream(importDTO.getSuperSerialNumber().split(Constant.SEP))
                        .filter(res -> !Constant.STAR.equals(res))
                        .collect(Collectors.toList());
                if (!new HashSet<>(serialNumbers).containsAll(supers)) {
                    importResult.append("上级序列代号在导入工艺工序找不到;");
                    canImport = false;
                }
            }
            // 编制人
            if (StringUtils.isNotBlank(importDTO.getEditorName())) {
                SysUserEntity user = sysUserService.lambdaQuery()
                        .eq(SysUserEntity::getNickname, importDTO.getEditorName())
                        .last("limit 1").one();
                if (Objects.isNull(user)) {
                    importResult.append("编制人必须在系统中存在；");
                    canImport = false;
                } else {
                    importDTO.setEditor(user.getUsername());
                }
            }
            if(errUniCode.contains(importDTO.getUniCode())) {
                importResult.append("唯一标识符重复;");
                canImport = false;
            }
            // 工序控制
            importDTO.setProcedureControllerCheck(StringUtils.isNotBlank(importDTO.getProcedureControllerCheckStr()) && Constant.TRUE.equals(importDTO.getProcedureControllerCheckStr()));

            importDTO.setVerifyPass(canImport);
            importDTO.setImportResult(importResult.toString());

            // 一些字符串空值的处理
            importDTO.setRemark(Optional.ofNullable(importDTO.getRemark()).orElse(""));
            importDTO.setCraftDesc(Optional.ofNullable(importDTO.getCraftDesc()).orElse(""));
        }

        // 序列代号、上级序列代号可能存在不填的情况。 处理工序上下级关系
        Map<String, List<CraftProcedureExcelDTO>> craftCodeMap = new HashMap<>();
        List<CraftProcedureExcelDTO> imports3 = imports2.stream().filter(CraftProcedureExcelDTO::getVerifyPass).collect(Collectors.toList());
        for (CraftProcedureExcelDTO importDTO : imports3) {
            if (craftCodeMap.get(importDTO.getCraftCode()) != null) {
                craftCodeMap.get(importDTO.getCraftCode()).add(importDTO);
            } else {
                craftCodeMap.put(importDTO.getCraftCode(), Stream.of(importDTO).collect(Collectors.toList()));
            }
        }
        for (Map.Entry<String, List<CraftProcedureExcelDTO>> entry : craftCodeMap.entrySet()) {
            List<CraftProcedureExcelDTO> craftProcedures = entry.getValue();
            // 设置序号标识
            int index = 0;
            for (CraftProcedureExcelDTO craftProcedure : craftProcedures) {
                craftProcedure.setIndex(String.valueOf(index++));
            }
            // 设置上级工序
            for (int i = 0; i < craftProcedures.size(); i++) {
                CraftProcedureExcelDTO craftProcedure = craftProcedures.get(i);
                if (Constant.STAR.equals(craftProcedure.getSuperSerialNumber())) {
                    // 首工序
                    craftProcedure.setSupIndexs(null);
                } else {
                    if (StringUtils.isBlank(craftProcedure.getSuperSerialNumber())) {
                        // 上级序列代号为空,则上级工序就是上一条
                        craftProcedure.setSupIndexs(i - 1 < 0 ? null : String.valueOf(craftProcedures.get(i - 1).getIndex()));
                    } else {
                        String[] superSerialNumbers = craftProcedure.getSuperSerialNumber().split(Constant.SEP);
                        List<String> supIndexs = new ArrayList<>();
                        for (String superSerialNumber : superSerialNumbers) {
                            for (CraftProcedureExcelDTO procedure : craftProcedures) {
                                if (superSerialNumber.equals(procedure.getSerialNumber())) {
                                    supIndexs.add(procedure.getIndex());
                                }
                            }
                        }
                        craftProcedure.setSupIndexs(String.join(Constant.SEP, supIndexs));
                    }
                }
            }
            // 设置下级工序
            for (CraftProcedureExcelDTO craftProcedure : craftProcedures) {
                String curIndex = String.valueOf(craftProcedure.getIndex());
                // 如果当前工序的序号出现在工序的上级工序序号中,则是这个工序的下级工序
                List<String> nextIndex = new ArrayList<>();
                for (CraftProcedureExcelDTO procedure : craftProcedures) {
                    if (StringUtils.isNotEmpty(procedure.getSupIndexs())) {
                        long count = Arrays.stream(procedure.getSupIndexs().split(Constant.SEP)).filter(curIndex::equals).count();
                        if (count > 0) {
                            nextIndex.add(procedure.getIndex());
                        }
                    }
                }
                craftProcedure.setNextIndexs(String.join(Constant.SEP, nextIndex));
            }
            // 校验导入工序是否是一个圈
            // 这里引用之前的方法, 通过hashCode()将唯一的字符串序号转换成int模拟id
            List<CraftProcedureEntity> testCraftProcedures = craftProcedures.stream().map(e -> {
                String supProcedureId = Arrays.stream(Optional.ofNullable(e.getSupIndexs()).orElse("").split(Constant.SEP))
                        .map(this::buildMockId)
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .collect(Collectors.joining(Constant.SEP));
                return CraftProcedureEntity.builder().id(buildMockId(e.getIndex())).supProcedureId(supProcedureId).build();
            }).collect(Collectors.toList());
            if (checkLoop(testCraftProcedures)) {
                craftProcedures.forEach(dto -> {
                    dto.setVerifyPass(false);
                    dto.setImportResult("工艺工序存在顺序循环引用");
                });
            }
        }

        // 校验不通过整个工艺都不能导入
        List<CraftProcedureExcelDTO> canImports = new ArrayList<>();
        List<String> noPassCraftCodes = allImports.stream().filter(res -> !res.getVerifyPass()).map(CraftProcedureExcelDTO::getCraftCode).distinct().collect(Collectors.toList());
        for (CraftProcedureExcelDTO importDTO : allImports) {
            if (noPassCraftCodes.contains(importDTO.getCraftCode())) {
                importDTO.setVerifyPass(false);
                if (StringUtils.isBlank(importDTO.getImportResult())) {
                    importDTO.setImportResult("当前工艺中其他工序验证不通过");
                }
            } else {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            }
        }
        return canImports;
    }

    private Integer buildMockId(String index) {
        if (StringUtils.isBlank(index) || index.equals(Constant.STAR)) {
            return null;
        }
        return index.hashCode();

    }

    @Override
    public ProcedureEntity getProcedure(Integer craftProcedureId) {
        CraftProcedureEntity entity = this.getById(craftProcedureId);
        if (entity == null) {
            return null;
        }
        return procedureService.getById(entity.getProcedureId());
    }

    @Override
    public Page<CraftProcedureVO> craftProcedureList(CraftProcedureQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<CraftProcedureEntity> page = this.baseMapper.pageV2(sql, dto.getPage());
        return JacksonUtil.convertPage(page, CraftProcedureVO.class);
    }

    @Override
    public Boolean isLastNoSubContractProcedure(Integer craftProcedureId) {
        CraftProcedureEntity craftProcedureEntity = this.getById(craftProcedureId);
        if (craftProcedureEntity == null) {
            return false;
        }
        // 委外工序，返回false
        if (OutsourcingProcedureEnum.NO.getCode() != craftProcedureEntity.getIsSubContractingOperation()) {
            return false;
        }
        // 最后一道工序，返回true
        if (Constant.YES.equals(craftProcedureEntity.getIsLastProcedure()) || StringUtils.isBlank(craftProcedureEntity.getNextProcedureId())) {
            return true;
        }
        // 非最后一道工序，判断该工序后的所有下级工序是否都是委外工序
        Map<Integer, String> nextProcedureIdMap = this.lambdaQuery().eq(CraftProcedureEntity::getCraftId, craftProcedureEntity.getCraftId()).list()
                .stream().filter(o -> StringUtils.isNotBlank(o.getNextProcedureId()))
                .collect(Collectors.toMap(CraftProcedureEntity::getId, CraftProcedureEntity::getNextProcedureId));
        List<Integer> collect = Arrays.stream(craftProcedureEntity.getNextProcedureId().split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toList());
        Set<Integer> nextProcedureIds = new HashSet<>(collect);
        // 获取所有的下级工序id
        getNextProcedureIds(collect, nextProcedureIds, nextProcedureIdMap);
        Long count = this.lambdaQuery().in(CraftProcedureEntity::getId, nextProcedureIds)
                .eq(CraftProcedureEntity::getIsSubContractingOperation, OutsourcingProcedureEnum.NO.getCode())
                .count();
        return count == 0;
    }

    /**
     * 获取所有的下级工序id
     * @param list
     * @param nextProcedureIds
     * @param nextProcedureIdMap
     */
    private void getNextProcedureIds(List<Integer> list, Set<Integer> nextProcedureIds, Map<Integer, String> nextProcedureIdMap) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Integer> nextIds = list.stream().map(nextProcedureIdMap::get).filter(StringUtils::isNotBlank).map(Integer::parseInt).collect(Collectors.toList());
        nextProcedureIds.addAll(nextIds);
        getNextProcedureIds(nextIds, nextProcedureIds, nextProcedureIdMap);
    }
}
