package com.yelink.dfs.service.product;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfscommon.entity.dfs.MaterialTypeConfigAttributeEntity;

import java.util.List;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-07-19 16:35
 */
public interface MaterialTypeConfigAttributeService extends IService<MaterialTypeConfigAttributeEntity> {

    /**
     * 添加物料属性配置
     *
     * @param materialTypeId   物料类型id
     * @param materialTypeName 物料类型名称
     */
    void saveMaterialTypeAttributeConfig(Integer materialTypeId, String materialTypeName, String userName, List<MaterialTypeConfigAttributeEntity> materialTypeConfigAttributeEntityList);

    List<MaterialTypeConfigAttributeEntity> getList(Integer materialTypeId);
}
