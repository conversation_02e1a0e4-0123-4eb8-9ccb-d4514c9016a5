package com.yelink.dfs.service.device;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.device.dto.IndicatorEntityDTO;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-05-26 09:36
 */
@Service
public interface IndicatorService {
    /**
     * 查询一个设备的某项指标记录
     *
     * @param page
     * @param tableName
     * @param fieldName
     * @param deviceId
     * @return
     */
    Page<IndicatorEntityDTO> getDateList(Page page, String tableName, String fieldName, Integer deviceId, String startDate, String endDate, String workOrderNumber, String materialName);


    /**
     * 插入指标值
     *
     * @param tableName 表名
     * @param deviceId  设施id
     * @param dataVal   指标值
     * @param time      时间
     * @param batch     工单号
     * @return
     */
    void addIndicator(String tableName, Integer deviceId, String dataVal, Date time, String batch);


    /**
     * 实时显示，查询最新十分钟数据
     *
     * @param deviceId
     * @param targetName
     * @return
     */
    List<IndicatorEntityDTO> getTenRecords(Integer deviceId, String targetName);

    /**
     * 实时显示，查询最新十分钟数据
     *
     * @param grid
     * @param targetName
     * @return
     */
//    List<IndicatorEntityDTO> getTenRecordsByGrid(Integer grid, String targetName);


    /**
     * 查找最近十五分钟的没一分钟最后一条数据
     *
     * @param deviceId
     * @param targetName
     * @return
     * <AUTHOR>
     */
    List<IndicatorEntityDTO> getRecordInmMinute(Integer deviceId, String targetName);

    /**
     * 实时显示，查询最新五种批次数据
     *
     * @param deviceId
     * @param targetName
     * @return
     * <AUTHOR>
     */
    List<IndicatorEntityDTO> getLatestFiveBatchOnTargetName(Integer deviceId, String targetName);

    /**
     * 获取该设备对应正在生产工艺的指标参考值
     *
     * @param deviceId
     * @param targetName
     * @return
     */
    Map<String, String> getReference(Integer deviceId, String targetName);

    /**
     * 按时间段查询指定批次的指标记录(不同的cycle类型)
     * @param deviceId
     * @param tableName
     * @param startDate
     * @param endDate
     * @param cycle
     * @param batches
     * @return
     */
    List<IndicatorEntityDTO> getIndicatorEntityByCycle(Integer deviceId, String tableName, String startDate, String endDate, Integer cycle, String[] batches);

    List<IndicatorEntityDTO> getDayRecords(Integer deviceId, String targetName);


    /**
     * 获取指标的对应的最新值
     *
     * @param deviceId
     * @param tableName
     * @param fieldName
     * @return
     */
    IndicatorEntityDTO getNewVal(String tableName, String fieldName, Integer deviceId);

    /**
     * 获取指标的某个时间的最新值
     *
     * @param deviceId
     * @param tableName
     * @param fieldName
     * @param time
     * @return
     */
    IndicatorEntityDTO getNewValByTime(String tableName, String fieldName, Integer deviceId, Date time);

    /**
     * 获取指标的在某个时间段内的最新值(不包含边界)
     *
     * @param tableName
     * @param fieldName
     * @param deviceId
     * @param timeDateStart
     * @param timeDateEnd
     * @return
     */
    IndicatorEntityDTO getLatestValByTimeRange(String tableName, String fieldName, Integer deviceId, Date timeDateStart, Date timeDateEnd);

    /**
     * 获取指标的对应的最新值
     *
     * @param deviceId
     * @param targetName
     * @param batch
     * @return
     * <AUTHOR>
     */
    IndicatorEntityDTO getNewValByBatch(String targetName, Integer deviceId, String batch);

    /**
     * 获取指定批次的实时数据
     *
     * @param deviceId
     * @param tableName
     * @param fieldName
     * @param batchs
     * @return
     */
    List<IndicatorEntityDTO> getRealTimeRecords(Integer deviceId, String tableName, String fieldName, String[] batchs);

    /**
     * 获取批次号对应的所有指标记录
     *
     * @param deviceId
     * @param tableName
     * @param fieldName
     * @param batch
     * @return
     */
    List<IndicatorEntityDTO> getMoreBatchRecords(Integer deviceId, String tableName, String fieldName, String batch);

    /**
     * 更新某个设备的某段时间的批次号
     *
     * @param deviceId
     * @param fieldName
     * @param tableName
     * @param batch
     * @param startDate
     * @param endDate
     * @return
     */
    void updateRecord(Integer deviceId, String tableName, String fieldName, String batch, String startDate, String endDate);

    /**
     * 删除某个设备的某段时间的批次号
     *
     * @param deviceId
     * @param fieldName
     * @param tableName
     * @param batch
     * @param startDate
     * @param endDate
     * @return
     */
    void deleteRecord(Integer deviceId, String tableName, String fieldName, String batch, String startDate, String endDate);

    /**
     * 同类设备相同时间点的OEE对比（专家系统）
     *
     * @param deviceId
     * @param tableName
     * @param fieldName
     * @return
     */
    List<IndicatorEntityDTO> getComparisonRecord(Integer deviceId, String tableName, String fieldName);


    List<IndicatorEntityDTO> getTargetRecords(Integer deviceId, String targetName, Integer limit, Date startTime);

    /**
     * 查询暂停时长的指标记录
     *
     * @param page
     * @param deviceId
     * @return
     */
    Page<IndicatorEntityDTO> getStateDateList(Page page, Integer deviceId);

    /**
     * 获取最后灭菌时长的批次号
     *
     * @param
     * @return
     */
    String getBatchByTime(String tableName, String theSterilizationEndTime, Integer deviceId);


    /**
     * 获取某个设备当天运行状态的时长
     *
     * @param deviceId
     * @return
     */
    IndicatorEntityDTO getToDayRunRecordList(Integer deviceId);

    /**
     * 组合指标--获取时间最近的一条数据
     *
     * @param
     * @return
     */
    IndicatorEntityDTO getRecentValue(Integer deviceId, String tableName, String fieldName, String startDate, String endDate, Boolean isFirst);

    /**
     * 组合指标--获取时间最远的一条数据
     *
     * @param
     * @return
     */
    IndicatorEntityDTO getFarthestValue(Integer deviceId, String tableName, String fieldName, String startDate, String endDate, Boolean isFirst);

    /**
     * 组合指标--获取数值最大的一条数据
     *
     * @param
     * @return
     */
    IndicatorEntityDTO getMaxValue(Integer deviceId, String tableName, String fieldName, String startDate, String endDate);

    /**
     * 组合指标--获取数值最小的一条数据
     *
     * @param
     * @return
     */
    IndicatorEntityDTO getMinValue(Integer deviceId, String tableName, String fieldName, String startDate, String endDate);

    /**
     * 组合指标--获取平均值
     *
     * @param
     * @return
     */
    IndicatorEntityDTO getAvgValue(Integer deviceId, String tableName, String fieldName, String startDate, String endDate);

    /**
     * 根据deviceId和时间区间拿到记录
     *
     * @param deviceIds
     * @param targetName
     * @param startTime
     * @param endTime
     * @return
     */
    List<IndicatorEntityDTO> getListByDeviceAndTime(String[] deviceIds, String targetName, Date startTime, Date endTime);

    /**
     * 设备oee
     *
     * @param page
     * @param deviceId
     * @return
     */
    Page<IndicatorEntityDTO> getDeviceOeeList(Page page, Integer deviceId);

    /**
     * 指标按5min、20min、1h聚合
     *
     * @param tableName
     * @param fieldName
     * @param deviceId
     * @param dataVal
     * @param time
     * @param batch
     */
    //void aggregation(String tableName, String fieldName, Integer deviceId, String dataVal, Date time, String batch);

    /**
     * 根据指标名和周期获取指标字段名和表名
     * 默认原始采集周期
     *
     * @param targetName
     * @return
     */
    IndicatorEntityDTO getFieldNameAndTableName(String targetName);

    /**
     * 根据指标名和周期获取指标字段名和表名
     *
     * @param targetName
     * @param cycle
     * @return
     */
    IndicatorEntityDTO getFieldNameAndTableName(String targetName, Integer cycle);

    /**
     * 获取指定时间段内最大最小值
     *
     * @param start
     * @param end
     * @param deviceId
     * @param targetName
     * @return
     */
    IndicatorEntityDTO getMaxMinTargetValue(Date start, Date end, Integer deviceId, String targetName);

    /**
     * 通过获取最新值，先用时间范围查询减少扫码，查询不到再用单个时间点查询
     *
     * @param tableName
     * @param fieldName
     * @param deviceId
     * @param startTime
     * @param endTime
     * @return
     */
    IndicatorEntityDTO getLatestValByTimeEfficiently(String tableName, String fieldName, Integer deviceId, Date startTime, Date endTime);
}
