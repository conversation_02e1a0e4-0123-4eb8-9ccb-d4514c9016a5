package com.yelink.dfs.service.impl.trace;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ReportFormConstant;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.order.OrderMergeStateEnum;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.constant.order.TakeOutApplicationStateEnum;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.constant.product.CraftStateEnum;
import com.yelink.dfs.constant.target.ManualTargetConst;
import com.yelink.dfs.constant.trace.WorkOrderTraceTableEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.feed.FeedRecordEntity;
import com.yelink.dfs.entity.iot.WorkOrderBarcodeEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.TakeOutApplicationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.vo.WorkOrderTraceBarcodeVO;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.dto.RecordManualCollectionDTO;
import com.yelink.dfs.entity.target.record.RecordDeviceDayRunEntity;
import com.yelink.dfs.entity.target.record.RecordManualCollectionEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.trace.BomExcelDTO;
import com.yelink.dfs.entity.trace.CraftExcelDTO;
import com.yelink.dfs.entity.trace.DefectExcelDTO;
import com.yelink.dfs.entity.trace.DeviceAlarmExcelDTO;
import com.yelink.dfs.entity.trace.DeviceRunningParamExcelDTO;
import com.yelink.dfs.entity.trace.DeviceRunningRecordExcelDTO;
import com.yelink.dfs.entity.trace.FeedingInfoExcelDTO;
import com.yelink.dfs.entity.trace.PassRateExcelDTO;
import com.yelink.dfs.entity.trace.ProductOrderExcelDTO;
import com.yelink.dfs.entity.trace.ProductOutputExcelDTO;
import com.yelink.dfs.entity.trace.PurchaseExcelDTO;
import com.yelink.dfs.entity.trace.PurchaseRequestExcelDTO;
import com.yelink.dfs.entity.trace.ReportLineExcelDTO;
import com.yelink.dfs.entity.trace.TakeOutApplicationExcelDTO;
import com.yelink.dfs.entity.trace.WorkOrderCapacityExcelDTO;
import com.yelink.dfs.entity.trace.WorkOrderDetailExcelDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseRequestInterface;
import com.yelink.dfs.open.v1.supplier.dto.SupplierSelectDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.common.config.OrderTypeConfigService;
import com.yelink.dfs.service.common.config.OrderTypeItemService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfs.service.iot.WorkOrderBarcodeService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.TakeOutApplicationService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.BomRawMaterialService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.target.record.RecordDeviceDayRunService;
import com.yelink.dfs.service.target.record.RecordManualCollectionService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.trace.WorkOrderTraceService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.PurchaseStateEnum;
import com.yelink.dfscommon.constant.dfs.code.FeedStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.ExportBillBatchDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseOpenSelectDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseRequestOpenSelectDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.PurchaseEntity;
import com.yelink.dfscommon.entity.ams.PurchaseRequestEntity;
import com.yelink.dfscommon.entity.dfs.OrderTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.OrderTypeItemEntity;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PageData;
import com.yelink.dfscommon.utils.PathUtils;
import com.yelink.dfscommon.utils.PdfUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.RoundingMode;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR> @Date 2021-05-21 14:14
 */
@Slf4j
@Service
@AllArgsConstructor
public class WorkOrderTraceServiceImpl implements WorkOrderTraceService {

    private FastDfsClientService fastDfsClientService;
    private WorkOrderService workOrderService;
    private final WorkOrderProcedureRelationService workOrderProcedureRelationService;
    private CraftService craftService;
    private ReportLineService reportLineService;
    private BomService bomService;
    private final BomRawMaterialService bomRawMaterialService;
    private RecordDeviceDayRunService recordDeviceDayRunService;
    private DeviceService deviceService;
    private TargetModelService targetModelService;
    private AlarmService alarmService;
    private WorkOrderBarcodeService barcodeService;
    private ProductionLineService lineService;
    private FacilitiesService facilitiesService;
    private StockInAndOutInterface stockInAndOutInterface;
    private TakeOutApplicationService takeOutApplicationService;
    private RecordWorkOrderUnqualifiedService workOrderUnqualifiedService;
    private RecordManualCollectionService recordManualCollectionService;
    private FeedRecordService feedRecordService;
    private SysUserService userService;
    private OrderWorkOrderService orderWorkOrderService;
    private final ProductFlowCodeRecordService productFlowCodeRecordService;
    private final MaterialService materialService;
    private ExtPurchaseInterface extPurchaseInterface;
    private ExtPurchaseRequestInterface extPurchaseRequestInterface;
    private SupplierService supplierService;
    private OrderTypeItemService orderTypeItemService;
    private OrderTypeConfigService orderTypeConfigService;

    private Map<String, BiConsumer<ExcelWriter, WorkOrderEntity>> getFunctionMap() {
        Map<String, BiConsumer<ExcelWriter, WorkOrderEntity>> map = new HashMap<>();
        map.put(WorkOrderTraceTableEnum.PRODUCT_ORDER.getName(), this::setProductOrderSheet);
        map.put(WorkOrderTraceTableEnum.WORK_ORDER_DETAIL.getName(), this::setWorkOrderDetailSheet);
        map.put(WorkOrderTraceTableEnum.REPORT_RECORD.getName(), this::setReportRecordSheet);
        map.put(WorkOrderTraceTableEnum.PURCHASE_REQUEST.getName(), this::setPurchaseRequestSheet);
        map.put(WorkOrderTraceTableEnum.PURCHASE.getName(), this::setPurchaseSheet);
        map.put(WorkOrderTraceTableEnum.BOM.getName(), this::setBomSheet);
        map.put(WorkOrderTraceTableEnum.CRAFT.getName(), this::setCraftSheet);
        map.put(WorkOrderTraceTableEnum.DEVICE_RUNNING_RECORD.getName(), this::setDeviceRunningRecordSheet);
        map.put(WorkOrderTraceTableEnum.DEVICE_RUNNING_PARAM.getName(), this::setDeviceRunningParamSheet);
        map.put(WorkOrderTraceTableEnum.DEVICE_ALARM.getName(), this::setDeviceAlarmSheet);
        map.put(WorkOrderTraceTableEnum.CAPACITY.getName(), this::setCapacitySheet);
        map.put(WorkOrderTraceTableEnum.BAR_CODE.getName(), this::setBarCodeSheet);
        map.put(WorkOrderTraceTableEnum.PRODUCT_OUTPUT.getName(), this::setProductOutputSheet);
        map.put(WorkOrderTraceTableEnum.TAKE_OUT_APPLICATION.getName(), this::setTakeOutApplicationSheet);
        map.put(WorkOrderTraceTableEnum.DEFECT_RECORD.getName(), this::setDefectRecordSheet);
        map.put(WorkOrderTraceTableEnum.PASS_RATE.getName(), this::setPassRateSheet);
        map.put(WorkOrderTraceTableEnum.FEEDING_INFO.getName(), this::setFeedingInfoSheet);
        return map;
    }

    private File getTemplateFile() {
        //从fastdfs获取用户上传的模板
        String fileUrl = fastDfsClientService.getUrlByFileCode(ReportFormConstant.WORK_ORDER_TRACE_TEMPLATE);
        if (StringUtils.isBlank(fileUrl)) {
            log.error("用户未上传生产工单追溯模板");
            throw new ResponseException("用户未上传生产工单追溯模板");
        }
        // 临时转储到服务器用于操作
        byte[] fileBuffer = fastDfsClientService.getFileStream(fileUrl);
        String dirPath = PathUtils.getAbsolutePath(this.getClass()) + File.separator + "temp" + File.separator + "trace";
        File dirFile = new File(dirPath);
        try {
            if (dirFile.exists()) {
                //删除旧文件
                Path path = Paths.get(dirPath);
                SimpleFileVisitor<Path> visitor = new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        Files.delete(file);
                        return FileVisitResult.CONTINUE;
                    }

                    @Override
                    public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                        Files.delete(dir);
                        return FileVisitResult.CONTINUE;
                    }
                };
                Files.walkFileTree(path, visitor);
            }
            boolean isMkdir = dirFile.mkdirs();
            String temFilePath = dirPath + File.separator + "trace_" + UUID.randomUUID() + Constant.XLSX;
            Files.write(Paths.get(temFilePath), fileBuffer);
            File newFile = new File(temFilePath);
            if (!newFile.exists()) {
                boolean isFileMkdir = newFile.createNewFile();
            }
            return newFile;
        } catch (IOException e) {
            log.error("临时转储模板到服务器失败");
            throw new ResponseException("临时转储模板到服务器失败");
        }
    }


    private void setProductOrderSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        List<ProductOrderEntity> productOrderEntities = orderWorkOrderService.productOrderListByWorkId(workOrder);
        if (CollectionUtils.isEmpty(productOrderEntities)) {
            return;
        }
        List<String> userNames = productOrderEntities.stream().map(ProductOrderEntity::getCreateBy).collect(Collectors.toList());
        Map<String, SysUserEntity> userEntityMap = userService.getListByUserNames(userNames).stream().collect(Collectors.toMap(SysUserEntity::getUsername, o -> o));

        List<ProductOrderExcelDTO> dtos = productOrderEntities.stream()
                .map(o -> ProductOrderExcelDTO.builder()
                        .orderNumber(o.getProductOrderNumber())
                        .stateName(OrderStateEnum.getNameByCode(o.getState()))
                        .deliveryDate(o.getProductOrderMaterials().get(0).getDeliveryDate())
                        .mergedState(o.getProductOrderMaterials().get(0).getMergedState())
                        .mergedStateName(OrderMergeStateEnum.getNameByCode(o.getProductOrderMaterials().get(0).getMergedState()))
                        .createName(userEntityMap.get(o.getCreateBy()) == null ? null : userEntityMap.get(o.getCreateBy()).getNickname())
                        .build()
                ).collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.PRODUCT_ORDER.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setWorkOrderDetailSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        OrderTypeItemEntity orderTypeEntity = orderTypeItemService.lambdaQuery()
                .eq(OrderTypeItemEntity::getBusinessTypeCode, workOrder.getBusinessType())
                .eq(OrderTypeItemEntity::getCode, workOrder.getOrderType())
                .one();
        OrderTypeConfigEntity businessTypeEntity = orderTypeConfigService.lambdaQuery().eq(OrderTypeConfigEntity::getCode, orderTypeEntity.getBusinessTypeCode()).one();
        workOrder.setStateName(WorkOrderStateEnum.getNameByCode(workOrder.getState()));
        MaterialEntity materialEntity = materialService.lambdaQuery()
                .select(MaterialEntity::getCode, MaterialEntity::getName)
                .eq(MaterialEntity::getCode, workOrder.getMaterialCode())
                .one();
        WorkOrderDetailExcelDTO dto = WorkOrderDetailExcelDTO.builder()
                .workOrderName(workOrder.getWorkOrderName())
                .workOrderNumber(workOrder.getWorkOrderNumber())
                .stateName(workOrder.getStateName())
                .materialName(materialEntity.getName())
                .materialCode(workOrder.getMaterialCode())
                .planQuantity(workOrder.getPlanQuantity())
                .saleOrderNumber(workOrder.getSaleOrderNumber())
                .productOrderNumber(workOrder.getProductOrderNumber())
                .magNickname(workOrder.getMagNickname())
                .magPhone(workOrder.getMagPhone())
                .orderTypeName(orderTypeEntity.getName())
                .businessTypeName(businessTypeEntity.getName())
                .createDate(workOrder.getCreateDate())
                .startDate(workOrder.getStartDate())
                .endDate(workOrder.getEndDate())
                .lineName(workOrder.getLineName())
                .priority(workOrder.getPriority())
                .finishCount(workOrder.getFinishCount())
                .unqualified(workOrder.getUnqualified())
                .actualStartDate(workOrder.getActualStartDate())
                .actualEndDate(workOrder.getActualEndDate())
                .remark(workOrder.getRemark())
                .build();
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.WORK_ORDER_DETAIL.getName()).build();
        excelWriter.write(Collections.singletonList(dto), writeSheet);
    }

    private void setReportRecordSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        List<ReportLineEntity> reportLineEntities = reportLineService.lambdaQuery()
                .eq(ReportLineEntity::getWorkOrder, workOrder.getWorkOrderNumber())
                .list();
        List<ReportLineExcelDTO> dtos = reportLineEntities.stream()
                .map(res -> ReportLineExcelDTO.builder()
                        .batch(res.getBatch())
                        .shiftType(res.getShiftType())
                        .finishCount(res.getFinishCount())
                        .unqualified(res.getUnqualified())
                        .effectiveHours(res.getEffectiveHours())
                        .reportTime(res.getReportTime())
                        .userNickname(res.getUserNickname())
                        .build())
                .collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.REPORT_RECORD.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setPurchaseRequestSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        // 工单关联的生产订单
        List<OrderWorkOrderEntity> orderWorkOrders = orderWorkOrderService.getByWorkOrderId(workOrder.getWorkOrderId(), OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        PageResult<PurchaseRequestEntity> pageResult = new PageResult<>();
        if (!CollectionUtils.isEmpty(orderWorkOrders)) {
            List<Integer> productOrderIds = orderWorkOrders.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
            pageResult = extPurchaseRequestInterface.getPage(PurchaseRequestOpenSelectDTO.builder().productOrderIds(productOrderIds).build());
        }
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return;
        }
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.PURCHASE_REQUEST.getName()).build();
        excelWriter.write(JacksonUtil.convertArray(pageResult.getRecords(), PurchaseRequestExcelDTO.class), writeSheet);
    }

    private void setPurchaseSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        PurchaseOpenSelectDTO build = PurchaseOpenSelectDTO.builder().state(PurchaseStateEnum.RELEASED.getCode() + Constant.SEP + PurchaseStateEnum.FINISHED.getCode() + Constant.SEP + PurchaseStateEnum.CLOSED.getCode()).workOrderNumber(workOrder.getWorkOrderNumber()).build();
        PageResult<PurchaseEntity> page = extPurchaseInterface.getPage(build);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return;
        }
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.PURCHASE.getName()).build();
        excelWriter.write(JacksonUtil.convertArray(page.getRecords(), PurchaseExcelDTO.class), writeSheet);
    }

    private void setBomSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        String materialCode = workOrder.getMaterialCode();
        List<BomEntity> bomEntityList = bomService.getListByMaterialCode(materialCode);
        if (CollectionUtils.isEmpty(bomEntityList)){
            return;
        }
        List<BomExcelDTO> list = new ArrayList<>();
        for (BomEntity bom : bomEntityList) {
            List<BomRawMaterialEntity> bomRawMaterials = bomRawMaterialService.getListByBomId(bom.getId());
            for (BomRawMaterialEntity bomRawMaterial : bomRawMaterials) {
                //查询原料信息
                MaterialEntity material = materialService.getEntityByCodeAndSkuId(bomRawMaterial.getCode(), bomRawMaterial.getSkuId());
                list.add(BomExcelDTO.builder()
                        .bomNum(bom.getBomNum())
                        .bomName(bom.getBomName())
                        .stateName(BomStateEnum.getNameByCode(bom.getState()))
                        .code(bomRawMaterial.getCode())
                        .name(material.getName())
                        .standard(material.getStandard())
                        .num(bomRawMaterial.getNum())
                        .number(bomRawMaterial.getNumber())
                        .comp(material.getComp())
                        .build());
            }
        }
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.BOM.getName()).build();
        excelWriter.write(list, writeSheet);
    }


    private void setCraftSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        // 工单关联的工艺工序
        List<WorkOrderProcedureRelationEntity> workRelationCraftProcedures = workOrderProcedureRelationService.getWorkOrderProcedureRelationEntities(workOrder.getWorkOrderNumber());
        if (CollectionUtils.isEmpty(workRelationCraftProcedures)) {
            return;
        }
        CraftEntity craftEntity = craftService.getById(workRelationCraftProcedures.get(0).getCraftId());
        if (Objects.isNull(craftEntity)) {
            return;
        }
        craftEntity.setStateName(CraftStateEnum.getNameByCode(craftEntity.getState()));
        List<String> usernames = Stream.of(craftEntity.getCreateBy(), craftEntity.getApprover()).distinct().collect(Collectors.toList());
        List<SysUserEntity> names = userService.getListByUserNames(usernames);
        Map<String, String> map = names.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        craftEntity.setCreateByNickname(map.get(craftEntity.getCreateBy()));
        craftEntity.setApproverName(map.get(craftEntity.getApprover()));
        CraftExcelDTO dto = CraftExcelDTO.builder()
                .craftCode(craftEntity.getCraftCode())
                .name(craftEntity.getName())
                .craftDesc(craftEntity.getCraftDesc())
                .materialCode(craftEntity.getMaterialCode())
                .materialName(materialService.getById(craftEntity.getMaterialId()).getName())
                .stateName(craftEntity.getStateName())
                .createByNickname(craftEntity.getCreateByNickname())
                .approverName(craftEntity.getApproverName())
                .createTime(craftEntity.getCreateTime())
                .updateTime(craftEntity.getUpdateTime())
                .build();
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.CRAFT.getName()).build();
        excelWriter.write(Collections.singletonList(dto), writeSheet);
    }

    private void setDeviceRunningRecordSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        List<RecordDeviceDayRunEntity> dayRunEntities = recordDeviceDayRunService.lambdaQuery()
                .eq(RecordDeviceDayRunEntity::getOrderNumber, workOrder.getWorkOrderNumber()).list();
        // 获取设备运行记录
        List<Integer> deviceIds = dayRunEntities.stream().map(RecordDeviceDayRunEntity::getDeviceId).distinct().collect(Collectors.toList());
        Map<Integer, String> deviceMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(deviceIds)) {
            List<DeviceEntity> deviceEntities = deviceService.listByIds(deviceIds);
            deviceMap = deviceEntities.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
        }
        for (RecordDeviceDayRunEntity runEntity : dayRunEntities) {
            runEntity.setStateName(DevicesStateEnum.getNameByCode(runEntity.getState()));
            runEntity.setDeviceName(deviceMap.get(runEntity.getDeviceId()));
        }
        List<DeviceRunningRecordExcelDTO> dtos = dayRunEntities.stream()
                .map(res -> DeviceRunningRecordExcelDTO.builder()
                        .deviceName(res.getDeviceName())
                        .stateName(res.getStateName())
                        .recordDate(res.getRecordDate())
                        .startTime(res.getStartTime())
                        .endTime(res.getEndTime())
                        .duration(res.getDuration())
                        .build())
                .collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.DEVICE_RUNNING_RECORD.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setDeviceRunningParamSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        // 通过产线获取设备列表(产线维度)
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getProductionLineId, workOrder.getLineId());
        List<DeviceEntity> deviceEntities = deviceService.list(deviceWrapper);
        List<TargetModelEntity> entities = new ArrayList<>();
        for (DeviceEntity deviceEntity : deviceEntities) {
            Page<TargetModelEntity> targetModelList = targetModelService.getList(deviceEntity.getModelId(),
                    deviceEntity.getDeviceId(), null, null);
            entities.addAll(targetModelList.getRecords());
        }
        List<DeviceRunningParamExcelDTO> dtos = entities.stream()
                .map(res -> DeviceRunningParamExcelDTO.builder()
                        .targetCnname(res.getTargetCnname())
                        .methodCname(res.getMethodCname())
                        .frequency(res.getFrequency())
                        .targetCollectionType(res.getTargetCollectionType())
                        .alarmCondition(res.getAlarmCondition())
                        .build())
                .collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.DEVICE_RUNNING_PARAM.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setDeviceAlarmSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        // 通过产线获取设备列表(产线维度)
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getProductionLineId, workOrder.getLineId())
                .isNotNull(DeviceEntity::getProductionLineId);
        List<DeviceEntity> deviceEntities = deviceService.list(deviceWrapper);
        List<Integer> deviceIds = deviceEntities.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
        List<AlarmEntity> entities = new ArrayList<>();
        if (!CollectionUtils.isEmpty(deviceIds)) {
            entities = alarmService.lambdaQuery()
                    .in(AlarmEntity::getDeviceId, deviceIds)
                    .between(AlarmEntity::getAlarmReportTime, workOrder.getStartDate(), workOrder.getEndDate())
                    .list();
        }
        List<DeviceAlarmExcelDTO> dtos = entities.stream().map(DeviceAlarmExcelDTO::convertToDTO).collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.DEVICE_ALARM.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setCapacitySheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        double finishCount = workOrder.getFinishCount() == null ? 0 : workOrder.getFinishCount();
        Date actualStartDate = workOrder.getActualStartDate();
        Date actualEndDate = workOrder.getActualEndDate();
        Double capacity = 0.0;
        if (actualStartDate != null) {
            long span = DateUtil.getTime(actualStartDate, actualEndDate == null ? new Date() : actualEndDate);
            double hourSpan = MathUtil.divideDouble(span, 60 * 60 * 1000, 2);
            capacity = MathUtil.divideDouble(finishCount, hourSpan, 2);
        }
        WorkOrderCapacityExcelDTO dto = WorkOrderCapacityExcelDTO.builder().capacity(capacity).build();
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.CAPACITY.getName()).build();
        excelWriter.write(Collections.singletonList(dto), writeSheet);
    }

    private void setBarCodeSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        Integer current = 1, size = Integer.MAX_VALUE;
        PageData<WorkOrderTraceBarcodeVO> retPageData;
        // 兼容之前逻辑：获取工单成品码
        PageData<WorkOrderBarcodeEntity> page = barcodeService.getBarcodeEntities(current, size, workOrder.getWorkOrderNumber(), null);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            // 之前逻辑没有找到数据,就走新逻辑
            retPageData = productFlowCodeRecordService.listWorkOrderTraceBarcode(current, size, workOrder.getWorkOrderNumber(), null);
        } else {
            List<WorkOrderTraceBarcodeVO> list = new ArrayList<>();
            for (WorkOrderBarcodeEntity record : page.getRecords()) {
                list.add(WorkOrderTraceBarcodeVO.builder()
                        .barcode(record.getBarcode())
                        .inputFid(record.getInputFid())
                        .inputFname(record.getInputFname())
                        .inputTime(record.getInputTime())
                        .produceFid(record.getProduceFid())
                        .produceFname(record.getProduceFname())
                        .produceTime(record.getProduceTime())
                        .build());
            }
            retPageData = new PageData<>(current, size, page.getTotal(), list);
        }
        List<WorkOrderTraceBarcodeVO> records = retPageData.getRecords();
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.BAR_CODE.getName()).build();
        excelWriter.write(records, writeSheet);
    }

    private void setProductOutputSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        List<StockInAndOutEntity> entities = JacksonUtil.getResponseArray(stockInAndOutInterface.listByWorkOrder(workOrder.getWorkOrderNumber()), StockInAndOutEntity.class);
        List<ProductOutputExcelDTO> dtos = entities.stream()
                .map(res -> ProductOutputExcelDTO.builder()
                        .warehouseName(res.getWarehouseName())
                        .inOrOutTypeName(res.getInOrOutTypeName())
                        .orderNumber(res.getOrderNumber())
                        .warehouseManagerName(res.getWarehouseManagerName())
                        .managerPhone(res.getManagerPhone())
                        .planTime(res.getPlanTime())
                        .actualTime(res.getActualTime())
                        .stateName(res.getStateName())
                        .build())
                .collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.PRODUCT_OUTPUT.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setTakeOutApplicationSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        List<TakeOutApplicationEntity> entities = takeOutApplicationService.lambdaQuery()
                .eq(TakeOutApplicationEntity::getWorkOrderNumber, workOrder.getWorkOrderNumber())
                .in(TakeOutApplicationEntity::getState, TakeOutApplicationStateEnum.RELEASED.getCode(),
                        TakeOutApplicationStateEnum.FINISHED.getCode(), TakeOutApplicationStateEnum.CLOSED.getCode())
                .list();
        for (TakeOutApplicationEntity entity : entities) {
            entity.setStateName(TakeOutApplicationStateEnum.getNameByCode(entity.getState()));
        }
        List<TakeOutApplicationExcelDTO> dtos = entities.stream()
                .map(res -> TakeOutApplicationExcelDTO.builder()
                        .applicationNum(res.getApplicationNum())
                        .stateName(res.getStateName())
                        .workOrderNumber(res.getWorkOrderNumber())
                        .workOrderName(res.getWorkOrderName())
                        .createTime(res.getCreateTime())
                        .planPickingTime(res.getPlanPickingTime())
                        .actualPickingTime(res.getActualPickingTime())
                        .build())
                .collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.TAKE_OUT_APPLICATION.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setDefectRecordSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        List<RecordWorkOrderUnqualifiedEntity> entities = workOrderUnqualifiedService.lambdaQuery()
                .eq(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, workOrder.getWorkOrderNumber()).list();
        List<String> userNames = entities.stream().map(RecordWorkOrderUnqualifiedEntity::getCreateBy).distinct().collect(Collectors.toList());
        List<SysUserEntity> userEntities = userService.getListByUserNames(userNames);
        Map<String, String> map = userEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        for (RecordWorkOrderUnqualifiedEntity entity : entities) {
            entity.setCreateByName(map.get(entity.getCreateBy()));
        }
        List<DefectExcelDTO> dtos = entities.stream()
                .map(res -> DefectExcelDTO.builder()
                        .sequenceId(res.getSequenceId())
                        .materialCode(res.getMaterialCode())
                        .abnormalName(res.getAbnormalName())
                        .createDate(res.getCreateDate())
                        .workOrderNumber(res.getWorkOrderNumber())
                        .fname(res.getFname())
                        .createByName(res.getCreateByName())
                        .build())
                .collect(Collectors.toList());
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.DEFECT_RECORD.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }

    private void setPassRateSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        // 获取手动采集指标的合格率
        List<RecordManualCollectionEntity> collectionEntities = recordManualCollectionService.lambdaQuery()
                .eq(RecordManualCollectionEntity::getBatch, workOrder.getWorkOrderNumber()).list();
        List<Integer> deviceIds = collectionEntities.stream().map(RecordManualCollectionEntity::getDeviceId).distinct().collect(Collectors.toList());
        Map<Integer, String> deviceMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(deviceIds)) {
            List<DeviceEntity> deviceEntities = deviceService.listByIds(deviceIds);
            deviceMap = deviceEntities.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
        }
        Map<Integer, List<RecordManualCollectionEntity>> map = collectionEntities.stream().collect(Collectors.groupingBy(RecordManualCollectionEntity::getDeviceId));
        List<PassRateExcelDTO> list = new ArrayList<>();

        for (Map.Entry<Integer, List<RecordManualCollectionEntity>> entry : map.entrySet()) {
            Integer deviceId = entry.getKey();
            List<RecordManualCollectionEntity> value = entry.getValue();
            double productionQuantity = 0;
            double qualified = 0;
            for (RecordManualCollectionEntity manualCollectionEntity : value) {
                JSONArray jsonArray = JSONArray.parseArray(manualCollectionEntity.getReportContent());
                if (jsonArray == null) {
                    continue;
                }
                List<RecordManualCollectionDTO> dtos = JSON.parseArray(jsonArray.toJSONString(), RecordManualCollectionDTO.class);
                for (RecordManualCollectionDTO dto : dtos) {
                    if (dto.getEname().equals(ManualTargetConst.PRODUCTION_QUANTITY)) {
                        productionQuantity += dto.getValue();
                    }
                    if (dto.getEname().equals(ManualTargetConst.QUALIFIED)) {
                        qualified += dto.getValue();
                    }
                }
                // 合格率
                Double passRate = productionQuantity == 0 ? 0 : MathUtil.divideDouble(qualified, productionQuantity, 4, RoundingMode.HALF_UP);
                PassRateExcelDTO rateVO = PassRateExcelDTO.builder()
                        .deviceId(deviceId)
                        .deviceName(deviceMap.get(deviceId))
                        .passRate(passRate)
                        .build();
                list.add(rateVO);
            }
        }
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.PASS_RATE.getName()).build();
        excelWriter.write(list, writeSheet);
    }

    private void setFeedingInfoSheet(ExcelWriter excelWriter, WorkOrderEntity workOrder) {
        List<FeedRecordEntity> entities = feedRecordService.lambdaQuery()
                .eq(FeedRecordEntity::getState, FeedStateEnum.RELEASED.getCode())
                .eq(FeedRecordEntity::getWorkOrderNum, workOrder.getWorkOrderNumber()).list();
        List<Integer> lineIds = entities.stream().map(FeedRecordEntity::getLineId).distinct().collect(Collectors.toList());
        Map<Integer, String> lineMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(lineIds)) {

            List<ProductionLineEntity> lineEntities = lineService.listByIds(lineIds);
            lineMap = lineEntities.stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
        }
        List<Integer> fids = entities.stream().map(FeedRecordEntity::getFacId).distinct().collect(Collectors.toList());
        Map<Integer, String> fMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(fids)) {
            List<FacilitiesEntity> facilitiesEntities = facilitiesService.listByIds(fids);
            fMap = facilitiesEntities.stream().collect(Collectors.toMap(FacilitiesEntity::getFid, FacilitiesEntity::getFname));
        }
        for (FeedRecordEntity entity : entities) {
            entity.setLineName(lineMap.get(entity.getLineId()));
            entity.setFacName(fMap.get(entity.getFacId()));
        }

        Map<String, SupplierEntity> supplierMap = new HashMap<>();
        // 查询供应商相关信息
        List<String> supplierCodes = entities.stream().map(FeedRecordEntity::getSupplierCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supplierCodes)) {
            Page<SupplierEntity> page = supplierService.supplierOpenPage(SupplierSelectDTO.builder().fullCodes(supplierCodes).build());
            if (!CollectionUtils.isEmpty(page.getRecords())) {
                supplierMap = page.getRecords().stream().collect(Collectors.toMap(SupplierEntity::getCode, o -> o));
            }
        }

        List<FeedingInfoExcelDTO> dtos = new ArrayList<>();
        for (FeedRecordEntity res : entities) {
            SupplierEntity supplierEntity = supplierMap.get(res.getSupplierCode());
            dtos.add(FeedingInfoExcelDTO.builder()
                    .lineName(res.getLineName())
                    .facName(res.getFacName())
                    .materialCode(res.getMaterialCode())
                    .materialName(res.getMaterialName())
                    .materialNum(res.getMaterialNum())
                    .batchNumber(res.getBatchNumber())
                    .supplierName(Objects.isNull(supplierEntity) ? null : supplierEntity.getName())
                    .feedTime(res.getFeedTime())
                    .build());
        }
        //写入新的sheet
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(WorkOrderTraceTableEnum.FEEDING_INFO.getName()).build();
        excelWriter.write(dtos, writeSheet);
    }


    @Override
    public File asposePrintDataToPdf(ExportBillBatchDTO batchDTO) {
        File tempDirFile = null, temPdfDirFile = null;
        try {
            // 临时文件目录
            String tempFileDir = PathUtils.getAbsolutePath(this.getClass()) + File.separator + "temp" + File.separator + "trace_" + UUID.randomUUID();
            tempDirFile = new File(tempFileDir);
            FileUtils.forceMkdir(tempDirFile);
            // 填充数据到excel,并将这些excel文件的装到临时目录中
            excelData(batchDTO, tempDirFile);

            // 处理生产pdf文件
            String temPdfDir = PathUtils.getAbsolutePath(this.getClass()) + File.separator + "temp" + File.separator + UUID.randomUUID();
            temPdfDirFile = new File(temPdfDir);
            if (!temPdfDirFile.exists()) {
                boolean mkdirs = temPdfDirFile.mkdirs();
            }
            File[] files = tempDirFile.listFiles();
            for (File temTemplateFile : files) {
                PdfUtil.excelToPdf(temTemplateFile, temPdfDir, 1, WorkOrderTraceTableEnum.values().length + 1, true);
            }
            // 合成后的目标pdf文件
            String targetPath = PathUtils.getAbsolutePath(this.getClass()) + File.separator + "temp" + File.separator + batchDTO.getFileName() + Constant.PDF;
            return PdfUtil.mergePdf(temPdfDir, targetPath);
        } catch (Exception e) {
            log.error("导出pdf文件失败", e);
            throw new ResponseException("导出pdf文件失败");
        } finally {
            // 删除临时文件夹
            FileUtils.deleteQuietly(tempDirFile);
            FileUtils.deleteQuietly(temPdfDirFile);
        }
    }

    /**
     * 获取模板文件, url对应模板不存在,这获取默认模板
     */
    private InputStream getTemplateBytes(String templateUrl, String defaultTemplateClassPath) throws IOException {
        byte[] templateBytes = null;
        if (StringUtils.isEmpty(templateUrl)) {
            // 如果用户没有上传模板,则使用默认模板
            templateBytes = this.downloadDefaultExportTemplate(defaultTemplateClassPath);
        } else {
            templateBytes = fastDfsClientService.downloadFile(templateUrl);
        }
        return new ByteArrayInputStream(templateBytes);
    }

    /**
     * 填充数据到 excel,并将这些excel文件装到临时目录中
     *
     * @param batchDTO
     * @param tempDirFile 临时目录文件对象
     * @return
     */
    private void excelData(ExportBillBatchDTO batchDTO, File tempDirFile) throws Exception {
        String defaultTemplateName = ReportFormConstant.DEFAULT_WORK_ORDER_TRACE_TEMPLATE + Constant.XLSX;
        if (StringUtils.isEmpty(batchDTO.getFileName())) {
            batchDTO.setFileName("工单追溯");
        }
        InputStream templateInputStream = null;
        ExcelWriter excelWriter = null;
        try {
            // 如果用户没有上传模板,则使用默认模板
            templateInputStream = getTemplateBytes(batchDTO.getFileAddress(), "classpath:template" + File.separator + defaultTemplateName);

            // workOrderNumbers 工单追溯这里只有一个
            List<String> workOrderNumbers = batchDTO.getExportBillNumberList();
            for (String workOrderNumber : workOrderNumbers) {
                WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);

                // 生产的excel文件
                File temTemplateFile = new File(tempDirFile.getPath() + File.separator + UUID.randomUUID() + Constant.XLSX);
                if (!temTemplateFile.exists()) {
                    // 不存在文件
                    boolean isMkdir = temTemplateFile.createNewFile();
                }
                excelWriter = EasyExcelFactory.write(temTemplateFile).withTemplate(templateInputStream)
                        .excelType(ExcelTypeEnum.XLSX).autoCloseStream(true).build();
                Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

                Map<String, BiConsumer<ExcelWriter, WorkOrderEntity>> functionMap = getFunctionMap();
                for (WorkOrderTraceTableEnum value : WorkOrderTraceTableEnum.values()) {
                    WriteSheet writeSheet = EasyExcelFactory.writerSheet(value.getName()).build();
                    if (Objects.nonNull(writeSheet)) {
                        //处理数据
                        BiConsumer<ExcelWriter, WorkOrderEntity> consumer = functionMap.get(value.getName());
                        consumer.accept(excelWriter, workOrder);
                    }
                }
                // 打开工作簿时应用程序是否应执行完全重新计算。
                workbook.setForceFormulaRecalculation(true);
                // 强行刷新单元格公式 计算公式保存结果，但不改变公式
                XSSFFormulaEvaluator.evaluateAllFormulaCells(workbook);
                excelWriter.finish();
            }
        } finally {
            // 关闭流
            IOUtils.closeQuietly(templateInputStream);
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public File asposePrintDataToExcel(ExportBillBatchDTO batchDTO) {
        try {
            // 临时文件目录
            String tempFileDir = PathUtils.getAbsolutePath(this.getClass()) + File.separator + "temp" + File.separator + "trace_" + UUID.randomUUID();
            File tempDirFile = new File(tempFileDir);
            FileUtils.forceMkdir(tempDirFile);

            // 填充数据到excel,并将这些excel文件的装到临时目录中
            excelData(batchDTO, tempDirFile);

            return tempDirFile;
        } catch (Exception e) {
            log.error("导出excel失败, ", e);
            throw new ResponseException("导出excel失败");
        }
    }

    @Override
    public void uploadCustomExportTemplate(MultipartFile file, String username) {

    }

    @Override
    public byte[] downloadCustomExportTemplate(Integer id) {
        return new byte[0];
    }
}
