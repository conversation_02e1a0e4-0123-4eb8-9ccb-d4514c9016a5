package com.yelink.dfs.service.common.config;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfscommon.entity.dfs.OrderPushDownItemWriteBackEntity;
import io.swagger.models.auth.In;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderPushDownItemWriteBackService extends IService<OrderPushDownItemWriteBackEntity> {

    /**
     * 通过itemId 获取实例
     * @param itemId itemId
     * @return 实例
     */
    OrderPushDownItemWriteBackEntity getByItemId(Integer itemId);

    /**
     * 获取实例列表
     * @param itemIds itemIds
     * @return 实例列表
     */
    List<OrderPushDownItemWriteBackEntity> getByItemIds(Collection<Integer> itemIds);
}

