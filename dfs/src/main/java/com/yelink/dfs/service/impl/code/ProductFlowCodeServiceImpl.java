package com.yelink.dfs.service.impl.code;

import com.alibaba.excel.util.IoUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.code.ProductFlowCodeMaintainStateEnum;
import com.yelink.dfs.constant.code.ProductFlowCodeQualityStateEnum;
import com.yelink.dfs.constant.code.ProductFlowCodeStateEnum;
import com.yelink.dfs.constant.mongodb.OperatorEnum;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.constant.rule.RulePrefixEnum;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.code.dto.CodeInformationDTO;
import com.yelink.dfs.entity.code.dto.CodeRecordSelectDTO;
import com.yelink.dfs.entity.code.dto.CodeRelevanceDTO;
import com.yelink.dfs.entity.code.dto.CodeRelevanceViewDTO;
import com.yelink.dfs.entity.code.dto.CodeRelevanceViewSelectDTO;
import com.yelink.dfs.entity.code.dto.CodeSelectDTO;
import com.yelink.dfs.entity.code.dto.CodeTargetDTO;
import com.yelink.dfs.entity.code.dto.CodeTargetSubmitDTO;
import com.yelink.dfs.entity.code.dto.CodeTypeDTO;
import com.yelink.dfs.entity.code.dto.FlowCodeInformationDTO;
import com.yelink.dfs.entity.code.dto.ProductFlowCodeImportDTO;
import com.yelink.dfs.entity.common.config.dto.ProductFlowCodeConfigDTO;
import com.yelink.dfs.entity.feed.FeedRecordEntity;
import com.yelink.dfs.entity.model.CompanyEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.CheckDeleteDTO;
import com.yelink.dfs.entity.product.dto.ProductFlowCodeSelectDTO;
import com.yelink.dfs.entity.product.dto.ProductFlowJudgeDTO;
import com.yelink.dfs.entity.product.vo.ProcedureRecordVO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.target.record.dto.BindBatchDTO;
import com.yelink.dfs.entity.target.record.dto.CheckReportBackDTO;
import com.yelink.dfs.entity.task.dto.ProductOrderTaskDTO;
import com.yelink.dfs.entity.task.dto.WorkOrderTaskDTO;
import com.yelink.dfs.mapper.code.CodeTargetRecordMapper;
import com.yelink.dfs.mapper.code.ProductFlowCodeMapper;
import com.yelink.dfs.mapper.code.ProductFlowCodeRecordMapper;
import com.yelink.dfs.mapper.feed.FeedRecordMapper;
import com.yelink.dfs.mapper.model.FacilitiesMapper;
import com.yelink.dfs.mapper.product.CraftProcedureMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.common.dto.DictDTO;
import com.yelink.dfs.open.v1.mongodb.dto.ConditionDTO;
import com.yelink.dfs.open.v1.supplier.dto.SupplierDetailDTO;
import com.yelink.dfs.open.v1.supplier.dto.SupplierSelectDTO;
import com.yelink.dfs.service.barcode.BarCodeAnalysisService;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.barcode.LabelTypeConfigService;
import com.yelink.dfs.service.code.CodeLogService;
import com.yelink.dfs.service.code.CodeRelevanceService;
import com.yelink.dfs.service.code.CodeTargetRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordExtendService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.model.CompanyService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.mongodb.MongodbService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.MaterialAttributeListService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.MaterialTypeConfigAttributeService;
import com.yelink.dfs.service.product.ProcedureInspectionConfigService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.dfs.barcode.LabelTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.CodeRelationTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.dto.BatchGenerateCodeDTO;
import com.yelink.dfscommon.dto.BatchGenerateCodeReqDTO;
import com.yelink.dfscommon.dto.BodeSelectDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.dfs.PrintInfoDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.dfs.CheckReportDTO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeListEntity;
import com.yelink.dfscommon.entity.dfs.MaterialTypeConfigAttributeEntity;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeRelevanceEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.CodeTargetRecordEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.DynamicEasyExcelImportUtils;
import com.yelink.dfscommon.utils.DynamicEasyExcelListener;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description 生产流水码
 * @Date 2022/3/31 14:40
 */
@Slf4j
@Service
public class ProductFlowCodeServiceImpl extends ServiceImpl<ProductFlowCodeMapper, ProductFlowCodeEntity> implements ProductFlowCodeService {

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private NumberRuleService numberRuleService;
    @Lazy
    @Autowired
    private MaterialService materialService;
    @Lazy
    @Autowired
    private WorkOrderService workOrderService;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Autowired
    private DictService dictService;
    @Resource
    @Lazy
    private LabelService labelService;
    @Autowired
    private ProductFlowCodeRecordMapper productFlowCodeRecordMapper;
    @Autowired
    private RuleSeqService ruleSeqService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    private CraftProcedureMapper craftProcedureMapper;
    @Resource
    private ModelService modelService;
    @Resource
    private OrderWorkOrderService orderWorkOrderService;
    @Resource
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    private CodeRelevanceService codeRelevanceService;
    @Resource
    @Lazy
    private ProductFlowCodeRecordService productFlowCodeRecordService;
    @Resource
    private FacilitiesMapper facilitiesMapper;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    @Lazy
    private OperationLogService operationLogService;
    @Resource
    private ProductFlowCodeMapper productFlowCodeMapper;
    @Resource
    private WorkPropertise workPropertise;
    @Resource
    private LabelTypeConfigService labelTypeConfigService;
    @Resource
    private MongodbService mongodbService;
    @Resource
    private CompanyService companyService;
    @Resource
    private FeedRecordMapper feedRecordMapper;
    @Resource
    private CodeTargetRecordMapper codeTargetRecordMapper;
    @Resource
    private ProcedureInspectionConfigService procedureInspectionConfigService;
    @Resource
    private MaterialAttributeListService materialAttributeListService;
    @Resource
    private ImportProgressService importProgressService;
    @Resource
    private CodeTargetRecordService codeTargetRecordService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private ExcelService excelService;
    @Autowired
    private SupplierService supplierService;
    @Resource
    private ProductFlowCodeRecordExtendService codeRecordExtendService;
    @Resource
    @Lazy
    private CraftProcedureService craftProcedureService;
    @Resource
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    @Lazy
    private CodeLogService codeLogService;
    @Resource
    private MaterialTypeConfigAttributeService materialTypeConfigAttributeService;
    @Resource
    @Lazy
    private BarCodeService barCodeService;

    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");
    /**
     * destination:去向（原料->成品）
     */
    private static final String DESTINATION = "destination";
    /**
     * trace:溯源（成品->原料）
     */
    private static final String TRACE = "trace";

    /**
     * 查询生产流水码列表
     */
    @Override
    public Page<ProductFlowCodeEntity> list(ProductFlowCodeSelectDTO selectDTO) {
        Page<ProductFlowCodeEntity> page = new Page<>();
        LambdaQueryWrapper<ProductFlowCodeEntity> wrapper = conditionQuery(selectDTO);
        //按照生产流水码编码进行排序(2.16.1现网查询使用了flowCode做索引导致查询性能降低，改为使用id排序)
//        if (Objects.nonNull(selectDTO.getSeqStart()) && Objects.nonNull(selectDTO.getSeqEnd())) {
        if (selectDTO.getSeqStart()!=null && selectDTO.getSeqEnd()!=null) {
            wrapper.orderByAsc(ProductFlowCodeEntity::getSeq);
        } else {
            if(selectDTO.getIsAsc()!=null&&selectDTO.getIsAsc()) {
                wrapper.orderByAsc(ProductFlowCodeEntity::getId);
            }else{
                wrapper.orderByDesc(ProductFlowCodeEntity::getId);
            }
        }
        if (selectDTO.getCurrentPage() == null || selectDTO.getSize() == null) {
            List<ProductFlowCodeEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = this.page(new Page<>(selectDTO.getCurrentPage(), selectDTO.getSize()), wrapper);
        }
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Boolean.TRUE.equals(selectDTO.getIsShowSimpleInfo())) {
            return page;
        }
        List<ProductFlowCodeEntity> records = page.getRecords();
        Map<String, ProductOrderEntity> productOrderEntityMap = new HashMap<>();
        // 如果为订单流水码，需要查询生产订单相关信息
        if (Objects.nonNull(selectDTO.getType()) && selectDTO.getType().equals(ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode())) {
            List<String> productOrderNumbers = records.stream().map(ProductFlowCodeEntity::getRelationNumber).distinct().collect(Collectors.toList());
            List<ProductOrderEntity> productOrderEntities = extProductOrderInterface.getListByProductOrderNumbers(productOrderNumbers);
            productOrderEntityMap = productOrderEntities.stream().collect(Collectors.toMap(ProductOrderEntity::getProductOrderNumber, o -> o));
        }
        for (ProductFlowCodeEntity productFlowCodeEntity : page.getRecords()) {
            productFlowCodeEntity.setProductOrderEntity(productOrderEntityMap.get(productFlowCodeEntity.getRelationNumber()));
        }
        batchShowStateAndName(page.getRecords());
        //获取条码关联属性值
        showTargetList(page.getRecords());
//        page.setRecords(records);
        return page;
    }



    /**
     * 获取条码详情
     * @param id
     * @return
     */
    @Override
    public ProductFlowCodeEntity getDetailById(Integer id){
        ProductFlowCodeEntity productFlowCodeEntity = this.getById(id);
        showStateAndName(productFlowCodeEntity);
        //展示关联属性
        this.showTargetList(Stream.of(productFlowCodeEntity).collect(Collectors.toList()));
        List<MaterialAttributeListEntity> materialAttributeList=materialAttributeListService.listAttributeByMaterial(productFlowCodeEntity.getMaterialCode());
        //加上物料类型的
        for(MaterialAttributeListEntity materialAttributeListEntity:materialAttributeList){
            if(productFlowCodeEntity.getTargetMap()==null){
                continue;
            }
            String value = productFlowCodeEntity.getTargetMap().get(materialAttributeListEntity.getAttributeCode());
            if(StringUtils.isNotBlank(value)) {
                materialAttributeListEntity.setValue(value);
            }
        }
        productFlowCodeEntity.setMaterialAttributeListEntityList(materialAttributeList);
        // 查询供应商相关信息
        SupplierEntity supplierEntity = supplierService.getEntityByCode(SupplierDetailDTO.builder().code(productFlowCodeEntity.getSupplierCode()).build());
        productFlowCodeEntity.setSupplierName(Objects.nonNull(supplierEntity) ? supplierEntity.getName() : null);
        return productFlowCodeEntity;
    }
    /**
     * 条件查询
     *
     * @param
     * @return
     */
    private LambdaQueryWrapper<ProductFlowCodeEntity> conditionQuery(ProductFlowCodeSelectDTO productFlowCodeSelectDTO) {
        LambdaQueryWrapper<ProductFlowCodeEntity> wrapper = new LambdaQueryWrapper<>();
        WrapperUtil.eq(wrapper, ProductFlowCodeEntity::getState, productFlowCodeSelectDTO.getState());
        WrapperUtil.like(wrapper, ProductFlowCodeEntity::getMaterialCode, productFlowCodeSelectDTO.getMaterialCode());
        // 物料特征参数
        wrapper.eq(productFlowCodeSelectDTO.getSkuId() != null, ProductFlowCodeEntity::getSkuId, productFlowCodeSelectDTO.getSkuId());
        // 打印状态
        wrapper.eq(!ObjectUtils.isEmpty(productFlowCodeSelectDTO.getIsPrint()), ProductFlowCodeEntity::getIsPrint, productFlowCodeSelectDTO.getIsPrint());
        WrapperUtil.like(wrapper, ProductFlowCodeEntity::getMaterialName, productFlowCodeSelectDTO.getMaterialName());
        WrapperUtil.like(wrapper, ProductFlowCodeEntity::getProductFlowCode, productFlowCodeSelectDTO.getProductFlowCode());
        WrapperUtil.eq(wrapper, ProductFlowCodeEntity::getMaterialType, productFlowCodeSelectDTO.getMaterialType());
        WrapperUtil.eq(wrapper, ProductFlowCodeEntity::getRelationType, productFlowCodeSelectDTO.getRelationType());
        //自增字段进行区间查询
        WrapperUtil.between(wrapper, ProductFlowCodeEntity::getSeq, productFlowCodeSelectDTO.getSeqStart(), productFlowCodeSelectDTO.getSeqEnd());
        //时间
        WrapperUtil.between(wrapper, ProductFlowCodeEntity::getCreateTime, productFlowCodeSelectDTO.getStartTime(), productFlowCodeSelectDTO.getEndTime());
        // 供应商编码
        WrapperUtil.like(wrapper, ProductFlowCodeEntity::getSupplierCode, productFlowCodeSelectDTO.getSupplierCode());
//        // 供应商名称
//        WrapperUtil.like(wrapper, ProductFlowCodeEntity::getSupplierName, productFlowCodeSelectDTO.getSupplierName());
        // 采购时间
        wrapper.between(StringUtils.isNoneBlank(productFlowCodeSelectDTO.getPurchaseStartTime(), productFlowCodeSelectDTO.getPurchaseEndTime()),
                ProductFlowCodeEntity::getPurchaseTime, productFlowCodeSelectDTO.getPurchaseStartTime(), productFlowCodeSelectDTO.getPurchaseEndTime());
        wrapper.in(!CollectionUtils.isEmpty(productFlowCodeSelectDTO.getProductFlowCodes()), ProductFlowCodeEntity::getProductFlowCode, productFlowCodeSelectDTO.getProductFlowCodes());
        // 供应商名称查询
        if (StringUtils.isNotBlank(productFlowCodeSelectDTO.getSupplierName())) {
            Page<SupplierEntity> page = supplierService.supplierOpenPage(SupplierSelectDTO.builder().name(productFlowCodeSelectDTO.getSupplierName()).build());
            if (CollectionUtils.isEmpty(page.getRecords())) {
                wrapper.isNull(ProductFlowCodeEntity::getProductFlowCode);
            }
            List<String> supplierCodes = page.getRecords().stream().map(SupplierEntity::getCode).collect(Collectors.toList());
            wrapper.in(ProductFlowCodeEntity::getSupplierCode, supplierCodes);
        }

        if (StringUtils.isNotBlank(productFlowCodeSelectDTO.getWorkOrder())) {
            List<String> relationOrderNumbers = Arrays.stream(productFlowCodeSelectDTO.getWorkOrder().split(Constant.SEP)).collect(Collectors.toList());
            wrapper.in(ProductFlowCodeEntity::getRelationNumber, relationOrderNumbers);
            // 生产订单备注字段筛选
            if (StringUtils.isNotBlank(productFlowCodeSelectDTO.getProductOrderRemark())) {
                PageResult<ProductOrderEntity> productOrderPage = extProductOrderInterface.getPage(
                        ProductOrderSelectOpenDTO.builder()
                                .productOrderNumbers(relationOrderNumbers)
                                .productOrderRemark(productFlowCodeSelectDTO.getProductOrderRemark())
                                .build()
                );
                List<String> productOrderNumbers = productOrderPage.getRecords().stream().map(ProductOrderEntity::getProductOrderNumber).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(productOrderNumbers)) {
                    wrapper.isNull(ProductFlowCodeEntity::getProductFlowCode);
                    return wrapper;
                }
                wrapper.in(ProductFlowCodeEntity::getRelationNumber, productOrderNumbers);
            }
        }
        //按工序过站记录筛选
        if (StringUtils.isNotBlank(productFlowCodeSelectDTO.getWorkOrder()) && productFlowCodeSelectDTO.getCraftProcedureId() != null && productFlowCodeSelectDTO.getIsRecord() != null) {
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getProdureId, productFlowCodeSelectDTO.getCraftProcedureId())
                    .eq(ProductFlowCodeRecordEntity::getRelationNumber, productFlowCodeSelectDTO.getWorkOrder());
            List<ProductFlowCodeRecordEntity> recordEntityList = productFlowCodeRecordMapper.selectList(lambdaQueryWrapper);
            if (productFlowCodeSelectDTO.getIsRecord()) {
                if (CollectionUtils.isEmpty(recordEntityList)) {
                    wrapper.isNull(ProductFlowCodeEntity::getProductFlowCode);
                    return wrapper;
                }
                wrapper.in(ProductFlowCodeEntity::getProductFlowCode, recordEntityList.stream().map(ProductFlowCodeRecordEntity::getProductFlowCode).collect(Collectors.toList()));
            } else if (!CollectionUtils.isEmpty(recordEntityList)) {
                wrapper.notIn(ProductFlowCodeEntity::getProductFlowCode, recordEntityList.stream().map(ProductFlowCodeRecordEntity::getProductFlowCode).collect(Collectors.toList()));
            }
        }
        //是否维修
        if (productFlowCodeSelectDTO.getIsMaintain() != null) {
            if (productFlowCodeSelectDTO.getIsMaintain()) {
                wrapper.isNotNull(ProductFlowCodeEntity::getMaintainState);
            } else {
                wrapper.isNull(ProductFlowCodeEntity::getMaintainState);
            }
        }
        wrapper.eq(productFlowCodeSelectDTO.getType() != null, ProductFlowCodeEntity::getType, productFlowCodeSelectDTO.getType());
        if (StringUtils.isNotBlank(productFlowCodeSelectDTO.getTypes())) {
            wrapper.in(ProductFlowCodeEntity::getType, productFlowCodeSelectDTO.getTypes().split(Constant.SEP));
        }
       //按工装筛选
        if(productFlowCodeSelectDTO.getIsProcessAssembly()!=null){
            List<DictEntity> list = dictService.commonTypeList(DictDTO.builder().type("materialType").isProcessAssembly(true).build()).getRecords();
            if(productFlowCodeSelectDTO.getIsProcessAssembly()){
                if(CollectionUtils.isEmpty(list)){
                    wrapper.isNull(ProductFlowCodeEntity::getProductFlowCode);
                }else{
                    wrapper.in(ProductFlowCodeEntity::getMaterialType,list.stream().map(DictEntity::getId).collect(Collectors.toList()));
                }
            }else{
                if(!CollectionUtils.isEmpty(list)){
                    wrapper.notIn(ProductFlowCodeEntity::getMaterialType,list.stream().map(DictEntity::getId).collect(Collectors.toList()));
                }
            }
        }
        return wrapper;
    }

    /**
     * 查询生产流水码列表(含过站记录，如无必要不调用!!!，换成listSimple)
     */
    @Override
    public Page<ProductFlowCodeEntity> listAndShowRecords(ProductFlowCodeSelectDTO productFlowCodeSelectDTO) {
        Page<ProductFlowCodeEntity> page;
        if (!CollectionUtils.isEmpty(productFlowCodeSelectDTO.getCraftProcedureIds())) {
            String idString = productFlowCodeSelectDTO.getCraftProcedureIds().stream()
                    .map(id -> id.toString())
                    .collect(Collectors.joining(", "));
            productFlowCodeSelectDTO.setCraftProcedureIdsString(idString);
        }
        if (productFlowCodeSelectDTO.getCurrentPage() == null) {
            productFlowCodeSelectDTO.setCurrentPage(1);
        }
        if (productFlowCodeSelectDTO.getSize() == null) {
            productFlowCodeSelectDTO.setSize(10000);
        }
        if (productFlowCodeSelectDTO.getProcedureIsRecord() != null && !productFlowCodeSelectDTO.getProcedureIsRecord()) {
            page = productFlowCodeMapper.selectByRecordNot(new Page<>(productFlowCodeSelectDTO.getCurrentPage(), productFlowCodeSelectDTO.getSize()), productFlowCodeSelectDTO);

        } else {
            page = productFlowCodeMapper.selectByRecord(new Page<>(productFlowCodeSelectDTO.getCurrentPage(), productFlowCodeSelectDTO.getSize()), productFlowCodeSelectDTO);
        }
        List<CraftProcedureEntity> craftProcedureEntities;
        //订单流水查询所有工单的工艺工序集合
        if (productFlowCodeSelectDTO.getType() != null && ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == productFlowCodeSelectDTO.getType()) {
            craftProcedureEntities = getCraftProcedureListByOrderNumber(productFlowCodeSelectDTO.getWorkOrder());
        } else {
            craftProcedureEntities = getCraftProcedureListByWorkOrder(productFlowCodeSelectDTO.getWorkOrder());
        }
        // 批量展示
        batchShowStateAndName(page.getRecords());
        batchSetRecords(page.getRecords(), craftProcedureEntities);
        return page;
    }


    /**
     * 查询工单是用的订单还是生产流水码
     *
     * @param workerOrderNumber
     * @return
     */
    @Override
    public Map<String, String> getRelationType(String workerOrderNumber) {
        Long count = this.lambdaQuery().eq(ProductFlowCodeEntity::getRelationNumber, workerOrderNumber)
                .count();
        Map<String, String> resultMap = new HashMap<>(2);
        if (count > 0) {
            resultMap.put("type", "1");
            resultMap.put("workOrder", workerOrderNumber);
        } else {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workerOrderNumber);
            resultMap.put("type", "4");
            resultMap.put("workOrder", workOrderEntity.getProductOrderNumber());
        }
        return resultMap;
    }

    /**
     * 获取订单工艺工序列表
     *
     * @param orderNumber
     * @return
     */
    @Override
    public List<CraftProcedureEntity> getCraftProcedureListByOrderNumber(String orderNumber) {
        List<WorkOrderEntity> workOrderEntityList = orderWorkOrderService.listWorkOrderByOrderNumber(orderNumber);
        if (CollectionUtils.isEmpty(workOrderEntityList)) {
            return new ArrayList<>();
        }
        List<CraftProcedureEntity> craftProcedureEntities = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : workOrderEntityList) {
            craftProcedureEntities.addAll(getCraftProcedureListByWorkOrder(workOrderEntity.getWorkOrderNumber()));
        }
        craftProcedureEntities =craftProcedureService.sortCraftProceduresLine(craftProcedureEntities);
        return craftProcedureEntities.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取工单工艺工序列表
     *
     * @param workOrderNumber
     * @return
     */
    @Override
    public List<CraftProcedureEntity> getCraftProcedureListByWorkOrder(String workOrderNumber) {
        Integer craftId = workOrderService.getCraftIdByWorkOrderNumber(workOrderNumber);
        if (Objects.isNull(craftId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CraftProcedureEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(CraftProcedureEntity::getCraftId, craftId);
        qw.orderByAsc(CraftProcedureEntity::getId);
        List<CraftProcedureEntity> list = craftProcedureMapper.selectList(qw);
        return craftProcedureService.sortCraftProceduresLine(list);
    }

    /**
     * 获取工单工艺工序列表(被勾选工序)
     *
     * @param workOrderNumber
     * @return
     */
    @Override
    public List<CraftProcedureEntity> getUsedCraftProcedureListByWorkOrder(String workOrderNumber) {
        List<CraftProcedureEntity> list = this.getCraftProcedureListByWorkOrder(workOrderNumber);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        //筛选出已经选择的工艺工序
        List<WorkOrderProcedureRelationEntity> relationEntityList = workOrderProcedureRelationService
                .lambdaQuery().eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumber)
                .list();
        //如果没选择工序就展示完整工艺路线
        if (CollectionUtils.isEmpty(relationEntityList)) {
            return list;
        }
        Set<Integer> craftProcedureIdSet = relationEntityList.stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toSet());
        return list.stream().filter(obj -> craftProcedureIdSet.contains(obj.getId())).collect(Collectors.toList());
    }

    @Override
    @Async
    public void addProductionCode(ProductFlowCodeEntity entity, String userName) {
        this.batchAddProductionCode(Stream.of(entity).collect(Collectors.toList()), false, userName);
    }

    @Override
    @Async
    public void batchAddProductionCode(List<ProductFlowCodeEntity> list, String userName) {
        this.batchAddProductionCode(list, true, userName);
    }

    /**
     * 在Async 方法上标注@Transactional会失效
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddProductionCode(List<ProductFlowCodeEntity> list, boolean isBatch, String userName) {
        //初始化进度
        ImportProgressDTO build = ImportProgressDTO.builder()
                .progress(Double.valueOf(com.yelink.dfscommon.constant.Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .code(Result.SUCCESS_CODE)
                .build();
        Map<Integer, List<String>> keyMap = new HashMap<>();
        int rowTotal = list.stream().map(ProductFlowCodeEntity::getNum).filter(o -> o != null && o > 0).reduce(Integer::sum).orElse(0);
        keyMap.put(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode(),
                Arrays.asList(RedisKeyPrefix.PRODUCTION_SEQUENCE_CODE_PROGRESS,
                        RedisKeyPrefix.PRODUCTION_SEQUENCE_CODE_PROGRESS_LOCK));
        keyMap.put(ProductFlowCodeTypeEnum.FINISHED_PRODUCT_CODE.getCode(),
                Arrays.asList(RedisKeyPrefix.FINISHED_PRODUCT_CODE_PROGRESS,
                        RedisKeyPrefix.FINISHED_PRODUCT_CODE_PROGRESS_LOCK));
        keyMap.put(ProductFlowCodeTypeEnum.PURCHASE_PRODUCT_CODE.getCode(),
                Arrays.asList(RedisKeyPrefix.PURCHASE_PRODUCT_CODE_PROGRESS,
                        RedisKeyPrefix.PURCHASE_PRODUCT_CODE_PROGRESS_LOCK));
        keyMap.put(ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode(),
                Arrays.asList(RedisKeyPrefix.ORDER_PRODUCT_CODE_PROGRESS,
                        RedisKeyPrefix.ORDER_PRODUCT_CODE_PROGRESS_LOCK));
        List<String> keyAndLock = keyMap.get(list.get(0).getType());
        String batchRedisKey = keyAndLock.get(0);
        String batchRedisLock = keyAndLock.get(1);
        if (isBatch) {
            // 处理Redis操作，重置进度和加锁
            this.processRedis(batchRedisKey, batchRedisLock, build);
        }
        try {
            if (rowTotal == 0) {
                Double processPercent = MathUtil.divideDouble(1, 1, 2);
                build = ImportProgressDTO.builder().progress(processPercent).executionDescription(String.format("新增完成：成功%s条；失败%s条", 0, 0)).executionStatus(processPercent.compareTo(1.0) == 0).code(Result.SUCCESS_CODE).build();
                // 设置生成进度
                setRedis("", batchRedisKey, build, isBatch);
                if (isBatch) {
                    // 释放批量锁
                    redisTemplate.delete(batchRedisLock);
                }
                return;
            }
            Date date = new Date();
            for (ProductFlowCodeEntity entity : list) {
                entity.setCreateBy(userName);
                entity.setCreateTime(date);
                String relationNumber=entity.getRelationNumber();
                if (relationNumber == null) {
                    relationNumber = "";
                }
                String redisKey = keyAndLock.get(0) +relationNumber;
                String redisLock = keyAndLock.get(1) + relationNumber;
                // 处理Redis操作，重置进度和加锁
                this.processRedis(redisKey, redisLock, build);

                try {
                    String materialCode, description, orderNumber;
                    if (ProductFlowCodeTypeEnum.PURCHASE_PRODUCT_CODE.getCode() == entity.getType()) {
                        //采购单品码逻辑
                        materialCode = entity.getMaterialCode();
                        orderNumber = entity.getRelationNumber();
                        entity.setRelationType(CodeRelationTypeEnum.PURCHASE_ORDER.getCode());
                    } else if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == entity.getType()) {
                        //订单流水码逻辑
                        if(StringUtils.isNotBlank(entity.getRelationNumber())) {
                            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(entity.getRelationNumber()).build());
                            Integer orderState = orderEntity.getState();
                            ProductOrderMaterialEntity orderMaterialEntity = orderEntity.getProductOrderMaterials().get(0);
                            entity.setCustomerCode(orderEntity.getCustomerCode());
                            entity.setCustomerName(orderEntity.getCustomerName());
                            entity.setCustomerMaterialCode(orderMaterialEntity.getCustomerMaterialCode());
                            entity.setCustomerMaterialName(orderMaterialEntity.getCustomerMaterialName());
                            entity.setCustomerSpecification(orderMaterialEntity.getCustomerSpecification());
                            orderNumber = entity.getRelationNumber();
                            if (orderState.equals(OrderStateEnum.CREATED.getCode())
                                    || orderState.equals(OrderStateEnum.CLOSED.getCode())
                                    || orderState.equals(OrderStateEnum.CANCELED.getCode())) {
                                //若订单单状态为创建、关闭、取消三种状态,提示该三种状态下不能创建生产流水码
                                description = "该订单[" + orderNumber + "]状态下不能添加订单流水码";
                                build.setExecutionDescription(description);
                                build.setExecutionStatus(false);
                                build.setProgress(1.0);
                                // 设置生成进度
                                setRedis(redisKey, batchRedisKey, build, isBatch);
                                throw new ResponseException(description);
                            }
                        }
                        materialCode = entity.getMaterialCode();
                        entity.setRelationType(CodeRelationTypeEnum.PRODUCT_ORDER.getCode());
                    } else {
                        //新增时，不添加组成信息，打印时，再新增组成信息，并更新流水码
                        WorkOrderEntity workOrderEntity=null;
                        if(StringUtils.isNotBlank(entity.getRelationNumber())) {
                             workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(entity.getRelationNumber());
                            orderNumber = workOrderEntity.getWorkOrderNumber();
                            if (workOrderEntity.getState().equals(WorkOrderStateEnum.CREATED.getCode())
                                    || workOrderEntity.getState().equals(WorkOrderStateEnum.CLOSED.getCode())
                                    || workOrderEntity.getState().equals(WorkOrderStateEnum.CANCELED.getCode())) {
                                //若工单状态为创建、关闭、取消三种状态,提示该三种状态下不能创建生产流水码
                                description = "该工单[" + orderNumber + "]状态下不能添加生产流水码";
                                build.setExecutionDescription(description);
                                build.setExecutionStatus(false);
                                build.setProgress(1.0);
                                // 设置生成进度
                                setRedis(redisKey, batchRedisKey, build, isBatch);
                                throw new ResponseException(description);
                            }
                            entity.setCustomerCode(workOrderEntity.getCustomerCode());
                            entity.setCustomerName(workOrderEntity.getCustomerName());
                            entity.setCustomerMaterialCode(workOrderEntity.getCustomerMaterialCode());
                            entity.setCustomerMaterialName(workOrderEntity.getCustomerMaterialName());
                            entity.setCustomerSpecification(workOrderEntity.getCustomerSpecification());
                        }
                        materialCode =entity.getMaterialCode()!=null?entity.getMaterialCode():workOrderEntity.getMaterialCode();
                        entity.setSkuId(workOrderEntity==null?null:workOrderEntity.getSkuId());
                        entity.setRelationType(CodeRelationTypeEnum.WORK_ORDER.getCode());

                    }

                    MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
                    if (materialEntity == null) {
                        description = "查询不到物料[" + materialCode + "]的信息";
                        build.setExecutionDescription(description);
                        build.setExecutionStatus(false);
                        build.setProgress(1.0);
                        // 设置生成进度
                        setRedis(redisKey, batchRedisKey, build, isBatch);
                        throw new ResponseException(description);
                    }
                    //判断当前工单下的物料是否支持 流水码管理
                    if (!materialEntity.getIsSupportCodeManage() && !entity.getType().equals(ProductFlowCodeTypeEnum.PURCHASE_PRODUCT_CODE.getCode())) {
                        description = "物料[" + materialCode + "]不支持序列号管理";
                        build.setExecutionDescription(description);
                        build.setExecutionStatus(false);
                        build.setProgress(1.0);
                        // 设置生成进度
                        setRedis(redisKey, batchRedisKey, build, isBatch);
                        throw new ResponseException(description);
                    }
                    //状态(默认为 未打印)
                    entity.setState(ProductFlowCodeStateEnum.NO_PRINT.getCode());
                    entity.setMaterialName(materialEntity.getName());
                    entity.setMaterialCode(materialEntity.getCode());
                    entity.setMaterialType(materialEntity.getType());
                    NumberRulesConfigEntity numberRulesConfigEntity = numberRuleService.getById(entity.getNumberRuleId());

                    // 批量单据生成码或一个单据批量生成码
                    Double processPercent;
                    List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(entity.getRuleDetail(), RuleDetailDTO.class);
                    List<ProductFlowCodeEntity> batchAddList = new ArrayList<>();
                    // 通过编码规则生成编码
                    for (int i = 1; i <= entity.getNum(); i++) {
                        NumberCodeDTO seqById = numberRuleService.generateRules(numberRulesConfigEntity.getId(), ruleDetailDTOs, entity.getRelatedMap());
                        if (seqById.getSeq() == null) {
                            build = ImportProgressDTO.builder().executionDescription("批量新增时，编码规则需要存在自动生成序号").executionStatus(false).progress(1.0).code(Result.FAIL_CODE).build();
                            // 设置生成进度
                            setRedis(redisKey, batchRedisKey, build, isBatch);
                            throw new ResponseException("批量新增时，编码规则需要存在自动生成序号");
                        }
                        entity.setId(null);
                        entity.setProductFlowCode(seqById.getCode());
                        entity.setSeq(seqById.getSeq());
                        batchAddList.add(JacksonUtil.convertObject(entity, ProductFlowCodeEntity.class));
                        // 编码规则有自动生成序号的，seq才加1
                        ruleSeqService.updateSeqEntity(entity.getRelatedMap(), numberRulesConfigEntity.getId(), false);
                    }
                    // 大批量新增可能会造成内存溢出，这里需要分批处理,每500条数据插入一次
                    int totalSize = batchAddList.size();
                    int batchCount = (totalSize + 499) / 500;
                    for (int i = 0; i < batchCount; i++) {
                        int fromIndex = i * 500;
                        int currentNum = Math.min((i + 1) * 500, totalSize);
                        List<ProductFlowCodeEntity> productFlowCodeEntities = batchAddList.subList(fromIndex, currentNum);
                        List<String> productFlowCodes = productFlowCodeEntities.stream().map(ProductFlowCodeEntity::getProductFlowCode).collect(Collectors.toList());
                        Long count = lambdaQuery().in(ProductFlowCodeEntity::getProductFlowCode, productFlowCodes).count();
                        if (count > 0) {
                            throw new ResponseException(RespCodeEnum.FLOW_CODE_REPEAT);
                        }
                        this.saveBatch(productFlowCodeEntities);
                        codeLogService.addCodeLog(productFlowCodeEntities, OperationType.ADD);
                        processPercent = MathUtil.divideDouble(currentNum, totalSize, 2);
                        build = ImportProgressDTO.builder().progress(processPercent).executionDescription(processPercent.compareTo(1.0) == 0 ? String.format("新增完成：成功%s条；失败%s条", currentNum, totalSize - currentNum) : String.format("正在处理中，已完成%s条；", currentNum))
                                .executionStatus(processPercent.compareTo(1.0) == 0).code(Result.SUCCESS_CODE).build();
                        // 设置生成进度
                        setRedis(redisKey, batchRedisKey, build, isBatch);
                    }
                    // 推送任务中心消息
                    sendTaskInfo(entity);
                } catch (Exception e) {
                    log.error("流水码新增数据错误", e);
                    CommonService commonService = SpringUtil.getBean(CommonService.class);
                    build = commonService.getImportProgressDTO(redisKey, e);
                    // 设置生成进度
                    setRedis(redisKey, batchRedisKey, build, isBatch);
                    recordLog(userName, build.getExecutionDescription());
                    // 手动触发事务回滚
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    break;
                } finally {
                    // 释放锁
                    redisTemplate.delete(redisLock);
                }
            }
        } finally {
            // 释放批量锁
            redisTemplate.delete(batchRedisLock);
        }
    }

    /**
     * 推送任务中心消息
     */
    private void sendTaskInfo(ProductFlowCodeEntity entity) {
        List<Integer> workOrderFlowCodeTypes = Arrays.asList(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode(),
                ProductFlowCodeTypeEnum.FINISHED_PRODUCT_CODE.getCode()
                );
        List<Integer> productOrderFlowCodeTypes = Collections.singletonList(
                ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode()
        );
        if (workOrderFlowCodeTypes.contains(entity.getType())) {
            WorkOrderExtendService workOrderExtendService = SpringUtil.getBean(WorkOrderExtendService.class);
            WorkOrderTaskDTO taskDTO = WorkOrderTaskDTO.builder()
                    .workOrderNumber(entity.getRelationNumber())
                    .flowCodeCreateBy(entity.getCreateBy())
                    .log(entity.getCreateBy() + "添加了流水码")
                    .build();
            workOrderExtendService.pushToTask(true, taskDTO);
        }
        if (productOrderFlowCodeTypes.contains(entity.getType())) {
            ProductOrderTaskDTO taskDTO = ProductOrderTaskDTO.builder()
                    .productOrderNumber(entity.getRelationNumber())
                    .flowCodeCreateBy(entity.getCreateBy())
                    .log(entity.getCreateBy() + "添加了流水码")
                    .build();
            extProductOrderInterface.pushToTask(taskDTO);
        }
    }

    /**
     * 处理Redis操作
     *
     * @param redisKey
     * @param redisLock
     * @param build
     */
    private void processRedis(String redisKey, String redisLock, ImportProgressDTO build) {
        // 重置进度
        redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        // 加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(redisLock, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            build = ImportProgressDTO.builder().executionDescription("当前方法已被占用，稍后再试").executionStatus(false).progress(1.0).code(Result.FAIL_CODE).build();
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            throw new ResponseException("当前方法已被占用，稍后再试");
        }
    }

    /**
     * Redis设置值
     *
     * @param redisKey
     * @param batchRedisKey
     * @param build
     */
    private void setRedis(String redisKey, String batchRedisKey, ImportProgressDTO build, boolean isBatch) {
        redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        if (isBatch) {
            redisTemplate.opsForValue().set(batchRedisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        }
    }


    /**
     * 判断流水码是否存在
     */
    private void judgeIsExistProductFlowCode(String productFlowCode, String redisKey) {
        ProductFlowCodeEntity productFlowCodeEntity = getFlowCodeByCode(productFlowCode);
        if (productFlowCodeEntity != null) {
            ImportProgressDTO build = new ImportProgressDTO();
            build.setExecutionDescription("该流水码已经存在，请重新生成");
            build.setExecutionStatus(false);
            build.setProgress(1.0);
            build.setCode(Result.FAIL_CODE);
            redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
            throw new ResponseException("该流水码已经存在，请重新生成");
        }
    }


    /**
     * 通过流水码获取对象
     *
     * @param flowCode
     * @return
     */
    public ProductFlowCodeEntity getFlowCodeByCode(String flowCode) {
        LambdaQueryWrapper<ProductFlowCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductFlowCodeEntity::getProductFlowCode, flowCode)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    /**
     * 批量新增生产流水码
     */
    @Override
    public void batchAddProductFlowCode(List<ProductFlowCodeEntity> flowCodeList, String relationNumber) {
        if (CollectionUtils.isEmpty(flowCodeList)) {
            return;
        }
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(relationNumber);
        if (Objects.isNull(workOrder)) {
            log.error("生产工单不存在, {}", relationNumber);
            throw new ResponseException("生产工单不存在");
        }
        // 批量新增流水码
        for (ProductFlowCodeEntity productFlowCode : flowCodeList) {
            productFlowCode.setType(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode());
            productFlowCode.setRelationType(ProductFlowCodeTypeEnum.getRelationTypeByCode(productFlowCode.getType()));
            productFlowCode.setRelationNumber(relationNumber);
            productFlowCode.setMaterialCode(workOrder.getMaterialCode());
            MaterialEntity materialEntity=materialService.getSimpleMaterialByCode(workOrder.getMaterialCode());
            productFlowCode.setMaterialType(materialEntity==null?null:materialEntity.getType());
            productFlowCode.setSkuId(workOrder.getSkuId());
        }
        this.batchAddCommonProductFlowCode(flowCodeList);
    }

    /**
     * 批量新增流水码
     */
    @Override
    public void batchAddCommonProductFlowCode(List<ProductFlowCodeEntity> flowCodeList) {
        if (CollectionUtils.isEmpty(flowCodeList)) {
            return;
        }
        String relationNumber = flowCodeList.get(0).getRelationNumber();
        final String lockKey = "DFS:batchAddProductFlowCode:" + relationNumber;
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(lockKey, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            log.info("获取redis同步锁失败,正在导入数据");
            throw new ResponseException("获取redis同步锁失败,正在导入数据");
        }
        try {
            String materialCode = flowCodeList.get(0).getMaterialCode();
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
            if (Objects.isNull(materialEntity)) {
                log.error("物料不存在, {}", materialCode);
                throw new ResponseException("物料不存在");
            }
            //判断物料是否支持流水码管理
            if (Boolean.FALSE.equals(materialEntity.getIsSupportCodeManage())) {
                log.error("该单据下的物料不支持序列号管理, {}", materialCode);
                throw new ResponseException("该单据下的物料不支持序列号管理");
            }
            Date nowDate = new Date();
            // 批量新增流水码
            for (ProductFlowCodeEntity productFlowCode : flowCodeList) {
                //状态(默认为 未打印)
                productFlowCode.setState(ProductFlowCodeStateEnum.NO_PRINT.getCode());
                productFlowCode.setMaterialName(materialEntity.getName());
                productFlowCode.setCreateTime(nowDate);
                productFlowCode.setRelationType(ProductFlowCodeTypeEnum.getRelationTypeByCode(productFlowCode.getType()));
                productFlowCode.setMaterialType(materialEntity==null?null:materialEntity.getType());
            }
            log.info("保存的流水码：{}", JSON.toJSONString(flowCodeList));
            this.saveBatch(flowCodeList);
            //删除流水码重复的数据、保留id最小的数据
            this.getBaseMapper().deleteDuplicateProductFlowCodeData(relationNumber);
        } finally {
            redisTemplate.delete(lockKey);
        }
    }

    /**
     * 新增生产流水码
     */
    @Override
    public void addProductFlowCode(ProductFlowCodeEntity entity) {
        final String lockKey = "DFS:addProductFlowCode:" + entity.getProductFlowCode();
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(lockKey, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            log.info("获取redis同步锁失败，稍后再试");
            throw new ResponseException("获取redis同步锁失败，稍后再试");
        }
        try {
            Long count = this.lambdaQuery().eq(ProductFlowCodeEntity::getProductFlowCode, entity.getProductFlowCode()).count();
            if (count > 0) {
                log.error("流水码已存在, {}", entity.getProductFlowCode());
                return;
            }
            String materialCode = entity.getMaterialCode();
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
            if (Objects.isNull(materialEntity)) {
                log.error("生产工单物料不存在, {}", materialCode);
                throw new ResponseException("生产工单物料不存在");
            }
            //判断当前工单下的物料是否支持 流水码管理
            if (Boolean.FALSE.equals(materialEntity.getIsSupportCodeManage())) {
                log.error("该工单下的物料不支持序列号管理, {}", materialCode);
                throw new ResponseException("该工单下的物料不支持序列号管理");
            }
            //状态(默认为 未打印)
            entity.setState(ProductFlowCodeStateEnum.NO_PRINT.getCode());
            entity.setMaterialName(materialEntity.getName());
            entity.setCreateTime(new Date());
            entity.setRelationType(ProductFlowCodeTypeEnum.getRelationTypeByCode(entity.getType()));
            entity.setMaterialType(materialEntity==null?null:materialEntity.getType());
            this.save(entity);
        } finally {
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    public CheckDeleteDTO checkRemoveProductFlowCode(Integer id) {
        CheckDeleteDTO checkDeleteDTO =CheckDeleteDTO.builder().isOk(true).build();
        ProductFlowCodeEntity flowCodeEntity = this.getById(id);
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getProductFlowCode, flowCodeEntity.getProductFlowCode());
        Long count = productFlowCodeRecordMapper.selectCount(lambdaQueryWrapper);
        if (count > 0) {
            checkDeleteDTO.setIsOk(false);
            checkDeleteDTO.setMessage("该条码已有扫码记录，是否删除");
        }
        if (ProductFlowCodeStateEnum.PRINT.getCode() == flowCodeEntity.getState()) {
            checkDeleteDTO.setIsOk(false);
            checkDeleteDTO.setMessage("该条码已打印，是否删除");
        }
        return checkDeleteDTO;
    }


    @Override
    public void removeProductFlowCode(Integer id) {
        ProductFlowCodeEntity flowCodeEntity = this.getById(id);
        this.removeById(id);
        //删除过站记录
        productFlowCodeRecordService.lambdaUpdate().eq(ProductFlowCodeRecordEntity::getProductFlowCode,flowCodeEntity.getProductFlowCode())
                .remove();
        redisTemplate.delete(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + flowCodeEntity.getProductFlowCode());
    }

    @Override
    public void exportExcel(ProductFlowCodeSelectDTO productFlowCodeSelectDTO, HttpServletResponse response) throws IOException {
        Page<ProductFlowCodeEntity> page = this.listAndShowRecords(productFlowCodeSelectDTO);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            batchShowStateAndName(page.getRecords());
            List<ProductFlowCodeImportDTO> dtos = JSONArray.parseArray(JSONObject.toJSONString(page.getRecords()), ProductFlowCodeImportDTO.class);
            EasyExcelUtil.export(response, null, null, dtos, ProductFlowCodeImportDTO.class);
        }
    }


    private void showStateAndName(ProductFlowCodeEntity productionCodeEntity) {
        //创建人
        productionCodeEntity.setCreateByNickName(Optional.ofNullable(sysUserService.getNicknameByUsername(productionCodeEntity.getCreateBy())).orElse(productionCodeEntity.getCreateBy()));
        //状态
        productionCodeEntity.setStateName(ProductFlowCodeStateEnum.getNameByCode(productionCodeEntity.getState()));
        MaterialEntity materialEntity = materialService.getEntityByCodeAndSkuId(productionCodeEntity.getMaterialCode(), productionCodeEntity.getSkuId());
        if (materialEntity != null) {
            productionCodeEntity.setMaterialName(materialEntity.getName());
            //物料类型名称
            productionCodeEntity.setMaterialTypeName(materialEntity.getTypeName());
        }
        productionCodeEntity.setMaterialFields(materialEntity);
        productionCodeEntity.setTypeName(ProductFlowCodeTypeEnum.getNameByCode(productionCodeEntity.getType()));

        // 设置物料字段
        productionCodeEntity.setMaterialFields(materialService.getMaterialEntityByCode(productionCodeEntity.getMaterialCode()));
        //维修状态和质检状态
        productionCodeEntity.setMaintainStateName(ProductFlowCodeMaintainStateEnum.getNameByCode(productionCodeEntity.getMaintainState()));
        productionCodeEntity.setQualityStateName(ProductFlowCodeQualityStateEnum.getNameByCode(productionCodeEntity.getQualityState()));
        //关联单据类型名称
        productionCodeEntity.setRelationTypeName(CodeRelationTypeEnum.getNameByCode(productionCodeEntity.getRelationType()));
        // 数量
        if (ProductFlowCodeTypeEnum.getBarCodeType().contains(productionCodeEntity.getType())) {
            // 查询批次信息
            BarCodeEntity barCodeEntity = barCodeService.getBarCodeByCode(productionCodeEntity.getProductFlowCode());
            productionCodeEntity.setCount(barCodeEntity.getCount());
        } else {
            productionCodeEntity.setCount(1.0);
        }
    }

    private void batchShowStateAndName(List<ProductFlowCodeEntity> productionCodes) {
        if (CollectionUtils.isEmpty(productionCodes)) {
            return;
        }
        Set<String> createBys=new HashSet<>();
        List<String> supplierCodes = new ArrayList<>();
        List<MaterialCodeAndSkuIdSelectDTO> codeAndSkuIds = new ArrayList<>();
        for(ProductFlowCodeEntity productFlowCodeEntity:productionCodes){
            // 查用户
            createBys.add(productFlowCodeEntity.getCreateBy());
            // 查物料
            MaterialCodeAndSkuIdSelectDTO materialCodeAndSkuIdSelectDTO=MaterialCodeAndSkuIdSelectDTO.builder()
                    .materialCode(productFlowCodeEntity.getMaterialCode())
                    .skuId(productFlowCodeEntity.getSkuId())
                    .build();
            codeAndSkuIds.add(materialCodeAndSkuIdSelectDTO);
            // 查询供应商相关信息
            supplierCodes.add(productFlowCodeEntity.getSupplierCode());
        }

        Map<String, SupplierEntity> supplierMap = new HashMap<>();
        // 查询供应商相关信息
        if (!CollectionUtils.isEmpty(supplierCodes)) {
            Page<SupplierEntity> supplierPage = supplierService.supplierOpenPage(SupplierSelectDTO.builder().fullCodes(supplierCodes).build());
            if (!CollectionUtils.isEmpty(supplierPage.getRecords())) {
                supplierMap = supplierPage.getRecords().stream().collect(Collectors.toMap(SupplierEntity::getCode, o -> o));
            }
        }
        // 查用户
        Map<String, String> userNameNickMap = sysUserService.getUserNameNickMap(createBys);
        // 查物料
        List<MaterialEntity> materials = materialService.listSimpleMaterialByCodesAndSkuIds(codeAndSkuIds);
        Map<String, MaterialEntity> keyMaterialMap = materials.stream().collect(Collectors.toMap(material -> ColumnUtil.getMaterialSku(material.getCode(), material.getSkuEntity()), v -> v));

        for (ProductFlowCodeEntity productFlowCode : productionCodes) {
            productFlowCode.setCreateByNickName(userNameNickMap.get(productFlowCode.getCreateBy()));
            // 设置物料字段
            MaterialEntity materialEntity = keyMaterialMap.get(ColumnUtil.getMaterialSku(productFlowCode.getMaterialCode(), productFlowCode.getSkuId()));
            if (materialEntity != null) {
                productFlowCode.setMaterialName(materialEntity.getName());
                productFlowCode.setMaterialFields(materialEntity);
                //物料类型名称
                productFlowCode.setMaterialTypeName(materialEntity.getTypeName());
            }
            //状态
            productFlowCode.setStateName(ProductFlowCodeStateEnum.getNameByCode(productFlowCode.getState()));
            productFlowCode.setTypeName(ProductFlowCodeTypeEnum.getNameByCode(productFlowCode.getType()));
            //维修状态和质检状态
            productFlowCode.setMaintainStateName(ProductFlowCodeMaintainStateEnum.getNameByCode(productFlowCode.getMaintainState()));
            productFlowCode.setQualityStateName(ProductFlowCodeQualityStateEnum.getNameByCode(productFlowCode.getQualityState()));
            //关联单据类型名称
            productFlowCode.setRelationTypeName(CodeRelationTypeEnum.getNameByCode(productFlowCode.getRelationType()));
            // 设置供应商相关信息
            SupplierEntity supplierEntity = supplierMap.get(productFlowCode.getSupplierCode());
            if (supplierEntity != null) {
                productFlowCode.setSupplierName(supplierEntity.getName());
            }
        }

    }

    /**
     * 查询过站信息
     *
     * @param productionCodeEntity
     * @param craftProcedureEntities
     */
    private void setRecords(ProductFlowCodeEntity productionCodeEntity, List<CraftProcedureEntity> craftProcedureEntities) {
        //过站记录
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> recordQueryWrapper = new LambdaQueryWrapper<>();
        recordQueryWrapper.eq(ProductFlowCodeRecordEntity::getProductFlowCode, productionCodeEntity.getProductFlowCode())
                .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode());
        List<ProductFlowCodeRecordEntity> recordEntityList = productFlowCodeRecordMapper.selectList(recordQueryWrapper);
        Map<Integer, List<ProductFlowCodeRecordEntity>> collect = recordEntityList.stream().filter(o -> o.getProdureId() != null).collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getProdureId));
        List<ProcedureRecordVO> procedureRecordVOList = new ArrayList<>();
        productionCodeEntity.setRecordStateName("未开始");
        Boolean isRecordFinish = true;
        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
            ProcedureRecordVO procedureRecordVO = ProcedureRecordVO.builder().procedureName(craftProcedureEntity.getProcedureName()).procedureIsRecord(false).build();
            if (collect.containsKey(craftProcedureEntity.getId())) {
                procedureRecordVO.setProcedureIsRecord(true);
                productionCodeEntity.setRecordStateName("进行中");
            } else {
                isRecordFinish = false;
            }
            procedureRecordVOList.add(procedureRecordVO);
        }
        if (isRecordFinish) {
            productionCodeEntity.setRecordStateName("已完成");
        }
        productionCodeEntity.setRecordVOList(procedureRecordVOList);
        //关联条码
        Set<String> codeSet = productFlowCodeRecordService.getRelevanceCodes(productionCodeEntity.getProductFlowCode());
        codeSet.remove(productionCodeEntity.getProductFlowCode());
        if (!CollectionUtils.isEmpty(codeSet)) {
            productionCodeEntity.setCodeRelevance(codeSet.stream().collect(Collectors.joining(Constant.SEP)));
        }
    }

    private void batchSetRecords(List<ProductFlowCodeEntity> productionCodes, List<CraftProcedureEntity> craftProcedures) {
        if (CollectionUtils.isEmpty(productionCodes)) {
            return;
        }
        if (!CollectionUtils.isEmpty(craftProcedures)) {
            List<String> flowCodes = productionCodes.stream().map(ProductFlowCodeEntity::getProductFlowCode).collect(Collectors.toList());
            List<ProductFlowCodeRecordEntity> allRecords = productFlowCodeRecordService.lambdaQuery()
                    .in(ProductFlowCodeRecordEntity::getProductFlowCode, flowCodes)
                    .eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                    .isNotNull(ProductFlowCodeRecordEntity::getProdureId)
                    .list();
            for (ProductFlowCodeRecordEntity recordEntity : allRecords) {
                // 扩展信息
                recordEntity.setCodeRecordExtendEntity(codeRecordExtendService.getEntity(recordEntity.getId()));
            }
            // 先按照flowCode分组
            Map<String, List<ProductFlowCodeRecordEntity>> flowCodeRecordsMap = allRecords.stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getProductFlowCode));
            for (ProductFlowCodeEntity productionCode : productionCodes) {
                List<ProductFlowCodeRecordEntity> records = flowCodeRecordsMap.getOrDefault(productionCode.getProductFlowCode(), Collections.emptyList());
                // 工序集合
                Map<Integer, List<ProductFlowCodeRecordEntity>> procedureMap = records.stream().collect(Collectors.groupingBy(ProductFlowCodeRecordEntity::getProdureId));
                List<ProcedureRecordVO> procedureRecordVOList = new ArrayList<>();
                productionCode.setRecordStateName("未开始");
                boolean isRecordFinish = true;
                for (CraftProcedureEntity craftProcedureEntity : craftProcedures) {
                    ProcedureRecordVO procedureRecordVO = ProcedureRecordVO.builder()
                            .procedureName(StringUtils.isNotBlank(craftProcedureEntity.getAlias()) ? craftProcedureEntity.getAlias() : craftProcedureEntity.getProcedureName())
                            .procedureIsRecord(false).supplementaryReport(false).build();
                    List<ProductFlowCodeRecordEntity> procedureRecordEntities = procedureMap.get(craftProcedureEntity.getId());
                    if (CollectionUtils.isEmpty(procedureRecordEntities)) {
                        isRecordFinish = false;
                    } else {
                        procedureRecordVO.setProcedureIsRecord(true);
                        productionCode.setRecordStateName("进行中");
                        // 判断是否补报
                        procedureRecordEntities.stream().filter(o -> Boolean.TRUE.equals(o.getCodeRecordExtendEntity().getSupplementaryReport()))
                                .findFirst().ifPresent(o -> procedureRecordVO.setSupplementaryReport(true));
                    }
                    procedureRecordVOList.add(procedureRecordVO);
                }
                if (isRecordFinish) {
                    productionCode.setRecordStateName("已完成");
                }
                productionCode.setRecordVOList(procedureRecordVOList);
            }
        }
        for (ProductFlowCodeEntity productionCode : productionCodes) {
            // 关联条码 - 这里无法批量查了
            Set<String> codeSet = productFlowCodeRecordService.getRelevanceCodes(productionCode.getProductFlowCode());
            codeSet.remove(productionCode.getProductFlowCode());
            if (!CollectionUtils.isEmpty(codeSet)) {
                productionCode.setCodeRelevance(String.join(Constant.SEP, codeSet));
            }
        }
    }

    /**
     * 根据流水号获取流水码
     *
     * @return
     */
    @Override
    public ProductFlowCodeEntity getByProductFlowCode(String productFlowCode) {
        ProductFlowCodeEntity productFlowCodeEntity;
        Object object = redisTemplate.opsForValue().get(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + productFlowCode);
        if (object != null) {
            productFlowCodeEntity = JSONObject.parseObject((String) object, ProductFlowCodeEntity.class);
        } else {
            LambdaQueryWrapper<ProductFlowCodeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductFlowCodeEntity::getProductFlowCode, productFlowCode);
            try {
                productFlowCodeEntity = this.getOne(lambdaQueryWrapper);
            } catch (Exception e) {
                throw new ResponseException(productFlowCode + "流水码重复");
            }
        }
        if (productFlowCodeEntity != null) {
            redisTemplate.opsForValue().set(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + productFlowCode, JSONObject.toJSONString(productFlowCodeEntity), workPropertise.getScannerRecordTime(), TimeUnit.MINUTES);
        }
        return productFlowCodeEntity;
    }


    @Override
    public void updateBatchEntity(ProductFlowCodeSelectDTO productFlowCodeDTO) {
        List<ProductFlowCodeEntity> productFlowCodeEntityList;
        if (CollectionUtils.isEmpty(productFlowCodeDTO.getList())) {
            Page<ProductFlowCodeEntity> page = this.list(productFlowCodeDTO);
            productFlowCodeEntityList = page.getRecords();
            updateState(productFlowCodeEntityList);
        } else {
            updateState(productFlowCodeDTO.getList());
        }
    }

    /**
     * 批量修改状态
     *
     * @param productFlowCodeEntityList
     */
    private void updateState(List<ProductFlowCodeEntity> productFlowCodeEntityList) {
        for (ProductFlowCodeEntity productFlowCodeEntity : productFlowCodeEntityList) {
            LambdaUpdateWrapper<ProductFlowCodeEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ProductFlowCodeEntity::getId, productFlowCodeEntity.getId())
                    .set(ProductFlowCodeEntity::getState, ProductFlowCodeStateEnum.PRINT.getCode());
            this.update(wrapper);
        }
    }

    @Override
    public PrintDTO print(ProductFlowCodeSelectDTO selectDTO) {
        List<PrintDTO.ParamDto> printDTOS = new ArrayList<>();
        // 必填项如果未填，则直接返回，防止前端误传导致CPU飙高、以及未打印的数据显示已打印
        boolean pageParams = Objects.isNull(selectDTO.getCurrentPage()) || Objects.isNull(selectDTO.getSize());
        if (StringUtils.isBlank(selectDTO.getProductFlowCode()) && pageParams) {
            throw new ResponseException(RespCodeEnum.PRINT_IS_REQUIRE);
        }
        List<ProductFlowCodeEntity> productFlowCodeEntityList = getProductFlowCodeEntities(selectDTO);

        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, selectDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        LabelTypeConfigEntity labelTypeConfigEntity = labelTypeConfigService.detail(labelEntity.getCodeType());
        BarCodeAnalysisService barCodeAnalysisService = SpringUtil.getBean(BarCodeAnalysisService.class);
        List<String> relateNumbers = productFlowCodeEntityList.stream().map(ProductFlowCodeEntity::getRelationNumber).distinct().collect(Collectors.toList());
        // 查询标签单据打印信息
        Map<String, PrintInfoDTO> printInfoMap = barCodeAnalysisService.getPrintInfoMap(labelTypeConfigEntity.getRelateType(), relateNumbers);
        // 每次打印，序号初始化为1
        int i = 1;
        // 获取打印模板, 替换打印模板里{ }的数据
        for (ProductFlowCodeEntity productFlowCodeEntity : productFlowCodeEntityList) {
            PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
            List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
            for (PrintDTO.ParamDto.Content content : printElements) {
                String data = content.getOptions().getTitle();
                if (StringUtils.isBlank(data)) {
                    continue;
                }
                // 替换占位符, 替换值为查询对象的某个属性值
                Matcher matcher = PATTERN.matcher(data);
                while (matcher.find()) {
                    String placeholder = matcher.group();
                    // 序号加1
                    if (placeholder.equals(LabelInfoEnum.SERIAL_NO.getPlaceholder())) {
                        data = String.valueOf(i);
                        i++;
                        continue;
                    }
                    CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                            .ruleType(labelEntity.getCodeType())
                            .relateType(labelTypeConfigEntity.getRelateType())
                            .materialCode(productFlowCodeEntity.getMaterialCode())
                            .relateNumber(productFlowCodeEntity.getRelationNumber())
                            .productFlowCode(productFlowCodeEntity.getType().equals(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode()) ? productFlowCodeEntity.getProductFlowCode() : null)
                            .purchaseSingleProductCode(productFlowCodeEntity.getType().equals(ProductFlowCodeTypeEnum.PURCHASE_PRODUCT_CODE.getCode()) ? productFlowCodeEntity.getProductFlowCode() : null)
                            .finishedProductCode(productFlowCodeEntity.getType().equals(ProductFlowCodeTypeEnum.FINISHED_PRODUCT_CODE.getCode()) ? productFlowCodeEntity.getProductFlowCode() : null)
                            .orderProductCode(productFlowCodeEntity.getType().equals(ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode()) ? productFlowCodeEntity.getProductFlowCode() : null)
                            .sliceDigits(content.getOptions().getSliceDigits())
                            .placeholder(placeholder)
                            .printInfoDto(printInfoMap.getOrDefault(productFlowCodeEntity.getRelationNumber(), new PrintInfoDTO()))
                            .build();
                    // 判断该标签是否存在公式，如果存在运算公式，将对数据进行四则运算
                    // 如果公式为空，则直接进行真实值替换占位符的逻辑
                    String placeholderValue = barCodeAnalysisService.replacePlaceholder(content, build);
                    data = data.replace(placeholder, placeholderValue);
                }
                content.getOptions().setTitle(data);
                // 字体大小如果为空,则设置默认值
                if (content.getOptions().getFontSize() == null) {
                    content.getOptions().setFontSize(9.0);
                }
            }
            printDTOS.addAll(printDTO.getPanels());
        }
        PrintDTO printDTO = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTOS).build();
        PrintDTO dto = labelService.getPrintDtoByOpenApi(printDTO);
        if (Objects.nonNull(selectDTO.getIsPreview()) && selectDTO.getIsPreview()) {
            return dto;
        }
        // 将查询到的数据标记为已打印
        List<String> printFlowCodes = productFlowCodeEntityList.stream().map(ProductFlowCodeEntity::getProductFlowCode).collect(Collectors.toList());
        this.lambdaUpdate().in(ProductFlowCodeEntity::getProductFlowCode, printFlowCodes).set(ProductFlowCodeEntity::getIsPrint, true).update();
        // 记录打印的编号
        dto.setPrintCodes(StringUtils.join(printFlowCodes, Constant.SEP));
        return dto;
    }

    /**
     * 获取码列表
     */
    private List<ProductFlowCodeEntity> getProductFlowCodeEntities(ProductFlowCodeSelectDTO productFlowCodeDTO) {
        List<ProductFlowCodeEntity> productFlowCodeEntityList;
        List<String> productFlowCodeList;
        if (StringUtils.isBlank(productFlowCodeDTO.getProductFlowCode())) {
            Page<ProductFlowCodeEntity> page = this.list(productFlowCodeDTO);
            productFlowCodeEntityList = page.getRecords();
        } else {
            String[] productFlows = productFlowCodeDTO.getProductFlowCode().split(Constant.SEP);
            productFlowCodeList = Arrays.asList(productFlows);
            LambdaQueryWrapper<ProductFlowCodeEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(ProductFlowCodeEntity::getProductFlowCode, productFlowCodeList);
            productFlowCodeEntityList = this.list(wrapper);
        }
        if (CollectionUtils.isEmpty(productFlowCodeEntityList)) {
            throw new ResponseException(RespCodeEnum.PRINT_DATA_IS_LOSE);
        }
        return productFlowCodeEntityList;
    }

    /**
     * 自动扫码条码解析
     *
     * @param code 条码号
     * @param type 条码类型
     * @return
     */
    @Override
    public ProductFlowCodeEntity analysisCode(String code, Integer type, String workOrderNumber) {
        ProductFlowCodeEntity productFlowCodeEntity = this.getByProductFlowCode(code);
        WorkOrderEntity workOrderEntity;
        //如果为空需要判断是否客户生成，是的话补全流水码记录
        if (productFlowCodeEntity == null) {
            if (StringUtils.isEmpty(workOrderNumber)) {
                List<DictEntity> dictBeginEntityList = dictService.getListByType(DictTypeEnum.BAR_CODE_SCANNER_BEGIN.getType(), null);
                List<DictEntity> dictEndEntityList = dictService.getListByType(DictTypeEnum.BAR_CODE_SCANNER_END.getType(), null);
                if (!CollectionUtils.isEmpty(dictBeginEntityList) && !CollectionUtils.isEmpty(dictEndEntityList)) {
                    int begin = Integer.parseInt(dictBeginEntityList.get(0).getDes());
                    int end = Integer.parseInt(dictEndEntityList.get(0).getDes());
                    workOrderNumber = code.substring(begin, end);
                }
            }
            workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
            if (workOrderEntity == null) {
                throw new ResponseException("找不到工单信息");
            }
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(workOrderEntity.getMaterialCode());
            if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == type) {
                //如果是订单流水码则关联的为生产订单
                List<ProductOrderEntity> productOrderEntityList = orderWorkOrderService.productOrderListByWorkId(workOrderEntity);
                if (CollectionUtils.isEmpty(productOrderEntityList)) {
                    throw new ResponseException("无对应生产订单");
                }
            }
            productFlowCodeEntity = ProductFlowCodeEntity.builder()
                    .productFlowCode(code)
                    .materialCode(workOrderEntity.getMaterialCode())
                    .materialName(materialEntity.getName())
                    .skuId(workOrderEntity.getSkuId())
                    .relationNumber(workOrderNumber)
                    .type(type)
                    .state(ProductFlowCodeStateEnum.PRINT.getCode())
                    .createTime(new Date()).build();
            this.save(productFlowCodeEntity);
        }
        if (productFlowCodeEntity == null) {
            throw new ResponseException("解析不到" + code + "流水码信息");
        }
        if (!productFlowCodeEntity.getType().equals(type)) {
            throw new ResponseException("采集器配置类型错误");
        }
        return productFlowCodeEntity;
    }

    @Override
    public Boolean checkProductFlowCode(String productFlowCode, String workOrderNumber) {
        LambdaQueryWrapper<ProductFlowCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductFlowCodeEntity::getRelationNumber, workOrderNumber)
                .eq(ProductFlowCodeEntity::getType, ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode())
                .eq(ProductFlowCodeEntity::getProductFlowCode, productFlowCode);
        ProductFlowCodeEntity entity = this.getOne(queryWrapper);
        if (entity == null) {
            //找到订单
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
            List<ProductOrderEntity> productOrderEntityList = orderWorkOrderService.productOrderListByWorkId(workOrderEntity);
            if (CollectionUtils.isEmpty(productOrderEntityList) || productOrderEntityList.size() > 1) {
                throw new ResponseException("该流水码" + productFlowCode + "不是该工单下维护的，请核对后扫描。");
            }
            LambdaQueryWrapper<ProductFlowCodeEntity> queryWrapperTwo = new LambdaQueryWrapper<>();
            queryWrapperTwo.eq(ProductFlowCodeEntity::getRelationNumber, productOrderEntityList.get(0).getProductOrderNumber())
                    .eq(ProductFlowCodeEntity::getType, ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode())
                    .eq(ProductFlowCodeEntity::getProductFlowCode, productFlowCode);
            ProductFlowCodeEntity entityTwo = this.getOne(queryWrapperTwo);
            if (entityTwo == null) {
                throw new ResponseException("该流水码" + productFlowCode + "不是该工单下维护的，请核对后扫描。");
            }
        }
        return true;
    }


    /**
     * 同步单品码
     *
     * @param productFlowCodeEntityList
     */
    @Override
    public void synProductFlowCode(List<ProductFlowCodeEntity> productFlowCodeEntityList,String userName) {
        for (ProductFlowCodeEntity productFlowCodeEntity : productFlowCodeEntityList) {
            Integer relationType=ProductFlowCodeTypeEnum.getRelationTypeByCode(productFlowCodeEntity.getType());
            if(relationType!=null) {
                productFlowCodeEntity.setRelationType(relationType);
            }
            if(productFlowCodeEntity.getState()==null){
                productFlowCodeEntity.setState(ProductFlowCodeStateEnum.NO_PRINT.getCode());
            }
            // 物料code为空默认拿单据物料
            MaterialEntity materialEntity=null;
            if(StringUtils.isBlank(productFlowCodeEntity.getMaterialCode())){
                if(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getRelationType().equals(productFlowCodeEntity.getRelationType())){
                    WorkOrderEntity workOrderEntity =workOrderService.getSimpleWorkOrderByNumber(productFlowCodeEntity.getRelationNumber());
                    materialEntity=materialService.getSimpleMaterialByCode(workOrderEntity==null?null:workOrderEntity.getMaterialCode());
                }
            }else {
                materialEntity = materialService.getSimpleMaterialByCode(productFlowCodeEntity.getMaterialCode());
            }
            productFlowCodeEntity.setMaterialType(materialEntity==null?null:materialEntity.getType());
            productFlowCodeEntity.setMaterialCode(materialEntity==null?null:materialEntity.getCode());
            productFlowCodeEntity.setMaterialName(materialEntity==null?null:materialEntity.getName());
            //创建时间
            if(productFlowCodeEntity.getCreateTime()==null){
                productFlowCodeEntity.setCreateTime(new Date());
            }
            //创建人员
            if(StringUtils.isBlank(productFlowCodeEntity.getCreateBy())){
                productFlowCodeEntity.setCreateBy(userName);
            }
            LambdaQueryWrapper<ProductFlowCodeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductFlowCodeEntity::getProductFlowCode, productFlowCodeEntity.getProductFlowCode())
                    .last("limit 1");
            ProductFlowCodeEntity productFlowCodeEntityTemp = this.getOne(lambdaQueryWrapper);
            if (productFlowCodeEntityTemp ==null) {
                this.save(productFlowCodeEntity);
            }
        }
    }

    @Override
    public WorkOrderEntity getNewestFlowCode(Integer fid, String workOrderNumber) {
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
        if (facilitiesEntity == null) {
            return null;
        }
        Integer productionLineId = facilitiesEntity.getProductionLineId();
        WorkOrderEntity workOrderEntity;
        if (StringUtils.isNotBlank(workOrderNumber)) {
            workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        } else {
            LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                    .eq(WorkOrderEntity::getLineId, productionLineId)
                    .orderByDesc(WorkOrderEntity::getStateChangeTime)
                    .last("limit 1");
            workOrderEntity = workOrderService.getOne(queryWrapper);
        }
        if (workOrderEntity == null) {
            return null;
        }
        //根据工位id查询对应的工位类型名称
        ModelEntity modelEntity = modelService.getById(facilitiesEntity.getModelId());
        if (modelEntity == null) {
            throw new ResponseException("未找到该工位模型信息");
        }
        String name = modelEntity.getName();
        //根据工单查询工艺
        Integer craftId = workOrderEntity.getCraftId();
        // 设置工序名称 工位类型结合工艺信息找到工序，可能会找到多个工序，则弹窗让其选择其中某个工序
        if (craftId != null) {
            LambdaQueryWrapper<CraftProcedureEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CraftProcedureEntity::getCraftId, craftId);
            List<CraftProcedureEntity> craftProcedureEntityList = craftProcedureMapper.selectList(wrapper);
            List<CraftProcedureEntity> collect = craftProcedureEntityList.stream().filter(craftProcedureEntity -> craftProcedureEntity.getFacType().equals(name)).collect(Collectors.toList());
            workOrderEntity.setCraftProcedureEntities(collect);
        }
        workOrderEntity.setMaterialFields(materialService.getEntityByCodeAndSkuId(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId()));
        return workOrderEntity;
    }


    /**
     * 增加条码关系
     *
     * @param codeRelevanceEntity
     */
    @Override
    public void relevanceCode(CodeRelevanceEntity codeRelevanceEntity) {
        LambdaQueryWrapper<CodeRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CodeRelevanceEntity::getCode, codeRelevanceEntity.getCode())
                .eq(CodeRelevanceEntity::getRelevanceCode, codeRelevanceEntity.getRelevanceCode());
        List<CodeRelevanceEntity> list = codeRelevanceService.list(lambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return;
        }
        codeRelevanceService.save(codeRelevanceEntity);
        //去除缓存
        Set<String> relevanceCodes = productFlowCodeRecordService.getRelevanceCodes(codeRelevanceEntity.getCode());
        relevanceCodes.addAll(productFlowCodeRecordService.getRelevanceCodes(codeRelevanceEntity.getRelevanceCode()));
        for (String code : relevanceCodes) {
            redisTemplate.delete(RedisKeyPrefix.SCANNER_RELEVANCE_CODES + code);
        }
    }



    /**
     * 删除条码关系
     *
     * @param codeRelevanceEntity
     */
    @Override
    public void removeRelevance(CodeRelevanceEntity codeRelevanceEntity) {
        codeRelevanceService.lambdaUpdate().eq(CodeRelevanceEntity::getCode, codeRelevanceEntity.getCode())
                .eq(CodeRelevanceEntity::getRelevanceCode, codeRelevanceEntity.getRelevanceCode())
                .remove();
    }

    /**
     * 删除条码关系
     *
     * @param codeRelevanceEntity
     */
    @Override
    public void removeAllRelevance(CodeRelevanceEntity codeRelevanceEntity) {
        codeRelevanceService.lambdaUpdate().eq(CodeRelevanceEntity::getCode, codeRelevanceEntity.getCode()).remove();
    }


    @Override
    public String getRelationByFlowCode(String flowCode) {
        LambdaQueryWrapper<ProductFlowCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductFlowCodeEntity::getProductFlowCode, flowCode).last("limit 1");
        ProductFlowCodeEntity productFlowCodeEntity = this.getOne(queryWrapper);
        if (productFlowCodeEntity != null) {
            return productFlowCodeEntity.getRelationNumber();
        }
        return null;
    }

    @Override
    public ProductFlowJudgeDTO JudgeFlowCode(String flowCode, Integer workCenterId, Integer lineId, Integer teamId, Integer deviceId) throws ParseException {
        LambdaQueryWrapper<ProductFlowCodeEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductFlowCodeEntity::getProductFlowCode, flowCode).last("limit 1");
        ProductFlowCodeEntity productFlowCodeEntity = this.getOne(queryWrapper);
        if (productFlowCodeEntity != null) {
            String relationNumber = productFlowCodeEntity.getRelationNumber();
            if (StringUtils.isNotBlank(relationNumber)) {
                Page<WorkOrderEntity> workOrderEntityPage = workOrderService.listWorkOrderByLineId(lineId, teamId, deviceId,
                        workCenterId, null, relationNumber, null, null, null, 1, 20, null, null, null);
                List<WorkOrderEntity> records = workOrderEntityPage.getRecords();
                if (!CollectionUtils.isEmpty(records)) {
                    WorkOrderEntity entity = workOrderEntityPage.getRecords().get(0);
                    if (entity != null) {
                        return ProductFlowJudgeDTO.builder().isFlowCode(true).workOrder(entity.getWorkOrderNumber()).build();
                    }
                }
            }
            return ProductFlowJudgeDTO.builder().isFlowCode(true).workOrder(null).build();
        }
        return ProductFlowJudgeDTO.builder().isFlowCode(false).workOrder(null).build();
    }

    /**
     * 刷新条码投入产出时间
     *
     * @param productFlowCodeEntity
     * @param time
     */
    @Override
    @Async
    public void refreshReportTime(ProductFlowCodeEntity productFlowCodeEntity, Date time) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.REFRESH_REPORT_TIME + productFlowCodeEntity.getProductFlowCode(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        try {
            if (productFlowCodeEntity != null) {
                productFlowCodeEntity.setEndTime(time);
                if (productFlowCodeEntity.getStartTime() == null) {
                    productFlowCodeEntity.setStartTime(time);
                }
                this.updateById(productFlowCodeEntity);
            }
        } finally {
            redisTemplate.delete(RedisKeyPrefix.REFRESH_REPORT_TIME + productFlowCodeEntity.getProductFlowCode());
        }
    }

    /**
     * 根据条码已经工位号找到对应工单
     *
     * @param productFlowCode
     * @param fid
     * @return
     */
    @Override
    public WorkOrderEntity getByProductFlowCodeAndFid(String productFlowCode, Integer fid) {
        //找到对应工艺
        ProductFlowCodeEntity productFlowCodeEntity = this.getByProductFlowCode(productFlowCode);
        if (productFlowCodeEntity == null) {
            return null;
        }
        //如果是订单流水码需要根据订单号查询到工单
        if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == productFlowCodeEntity.getType()) {
            return this.getWorkOderByOrderNumberAndFid(productFlowCodeEntity.getRelationNumber(), fid);
        }
        return workOrderService.getSimpleWorkOrderByNumber(productFlowCodeEntity.getRelationNumber());
    }

    /**
     * 根据流水码和生产基本单元查询工单
     * @param productFlowCode 条码号
     * @param productBasicUnitId 生产基本单元
     * @return
     */
    @Override
    public  WorkOrderEntity getWorkOrderByCodeAndProductionBasicUnit(String productFlowCode, Integer productBasicUnitId){
        ProductFlowCodeEntity productFlowCodeEntity = this.getByProductFlowCode(productFlowCode);
        if (productFlowCodeEntity == null) {
            throw new ResponseException("流水码" + productFlowCode + "无法解析");
        }
        //工单流水直接查询工单
        if (ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode() == productFlowCodeEntity.getType()) {
            return workOrderService.getSimpleWorkOrderByNumber(productFlowCodeEntity.getRelationNumber());
        }
        //如果是订单流水码需要根据订单号和生产基本单元查询到工单,优先取投产工单
        if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == productFlowCodeEntity.getType()) {
            List<WorkOrderEntity> workOrderEntityList = workOrderService.lambdaQuery()
                    .eq(WorkOrderEntity::getProductOrderNumber,productFlowCodeEntity.getRelationNumber())
                    .eq(WorkOrderEntity::getProductionBasicUnitId,productBasicUnitId).list();
            if(CollectionUtils.isEmpty(workOrderEntityList)){
                throw new ResponseException("无对应生产工单");
            }
            if(workOrderEntityList.size()==1){
                return workOrderEntityList.get(0);
            }
            //删选出投产工单
            List<WorkOrderEntity> workOrderEntityListInvestment = workOrderEntityList.stream().filter(workOrderEntity -> workOrderEntity.getState()==WorkOrderStateEnum.INVESTMENT.getCode()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(workOrderEntityListInvestment)||workOrderEntityListInvestment.size()>1){
                throw new ResponseException("存在多个符合条件生产工单");
            }
            return workOrderEntityListInvestment.get(0);
        }
        throw new ResponseException("流水码" + productFlowCode + "查找不到对应工单");
    }
    /**
     * 根据订单好以及工位号找到对应工单
     *
     * @param productOrderNumber
     * @param fid
     * @return
     */
    @Override
    public WorkOrderEntity getWorkOderByOrderNumberAndFid(String productOrderNumber, Integer fid) {
        FacilitiesEntity facilitiesEntity = facilitiesMapper.selectById(fid);
        if (facilitiesEntity == null) {
            throw new ResponseException("无对应工位");
        }
        WorkOrderEntity workOrderEntity = orderWorkOrderService.getWorkOrderByOrderIdAndLineId(productOrderNumber, facilitiesEntity.getProductionLineId(),
                OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode(), null, null,fid);
        return workOrderEntity;
    }

    @Override
    public List<WorkOrderEntity> getWorkOrderByProductFlowCode(String productFlowCode) {
        //找到对应工艺
        ProductFlowCodeEntity productFlowCodeEntity = this.getByProductFlowCode(productFlowCode);
        if (productFlowCodeEntity == null) {
            return Collections.emptyList();
        }
        //如果是订单流水码需要根据订单号查询到工单
        if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == productFlowCodeEntity.getType()) {
            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(productFlowCodeEntity.getRelationNumber()).build());
            if (orderEntity == null) {
                throw new ResponseException("条码对应生产订单不存在");
            }
            List<WorkOrderEntity> workOrderEntities = orderWorkOrderService.listWorkOrdersByIdAndType(orderEntity.getProductOrderId(), OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
            if (CollectionUtils.isEmpty(workOrderEntities)) {
                return Collections.emptyList();
            } else {
                return workOrderEntities;
            }
        }
        return Collections.singletonList(workOrderService.getSimpleWorkOrderByNumber(productFlowCodeEntity.getRelationNumber()));
    }

    @Override
    public List<BatchGenerateCodeDTO> generateCount(BatchGenerateCodeReqDTO dto) {
        if (CollectionUtils.isEmpty(dto.getOrderNumbers())) {
            return new ArrayList<>();
        }
        return this.baseMapper.generateCount(dto);
    }

    @Override
    public CheckReportBackDTO checkNum(CheckReportDTO checkReportDTO) {
        FullPathCodeDTO dto;
        if (ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode() == checkReportDTO.getType()) {
            dto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.PRODUCT_FLOW_CODE_VERIFICATION_QUANTITY_CONFIG).build();
        } else if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == checkReportDTO.getType()) {
            dto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.ORDER_PRODUCT_CODE_VERIFICATION_QUANTITY_CONFIG).build();
        } else {
            throw new ResponseException("该流水码类型暂不支持数量校验");
        }
        // 查询流水码数量校验配置
        ProductFlowCodeConfigDTO config = businessConfigService.getValueDto(dto, ProductFlowCodeConfigDTO.class);
        CheckReportBackDTO checkReportBackDTO = CheckReportBackDTO.builder().isLimit(false).isTips(false).build();
        if (Constants.NULL_STR.equals(config.getVerCompletedEnable())) {
            return checkReportBackDTO;
        }
        String orderNumber = checkReportDTO.getOrderNumber();
        Double planQuantity = 0.0;
        // 查询单据的计划数
        if (ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode() == checkReportDTO.getType()) {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(orderNumber);
            if (workOrderEntity == null) {
                throw new ResponseException("找不到" + orderNumber + "对应工单信息");
            }
            planQuantity = workOrderEntity.getPlanQuantity();
        } else if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == checkReportDTO.getType()) {
            ProductOrderDetailDTO orderDetailDTO = ProductOrderDetailDTO.builder().productOrderNumber(orderNumber).build();
            ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(orderDetailDTO);
            if (productOrderEntity == null) {
                throw new ResponseException("找不到" + orderNumber + "对应订单信息");
            }
            planQuantity = productOrderEntity.getProductOrderMaterial().getPlanQuantity();
        }
        // 查询单据已生成码的数量
        BatchGenerateCodeReqDTO reqDTO = BatchGenerateCodeReqDTO.builder().orderNumbers(Stream.of(orderNumber).collect(Collectors.toList()))
                .type(checkReportDTO.getType()).build();
        List<BatchGenerateCodeDTO> generateCounts = this.generateCount(reqDTO);
        Double totalCount = checkReportDTO.getFinishCount();
        if (!CollectionUtils.isEmpty(generateCounts)) {
            totalCount = totalCount + generateCounts.get(0).getExistQuantity();
        }
        if (MathUtil.compareTo(totalCount, planQuantity) == 1) {
            if (Constants.LIMIT.equals(config.getVerCompletedEnable())) {
                checkReportBackDTO.setIsLimit(true);
                checkReportBackDTO.setLimitMessage(config.getVerCompletedLimit());
                return checkReportBackDTO;
            } else if (Constants.TIPS.equals(config.getVerCompletedEnable())) {
                checkReportBackDTO.setIsTips(true);
                checkReportBackDTO.setTipsMessage(config.getVerCompletedTips());
            }
        }
        return checkReportBackDTO;
    }

    @Override
    public void judgeBeforePrint(ProductFlowCodeSelectDTO productFlowCodeDTO) {
        long count;
        if (StringUtils.isNotBlank(productFlowCodeDTO.getProductFlowCode())) {
            List<String> productFlowCodeList = Arrays.asList(productFlowCodeDTO.getProductFlowCode().split(Constant.SEP));
            count = this.lambdaQuery().in(ProductFlowCodeEntity::getProductFlowCode, productFlowCodeList).eq(ProductFlowCodeEntity::getIsPrint, true).count();
        } else {
            LambdaQueryWrapper<ProductFlowCodeEntity> wrapper = conditionQuery(productFlowCodeDTO);
            // 如果存在已打印的数据，抛出异常
            wrapper.eq(ProductFlowCodeEntity::getIsPrint, true);
            count = this.count(wrapper);
        }
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.FOUND_PRINTED_DATA);
        }
    }

    @Async
    @Override
    public void autoGenerate(List<BatchGenerateCodeDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 判断编码规则是否合法, 如果存在人工输入或者没有自增序列号的，不允许自动创建
        Integer ruleId = list.get(0).getRuleId();
        String userName = list.get(0).getUserName();
        NumberRulesConfigEntity rulesConfigEntity = numberRuleService.getById(ruleId);
        List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(rulesConfigEntity.getPrefixDetail(), RuleDetailDTO.class);
        List<String> ruleCode = ruleDetailDTOs.stream().map(RuleDetailDTO::getCode).collect(Collectors.toList());
        if (ruleCode.contains(RulePrefixEnum.ARTIFICIAL_TYPE.getCode())) {
            recordLog(userName, RespCodeEnum.RULE_HAS_MANUAL_INPUT.getMsgDes());
            throw new ResponseException(RespCodeEnum.RULE_HAS_MANUAL_INPUT);
        }
        if (!ruleCode.contains(RulePrefixEnum.AUTO_INCREMENT_TYPE.getCode())) {
            recordLog(userName, RespCodeEnum.RULE_HAS_NOT_AUTO_INCREMENT.getMsgDes());
            throw new ResponseException(RespCodeEnum.RULE_HAS_NOT_AUTO_INCREMENT);
        }

        // 查询流水码生成数量
        List<String> orderNumbers = list.stream().map(BatchGenerateCodeDTO::getOrderNumber).collect(Collectors.toList());
        Integer type = list.get(0).getType();
        Map<String, Double> map = this.generateCount(BatchGenerateCodeReqDTO.builder().orderNumbers(orderNumbers).type(type).build())
                .stream().collect(Collectors.toMap(BatchGenerateCodeDTO::getOrderNumber, BatchGenerateCodeDTO::getExistQuantity, Double::sum));
        List<ProductFlowCodeEntity> flowCodeEntities = new ArrayList<>();

        for (BatchGenerateCodeDTO dto : list) {
            // 设置关联单据对象
            Map<String, String> relatedMap = new HashMap<>();
            Map<String, String> numberMap = new HashMap<>();
            String key, ruleType;
            FullPathCodeDTO configDto;
            String orderNumber = dto.getOrderNumber();
            if (ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode() == type) {
                key = AutoIncrementConfigureTypeEnum.PRODUCT_ORDER.getCode();
                configDto = FullPathCodeDTO.builder()
                        .fullPathCode(ConfigConstant.ORDER_PRODUCT_CODE_AUTO_CREATE_CONFIG).build();
                ruleType = LabelTypeEnum.PRODUCT_ORDER_FLOW_CODE.getCode();
                numberMap.put("20", orderNumber);
            } else if (ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode() == type) {
                key = AutoIncrementConfigureTypeEnum.WORK_ORDER.getCode();
                relatedMap.put(AutoIncrementConfigureTypeEnum.LINE.getCode(), dto.getLineCode());
                relatedMap.put(AutoIncrementConfigureTypeEnum.LINE_MODEL.getCode(), dto.getLineModelCode());
                configDto = FullPathCodeDTO.builder()
                        .fullPathCode(ConfigConstant.PRODUCT_FLOW_CODE_AUTO_CREATE_CONFIG).build();
                ruleType = LabelTypeEnum.PRODUCT_FLOW_CODE.getCode();
                numberMap.put("3", orderNumber);
                numberMap.put("9", dto.getLineModelCode());
                numberMap.put("10", dto.getLineCode());
            } else {
                recordLog(userName, "暂不支持" + ProductFlowCodeTypeEnum.getNameByCode(type));
                throw new ResponseException("暂不支持" + ProductFlowCodeTypeEnum.getNameByCode(type));
            }
            numberMap.put("6", dto.getMaterialCode());
            relatedMap.put(key, orderNumber);
            // 查询流水码自动创建配置
            ProductFlowCodeConfigDTO config = businessConfigService.getValueDto(configDto, ProductFlowCodeConfigDTO.class);
            List<Integer> materialTypes = config.getMaterialTypes();
            // 校验物料类型
            if (!CollectionUtils.isEmpty(materialTypes)) {
                MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(dto.getMaterialCode());
                if (materialEntity == null) {
                    log.info("找不到物料信息，单据：{}，物料编码：{}", orderNumber, dto.getMaterialCode());
                    continue;
                }
                if (!materialTypes.contains(materialEntity.getType())) {
                    continue;
                }
            }
            // 计算生成数量
            double planQuantity = dto.getPlanQuantity() == null ? 0.0 : dto.getPlanQuantity();
            Double existQuantity = map.getOrDefault(orderNumber, 0.0);
            double quantity = MathUtil.sub(planQuantity, existQuantity);
            // 生成数量小于等于0，跳过
            if (quantity <= 0) {
                continue;
            }
            // 获取标签打印规则
            NumberCodeDTO codeDTO = numberRuleService.getSeqById(RuleSeqDTO.builder().id(ruleId).ruleType(ruleType).relatedMap(relatedMap).numberMap(numberMap).build());

            // 组装实体
            ProductFlowCodeEntity build = ProductFlowCodeEntity.builder().materialCode(dto.getMaterialCode()).skuId(dto.getSkuId()).num((int) Math.round(quantity))
                    .numberRuleId(ruleId).relationNumber(orderNumber).ruleDetail(JSON.toJSONString(codeDTO.getPrefixValueList()))
                    .type(type).relatedMap(relatedMap).orderState(dto.getOrderState()).build();
            flowCodeEntities.add(build);
        }
        if (CollectionUtils.isEmpty(flowCodeEntities)) {
            return;
        }
        // 批量新增单品码
        if (flowCodeEntities.size() == 1) {
            this.batchAddProductionCode(flowCodeEntities, false, list.get(0).getUserName());
        } else {
            this.batchAddProductionCode(flowCodeEntities, true, list.get(0).getUserName());
        }
    }

    @Override
    public void bindBatch(BindBatchDTO bindBatchDTO) {
        // 如果全选绑定，获取该工单下的所有生产流水码
        if (Objects.nonNull(bindBatchDTO.getIsChooseBindAll()) && bindBatchDTO.getIsChooseBindAll()) {
            List<String> productFlowCodes = this.lambdaQuery()
                    .eq(ProductFlowCodeEntity::getType, ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode())
                    .eq(ProductFlowCodeEntity::getRelationNumber, bindBatchDTO.getRelatedNumber())
                    .list().stream()
                    .map(ProductFlowCodeEntity::getProductFlowCode).collect(Collectors.toList());
            this.lambdaUpdate()
                    .in(ProductFlowCodeEntity::getProductFlowCode, productFlowCodes)
                    .set(ProductFlowCodeEntity::getBatch, bindBatchDTO.getBatch())
                    .update();
            return;
        }
        this.lambdaUpdate()
                .in(ProductFlowCodeEntity::getProductFlowCode, bindBatchDTO.getProductFlowCodeList())
                .set(ProductFlowCodeEntity::getBatch, bindBatchDTO.getBatch())
                .update();
    }

    private void recordLog(String username, String logMsg) {
        String nickName = sysUserService.getNicknameByUsername(username);
        OperationLogEntity productOrderLogEntity = OperationLogEntity.builder()
                .module("计划调度")
                .type(OperationType.ADD)
                .des("自动创建流水码错误：" + logMsg)
                .username(username)
                .nickname(nickName)
                .createTime(new Date())
                .build();
        operationLogService.manualInsert(productOrderLogEntity);
    }

    @Override
    public void deleteByCode(String code, String workOrderNumber) {
        this.lambdaUpdate().eq(ProductFlowCodeEntity::getProductFlowCode, code)
                .eq(ProductFlowCodeEntity::getRelationNumber, workOrderNumber).remove();
    }

    @Override
    public IPage<ExcelTask> detailTaskPage(Integer current, Integer size) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_CODE.name());
        return excelService.listPage(excelTask, current, size);
    }

    @Override
    public IPage<ExcelTask> relevanceTaskPage(Integer current, Integer size) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_RELEVANCE_CODE.name());
        return excelService.listPage(excelTask, current, size);
    }


    @Override
    public ExcelTask detailListTaskById(Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_CODE.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        ExcelTask task = records.get(0);
        task.setEstimateCount(task.getTotalCount());
        return task;
    }

    @Override
    public ExcelTask detailRelevanceTaskById(Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.PRODUCT_FLOW_RELEVANCE_CODE.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        ExcelTask task = records.get(0);
        task.setEstimateCount(task.getTotalCount());
        return task;
    }

    @Override
    public List<CodeRelevanceEntity> listRelevance(String finishProductCode) {
        return codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getCode, finishProductCode).list();
    }

    @Override
    public List<String> getRelationNumberList(String relatedType, String relationNumber) {
        return this.baseMapper.getRelationNumberListByRelatedType(relatedType, relationNumber);
    }


    /**
     * 成品信息列表
     *
     * @param codeSelectDTO
     * @return
     */
    @Override
    public List<CodeInformationDTO> listCodeInforMation(BodeSelectDTO codeSelectDTO) {
        LambdaQueryWrapper<ProductFlowCodeEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        WrapperUtil.eq(lambdaQueryWrapper, ProductFlowCodeEntity::getProductFlowCode, codeSelectDTO.getCode());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(codeSelectDTO.getMaterialCodeList()), ProductFlowCodeEntity::getMaterialCode, codeSelectDTO.getMaterialCodeList());
        lambdaQueryWrapper.in(!CollectionUtils.isEmpty(codeSelectDTO.getMaterialNameList()), ProductFlowCodeEntity::getMaterialName, codeSelectDTO.getMaterialNameList());
        WrapperUtil.between(lambdaQueryWrapper, ProductFlowCodeEntity::getCreateTime, codeSelectDTO.getStartTime(), codeSelectDTO.getEndTime());
        List<ProductFlowCodeEntity> productFlowCodeEntityList = this.list(lambdaQueryWrapper);
        if (productFlowCodeEntityList.size() > 1000) {
            throw new ResponseException("数据过大，请增加筛选条件");
        }
        //物料
        Map<String, MaterialEntity> materialEntityMap = new HashMap<>();
        //企业
        CompanyEntity companyEntity = companyService.detail();
        //工单
        Map<String, WorkOrderEntity> workOrderEntityMap = new HashMap<>();
        List<CodeInformationDTO> codeInformationDTOS = new ArrayList<>();
        for (ProductFlowCodeEntity productFlowCodeEntity : productFlowCodeEntityList) {
            //工单
            WorkOrderEntity workOrderEntity = null;
            if (workOrderEntityMap.containsKey(productFlowCodeEntity.getRelationNumber())) {
                workOrderEntity = workOrderEntityMap.get(productFlowCodeEntity.getRelationNumber());
            } else {
                workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(productFlowCodeEntity.getRelationNumber());
                workOrderEntityMap.put(productFlowCodeEntity.getRelationNumber(), workOrderEntity);
            }
            //物料
            MaterialEntity materialEntity = null;
            if (materialEntityMap.containsKey(productFlowCodeEntity.getMaterialCode())) {
                materialEntity = materialEntityMap.get(productFlowCodeEntity.getMaterialCode());
            } else {
                materialEntity = materialService.getMaterialEntityByCode(productFlowCodeEntity.getMaterialCode());
            }
            //上料信息
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> recordLambdaQueryWrapper = new LambdaQueryWrapper<>();
            recordLambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getProductFlowCode, productFlowCodeEntity.getProductFlowCode());
            List<Integer> recordIds = productFlowCodeRecordMapper.selectList(recordLambdaQueryWrapper).stream().map(ProductFlowCodeRecordEntity::getId).collect(Collectors.toList());
            List<FeedRecordEntity> feedRecordEntityList = this.getFeedRecordList(productFlowCodeEntity.getProductFlowCode());
            List<CodeTargetRecordEntity> recordEntityList = this.getCodeTargetRecordList(productFlowCodeEntity.getProductFlowCode());
            CodeInformationDTO codeInformationDTO = CodeInformationDTO.builder()
                    .companyEntity(companyEntity)
                    .productFlowCodeEntity(productFlowCodeEntity)
                    .materialEntity(materialEntity)
                    .feedRecordEntityList(feedRecordEntityList)
                    .workOrderEntity(workOrderEntity)
                    .code(productFlowCodeEntity.getProductFlowCode())
                    .codeTargetRecordEntityList(recordEntityList)
                    .build();
            codeInformationDTOS.add(codeInformationDTO);
        }
        return codeInformationDTOS;
    }

    /**
     * 获取条码的上料记录列表
     *
     * @param code
     * @return
     */
    private List<FeedRecordEntity> getFeedRecordList(String code) {
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> recordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        recordLambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getProductFlowCode, code);
        List<Integer> recordIds = productFlowCodeRecordMapper.selectList(recordLambdaQueryWrapper).stream().map(ProductFlowCodeRecordEntity::getId).collect(Collectors.toList());
        List<FeedRecordEntity> feedRecordEntityList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordIds)) {
            LambdaQueryWrapper<FeedRecordEntity> feedRecordQueryWrapper = new LambdaQueryWrapper<>();
            feedRecordQueryWrapper.in(FeedRecordEntity::getCodeRecordId, recordIds);
            feedRecordEntityList = feedRecordMapper.selectList(feedRecordQueryWrapper);
        }
        return feedRecordEntityList;
    }

    /**
     * 获取条码指标上传记录
     * @param code
     * @return
     */
    private List<CodeTargetRecordEntity>  getCodeTargetRecordList(String code){
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> recordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        recordLambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getProductFlowCode, code);
        List<Integer> recordIds = productFlowCodeRecordMapper.selectList(recordLambdaQueryWrapper).stream().map(ProductFlowCodeRecordEntity::getId).collect(Collectors.toList());
        List<CodeTargetRecordEntity> recordEntityList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(recordIds)) {
            //产品检验信息
            LambdaQueryWrapper<CodeTargetRecordEntity> codeTargetQueryWrapper = new LambdaQueryWrapper<>();
            codeTargetQueryWrapper.in(CodeTargetRecordEntity::getCodeRecordId, recordIds);
            List<CodeTargetRecordEntity> recordEntityListTemp = codeTargetRecordMapper.selectList(codeTargetQueryWrapper);
            for (CodeTargetRecordEntity codeTargetRecordEntity : recordEntityListTemp) {
                ProcedureInspectionConfigEntity procedureInspectionConfigEntity = procedureInspectionConfigService.getProcedureInspectionConfigDetail(codeTargetRecordEntity.getProcedureInspectionConfigId());
                if (procedureInspectionConfigEntity != null) {
                    codeTargetRecordEntity.setProcedureInspectionConfigEntity(procedureInspectionConfigEntity);
                    recordEntityList.add(codeTargetRecordEntity);
                }
            }
        }
       return recordEntityList;
    }
    public Set<String> getFirstRelevaceCodes(Set<String> codes){
        Set<String> set=new HashSet<>();
        for(String code:codes) {
            List<CodeRelevanceEntity> list = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getRelevanceCode, code).list();
            if (CollectionUtils.isEmpty(list)) {
                set.add(code);
            }else{
                Set<String> setTemp=new HashSet<>();
                setTemp.add(code);
                set.addAll(getFirstRelevaceCodes(setTemp));
            }
        }
        return set;
    }

    /**
     *
     * 获取顶部条码
     * @param codes
     * @param resultSet
     * @param totalCode
     * @return
     */
    @Override
    public Set<String> getFirstRelevanceCodes( Set<String> codes, Set<String> resultSet,Set<String> totalCode) {
        for (String code : codes) {
            if (totalCode.contains(code)) {
                resultSet.add(code);
                return resultSet;
            }
            totalCode.add(code);
            List<CodeRelevanceEntity> list = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getRelevanceCode, code).list();
            if (CollectionUtils.isEmpty(list)) {
                resultSet.add(code);
            } else {
                resultSet.addAll(getFirstRelevanceCodes(list.stream().map(CodeRelevanceEntity::getCode).collect(Collectors.toSet()), resultSet, totalCode));
            }
        }
        return resultSet;
    }

    /**
     * 查询条码关联关系树（全查）
     * @param code
     * @return
     */
    @Override
    public List<ProductFlowCodeEntity> getRelevanceCodesTree(String code) {
        Set<String> firstCodes = this.getFirstRelevanceCodes(Collections.singleton(code),new HashSet<>(),new HashSet<>());
        List<ProductFlowCodeEntity> list = this.lambdaQuery().in(ProductFlowCodeEntity::getProductFlowCode,firstCodes).list();
        this.setRelevanceCodesTree(list,new HashSet<>());
        return list;
    }

    /**
     * 设置条码后续关联关系树（上查）
     * @param productFlowCodeEntityList
     */
    public List<ProductFlowCodeEntity> setRelevanceCodesTreeUp(List<ProductFlowCodeEntity> productFlowCodeEntityList,Set<String> totalCodes){
        if(CollectionUtils.isEmpty(productFlowCodeEntityList)){
            return productFlowCodeEntityList;
        }
        List<ProductFlowCodeEntity> productFlowCodeEntityListTmp = new ArrayList<>();
        for(ProductFlowCodeEntity productFlowCodeEntity:productFlowCodeEntityList){
            if(totalCodes.contains(productFlowCodeEntity.getProductFlowCode())){
                continue;
            }
            totalCodes.add(productFlowCodeEntity.getProductFlowCode());
            List<CodeRelevanceEntity> codeRelevanceEntityList = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getRelevanceCode, productFlowCodeEntity.getProductFlowCode())
                    .list();
            if(CollectionUtils.isEmpty(codeRelevanceEntityList)){
                continue;
            }
            List<String> codes = codeRelevanceEntityList.stream().map(CodeRelevanceEntity::getCode).collect(Collectors.toList());
            List<ProductFlowCodeEntity> relevanceCodes = this.lambdaQuery().in(ProductFlowCodeEntity::getProductFlowCode,codes).list();
            if(CollectionUtils.isEmpty(relevanceCodes)){
                continue;
            }
            for(ProductFlowCodeEntity relevanceCode:relevanceCodes){
                relevanceCode.setRelevanceCodes(Collections.singletonList(productFlowCodeEntity));
                productFlowCodeEntityListTmp.add(relevanceCode);
            }
        }
        if(CollectionUtils.isEmpty(productFlowCodeEntityListTmp)){
            return productFlowCodeEntityList;
        }
        return this.setRelevanceCodesTreeUp(productFlowCodeEntityListTmp,totalCodes);
    }
    /**
     * 设置条码后续关联关系树（往后）,溯源（成品->原料）
     * @param productFlowCodeEntityList
     */
    @Override
    public void setRelevanceCodesTree(List<ProductFlowCodeEntity> productFlowCodeEntityList,Set<String> totalCodes){
        this.setRelevanceCodesTree(productFlowCodeEntityList, totalCodes, new ArrayList<>(), TRACE);
    }

    /**
     * 设置条码后续关联关系树
     * @param list
     */
    @Override
    public void setRelevanceCodesTree(List<ProductFlowCodeEntity> list, Set<String> totalCodes, List<ProductFlowCodeEntity> totalList, String selectType) {
        // 获取物料相关字段
        List<MaterialCodeAndSkuIdSelectDTO> codeAndSkuIds = list.stream()
                .map(res -> MaterialCodeAndSkuIdSelectDTO.builder().materialCode(res.getMaterialCode()).skuId(res.getSkuId()).build())
                .collect(Collectors.toList());
        List<MaterialEntity> materials = materialService.listSimpleMaterialByCodesAndSkuIds(codeAndSkuIds);
        Map<String, MaterialEntity> keyMaterialMap = materials.stream().collect(Collectors.toMap(material -> ColumnUtil.getMaterialSku(material.getCode(), material.getSkuEntity()), v -> v));

        for(ProductFlowCodeEntity productFlowCodeEntity : list){
            productFlowCodeEntity.setRelationTypeName(CodeRelationTypeEnum.getNameByCode(productFlowCodeEntity.getRelationType()));
            MaterialEntity materialEntity = keyMaterialMap.get(ColumnUtil.getMaterialSku(productFlowCodeEntity.getMaterialCode(), productFlowCodeEntity.getSkuId()));
            productFlowCodeEntity.setMaterialFields(materialEntity);

            if(totalCodes.contains(productFlowCodeEntity.getProductFlowCode())){
                continue;
            }
            totalCodes.add(productFlowCodeEntity.getProductFlowCode());
            totalList.add(productFlowCodeEntity);
            List<CodeRelevanceEntity> codeRelevanceEntityList;
            List<String> codes;
            // 去向（原料->成品）
            if (DESTINATION.equals(selectType)) {
                codeRelevanceEntityList = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getRelevanceCode,productFlowCodeEntity.getProductFlowCode()).list();
                codes = codeRelevanceEntityList.stream().map(CodeRelevanceEntity::getCode).collect(Collectors.toList());
            } else {
                // 溯源（成品->原料）
                codeRelevanceEntityList = codeRelevanceService.lambdaQuery().eq(CodeRelevanceEntity::getCode,productFlowCodeEntity.getProductFlowCode()).list();
                codes = codeRelevanceEntityList.stream().map(CodeRelevanceEntity::getRelevanceCode).collect(Collectors.toList());
            }
            if(CollectionUtils.isEmpty(codes)){
                continue;
            }
            List<ProductFlowCodeEntity> relevanceCodes = this.lambdaQuery().in(ProductFlowCodeEntity::getProductFlowCode,codes).list();
            this.setRelevanceCodesTree(relevanceCodes, totalCodes, totalList, selectType);
            productFlowCodeEntity.setRelevanceCodes(relevanceCodes);

        }
    }



    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importCodeExcel(String filename, InputStream inputStream, String username) {
        final String lockKey = RedisKeyPrefix.CODE_IMPORT_LOCK, importProgressKey = RedisKeyPrefix.CODE_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            byte[] stream = IoUtils.toByteArray(inputStream);
            DynamicEasyExcelListener readListener = new DynamicEasyExcelListener();
            List<Map<String, String>> dataList = DynamicEasyExcelImportUtils.parseExcelToView(stream, 0, 3, readListener);
            //3、通过配置将数据转换并复制到模板原始数据
            log.info("excel读取的数据:{}", JSON.toJSONString(dataList));
            // 获取表头，第一行为注意事项说明，第二行为表头
            int successCount = 0;
            int failCount = 0;
            Map<String, MaterialEntity> materialEntityMap = new HashMap<>();
            Map<String, WorkOrderEntity> workOrderEntityMap = new HashMap<>();
            Map<String, ProductOrderEntity> productOrderMap = new HashMap<>();
            List<String> failLog = new ArrayList<>();
            for (Map<String, String> map : dataList) {
                ProductFlowCodeEntity productFlowCodeEntity = new ProductFlowCodeEntity();
                productFlowCodeEntity.setProductFlowCode(map.get("code"));
                map.remove("code");
                productFlowCodeEntity.setMaterialCode(map.get("materialCode"));
                map.remove("materialCode");
                productFlowCodeEntity.setMaterialName(map.get("materialName"));
                map.remove("materialName");
                MaterialEntity materialEntity = materialEntityMap.get(productFlowCodeEntity.getMaterialCode());
                if (materialEntity == null) {
                    materialEntity = materialService.getSimpleMaterialByCode(productFlowCodeEntity.getMaterialCode());
                    if (materialEntity == null) {
                        failCount++;
                        failLog.add("条码" + productFlowCodeEntity.getProductFlowCode() + "找不到" + productFlowCodeEntity.getMaterialCode() + "对应物料编码");
                        continue;
                    }
                }
                //判断当前工单下的物料是否支持 流水码管理
                if (!materialEntity.getIsSupportCodeManage()) {
                    failCount++;
                    failLog.add("条码" + productFlowCodeEntity.getProductFlowCode() + "的" + productFlowCodeEntity.getMaterialCode() + "对应物料不支持流水码管理");
                    continue;
                }
                productFlowCodeEntity.setMaterialType(materialEntity.getType());
                String relationType = map.get("relationType");
                map.remove("relationType");
                if (relationType != null) {
                    productFlowCodeEntity.setRelationType(CodeRelationTypeEnum.getCodeByName(relationType));
                }
                productFlowCodeEntity.setRelationNumber(map.get("relationNumber"));
                map.remove("relationNumber");
                if (productFlowCodeEntity.getRelationType() != null) {
                    //生产工单
                    if (CodeRelationTypeEnum.WORK_ORDER.getCode().equals(productFlowCodeEntity.getRelationType())) {
                        productFlowCodeEntity.setType(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode());
                        if (StringUtils.isNotBlank(productFlowCodeEntity.getRelationNumber())) {
                            WorkOrderEntity workOrderEntity = workOrderEntityMap.get(productFlowCodeEntity.getRelationNumber());
                            if (workOrderEntity == null) {
                                workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(productFlowCodeEntity.getRelationNumber());
                                if (workOrderEntity == null) {
                                    failCount++;
                                    failLog.add("标识" + productFlowCodeEntity.getProductFlowCode() + "找不到" + productFlowCodeEntity.getRelationNumber() + "对应工单");
                                    continue;
                                }
                                workOrderEntityMap.put(productFlowCodeEntity.getRelationNumber(), workOrderEntity);
                            }
                        }

                    } else if (CodeRelationTypeEnum.PRODUCT_ORDER.getCode().equals(productFlowCodeEntity.getRelationType())) {
                        productFlowCodeEntity.setType(ProductFlowCodeTypeEnum.ORDER_PRODUCT_CODE.getCode());
                        if (StringUtils.isNotBlank(productFlowCodeEntity.getRelationNumber())) {
                            //生产订单
                            ProductOrderEntity productOrderEntity = productOrderMap.get(productFlowCodeEntity.getRelationNumber());
                            if (productOrderEntity == null) {
                                productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(productFlowCodeEntity.getRelationNumber()).build());
                                if (productOrderEntity == null) {
                                    failCount++;
                                    failLog.add("标识" + productFlowCodeEntity.getProductFlowCode() + "找不到" + productFlowCodeEntity.getRelationNumber() + "对应生产订单");
                                    continue;
                                }
                                productOrderMap.put(productFlowCodeEntity.getRelationNumber(), productOrderEntity);
                            }
                        }
                    } else {
                        failCount++;
                        failLog.add("标识" + productFlowCodeEntity.getProductFlowCode() + "找不到" + relationType + "对应关联单据类型");
                        continue;
                    }
                }
                map.remove("type");
                // 供应商编码
                String supplierCode = map.get("supplierCode");
                map.remove("supplierCode");
                if (StringUtils.isNotBlank(supplierCode)) {
                    SupplierEntity supplierEntity = supplierService.lambdaQuery().eq(SupplierEntity::getCode, supplierCode).one();
                    if (supplierEntity == null) {
                        failCount++;
                        failLog.add("标识" + productFlowCodeEntity.getProductFlowCode() + "找不到" + productFlowCodeEntity.getMaterialCode() + "对应供应商编码");
                        continue;
                    }
                    productFlowCodeEntity.setSupplierCode(supplierCode);
//                    productFlowCodeEntity.setSupplierName(supplierEntity.getName());
                }
                // 采购时间
                productFlowCodeEntity.setPurchaseTime(DateUtil.parseDateTime(map.get("purchaseTime")));
                map.remove("purchaseTime");
                productFlowCodeEntity.setState(ProductFlowCodeStateEnum.NO_PRINT.getCode());
                productFlowCodeEntity.setCreateBy(username);
                productFlowCodeEntity.setCreateTime(new Date());
                ProductFlowCodeEntity productFlowCodeEntityTemp = this.getFlowCodeByCode(productFlowCodeEntity.getProductFlowCode());
                if (productFlowCodeEntityTemp != null) {
                    Long count = productFlowCodeRecordService.lambdaQuery().eq(ProductFlowCodeRecordEntity::getProductFlowCode, productFlowCodeEntity.getProductFlowCode())
                            .count();
                    if (count > 0) {
                        failCount++;
                        failLog.add("标识" + productFlowCodeEntity.getProductFlowCode() + "已存在并已生成过站记录");
                        continue;
                    }
                    productFlowCodeEntity.setId(productFlowCodeEntityTemp.getId());
                    //删除缓存
                    redisTemplate.delete(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + productFlowCodeEntityTemp.getProductFlowCode());
                }
                this.saveOrUpdate(productFlowCodeEntity);
                codeTargetRecordService.addCodeTargetMongodb(productFlowCodeEntity.getProductFlowCode(), map, materialEntity.getType());
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, dataList.size(), successCount, failCount);
            }
            // 组装日志
            String logString = failLog.stream().collect(Collectors.joining("\n"));
            //创建并上传日志
            String url = importDataRecordService.writeLogData(logString, username, filename, ImportTypeEnum.CODE_IMPORT.getType(), successCount, failCount);
            // 完成
            importProgressService.updateProgress(importProgressKey, url, true, dataList.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }


    @Override
    public String getExcelImportProgress() {
        Object o = redisTemplate.opsForValue().get(RedisKeyPrefix.CODE_IMPORT_PROGRESS);
        if (o != null) {
            return o.toString();
        }
        return null;
    }

    /**
     * 单品码导出
     * @param response
     * @param selectDTO
     */
    @Override
    public void export(HttpServletResponse response, ProductFlowCodeSelectDTO selectDTO) throws IOException {
        Page<ProductFlowCodeEntity> page = this.list(selectDTO);
        List<Map<String, Object>> dataSource = new ArrayList<>();
        //获取表头
        List<String> headers = new ArrayList<>();
        headers.add("单品码");
        headers.add("单品类型");
        headers.add("物料编码");
        headers.add("物料名称");
        headers.add("物料分类");
        headers.add("关联单据类型");
        headers.add("关联单据号");
        headers.add("供应商编码");
        headers.add("供应商名称");
        headers.add("采购时间");
        List<MaterialAttributeEntity> materialAttributeEntities = materialAttributeListService.listByMaterialType(selectDTO.getMaterialType());
        List<String> filters = new ArrayList<>();
        filters.addAll(headers);
        headers.addAll(materialAttributeEntities.stream().map(MaterialAttributeEntity::getAttributeName).collect(Collectors.toList()));
        filters.addAll(materialAttributeEntities.stream().map(MaterialAttributeEntity::getAttributeCode).collect(Collectors.toList()));

        Map<String, SupplierEntity> supplierMap = new HashMap<>();
        // 查询供应商相关信息
        List<String> supplierCodes = page.getRecords().stream().map(ProductFlowCodeEntity::getSupplierCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supplierCodes)) {
            Page<SupplierEntity> supplierPage = supplierService.supplierOpenPage(SupplierSelectDTO.builder().fullCodes(supplierCodes).build());
            if (!CollectionUtils.isEmpty(supplierPage.getRecords())) {
                supplierMap = supplierPage.getRecords().stream().collect(Collectors.toMap(SupplierEntity::getCode, o -> o));
            }
        }

        for(ProductFlowCodeEntity productFlowCodeEntity :page.getRecords()){
            Map<String, Object> map = new HashMap<>();
            map.put("单品码",productFlowCodeEntity.getProductFlowCode());
            map.put("单品类型",productFlowCodeEntity.getRelationTypeName());
            map.put("物料编码",productFlowCodeEntity.getMaterialCode());
            map.put("物料名称",productFlowCodeEntity.getMaterialName());
            map.put("物料分类",productFlowCodeEntity.getMaterialTypeName());
            map.put("关联单据类型",productFlowCodeEntity.getRelationTypeName());
            map.put("关联单据号",productFlowCodeEntity.getRelationNumber());
            map.put("供应商编码",productFlowCodeEntity.getSupplierCode());

            // 设置供应商相关信息
            SupplierEntity supplierEntity = supplierMap.get(productFlowCodeEntity.getSupplierCode());
            map.put("供应商名称", Objects.isNull(supplierEntity) ? null : supplierEntity.getName());
            map.put("采购时间",productFlowCodeEntity.getPurchaseTime());
            if(productFlowCodeEntity.getTargetMap()!=null) {
                map.putAll( productFlowCodeEntity.getTargetMap());
            }
            dataSource.add(map);
        }
        //导出文件名
        String fileName = "example.xlsx";
//Excel表格的sheet名称
        String sheetName = "Sheet1";

//创建工作簿
        XSSFWorkbook workbook = new XSSFWorkbook();
//创建表格
        XSSFSheet sheet = workbook.createSheet(sheetName);

//设置表头样式
        XSSFCellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);



//写入表头
        XSSFRow headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.size(); i++) {
            XSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(headers.get(i));
            cell.setCellStyle(headerStyle);
        }

//写入数据
        int rowIndex = 1;
        for (Map<String, Object> row : dataSource) {
            XSSFRow dataRow = sheet.createRow(rowIndex++);
            int columnIndex = 0;
            for (String key : filters) {
                XSSFCell cell = dataRow.createCell(columnIndex++);
                cell.setCellValue(row.get(key)==null?null:row.get(key).toString());
            }
        }

        // 将Excel文件写入输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        byte[] bytes = outputStream.toByteArray();

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        ServletOutputStream out = response.getOutputStream();
        out.write(bytes);
        out.flush();
        out.close();
    }

    /**
     * 导出查询列表
     *
     * @param productFlowCodeSelectDTO
     * @return
     */
    @Override
    public Page<ProductFlowCodeEntity> exportList(ProductFlowCodeSelectDTO productFlowCodeSelectDTO) {
        Page<ProductFlowCodeEntity> page = new Page<>();
        List<ProductFlowCodeEntity> list = productFlowCodeMapper.exportList(productFlowCodeSelectDTO);
        this.batchShowStateAndName(list);
        productFlowCodeSelectDTO.setCurrentMinId(list.stream().mapToInt(ProductFlowCodeEntity::getId).min().orElse(0));
        if(productFlowCodeSelectDTO.getTotal()==null){
            page.setTotal(productFlowCodeMapper.exportListTotal(productFlowCodeSelectDTO).longValue());
        }else{
            page.setTotal(productFlowCodeSelectDTO.getTotal());
        }
        showTargetList(list);
        page.setRecords(list);
        return page;
    }

    /**
     * 从mongdb获取条码的关联属性
     *
     * @param productFlowCodeEntity
     * @return
     */
    @Override
    public Map<String,String> getCodeTargetMap(ProductFlowCodeEntity productFlowCodeEntity){
         //展示关联属性
         ConditionDTO conditionDTO = ConditionDTO.builder()
                 .field("code").operator(OperatorEnum.is.name()).value(productFlowCodeEntity.getProductFlowCode()).build();
         List<ConditionDTO> conditionDTOList = new ArrayList<>();
         conditionDTOList.add(conditionDTO);
         //单品关联属性
         Map<String,Object> map =mongodbService.findOne(conditionDTOList,null,Constant.MATERIAL_TYPE_TARGET+productFlowCodeEntity.getMaterialType());
         if(map!=null){
            return   JSONObject.parseObject(JSON.toJSONString(map.get("target")), Map.class);
         }
         return new HashMap<>();
     }

    /**
     * 从mongdb获取条码的扩展字段属性值
     *
     * @param productFlowCodeList
     * @return
     */
    @Override
    public Map<String,Map<String,String>> getCodeExtendMap(List<String> productFlowCodeList) {
        Map<String, Map<String, String>> targetMap = new HashMap<>();
        if (CollectionUtils.isEmpty(productFlowCodeList)) {
            return targetMap;
        }
        ProductFlowCodeEntity productFlowCodeEntity = this.getByProductFlowCode(productFlowCodeList.get(0));
        if (productFlowCodeEntity == null) {
            return targetMap;
        }
        targetMap = this.listCodeTargetMap(productFlowCodeList, productFlowCodeEntity.getMaterialType());
        // 去除不是扩展字段的数据
        removeNotExtend(targetMap, productFlowCodeEntity.getMaterialType());
        return targetMap;
    }

    /**
     * 去除不是扩展字段的数据
     * @param extendMap
     */
    private void removeNotExtend(Map<String, Map<String, String>> extendMap, Integer materialType){
        if(CollectionUtils.isEmpty(extendMap)){
            return;
        }
        // 查询物料类型关联的条码扩展字段
        Set<String> attributeCodeSet = materialTypeConfigAttributeService.lambdaQuery()
                .eq(MaterialTypeConfigAttributeEntity::getMaterialTypeId, materialType)
                .list()
                .stream().map(MaterialTypeConfigAttributeEntity::getAttributeCode).collect(Collectors.toSet());
        for (Map.Entry<String, Map<String, String>> stringMapEntry : extendMap.entrySet()) {
            Map<String, String> valueMap = stringMapEntry.getValue();
            if (CollectionUtils.isEmpty(valueMap)) {
                continue;
            }
            // 删除该物料类型没有关联的条码扩展字段
            valueMap.entrySet().removeIf(valueEntry -> !attributeCodeSet.contains(valueEntry.getKey()));
        }
    }

    /**
     * 从mongdb获取条码的关联属性
     *
     * @param productFlowCodeList
     * @return
     */
    @Override
    public Map<String,Map<String,String>> listCodeTargetMap(List<String> productFlowCodeList,Integer materialType){
        //展示关联属性
        ConditionDTO conditionDTO = ConditionDTO.builder()
                .field("code").operator(OperatorEnum.in.name()).value(productFlowCodeList).build();
        List<ConditionDTO> conditionDTOList = new ArrayList<>();
        conditionDTOList.add(conditionDTO);
        Map<String,Map<String,String>> codeTargetMap = new HashMap<>();
        //单品关联属性
        Page page =mongodbService.page(conditionDTOList,null,Constant.MATERIAL_TYPE_TARGET+materialType,null,null);
         for( Object map:page.getRecords()){
             CodeTargetSubmitDTO codeTargetSubmitDTO =  JSONObject.parseObject(JSON.toJSONString(map), CodeTargetSubmitDTO.class);
             if(codeTargetSubmitDTO==null){
                 continue;
             }
             codeTargetMap.put(codeTargetSubmitDTO.getCode(),codeTargetSubmitDTO.getTarget());
         }
        return codeTargetMap;
    }


    /**
     * 批量拿到条码关联属性值
     * @param list
     */
    void showTargetList(List<ProductFlowCodeEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, Map<String, String>> materialMap = new HashMap<>();
        Map<String, Map<String, String>> mapMap = listCodeTargetMap(list.stream().map(ProductFlowCodeEntity::getProductFlowCode).collect(Collectors.toList()), list.get(0).getMaterialType());
        Map<String, Map<String, String>> extendMap = mapMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> new HashMap<>(entry.getValue())));
        // 去除不是扩展字段的数据
        removeNotExtend(extendMap, list.get(0).getMaterialType());
        for (ProductFlowCodeEntity productFlowCodeEntity : list) {
            Map<String, String> targetMap = new HashMap<>();
            List<MaterialAttributeListEntity> materialAttributeList;
            if (materialMap.get(productFlowCodeEntity.getMaterialCode()) == null) {
                List<String> materialList = new ArrayList<>();
                materialList.add(productFlowCodeEntity.getMaterialCode());
                materialAttributeList = materialAttributeListService.getList(materialList);
                targetMap.putAll(materialAttributeList.stream().filter(o -> o.getAttributeCode() != null).filter(o -> o.getValue() != null).collect(Collectors.toMap(MaterialAttributeListEntity::getAttributeCode, MaterialAttributeListEntity::getValue)));
                materialMap.put(productFlowCodeEntity.getMaterialCode(), targetMap);
            } else {
                targetMap.putAll(materialMap.get(productFlowCodeEntity.getMaterialCode()));
            }
            if (mapMap.get(productFlowCodeEntity.getProductFlowCode()) != null) {

                targetMap.putAll(mapMap.get(productFlowCodeEntity.getProductFlowCode()));
            }
            productFlowCodeEntity.setTargetMap(targetMap);
            // 设置扩展属性信息
            productFlowCodeEntity.setExtendMap(extendMap.getOrDefault(productFlowCodeEntity.getProductFlowCode(), new HashMap<>()));
        }
    }


    /**
     * 在mangoDb存储条码的关联关系
     *
     * @param code
     * @param type
     * @param relevanceCode
     * @param relevanceType
     */
    void addCodeMongoRelevance(String code,String type,String relevanceCode,String relevanceType) {
        //在mongodb添加关联关系
        ConditionDTO conditionDTO = ConditionDTO.builder()
                .field("target.code").operator(OperatorEnum.is.name()).value(relevanceCode).build();
        List<ConditionDTO> conditionlist = new ArrayList<>();
        conditionlist.add(conditionDTO);

        //单品关联属性
        Page page = mongodbService.page(conditionlist, null, "dfs_code_relevance", null, null);
        List<CodeRelevanceDTO> codeRelevanceDTOList = JSONObject.parseArray(JSON.toJSONString(page.getRecords()), CodeRelevanceDTO.class);
        //查不到记录
        if (CollectionUtils.isEmpty(codeRelevanceDTOList)) {
            CodeTypeDTO codeTypeDTO1 = CodeTypeDTO.builder().code(code).type(type).build();
            CodeTypeDTO codeTypeDTO2 = CodeTypeDTO.builder().code(relevanceCode).type(relevanceType).build();
            List<CodeTypeDTO> codeTypeDTOList = new ArrayList<>();
            codeTypeDTOList.add(codeTypeDTO1);
            codeTypeDTOList.add(codeTypeDTO2);
            CodeRelevanceDTO codeRelevanceDTO = CodeRelevanceDTO.builder().target(codeTypeDTOList).build();
            mongodbService.add(codeRelevanceDTO, "dfs_code_relevance");
        } else {
            for (CodeRelevanceDTO codeRelevanceDTO : codeRelevanceDTOList) {
                List<CodeTypeDTO> codeTypeDTOListAdd = new ArrayList<>();
                Boolean isHave = false;
                for (CodeTypeDTO codeTypeDTO : codeRelevanceDTO.getTarget()) {
                    if (codeTypeDTO == null || codeTypeDTO.getType() == null) {
                        continue;
                    }
                    if (!codeTypeDTO.getType().equals(type)) {
                        codeTypeDTOListAdd.add(codeTypeDTO);
                    } else if (!codeTypeDTO.getCode().equals(code)) {
                        isHave = true;
                    }
                }
                if (isHave) {
                    //已有同类型记录时新增一行
                    codeTypeDTOListAdd.add(CodeTypeDTO.builder().code(code).type(type).build());
                    CodeRelevanceDTO codeRelevanceDTOAdd = CodeRelevanceDTO.builder().target(codeTypeDTOListAdd).build();
                    mongodbService.add(codeRelevanceDTOAdd, "dfs_code_relevance");
                } else {
                    //无同类型记录时直接修改改
                    codeTypeDTOListAdd.add(CodeTypeDTO.builder().code(code).type(type).build());
                    CodeRelevanceDTO codeRelevanceDTOAdd = CodeRelevanceDTO.builder().target(codeTypeDTOListAdd)._id(codeRelevanceDTO.get_id()).build();
                    mongodbService.updateAllById(codeRelevanceDTOAdd, "dfs_code_relevance");
                }
            }
        }
    }


    /**
     * 同步条码属性(无过站记录)
     */
    @Override
    public void sysCodeTarget(List<CodeTargetDTO> codeTargetDTOList){
        if(CollectionUtils.isEmpty(codeTargetDTOList)){
            return;
        }
        for(CodeTargetDTO codeTargetDTO:codeTargetDTOList) {
            ProductFlowCodeEntity productFlowCodeEntity=this.getByProductFlowCode(codeTargetDTO.getCode());
            if(productFlowCodeEntity==null){
                throw new ResponseException("无{"+codeTargetDTO.getCode()+"}对应条码信息");
            }
            CodeTargetRecordEntity codeTargetRecordEntity = CodeTargetRecordEntity.builder()
                    .id(null)
                    .code(codeTargetDTO.getCode())
                    .targetEname(codeTargetDTO.getTargetEname())
                    .targetName(codeTargetDTO.getTargetName())
                    .type(codeTargetDTO.getType())
                    .url(codeTargetDTO.getUrl())
                    .value(codeTargetDTO.getValue())
                    .result(codeTargetDTO.getResult())
                    .reportBy(codeTargetDTO.getReportBy())
                    .reportTime(new Date())
                    .build();
            codeTargetRecordService.save(codeTargetRecordEntity);
            //mongodb插入对应的标识属性
            codeTargetRecordService.addCodeTarget(codeTargetDTO.getCode(),codeTargetRecordEntity.getTargetEname(),codeTargetRecordEntity.getValue(),productFlowCodeEntity.getMaterialType(),productFlowCodeEntity.getMaterialCode(),productFlowCodeEntity.getMaterialName(),codeTargetRecordEntity.getReportBy(),Boolean.FALSE.equals(codeTargetRecordEntity.getResult())?"不合格":"合格",codeTargetDTO.getUrl());

        }
    }


    /**
     * 获取条码信息
     *
     * @param codeSelectDTO
     * @return
     */
    @Override
    public FlowCodeInformationDTO getCodeInformation(CodeSelectDTO codeSelectDTO) {
        ProductFlowCodeEntity productFlowCodeEntity = this.lambdaQuery()
                .eq(ProductFlowCodeEntity::getProductFlowCode, codeSelectDTO.getCode())
                .last("limit 1").one();
        if (productFlowCodeEntity == null) {
            throw new ResponseException("找不到条码信息");
        }
        showStateAndName(productFlowCodeEntity);
        FlowCodeInformationDTO flowCodeInformationDTO = FlowCodeInformationDTO.builder().productFlowCodeEntity(productFlowCodeEntity).build();
        //过站记录
        if (codeSelectDTO.getIsShowRecord() != null && codeSelectDTO.getIsShowRecord()) {
            Page<ProductFlowCodeRecordEntity> codeRecordEntityPage = productFlowCodeRecordService.list(CodeRecordSelectDTO.builder().productFlowCode(productFlowCodeEntity.getProductFlowCode()).build());
            flowCodeInformationDTO.setProductFlowCodeRecordList(codeRecordEntityPage.getRecords());
        }
        //条码关联关系
        if (codeSelectDTO.getIsShowRelevance() != null && codeSelectDTO.getIsShowRelevance()) {
            flowCodeInformationDTO.setRelevanceCodeTree(this.getRelevanceCodesTree(productFlowCodeEntity.getProductFlowCode()));
        }
        //条码属性
        if (codeSelectDTO.getIsShowTarget() != null && codeSelectDTO.getIsShowTarget()) {
            this.showTargetList(Stream.of(productFlowCodeEntity).collect(Collectors.toList()));
            List<MaterialAttributeListEntity> materialAttributeList = materialAttributeListService.listAttributeByMaterial(productFlowCodeEntity.getMaterialCode());
            //加上物料类型的
            for (MaterialAttributeListEntity materialAttributeListEntity : materialAttributeList) {
                if (productFlowCodeEntity.getTargetMap() == null) {
                    continue;
                }
                String value = productFlowCodeEntity.getTargetMap().get(materialAttributeListEntity.getAttributeCode());
                if (StringUtils.isNotBlank(value)) {
                    materialAttributeListEntity.setValue(value);
                }
            }
            flowCodeInformationDTO.setMaterialAttributeList(materialAttributeList);
        }
        //上料记录
        if (codeSelectDTO.getIsShowFeedRecord() != null && codeSelectDTO.getIsShowFeedRecord()) {
            flowCodeInformationDTO.setFeedRecordEntityList(this.getFeedRecordList(productFlowCodeEntity.getProductFlowCode()));
        }
        //指标上传记录
        if (codeSelectDTO.getIsShowTargetRecord() != null && codeSelectDTO.getIsShowTargetRecord()) {
            flowCodeInformationDTO.setCodeTargetRecordEntityList(this.getCodeTargetRecordList(productFlowCodeEntity.getProductFlowCode()));
        }
        //行为日志
        if (codeSelectDTO.getIsShowLog() != null && codeSelectDTO.getIsShowLog()) {
            flowCodeInformationDTO.setCodeLogEntityList(codeLogService.listByCode(productFlowCodeEntity.getProductFlowCode()));
        }
        return flowCodeInformationDTO;
    }

    /**
     * 添加条码属性
     * @param submitList
     */
    @Override
    public void addCodeTarget(List<CodeTargetSubmitDTO> submitList) {
        if(CollectionUtils.isEmpty(submitList)){
            return;
        }
        List<CodeTargetDTO> targets = new ArrayList<>();
        for (CodeTargetSubmitDTO dto : submitList) {
            Map<String, String> targetMap = dto.getTarget();
            if (CollectionUtils.isEmpty(targetMap)) {
                continue;
            }
            for (Map.Entry<String, String> entry : targetMap.entrySet()) {
                targets.add(CodeTargetDTO.builder().code(dto.getCode())
                        .targetEname(entry.getKey()).value(entry.getValue())
                        .build());
            }
        }
        this.sysCodeTarget(targets);
    }

    @Override
    public List<CodeRelevanceViewDTO> codeRelevanceView(CodeRelevanceViewSelectDTO dto) {
        BomService bomService = SpringUtil.getBean(BomService.class);
        ProductFlowCodeEntity entity = this.getByProductFlowCode(dto.getCode());
        if (entity == null) {
            throw new ResponseException("找不到对应条码信息");
        }
        List<ProductFlowCodeEntity> relevanceList = new ArrayList<>();
        // 设置条码后续关联关系树
        this.setRelevanceCodesTree(Stream.of(entity).collect(Collectors.toList()), new HashSet<>(), relevanceList, dto.getSelectType());
        // 去除本身
        entity = relevanceList.remove(0);

        // 去向条码视图、溯源条码视图、去向bom视图
        if ("code".equals(dto.getViewType()) || DESTINATION.equals(dto.getSelectType())) {
            return Stream.of(this.codeView(entity)).collect(Collectors.toList());
        }
        // 溯源bom视图
        String materialCode = entity.getMaterialCode();
        Integer skuId = entity.getSkuId();
        // 根节点
        CodeRelevanceViewDTO viewDTO = this.getViewDTO(entity);
        // 查询bom
        BomEntity bomEntity = bomService.getLatestSimpleBom(materialCode, skuId);
        if (bomEntity == null) {
            // 没有bom，将所有关联条码放在第二层，并标记为不属于物料bom
            List<CodeRelevanceViewDTO> children = new ArrayList<>();
            for (int i = 0; i < relevanceList.size(); i++) {
                CodeRelevanceViewDTO relevanceViewDTO = this.getViewDTO(relevanceList.get(i));
                relevanceViewDTO.setIsBomCode(false);
                children.add(relevanceViewDTO);
            }
            viewDTO.setChildren(children);
            return Stream.of(viewDTO).collect(Collectors.toList());
        }
        // 查询多级bom
        List<BomRawMaterialEntity> multiLevelBom = bomService.getMultiLevelBom(materialCode, skuId, bomEntity.getBomNum(), bomEntity.getProductionState());
        Map<String, List<ProductFlowCodeEntity>> codeEntityMap = relevanceList.stream()
                .collect(Collectors.groupingBy(o -> ColumnUtil.getMaterialSku(o.getMaterialCode(), o.getSkuId())));
        Set<String> isBomCodeSet = new HashSet<>();
        List<CodeRelevanceViewDTO> children = this.bomView(multiLevelBom, codeEntityMap, isBomCodeSet);
        // 过滤出不属于物料bom的条码map，将其放在第二层
        codeEntityMap.entrySet().removeIf(entry -> isBomCodeSet.contains(entry.getKey()));
        codeEntityMap.forEach((key, value) -> children.add(CodeRelevanceViewDTO.builder()
                .code(value.get(0).getProductFlowCode())
                .materialCode(value.get(0).getMaterialCode())
                .skuId(value.get(0).getSkuId())
                .isBomCode(false)
                .codeEntities(value)
                .materialFields(value.get(0).getMaterialFields())
                .build()));
        viewDTO.setChildren(children);
        return Stream.of(viewDTO).collect(Collectors.toList());
    }

    /**
     * 获取条码视图
     * @param entity
     * @return
     */
    private CodeRelevanceViewDTO codeView(ProductFlowCodeEntity entity) {
        // 转换对象
        CodeRelevanceViewDTO viewDTO = this.getViewDTO(entity);
        List<ProductFlowCodeEntity> relevanceCodes = entity.getRelevanceCodes();
        if (CollectionUtils.isEmpty(relevanceCodes)) {
            return viewDTO;
        }
        List<CodeRelevanceViewDTO> children = relevanceCodes.stream().map(this::codeView).collect(Collectors.toList());
        viewDTO.setChildren(children);
        return viewDTO;
    }

    /**
     * 转换对象
     * @param entity
     * @return
     */
    private CodeRelevanceViewDTO getViewDTO(ProductFlowCodeEntity entity) {
        return CodeRelevanceViewDTO.builder()
                .code(entity.getProductFlowCode())
                .materialCode(entity.getMaterialCode())
                .skuId(entity.getSkuId())
                .codeEntities(Stream.of(entity).collect(Collectors.toList()))
                .isBomCode(true)
                .materialFields(entity.getMaterialFields())
                .build();
    }

    /**
     * 获取bom视图
     * @param multiLevelBom
     * @return
     */
    private List<CodeRelevanceViewDTO> bomView(List<BomRawMaterialEntity> multiLevelBom, Map<String, List<ProductFlowCodeEntity>> codeEntityMap, Set<String> isBomCodeSet) {
        List<CodeRelevanceViewDTO> result = new ArrayList<>();
        for (BomRawMaterialEntity bomRawMaterialEntity : multiLevelBom) {
            // 转换对象
            CodeRelevanceViewDTO viewDTO = CodeRelevanceViewDTO.builder()
                    .materialCode(bomRawMaterialEntity.getCode())
                    .skuId(bomRawMaterialEntity.getSkuId())
                    .isBomCode(true)
                    .materialFields(bomRawMaterialEntity.getMaterialFields())
                    .build();
            result.add(viewDTO);
            String materialSku = ColumnUtil.getMaterialSku(viewDTO.getMaterialCode(), viewDTO.getSkuId());
            isBomCodeSet.add(materialSku);
            List<ProductFlowCodeEntity> codeEntities = codeEntityMap.getOrDefault(materialSku, new ArrayList<>());
            if (CollectionUtils.isEmpty(codeEntities)) {
                continue;
            }
            viewDTO.setCode(codeEntities.get(0).getProductFlowCode());
            viewDTO.setCodeEntities(codeEntities);
            List<BomRawMaterialEntity> childrenBomRaw = bomRawMaterialEntity.getChildren();
            if (CollectionUtils.isEmpty(childrenBomRaw)) {
                continue;
            }
            List<CodeRelevanceViewDTO> children = bomView(childrenBomRaw, codeEntityMap, isBomCodeSet);
            viewDTO.setChildren(children);
        }
        return result;
    }

}

