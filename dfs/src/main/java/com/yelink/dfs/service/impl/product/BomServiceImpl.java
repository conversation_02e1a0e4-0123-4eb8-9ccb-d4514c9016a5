package com.yelink.dfs.service.impl.product;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.yelink.dfs.common.CodeFactory;
import com.yelink.dfs.common.CommonDeleteHandler;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.ReportFormConstant;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.constant.model.VersionModelIdEnum;
import com.yelink.dfs.constant.product.BomItemTypeEnum;
import com.yelink.dfs.constant.product.ProductionStateEnum;
import com.yelink.dfs.constant.product.BomRawMaterialEnum;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.constant.product.MaterialReplaceStrategyEnum;
import com.yelink.dfs.constant.product.MaterialSortEnum;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.constant.rule.RulePrefixEnum;
import com.yelink.dfs.controller.product.BomDetailListExportHandler;
import com.yelink.dfs.controller.product.BomSimpleListExportHandler;
import com.yelink.dfs.entity.common.DataDelVO;
import com.yelink.dfs.entity.common.ExcelTemplateSetDTO;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.common.QueryDTO;
import com.yelink.dfs.entity.common.VersionChangeDTO;
import com.yelink.dfs.entity.common.VersionChangeRecordSelectDTO;
import com.yelink.dfs.entity.common.config.dto.BomVersionConfDTO;
import com.yelink.dfs.entity.order.BomTileEntity;
import com.yelink.dfs.entity.order.TakeOutApplicationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ReplaceBomMaterialEntity;
import com.yelink.dfs.entity.product.ReplaceSchemeEntity;
import com.yelink.dfs.entity.product.ReplaceSchemeMaterialEntity;
import com.yelink.dfs.entity.product.dto.AuxiliaryAttrValueSelectDTO;
import com.yelink.dfs.entity.product.dto.BomCopyDTO;
import com.yelink.dfs.entity.product.dto.BomCopyImportExcelDTO;
import com.yelink.dfs.entity.product.dto.BomImpactAnalysisDTO;
import com.yelink.dfs.entity.product.dto.BomImportExcelDTO;
import com.yelink.dfs.entity.product.dto.BomImportExcelLogDTO;
import com.yelink.dfs.entity.product.dto.BomImportExcelOtherDTO;
import com.yelink.dfs.entity.product.dto.BomMaterialImportDTO;
import com.yelink.dfs.entity.product.dto.BomReverseDTO;
import com.yelink.dfs.entity.product.dto.BomSelectByMaterialDetailDTO;
import com.yelink.dfs.entity.product.dto.BomSelectDTO;
import com.yelink.dfs.entity.product.dto.DetailBomExportDTO;
import com.yelink.dfs.entity.product.dto.ImpactAnalysisSelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialRelateDataDTO;
import com.yelink.dfs.entity.product.dto.MaterialSkuEntitySelectDTO;
import com.yelink.dfs.entity.product.dto.ReplaceSchemeSelectDTO;
import com.yelink.dfs.entity.product.dto.SimpleBomExportDTO;
import com.yelink.dfs.entity.product.vo.ReplaceSchemeRelateBomVO;
import com.yelink.dfs.entity.product.vo.ReplaceSchemeVO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.event.MaterialHaveReleasedStateBomEvent;
import com.yelink.dfs.mapper.order.TakeOutApplicationMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.product.BomMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.material.dto.MaterialDetailDTO;
import com.yelink.dfs.open.v1.production.dto.BomMaterialSelectDTO;
import com.yelink.dfs.open.v1.production.dto.BomNumbersDTO;
import com.yelink.dfs.open.v1.production.dto.BomOpenEntityDTO;
import com.yelink.dfs.open.v1.production.dto.BomRawMaterialDeleteDTO;
import com.yelink.dfs.open.v1.production.dto.BomRawMaterialInsertDTO;
import com.yelink.dfs.open.v1.production.dto.BomRawMaterialOpenEntityDTO;
import com.yelink.dfs.open.v1.production.dto.BomRawMaterialUpdateDTO;
import com.yelink.dfs.open.v1.production.dto.BomReplaceMaterialSelectDTO;
import com.yelink.dfs.open.v1.production.dto.BomUpdateInsertDTO;
import com.yelink.dfs.open.v2.bom.dto.BomQueryDTO;
import com.yelink.dfs.open.v2.bom.dto.BomQuerySqlDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DeleteRecordService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.notice.InfoNoticeConfigService;
import com.yelink.dfs.service.order.BomTileService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.product.AuxiliaryAttrValueService;
import com.yelink.dfs.service.product.BomRawMaterialService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrSkuService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ReplaceBomMaterialService;
import com.yelink.dfs.service.product.ReplaceSchemeMaterialService;
import com.yelink.dfs.service.product.ReplaceSchemeService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.common.excel.ImportFailException;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.ModuleEnum;
import com.yelink.dfscommon.constant.dfs.UploadFileCodeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.config.ConfigValueConstant;
import com.yelink.dfscommon.constant.dfs.notice.NoticeTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.OrderStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.product.BomVersionReleaseEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.DataDelSchema;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.BomKeyDTO;
import com.yelink.dfscommon.dto.dfs.BomRawMaterialReturnDTO;
import com.yelink.dfscommon.dto.dfs.BomRouteDTO;
import com.yelink.dfscommon.dto.dfs.GenerateCodeDTO;
import com.yelink.dfscommon.dto.dfs.GenerateRuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuInsertResDTO;
import com.yelink.dfscommon.dto.dfs.NoticeConfigBuilder;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.PushDownMaterialDTO;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.dfs.AuxiliaryAttrValueEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrSkuEntity;
import com.yelink.dfscommon.entity.dfs.RationalDTO;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelDeleteRowUtils;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PageUtil;
import com.yelink.dfscommon.utils.PathUtils;
import com.yelink.dfscommon.utils.Rational;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.BaseFormulaEvaluator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-04-06 19:38
 */
@Service
@Slf4j
public class BomServiceImpl extends ServiceImpl<BomMapper, BomEntity> implements BomService {

    private static final String TABLE_NAME = "ImpactAnalysisFile";
    private static final String SHEET_NAME = "ImpactAnalysisSheet";
    @Resource
    private BomRawMaterialService bomRawMaterialService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private TakeOutApplicationMapper takeOutApplicationMapper;
    @Resource
    private WorkOrderMapper workOrderMapper;
    @Resource
    private MaterialService materialService;
    @Resource
    private BomTileService bomTileService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private ApproveConfigService approveConfigService;
    @Resource
    private CraftService craftService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private ReplaceSchemeService replaceSchemeService;
    @Resource
    private ReplaceSchemeMaterialService replaceSchemeMaterialService;
    @Resource
    private AppendixService appendixService;
    @Resource
    private SkuService skuService;
    @Resource
    private UploadService uploadService;
    @Resource
    private ImportProgressService importProgressService;
    @Resource
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    private ReplaceBomMaterialService replaceBomMaterialService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private StockInventoryDetailInterface stockInventoryDetailInterface;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    private MaterialAuxiliaryAttrSkuService materialAuxiliaryAttrSkuService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonDeleteHandler commonDeleteHandler;
    @Resource
    private CodeFactory codeFactory;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    private InfoNoticeConfigService infoNoticeConfigService;
    @Resource
    private MaterialAuxiliaryAttrSkuService materialSkuService;
    @Resource
    private AuxiliaryAttrValueService auxiliaryAttrValueService;
    @Resource
    private MaterialAuxiliaryAttrService materialAuxiliaryAttrService;
    @Resource
    @Lazy
    private DeleteRecordService deleteRecordService;
    @Resource
    private VersionChangeRecordService versionChangeRecordService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private CommonService commonService;

    /**
     * BOM第15层，用于防止bom嵌套循环
     */
    public static final Integer BOM_LAYER_MAX_NUM = 15;


    @Override
    public Page<BomEntity> list(BomSelectDTO selectDTO) {
        int current = selectDTO.getCurrent() == null ? 1 : selectDTO.getCurrent();
        int size = selectDTO.getSize() == null ? Integer.MAX_VALUE : selectDTO.getSize();
        LambdaQueryWrapper<BomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(StringUtils.isNotBlank(selectDTO.getApprover()), BomEntity::getApprover, selectDTO.getApprover())
                .like(StringUtils.isNotBlank(selectDTO.getCode()), BomEntity::getCode, selectDTO.getCode())
                .eq(StringUtils.isNotBlank(selectDTO.getFullMaterialCode()), BomEntity::getCode, selectDTO.getFullMaterialCode())
                .like(StringUtils.isNotBlank(selectDTO.getBomName()), BomEntity::getBomName, selectDTO.getBomName())
                .like(StringUtils.isNotBlank(selectDTO.getBomNum()), BomEntity::getBomNum, selectDTO.getBomNum())
                .like(StringUtils.isNotBlank(selectDTO.getOriginNum()), BomEntity::getOriginNum, selectDTO.getOriginNum())
                .like(StringUtils.isNotBlank(selectDTO.getVersion()), BomEntity::getVersion, selectDTO.getVersion())
                .eq(StringUtils.isNotBlank(selectDTO.getFullVersion()), BomEntity::getVersion, selectDTO.getFullVersion())
                .eq(selectDTO.getSkuId() != null, BomEntity::getSkuId, selectDTO.getSkuId())
                .between(StringUtils.isNoneEmpty(selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime()), BomEntity::getCreateTime, selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime())
                .between(StringUtils.isNoneEmpty(selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime()), BomEntity::getUpdateTime, selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime())
                .in(CollectionUtils.isNotEmpty(selectDTO.getBomIds()), BomEntity::getId, selectDTO.getBomIds())
                .in(CollectionUtils.isNotEmpty(selectDTO.getBomCodes()), BomEntity::getBomNum, selectDTO.getBomCodes())
        ;
        // 判断是否为最父级的BOM。为是的BOM查询原则：试产的生产状态查询试产的BOM,量产的生产状态查询量产的BOM。
        // 为否的BOM查询原则：试产的生产状态查询试产/量产的BOM,量产的生产状态查询量产的BOM。
        if (StringUtils.isNotBlank(selectDTO.getProductionState())) {
            // 处理生产状态条件，支持逗号分隔的多个状态
            List<String> productionStates = Arrays.stream(selectDTO.getProductionState().split(Constant.SEP))
                    .collect(Collectors.toList());
            boolean productionStateCondition = productionStates.contains(ProductionStateEnum.MASS_PRODUCTION.getCode());
            if (selectDTO.getIsTopBom() || productionStateCondition) {
                // 最父级BOM或包含量产状态时，按传入的生产状态精确查询
                wrapper.in(BomEntity::getProductionState, productionStates);
            }
        }
        //状态
        if (StringUtils.isNotBlank(selectDTO.getState())) {
            List<Integer> states = Arrays.stream(selectDTO.getState().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            wrapper.in(BomEntity::getState, states);
        }
        // 生产工单号
        if (StringUtils.isNotBlank(selectDTO.getWorkOrderNumber())) {
            LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
            workOrderWrapper.eq(WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber());
            WorkOrderEntity workOrderEntity = workOrderMapper.selectOne(workOrderWrapper);
            wrapper.in(BomEntity::getCode, workOrderEntity.getMaterialCode());
        }
        wrapper.orderByDesc(BomEntity::getCreateTime)
                .orderByDesc(BomEntity::getId);

        Boolean isTemplate = selectDTO.getIsTemplate();
        // 兼容旧功能，默认查非模板数据
        if (isTemplate == null) {
            isTemplate = Constants.FALSE;
        }
        wrapper.eq(BomEntity::getIsTemplate, isTemplate);
        //使用物料条件查询
        MaterialSkuEntitySelectDTO skuSelectDTO = MaterialSkuEntitySelectDTO.builder().materialFields(selectDTO.getMaterialFields())
                .materialSkus(selectDTO.getMaterialSkus()).build();
        MaterialSkuDTO skuDTO = materialService.codesAndSkuIdsByMaterialFields(skuSelectDTO);
        if (skuDTO.getUseConditions()) {
            List<String> materialCodes = skuDTO.getMaterialCodes();
            List<Integer> skuIds = skuDTO.getSkuIds();
            if (CollectionUtils.isEmpty(materialCodes) && CollectionUtils.isEmpty(skuIds)) {
                return new Page<>();
            }
            wrapper.in(CollectionUtils.isNotEmpty(materialCodes), BomEntity::getCode, materialCodes);
            wrapper.in(CollectionUtils.isNotEmpty(skuIds), BomEntity::getSkuId, skuIds);
        }
        // bom物料行id查询
        if (CollectionUtils.isNotEmpty(selectDTO.getBomRawMaterialIds())) {
            List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.listByIds(selectDTO.getBomRawMaterialIds());
            List<Integer> bomIds = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getBomId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bomIds)) {
                return new Page<>();
            }
            wrapper.in(BomEntity::getId, bomIds);
        }

        Page<BomEntity> page = this.page(new Page<>(current, size), wrapper);
        List<BomEntity> result = page.getRecords();
        // 获取中文名称
        showName(result);
        // 展示子物料信息
        boolean isShowBomRawMaterial = Objects.nonNull(selectDTO.getIsShowBomRawMaterialInfo()) ? selectDTO.getIsShowBomRawMaterialInfo() : true;
        if (CollectionUtils.isNotEmpty(result) && isShowBomRawMaterial) {
            // 获取bom物料行信息
            List<Integer> bomIds = result.stream().map(BomEntity::getId).collect(Collectors.toList());
            List<BomRawMaterialEntity> materialEntities = bomRawMaterialService.lambdaQuery().in(BomRawMaterialEntity::getBomId, bomIds).list();
            // 填充物料对象
            List<MaterialEntity> materialFields = materialService.listSimpleMaterialByCodesAndSkuIds(materialEntities.stream().map(e ->
                    MaterialCodeAndSkuIdSelectDTO.builder()
                            .materialCode(e.getCode())
                            .skuId(e.getSkuId())
                            .build()
            ).collect(Collectors.toList()));
            Map<String, MaterialEntity> materialSkuMap = materialFields.stream()
                    .collect(Collectors.toMap(e -> ColumnUtil.getMaterialSku(e.getCode(), e.getSkuEntity()), Function.identity(), (v1, v2) -> v2));
            // 查询bom物料行对应的替代方案
            Map<Integer, List<ReplaceBomMaterialEntity>> bomReplaceMap = replaceBomMaterialService.lambdaQuery().in(ReplaceBomMaterialEntity::getBomId, bomIds)
                    .list().stream()
                    .collect(Collectors.groupingBy(ReplaceBomMaterialEntity::getBomMaterialId));
            Map<Integer, List<BomRawMaterialEntity>> bomRawMap = materialEntities.stream().collect(Collectors.groupingBy(BomRawMaterialEntity::getBomId));
            for (BomEntity bomEntity : result) {
                List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMap.get(bomEntity.getId());
                for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
                    bomRawMaterialEntity.setBomTypeName(bomRawMaterialEntity.getBomType() != null ? BomItemTypeEnum.getNameByCode(Integer.valueOf(bomRawMaterialEntity.getBomType())) : null);
                    bomRawMaterialEntity.setMaterialFields(materialSkuMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId())));
                    List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = bomReplaceMap.get(bomRawMaterialEntity.getId());
                    if (CollectionUtils.isEmpty(replaceBomMaterialEntities)) {
                        continue;
                    }
                    bomRawMaterialEntity.setReplaceMaterialId(Joiner.on(Constant.SEP).join(replaceBomMaterialEntities.stream().map(ReplaceBomMaterialEntity::getReplaceMaterialId).collect(Collectors.toSet())));
                }
                bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities);
            }
        }
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeEntityById(Integer id) {
        BomEntity bomEntity = this.selectById(id);
        if (bomEntity == null) {
            return true;
        }
        Map<String, String> materialCodeNameMap = materialService.getMaterialCodeNameMap(Collections.singleton(bomEntity.getCode()));
        // 删除相关表
        List<DataDelSchema> deleteSchemas = getDeleteSchemas();
        preHandleData(bomEntity, deleteSchemas);
        // 保存相关数据
        DataDelVO dataDelVO = commonDeleteHandler.buildDeleteRecord(deleteSchemas);
        dataDelVO.getDeleteRecords().forEach(e -> {
            e.setOrderNumber(bomEntity.getBomNum());
            e.setMaterialCode(bomEntity.getCode());
            e.setMaterialName(materialCodeNameMap.get(bomEntity.getCode()));
            e.setOrderType(ModuleEnum.BOM.getCode());
        });
        deleteRecordService.saveBatch(dataDelVO.getDeleteRecords());
        // 执行删除
        commonDeleteHandler.doDelete(dataDelVO.getLastDeletes());

        if (!bomEntity.getIsTemplate()) {
            // 物料有无生效状态的bom 事件
            applicationContext.publishEvent(new MaterialHaveReleasedStateBomEvent(this, bomEntity));
            // 推送消息
            messagePushToKafkaService.pushNewMessage(bomEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_DELETE_MESSAGE);
        } else {
            // 推送消息
            messagePushToKafkaService.pushNewMessage(bomEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_TEMPLATE_DELETE_MESSAGE);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BomEntity saveEntity(BomEntity bomEntity) {
        // 如果传入的编码为空，则由编码规则生成
        Map<String, String> numberMap = new HashMap<>();
        Map<String, String> relatedMap = new HashMap<>();
        numberMap.put("6", bomEntity.getCode());
        relatedMap.put("materialCode", bomEntity.getCode());
        if (StringUtils.isBlank(bomEntity.getBomNum())) {
            if (Objects.isNull(bomEntity.getNumberRuleId())) {
                throw new ResponseException(RespCodeEnum.NEED_NUMBER_RULE);
            }
            NumberCodeDTO seqById = numberRuleService.getSeqById(
                    RuleSeqDTO.builder()
                            .id(bomEntity.getNumberRuleId())
                            .numberMap(numberMap)
                            .relatedMap(relatedMap)
                            .build()
            );
            bomEntity.setBomNum(seqById.getCode());
        }
        bomEntity.setOriginNum(null);
        bomEntity.setOriginId(null);
        bomEntity.setId(null);
        Boolean isTemplate = bomEntity.getIsTemplate();
        // 默认非模板
        if (isTemplate == null) {
            isTemplate = Constants.FALSE;
            bomEntity.setIsTemplate(isTemplate);
        }

        // 兼容历史逻辑，如果productionState为空，设置默认值为量产
        if (StringUtils.isBlank(bomEntity.getProductionState())) {
            bomEntity.setProductionState(ProductionStateEnum.MASS_PRODUCTION.getCode());
        }
        //同一物料只能对应唯一BOM编码，通过版本区分统一物料对应的多个BOM
        // 校验BOM编号是否重复
        LambdaQueryWrapper<BomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BomEntity::getBomNum, bomEntity.getBomNum());
        wrapper.eq(BomEntity::getIsTemplate, bomEntity.getIsTemplate());
        if (count(wrapper) > 0) {
            if (!isTemplate) {
                throw new ResponseException(RespCodeEnum.BOM_NUM_DUPLICATE);
            } else {
                // bom模板
                throw new ResponseException(RespCodeEnum.TEMPLATE_CODE_DUPLICATE);
            }
        }
        // 创建时状态设置为创建
        bomEntity.setState(BomStateEnum.CREATE.getCode());
        // 如果需要审批，则缺省为待审核状态
        if (!isTemplate && approveConfigService.getConfigByCode(ApproveModuleEnum.BOM.getCode())) {
            bomEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        if (isTemplate && approveConfigService.getConfigByCode(ApproveModuleEnum.BOM_TEMPLATE.getCode())) {
            bomEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        bomEntity.setCreateTime(Objects.nonNull(bomEntity.getCreateTime()) ? bomEntity.getCreateTime() : new Date());
        bomEntity.setUpdateTime(new Date());
        this.save(bomEntity);
        // 如调用到编码规则，则相对应的所有序列号加1
        if (Objects.nonNull(bomEntity.getNumberRuleId())) {
            ruleSeqService.updateSeqEntity(relatedMap, bomEntity.getNumberRuleId());
        }
        initOriginInfo(bomEntity);
        // 更新附件表
        updateFile(bomEntity);
        // bom子物料数据处理
        bomEntity.getBomRawMaterialEntities().forEach(o -> o.setId(null));
        dealRawMaterialData(bomEntity);
        if (StringUtils.isBlank(bomEntity.getVersion())) {
            bomEntity.setVersion(generateVersion(bomEntity.getBomNum()));
            this.updateById(bomEntity);
        }
        // 如调用到编码规则，则相对应的所有序列号加1
        if (Objects.nonNull(bomEntity.getGenerateRuleDetailDTO())) {
            GenerateRuleDetailDTO ruleDetailDTO = bomEntity.getGenerateRuleDetailDTO();
            ruleSeqService.updateSeqEntityWithNewTra(ruleDetailDTO.getRelatedMap(), ruleDetailDTO.getNumberRulesId(), false);
        }
        // 推送消息
        if (!isTemplate) {
            messagePushToKafkaService.pushNewMessage(bomEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_ADD_MESSAGE);
        } else {
            messagePushToKafkaService.pushNewMessage(bomEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_TEMPLATE_ADD_MESSAGE);
        }
        // 版本变更记录
        initVersionLog(bomEntity);
        return bomEntity;
    }

    /**
     * 更新附件表
     */
    private void updateFile(BomEntity bomEntity) {
        List<AppendixEntity> appendixEntities = bomEntity.getAppendixEntities();
        if (CollectionUtils.isNotEmpty(appendixEntities)) {
            for (AppendixEntity appendixEntity : appendixEntities) {
                appendixEntity.setId(null);
                appendixEntity.setRelateId(bomEntity.getId().toString());
                appendixEntity.setIsUsed(true);
                appendixEntity.setCreateUser(bomEntity.getCreateBy());
                appendixEntity.setCreateTime(new Date());
                appendixEntity.setRelateName(bomEntity.getBomName());
            }
            appendixService.saveBatch(bomEntity.getAppendixEntities());
        }
    }


    /**
     * 新增生效BOM
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReleasedEntity(BomEntity entity) {
        Boolean isTemplate = entity.getIsTemplate();
        // 默认非模板
        if (isTemplate == null) {
            isTemplate = Constants.FALSE;
            entity.setIsTemplate(isTemplate);
        }
        if (!isTemplate && approveConfigService.getConfigByCode(ApproveModuleEnum.BOM.getCode())) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        if (isTemplate && approveConfigService.getConfigByCode(ApproveModuleEnum.BOM_TEMPLATE.getCode())) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        saveEntity(entity);
        entity.setState(BomStateEnum.RELEASED.getCode());
        updateEntity(entity);
        // 物料有无生效状态的bom 事件
        if (!isTemplate) {
            applicationContext.publishEvent(new MaterialHaveReleasedStateBomEvent(this, entity));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEntity(BomEntity bomEntity) {
        BomEntity oldBom = getById(bomEntity.getId());
        if (oldBom == null) {
            throw new ResponseException(String.format("id=%s的bom不存在", bomEntity.getId()));
        }

        // 兼容历史逻辑，如果productionState为空，设置默认值为量产
        if (StringUtils.isBlank(bomEntity.getProductionState())) {
            bomEntity.setProductionState(ProductionStateEnum.MASS_PRODUCTION.getCode());
        }

        // 如果是生效状态，则量产态不能改为试产状态
        boolean changeProductionState = !Objects.equals(oldBom.getProductionState(), bomEntity.getProductionState())
                && bomEntity.getProductionState().equals(ProductionStateEnum.TEST_PRODUCTION.getCode());
        if (bomEntity.getState() == BomStateEnum.RELEASED.getCode() && changeProductionState) {
            throw new ResponseException(RespCodeEnum.DFS_BOM_STATE_NOT_CROSS_LEVEL);
        }
        // 编码不能更新，直接设置为老的
        bomEntity.setBomNum(oldBom.getBomNum());
        // 原始的信息
        bomEntity.setOriginId(Optional.ofNullable(oldBom.getOriginId()).orElse(oldBom.getId()));
        bomEntity.setOriginNum(Optional.ofNullable(oldBom.getOriginNum()).orElse(oldBom.getBomNum()));
        Boolean isTemplate = bomEntity.getIsTemplate();
        // 默认非模板
        if (isTemplate == null) {
            isTemplate = Constants.FALSE;
        }

        //如果是驳回状态,修改数据后变更为待审核状态。此处只要是创建状态,状态改为待审批可覆盖这个需求
        if (!isTemplate && bomEntity.getState() == BomStateEnum.CREATE.getCode()
                && approveConfigService.getConfigByCode(ApproveModuleEnum.BOM.getCode())) {
            bomEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        //如果是驳回状态,修改数据后变更为待审核状态。此处只要是创建状态,状态改为待审批可覆盖这个需求
        if (isTemplate && bomEntity.getState() == BomStateEnum.CREATE.getCode()
                && approveConfigService.getConfigByCode(ApproveModuleEnum.BOM_TEMPLATE.getCode())) {
            bomEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 版本有变更
        boolean changeVersion = !Objects.equals(oldBom.getVersion(), bomEntity.getVersion());
        // 保存为新版本（全新的实例）
        boolean saveToCopy = bomEntity.getSaveToCopy() && !isTemplate;
        if (saveToCopy) {
            // 获取版本号
            String newVersion;
            String originNum = oldBom.getOriginNum();
            if (changeVersion) {
                String curVersion = bomEntity.getVersion();
                String uniCode = buildVersionInfo(originNum, curVersion);
                boolean exists = lambdaQuery()
                        .eq(BomEntity::getBomNum, uniCode)
                        .or(o -> o
                                .eq(BomEntity::getOriginNum, originNum)
                                .eq(BomEntity::getVersion, curVersion)
                        )
                        .exists();
                if (exists) {
                    throw new ResponseException(String.format("BOM:[%s]已存在", uniCode));
                }
                newVersion = curVersion;
            } else {
                // 引用编码规则
                newVersion = generateVersion(originNum);
            }
            // 生成新的
            Date now = new Date();
            bomEntity.setId(null);
            bomEntity.setBomNum(buildVersionInfo(originNum, newVersion));
            bomEntity.setVersion(newVersion);
            bomEntity.setCreateTime(now);
            bomEntity.setUpdateTime(now);
            bomEntity.initReleaseTime();
            this.save(bomEntity);
            versionChangeRecordService.changeVersion(VersionChangeDTO.builder()
                    .relateType(VersionModelIdEnum.BOM)
                    .description("BOM - 版本变更")
                    .relateId(bomEntity.getId())
                    .firstId(bomEntity.getOriginId())
                    .versionCurrent(newVersion)
                    .change(VersionChangeDTO.ChangeDTO.builder()
                            .lastId(oldBom.getId())
                            .lastVersion(oldBom.getVersion())
                            .build()
                    )
                    .build()
            );
        } else {
            // 生效时间
            if (bomEntity.getState() == BomStateEnum.RELEASED.getCode() && !Objects.equals(oldBom.getState(), bomEntity.getState())) {
                bomEntity.setReleaseTime(new Date());
            }
            this.updateById(bomEntity);
            // 版本变更记录
            changeVersionLog(bomEntity);
        }
        //传null不更新，传空数组则是删除
        if (bomEntity.getBomRawMaterialEntities() != null) {
            // 删除原绑定所需的原料及数量
            LambdaQueryWrapper<BomRawMaterialEntity> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(BomRawMaterialEntity::getBomId, bomEntity.getId());
            bomRawMaterialService.remove(deleteWrapper);
            // bom子物料数据处理
            bomEntity.getBomRawMaterialEntities().forEach(o -> o.setId(null));
            dealRawMaterialData(bomEntity);
        }
        //  更改物料附件，如果传null则不更新，传空数组则是删除
        if (CollectionUtils.isNotEmpty(bomEntity.getAppendixEntities())) {
            appendixService.updateAppendix(bomEntity);
        }

        // 处理历史相同物料编码的BOM
        refreshReleasedBom(Collections.singletonList(bomEntity.getId()));
        // 物料有无生效状态的bom 事件
        if (!isTemplate) {
            applicationContext.publishEvent(new MaterialHaveReleasedStateBomEvent(this, bomEntity));
        }
        // 推送消息
        if (!isTemplate) {
            messagePushToKafkaService.pushNewMessage(bomEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_UPDATE_MESSAGE);
        } else {
            messagePushToKafkaService.pushNewMessage(bomEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_TEMPLATE_UPDATE_MESSAGE);
        }
    }

    private String generateVersion(String originNum) {
        String newVersion = null;
        int createVersionCount = 0;
        while (createVersionCount++ < 10) {
            // 需要按工艺编码归一，且获取的数据中需要剔除工艺编码信息，保留版本号
            Map<String, String> numberMap = new HashMap<>(8);
            numberMap.put(RulePrefixEnum.BOM_CODE.getCode(), originNum);
            Map<String, String> relatedMap = new HashMap<>(8);
            relatedMap.put(AutoIncrementConfigureTypeEnum.BOM_CODE.getCode(), originNum);
            GenerateCodeDTO build = GenerateCodeDTO.builder()
                    .type("105")
                    .numberMap(numberMap)
                    .relatedMap(relatedMap)
                    .build();
            String curVersion = numberRuleService.generateCodeByTypeWithNewTra(build);
            if (curVersion == null) {
                throw new ResponseException("默认的BOM版本的编码规则没有'自动生成序号'");
            }
            // 将originCode替换为空字符串
            curVersion = curVersion.replace(originNum, "");

            String finalCurVersion = curVersion;
            boolean exists = lambdaQuery()
                    .eq(BomEntity::getCraftCode, buildVersionInfo(originNum, curVersion))
                    .or(o -> o
                            .eq(BomEntity::getOriginNum, originNum)
                            .eq(BomEntity::getVersion, finalCurVersion)
                    )
                    .exists();
            if (!exists) {
                newVersion = curVersion;
                break;
            }
        }
        if (newVersion == null) {
            throw new ResponseException("尝试按照编码规则生成新版本失败，请重试或创建新的版本编码规则（需带有自动生成项）");
        }
        return newVersion;
    }

    /**
     * 处理历史相同物料编码的BOM
     */
    @Override
    public void refreshReleasedBom(List<Integer> originBomIds) {
        if (CollectionUtils.isEmpty(originBomIds)) {
            return;
        }
        // 获取版本管理的软件参数配置，如果同物料只能生效一个BOM，则需要废弃其他BOM
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.BOM_VERSION_MNG).build();
        BomVersionConfDTO config = businessConfigService.getValueDto(dto, BomVersionConfDTO.class);
        if (config.getEnableReleaseCount().equals(BomVersionReleaseEnum.MORE.getCode())) {
            return;
        }
        // 找出生效的
        List<BomEntity> originBoms = this.lambdaQuery()
                .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                .in(BomEntity::getId, originBomIds).list();

        for (BomEntity originBom : originBoms) {
            // 当前需要矫正状态的BOM集合
            List<BomEntity> boms = this.lambdaQuery()
                    // 非模板
                    .eq(BomEntity::getIsTemplate, false)
                    // 生效的
                    .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                    // 不包含自己
                    .ne(BomEntity::getId, originBom.getId())
                    .eq(BomEntity::getCode, originBom.getCode())
                    .list();
            // 存在其他生效的才进行矫正
            if (boms.isEmpty()) {
                continue;
            }
            List<Integer> bomIds = boms.stream().map(BomEntity::getId).collect(Collectors.toList());
            this.lambdaUpdate().set(BomEntity::getState, BomStateEnum.STOP_USING.getCode()).in(BomEntity::getId, bomIds).update();
            for (BomEntity bom : boms) {
                versionChangeRecordService.changeVersion(VersionChangeDTO.builder()
                        .relateType(VersionModelIdEnum.BOM)
                        .description("BOM - 根据版本规则管理, 自动停用")
                        .relateId(bom.getId())
                        .versionCurrent(bom.getVersion())
                        .build()
                );
            }
        }
    }

    /**
     * bom子物料数据处理
     */
    @Transactional(rollbackFor = Exception.class)
    public void dealRawMaterialData(BomEntity bomEntity) {
        if (CollectionUtils.isEmpty(bomEntity.getBomRawMaterialEntities())) {
            return;
        }
        List<BomTileEntity> tileList = new ArrayList<>();
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomEntity.getBomRawMaterialEntities();
        // 重新绑定物料关系及倒排数据
        Boolean isTemplate = bomEntity.getIsTemplate();
        String verMaterialEnable = getVerMaterialEnable();
        // 通过特征参数和物料编码去重
        List<BomRawMaterialEntity> distinctMaterialList = bomRawMaterialEntities.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                        new TreeSet<>(Comparator.comparing(o -> ColumnUtil.getMaterialSku(o.getCode(), o.getSkuId())))), ArrayList::new));
        // 检验是否存在相同物料
        if (BomRawMaterialEnum.NOT_REPEAT.getCode().equals(verMaterialEnable) && bomRawMaterialEntities.size() > distinctMaterialList.size()) {
            String duplicateMessage = buildDuplicateMaterialMessage(bomRawMaterialEntities);
            throw new ResponseException(duplicateMessage);
        }
        // 合并特殊处理
        if (BomRawMaterialEnum.REPEAT_MERGE.getCode().equals(verMaterialEnable)) {
            // 通过特征参数和物料编码去重，相同的进行合并处理，分子分母需要通分（比如 3/5 + 2/5 = 1/1）
            Map<String, List<BomRawMaterialEntity>> map = bomRawMaterialEntities.stream()
                    .collect(Collectors.groupingBy(o -> ColumnUtil.getMaterialSku(o.getCode(), o.getSkuId())));
            for (BomRawMaterialEntity bomRawMaterialEntity : distinctMaterialList) {
                // 重新设置分子分母
                List<BomRawMaterialEntity> listMaterialEntities = map.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId()));
                Rational add = new Rational();
                double fixedDamage = 0;
                for (BomRawMaterialEntity materialEntity : listMaterialEntities) {
                    if (materialEntity.getFixedDamage() != null) {
                        fixedDamage = fixedDamage + materialEntity.getFixedDamage();
                    }
                    if (!ObjectUtils.isEmpty(materialEntity.getNum()) && !ObjectUtils.isEmpty(materialEntity.getNumber())) {
                        Rational rational = new Rational(materialEntity.getNum().doubleValue(), materialEntity.getNumber().doubleValue());
                        add = add.add(rational);
                    }
                }
                bomRawMaterialEntity.setNum(BigDecimal.valueOf(add.getNumerator()));
                bomRawMaterialEntity.setNumber(BigDecimal.valueOf(add.getDenominator()));
                bomRawMaterialEntity.setFixedDamage(fixedDamage);
            }
            bomRawMaterialEntities = distinctMaterialList;
        }
        // 通用处理
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            if (Boolean.FALSE.equals(isTemplate) && bomRawMaterialEntity.getCode().equals(bomEntity.getCode())) {
                throw new ResponseException("bom清单下的物料需不同于本身的物料");
            }
            // 分母不能为0
            if (bomRawMaterialEntity.getNumber() != null && bomRawMaterialEntity.getNumber().compareTo(BigDecimal.ZERO) == 0) {
                throw new ResponseException("分母不能为0");
            }
//            bomRawMaterialEntity.setId(null);
            bomRawMaterialEntity.setBomId(bomEntity.getId());
            // 单个保存，因为下面替代料保存需要id，批量保存获取不到，
            // 因为存在相同物料，保存后也查询不到唯一记录，所以只能单个保存
            bomRawMaterialService.save(bomRawMaterialEntity);
            if (Boolean.FALSE.equals(isTemplate)) {
                tileList.add(BomTileEntity.builder().bomMaterialCode(bomEntity.getCode()).materialCode(bomRawMaterialEntity.getCode()).build());
            }
        }
        // 检验新增BOM是否构成循环
        bomRawMaterialEntities.forEach(o -> checkBomCycle(o.getCode(), o.getSkuId()));
        checkBomCycle(bomEntity.getCode(), bomEntity.getSkuId());
        // 平铺BOM，用于倒排,当bom为生效时才插入
        if (bomEntity.getState() == BomStateEnum.RELEASED.getCode()) {
            // 删除原平铺bom
            bomTileService.removeByBomMaterialCode(bomEntity.getCode());
            bomTileService.saveBatch(tileList);
        }
        //更新Bom子物料和替代料关系
        //删除BOM下面所有的关联关系后重新插入
        replaceBomMaterialService.lambdaUpdate().eq(ReplaceBomMaterialEntity::getBomId, bomEntity.getId()).remove();
        List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = new ArrayList<>();
        // 重新绑定替代物料的关系
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            if (StringUtils.isBlank(bomRawMaterialEntity.getReplaceMaterialId())) {
                continue;
            }
            // 不能存在相同的替代物
            List<ReplaceSchemeMaterialEntity> list = replaceSchemeMaterialService.listByIds(Arrays.asList(bomRawMaterialEntity.getReplaceMaterialId().split(Constant.SEP)));
            Map<String, List<ReplaceSchemeMaterialEntity>> map = list.stream().collect(Collectors.groupingBy(ReplaceSchemeMaterialEntity::getReplaceMaterialCode));
            map.forEach((key, value) -> {
                if (value.size() > 1) {
                    throw new ResponseException("BOM存在相同替代物");
                }
            });

            // 如果子物料也是bom，需要判断作为bom的它的子物料是否和替代物料重复，如果有则报错
            BomEntity latestBom = this.getLatestBom(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId(), BomStateEnum.RELEASED.getCode());
            if (Objects.nonNull(latestBom)) {
                List<String> collect = latestBom.getBomRawMaterialEntities().stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
                collect.forEach(o -> {
                    if (map.containsKey(o)) {
                        throw new ResponseException("子物料" + bomRawMaterialEntity.getCode() + "为bom，且该bom下存在和替代物相同的物料");
                    }
                });
            }

            String[] split = bomRawMaterialEntity.getReplaceMaterialId().split(Constant.SEP);
            for (String s : split) {
                ReplaceBomMaterialEntity build = ReplaceBomMaterialEntity.builder()
                        .bomId(bomEntity.getId())
                        .bomMaterialId(bomRawMaterialEntity.getId())
                        .replaceMaterialId(Integer.valueOf(s))
                        .extendOne(bomRawMaterialEntity.getExtendOne())
                        .build();
                replaceBomMaterialEntities.add(build);
            }
        }
        replaceBomMaterialService.saveBatch(replaceBomMaterialEntities);
    }


    /**
     * 获取bom子物料行校验方式编码
     *
     * @return
     */
    private String getVerMaterialEnable() {
        // 获取bom子项物料校验方式
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.BOM_RAW_MATERIAL_CONFIG).build();
        Map<String, String> valueMap = businessConfigService.getValueMap(dto);
        return JSON.parseObject(valueMap.get(ConfigValueConstant.VER_MATERIAL_ENABLE), String.class);
    }

    /**
     * 检验循环Bom
     *
     * @param materialCode
     */
    private void checkBomCycle(String materialCode, Integer skuId) {
        Map<BomKeyDTO, BomEntity> bomMap = new HashMap<>();
        // 获取创建时间最新的bom
        List<BomRawMaterialEntity> bomRawMaterialEntities = getLatestBomInCache(bomMap, new BomKeyDTO(materialCode, skuId), null);
        if (CollectionUtils.isEmpty(bomRawMaterialEntities)) {
            return;
        }
        for (BomRawMaterialEntity materialEntity : bomRawMaterialEntities) {
            // 获取下一级bom
            getNextLevelBom(materialEntity, bomMap, materialCode);
        }
    }

    private void getNextLevelBom(BomRawMaterialEntity materialEntity, Map<BomKeyDTO, BomEntity> bomMap, String materialCode) {
        getNextLevelBom(materialEntity, bomMap, materialCode, new ArrayList<>());
    }

    private void getNextLevelBom(BomRawMaterialEntity materialEntity, Map<BomKeyDTO, BomEntity> bomMap, String materialCode, List<String> path) {
        // 获取物料关联的最新的bom
        BomKeyDTO dto = new BomKeyDTO(materialEntity.getCode(), materialEntity.getSkuId());
        String currentMaterialSku = ColumnUtil.getMaterialSku(materialEntity.getCode(), materialEntity.getSkuId());
        path.add(currentMaterialSku);

        List<BomRawMaterialEntity> bomRawMaterialEntities = getLatestBomInCache(bomMap, dto, null);
        for (BomRawMaterialEntity entity : bomRawMaterialEntities) {
            if (materialCode.equals(entity.getCode())) {
                // 添加循环的终点
                path.add(ColumnUtil.getMaterialSku(entity.getCode(), entity.getSkuId()));
                String cyclePath = formatCyclePath(path);
                throw new ResponseException("检测到BOM循环：" + cyclePath);
            }
            //  recursive call 获取下一级bom
            getNextLevelBom(entity, bomMap, materialCode, new ArrayList<>(path));
        }
    }

    /**
     * 检测并标记循环BOM
     *
     * @param bomImportExcelDTOS 导入的BOM列表
     */
    private void checkAndMarkCyclicBoms(List<BomImportExcelDTO> bomImportExcelDTOS) {
        if (CollectionUtils.isEmpty(bomImportExcelDTOS)) {
            return;
        }

        // 构建导入BOM的依赖关系图
        Map<String, Set<String>> importBomDependencies = new HashMap<>();
        Map<String, BomImportExcelDTO> importBomMap = new HashMap<>();

        for (BomImportExcelDTO bomImportDTO : bomImportExcelDTOS) {
            if (StringUtils.isBlank(bomImportDTO.getMaterialCode()) || CollectionUtils.isEmpty(bomImportDTO.getSonMaterialList())) {
                continue;
            }

            String bomMaterialSku = ColumnUtil.getMaterialSku(bomImportDTO.getMaterialCode(), bomImportDTO.getSkuId());
            importBomMap.put(bomMaterialSku, bomImportDTO);

            Set<String> dependencies = new HashSet<>();
            for (BomMaterialImportDTO sonMaterial : bomImportDTO.getSonMaterialList()) {
                if (StringUtils.isNotBlank(sonMaterial.getCode())) {
                    dependencies.add(ColumnUtil.getMaterialSku(sonMaterial.getCode(), sonMaterial.getSkuId()));
                }
            }
            importBomDependencies.put(bomMaterialSku, dependencies);
        }

        // 检测循环并标记相关BOM
        detectAndMarkCyclicBoms(importBomDependencies, importBomMap);
    }

    /**
     * 检测循环并标记相关BOM
     *
     * @param dependencies BOM依赖关系图
     * @param importBomMap 导入BOM映射
     */
    private void detectAndMarkCyclicBoms(Map<String, Set<String>> dependencies, Map<String, BomImportExcelDTO> importBomMap) {
        // 计算每个节点的入度
        Map<String, Integer> inDegree = new HashMap<>();
        for (String bom : dependencies.keySet()) {
            inDegree.put(bom, 0);
        }

        for (Set<String> deps : dependencies.values()) {
            for (String dep : deps) {
                if (importBomMap.containsKey(dep)) { // 只考虑导入文件中的依赖
                    inDegree.put(dep, inDegree.getOrDefault(dep, 0) + 1);
                }
            }
        }

        // 拓扑排序
        Queue<String> queue = new LinkedList<>();
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.offer(entry.getKey());
            }
        }

        int processedCount = 0;
        while (!queue.isEmpty()) {
            String current = queue.poll();
            processedCount++;

            Set<String> currentDeps = dependencies.get(current);
            if (currentDeps != null) {
                for (String dep : currentDeps) {
                    if (importBomMap.containsKey(dep)) { // 只考虑导入文件中的依赖
                        inDegree.put(dep, inDegree.get(dep) - 1);
                        if (inDegree.get(dep) == 0) {
                            queue.offer(dep);
                        }
                    }
                }
            }
        }

        // 如果处理的节点数少于总节点数，说明存在循环
        if (processedCount < importBomMap.size()) {
            // 找出参与循环的BOM
            Set<String> cyclicBoms = new HashSet<>();
            for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
                if (entry.getValue() > 0) {
                    cyclicBoms.add(entry.getKey());
                }
            }

            // 找到所有循环路径并标记相关BOM
            Map<String, String> cyclePaths = findAllCyclePaths(dependencies, cyclicBoms, importBomMap);
            markSpecificCyclicBoms(cyclePaths, importBomMap);
        }
    }

    /**
     * 找到所有循环路径
     *
     * @param dependencies BOM依赖关系图
     * @param cyclicBoms 参与循环的BOM集合
     * @param importBomMap 导入BOM映射
     * @return 每个循环BOM对应的循环路径
     */
    private Map<String, String> findAllCyclePaths(Map<String, Set<String>> dependencies, Set<String> cyclicBoms, Map<String, BomImportExcelDTO> importBomMap) {
        Map<String, String> cyclePaths = new HashMap<>();
        Set<String> processedBoms = new HashSet<>();

        for (String cyclicBom : cyclicBoms) {
            if (processedBoms.contains(cyclicBom)) {
                continue;
            }

            List<String> path = new ArrayList<>();
            Set<String> visited = new HashSet<>();

            if (findCyclePathDFS(cyclicBom, dependencies, cyclicBoms, path, visited)) {
                String cyclePathString = buildCyclePathString(path, importBomMap);
                // 将路径中的所有BOM都标记为已处理，并设置相同的循环路径
                for (String bomInPath : path) {
                    if (cyclicBoms.contains(bomInPath)) {
                        cyclePaths.put(bomInPath, cyclePathString);
                        processedBoms.add(bomInPath);
                    }
                }
            }
        }

        // 对于没有找到路径的循环BOM，使用简单的错误信息
        for (String cyclicBom : cyclicBoms) {
            if (!cyclePaths.containsKey(cyclicBom)) {
                BomImportExcelDTO bomDTO = importBomMap.get(cyclicBom);
                String bomName = bomDTO != null ? (bomDTO.getMaterialCode() + (bomDTO.getSkuId() != null ? "-" + bomDTO.getSkuId() : "")) : cyclicBom;
                cyclePaths.put(cyclicBom, "参与循环依赖：" + bomName);
            }
        }

        return cyclePaths;
    }

    /**
     * 标记特定的循环BOM
     *
     * @param cyclePaths 循环路径映射
     * @param importBomMap 导入BOM映射
     */
    private void markSpecificCyclicBoms(Map<String, String> cyclePaths, Map<String, BomImportExcelDTO> importBomMap) {
        for (Map.Entry<String, String> entry : cyclePaths.entrySet()) {
            String cyclicBomSku = entry.getKey();
            String cyclePath = entry.getValue();

            BomImportExcelDTO bomDTO = importBomMap.get(cyclicBomSku);
            if (bomDTO != null) {
                bomDTO.setVerifyPass(false);
                bomDTO.setImportResult("导入文件中存在循环依赖：" + cyclePath);
            }
        }
    }

    /**
     * 使用DFS查找循环路径
     *
     * @param current 当前节点
     * @param dependencies 依赖关系图
     * @param cyclicBoms 循环节点集合
     * @param path 当前路径
     * @param visited 已访问节点
     * @return 是否找到循环
     */
    private boolean findCyclePathDFS(String current, Map<String, Set<String>> dependencies, Set<String> cyclicBoms,
                                    List<String> path, Set<String> visited) {
        if (path.contains(current)) {
            // 找到循环，截取循环部分
            int cycleStart = path.indexOf(current);
            List<String> cyclePath = new ArrayList<>(path.subList(cycleStart, path.size()));
            cyclePath.add(current); // 添加回到起点的节点
            path.clear();
            path.addAll(cyclePath);
            return true;
        }

        if (visited.contains(current)) {
            return false;
        }

        visited.add(current);
        path.add(current);

        Set<String> deps = dependencies.get(current);
        if (deps != null) {
            for (String dep : deps) {
                if (cyclicBoms.contains(dep)) {
                    if (findCyclePathDFS(dep, dependencies, cyclicBoms, path, visited)) {
                        return true;
                    }
                }
            }
        }

        path.remove(path.size() - 1);
        return false;
    }

    /**
     * 构建循环路径字符串，使用箭头标注
     *
     * @param path 循环路径
     * @param importBomMap 导入BOM映射
     * @return 格式化的循环路径字符串
     */
    private String buildCyclePathString(List<String> path, Map<String, BomImportExcelDTO> importBomMap) {
        if (path.isEmpty()) {
            return "未知循环路径";
        }

        List<String> formattedPath = new ArrayList<>();
        for (String materialSku : path) {
            BomImportExcelDTO bomDTO = importBomMap.get(materialSku);
            if (bomDTO != null) {
                // 格式：物料编码-skuId（skuId为空时不拼接）
                String formatted = bomDTO.getMaterialCode() + (bomDTO.getSkuId() != null ? "-" + bomDTO.getSkuId() : "");
                formattedPath.add(formatted);
            } else {
                formattedPath.add(materialSku);
            }
        }

        return String.join(" → ", formattedPath);
    }



    /**
     * 检验导入BOM是否构成循环
     *
     * @param bomMaterialCode BOM主物料编码
     * @param bomSkuId BOM主物料SKU ID
     * @param sonMaterialList 子物料列表
     */
    private void checkBomCycleForImport(String bomMaterialCode, Integer bomSkuId, List<BomMaterialImportDTO> sonMaterialList) {
        if (StringUtils.isBlank(bomMaterialCode) || CollectionUtils.isEmpty(sonMaterialList)) {
            return;
        }

        // 使用Set记录访问路径，避免重复检查
        Set<String> visitedPath = new HashSet<>();
        visitedPath.add(ColumnUtil.getMaterialSku(bomMaterialCode, bomSkuId));

        // 检查每个子物料是否会形成循环
        for (BomMaterialImportDTO sonMaterial : sonMaterialList) {
            if (StringUtils.isNotBlank(sonMaterial.getCode())) {
                checkBomCycleRecursive(bomMaterialCode, bomSkuId, sonMaterial.getCode(),
                    sonMaterial.getSkuId(), visitedPath, new ArrayList<>(visitedPath));
            }
        }
    }

    /**
     * 递归检查BOM循环
     *
     * @param rootMaterialCode 根物料编码
     * @param rootSkuId 根物料SKU ID
     * @param currentMaterialCode 当前检查的物料编码
     * @param currentSkuId 当前检查的物料SKU ID
     * @param globalVisited 全局访问记录
     * @param currentPath 当前路径
     */
    private void checkBomCycleRecursive(String rootMaterialCode, Integer rootSkuId,
                                       String currentMaterialCode, Integer currentSkuId,
                                       Set<String> globalVisited, List<String> currentPath) {
        String currentMaterialSku = ColumnUtil.getMaterialSku(currentMaterialCode, currentSkuId);

        // 如果当前物料就是根物料，说明形成了循环
        if (rootMaterialCode.equals(currentMaterialCode) && Objects.equals(rootSkuId, currentSkuId)) {
            currentPath.add(currentMaterialSku);
            String cyclePath = formatCyclePath(currentPath);
            throw new ResponseException("检测到循环依赖：" + cyclePath);
        }

        // 如果在当前路径中已经访问过，说明形成了循环
        if (currentPath.contains(currentMaterialSku)) {
            currentPath.add(currentMaterialSku);
            String cyclePath = formatCyclePath(currentPath);
            throw new ResponseException("检测到循环依赖：" + cyclePath);
        }

        // 如果全局已访问过，跳过（避免重复检查）
        if (globalVisited.contains(currentMaterialSku)) {
            return;
        }

        // 添加到访问记录
        globalVisited.add(currentMaterialSku);
        currentPath.add(currentMaterialSku);

        try {
            // 获取当前物料的BOM信息
            Map<BomKeyDTO, BomEntity> bomMap = new HashMap<>();
            BomKeyDTO dto = new BomKeyDTO(currentMaterialCode, currentSkuId);
            List<BomRawMaterialEntity> bomRawMaterialEntities = getLatestBomInCache(bomMap, dto, null);

            // 递归检查子物料
            for (BomRawMaterialEntity entity : bomRawMaterialEntities) {
                checkBomCycleRecursive(rootMaterialCode, rootSkuId, entity.getCode(),
                    entity.getSkuId(), globalVisited, new ArrayList<>(currentPath));
            }
        } finally {
            // 回溯时移除当前路径记录
            currentPath.remove(currentMaterialSku);
        }
    }

    /**
     * 格式化循环路径，将物料SKU转换为物料编码-skuId格式并使用箭头连接
     *
     * @param path 循环路径
     * @return 格式化的循环路径字符串
     */
    private String formatCyclePath(List<String> path) {
        if (path.isEmpty()) {
            return "未知循环路径";
        }

        List<String> formattedPath = new ArrayList<>();
        for (String materialSku : path) {
            // 解析materialSku，格式为：materialCode + "SKU" + skuId
            String formatted = parseMaterialSkuToReadableFormat(materialSku);
            formattedPath.add(formatted);
        }

        return String.join(" → ", formattedPath);
    }

    /**
     * 解析物料SKU为可读格式
     *
     * @param materialSku 物料SKU字符串
     * @return 格式化的字符串（物料编码-skuId）
     */
    private String parseMaterialSkuToReadableFormat(String materialSku) {
        if (StringUtils.isBlank(materialSku)) {
            return materialSku;
        }

        // materialSku格式：materialCode + "-skuId" + skuId
        int skuIndex = materialSku.lastIndexOf("-skuId");
        if (skuIndex > 0) {
            String materialCode = materialSku.substring(0, skuIndex);
            String skuId = materialSku.substring(skuIndex + 6); // "-skuId"长度为6

            // 如果skuId为默认值0，则不显示skuId
            if ("0".equals(skuId)) {
                return materialCode;
            } else {
                return materialCode + "-" + skuId;
            }
        }

        return materialSku;
    }

    /**
     * 构建重复物料的详细错误信息
     *
     * @param bomRawMaterialEntities BOM原料实体列表
     * @return 包含重复物料详细信息的错误消息
     */
    private String buildDuplicateMaterialMessage(List<BomRawMaterialEntity> bomRawMaterialEntities) {
        // 按物料编码和SKU分组，找出重复的物料
        Map<String, List<BomRawMaterialEntity>> materialSkuMap = bomRawMaterialEntities.stream()
                .collect(Collectors.groupingBy(o -> ColumnUtil.getMaterialSku(o.getCode(), o.getSkuId())));

        // 找出重复的物料组
        List<List<BomRawMaterialEntity>> duplicateGroups = materialSkuMap.values().stream()
                .filter(group -> group.size() > 1)
                .collect(Collectors.toList());

        if (duplicateGroups.isEmpty()) {
            // 如果没有找到重复组，但确实存在重复，则分析所有物料找出可能的问题
            return buildFallbackDuplicateMessage(bomRawMaterialEntities);
        }

        return buildDetailedDuplicateMessage(duplicateGroups);
    }

    /**
     * 构建详细的重复物料错误信息
     *
     * @param duplicateGroups 重复物料组列表
     * @return 详细的错误信息
     */
    private String buildDetailedDuplicateMessage(List<List<BomRawMaterialEntity>> duplicateGroups) {
        // 收集所有需要查询的SKU ID
        Set<Integer> skuIds = new HashSet<>();

        for (List<BomRawMaterialEntity> group : duplicateGroups) {
            for (BomRawMaterialEntity entity : group) {
                if (entity.getSkuId() != null) {
                    skuIds.add(entity.getSkuId());
                }
            }
        }

        // 批量查询SKU信息
        Map<Integer, SkuEntity> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            skuMap = skuService.getSkusByIds(skuIds)
                    .stream()
                    .collect(Collectors.toMap(SkuEntity::getSkuId, Function.identity()));
        }

        // 构建错误信息
        StringBuilder message = new StringBuilder("BOM存在相同物料：");

        for (int i = 0; i < duplicateGroups.size(); i++) {
            List<BomRawMaterialEntity> group = duplicateGroups.get(i);
            BomRawMaterialEntity firstEntity = group.get(0);

            // 获取SKU信息
            String skuName = "";
            if (firstEntity.getSkuId() != null && firstEntity.getSkuId() != 0) {
                SkuEntity sku = skuMap.get(firstEntity.getSkuId());
                skuName = sku != null ? sku.getSkuName() : "";
            }

            // 构建单个重复物料的信息
            if (i > 0) {
                message.append("；");
            }
            message.append("物料编码：").append(firstEntity.getCode());
            if (StringUtils.isNotBlank(skuName)) {
                message.append("，SKU名称：").append(skuName);
            }
        }

        return message.toString();
    }

    /**
     * 当无法找到明确重复组时的备用消息构建方法
     *
     * @param bomRawMaterialEntities BOM原料实体列表
     * @return 备用错误信息
     */
    private String buildFallbackDuplicateMessage(List<BomRawMaterialEntity> bomRawMaterialEntities) {
        // 收集所有物料编码进行分析
        Set<String> materialCodes = bomRawMaterialEntities.stream()
                .map(BomRawMaterialEntity::getCode)
                .collect(Collectors.toSet());

        Set<Integer> skuIds = bomRawMaterialEntities.stream()
                .map(BomRawMaterialEntity::getSkuId)
                .filter(Objects::nonNull)
                .filter(id -> id != 0)
                .collect(Collectors.toSet());

        // 批量查询SKU信息
        Map<Integer, SkuEntity> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            skuMap = skuService.getSkusByIds(skuIds)
                    .stream()
                    .collect(Collectors.toMap(SkuEntity::getSkuId, Function.identity()));
        }

        StringBuilder message = new StringBuilder("BOM存在相同物料，涉及物料编码：");
        message.append(String.join("、", materialCodes));

        // 构建SKU信息
        if (!skuIds.isEmpty()) {
            List<String> skuInfos = new ArrayList<>();
            for (Integer skuId : skuIds) {
                SkuEntity sku = skuMap.get(skuId);
                String skuName = sku != null ? sku.getSkuName() : "未知SKU";
                skuInfos.add(skuName);
            }
            message.append("，涉及SKU名称：").append(String.join("、", skuInfos));
        }

        return message.toString();
    }

    /**
     * 构建导入时重复物料的详细错误信息
     *
     * @param bomImportDTOSonMaterialList BOM导入DTO子物料列表
     * @return 包含重复物料详细信息的错误消息
     */
    private String buildDuplicateMaterialMessageForImport(List<BomMaterialImportDTO> bomImportDTOSonMaterialList) {
        // 按物料编码和SKU分组，找出重复的物料
        Map<String, List<BomMaterialImportDTO>> materialSkuMap = bomImportDTOSonMaterialList.stream()
                .collect(Collectors.groupingBy(o -> ColumnUtil.getMaterialSku(o.getCode(), o.getSkuId())));

        // 找出重复的物料组
        List<List<BomMaterialImportDTO>> duplicateGroups = materialSkuMap.values().stream()
                .filter(group -> group.size() > 1)
                .collect(Collectors.toList());

        if (duplicateGroups.isEmpty()) {
            // 如果没有找到重复组，但确实存在重复，则分析所有物料找出可能的问题
            return buildFallbackDuplicateMessageForImport(bomImportDTOSonMaterialList);
        }

        return buildDetailedDuplicateMessageForImport(duplicateGroups);
    }

    /**
     * 构建导入时详细的重复物料错误信息
     *
     * @param duplicateGroups 重复物料组列表
     * @return 详细的错误信息
     */
    private String buildDetailedDuplicateMessageForImport(List<List<BomMaterialImportDTO>> duplicateGroups) {
        // 收集所有需要查询的SKU ID
        Set<Integer> skuIds = new HashSet<>();

        for (List<BomMaterialImportDTO> group : duplicateGroups) {
            for (BomMaterialImportDTO dto : group) {
                if (dto.getSkuId() != null) {
                    skuIds.add(dto.getSkuId());
                }
            }
        }

        // 批量查询SKU信息
        Map<Integer, SkuEntity> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            skuMap = skuService.getSkusByIds(skuIds)
                    .stream()
                    .collect(Collectors.toMap(SkuEntity::getSkuId, Function.identity()));
        }

        // 构建错误信息
        StringBuilder message = new StringBuilder("BOM存在相同子物料：");

        for (int i = 0; i < duplicateGroups.size(); i++) {
            List<BomMaterialImportDTO> group = duplicateGroups.get(i);
            BomMaterialImportDTO firstDto = group.get(0);

            // 获取SKU信息
            String skuName = "";
            if (firstDto.getSkuId() != null && firstDto.getSkuId() != 0) {
                SkuEntity sku = skuMap.get(firstDto.getSkuId());
                skuName = sku != null ? sku.getSkuName() : "";
            }

            // 构建单个重复物料的信息
            if (i > 0) {
                message.append("；");
            }
            message.append("物料编码：").append(firstDto.getCode());
            if (StringUtils.isNotBlank(skuName)) {
                message.append("，SKU名称：").append(skuName);
            }
        }

        return message.toString();
    }

    /**
     * 当无法找到明确重复组时的备用消息构建方法（导入版本）
     *
     * @param bomImportDTOSonMaterialList BOM导入DTO子物料列表
     * @return 备用错误信息
     */
    private String buildFallbackDuplicateMessageForImport(List<BomMaterialImportDTO> bomImportDTOSonMaterialList) {
        // 收集所有物料编码进行分析
        Set<String> materialCodes = bomImportDTOSonMaterialList.stream()
                .map(BomMaterialImportDTO::getCode)
                .collect(Collectors.toSet());

        Set<Integer> skuIds = bomImportDTOSonMaterialList.stream()
                .map(BomMaterialImportDTO::getSkuId)
                .filter(Objects::nonNull)
                .filter(id -> id != 0)
                .collect(Collectors.toSet());

        // 批量查询SKU信息
        Map<Integer, SkuEntity> skuMap = new HashMap<>();
        if (!skuIds.isEmpty()) {
            skuMap = skuService.getSkusByIds(skuIds)
                    .stream()
                    .collect(Collectors.toMap(SkuEntity::getSkuId, Function.identity()));
        }

        StringBuilder message = new StringBuilder("BOM存在相同子物料，涉及物料编码：");
        message.append(String.join("、", materialCodes));

        // 构建SKU信息
        if (!skuIds.isEmpty()) {
            List<String> skuInfos = new ArrayList<>();
            for (Integer skuId : skuIds) {
                SkuEntity sku = skuMap.get(skuId);
                String skuName = sku != null ? sku.getSkuName() : "未知SKU";
                skuInfos.add(skuName);
            }
            message.append("，涉及SKU名称：").append(String.join("、", skuInfos));
        }

        return message.toString();
    }

    @Override
    public BomEntity selectById(Integer id) {
        // 根据id查询
        BomEntity bomEntity = this.getById(id);
        if (Objects.isNull(bomEntity)) {
            return null;
        }
        // 如果查询对象为成品/半成品，查询所绑定的原料
        List<BomRawMaterialEntity> list = bomRawMaterialService.getListByBomId(bomEntity.getId());
        // 获取物料列表
        List<MaterialCodeAndSkuIdSelectDTO> codes = list.stream()
                .map(materielLine -> MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(materielLine.getCode())
                        .skuId(materielLine.getSkuId()).build())
                .collect(Collectors.toList());
        Map<String, MaterialEntity> codeMaterialMap = materialService.getMaterialEntity(codes);
        for (BomRawMaterialEntity bomRawMaterialEntity : list) {
            MaterialEntity materialEntity = codeMaterialMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId()));
            bomRawMaterialEntity.setMaterialFields(materialEntity);
            List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = replaceBomMaterialService.listByBomMaterialId(bomRawMaterialEntity.getId());
            if (CollectionUtils.isEmpty(replaceBomMaterialEntities)) {
                continue;
            }
            // 查询物料替代方案列表
            Set<Integer> collect = replaceBomMaterialEntities.stream().map(ReplaceBomMaterialEntity::getReplaceMaterialId).collect(Collectors.toSet());
            bomRawMaterialEntity.setReplaceMaterialId(Joiner.on(Constant.SEP).join(collect));
        }
        bomEntity.setBomRawMaterialEntities(list);
        //获取物料附件
        List<AppendixEntity> appendixEntities = appendixService.lambdaQuery()
                .eq(AppendixEntity::getRelateId, bomEntity.getId())
                .eq(AppendixEntity::getType, AppendixTypeEnum.BOM_APPENDIX.getCode())
                .list();
        bomEntity.setAppendixEntities(appendixEntities);
        // 获取人名和状态名称
        List<BomEntity> bomEntities = new ArrayList<>();
        bomEntities.add(bomEntity);
        showName(bomEntities);
        buildLastVersion(bomEntity);
        return bomEntity;
    }

    /**
     * 查询物料替代方案列表
     */
    private void getReplaceSchemeList(Set<Integer> idList, List<ReplaceSchemeVO> schemeVOS) {
        List<ReplaceSchemeMaterialEntity> schemeMaterialEntities = replaceSchemeMaterialService.listByIds(idList);
        // 查询替代方案
        List<String> schemeCodes = schemeMaterialEntities.stream().map(ReplaceSchemeMaterialEntity::getSchemeCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return;
        }
        Map<String, ReplaceSchemeEntity> map = replaceSchemeService.lambdaQuery()
                .in(ReplaceSchemeEntity::getSchemeCode, schemeCodes).list()
                .stream()
                .collect(Collectors.toMap(ReplaceSchemeEntity::getSchemeCode, o -> o));
        // 填充数据
        for (ReplaceSchemeMaterialEntity schemeMaterialEntity : schemeMaterialEntities) {
            ReplaceSchemeEntity replaceSchemeEntity = map.get(schemeMaterialEntity.getSchemeCode());
            schemeVOS.add(
                    ReplaceSchemeVO.builder()
                            .schemeCode(replaceSchemeEntity.getSchemeCode())
                            .schemeName(replaceSchemeEntity.getSchemeName())
                            .mainMaterialCode(replaceSchemeEntity.getMainMaterialCode())
                            .mainDenominator(replaceSchemeEntity.getMainDenominator())
                            .mainMolecule(replaceSchemeEntity.getMainMolecule())
                            .mainMaterialFields(materialService.getEntityByCodeAndSkuIdAndShowBom(replaceSchemeEntity.getMainMaterialCode(), replaceSchemeEntity.getSkuId()))
                            .replaceMaterialCode(schemeMaterialEntity.getReplaceMaterialCode())
                            .replaceDenominator(schemeMaterialEntity.getReplaceDenominator())
                            .replaceMolecule(schemeMaterialEntity.getReplaceMolecule())
                            .replaceMaterialFields(materialService.getEntityByCodeAndSkuIdAndShowBom(schemeMaterialEntity.getReplaceMaterialCode(), schemeMaterialEntity.getSkuId()))
                            .build()
            );
        }
    }

    /**
     * 获取中文名称
     */
    private void showName(List<BomEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<SysUserEntity> sysUserEntities = sysUserService.selectList();
        Map<String, String> nickNames = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        Map<String, String> signatureUrlMap = sysUserService.getSignatureUrlMap(null);
        List<MaterialCodeAndSkuIdSelectDTO> materialCodes = list.stream()
                .filter(entity -> StringUtils.isNotBlank(entity.getCode()))
                .map(entity -> MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(entity.getCode())
                        .skuId(entity.getSkuId()).build())
                .collect(Collectors.toList());
        Map<String, MaterialEntity> codeMaterialMap = materialService.getMaterialEntity(materialCodes);
        for (BomEntity entity : list) {
            // 获取状态名称
            entity.setStateName(BomStateEnum.getNameByCode(entity.getState()));
            entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
            // 获取人名
            entity.setApproverName(nickNames.getOrDefault(entity.getApprover(), entity.getApprover()));
            entity.setActualApproverName(nickNames.getOrDefault(entity.getActualApprover(), entity.getActualApprover()));
            entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
            entity.setCreateByNickname(nickNames.getOrDefault(entity.getCreateBy(), entity.getCreateBy()));
            entity.setUpdateByNickname(nickNames.getOrDefault(entity.getUpdateBy(), entity.getUpdateBy()));
            entity.setEditorName(nickNames.getOrDefault(entity.getEditor(), entity.getEditor()));
            //获取物料
            entity.setMaterialFields(StringUtils.isNotBlank(entity.getCode()) ? codeMaterialMap.get(ColumnUtil.getMaterialSku(entity.getCode(), entity.getSkuId())) : null);
            // 获取sku名称
            entity.setSkuEntity(skuService.getById(entity.getSkuId()));
            entity.setProductionStateName(ProductionStateEnum.getNameByCode(entity.getProductionState()));
            buildLastVersion(entity);
        }
    }


    @Override
    public List<MaterialRelateDataDTO> getRelateData(String bomNum) {
        List<MaterialRelateDataDTO> list = new ArrayList<>();
        // 查询生产领料单，根据领料单查询对应的工单
        LambdaQueryWrapper<TakeOutApplicationEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(TakeOutApplicationEntity::getBomNum, bomNum);
        List<TakeOutApplicationEntity> takeOutApplicationEntities = takeOutApplicationMapper.selectList(qw);
        takeOutApplicationEntities.forEach(takeOutApplicationEntity -> {
            WorkOrderEntity orderEntity = workOrderMapper.getByWorkOrderNumber(takeOutApplicationEntity.getWorkOrderNumber());
            if (Objects.isNull(orderEntity)) {
                throw new ResponseException(RespCodeEnum.WORK_ORDER_SELECT_FAIL.fmtDes(takeOutApplicationEntity.getWorkOrderNumber()));
            }
            MaterialRelateDataDTO materialRelateDataDTO = MaterialRelateDataDTO.builder()
                    .number(orderEntity.getWorkOrderNumber())
                    .state(orderEntity.getState())
                    .type("工单")
                    .build();
            materialRelateDataDTO.setStateName(WorkOrderStateEnum.getNameByCode(materialRelateDataDTO.getState()));
            list.add(materialRelateDataDTO);
        });
        // 去重
        return list.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public BomEntity getLatestBomByCode(String code, Integer skuId) {
        return this.getLatestBom(code, skuId, BomStateEnum.RELEASED.getCode());
    }

    @Override
    public BomEntity getLatestSimpleBom(String code, Integer skuId) {
        skuId = Objects.isNull(skuId) ? 0 : skuId;
        BomEntity one = this.lambdaQuery().eq(BomEntity::getCode, code)
                .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                .eq(BomEntity::getIsTemplate, Constants.FALSE)
                .eq(true, BomEntity::getSkuId, skuId)
                .orderByDesc(BomEntity::getCreateTime)
                .orderByDesc(BomEntity::getId).last("limit 1").one();
        if (Objects.isNull(one)) {
            one = this.lambdaQuery().eq(BomEntity::getCode, code)
                    .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                    .eq(BomEntity::getIsTemplate, Constants.FALSE)
                    .eq(BomEntity::getSkuId, 0)
                    .orderByDesc(BomEntity::getCreateTime)
                    .orderByDesc(BomEntity::getId).last("limit 1").one();
        }
        return one;
    }

    private BomEntity getLatestBom(String code, Integer skuId, Integer state) {
        skuId = Objects.isNull(skuId) ? 0 : skuId;
        BomEntity bomEntity = this.lambdaQuery().eq(BomEntity::getCode, code)
                .eq(state != null, BomEntity::getState, state)
                .eq(BomEntity::getIsTemplate, Constants.FALSE)
                // 历史数据默认查询量产BOM
                .eq(BomEntity::getProductionState, ProductionStateEnum.MASS_PRODUCTION.getCode())
                .eq(BomEntity::getSkuId, skuId)
                .orderByDesc(BomEntity::getCreateTime)
                .orderByDesc(BomEntity::getId).last("limit 1").one();
        if (Objects.isNull(bomEntity)) {
            bomEntity = this.lambdaQuery().eq(BomEntity::getCode, code)
                    .eq(state != null, BomEntity::getState, state)
                    .eq(BomEntity::getIsTemplate, Constants.FALSE)
                    // 历史数据默认查询量产BOM
                    .eq(BomEntity::getProductionState, ProductionStateEnum.MASS_PRODUCTION.getCode())
                    .eq(BomEntity::getSkuId, 0)
                    .orderByDesc(BomEntity::getCreateTime)
                    .orderByDesc(BomEntity::getId).last("limit 1").one();
            if (Objects.isNull(bomEntity)) {
                return null;
            }
        }
        bomEntity.setMaterialFields(materialService.getEntityByCodeAndSkuIdAndShowBom(code, bomEntity.getSkuId()));
        buildLastVersion(bomEntity);

        // 获取过滤联产品和副产品后的bom原料信息
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.getListByBomIdFilterOutputProduct(bomEntity.getId());
        // 获取物料相关字段
        List<MaterialCodeAndSkuIdSelectDTO> codeAndSkuIds = bomRawMaterialEntities.stream()
                .map(res -> MaterialCodeAndSkuIdSelectDTO.builder().materialCode(res.getCode()).skuId(res.getSkuId()).build())
                .collect(Collectors.toList());
        List<MaterialEntity> materials = materialService.listSimpleMaterialByCodesAndSkuIds(codeAndSkuIds);
        Map<String, MaterialEntity> keyMaterialMap = materials.stream().collect(Collectors.toMap(material -> ColumnUtil.getMaterialSku(material.getCode(), material.getSkuEntity()), v -> v));
        // 查询物料当前库存
        List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
        bomRawMaterialEntities.forEach(o -> materialDetails.add(StockMaterialDetailEntity.builder().productCode(o.getCode()).skuId(Constants.SKU_ID_DEFAULT_VAL).build()));
        Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(stockInventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new com.alibaba.fastjson.TypeReference<Map<String, BigDecimal>>() {
        }, null);
        materialCodeStockMap = materialCodeStockMap == null ? new HashMap<>() : materialCodeStockMap;
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            MaterialEntity materialEntity = keyMaterialMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId()));
            bomRawMaterialEntity.setMaterialFields(materialEntity);
            // 设置用量
            Double useNum = MathUtil.divideDouble(bomRawMaterialEntity.getNum().doubleValue(), bomRawMaterialEntity.getNumber().doubleValue(), 1);
            bomRawMaterialEntity.setUseNum(useNum);
            // 展示子项类型名称
            bomRawMaterialEntity.setBomTypeName(bomRawMaterialEntity.getBomType() != null ? BomItemTypeEnum.getNameByCode(Integer.valueOf(bomRawMaterialEntity.getBomType())) : null);
            List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = replaceBomMaterialService.listByBomMaterialId(bomRawMaterialEntity.getId());
            if (CollectionUtils.isNotEmpty(replaceBomMaterialEntities)) {
                // 查询物料替代方案列表
                Set<Integer> collect = replaceBomMaterialEntities.stream().map(ReplaceBomMaterialEntity::getReplaceMaterialId).collect(Collectors.toSet());
                bomRawMaterialEntity.setReplaceMaterialId(Joiner.on(Constant.SEP).join(collect));
            }
            // 查询物料当前库存
            bomRawMaterialEntity.setStockQuantity(materialCodeStockMap.getOrDefault(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), Constants.SKU_ID_DEFAULT_VAL), BigDecimal.ZERO).doubleValue());
        }
        // 根据bomId排序，目的是获取标准BOM
        bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities.stream()
                .sorted(Comparator.comparing(BomRawMaterialEntity::getBomId)).collect(Collectors.toList()));
        return bomEntity;
    }

    private void buildLastVersion(BomEntity bomEntity) {
        // 获取最大修订次数
        Integer latestVersionRevision = versionChangeRecordService.getLatestVersionRevision(VersionChangeRecordSelectDTO
                .builder()
                .relateType(VersionModelIdEnum.BOM)
                .relateId(bomEntity.getId()).build());
        bomEntity.setVersionRevision(latestVersionRevision);
    }

    @Override
    public BomEntity selectNewByProductCode(String code, Integer skuId) {
        // 获取启用状态下最新的bom
        LambdaQueryWrapper<BomEntity> bomWrapper = new LambdaQueryWrapper<>();
        bomWrapper.eq(BomEntity::getCode, code).eq(BomEntity::getIsTemplate, Constants.FALSE)
                .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                .eq(skuId != null, BomEntity::getSkuId, skuId)
                .orderByDesc(BomEntity::getCreateTime)
                .orderByDesc(BomEntity::getId).last("limit 1");
        BomEntity bomEntity = this.getOne(bomWrapper);
        if (Objects.nonNull(bomEntity)) {
            List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.getListByBomId(bomEntity.getId());
            bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities);
        }
        return bomEntity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateState(Integer state, Integer id, String username) {
        BomEntity bomEntity = this.getById(id);
        LambdaUpdateWrapper<BomEntity> uw = new LambdaUpdateWrapper<>();
        // 只更新状态
        uw.eq(BomEntity::getId, bomEntity.getId())
                .set(BomEntity::getState, state);
        boolean ret = this.update(uw);
        // 处理历史相同物料编码的BOM
        refreshReleasedBom(Collections.singletonList(bomEntity.getId()));
        // 物料有无生效状态的bom 事件
        if (Boolean.FALSE.equals(bomEntity.getIsTemplate())) {
            applicationContext.publishEvent(new MaterialHaveReleasedStateBomEvent(this, bomEntity));
        }
        return ret;
    }

    /**
     * 上传变更记录
     */
    @Override
    public void initVersionLog(BomEntity bomEntity) {
        versionChangeRecordService.changeVersion(VersionChangeDTO.builder()
                .relateType(VersionModelIdEnum.BOM)
                .relateId(bomEntity.getId())
                .versionCurrent(bomEntity.getVersion())
                .editor(bomEntity.getUpdateBy())
                .description("BOM -- 新增")
                .build());
    }

    /**
     * 上传变更记录
     */
    @Override
    public void changeVersionLog(BomEntity bomEntity) {
        versionChangeRecordService.changeVersion(VersionChangeDTO.builder()
                .relateType(VersionModelIdEnum.BOM)
                .relateId(bomEntity.getId())
                .versionCurrent(bomEntity.getVersion())
                .editor(bomEntity.getUpdateBy())
                .description("BOM -- 修改")
                .build());
    }

    @Override
    public List<BomRawMaterialEntity> getLatestBomInCache(Map<BomKeyDTO, BomEntity> bomMap, BomKeyDTO dto) {
        return this.getLatestBomInCache(bomMap, dto, BomStateEnum.RELEASED.getCode());
    }

    private List<BomRawMaterialEntity> getLatestBomInCache(Map<BomKeyDTO, BomEntity> bomMap, BomKeyDTO dto, Integer state) {
        List<BomRawMaterialEntity> bomRawMaterialList = new ArrayList<>();
        // 获取物料关联的最新的bom
        BomEntity bom = bomMap.get(dto);
        if (bom == null) {
            bom = this.getLatestBom(dto.getMaterialCode(), dto.getSkuId(), state);
            if (Objects.isNull(bom)) {
                return bomRawMaterialList;
            }
            bomMap.put(dto, bom);
        }
        return bom.getBomRawMaterialEntities() == null ? new ArrayList<>() : bom.getBomRawMaterialEntities();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username) {
        if (id == null) {
            return;
        }
        BomEntity entity = this.getById(id);
        if (entity == null || entity.getState() == null) {
            return;
        }
        Integer preApprovalStatus = entity.getApprovalStatus();
        if (preApprovalStatus == null) {
            preApprovalStatus = entity.getState().equals(BomStateEnum.CREATE.getCode()) ?
                    ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.REJECTED.getCode();
        }
        boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();

        boolean toReject = approvalStatus == ApprovalStatusEnum.REJECTED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        if (toApproved || toReject) {
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(approvalSuggestion);
            entity.setActualApprover(username);
            entity.setApprovalTime(new Date());
            if (toApproved) {
                entity.setState(BomStateEnum.RELEASED.getCode());
            }
            this.updateById(entity);
            // 处理历史相同物料编码的BOM
            refreshReleasedBom(Collections.singletonList(entity.getId()));
            if (Boolean.FALSE.equals(entity.getIsTemplate())) {
                // 物料有无生效状态的bom 事件
                applicationContext.publishEvent(new MaterialHaveReleasedStateBomEvent(this, entity));
                // 推送消息
                messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_APPROVAL_STATUS_CHANGE_MESSAGE);
            } else {
                // 推送消息
                messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_TEMPLATE_APPROVAL_STATUS_CHANGE_MESSAGE);
            }
            sendApproveMsg(entity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approveBatch(ApproveBatchDTO dto) {
        List<Integer> ids = dto.getIds();
        Integer approvalStatus = dto.getApprovalStatus();
        if (CollectionUtils.isEmpty(ids) || approvalStatus == null) {
            return;
        }
        List<BomEntity> entities = this.listByIds(ids);
        List<String> approvers = entities.stream().map(BomEntity::getApprover).distinct().collect(Collectors.toList());
        approvers.remove(dto.getActualApprover());
        if (CollectionUtils.isNotEmpty(approvers)) {
            throw new ResponseException(RespCodeEnum.CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN);
        }
        List<BomEntity> list = entities.stream().filter(o -> BomStateEnum.CREATE.getCode() == o.getState())
                .peek(o -> {
                    if (o.getApprovalStatus() == null) {
                        o.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    }
                })
                .filter(o -> o.getApprovalStatus().equals(ApprovalStatusEnum.TO_BE_APPROVAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Date date = new Date();
        for (BomEntity entity : list) {
            Integer preApprovalStatus = entity.getApprovalStatus();
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(dto.getApprovalSuggestion());
            entity.setActualApprover(dto.getActualApprover());
            entity.setApprovalTime(date);

            boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                    && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
            if (toApproved) {
                entity.setState(BomStateEnum.RELEASED.getCode());
            }
        }
        this.updateBatchById(list);
        // 处理历史相同物料编码的BOM
        List<Integer> bomIds = list.stream().map(BomEntity::getId).collect(Collectors.toList());
        refreshReleasedBom(bomIds);
        list.forEach(entity -> {
            if (Boolean.FALSE.equals(entity.getIsTemplate())) {
                // 物料有无生效状态的bom 事件
                applicationContext.publishEvent(new MaterialHaveReleasedStateBomEvent(this, entity));
                // 推送消息
                messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_APPROVAL_STATUS_CHANGE_MESSAGE);
            } else {
                // 推送消息
                messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_TEMPLATE_APPROVAL_STATUS_CHANGE_MESSAGE);
            }
            sendApproveMsg(entity);
        });
    }

    private void sendApproveMsg(BomEntity entity) {
        NoticeConfigBuilder noticeConfigBuilder = NoticeConfigBuilder.builder()
                .sendUsername(entity.getActualApprover())
                .bomNum(entity.getBomNum())
                .materialCode(entity.getCode())
                .approvalTime(DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT))
                .approvalStateName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()))
                .noticeType(NoticeTypeEnum.APPROVE_BOM_NOTICE.getCode())
                .redisKey(NoticeTypeEnum.APPROVE_BOM_NOTICE.getCode() + entity.getCode() + DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT))
                .build();
        infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
    }

    @Override
    public List<BomRawMaterialEntity> getMultiLevelBom(String materialCode, Integer skuId, String bomCode, String productionState) {
        // 兼容历史逻辑，不填默认为量产
        if (StringUtils.isBlank(productionState)) {
            productionState = ProductionStateEnum.MASS_PRODUCTION.getCode();
        }
        // 记录bom循环的物料编码链路
        List<String> traceList = new ArrayList<>();
        traceList.add(materialCode);
        List<BomRawMaterialEntity> bomRawMaterialEntities;
        if (StringUtils.isNotBlank(bomCode)) {
            BomEntity bomEntity = getBomDetailByBomCode(bomCode);
            if (Objects.isNull(bomEntity)) {
                return new ArrayList<>();
            }
            bomRawMaterialEntities = bomEntity.getBomRawMaterialEntities();
        } else {
            // 获取生效状态、创建时间最新的bom
            // 第一次BOM需要根据生产状态进行数据过滤
            BomMaterialSelectDTO bomMaterialSelectDTO = BomMaterialSelectDTO.builder()
                    // 试产状态则查询试产/量产最新的BOM
                    .productionState(ProductionStateEnum.TEST_PRODUCTION.getCode().equals(productionState) ? null : productionState)
                    .materialCode(materialCode)
                    .skuId(skuId)
                    .build();
            BomEntity bomEntity = getNewBom(bomMaterialSelectDTO);
            if (Objects.isNull(bomEntity)) {
                return new ArrayList<>();
            }
            bomRawMaterialEntities = bomEntity.getBomRawMaterialEntities();
        }
        if (CollectionUtils.isEmpty(bomRawMaterialEntities)) {
            return new ArrayList<>();
        }
        List<BomRawMaterialEntity> list = new ArrayList<>();
        for (BomRawMaterialEntity materialEntity : bomRawMaterialEntities) {
            // 添加链路记录
            traceList.add(materialEntity.getCode());
            Rational coefficient = new Rational(materialEntity.getNum().doubleValue(), materialEntity.getNumber().doubleValue());
            materialEntity.setWorkOrderMaterialQuantity(BigDecimal.valueOf(coefficient.doubleValue()));
            list.add(materialEntity);
            // 获取下一级bom
            setSubLevelBom(materialEntity, coefficient, materialCode, traceList, productionState);
            // 该链路不循环，去除记录
            traceList.remove(materialEntity.getCode());
        }
        // 填充物料对象
        List<MaterialEntity> materialFields = materialService.listSimpleMaterialByCodesAndSkuIds(list.stream().map(e ->
                MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(e.getCode())
                        .skuId(e.getSkuId())
                        .build()
        ).collect(Collectors.toList()));
        Map<String, MaterialEntity> materialSkuMap = materialFields.stream()
                .collect(Collectors.toMap(e -> ColumnUtil.getMaterialSku(e.getCode(), e.getSkuEntity()), Function.identity(), (v1, v2) -> v2));
        for (BomRawMaterialEntity bomRawMaterialEntity : list) {
            bomRawMaterialEntity.setMaterialFields(materialSkuMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId())));
        }
        return list;
    }

    /**
     * 获取下一级bom
     */
    private void setSubLevelBom(BomRawMaterialEntity materialEntity, Rational materialQuantity,
                                String materialCode, List<String> traceList, String productionState) {
        // 多级BOM如果顶级的生产状态为量产，则获取量产最新BOM，如果为试产，则获取量产/试产的最新BOM
        BomMaterialSelectDTO bomMaterialSelectDTO = BomMaterialSelectDTO.builder()
                .productionState(productionState.equals(ProductionStateEnum.TEST_PRODUCTION.getCode()) ? null : ProductionStateEnum.MASS_PRODUCTION.getCode())
                .materialCode(materialEntity.getCode())
                .skuId(materialEntity.getSkuId())
                .build();
        BomEntity bomEntity = getNewBom(bomMaterialSelectDTO);
        if (Objects.isNull(bomEntity) || CollectionUtils.isEmpty(bomEntity.getBomRawMaterialEntities())) {
            return;
        }
        List<BomRawMaterialEntity> list = new ArrayList<>();
        for (BomRawMaterialEntity entity : bomEntity.getBomRawMaterialEntities()) {
            // 添加链路记录
            traceList.add(entity.getCode());
            if (materialCode.equals(entity.getCode())) {
                log.error("bom循环，循环链为：" + traceList);
                throw new ResponseException("bom循环");
            }
            Rational coefficient = new Rational(entity.getNum().doubleValue(), entity.getNumber().doubleValue());
            Rational subMaterialQuantity = materialQuantity.mulitiply(coefficient);
            entity.setWorkOrderMaterialQuantity(BigDecimal.valueOf(subMaterialQuantity.doubleValue()));
            list.add(entity);
            // 获取下一级bom recursive call
            setSubLevelBom(entity, subMaterialQuantity, materialCode, traceList, productionState);
            // 该链路不循环，去除记录
            traceList.remove(entity.getCode());
        }
        materialEntity.setChildren(list);
    }

    @Override
    public List<BomEntity> getListByMaterialCode(String materialCode) {
        return this.getListByCodeAndSkuId(materialCode, null, ProductionStateEnum.MASS_PRODUCTION.getCode());
    }

    @Override
    public List<BomEntity> getListByCodeAndSkuId(String materialCode, Integer skuId, String productionState) {
        LambdaQueryWrapper<BomEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BomEntity::getCode, materialCode)
                .eq(StringUtils.isNotBlank(productionState), BomEntity::getProductionState, productionState)
                .eq(BomEntity::getIsTemplate, Constants.FALSE)
                .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                .eq(skuId != null, BomEntity::getSkuId, skuId);
        return this.list(queryWrapper);
    }

    @Override
    public List<BomRawMaterialReturnDTO> getNextBomByCode(BomMaterialSelectDTO bomMaterialSelectDTO) {
        List<BomRawMaterialReturnDTO> list = new ArrayList<>();
        Map<BomKeyDTO, BomEntity> bomMap = new HashMap<>();
        String materialCode = bomMaterialSelectDTO.getMaterialCode();
        Integer skuId = bomMaterialSelectDTO.getSkuId();
        List<BomRawMaterialEntity> bomRawMaterialEntities;
        if (StringUtils.isNotBlank(bomMaterialSelectDTO.getBomCode())) {
            BomEntity bomEntity = getBomDetailByBomCode(bomMaterialSelectDTO.getBomCode());
            bomRawMaterialEntities = bomEntity.getBomRawMaterialEntities();
        } else {
            // 获取生效状态、创建时间最新的bom
            bomRawMaterialEntities = getLatestBomInCache(bomMap, new BomKeyDTO(materialCode, skuId));
        }
        if (CollectionUtils.isEmpty(bomRawMaterialEntities)) {
            return list;
        }
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            Rational rational = new Rational(bomRawMaterialEntity.getNum().doubleValue(), bomRawMaterialEntity.getNumber().doubleValue());
            bomRawMaterialEntity.setRational(RationalDTO.builder().numerator(rational.getNumerator()).denominator(rational.getDenominator()).build());
        }
        List<BomRawMaterialReturnDTO> returnDTOS = JacksonUtil.convertArray(bomRawMaterialEntities, BomRawMaterialReturnDTO.class);
        list.addAll(returnDTOS);
        Map<String, MaterialEntity> materialMap = new HashMap<>();
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            // 获取下一级bom
            getNextBomRawMaterial(bomRawMaterialEntity, materialCode, bomMap, materialMap, list);
        }
        return list;
    }

    private void getNextBomRawMaterial(BomRawMaterialEntity materialEntity, String materialCode, Map<BomKeyDTO, BomEntity> bomMap,
                                       Map<String, MaterialEntity> materialMap, List<BomRawMaterialReturnDTO> list) {
        BomKeyDTO dto = new BomKeyDTO(materialEntity.getCode(), materialEntity.getSkuId());
        List<BomRawMaterialEntity> bomRawMaterialEntities = getLatestBomInCache(bomMap, dto);
        if (CollectionUtils.isEmpty(bomRawMaterialEntities)) {
            return;
        }
        for (BomRawMaterialEntity entity : bomRawMaterialEntities) {
            if (materialCode.equals(entity.getCode())) {
                throw new ResponseException("bom循环");
            }
            MaterialEntity material = materialService.getMaterialBySkuInCache(materialMap, entity.getCode(), entity.getSkuId());
            entity.setMaterialFields(material);
            Rational rational = new Rational(materialEntity.getNum().doubleValue(), materialEntity.getNumber().doubleValue());
            entity.calRational(rational);
        }
        List<BomRawMaterialReturnDTO> returnDTOS = JacksonUtil.convertArray(bomRawMaterialEntities, BomRawMaterialReturnDTO.class);
        list.addAll(returnDTOS);
        for (BomRawMaterialEntity entity : bomRawMaterialEntities) {
            // 获取下一级bom
            getNextBomRawMaterial(entity, materialCode, bomMap, materialMap, list);
        }
    }

    @Override
    @Async
    public void tileBom(List<BomEntity> bomList) {
        // 过滤获取类型为生产品的bom，如果相同的则取第一条，第一条为标准BOM
        List<MaterialEntity> materials = materialService.getMaterialsByCodes(bomList.stream().map(BomEntity::getCode).collect(Collectors.toSet()));
        List<String> strings = materials.stream().filter(o -> o.getSort().equals(MaterialSortEnum.PRODUCT.getCode()))
                .map(MaterialEntity::getCode).collect(Collectors.toList());
        Map<String, BomEntity> productBomList = bomList.stream().filter(o -> strings.contains(o.getCode()))
                .collect(Collectors.toMap(BomEntity::getCode, o -> o, (o1, o2) -> o1.getId() < o2.getId() ? o1 : o2));
        List<BomEntity> bomEntities = new ArrayList<>(productBomList.values());
        // 拆解并平铺bom
        int progress = 0;
        for (BomEntity entity : bomEntities) {
            progress += 1;
            // 无论新增还是更新， 都删除后新增
            bomTileService.removeByBomMaterialCode(entity.getCode());
            List<BomTileEntity> tailList = new ArrayList<>();
            int bomLayer = 0;
            splitAndTailBom(tailList, bomLayer, entity.getCode(), entity.getId());
            // 批量保存
            bomTileService.saveBatch(tailList);
            log.info("平铺BOM：，{}，成功。总计，{}，已处理，{}", entity.getCode(), bomEntities.size(), progress);
        }
    }


    /**
     * 拆解并平铺bom（限制只处理10层，避免嵌套循环）
     */
    private void splitAndTailBom(List<BomTileEntity> tileList, Integer bomLayer, String materialCode, Integer bomId) {
        if (bomLayer > BOM_LAYER_MAX_NUM) {
            return;
        }
        List<BomRawMaterialEntity> bomRawEntities = bomRawMaterialService.getListByBomId(bomId);
        if (CollectionUtils.isNotEmpty(bomRawEntities)) {
            // 同一层只加一次
            bomLayer += 1;
            // 平铺
            for (BomRawMaterialEntity bomRawEntity : bomRawEntities) {
                // 可能存在不同bom下有相同的物料编码，如果存在则返回
                LambdaQueryWrapper<BomTileEntity> bomTailWrapper = new LambdaQueryWrapper<>();
                bomTailWrapper.eq(BomTileEntity::getBomMaterialCode, materialCode)
                        .eq(BomTileEntity::getMaterialCode, bomRawEntity.getCode());
                if (bomTileService.count(bomTailWrapper) > 0) {
                    continue;
                }
                tileList.add(BomTileEntity.builder().bomMaterialCode(materialCode).materialCode(bomRawEntity.getCode()).build());
                // 如果该物料下层还存在物料，则往下层拆
                LambdaQueryWrapper<BomEntity> bomWrapper = new LambdaQueryWrapper<>();
                bomWrapper.eq(BomEntity::getCode, bomRawEntity.getCode()).orderByAsc(BomEntity::getId);
                List<BomEntity> bomEntities = this.list(bomWrapper);
                if (CollectionUtils.isEmpty(bomEntities)) {
                    continue;
                }
                splitAndTailBom(tileList, bomLayer, materialCode, bomEntities.get(0).getId());
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer copyBom(BomCopyDTO bomCopyDTO, String name) {
        if (StringUtils.isBlank(bomCopyDTO.getBomCode())) {
            throw new ResponseException(RespCodeEnum.BOM_NUMBER_IS_EMPTY);
        }
        BomEntity bomEntity = this.selectById(bomCopyDTO.getId());
        LambdaQueryWrapper<BomEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BomEntity::getBomNum, bomCopyDTO.getBomCode())
                .eq(BomEntity::getIsTemplate, Constants.FALSE);
        if (count(queryWrapper) > 0) {
            throw new ResponseException(RespCodeEnum.BOM_NUM_DUPLICATE);
        }
        bomEntity.setId(null);
        // 设置为创建
        bomEntity.setState(BomStateEnum.CREATE.getCode());
        // 如果需要审批，则缺省为待审核状态
        if (Boolean.TRUE.equals(approveConfigService.getConfigByCode(ApproveModuleEnum.BOM.getCode()))) {
            bomEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        bomEntity.setBomNum(bomCopyDTO.getBomCode());
        bomEntity.setCreateTime(new Date());
        bomEntity.setUpdateTime(new Date());
        bomEntity.setCreateBy(name);
        bomEntity.setUpdateBy(name);
        bomEntity.setIsTemplate(Constants.FALSE);
        this.save(bomEntity);
        List<BomRawMaterialEntity> bomRawMaterialEntities = new ArrayList<>();
        // 重新绑定物料关系及倒排数据
        for (BomRawMaterialEntity bomRawMaterialEntity : bomEntity.getBomRawMaterialEntities()) {
            bomRawMaterialEntity.setId(null);
            bomRawMaterialEntity.setBomId(bomEntity.getId());
            bomRawMaterialEntities.add(bomRawMaterialEntity);
        }
        bomRawMaterialService.saveBatch(bomRawMaterialEntities);
        // 版本变更记录
        initVersionLog(bomEntity);
        return bomEntity.getId();
    }

    /**
     * 通过接口同步Bom信息
     *
     * @param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sysnchBoms(BomOpenEntityDTO bomOpenEntityDTO, String name) {
        MaterialEntity one = materialService.getSimpleMaterialByCode(bomOpenEntityDTO.getCode());
        if (Objects.isNull(one)) {
            throw new ResponseException(RespCodeEnum.SYNC_BOM_IS_MATERIAL_EXIST);
        }
        BomEntity bomEntity = new BomEntity();
        BeanUtils.copyProperties(bomOpenEntityDTO, bomEntity);
        bomEntity.setCreateTime(new Date());
        bomEntity.setUpdateTime(new Date());
        bomEntity.setCreateBy(name);
        bomEntity.setUpdateBy(name);
        bomEntity.setIsTemplate(Constants.FALSE);
        bomEntity.initReleaseTime();
        this.save(bomEntity);
        List<BomRawMaterialEntity> bomRawMaterialEntities = new ArrayList<>();
        for (BomRawMaterialOpenEntityDTO bomRawMaterial : bomOpenEntityDTO.getBomRawMaterialEntities()) {
            MaterialEntity material = materialService.getSimpleMaterialByCode(bomRawMaterial.getCode());
            if (Objects.isNull(material)) {
                throw new ResponseException(RespCodeEnum.SYNC_BOM_IS_MATERIAL_EXIST);
            }
            BomRawMaterialEntity bomRawMaterialEntity = new BomRawMaterialEntity();
            BeanUtils.copyProperties(bomRawMaterial, bomRawMaterialEntity);
            bomRawMaterialEntity.setBomId(bomEntity.getId());
            bomRawMaterialService.save(bomRawMaterialEntity);
            bomRawMaterialEntities.add(bomRawMaterialEntity);
        }
        // 推送消息
        bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities);
        messagePushToKafkaService.pushNewMessage(bomEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.BOM_ADD_MESSAGE);
        return true;
    }

    @Override
    public BomEntity getNewBom(BomMaterialSelectDTO bomMaterialSelectDTO) {
        int skuId = bomMaterialSelectDTO.getSkuId() == null ? 0 : bomMaterialSelectDTO.getSkuId();
        String materialCode = bomMaterialSelectDTO.getMaterialCode();

        LambdaQueryWrapper<BomEntity> wrapper = new LambdaQueryWrapper<>();
        // 如果传了bomCode，则不需要做此判断
        if (StringUtils.isBlank(bomMaterialSelectDTO.getBomCode())) {
            // 判断是否为最父级的BOM。为是的BOM查询原则：试产的生产状态查询试产的BOM,量产的生产状态查询量产的BOM。
            // 为否的BOM查询原则：试产的生产状态查询试产/量产的BOM,量产的生产状态查询量产的BOM。
            wrapper.eq(StringUtils.isNotBlank(bomMaterialSelectDTO.getProductionState()), BomEntity::getProductionState, bomMaterialSelectDTO.getProductionState());
        }
        wrapper.eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                .eq(StringUtils.isNotBlank(materialCode), BomEntity::getSkuId, skuId)
                // 不传物料默认获取为BOM模板
                .eq(StringUtils.isNotBlank(materialCode), BomEntity::getCode, materialCode)
                .isNull(StringUtils.isBlank(materialCode), BomEntity::getCode)
                // bom编码查询
                .eq(!Objects.isNull(bomMaterialSelectDTO.getBomCode()), BomEntity::getBomNum, bomMaterialSelectDTO.getBomCode())
                .orderByDesc(BomEntity::getCreateTime)
                .orderByDesc(BomEntity::getId).last("limit 1");

        BomEntity one = this.getOne(wrapper);
        // 如果传入的skuId找不到数据，则尝试用skuId=0查询
        if (one == null) {
            LambdaQueryWrapper<BomEntity> skuZeroWrapper = new LambdaQueryWrapper<>();
            // 如果传了bomCode，则不需要做此判断
            if (StringUtils.isBlank(bomMaterialSelectDTO.getBomCode())) {
                // 判断是否为最父级的BOM。为是的BOM查询原则：试产的生产状态查询试产的BOM,量产的生产状态查询量产的BOM。
                // 为否的BOM查询原则：试产的生产状态查询试产/量产的BOM,量产的生产状态查询量产的BOM。
                skuZeroWrapper.eq(StringUtils.isNotBlank(bomMaterialSelectDTO.getProductionState()), BomEntity::getProductionState, bomMaterialSelectDTO.getProductionState());
            }
            skuZeroWrapper.eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                    .eq(StringUtils.isNotBlank(materialCode), BomEntity::getSkuId, 0)
                    // 不传物料默认获取为BOM模板
                    .eq(StringUtils.isNotBlank(materialCode), BomEntity::getCode, materialCode)
                    .isNull(StringUtils.isBlank(materialCode), BomEntity::getCode)
                    // bom编码查询
                    .eq(!Objects.isNull(bomMaterialSelectDTO.getBomCode()), BomEntity::getBomNum, bomMaterialSelectDTO.getBomCode())
                    .orderByDesc(BomEntity::getCreateTime)
                    .orderByDesc(BomEntity::getId).last("limit 1");
            one = this.getOne(skuZeroWrapper);
        }

        if (one == null) {
            return null;
        }
        // 设置物料
        MaterialEntity materialEntityByCode = materialService.getEntityByCodeAndSkuIdAndShowBom(materialCode, skuId);
        one.setMaterialFields(materialEntityByCode);
        // 查询工艺（优化：只在需要时查询）
        if (StringUtils.isNotBlank(one.getCraftCode()) && StringUtils.isNotBlank(one.getCraftVersion())) {
            CraftEntity craftEntity = craftService.lambdaQuery().select(CraftEntity::getCraftId)
                    .eq(CraftEntity::getCraftCode, one.getCraftCode()).eq(CraftEntity::getIsTemplate, Constants.FALSE)
                    .eq(CraftEntity::getCraftVersion, one.getCraftVersion()).last("limit 1").one();
            one.setCraftId(craftEntity == null ? null : craftEntity.getCraftId());
        }
        // 获取最大修订次数
        buildLastVersion(one);
        // 获取过滤联产品和副产品后的bom原料信息
        List<BomRawMaterialEntity> list = bomRawMaterialService.getListByBomIdFilterOutputProduct(one.getId());
        // 优化：提前返回，避免不必要的处理
        if (CollectionUtils.isEmpty(list)) {
            one.setStateName(BomStateEnum.getNameByCode(one.getState()));
            one.setBomRawMaterialEntities(Collections.emptyList());
            return one;
        }
        List<String> codes = list.stream().map(BomRawMaterialEntity::getCode).distinct().collect(Collectors.toList());
        // 批量查询物料信息
        Map<String, MaterialEntity> materialEntityMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, codes).list().stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));
        // 批量查询SKU信息
        Set<Integer> skuIds = list.stream().map(BomRawMaterialEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, SkuEntity> skuMap = skuService.getSkusByIds(skuIds).stream().collect(Collectors.toMap(SkuEntity::getSkuId, Function.identity()));

        // 批量查询物料辅助属性
        Map<String, List<MaterialAuxiliaryAttrEntity>> codeAttrs = materialAuxiliaryAttrService.lambdaQuery()
                .in(MaterialAuxiliaryAttrEntity::getMaterialCode, codes)
                .list().stream().collect(Collectors.groupingBy(MaterialAuxiliaryAttrEntity::getMaterialCode));
            // 批量查询所有相关的BOM信息
            Map<String, BomEntity> latestBomMap = new HashMap<>();
            Set<String> uniqueCodes = list.stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toSet());
            for (String code : uniqueCodes) {
                BomEntity latestBom = this.getLatestSimpleBom(code, skuId);
                if (latestBom != null) {
                    latestBomMap.put(code, latestBom);
                }
            }
            // 批量查询替代物料信息
            List<Integer> bomMaterialIds = list.stream().map(BomRawMaterialEntity::getId).collect(Collectors.toList());
            Map<Integer, List<ReplaceBomMaterialEntity>> replaceMaterialMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(bomMaterialIds)) {
                List<ReplaceBomMaterialEntity> allReplaceMaterials = replaceBomMaterialService.lambdaQuery().in(ReplaceBomMaterialEntity::getBomMaterialId, bomMaterialIds).list();
                replaceMaterialMap = allReplaceMaterials.stream().collect(Collectors.groupingBy(ReplaceBomMaterialEntity::getBomMaterialId));
            }

            // 处理每个BOM原料实体
            for (BomRawMaterialEntity entity : list) {
                // 设置物料字段
                MaterialEntity materialEntity = JacksonUtil.convertObject(materialEntityMap.get(entity.getCode()), MaterialEntity.class);
                if (materialEntity != null) {
                    materialEntity.setSkuEntity(skuMap.get(entity.getSkuId()));
                    materialEntity.setAuxiliaryAttrEntities(codeAttrs.getOrDefault(materialEntity.getCode(), new ArrayList<>()));
                    materialEntity.setSortName(MaterialSortEnum.getNameByCode(materialEntity.getSort()));
                    // 设置BOM信息
                    BomEntity latestBom = latestBomMap.get(entity.getCode());
                    if (latestBom != null) {
                        materialEntity.setBomNum(latestBom.getBomNum());
                        materialEntity.setBomVersion(latestBom.getVersion());
                        materialEntity.setHaveBom(true);
                    } else {
                        materialEntity.setHaveBom(false);
                    }
                }
                entity.setMaterialFields(materialEntity);
                // 设置BOM类型名称
                entity.setBomTypeName(entity.getBomType() != null ?
                    BomItemTypeEnum.getNameByCode(Integer.valueOf(entity.getBomType())) : null);
                // 设置替代物料信息
                List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = replaceMaterialMap.get(entity.getId());
                if (CollectionUtils.isNotEmpty(replaceBomMaterialEntities)) {
                    Set<Integer> replaceIds = replaceBomMaterialEntities.stream().map(ReplaceBomMaterialEntity::getReplaceMaterialId).collect(Collectors.toSet());
                    entity.setReplaceMaterialId(Joiner.on(Constant.SEP).join(replaceIds));
                }
                // 设置用量比例
                Rational rational = new Rational(entity.getNum().doubleValue(), entity.getNumber().doubleValue());
                entity.setRational(RationalDTO.builder().numerator(rational.getNumerator()).denominator(rational.getDenominator()).build());
            }
            one.setStateName(BomStateEnum.getNameByCode(one.getState()));
            one.setBomRawMaterialEntities(list);
            return one;
    }

    /**
     * 获取物料BOM
     *
     * @param materialCode
     * @param salesQuantity
     * @return
     */
    @Override
    public List<PushDownMaterialDTO> getMaterialBomList(String materialCode, Integer skuId, Double salesQuantity) {
        // 检验是否存在多个生效状态的bom
        checkMultipleReleasedBom(materialCode, skuId);
        //获取最新BOM
        BomEntity bomEntity = selectNewByProductCode(materialCode, skuId);
        List<PushDownMaterialDTO> bomRawMaterials = new ArrayList<>();
        //递归查询子级所有BOM
        getWholeBomTree(bomEntity, bomRawMaterials, salesQuantity);
        if (CollectionUtils.isEmpty(bomRawMaterials)) {
            return bomRawMaterials;
        }
        List<String> materialCodes = bomRawMaterials.stream().map(PushDownMaterialDTO::getMaterialCode).distinct().collect(Collectors.toList());
        List<MaterialEntity> materialEntities = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes).list();
        Map<String, MaterialEntity> materialMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
        //处理Map
        for (PushDownMaterialDTO materialDTO : bomRawMaterials) {
            MaterialEntity materialEntity = materialMap.get(materialDTO.getMaterialCode());
            if (materialEntity == null) {
                continue;
            }
            materialDTO.setMaterialName(materialEntity.getName());
            materialDTO.setMaterialFields(JSON.parseObject(JSON.toJSONString(materialEntity), com.yelink.dfscommon.entity.dfs.MaterialEntity.class));
        }
        return bomRawMaterials;
    }

    /**
     * 获取整个BOM,包括子级BOM
     *
     * @param bom
     * @param bomRawMaterials
     * @param count
     */
    private void getWholeBomTree(BomEntity bom, List<PushDownMaterialDTO> bomRawMaterials, Double count) {
        if (Objects.isNull(bom) || CollectionUtils.isEmpty(bom.getBomRawMaterialEntities())) {
            return;
        }
        for (BomRawMaterialEntity bomRawMaterialEntity : bom.getBomRawMaterialEntities()) {
            String bomRawMaterialCode = bomRawMaterialEntity.getCode();
            Integer skuId = bomRawMaterialEntity.getSkuId();
            if (bomRawMaterialEntity.getNumber().compareTo(BigDecimal.ZERO) <= 0) {
                bomRawMaterialEntity.setNumber(new BigDecimal(1));
            }
            //计算需要多少物料, 子BOM推荐值 = 销售数量*BOM系数
            Double materialCount = new Rational(count).mulitiply(new Rational(bomRawMaterialEntity.getNum().doubleValue(), bomRawMaterialEntity.getNumber().doubleValue())).doubleValue();

            PushDownMaterialDTO build = PushDownMaterialDTO.builder()
                    .materialCode(bomRawMaterialCode)
                    .skuId(skuId)
                    .bomNum(bom.getBomNum())
                    .bomVersion(bom.getVersion())
                    .pushNum(MathUtil.round(materialCount, 1))
                    .build();
            bomRawMaterials.add(build);
            // 判断bom中的物料是否存在多个生效状态的bom
            checkMultipleReleasedBom(bomRawMaterialCode, skuId);
            //获取最新BOM
            BomEntity bomEntity = selectNewByProductCode(bomRawMaterialCode, skuId);
            //递归查询子级所有BOM
            getWholeBomTree(bomEntity, bomRawMaterials, materialCount);
        }
    }

    /**
     * 校验是否存在多个生效状态的bom
     *
     * @param materialCode
     */
    private void checkMultipleReleasedBom(String materialCode, Integer skuId) {
        List<BomEntity> bomEntities = getListByCodeAndSkuId(materialCode, skuId, ProductionStateEnum.MASS_PRODUCTION.getCode());
        if (bomEntities.size() >= 2) {
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
            log.error("{}({})有多个生效状态的BOM", materialEntity.getName(), materialCode);
            throw new ResponseException(materialEntity.getName() + "(" + materialCode + ")有多个生效状态的BOM");
        }
    }

    @Override
    public Page<BomImpactAnalysisDTO> impactAnalysis(ImpactAnalysisSelectDTO selectDTO) {
        int current = selectDTO.getCurrent() == null ? 1 : selectDTO.getCurrent();
        int size = selectDTO.getSize() == null ? Integer.MAX_VALUE : selectDTO.getSize();
        List<BomImpactAnalysisDTO> list = new ArrayList<>();
        // 获取工艺关联的生产订单(生效)
        getRelatedProductOrder(list, selectDTO);
        // 分页展示
        Page<BomImpactAnalysisDTO> page = new Page<>(current, size, list.size());
        page.setRecords(PageUtil.startPage(list, current, size));
        return page;
    }

    @Override
    public void impactAnalysisExport(ImpactAnalysisSelectDTO selectDTO, HttpServletResponse response) throws IOException {
        // 获取导出的数据
        selectDTO.setCurrent(1);
        selectDTO.setSize(Integer.MAX_VALUE);
        List<BomImpactAnalysisDTO> records = impactAnalysis(selectDTO).getRecords();
        if (CollectionUtils.isEmpty(records)) {
            EasyExcelUtil.export(response, TABLE_NAME, SHEET_NAME, new ArrayList<>(), BomImpactAnalysisDTO.class);
            return;
        }
        // 导出
        EasyExcelUtil.export(response, TABLE_NAME, SHEET_NAME, records, BomImpactAnalysisDTO.class);
    }

    @Override
    public Page<ReplaceSchemeRelateBomVO> getBomListByMaterialCode(BomSelectByMaterialDetailDTO selectDTO) {
        int current = selectDTO.getCurrent() == null ? 1 : selectDTO.getCurrent();
        int size = selectDTO.getSize() == null ? 20 : selectDTO.getSize();
        List<ReplaceSchemeRelateBomVO> replaceSchemeRelateBomVOS = new ArrayList<>();
        //通过主物料查询所有关联的BOM子物料信息
        Page<BomRawMaterialEntity> rawMaterialEntities = bomRawMaterialService.lambdaQuery()
                .eq(BomRawMaterialEntity::getCode, selectDTO.getMaterialCode())
                .eq(BomRawMaterialEntity::getSkuId, Objects.isNull(selectDTO.getSkuId()) ? 0 : selectDTO.getSkuId())
                .page(new Page<>(current, size));
        List<BomRawMaterialEntity> lists = rawMaterialEntities.getRecords();
        ///通过特征参数查询bom
        List<SkuEntity> list = skuService.lambdaQuery()
                .like(SkuEntity::getSkuName, (StringUtils.isBlank(selectDTO.getValueName()) ? null : selectDTO.getValueName()))
                .list();
        Set<Integer> skuId = new HashSet<>();
        Set<Integer> bomIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(list)) {
            skuId = list.stream().map(SkuEntity::getSkuId).collect(Collectors.toSet());
        }
        if (CollectionUtils.isNotEmpty(lists)) {
            bomIds = lists.stream().map(BomRawMaterialEntity::getBomId).collect(Collectors.toSet());
        }
        LambdaQueryWrapper<BomEntity> wrapper = new LambdaQueryWrapper<>();
        WrapperUtil.like(wrapper, BomEntity::getBomNum, (StringUtils.isBlank(selectDTO.getBomNum()) ? null : selectDTO.getBomNum()));
        wrapper.in(!bomIds.isEmpty(), BomEntity::getId, bomIds);
        wrapper.in(!skuId.isEmpty(), BomEntity::getSkuId, skuId);
        wrapper.like(!StringUtils.isBlank(selectDTO.getCode()), BomEntity::getCode, selectDTO.getCode());
        List<BomEntity> boms = this.list(wrapper);

        Map<Integer, BomEntity> bomIdMap = boms.stream().collect(Collectors.toMap(BomEntity::getId, o -> o));

        for (BomRawMaterialEntity entity : lists) {
            BomEntity bomEntity = bomIdMap.get(entity.getBomId());
            if (Objects.isNull(bomEntity)) {
                continue;
            }
            SkuEntity skuEntity = null;
            if (null != bomEntity.getSkuId()) {
                skuEntity = skuService.getById(bomEntity.getSkuId());
            }
            MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, bomEntity.getCode()).one();
            ReplaceSchemeRelateBomVO build = ReplaceSchemeRelateBomVO.builder()
                    .id(bomEntity.getId())
                    .bomMaterialId(entity.getId())
                    .bomNum(bomEntity.getBomNum())
                    .materialCode(bomEntity.getCode())
                    .materialName(materialEntity.getName())
                    .skuName(skuEntity == null ? null : skuEntity.getSkuName())
                    .remark(bomEntity.getBomDescribe())
                    .extendOne(entity.getExtendOne())
                    .build();
            replaceSchemeRelateBomVOS.add(build);
        }
        Page<ReplaceSchemeRelateBomVO> bomEntityPage = new Page<>(current, size, rawMaterialEntities.getTotal());
        bomEntityPage.setRecords(replaceSchemeRelateBomVOS);
        return bomEntityPage;
    }

    @Override
    public List<ReplaceSchemeRelateBomVO> getReplaceBomDetailId(List<ReplaceBomMaterialEntity> replaceBomMaterialEntities) {
        if (replaceBomMaterialEntities.isEmpty()) {
            return null;
        }
        //查询bom子物料
        Set<Integer> bomMaterialIds = replaceBomMaterialEntities.stream().map(ReplaceBomMaterialEntity::getBomMaterialId).collect(Collectors.toSet());
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.lambdaQuery().in(BomRawMaterialEntity::getId, bomMaterialIds).list();
        //查询bom
        Set<Integer> bomIds = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getBomId).collect(Collectors.toSet());
        List<BomEntity> bomEntities = this.lambdaQuery().in(BomEntity::getId, bomIds).list();
        Map<Integer, BomEntity> bomEntityMap = bomEntities.stream().collect(Collectors.toMap(BomEntity::getId, o -> o));

        List<ReplaceSchemeRelateBomVO> replaceSchemeRelateBomVOS = new ArrayList<>();

        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            BomEntity bomEntity = bomEntityMap.get(bomRawMaterialEntity.getBomId());
            SkuEntity skuEntity = null;
            if (null != bomEntity.getSkuId()) {
                skuEntity = skuService.getById(bomEntity.getSkuId());
            }
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(bomEntity.getCode());
            ReplaceSchemeRelateBomVO build = ReplaceSchemeRelateBomVO.builder()
                    .id(bomEntity.getId())
                    .bomMaterialId(bomRawMaterialEntity.getId())
                    .bomNum(bomEntity.getBomNum())
                    .materialCode(bomEntity.getCode())
                    .materialName(materialEntity.getName())
                    .skuName(skuEntity == null ? null : skuEntity.getSkuName())
                    .remark(bomEntity.getBomDescribe())
                    .extendOne(bomRawMaterialEntity.getExtendOne())
                    .build();
            replaceSchemeRelateBomVOS.add(build);
        }
        return replaceSchemeRelateBomVOS;
    }

    @Override
    public Boolean batchUpdateState(BatchChangeStateDTO batchApprovalDTO, String username) {
        List<BomEntity> list = this.lambdaQuery().in(BomEntity::getId, batchApprovalDTO.getIds()).list();
        // 改状态时，要求状态都是一样的才能改
        long stateCount = list.stream().map(BomEntity::getState).distinct().count();
        if (stateCount > 1) {
            throw new ResponseException(RespCodeEnum.MUST_BE_IN_THE_SAME_STATE_TO_MODIFY);
        }
        Integer integer = list.stream().map(BomEntity::getState).distinct().collect(Collectors.toList()).get(0);
        if (Objects.equals(integer, batchApprovalDTO.getState())) {
            return true;
        }
        if (integer > batchApprovalDTO.getState()) {
            throw new ResponseException(RespCodeEnum.UPDATE_STATE_NOT_CROSS_LEVEL);
        }
        //开启审批后不可直接将创建状态改为生效
        Boolean responseObject = approveConfigService.getConfigByCode(ApproveModuleEnum.BOM.getCode());
        if (BomStateEnum.CREATE.getCode() == integer && Boolean.TRUE.equals(responseObject)) {
            throw new ResponseException(RespCodeEnum.APPROVE_IS_USER_UPDATE_STATE_IS_EFFECT);
        }
        boolean update = this.lambdaUpdate().in(BomEntity::getId, batchApprovalDTO.getIds())
                .set(BomEntity::getState, batchApprovalDTO.getState())
                .set(BomEntity::getUpdateTime, new Date())
                .set(BomEntity::getUpdateBy, username)
                .set(Objects.equals(BomStateEnum.RELEASED.getCode(), integer), BomEntity::getReleaseTime, new Date())
                .update();
        // 处理历史相同物料编码的BOM
        refreshReleasedBom(batchApprovalDTO.getIds());
        return update;
    }

    /**
     * 获取关联的生产订单(生效)
     */
    private void getRelatedProductOrder(List<BomImpactAnalysisDTO> list, ImpactAnalysisSelectDTO selectDTO) {
        if (StringUtils.isNotBlank(selectDTO.getOrderType()) && !selectDTO.getOrderType().equals(OrderNumTypeEnum.PRODUCT_ORDER.getTypeName())) {
            return;
        }
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().bomNumbers(Stream.of(selectDTO.getBomNum()).collect(Collectors.toList())).build());
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return;
        }

        List<ProductOrderEntity> releasedProductOrders = pageResult.getRecords().stream().filter(o -> o.getState().equals(OrderStateEnum.RELEASED.getCode())).collect(Collectors.toList());
        for (ProductOrderEntity productOrderEntity : releasedProductOrders) {
            list.add(
                    BomImpactAnalysisDTO.builder()
                            .orderCode(productOrderEntity.getProductOrderNumber())
                            .orderStateName(OrderStateEnum.getNameByCode(productOrderEntity.getState()))
                            .orderType(OrderNumTypeEnum.PRODUCT_ORDER.getTypeName())
                            .build()
            );
        }
    }

    @Override
    public void uploadCustomTemplate(MultipartFile file, String operationUsername) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        // 上传
        uploadService.uploadReferencedFile(UploadFileCodeEnum.BOM_TEMPLATE_NAME.getCode(), operationUsername, file);
    }

    @Override
    public byte[] downloadImportTemplate() throws IOException {
        Workbook templateWorkbook = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        InputStream inputStream = null;
        try {
            inputStream = getTransferTemplate();
            templateWorkbook = WorkbookFactory.create(inputStream);
            //删除说明sheet和目标数据sheet
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } finally {
            ExcelUtil.closeWorkBook(templateWorkbook);
            IOUtils.closeQuietly(byteArrayOutputStream);
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public InputStream downloadCustomImportTemplate() throws IOException {
        byte[] download = uploadService.download(UploadFileCodeEnum.BOM_TEMPLATE_NAME.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            return new ByteArrayInputStream(download);
        }
        throw new ResponseException("未配置自定义转换模板");
    }

    /**
     * 获取转换表格文件
     *
     * @return
     * @throws IOException
     */
    private InputStream getTransferTemplate() throws IOException {
        InputStream inputStream;
        byte[] download = uploadService.download(UploadFileCodeEnum.BOM_TEMPLATE_NAME.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            inputStream = new ByteArrayInputStream(download);
        } else {
            //默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template/BomTemplate.xlsx");
            inputStream = resource.getInputStream();
        }
        return inputStream;
    }

    @Transactional(rollbackFor = Exception.class)
    @Async
    @Override
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        //初始化锁 + 进度
        importProgressService.initLockProgress(RedisKeyPrefix.BOM_IMPORT_LOCK, RedisKeyPrefix.BOM_IMPORT_PROGRESS);
        InputStream templateInputStream = null;
        File templateFile = null;
        try {
            //1、获取模板文件 ： 用户自定义/默认模板,转成文件
            templateInputStream = getTransferTemplate();
            templateFile = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!templateFile.getParentFile().exists()) {
                templateFile.getParentFile().mkdirs();
            }
            FileUtils.copyInputStreamToFile(templateInputStream, templateFile);
            //2、读取模板配置
            ExcelTemplateSetDTO excelTemplateSetDTO = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ExcelTemplateSetDTO.class, 0, 3).get(0);
            //3、通过配置将数据转换并复制到模板原始数据
            List<BomImportExcelDTO> bomImportExcelDTOS = executeDataToTarget(WorkbookFactory.create(inputStream), templateFile, excelTemplateSetDTO);
            //4、校验并转换数据
            List<BomEntity> bomEntities = this.verifyFormat(bomImportExcelDTOS, operationUsername);
            //5、验证结果上传文件服务器
            //得到转换后的日志数据
            List<BomImportExcelLogDTO> bomImportExcelDTOSUpload = BomImportExcelLogDTO.getImportLogs(bomImportExcelDTOS);
            String importUrl = importProgressService.verifyResultToExcelUploadTo(bomImportExcelDTOSUpload, BomImportExcelLogDTO.class, operationUsername);
            //6、保存导入记录
            int countPass = CollectionUtils.isEmpty(bomImportExcelDTOS) ? 0 : (int) bomImportExcelDTOS.stream().filter(BomImportExcelDTO::getVerifyPass).count();
            int sumPass = bomImportExcelDTOS.size();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.BOM_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.BOM_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(sumPass - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //7、保存数据
            int rowTotal = bomEntities.size();
            if (rowTotal > 0) {
                for (int i = 0; i < rowTotal; i++) {
                    BomEntity bomEntity = bomEntities.get(i);
                    //保存可以导入的数据
                    this.saveOrUpdateImportEntity(bomEntity);
                    importProgressService.updateProgress(RedisKeyPrefix.BOM_IMPORT_PROGRESS, importUrl, sumPass, countPass, i + 1);
                }
            } else {
                importProgressService.updateProgress(RedisKeyPrefix.BOM_IMPORT_PROGRESS, importUrl, sumPass, 0, 0);
            }
        } catch (Exception e) {
            importProgressService.importProgressException(RedisKeyPrefix.BOM_IMPORT_PROGRESS, e);
            log.error("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(templateInputStream);
            IOUtils.closeQuietly(inputStream);
            FileUtils.deleteQuietly(templateFile);
            importProgressService.releaseLock(RedisKeyPrefix.BOM_IMPORT_LOCK);
        }
    }


    /**
     * 新增或更新数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateImportEntity(BomEntity bomEntity) {
        BomEntity one = this.lambdaQuery().eq(BomEntity::getBomNum, bomEntity.getBomNum()).eq(BomEntity::getIsTemplate, Constants.FALSE).one();
        List<BomRawMaterialEntity> newDatas = bomEntity.getBomRawMaterialEntities();
        // 如果存在bom，对存在的子物料作更新处理，不存在则新增
        if (Objects.nonNull(one)) {
            bomEntity.setId(one.getId());
            // 更新bom
            this.updateById(bomEntity);
            // 版本变更记录
            changeVersionLog(bomEntity);
            // 处理历史相同物料编码的BOM
            refreshReleasedBom(Collections.singletonList(one.getId()));

            // 删除旧bom物料清单数据以及 替代关系
            List<String> newBomRawMaterialCodes = newDatas.stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
            List<BomRawMaterialEntity> oldDatas = bomRawMaterialService.lambdaQuery().eq(BomRawMaterialEntity::getBomId, one.getId()).list();
            List<String> deletePreRowCodes = oldDatas.stream()
                    .map(BomRawMaterialEntity::getCode)
                    .filter(preMaterialCode -> !newBomRawMaterialCodes.contains(preMaterialCode))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deletePreRowCodes)) {
                bomRawMaterialService.lambdaUpdate().in(BomRawMaterialEntity::getCode, deletePreRowCodes).remove();
            }
            // 删除BOM下面所有的关联关系后重新插入
            replaceBomMaterialService.lambdaUpdate().eq(ReplaceBomMaterialEntity::getBomId, one.getId()).remove();

            // bom物料清单的物料编码相同则更新，否则新增
            Map<String, Integer> oldDataMap = oldDatas.stream().collect(Collectors.toMap(BomRawMaterialEntity::getCode, BomRawMaterialEntity::getId));
            List<BomRawMaterialEntity> saveDatas = newDatas.stream().filter(o -> !oldDataMap.containsKey(o.getCode())).collect(Collectors.toList());
            List<BomRawMaterialEntity> updateDatas = newDatas.stream().filter(o -> oldDataMap.containsKey(o.getCode())).collect(Collectors.toList());
            for (BomRawMaterialEntity saveData : saveDatas) {
                saveData.setBomId(one.getId());
            }
            bomRawMaterialService.saveBatch(saveDatas);
            // 更新替代关系
            updateReplaceBomRawMaterialRelated(one, saveDatas);

            for (BomRawMaterialEntity updateData : updateDatas) {
                updateData.setId(oldDataMap.get(updateData.getCode()));
            }
            bomRawMaterialService.updateBatchById(updateDatas);
            // 更新替代关系
            updateReplaceBomRawMaterialRelated(one, updateDatas);

        } else {
            // 新增bom和bom物料清单
            bomEntity.setCreateTime(Objects.nonNull(bomEntity.getCreateTime()) ? bomEntity.getCreateTime() : new Date());
            bomEntity.setUpdateTime(Objects.nonNull(bomEntity.getUpdateTime()) ? bomEntity.getUpdateTime() : new Date());
            this.save(bomEntity);
            // 版本变更记录
            initVersionLog(bomEntity);
            // 重新查询防止id丢失
            one = this.lambdaQuery().eq(BomEntity::getBomNum, bomEntity.getBomNum())
                    .eq(BomEntity::getIsTemplate, Constants.FALSE).one();
            for (BomRawMaterialEntity bomRawMaterialEntity : newDatas) {
                bomRawMaterialEntity.setBomId(bomEntity.getId());
            }
            bomRawMaterialService.saveBatch(newDatas);
            // 新增替代关系
            updateReplaceBomRawMaterialRelated(one, newDatas);
        }
        this.initOriginInfo(bomEntity);
        // 删除并更新bom平铺关系表
        List<BomTileEntity> tileEntities = new ArrayList<>();
        bomTileService.removeByBomMaterialCode(one.getCode());
        for (BomRawMaterialEntity newData : newDatas) {
            tileEntities.add(BomTileEntity.builder().bomMaterialCode(one.getCode()).materialCode(newData.getCode()).build());
        }
        bomTileService.saveBatch(tileEntities);

        // 物料有无生效状态的bom 事件
        applicationContext.publishEvent(new MaterialHaveReleasedStateBomEvent(this, bomEntity));
    }

    /**
     * 更新物料清单的替代关系
     */
    private void updateReplaceBomRawMaterialRelated(BomEntity one, List<BomRawMaterialEntity> saveDatas) {
        List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = new ArrayList<>();
        for (BomRawMaterialEntity bomRawMaterialEntity : saveDatas) {
            if (StringUtils.isBlank(bomRawMaterialEntity.getReplaceMaterialId())) {
                continue;
            }
            String[] split = bomRawMaterialEntity.getReplaceMaterialId().split(Constant.SEP);
            for (String s : split) {
                ReplaceBomMaterialEntity build = ReplaceBomMaterialEntity.builder()
                        .bomId(one.getId())
                        .bomMaterialId(bomRawMaterialEntity.getId())
                        .replaceMaterialId(Integer.valueOf(s))
                        .build();
                replaceBomMaterialEntities.add(build);
            }
        }
        if (CollectionUtils.isNotEmpty(replaceBomMaterialEntities)) {
            replaceBomMaterialService.saveBatch(replaceBomMaterialEntities);
        }
    }

    /**
     * 处理单据模式导入数据，判断导入数据 统计导入结果
     *
     * @param importWorkbook
     * @param templateFile
     * @param excelTemplateSetDTO
     * @return
     */
    private List<BomImportExcelDTO> executeDataToTarget(Workbook importWorkbook, File templateFile, ExcelTemplateSetDTO excelTemplateSetDTO) throws IOException {
        List<BomImportExcelDTO> list = new ArrayList<>();
        // 列表模式数据读取
        if (ReportFormConstant.LIST_MODE.equals(excelTemplateSetDTO.getExcelType())) {
            Sheet importSheet = importWorkbook.getSheetAt(0);
            int rowTotal = importSheet.getPhysicalNumberOfRows() - 1;
            Workbook templateWorkbook = WorkbookFactory.create(templateFile);
            try {
                //清空模板中的导入数据防止执行公式占用大量内存，再创建一个空的sheet用于接收数据
                templateWorkbook.removeSheetAt(2);
                templateWorkbook.createSheet("导入数据");
                Sheet templateResourceSheet = templateWorkbook.getSheetAt(2);
                for (int i = 2; i <= rowTotal; i++) {
                    ExcelUtil.clearSheet(templateResourceSheet, 3, excelTemplateSetDTO.getColumnNum());
                    Row templateResourceSheetRow = templateResourceSheet.getRow(2);
                    if (templateResourceSheetRow == null) {
                        templateResourceSheetRow = templateResourceSheet.createRow(2);
                    }
                    for (int k = 0; k < excelTemplateSetDTO.getColumnNum(); k++) {
                        Row row = importSheet.getRow(i);
                        if (row == null) {
                            continue;
                        }
                        Cell xssfCell = row.getCell(k);
                        if (xssfCell != null) {
                            // 复制之前清除公式
                            ExcelDeleteRowUtils.clearExcelFormal(xssfCell);
                            ExcelUtil.copyCell(xssfCell, templateResourceSheetRow.createCell(k));
                        }
                    }
                    log.info("读取" + i + "条数据");
                    // 打开工作簿时应用程序是否应执行完全重新计算。
                    templateWorkbook.setForceFormulaRecalculation(true);
                    // 强行刷新单元格公式 计算公式保存结果，但不改变公式
                    BaseFormulaEvaluator.evaluateAllFormulaCells(templateWorkbook);

                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    templateWorkbook.write(outputStream);
                    //byteStream无法多次读取
                    ByteArrayInputStream byteStream = new ByteArrayInputStream(outputStream.toByteArray());
                    ByteArrayInputStream byteStream1 = new ByteArrayInputStream(outputStream.toByteArray());
                    IOUtils.closeQuietly(outputStream);
                    List<BomImportExcelDTO> bomImportExcelDTOS = EasyExcelUtil.read(byteStream, BomImportExcelDTO.class, 1, 2);
                    if (CollectionUtils.isEmpty(bomImportExcelDTOS)) {
                        continue;
                    }
                    BomImportExcelDTO bomImportExcelDTO = bomImportExcelDTOS.get(0);
                    List<BomMaterialImportDTO> read = EasyExcelUtil.read(byteStream1, BomMaterialImportDTO.class, 1, 2);
                    bomImportExcelDTO.setSonMaterialList(read);
                    list.add(bomImportExcelDTO);
                }
                //数据合并
                Map<String, List<BomImportExcelDTO>> collect = list.stream()
                        .collect(Collectors.groupingBy(BomImportExcelDTO::getBomNum));
                list = new ArrayList<>();
                for (String s : collect.keySet()) {
                    List<BomImportExcelDTO> bomImportExcelDTOS = collect.get(s);
                    BomImportExcelDTO bomImportExcelDTO = bomImportExcelDTOS.get(0);
                    List<BomMaterialImportDTO> sonMaterialList = bomImportExcelDTO.getSonMaterialList().size() > 0 ? new ArrayList<>() : bomImportExcelDTO.getSonMaterialList();
                    for (BomImportExcelDTO importExcelDTO : bomImportExcelDTOS) {
                        sonMaterialList.addAll(importExcelDTO.getSonMaterialList());
                    }
                    bomImportExcelDTO.setSonMaterialList(sonMaterialList);
                    list.add(bomImportExcelDTO);
                }
            } finally {
                ExcelUtil.closeWorkBook(templateWorkbook);
                ExcelUtil.closeWorkBook(importWorkbook);
            }
            return list;
        }
        // 单据模式
        if (ReportFormConstant.BILL_MODE.equals(excelTemplateSetDTO.getExcelType())) {
            int numberOfSheets = importWorkbook.getNumberOfSheets();
            for (int j = 0; j < numberOfSheets; j++) {
                Sheet importSheet = importWorkbook.getSheetAt(j);
                int rowTotal = importSheet.getPhysicalNumberOfRows() - 1;
                Workbook templateWorkbook = WorkbookFactory.create(templateFile);
                try {
                    Sheet templateResourceSheet = templateWorkbook.getSheetAt(2);
                    //清空自定义模板中导入数据sheet页的数据
                    ExcelUtil.clearSheet(templateResourceSheet, templateResourceSheet.getLastRowNum() + 1, excelTemplateSetDTO.getColumnNum());
                    for (int i = 0; i <= rowTotal; i++) {
                        Row templateResourceSheetRow = templateResourceSheet.getRow(i);
                        if (templateResourceSheetRow == null) {
                            templateResourceSheetRow = templateResourceSheet.createRow(i);
                        }
                        for (int k = 0; k < excelTemplateSetDTO.getColumnNum(); k++) {
                            Row row = importSheet.getRow(i);
                            if (row == null) {
                                continue;
                            }
                            Cell xssfCell = row.getCell(k);
                            if (xssfCell != null) {
                                // 复制之前清除公式
                                ExcelDeleteRowUtils.clearExcelFormal(xssfCell);
                                ExcelUtil.copyCell(xssfCell, templateResourceSheetRow.createCell(k));
                            }
                        }
                        log.info("读取" + i + "条数据");
                    }
                    // 打开工作簿时应用程序是否应执行完全重新计算。
                    templateWorkbook.setForceFormulaRecalculation(true);
                    // 强行刷新单元格公式 计算公式保存结果，但不改变公式
                    BaseFormulaEvaluator.evaluateAllFormulaCells(templateWorkbook);
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    templateWorkbook.write(outputStream);
                    ByteArrayInputStream byteStream = new ByteArrayInputStream(outputStream.toByteArray());
                    ByteArrayInputStream byteStream1 = new ByteArrayInputStream(outputStream.toByteArray());
                    ByteArrayInputStream byteStream2 = new ByteArrayInputStream(outputStream.toByteArray());
                    IOUtils.closeQuietly(outputStream);

                    List<BomImportExcelOtherDTO> bomImportExcelDTOS = EasyExcelUtil.read(byteStream, BomImportExcelOtherDTO.class, 1, 2);
                    if (CollectionUtils.isEmpty(bomImportExcelDTOS)) {
                        continue;
                    }
                    BomImportExcelOtherDTO bomImportExcelDTO = bomImportExcelDTOS.get(0);
                    List<BomMaterialImportDTO> read = EasyExcelUtil.read(byteStream1, BomMaterialImportDTO.class, 1, 2);
                    //兼容历史模板
                    if (CollectionUtils.isEmpty(read)) {
                        read = EasyExcelUtil.read(byteStream2, BomMaterialImportDTO.class, 1, 5);
                    }
                    bomImportExcelDTO.setSonMaterialList(read);
                    // 再转回来
                    BomImportExcelDTO excelDTO = JacksonUtil.convertObject(bomImportExcelDTO, BomImportExcelDTO.class);
                    list.add(excelDTO);
                    //处理完一个sheet后清空数据，避免导入多个空sheet时由于数据未清理导入重复数据
                    ExcelUtil.clearSheet(templateResourceSheet, rowTotal + 1, excelTemplateSetDTO.getColumnNum());
                    // 打开工作簿时应用程序是否应执行完全重新计算。
                    templateWorkbook.setForceFormulaRecalculation(true);
                    // 强行刷新单元格公式 计算公式保存结果，但不改变公式
                    XSSFFormulaEvaluator.evaluateAllFormulaCells(templateWorkbook);
                    ByteArrayOutputStream outputStream1 = new ByteArrayOutputStream();
                    templateWorkbook.write(outputStream1);
                } finally {
                    ExcelUtil.closeWorkBook(templateWorkbook);
                    ExcelUtil.closeWorkBook(importWorkbook);
                }
            }
        }
        return list;
    }

    public static Pattern p = Pattern.compile("^[\\w.-]+$");
    public static Pattern n = Pattern.compile("-?[0-9]+.?[0-9]*");


    /**
     * 校验数据
     *
     * @param bomImportExcelDTOS
     * @param operationUsername
     * @return
     */
    private List<BomEntity> verifyFormat(List<BomImportExcelDTO> bomImportExcelDTOS, String operationUsername) {
        boolean canImport;
        StringBuilder importResult;
        String verMaterialEnable = getVerMaterialEnable();

        // 1、首先进行全局循环检测（检测导入文件内部的循环依赖）
        // 只标记参与循环的BOM为不可导入，其他BOM可以正常导入
        checkAndMarkCyclicBoms(bomImportExcelDTOS);

        for (BomImportExcelDTO bomImportDTO : bomImportExcelDTOS) {
            importResult = new StringBuilder();
            canImport = true;
            //校验BOM编号
            if (StringUtils.isBlank(bomImportDTO.getBomNum())) {
                importResult.append("BOM编号字段不合法；");
                canImport = false;
            }
            //校验物料编码
            MaterialEntity materialEntity = null;
            if (StringUtils.isBlank(bomImportDTO.getMaterialCode())) {
                importResult.append("物料编码不能为空；");
                canImport = false;
            } else {
                materialEntity = materialService.getSimpleMaterialByCode(bomImportDTO.getMaterialCode());
                if (materialEntity == null) {
                    importResult.append("物料编码字段不合法,请检查物料编码是否存在；");
                    canImport = false;
                }
            }
            //校验BOM版本号
            if (StringUtils.isBlank(bomImportDTO.getVersion())) {
                importResult.append("BOM版本字段必填，不可为空；");
                canImport = false;
            }
            //校验工艺编码
            if (StringUtils.isNotBlank(bomImportDTO.getCraftCode())) {
                if (!craftService.lambdaQuery().eq(CraftEntity::getCraftCode, bomImportDTO.getCraftCode()).exists()) {
                    importResult.append("工艺编码在系统中不存在，请确认；");
                    canImport = false;
                }
            }
            //校验编制人姓名
            if (StringUtils.isNotBlank(bomImportDTO.getEditorName())) {
                Long count = sysUserService.lambdaQuery().eq(SysUserEntity::getNickname, bomImportDTO.getEditorName()).count();
                if (count > 1) {
                    importResult.append("存在同个相同名称的编制人，不能导入；");
                    canImport = false;
                } else {
                    SysUserEntity sysUserEntity = sysUserService.lambdaQuery().eq(SysUserEntity::getNickname, bomImportDTO.getEditorName()).one();
                    if (sysUserEntity == null) {
                        importResult.append("编制人在系统中不存在，请确认；");
                        canImport = false;
                    } else {
                        bomImportDTO.setEditor(sysUserEntity.getUsername());
                    }
                }
            }
            // 校验主物料特征参数 -> 填充skuId
            if (StringUtils.isNotBlank(bomImportDTO.getAuxiliaryAttrValues())) {
                List<MaterialAuxiliaryAttrEntity> auxiliaryAttrs = materialAuxiliaryAttrService.lambdaQuery().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, bomImportDTO.getMaterialCode()).list();
                Set<String> attrValueNames = new HashSet<>(Arrays.asList(bomImportDTO.getAuxiliaryAttrValues().split(com.yelink.dfscommon.constant.Constant.SEP)));
                if (CollectionUtils.isEmpty(auxiliaryAttrs) || auxiliaryAttrs.size() != attrValueNames.size()) {
                    importResult.append("只能导入该物料配置的特征参数；");
                    canImport = false;
                } else {
                    // 查询特征参数值列表
                    Map<String, String> attrCodeNameMap = auxiliaryAttrs.stream().collect(Collectors.toMap(MaterialAuxiliaryAttrEntity::getAuxiliaryAttrCode, MaterialAuxiliaryAttrEntity::getAuxiliaryAttrName));
                    AuxiliaryAttrValueSelectDTO selectDTO = AuxiliaryAttrValueSelectDTO.builder()
                            .auxiliaryAttrCode(String.join(com.yelink.dfscommon.constant.Constant.SEP, attrCodeNameMap.keySet()))
                            .build();
                    List<AuxiliaryAttrValueEntity> auxiliaryAttrValues = auxiliaryAttrValueService.getList(selectDTO);
                    Map<String, AuxiliaryAttrValueEntity> valueNameMap = auxiliaryAttrValues.stream().collect(Collectors.toMap(AuxiliaryAttrValueEntity::getValueName, v -> v));
                    List<MaterialAuxiliaryAttrSkuEntity> addSkuList = new ArrayList<>();
                    for (String attrValueName : attrValueNames) {
                        AuxiliaryAttrValueEntity auxiliaryAttrValueEntity = valueNameMap.get(attrValueName);
                        if (Objects.nonNull(auxiliaryAttrValueEntity)) {
                            addSkuList.add(MaterialAuxiliaryAttrSkuEntity.builder()
                                    .createBy(operationUsername)
                                    .createTime(new Date())
                                    .materialCode(bomImportDTO.getMaterialCode())
                                    .auxiliaryAttrCode(auxiliaryAttrValueEntity.getAuxiliaryAttrCode())
                                    .auxiliaryAttrName(attrCodeNameMap.get(auxiliaryAttrValueEntity.getAuxiliaryAttrCode()))
                                    .valueCode(auxiliaryAttrValueEntity.getValueCode())
                                    .valueName(auxiliaryAttrValueEntity.getValueName())
                                    .build());
                        }
                    }
                    if (addSkuList.size() != attrValueNames.size()) {
                        importResult.append("导入特征参数与系统需要配置的特征参数不匹配；");
                        canImport = false;
                    } else {
                        try {
                            //新增物料sku
                            MaterialSkuInsertResDTO skuDTO = materialSkuService.add(addSkuList, operationUsername);
                            bomImportDTO.setSkuId(skuDTO.getSkuId());
                        } catch (Exception e) {
                            importResult.append("新增物料sku错误；");
                            canImport = false;
                        }
                    }
                }
            }
            //校验审批人姓名
            if (Boolean.TRUE.equals(approveConfigService.getConfigByCode(ApproveModuleEnum.BOM.getCode()))) {
                if (StringUtils.isBlank(bomImportDTO.getApproverName())) {
                    importResult.append("开启审批，审批人字段必填，请确认；");
                    canImport = false;
                } else {
                    Long count = sysUserService.lambdaQuery().eq(SysUserEntity::getNickname, bomImportDTO.getApproverName()).count();
                    if (count > 1) {
                        importResult.append("存在同个相同名称的审批人，不能导入；");
                        canImport = false;
                    } else {
                        SysUserEntity sysUserEntity = sysUserService.lambdaQuery().eq(SysUserEntity::getNickname, bomImportDTO.getApproverName()).one();
                        if (sysUserEntity == null) {
                            importResult.append("审批人在系统中不存在，请确认；");
                            canImport = false;
                        } else {
                            bomImportDTO.setApprover(sysUserEntity.getUsername());
                        }
                    }
                }
            }
            //校验状态
            if (StringUtils.isBlank(bomImportDTO.getStateName())) {
                importResult.append("状态字段必填，请确认；");
                canImport = false;
            } else {
                if (!(bomImportDTO.getStateName().equals(BomStateEnum.CREATE.getName()) || bomImportDTO.getStateName().equals(BomStateEnum.RELEASED.getName()))) {
                    importResult.append("状态字段只能是创建或生效，请确认；");
                    canImport = false;
                }
                bomImportDTO.setState(BomStateEnum.getCodeByName(bomImportDTO.getStateName()));
            }

            //校验当物料存在时处理逻辑
            BomEntity oldBomEntity = this.lambdaQuery().eq(BomEntity::getBomNum, bomImportDTO.getBomNum())
                    .eq(BomEntity::getIsTemplate, Constants.FALSE).one();
            if (oldBomEntity != null) {
                Integer state = oldBomEntity.getState();
                //系统中,对应数据的状态为创建或生效
                if (BomStateEnum.CREATE.getCode() != state && BomStateEnum.RELEASED.getCode() != state) {
                    importResult.append("系统已存在数据，且状态非创建或生效");
                    canImport = false;
                }
                //导入表中数据的状态,等于或大于,系统中对应数据的状态;(例如:创建态不能覆盖生效态)
                Integer dtoState = bomImportDTO.getState();
                if (BomStateEnum.RELEASED.getCode() == state && BomStateEnum.CREATE.getCode() == dtoState) {
                    importResult.append("系统已存在数据,且状态不可覆盖");
                    canImport = false;
                }
//                Date dtoUpdateTime = DateUtil.parse(bomImportDTO.getUpdateTime(), DateUtil.DATETIME_FORMAT);
                Date dtoUpdateTime = bomImportDTO.getUpdateTime();
                if (dtoUpdateTime == null) {
                    importResult.append("未获取到更新时间或更新时间为空,不作导入。");
                    canImport = false;
                    dtoUpdateTime = oldBomEntity.getCreateTime();
                }
                Date entityCreateTime = oldBomEntity.getCreateTime();
                Date entityUpdateTime = oldBomEntity.getUpdateTime();
                Date date = entityUpdateTime != null && entityUpdateTime.after(entityCreateTime) ? entityUpdateTime : entityCreateTime;
                //(导入表中的更新时间 > 对应数据的创建/更新时间
                if (dtoUpdateTime.before(date)) {
                    importResult.append("更新时间早于系统中更新时间");
                    canImport = false;
                }
            }

            List<BomMaterialImportDTO> bomImportDTOSonMaterialList = bomImportDTO.getSonMaterialList();

            //重复子物料根据配置校验
            List<BomMaterialImportDTO> distinctMaterialList = bomImportDTOSonMaterialList.stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(o -> ColumnUtil.getMaterialSku(o.getCode(), o.getSkuId())))), ArrayList::new));
            //1、不允许重复： 不允许子物料行存在相同物料
            if (BomRawMaterialEnum.NOT_REPEAT.getCode().equals(verMaterialEnable) && bomImportDTOSonMaterialList.size() > distinctMaterialList.size()) {
                String duplicateMessage = buildDuplicateMaterialMessageForImport(bomImportDTOSonMaterialList);
                importResult.append(duplicateMessage).append("；");
                canImport = false;
            }

            //2、校验BOM循环：检查当前BOM与数据库中已有BOM是否会形成循环依赖
            // 只有在全局循环检测通过且当前BOM验证通过的情况下才进行此检测
            if (bomImportDTO.getVerifyPass() == null || bomImportDTO.getVerifyPass()) {
                try {
                    checkBomCycleForImport(bomImportDTO.getMaterialCode(), bomImportDTO.getSkuId(), bomImportDTOSonMaterialList);
                } catch (ResponseException e) {
                    importResult.append("BOM与已有数据存在循环依赖：").append(e.getMessage()).append("；");
                    canImport = false;
                }
            }
            // 2、重复合并：子物料存在相同物料行数据合并-》特殊处理
            if (BomRawMaterialEnum.REPEAT_MERGE.getCode().equals(verMaterialEnable)) {
                // 通过特征参数和物料编码去重，相同的进行合并处理，分子分母需要通分（比如 3/5 + 2/5 = 1/1）
                Map<String, List<BomMaterialImportDTO>> map = bomImportDTOSonMaterialList.stream()
                        .collect(Collectors.groupingBy(bomMaterialImportDTO -> ColumnUtil.getMaterialSku(bomMaterialImportDTO.getCode(), bomMaterialImportDTO.getSkuId())));
                for (BomMaterialImportDTO bomMaterialImportDTO : distinctMaterialList) {
                    // 重新设置分子分母
                    List<BomMaterialImportDTO> listMaterialEntities = map.get(ColumnUtil.getMaterialSku(bomMaterialImportDTO.getCode(), bomMaterialImportDTO.getSkuId()));
                    Rational add = new Rational();
                    double fixedDamage = 0;
                    double lossRate = 0;
                    for (BomMaterialImportDTO bomMaterialImportDTO1 : listMaterialEntities) {
                        bomMaterialImportDTO1.setNum(Objects.nonNull(bomMaterialImportDTO1.getNum()) ? bomMaterialImportDTO1.getNum() : 1.0);
                        if (bomMaterialImportDTO1.getFixedDamage() != null) {
                            fixedDamage = fixedDamage + bomMaterialImportDTO1.getFixedDamage();
                        }
                        if (bomMaterialImportDTO1.getLossRate() != null) {
                            lossRate = lossRate + bomMaterialImportDTO1.getLossRate();
                        }
                        if (!ObjectUtils.isEmpty(bomMaterialImportDTO1.getNum()) && !ObjectUtils.isEmpty(bomMaterialImportDTO1.getNumber())) {
                            Rational rational = new Rational(bomMaterialImportDTO1.getNum(), bomMaterialImportDTO1.getNumber());
                            add = add.add(rational);
                        }
                    }
                    bomMaterialImportDTO.setNum(add.getNumerator());
                    bomMaterialImportDTO.setNumber(add.getDenominator());
                    bomMaterialImportDTO.setFixedDamage(fixedDamage);
                    bomMaterialImportDTO.setLossRate(lossRate);
                }
                bomImportDTO.setSonMaterialList(distinctMaterialList);
            }

            //3、校验子物料
            canImport = checkBomMaterial(operationUsername, bomImportDTO, canImport);

            // 只有在全局循环检测没有标记为失败的情况下才更新验证状态
            if (bomImportDTO.getVerifyPass() == null || bomImportDTO.getVerifyPass()) {
                bomImportDTO.setVerifyPass(canImport);
                bomImportDTO.setImportResult(importResult.toString());
            } else {
                // 如果已经被全局循环检测标记为失败，追加当前的错误信息
                if (importResult.length() > 0) {
                    String existingResult = bomImportDTO.getImportResult();
                    bomImportDTO.setImportResult(existingResult + "；" + importResult.toString());
                }
            }
        }
        //转换数据
        List<BomEntity> result = new ArrayList<>();
        List<BomImportExcelDTO> bomImportExcelDTOS1 = bomImportExcelDTOS.stream().filter(BomImportExcelDTO::getVerifyPass).collect(Collectors.toList());
        for (BomImportExcelDTO bomImportExcelDTO : bomImportExcelDTOS1) {
            Integer approvalStatus = null;
            if (Boolean.TRUE.equals(approveConfigService.getConfigByCode(ApproveModuleEnum.BOM.getCode()))) {
                if (bomImportExcelDTO.getState() == BomStateEnum.CREATE.getCode()) {
                    approvalStatus = ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
                } else {
                    approvalStatus = ApprovalStatusEnum.APPROVED.getCode();
                }
            }
            // 过滤替代件
            List<BomRawMaterialEntity> bomRawMaterialEntities = bomImportExcelDTO.getSonMaterialList().stream()
                    .filter(o -> !BomItemTypeEnum.REPLACE_ITEM.getName().equals(o.getBomTypeName()))
                    .map(bomMaterialImportDTO ->
                            BomRawMaterialEntity.builder()
                                    .code(bomMaterialImportDTO.getCode())
                                    .num(BigDecimal.valueOf(bomMaterialImportDTO.getNum()))
                                    .number(BigDecimal.valueOf(bomMaterialImportDTO.getNumber()))
                                    .fixedDamage(bomMaterialImportDTO.getFixedDamage())
                                    .lossRate(bomMaterialImportDTO.getLossRate())
                                    .bomType(bomMaterialImportDTO.getBomType())
                                    .replaceMaterialId(bomMaterialImportDTO.getReplaceMaterialIds())
                                    .remark(bomMaterialImportDTO.getRemark())
                                    .extendOne(bomMaterialImportDTO.getExtendOne())
                                    .skuId(bomMaterialImportDTO.getSkuId())
                                    .build()).collect(Collectors.toList());

            BomEntity bomEntity = BomEntity.builder()
                    .bomNum(bomImportExcelDTO.getBomNum())
                    .code(bomImportExcelDTO.getMaterialCode())
                    .skuId(bomImportExcelDTO.getSkuId())
                    .state(bomImportExcelDTO.getState())
                    .bomDescribe(bomImportExcelDTO.getBomDescribe())
                    .version(bomImportExcelDTO.getVersion())
                    .createBy(operationUsername)
                    .approver(bomImportExcelDTO.getApprover())
                    .approvalStatus(approvalStatus)
                    .importTime(new Date())
//                    .updateTime(DateUtil.parse(bomImportExcelDTO.getUpdateTime(), DateUtil.DATETIME_FORMAT))
                    .updateTime(bomImportExcelDTO.getUpdateTime())
                    .createTime(Objects.nonNull(bomImportExcelDTO.getCreateTime()) ? bomImportExcelDTO.getCreateTime() : new Date())
                    .craftCode(bomImportExcelDTO.getCraftCode())
                    .editor(bomImportExcelDTO.getEditor())
                    .editorName(bomImportExcelDTO.getEditorName())
                    .bomRawMaterialEntities(bomRawMaterialEntities)
                    .isTemplate(false)
                    .build();

            result.add(bomEntity);
        }
        return result;
    }

    /**
     * 校验子物料
     */
    private boolean checkBomMaterial(String operationUsername, BomImportExcelDTO bomImportDTO, boolean canImport) {
        MaterialEntity materialEntity;
        List<BomMaterialImportDTO> bomImportDTOSonMaterialList = bomImportDTO.getSonMaterialList();
        // 子项类型为替代件的物料不保存到bom物料表里，而是当作新/旧替代方案的替代物料
        List<BomMaterialImportDTO> ramMaterialList = bomImportDTOSonMaterialList.stream()
                .filter(bomMaterialImportDTO -> !BomItemTypeEnum.REPLACE_ITEM.getName().equals(bomMaterialImportDTO.getBomTypeName()))
                .collect(Collectors.toList());
        List<BomMaterialImportDTO> replaceItemList = bomImportDTOSonMaterialList.stream()
                .filter(bomMaterialImportDTO -> BomItemTypeEnum.REPLACE_ITEM.getName().equals(bomMaterialImportDTO.getBomTypeName()))
                .collect(Collectors.toList());
        Map<String, BomMaterialImportDTO> rawMap = ramMaterialList.stream()
                .collect(Collectors.toMap(o -> o.getCode() + Constants.CROSSBAR + o.getValueNames(), o -> o, (v1, v2) -> v1));
        Map<String, List<BomMaterialImportDTO>> replaceItemMap = replaceItemList.stream()
                .collect(Collectors.groupingBy(o -> o.getReplaceMaterialCode() + Constants.CROSSBAR + o.getReplaceMaterialSkuNames()));

        for (BomMaterialImportDTO rawMaterial : ramMaterialList) {
            StringBuilder importResult1 = new StringBuilder();
            //校验子物料编码
            if (StringUtils.isBlank(rawMaterial.getCode())) {
                importResult1.append("子物料编码字段不能为空；");
                canImport = false;
            } else {
                materialEntity = materialService.getSimpleMaterialByCode(rawMaterial.getCode());
                if (materialEntity == null) {
                    importResult1.append("子物料编码字段不合法,请检查物料编码是否存在；");
                    canImport = false;
                }
                if (rawMaterial.getCode().equals(bomImportDTO.getMaterialCode())) {
                    importResult1.append("子物料编码和BOM物料编码不可重复；");
                    canImport = false;
                }
            }
            //检验子物料BOM子项类型
            if (StringUtils.isBlank(rawMaterial.getBomTypeName())) {
                rawMaterial.setBomType(BomItemTypeEnum.ORDINARY_PARTS.getCode() + "");
            } else {
                Integer bomItemName = BomItemTypeEnum.getCodeByName(rawMaterial.getBomTypeName());
                if (Objects.isNull(bomItemName)) {
                    importResult1.append("BOM子项类型只能为普通件/替代件/返还件/联产品/副产品");
                    canImport = false;
                }
                rawMaterial.setBomType(String.valueOf(BomItemTypeEnum.getCodeByName(rawMaterial.getBomTypeName())));
            }
            // 校验子物料特征参数 -> 填充skuId
            if (StringUtils.isNotBlank(rawMaterial.getAuxiliaryAttrValues())) {
                List<MaterialAuxiliaryAttrEntity> auxiliaryAttrs = materialAuxiliaryAttrService.lambdaQuery().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, rawMaterial.getCode()).list();
                Set<String> attrValueNames = new HashSet<>(Arrays.asList(rawMaterial.getAuxiliaryAttrValues().split(com.yelink.dfscommon.constant.Constant.SEP)));
                if (CollectionUtils.isEmpty(auxiliaryAttrs) || auxiliaryAttrs.size() != attrValueNames.size()) {
                    importResult1.append("只能导入该物料配置的特征参数；");
                    canImport = false;
                } else {
                    // 查询特征参数值列表
                    Map<String, String> attrCodeNameMap = auxiliaryAttrs.stream().collect(Collectors.toMap(MaterialAuxiliaryAttrEntity::getAuxiliaryAttrCode, MaterialAuxiliaryAttrEntity::getAuxiliaryAttrName));
                    AuxiliaryAttrValueSelectDTO selectDTO = AuxiliaryAttrValueSelectDTO.builder()
                            .auxiliaryAttrCode(String.join(com.yelink.dfscommon.constant.Constant.SEP, attrCodeNameMap.keySet()))
                            .build();
                    List<AuxiliaryAttrValueEntity> auxiliaryAttrValues = auxiliaryAttrValueService.getList(selectDTO);
                    Map<String, AuxiliaryAttrValueEntity> valueNameMap = auxiliaryAttrValues.stream().collect(Collectors.toMap(AuxiliaryAttrValueEntity::getValueName, v -> v));
                    List<MaterialAuxiliaryAttrSkuEntity> addSkuList = new ArrayList<>();
                    for (String attrValueName : attrValueNames) {
                        AuxiliaryAttrValueEntity auxiliaryAttrValueEntity = valueNameMap.get(attrValueName);
                        if (Objects.nonNull(auxiliaryAttrValueEntity)) {
                            addSkuList.add(MaterialAuxiliaryAttrSkuEntity.builder()
                                    .createBy(operationUsername)
                                    .createTime(new Date())
                                    .materialCode(rawMaterial.getCode())
                                    .auxiliaryAttrCode(auxiliaryAttrValueEntity.getAuxiliaryAttrCode())
                                    .auxiliaryAttrName(attrCodeNameMap.get(auxiliaryAttrValueEntity.getAuxiliaryAttrCode()))
                                    .valueCode(auxiliaryAttrValueEntity.getValueCode())
                                    .valueName(auxiliaryAttrValueEntity.getValueName())
                                    .build());
                        }
                    }
                    if (addSkuList.size() != attrValueNames.size()) {
                        importResult1.append("导入特征参数与系统需要配置的特征参数不匹配；");
                        canImport = false;
                    } else {
                        try {
                            //新增物料sku
                            MaterialSkuInsertResDTO skuDTO = materialSkuService.add(addSkuList, operationUsername);
                            rawMaterial.setSkuId(skuDTO.getSkuId());
                        } catch (Exception e) {
                            importResult1.append("新增物料sku错误；");
                            canImport = false;
                        }
                    }
                }
            }
            //校验子物料用量分子
            if (ObjectUtils.isEmpty(rawMaterial.getNum())) {
                rawMaterial.setNum(1.0);
            }
            //校验子物料用量分母
            if (ObjectUtils.isEmpty(rawMaterial.getNumber())) {
                rawMaterial.setNumber(1.0);
            }
            //校验子物料笃定损耗
            if (null == rawMaterial.getFixedDamage()) {
                rawMaterial.setFixedDamage(0.0);
            } else {
                if (rawMaterial.getFixedDamage() < 0) {
                    importResult1.append("子物料固定损耗必须是非负数；");
                    canImport = false;
                }
            }
            //校验子物料损耗率
            if (null == rawMaterial.getLossRate()) {
                rawMaterial.setLossRate(0.0);
            } else {
                if (rawMaterial.getLossRate() < 0 || rawMaterial.getLossRate() >= 1) {
                    importResult1.append("子物料损耗率必须是0 <= 损耗率 < 1的小数；");
                    canImport = false;
                }
            }
            if (StringUtils.isNotBlank(rawMaterial.getValueNames())) {
                try {
                    String[] split1 = rawMaterial.getValueNames().split(",");
                    Set<String> attrValueNames = new HashSet<>(Arrays.asList(split1));

                    MaterialSkuInsertResDTO materialSkuInsertResDTO = materialAuxiliaryAttrSkuService.add(rawMaterial.getCode(), attrValueNames, operationUsername);
                    rawMaterial.setSkuId(materialSkuInsertResDTO.getSkuId());
                } catch (ResponseException e) {
                    importResult1.append("物料特征参数配置错误");
                    canImport = false;
                    log.error("", e);
                }
            }
            rawMaterial.setImportResult1(importResult1.toString());
        }
        // 子项类型为替代件的物料不保存到bom物料表里，而是当作新替代方案的替代物料
        for (BomMaterialImportDTO replaceMaterial : replaceItemList) {
            MaterialSkuInsertResDTO materialSkuInsertResDTO = new MaterialSkuInsertResDTO();
            if (StringUtils.isNotBlank(replaceMaterial.getReplaceMaterialSkuNames())) {
                materialSkuInsertResDTO = getSkuDto(operationUsername, replaceMaterial);
            }
            String key = replaceMaterial.getReplaceMaterialCode() + Constants.CROSSBAR + replaceMaterial.getReplaceMaterialSkuNames();
            StringBuilder importResult1 = new StringBuilder();
            if (StringUtils.isAllBlank(replaceMaterial.getReplaceMaterialCode(), replaceMaterial.getReplaceMaterialSkuNames())) {
                importResult1.append("子物料BOM子项类型为替代件时，被替代的普通件物料编码和特征参数不能同时为空；");
                canImport = false;
            } else if (!rawMap.containsKey(key)) {
                // 被替代的普通件可能还未存储到数据库中，需要校验当前文档中的和数据库中的数据
                Long count = bomRawMaterialService.lambdaQuery().eq(BomRawMaterialEntity::getCode, replaceMaterial.getReplaceMaterialCode())
                        .eq(Objects.nonNull(materialSkuInsertResDTO.getSkuId()), BomRawMaterialEntity::getSkuId, materialSkuInsertResDTO.getSkuId())
                        .count();
                if (count == 0) {
                    // 被替代的普通件需要在子物料编码列中存在
                    importResult1.append("被替代的普通件物料不存在；");
                    canImport = false;
                }
            }
            replaceMaterial.setImportResult1(importResult1.toString());
        }

        for (Map.Entry<String, List<BomMaterialImportDTO>> entry : replaceItemMap.entrySet()) {
            String key = entry.getKey();
            List<BomMaterialImportDTO> importDTOS = entry.getValue();
            ArrayList<ReplaceSchemeMaterialEntity> list = new ArrayList<>();
            ReplaceSchemeEntity replaceSchemeEntity = new ReplaceSchemeEntity();
            // 使用缓存保存主对象,编码采用自动生成方式
            String schemeCode = codeFactory.getOrderNumber("sc");
            BomMaterialImportDTO bomMaterialImportDTO = rawMap.get(key);
            if (bomMaterialImportDTO == null) {
                continue;
            }
            if (!redisTemplate.hasKey(RedisKeyPrefix.REPLACE_SCHEME_KEY + schemeCode)) {
                replaceSchemeEntity = ReplaceSchemeEntity.builder()
                        .schemeCode(schemeCode)
                        .schemeName(schemeCode)
                        .replaceStrategy(MaterialReplaceStrategyEnum.MIXED_AND_BATCH_REPLACE.getCode())
                        .mainMaterialCode(bomMaterialImportDTO.getCode())
                        .mainBomType(BomItemTypeEnum.getCodeByName(bomMaterialImportDTO.getBomTypeName()))
                        .mainMolecule(1.0)
                        .mainDenominator(1.0)
                        .approver(operationUsername)
                        .build();
                redisTemplate.opsForValue().set(RedisKeyPrefix.REPLACE_SCHEME_KEY + schemeCode, JSON.toJSONString(replaceSchemeEntity));
            } else {
                replaceSchemeEntity = JSON.parseObject((String) redisTemplate.opsForValue().get(RedisKeyPrefix.REPLACE_SCHEME_KEY + schemeCode), ReplaceSchemeEntity.class);
            }

            for (BomMaterialImportDTO replaceMaterial : importDTOS) {
                StringBuilder importResult1 = new StringBuilder();
                MaterialSkuInsertResDTO materialSkuInsertResDTO = null;
                if (StringUtils.isNotBlank(replaceMaterial.getReplaceMaterialSkuNames())) {
                    materialSkuInsertResDTO = getSkuDto(operationUsername, replaceMaterial);
                }

                // 如果普通件关联了已存在的替代方案编码，需要判断子物料子项类型有没有为替代件的物料，有则进行强关联，没有则将替代方案下面的物料进行全绑定
                if (StringUtils.isNotBlank(bomMaterialImportDTO.getReplaceSchemeCodes())) {
                    String[] split = bomMaterialImportDTO.getReplaceSchemeCodes().split(Constant.SEP);
                    List<Integer> ids = new ArrayList<>();
                    for (String s : split) {
                        List<ReplaceSchemeMaterialEntity> enableReplaceMaterial = replaceSchemeService.getReplaceMaterial(bomMaterialImportDTO.getCode());
                        List<ReplaceSchemeMaterialEntity> replaceSchemeMaterialEntities = enableReplaceMaterial.stream()
                                .filter(o -> o.getSchemeCode().equals(s))
                                .filter(o -> o.getReplaceMaterialCode().equals(replaceMaterial.getCode()))
                                .collect(Collectors.toList());
                        if (replaceSchemeMaterialEntities.isEmpty()) {
                            importResult1.append(replaceMaterial.getImportResult1());
                            importResult1.append("替代方案不存在或者替代方案下的替代物料不存在；");
                            canImport = false;
                        } else {
                            replaceSchemeMaterialEntities.forEach(replaceSchemeMaterialEntity -> ids.add(replaceSchemeMaterialEntity.getId()));
                        }
                    }
                    // 找到被替代的普通件
                    bomMaterialImportDTO.setReplaceMaterialIds(Joiner.on(Constant.SEP).join(ids));
                } else {
                    // 如果没有填替代方案编码，则组转数据用来新增替代方案
                    replaceSchemeEntity.setSkuId(Objects.nonNull(materialSkuInsertResDTO) ? materialSkuInsertResDTO.getSkuId() : null);
                    list.add(ReplaceSchemeMaterialEntity.builder()
                            .schemeCode(schemeCode)
                            .replaceMaterialCode(replaceMaterial.getCode())
                            .replaceBomType(BomItemTypeEnum.REPLACE_ITEM.getCode())
                            .replaceMolecule(replaceMaterial.getNum())
                            .replaceDenominator(replaceMaterial.getNumber())
                            .priority(1)
                            .remark(replaceMaterial.getRemark())
                            .build());
                }
                replaceMaterial.setImportResult1(importResult1.toString());
            }
            replaceSchemeEntity.setSchemeMaterialEntities(list);
            // 新增生效的替代方案
            if (CollectionUtils.isNotEmpty(list) && canImport) {
                if (approveConfigService.getConfigByCode(ApproveModuleEnum.REPLACE_SCHEME.getCode())) {
                    replaceSchemeService.add(replaceSchemeEntity, operationUsername);
                    replaceSchemeService.approveBatch(ApproveBatchDTO.builder()
                            .approvalStatus(ApprovalStatusEnum.APPROVED.getCode())
                            .actualApprover(operationUsername)
                            .ids(Collections.singletonList(replaceSchemeEntity.getId()))
                            .build());
                } else {
                    replaceSchemeService.addReleasedOrder(replaceSchemeEntity, operationUsername);
                }
                // 查询新增后的替代方案物料id
                List<ReplaceSchemeMaterialEntity> schemeMaterialEntities = replaceSchemeMaterialService.lambdaQuery()
                        .eq(ReplaceSchemeMaterialEntity::getSchemeCode, replaceSchemeEntity.getSchemeCode())
                        .list();
                List<Integer> ids = schemeMaterialEntities.stream().map(ReplaceSchemeMaterialEntity::getId).collect(Collectors.toList());
                bomMaterialImportDTO.setReplaceMaterialIds(Joiner.on(Constant.SEP).join(ids));
            }
        }

        return canImport;
    }

    private MaterialSkuInsertResDTO getSkuDto(String operationUsername, BomMaterialImportDTO replaceMaterial) {
        String[] split1 = replaceMaterial.getReplaceMaterialSkuNames().split(",");
        Set<String> attrValueNames = new HashSet<>(Arrays.asList(split1));
        return materialAuxiliaryAttrSkuService.add(replaceMaterial.getReplaceMaterialCode(), attrValueNames, operationUsername);
    }


    @Override
    public ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.BOM_IMPORT_PROGRESS);
    }


    @Override
    public BomRouteDTO getRelateBomMaterials(String materialCode, Integer skuId, String productionState) {
        skuId = skuId == null ? 0 : skuId;
        // 兼容历史逻辑，不填默认为量产
        if (StringUtils.isBlank(productionState)) {
            productionState = ProductionStateEnum.MASS_PRODUCTION.getCode();
        }

        //获取最新BOM信息
        LambdaQueryWrapper<BomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BomEntity::getCode, materialCode)
                .eq(BomEntity::getSkuId, skuId)
                .eq(BomEntity::getProductionState, productionState)
                .orderByDesc(BomEntity::getCreateTime)
                .orderByDesc(BomEntity::getId)
                .last("limit 1");
        BomEntity one = this.getOne(wrapper);

        if (Objects.isNull(one)) {
            // 如果指定skuId找不到，则查找skuId=0的记录
            wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BomEntity::getCode, materialCode)
                    .eq(BomEntity::getSkuId, 0)
                    .eq(BomEntity::getProductionState, productionState)
                    .orderByDesc(BomEntity::getCreateTime)
                    .orderByDesc(BomEntity::getId)
                    .last("limit 1");
            one = this.getOne(wrapper);
            if (Objects.isNull(one)) {
                return null;
            }
        }
        BomRouteDTO build = BomRouteDTO.builder().materialCode(one.getCode()).skuId(one.getSkuId()).build();
        List<BomRouteDTO> bomTrees = getBomTrees(materialCode, skuId, productionState);
        build.setChildrenTree(bomTrees);
        return build;
    }

    @Override
    public List<SimpleBomExportDTO> convertToSimpleBomExportDTO(List<BomEntity> records) {
        List<SimpleBomExportDTO> exportEntities = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return exportEntities;
        }
        for (BomEntity bomEntity : records) {
            exportEntities.add(SimpleBomExportDTO.builder()
                    .bomNum(bomEntity.getBomNum())
                    .stateName(bomEntity.getStateName())
                    .version(bomEntity.getVersion())
                    .materialCode(bomEntity.getCode())
                    .materialName(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getName() : null)
                    .createByNickname(bomEntity.getCreateByNickname())
                    .updateByNickname(bomEntity.getUpdateByNickname())
                    .editorName(bomEntity.getEditorName())
                    .createTime(bomEntity.getCreateTime())
                    .updateTime(bomEntity.getUpdateTime())
                    .craftCode(bomEntity.getCraftCode())
                    .craftVersion(bomEntity.getCraftVersion())
                    .productionStateName(ProductionStateEnum.getNameByCode(
                            StringUtils.isNotBlank(bomEntity.getProductionState()) ?
                                    bomEntity.getProductionState() :
                                    ProductionStateEnum.MASS_PRODUCTION.getCode()))
                    .materialDrawingNumber(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getDrawingNumber() : null)
                    .materialStandard(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getStandard() : null)
                    .materialRawMaterial(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getRawMaterial() : null)
                    .build());
        }
        return exportEntities;
    }

    @Override
    public void uploadSimpleListExportTemplate(MultipartFile file, String username) throws IOException {
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(
                file.getName(),
                file.getOriginalFilename(),
                file.getContentType(),
                EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile, ModelUploadFileEnum.BOM_SIMPLE_REPORT.getCode(), username);
    }

    @Override
    public Long simpleListExportTask(BomSelectDTO selectDTO, String username) {
        DataExportParam<BomSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.BOM_SIMPLE_RECORD.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.BOM_SIMPLE_RECORD.name());
        if (Boolean.TRUE.equals(selectDTO.getExportMultiLevelBom())) {
            dataExportParam.setExportFileName(BusinessCodeEnum.MULTI_BOM_SIMPLE_RECORD.getCodeName());
            dataExportParam.setBusinessCode(BusinessCodeEnum.MULTI_BOM_SIMPLE_RECORD.name());
        }
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(BomSelectDTO.class.getName(), selectDTO);
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, BomSimpleListExportHandler.class);
    }

    @Override
    public IPage<ExcelTask> simpleTaskPage(Integer current, Integer size, Boolean exportMultiLevelBom) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.BOM_SIMPLE_RECORD.name());
        if (Boolean.TRUE.equals(exportMultiLevelBom)) {
            excelTask.setBusinessCode(BusinessCodeEnum.MULTI_BOM_SIMPLE_RECORD.name());
        }
        return excelService.listPage(excelTask, current, size);
    }

    @Override
    public ExcelTask simpleListTaskById(Long taskId, Boolean exportMultiLevelBom) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.BOM_SIMPLE_RECORD.name());
        if (Boolean.TRUE.equals(exportMultiLevelBom)) {
            excelTask.setBusinessCode(BusinessCodeEnum.MULTI_BOM_SIMPLE_RECORD.name());
        }
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        return records.get(0);
    }

    @Override
    public List<DetailBomExportDTO> convertToDetailBomExportDTO(List<BomEntity> records, Boolean exportReplaceMaterial) {
        List<DetailBomExportDTO> exportEntities = new ArrayList<>();
        exportReplaceMaterial = !Objects.isNull(exportReplaceMaterial) && exportReplaceMaterial;
        if (CollectionUtils.isEmpty(records)) {
            return exportEntities;
        }
        // 获取子物料数据
        List<Integer> bomIds = records.stream().map(BomEntity::getId).collect(Collectors.toList());
        Map<Integer, List<BomRawMaterialEntity>> bomRawMap = bomRawMaterialService.lambdaQuery().in(BomRawMaterialEntity::getBomId, bomIds)
                .list().stream().collect(Collectors.groupingBy(BomRawMaterialEntity::getBomId));
        // 填充数据
        for (BomEntity bomEntity : records) {
            List<BomRawMaterialEntity> rawMaterialEntities = bomRawMap.get(bomEntity.getId());
            // 获取物料列表
            List<String> subMaterialCodes = rawMaterialEntities.stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
            Map<String, MaterialEntity> subMaterialMap = materialService.lambdaQuery().in(MaterialEntity::getCode, subMaterialCodes)
                    .list().stream()
                    .collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
            SkuEntity skuEntity = skuService.getById(bomEntity.getSkuId());
            // 查询替代关系
            BomReplaceMaterialSelectDTO selectDTO = BomReplaceMaterialSelectDTO.builder().bomCode(bomEntity.getBomNum()).build();
            Page<ReplaceSchemeVO> schemeVOPage = getReplaceMaterialsByBomCode(selectDTO);
            Map<String, ReplaceSchemeVO> replaceSchemeVOMap = schemeVOPage.getRecords().stream()
                    .collect(Collectors.toMap(o -> o.getMainMaterialCode() + Constants.CROSSBAR + o.getReplaceMaterialId(), o -> o));

            List<Integer> bomRawMaterialIds = rawMaterialEntities.stream().map(BomRawMaterialEntity::getId).collect(Collectors.toList());
            Map<Integer, List<ReplaceBomMaterialEntity>> bomMaterialReplaceMap = replaceBomMaterialService.lambdaQuery()
                    .in(ReplaceBomMaterialEntity::getBomMaterialId, bomRawMaterialIds)
                    .list().stream()
                    .collect(Collectors.groupingBy(ReplaceBomMaterialEntity::getBomMaterialId));

            for (BomRawMaterialEntity rawMaterialEntity : rawMaterialEntities) {
                SkuEntity subSkuEntity = skuService.getById(rawMaterialEntity.getSkuId());
                MaterialEntity subMaterialEntity = subMaterialMap.get(rawMaterialEntity.getCode());
                DetailBomExportDTO bomExportDTO = DetailBomExportDTO.builder()
                        .bomNum(bomEntity.getBomNum())
                        .stateName(bomEntity.getStateName())
                        .productionStateName(bomEntity.getProductionStateName())
                        .version(bomEntity.getVersion())
                        .materialCode(bomEntity.getCode())
                        .auxiliaryAttrValues(Objects.isNull(skuEntity) ? null : skuEntity.getSkuName())
                        .createByNickname(bomEntity.getCreateByNickname())
                        .updateByNickname(bomEntity.getUpdateByNickname())
                        .editorName(bomEntity.getEditorName())
                        .createTime(bomEntity.getCreateTime())
                        .updateTime(bomEntity.getUpdateTime())
                        .craftCode(bomEntity.getCraftCode())
                        .craftVersion(bomEntity.getCraftVersion())
                        .subMaterialCode(rawMaterialEntity.getCode())
                        .subAuxiliaryAttrValues(Objects.isNull(subSkuEntity) ? null : subSkuEntity.getSkuName())
                        .subBomTypeName(StringUtils.isNotBlank(rawMaterialEntity.getBomType()) ? BomItemTypeEnum.getNameByCode(Integer.valueOf(rawMaterialEntity.getBomType())) : null)
                        // 分子
                        .subBomNumerator(rawMaterialEntity.getNum())
                        // 分母
                        .subBomDenominator(rawMaterialEntity.getNumber())
                        .subRemark(rawMaterialEntity.getRemark())
                        .subFixedDamage(rawMaterialEntity.getFixedDamage())
                        .subLossRate(rawMaterialEntity.getLossRate())
                        // 主物料相关物料字段
                        .materialName(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getName() : null)
                        .materialDrawingNumber(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getDrawingNumber() : null)
                        .materialStandard(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getStandard() : null)
                        .materialRawMaterial(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getRawMaterial() : null)
                        // 子物料相关物料字段
                        .subMaterialDrawingNumber(subMaterialEntity.getDrawingNumber())
                        .subMaterialStandard(subMaterialEntity.getStandard())
                        .subMaterialLevel(subMaterialEntity.getLevel())
                        .subMaterialName(subMaterialEntity.getName())
                        .customFieldOne(subMaterialEntity.getCustomFieldOne() != null ? subMaterialEntity.getCustomFieldOne().toString() : null)
                        .customFieldTwo(subMaterialEntity.getCustomFieldTwo() != null ? subMaterialEntity.getCustomFieldTwo().toString() : null)
                        .customFieldThree(subMaterialEntity.getCustomFieldThree() != null ? subMaterialEntity.getCustomFieldThree().toString() : null)
                        .customFieldFour(subMaterialEntity.getCustomFieldFour() != null ? subMaterialEntity.getCustomFieldFour().toString() : null)
                        .customFieldFive(subMaterialEntity.getCustomFieldFive() != null ? subMaterialEntity.getCustomFieldFive().toString() : null)
                        .customFieldSix(subMaterialEntity.getCustomFieldSix() != null ? subMaterialEntity.getCustomFieldSix().toString() : null)
                        .customFieldSeven(subMaterialEntity.getCustomFieldSeven() != null ? subMaterialEntity.getCustomFieldSeven().toString() : null)
                        .customFieldEight(subMaterialEntity.getCustomFieldEight() != null ? subMaterialEntity.getCustomFieldEight().toString() : null)
                        .customFieldNine(subMaterialEntity.getCustomFieldNine() != null ? subMaterialEntity.getCustomFieldNine().toString() : null)
                        .customFieldTen(subMaterialEntity.getCustomFieldTen() != null ? subMaterialEntity.getCustomFieldTen().toString() : null)
                        .customFieldEleven(subMaterialEntity.getCustomFieldEleven() != null ? subMaterialEntity.getCustomFieldEleven().toString() : null)
                        .build();

                // 先导出原始BOM子物料记录
                exportEntities.add(bomExportDTO);

                // 只有选择导出替代关系时，才额外导出替代物料
                if (exportReplaceMaterial) {
                    List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = bomMaterialReplaceMap.get(rawMaterialEntity.getId());
                    if (CollectionUtils.isNotEmpty(replaceBomMaterialEntities)) {
                        List<Integer> replaceMaterialIds = replaceBomMaterialEntities.stream().map(ReplaceBomMaterialEntity::getReplaceMaterialId).collect(Collectors.toList());
                        List<ReplaceSchemeVO> replaceSchemeVOS = replaceMaterialIds.stream()
                                .filter(o -> !o.equals(0))
                                .map(o -> replaceSchemeVOMap.get(rawMaterialEntity.getCode() + Constants.CROSSBAR + o)).collect(Collectors.toList());
                        for (ReplaceSchemeVO replaceSchemeVO : replaceSchemeVOS) {
                            // 为替代物料创建新的导出记录
                            DetailBomExportDTO replaceExportDTO = DetailBomExportDTO.builder()
                                    .bomNum(bomEntity.getBomNum())
                                    .stateName(bomEntity.getStateName())
                                    .version(bomEntity.getVersion())
                                    .materialCode(bomEntity.getCode())
                                    .auxiliaryAttrValues(Objects.isNull(skuEntity) ? null : skuEntity.getSkuName())
                                    .createByNickname(bomEntity.getCreateByNickname())
                                    .updateByNickname(bomEntity.getUpdateByNickname())
                                    .editorName(bomEntity.getEditorName())
                                    .createTime(bomEntity.getCreateTime())
                                    .updateTime(bomEntity.getUpdateTime())
                                    .craftCode(bomEntity.getCraftCode())
                                    .craftVersion(bomEntity.getCraftVersion())
                                    .subMaterialCode(rawMaterialEntity.getCode())
                                    .subAuxiliaryAttrValues(Objects.isNull(subSkuEntity) ? null : subSkuEntity.getSkuName())
                                    .subBomTypeName(StringUtils.isNotBlank(rawMaterialEntity.getBomType()) ? BomItemTypeEnum.getNameByCode(Integer.valueOf(rawMaterialEntity.getBomType())) : null)
                                    // 分子
                                    .subBomNumerator(rawMaterialEntity.getNum())
                                    // 分母
                                    .subBomDenominator(rawMaterialEntity.getNumber())
                                    .subRemark(rawMaterialEntity.getRemark())
                                    .subFixedDamage(rawMaterialEntity.getFixedDamage())
                                    .subLossRate(rawMaterialEntity.getLossRate())
                                    // 主物料相关物料字段
                                    .materialName(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getName() : null)
                                    .materialDrawingNumber(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getDrawingNumber() : null)
                                    .materialStandard(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getStandard() : null)
                                    .materialRawMaterial(Objects.nonNull(bomEntity.getMaterialFields()) ? bomEntity.getMaterialFields().getRawMaterial() : null)
                                    // 子物料相关物料字段
                                    .subMaterialDrawingNumber(subMaterialEntity.getDrawingNumber())
                                    .subMaterialStandard(subMaterialEntity.getStandard())
                                    .subMaterialLevel(subMaterialEntity.getLevel())
                                    .subMaterialName(subMaterialEntity.getName())
                                    .customFieldOne(subMaterialEntity.getCustomFieldOne() != null ? subMaterialEntity.getCustomFieldOne().toString() : null)
                                    .customFieldTwo(subMaterialEntity.getCustomFieldTwo() != null ? subMaterialEntity.getCustomFieldTwo().toString() : null)
                                    .customFieldThree(subMaterialEntity.getCustomFieldThree() != null ? subMaterialEntity.getCustomFieldThree().toString() : null)
                                    .customFieldFour(subMaterialEntity.getCustomFieldFour() != null ? subMaterialEntity.getCustomFieldFour().toString() : null)
                                    .customFieldFive(subMaterialEntity.getCustomFieldFive() != null ? subMaterialEntity.getCustomFieldFive().toString() : null)
                                    .customFieldSix(subMaterialEntity.getCustomFieldSix() != null ? subMaterialEntity.getCustomFieldSix().toString() : null)
                                    .customFieldSeven(subMaterialEntity.getCustomFieldSeven() != null ? subMaterialEntity.getCustomFieldSeven().toString() : null)
                                    .customFieldEight(subMaterialEntity.getCustomFieldEight() != null ? subMaterialEntity.getCustomFieldEight().toString() : null)
                                    .customFieldNine(subMaterialEntity.getCustomFieldNine() != null ? subMaterialEntity.getCustomFieldNine().toString() : null)
                                    .customFieldTen(subMaterialEntity.getCustomFieldTen() != null ? subMaterialEntity.getCustomFieldTen().toString() : null)
                                    .customFieldEleven(subMaterialEntity.getCustomFieldEleven() != null ? subMaterialEntity.getCustomFieldEleven().toString() : null)
                                    // 替代关系字段
                                    .schemeCode(replaceSchemeVO.getSchemeCode())
                                    .schemeName(replaceSchemeVO.getSchemeName())
                                    .mainMaterialCode(rawMaterialEntity.getCode())
                                    .mainMaterialName(subMaterialEntity.getName())
                                    .replaceMaterialCode(replaceSchemeVO.getReplaceMaterialCode())
                                    .replaceMaterialName(replaceSchemeVO.getReplaceMaterialFields().getName())
                                    .replaceMolecule(replaceSchemeVO.getReplaceMolecule())
                                    .replaceDenominator(replaceSchemeVO.getReplaceDenominator())
                                    .build();
                            exportEntities.add(replaceExportDTO);
                        }
                    }
                }
            }
        }
        return exportEntities;
    }

    @Override
    public void uploadDetailListExportTemplate(MultipartFile file, String username) throws IOException {
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(
                file.getName(),
                file.getOriginalFilename(),
                file.getContentType(),
                EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile, ModelUploadFileEnum.BOM_DETAIL_REPORT.getCode(), username);
    }

    @Override
    public Long detailListExportTask(BomSelectDTO selectDTO, String username) {
        DataExportParam<BomSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.BOM_DETAIL_RECORD.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.BOM_DETAIL_RECORD.name());
        if (Boolean.TRUE.equals(selectDTO.getExportMultiLevelBom())) {
            dataExportParam.setExportFileName(BusinessCodeEnum.MULTI_BOM_DETAIL_RECORD.getCodeName());
            dataExportParam.setBusinessCode(BusinessCodeEnum.MULTI_BOM_DETAIL_RECORD.name());
        }
        if (Boolean.TRUE.equals(selectDTO.getExportMultiLevelBom()) && Boolean.TRUE.equals(selectDTO.getExportReplaceMaterial())) {
            // 多级BOM详细信息+替代关系数据导出
            dataExportParam.setExportFileName(BusinessCodeEnum.MULTI_BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.getCodeName());
            dataExportParam.setBusinessCode(BusinessCodeEnum.MULTI_BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.name());
        } else if (Boolean.TRUE.equals(selectDTO.getExportMultiLevelBom())) {
            // 多级BOM详细信息数据导出记录
            dataExportParam.setExportFileName(BusinessCodeEnum.MULTI_BOM_DETAIL_RECORD.getCodeName());
            dataExportParam.setBusinessCode(BusinessCodeEnum.MULTI_BOM_DETAIL_RECORD.name());
        } else if (Boolean.TRUE.equals(selectDTO.getExportReplaceMaterial())) {
            // BOM详细信息+替代关系数据导出
            dataExportParam.setExportFileName(BusinessCodeEnum.BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.getCodeName());
            dataExportParam.setBusinessCode(BusinessCodeEnum.BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.name());
        }
        dataExportParam.setCreateUserCode(username);
        selectDTO.setTemplateSheetName("数据源");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(BomSelectDTO.class.getName(), selectDTO);
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, BomDetailListExportHandler.class);
    }

    @Override
    public IPage<ExcelTask> detailTaskPage(Integer current, Integer size, Boolean exportMultiLevelBom, Boolean exportReplaceMaterial) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.BOM_DETAIL_RECORD.name());
        if (Boolean.TRUE.equals(exportMultiLevelBom) && Boolean.TRUE.equals(exportReplaceMaterial)) {
            // 多级BOM详细信息+替代关系数据导出
            excelTask.setBusinessCode(BusinessCodeEnum.MULTI_BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.name());
        } else if (Boolean.TRUE.equals(exportMultiLevelBom)) {
            // 多级BOM详细信息数据导出记录
            excelTask.setBusinessCode(BusinessCodeEnum.MULTI_BOM_DETAIL_RECORD.name());
        } else if (Boolean.TRUE.equals(exportReplaceMaterial)) {
            // BOM详细信息+替代关系数据导出
            excelTask.setBusinessCode(BusinessCodeEnum.BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.name());
        }
        return excelService.listPage(excelTask, current, size);
    }

    @Override
    public ExcelTask detailListTaskById(Long taskId, Boolean exportMultiLevelBom, Boolean exportReplaceMaterial) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.BOM_DETAIL_RECORD.name());
        if (Boolean.TRUE.equals(exportMultiLevelBom) && Boolean.TRUE.equals(exportReplaceMaterial)) {
            // 多级BOM详细信息+替代关系数据导出
            excelTask.setBusinessCode(BusinessCodeEnum.MULTI_BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.name());
        } else if (Boolean.TRUE.equals(exportMultiLevelBom)) {
            // 多级BOM详细信息数据导出记录
            excelTask.setBusinessCode(BusinessCodeEnum.MULTI_BOM_DETAIL_RECORD.name());
        } else if (Boolean.TRUE.equals(exportReplaceMaterial)) {
            // BOM详细信息+替代关系数据导出
            excelTask.setBusinessCode(BusinessCodeEnum.BOM_DETAIL_AND_REPLACE_MATERIAL_RECORD.name());
        }
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        ExcelTask task = records.get(0);
        task.setEstimateCount(task.getTotalCount());
        return task;
    }

    @Override
    public BomEntity getBomDetailByBomCode(String bomCode) {
        BomEntity bomEntity = this.lambdaQuery().eq(BomEntity::getBomNum, bomCode).one();
        if (Objects.isNull(bomEntity)) {
            return null;
        }
        // 获取过滤联产品和副产品后的bom原料信息
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.getListByBomIdFilterOutputProduct(bomEntity.getId());
        bomEntity.setMaterialFields(materialService.getEntityByCodeAndSkuIdAndShowBom(bomEntity.getCode(), bomEntity.getSkuId()));

        // 获取物料相关字段
        List<MaterialCodeAndSkuIdSelectDTO> codeAndSkuIds = bomRawMaterialEntities.stream()
                .map(res -> MaterialCodeAndSkuIdSelectDTO.builder().materialCode(res.getCode()).skuId(res.getSkuId()).build())
                .collect(Collectors.toList());
        List<MaterialEntity> materials = materialService.listSimpleMaterialByCodesAndSkuIds(codeAndSkuIds);
        Map<String, MaterialEntity> keyMaterialMap = materials.stream().collect(Collectors.toMap(material -> ColumnUtil.getMaterialSku(material.getCode(), material.getSkuEntity()), v -> v));
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            MaterialEntity materialEntity = keyMaterialMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId()));
            bomRawMaterialEntity.setMaterialFields(materialEntity);
            // 设置用量
            Double useNum = MathUtil.divideDouble(bomRawMaterialEntity.getNum().doubleValue(), bomRawMaterialEntity.getNumber().doubleValue(), 1);
            bomRawMaterialEntity.setUseNum(useNum);
            // 展示子项类型名称
            bomRawMaterialEntity.setBomTypeName(bomRawMaterialEntity.getBomType() != null ? BomItemTypeEnum.getNameByCode(Integer.valueOf(bomRawMaterialEntity.getBomType())) : null);
        }
        bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities);
        return bomEntity;
    }

    private List<BomRouteDTO> getBomTrees(String materialCode, Integer skuId, String productionState) {
        skuId = skuId == null ? 0 : skuId;
        // 兼容历史逻辑，不填默认为量产
        if (StringUtils.isBlank(productionState)) {
            productionState = ProductionStateEnum.MASS_PRODUCTION.getCode();
        }

        //获取最新BOM信息
        LambdaQueryWrapper<BomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BomEntity::getCode, materialCode)
                .eq(BomEntity::getSkuId, skuId)
                .eq(BomEntity::getProductionState, productionState)
                .orderByDesc(BomEntity::getCreateTime)
                .orderByDesc(BomEntity::getId)
                .last("limit 1");
        BomEntity one = this.getOne(wrapper);

        if (Objects.isNull(one)) {
            // 如果指定skuId找不到，则查找skuId=0的记录
            wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BomEntity::getCode, materialCode)
                    .eq(BomEntity::getSkuId, 0)
                    .eq(BomEntity::getProductionState, productionState)
                    .orderByDesc(BomEntity::getCreateTime)
                    .orderByDesc(BomEntity::getId)
                    .last("limit 1");
            one = this.getOne(wrapper);
            if (Objects.isNull(one)) {
                return null;
            }
        }
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.getListByBomId(one.getId());
        List<BomRouteDTO> bomRouteDTOS = new ArrayList<>(16);
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            BomRouteDTO build1 = BomRouteDTO.builder().materialCode(bomRawMaterialEntity.getCode()).skuId(one.getSkuId()).build();
            List<BomRouteDTO> bomRouteDTO = getBomTrees(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId(), productionState);
            build1.setChildrenTree(bomRouteDTO);
            bomRouteDTOS.add(build1);
        }
        return bomRouteDTOS;
    }

    private static final String BOM_ID = "bom_id";
    private static final String BOM_NUM = "bom_num";
    private static final String BOM_CODE = "bom_code";
    private static final String BOM_RAW_MATERIAL_ID = "bom_raw_material_id";

    private List<DataDelSchema> getCheckSchemas() {
        return Arrays.asList(
                DataDelSchema.builder().tableName("dfs_craft").originColumnName(BOM_ID).tableNickName("工艺").build(),
                DataDelSchema.builder().tableName("dfs_take_out_application").tableNickName("生产领料单").originColumnName(BOM_ID).build(),
                DataDelSchema.builder().tableName("dfs_work_order_material_list_material").tableNickName("生产工单用料清单物料").originColumnName(BOM_RAW_MATERIAL_ID).build()
        );
    }

    private List<DataDelSchema> getDeleteSchemas() {
        return Arrays.asList(
                DataDelSchema.builder().tableName("dfs_bom").originColumnName(BOM_NUM).build(),
//                DataDelSchema.builder().tableName("dfs_bom_tile").originColumnName(BOM_CODE).build(),// 这张表有问题，不动
                DataDelSchema.builder().tableName("dfs_replace_bom_material").tableNickName("物料替代方案").originColumnName(BOM_ID).build(),
                DataDelSchema.builder().tableName("dfs_bom_raw_material").originColumnName(BOM_ID).build()
        );
    }

    @Override
    public String deleteCheck(Integer bomId) {
        BomEntity bom = getById(bomId);
        // 赋予输入值
        List<DataDelSchema> dataDelSchemas = getCheckSchemas();
        preHandleData(bom, dataDelSchemas);
        // 关联数据校验
        commonDeleteHandler.doCheck(dataDelSchemas);
        return commonDeleteHandler.getRedisKey(dataDelSchemas);
    }

    private void preHandleData(BomEntity bom, List<DataDelSchema> dataDelSchemas) {
        List<BomRawMaterialEntity> bomRawMaterials = bomRawMaterialService.getListByBomId(bom.getId());
        List<Object> bomRawMaterialIds = bomRawMaterials.stream().map(BomRawMaterialEntity::getId).collect(Collectors.toList());
        for (DataDelSchema schema : dataDelSchemas) {
            if (BOM_ID.equals(schema.getOriginColumnName())) {
                schema.setValues(Collections.singletonList(bom.getId()));
            }
            if (BOM_NUM.equals(schema.getOriginColumnName())) {
                schema.setValues(Collections.singletonList(bom.getBomNum()));
            }
            if (BOM_CODE.equals(schema.getOriginColumnName())) {
                schema.setValues(Collections.singletonList(bom.getCode()));
            }
            if (BOM_RAW_MATERIAL_ID.equals(schema.getOriginColumnName())) {
                schema.setValues(bomRawMaterialIds);
            }
        }
    }

    @Override
    public Object deleteCheckProgress(String key) {
        return commonDeleteHandler.checkProgress(key);
    }

    @Override
    public Page<BomReverseDTO> reverseCheckBom(MaterialDetailDTO dto) {
        List<BomReverseDTO> list = new ArrayList<>();
        String materialCode = dto.getCode();
        Integer skuId = dto.getSkuId();
        // 查询传入的物料是否为bom的子物料
        LambdaQueryWrapper<BomRawMaterialEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BomRawMaterialEntity::getCode, materialCode)
                .eq(Objects.nonNull(skuId), BomRawMaterialEntity::getSkuId, skuId);
        Page<BomRawMaterialEntity> page = bomRawMaterialService.page(dto.buildPage(), wrapper);
        List<BomRawMaterialEntity> bomRawMaterialEntities = page.getRecords();
        if (CollectionUtils.isEmpty(bomRawMaterialEntities)) {
            return new Page<>();
        }
        // 查询子物料关联的bom
        List<Integer> bomIds = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getBomId).collect(Collectors.toList());
        Map<Integer, BomEntity> bomMap = this.listByIds(bomIds).stream()
                .peek(o -> {
                    o.setMaterialFields(materialService.lambdaQuery().eq(MaterialEntity::getCode, o.getCode()).one());
                    o.setSkuEntity(skuService.getById(o.getSkuId()));
                })
                .collect(Collectors.toMap(BomEntity::getId, o -> o));
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, materialCode).one();
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            BomEntity bomEntity = bomMap.get(bomRawMaterialEntity.getBomId());
            BomReverseDTO build = BomReverseDTO.builder()
                    .bomEntity(bomEntity)
                    .materialFields(materialEntity)
                    .skuEntity(skuService.getById(bomRawMaterialEntity.getSkuId()))
                    .build();
            getBomReverseDTOS(build);
            list.add(build);
        }
        Page<BomReverseDTO> dtoPage = new Page<>(dto.getCurrent(), dto.getSize());
        dtoPage.setRecords(list);
        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsert(BomUpdateInsertDTO dto) {
        BomEntity entity = JacksonUtil.convertObject(dto, BomEntity.class);
        Date now = new Date();

        // 兼容历史逻辑，如果productionState为空，设置默认值为量产
        if (StringUtils.isBlank(entity.getProductionState())) {
            entity.setProductionState(ProductionStateEnum.MASS_PRODUCTION.getCode());
        }

        BomEntity one = this.lambdaQuery().eq(BomEntity::getBomNum, dto.getBomNum()).one();
        if (one == null) {
            //新增
            entity.setUpdateTime(entity.getUpdateTime() == null ? now : entity.getUpdateTime());
            entity.setCreateTime(entity.getCreateTime() == null ? now : entity.getCreateTime());
            if (entity.getState() != null && BomStateEnum.CREATE.getCode() == entity.getState()) {
                saveEntity(entity);
            } else {
                saveReleasedEntity(entity);
            }
        } else {
            //更新
            FieldUtil.copyValueTo(one, entity);
            updateEntity(entity);
        }
    }

    private void getBomReverseDTOS(BomReverseDTO build) {
        List<BomReverseDTO> list = new ArrayList<>();
        // 根据bomId找到父级Bom物料
        // 查询传入的物料是否为bom的子物料
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.lambdaQuery()
                .eq(BomRawMaterialEntity::getCode, build.getBomEntity().getCode())
                .eq(Objects.nonNull(build.getBomEntity().getSkuId()), BomRawMaterialEntity::getSkuId, build.getBomEntity().getSkuId())
                .list();
        if (CollectionUtils.isEmpty(bomRawMaterialEntities)) {
            return;
        }
        // 查询子物料关联的bom
        List<Integer> bomIds = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getBomId).collect(Collectors.toList());
        Map<Integer, BomEntity> bomMap = this.listByIds(bomIds).stream()
                .peek(o -> {
                    o.setMaterialFields(materialService.lambdaQuery().eq(MaterialEntity::getCode, o.getCode()).one());
                    o.setSkuEntity(skuService.getById(o.getSkuId()));
                })
                .collect(Collectors.toMap(BomEntity::getId, o -> o));
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, build.getBomEntity().getCode()).one();
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            BomEntity bomEntity = bomMap.get(bomRawMaterialEntity.getBomId());
            BomReverseDTO parentBuild = BomReverseDTO.builder()
                    .bomEntity(bomEntity)
                    .materialFields(materialEntity)
                    .skuEntity(skuService.getById(bomRawMaterialEntity.getSkuId()))
                    .build();
            // 根据bomId找到父级Bom
            getBomReverseDTOS(parentBuild);
            list.add(parentBuild);
        }
        build.setParentDTOs(list);
    }

    @Override
    public List<BomEntity> getMultiLevelBom(List<BomEntity> records) {
        List<BomEntity> multiBomList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return multiBomList;
        }
        // 获取子物料数据
        List<Integer> bomIds = records.stream().map(BomEntity::getId).collect(Collectors.toList());
        Map<Integer, List<BomRawMaterialEntity>> bomRawMap = bomRawMaterialService.lambdaQuery().in(BomRawMaterialEntity::getBomId, bomIds)
                .list().stream().collect(Collectors.groupingBy(BomRawMaterialEntity::getBomId));
        Set<Integer> multiBomIds = new LinkedHashSet<>();
        Map<BomKeyDTO, List<BomRawMaterialEntity>> multiLevelBomMap = new HashMap<>();
        // 物料类型map
        Map<String, String> materialSortMap = new HashMap<>();
        // 填充数据
        for (BomEntity bomEntity : records) {
            multiBomIds.add(bomEntity.getId());
            List<BomRawMaterialEntity> rawMaterialEntities = bomRawMap.get(bomEntity.getId());
            for (BomRawMaterialEntity rawMaterialEntity : rawMaterialEntities) {
                String materialCode = rawMaterialEntity.getCode();
                Integer skuId = rawMaterialEntity.getSkuId();
                String materialSort = materialSortMap.get(materialCode);
                if (StringUtils.isBlank(materialSort)) {
                    // 查询物料分类
                    MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
                    materialSort = materialEntity.getSort();
                    materialSortMap.put(materialCode, materialSort);
                }
                // 采购品跳过
                if (MaterialSortEnum.PURCHASE.getCode().equals(materialSort)) {
                    continue;
                }
                BomKeyDTO bomKeyDTO = new BomKeyDTO(materialCode, skuId);
                List<BomRawMaterialEntity> multiLevelBom = multiLevelBomMap.get(bomKeyDTO);
                if (multiLevelBom == null) {
                    // 查询多级bom
                    multiLevelBom = this.getMultiLevelBom(materialCode, skuId, null, bomEntity.getProductionState());
                    multiLevelBomMap.put(bomKeyDTO, multiLevelBom);
                }
                // 获取所有多级bom的id
                for (BomRawMaterialEntity multiEntity : multiLevelBom) {
                    multiBomIds.add(multiEntity.getBomId());
                    setChildrenBomIds(multiBomIds, multiEntity.getChildren());
                }
            }
        }
        multiBomList = this.lambdaQuery().in(BomEntity::getId, multiBomIds).list();
        this.showName(multiBomList);
        Map<Integer, Integer> bomIdSeqMap = new HashMap<>();
        int seq = 1;
        for (Integer bomId : multiBomIds) {
            bomIdSeqMap.put(bomId, seq++);
        }
        // 按照多级bom树排序
        multiBomList.sort(Comparator.comparing(o -> bomIdSeqMap.get(o.getId())));
        return multiBomList;
    }

    @Override
    public Page<ReplaceSchemeVO> getReplaceMaterialsByBomCode(BomReplaceMaterialSelectDTO selectDTO) {
        List<ReplaceSchemeVO> schemeVOS = new ArrayList<>();
        List<Integer> idList = new ArrayList<>();
        // 根据id查询
        BomEntity bomEntity = this.lambdaQuery().eq(BomEntity::getBomNum, selectDTO.getBomCode()).one();
        if (Objects.isNull(bomEntity)) {
            return selectDTO.buildPage();
        }
        // 如果查询对象为成品/半成品，查询所绑定的原料
        List<BomRawMaterialEntity> list = bomRawMaterialService.getListByBomId(bomEntity.getId());
        for (BomRawMaterialEntity bomRawMaterialEntity : list) {
            List<ReplaceBomMaterialEntity> replaceBomMaterialEntities = replaceBomMaterialService.listByBomMaterialId(bomRawMaterialEntity.getId());
            if (CollectionUtils.isEmpty(replaceBomMaterialEntities)) {
                continue;
            }
            // 查询物料替代方案列表
            idList.addAll(replaceBomMaterialEntities.stream().map(ReplaceBomMaterialEntity::getReplaceMaterialId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(idList)) {
            return selectDTO.buildPage();
        }
        ReplaceSchemeSelectDTO build = ReplaceSchemeSelectDTO.builder()
                .showType(ShowTypeEnum.MATERIAL.getType())
                .replaceMaterialIds(idList.stream().map(String::valueOf).collect(Collectors.joining(Constants.SEP)))
                .schemeCode(selectDTO.getSchemeCode())
                .schemeName(selectDTO.getSchemeName())
                .mainMaterialCode(selectDTO.getMainMaterialCode())
                .replaceMaterialFields(selectDTO.getReplaceMaterialFields())
                .build();
        build.setCurrent(selectDTO.getCurrent());
        build.setSize(selectDTO.getSize());
        Page<ReplaceSchemeEntity> schemePage = replaceSchemeService.getList(build);
        List<ReplaceSchemeMaterialEntity> schemeMaterialEntities = schemePage.getRecords().stream().map(ReplaceSchemeEntity::getSchemeMaterialEntity).collect(Collectors.toList());
        // 查询替代方案
        List<String> schemeCodes = schemeMaterialEntities.stream().map(ReplaceSchemeMaterialEntity::getSchemeCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(schemeCodes)) {
            return selectDTO.buildPage();
        }
        List<ReplaceSchemeEntity> replaceSchemeEntities = replaceSchemeService.lambdaQuery().in(ReplaceSchemeEntity::getSchemeCode, schemeCodes).list();
        Map<String, ReplaceSchemeEntity> map = replaceSchemeEntities.stream().collect(Collectors.toMap(ReplaceSchemeEntity::getSchemeCode, o -> o));
        // 获取物料列表
        List<MaterialCodeAndSkuIdSelectDTO> schemeMaterialCodes = replaceSchemeEntities.stream()
                .map(materielLine -> MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(materielLine.getMainMaterialCode())
                        .skuId(materielLine.getSkuId()).build())
                .collect(Collectors.toList());
        schemeMaterialCodes.addAll(
                schemeMaterialEntities.stream()
                        .map(materielLine -> MaterialCodeAndSkuIdSelectDTO.builder()
                                .materialCode(materielLine.getReplaceMaterialCode())
                                .skuId(materielLine.getSkuId()).build())
                        .collect(Collectors.toList()));
        Map<String, MaterialEntity> schemeMaterialMap = materialService.getMaterialEntity(schemeMaterialCodes);
        // 填充数据
        for (ReplaceSchemeMaterialEntity schemeMaterialEntity : schemeMaterialEntities) {
            ReplaceSchemeEntity replaceSchemeEntity = map.get(schemeMaterialEntity.getSchemeCode());
            schemeVOS.add(
                    ReplaceSchemeVO.builder()
                            .schemeCode(replaceSchemeEntity.getSchemeCode())
                            .schemeName(replaceSchemeEntity.getSchemeName())
                            .mainMaterialCode(replaceSchemeEntity.getMainMaterialCode())
                            .mainDenominator(replaceSchemeEntity.getMainDenominator())
                            .mainMolecule(replaceSchemeEntity.getMainMolecule())
                            .mainMaterialFields(schemeMaterialMap.get(ColumnUtil.getMaterialSku(replaceSchemeEntity.getMainMaterialCode(), replaceSchemeEntity.getSkuId())))
                            .replaceMaterialId(schemeMaterialEntity.getId())
                            .replaceMaterialCode(schemeMaterialEntity.getReplaceMaterialCode())
                            .replaceDenominator(schemeMaterialEntity.getReplaceDenominator())
                            .replaceMolecule(schemeMaterialEntity.getReplaceMolecule())
                            .replaceMaterialFields(schemeMaterialMap.get(ColumnUtil.getMaterialSku(schemeMaterialEntity.getReplaceMaterialCode(), schemeMaterialEntity.getSkuId())))
                            .build()
            );
        }
        Page<ReplaceSchemeVO> pageVO = selectDTO.buildPage();
        pageVO.setTotal(schemePage.getTotal());
        pageVO.setRecords(schemeVOS);
        return pageVO;
    }

    /**
     * 递归获取子bom的id
     *
     * @param multiBomIds
     * @param children
     */
    private void setChildrenBomIds(Set<Integer> multiBomIds, List<BomRawMaterialEntity> children) {
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        for (BomRawMaterialEntity multiEntity : children) {
            multiBomIds.add(multiEntity.getBomId());
            setChildrenBomIds(multiBomIds, multiEntity.getChildren());
        }
    }

    public void initOriginInfo(BomEntity entity) {
        entity.setOriginId(Optional.ofNullable(entity.getOriginId()).orElse(entity.getId()));
        entity.setOriginNum(Optional.ofNullable(entity.getOriginNum()).orElse(entity.getBomNum()));
        this.updateById(entity);
    }

    private String buildVersionInfo(String info, String version) {
        return info + "(" + version + ")";
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyImport(String originalFilename, InputStream inputStream, String username) {
        String lockKey = RedisKeyPrefix.BOM_COPY_IMPORT_LOCK;
        String importProgressKey = RedisKeyPrefix.BOM_COPY_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            // 读取excel第二个sheet,从第3行开始读的数据
            List<BomCopyImportExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, BomCopyImportExcelDTO.class, 0, 2);
            // 2 校验保存
            List<BomEntity> bomList = verifyCopyImports(username, totalImportRecords);
            for (BomEntity bom : bomList) {
                saveEntity(bom);
            }
            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, BomCopyImportExcelDTO.class, username);
            //4、保存导入记录
            int successCount = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(BomCopyImportExcelDTO::getVerifyPass).count();
            int failCount = totalImportRecords.size() - successCount;
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(originalFilename)
                    .importType(ImportTypeEnum.BOM_COPY_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.BOM_COPY_IMPORT.getTypeName())
                    .createBy(username)
                    .createTime(new Date())
                    .successNumber(successCount)
                    .failNumber(failCount)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(username).build());
            importProgressService.updateProgress(importProgressKey, importUrl, true, failCount + successCount, successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Integer> batchAddMaterials(BomRawMaterialInsertDTO dto) {
        //校验BOM是否存在
        String bomNum = dto.getBomNum();
        String version = dto.getVersion();
        BomEntity bomEntity = this.lambdaQuery()
                .eq(BomEntity::getBomNum, bomNum)
                .eq(BomEntity::getVersion, version).one();
        if (bomEntity == null) {
            throw new ResponseException(RespCodeEnum.BOM_NOT_EIXSTS);
        }
        //查出数据库已有的子物料
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.lambdaQuery().eq(BomRawMaterialEntity::getBomId, bomEntity.getId()).list();
        List<Integer> idsInDB = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getId).collect(Collectors.toList());
        List<BomRawMaterialInsertDTO.BomRawMaterials> bomRawMaterials = dto.getBomRawMaterials();

        for (BomRawMaterialInsertDTO.BomRawMaterials dtoMaterial : bomRawMaterials) {
            BomRawMaterialEntity bomRawMaterialEntity = JacksonUtil.convertObject(dtoMaterial, BomRawMaterialEntity.class);

            if (StringUtils.isNotBlank(bomRawMaterialEntity.getBomType())) {
                bomRawMaterialEntity.setBomType(String.valueOf(BomItemTypeEnum.ORDINARY_PARTS.getCode()));
            }
            bomRawMaterialEntity.setBomId(bomEntity.getId());
            bomRawMaterialEntity.setBomNum(bomNum);
            bomRawMaterialEntity.setVersion(version);
            if (bomRawMaterialEntity.getNum() == null) {
                bomRawMaterialEntity.setNum(new BigDecimal(1));
            }
            if (bomRawMaterialEntity.getNumber() == null) {
                bomRawMaterialEntity.setNumber(new BigDecimal(1));
            }
            if (bomRawMaterialEntity.getNumber() == null) {
                bomRawMaterialEntity.setNumber(new BigDecimal(1));
            }
            //只传了sku编码
            if (dtoMaterial.getSkuId() == null && StringUtils.isNotBlank(dtoMaterial.getSkuCode())) {
                SkuEntity one = skuService.lambdaQuery().eq(SkuEntity::getSkuCode, dtoMaterial.getSkuCode()).one();
                bomRawMaterialEntity.setBomId(one.getSkuId());
            }
            bomRawMaterialEntities.add(bomRawMaterialEntity);
        }
        //批量插入数据
        bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities);

        //删除数据库里的子物料
        bomRawMaterialService.lambdaUpdate().eq(BomRawMaterialEntity::getBomId, bomEntity.getId()).remove();
        dealRawMaterialData(bomEntity);

        //返回插入的ID列表
        List<Integer> allIds = bomRawMaterialEntities.stream()
                .map(BomRawMaterialEntity::getId)
                .collect(Collectors.toList());
        allIds.removeAll(idsInDB);
        return allIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Integer> batchUpdateMaterials(BomRawMaterialUpdateDTO dto) {
        //校验BOM是否存在
        String bomNum = dto.getBomNum();
        String version = dto.getVersion();
        BomEntity bomEntity = this.lambdaQuery()
                .eq(BomEntity::getBomNum, bomNum)
                .eq(BomEntity::getVersion, version).one();
        if (bomEntity == null) {
            throw new ResponseException(RespCodeEnum.BOM_NOT_EIXSTS);
        }

        // 查询旧bom子物料
        List<BomRawMaterialEntity> oldList = bomRawMaterialService.lambdaQuery().eq(BomRawMaterialEntity::getBomId, bomEntity.getId()).list();
        Map<Integer, BomRawMaterialEntity> dbMap = oldList.stream().collect(Collectors.toMap(BomRawMaterialEntity::getId, o -> o));

        List<BomRawMaterialUpdateDTO.BomRawMaterials> bomRawMaterials = dto.getBomRawMaterials();
        for (BomRawMaterialUpdateDTO.BomRawMaterials dtoMaterial : bomRawMaterials) {
            BomRawMaterialEntity bomRawMaterialEntity = JacksonUtil.convertObject(dtoMaterial, BomRawMaterialEntity.class);

            BomRawMaterialEntity old = dbMap.get(bomRawMaterialEntity.getId());
            //填充新对象
            FieldUtil.copyValueTo(old, bomRawMaterialEntity);

            //只传了sku编码
            if (dtoMaterial.getSkuId() == null && StringUtils.isNotBlank(dtoMaterial.getSkuCode())) {
                SkuEntity one = skuService.lambdaQuery().eq(SkuEntity::getSkuCode, dtoMaterial.getSkuCode()).one();
                bomRawMaterialEntity.setBomId(one.getSkuId());
            }
            //替换原有
            dbMap.put(bomRawMaterialEntity.getId(), bomRawMaterialEntity);
        }
        //批量插入数据
        List<BomRawMaterialEntity> collect = dbMap.values().stream().collect(Collectors.toList());
        bomEntity.setBomRawMaterialEntities(collect);
        //删除数据库里的子物料
        bomRawMaterialService.lambdaUpdate().eq(BomRawMaterialEntity::getBomId, bomEntity.getId()).remove();
        dealRawMaterialData(bomEntity);

        //返回插入的ID列表
        return bomRawMaterials.stream()
                .map(BomRawMaterialUpdateDTO.BomRawMaterials::getId)
                .collect(Collectors.toList());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Integer> batchDeleteMaterials(BomRawMaterialDeleteDTO dto) {
        //删除数据库里的子物料
        bomRawMaterialService.removeByIds(dto.getIds());
        //删除替代料
        replaceBomMaterialService.lambdaUpdate().in(ReplaceBomMaterialEntity::getBomMaterialId, dto.getIds()).remove();

        return dto.getIds();
    }

    @Override
    @Async
    public void batchDelete(BomNumbersDTO bomNumbersDTO, String code, String username) {
        List<String> bomCodes = bomNumbersDTO.getBomNums();
        if (CollectionUtils.isEmpty(bomCodes)) {
            return;
        }
        BomService bomService = SpringUtil.getBean(BomService.class);
        // 删除的单据
        String progressKey = com.yelink.dfscommon.constant.RedisKeyPrefix.DELETE_PROGRESS + code;
        // 初始化进度，获取锁（给单加锁,同一个单，在没有删除完成之前，不能多次删除）
        // 对用户也要加锁（如果不加，后续取缓存的时候有问题）
        commonService.initLockProgress(progressKey, bomCodes, com.yelink.dfscommon.constant.RedisKeyPrefix.DELETE_BOM_LOCK, 5, TimeUnit.MINUTES, username);
        try {
            int i = 1;
            List<Integer> bomIds = bomService.lambdaQuery()
                    .select(BomEntity::getId)
                    .in(BomEntity::getBomNum, bomCodes)
                    .list().stream()
                    .map(BomEntity::getId)
                    .collect(Collectors.toList());
            for (Integer bomId : bomIds) {
                bomService.removeEntityById(bomId);
                // 手动更新进度
                Double processPercent = MathUtil.divideDouble(i, bomCodes.size(), 2);
                commonService.updateProgress(progressKey, processPercent, String.format("正在处理中，请耐心等候...当前删除了%s个BOM；", i));
                i = i + 1;
            }
            // 手动更新进度
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(String.format("删除了%s个BOM，其中成功%s个，失败0个；", bomCodes.size(), bomCodes.size()));
            commonService.updateProgress(progressKey, 1.0, stringBuilder.toString());
        } catch (Exception e) {
            commonService.importProgressException(progressKey, e);
            log.warn("删除BOM过程中出错", e);
        } finally {
            // 删除锁
            commonService.releaseLock(bomCodes, com.yelink.dfscommon.constant.RedisKeyPrefix.DELETE_BOM_LOCK, username);
        }
    }

    @Override
    public Page<BomEntity> list(BomQueryDTO dto) {
        Map<String, QueryDTO<BomQueryDTO.CustomerQueryCriteriaDTO>> dtos = SqlUtil.split(dto, "bom");
        QueryDTO<BomQueryDTO.CustomerQueryCriteriaDTO> bomDTO = dtos.get("bom");
        QueryDTO<BomQueryDTO.CustomerQueryCriteriaDTO> bomMaterialDTO = dtos.get("bomMaterial");
        BomQuerySqlDTO sqlDTO = BomQuerySqlDTO.builder()
                .bomSql(SqlUtil.getSelectSql(bomDTO, false))
                .bomMaterialSql(SqlUtil.getSelectSql(bomMaterialDTO, false))
                .orderBySql(
                        Stream.of(
                                SqlUtil.getSortSql(bomDTO, false),
                                SqlUtil.getSortSql(bomMaterialDTO, false)
                        ).filter(StringUtils::isNotBlank).collect(Collectors.joining(Constant.SEP))
                )
                .build();
        Page<BomEntity> page = this.baseMapper.getList(sqlDTO, dto.getPage());
        showName(page.getRecords());
        showAddition(page.getRecords(), dto.getQueryAddition());
        return page;
    }

    @Override
    public void sync(List<BomUpdateInsertDTO> dtos) {
        for (BomUpdateInsertDTO dto : dtos) {
            upsert(dto);
        }
    }

    private void showAddition(List<BomEntity> records, List<String> queryAddition) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(queryAddition) && queryAddition.contains("bomRawMaterialEntities")) {
            List<Integer> bomIds = records.stream().map(BomEntity::getId).collect(Collectors.toList());
            // 获取bom物料行信息
            List<BomRawMaterialEntity> materialEntities = bomRawMaterialService.lambdaQuery().in(BomRawMaterialEntity::getBomId, bomIds).list();
            Map<Integer, List<BomRawMaterialEntity>> bomRawMap = materialEntities.stream().collect(Collectors.groupingBy(BomRawMaterialEntity::getBomId));
            List<MaterialCodeAndSkuIdSelectDTO> materialCodes = records.stream()
                    .map(entity -> MaterialCodeAndSkuIdSelectDTO.builder()
                            .materialCode(entity.getCode())
                            .skuId(entity.getSkuId()).build())
                    .collect(Collectors.toList());
            Map<String, MaterialEntity> codeMaterialMap = materialService.getMaterialEntity(materialCodes);
            for (BomEntity bomEntity : records) {
                List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMap.get(bomEntity.getId());
                for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
                    bomRawMaterialEntity.setMaterialFields(codeMaterialMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId())));
                }
                bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities);
            }
        }
    }

    private List<BomEntity> verifyCopyImports(String username, List<BomCopyImportExcelDTO> totalImportRecords) {
        // 按照 (原始BOM编号 + 物料特征参数) 进行分组
        Map<String, List<BomCopyImportExcelDTO>> copyGroupsMap = totalImportRecords.stream().collect(Collectors.groupingBy(BomCopyImportExcelDTO::getGroupCode));
        List<BomEntity> saveBomList = new ArrayList<>();
        for (List<BomCopyImportExcelDTO> copyGroup : copyGroupsMap.values()) {
            try {
                BomCopyImportExcelDTO dto = copyGroup.get(0);
                // 校验bom编号及物料(如果填写)
                String originBomNum = dto.getOriginBomNum();
                if (StringUtils.isBlank(originBomNum)) {
                    throw new ImportFailException("复制BOM编号不能为空。");
                }
                List<String> materialCodes = copyGroup.stream().map(BomCopyImportExcelDTO::getMaterialCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                if (materialCodes.size() > 1) {
                    throw new ImportFailException("该分组下的物料编码存在多个，请检查。");
                }
                BomEntity originBom = lambdaQuery().eq(BomEntity::getOriginNum, originBomNum).orderByDesc(BomEntity::getId).last("limit 1").one();
                if (originBom == null) {
                    throw new ImportFailException(String.format("编号: [%s]的BOM不存在。", originBomNum));
                }
                String materialCode = originBom.getCode();
                if (!materialCodes.isEmpty() && !Objects.equals(materialCode, materialCodes.get(0))) {
                    throw new ImportFailException("复制BOM编号对应的物料编码与所填的物料编码不一致，请检查。");
                }
                BomEntity saveBom = BeanUtil.copyProperties(originBom, BomEntity.class, "id");
                // 校验特征参数的特征值
                String valueNames = dto.getValueNames();
                if (StringUtils.isNotBlank(valueNames)) {
                    List<String> attrValueNames = Arrays.stream(valueNames.split("\\.")).collect(Collectors.toList());
                    try {
                        MaterialSkuInsertResDTO mainSku = materialAuxiliaryAttrSkuService.addByNewTrans(materialCode, attrValueNames, username);
                        saveBom.setSkuId(mainSku.getSkuId());
                    } catch (Exception ex) {
                        throw new ImportFailException(String.format("BOM物料特征参数异常: %s。", ex.getMessage()));
                    }
                }
                // 处理bom子物料: 以序号进行配对
                if (copyGroup.stream().map(BomCopyImportExcelDTO::getSubNo).anyMatch(Objects::isNull)) {
                    throw new ImportFailException("该分组下的子物料序号存在未填，请检查。");
                }
                if (copyGroup.stream().map(BomCopyImportExcelDTO::getSubNo).distinct().count() != copyGroup.size()) {
                    throw new ImportFailException("该分组下的子物料序号存在重复，请检查。");
                }
                List<BomRawMaterialEntity> originBomRaws = bomRawMaterialService.lambdaQuery().eq(BomRawMaterialEntity::getBomId, originBom.getId()).list();
                for (BomCopyImportExcelDTO subDto : copyGroup) {
                    int subNo;
                    try {
                        subNo = Integer.parseInt(subDto.getSubNo());
                    } catch (Exception ex) {
                        throw new ImportFailException(String.format("该分组下的子物料序号: [%s]错误, 需为整数。", subDto.getSubNo()));
                    }
                    if (subNo < 1) {
                        throw new ImportFailException(String.format("该分组下的子物料序号: [%s]错误, 至少大于0。", subNo));
                    }
                    if (subNo > originBomRaws.size()) {
                        throw new ImportFailException(String.format("该分组下的子物料序号: [%s]错误, 大于复制BOM下的子物料数。", subNo));
                    }
                }
                // 原始bom子物料中，不包含在用户填写中的。需要直接复制过去
                List<BomRawMaterialEntity> saveBomRawMaterials = new ArrayList<>();
                Map<Integer, BomCopyImportExcelDTO> subNoGroup = copyGroup.stream().collect(Collectors.toMap(g -> Integer.parseInt(g.getSubNo()), Function.identity()));
                for (int i = 0; i < originBomRaws.size(); i++) {
                    BomRawMaterialEntity saveBomRawMaterial = BeanUtil.copyProperties(originBomRaws.get(i), BomRawMaterialEntity.class, "id", "bomId");
                    int subNo = i + 1;
                    if (subNoGroup.containsKey(subNo)) {
                        BomCopyImportExcelDTO subDto = subNoGroup.get(subNo);
                        // 校验子物料编码
                        if (StringUtils.isNotBlank(subDto.getSubMaterialCode()) && !saveBomRawMaterial.getCode().equals(subDto.getSubMaterialCode())) {
                            throw new ImportFailException(String.format("该分组下的子物料序号: [%s]的物料编码与所填的子物料编码不一致，请检查。", subNo));
                        }
                        // 校验子物料特征参数的特征值
                        String subValueNames = subDto.getSubValueNames();
                        if (StringUtils.isNotBlank(subValueNames)) {
                            List<String> subAttrValueNames = Arrays.stream(subValueNames.split("\\.")).collect(Collectors.toList());
                            try {
                                MaterialSkuInsertResDTO subSku = materialAuxiliaryAttrSkuService.addByNewTrans(saveBomRawMaterial.getCode(), subAttrValueNames, username);
                                saveBomRawMaterial.setSkuId(subSku.getSkuId());
                            } catch (Exception ex) {
                                throw new ImportFailException(String.format("该分组下的子物料序号: [%s], 子物料特征参数异常: %s。", subNo, ex.getMessage()));
                            }
                        }
                    }
                    saveBomRawMaterials.add(saveBomRawMaterial);
                }
                // 赋予bom编码: 这个传参直接递增
                List<NumberRulesConfigEntity> ruleConfigs = numberRuleService.getByTypeCode("4");
                if (CollectionUtils.isEmpty(ruleConfigs)) {
                    throw new ImportFailException("未配置物料编码规则");
                }
                NumberRulesConfigEntity selected = ruleConfigs.stream()
                        .filter(item -> Boolean.TRUE.equals(item.getIsDefault()))
                        .findFirst()
                        .orElseGet(() -> ruleConfigs.get(0));
                NumberCodeDTO numberCode = numberRuleService.getSeqById(RuleSeqDTO.builder().id(selected.getId()).build());
                saveBom.setBomNum(numberCode.getCode());
                saveBom.setCreateTime(Objects.nonNull(dto.getCreateTime()) ? dto.getCreateTime() : new Date());
                // 组装数据
                saveBom.setBomRawMaterialEntities(saveBomRawMaterials);
                saveBomList.add(saveBom);
            } catch (Exception ex) {
                copyGroup.forEach(e -> {
                    e.setVerifyPass(false);
                    e.setImportResult(ex.getMessage());
                });
            } finally {
                copyGroup.forEach(e -> {
                    if (e.getVerifyPass() == null) {
                        e.setVerifyPass(true);
                        e.setImportResult("校验通过");
                    }
                });
            }
        }
        return saveBomList;
    }

}
