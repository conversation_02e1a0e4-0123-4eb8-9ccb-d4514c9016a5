package com.yelink.dfs.service.impl.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.CodeFactory;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.constant.order.PriorityTypeEnum;
import com.yelink.dfs.constant.order.ScheduleStatusEnum;
import com.yelink.dfs.entity.capacity.CapacityEntity;
import com.yelink.dfs.entity.capacity.dto.CapacityGetDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitTypeSelectDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionResourceDTO;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.BomTileEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderMaterialPlanEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.ExtendFieldDTO;
import com.yelink.dfs.entity.order.dto.OrderWorkOrderScheduleDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSyncDTO;
import com.yelink.dfs.entity.order.dto.WorkingHoursDTO;
import com.yelink.dfs.entity.order.tmp.ConfigScheduleReverseMaterialDetailEntity;
import com.yelink.dfs.entity.order.tmp.TmpMaterialReadinessInspectionEntity;
import com.yelink.dfs.entity.order.tmp.TmpWorkOrderScheduleEntity;
import com.yelink.dfs.entity.order.vo.CapacityLoadVo;
import com.yelink.dfs.entity.order.vo.DateTreeVO;
import com.yelink.dfs.entity.order.vo.MaterialInventoryVO;
import com.yelink.dfs.entity.order.vo.MaterialStockVO;
import com.yelink.dfs.entity.order.vo.MonthVO;
import com.yelink.dfs.entity.order.vo.QuantityFieldVo;
import com.yelink.dfs.entity.order.vo.WorkOrderScheduleSumVO;
import com.yelink.dfs.entity.order.vo.YearVO;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.MaterialsSelectDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.capacity.CapacityMapper;
import com.yelink.dfs.mapper.manufacture.ProductionLineMapper;
import com.yelink.dfs.mapper.order.TmpMaterialReadinessInspectionMapper;
import com.yelink.dfs.mapper.order.TmpWorkOrderScheduleMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseReceiptInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.config.OrderTypeConfigService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.impl.synchronize.common.SynCommon;
import com.yelink.dfs.service.model.AreaService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.BomTileService;
import com.yelink.dfs.service.order.ConfigScheduleReverseMaterialDetailService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderMaterialPlanService;
import com.yelink.dfs.service.order.TmpMaterialReadinessInspectionService;
import com.yelink.dfs.service.order.TmpWorkOrderScheduleService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.constant.CategoryTypeEnum;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.MatchRuleEnum;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.AmsEventTypeEnum;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.dto.CommonTableDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.purchase.PurchaseReceiptStackAmountDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeInfoVO;
import com.yelink.dfscommon.dto.dfs.BomKeyDTO;
import com.yelink.dfscommon.dto.wms.StockInventorySelectDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.dto.ReverseMaterialDTO;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.wms.StockInventoryDetailEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DataConversionUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.Rational;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.Collator;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-12-21 17:42
 */
@Service
@Slf4j
@AllArgsConstructor
public class TmpWorkOrderScheduleServiceImpl extends ServiceImpl<TmpWorkOrderScheduleMapper, TmpWorkOrderScheduleEntity> implements TmpWorkOrderScheduleService {

    private static final String LINE = "line";

    protected WorkOrderService workOrderService;
    protected WorkOrderMapper workOrderMapper;
    protected GridService gridService;
    protected ProductionLineMapper productionLineMapper;
    protected CodeFactory codeFactory;
    protected OrderWorkOrderService orderWorkOrderService;
    protected WorkPropertise workPropertise;
    protected MaterialService materialService;
    protected WorkOrderPlanService workOrderPlanService;
    protected DictService dictService;
    protected StockInventoryDetailInterface inventoryDetailInterface;
    protected BomService bomService;
    protected TmpMaterialReadinessInspectionService inspectionService;
    protected CapacityMapper capacityMapper;
    protected WorkCalendarService workCalendarService;
    protected RecordWorkOrderMaterialPlanService materialPlanService;
    protected SynCommon synCommon;
    protected TmpMaterialReadinessInspectionMapper inspectionMapper;
    protected RedisTemplate<String, Object> redisTemplate;
    protected ConfigScheduleReverseMaterialDetailService scheduleReverseService;
    protected BomTileService bomTileService;
    protected WorkOrderProcedureRelationService workOrderProcedureRelationService;
    protected SysUserService sysUserService;
    protected OrderTypeConfigService orderTypeConfigService;
    protected MessagePushToKafkaService messagePushToKafkaService;
    protected ExtSaleOrderInterface extSaleOrderInterface;
    protected ExtProductOrderInterface extProductOrderInterface;
    protected ExtPurchaseReceiptInterface extPurchaseReceiptInterface;
    protected WorkCenterService workCenterService;
    protected WorkOrderBasicUnitRelationService basicUnitRelationService;
    protected CapacityService capacityService;
    private WorkOrderExtendService workOrderExtendService;
    private AreaService areaService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void issue(WorkOrderEntity entity) {
        //将新数据中为null的值补上
        entity = completedNewWorkOrder(entity);
        MaterialEntity materialEntity = materialService.lambdaQuery().select(MaterialEntity::getComp).eq(MaterialEntity::getCode, entity.getMaterialCode()).one();
        DictEntity dictEntity = dictService.getCodeByName(materialEntity.getComp());
        //修改工单为生效
        entity.setState(WorkOrderStateEnum.RELEASED.getCode());
        entity.setPriority(PriorityTypeEnum.NORMAL.getName());
        //修改为已下发
        entity.setIssue(true);
        // 获取工序工艺
        entity.setCraftProcedureEntities(workOrderProcedureRelationService.getCraftProcedureListByWorkOrderNumber(entity.getWorkOrderNumber()));
        workOrderService.updateByWorkId(entity, entity.getUpdateBy());
        WorkOrderSyncDTO workOrderSyncDTO = JSONObject.parseObject(JSON.toJSONString(entity), WorkOrderSyncDTO.class);
        BomEntity bomEntity = bomService.getLatestBomByCode(workOrderSyncDTO.getMaterialCode(), entity.getSkuId());
        //给erp传bom版本（用工艺名字字段代传）
        if (bomEntity != null) {
            workOrderSyncDTO.setCraftName(bomEntity.getBomName());
        }
        //给erp转单位（name-->code）
        workOrderSyncDTO.setUnit(dictEntity.getCode());
        //同步erp数据
        ResponseData responseData = synCommon.insertWorkOrder(workOrderSyncDTO);
        if (!"200".equals(responseData.getCode())) {
            throw new ResponseException(responseData.getMessage());
        }
        // 将该工单数据更新到工单排程表
        LambdaUpdateWrapper<TmpWorkOrderScheduleEntity> scheduleQw = new LambdaUpdateWrapper<>();
        scheduleQw.eq(TmpWorkOrderScheduleEntity::getWorkOrderId, entity.getWorkOrderId())
                .set(TmpWorkOrderScheduleEntity::getLineName, entity.getLineName())
                .set(TmpWorkOrderScheduleEntity::getLineId, entity.getLineId())
                .set(TmpWorkOrderScheduleEntity::getPlanQuantity, entity.getPlanQuantity())
                .set(TmpWorkOrderScheduleEntity::getFinishCount, entity.getFinishCount())
                .set(TmpWorkOrderScheduleEntity::getStartDate, entity.getStartDate())
                .set(TmpWorkOrderScheduleEntity::getEndDate, entity.getEndDate())
                .set(TmpWorkOrderScheduleEntity::getMaterialOweNum, entity.getMaterialOweNum())
                .set(TmpWorkOrderScheduleEntity::getState, entity.getState())
                .set(TmpWorkOrderScheduleEntity::getStateName, WorkOrderStateEnum.getNameByCode(entity.getState()))
                .set(TmpWorkOrderScheduleEntity::getUnqualified, entity.getUnqualified())
                .set(TmpWorkOrderScheduleEntity::getProgress, entity.getProgress())
                .set(TmpWorkOrderScheduleEntity::getActualStartDate, entity.getActualStartDate())
                .set(TmpWorkOrderScheduleEntity::getActualEndDate, entity.getActualEndDate());
        this.update(scheduleQw);
    }

    private WorkOrderEntity completedNewWorkOrder(WorkOrderEntity entity) {
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(entity.getWorkOrderNumber());
        WorkOrderEntity old = workOrderService.getWorkOrderById(detailDTO);
        JSONObject oldJsonObject = JSON.parseObject(JSON.toJSONString(old));
        JSONObject newJsonObject = JSON.parseObject(JSON.toJSONString(entity));
        for (Map.Entry<String, Object> entry : newJsonObject.entrySet()) {
            //新值覆盖旧值
            if (entry.getValue() != null) {
                oldJsonObject.put(entry.getKey(), entry.getValue());
            }
        }
        return JSONObject.parseObject(oldJsonObject.toJSONString(), WorkOrderEntity.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scheduleUpdate(WorkOrderEntity entity) {
        //工单排程发放之后的状态下不能再修改
        if (entity.getState().equals(WorkOrderStateEnum.RELEASED.getCode())) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_TO_EXAMINE_FAIL);
        }
        Integer lineId = entity.getLineId();
        ProductionLineEntity lineEntity = productionLineMapper.selectById(lineId);
        UpdateWrapper<WorkOrderEntity> uw = new UpdateWrapper<>();
        uw.lambda().eq(WorkOrderEntity::getWorkOrderId, entity.getWorkOrderId())
                .set(WorkOrderEntity::getLineId, entity.getLineId())
                .set(WorkOrderEntity::getLineCode, lineEntity.getProductionLineCode())
                .set(WorkOrderEntity::getLineName, lineEntity.getName())
                .set(WorkOrderEntity::getStartDate, entity.getStartDate())
                .set(WorkOrderEntity::getEndDate, entity.getEndDate())
                .set(WorkOrderEntity::getPlanQuantity, entity.getPlanQuantity());
        workOrderService.update(uw);
        //修改工单计划
        workOrderPlanService.createWorkOrderPlan(entity);
        // 工单排程计划物料计算
        scheduleUpdateMaterialPlan(entity);
        // 将该工单数据更新到工单排程表
        LambdaUpdateWrapper<TmpWorkOrderScheduleEntity> scheduleQw = new LambdaUpdateWrapper<>();
        scheduleQw.eq(TmpWorkOrderScheduleEntity::getWorkOrderId, entity.getWorkOrderId())
                .set(TmpWorkOrderScheduleEntity::getLineName, lineEntity.getName())
                .set(TmpWorkOrderScheduleEntity::getLineId, lineEntity.getProductionLineId())
                .set(TmpWorkOrderScheduleEntity::getPlanQuantity, entity.getPlanQuantity())
                .set(TmpWorkOrderScheduleEntity::getFinishCount, entity.getFinishCount())
                .set(TmpWorkOrderScheduleEntity::getStartDate, entity.getStartDate())
                .set(TmpWorkOrderScheduleEntity::getEndDate, entity.getEndDate())
                .set(TmpWorkOrderScheduleEntity::getMaterialOweNum, entity.getMaterialOweNum())
                .set(TmpWorkOrderScheduleEntity::getState, entity.getState())
                .set(TmpWorkOrderScheduleEntity::getStateName, WorkOrderStateEnum.getNameByCode(entity.getState()))
                .set(TmpWorkOrderScheduleEntity::getUnqualified, entity.getUnqualified())
                .set(TmpWorkOrderScheduleEntity::getProgress, entity.getProgress())
                .set(TmpWorkOrderScheduleEntity::getActualStartDate, entity.getActualStartDate())
                .set(TmpWorkOrderScheduleEntity::getActualEndDate, entity.getActualEndDate());
        this.update(scheduleQw);
    }

    @Override
    public Page<TmpWorkOrderScheduleEntity> getWorkOrderScheduleList(OrderWorkOrderScheduleDTO dto, String username) {
        // 只选择当前车间下绑定的生产订单的关联的工单列表
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> queryWrapper = new LambdaQueryWrapper<>();
        // 条件查询
        conditionQuery(dto, queryWrapper);
        // 前端触发的排序
        if (StringUtils.isNotEmpty(dto.getKeys())) {
            String[] allKeys = dto.getKeys().split(Constant.SEP);
            StringBuilder stringBuilder = new StringBuilder();
            for (String allKey : allKeys) {
                String[] keySort = allKey.split(Constant.UNDERLINE);
                // 根据排序字段和排序方式进行排序，为2是因为前端把需要排序的字段都传了，如果没传ASC/DESC，则不走下面逻辑
                if (keySort.length == 2) {
                    stringBuilder.append(keySort[0]).append(Constant.BLANK).append(keySort[1]).append(Constant.SEP);
                } else {
                    stringBuilder.append(keySort[0]).append(Constant.BLANK).append("is null").append(Constant.SEP);
                }
            }
            queryWrapper.last("ORDER BY " + DataConversionUtil.humpToUnderline(String.valueOf(stringBuilder.deleteCharAt(stringBuilder.length() - 1))));
        } else if (StringUtils.isNotBlank(dto.getSortType())) {
            String sortType = dto.getSortType();
            if ("time".equals(sortType)) {
                queryWrapper.orderByAsc(TmpWorkOrderScheduleEntity::getStartDate);
            } else {
                queryWrapper.orderByAsc(TmpWorkOrderScheduleEntity::getLineId);
            }
        } else {
            // 后端默认排序：先按产线名称排序，再按计划开始时间排序
            queryWrapper.last("ORDER BY line_name ASC, start_date ASC");
        }
        queryWrapper.orderByDesc(TmpWorkOrderScheduleEntity::getId);

        Page<TmpWorkOrderScheduleEntity> page = this.page(new Page<>(dto.getCurrent(), dto.getSize()), queryWrapper);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<String> materialCodes = page.getRecords().stream().map(TmpWorkOrderScheduleEntity::getMaterialCode).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(materialCodes)) {
                // 获取物料相关字段
                Map<String, MaterialEntity> materialEntityMap = materialService.lambdaQuery()
                        .in(MaterialEntity::getCode, materialCodes)
                        .list()
                        .stream().collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
                // 获取拓展字段（原料字段）
                List<Map<String, String>> listMap = workOrderMapper.getScheduleReverseMaterialConf(materialCodes);
                Map<String, Map<String, String>> collect = listMap.stream()
                        .collect(Collectors.groupingBy(o -> o.get(DataConversionUtil.humpToUnderline(com.yelink.dfs.constant.order.Constant.MATERIAL_CODE)),
                                Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
                for (TmpWorkOrderScheduleEntity vo : page.getRecords()) {
                    vo.setMaterialFields(materialEntityMap.get(vo.getMaterialCode()));
                    if (!CollectionUtils.isEmpty(collect) && collect.containsKey(vo.getMaterialCode())) {
                        vo.setExtend(collect.get(vo.getMaterialCode()));
                    }
                }
            }
        }
        return page;
    }

    private void scheduleUpdateMaterialPlan(WorkOrderEntity workOrderEntity) {
        QueryWrapper<RecordWorkOrderMaterialPlanEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RecordWorkOrderMaterialPlanEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                .eq(RecordWorkOrderMaterialPlanEntity::getMaterialCode, workOrderEntity.getMaterialCode());
        boolean remove = materialPlanService.remove(queryWrapper);
        if (remove) {
            scheduleInsertMaterialPlan(workOrderEntity);
        }
    }

    private void scheduleInsertMaterialPlan(WorkOrderEntity workOrderEntity) {
        String materialCode = workOrderEntity.getMaterialCode();
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
        Boolean isDoubleUnit = materialEntity.getIsDoubleUnit();
        if (isDoubleUnit) {
            //此物料得第二个单位
            String measurementUnit = materialEntity.getUnit();
            //此物料的换算系数
            Rational rational = new Rational(materialEntity.getUnitNumerator(), materialEntity.getUnitDenominator());
//            Double scaleFactor = materialEntity.getScaleFactor();
            Double scaleFactor = rational.doubleValue();
            Double planQuantity = workOrderEntity.getPlanQuantity();
            double measurementQuantity = scaleFactor * planQuantity;
            RecordWorkOrderMaterialPlanEntity.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                    .materialCode(workOrderEntity.getMaterialCode())
                    .planQuantity(workOrderEntity.getPlanQuantity())
                    .unit(materialEntity.getComp())
                    .measurementQuantity(measurementQuantity)
                    .measurementUnit(measurementUnit)
                    .createTime(new Date())
                    .build();
        }
    }

    /**
     * 条件过滤
     */
    private void conditionQuery(OrderWorkOrderScheduleDTO dto, LambdaQueryWrapper<TmpWorkOrderScheduleEntity> queryWrapper) {
        WrapperUtil.like(queryWrapper, TmpWorkOrderScheduleEntity::getWorkOrderNumber, dto.getWorkOrderNumber());
        WrapperUtil.like(queryWrapper, TmpWorkOrderScheduleEntity::getOrderNumber, dto.getOrderNumber());
        WrapperUtil.eq(queryWrapper, TmpWorkOrderScheduleEntity::getState, dto.getState());
        WrapperUtil.like(queryWrapper, TmpWorkOrderScheduleEntity::getSaleOrderCode, dto.getSaleOrderCode());
        WrapperUtil.like(queryWrapper, TmpWorkOrderScheduleEntity::getGname, dto.getGridName());

        // 产线名称过滤
        if (StringUtils.isNotEmpty(dto.getLineName())) {
            queryWrapper.in(TmpWorkOrderScheduleEntity::getLineName, Arrays.stream(dto.getLineName()
                    .split(Constant.SEP)).collect(Collectors.toList()));
        }

        // 相关日期字段过滤
        relatedDateFilter(dto, queryWrapper);

        // 排程状态过滤
        if (StringUtils.isNotBlank(dto.getScheduleStatusName())) {
            queryWrapper.eq(TmpWorkOrderScheduleEntity::getScheduleStatusName, dto.getScheduleStatusName());
        }

        // 物料名称模糊过滤
        if (StringUtils.isNotBlank(dto.getMaterialName())) {
            List<String> materialCodes = materialService.lambdaQuery()
                    .in(MaterialEntity::getName, Arrays.stream(dto.getMaterialName().split(Constant.SEP)).collect(Collectors.toList()))
                    .list()
                    .stream()
                    .map(MaterialEntity::getCode).collect(Collectors.toList());
            queryWrapper.in(TmpWorkOrderScheduleEntity::getMaterialCode, materialCodes);
        }

        // 物料编码模糊过滤
        if (StringUtils.isNotBlank(dto.getMaterialCode())) {
            queryWrapper.in(TmpWorkOrderScheduleEntity::getMaterialCode, Arrays.stream(dto.getMaterialCode()
                    .split(Constant.SEP)).collect(Collectors.toList()));
        }

        // 国内国外
        if (StringUtils.isNotBlank(dto.getModelType())) {
            queryWrapper.in(TmpWorkOrderScheduleEntity::getModelType, Arrays.stream(dto.getModelType()
                    .split(Constant.SEP)).collect(Collectors.toList()));
        }

        // 客户名称过滤
        if (StringUtils.isNotBlank(dto.getCustomerName())) {
            queryWrapper.in(TmpWorkOrderScheduleEntity::getCustomerName, Arrays.stream(dto.getCustomerName()
                    .split(Constant.SEP)).collect(Collectors.toList()));
        }

        // 计划开始生产时间过滤
        if (StringUtils.isNotEmpty(dto.getPlannedDeliveryStartTime())
                && StringUtils.isNotEmpty(dto.getPlannedDeliveryEndTime())) {
            queryWrapper.between(TmpWorkOrderScheduleEntity::getPlannedDeliveryDate, dto.getPlannedDeliveryStartTime(),
                    dto.getPlannedDeliveryEndTime());
        }

        // 数字过滤
        if (StringUtils.isNotEmpty(dto.getNumberFilterList())) {
            digitalFilter(dto.getNumberFilterList(), queryWrapper);
        }

        // 拓展字段过滤
        if (!CollectionUtils.isEmpty(dto.getExtendMaterialList())) {
            List<ExtendFieldDTO> extendFieldDTOS = dto.getExtendMaterialList().stream()
                    .filter(o -> StringUtils.isNotEmpty(o.getValue())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(extendFieldDTOS)) {
                List<String> materialCodes = workOrderMapper.getScheduleReverseMaterialCode(extendFieldDTOS);
                if (!CollectionUtils.isEmpty(materialCodes)) {
                    queryWrapper.in(TmpWorkOrderScheduleEntity::getMaterialCode, materialCodes);
                }
            }
        }

    }

    /**
     * 相关日期字段过滤
     */
    private void relatedDateFilter(OrderWorkOrderScheduleDTO dto, LambdaQueryWrapper<TmpWorkOrderScheduleEntity> queryWrapper) {
        if (StringUtils.isNotBlank(dto.getPlannedShipDate())) {
            dateFilter(dto.getPlannedShipDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.PLANNED_SHIP_DATE);
        }
        if (StringUtils.isNotBlank(dto.getDeliveryDeadline())) {
            dateFilter(dto.getDeliveryDeadline(), queryWrapper, com.yelink.dfs.constant.order.Constant.DELIVERY_DEADLINE);
        }
        if (StringUtils.isNotBlank(dto.getOrderDate())) {
            dateFilter(dto.getOrderDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.ORDER_DATE);
        }
        if (StringUtils.isNotBlank(dto.getSaleOrderChangeTime())) {
            dateFilter(dto.getSaleOrderChangeTime(), queryWrapper, com.yelink.dfs.constant.order.Constant.SALE_ORDER_CHANGE_TIME);
        }
        if (StringUtils.isNotBlank(dto.getDeliveryDate())) {
            dateFilter(dto.getDeliveryDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.DELIVERY_DATE);
        }
        if (StringUtils.isNotBlank(dto.getStartDate())) {
            dateFilter(dto.getStartDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.START_DATE);
        }
        if (StringUtils.isNotBlank(dto.getEndDate())) {
            dateFilter(dto.getEndDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.END_DATE);
        }
        if (StringUtils.isNotBlank(dto.getCreateTime())) {
            dateFilter(dto.getCreateTime(), queryWrapper, com.yelink.dfs.constant.order.Constant.CREATE_TIME);
        }
        if (StringUtils.isNotBlank(dto.getUpdateTime())) {
            dateFilter(dto.getUpdateTime(), queryWrapper, com.yelink.dfs.constant.order.Constant.UPDATE_TIME);
        }
        if (StringUtils.isNotBlank(dto.getSynchronizationTime())) {
            dateFilter(dto.getSynchronizationTime(), queryWrapper, com.yelink.dfs.constant.order.Constant.SYNCHRONIZATION_TIME);
        }
        if (StringUtils.isNotBlank(dto.getApprovalTime())) {
            dateFilter(dto.getApprovalTime(), queryWrapper, com.yelink.dfs.constant.order.Constant.APPROVAL_TIME);
        }
        if (StringUtils.isNotBlank(dto.getArrangementDate())) {
            dateFilter(dto.getArrangementDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.ARRANGEMENT_DATE);
        }
        if (StringUtils.isNotBlank(dto.getPlannedDeliveryDate())) {
            dateFilter(dto.getPlannedDeliveryDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.PLANNED_DELIVERY_DATE);
        }
        if (StringUtils.isNotBlank(dto.getStartDate())) {
            dateFilter(dto.getStartDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.START_DATE);
        }
        if (StringUtils.isNotBlank(dto.getEndDate())) {
            dateFilter(dto.getEndDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.END_DATE);
        }
        if (StringUtils.isNotBlank(dto.getEndDate())) {
            dateFilter(dto.getEndDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.ACTUAL_START_DATE);
        }
        if (StringUtils.isNotBlank(dto.getEndDate())) {
            dateFilter(dto.getEndDate(), queryWrapper, com.yelink.dfs.constant.order.Constant.ACTUAL_END_DATE);
        }
    }

    /**
     * 数字过滤
     */
    private void digitalFilter(String numberFilterList, LambdaQueryWrapper<TmpWorkOrderScheduleEntity> wrapper) {
        //String 转对象
        List<QuantityFieldVo> list = JSONArray.parseArray(numberFilterList, QuantityFieldVo.class);
        for (QuantityFieldVo quantityFieldVo : list) {
            String field = DataConversionUtil.humpToUnderline(quantityFieldVo.getId());
            String fieldTerm = quantityFieldVo.getTerm();
            String fieldValue = quantityFieldVo.getValue();
            //大于等于过滤
            if (fieldTerm.equals(com.yelink.dfs.constant.order.Constant.GT_EQUAL)) {
                wrapper.apply(field + " >= " + Double.parseDouble(fieldValue));
            }
            //小于等于过滤
            else if (fieldTerm.equals(com.yelink.dfs.constant.order.Constant.LT_EQUAL)) {
                wrapper.apply(field + " <= " + Double.parseDouble(fieldValue));
            }
            //范围过滤
            else if (fieldTerm.equals(com.yelink.dfs.constant.order.Constant.RANGE)) {
                String[] value = fieldValue.split(Constant.SEP);
                double v1 = Double.parseDouble(value[0]);
                double v2 = Double.parseDouble(value[1]);
                wrapper.apply(field + " BETWEEN " + v1 + " AND " + v2);
            }
        }
    }

    /**
     * 日期过滤
     */
    private void dateFilter(String dateField, LambdaQueryWrapper<TmpWorkOrderScheduleEntity> wrapper, String fieldName) {
        List<String> dateArray;
        String sqlDateField = DataConversionUtil.humpToUnderline(fieldName);
        dateArray = Arrays.stream(dateField.split(Constant.SEP)).collect(Collectors.toList());
        StringBuilder sql = new StringBuilder();
        for (int i = 0; i < dateArray.size(); i++) {
            sql.append(Constant.SINGLE_QUOTE).append(dateArray.get(i)).append(Constant.SINGLE_QUOTE).append(Constant.SEP);
        }
        String stringSql = sql.deleteCharAt(sql.length() - 1).toString();
        //查询日期满足条件且为空的值
        if (stringSql.contains(com.yelink.dfs.constant.order.Constant.NULL)) {
            wrapper.apply("(DATE_FORMAT(" + sqlDateField + ",'%Y-%m-%d') in (" + stringSql + ") or " + sqlDateField + " is null )");
        } else {
            wrapper.apply("DATE_FORMAT(" + sqlDateField + ",'%Y-%m-%d') in (" + stringSql + ")");
        }
    }

    @Override
    public void scheduleExport(String gridCodes, HttpServletResponse response) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
        List<List<String>> strings = new ArrayList<>();
        //设置表头
        List<String> tableNames = new ArrayList<>();
        tableNames.add("生产工单号");
        tableNames.add("产品型号");
        tableNames.add("计划数量");
        tableNames.add("物料类型");
        tableNames.add("车间名称");
        tableNames.add("产线名称");
        tableNames.add("状态");
        tableNames.add("计划交付时间");
        tableNames.add("计划开始时间");
        tableNames.add("计划结束时间");
        tableNames.add("当前库存");
        tableNames.add("物料是否齐套");
        tableNames.add("齐套数量");
        tableNames.add("欠套数量");
        tableNames.add("理论工时");
        tableNames.add("关联生产订单号");
        tableNames.add("图纸编号");
        tableNames.add("关联销售订单号");
        tableNames.add("关联型号");
        tableNames.add("关联数量");
        tableNames.add("客户名称");
        tableNames.add("包装方式");
        tableNames.add("辅材型号");
        tableNames.add("备注");
        strings.add(tableNames);
        //设置字段记录
        List<WorkOrderEntity> workOrderList = getOrderBingWorkOrderList(gridCodes);
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }
        for (WorkOrderEntity entity : workOrderList) {
            // 物料类型
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(entity.getMaterialCode());
            // 假如找不到物料，则记录找不到物料的信息
            if (materialEntity == null) {
                throw new ResponseException("找不到该物料" + entity.getMaterialCode());
            }
            DictEntity dictEntity = dictService.getById(materialEntity.getType());
            String materialType = dictEntity != null ? dictEntity.getName() : null;
            //车间名称
            List<ProductOrderEntity> productOrderEntities = orderWorkOrderService.productOrderListByWorkId(entity);

            List<SaleOrderEntity> saleOrders = orderWorkOrderService.listSaleOrderByWorkIdOfProductOrder(entity.getWorkOrderId());
            if (CollectionUtils.isEmpty(productOrderEntities)) {
                return;
            }
            ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(productOrderEntities.get(0).getProductOrderNumber()).build());

            String gridCode = productOrderEntity == null ? null : productOrderEntity.getProductOrderMaterial().getGridCode();
            GridEntity gridEntity = gridService.getDetailByCode(gridCode);
            Date date = productOrderEntity == null ? null : productOrderEntity.getProductOrderMaterial().getPlanProductStartTime();
            String plannedDelivery = null;
            if (date != null) {
                plannedDelivery = dateFormat.format(date);
            }
            String startDate = dateFormat.format(entity.getStartDate());
            String endDate = dateFormat.format(entity.getEndDate());
            List<String> values = new ArrayList<>();
            values.add(entity.getWorkOrderNumber());
            values.add(entity.getMaterialCode());
            values.add(entity.getPlanQuantity().toString());
            if (materialType == null) {
                values.add("--");
            } else {
                values.add(materialType);
            }
            values.add(gridEntity.getGname());
            values.add(entity.getLineName());
            values.add(WorkOrderStateEnum.getNameByCode(entity.getState()));
            if (plannedDelivery == null) {
                values.add("--");
            } else {
                values.add(plannedDelivery);
            }
            values.add(startDate);
            values.add(endDate);
            if (entity.getCurrentInventory() == null) {
                values.add("--");
            } else {
                values.add(entity.getCurrentInventory().toString());
            }
            if (entity.getMaterialIsComplete() == null) {
                values.add("--");
            } else if (entity.getMaterialIsComplete()) {
                values.add("是");
            } else if (!entity.getMaterialIsComplete()) {
                values.add("否");
            }
            if (entity.getMaterialCompleteNum() == null) {
                values.add("--");
            } else {
                values.add(entity.getMaterialCompleteNum().toString());
            }
            if (entity.getMaterialOweNum() == null) {
                values.add("--");
            } else {
                values.add(entity.getMaterialOweNum().toString());
            }
            if (entity.getTheoreticalWorkingHours() == null) {
                values.add("--");
            } else {
                values.add(entity.getTheoreticalWorkingHours().toString());
            }
            values.add(productOrderEntity.getProductOrderMaterial() == null ? null : productOrderEntity.getProductOrderMaterial().getProductOrderNumber());
            values.add(productOrderEntity.getProductOrderMaterial() == null ? null : productOrderEntity.getProductOrderMaterial().getSaleOrderCode());
            if (CollectionUtils.isEmpty(saleOrders)) {
                List<Integer> saleOrderIds = saleOrders.stream().map(SaleOrderEntity::getSaleOrderId).collect(Collectors.toList());
                PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderIds(saleOrderIds).build());
                List<SaleOrderMaterialEntity> saleOrderMaterialEntities = pageResult.getRecords().stream().map(SaleOrderVO::getSaleOrderMaterial).collect(Collectors.toList());

                // 销售订单的产品名称
                String materialCode = saleOrderMaterialEntities.stream().map(SaleOrderMaterialEntity::getMaterialCode).collect(Collectors.joining(","));
                values.add(materialCode);
                // 发货数量
                values.add(String.valueOf(saleOrderMaterialEntities.stream().mapToDouble(SaleOrderMaterialEntity::getAppliedQuantity).sum()));
                // 客户名称
                String customerNames = saleOrders.stream().map(SaleOrderEntity::getCustomerName).collect(Collectors.joining(","));
                values.add(customerNames);
                // 包装方式
                String packingMethod = saleOrderMaterialEntities.stream().map(SaleOrderMaterialEntity::getPackingMethod).collect(Collectors.joining(","));
                values.add(packingMethod == null ? "" : packingMethod);
                // 辅材型号
                String auxiliaryMaterialModel = saleOrderMaterialEntities.stream().map(SaleOrderMaterialEntity::getAuxiliaryMaterialModel).collect(Collectors.joining(","));
                values.add(auxiliaryMaterialModel == null ? "" : auxiliaryMaterialModel);
                if (productOrderEntity.getProductOrderMaterial() == null ? null : productOrderEntity.getProductOrderMaterial().getRemark() == null) {
                    values.add("--");
                } else {
                    values.add(productOrderEntity.getProductOrderMaterial().getRemark());
                }
            }
            strings.add(values);
        }
        try {
            ExcelUtil.exportWithContent(strings, response);
        } catch (IOException e) {
            log.error("导出excel异常", e);
        }
    }

    private List<WorkOrderEntity> getOrderBingWorkOrderList(String gridCodes) {
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().gridCodes(gridCodes).build());
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new ArrayList<>();
        }
        List<Integer> orderIdCollect = pageResult.getRecords().stream().map(ProductOrderEntity::getProductOrderMaterial).map(ProductOrderMaterialEntity::getProductOrderId).collect(Collectors.toList());
        return orderWorkOrderService.listWorkOrderByOrderIds(orderIdCollect, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scheduleInsert(List<WorkOrderEntity> list, String username, String workOrderNumber) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //批量新增工单排程(新增状态为创建状态的工单)
        int n = 1;
        String num;
        Integer productOrderMaterialId = null;
        for (WorkOrderEntity entity : list) {
            //生成工单号
            if (StringUtils.isBlank(workOrderNumber)) {
                num = codeFactory.getOrderNumber(workPropertise.getWorkOrderHeader());
            } else {
                int serialNo = n++;
                String str = String.format("%02d", serialNo);
                //工单号 = 工单号 + 流水号
                num = workOrderNumber + str;
            }
            // 根据单据类型配置动态获取默认值
            OrderTypeInfoVO defaultOrderTypeVO = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());
            entity.setOrderType(defaultOrderTypeVO.getOrderType());
            entity.setBusinessType(defaultOrderTypeVO.getBusinessTypeCode());

            SysUserEntity userEntity = sysUserService.selectByUsername(username);
            //获取物料当前库存
            double currentInventory = delMaterialCurrentInventory(entity.getMaterialCode());
            entity.setWorkOrderNumber(num);
            entity.setWorkOrderName(num);
            entity.setState(WorkOrderStateEnum.CREATED.getCode());
            entity.setType(ModelEnum.WORK_ORDER.getType());
            entity.setMaterialCode(entity.getMaterialCode());
            entity.setLineId(entity.getLineId());
            entity.setLineCode(entity.getLineCode());
            entity.setLineName(entity.getLineName());
            entity.setStartDate(entity.getStartDate());
            entity.setEndDate(entity.getEndDate());
            entity.setPlanQuantity(entity.getPlanQuantity());
            entity.setFinishCount(0.0);
            entity.setProgress(0.0);
            entity.setMagName(username);
            entity.setMagNickname(userEntity.getNickname());
            entity.setMagPhone(userEntity.getMobile());
            entity.setApprover(username);
            entity.setPriority(entity.getPriority());
            entity.setIsPid(false);
            entity.setCreateDate(new Date());
            entity.setCreateBy(username);
            entity.setCurrentInventory(currentInventory);
            entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
            entity.setApprover(username);
            // 判断生产订单是否能够排程
            List<ProductOrderEntity> productOrderList = entity.getProductOrderList();
            // 推送至kafka，获取生产订单物料数据
            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(productOrderList.get(0).getProductOrderNumber()).build());

            if (orderEntity == null) {
                return;
            }

            productOrderMaterialId = orderEntity.getProductOrderMaterial().getId();
            // 只有待排程的才能新增工单
            if (orderEntity.getProductOrderMaterial().getSchedulingStatus().equals(ScheduleStatusEnum.TO_BE_SCHEDULED.getType())) {
                // 工单编号判断去重
                notRepeat(entity.getWorkOrderNumber());
                // 设置生产基本单元id和隔离ID
                workOrderService.setProductionBasicUnitIdAndIsolationId(entity);
                // 新增工单
                workOrderService.save(entity);
                //关联生产订单订单
                saveOrderRelation(entity);
                //生产工单计划
                workOrderPlanService.createWorkOrderPlan(entity);
                //工单排程计算重量
                scheduleInsertMaterialPlan(entity);
                // 工单排程表更新/新增数据
                saveScheduleEntity(entity, orderEntity.getProductOrderMaterial());
            } else {
                throw new ResponseException(RespCodeEnum.PRODUCT_ORDER_NOT_SCHEDULE);
            }
        }
        //修改生产订单排程状态为已排程
        ProductOrderMaterialEntity build = ProductOrderMaterialEntity.builder()
                .id(productOrderMaterialId)
                .schedulingStatus(ScheduleStatusEnum.SCHEDULED.getType())
                .build();
        messagePushToKafkaService.pushNewMessage(build, Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.UPDATE_PRODUCT_ORDER_MATERIAL);

        //插入工单关联信息
        List<Integer> workOrderIds = list.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        int[] arr2 = workOrderIds.stream().mapToInt(Integer::intValue).toArray();
        String[] strings = Arrays.stream(arr2).mapToObj(String::valueOf).toArray(String[]::new);
        orderWorkOrderService.add(productOrderMaterialId, strings, Constant.ORDER_TYPE);
    }

//    /**
//     * 订单自动排产工单
//     *
//     * @param list
//     * @param username
//     * @param workOrderNumber
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void scheduleInsertAuto(List<WorkOrderEntity> list, String username, String workOrderNumber) {
//        if (CollectionUtils.isEmpty(list)) {
//            return;
//        }
//        //批量新增工单排程(新增状态为创建状态的工单)
//        int n = 1;
//        String num;
//        Integer productOrderId = null;
//        for (WorkOrderEntity entity : list) {
//            //拿到产线
//            List<WorkCenterEntity> workCenterEntityList = workCenterService.list();
//            if (workCenterEntityList == null) {
//                throw new ResponseException("未配置工作中心");
//            }
//            WorkCenterEntity workCenterEntity = workCenterEntityList.get(0);
//            LambdaQueryWrapper<ProductionLineEntity> productionLineQueryWrapper = new LambdaQueryWrapper<>();
//            productionLineQueryWrapper.eq(ProductionLineEntity::getWorkCenterId, workCenterEntity.getId());
//            List<ProductionLineEntity> lineEntityList = productionLineMapper.selectList(productionLineQueryWrapper);
//            if (CollectionUtils.isEmpty(lineEntityList)) {
//                throw new ResponseException("工作中心:" + workCenterEntity.getName() + "下未配置制造单元");
//            }
//            for (ProductionLineEntity productionLineEntity : lineEntityList) {
//                if (productionLineEntity.getScheduleDate() == null) {
//                    LambdaQueryWrapper<WorkOrderEntity> workOrderQueryWrapper = new LambdaQueryWrapper<>();
//                    workOrderQueryWrapper.eq(WorkOrderEntity::getLineId, productionLineEntity.getProductionLineId())
//                            .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode());
//                    List<WorkOrderEntity> workOrderEntities = workOrderService.list(workOrderQueryWrapper);
//                    if (!CollectionUtils.isEmpty(workOrderEntities)) {
//                        WorkOrderEntity workOrderEntityTemp = workOrderEntities.get(0);
//                        //拿计划完成时间
//                        productionLineEntity.setScheduleDate(workOrderEntityTemp.getEndDate());
//                        continue;
//                    }
//                    productionLineEntity.setScheduleDate(new Date());
//                }
//            }
//            //选中制造单元
//            ProductionLineEntity productionLineEntity = lineEntityList.stream().sorted(Comparator.comparing(ProductionLineEntity::getScheduleDate)).collect(Collectors.toList()).get(0);
//            //找到对应产线下对应物料的产能
//            Double standardCapacity = commonService.getDefaultStandardCapacity(productionLineEntity.getProductionLineId(), entity.getMaterialCode());
//            if (standardCapacity == null) {
//                throw new ResponseException("未配置制造单元:" + productionLineEntity.getName() + "产能");
//            }
//            Double workTime = MathUtil.divideDouble(entity.getPlanQuantity(), standardCapacity, 2);
//            entity.setLineId(productionLineEntity.getProductionLineId());
//            entity.setLineCode(productionLineEntity.getProductionLineCode());
//            entity.setLineName(productionLineEntity.getName());
//            entity.setStartDate(productionLineEntity.getScheduleDate());
//            entity.setEndDate(DateUtil.addHour(productionLineEntity.getScheduleDate(), workTime.intValue()));
//            entity.setPlanQuantity(entity.getPlanQuantity());
//            entity.setWorkCenterId(productionLineEntity.getWorkCenterId());
//            entity.setWorkCenterType(WorkCenterTypeEnum.LINE.getCode());
//            entity.setWorkCenterName(workCenterEntity.getName());
//            entity.setPriority(PriorityTypeEnum.NORMAL.getName());
//            productionLineEntity.setScheduleDate(entity.getEndDate());
//            productionLineMapper.updateById(productionLineEntity);
//
//
//            //生成工单号
//            if (StringUtils.isBlank(workOrderNumber)) {
//                num = codeFactory.getOrderNumber(workPropertise.getWorkOrderHeader());
//            } else {
//                int serialNo = n++;
//                String str = String.format("%02d", serialNo);
//                //工单号 = 工单号 + 流水号
//                num = workOrderNumber + str;
//            }
//            SysUserEntity userEntity = sysUserService.selectByUsername(username);
//            //获取物料当前库存
//            double currentInventory = delMaterialCurrentInventory(entity.getMaterialCode());
//            entity.setWorkOrderNumber(num);
//            entity.setWorkOrderName(num);
//            entity.setState(WorkOrderStateEnum.CREATED.getCode());
//            entity.setType(ModelEnum.WORK_ORDER.getType());
//            entity.setMaterialCode(entity.getMaterialCode());
////            entity.setLineId(entity.getLineId());
////            entity.setLineCode(entity.getLineCode());
////            entity.setLineName(entity.getLineName());
////            entity.setStartDate(entity.getStartDate());
////            entity.setEndDate(entity.getEndDate());
////            entity.setPlanQuantity(entity.getPlanQuantity());
//            entity.setFinishCount(0.0);
//            entity.setProgress(0.0);
//            entity.setOrderType(Constant.END);
//            entity.setMagName(username);
//            entity.setMagNickname(userEntity.getNickname());
//            entity.setMagPhone(userEntity.getMobile());
//            entity.setApprover(username);
//            entity.setPriority(entity.getPriority());
//            entity.setIsPid(false);
//            entity.setCreateDate(new Date());
//            entity.setCreateBy(username);
//            entity.setCurrentInventory(currentInventory);
//            entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
//            entity.setApprover(username);
//            // 判断生产订单是否能够排程
//            List<ProductOrderEntity> productOrderList = entity.getProductOrderPage();
//            ProductOrderMaterialEntity productOrderMaterialEntity = JacksonUtil.getResponseObject(productOrderMaterialInterface.getByProductOrderNumber(productOrderList.get(0).getProductOrderNumber()), ProductOrderMaterialEntity.class);
//            productOrderId = productOrderMaterialEntity.getId();
//            // 只有待排程的才能新增工单
//            if (productOrderMaterialEntity.getSchedulingStatus().equals(ScheduleStatusEnum.TO_BE_SCHEDULED.getType())) {
//                // 工单编号判断去重
//                notRepeat(entity.getWorkOrderNumber());
//                // 设置生产基本单元id和隔离ID
//                workOrderService.setProductionBasicUnitIdAndIsolationId(entity);
//                // 新增工单
//                workOrderService.save(entity);
//                //关联生产订单订单
//                saveOrderRelation(entity);
//                //生产工单计划
//                workOrderPlanService.createWorkOrderPlan(entity);
//                //工单排程计算重量
//                scheduleInsertMaterialPlan(entity);
//                // 工单排程表更新/新增数据
//                saveScheduleEntity(entity, productOrderMaterialEntity);
//            } else {
//                throw new ResponseException(RespCodeEnum.PRODUCT_ORDER_NOT_SCHEDULE);
//            }
//        }
//        //修改生产订单排程状态为已排程
//        ProductOrderMaterialEntity build = ProductOrderMaterialEntity.builder()
//                .id(productOrderId)
//                .schedulingStatus(ScheduleStatusEnum.SCHEDULED.getType())
//                .build();
//        productOrderMaterialInterface.updateByProductOrderEntity(build);
//
//        //插入工单关联信息
//        List<Integer> workOrderIds = list.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
//        int[] arr2 = workOrderIds.stream().mapToInt(Integer::intValue).toArray();
//        String[] strings = Arrays.stream(arr2).mapToObj(String::valueOf).toArray(String[]::new);
//        orderWorkOrderService.add(productOrderId, strings, Constant.ORDER_TYPE);
//    }

    /**
     * 判断是否去重
     */
    protected void notRepeat(String code) {
        //查询编号是否存在
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(code);
        if (Objects.nonNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_CODE_DUPLICATE.getMsgDes());
        }
    }

    /**
     * 关联生产订单订单
     */
    private void saveOrderRelation(WorkOrderEntity workOrderEntity) {
        Integer workOrderId = workOrderEntity.getWorkOrderId();
        //生产订单
        List<ProductOrderEntity> productOrderList = workOrderEntity.getProductOrderList();

        String orderNumber = null;
        if (!CollectionUtils.isEmpty(productOrderList)) {
            List<OrderWorkOrderEntity> orderWorkOrderEntities = productOrderList.stream()
                    .map(o -> OrderWorkOrderEntity.builder()
                            .workOrderId(workOrderId).orderId(o.getProductOrderId())
                            .orderType(OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode()).build())
                    .collect(Collectors.toList());
            orderWorkOrderService.saveBatch(orderWorkOrderEntities);
            orderNumber = productOrderList.stream().map(ProductOrderEntity::getProductOrderNumber).collect(Collectors.joining(","));
        }
        workOrderEntity.setProductOrderNumber(orderNumber);
        //销售订单
        String saleOrderNumber = null;
        List<SaleOrderEntity> saleOrderList = workOrderEntity.getSaleOrderList();
        if (!CollectionUtils.isEmpty(saleOrderList)) {
            List<OrderWorkOrderEntity> saleOrderWorkOrderEntities = saleOrderList.stream()
                    .map(o -> OrderWorkOrderEntity.builder()
                            .workOrderId(workOrderId).orderId(o.getSaleOrderId())
                            .orderType(OrderNumTypeEnum.SALE_ORDER.getTypeCode()).build())
                    .collect(Collectors.toList());
            orderWorkOrderService.saveBatch(saleOrderWorkOrderEntities);
            saleOrderNumber = saleOrderList.stream().map(SaleOrderEntity::getSaleOrderNumber).collect(Collectors.joining(","));
        }
        workOrderEntity.setSaleOrderNumber(saleOrderNumber);
        //更新逗号隔开的多个订单号至orderNumber字段
        LambdaUpdateWrapper<WorkOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrderEntity::getWorkOrderId, workOrderId)
                .set(WorkOrderEntity::getProductOrderNumber, workOrderEntity.getProductOrderNumber())
                .set(WorkOrderEntity::getSaleOrderNumber, workOrderEntity.getSaleOrderNumber());
        workOrderService.update(updateWrapper);
    }

    /**
     * 工单排程表新增一条数据
     */
    private void saveScheduleEntity(WorkOrderEntity entity, ProductOrderMaterialEntity productOrderMaterialEntity) {
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(TmpWorkOrderScheduleEntity::getOrderNumber, productOrderMaterialEntity.getProductOrderNumber());
        List<TmpWorkOrderScheduleEntity> scheduleEntities = this.list(qw);
        // 如果只有一条数据，且没有绑定工单，则将工单信息更新到该条数据上
        if (scheduleEntities.size() == 1 && scheduleEntities.get(0).getWorkOrderNumber() == null) {
            LambdaUpdateWrapper<TmpWorkOrderScheduleEntity> uw = new LambdaUpdateWrapper<>();
            uw.eq(TmpWorkOrderScheduleEntity::getOrderNumber, productOrderMaterialEntity.getProductOrderNumber())
                    .set(TmpWorkOrderScheduleEntity::getWorkOrderId, entity.getWorkOrderId())
                    .set(TmpWorkOrderScheduleEntity::getScheduleStatusName, ScheduleStatusEnum.SCHEDULED.getTypeName())
                    .set(TmpWorkOrderScheduleEntity::getWorkOrderNumber, entity.getWorkOrderNumber())
                    .set(TmpWorkOrderScheduleEntity::getLineName, entity.getLineName())
                    .set(TmpWorkOrderScheduleEntity::getLineId, entity.getLineId())
                    .set(TmpWorkOrderScheduleEntity::getPlanQuantity, entity.getPlanQuantity())
                    .set(TmpWorkOrderScheduleEntity::getFinishCount, entity.getFinishCount())
                    .set(TmpWorkOrderScheduleEntity::getStartDate, entity.getStartDate())
                    .set(TmpWorkOrderScheduleEntity::getEndDate, entity.getEndDate())
                    .set(TmpWorkOrderScheduleEntity::getMaterialOweNum, entity.getMaterialOweNum())
                    .set(TmpWorkOrderScheduleEntity::getState, entity.getState())
                    .set(TmpWorkOrderScheduleEntity::getStateName, WorkOrderStateEnum.getNameByCode(entity.getState()))
                    .set(TmpWorkOrderScheduleEntity::getUnqualified, entity.getUnqualified())
                    .set(TmpWorkOrderScheduleEntity::getProgress, entity.getProgress())
                    .set(TmpWorkOrderScheduleEntity::getActualStartDate, entity.getActualStartDate())
                    .set(TmpWorkOrderScheduleEntity::getActualEndDate, entity.getActualEndDate());
            this.update(uw);
        } else {
            // 获取车间名称
            GridEntity gridEntity = gridService.getDetailByCode(productOrderMaterialEntity.getGridCode());
            // 获取关联的销售订单
            PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderMaterialIds(Stream.of(productOrderMaterialEntity.getRelateSaleOrderMaterialId()).collect(Collectors.toList())).build());
            SaleOrderEntity saleOrderEntity = null;
            SaleOrderMaterialEntity saleOrderMaterialEntity = null;

            if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
                saleOrderMaterialEntity = pageResult.getRecords().get(0).getSaleOrderMaterial();
                saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(saleOrderMaterialEntity.getSaleOrderNumber()).build());
            }
            TmpWorkOrderScheduleEntity scheduleEntity = TmpWorkOrderScheduleEntity.builder()
                    .orderId(productOrderMaterialEntity.getProductOrderId())
                    .orderNumber(productOrderMaterialEntity.getProductOrderNumber())
                    .scheduleStatusName(ScheduleStatusEnum.SCHEDULED.getTypeName())
                    .orderDate(productOrderMaterialEntity.getOrderDate())
                    // 要货日期
                    .deliveryDeadline(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getRequireGoodsDate())
                    // 计划生产开始时间
                    .plannedDeliveryDate(productOrderMaterialEntity.getPlanProductStartTime())
                    // 发货日期
                    .plannedShipDate(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getDeliveryDate())
                    .scheduleStatusName(ScheduleStatusEnum.getNameByType(productOrderMaterialEntity.getSchedulingStatus()))
                    // 订单来源（国内/国外）
                    .modelType(saleOrderEntity == null ? null : saleOrderEntity.getSaleOrderSource())
                    .customerName(saleOrderEntity == null ? null : saleOrderEntity.getCustomerName())
                    // 下发数量
                    .needProduceQuantity(productOrderMaterialEntity.getNeedProduceQuantity())
                    .saleOrderCode(productOrderMaterialEntity.getSaleOrderCode())
                    .productCode(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getMaterialCode())
                    .productReserveNum(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getSalesQuantity())
                    .packingMethod(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getPackingMethod())
                    .workOrderId(entity.getWorkOrderId())
                    .workOrderNumber(entity.getWorkOrderNumber())
                    .lineName(entity.getLineName())
                    .lineId(entity.getLineId())
                    .planQuantity(entity.getPlanQuantity())
                    .finishCount(entity.getFinishCount())
                    .startDate(entity.getStartDate())
                    .endDate(entity.getEndDate())
                    .materialIsComplete(entity.getMaterialIsComplete())
                    .materialOweNum(entity.getMaterialOweNum())
                    .state(entity.getState())
                    .stateName(WorkOrderStateEnum.getNameByCode(entity.getState()))
                    .unqualified(entity.getUnqualified())
                    .progress(entity.getProgress())
                    .actualStartDate(entity.getActualStartDate())
                    .actualEndDate(entity.getActualEndDate())
                    .scheduleStatusName(ScheduleStatusEnum.SCHEDULED.getTypeName())
                    .materialCode(productOrderMaterialEntity.getMaterialCode())
                    .gridCode(productOrderMaterialEntity.getGridCode())
                    .gname(gridEntity.getGname())
                    .build();
            this.save(scheduleEntity);
        }
    }

    /**
     * 获取物料当前库存
     */
    protected double delMaterialCurrentInventory(String materialCode) {
        //堆放数量
        Double stock = extPurchaseReceiptInterface.getPurchaseReceiptMaterialStackAmount(PurchaseReceiptStackAmountDTO.builder().materialCode(materialCode).build());
        //库存数量
        Double sum = JacksonUtil.getResponseObject(inventoryDetailInterface.getStockInventoryByCode(materialCode), Double.class);
        return stock + sum;
    }

    @Override
    public String getMaterialCompletenessProgress() {
        String redisKey = RedisKeyPrefix.SCHEDULE_MATERIAL_COMPLETENESS_PROGRESS;
        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public DateTreeVO getDateTree(OrderWorkOrderScheduleDTO dto, String date) {
        DateTreeVO dateTreeVO = new DateTreeVO();
        //获取节点数据
        dateTreeVO.setYears(getDateNode(dto, date));
        return dateTreeVO;
    }

    @Override
    public WorkOrderScheduleSumVO getWorkOrderScheduleSum(OrderWorkOrderScheduleDTO dto) {
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> queryWrapper = new LambdaQueryWrapper<>();
        conditionQuery(dto, queryWrapper);
        List<TmpWorkOrderScheduleEntity> list = this.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            // 计划总数
            double planQuantitySum = list.stream().filter(entity -> entity.getPlanQuantity() != null)
                    .mapToDouble(TmpWorkOrderScheduleEntity::getPlanQuantity).sum();
            // 物料欠套数量总数
            double materialOweNumSum = list.stream().filter(entity -> entity.getMaterialOweNum() != null)
                    .mapToDouble(TmpWorkOrderScheduleEntity::getMaterialOweNum).sum();
            // 完成数量总数
            double finishCountSum = list.stream().filter(entity -> entity.getFinishCount() != null)
                    .mapToDouble(TmpWorkOrderScheduleEntity::getFinishCount).sum();
            // 下发数量总数
            double needProduceQuantitySum = list.stream().filter(entity -> entity.getNeedProduceQuantity() != null)
                    .mapToDouble(TmpWorkOrderScheduleEntity::getNeedProduceQuantity).sum();
            // 订单数量总数
            double productReserveNumSum = list.stream().filter(entity -> entity.getProductReserveNum() != null)
                    .mapToDouble(TmpWorkOrderScheduleEntity::getProductReserveNum).sum();
            // 不合格数量总数
            double unqualifiedSum = list.stream().filter(entity -> entity.getUnqualified() != null)
                    .mapToDouble(TmpWorkOrderScheduleEntity::getUnqualified).sum();

            return WorkOrderScheduleSumVO.builder()
                    .planQuantity(planQuantitySum)
                    .materialOweNum(materialOweNumSum)
                    .finishCount(finishCountSum)
                    .needProduceQuantity(needProduceQuantitySum)
                    .productReserveNum(productReserveNumSum)
                    .unqualified(unqualifiedSum)
                    .build();
        }
        return null;
    }

    @Override
    public List<String> getOrderCusNames(OrderWorkOrderScheduleDTO dto) {
        QueryWrapper<TmpWorkOrderScheduleEntity> qw = new QueryWrapper<>();
        // 条件查询
        conditionQuery(dto, qw.lambda());
        qw.select(DataConversionUtil.humpToUnderline(com.yelink.dfs.constant.order.Constant.CUSTOMER_NAME));
        List<Object> customerList = this.listObjs(qw);
        if (CollectionUtils.isEmpty(customerList)) {
            return new ArrayList<>();
        }
        return customerList.stream().filter(Objects::nonNull)
                .filter(o -> Strings.isNotEmpty(String.valueOf(o))).map(String::valueOf)
                .sorted(Collator.getInstance(Locale.CHINA))
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getOrderPackingMethod(OrderWorkOrderScheduleDTO dto) {
        QueryWrapper<TmpWorkOrderScheduleEntity> qw = new QueryWrapper<>();
        // 条件查询
        conditionQuery(dto, qw.lambda());
        qw.select(DataConversionUtil.humpToUnderline(com.yelink.dfs.constant.order.Constant.PACKING_METHOD));
        List<Object> packingMethodList = this.listObjs(qw);
        if (CollectionUtils.isEmpty(packingMethodList)) {
            return new ArrayList<>();
        }
        return packingMethodList.stream().filter(Objects::nonNull)
                .filter(o -> Strings.isNotEmpty(String.valueOf(o))).map(String::valueOf)
                .sorted(Collator.getInstance(Locale.CHINA))
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getAllLineName(OrderWorkOrderScheduleDTO dto) {
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> qw = new LambdaQueryWrapper<>();
        // 条件查询
        conditionQuery(dto, qw);
        List<TmpWorkOrderScheduleEntity> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().filter(o -> o.getLineName() != null).map(TmpWorkOrderScheduleEntity::getLineName)
                .sorted(Collator.getInstance(Locale.CHINA)).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getMaterialNameList(OrderWorkOrderScheduleDTO dto) {
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> qw = new LambdaQueryWrapper<>();
        // 条件查询
        conditionQuery(dto, qw);
        List<TmpWorkOrderScheduleEntity> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> materialCodes = list.stream().map(TmpWorkOrderScheduleEntity::getMaterialCode).collect(Collectors.toList());
        return materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list()
                .stream()
                .map(MaterialEntity::getName)
                .sorted(Collator.getInstance(Locale.CHINA))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getMaterialCodeList(OrderWorkOrderScheduleDTO dto) {
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> qw = new LambdaQueryWrapper<>();
        // 条件查询
        conditionQuery(dto, qw);
        List<TmpWorkOrderScheduleEntity> list = this.list(qw);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(TmpWorkOrderScheduleEntity::getMaterialCode)
                .filter(Objects::nonNull).sorted(Collator.getInstance(Locale.CHINA))
                .distinct().collect(Collectors.toList());
    }

    /**
     * 获取节点数据
     */
    private List<YearVO> getDateNode(OrderWorkOrderScheduleDTO dto, String date) {
        QueryWrapper<TmpWorkOrderScheduleEntity> qw = new QueryWrapper<>();
        // 条件查询
        conditionQuery(dto, qw.lambda());
        qw.select(DataConversionUtil.humpToUnderline(date));
        //获取查询字段日期值list
        List<Object> scheduleList = this.listObjs(qw);
        //对查询到的日期list格式转换、过滤 得到日期树初始数据
        if (CollectionUtils.isEmpty(scheduleList)) {
            return new ArrayList<>();
        }
        List<String> dateList = scheduleList.stream().filter(Objects::nonNull)
                .map(o -> DateUtil.format((Date) o, DateUtil.DATE_FORMAT)).distinct().collect(Collectors.toList());

        List<YearVO> yearVOList = new ArrayList<>();
        // 对列表进行年、月、日分组
        List<String> yearList = dateList.stream().map(o -> DateUtil.dateToDateTime(o, DateUtil.YEAR_FORMAT))
                .distinct().collect(Collectors.toList());
        for (String year : yearList) {
            YearVO yearVO = YearVO.builder().year(year).build();
            yearVOList.add(yearVO);
            yearVOList.sort(Comparator.comparing(YearVO::getYear));
            // 获取年份下的月份
            List<String> monthList = dateList.stream().filter(o -> o.contains(year))
                    .map(o -> DateUtil.dateToDateTime(o, DateUtil.ONLY_MONTH_FORMAT))
                    .distinct().sorted(Comparator.comparing(m -> m)).collect(Collectors.toList());
            List<MonthVO> monthVOList = new ArrayList<>();
            for (String month : monthList) {
                MonthVO monthVO = MonthVO.builder().month(month).build();
                monthVOList.add(monthVO);
                yearVO.setMonths(monthVOList);
                // 获取月份下的日
                String yearMonth = year + "-" + month;
                List<String> dayList = dateList.stream().filter(o -> o.contains(yearMonth))
                        .map(o -> DateUtil.dateToDateTime(o, DateUtil.DAY_FORMAT))
                        .distinct().sorted(Comparator.comparing(d -> d)).collect(Collectors.toList());
                monthVO.setDays(dayList);
            }
        }

        return yearVOList;
    }

    @Override
    @Async
    public void materialInspection() {
        // 加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.SCHEDULE_MATERIAL_LOCK, new Date(), 1, TimeUnit.SECONDS);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        String resetKey = RedisKeyPrefix.SCHEDULE_MATERIAL_COMPLETENESS_PROGRESS;
        try {
            // 计算物料齐备性前先清空临时表
            inspectionMapper.truncateTmpStockTable();
            Map<String, List<MaterialStockVO>> map = new LinkedHashMap<>();
            Map<BomKeyDTO, BomEntity> bomMap = new HashMap<>();
            int current = 1;
            int size = 50;
            // 分页查询工单进行业务处理，防止查询的大量数据占用大量内存，防止CPU飙高
            //获取为创建、生效的所有工单 并按计划开始时间排序（顺序）
            LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(WorkOrderEntity::getState, WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.RELEASED.getCode())
                    .orderByAsc(WorkOrderEntity::getStartDate)
                    .orderByAsc(WorkOrderEntity::getWorkOrderId);
            Page<WorkOrderEntity> page = workOrderService.page(new Page<>(current, size), wrapper);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                throw new ResponseException(RespCodeEnum.NOT_FOUND_INSPECT_WORK_ORDER);
            }
//            List<WorkOrderEntity> list = workOrderMapper.getInspectionWorkOrderList();
//            if (CollectionUtils.isEmpty(list)) {
//                throw new ResponseException(RespCodeEnum.NOT_FOUND_INSPECT_WORK_ORDER);
//            }
            long pages = page.getPages();
            long isHandledCount = 0;
            for (long i = 1; i <= pages; ) {
                calMaterialInspection(map, bomMap, page);
                // 已计算的数量存入到redis里，用于计算进度
                isHandledCount = i == pages ? page.getTotal() : isHandledCount + 50;
                String process = String.valueOf(MathUtil.divideDouble(isHandledCount, page.getTotal(), 2));
                redisTemplate.opsForValue().set(RedisKeyPrefix.SCHEDULE_MATERIAL_COMPLETENESS_PROGRESS, process);
                // 查询下批工单
                page = workOrderService.page(new Page<>(++i, size), wrapper);
            }
            log.info("=============================执行完成================================");
        } catch (ResponseException e) {
            redisTemplate.opsForValue().set(resetKey, e.getRespCodeEnum().getMsgDes());
        } catch (Exception e) {
            log.error("", e);
            redisTemplate.opsForValue().set(resetKey, "error");
        } finally {
            // 释放锁
            redisTemplate.delete(RedisKeyPrefix.SCHEDULE_MATERIAL_LOCK);
        }

    }

    /**
     * 计算物料齐备型
     */
    private void calMaterialInspection(Map<String, List<MaterialStockVO>> map, Map<BomKeyDTO, BomEntity> bomMap, Page<WorkOrderEntity> page) {
        for (int i = 0; i < page.getRecords().size(); i++) {
            int isNegative = 0;
            Date now = new Date();
            WorkOrderEntity workOrderEntity = page.getRecords().get(i);
            String materialCode = workOrderEntity.getMaterialCode();
            // 获取该物料BOM下的物料的库存(库存明细)
            // 涉及算法：相同时间的覆盖，不同时间的插入一条数据（两种方式都要对大于等于该时间的数量进行计算）
            StockInventoryDetailEntity stockMaterialEntity = getInventoryEntity(materialCode);
            // 判断map对象中是否存在该物料
            if (map.containsKey(materialCode)) {
                calMaterialNumIfExist(map, workOrderEntity, materialCode);
            } else {
                List<MaterialStockVO> newVoListInNewMaterialCode = new ArrayList<>();
                calMaterialNumIfNotExist(map, newVoListInNewMaterialCode, workOrderEntity, materialCode, stockMaterialEntity);
                map.put(materialCode, newVoListInNewMaterialCode);
            }
            // 获取最新的bom
            List<BomRawMaterialEntity> bomRawMaterialList = bomService.getLatestBomInCache(bomMap, new BomKeyDTO(materialCode));
            //倒扣各种物料并插入到临时表中
            List<TmpMaterialReadinessInspectionEntity> inspectionEntities = new ArrayList<>();
            Map<String, List<MaterialStockVO>> newMap = new HashMap<>();
            List<String> rawMaterials = new ArrayList<>();
            // bom下物料的齐备性检查
            for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialList) {
                // 如果bom下存在相同的物料，则取bom编码最小的那个（编码最小的为标准bom）
                if (rawMaterials.contains(bomRawMaterialEntity.getCode())) {
                    continue;
                } else {
                    rawMaterials.add(bomRawMaterialEntity.getCode());
                }
                isNegative = rawMaterialInspect(map, isNegative, workOrderEntity,
                        inspectionEntities, newMap, bomRawMaterialEntity);
            }
            // 不为0则一并更新到map对象中，更新库存
            if (isNegative == 0) {
                map.putAll(newMap);
            }
            inspectionService.saveBatch(inspectionEntities);
            // 同步到工单排程表里(是否齐套、齐套数量、不齐套数量)
            syncDataToWorkOrder(workOrderEntity, inspectionEntities);
            log.info("耗时:" + (System.currentTimeMillis() - now.getTime()));
        }
    }

    /**
     * bom下物料的齐备性检查
     * 涉及算法：获取bom下的所有物料，对其进行扣除，只要bom下的其中一个物料扣除后出现负数的情况，则不对缓存中对应的物料数量进行更新，
     * * 当扣除后整体都不为负数时，才整体更新库存，同时清空这个时间点之前的所有数据（目的是防止数据堆积无用的数据，降低查询效率）
     */
    private int rawMaterialInspect(Map<String, List<MaterialStockVO>> map, int isNegative, WorkOrderEntity workOrderEntity,
                                   List<TmpMaterialReadinessInspectionEntity> inspectionEntities,
                                   Map<String, List<MaterialStockVO>> newMap, BomRawMaterialEntity bomRawMaterialEntity) {
        double currentQuantity = 0.0, deductQuantity = 0.0, needQuantity = 0.0, completeQuantity = 0.0;
        String bomRawMaterialCode = bomRawMaterialEntity.getCode();
        DictEntity dictEntity = dictService.getById(bomRawMaterialEntity.getType());
        String bomRawMaterialType = dictEntity != null ? dictEntity.getName() : null;
        Rational bomCoefficient = new Rational(bomRawMaterialEntity.getNum().doubleValue(), bomRawMaterialEntity.getNumber().doubleValue());
        Rational planQuantity = new Rational(workOrderEntity.getPlanQuantity());
        Rational planMaterialQuantity = planQuantity.mulitiply(bomCoefficient);

        // 判断map对象中是否存在该物料
        if (map.containsKey(bomRawMaterialCode)) {
            List<MaterialStockVO> vos = map.get(bomRawMaterialCode);
            vos.sort(Comparator.comparing(MaterialStockVO::getTimestamp));
            List<Long> dates = vos.stream().map(a -> a.getTimestamp().getTime()).collect(Collectors.toList());
            long time = workOrderEntity.getEndDate().getTime();
            // 算法：时间相同则覆盖该值，且大于等于该时间的都要扣除对应数量;
            //       不存在该时间时，则插入一条数据，且大于等于该时间的都要扣除对应数量;
            if (dates.contains(time)) {
                List<MaterialStockVO> materialVoList = new ArrayList<>();
                for (MaterialStockVO vo : vos) {
                    double newNum = vo.getNum() - planMaterialQuantity.doubleValue();
                    if (vo.getTimestamp().getTime() >= time) {
                        if (vo.getTimestamp().getTime() == time) {
                            deductQuantity = newNum;
                            currentQuantity = vo.getNum();
                            needQuantity = planMaterialQuantity.doubleValue();
                            if (deductQuantity >= 0) {
                                completeQuantity = workOrderEntity.getPlanQuantity();
                            } else {
                                isNegative = 1;
                                Rational needQuantityRational = new Rational(needQuantity);
                                Rational deductQuantityRational = new Rational(deductQuantity);
                                Rational divide = needQuantityRational.add(deductQuantityRational).divide(bomCoefficient);
                                completeQuantity = divide.doubleValue();
                            }
                        }
                        // 循环遍历，大于等于该时间的都put进newMap里
                        setNewValToMap(workOrderEntity, newMap, newNum, bomRawMaterialCode, materialVoList, bomCoefficient.doubleValue());
                    }
                }
            } else {
                List<MaterialStockVO> materialVoList = new ArrayList<>();
                for (MaterialStockVO vo : vos) {
                    // 数量为上一次减去本次新增的数量
                    if (time > vo.getTimestamp().getTime()) {
                        double newNum = vo.getNum() - planMaterialQuantity.doubleValue();
                        deductQuantity = newNum;
                        currentQuantity = vo.getNum();
                        needQuantity = planMaterialQuantity.doubleValue();
                        if (deductQuantity >= 0) {
                            completeQuantity = workOrderEntity.getPlanQuantity();
                        } else {
                            isNegative = 1;
                            Rational needQuantityRational = new Rational(needQuantity);
                            Rational deductQuantityRational = new Rational(deductQuantity);
                            Rational divide = needQuantityRational.add(deductQuantityRational).divide(bomCoefficient);
                            completeQuantity = divide.doubleValue();
                        }
                        // put进newMap里
                        setNewValToMap(workOrderEntity, newMap, newNum, bomRawMaterialCode, materialVoList, bomCoefficient.doubleValue());
                        continue;
                    }
                    // 后续对象的数量统一减去新增的数量
                    if (vo.getTimestamp().getTime() > time) {
                        Double newNum = vo.getNum() - planMaterialQuantity.doubleValue();
                        setNewValToMap(workOrderEntity, newMap, newNum, bomRawMaterialCode, materialVoList, bomCoefficient.doubleValue());
                    }
                }
            }
        } else {
            List<MaterialStockVO> materialVoList = new ArrayList<>();
            // 获取该物料BOM下的物料的库存(库存明细)
            StockInventoryDetailEntity materialEntity = getInventoryEntity(bomRawMaterialCode);
            currentQuantity = materialEntity == null ? 0.0 : materialEntity.getInventoryQuantity();
            deductQuantity = materialEntity == null ? currentQuantity - workOrderEntity.getPlanQuantity() :
                    materialEntity.getInventoryQuantity() - workOrderEntity.getPlanQuantity();
            needQuantity = planMaterialQuantity.doubleValue();
            if (deductQuantity >= 0) {
                completeQuantity = workOrderEntity.getPlanQuantity();
            } else {
                isNegative = 1;
                Rational needQuantityRational = new Rational(needQuantity);
                Rational deductQuantityRational = new Rational(deductQuantity);
                Rational divide = needQuantityRational.add(deductQuantityRational).divide(bomCoefficient);
                completeQuantity = divide.doubleValue();
            }
            // 工单结束时间存一次到map中
            MaterialStockVO voEnd = MaterialStockVO.builder()
                    .timestamp(workOrderEntity.getStartDate())
                    .num(deductQuantity)
                    .ratio(bomCoefficient.doubleValue())
                    .build();
            materialVoList.add(voEnd);
            newMap.put(bomRawMaterialCode, materialVoList);
        }
        // 创建实体对象
        buildInspectEntity(workOrderEntity, inspectionEntities, currentQuantity, deductQuantity,
                needQuantity, completeQuantity, bomRawMaterialCode, bomRawMaterialType);
        return isNegative;
    }

    private StockInventoryDetailEntity getInventoryEntity(String materialCode) {
        // 除了不良库，获取所有库各个物料总和
        List<StockInventoryDetailEntity> list = JacksonUtil.getResponseArray(inventoryDetailInterface.getList(StockInventorySelectDTO.builder().materialCode(materialCode).build()), StockInventoryDetailEntity.class);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Double normalNum = extPurchaseReceiptInterface.getPurchaseReceiptMaterialStackAmount(PurchaseReceiptStackAmountDTO.builder().materialCode(materialCode).build());
        double sum = list.stream().filter(o -> !"不良库".equals(o.getWarehouseCode()))
                .filter(o -> Objects.nonNull(o.getInventoryQuantity()))
                .mapToDouble(StockInventoryDetailEntity::getInventoryQuantity).sum();
        StockInventoryDetailEntity entity = list.get(0);
        entity.setInventoryQuantity(sum + normalNum);
        return entity;
    }

    private void setNewValToMap(WorkOrderEntity workOrderEntity, Map<String, List<MaterialStockVO>> map,
                                Double deductQuantity, String bomRawMaterialCode,
                                List<MaterialStockVO> materialVoList, Double newNum) {
        MaterialStockVO stockConsumeVo = MaterialStockVO.builder()
                .timestamp(workOrderEntity.getStartDate())
                .num(deductQuantity)
                .ratio(newNum)
                .build();
        materialVoList.add(stockConsumeVo);
        map.put(bomRawMaterialCode, materialVoList);
    }

    private void buildInspectEntity(WorkOrderEntity workOrderEntity, List<TmpMaterialReadinessInspectionEntity> inspectionEntities,
                                    Double currentQuantity, Double deductQuantity, Double needQuantity,
                                    Double completeQuantity, String bomRawMaterialCode,
                                    String bomRawMaterialType) {
        TmpMaterialReadinessInspectionEntity entity = TmpMaterialReadinessInspectionEntity.builder()
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .materialCode(workOrderEntity.getMaterialCode())
                .materialQuantity(workOrderEntity.getPlanQuantity())
                .rawMaterialCode(bomRawMaterialCode)
                .rawMaterialType(bomRawMaterialType)
                .currentInventory(currentQuantity)
                .deductInventory(deductQuantity)
                .rawMaterialQuantity(needQuantity)
                .materialCompleteNum(completeQuantity)
                .createTime(new Date())
                .build();
        inspectionEntities.add(entity);
    }

    private void syncDataToWorkOrder(WorkOrderEntity workOrderEntity, List<TmpMaterialReadinessInspectionEntity> inspectionEntities) {
        Optional<TmpMaterialReadinessInspectionEntity> min = inspectionEntities.stream()
                .min(Comparator.comparing(TmpMaterialReadinessInspectionEntity::getMaterialCompleteNum));
        if (min.isPresent()) {
            TmpMaterialReadinessInspectionEntity inspectionEntity = min.get();
            if (inspectionEntity.getMaterialCompleteNum().equals(workOrderEntity.getPlanQuantity())) {
                workOrderEntity.setMaterialIsComplete(true);
                workOrderEntity.setMaterialOweNum(0.0);
                workOrderEntity.setMaterialCompleteNum(workOrderEntity.getPlanQuantity());
            } else {
                workOrderEntity.setMaterialIsComplete(false);
                workOrderEntity.setMaterialOweNum(workOrderEntity.getPlanQuantity() - inspectionEntity.getMaterialCompleteNum());
                workOrderEntity.setMaterialCompleteNum(inspectionEntity.getMaterialCompleteNum());
            }
            workOrderService.updateById(workOrderEntity);
        }
    }

    private void calMaterialNumIfNotExist(Map<String, List<MaterialStockVO>> map, List<MaterialStockVO> voList,
                                          WorkOrderEntity workOrderEntity, String materialCode,
                                          StockInventoryDetailEntity stockMaterialEntity) {
        // 初始化库存
        MaterialStockVO voStart = MaterialStockVO.builder()
                .timestamp(workOrderEntity.getStartDate())
                .num(stockMaterialEntity == null ? 0.0 : stockMaterialEntity.getInventoryQuantity())
                .build();
        voList.add(voStart);
        MaterialStockVO voEnd = MaterialStockVO.builder()
                .timestamp(workOrderEntity.getEndDate())
                .num(stockMaterialEntity == null ? workOrderEntity.getPlanQuantity() :
                        stockMaterialEntity.getInventoryQuantity() + workOrderEntity.getPlanQuantity())
                .build();
        voList.add(voEnd);
        map.put(materialCode, voList);
    }

    private void calMaterialNumIfExist(Map<String, List<MaterialStockVO>> map, WorkOrderEntity workOrderEntity, String materialCode) {
        List<MaterialStockVO> vos = map.get(materialCode);
        // 用于工单的排序
        vos.sort(Comparator.comparing(MaterialStockVO::getTimestamp));
        List<Long> dates = vos.stream().map(a -> a.getTimestamp().getTime()).collect(Collectors.toList());
        long time = workOrderEntity.getEndDate().getTime();
        // 存在时的算法：大于等于该时间的统一增加数量，
        // 不存在时的算法：改新增的数据，数量为上一条数据的数量加上自己的数量，后续对象的数据，统一新增变量
        if (dates.contains(time)) {
            for (MaterialStockVO vo : vos) {
                if (vo.getTimestamp().getTime() >= time) {
                    Double newNum = vo.getNum() + workOrderEntity.getPlanQuantity();
                    vo.setNum(newNum);
                }
            }
        } else {
            List<Long> lessList = vos.stream().map(a -> a.getTimestamp().getTime()).filter(o -> o < time).collect(Collectors.toList());
            // 大于新时间的对象，统一加上新增的数量
            for (int j = lessList.size(); j < vos.size(); j++) {
                MaterialStockVO vo = vos.get(j);
                Double newNum = vo.getNum() + workOrderEntity.getPlanQuantity();
                vo.setNum(newNum);
            }
            // 小于新时间的对象，将新增的时间对象按时间顺序插入到vos中
            if (!CollectionUtils.isEmpty(lessList)) {
                // 只新增一次，数量为上一次加上本次新增的数量
                MaterialStockVO voEnd = MaterialStockVO.builder()
                        .timestamp(workOrderEntity.getEndDate())
                        .num(vos.get(lessList.size() - 1).getNum() + workOrderEntity.getPlanQuantity())
                        .build();
                vos.add(voEnd);
            }
        }
    }

    @Override
    public Page<TmpMaterialReadinessInspectionEntity> inspectDetails(String workOrderNumber, Integer
            current, Integer size) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        Page<TmpMaterialReadinessInspectionEntity> listByWorkOrderNumber = inspectionService.getListByWorkOrderNumber(workOrderNumber, current, size);
        for (TmpMaterialReadinessInspectionEntity entity : listByWorkOrderNumber.getRecords()) {
            // 获取物料类型
            entity.setMaterialIsComplete(workOrderEntity.getMaterialIsComplete());
            entity.setMaterialCompleteNum(workOrderEntity.getMaterialCompleteNum());
            entity.setMaterialOweNum(workOrderEntity.getMaterialOweNum());
            entity.setMaterialFields(materialService.getMaterialEntityByCode(entity.getRawMaterialCode()));
            inspectionService.updateById(entity);
        }
        return listByWorkOrderNumber;
    }

    @Override
    public List<CapacityLoadVo> getCapacityLoad() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_FORMAT);
        ArrayList<CapacityLoadVo> capacityLoadVos = new ArrayList<>();
        //所有工单数据
        //拿到计划开始为未来一周的数据并按顺序排
        List<WorkOrderEntity> list = workOrderMapper.getFutureSevenDaysList();
        if (CollectionUtils.isEmpty(list)) {
            return capacityLoadVos;
        }
        //将所有的工单根据产线分组
        Map<Integer, List<WorkOrderEntity>> collect = list.stream().filter(o -> o.getLineId() != null)
                .collect(Collectors.groupingBy(WorkOrderEntity::getLineId));
        for (Map.Entry<Integer, List<WorkOrderEntity>> entry : collect.entrySet()) {
            Integer lineId = entry.getKey();
            HashMap<String, List<WorkingHoursDTO>> dataMap = new HashMap<>();
            //相同产线下的所有工单
            List<WorkOrderEntity> lineWorkOrderList = entry.getValue();
            //开始时间天进行分组
            Map<Date, List<WorkOrderEntity>> dateListMap = lineWorkOrderList.stream().filter(o -> o.getStartDate() != null)
                    .collect(Collectors.groupingBy(WorkOrderEntity::getStartDate));
            // 获取工单关联的主资源
            List<String> workOrderNumbers = lineWorkOrderList.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            Map<String, List<ProductionResourceDTO>> basicUnitMap = basicUnitRelationService.lambdaQuery()
                    .in(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.groupingBy(
                            WorkOrderBasicUnitRelationEntity::getWorkOrderNumber,
                            Collectors.mapping(
                                    entity -> ProductionResourceDTO.builder()
                                            .workCenterType(entity.getWorkCenterType())
                                            .productionBasicUnitId(entity.getProductionBasicUnitId())
                                            .build(),
                                    Collectors.toList()
                            )
                    ));
            // 查询工单关联的关联资源
            Map<Integer, List<ProductionResourceDTO>> relatedResourceMap = capacityService.getRelatedResourceByWorkOrder(workOrderNumbers);
            for (Map.Entry<Date, List<WorkOrderEntity>> dateListEntry : dateListMap.entrySet()) {
                ArrayList<WorkingHoursDTO> hoursVoList = new ArrayList<>();
                //每一天的工单（存在多个）
                List<WorkOrderEntity> records = dateListEntry.getValue();
                //计划开始时间
                Date startDate = dateListEntry.getKey();
                String time = dateFormat.format(startDate);
                //遍历循环计算每天下的所有工单的工作时长
                records.forEach(workOrderEntity -> {
                    //给各个工单插入理论工作时间
                    //计算每个工单的生产的理论工作时长
                    CapacityGetDTO selectDTO = CapacityGetDTO.builder()
                            .materialCode(workOrderEntity.getMaterialCode())
                            .workCenterId(workOrderEntity.getWorkCenterId())
                            .mainResourceDTOS(basicUnitMap.get(workOrderEntity.getWorkOrderNumber()))
                            .relatedResourceDTOS(relatedResourceMap.get(workOrderEntity.getWorkOrderId()))
                            .build();
                    CapacityEntity capacityEntity = capacityService.getCapacity(selectDTO);
                    double theoreticalWorkingHours = 0;
                    if (capacityEntity != null) {
                        Double capacity = capacityEntity.getCapacity();
                        Double planQuantity = workOrderEntity.getPlanQuantity();
                        theoreticalWorkingHours = MathUtil.divideDouble(planQuantity, capacity, 2);
                    }
                    workOrderEntity.setTheoreticalWorkingHours(theoreticalWorkingHours);
                    //插入工单的理论工作时长
                    workOrderService.updateById(workOrderEntity);
                });
                //当天的产线的工作时长的总和
                double sum = records.stream().mapToDouble(WorkOrderEntity::getTheoreticalWorkingHours).sum();
                //产线日历的可用时间
                Double hours = workCalendarService.traceWorkDuration(lineId, LINE, startDate);
                WorkingHoursDTO workingHours = WorkingHoursDTO.builder().useHours(sum).availableHours(hours != null ? hours : 0).build();
                hoursVoList.add(workingHours);
                dataMap.put(time, hoursVoList);
            }
            //通过产线查询车间
            ProductionLineEntity lineEntity = productionLineMapper.selectById(lineId);
            GridEntity gridEntity = gridService.getById(lineEntity.getGid());
            //组装数据
            CapacityLoadVo build = CapacityLoadVo.builder()
                    .gridName(gridEntity.getGname())
                    .lineName(lineEntity.getName())
                    .data(dataMap)
                    .build();
            capacityLoadVos.add(build);
        }
        return capacityLoadVos;
    }

    @Override
    public void scheduleMaterialConfInReverse(List<ReverseMaterialDTO> dtos) {
        String resetKey = RedisKeyPrefix.SCHEDULE_MATERIAL_REVERSE_PROGRESS;
        //加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.SCHEDULE_REVERSE_MATERIAL_LOCK, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            redisTemplate.opsForValue().set(resetKey, "1");
            return;
        }
        try {
            // 初始化配置表
            scheduleReverseService.remove(null);
            workOrderMapper.dropScheduleReverseMaterialTable();
            workOrderMapper.createScheduleInitReverseMaterialTable();
            if (CollectionUtils.isEmpty(dtos)) {
                redisTemplate.opsForValue().set(resetKey, "1");
                return;
            }
            // 将配置列表添加到配置表里
            scheduleReverseService.saveBatch(JSONObject.parseArray(JSONObject.toJSONString(dtos), ConfigScheduleReverseMaterialDetailEntity.class));
            // 去重辅助计划的物料编码
            List<String> materialCodes = distinctScheduleMaterialCode();
            if (CollectionUtils.isEmpty(materialCodes)) {
                redisTemplate.opsForValue().set(resetKey, "1");
                return;
            }
            // 重新新建初始化表（只有一个字段，materialCode），通过前端传递的列名新增字段
            workOrderMapper.initScheduleMaterialCodeData(materialCodes);
            for (int i = 0; i < dtos.size(); i++) {
                ReverseMaterialDTO dto = dtos.get(i);
                // 随机生成不重复的UUID当作字段名，避免使用中文导致编码失败
                String fieldName = UUID.randomUUID().toString();
                workOrderMapper.addColumnToScheduleMaterialTable(fieldName, dto.getColumnName());
                // 通过前端传递的类型和规则去物料表（dfs_material）匹配相应的物料编码
                List<MaterialEntity> materialList = matchMaterialEntitiesByRules(dto);

                if (!CollectionUtils.isEmpty(materialList)) {
                    // 将原料编码更新到倒排表的新增字段中
                    updateFieldData(materialCodes, fieldName, materialList);
                }
                // 已计算的数量存入到redis里，用于计算进度
                String redisKey = RedisKeyPrefix.SCHEDULE_MATERIAL_REVERSE_PROGRESS;
                String process = String.valueOf(MathUtil.divideDouble(i + 1, dtos.size(), 2));
                redisTemplate.opsForValue().set(redisKey, process);
            }
        } catch (ResponseException e) {
            redisTemplate.opsForValue().set(resetKey, e.getRespCodeEnum().getMsgDes());
        } catch (Exception e) {
            log.error("", e);
            redisTemplate.opsForValue().set(resetKey, "error");
        } finally {
            // 释放锁
            redisTemplate.delete(RedisKeyPrefix.SCHEDULE_REVERSE_MATERIAL_LOCK);
        }
    }

    /**
     * 通过传递的类型和规则去物料表（dfs_material）匹配相应的物料编码
     */
    private List<MaterialEntity> matchMaterialEntitiesByRules(ReverseMaterialDTO dto) {
        // 通过物料类型名称获取多条相同名称的对象
        List<String> materialTypeIds = dictService.lambdaQuery().eq(DictEntity::getName, dto.getMaterialTypeName()).list()
                .stream().map(DictEntity::getId).map(String::valueOf).collect(Collectors.toList());

        // 匹配规则
        MaterialsSelectDTO materialsSelectDTO = new MaterialsSelectDTO();
        materialsSelectDTO.setTypes(String.join(Constants.SEP, materialTypeIds));

        List<String> applyStrs = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getMaterialNameMatchRule()) && StringUtils.isNotEmpty(dto.getMaterialNameMatchVal())) {
            if (dto.getMaterialNameMatchRule().equals(MatchRuleEnum.START_WITH.getName())) {
                applyStrs.add("name like '" + dto.getMaterialNameMatchVal() + "%'");
            } else if (dto.getMaterialNameMatchRule().equals(MatchRuleEnum.END_WITH.getName())) {
                applyStrs.add("name like '%" + dto.getMaterialNameMatchVal() + "'");
            } else {
                applyStrs.add("name like '%" + dto.getMaterialNameMatchVal() + "%'");
            }
        }
        if (StringUtils.isNotEmpty(dto.getMaterialStandardMatchRule()) && StringUtils.isNotEmpty(dto.getMaterialStandardMatchVal())) {
            if (dto.getMaterialStandardMatchRule().equals(MatchRuleEnum.START_WITH.getName())) {
                applyStrs.add("standard like '" + dto.getMaterialStandardMatchVal() + "%'");
            } else if (dto.getMaterialStandardMatchRule().equals(MatchRuleEnum.END_WITH.getName())) {
                applyStrs.add("standard like '%" + dto.getMaterialStandardMatchVal() + "'");
            } else {
                applyStrs.add("standard like '%" + dto.getMaterialStandardMatchVal() + "%'");
            }
        }
        materialsSelectDTO.setApplyStrs(applyStrs);
        Page<MaterialEntity> materialPage = materialService.getList(materialsSelectDTO);
        return materialPage.getRecords();
    }

    /**
     * 去重工单排程的物料编码
     */
    private List<String> distinctScheduleMaterialCode() {
        QueryWrapper<TmpWorkOrderScheduleEntity> qw = new QueryWrapper<>();
        qw.select("distinct " + DataConversionUtil.humpToUnderline(com.yelink.dfs.constant.order.Constant.MATERIAL_CODE));
        List<Object> objects = this.listObjs(qw);
        if (CollectionUtils.isEmpty(objects)) {
            return null;
        }
        return objects.stream().map(String::valueOf).collect(Collectors.toList());
    }

    /**
     * 将原料编码更新到倒排表的新增字段中
     */
    private void updateFieldData(List<String> materialCodes, String fieldName, List<MaterialEntity> materialEntities) {
        // 如果包含bom编码，则将原料编码更新到倒排表的新增字段中
        List<String> rawMaterialCodes = materialEntities.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
        LambdaQueryWrapper<BomTileEntity> bomTileWrapper = new LambdaQueryWrapper<>();
        bomTileWrapper.in(BomTileEntity::getMaterialCode, rawMaterialCodes);
        List<BomTileEntity> bomTileEntities = bomTileService.list(bomTileWrapper);
        for (BomTileEntity tileEntity : bomTileEntities) {
            if (materialCodes.contains(tileEntity.getBomMaterialCode())) {
                List<MaterialEntity> collect = materialEntities.stream()
                        .filter(o -> o.getCode().equals(tileEntity.getMaterialCode())).collect(Collectors.toList());
                // 获取属于bom编码的原料编码，如果存在则按逗号拼接
                String newMaterialName = null;
                String elderMaterialName = workOrderMapper.getScheduleFieldData(tileEntity.getBomMaterialCode(), fieldName);
                if (StringUtils.isNotEmpty(elderMaterialName)) {
                    List<String> materialNames = Arrays.asList(elderMaterialName.split(Constant.SEP));
                    if (!materialNames.contains(collect.get(0).getName())) {
                        newMaterialName = elderMaterialName + Constant.SEP + collect.get(0).getName();
                    }
                    workOrderMapper.updateScheduleFieldData(tileEntity.getBomMaterialCode(), fieldName,
                            StringUtils.isEmpty(newMaterialName) ? elderMaterialName : newMaterialName);
                } else {
                    workOrderMapper.updateScheduleFieldData(tileEntity.getBomMaterialCode(), fieldName, collect.get(0).getName());
                }
            }
        }
    }

    @Override
    public List<CommonTableDTO> getScheduleReverseExtendFieldData() {
        return workOrderMapper.getScheduleReverseExtendFieldData(workPropertise.getTableSchema());
    }


    @Override
    public List<String> getScheduleReverseMaterialNameList(OrderWorkOrderScheduleDTO dto, String fieldName) {
        // 获取过滤后的辅助计划列表
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> qw = new LambdaQueryWrapper<>();
        // 条件查询
        conditionQuery(dto, qw);
        List<TmpWorkOrderScheduleEntity> records = this.list(qw);
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<String> materialCodes = records.stream().map(TmpWorkOrderScheduleEntity::getMaterialCode).collect(Collectors.toList());
        // 获取拓展字段（原料字段）
        List<Map<String, String>> listMap = workOrderMapper.getScheduleReverseMaterialConf(materialCodes);
        List<String> list = new ArrayList<>();
        for (Map<String, String> stringMap : listMap) {
            for (Map.Entry<String, String> entry : stringMap.entrySet()) {
                if (entry.getKey().equals(fieldName)) {
                    // 可能存在多个物料编码
                    List<String> materialNames = Arrays.stream(entry.getValue().split(Constant.SEP)).collect(Collectors.toList());
                    for (String materialName : materialNames) {
                        if (list.contains(materialName)) {
                            continue;
                        }
                        list.add(materialName);
                    }
                }
            }
        }
        // 首字母排序
        return list.stream().sorted(Collator.getInstance(Locale.CHINA)).collect(Collectors.toList());
    }

    @Override
    public MaterialInventoryVO getScheduleMaterialInventory(String workOrderNumber, String materialName) {
        // 通过物料名称获取物料编码
        LambdaQueryWrapper<MaterialEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialEntity::getName, materialName);
        MaterialEntity materialEntity = materialService.getOne(wrapper);
        if (materialEntity == null) {
            return MaterialInventoryVO.builder().build();
        }

        // 获取物料当前库存
        double currentInventory = delMaterialCurrentInventory(materialEntity.getCode());
        // 获取物料可用库存
        TmpMaterialReadinessInspectionEntity inspectionEntity = inspectionService.lambdaQuery()
                .eq(TmpMaterialReadinessInspectionEntity::getWorkOrderNumber, workOrderNumber)
                .eq(TmpMaterialReadinessInspectionEntity::getRawMaterialCode, materialEntity.getCode())
                .last("limit 1")
                .one();
        if (inspectionEntity == null) {
            return MaterialInventoryVO.builder().currentInventory(currentInventory).build();
        }
        return MaterialInventoryVO.builder().currentInventory(currentInventory)
                .availableInventory(inspectionEntity.getMaterialQuantity()).build();
    }

    @Override
    public String getScheduleReverseMaterialProgress() {
        String redisKey = RedisKeyPrefix.SCHEDULE_MATERIAL_REVERSE_PROGRESS;
        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public Page<TmpWorkOrderScheduleEntity> getBindGridList(String gridCodes, Boolean isConcludeInComplete) {
        if (StringUtils.isEmpty(gridCodes)) {
            return null;
        }
        List<String> gridCodeArray = Arrays.stream(gridCodes.split(Constant.SEP)).collect(Collectors.toList());
        // 删除该车间对应的排程数据
        LambdaQueryWrapper<TmpWorkOrderScheduleEntity> removeWrapper = new LambdaQueryWrapper<>();
        removeWrapper.in(TmpWorkOrderScheduleEntity::getGridCode, gridCodeArray);
        this.remove(removeWrapper);
        // 获取状态为创建、生效，计划交付日期不为空的生产订单
        StringBuilder states = new StringBuilder();
        states.append(OrderStateEnum.CREATED.getCode()).append(Constant.SEP).append(OrderStateEnum.RELEASED.getCode());

        ProductOrderSelectOpenDTO build = ProductOrderSelectOpenDTO.builder()
                .states(states.toString())
                // 车间编码
                .gridCodes(gridCodes)
                .build();
        PageResult<ProductOrderEntity> productPage = extProductOrderInterface.getPage(build);
        if (CollectionUtils.isEmpty(productPage.getRecords())) {
            return new Page<>();
        }

        List<TmpWorkOrderScheduleEntity> scheduleEntities = new ArrayList<>();
        for (ProductOrderEntity productOrderEntity : productPage.getRecords()) {
            ProductOrderMaterialEntity productOrderMaterial = productOrderEntity.getProductOrderMaterial();
            // 排程状态名称
            productOrderMaterial.setSchedulingStatus(ScheduleStatusEnum.getNameByType(productOrderMaterial.getSchedulingStatus()));
            // 获取车间名称
            GridEntity gridEntity = gridService.getDetailByCode(productOrderMaterial.getGridCode());
            productOrderMaterial.setGridName(gridEntity.getGname());
            // 获取关联的工单信息
            List<WorkOrderEntity> workOrderEntities = orderWorkOrderService.listWorkOrderByOrderId(productOrderMaterial.getProductOrderId(), OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
            if (!CollectionUtils.isEmpty(workOrderEntities)) {
                for (WorkOrderEntity workOrderEntity : workOrderEntities) {
                    // 存在关联工单--新建工单排程对象
                    buildScheduleEntityIfHasWorkOrder(scheduleEntities, productOrderMaterial, workOrderEntity);
                }
            } else {
                // 无关联工单--新建工单排程对象
                buildScheduleEntityIfHasNotWorkOrder(scheduleEntities, productOrderMaterial);
            }
        }
        this.saveBatch(scheduleEntities);
        Page<TmpWorkOrderScheduleEntity> page = new Page<>();
        page.setRecords(scheduleEntities);
        page.setTotal(scheduleEntities.size());
        // 获取拓展字段（原料字段）
        if (CollectionUtils.isEmpty(scheduleEntities)) {
            return page;
        }
        List<String> materialCodes = scheduleEntities.stream().map(TmpWorkOrderScheduleEntity::getMaterialCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialCodes)) {
            return page;
        }
        // 获取物料相关字段
        Map<String, MaterialEntity> materialEntityMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list()
                .stream().collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
        List<Map<String, String>> listMap = workOrderMapper.getScheduleReverseMaterialConf(materialCodes);
        Map<String, Map<String, String>> collect = listMap.stream()
                .collect(Collectors.groupingBy(o -> o.get(DataConversionUtil.humpToUnderline(com.yelink.dfs.constant.order.Constant.MATERIAL_CODE)),
                        Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        for (TmpWorkOrderScheduleEntity vo : scheduleEntities) {
            vo.setMaterialFields(materialEntityMap.get(vo.getMaterialCode()));
            if (!CollectionUtils.isEmpty(collect) && collect.containsKey(vo.getMaterialCode())) {
                vo.setExtend(collect.get(vo.getMaterialCode()));
            }
        }
        return page;
    }

    /**
     * 无关联工单--新建工单排程对象
     */
    private void buildScheduleEntityIfHasNotWorkOrder(List<TmpWorkOrderScheduleEntity> scheduleEntities, ProductOrderMaterialEntity productOrderMaterialEntity) {
        // 获取关联的销售订单
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderMaterialIds(Stream.of(productOrderMaterialEntity.getRelateSaleOrderMaterialId()).collect(Collectors.toList())).build());

        SaleOrderEntity saleOrderEntity = null;
        SaleOrderMaterialEntity saleOrderMaterialEntity = null;

        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            saleOrderMaterialEntity = pageResult.getRecords().get(0).getSaleOrderMaterial();
            saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(saleOrderMaterialEntity.getSaleOrderNumber()).build());
        }
        TmpWorkOrderScheduleEntity scheduleEntity = TmpWorkOrderScheduleEntity.builder()
                .orderId(productOrderMaterialEntity.getProductOrderId())
                .orderNumber(productOrderMaterialEntity.getProductOrderNumber())
                .scheduleStatusName(productOrderMaterialEntity.getSchedulingStatusName())
                .orderDate(productOrderMaterialEntity.getOrderDate())
                .gname(productOrderMaterialEntity.getGridName())
                .deliveryDeadline(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getRequireGoodsDate())
                .plannedDeliveryDate(productOrderMaterialEntity.getPlanProductStartTime())
                .plannedShipDate(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getDeliveryDate())
                .planQuantity(0.0)
                .modelType(saleOrderEntity == null ? null : saleOrderEntity.getSaleOrderSource())
                .customerName(saleOrderEntity == null ? null : saleOrderEntity.getCustomerName())
                .materialCode(productOrderMaterialEntity.getMaterialCode())
                .needProduceQuantity(productOrderMaterialEntity.getNeedProduceQuantity())
                .saleOrderCode(productOrderMaterialEntity.getSaleOrderCode())
                .productCode(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getMaterialCode())
                .productReserveNum(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getSalesQuantity())
                .packingMethod(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getPackingMethod())
                .gridCode(productOrderMaterialEntity.getGridCode())
                .sortDate(productOrderMaterialEntity.getPlanProductEndTime() == null ?
                        productOrderMaterialEntity.getCreateTime() : productOrderMaterialEntity.getPlanProductEndTime())
                .build();
        scheduleEntities.add(scheduleEntity);
    }

    /**
     * 存在关联工单--新建工单排程对象
     */
    private void buildScheduleEntityIfHasWorkOrder(List<TmpWorkOrderScheduleEntity> scheduleEntities, ProductOrderMaterialEntity productOrderMaterialEntity, WorkOrderEntity workOrderEntity) {
        // 获取关联的销售订单
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderMaterialIds(Stream.of(productOrderMaterialEntity.getRelateSaleOrderMaterialId()).collect(Collectors.toList())).build());

        SaleOrderEntity saleOrderEntity = null;
        SaleOrderMaterialEntity saleOrderMaterialEntity = null;

        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            saleOrderMaterialEntity = pageResult.getRecords().get(0).getSaleOrderMaterial();
            saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(saleOrderMaterialEntity.getSaleOrderNumber()).build());
        }

        TmpWorkOrderScheduleEntity scheduleEntity = TmpWorkOrderScheduleEntity.builder()
                .orderId(productOrderMaterialEntity.getProductOrderId())
                .orderNumber(productOrderMaterialEntity.getProductOrderNumber())
                .scheduleStatusName(productOrderMaterialEntity.getSchedulingStatusName())
                .orderDate(productOrderMaterialEntity.getOrderDate())
                .gname(productOrderMaterialEntity.getGridName())
                .deliveryDeadline(productOrderMaterialEntity.getRequireGoodsDate())
                .plannedDeliveryDate(productOrderMaterialEntity.getPlanProductStartTime())
                .plannedShipDate(productOrderMaterialEntity.getDeliveryDate())
                .modelType(saleOrderEntity == null ? null : saleOrderEntity.getSaleOrderSource())
                .customerName(saleOrderEntity == null ? null : saleOrderEntity.getCustomerName())
                .materialCode(productOrderMaterialEntity.getMaterialCode())
                .needProduceQuantity(productOrderMaterialEntity.getNeedProduceQuantity())
                .saleOrderCode(productOrderMaterialEntity.getSaleOrderCode())
                .productCode(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getMaterialCode())
                .productReserveNum(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getSalesQuantity())
                .packingMethod(saleOrderMaterialEntity == null ? null : saleOrderMaterialEntity.getPackingMethod())
                .gridCode(productOrderMaterialEntity.getGridCode())
                .workOrderId(workOrderEntity.getWorkOrderId())
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .lineId(workOrderEntity.getLineId())
                .lineName(workOrderEntity.getLineName())
                .planQuantity(workOrderEntity.getPlanQuantity())
                .finishCount(workOrderEntity.getFinishCount())
                .startDate(workOrderEntity.getStartDate())
                .endDate(workOrderEntity.getEndDate())
                .materialIsComplete(workOrderEntity.getMaterialIsComplete())
                .materialOweNum(workOrderEntity.getMaterialOweNum())
                .state(workOrderEntity.getState())
                .stateName(WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()))
                .unqualified(workOrderEntity.getUnqualified())
                .progress(workOrderEntity.getProgress())
                .actualStartDate(workOrderEntity.getActualStartDate())
                .actualEndDate(workOrderEntity.getActualEndDate())
                .sortDate(productOrderMaterialEntity.getPlanProductEndTime() == null ?
                        productOrderMaterialEntity.getCreateTime() : productOrderMaterialEntity.getPlanProductEndTime())
                .build();
        scheduleEntities.add(scheduleEntity);
    }

}
