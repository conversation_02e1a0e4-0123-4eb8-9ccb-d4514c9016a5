package com.yelink.dfs.service.impl.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.ReportFormConstant;
import com.yelink.dfs.constant.code.ProductFlowCodeQualityStateEnum;
import com.yelink.dfs.constant.defect.DealEnum;
import com.yelink.dfs.constant.notice.NoticeStateEnum;
import com.yelink.dfs.entity.code.CodeReportEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.common.config.dto.DefectConfigDTO;
import com.yelink.dfs.entity.common.config.dto.QualityInspectionConfigDTO;
import com.yelink.dfs.entity.defect.DefectRecordPicEntity;
import com.yelink.dfs.entity.defect.DefectSchemeEntity;
import com.yelink.dfs.entity.defect.DefectSchemeFacilitiesEntity;
import com.yelink.dfs.entity.defect.dto.DefectRecordExcelDTO;
import com.yelink.dfs.entity.defect.dto.DefectRecordPadDTO;
import com.yelink.dfs.entity.defect.dto.DefectRecordQueryDTO;
import com.yelink.dfs.entity.defect.dto.FacDTO;
import com.yelink.dfs.entity.maintain.MaintainDefineEntity;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.maintain.dto.MaintainDefineDTO;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.notice.InfoNoticeConfigEntity;
import com.yelink.dfs.entity.notice.InfoNoticeDefectEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.ShowExtraNameDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderWrapperDTO;
import com.yelink.dfs.entity.order.vo.DefectCountVO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureDefectSchemeEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.code.ProductFlowCodeRecordMapper;
import com.yelink.dfs.mapper.order.RecordWorkOrderUnqualifiedMapper;
import com.yelink.dfs.mapper.order.WorkOrderProcedureRelationMapper;
import com.yelink.dfs.open.v2.code.dto.ProductFlowCodeRecordAddDTO;
import com.yelink.dfs.open.v2.code.vo.CodeRecordResultVO;
import com.yelink.dfs.open.v2.defect.dto.RecordWorkOrderUnqualifiedAddDTO;
import com.yelink.dfs.open.v2.defect.dto.RecordWorkOrderUnqualifiedBatchDeleteDTO;
import com.yelink.dfs.open.v2.defect.dto.RecordWorkOrderUnqualifiedQueryDTO;
import com.yelink.dfs.open.v2.defect.vo.RecordWorkOrderUnqualifiedVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.code.CodeReportService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.code.ScannerService;
import com.yelink.dfs.service.code.v2.ProductFlowCodeRecordV2Service;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.defect.BaseTypeService;
import com.yelink.dfs.service.defect.DefectDefineService;
import com.yelink.dfs.service.defect.DefectRecordPicService;
import com.yelink.dfs.service.defect.DefectSchemeFacilitiesService;
import com.yelink.dfs.service.defect.DefectSchemeService;
import com.yelink.dfs.service.maintain.MaintainDefineService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.notice.InfoNoticeConfigService;
import com.yelink.dfs.service.notice.InfoNoticeDefectService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureDefectSchemeService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.notice.NoticeTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.DefectDefineDTO;
import com.yelink.dfscommon.dto.dfs.DefectRecordDTO;
import com.yelink.dfscommon.dto.dfs.NoticeConfigBuilder;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.ProductFlowCodeResultEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.productFlowCode.AddFlowCodeBatchDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.AddFlowCodeDTO;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PathUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-06-08 16:20
 */
@Service
public class RecordWorkOrderUnqualifiedServiceImpl extends ServiceImpl<RecordWorkOrderUnqualifiedMapper, RecordWorkOrderUnqualifiedEntity> implements RecordWorkOrderUnqualifiedService {
    @Resource
    private DictService dictService;
    @Resource
    private SysUserService userService;
    @Resource
    private OrderWorkOrderService orderWorkOrderService;
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    private DefectRecordPicService picService;
    @Resource
    private DefectSchemeFacilitiesService schemeFacilitiesService;
    @Resource
    private RecordWorkOrderCountService recordWorkOrderCountService;
    @Resource
    private RecordWorkOrderUnqualifiedMapper recordWorkOrderUnqualifiedMapper;
    @Resource
    private UploadService uploadService;
    @Resource
    private BaseTypeService baseTypeService;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private MaterialService materialService;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    @Lazy
    private ProductFlowCodeRecordService productFlowCodeRecordService;
    @Resource
    private ProductFlowCodeRecordMapper productFlowCodeRecordMapper;
    @Resource
    private ProductFlowCodeService productFlowCodeService;
    @Resource
    @Lazy
    private ScannerService scannerService;
    @Resource
    private WorkCenterService workCenterService;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    private MaintainDefineService maintainDefineService;

    @Resource
    private ProcedureService procedureService;
    @Resource
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    private ProcedureDefectSchemeService procedureDefectSchemeService;
    @Resource
    private DefectSchemeService defectSchemeService;
    @Resource
    private InfoNoticeConfigService infoNoticeConfigService;
    @Resource
    private WorkOrderProcedureRelationMapper workOrderProcedureRelationMapper;
    @Resource
    private InfoNoticeDefectService infoNoticeDefectService;
    @Resource
    private MaintainRecordService  maintainRecordService;
    @Resource
    private DefectDefineService defectDefineService;
    @Resource
    private RedisTemplate redisTemplate;
    private ThreadPoolExecutor threadPool = (ThreadPoolExecutor) Executors.newFixedThreadPool(10);
    @Resource
    private CodeReportService codeReportService;

    @Override
    public Page<RecordWorkOrderUnqualifiedEntity> getList(DefectRecordQueryDTO queryDTO) {
        Integer current = queryDTO.getCurrent();
        Integer size = queryDTO.getSize();
        Page<RecordWorkOrderUnqualifiedEntity> page = queryDTO.limitPage(RecordWorkOrderUnqualifiedEntity.class);
        // 工厂型号、物料名称、物料规格
        List<String> materialCodes = new ArrayList<>();
        if (StringUtils.isNotBlank(queryDTO.getFactoryModel()) || StringUtils.isNotBlank(queryDTO.getMaterialName()) || StringUtils.isNotBlank(queryDTO.getMaterialStandard())) {
            List<MaterialEntity> materialEntities = materialService.lambdaQuery().select(MaterialEntity::getCode)
                    .like(StringUtils.isNotBlank(queryDTO.getFactoryModel()), MaterialEntity::getFactoryModel, queryDTO.getFactoryModel())
                    .like(StringUtils.isNotBlank(queryDTO.getMaterialName()), MaterialEntity::getName, queryDTO.getMaterialName())
                    .like(StringUtils.isNotBlank(queryDTO.getMaterialStandard()), MaterialEntity::getStandard, queryDTO.getMaterialStandard())
                    .list();
            if (CollectionUtils.isEmpty(materialEntities)) {
                return page;
            }
            materialCodes = materialEntities.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
        }
        LambdaQueryWrapper<RecordWorkOrderUnqualifiedEntity> wrapper = new LambdaQueryWrapper<>();

        // 2.16.2 补充
        // 操作员
        List<String> createBys = null;
        if (StringUtils.isNotBlank(queryDTO.getCreateByName())) {
            List<SysUserEntity> sysUsers = userService.lambdaQuery().like(StringUtils.isNotBlank(queryDTO.getCreateByName()), SysUserEntity::getNickname, queryDTO.getCreateByName()).list();
            if (CollectionUtils.isEmpty(sysUsers)) {
                return page;
            }
            createBys = sysUsers.stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        // 生产订单、销售订单
        List<String> workOrderNumbers = null;
        if (StringUtils.isNotBlank(queryDTO.getProductOrderNumber()) || StringUtils.isNotBlank(queryDTO.getSaleOrderNumber())
        || StringUtils.isNotBlank(queryDTO.getWorkCenterId()) || StringUtils.isNotBlank(queryDTO.getProcedureIds())
        || !CollectionUtils.isEmpty(queryDTO.getWorkOrderNumbers())
        || StringUtils.isNotBlank(queryDTO.getLineIds())
        || StringUtils.isNotBlank(queryDTO.getDeviceIds())
        || StringUtils.isNotBlank(queryDTO.getTeamIds())
        ) {
            LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = Wrappers.lambdaQuery(WorkOrderEntity.class)
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .in(!CollectionUtils.isEmpty(queryDTO.getWorkOrderNumbers()), WorkOrderEntity::getWorkOrderNumber, queryDTO.getWorkOrderNumbers())
                    .like(StringUtils.isNotBlank(queryDTO.getProductOrderNumber()), WorkOrderEntity::getProductOrderNumber, queryDTO.getProductOrderNumber())
                    .like(StringUtils.isNotBlank(queryDTO.getSaleOrderNumber()), WorkOrderEntity::getSaleOrderNumber, queryDTO.getSaleOrderNumber());
            workOrderService.buildWrapper(workOrderWrapper, WorkOrderWrapperDTO.builder()
                    .workCenterIds(queryDTO.getWorkCenterId())
                    .procedureIds(queryDTO.getProcedureIds())
                    .lineIds(queryDTO.getLineIds())
                    .deviceIds(queryDTO.getDeviceIds())
                    .teamIds(queryDTO.getTeamIds())
                    .build()
            );
            List<WorkOrderEntity> workOrderEntities = workOrderService.list(workOrderWrapper);
            if (CollectionUtils.isEmpty(workOrderEntities)) {
                return page;
            }
            workOrderNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        }

        // 模糊搜索
        wrapper.like(StringUtils.isNotBlank(queryDTO.getSequenceId()), RecordWorkOrderUnqualifiedEntity::getSequenceId, queryDTO.getSequenceId());
        wrapper.eq(StringUtils.isNotBlank(queryDTO.getFullSequenceId()), RecordWorkOrderUnqualifiedEntity::getSequenceId, queryDTO.getFullSequenceId());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getSerialNumber()), RecordWorkOrderUnqualifiedEntity::getSerialNumber, queryDTO.getSerialNumber());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getAbnormalName()), RecordWorkOrderUnqualifiedEntity::getAbnormalName, queryDTO.getAbnormalName());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getRemark()), RecordWorkOrderUnqualifiedEntity::getRemark, queryDTO.getRemark());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getWorkOrderName()), RecordWorkOrderUnqualifiedEntity::getWorkOrderName, queryDTO.getWorkOrderName());
        wrapper.in(!CollectionUtils.isEmpty(createBys), RecordWorkOrderUnqualifiedEntity::getCreateBy, createBys);
        wrapper.in(!CollectionUtils.isEmpty(workOrderNumbers), RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, workOrderNumbers);

        wrapper.like(StringUtils.isNotBlank(queryDTO.getWorkOrderNumber()), RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, queryDTO.getWorkOrderNumber());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getWorkOrder()), RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, queryDTO.getWorkOrder());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getMaterialCode()), RecordWorkOrderUnqualifiedEntity::getMaterialCode, queryDTO.getMaterialCode());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getWorkCenterName()), RecordWorkOrderUnqualifiedEntity::getWorkCenterName, queryDTO.getWorkCenterName());
        // 关联资源
        wrapper.like(StringUtils.isNotBlank(queryDTO.getRelevanceResourceName()), RecordWorkOrderUnqualifiedEntity::getRelevanceResourceName, queryDTO.getRelevanceResourceName());
        wrapper.in(StringUtils.isNotBlank(queryDTO.getRelevanceResourceNames()), RecordWorkOrderUnqualifiedEntity::getRelevanceResourceName, StringUtils.isNotBlank(queryDTO.getRelevanceResourceNames()) ? queryDTO.getRelevanceResourceNames().split(Constants.SEP) : null);
        wrapper.in(!CollectionUtils.isEmpty(materialCodes), RecordWorkOrderUnqualifiedEntity::getMaterialCode, materialCodes);
        wrapper.in(!CollectionUtils.isEmpty(queryDTO.getFid()), RecordWorkOrderUnqualifiedEntity::getFid, queryDTO.getFid());
        wrapper.in(!CollectionUtils.isEmpty(queryDTO.getDefectType()), RecordWorkOrderUnqualifiedEntity::getDefectType, queryDTO.getDefectType());
        wrapper.in(!CollectionUtils.isEmpty(queryDTO.getState()), RecordWorkOrderUnqualifiedEntity::getState, queryDTO.getState());
        wrapper.between(StringUtils.isNoneBlank(queryDTO.getStart(), queryDTO.getEnd()), RecordWorkOrderUnqualifiedEntity::getCreateDate, queryDTO.getStart(), queryDTO.getEnd());
        // 扩展字段
        wrapper.like(StringUtils.isNotBlank(queryDTO.getRecordWorkOrderUnqualifiedExtendOne()), RecordWorkOrderUnqualifiedEntity::getRecordWorkOrderUnqualifiedExtendOne, queryDTO.getRecordWorkOrderUnqualifiedExtendOne());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getRecordWorkOrderUnqualifiedExtendTwo()), RecordWorkOrderUnqualifiedEntity::getRecordWorkOrderUnqualifiedExtendTwo, queryDTO.getRecordWorkOrderUnqualifiedExtendTwo());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getRecordWorkOrderUnqualifiedExtendThree()), RecordWorkOrderUnqualifiedEntity::getRecordWorkOrderUnqualifiedExtendThree, queryDTO.getRecordWorkOrderUnqualifiedExtendThree());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getRecordWorkOrderUnqualifiedExtendFour()), RecordWorkOrderUnqualifiedEntity::getRecordWorkOrderUnqualifiedExtendFour, queryDTO.getRecordWorkOrderUnqualifiedExtendFour());
        //是否过滤合格信息
        if(queryDTO.getIsFilterQualified()!=null&&queryDTO.getIsFilterQualified()){
            wrapper.ne(RecordWorkOrderUnqualifiedEntity::getAbnormalName,Constant.QUALIFIED_CODE);
        }
        wrapper.orderByDesc(RecordWorkOrderUnqualifiedEntity::getCreateDate)
                .orderByDesc(RecordWorkOrderUnqualifiedEntity::getId);
        wrapper.orderByDesc(RecordWorkOrderUnqualifiedEntity::getCreateDate);
        if (current == null || size == null) {
            List<RecordWorkOrderUnqualifiedEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            this.page(page, wrapper);
        }
        List<RecordWorkOrderUnqualifiedEntity> records = page.getRecords();
        setNameByList(records);
        // 2.21 添加维修类型
        setMaintainByList(records);
        // 设置状态名称
        setMaintainStateNameByList(records);
        return page;
    }

    @Override
    public Page<RecordWorkOrderUnqualifiedEntity> statementRecordList(DefectRecordQueryDTO queryDTO) {
        Page<RecordWorkOrderUnqualifiedEntity> page = this.getList(queryDTO);
        // 设置客户编号、名称
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<Integer> workOrderIds = page.getRecords().stream().map(RecordWorkOrderUnqualifiedEntity::getWorkOrderId).collect(Collectors.toList());
            Map<Integer, SaleOrderVO> map = orderWorkOrderService.listOrderByWorkIdList(workOrderIds);
            for (RecordWorkOrderUnqualifiedEntity entity : page.getRecords()) {
                SaleOrderVO orderEntity = map.getOrDefault(entity.getWorkOrderId(), new SaleOrderVO());
                entity.setCustomer(orderEntity.getCustomerName());
                entity.setCustomerCode(orderEntity.getCustomerCode());
            }
        }
        return page;
    }

    @Override
    public void updateRecord(String productCode, List<MaintainDefineDTO> maintainDefineDTOList) {
        if (StringUtils.isEmpty(productCode) || CollectionUtils.isEmpty(maintainDefineDTOList)) {
            return;
        }
        maintainDefineDTOList = maintainDefineDTOList.stream().filter(item -> (!ObjectUtils.isEmpty(item.getDefectId()) && item.getDefectId()>0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(maintainDefineDTOList)) {
            return;
        }
        Map<Integer,List<Integer>> maintainDefineDTOMap = maintainDefineDTOList.stream().collect(Collectors.groupingBy(MaintainDefineDTO::getDefectId,Collectors.mapping(MaintainDefineDTO::getMaintainId,Collectors.toList())));
        Iterator<Map.Entry<Integer,List<Integer>>> iterator = maintainDefineDTOMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer,List<Integer>> entry = iterator.next();
            List<Integer> maintainIds = entry.getValue();
            if (CollectionUtils.isEmpty(maintainIds)) {
                continue;
            }
            StringBuilder maintainSB = new StringBuilder();
            maintainIds.stream().forEach(item->maintainSB.append(item).append(","));
            LambdaUpdateWrapper<RecordWorkOrderUnqualifiedEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(true, RecordWorkOrderUnqualifiedEntity::getMaintainIds,maintainSB.subSequence(0,maintainSB.length()-1));
            wrapper.set(true, RecordWorkOrderUnqualifiedEntity::getState, DealEnum.DEAL.getCode());
            wrapper.eq(RecordWorkOrderUnqualifiedEntity::getDefectId, entry.getKey());
            wrapper.eq(RecordWorkOrderUnqualifiedEntity::getSequenceId, productCode);
            this.baseMapper.update(null,wrapper);
        }
    }

    @Override
    public void setMaintainStateNameByList(List<RecordWorkOrderUnqualifiedEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (RecordWorkOrderUnqualifiedEntity recordItem : list) {
            recordItem.setStateName(DealEnum.getNameByCode(recordItem.getState()));
        }
    }

    @Override
    public void setMaintainByList(List<RecordWorkOrderUnqualifiedEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, MaintainDefineEntity> maintainDefineEntityMap = new HashMap<>();
        for (RecordWorkOrderUnqualifiedEntity recordItem : list) {
            if (StringUtils.isEmpty(recordItem.getMaintainIds())) {
                continue;
            }
            String[] maintainIds = recordItem.getMaintainIds().split(",");
            StringBuilder maintainCodesSB = new StringBuilder();
            StringBuilder maintainNamesSB = new StringBuilder();
            for(String maintainId : maintainIds) {
               if (!maintainDefineEntityMap.containsKey(maintainId)) {
                   MaintainDefineEntity maintainDefineEntity =  maintainDefineService.getDetailById(Integer.valueOf(maintainId));
                   if (maintainDefineEntity == null) {
                       continue;
                   }
                   maintainDefineEntityMap.put(maintainId, maintainDefineEntity);
               }
               maintainCodesSB.append(maintainDefineEntityMap.get(maintainId).getMaintainCode()).append(",");
               maintainNamesSB.append(maintainDefineEntityMap.get(maintainId).getMaintainName()).append(",");
            }
            recordItem.setMaintainCodes(maintainCodesSB.length()>0 ? maintainCodesSB.substring(0, maintainCodesSB.length()-1):"");
            recordItem.setMaintainNames(maintainNamesSB.length()>0 ? maintainNamesSB.substring(0, maintainNamesSB.length()-1):"");
        }
    }

    @Override
    public void setNameByList(List<RecordWorkOrderUnqualifiedEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        String[] userList = list.stream().map(RecordWorkOrderUnqualifiedEntity::getCreateBy)
                .filter(Objects::nonNull).distinct().toArray(String[]::new);
        List<SysUserEntity> userEntities = userService.selectByUsernames(userList);
        //用户名要先转小写，因为到keyclock获取的用户名是小写，而用户操作过程中使用的很可能有大写
        Map<String, String> map = userEntities.stream()
                .peek(u -> u.setUsername(u.getUsername().toLowerCase()))
                .collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .in(WorkOrderEntity::getWorkOrderNumber, list.stream().map(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber).collect(Collectors.toSet()))
                .list();
        workOrderService.showWorkOrdersExtraName(workOrderEntities, ShowExtraNameDTO.builder().procedure(true).team(true).device(true).build());
        Map<String, WorkOrderEntity> workOrderNumberEntityMap = workOrderEntities.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, v -> v));
        for (RecordWorkOrderUnqualifiedEntity entity : list) {
            String materialCode = entity.getMaterialCode();
            // 关联生产订单、关联销售订单、工单名称、物料编码、物料名称、物料规格、工厂型号
            WorkOrderEntity workOrderEntity = workOrderNumberEntityMap.get(entity.getWorkOrderNumber());
            MaterialEntity materialEntity = materialService.lambdaQuery()
                    .select(MaterialEntity::getName, MaterialEntity::getStandard, MaterialEntity::getFactoryModel)
                    .eq(MaterialEntity::getCode, materialCode)
                    .one();
            if (workOrderEntity != null) {
                entity.setWorkOrderId(workOrderEntity.getWorkOrderId());
                entity.setProductOrderNumber(workOrderEntity.getProductOrderNumber());
                entity.setSaleOrderNumber(workOrderEntity.getSaleOrderNumber());
                entity.setWorkCenterId(workOrderEntity.getWorkCenterId());
                entity.setProcedureName(workOrderEntity.getProcedureName());
                entity.setProductionBaseUnitName(workOrderEntity.getProductBasicUnitsNameStr());
            }
            if (materialEntity != null) {
                entity.setMaterialName(materialEntity.getName());
                entity.setMaterialStandard(materialEntity.getStandard());
                entity.setFactoryModel(materialEntity.getFactoryModel());
            }
            //创建人
            String createBy = entity.getCreateBy();
            entity.setCreateByName(map.get(createBy));
            //不良类型
            entity.setDefectTypeName(baseTypeService.getDefectTypeNameByCode(entity.getDefectType()));
            //图片地址
            String barCode = entity.getSequenceId();
            List<String> urls = getPicUrlsByBarCode(barCode, entity.getDefectType(), entity.getAbnormalName());
            entity.setPicUrls(urls);
        }
    }

    /**
     * 通过二维码获取图片地址
     *
     * @param barCode
     * @return
     */
    private List<String> getPicUrlsByBarCode(String barCode, String defectType, String defectName) {
        List<String> list = new ArrayList<>();
        LambdaQueryWrapper<DefectRecordPicEntity> picWrapper = new LambdaQueryWrapper<>();
        picWrapper.eq(DefectRecordPicEntity::getBarCode, barCode);
        picWrapper.eq(DefectRecordPicEntity::getDefectType, defectType);
        picWrapper.eq(DefectRecordPicEntity::getDefectName, defectName);
        List<DefectRecordPicEntity> picEntityList = picService.list(picWrapper);
        if (!CollectionUtils.isEmpty(picEntityList)) {
            list = picEntityList.stream().map(DefectRecordPicEntity::getUrl).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public RecordWorkOrderUnqualifiedEntity getDetail(Integer id) {
        RecordWorkOrderUnqualifiedEntity entity = this.getById(id);
        if (entity != null) {
            setNameByEntity(entity);
        }
        return entity;
    }

    private void setNameByEntity(RecordWorkOrderUnqualifiedEntity entity) {
        entity.setDefectTypeName(baseTypeService.getDefectTypeNameByCode(entity.getDefectType()));
        String createBy = entity.getCreateBy();
        String nickname = userService.getNicknameByUsername(createBy);
        entity.setCreateByName(nickname);
    }

    @Override
    public ArrayList<FacDTO> getFacilities() {
        List<DefectSchemeFacilitiesEntity> list = schemeFacilitiesService.list();
        ArrayList<FacDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return dtos;
        }
        List<Integer> collect = list.stream().map(DefectSchemeFacilitiesEntity::getFid).distinct().collect(Collectors.toList());
        List<FacilitiesEntity> facilitiesEntities = facilitiesService.listByIds(collect);
        dtos = facilitiesEntities.stream().map(facilitiesEntity -> FacDTO.builder().fid(facilitiesEntity.getFid()).fname(facilitiesEntity.getFname()).build()).collect(Collectors.toCollection(ArrayList::new));
        return dtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRecord(DefectRecordDTO dto, String username) {
        List<DefectDefineDTO> defines = dto.getDefines();
        if (CollectionUtils.isEmpty(defines)) {
            return;
        }
        String barCode = dto.getBarCode();
        Integer quantity = dto.getQuantity();
        String workOrder = dto.getWorkOrder();
        if (StringUtils.isNotBlank(barCode)) {
            // 2.13.2 允许填写上报数量 生成多条不良记录
            if (quantity > 1) {
                throw new ResponseException(RespCodeEnum.BARCODE_CAN_ONLY_BE_ONE_RECORD);
            }
            //王祥产品经理加了一个通过条码直接跳转不良报工页面的功能,但报工页面功能没有改动
            //此判断是为了防止(产品挖坑)后续条码继续使用前一条码不良报工页面的工单报工,导致工单对不上
            //如果条码未绑定工单，则不受影响 --2022年2月9日
//            String workOrderByBarcode = workOrderBarcodeService.getWorkOrderByBarcode(barCode);
//            if (StringUtils.isNotBlank(workOrderByBarcode) && !workOrderByBarcode.equals(workOrder)) {
//                throw new ResponseException(RespCodeEnum.MISMATCH_BETWEEN_WORK_ORDER_AND_BARCODE);
//            }
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        Integer fid = dto.getFid();
        FacilitiesEntity entity = facilitiesService.getById(fid);
        // 工序质检的时候，没有工位
        String fname = Optional.ofNullable(entity).map(FacilitiesEntity::getFname).orElse(null);
        Date date = Optional.ofNullable(dto.getFakerTime()).orElse(new Date());
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrder);

        if (workOrderEntity == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_NOT_FOUND);
        }
        // 校验投产状态
        checkWorkOrderState(workOrderEntity);
        WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getWorkCenterId());
        String relevanceResourceName = null;
        String workCenterName = null;
        if (workCenterEntity != null) {
            workCenterName = workCenterEntity.getName();
            String relevanceType = workCenterEntity.getRelevanceType();
            // 获取关联资源、生产基本单元名称
            relevanceResourceName = getRelevanceTypeName(workOrderEntity, relevanceType);
        }
        Date recordDate = dictService.getRecordDate(date);
        long millis = System.currentTimeMillis();
        ArrayList<RecordWorkOrderUnqualifiedEntity> list = new ArrayList<>();
        // 合格项不刷新工单不良数
        boolean isOk = checkIsOk(dto);
        boolean checkQualified = isCheckQualified(dto);
        //质检工位机调用
        if (dto.getCodeRecordId() != null) {
            if (!isOk) {
                ProductFlowCodeRecordEntity productFlowCodeRecordEntity = ProductFlowCodeRecordEntity.builder().id(dto.getCodeRecordId())
                        .isQuality(true).isUnqualified(true).firstUnqualified(true).isReport(false).build();
                if (dto.getCodeRecordState() != null) {
                    productFlowCodeRecordEntity.setState(dto.getCodeRecordState());
                }
                productFlowCodeRecordMapper.updateById(productFlowCodeRecordEntity);
                //刷掉产出数
                productFlowCodeRecordService.refreshCodeUnqualifiedAndReport(productFlowCodeRecordService.getById(productFlowCodeRecordEntity.getId()));
                //刷掉条码检查状态
                productFlowCodeService.lambdaUpdate().eq(ProductFlowCodeEntity::getProductFlowCode, barCode)
                        .set(ProductFlowCodeEntity::getQualityState, ProductFlowCodeQualityStateEnum.FALSE.getCode())
                        .set(ProductFlowCodeEntity::getMaintainState, null).update();

            } else {
                ProductFlowCodeRecordEntity productFlowCodeRecordEntity = ProductFlowCodeRecordEntity.builder().id(dto.getCodeRecordId())
                        .isQuality(true).build();
                productFlowCodeRecordMapper.updateById(productFlowCodeRecordEntity);
                productFlowCodeService.lambdaUpdate().eq(ProductFlowCodeEntity::getProductFlowCode, barCode)
                        .set(ProductFlowCodeEntity::getQualityState, ProductFlowCodeQualityStateEnum.TRUE.getCode())
                        .set(ProductFlowCodeEntity::getMaintainState, null).update();
                //刷掉不合格数
                productFlowCodeRecordService.cancelCodeUnqualifiedAndReport(productFlowCodeRecordService.getById(dto.getCodeRecordId()));
            }
            redisTemplate.delete(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + barCode);
            for (DefectDefineDTO define : defines) {
                list.add(RecordWorkOrderUnqualifiedEntity.builder()
                        .sequenceId(barCode)
                        .serialNumber(barCode)
                        .workOrderNumber(workOrder)
                        .workOrderName(workOrderEntity.getWorkOrderName())
                        .materialCode(workOrderEntity.getMaterialCode())
                        .fid(fid)
                        .fname(fname)
                        .remark(define.getRemark())
                        .schemeId(define.getSchemeId())
                        .defectId(define.getDefectId())
                        .defectType(define.getDefectType())
                        .abnormalName(define.getDefectName())
                        .createBy(username)
                        .recordDate(recordDate)
                        .createDate(date)
                        .codeRecordId(dto.getCodeRecordId())
                        .workCenterName(workCenterName)
                        .remark(dto.getRemark())
                        .relevanceResourceName(relevanceResourceName)
                        .recordWorkOrderUnqualifiedExtendOne(dto.getRecordWorkOrderUnqualifiedExtendOne())
                        .recordWorkOrderUnqualifiedExtendTwo(dto.getRecordWorkOrderUnqualifiedExtendTwo())
                        .recordWorkOrderUnqualifiedExtendThree(dto.getRecordWorkOrderUnqualifiedExtendThree())
                        .recordWorkOrderUnqualifiedExtendFour(dto.getRecordWorkOrderUnqualifiedExtendFour())
                        .build());
                //每个不良类型下不良原因对应的图片
                if (!CollectionUtils.isEmpty(define.getPicUrls())) {
                    ArrayList<DefectRecordPicEntity> picEntities = new ArrayList<>(define.getPicUrls().size());
                    for (String picUrl : define.getPicUrls()) {
                        picEntities.add(DefectRecordPicEntity.builder().serialNumber(barCode).barCode(barCode).url(picUrl)
                                .defectType(define.getDefectType()).defectName(define.getDefectName()).build());
                    }
                    picService.saveBatch(picEntities);
                    for (DefectRecordPicEntity picEntity : picEntities) {
                        //标记上传文件
                        uploadService.markUploadFile(picEntity.getUrl(), picEntity);
                    }
                }
            }
        } else {
            if(StringUtils.isNotBlank(barCode)) {
                if (dto.getCodeRecordId() == null) {
                    AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
                            .productFlowCode(barCode)
                            .fid(fid)
                            .userName(username)
                            .craftProcedureId(null)
                            .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                            .isEndTime(true)
                            .autoAddCode(true)
                            .needFid(fid != null)
                            .isInput(false)
                            .isReport(false)
                            .workOrderNumber(dto.getWorkOrder())
                            .isUnqualified(!isOk).build();
                    //开启合格检测且提交合格数据算产出
                    if (checkQualified && Constant.QUALIFIED_TYPE_NAME.equals(dto.getDefines().get(0).getDefectName())) {
                        addFlowCodeDTO.setIsReport(true);
                    }
                    //如果设置了不合格过站记录状态
                    if (dto.getCodeRecordState() != null && !isOk) {
                        addFlowCodeDTO.setState(dto.getCodeRecordState());
                    }
                    ProductFlowCodeResultEntity productFlowCodeResultEntity = scannerService.addCodeRecordQuality(addFlowCodeDTO);
                    dto.setCodeRecordId(productFlowCodeResultEntity.getCodeRecordId());
                }
            }
            //工位质检调用
            List<String> codeList=new ArrayList<>();
            for (int i = 0; i < quantity; i++) {
                String productFlowCode = barCode;
                // 如果不输入产品标签，则默认采用质检号的标签规则
                if (StringUtils.isBlank(barCode)) {
                    productFlowCode = numberRuleService.getNumberByRuleType(13);
                }
                String serialNumber = "D" + millis + i;
                String sequenceId = StringUtils.isBlank(productFlowCode) ? serialNumber : productFlowCode;
                //添加扫码记录
                codeList.add(sequenceId);
                for (DefectDefineDTO define : defines) {
                    list.add(RecordWorkOrderUnqualifiedEntity.builder()
                            .sequenceId(sequenceId)
                            .serialNumber(serialNumber)
                            .workOrderNumber(workOrder)
                            .workOrderName(workOrderEntity.getWorkOrderName())
                            .materialCode(workOrderEntity.getMaterialCode())
                            .fid(fid)
                            .fname(fname)
                            .remark(define.getRemark())
                            .schemeId(define.getSchemeId())
                            .defectId(define.getDefectId())
                            .defectType(define.getDefectType())
                            .abnormalName(define.getDefectName())
                            .createBy(username)
                            .recordDate(recordDate)
                            .createDate(date)
                            .codeRecordId(dto.getCodeRecordId())
                            .workCenterName(workCenterName)
                            .relevanceResourceName(relevanceResourceName)
                            .remark(dto.getRemark())
                            .recordWorkOrderUnqualifiedExtendOne(dto.getRecordWorkOrderUnqualifiedExtendOne())
                            .recordWorkOrderUnqualifiedExtendTwo(dto.getRecordWorkOrderUnqualifiedExtendTwo())
                            .recordWorkOrderUnqualifiedExtendThree(dto.getRecordWorkOrderUnqualifiedExtendThree())
                            .recordWorkOrderUnqualifiedExtendFour(dto.getRecordWorkOrderUnqualifiedExtendFour())
                            .build());
                    //每个不良类型下不良原因对应的图片
                    if (!CollectionUtils.isEmpty(define.getPicUrls())) {
                        ArrayList<DefectRecordPicEntity> picEntities = new ArrayList<>(define.getPicUrls().size());
                        for (String picUrl : define.getPicUrls()) {
                            picEntities.add(DefectRecordPicEntity.builder().serialNumber(serialNumber).barCode(sequenceId).url(picUrl)
                                    .defectType(define.getDefectType()).defectName(define.getDefectName()).build());
                        }
                        picService.saveBatch(picEntities);
                        for (DefectRecordPicEntity picEntity : picEntities) {
                            //标记上传文件
                            uploadService.markUploadFile(picEntity.getUrl(), picEntity);
                        }
                    }
                }
                dto.setCodeRecordId(null);

            }
            if (dto.getCodeRecordId() == null&&StringUtils.isBlank(barCode)) {
                //获取工序质检算产出数据
                AddFlowCodeBatchDTO addFlowCodeBatchDTO = AddFlowCodeBatchDTO.builder()
                        .productFlowCodeList(codeList)
                        .fid(fid)
                        .userName(username)
                        .craftProcedureId(null)
                        .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
                        .isEndTime(true)
                        .autoAddCode(true)
                        .needFid(fid != null)
                        .isInput(false)
                        .isReport(false)
                        .workOrderNumber(dto.getWorkOrder())
                        .isUnqualified(!isOk).build();
                //开启合格检测且提交合格数据算产出
                if (checkQualified && Constant.QUALIFIED_TYPE_NAME.equals(dto.getDefines().get(0).getDefectName())) {
                    addFlowCodeBatchDTO.setIsReport(true);
                }
                //如果设置了不合格过站记录状态
                if (dto.getCodeRecordState() != null && !isOk) {
                    addFlowCodeBatchDTO.setState(dto.getCodeRecordState());
                }
                scannerService.addCodeRecordQualityBatch(addFlowCodeBatchDTO);
            }
        }


        this.saveBatch(list);
        //推送kafka消息
        messagePushToKafkaService.pushNewMessage(list, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_UNQUALIFIED_ADD_LIST_MESSAGE);
        //查询RecordWorkOrderCountEntity
        RecordWorkOrderCountEntity countEntity = recordWorkOrderCountService.getByWorkOrderNumber(workOrder, fid, recordDate);
        Integer defectiveQuantity = recordWorkOrderUnqualifiedMapper.getUnqualifiedByDate(workOrder, fid, recordDate);
        countEntity.setDefectiveQuantity(defectiveQuantity);
        Integer checkedQuantity = countEntity.getCheckedQuantity();
        if (checkedQuantity != null && checkedQuantity != 0 && checkedQuantity >= defectiveQuantity) {
            Integer qualifiedNumber = checkedQuantity - defectiveQuantity;
            countEntity.setQualifiedNumber(qualifiedNumber);
            countEntity.setFinalQualifiedNumber(qualifiedNumber + countEntity.getRepairQualifiedNumber());
            //如果已检查数为0，则不计算直通率
            double passRate = MathUtil.divideDouble(qualifiedNumber, checkedQuantity, 4) * 100;
            countEntity.setPassRate(passRate);
            countEntity.setFinalQualifiedRate(countEntity.getFinalQualifiedNumber() / checkedQuantity.doubleValue());
        }
        recordWorkOrderCountService.updateById(countEntity);
        //处理其他数据表
//        recordWorkOrderCountService.dealWithFacTables(fid, workOrder);
        //已检查数依旧是从计数器中获取的当天0点至现在的计数，以下方法放心食用
//        recordWorkOrderCountService.dealWithLineTables(countEntity);

        // 不良数据推送
        defectNotice(workOrderEntity);

    }


    /**
     * 删除质检记录（不会自动冲正不良数）
     *
     * @param recordId
     */
    @Override
    public void deleteRecord(Integer recordId) {
        RecordWorkOrderUnqualifiedEntity recordWorkOrderUnqualifiedEntity = this.getById(recordId);
        if (recordWorkOrderUnqualifiedEntity == null) {
            throw new ResponseException("质检记录不存在");
        }
        if (DealEnum.DEAL.getCode().equals(recordWorkOrderUnqualifiedEntity.getState())) {
            throw new ResponseException("已处理数据不能删除");
        }
        this.removeById(recordId);
        //如果条码的所有未处理不良都被删除，取消不良
        Long count = this.lambdaQuery().eq(RecordWorkOrderUnqualifiedEntity::getSequenceId, recordWorkOrderUnqualifiedEntity.getSequenceId())
                .eq(RecordWorkOrderUnqualifiedEntity::getState, DealEnum.UN_DEAL.getCode()).count();

        if (count <= 0) {
            ProductFlowCodeEntity productFlowCodeEntity = productFlowCodeService.getByProductFlowCode(recordWorkOrderUnqualifiedEntity.getSequenceId());
            if (productFlowCodeEntity == null) {
                return;
            }
            productFlowCodeEntity.setQualityState(ProductFlowCodeQualityStateEnum.TRUE.getCode());
            productFlowCodeService.updateById(productFlowCodeEntity);
            //如果不存在处理的记录，去掉初次不良的标识
            Long dealCount = this.lambdaQuery().eq(RecordWorkOrderUnqualifiedEntity::getSequenceId, recordWorkOrderUnqualifiedEntity.getSequenceId())
                    .eq(RecordWorkOrderUnqualifiedEntity::getState, DealEnum.DEAL.getCode()).count();
            if (dealCount <= 0) {
                CodeReportEntity codeReportEntity = codeReportService.lambdaQuery()
                        .eq(CodeReportEntity::getProductFlowCode, recordWorkOrderUnqualifiedEntity.getSequenceId()).last("limit 1").one();
                if(codeReportEntity!=null) {
                    codeReportEntity.setIsFirstUnqualified(false);
                    codeReportService.updateById(codeReportEntity);
                }
            }
            ProductFlowCodeRecordEntity productFlowCodeRecordEntity = ProductFlowCodeRecordEntity.builder()
                    .relationNumber(recordWorkOrderUnqualifiedEntity.getWorkOrderNumber())
                    .productFlowCode(recordWorkOrderUnqualifiedEntity.getSequenceId())
                    .version(productFlowCodeEntity.getVersion()).build();
            productFlowCodeRecordService.cancelCodeUnqualifiedAndReport(productFlowCodeRecordEntity);
        }
    }

    @Override
    public void defectNotice(WorkOrderEntity workOrderEntity) {
        if (ObjectUtils.isEmpty(workOrderEntity)) {
            return;
        }

        // 不良数据推送
        threadPool.execute(() -> {
            try
            {
                innerPushDefectNotice(workOrderEntity);
            }
            catch (Exception ex) {
                log.error("不良数据推送失败",ex);
            }
        });
    }

    protected void innerPushDefectNotice(WorkOrderEntity workOrderEntity)
    {
        List<String> noticeTypeList = new ArrayList<>();
        noticeTypeList.add(NoticeTypeEnum.DEFECT_NUM_THRESHOLD_NOTICE.getCode());
        noticeTypeList.add(NoticeTypeEnum.DEFECT_RATE_THRESHOLD_NOTICE.getCode());
        noticeTypeList.add(NoticeTypeEnum.CONCRETE_DEFECT_NUM_THRESHOLD_NOTICE.getCode());
        List<InfoNoticeConfigEntity> noticeConfigList = infoNoticeConfigService.getListByNoticeType(noticeTypeList);
        if (noticeConfigList.isEmpty()) {
            return;
        }
        // 若配置无启用，则不进行数据推送
        if(!judgeDefectNotice(noticeConfigList)) {
            return;
        }

        // 工单完成数量
        Double workOrderFinishNum = workOrderEntity.getFinishCount();
        // 不良数量
        Double unqualifiedNum = workOrderEntity.getUnqualified();
        // 不良率
        Double defectRate = MathUtil.divideDouble(unqualifiedNum,workOrderFinishNum,4);

        // 工单工序
        LambdaQueryWrapper<WorkOrderProcedureRelationEntity> procedureRelationWrapper  = new LambdaQueryWrapper<>();
        procedureRelationWrapper.eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber());
        List<WorkOrderProcedureRelationEntity> procedureRelationEntityList = workOrderProcedureRelationMapper.selectList(procedureRelationWrapper);
        String procedureNames = "";
        if (!procedureRelationEntityList.isEmpty()) {
            procedureNames = procedureRelationEntityList.stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureName).collect(Collectors.joining(com.yelink.dfscommon.constant.Constant.SEP));
        }

        // 消息通知
        for(InfoNoticeConfigEntity noticeConfig : noticeConfigList) {
            if (NoticeStateEnum.DISABLE.getCode().equals(noticeConfig.getState())) {
                continue;
            }
            NoticeTypeEnum noticeTypeEnum = NoticeTypeEnum.getByCode(noticeConfig.getNoticeType());
            switch (noticeTypeEnum) {
                case DEFECT_NUM_THRESHOLD_NOTICE:
                    defectNumNotice(noticeConfig, workOrderEntity, procedureNames, unqualifiedNum);
                    break;
                case DEFECT_RATE_THRESHOLD_NOTICE:
                    defectRateNotice(noticeConfig, workOrderEntity, procedureNames, defectRate);
                    break;
                case CONCRETE_DEFECT_NUM_THRESHOLD_NOTICE:
                    concreteDefectNumNotice(noticeConfig, workOrderEntity, procedureNames);
                    break;
                default:break;
            }
        }
    }


    /**
     * 不良数量阈值报警
     * @param noticeConfig
     * @param workOrderEntity
     * @param procedureNames
     * @param defectNum
     */
    private void defectNumNotice(InfoNoticeConfigEntity noticeConfig, WorkOrderEntity workOrderEntity, String procedureNames, Double defectNum) {
      // 若消息配置的不良数量小于等于0.0，或者实际不良数量小于配置的不良数量，将不进行消息的推送
      if (noticeConfig.getDefectNum() <= 0.0 || defectNum < noticeConfig.getDefectNum()) {
          return;
      }
      NoticeConfigBuilder noticeConfigBuilder = new NoticeConfigBuilder();
      noticeConfigBuilder.setNoticeType(NoticeTypeEnum.DEFECT_NUM_THRESHOLD_NOTICE.getCode());
      noticeConfigBuilder.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
      noticeConfigBuilder.setDefectNumThreshold(noticeConfig.getDefectNum());
      noticeConfigBuilder.setProcedureName(procedureNames);

      infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
    }

    /**
     * 不良率阈值报警
     * @param noticeConfig
     * @param workOrderEntity
     * @param procedureNames
     * @param defectRate
     */
    private void defectRateNotice(InfoNoticeConfigEntity noticeConfig, WorkOrderEntity workOrderEntity, String procedureNames, Double defectRate) {
        // 若消息配置的不良数量小于等于0.0，或者实际不良数量小于配置的不良数量，将不进行消息的推送
        if (noticeConfig.getDefectRate() <= 0.0 || defectRate*100 < noticeConfig.getDefectRate()) {
            return;
        }
        NoticeConfigBuilder noticeConfigBuilder = new NoticeConfigBuilder();
        noticeConfigBuilder.setNoticeType(NoticeTypeEnum.DEFECT_RATE_THRESHOLD_NOTICE.getCode());
        noticeConfigBuilder.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
        noticeConfigBuilder.setDefectRateThreshold(noticeConfig.getDefectRate());
        noticeConfigBuilder.setProcedureName(procedureNames);

        infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
    }

    /**
     * 具体不良数阈值报警
     * @param noticeConfig
     * @param workOrderEntity
     * @param procedureNames
     */
    private void concreteDefectNumNotice(InfoNoticeConfigEntity noticeConfig, WorkOrderEntity workOrderEntity, String procedureNames) {
        // 若消息配置的不良数量小于等于0.0，或者实际不良数量小于配置的不良数量，将不进行消息的推送
        if (noticeConfig.getDefectNum() <= 0.0) {
            return;
        }

        List<InfoNoticeDefectEntity> infoNoticeDefectEntityList =  infoNoticeDefectService.getByConfigId(noticeConfig.getId());
        if (infoNoticeDefectEntityList.isEmpty()) {
            return;
        }

        LambdaQueryWrapper<RecordWorkOrderUnqualifiedEntity> unqualifyWrapper = null;
        NoticeConfigBuilder noticeConfigBuilder = null;
        for(InfoNoticeDefectEntity defectItem : infoNoticeDefectEntityList) {
            unqualifyWrapper = new LambdaQueryWrapper<>();
            unqualifyWrapper.eq(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber());
            unqualifyWrapper.eq(RecordWorkOrderUnqualifiedEntity::getDefectId, defectItem.getDefectId());
            Long defectNumLong =  this.baseMapper.selectCount(unqualifyWrapper);
            Double defectNum = ObjectUtils.isEmpty(defectNumLong) ? 0.0 : defectNumLong;
            if (defectNum < noticeConfig.getDefectNum()) {
                continue;
            }
            noticeConfigBuilder = new NoticeConfigBuilder();
            noticeConfigBuilder.setNoticeType(NoticeTypeEnum.CONCRETE_DEFECT_NUM_THRESHOLD_NOTICE.getCode());
            noticeConfigBuilder.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
            noticeConfigBuilder.setDefectNumThreshold(noticeConfig.getDefectNum());
            noticeConfigBuilder.setProcedureName(procedureNames);
            noticeConfigBuilder.setDefectName(defectItem.getDefectName());

            infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
        }
    }



    /**
     * 若有一个启用，则返回true；否则返回false
     * @param noticeConfigList
     * @return
     */
    private Boolean judgeDefectNotice(List<InfoNoticeConfigEntity> noticeConfigList) {
       if (CollectionUtil.isEmpty(noticeConfigList)) {
           return false;
       }
       for(InfoNoticeConfigEntity item : noticeConfigList) {
           if (NoticeStateEnum.ENABLE.getCode().equals(item.getState())) {
               return true;
           }
       }
       return false;
    }

    private boolean isCheckQualified(DefectRecordDTO dto) {
        //工序质检报工获取是否算产出数,如果合格检查开启则算产出数
        //工艺工序关联的质检方案优先级大于工序配置的
        if (dto.getProcedureId() == null) {
            return false;
        }
        ProcedureEntity procedureEntity = procedureService.getById(dto.getProcedureId());
        WorkOrderProcedureRelationEntity workOrderProcedure = workOrderProcedureRelationService.lambdaQuery()
                .eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, dto.getWorkOrder())
                .eq(WorkOrderProcedureRelationEntity::getProcedureId, dto.getProcedureId())
                .last("limit 1")
                .one();
        if (!Objects.isNull(workOrderProcedure)) {
            ProcedureDefectSchemeEntity procedureDefectSchemeEntity = procedureDefectSchemeService.getOneById(workOrderProcedure.getCraftProcedureId());
            if (!Objects.isNull(procedureDefectSchemeEntity)) {
                DefectSchemeEntity defectScheme = defectSchemeService.getById(procedureDefectSchemeEntity.getSchemeId());
                return defectScheme == null ? false : defectScheme.getQualifiedCheck();
            }
        }
        return procedureEntity == null ? false : procedureEntity.getQualifiedCheck();
    }

    private boolean checkIsOk(DefectRecordDTO dto) {
        // 不良配置 校验
        FullPathCodeDTO configDto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.QUALITY_DEFECT_CONFIG).build();
        return checkIsOk(dto, configDto);

    }
    @Override
    public boolean checkIsOk(DefectRecordDTO dto, FullPathCodeDTO configDto) {
        // 1 不参与计算不良数的工位 -> 匹配直接合格
        DefectConfigDTO defectConfig = businessConfigService.getValueDto(configDto, DefectConfigDTO.class);
        List<Integer> notCalFacilities = Optional.ofNullable(defectConfig.getNotCalFacilities()).orElse(Collections.emptyList());
        if(notCalFacilities.contains(dto.getFid())) {
            return true;
        }
        // 2 不参与计算不良数的不良项 -> 所有的都匹配则直接合格
        List<DefectDefineDTO> defines = dto.getDefines();
        // 2.1 配置的
        List<Integer> notCalDefects = Optional.ofNullable(defectConfig.getNotCalDefects()).orElse(new ArrayList<>());
        // 2.2 内置的
        notCalDefects.add(Constant.QUALIFIED_ID);
        // 有其中一项不是默认的合格项 -> 直接不合格
        return defines.stream().map(DefectDefineDTO::getDefectId).allMatch(notCalDefects::contains);

    }

    private void checkWorkOrderState(WorkOrderEntity workOrderEntity) {
        // 从业务配置中获取
        FullPathCodeDTO configDto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.QUALITY_INSPECTION_CONFIG).build();
        QualityInspectionConfigDTO qualityInspectionConfigDTO = businessConfigService.getValueDto(configDto, QualityInspectionConfigDTO.class);
        List<Integer> configState = Optional.ofNullable(qualityInspectionConfigDTO.getOriginalOrderStates()).orElse(Collections.emptyList());
        HashSet<Integer> enableStates = new HashSet<>(configState);
        // 投产状态的无需配置即可直接检测
        enableStates.add(WorkOrderStateEnum.INVESTMENT.getCode());
        Integer state = workOrderEntity.getState();
        if (!enableStates.contains(state)) {
            throw new ResponseException("当前工单状态:" + WorkOrderStateEnum.getNameByCode(state) + "不可进行质检");
        }
    }

    /**
     * 获取关联资源、生产基本单元名称
     *
     * @param workOrderEntity 工单
     * @param relevanceType   关联类型
     * @return
     */
    private String getRelevanceTypeName(WorkOrderEntity workOrderEntity, String relevanceType) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        String resourceName = null;
        if (WorkCenterTypeEnum.LINE.getCode().equals(relevanceType)) {
            workOrderService.getRelevanceLineById(workOrderEntity);
            if (!CollectionUtils.isEmpty(workOrderEntity.getRelevanceLineNames())) {
                resourceName = String.join(Constant.SEP, workOrderEntity.getRelevanceLineNames());
            }

        } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(relevanceType)) {
            workOrderService.getRelevanceDeviceById(workOrderEntity);
            if (!CollectionUtils.isEmpty(workOrderEntity.getRelevanceDeviceNames())) {
                resourceName = String.join(Constant.SEP, workOrderEntity.getRelevanceDeviceNames());
            }
        } else {
            workOrderService.getRelevanceTeamById(workOrderEntity);
            if (!CollectionUtils.isEmpty(workOrderEntity.getRelevanceTeamNames())) {
                resourceName = String.join(Constant.SEP, workOrderEntity.getRelevanceTeamNames());
            }
        }
        return resourceName;
    }


    @Override
    public DefectRecordPadDTO getPadDTOByBarCode(String barCode, Integer fid) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        LambdaQueryWrapper<RecordWorkOrderUnqualifiedEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordWorkOrderUnqualifiedEntity::getSequenceId, barCode);
        List<RecordWorkOrderUnqualifiedEntity> list = this.list(wrapper);
        for (RecordWorkOrderUnqualifiedEntity recordWorkOrderUnqualifiedEntity : list) {
            //获取图片
            List<String> urls = getPicUrlsByBarCode(barCode, recordWorkOrderUnqualifiedEntity.getDefectType(), recordWorkOrderUnqualifiedEntity.getAbnormalName());
            recordWorkOrderUnqualifiedEntity.setPicUrls(urls);
            //对应维修方案
            recordWorkOrderUnqualifiedEntity.setMaintainSchemePadDTO(defectDefineService.getMaintainSchemePad(recordWorkOrderUnqualifiedEntity.getDefectId()));
        }
        DefectRecordPadDTO build = DefectRecordPadDTO.builder().list(list).build();
        if (!CollectionUtils.isEmpty(list)) {
          //找到对应维修记录
            for(RecordWorkOrderUnqualifiedEntity recordWorkOrderUnqualifiedEntity:list){
                if(StringUtils.isNotBlank(recordWorkOrderUnqualifiedEntity.getMaintainIds())){
//                    List<Integer> idList = Arrays.stream(recordWorkOrderUnqualifiedEntity.getMaintainIds().split(","))
//                            .mapToInt(Integer::parseInt)
//                            .boxed()
//                            .collect(Collectors.toList());
                    List<MaintainRecordEntity> maintainRecordEntityList =  maintainRecordService.lambdaQuery().eq(MaintainRecordEntity::getBarCode,recordWorkOrderUnqualifiedEntity.getSequenceId())
                            .eq(MaintainRecordEntity::getDefectId,recordWorkOrderUnqualifiedEntity.getDefectId())
                            .list();
                    maintainRecordService.setNameByList(maintainRecordEntityList);
                    Collections.reverse(maintainRecordEntityList);
                    recordWorkOrderUnqualifiedEntity.setMaintainRecordEntityList(maintainRecordEntityList);
                }
            }
            setNameByList(list);
            List<String> workOrders = list.stream().map(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
//            if (workOrders.size() > 1) {
//                throw new ResponseException("条码关联了多个工单");
//            }
            String workOrder = workOrders.get(0);
            WorkOrderEntity entity = workOrderService.getSimpleWorkOrderByNumber(workOrder);
            if (entity != null) {
                MaterialEntity materialEntity = materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId());
                entity.setMaterialFields(materialEntity);
            }
            build.setWorkOrderEntity(entity);
        } else {
            //根据条码和工位找到对应工单
            WorkOrderEntity entity = productFlowCodeService.getByProductFlowCodeAndFid(barCode, fid);
            if (entity != null) {
                MaterialEntity materialEntity = materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId());
                entity.setMaterialFields(materialEntity);
            }
            build.setWorkOrderEntity(entity);
        }
        return build;
    }

    @Override
    public List<String> getWorkOrders() {
        LambdaQueryWrapper<RecordWorkOrderUnqualifiedEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber);
        List<RecordWorkOrderUnqualifiedEntity> list = this.list(wrapper);
        return list.stream().map(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
    }

    @Override
    public void exportRecordList(DefectRecordQueryDTO queryDTO, HttpServletResponse response) {
        //获取文件
        String destFileName = "recordTemp_" + UUID.randomUUID() + Constant.XLSX;
        File destFile = new File(PathUtils.getAbsolutePath(this.getClass()) + File.separator + destFileName);
        try(InputStream templateInputStream = getTemplateFile();) {
            if (!destFile.exists()) {
                boolean newFile = destFile.createNewFile();
            }
            ExcelWriter excelWriter = EasyExcelFactory.write(destFile).withTemplate(templateInputStream).excelType(ExcelTypeEnum.XLSX).autoCloseStream(true).build();
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            //收集sheet名
            String defaultSheetName = "工位质检-检测记录";
            Sheet sheet = workbook.getSheet(defaultSheetName);
            if (sheet != null) {
                //删除原sheet
                int sheetIndex = workbook.getSheetIndex(sheet);
                workbook.removeSheetAt(sheetIndex);
                //处理数据
                Page<RecordWorkOrderUnqualifiedEntity> page = getList(queryDTO);
                List<RecordWorkOrderUnqualifiedEntity> list = page.getRecords();
                String jsonString = JSONObject.toJSONString(list);
                List<DefectRecordExcelDTO> dtos = JSONArray.parseArray(jsonString, DefectRecordExcelDTO.class);
                //写入新的sheet
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(defaultSheetName).head(DefectRecordExcelDTO.class).build();
                excelWriter.write(dtos, writeSheet);
            }
            //设置强制计算公式
            workbook.setForceFormulaRecalculation(true);
            excelWriter.finish();
            ExcelTemplateImportUtil.responseToClient(response, new FileInputStream(destFile), destFileName);
        } catch (IOException e) {
            log.error("excel导出异常", e);
        } finally {
            FileUtils.deleteQuietly(destFile);
        }
    }

    /**
     * 获取文件
     */
    private InputStream getTemplateFile() {
        //从fastdfs获取用户上传的模板
        String fileUrl = fastDfsClientService.getUrlByFileCode(ReportFormConstant.DEFECT_RECORD_TEMPLATE);
        if (StringUtils.isBlank(fileUrl)) {
            try {
                return getDefaultTemplateInputStream();
            } catch (IOException e) {
                log.error("获取默认模板出错", e);
            }
        }
        // 临时转储到服务器用于操作
        byte[] fileBuffer = fastDfsClientService.getFileStream(fileUrl);
        return new ByteArrayInputStream(fileBuffer);
    }

    @Override
    public List<RecordWorkOrderUnqualifiedEntity> getUnqualifiedByFidAndWorkOrders(List<String> workOrderNumbers, Integer fid) {
        List<RecordWorkOrderUnqualifiedEntity> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return list;
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        // 查询对应工单的名字
        Map<String, String> workOrderNumberNameMap = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getWorkOrderName)
                .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                .list()
                .stream()
                .collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getWorkOrderName));
        workOrderNumbers.forEach(workOrderNumber -> {
            Integer unqualified = recordWorkOrderUnqualifiedMapper.getUnqualified(workOrderNumber, fid);
            list.add(RecordWorkOrderUnqualifiedEntity.builder()
                    .workOrderNumber(workOrderNumber)
                    .workOrderName(workOrderNumberNameMap.get(workOrderNumber))
                    .unqualified(unqualified)
                    .build());
        });
        return list;
    }

    /**
     * 导出检测记录默认模板
     *
     * @param response
     */
    @Override
    public void exportRecordTemplate(HttpServletResponse response) {
        try(InputStream bis = getDefaultTemplateInputStream();) {
            ExcelTemplateImportUtil.responseToClient(response, bis, "质检记录默认模板" + Constant.XLSX);
        } catch (IOException e) {
            log.error("文件获取失败", e);
            throw new ResponseException(RespCodeEnum.FILE_DOWNLOAD_FAILED);
        }
    }

    private InputStream getDefaultTemplateInputStream() throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        org.springframework.core.io.Resource resource = resolver.getResource("classpath:template/defaultDefectRecordTemplate.xlsx");
        if (!resource.exists()) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        return resource.getInputStream();
    }

    /**
     * 用户上传质检记录模板
     *
     * @param file
     */
    @Override
    public void uploadRecordTemplate(MultipartFile file, String username) {
        if (file == null || file.isEmpty()) {
            throw new ResponseException(RespCodeEnum.IMPORT_FILE_IS_NULL);
        }
        //文件后缀判断
        String originalFilename = file.getOriginalFilename();
        if (!ExcelUtil.isExcelFile(originalFilename)) {
            throw new ResponseException(RespCodeEnum.PARSING_DATA_IS_EMPTY);
        }
        // 保存文件到fast-dfs
        String fileName = ReportFormConstant.DEFECT_RECORD_TEMPLATE;
        fastDfsClientService.uploadFile(file, username, fileName);
    }

    @Override
    public List<DefectCountVO> getWorkOrderDefectCount(String workOrderNumber) {
        List<RecordWorkOrderUnqualifiedEntity> allDefects = lambdaQuery()
                .eq(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, workOrderNumber)
                .ne(RecordWorkOrderUnqualifiedEntity::getDefectId, Constant.QUALIFIED_ID).list();
        Map<String, List<RecordWorkOrderUnqualifiedEntity>> defectNameMap = allDefects.stream().filter(e -> e.getAbnormalName() != null)
                .collect(Collectors.groupingBy(RecordWorkOrderUnqualifiedEntity::getAbnormalName));
        return defectNameMap.entrySet().stream().map(entry -> {
            String defectName = entry.getKey();
            List<RecordWorkOrderUnqualifiedEntity> defects = entry.getValue();
            RecordWorkOrderUnqualifiedEntity defect = defects.get(0);
            return DefectCountVO.builder()
                    .defectName(defectName)
                    .defectId(defect.getDefectId())
                    .defectType(defect.getDefectType())
                    .count(defects.size())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<RecordWorkOrderUnqualifiedEntity>> workOrderUnqualifiedRecordsMap(Collection<String> workOrderNumbers, Date startTime, Date endTime) {
        List<RecordWorkOrderUnqualifiedEntity> records = CollUtil.isEmpty(workOrderNumbers)? Collections.emptyList() :
                this.lambdaQuery()
                        .in(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, workOrderNumbers)
                        .ne(RecordWorkOrderUnqualifiedEntity::getDefectId, Constant.QUALIFIED_ID)
                        .ge(startTime != null, RecordWorkOrderUnqualifiedEntity::getCreateDate, startTime)
                        .lt(endTime != null, RecordWorkOrderUnqualifiedEntity::getCreateDate, endTime)
                        .list();
        return records.stream().collect(Collectors.groupingBy(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber));
    }



    @Override
    public Page<RecordWorkOrderUnqualifiedVO> pageV2(RecordWorkOrderUnqualifiedQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<RecordWorkOrderUnqualifiedEntity> page = this.baseMapper.getPageV2(sql, dto.getPage());
        setNameByList(page.getRecords());
        return JacksonUtil.convertPage(page, RecordWorkOrderUnqualifiedVO.class);
    }


//    public void unqualifiedRecordBatchDelete(RecordWorkOrderUnqualifiedBatchDeleteDTO recordWorkOrderUnqualifiedBatchDeleteDTO){
//
//    }

//    /**
//     * 添加质检记录
//     */
//    public void addRecordWorkOrderUnqualified(RecordWorkOrderUnqualifiedAddDTO dto,String  userName) {
//       //1、校验关键信息
//        if (CollectionUtils.isEmpty(dto.getDefines())) {
//          throw new ResponseException("不良项列表不能为空");
//        }
//        if(dto.getQuantity()==null){
//            dto.setQuantity(1);
//        }
//        if(StringUtils.isNotBlank(dto.getCode())&&dto.getQuantity()>1){
//            throw new ResponseException(RespCodeEnum.BARCODE_CAN_ONLY_BE_ONE_RECORD);
//        }
//        if(StringUtils.isBlank(dto.getWorkOrderNumber())){
//            throw new ResponseException("工单号不能为空");
//        }
//         WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
//        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(dto.getWorkOrderNumber());
//        if (workOrderEntity == null) {
//            throw new ResponseException(RespCodeEnum.WORK_ORDER_NOT_FOUND);
//        }
//        FacilitiesEntity entity = facilitiesService.getById(dto.getFid());
//        // 工序质检的时候，没有工位
//        String fname = Optional.ofNullable(entity).map(FacilitiesEntity::getFname).orElse(null);
//        // 校验投产状态
//        checkWorkOrderState(workOrderEntity);
//
//        //判断是否合格
//        boolean isOk = checkIsOk(DefectRecordDTO.builder().fid(dto.getFid()).defines(dto.getDefines()).build());
//        Date date = dto.getFakerTime()!=null?dto.getFakerTime():new Date();
//
//        //2、添加过站记录（工位质检不添加过站记录）
//        if(dto.getCraftProcedureId()!=null&&dto.getQuantity()==1){
//            ProductFlowCodeRecordAddDTO addDTO = ProductFlowCodeRecordAddDTO.builder()
//                    .code(dto.getCode())
//                    .craftProcedureId(dto.getCraftProcedureId())
//                    .fcode(fname)
//                    .reportBy(userName)
//                    .reportTime(date)
//                    .build();
//            CodeRecordResultVO codeRecordResultVO = productFlowCodeRecordV2Service.addCodeRecord(addDTO);
//        }
//
//        //3、添加质检记录
//         //生成记录号，代表是同一次提交
//        long millis = System.currentTimeMillis();
//        String serialNumber = "D" + millis;
//
//        for (int i = 0; i < dto.getQuantity(); i++) {
//            String productFlowCode = barCode;
//            // 如果不输入产品标签，则默认采用质检号的标签规则
//            if (StringUtils.isBlank(barCode)) {
//                productFlowCode = numberRuleService.getNumberByRuleType(13);
//            }
//
//            String serialNumber = "D" + millis + i;
//            String sequenceId = StringUtils.isBlank(productFlowCode) ? serialNumber : productFlowCode;
//            //添加扫码记录
//            codeList.add(sequenceId);
//            for (DefectDefineDTO define : defines) {
//                list.add(RecordWorkOrderUnqualifiedEntity.builder()
//                        .sequenceId(sequenceId)
//                        .serialNumber(serialNumber)
//                        .workOrderNumber(workOrder)
//                        .workOrderName(workOrderEntity.getWorkOrderName())
//                        .materialCode(workOrderEntity.getMaterialCode())
//                        .fid(fid)
//                        .fname(fname)
//                        .remark(define.getRemark())
//                        .schemeId(define.getSchemeId())
//                        .defectId(define.getDefectId())
//                        .defectType(define.getDefectType())
//                        .abnormalName(define.getDefectName())
//                        .createBy(username)
//                        .recordDate(recordDate)
//                        .createDate(date)
//                        .codeRecordId(dto.getCodeRecordId())
//                        .workCenterName(workCenterName)
//                        .relevanceResourceName(relevanceResourceName)
//                        .remark(dto.getRemark())
//                        .recordWorkOrderUnqualifiedExtendOne(dto.getRecordWorkOrderUnqualifiedExtendOne())
//                        .recordWorkOrderUnqualifiedExtendTwo(dto.getRecordWorkOrderUnqualifiedExtendTwo())
//                        .recordWorkOrderUnqualifiedExtendThree(dto.getRecordWorkOrderUnqualifiedExtendThree())
//                        .recordWorkOrderUnqualifiedExtendFour(dto.getRecordWorkOrderUnqualifiedExtendFour())
//                        .build());
//                //每个不良类型下不良原因对应的图片
//                if (!CollectionUtils.isEmpty(define.getPicUrls())) {
//                    ArrayList<DefectRecordPicEntity> picEntities = new ArrayList<>(define.getPicUrls().size());
//                    for (String picUrl : define.getPicUrls()) {
//                        picEntities.add(DefectRecordPicEntity.builder().serialNumber(serialNumber).barCode(sequenceId).url(picUrl)
//                                .defectType(define.getDefectType()).defectName(define.getDefectName()).build());
//                    }
//                    picService.saveBatch(picEntities);
//                    for (DefectRecordPicEntity picEntity : picEntities) {
//                        //标记上传文件
//                        uploadService.markUploadFile(picEntity.getUrl(), picEntity);
//                    }
//                }
//            }
//            dto.setCodeRecordId(null);
//
//        }
//
//
//        WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getWorkCenterId());
//        String relevanceResourceName = null;
//        String workCenterName = null;
//        if (workCenterEntity != null) {
//            workCenterName = workCenterEntity.getName();
//            String relevanceType = workCenterEntity.getRelevanceType();
//            // 获取关联资源、生产基本单元名称
//            relevanceResourceName = getRelevanceTypeName(workOrderEntity, relevanceType);
//        }
//        Date recordDate = dictService.getRecordDate(date);
//        long millis = System.currentTimeMillis();
//        ArrayList<RecordWorkOrderUnqualifiedEntity> list = new ArrayList<>();
//        // 合格项不刷新工单不良数
//        boolean isOk = checkIsOk(dto);
//        boolean checkQualified = isCheckQualified(dto);
//        //质检工位机调用
//        if (dto.getCodeRecordId() != null) {
//            if (!isOk) {
//                ProductFlowCodeRecordEntity productFlowCodeRecordEntity = ProductFlowCodeRecordEntity.builder().id(dto.getCodeRecordId())
//                        .isQuality(true).isUnqualified(true).firstUnqualified(true).isReport(false).build();
//                if (dto.getCodeRecordState() != null) {
//                    productFlowCodeRecordEntity.setState(dto.getCodeRecordState());
//                }
//                productFlowCodeRecordMapper.updateById(productFlowCodeRecordEntity);
//                //刷掉产出数
//                productFlowCodeRecordService.refreshCodeUnqualifiedAndReport(productFlowCodeRecordService.getById(productFlowCodeRecordEntity.getId()));
//                //刷掉条码检查状态
//                productFlowCodeService.lambdaUpdate().eq(ProductFlowCodeEntity::getProductFlowCode, barCode)
//                        .set(ProductFlowCodeEntity::getQualityState, ProductFlowCodeQualityStateEnum.FALSE.getCode())
//                        .set(ProductFlowCodeEntity::getMaintainState, null).update();
//
//            } else {
//                ProductFlowCodeRecordEntity productFlowCodeRecordEntity = ProductFlowCodeRecordEntity.builder().id(dto.getCodeRecordId())
//                        .isQuality(true).build();
//                productFlowCodeRecordMapper.updateById(productFlowCodeRecordEntity);
//                productFlowCodeService.lambdaUpdate().eq(ProductFlowCodeEntity::getProductFlowCode, barCode)
//                        .set(ProductFlowCodeEntity::getQualityState, ProductFlowCodeQualityStateEnum.TRUE.getCode())
//                        .set(ProductFlowCodeEntity::getMaintainState, null).update();
//                //刷掉不合格数
//                productFlowCodeRecordService.cancelCodeUnqualifiedAndReport(productFlowCodeRecordService.getById(dto.getCodeRecordId()));
//            }
//            redisTemplate.delete(RedisKeyPrefix.SCANNER_PRODUCT_FLOW_CODE + barCode);
//            for (DefectDefineDTO define : defines) {
//                list.add(RecordWorkOrderUnqualifiedEntity.builder()
//                        .sequenceId(barCode)
//                        .serialNumber(barCode)
//                        .workOrderNumber(workOrder)
//                        .workOrderName(workOrderEntity.getWorkOrderName())
//                        .materialCode(workOrderEntity.getMaterialCode())
//                        .fid(fid)
//                        .fname(fname)
//                        .remark(define.getRemark())
//                        .schemeId(define.getSchemeId())
//                        .defectId(define.getDefectId())
//                        .defectType(define.getDefectType())
//                        .abnormalName(define.getDefectName())
//                        .createBy(username)
//                        .recordDate(recordDate)
//                        .createDate(date)
//                        .codeRecordId(dto.getCodeRecordId())
//                        .workCenterName(workCenterName)
//                        .remark(dto.getRemark())
//                        .relevanceResourceName(relevanceResourceName)
//                        .recordWorkOrderUnqualifiedExtendOne(dto.getRecordWorkOrderUnqualifiedExtendOne())
//                        .recordWorkOrderUnqualifiedExtendTwo(dto.getRecordWorkOrderUnqualifiedExtendTwo())
//                        .recordWorkOrderUnqualifiedExtendThree(dto.getRecordWorkOrderUnqualifiedExtendThree())
//                        .recordWorkOrderUnqualifiedExtendFour(dto.getRecordWorkOrderUnqualifiedExtendFour())
//                        .build());
//                //每个不良类型下不良原因对应的图片
//                if (!CollectionUtils.isEmpty(define.getPicUrls())) {
//                    ArrayList<DefectRecordPicEntity> picEntities = new ArrayList<>(define.getPicUrls().size());
//                    for (String picUrl : define.getPicUrls()) {
//                        picEntities.add(DefectRecordPicEntity.builder().serialNumber(barCode).barCode(barCode).url(picUrl)
//                                .defectType(define.getDefectType()).defectName(define.getDefectName()).build());
//                    }
//                    picService.saveBatch(picEntities);
//                    for (DefectRecordPicEntity picEntity : picEntities) {
//                        //标记上传文件
//                        uploadService.markUploadFile(picEntity.getUrl(), picEntity);
//                    }
//                }
//            }
//        } else {
//            if(StringUtils.isNotBlank(barCode)) {
//                if (dto.getCodeRecordId() == null) {
//                    AddFlowCodeDTO addFlowCodeDTO = AddFlowCodeDTO.builder()
//                            .productFlowCode(barCode)
//                            .fid(fid)
//                            .userName(username)
//                            .craftProcedureId(null)
//                            .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
//                            .isEndTime(true)
//                            .autoAddCode(true)
//                            .needFid(fid != null)
//                            .isInput(false)
//                            .isReport(false)
//                            .workOrderNumber(dto.getWorkOrder())
//                            .isUnqualified(!isOk).build();
//                    //开启合格检测且提交合格数据算产出
//                    if (checkQualified && Constant.QUALIFIED_TYPE_NAME.equals(dto.getDefines().get(0).getDefectName())) {
//                        addFlowCodeDTO.setIsReport(true);
//                    }
//                    //如果设置了不合格过站记录状态
//                    if (dto.getCodeRecordState() != null && !isOk) {
//                        addFlowCodeDTO.setState(dto.getCodeRecordState());
//                    }
//                    ProductFlowCodeResultEntity productFlowCodeResultEntity = scannerService.addCodeRecordQuality(addFlowCodeDTO);
//                    dto.setCodeRecordId(productFlowCodeResultEntity.getCodeRecordId());
//                }
//            }
//            //工位质检调用
//            List<String> codeList=new ArrayList<>();
//            for (int i = 0; i < quantity; i++) {
//                String productFlowCode = barCode;
//                // 如果不输入产品标签，则默认采用质检号的标签规则
//                if (StringUtils.isBlank(barCode)) {
//                    productFlowCode = numberRuleService.getNumberByRuleType(13);
//                }
//                String serialNumber = "D" + millis + i;
//                String sequenceId = StringUtils.isBlank(productFlowCode) ? serialNumber : productFlowCode;
//                //添加扫码记录
//                codeList.add(sequenceId);
//                for (DefectDefineDTO define : defines) {
//                    list.add(RecordWorkOrderUnqualifiedEntity.builder()
//                            .sequenceId(sequenceId)
//                            .serialNumber(serialNumber)
//                            .workOrderNumber(workOrder)
//                            .workOrderName(workOrderEntity.getWorkOrderName())
//                            .materialCode(workOrderEntity.getMaterialCode())
//                            .fid(fid)
//                            .fname(fname)
//                            .remark(define.getRemark())
//                            .schemeId(define.getSchemeId())
//                            .defectId(define.getDefectId())
//                            .defectType(define.getDefectType())
//                            .abnormalName(define.getDefectName())
//                            .createBy(username)
//                            .recordDate(recordDate)
//                            .createDate(date)
//                            .codeRecordId(dto.getCodeRecordId())
//                            .workCenterName(workCenterName)
//                            .relevanceResourceName(relevanceResourceName)
//                            .remark(dto.getRemark())
//                            .recordWorkOrderUnqualifiedExtendOne(dto.getRecordWorkOrderUnqualifiedExtendOne())
//                            .recordWorkOrderUnqualifiedExtendTwo(dto.getRecordWorkOrderUnqualifiedExtendTwo())
//                            .recordWorkOrderUnqualifiedExtendThree(dto.getRecordWorkOrderUnqualifiedExtendThree())
//                            .recordWorkOrderUnqualifiedExtendFour(dto.getRecordWorkOrderUnqualifiedExtendFour())
//                            .build());
//                    //每个不良类型下不良原因对应的图片
//                    if (!CollectionUtils.isEmpty(define.getPicUrls())) {
//                        ArrayList<DefectRecordPicEntity> picEntities = new ArrayList<>(define.getPicUrls().size());
//                        for (String picUrl : define.getPicUrls()) {
//                            picEntities.add(DefectRecordPicEntity.builder().serialNumber(serialNumber).barCode(sequenceId).url(picUrl)
//                                    .defectType(define.getDefectType()).defectName(define.getDefectName()).build());
//                        }
//                        picService.saveBatch(picEntities);
//                        for (DefectRecordPicEntity picEntity : picEntities) {
//                            //标记上传文件
//                            uploadService.markUploadFile(picEntity.getUrl(), picEntity);
//                        }
//                    }
//                }
//                dto.setCodeRecordId(null);
//
//            }
//            if (dto.getCodeRecordId() == null) {
//                //获取工序质检算产出数据
//                AddFlowCodeBatchDTO addFlowCodeBatchDTO = AddFlowCodeBatchDTO.builder()
//                        .productFlowCodeList(codeList)
//                        .fid(fid)
//                        .userName(username)
//                        .craftProcedureId(null)
//                        .state(ProductFlowCodeRecordStateEnum.COMPLETE.getCode())
//                        .isEndTime(true)
//                        .autoAddCode(true)
//                        .needFid(fid != null)
//                        .isInput(false)
//                        .isReport(false)
//                        .workOrderNumber(dto.getWorkOrder())
//                        .isUnqualified(!isOk).build();
//                //开启合格检测且提交合格数据算产出
//                if (checkQualified && Constant.QUALIFIED_TYPE_NAME.equals(dto.getDefines().get(0).getDefectName())) {
//                    addFlowCodeBatchDTO.setIsReport(true);
//                }
//                //如果设置了不合格过站记录状态
//                if (dto.getCodeRecordState() != null && !isOk) {
//                    addFlowCodeBatchDTO.setState(dto.getCodeRecordState());
//                }
//                scannerService.addCodeRecordQualityBatch(addFlowCodeBatchDTO);
//            }
//    }
}
