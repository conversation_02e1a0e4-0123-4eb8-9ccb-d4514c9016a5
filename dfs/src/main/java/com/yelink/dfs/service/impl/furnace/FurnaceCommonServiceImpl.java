package com.yelink.dfs.service.impl.furnace;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.furnace.FurnaceStateEnum;
import com.yelink.dfs.constant.furnace.LaboratoryStateEnum;
import com.yelink.dfs.entity.furnace.ConfigDefValEntity;
import com.yelink.dfs.entity.furnace.FurnaceEntity;
import com.yelink.dfs.entity.furnace.LaboratoryReportEntity;
import com.yelink.dfs.entity.furnace.LaboratoryReportSpecimenEntity;
import com.yelink.dfs.entity.furnace.RefiningFurnaceRecordEntity;
import com.yelink.dfs.entity.furnace.SecondaryMaterialEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.mapper.furnace.FurnaceMapper;
import com.yelink.dfs.mapper.furnace.LaboratoryReportMapper;
import com.yelink.dfs.mapper.furnace.SecondaryMaterialMapper;
import com.yelink.dfs.service.furnace.ConfigDefValService;
import com.yelink.dfs.service.furnace.FurnaceCommonService;
import com.yelink.dfs.service.furnace.LaboratoryReportSpecimenService;
import com.yelink.dfs.service.furnace.RefiningFurnaceRecordService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-08-03 19:22
 */
@AllArgsConstructor
@Service
@Slf4j
public class FurnaceCommonServiceImpl implements FurnaceCommonService {

    private LaboratoryReportMapper laboratoryReportMapper;
    private LaboratoryReportSpecimenService laboratoryReportSpecimenService;
    private FurnaceMapper furnaceMapper;
    private RefiningFurnaceRecordService refiningFurnaceRecordService;
    private SecondaryMaterialMapper secondaryMaterialMapper;
    private ConfigDefValService configDefValService;
    private WorkOrderService workOrderService;
    private BomService bomService;

    /**
     * 确认化验结果
     *
     * @param number
     * @param furnaceCode
     * @param sequence
     * @param userName
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class ,propagation = Propagation.REQUIRES_NEW)
    @Override
    public void submit(String number, String furnaceCode, Integer sequence, Integer fid, String userName, Integer reportFid) {
        //校验炉次状态
        LambdaQueryWrapper<FurnaceEntity> furnaceWrapper = new LambdaQueryWrapper<>();
        furnaceWrapper.eq(FurnaceEntity::getFurnaceCode, furnaceCode)
                .eq(FurnaceEntity::getFid, fid);
        FurnaceEntity furnaceEntity = furnaceMapper.selectOne(furnaceWrapper);
        RefiningFurnaceRecordEntity recordEntity = refiningFurnaceRecordService.selectBy(furnaceCode,fid);
        //电炉阶段
        boolean furnaceState = furnaceEntity != null && FurnaceStateEnum.RUNNING.getCode() == (furnaceEntity.getState());
        //精炼炉阶段
        boolean refiningState =recordEntity != null && FurnaceStateEnum.RUNNING.getCode() == (recordEntity.getState());
        if (furnaceState||refiningState) {
            LaboratoryReportEntity laboratoryReportEntity = LaboratoryReportEntity.builder()
                    .furnaceCode(furnaceCode)
                    .sequence(sequence)
                    .fid(fid)
                    .affirmNumber(number)
                    .state(LaboratoryStateEnum.NOT_AFFIRM.getCode())
                    .createBy(userName)
                    .createDate(new Date())
                    .updateBy(userName)
                    .updateDate(new Date())
                    .reportFid(reportFid)
                    .build();
            laboratoryReportMapper.insert(laboratoryReportEntity);
            //修改化验样次状态
            LambdaUpdateWrapper<LaboratoryReportSpecimenEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(LaboratoryReportSpecimenEntity::getNumber, number);
            wrapper.set(LaboratoryReportSpecimenEntity::getState, 1);
            laboratoryReportSpecimenService.update(wrapper);
            return;
        }
        throw new ResponseException(RespCodeEnum.FURNACE_TIME_EXPIRED);
    }



    @Override
    public void saveByWorkOrderProductCode(String workOrderNum, Integer sequence, String furnaceCode, Integer fid, String username) {
        //找到对应出钢量
        LambdaQueryWrapper<ConfigDefValEntity> configqw = new LambdaQueryWrapper<>();
        configqw.eq(ConfigDefValEntity::getCode, Constant.TAPPING_CAPACITY_EN)
                .eq(ConfigDefValEntity::getFid, fid);
        ConfigDefValEntity tapDefValEntity = configDefValService.getOne(configqw);

        List<SecondaryMaterialEntity> list = new ArrayList<>();
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNum);
        BomEntity bomEntity = bomService.getLatestBomByCode(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId());
        for (BomRawMaterialEntity rawMaterialEntity : bomEntity.getBomRawMaterialEntities()) {
            LambdaQueryWrapper<ConfigDefValEntity> qw = new LambdaQueryWrapper<>();
            qw.eq(ConfigDefValEntity::getCode, rawMaterialEntity.getCode())
                    .eq(ConfigDefValEntity::getFid, fid);
            ConfigDefValEntity defValEntity = configDefValService.getOne(qw);
            SecondaryMaterialEntity entity = SecondaryMaterialEntity.builder()
                    .furnaceCode(furnaceCode)
                    .fid(fid)
                    .seq(sequence)
                    .materialCode(rawMaterialEntity.getCode())
                    .isConfirm(false)
                    .isAdd(false)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .createBy(username)
                    .updateBy(username)
                    // 暂时默认为缺省值
                    .correctionValue(defValEntity == null ? null : defValEntity.getDefVal())
                    .tappingCapacityVal(tapDefValEntity == null ? null : tapDefValEntity.getDefVal())
                    .build();
            list.add(entity);
        }
        for (SecondaryMaterialEntity secondaryMaterialEntity : list) {
            secondaryMaterialMapper.insert(secondaryMaterialEntity);
        }
    }
}
