package com.yelink.dfs.service.rule.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.rule.RulePrefixDTO;
import com.yelink.dfs.entity.rule.RulePrefixConfigEntity;
import com.yelink.dfs.entity.rule.RulePrefixExtendEntity;
import com.yelink.dfs.mapper.rule.RulePrefixConfigMapper;
import com.yelink.dfs.mapper.rule.RulePrefixExtendMapper;
import com.yelink.dfs.service.rule.RulePrefixConfigService;
import com.yelink.dfscommon.constant.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 编码规则前缀配置服务实现类
 */
@Service
public class RulePrefixConfigServiceImpl extends ServiceImpl<RulePrefixConfigMapper, RulePrefixConfigEntity> implements RulePrefixConfigService {

    @Autowired
    private RulePrefixExtendMapper rulePrefixExtendMapper;

    @Override
    public RulePrefixConfigEntity getByCode(String code) {
        return this.getOne(new LambdaQueryWrapper<RulePrefixConfigEntity>()
                .eq(RulePrefixConfigEntity::getCode, code));
    }

    @Override
    public List<RulePrefixDTO> getByRuleType(String ruleType) {
        List<RulePrefixDTO> list = new ArrayList<>();
        List<RulePrefixConfigEntity> configs = this.list();

        for (RulePrefixConfigEntity config : configs) {
            if (StringUtils.hasText(config.getAllowRuleTypes())) {
                List<String> allowTypes = Arrays.stream(config.getAllowRuleTypes().split(Constants.SEP)).collect(Collectors.toList());
                if (allowTypes.contains(ruleType)) {
                    list.add(convertToDTO(config));
                }
            }
        }
        return list;
    }

    @Override
    public List<RulePrefixDTO> listAll() {
        List<RulePrefixConfigEntity> configs = this.list();
        return configs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, String>> getRuleExtendList(String prefixCode) {
        List<RulePrefixExtendEntity> list = rulePrefixExtendMapper.selectList(
                new LambdaQueryWrapper<RulePrefixExtendEntity>()
                        .eq(RulePrefixExtendEntity::getPrefixCode, prefixCode));

        return list.stream()
                .map(extend -> {
                    Map<String, String> map = new HashMap<>(2);
                    map.put("pattern", extend.getPattern());
                    map.put("value", extend.getValue());
                    return map;
                })
                .collect(Collectors.toList());
    }

    private RulePrefixDTO convertToDTO(RulePrefixConfigEntity config) {
        List<String> allowTypes = StringUtils.hasText(config.getAllowRuleTypes()) ?
                Arrays.stream(config.getAllowRuleTypes().split(Constants.SEP))
                        .collect(Collectors.toList()) :
                new ArrayList<>();

        return RulePrefixDTO.builder()
                .code(config.getCode())
                .type(config.getType())
                .implName(config.getImplName())
                .ruleExtendList(getRuleExtendList(config.getCode()))
                .allowRuleTypeList(allowTypes)
                .build();
    }
}