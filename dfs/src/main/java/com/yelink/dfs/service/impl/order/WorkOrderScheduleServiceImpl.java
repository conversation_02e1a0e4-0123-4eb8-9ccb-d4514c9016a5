package com.yelink.dfs.service.impl.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportHandler;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.controller.order.WorkOrderScheduleExport1Handler;
import com.yelink.dfs.controller.order.WorkOrderScheduleExport2Handler;
import com.yelink.dfs.entity.capacity.vo.CapacityVO;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.datax.DataxSaleOrderMaterialEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.DeviceGroupDeviceEntity;
import com.yelink.dfs.entity.device.DeviceGroupEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.dto.ScheduleBasicUnitDTO;
import com.yelink.dfs.entity.model.dto.ScheduleBasicUnitDTOV2;
import com.yelink.dfs.entity.model.dto.ScheduleBasicUnitTotalDTO;
import com.yelink.dfs.entity.model.dto.ScheduleDTO;
import com.yelink.dfs.entity.model.dto.ScheduleWorkOrderDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterScheduleDTO;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderCreateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleTotalDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderScheduleUpdateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.mapper.device.DeviceGroupMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderPlanScheduleWriteBackDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderPlanScheduleWriteBackDetailDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.service.datax.DataxSaleOrderMaterialService;
import com.yelink.dfs.service.device.DeviceGroupDeviceService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterDeviceService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderScheduleService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.order.PriorityTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderScheduleStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.ProductionBasicUnitDTO;
import com.yelink.dfscommon.dto.dfs.TimeDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderProductBasicUnitChangeDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-12-21 17:42
 */
@Service
@Slf4j
@AllArgsConstructor
public class WorkOrderScheduleServiceImpl implements WorkOrderScheduleService {

    private final WorkOrderPlanService workOrderPlanService;

    protected WorkOrderService workOrderService;

    protected WorkCenterService workCenterService;

    protected DeviceService deviceService;

    protected WorkCenterDeviceService workCenterDeviceService;

    private FormFieldRuleConfigService formFieldRuleConfigService;

    private MaterialService materialService;

    private WorkOrderProcedureRelationService workOrderProcedureRelationService;

    private DeviceGroupDeviceService deviceGroupDeviceService;

    private DeviceGroupMapper deviceGroupMapper;

    private ProductionLineService productionLineService;

    private ModelService modelService;

    private SysTeamService sysTeamService;

    private MessagePushToKafkaService messagePushToKafkaService;

    private DictService dictService;

    private ExtProductOrderInterface extProductOrderInterface;

    private DataxSaleOrderMaterialService dataxSaleOrderMaterialService;

    private ExcelService excelService;

    private ModelUploadFileService modelUploadFileService;

    private CapacityService capacityService;

    private WorkOrderExtendService workOrderExtendService;

    /**
     * 按照生产基本单元分组处理工单排程数据
     *
     * @param scheduleBasicUnitDTOS
     * @param totalWorkOrderScheduleDTO
     * @return
     */
    private static Map<Integer, List<WorkOrderScheduleDTO>> groupBasicUnitWorkOrders(
            ScheduleWorkOrderDTO scheduleBasicUnitDTOS, List<WorkOrderScheduleDTO> totalWorkOrderScheduleDTO) {
        // key-> 生产基本单元
        Map<Integer, List<WorkOrderScheduleDTO>> unitWorkOrderMap = new HashMap<>();

        //如果是关联资源的场景
        if (Boolean.FALSE.equals(scheduleBasicUnitDTOS.getPrimary())) {
            String productionResourceType = scheduleBasicUnitDTOS.getProductionResourceType();
            WorkCenterTypeEnum workCenterTypeEnum = WorkCenterTypeEnum.getByCode(productionResourceType);
            if (ObjectUtil.isEmpty(workCenterTypeEnum)) {
                throw new ResponseException("找不到对应的关联资源类型");
            }
            switch (workCenterTypeEnum) {
                case LINE:
                    unitWorkOrderMap = totalWorkOrderScheduleDTO.stream()
                            .filter(dto -> dto.getRelevanceLineIds() != null && !dto.getRelevanceLineIds().isEmpty())
                            .flatMap(dto -> dto.getRelevanceLineIds().stream()
                                    .map(lineId -> new AbstractMap.SimpleEntry<>(lineId, dto))).collect(
                                    Collectors.groupingBy(AbstractMap.SimpleEntry::getKey,
                                            Collectors.mapping(AbstractMap.SimpleEntry::getValue,
                                                    Collectors.toList())));
                    break;
                case TEAM:
                    unitWorkOrderMap = totalWorkOrderScheduleDTO.stream()
                            .filter(dto -> dto.getRelevanceTeamIds() != null && !dto.getRelevanceTeamIds().isEmpty())
                            .flatMap(dto -> dto.getRelevanceTeamIds().stream()
                                    .map(lineId -> new AbstractMap.SimpleEntry<>(lineId, dto))).collect(
                                    Collectors.groupingBy(AbstractMap.SimpleEntry::getKey,
                                            Collectors.mapping(AbstractMap.SimpleEntry::getValue,
                                                    Collectors.toList())));
                    break;
                case DEVICE:
                    unitWorkOrderMap = totalWorkOrderScheduleDTO.stream()
                            .filter(dto -> dto.getRelevanceDeviceIds() != null && !dto.getRelevanceDeviceIds()
                                    .isEmpty()).flatMap(dto -> dto.getRelevanceDeviceIds().stream()
                                    .map(lineId -> new AbstractMap.SimpleEntry<>(lineId, dto))).collect(
                                    Collectors.groupingBy(AbstractMap.SimpleEntry::getKey,
                                            Collectors.mapping(AbstractMap.SimpleEntry::getValue,
                                                    Collectors.toList())));
                    break;
                default:
            }
        } else {
            //主资源的场景
            unitWorkOrderMap = totalWorkOrderScheduleDTO.stream().filter(v -> null != v.getProductionBasicUnitId())
                    .collect(Collectors.groupingBy(WorkOrderScheduleDTO::getProductionBasicUnitId));
        }
        return unitWorkOrderMap;
    }

    /**
     * 获取工作中心列表（统计待排产、已排程数量）
     *
     * @return
     */
    @Override
    public List<WorkCenterScheduleDTO> listWorkCenter() {
        List<WorkCenterEntity> workCenterEntityList = workCenterService.list();
        List<WorkCenterScheduleDTO> workCenterScheduleDTOS = new ArrayList<>();
        for (WorkCenterEntity workCenterEntity : workCenterEntityList) {
            WorkCenterScheduleDTO workCenterScheduleDTO = WorkCenterScheduleDTO.builder().id(workCenterEntity.getId())
                    .code(workCenterEntity.getCode()).name(workCenterEntity.getName()).type(workCenterEntity.getType())
                    .build();
            /* 待排数 */
            Long pendingNumber = workOrderService.lambdaQuery()
                    .eq(WorkOrderEntity::getWorkCenterId, workCenterEntity.getId())
                    .eq(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.RELEASED.getCode()).count();
            /* 已排数 */
            Long alreadyNumber = workOrderService.lambdaQuery()
                    .eq(WorkOrderEntity::getWorkCenterId, workCenterEntity.getId())
                    .eq(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.INVESTMENT.getCode()).count();
            workCenterScheduleDTO.setPendingNumber(pendingNumber.intValue());
            workCenterScheduleDTO.setAlreadyNumber(alreadyNumber.intValue());
            //上次导入时间
            DictEntity dictEntity = dictService.lambdaQuery().eq(DictEntity::getType, Constant.SCHEDULE_TIME)
                    .eq(DictEntity::getCode, workCenterEntity.getId()).last("limit 1").one();
            workCenterScheduleDTO.setLastImportTime(dictEntity == null ? null : dictEntity.getCode());
            workCenterScheduleDTOS.add(workCenterScheduleDTO);
        }
        return workCenterScheduleDTOS;
    }

    /**
     * 获取工作中心下的生产基本单元列表
     *
     * @param workCenterId
     * @return
     */
    @Override
    public List<ScheduleBasicUnitTotalDTO> listWorkCenterBasicUnit(Integer workCenterId, String planStartTime,
            String planEndTime, String productionResourceType) {
        Map<String, List<ScheduleBasicUnitDTO>> map = new HashMap<>();
        WorkCenterEntity workCenterEntity = workCenterService.getById(workCenterId);
        if (workCenterEntity == null) {
            throw new ResponseException("无对应工作中心");
        }
        List<ScheduleBasicUnitDTO> scheduleBasicUnitDTOList = new ArrayList<>();
        List<ScheduleBasicUnitTotalDTO> scheduleBasicUnitTotalDTOS = new ArrayList<>();
        String type1 =
                StringUtils.isNotEmpty(productionResourceType) ? productionResourceType : workCenterEntity.getType();

        String relevanceType = workCenterEntity.getRelevanceType();
        boolean primaryType = !type1.equals(relevanceType);
        if (WorkCenterTypeEnum.DEVICE.getCode().equals(type1)) {
            //设备
            List<DeviceEntity> deviceEntityList;
            if (primaryType) {
                deviceEntityList = workCenterService.listDeviceByCenterId(workCenterEntity.getId(), false, null, null);
            } else {
                deviceEntityList = workCenterService.getRelevanceDevice(workCenterEntity.getId());
            }
            if (CollectionUtils.isEmpty(deviceEntityList)) {
                return scheduleBasicUnitTotalDTOS;
            }
            for (DeviceEntity deviceEntity : deviceEntityList) {
                ScheduleBasicUnitDTO scheduleBasicUnitDTO = ScheduleBasicUnitDTO.builder()
                        .id(deviceEntity.getDeviceId()).workCenterId(workCenterId).code(deviceEntity.getDeviceCode())
                        .name(deviceEntity.getDeviceName()).build();
                scheduleBasicUnitDTO.setType(deviceEntity.getModelName());
                //任务数
                Long count = workOrderService.lambdaQuery()
                        .eq(WorkOrderEntity::getWorkCenterId, workCenterEntity.getId())
                        .eq(WorkOrderEntity::getProductionBasicUnitId, deviceEntity.getDeviceId())
                        .eq(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.INVESTMENT.getCode())
                        .le(WorkOrderEntity::getStartDate, planEndTime).ge(WorkOrderEntity::getEndDate, planStartTime)
                        .count();
                scheduleBasicUnitDTO.setCount(count.intValue());
                scheduleBasicUnitDTOList.add(scheduleBasicUnitDTO);
            }

        } else if (WorkCenterTypeEnum.LINE.getCode().equals(type1)) {
            //产线
            List<ProductionLineEntity> productionLineEntities;
            if (primaryType) {
                productionLineEntities = productionLineService.lambdaQuery()
                        .eq(ProductionLineEntity::getWorkCenterId, workCenterId).list();
            } else {
                productionLineEntities = workCenterService.getRelevanceLines(workCenterId);
            }
            for (ProductionLineEntity productionLineEntity : productionLineEntities) {
                ScheduleBasicUnitDTO scheduleBasicUnitDTO = ScheduleBasicUnitDTO.builder()
                        .id(productionLineEntity.getProductionLineId())
                        .code(productionLineEntity.getProductionLineCode()).name(productionLineEntity.getName())
                        .workCenterId(workCenterId)
                        .type(modelService.getModelNameById(productionLineEntity.getModelId())).build();
                //任务数
                Long count = workOrderService.lambdaQuery()
                        .eq(WorkOrderEntity::getWorkCenterId, workCenterEntity.getId())
                        .eq(WorkOrderEntity::getProductionBasicUnitId, productionLineEntity.getProductionLineId())
                        .eq(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.INVESTMENT.getCode())
                        .le(WorkOrderEntity::getStartDate, planEndTime).ge(WorkOrderEntity::getEndDate, planStartTime)
                        .count();
                scheduleBasicUnitDTO.setCount(count.intValue());
                scheduleBasicUnitDTOList.add(scheduleBasicUnitDTO);
            }
        } else if (WorkCenterTypeEnum.TEAM.getCode().equals(type1)) {
            //班组
            List<SysTeamEntity> teamEntityList;
            if (primaryType) {
                teamEntityList = sysTeamService.listByWorkCenter(workCenterId);
            } else {
                teamEntityList = workCenterService.getRelevanceTeams(workCenterId);
            }
            if (CollectionUtils.isEmpty(teamEntityList)) {
                return scheduleBasicUnitTotalDTOS;
            }
            for (SysTeamEntity sysTeamEntity : teamEntityList) {
                ScheduleBasicUnitDTO scheduleBasicUnitDTO = ScheduleBasicUnitDTO.builder().id(sysTeamEntity.getId())
                        .code(sysTeamEntity.getTeamCode()).name(sysTeamEntity.getTeamName())
                        .type(sysTeamEntity.getTeamTypeName()).workCenterId(workCenterId).build();
                //任务数
                Long count = workOrderService.lambdaQuery()
                        .eq(WorkOrderEntity::getWorkCenterId, workCenterEntity.getId())
                        .eq(WorkOrderEntity::getProductionBasicUnitId, sysTeamEntity.getId())
                        .eq(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.INVESTMENT.getCode())
                        .le(WorkOrderEntity::getStartDate, planEndTime).ge(WorkOrderEntity::getEndDate, planStartTime)
                        .count();
                scheduleBasicUnitDTO.setCount(count.intValue());
                scheduleBasicUnitDTOList.add(scheduleBasicUnitDTO);
            }
        }

        map = scheduleBasicUnitDTOList.stream().collect(Collectors.groupingBy(ScheduleBasicUnitDTO::getType));
        for (String type : map.keySet()) {
            List<ScheduleBasicUnitDTO> temp = map.get(type);
            Integer count = 0;
            if (!CollectionUtils.isEmpty(temp)) {
                count = temp.stream().filter(o -> o.getCount() != null).mapToInt(ScheduleBasicUnitDTO::getCount).sum();
            }
            ScheduleBasicUnitTotalDTO scheduleBasicUnitTotalDTO = ScheduleBasicUnitTotalDTO.builder().type(type)
                    .count(count).basicUnitList(temp).build();
            scheduleBasicUnitTotalDTOS.add(scheduleBasicUnitTotalDTO);
        }
        scheduleBasicUnitTotalDTOS = scheduleBasicUnitTotalDTOS.stream()
                .sorted(Comparator.comparing(ScheduleBasicUnitTotalDTO::getType)).collect(Collectors.toList());
        return scheduleBasicUnitTotalDTOS;
    }

    /**
     * 导入生产工单
     *
     * @param workCenterId
     */
    @Override
    public void importSchedule(Integer workCenterId) {
        workOrderService.lambdaUpdate().eq(WorkOrderEntity::getWorkCenterId, workCenterId)
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode())
                .eq(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.CREATED.getCode())
                .set(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.RELEASED.getCode()).update();
        DictEntity dictEntity = DictEntity.builder().code(workCenterId.toString()).type(Constant.SCHEDULE_TIME)
                .name(DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT)).build();
        //记录导入时间
        DictEntity dictEntityTemp = dictService.lambdaQuery().eq(DictEntity::getType, Constant.SCHEDULE_TIME)
                .eq(DictEntity::getCode, workCenterId.toString()).last("limit 1").one();
        if (dictEntityTemp != null) {
            dictEntity.setId(dictEntityTemp.getId());
        }
        dictService.saveOrUpdate(dictEntity);
    }

    /**
     * 获取工作中心下待排产工单列表
     *
     * @param workOrderScheduleSelectDTO
     * @return
     */
    @Override
    public List<WorkOrderScheduleDTO> listWorkOrderPending(WorkOrderScheduleSelectDTO workOrderScheduleSelectDTO) {
        List<Integer> stateList = Stream.of(WorkOrderStateEnum.RELEASED.getCode(),
                        WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode())
                .collect(Collectors.toList());

        workOrderScheduleSelectDTO.setStates(stateList);
        workOrderScheduleSelectDTO.setPendentQuantity(0d);
        List<WorkOrderEntity> workOrders = listWorkOrder(workOrderScheduleSelectDTO);
        List<WorkOrderScheduleDTO> workDTO = this.getWorkDTO(workOrders);
        //列表排序：
        // 1. 根据排产状态，部分排产 > 待排产进行排序，
        // 2. 交货日期排序
        // 3. 下单时间进行排序
        workDTO.sort(Comparator.comparing(WorkOrderScheduleDTO::getSchedulingState,
                        Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(WorkOrderScheduleDTO::getRequireGoodsDate,
                        Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(WorkOrderScheduleDTO::getOrderDate, Comparator.nullsLast(Comparator.naturalOrder())));
        return workDTO;
    }

    @Override
    public List<WorkOrderEntity> listWorkOrder(WorkOrderScheduleSelectDTO selectDTO) {
        int current = Objects.isNull(selectDTO.getCurrent()) ? 1 : selectDTO.getCurrent();
        int size = Objects.isNull(selectDTO.getSize()) ? Integer.MAX_VALUE : selectDTO.getSize();
        Page<WorkOrderEntity> workOrderEntityPage = workOrderService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(selectDTO.getWorkOrderNumbers()), WorkOrderEntity::getWorkOrderNumber,
                        selectDTO.getWorkOrderNumbers())
                .in(CollectionUtils.isNotEmpty(selectDTO.getWorkOrderIds()), WorkOrderEntity::getWorkOrderId,
                        selectDTO.getWorkOrderIds())
                .in(CollectionUtils.isNotEmpty(selectDTO.getStates()), WorkOrderEntity::getState, selectDTO.getStates())
                .in(CollectionUtils.isNotEmpty(selectDTO.getSchedulingState()), WorkOrderEntity::getSchedulingState,
                        selectDTO.getSchedulingState())
                .eq(Objects.nonNull(selectDTO.getWorkCenterId()), WorkOrderEntity::getWorkCenterId,
                        selectDTO.getWorkCenterId())
                // 生产基本单元id
                .eq(Objects.nonNull(selectDTO.getProductionBasicUnitId()), WorkOrderEntity::getProductionBasicUnitId,
                        selectDTO.getProductionBasicUnitId())
                // 待排产数量
                .gt(Objects.nonNull(selectDTO.getPendentQuantity()), WorkOrderEntity::getPlanQuantity,
                        selectDTO.getPendentQuantity())
                .le(StringUtils.isNotBlank(selectDTO.getIntervalEndTime()), WorkOrderEntity::getStartDate,
                        selectDTO.getIntervalEndTime())
                .ge(StringUtils.isNotBlank(selectDTO.getIntervalStartTime()), WorkOrderEntity::getEndDate,
                        selectDTO.getIntervalStartTime()).orderByDesc(WorkOrderEntity::getCreateDate)
                .orderByDesc(WorkOrderEntity::getWorkOrderId).page(new Page<>(current, size));
        // 设置表单拓展字段名称
        Map<String, String> fieldRuleConfMap = formFieldRuleConfigService.getExtendFieldNameMap("workOrder.list");
        for (WorkOrderEntity workOrderEntity : workOrderEntityPage.getRecords()) {
            // 设置拓展字段中文名
            workOrderService.setWorkOrderExtendFieldName(fieldRuleConfMap, workOrderEntity);
        }
        return workOrderEntityPage.getRecords();
    }

    private List<WorkOrderScheduleDTO> getWorkDTO(List<WorkOrderEntity> list) {
        List<WorkOrderScheduleDTO> workOrderScheduleDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return workOrderScheduleDTOList;
        }
        //查找生产工单关联的生产订单
        List<String> numbers = list.stream().map(WorkOrderEntity::getProductOrderNumber).filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        Map<String, List<ProductOrderEntity>> orderMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(numbers)) {
            List<ProductOrderEntity> productOrderEntityList = extProductOrderInterface.getListByProductOrderNumbers(
                    numbers);
            orderMap = productOrderEntityList.stream()
                    .collect(Collectors.groupingBy(ProductOrderEntity::getProductOrderNumber));
        }
        //查询生产工单关联的销售订单
        List<Integer> relateSaleOrderMaterialIds = list.stream().map(WorkOrderEntity::getRelateOrderMaterialId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<DataxSaleOrderMaterialEntity> saleOrderMaterials =
                CollUtil.isEmpty(relateSaleOrderMaterialIds) ? Collections.emptyList()
                        : dataxSaleOrderMaterialService.listByIds(relateSaleOrderMaterialIds);
        Map<Integer, DataxSaleOrderMaterialEntity> idSaleOrderMaterialMap = saleOrderMaterials.stream()
                .collect(Collectors.toMap(DataxSaleOrderMaterialEntity::getId, Function.identity()));

        //工作中心
        Set<Integer> workCenterIdSet = list.stream().map(WorkOrderEntity::getWorkCenterId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Integer, WorkCenterEntity> workCenterIdEntityMap =
                CollectionUtils.isEmpty(workCenterIdSet) ? new HashMap<>()
                        : workCenterService.listByIds(workCenterIdSet).stream()
                                .collect(Collectors.toMap(WorkCenterEntity::getId, v -> v));

        Set<String> materialCodes = list.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
        Map<String, MaterialEntity> materialCodeEntityMap = materialService.getMaterialEntityByCodes(materialCodes)
                .stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        // 获取关联的工序名称
        Map<String, String> procedureNameMap = workOrderProcedureRelationService.getProcedureNameMap(list);
        // 单件理论工时
        Map<String, Double> theoryHourMap = workOrderService.workOrderTheoryHourMap(list);

        //产能
        Map<String, CapacityVO> workOrderCapacityMap = capacityService.getWorkOrderListCapacity(list);
        // 设置工单日计划列表数据
        List<String> workOrderNumbers = list.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        Map<String, List<WorkOrderPlanEntity>> workOrderPlanGroup = workOrderPlanService.getByWorkOrderNumbers(workOrderNumbers, true);
        for (WorkOrderEntity workOrderEntity : list) {
            workOrderEntity.setWorkOrderPlanList(workOrderPlanGroup.getOrDefault(workOrderEntity.getWorkOrderNumber(), Collections.emptyList()));
            //订单信息
            List<ProductOrderEntity> productOrderEntityListTemp = orderMap.get(workOrderEntity.getProductOrderNumber());
            ProductOrderEntity productOrderEntity =
                    CollectionUtils.isEmpty(productOrderEntityListTemp) ? null : productOrderEntityListTemp.get(0);
            DataxSaleOrderMaterialEntity saleOrderMaterial = idSaleOrderMaterialMap.get(
                    workOrderEntity.getRelateOrderMaterialId());

            WorkOrderScheduleDTO workOrderScheduleDTO = WorkOrderScheduleDTO.convertToDTO(workOrderEntity);

            MaterialEntity materialEntity = materialCodeEntityMap.getOrDefault(workOrderEntity.getMaterialCode(),
                    new MaterialEntity());
            workOrderScheduleDTO.setMaterialName(materialEntity.getName());
            workOrderScheduleDTO.setMaterialStandard(materialEntity.getStandard());
            workOrderScheduleDTO.setComp(materialEntity.getComp());
            workOrderScheduleDTO.setUnit(materialEntity.getUnit());

            WorkCenterEntity workCenterEntity = workCenterIdEntityMap.getOrDefault(workOrderEntity.getWorkCenterId(),
                    new WorkCenterEntity());
            workOrderScheduleDTO.setWorkCenterType(workCenterEntity.getType());
            workOrderScheduleDTO.setWorkCenterRelevanceType(workCenterEntity.getRelevanceType());

            workOrderScheduleDTO.setProcedureName(procedureNameMap.get(workOrderEntity.getWorkOrderNumber()));
            // 查询关联资源
            String workCenterRelevanceType = workCenterEntity.getRelevanceType();
            if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterRelevanceType)) {
                // 获取工单关联制造单元资源
                workOrderService.getRelevanceLineById(workOrderEntity);
                workOrderScheduleDTO.setRelevanceLineIds(workOrderEntity.getRelevanceLineIds());
            } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterRelevanceType)) {
                // 获取工单关联设备资源
                workOrderService.getRelevanceDeviceById(workOrderEntity);
                workOrderScheduleDTO.setRelevanceDeviceIds(workOrderEntity.getRelevanceDeviceIds());
            } else if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterRelevanceType)) {
                // 获取工单关联班组资源
                workOrderService.getRelevanceTeamById(workOrderEntity);
                workOrderScheduleDTO.setRelevanceTeamIds(workOrderEntity.getRelevanceTeamIds());
            }
            // 计划理论工时
            Double theoryHour = theoryHourMap.get(workOrderEntity.getWorkOrderNumber());
            workOrderScheduleDTO.setPlanTheoryHour(
                    NullableDouble.of(theoryHour).mul(workOrderEntity.getPlanQuantity()).scale(2).cal());

            CapacityVO capacityVO = workOrderCapacityMap.get(workOrderEntity.getWorkOrderNumber());
            workOrderScheduleDTO.setCapacity(capacityVO.getCapacity());

            workOrderScheduleDTO.setPlanProductEndTime(productOrderEntity == null ? null
                    : productOrderEntity.getProductOrderMaterial().getPlanProductEndTime());
            if (Objects.nonNull(saleOrderMaterial)) {
                workOrderScheduleDTO.setOrderDate(saleOrderMaterial.getOrderDate());
                workOrderScheduleDTO.setRequireGoodsDate(saleOrderMaterial.getRequireGoodsDate());
            }
            workOrderScheduleDTO.setPriorityCode(PriorityTypeEnum.getCodeByName(workOrderEntity.getPriority()));
            //如果为投产状态则计划开始时间为实际开始时间
            if (WorkOrderStateEnum.INVESTMENT.getCode().equals(workOrderScheduleDTO.getState())) {
                workOrderScheduleDTO.setStartDate(workOrderEntity.getActualStartDate());
            }
            if (workOrderScheduleDTO.getStartDate() != null && workOrderScheduleDTO.getEndDate() != null) {
                Double differenceDay = DateUtil.getDifferenceDay(workOrderScheduleDTO.getStartDate(),
                        workOrderScheduleDTO.getStartDate(), 1);
                workOrderScheduleDTO.setPlanDays(differenceDay);
            }
            workOrderScheduleDTOList.add(workOrderScheduleDTO);
        }
        return workOrderScheduleDTOList;
    }

    /**
     * 获取生产基本单元下已排产工单列表
     *
     * @param scheduleBasicUnitDTOS
     * @param scheduleList
     * @return
     */
    @Override
    public List<WorkOrderScheduleTotalDTO> listWorkOrderAlready(List<ScheduleBasicUnitDTO> scheduleBasicUnitDTOS,
            String planStartTime, String planEndTime, List<Integer> scheduleList) {
        List<WorkOrderScheduleTotalDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(scheduleBasicUnitDTOS)) {
            return resultList;
        }
        List<Integer> states = Stream.of(WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode(),
                WorkOrderStateEnum.HANG_UP.getCode()).collect(Collectors.toList());

        for (ScheduleBasicUnitDTO scheduleBasicUnitDTO : scheduleBasicUnitDTOS) {
            WorkOrderScheduleSelectDTO selectDTO = WorkOrderScheduleSelectDTO.builder().states(states)
                    .workCenterId(scheduleBasicUnitDTO.getWorkCenterId())
                    .productionBasicUnitId(scheduleBasicUnitDTO.getId())
                    .schedulingState(Collections.singletonList(WorkOrderScheduleStateEnum.INVESTMENT.getCode()))
                    .intervalStartTime(planStartTime).intervalEndTime(planEndTime).workOrderIds(scheduleList).build();
            List<WorkOrderEntity> workOrderList = listWorkOrder(selectDTO);

            List<WorkOrderScheduleDTO> workOrderScheduleDTOList = this.getWorkDTO(workOrderList);
            workOrderScheduleDTOList = workOrderScheduleDTOList.stream()
                    .sorted(Comparator.comparing(WorkOrderScheduleDTO::getStartDate)).collect(Collectors.toList());
            WorkOrderScheduleTotalDTO workOrderScheduleTotalDTO = WorkOrderScheduleTotalDTO.builder()
                    .id(scheduleBasicUnitDTO.getId()).name(scheduleBasicUnitDTO.getName())
                    .type(scheduleBasicUnitDTO.getType()).workOrderScheduleDTOList(workOrderScheduleDTOList)
                    .workCenterId(scheduleBasicUnitDTO.getWorkCenterId()).build();
            resultList.add(workOrderScheduleTotalDTO);
        }
        return resultList;
    }

    /**
     * 修改工单排程计划
     *
     * @param workOrderUpdateDTOS
     * @param username
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void workOrderUpdate(List<WorkOrderScheduleUpdateDTO> workOrderUpdateDTOS, String username) {
        if (CollectionUtils.isEmpty(workOrderUpdateDTOS)) {
            return;
        }
        Map<Integer, WorkCenterEntity> workCneterTypeMap = new HashMap<>();
        List<WorkOrderPlanScheduleWriteBackDetailDTO> detail = new ArrayList<>();

        Set<String> successWorkOrderNumbers = new HashSet<>();
        
        // key-> 工单编号 ， value -> 排产数量累加
        Map<String, Double> workOrderPlanQuantityMap = new HashMap<>();
        for (WorkOrderScheduleUpdateDTO workOrderScheduleUpdateDTO : workOrderUpdateDTOS) {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(
                    workOrderScheduleUpdateDTO.getWorkOrderNumber());
            if (workOrderEntity == null) {
                log.error(
                        "确认计划空指针，当前数据为：" + JacksonUtil.toJSONString(workOrderScheduleUpdateDTO) + "。完整入参为："
                                + JacksonUtil.toJSONString(workOrderUpdateDTOS));
                throw new ResponseException("找不到对应工单编号: " + workOrderScheduleUpdateDTO.getWorkOrderNumber());
            }

            WorkCenterEntity workCenterEntity;
            if (!workCneterTypeMap.containsKey(workOrderEntity.getWorkCenterId())) {
                workCenterEntity = workCenterService.getWithBasicUnits(workOrderEntity.getWorkCenterId());
                workCneterTypeMap.put(workOrderEntity.getWorkCenterId(), workCenterEntity);
            } else {
                workCenterEntity = workCneterTypeMap.get(workOrderEntity.getWorkCenterId());
            }
            workOrderEntity.setWorkCenterType(workCenterEntity.getType());
            
            //处理主资源
            handleBasicResource(workOrderScheduleUpdateDTO, workOrderEntity, workCenterEntity);
            workOrderService.updateById(workOrderEntity);

            //处理关联资源
            handleRelevanceResource(workOrderScheduleUpdateDTO, workOrderEntity);

            // 构建日计划回写对象
            detail.addAll(buildWorkOrderPlan(workOrderEntity, workOrderScheduleUpdateDTO));
            successWorkOrderNumbers.add(workOrderScheduleUpdateDTO.getWorkOrderNumber());
            
            // 累加工单的排产数量
            Double planQuantity = workOrderPlanQuantityMap.getOrDefault(workOrderScheduleUpdateDTO.getWorkOrderNumber(), 0d);
            if (null != workOrderScheduleUpdateDTO.getPlannedScheduleQuantity()) {
                planQuantity = NullableDouble.of(planQuantity).add(workOrderScheduleUpdateDTO.getPlannedScheduleQuantity())
                        .scale(2).cal(0);
            }
            workOrderPlanQuantityMap.put(workOrderScheduleUpdateDTO.getWorkOrderNumber(), planQuantity);
        }
        WorkOrderPlanScheduleWriteBackDTO workOrderPlanScheduleWriteBackDTO = new WorkOrderPlanScheduleWriteBackDTO();
        workOrderPlanScheduleWriteBackDTO.setDetail(detail);
        List<String> res = workOrderPlanService.scheduleWriteBack(workOrderPlanScheduleWriteBackDTO);
        log.info("工单日计划排产回写返回结果 {}", res);
        if (!ObjectUtils.isEmpty(res)) {
            throw new ResponseException("日计划排产写入失败");
        }

        Map<String, List<WorkOrderPlanEntity>> workOrderPlanGroup = workOrderPlanService.getByWorkOrderNumbers(successWorkOrderNumbers, false);
        //全部成功执行后再推送消息到kafka
        for (String workOrderNumber : successWorkOrderNumbers) {
            WorkOrderEntity workOrderEntityNew = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
            workOrderEntityNew.setWorkOrderPlanList(workOrderPlanGroup.getOrDefault(workOrderEntityNew.getWorkOrderNumber(), Collections.emptyList()));
            
            // 计算计划结束时间，先按照当前计划的最晚时间。
            handlePlanTime(workOrderEntityNew);
            
            handleScheduleState(workOrderEntityNew, workOrderPlanQuantityMap.get(workOrderNumber));
            messagePushToKafkaService.pushNewMessage(workOrderEntityNew, Constants.KAFKA_VALUE_CHAIN_TOPIC,
                    KafkaMessageTypeEnum.WORK_ORDER_UPDATE_MESSAGE);

        }
    }

    private void handlePlanTime(WorkOrderEntity workOrderEntity) {
        // 计算计划开始和结束时间
        List<WorkOrderPlanEntity> workOrderPlanList = workOrderEntity.getWorkOrderPlanList();
        if (CollectionUtils.isEmpty(workOrderPlanList)) {
            return;
        }

        // 过滤有效计划 (计划数量大于0且有日期的计划)
        List<Date> validPlanDates = workOrderPlanList.stream()
                .filter(plan -> plan.getPlanQuantity() != null && plan.getPlanQuantity() > 0
                        && plan.getTime() != null)
                .map(WorkOrderPlanEntity::getTime).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(validPlanDates)) {
            // 找出有效计划中最早的日期
            Optional<Date> earliestDateOpt = validPlanDates.stream().min(Date::compareTo);
            // 找出有效计划中最晚的日期
            Optional<Date> latestDateOpt = validPlanDates.stream().max(Date::compareTo).map(
                    DateUtil::getEndTimeOfCurrentDay);
            // 设置计划开始时间
            earliestDateOpt.ifPresent(workOrderEntity::setStartDate);
            // 设置计划结束时间
            latestDateOpt.ifPresent(workOrderEntity::setEndDate);
        }
    }

    private void handleScheduleState(WorkOrderEntity workOrderEntity, Double workOrderPlanQuantitySum) {
        if (null == workOrderPlanQuantitySum) {
            throw new ResponseException("工单编号: " + workOrderEntity.getWorkOrderNumber() + "排产数量为空");
        }
        // 已排数量 =  当前日期之后的所有资源已排数量累加
        // 待排数量 = 计划数量-完成数量-已排数量
        List<WorkOrderPlanEntity> workOrderPlanList = workOrderEntity.getWorkOrderPlanList();
        Date date = new Date();
        Double scheduledQuantity = 0.0;

        if (CollUtil.isNotEmpty(workOrderPlanList)) {
            Date startOfDay = cn.hutool.core.date.DateUtil.beginOfDay(date);
            scheduledQuantity = workOrderPlanList.stream()
                    .filter(plan -> plan.getTime() != null && !plan.getTime().before(startOfDay))
                    .mapToDouble(plan -> plan.getPlanQuantity() != null ? plan.getPlanQuantity() : 0.0).sum();
        }

        // 更新工单已排数量
        workOrderEntity.setProductCount(scheduledQuantity);
        // 计算待排数量 = 计划数量 - 完成数量 - 所有资源的计划排产数量和
        Double planQuantity = workOrderEntity.getPlanQuantity();
        Double finishCount = workOrderEntity.getFinishCount();
        Double tmpPendingQuantity = MathUtil.sub(planQuantity,
                MathUtil.add(finishCount != null ? finishCount : 0.0, workOrderPlanQuantitySum));
        workOrderEntity.setPendentQuantity(tmpPendingQuantity);
        
        if (null == finishCount) {
            finishCount = 0d;
        }
        double dif = planQuantity - finishCount - scheduledQuantity;
        if (planQuantity <= finishCount) {
            // 已完成状态 : 计划数量 <= 已完成数量
            workOrderEntity.setSchedulingState(WorkOrderScheduleStateEnum.FINISH.getCode());
        } else if (dif <= 0) {
            // 已排产状态 :  生产计划数量 <= 已完成数量 + 已排产数量
            workOrderEntity.setSchedulingState(WorkOrderScheduleStateEnum.INVESTMENT.getCode());
        } else if (dif > 0 && scheduledQuantity > 0) {
            // 部分排产状态：  生产计划数量 > 已完成数量 + 已排产数量
            workOrderEntity.setSchedulingState(WorkOrderScheduleStateEnum.PARTIALLY_SCHEDULED.getCode());
        } else {
            // 待排产状态 : 其他情况
            workOrderEntity.setSchedulingState(WorkOrderScheduleStateEnum.RELEASED.getCode());
        }
        workOrderService.updateById(workOrderEntity);
    }

    /**
     * 根据请求参数构建工单日计划更新对象，并且计算计划结束时间并更新到工单实体中
     *
     * @param workOrderEntity            工单对象
     * @param workOrderScheduleUpdateDTO 工单排程更新对象，包含日计划
     */
    private List<WorkOrderPlanScheduleWriteBackDetailDTO> buildWorkOrderPlan(WorkOrderEntity workOrderEntity,
            WorkOrderScheduleUpdateDTO workOrderScheduleUpdateDTO) {
        List<WorkOrderPlanScheduleWriteBackDetailDTO> res = new ArrayList<>();
        // 根据请求参数的生产基本单元id 进行回写日计划
        // 一个工单允许分配到多个不同的生产基本单元
        Integer basicUnitId = workOrderScheduleUpdateDTO.getProductionBasicUnitId();
        String workCenterType = workOrderEntity.getWorkCenterType();
        if (!workOrderScheduleUpdateDTO.isPrimary()) {
            workCenterType = workOrderScheduleUpdateDTO.getRelevanceType();
        }
        List<WorkOrderScheduleUpdateDTO.WorkOrderPlanScheduleDTO> list = workOrderScheduleUpdateDTO.getWorkOrderPlanScheduleDTOList();

        // 日计划删除逻辑，
        // 1. 不传日计划列表，并且携带删除标识，则删除整个工单的日计划
        // 2. 传日计划列表，并且携带删除标识, 则只删除携带日计划列表数据
        if (org.springframework.util.CollectionUtils.isEmpty(list) && Boolean.TRUE.equals(
                workOrderScheduleUpdateDTO.getDeleteWorkOrderPlan())) {
            WorkOrderPlanScheduleWriteBackDetailDTO workOrderPlanScheduleWriteBackDetailDTO = new WorkOrderPlanScheduleWriteBackDetailDTO();
            workOrderPlanScheduleWriteBackDetailDTO.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
            workOrderPlanScheduleWriteBackDetailDTO.setProductionBasicUnitId(basicUnitId);
            workOrderPlanScheduleWriteBackDetailDTO.setProductionBasicUnitType(workCenterType);
            workOrderPlanScheduleWriteBackDetailDTO.setIsDelete(true);
            res.add(workOrderPlanScheduleWriteBackDetailDTO);
        } else {
            for (WorkOrderScheduleUpdateDTO.WorkOrderPlanScheduleDTO workOrderPlanScheduleDTO : list) {
                WorkOrderPlanScheduleWriteBackDetailDTO workOrderPlanScheduleWriteBackDetailDTO = new WorkOrderPlanScheduleWriteBackDetailDTO();
                BeanUtils.copyProperties(workOrderPlanScheduleDTO, workOrderPlanScheduleWriteBackDetailDTO);
                workOrderPlanScheduleWriteBackDetailDTO.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
                workOrderPlanScheduleWriteBackDetailDTO.setIsMain(workOrderScheduleUpdateDTO.isPrimary());
                workOrderPlanScheduleWriteBackDetailDTO.setProductionBasicUnitId(basicUnitId);
                workOrderPlanScheduleWriteBackDetailDTO.setProductionBasicUnitType(workCenterType);
                workOrderPlanScheduleWriteBackDetailDTO.setIsDelete(
                        Boolean.TRUE.equals(workOrderScheduleUpdateDTO.getDeleteWorkOrderPlan()));
                res.add(workOrderPlanScheduleWriteBackDetailDTO);
            }
        }
        return res;
    }

    private void handleBasicResource(WorkOrderScheduleUpdateDTO workOrderScheduleUpdateDTO,
            WorkOrderEntity workOrderEntity, WorkCenterEntity workCenterEntity) {
        if (Boolean.TRUE.equals(workOrderScheduleUpdateDTO.getDeleteWorkOrderPlan())) {
            log.debug("删除工单日计划， 不更新排产资源信息");
            return;
        }

        Integer productionBasicUnitId = workOrderScheduleUpdateDTO.getProductionBasicUnitId();

        List<WorkOrderBasicUnitRelationEntity> productBasicUnits = workOrderEntity.getProductBasicUnits();
        List<Integer> workOrderBasicUnitIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(productBasicUnits)) {
            workOrderBasicUnitIds = productBasicUnits.stream()
                    .map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
        } else {
            workOrderEntity.setProductionBasicUnitId(productionBasicUnitId);
        }
        // 排产资源是关联资源的情况下，
        // 并且当前工单没有绑定生产基本单元+请求参数中也没有生产基本单元id，则从工作中心的生产基本单元中获取第一个
        if (!workOrderScheduleUpdateDTO.isPrimary() && workOrderEntity.getProductionBasicUnitId() == null
                && productionBasicUnitId == null) {

            Optional<ProductionBasicUnitDTO> optionalProductionBasicUnitDTO = workCenterEntity.getProductionBasicUnitDTOList()
                    .stream().findFirst();

            if (optionalProductionBasicUnitDTO.isPresent()) {
                ProductionBasicUnitDTO scheduleBasicUnitDTO = optionalProductionBasicUnitDTO.get();
                workOrderEntity.setProductionBasicUnitId(scheduleBasicUnitDTO.getId());
                productionBasicUnitId = scheduleBasicUnitDTO.getId();
            }
        }
        //调整生产基本单元
        //1. 请求参数指定生产基本单元的 2.或者当前工单没有绑定生产基本单元
        if (productionBasicUnitId != null && !workOrderBasicUnitIds.contains(productionBasicUnitId)) {
            workOrderBasicUnitIds.add(productionBasicUnitId);
            WorkOrderProductBasicUnitChangeDTO workOrderProductBasicUnitChangeDTO = new WorkOrderProductBasicUnitChangeDTO();
            workOrderProductBasicUnitChangeDTO.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
            workOrderProductBasicUnitChangeDTO.setProductBasicUnitIds(workOrderBasicUnitIds);
            workOrderExtendService.changeWorkOrderInvestProductBasicUnit(workOrderProductBasicUnitChangeDTO);
        }
    }

    private void handleRelevanceResource(WorkOrderScheduleUpdateDTO workOrderScheduleUpdateDTO,
            WorkOrderEntity workOrderEntity) {
        if (Boolean.TRUE.equals(workOrderScheduleUpdateDTO.getDeleteWorkOrderPlan())) {
            return;
        }

        if (!workOrderScheduleUpdateDTO.isPrimary()) {
            List<Integer> productionRelevanceUnitId = Collections.singletonList(
                    workOrderScheduleUpdateDTO.getProductionRelevanceUnitId());
            String relevanceType = workOrderScheduleUpdateDTO.getRelevanceType();
            WorkCenterTypeEnum workCenterTypeEnum = WorkCenterTypeEnum.getByCode(relevanceType);
            if (ObjectUtil.isEmpty(workCenterTypeEnum)) {
                throw new ResponseException("找不到对应的关联资源类型");
            }
            switch (workCenterTypeEnum) {
                case LINE:
                    List<ProductionLineEntity> productionLineEntities = productionLineService.listByIds(
                            productionRelevanceUnitId);
                    if (ObjectUtil.isEmpty(productionLineEntities)) {
                        throw new ResponseException("找不到对应制造单元");
                    }
                    workOrderEntity.setRelevanceLineIds(productionRelevanceUnitId);
                    break;
                case TEAM:
                    workOrderEntity.setRelevanceTeamIds(productionRelevanceUnitId);
                    break;
                case DEVICE:
                    workOrderEntity.setRelevanceDeviceIds(productionRelevanceUnitId);
                    break;
                default:
            }
            workOrderService.saveRelevanceResource(workOrderEntity);
        }
    }

    @Override
    public List<CommonState> getAllState() {
        WorkOrderScheduleStateEnum[] values = WorkOrderScheduleStateEnum.values();
        List<CommonState> states = new ArrayList<>();
        for (WorkOrderScheduleStateEnum stateEnum : values) {
            states.add(CommonState.builder().code(stateEnum.getCode()).name(stateEnum.getName()).build());
        }
        return states;
    }

    @Override
    public List<WorkOrderEntity> listForCapacityUtilizationRate(Integer workCenterId, Integer productionBasicUnitId,
            Date startTime, Date endTime) {
        return workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkCenterId, workCenterId)
                .eq(WorkOrderEntity::getProductionBasicUnitId, productionBasicUnitId)
                .ge(WorkOrderEntity::getEndDate, startTime).le(WorkOrderEntity::getStartDate, endTime)
                .eq(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.INVESTMENT.getCode()).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WorkOrderScheduleDTO> createFromWorkOrder(List<WorkOrderCreateDTO> workOrderCreateDTOS,
            String username) {
        workOrderService.createFromWorkOrder(workOrderCreateDTOS, username);
        List<String> workOrderNumbers = new ArrayList<>();
        for (int i = 0; i < workOrderCreateDTOS.size(); i++) {
            workOrderCreateDTOS.get(i).setSeq(i);
            workOrderNumbers.add(workOrderCreateDTOS.get(i).getWorkOrderNumber());
        }
        Map<String, Integer> seqMap = workOrderCreateDTOS.stream()
                .collect(Collectors.toMap(WorkOrderCreateDTO::getWorkOrderNumber, WorkOrderCreateDTO::getSeq));
        WorkOrderScheduleSelectDTO selectDTO = WorkOrderScheduleSelectDTO.builder().workOrderNumbers(workOrderNumbers)
                .build();
        List<WorkOrderEntity> workOrderList = this.listWorkOrder(selectDTO);
        // 排序
        workOrderList.sort(Comparator.comparing(o -> seqMap.get(o.getWorkOrderNumber())));
        return this.getWorkDTO(workOrderList);
    }

    /**
     * 获取已排产时间列表
     *
     * @param workCenterId
     * @return
     */
    @Override
    public List<String> listTimeAlready(Integer workCenterId) {
        Date date = dictService.getRecordDate(new Date());
        WorkOrderSelectDTO workOrderSelectDTO = WorkOrderSelectDTO.builder().isShowSimpleInfo(true)
                .workCenterId(String.valueOf(workCenterId))
                .schedulingState(WorkOrderScheduleStateEnum.INVESTMENT.getCode()).build();
        Page<WorkOrderEntity> list = workOrderService.getWorkOrderEntityPage(workOrderSelectDTO, null);
        List<TimeDTO> timeDTOS = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : list.getRecords()) {
            TimeDTO timeDTO = TimeDTO.builder().startDate(workOrderEntity.getStartDate())
                    .endDate(workOrderEntity.getEndDate()).build();
            timeDTOS.add(timeDTO);
        }
        timeDTOS = DateUtil.mergeTimeIntervals(timeDTOS);
        List<Date> dateList = new ArrayList<>();
        for (TimeDTO timeDTO : timeDTOS) {
            dateList.addAll(DateUtil.getBetweenDate(timeDTO.getStartDate(), timeDTO.getEndDate()));
        }
        dateList = dateList.stream().distinct().collect(Collectors.toList());
        List<String> stringList = new ArrayList<>();
        for (Date dateTemp : dateList) {
            stringList.add(DateUtil.dateStr(dateTemp));
        }
        return stringList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public WorkOrderEntity cancelSplit(Integer workOrderId, String username) {
        // 删除拆分工单
        WorkOrderEntity workOrderEntity = workOrderService.deleteById(workOrderId, username);
        String originalWorkOrderNumber = workOrderEntity.getOriginalWorkOrderNumber();
        if (StringUtils.isBlank(originalWorkOrderNumber)) {
            return null;
        }
        WorkOrderEntity originalEntity = workOrderService.getSimpleWorkOrderByNumber(originalWorkOrderNumber);
        if (originalEntity == null) {
            return null;
        }
        originalEntity = workOrderService.getWorkOrderById(new WorkOrderDetailDTO(originalWorkOrderNumber));
        double planQuantity = MathUtil.add(originalEntity.getPlanQuantity(), workOrderEntity.getPlanQuantity());
        // 将取消拆分的计划数量加回原工单
        originalEntity.setPlanQuantity(planQuantity);
        workOrderService.updateByWorkId(originalEntity, username);
        // 单件理论工时
        Map<String, Double> theoryHourMap = workOrderService.workOrderTheoryHourMap(
                Stream.of(originalEntity).collect(Collectors.toList()));
        Double theoryHour = theoryHourMap.get(originalEntity.getWorkOrderNumber());
        originalEntity.setTheoryHour(theoryHour);
        originalEntity.setProduceTheoryHour(
                NullableDouble.of(theoryHour).mul(originalEntity.getFinishCount()).scale(2).cal());
        originalEntity.setPlanTheoryHour(
                NullableDouble.of(theoryHour).mul(originalEntity.getPlanQuantity()).scale(2).cal());
        return originalEntity;
    }

    @Override
    public Long exportTask(ScheduleDTO scheduleBasicUnitDTOS, String username) {
        DataExportParam<WorkOrderSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.WORK_SCHEDULE_PRODUCT_LIST.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.WORK_SCHEDULE_PRODUCT_LIST.name());
        dataExportParam.setCreateUserCode(username);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(ScheduleDTO.class.getName(), scheduleBasicUnitDTOS);
        String clearSheetNames = "未排产,已排产";
        parameters.put("notSchedule", "未排产");
        parameters.put("schedule", "已排产");
        parameters.put("clearSheetNames", clearSheetNames);
        dataExportParam.setParameters(parameters);
        List<Class<? extends ExportHandler>> handles = new ArrayList<>();
        handles.add(WorkOrderScheduleExport1Handler.class);
        handles.add(WorkOrderScheduleExport2Handler.class);
        return excelService.doExport(dataExportParam, handles.toArray(new Class[0]));
    }

    @Override
    public IPage<ExcelTask> taskPage(Integer current, Integer size) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.WORK_SCHEDULE_PRODUCT_LIST.name());
        return excelService.listPage(excelTask, current, size);
    }

    @Override
    public ExcelTask taskById(Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.WORK_SCHEDULE_PRODUCT_LIST.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (org.springframework.util.CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        return records.get(0);
    }

    @Override
    public void uploadListExportTemplate(MultipartFile file, String username) throws IOException {
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(file.getName(), file.getOriginalFilename(),
                file.getContentType(), EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile,
                ModelUploadFileEnum.WORK_SCHEDULE_PRODUCT_LIST.getCode(), username);
    }

    @Override
    public List<WorkOrderScheduleTotalDTO> listWorkOrderAlreadyV2(ScheduleWorkOrderDTO scheduleBasicUnitDTOS,
            String planStartTime, String planEndTime) {
        List<WorkOrderScheduleTotalDTO> resultList = new ArrayList<>();
        List<ScheduleBasicUnitDTOV2> productionResourceList = scheduleBasicUnitDTOS.getProductionResourceList();
        if (CollectionUtils.isEmpty(productionResourceList)) {
            return resultList;
        }
        List<Integer> states = Stream.of(WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode(),
                WorkOrderStateEnum.HANG_UP.getCode()).collect(Collectors.toList());

        List<Integer> schedulingStates = Arrays.asList(WorkOrderScheduleStateEnum.INVESTMENT.getCode(),
                WorkOrderScheduleStateEnum.PARTIALLY_SCHEDULED.getCode());
        WorkOrderScheduleSelectDTO selectDTO = WorkOrderScheduleSelectDTO.builder().states(states)
                .workCenterId(scheduleBasicUnitDTOS.getWorkCenterId()).schedulingState(schedulingStates)
                .intervalStartTime(planStartTime).intervalEndTime(planEndTime).build();
        // 筛选条件还没有添加基本单元id的工单列表
        List<WorkOrderEntity> workOrderList = listWorkOrder(selectDTO);
        List<WorkOrderScheduleDTO> totalWorkOrderScheduleDTO = this.getWorkDTO(workOrderList);
        Map<Integer, List<WorkOrderScheduleDTO>> unitWorkOrderMap = groupBasicUnitWorkOrders(scheduleBasicUnitDTOS,
                totalWorkOrderScheduleDTO);

        for (ScheduleBasicUnitDTOV2 scheduleBasicUnitDTO : productionResourceList) {
            List<WorkOrderScheduleDTO> workOrderScheduleDTOS = unitWorkOrderMap.get(scheduleBasicUnitDTO.getId());
            List<WorkOrderScheduleDTO> collect = new ArrayList<>();
            if (ObjectUtils.isEmpty(workOrderScheduleDTOS)) {
                log.error("找不到对应的工单数据，当前数据为：" + JacksonUtil.toJSONString(scheduleBasicUnitDTO));
            } else {
                collect = workOrderScheduleDTOS.stream()
                        .sorted(Comparator.comparing(WorkOrderScheduleDTO::getStartDate)).collect(Collectors.toList());
            }
            WorkOrderScheduleTotalDTO workOrderScheduleTotalDTO = WorkOrderScheduleTotalDTO.builder()
                    .id(scheduleBasicUnitDTO.getId()).name(scheduleBasicUnitDTO.getName())
                    .code(scheduleBasicUnitDTO.getCode()).type(scheduleBasicUnitDTO.getType())
                    .workOrderScheduleDTOList(collect).workCenterId(scheduleBasicUnitDTOS.getWorkCenterId()).build();
            resultList.add(workOrderScheduleTotalDTO);
        }
        return resultList;
    }


}
