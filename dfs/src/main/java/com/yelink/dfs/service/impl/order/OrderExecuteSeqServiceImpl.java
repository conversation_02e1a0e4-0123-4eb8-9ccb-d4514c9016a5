package com.yelink.dfs.service.impl.order;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.ReportType;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.order.OrderExecuteSeqEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.target.record.ReportCountEntity;
import com.yelink.dfs.event.PerMinute;
import com.yelink.dfs.event.listener.PerMinuteListener;
import com.yelink.dfs.mapper.order.OrderExecuteSeqMapper;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.order.EuiOccupyService;
import com.yelink.dfs.service.order.OrderExecuteSeqService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.target.record.ReportCountService;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2022/11/4
 */
@Service
public class OrderExecuteSeqServiceImpl extends ServiceImpl<OrderExecuteSeqMapper, OrderExecuteSeqEntity> implements OrderExecuteSeqService, CommandLineRunner, PerMinuteListener {

    public static final int executeFrequency = 5;
    @Autowired
    private DictService dictService;
    @Lazy
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private WorkOrderDeviceRelevanceService workOrderDeviceRelevanceService;
    @Lazy
    @Autowired
    private EuiOccupyService euiOccupyService;
    @Autowired
    @Lazy
    private ProductionLineService productionLineService;
    @Lazy
    @Autowired
    private ReportCountService reportCountService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;
    private int i = 1;
    private int EXECUTE_FREQUENCY = 5;
    private final static String INIT_FLAG = "WORK_ORDER_EXECUTE_SEQ_INIT";
    private final static String INIT_FLAG_NAME = "工单顺序表数据初始化标识";

    @Override
    public void updateOrderExecuteSeq(WorkOrderEntity entity) {
        Date now = Optional.ofNullable(entity.getFakerTime()).orElse(new Date());

        //投产情况下插入优先级，生产基本单元为设备>关联资源为设备>生产基本单元为产线
        if (entity.getState().equals(WorkOrderStateEnum.INVESTMENT.getCode())) {

            //查询关联资源
            List<WorkOrderDeviceRelevanceEntity> list = workOrderDeviceRelevanceService
                    .lambdaQuery().eq(WorkOrderDeviceRelevanceEntity::getWorkOrderId, entity.getWorkOrderId()).list();

            List<WorkOrderBasicUnitRelationEntity> producingResources = workOrderBasicUnitRelationService.lambdaQuery()
                    .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, entity.getWorkOrderNumber())
                    .eq(WorkOrderBasicUnitRelationEntity::getIsProducing, true).list();
            //如果主生产资源是设备的情况
            if (WorkCenterTypeEnum.DEVICE.getCode().equals(entity.getWorkCenterType())) {
                List<OrderExecuteSeqEntity> saveList = new ArrayList<>();
                for (WorkOrderBasicUnitRelationEntity producingResource : producingResources) {
                    saveList.add(OrderExecuteSeqEntity.builder()
                            .orderNumber(entity.getWorkOrderNumber())
                            .deviceId(producingResource.getProductionBasicUnitId())
                            .deviceAsResource(false)
                            .isOperation(false)
                            .createTime(now)
                            .build());
                }
                this.saveBatch(saveList);
            } else if (!CollectionUtils.isEmpty(list)) {
                //关联资源为设备的情况
                List<OrderExecuteSeqEntity> collect = list.stream()
                        .map(o -> OrderExecuteSeqEntity.builder()
                                .orderNumber(entity.getWorkOrderNumber())
                                .deviceId(o.getDeviceId())
                                .deviceAsResource(true)
                                .isOperation(false)
                                .createTime(now)
                                .build()
                        ).collect(Collectors.toList());

                this.saveBatch(collect);
            } else if (entity.getLineId() != null) {
                this.save(
                        OrderExecuteSeqEntity.builder()
                                .orderNumber(entity.getWorkOrderNumber())
                                .lineId(entity.getLineId())
                                .deviceAsResource(false)
                                .isOperation(false)
                                .createTime(now)
                                .build()
                );
            }

            //插入自动记录
            //reportCountService.insertAutoRecord(entity, now);

            //兼容旧特性-----第一次投产往前修改时间（工作中心为产线的情况）
/*            if (ifAdvanceInvest(entity)) {
                //插入计数器占用时间段
                euiOccupyService.insertEuiOccupy(entity, now);
                if (entity.getIsChangeInvestTime() != null && entity.getIsChangeInvestTime()) {
                    euiOccupyService.updateInvestTimeWhenReport(entity);
                }
            }*/
            //存入redis
            redisTemplate.opsForValue().set(RedisKeyPrefix.ORDER_EXECUTE_SEQ_LIST, JSON.toJSONString(this.list()));
        }

        if (entity.getState().equals(WorkOrderStateEnum.HANG_UP.getCode())
                || entity.getState().equals(WorkOrderStateEnum.FINISHED.getCode())
                || entity.getState().equals(WorkOrderStateEnum.CLOSED.getCode())
                || entity.getState().equals(WorkOrderStateEnum.CANCELED.getCode())
        ) {

            //兼容旧特性-----第一次投产往前修改时间（工作中心为产线的情况）
            /*boolean ifAdvanceInvest = ifAdvanceInvest(entity);
            if (ifAdvanceInvest) {
                euiOccupyService.updateEuiOccupy(entity, now);
            }*/

            this.lambdaUpdate().eq(OrderExecuteSeqEntity::getOrderNumber, entity.getWorkOrderNumber()).remove();
            //存入redis
            redisTemplate.opsForValue().set(RedisKeyPrefix.ORDER_EXECUTE_SEQ_LIST, JSON.toJSONString(this.list()));
            /*if (ifAdvanceInvest) {
                String nextWorkOrderNumber = getOccupyOrderByLineId(entity.getLineId());
                if (!StringUtils.isEmpty(nextWorkOrderNumber)) {
                    WorkOrderEntity nextWorkOrder = workOrderService.getSimpleWorkOrderByNumber(nextWorkOrderNumber);
                    if (nextWorkOrder != null) {
                        euiOccupyService.insertEuiOccupy(nextWorkOrder, now);
                    }
                }
            }*/
        }
    }

    @Override
    public List<String> getCalOrder(Integer deviceId, Integer lineId, Boolean limitOne) {
        LambdaQueryWrapper<WorkOrderBasicUnitRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                .eq(WorkOrderBasicUnitRelationEntity::getIsProducing, true);
        if (deviceId != null) {
            queryWrapper.eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.DEVICE.getCode());
            queryWrapper.eq(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, deviceId);
        } else {
            queryWrapper.eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.LINE.getCode());
            queryWrapper.eq(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, lineId);
        }
        queryWrapper.last(limitOne, Constant.LIMIT_ONE);

        List<String> collect = workOrderBasicUnitRelationService.list(queryWrapper).stream()
                .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            return collect;
        }
        //设备id为空的情况，则工位没有绑定设备
        if (deviceId == null) {
            return new ArrayList<>();
        }
        //以下是设备作为关联资源的情况
        List<String> workOrderNumbers = this.lambdaQuery()
                .select(OrderExecuteSeqEntity::getOrderNumber)
                .eq(OrderExecuteSeqEntity::getIsOperation, false)
                .eq(OrderExecuteSeqEntity::getDeviceAsResource, true)
                .eq(OrderExecuteSeqEntity::getDeviceId, deviceId)
                .orderByAsc(limitOne, OrderExecuteSeqEntity::getId)
                .last(limitOne, Constant.LIMIT_ONE)
                .list()
                .stream().map(OrderExecuteSeqEntity::getOrderNumber).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return new ArrayList<>();
        }

        return workOrderNumbers;
    }

    @Override
    public String getCalOrderByLineId(Integer lineId) {
        List<String> calOrder = getCalOrder(null, lineId, true);
        return CollectionUtils.isEmpty(calOrder) ? null : calOrder.get(0);
    }

    @Override
    public String getCalOrderByDeviceId(Integer deviceId) {
        List<String> calOrder = getCalOrder(deviceId, null, true);
        return CollectionUtils.isEmpty(calOrder) ? null : calOrder.get(0);
    }

    /**
     * 判断是否兼容旧特性
     */
    private boolean ifAdvanceInvest(WorkOrderEntity entity) {
        if (entity.getLineId() == null) {
            return false;
        }
        //判断该工单是否独占计数器
        //根据产线查询，如果第一条是该工单，则该工单独占计数器
        OrderExecuteSeqEntity orderExecuteSeqEntity = this.lambdaQuery()
                .eq(OrderExecuteSeqEntity::getLineId, entity.getLineId())
                .orderByAsc(OrderExecuteSeqEntity::getCreateTime)
                .orderByAsc(OrderExecuteSeqEntity::getId).last("limit 1").one();
        if (orderExecuteSeqEntity == null) {
            return false;
        }
        return orderExecuteSeqEntity.getOrderNumber().equals(entity.getWorkOrderNumber());
    }

    @Override
    public String getOccupyOrderByLineId(Integer lineId) {
        String json = (String) redisTemplate.opsForValue().get(RedisKeyPrefix.ORDER_EXECUTE_SEQ_LIST);
        if (StringUtils.isNotBlank(json)) {
            List<OrderExecuteSeqEntity> entities = JSON.parseArray(json, OrderExecuteSeqEntity.class);
            Optional<OrderExecuteSeqEntity> result = entities.stream()
                    .filter(o -> o.getLineId() != null && o.getLineId().equals(lineId) && !o.getIsOperation())
                    .min(Comparator.comparingInt(OrderExecuteSeqEntity::getId));
            return result.map(OrderExecuteSeqEntity::getOrderNumber).orElse(null);
        }

        OrderExecuteSeqEntity orderExecuteSeqEntity = this.lambdaQuery()
                .eq(OrderExecuteSeqEntity::getLineId, lineId)
                .eq(OrderExecuteSeqEntity::getIsOperation, false)
                .orderByAsc(OrderExecuteSeqEntity::getId)
                .last("limit 1").one();
        return orderExecuteSeqEntity == null ? null : orderExecuteSeqEntity.getOrderNumber();
    }

    @Override
    public String getOccupyOrderByDeviceId(Integer deviceId) {
        String json = (String) redisTemplate.opsForValue().get(RedisKeyPrefix.ORDER_EXECUTE_SEQ_LIST);
        if (StringUtils.isNotBlank(json)) {
            List<OrderExecuteSeqEntity> entities = JSON.parseArray(json, OrderExecuteSeqEntity.class);
            Optional<OrderExecuteSeqEntity> result = entities.stream()
                    .filter(o -> o.getDeviceId() != null && o.getDeviceId().equals(deviceId) && !o.getIsOperation())
                    .min(Comparator.comparingInt(OrderExecuteSeqEntity::getId));
            return result.map(OrderExecuteSeqEntity::getOrderNumber).orElse(null);
        }

        OrderExecuteSeqEntity orderExecuteSeqEntity = this.lambdaQuery()
                .eq(OrderExecuteSeqEntity::getDeviceId, deviceId)
                .eq(OrderExecuteSeqEntity::getIsOperation, false)
                .orderByAsc(OrderExecuteSeqEntity::getId)
                .last("limit 1").one();
        return orderExecuteSeqEntity == null ? null : orderExecuteSeqEntity.getOrderNumber();
    }

    /**
     * 检查工单状态并移除
     *
     * @param event
     */
    @Override
    public void perMinuteTrigger(PerMinute event) {
        //分布式锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.ORDER_EXECUTE_SEQ_CHECK_ORDER_STATE, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        //查出OrderExecuteSeq中所有工单
        List<String> list = this.lambdaQuery()
                .select(OrderExecuteSeqEntity::getOrderNumber)
                .eq(OrderExecuteSeqEntity::getIsOperation, false)
                .list().stream().map(OrderExecuteSeqEntity::getOrderNumber)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        //挑出不是投产的工单
        List<String> orderEntities = workOrderService.lambdaQuery()
                .select(WorkOrderEntity::getWorkOrderNumber)
                .ne(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .in(WorkOrderEntity::getWorkOrderNumber, list).list().stream()
                .map(WorkOrderEntity::getWorkOrderNumber)
                .collect(Collectors.toList());
        //删除
        if (CollectionUtils.isEmpty(orderEntities)) {
            return;
        }
        this.lambdaUpdate().in(OrderExecuteSeqEntity::getOrderNumber, orderEntities).remove();
    }


    /**
     * 数据迁移方法
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(String... args) {
        //try、catch避免影响启动
        try {
            initOrderExecuteSeq();
        } catch (Exception e) {
            log.error("", e);
        }
    }

    private void initOrderExecuteSeq() {
        DictEntity one = dictService.lambdaQuery().eq(DictEntity::getType, INIT_FLAG).one();
        if (one != null) {
            return;
        }
        dictService.save(DictEntity.builder().type(INIT_FLAG).name(INIT_FLAG_NAME).createTime(new Date()).build());

        //获取已经处理的工单
        List<String> dealWorkOrders = this.lambdaQuery().select(OrderExecuteSeqEntity::getOrderNumber)
                .eq(OrderExecuteSeqEntity::getIsOperation, false).list()
                .stream().map(OrderExecuteSeqEntity::getOrderNumber).collect(Collectors.toList());

        //查询所有产线
        List<ProductionLineEntity> list = productionLineService.lambdaQuery().list();
        for (ProductionLineEntity lineEntity : list) {
            //查询最后一条，如果是占用计数器的状态，则取其工单执行新方法
            ReportCountEntity reportCountEntity = reportCountService.lambdaQuery()
                    .eq(ReportCountEntity::getLineId, lineEntity.getProductionLineId())
                    .orderByDesc(ReportCountEntity::getCreateTime).orderByDesc(ReportCountEntity::getId)
                    .last("limit 1").one();
            if (reportCountEntity == null) {
                continue;
            }
            if (dealWorkOrders.contains(reportCountEntity.getWorkOrder())) {
                continue;
            }
            if (ReportType.activeTypeList.contains(reportCountEntity.getType())) {
                WorkOrderEntity entity = workOrderService.getSimpleWorkOrderByNumber(reportCountEntity.getWorkOrder());
                updateOrderExecuteSeq(entity);
                dealWorkOrders.add(entity.getWorkOrderNumber());
            }
        }

        //查询所有投产工单，排除已经处理了的工单
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .notIn(!CollectionUtils.isEmpty(dealWorkOrders), WorkOrderEntity::getWorkOrderNumber, dealWorkOrders)
                .orderByDesc(WorkOrderEntity::getStartDate)
                .list();
        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            updateOrderExecuteSeq(workOrderEntity);
        }
    }
}

