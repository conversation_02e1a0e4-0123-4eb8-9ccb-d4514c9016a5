package com.yelink.dfs.service.impl.product;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.CommonDeleteHandler;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.constant.common.config.MatchMethodEnum;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.constant.order.TakeOutApplicationStateEnum;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.constant.product.CraftStateEnum;
import com.yelink.dfs.constant.product.InspectTriggerConditionEnum;
import com.yelink.dfs.constant.product.MaterialInspectMethodEnum;
import com.yelink.dfs.constant.product.MaterialSortEnum;
import com.yelink.dfs.constant.product.MaterialStateEnum;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.constant.rule.RulePrefixEnum;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.common.DataDelVO;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.common.PreviewTemplateEntity;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.entity.common.config.dto.MaterialInspectConfDTO;
import com.yelink.dfs.entity.common.config.dto.MaterialQRCodeConfDTO;
import com.yelink.dfs.entity.common.config.dto.MaterialQueryConfigDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.open.v2.common.constant.BatchOperationErrorType;
import com.yelink.dfs.open.v2.common.util.BatchOperationResultBuilder;
import com.yelink.dfscommon.exception.BatchOperationException;
import com.yelink.dfs.open.v2.material.dto.MaterialBatchDeleteDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialBatchInsertDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialBatchUpdateDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialDetailQueryDTO;
import com.yelink.dfs.open.v2.material.vo.MaterialBatchDeleteResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialBatchInsertResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialBatchUpdateResultVO;
import com.yelink.dfs.open.v2.material.vo.MaterialFieldDetailVO;
import com.yelink.dfs.open.v2.material.vo.MaterialFieldVO;
import com.yelink.dfs.open.v2.material.vo.MaterialSyncResultVO;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.dfs.rule.NumberRuleTypeEnum;
import com.yelink.dfscommon.dto.dfs.CustomerMaterialByMaterialDTO;
import com.yelink.dfs.entity.pack.PackageSchemeEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.entity.dfs.material.MaterialInspectMethodEntity;
import com.yelink.dfscommon.entity.dfs.material.MaterialInspectTriggerConditionEntity;
import com.yelink.dfs.entity.product.dto.MaterialBatchUpdateInspectDemandDTO;
import com.yelink.dfs.entity.product.dto.MaterialCacheSelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialEntitySelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialExcelDTO;
import com.yelink.dfs.entity.product.dto.MaterialExcelImportDTO;
import com.yelink.dfs.entity.product.dto.MaterialImportDTO;
import com.yelink.dfs.entity.product.dto.MaterialRelateDataDTO;
import com.yelink.dfs.entity.product.dto.MaterialSkuEntitySelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialTypeAddDTO;
import com.yelink.dfs.entity.product.dto.MaterialsSelectDTO;
import com.yelink.dfs.entity.product.dto.extend.CommonExtendDTO;
import com.yelink.dfs.entity.supplier.dto.SupplierMaterialByMaterialDTO;
import com.yelink.dfs.event.MaterialEntityUpdateEvent;
import com.yelink.dfs.event.MaterialHaveReleasedStateBomEvent;
import com.yelink.dfs.event.MaterialHaveReleasedStateCraftEvent;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.product.MaterialMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseRequestInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.open.v1.aksk.dto.OpenApiDTO;
import com.yelink.dfs.open.v1.aksk.dto.RelatedMaterialRequestParam;
import com.yelink.dfs.open.v1.aksk.dto.RelatedMaterialResponseDTO;
import com.yelink.dfs.open.v1.material.dto.MaterialTypeInsert2DTO;
import com.yelink.dfs.open.v1.material.dto.MaterialTypeInsertDTO;
import com.yelink.dfs.open.v1.material.dto.UpdateInsertMaterialDTO;
import com.yelink.dfs.open.v2.material.dto.MaterialQueryDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.barcode.BarCodeAnalysisService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.barcode.LabelTypeConfigService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DeleteRecordService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.RedisService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.service.impl.product.dto.MaterialInspectExcelDTO;
import com.yelink.dfs.service.notice.InfoNoticeConfigService;
import com.yelink.dfs.service.open.OpenApiConfigService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.pack.PackageSchemeService;
import com.yelink.dfs.service.product.AuxiliaryAttrService;
import com.yelink.dfs.service.product.BomRawMaterialService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialAttributeListService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrSkuService;
import com.yelink.dfs.service.product.MaterialInspectMethodService;
import com.yelink.dfs.service.product.MaterialInspectTriggerConditionService;
import com.yelink.dfs.service.product.MaterialProcessAssemblyService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.api.qms.IncomingInspectionInterface;
import com.yelink.dfscommon.api.qms.InspectionSchemeInterface;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.OpenApiEnum;
import com.yelink.dfscommon.constant.OrderShowTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.PurchaseStateEnum;
import com.yelink.dfscommon.constant.ams.RequestStateEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.ModuleEnum;
import com.yelink.dfscommon.constant.dfs.UploadFileCodeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.notice.NoticeTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberRulesConfigEntity;
import com.yelink.dfscommon.constant.qms.IncomingInspectionStateEnum;
import com.yelink.dfscommon.constant.qms.InspectionsSchemeTypeEnum;
import com.yelink.dfscommon.constant.qms.ShelfLifeUnitEnum;
import com.yelink.dfscommon.constant.wms.StockInputOrOutputTypeEnum;
import com.yelink.dfscommon.constant.wms.StockOrderStateEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.CommMaterialEntitySelectDTO;
import com.yelink.dfscommon.dto.DataDelSchema;
import com.yelink.dfscommon.dto.ExcelTemplateSetDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.MaterialEntityOpenSelectDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseOpenSelectDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseRequestOpenSelectDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.dfs.GenerateCodeDTO;
import com.yelink.dfscommon.dto.dfs.GenerateRuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuSelectDTO;
import com.yelink.dfscommon.dto.dfs.NoticeConfigBuilder;
import com.yelink.dfscommon.dto.wms.StockInventorySelectDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.ams.PurchaseEntity;
import com.yelink.dfscommon.entity.ams.PurchaseRequestEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.common.FileModel;
import com.yelink.dfscommon.entity.dfs.AuxiliaryAttrEntity;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeListEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrSkuEntity;
import com.yelink.dfscommon.entity.dfs.MaterialProcessAssemblyEntity;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.entity.qms.IncomingInspectionEntity;
import com.yelink.dfscommon.entity.qms.QualityInspectionSchemeEntity;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import com.yelink.dfscommon.entity.wms.StockInventoryDetailEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.dfscommon.utils.FileUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PathUtils;
import com.yelink.dfscommon.utils.Rational;
import com.yelink.dfscommon.utils.ZipAndRarUtil;
import com.yelink.inner.service.KafkaWebSocketPublisher;
import com.yelink.notice.constant.TopicEnum;
import com.yelink.notice.entity.MessageContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-04-14 14:20
 */
@Slf4j
@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, MaterialEntity> implements MaterialService {

    public static final String YES = "是";
    public static final String NO = "否";
    @Resource
    private MaterialMapper materialMapper;
    @Resource
    @Lazy
    private BomRawMaterialService bomRawMaterialService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private WorkOrderMapper workOrderMapper;
    @Resource
    private StockInventoryDetailInterface stockInventoryDetailInterface;
    @Resource
    private StockInAndOutInterface stockInAndOutInterface;
    @Resource
    @Lazy
    private DictService dictService;
    @Resource
    private ApproveConfigService approveConfigService;
    @Resource
    private UploadService uploadService;
    @Resource
    private InspectionSchemeInterface inspectionSchemeInterface;
    @Resource
    private IncomingInspectionInterface incomingInspectionInterface;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private AppendixService appendixService;
    @Resource
    @Lazy
    private CraftService craftService;
    @Resource
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Lazy
    @Resource
    private BomService bomService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    private ImportProgressService importProgressService;
    @Resource
    private MaterialAuxiliaryAttrService materialAuxiliaryAttrService;
    @Resource
    private SkuService skuService;
    @Resource
    private MaterialAuxiliaryAttrSkuService materialSkuService;
    @Resource
    private ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    private ExtPurchaseInterface extPurchaseInterface;
    @Resource
    private ExtPurchaseRequestInterface extPurchaseRequestInterface;
    @Resource
    private LabelService labelService;
    @Resource
    private PackageSchemeService packageSchemeService;
    @Resource
    private KafkaWebSocketPublisher kafkaWebSocketPublisher;
    @Resource
    private MaterialInspectMethodService materialInspectMethodService;
    @Resource
    private MaterialInspectTriggerConditionService materialInspectTriggerConditionService;
    @Resource
    private MaterialAttributeListService materialAttributeListService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private ExcelService excelService;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    private AuxiliaryAttrService auxiliaryAttrService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private CommonDeleteHandler commonDeleteHandler;
    @Resource
    private OpenApiConfigService apiConfigService;
    @Resource
    private RedisService redisService;
    @Resource
    private MaterialProcessAssemblyService materialProcessAssemblyService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    @Lazy
    private CustomerService customerService;
    @Resource
    @Lazy
    private SupplierService supplierService;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    private InfoNoticeConfigService infoNoticeConfigService;
    @Resource
    private FormFieldRuleConfigService formFieldRuleConfigService;
    @Resource
    @Lazy
    private DeleteRecordService deleteRecordService;
    @Resource
    private MaterialCache materialCache;
    @Resource
    private LabelTypeConfigService labelTypeConfigService;
    @Resource
    private MaterialExtendService materialExtendService;

    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");

    @Override
    public Page<MaterialEntity> getList(MaterialsSelectDTO selectDTO) {
        LambdaQueryWrapper<MaterialEntity> lambda = conditionQuery(selectDTO);

        Page<MaterialEntity> page = selectDTO.sortAndLimitPage(MaterialEntity.class);
        page(page, lambda);
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Objects.nonNull(selectDTO.getIsShowSimpleInfo()) && selectDTO.getIsShowSimpleInfo()) {
            setSkuEntities(page.getRecords());
            return page;
        }
        showName(page.getRecords());
        // 展示bom编码
        if (Objects.isNull(selectDTO.getIsShowBomInfo()) || selectDTO.getIsShowBomInfo()) {
            showBom(page.getRecords());
        }
        // 展示工艺信息
        showCraft(page.getRecords());
        // 设置物料当前库存数量
        if (Objects.isNull(selectDTO.getIsShowStockInfo()) || selectDTO.getIsShowStockInfo()) {
            setMaterialStockQuantity(page.getRecords());
        }
        return page;
    }

    private void setSkuEntities(List<MaterialEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> materialCodes = list.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
        Map<String, List<MaterialAuxiliaryAttrEntity>> codeAttrs = materialAuxiliaryAttrService.lambdaQuery()
                .in(MaterialAuxiliaryAttrEntity::getMaterialCode, materialCodes)
                .list().stream().collect(Collectors.groupingBy(MaterialAuxiliaryAttrEntity::getMaterialCode));
        list.forEach(materialEntity -> {
            materialEntity.setAuxiliaryAttrEntities(codeAttrs.getOrDefault(materialEntity.getCode(), new ArrayList<>()));
        });
    }

    /**
     * 条件查询
     */
    private LambdaQueryWrapper<MaterialEntity> conditionQuery(MaterialsSelectDTO selectDTO) {
        List<String> codeList = selectDTO.getCodeList();
        if (CollectionUtils.isEmpty(codeList) && StringUtils.isNotEmpty(selectDTO.getCodes())) {
            codeList = Arrays.asList(selectDTO.getCodes().split(Constants.SEP));
        }
        LambdaQueryWrapper<MaterialEntity> lambda = new LambdaQueryWrapper<>();
        lambda
                .eq(!Objects.isNull(selectDTO.getApprovalStatus()), MaterialEntity::getApprovalStatus, selectDTO.getApprovalStatus())
                .eq(StringUtils.isNotEmpty(selectDTO.getApprover()), MaterialEntity::getApprover, selectDTO.getApprover())
                .eq(!Objects.isNull(selectDTO.getIsAuxiliaryMaterial()), MaterialEntity::getIsAuxiliaryMaterial, selectDTO.getIsAuxiliaryMaterial())
                .eq(!Objects.isNull(selectDTO.getLoseRate()), MaterialEntity::getLoseRate, selectDTO.getLoseRate())
                .eq(!Objects.isNull(selectDTO.getMaterialPrice()), MaterialEntity::getMaterialPrice, selectDTO.getMaterialPrice())
                .like(StringUtils.isNotEmpty(selectDTO.getStandard()), MaterialEntity::getStandard, escapeSpecialCharacters(selectDTO.getStandard()))
                .like(StringUtils.isNotEmpty(selectDTO.getVersion()), MaterialEntity::getVersion, selectDTO.getVersion())
                .like(StringUtils.isNotEmpty(selectDTO.getComp()), MaterialEntity::getComp, escapeSpecialCharacters(selectDTO.getComp()))
                .like(StringUtils.isNotEmpty(selectDTO.getUnit()), MaterialEntity::getUnit, escapeSpecialCharacters(selectDTO.getUnit()))
                .like(StringUtils.isNotEmpty(selectDTO.getDrawingNumber()), MaterialEntity::getDrawingNumber, escapeSpecialCharacters(selectDTO.getDrawingNumber()))
                .like(StringUtils.isNotEmpty(selectDTO.getRawMaterial()), MaterialEntity::getRawMaterial, escapeSpecialCharacters(selectDTO.getRawMaterial()))
                .like(StringUtils.isNotEmpty(selectDTO.getFactoryModel()), MaterialEntity::getFactoryModel, escapeSpecialCharacters(selectDTO.getFactoryModel()))
                .like(StringUtils.isNotEmpty(selectDTO.getLevel()), MaterialEntity::getLevel, selectDTO.getLevel())
                .like(StringUtils.isNotEmpty(selectDTO.getRemark()), MaterialEntity::getRemark, escapeSpecialCharacters(selectDTO.getRemark()))
                .like(StringUtils.isNotEmpty(selectDTO.getCreateBy()), MaterialEntity::getCreateBy, selectDTO.getCreateBy())
                .eq(!Objects.isNull(selectDTO.getHaveBom()), MaterialEntity::getHaveBom, selectDTO.getHaveBom())
                .eq(!Objects.isNull(selectDTO.getHaveCraft()), MaterialEntity::getHaveCraft, selectDTO.getHaveCraft())
                // 物料拓展字段查询
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldOne()), MaterialEntity::getCustomFieldOne, selectDTO.getCustomFieldOne())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldTwo()), MaterialEntity::getCustomFieldTwo, selectDTO.getCustomFieldTwo())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldThree()), MaterialEntity::getCustomFieldThree, selectDTO.getCustomFieldThree())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldFour()), MaterialEntity::getCustomFieldFour, selectDTO.getCustomFieldFour())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldFive()), MaterialEntity::getCustomFieldFive, selectDTO.getCustomFieldFive())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldSix()), MaterialEntity::getCustomFieldSix, selectDTO.getCustomFieldSix())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldSeven()), MaterialEntity::getCustomFieldSeven, selectDTO.getCustomFieldSeven())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldEight()), MaterialEntity::getCustomFieldEight, selectDTO.getCustomFieldEight())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldNine()), MaterialEntity::getCustomFieldNine, selectDTO.getCustomFieldNine())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldTen()), MaterialEntity::getCustomFieldTen, selectDTO.getCustomFieldTen())
                .like(!ObjectUtils.isEmpty(selectDTO.getCustomFieldEleven()), MaterialEntity::getCustomFieldEleven, selectDTO.getCustomFieldEleven())

                .eq(selectDTO.getType() != null, MaterialEntity::getType, selectDTO.getType())
                .eq(StringUtils.isNotEmpty(selectDTO.getSort()), MaterialEntity::getSort, selectDTO.getSort())
                .eq(selectDTO.getState() != null, MaterialEntity::getState, selectDTO.getState())
                .eq(StringUtils.isNoneBlank(selectDTO.getFullCode()), MaterialEntity::getCode, selectDTO.getFullCode())
                .eq(selectDTO.getIsBatchMag() != null, MaterialEntity::getIsBatchMag, selectDTO.getIsBatchMag())
                .eq(selectDTO.getIsSupportCodeManage() != null, MaterialEntity::getIsSupportCodeManage, selectDTO.getIsSupportCodeManage())
                .in(!CollectionUtils.isEmpty(codeList), MaterialEntity::getCode, codeList)
                // 打印状态
                .eq(!ObjectUtils.isEmpty(selectDTO.getIsPrint()), MaterialEntity::getIsPrint, selectDTO.getIsPrint())
                .in(!CollectionUtils.isEmpty(selectDTO.getMaterialIds()), MaterialEntity::getId, selectDTO.getMaterialIds())
                .between(StringUtils.isNoneEmpty(selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime()), MaterialEntity::getCreateTime, selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime())
                .between(StringUtils.isNoneEmpty(selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime()), MaterialEntity::getUpdateTime, selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime());
        if (StringUtils.isNotEmpty(selectDTO.getSorts())) {
            String[] splits = selectDTO.getSorts().split(Constant.SEP);
            lambda.in(MaterialEntity::getSort, Arrays.asList(splits));
        }
        if (StringUtils.isNotEmpty(selectDTO.getTypes())) {
            List<Integer> types = Arrays.stream(selectDTO.getTypes().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            lambda.in(MaterialEntity::getType, types);
        }
        if (StringUtils.isNotEmpty(selectDTO.getStates())) {
            List<Integer> states = Arrays.stream(selectDTO.getStates().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            lambda.in(MaterialEntity::getState, states);
        }
        if (Objects.nonNull(selectDTO.getUploadFile())) {
            List<String> materialIds = appendixService.lambdaQuery().eq(AppendixEntity::getType, AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                    .list().stream()
                    .map(AppendixEntity::getRelateId).distinct().collect(Collectors.toList());
            if (selectDTO.getUploadFile()) {
                if (CollectionUtils.isEmpty(materialIds)) {
                    lambda.isNull(MaterialEntity::getId);
                } else {
                    lambda.in(MaterialEntity::getId, materialIds);
                }
            } else {
                lambda.notIn(!CollectionUtils.isEmpty(materialIds), MaterialEntity::getId, materialIds);
            }

        }
        if (StringUtils.isNotEmpty(selectDTO.getExcludeIds())) {
            List<Integer> excludeIds = Arrays.stream(selectDTO.getExcludeIds().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            lambda.notIn(MaterialEntity::getId, excludeIds);
        }
        // 拼接sql
        if (!CollectionUtils.isEmpty(selectDTO.getApplyStrs())) {
            for (String applyStr : selectDTO.getApplyStrs()) {
                lambda.apply(applyStr);
            }
        }
        // 如果前端不设置排序字段，默认按主表创建时间倒序，否则按前端排序字段进行排序
        if (CollectionUtils.isEmpty(selectDTO.getSortList())) {
            lambda.orderByDesc(MaterialEntity::getCreateTime).orderByDesc(MaterialEntity::getId);
        }
        MaterialQueryConfigDTO queryConf = materialExtendService.queryConf();
        // 禁用缓存
        if(materialCache.isDisableCache()) {
            lambda.like(StringUtils.isNotEmpty(selectDTO.getCode()) && queryConf.toCodeMatchMethod() == MatchMethodEnum.LIKE, MaterialEntity::getCode, escapeSpecialCharacters(selectDTO.getCode()))
                    .likeRight(StringUtils.isNotEmpty(selectDTO.getCode()) && queryConf.toCodeMatchMethod() == MatchMethodEnum.LIKE_RIGHT, MaterialEntity::getCode, escapeSpecialCharacters(selectDTO.getCode()))
                    .like(StringUtils.isNotEmpty(selectDTO.getName()) && queryConf.toNameMatchMethod() == MatchMethodEnum.LIKE, MaterialEntity::getName, escapeSpecialCharacters(selectDTO.getName()))
                    .likeRight(StringUtils.isNotEmpty(selectDTO.getName()) && queryConf.toNameMatchMethod() == MatchMethodEnum.LIKE_RIGHT, MaterialEntity::getName, escapeSpecialCharacters(selectDTO.getName()))
            ;
        }else {
            // 将缓存的物料编码与其他条件组合
            if(StringUtils.isNotEmpty(selectDTO.getCode()) || StringUtils.isNotEmpty(selectDTO.getName())) {
                List<String> materialCodes = materialCache.query(MaterialCacheSelectDTO.builder()
                        .likeCode(selectDTO.getCode())
                        .likeName(selectDTO.getName())
                        .queryConfig(queryConf)
                        .build()
                );
                if(CollectionUtils.isEmpty(materialCodes)) {
                    lambda.eq(MaterialEntity::getId, -1);
                }else {
                    lambda.in(MaterialEntity::getCode, materialCodes);
                }
            }
        }
        return lambda;
    }

    private String escapeSpecialCharacters(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }
        // 在此进行特殊字符转义处理，例如替换某些特殊字符为转义字符等
        return input.replaceAll("[\\\\_%]", "\\\\$0");
    }

    /**
     * 物料有无生效状态bom, 只有生效状态下的bom才算
     */
    @Async
    @TransactionalEventListener(value = MaterialHaveReleasedStateBomEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void bomStateChangeEventListener(MaterialHaveReleasedStateBomEvent bomStateChangeEvent) {
        String materialCode = bomStateChangeEvent.getBomEntity().getCode();
        BomEntity bom = bomService.getById(bomStateChangeEvent.getBomEntity().getId());
        // 如果一个物料有生效状态的 bom, 那么物料表的中 haveBom 字段为true
        boolean haveBom;
        if (Objects.nonNull(bom) && BomStateEnum.RELEASED.getCode() == bom.getState()
                && Objects.nonNull(bom.getIsTemplate()) && !bom.getIsTemplate()) {
            materialCode = bom.getCode();
            haveBom = true;
        } else {
            // 查询该物料的 bom
            long releasedBomCount = bomService.lambdaQuery()
                    .eq(BomEntity::getCode, materialCode)
                    .eq(BomEntity::getIsTemplate, Constants.FALSE)
                    .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                    .count();
            haveBom = releasedBomCount > 0;
        }
        this.lambdaUpdate()
                .eq(MaterialEntity::getCode, materialCode)
                .update(MaterialEntity.builder().haveBom(haveBom).build());
    }


    /**
     * 物料有无生效状态工艺, 只有生效状态下的工艺才算
     */
    @Async
    @TransactionalEventListener(value = MaterialHaveReleasedStateCraftEvent.class, phase = TransactionPhase.AFTER_COMMIT)
    public void craftStateChangeEventListener(MaterialHaveReleasedStateCraftEvent craftEvent) {
        String materialCode = craftEvent.getCraftEntity().getMaterialCode();
        CraftEntity craftEntity = craftService.getById(craftEvent.getCraftEntity().getCraftId());
        // 如果一个物料有生效状态的 工艺, 那么物料表的中 haveCraft 字段为true
        boolean haveCraft;
        if (Objects.nonNull(craftEntity) && CraftStateEnum.RELEASED.getCode() == craftEntity.getState()
                && Objects.nonNull(craftEntity.getIsTemplate()) && !craftEntity.getIsTemplate()) {
            materialCode = craftEntity.getMaterialCode();
            haveCraft = true;
        } else {
            // 查询该物料的 工艺
            long craftCount = craftService.lambdaQuery()
                    .eq(CraftEntity::getState, CraftStateEnum.RELEASED.getCode())
                    .eq(CraftEntity::getMaterialCode, materialCode)
                    .eq(CraftEntity::getIsTemplate, Constants.FALSE)
                    .count();
            haveCraft = craftCount > 0;
        }
        this.lambdaUpdate()
                .eq(MaterialEntity::getCode, materialCode)
                .update(MaterialEntity.builder().haveCraft(haveCraft).build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeEntityById(Integer id) {
        MaterialEntity materialEntity = this.getById(id);
        // 新需求：物料删除校验不通过状态来判断，通过接口/materials/delete/check 判断是否关联数据
//        if (MaterialStateEnum.CREATE.getCode() != materialEntity.getState() && MaterialStateEnum.ABANDON.getCode() != materialEntity.getState()) {
//            throw new ResponseException("当前物料不为创建或废弃状态，无法删除");
//        }
        // 删除相关表
        List<DataDelSchema> deleteSchemas = getDeleteSchemas();
        preHandleData(materialEntity, deleteSchemas);
        // 保存相关数据
        DataDelVO dataDelVO = commonDeleteHandler.buildDeleteRecord(deleteSchemas);
        dataDelVO.getDeleteRecords().forEach(e -> {
            e.setOrderNumber(materialEntity.getCode());
            e.setMaterialCode(materialEntity.getCode());
            e.setMaterialName(materialEntity.getName());
            e.setOrderType(ModuleEnum.MATERIAL.getCode());
        });
        deleteRecordService.saveBatch(dataDelVO.getDeleteRecords());
        // 执行删除
        commonDeleteHandler.doDelete(dataDelVO.getLastDeletes());
        // 调用删除物料的方法时，推送消息
        messagePushToKafkaService.pushNewMessage(this.getById(id), Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_DELETE_MESSAGE);
        // 刷新缓存
        materialCache.noticeInstanceRefreshCache();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEntity(MaterialEntity materialEntity) {
        // 如果传入的编码为空，则由编码规则生成
        DictEntity materialTypeEntity = dictService.getById(materialEntity.getType());
        Map<String, String> numberMap = new HashMap<>();
        Map<String, String> relatedMap = new HashMap<>();
        numberMap.put("28", materialTypeEntity.getCode());
        relatedMap.put("materialTypeCode", materialTypeEntity.getCode());
        if (StringUtils.isBlank(materialEntity.getCode())) {
            if (Objects.isNull(materialEntity.getNumberRuleId())) {
                throw new ResponseException(RespCodeEnum.NEED_NUMBER_RULE);
            }
            NumberCodeDTO seqById = numberRuleService.getSeqById(
                    RuleSeqDTO.builder()
                            .id(materialEntity.getNumberRuleId())
                            .numberMap(numberMap)
                            .relatedMap(relatedMap)
                            .build()
            );
            materialEntity.setCode(seqById.getCode());
        }
        // 创建时状态设置为创建
        materialEntity.setState(MaterialStateEnum.CREATE.getCode());
        // 判断编号是否重复
        notRepeat(materialEntity);
        materialEntity.setCreateTime(new Date());
        materialEntity.setUpdateTime(materialEntity.getCreateTime());
        // 如果需要审批，则缺省为待审核状态
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.MATERIAL.getCode())) {
            materialEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 获取物料类型名称
        materialEntity.setTypeName(dictService.getNameById(materialEntity.getType()));
        // 原换算系数 = 计量系数分子 / 计量系数分母
        if (materialEntity.getIsDoubleUnit()) {
            // 计量单位不能和第一单位一样
            if (materialEntity.getComp().equals(materialEntity.getUnit())) {
                throw new ResponseException(RespCodeEnum.MATERIAL_UNIT_NOT_EQUAL_MEASUREMENT_UNIT);
            }
            if (materialEntity.getUnitNumerator() <= 0 || materialEntity.getUnitDenominator() <= 0) {
                throw new ResponseException(RespCodeEnum.MATERIAL_UNIT_NUMERATOR_OR_DENOMINATOR_NOT_NULL);
            }
            Rational rational = new Rational(materialEntity.getUnitNumerator(), materialEntity.getUnitDenominator());
            materialEntity.setScaleFactor(rational.doubleValue());
        }
        // 新增物料（成品/半成品/原料）
        boolean insert = this.save(materialEntity);
        // 如调用到编码规则，则相对应的所有序列号加1
        if (Objects.nonNull(materialEntity.getNumberRuleId())) {
            ruleSeqService.updateSeqEntity(relatedMap, materialEntity.getNumberRuleId());
        }
        // 调用新增物料的方法时，推送消息
        messagePushToKafkaService.pushNewMessage(materialEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_ADD_MESSAGE);
        // 更新检测方案 @RequestBody(required = false)不能传null
        QualityInspectionSchemeEntity updateScheme = Objects.isNull(materialEntity.getQualityInspectionSchemeEntity()) ? new QualityInspectionSchemeEntity() : materialEntity.getQualityInspectionSchemeEntity();
        inspectionSchemeInterface.updateByMaterial(updateScheme, materialEntity.getId());
        // 刷新检验方式控制
        materialInspectMethodService.lambdaUpdate().eq(MaterialInspectMethodEntity::getMaterialCode, materialEntity.getCode()).remove();
        saveOrUpdateInspectSchemeMethod(materialEntity);
        // 刷新检验触发条件
        materialInspectTriggerConditionService.lambdaUpdate().eq(MaterialInspectTriggerConditionEntity::getMaterialCode, materialEntity.getCode()).remove();
        saveOrUpdateInspectTriggerCondition(materialEntity);
        // 检验是否开启物料检验配置，开启则绑定 对应类型的检验要求
        bindMaterialInspect(materialEntity.getCode());

        // 更新附件表
        List<AppendixEntity> appendixEntities = materialEntity.getAppendixEntities();
        if (!CollectionUtils.isEmpty(appendixEntities)) {
            for (AppendixEntity appendixEntity : appendixEntities) {
                appendixEntity.setRelateId(materialEntity.getId().toString());
                appendixEntity.setIsUsed(true);
                appendixEntity.setCreateUser(materialEntity.getCreateBy());
                appendixEntity.setCreateTime(new Date());
                appendixEntity.setRelateName(materialEntity.getName());
            }
            appendixService.saveBatch(materialEntity.getAppendixEntities());
        }
        // 绑定特征参数
        bindAuxiliaryAttr(materialEntity);
        // 保存工装参数
        materialProcessAssemblyService.save(materialEntity);
        // 绑定客户物料
        CustomerMaterialByMaterialDTO customerMaterialDTO = CustomerMaterialByMaterialDTO.builder()
                .materialCode(materialEntity.getCode())
                .customerMaterials(materialEntity.getCustomerMaterials())
                .build();
        customerService.refreshCustomerMaterialsByMaterial(customerMaterialDTO);
        // 绑定供应商物料
        SupplierMaterialByMaterialDTO supplierMaterialDTO = SupplierMaterialByMaterialDTO.builder()
                .materialId(materialEntity.getId())
                .supplierMaterials(materialEntity.getSupplierMaterials())
                .build();
        supplierService.refreshSupplierMaterialsByMaterial(supplierMaterialDTO);
        // 刷新缓存
        materialCache.noticeInstanceRefreshCache();
        return insert;
    }

    /**
     * 检验是否开启物料检验配置，开启则绑定 对应类型的检验要求
     */
    private void bindMaterialInspect(String materialCode) {
        FullPathCodeDTO dto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.MATERIAL_INSPECT_CONF).build();
        MaterialInspectConfDTO config = businessConfigService.getValueDto(dto, MaterialInspectConfDTO.class);
        if (config.getIsAutoBindIncomingInspect()) {
            // 如果原先存在值，则不覆盖，没值才绑定
            Long count = materialInspectMethodService.lambdaQuery().eq(MaterialInspectMethodEntity::getMaterialCode, materialCode).count();
            if (count > 0) {
                return;
            }
            materialInspectMethodService.save(
                    MaterialInspectMethodEntity.builder()
                            .materialCode(materialCode)
                            .inspectTypeCode(InspectionsSchemeTypeEnum.INCOMING_INSPECTION.getCode())
                            .inspectTypeName(InspectionsSchemeTypeEnum.INCOMING_INSPECTION.getName())
                            .inspectMethodCode(MaterialInspectMethodEnum.PURCHASE_INPUT_INCOMING_INSPECTION.getCode())
                            .inspectMethodName(MaterialInspectMethodEnum.PURCHASE_INPUT_INCOMING_INSPECTION.getName())
                            .build());
            materialInspectTriggerConditionService.save(
                    MaterialInspectTriggerConditionEntity.builder()
                            .materialCode(materialCode)
                            .inspectTypeCode(InspectionsSchemeTypeEnum.INCOMING_INSPECTION.getCode())
                            .inspectTypeName(InspectionsSchemeTypeEnum.INCOMING_INSPECTION.getName())
                            .inspectTriggerConditionCode(InspectTriggerConditionEnum.CHECK_BY_INCOMING.getCode())
                            .inspectTriggerConditionName(InspectTriggerConditionEnum.CHECK_BY_INCOMING.getName())
                            .build()
            );
        }
    }

    /**
     * 刷新校验方式控制
     */
    private void saveOrUpdateInspectSchemeMethod(MaterialEntity materialEntity) {
        if (materialEntity.getInspectMethodMap() == null) {
            return;
        }
        for (Map.Entry<String, List<MaterialInspectMethodEntity>> entry : materialEntity.getInspectMethodMap().entrySet()) {
            entry.getValue().forEach(
                    o -> {
                        // 可以不需要绑定检验方案，可由qms服务中的检验方案绑定物料
//                        List<QualityInspectionSchemeEntity> inspectionSchemeEntities = materialEntity.getQualityInspectionSchemeMap().get(o.getInspectTypeName());
//                        if (CollectionUtils.isEmpty(inspectionSchemeEntities)) {
//                            throw new ResponseException(RespCodeEnum.MATERIAL_NEED_BIND_INSPECTION_SCHEME_AND_METHOD);
//                        }
                        o.setId(null);
                        o.setMaterialCode(materialEntity.getCode());
                        o.setInspectTypeCode(InspectionsSchemeTypeEnum.getCodeByName(entry.getKey()));
                        o.setInspectTypeName(entry.getKey());
                        o.setInspectMethodName(MaterialInspectMethodEnum.getNameByCode(o.getInspectMethodCode()));
                    }
            );
            materialInspectMethodService.saveBatch(entry.getValue());
        }
    }

    /**
     * 刷新检验触发条件
     */
    private void saveOrUpdateInspectTriggerCondition(MaterialEntity materialEntity) {
        if (materialEntity.getInspectTriggerConditionMap() == null) {
            return;
        }
        for (Map.Entry<String, List<MaterialInspectTriggerConditionEntity>> entry : materialEntity.getInspectTriggerConditionMap().entrySet()) {
            entry.getValue().forEach(
                    o -> {
                        o.setId(null);
                        o.setMaterialCode(materialEntity.getCode());
                        o.setInspectTypeCode(InspectionsSchemeTypeEnum.getCodeByName(entry.getKey()));
                        o.setInspectTypeName(entry.getKey());
                        o.setInspectTriggerConditionName(InspectTriggerConditionEnum.getNameByCode(o.getInspectTriggerConditionCode()));
                    }
            );
            materialInspectTriggerConditionService.saveBatch(entry.getValue());
        }
    }

    /**
     * 绑定特征参数
     */
    private void bindAuxiliaryAttr(MaterialEntity materialEntity) {
        if (!CollectionUtils.isEmpty(materialEntity.getAuxiliaryAttrEntities())) {
            // 删除原绑定关系
            materialAuxiliaryAttrService.lambdaUpdate().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, materialEntity.getCode()).remove();
            for (MaterialAuxiliaryAttrEntity auxiliaryAttrEntity : materialEntity.getAuxiliaryAttrEntities()) {
                auxiliaryAttrEntity.setMaterialCode(materialEntity.getCode());
                auxiliaryAttrEntity.setCreateBy(materialEntity.getCreateBy());
                auxiliaryAttrEntity.setUpdateBy(materialEntity.getCreateBy());
                auxiliaryAttrEntity.setCreateTime(new Date());
                auxiliaryAttrEntity.setUpdateTime(new Date());
            }
            materialAuxiliaryAttrService.saveBatch(materialEntity.getAuxiliaryAttrEntities());
        }
    }

    /**
     * 新增生效物料
     *
     * @param entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReleasedEntity(MaterialEntity entity) {
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.MATERIAL.getCode())) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        saveEntity(entity);
        entity.setState(MaterialStateEnum.RELEASE.getCode());
        updateEntity(entity);
    }


    @Override
    public boolean updateEntity(MaterialEntity materialEntity) {
        MaterialEntity old = getById(materialEntity.getId());
        //若物料是生效状态，允许修改检测方案信息
        if (materialEntity.getState() == MaterialStateEnum.CREATE.getCode() || materialEntity.getState() == MaterialStateEnum.RELEASE.getCode()) {
            // 更新检测方案 @RequestBody(required = false)不能传null
            QualityInspectionSchemeEntity updateScheme = Objects.isNull(materialEntity.getQualityInspectionSchemeEntity()) ? new QualityInspectionSchemeEntity() : materialEntity.getQualityInspectionSchemeEntity();
            inspectionSchemeInterface.updateByMaterial(updateScheme, materialEntity.getId());
            // 刷新校验方式控制
            materialInspectMethodService.lambdaUpdate().eq(MaterialInspectMethodEntity::getMaterialCode, materialEntity.getCode()).remove();
            saveOrUpdateInspectSchemeMethod(materialEntity);
            // 刷新检验触发条件
            materialInspectTriggerConditionService.lambdaUpdate().eq(MaterialInspectTriggerConditionEntity::getMaterialCode, materialEntity.getCode()).remove();
            saveOrUpdateInspectTriggerCondition(materialEntity);
        }
        // 如果改为创建态，需清空审批相关字段数据
        if (materialEntity.getState() == MaterialStateEnum.CREATE.getCode()) {
//            materialEntity.setApprover(null);
            materialEntity.setActualApprover(null);
            materialEntity.setApprovalTime(null);
            materialEntity.setApprovalStatus(null);
            materialEntity.setApprovalSuggestion(null);
        }
        if(materialEntity.getState() == MaterialStateEnum.RELEASE.getCode() && !Objects.equals(old.getState(), materialEntity.getState()) ){
            materialEntity.setReleaseTime(new Date());
        }

        //  更改物料附件，如果传null则不更新，传空数组则是删除
        if (materialEntity.getAppendixEntities() != null) {
            appendixService.updateMaterialBatch(materialEntity);
        }
        //如果是驳回状态,修改数据后变更为待审核状态。此处只要是创建状态,状态改为待审批可覆盖这个需求
        if (materialEntity.getState() == MaterialStateEnum.CREATE.getCode()
                && approveConfigService.getConfigByCode(ApproveModuleEnum.MATERIAL.getCode())) {
            materialEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        // 获取物料类型名称
        materialEntity.setTypeName(dictService.getNameById(materialEntity.getType()));
        if (StringUtils.isBlank(materialEntity.getEditor())) {
            materialEntity.setEditor(materialEntity.getUpdateBy());
            materialEntity.setEditorName(sysUserService.getNicknameByUsername(materialEntity.getUpdateBy()));
        }
        // 原换算系数 = 计量系数分子 / 计量系数分母
        if (materialEntity.getIsDoubleUnit()) {
            // 计量单位不能和第一单位一样
            if (materialEntity.getComp().equals(materialEntity.getUnit())) {
                throw new ResponseException(RespCodeEnum.MATERIAL_UNIT_NOT_EQUAL_MEASUREMENT_UNIT);
            }
            if (materialEntity.getUnitNumerator() <= 0 || materialEntity.getUnitDenominator() <= 0) {
                throw new ResponseException(RespCodeEnum.MATERIAL_UNIT_NUMERATOR_OR_DENOMINATOR_NOT_NULL);
            }
            Rational rational = new Rational(materialEntity.getUnitNumerator(), materialEntity.getUnitDenominator());
            materialEntity.setScaleFactor(rational.doubleValue());
        }
        boolean update = this.updateById(materialEntity);
        // 调用更新物料的方法时，推送消息
        messagePushToKafkaService.pushNewMessage(materialEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.MATERIAL_UPDATE_MESSAGE);
        //标记上传文件
        uploadService.markUploadFile(old.getFileUrl(), materialEntity.getFileUrl(), materialEntity);
        //发布物料信息变更事件
        applicationContext.publishEvent(new MaterialEntityUpdateEvent(this, materialEntity));
        // 绑定特征参数
        bindAuxiliaryAttr(materialEntity);
        // 保存工装参数
        materialProcessAssemblyService.save(materialEntity);
        // 检验是否开启物料检验配置，开启则绑定 对应类型的检验要求
        bindMaterialInspect(materialEntity.getCode());
        // 刷新缓存
        if(!Objects.equals(old.getName(), materialEntity.getName()) || !Objects.equals(old.getCode(), materialEntity.getCode())) {
            materialCache.noticeInstanceRefreshCache();
        }
        return update;
    }

    /**
     * 判断是否存在过程中的数据（发放、完成、投入、挂起）
     *
     * @param code 编码
     */
    private void judgeRelateProcessData(String code) {
        // BOM
        if (materialMapper.getCountByProcessBomData(code) > 0) {
            throw new ResponseException(RespCodeEnum.HAS_BOM_PROCESS_DATA);
        }
        // 采购需求单
        MaterialEntityOpenSelectDTO materialEntityOpenSelectDTO = MaterialEntityOpenSelectDTO.builder().code(code).build();
        PurchaseRequestOpenSelectDTO selectDTO = PurchaseRequestOpenSelectDTO.builder().materialFields(materialEntityOpenSelectDTO).build();
        PageResult<PurchaseRequestEntity> requestInterfacePage = extPurchaseRequestInterface.getPage(selectDTO);
        if (!CollectionUtils.isEmpty(requestInterfacePage.getRecords())) {
            long purchaseRequestCount = requestInterfacePage.getRecords().stream().filter(o -> o.getState().equals(RequestStateEnum.RELEASED.getCode()) || o.getState().equals(RequestStateEnum.FINISHED.getCode())).count();
            if (purchaseRequestCount > 0) {
                throw new ResponseException(RespCodeEnum.HAS_REQUEST_PROCESS_DATA);
            }
        }

        MaterialEntityOpenSelectDTO materialEntitySelectDTO = MaterialEntityOpenSelectDTO.builder().code(code).build();
        PageResult<PurchaseEntity> pageResult = extPurchaseInterface.getPage(PurchaseOpenSelectDTO.builder().materialFields(materialEntitySelectDTO).build());
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            long purchaseCount = pageResult.getRecords().stream().filter(o -> o.getState().equals(PurchaseStateEnum.RELEASED.getCode()) || o.getState().equals(PurchaseStateEnum.FINISHED.getCode())).count();
            if (purchaseCount > 0) {
                throw new ResponseException(RespCodeEnum.HAS_PURCHASE_PROCESS_DATA);
            }
        }
        // 销售订单
        // 查询该物料对应生效、完成状态的销售订单数量
        String states = OrderStateEnum.RELEASED.getCode() + Constants.SEP + OrderStateEnum.FINISHED.getCode();
        CommMaterialEntitySelectDTO materialSelectDto = new CommMaterialEntitySelectDTO();
        materialSelectDto.setCode(code);
        SaleOrderSelectOpenDTO build = SaleOrderSelectOpenDTO.builder().materialFields(materialSelectDto).states(states).showType(OrderShowTypeEnum.ORDER.getType()).build();
        PageResult<SaleOrderVO> responsePage = extSaleOrderInterface.getPage(build);
        if (!CollectionUtils.isEmpty(responsePage.getRecords())) {
            throw new ResponseException(RespCodeEnum.HAS_ORDER_PROCESS_DATA);
        }
        // 领料单
        if (materialMapper.getCountByProcessTakeOutApplicationData(code) > 0) {
            throw new ResponseException(RespCodeEnum.HAS_TAKE_OUT_APPLICATION_PROCESS_DATA);
        }
        // 出入库相关
        List<StockInAndOutEntity> inAndOutEntities = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, null), StockInAndOutEntity.class);
        if (inAndOutEntities.size() > 0) {
            throw new ResponseException(RespCodeEnum.HAS_TAKE_IN_AND_OUT_PROCESS_DATA);
        }
        // 工单
        LambdaQueryWrapper<WorkOrderEntity> workOrderQW = new LambdaQueryWrapper<>();
        workOrderQW.eq(WorkOrderEntity::getMaterialCode, code);
        workOrderQW.and(wrapper -> wrapper.eq(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode()).or()
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode()).or()
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.HANG_UP.getCode()).or()
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode()));
        if (workOrderMapper.selectCount(workOrderQW) > 0) {
            throw new ResponseException(RespCodeEnum.HAS_WORK_ORDER_PROCESS_DATA);
        }
    }

    @Override
    public MaterialEntity selectByMaterialCode(String code) {
        // 注意：这里不能使用Map缓存查询的MaterialEntity对象, 使用造成栈溢出(无限递归):
        // 因为Debugger会调用对象的toString()方法以显示对象中的所有数据信息。然而所有对象都使用lombok的@Data注解重写了toString()方法。
        // 问题就出在这里, 这意味着程序会不断地调用A类和B类的toString()方法,永不停止,结果就造成了StackOverFlow的问题。
        MaterialEntity materialEntity = this.getSimpleMaterialByCode(code);
        if (materialEntity == null) {
            return null;
        }
        // v1.12.1 根据物料Code获取工艺Id,提供前台附件查看跳转需要的参数
        CraftEntity craftByMaterialCode = craftService.getCraftByMaterialCode(code);
        materialEntity.setCraftId(craftByMaterialCode == null ? null : craftByMaterialCode.getCraftId());
        // 获取bom列表及bom绑定的原料信息
        getBomAndRawMaterial(code, materialEntity);
        // 查询检测方案
        List<QualityInspectionSchemeEntity> qualityInspectionSchemeEntities = JacksonUtil.getResponseArray(inspectionSchemeInterface.getInspectionSchemeList(materialEntity.getId()), QualityInspectionSchemeEntity.class);
        if (!CollectionUtils.isEmpty(qualityInspectionSchemeEntities)) {
            materialEntity.setQualityInspectionSchemeEntities(qualityInspectionSchemeEntities);
            Map<String, List<QualityInspectionSchemeEntity>> inspectSchemeMap = qualityInspectionSchemeEntities.stream().collect(Collectors.groupingBy(QualityInspectionSchemeEntity::getTypeName));
            materialEntity.setQualityInspectionSchemeMap(inspectSchemeMap);
        }
        // 查询检验方式控制
        List<MaterialInspectMethodEntity> inspectSchemeMethodEntities = materialInspectMethodService.lambdaQuery().eq(MaterialInspectMethodEntity::getMaterialCode, materialEntity.getCode()).list();
        Map<String, List<MaterialInspectMethodEntity>> inspectMethodMap = inspectSchemeMethodEntities.stream().collect(Collectors.groupingBy(MaterialInspectMethodEntity::getInspectTypeName));
        materialEntity.setInspectMethodMap(inspectMethodMap);
        // 查询检验触发条件
        List<MaterialInspectTriggerConditionEntity> triggerConditionEntities = materialInspectTriggerConditionService.lambdaQuery().eq(MaterialInspectTriggerConditionEntity::getMaterialCode, materialEntity.getCode()).list();
        Map<String, List<MaterialInspectTriggerConditionEntity>> triggerConditionMap = triggerConditionEntities.stream().collect(Collectors.groupingBy(MaterialInspectTriggerConditionEntity::getInspectTypeName));
        materialEntity.setInspectTriggerConditionMap(triggerConditionMap);

        //获取物料附件
        List<AppendixEntity> appendixEntities = appendixService.lambdaQuery()
                .eq(AppendixEntity::getRelateId, materialEntity.getId())
                .eq(AppendixEntity::getType, AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                .list();
        materialEntity.setAppendixEntities(appendixEntities);
        // 查询关联的特征参数
        materialEntity.setAuxiliaryAttrEntities(materialAuxiliaryAttrService.lambdaQuery().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, materialEntity.getCode()).list());
        // 获取中文名称
        showName(Stream.of(materialEntity).collect(Collectors.toList()));
        // 展示工艺信息
        showCraft(Stream.of(materialEntity).collect(Collectors.toList()));
        return materialEntity;
    }

    @Override
    public List<AppendixEntity> getAppendixList(String code) {
        MaterialEntity materialEntity = this.getSimpleMaterialByCode(code);
        if (materialEntity == null) {
            return new ArrayList<>();
        }
        return appendixService.lambdaQuery()
                .eq(AppendixEntity::getRelateId, materialEntity.getId())
                .eq(AppendixEntity::getType, AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                .list();
    }

    /**
     * 获取bom列表及bom绑定的原料信息
     */
    private void getBomAndRawMaterial(String code, MaterialEntity materialEntity) {
        LambdaQueryWrapper<BomEntity> bomWrapper = new LambdaQueryWrapper<>();
        bomWrapper.eq(BomEntity::getCode, code).eq(BomEntity::getIsTemplate, Constants.FALSE);
        List<BomEntity> bomEntityList = bomService.list(bomWrapper);
        for (BomEntity bomEntity : bomEntityList) {
            bomEntity.setStateName(BomStateEnum.getNameByCode(bomEntity.getState()));
            bomEntity.setSkuEntity(skuService.getById(bomEntity.getSkuId()));

            List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.getListByBomId(bomEntity.getId());

            for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
                //查询原料信息
                MaterialEntity material = this.getEntityByCodeAndSkuId(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId());
                if (material == null) {
                    log.error("数据错误，物料{} 的最新bom中子物料{}不存在系统中", code, bomRawMaterialEntity.getCode());
                    continue;
                }
                bomRawMaterialEntity.setMaterialId(material.getId());
                //前端展示物料所有仓库的总数量workorders/all/workorders/simple
                List<StockInventoryDetailEntity> allHouse = JacksonUtil.getResponseArray(stockInventoryDetailInterface.getList(StockInventorySelectDTO.builder()
                        .materialCode(material.getCode())
                        .build()
                ), StockInventoryDetailEntity.class);
                double total = allHouse.stream().mapToDouble(StockInventoryDetailEntity::getStockLocationQuantity).sum();
                bomRawMaterialEntity.setMaterialFields(material);
                bomRawMaterialEntity.setAllQuantity(total);
                bomRawMaterialEntity.setSkuEntity(skuService.getById(bomRawMaterialEntity.getSkuId()));
            }
            bomEntity.setBomRawMaterialEntities(bomRawMaterialEntities);
        }
        materialEntity.setBomEntities(bomEntityList);
    }

    @Override
    public MaterialEntity selectByCodeAndSkuId(String code, Integer skuId) {
        MaterialEntity materialEntity = this.selectByMaterialCode(code);
        if (materialEntity != null && skuId != null) {
            SkuEntity skuEntity = skuService.getById(skuId);
            materialEntity.setSkuEntity(skuEntity);
        }
        // 展示bom编码和bom版本信息
        showBom(Stream.of(materialEntity).collect(Collectors.toList()), skuId);
        return materialEntity;
    }

    @Override
    public MaterialEntity getSimpleMaterialByCode(String materialCode) {
        if (StringUtils.isBlank(materialCode)) {
            return null;
        }
        return this.lambdaQuery().eq(MaterialEntity::getCode, materialCode).one();
    }

    /**
     * 获取中文名称
     *
     * @param list 实体类
     */
    private void showName(List<MaterialEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> usernameSet = list.stream().map(MaterialEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toSet());
        usernameSet.addAll(list.stream().map(MaterialEntity::getUpdateBy).filter(Objects::nonNull).collect(Collectors.toSet()));
        usernameSet.addAll(list.stream().map(MaterialEntity::getApprover).filter(Objects::nonNull).collect(Collectors.toSet()));
        usernameSet.addAll(list.stream().map(MaterialEntity::getActualApprover).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String, String> nickNames = sysUserService.getUserNameNickMap(new ArrayList<>(usernameSet));
        Map<String, String> signatureUrlMap = sysUserService.getSignatureUrlMap(new ArrayList<>(usernameSet));

        List<String> materialCodes = list.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
        Map<String, List<MaterialAuxiliaryAttrEntity>> codeAttrs = materialAuxiliaryAttrService.lambdaQuery()
                .in(MaterialAuxiliaryAttrEntity::getMaterialCode, materialCodes)
                .list().stream().collect(Collectors.groupingBy(MaterialAuxiliaryAttrEntity::getMaterialCode));
        //获取包装方案
        List<String> packages = list.stream().map(MaterialEntity::getPackageSchemeCode).collect(Collectors.toList());
        List<PackageSchemeEntity> detailByCodes = packageSchemeService.getDetailByCodes(packages);
        Map<String, List<PackageSchemeEntity>> packageMaps = new HashMap<>();
        if (!CollectionUtils.isEmpty(detailByCodes)) {
            packageMaps = detailByCodes.stream().collect(Collectors.groupingBy(PackageSchemeEntity::getSchemeCode));
        }
        // 设置表单拓展字段名称
        Map<String, String> fieldRuleConfMap = formFieldRuleConfigService.getExtendFieldNameMap("material.list");
        // 获取物料属性清单
        Map<String, List<MaterialAttributeListEntity>> attributeListMap = materialAttributeListService.getList(materialCodes)
                .stream().collect(Collectors.groupingBy(MaterialAttributeListEntity::getMaterialCode));
        Map<String, MaterialProcessAssemblyEntity> processAssemblyMap = materialProcessAssemblyService.lambdaQuery().in(MaterialProcessAssemblyEntity::getMaterialCode, materialCodes).list()
                .stream().collect(Collectors.toMap(MaterialProcessAssemblyEntity::getMaterialCode, v -> v));
        // 判断是否存在附件
        List<Integer> materialIds = list.stream().map(MaterialEntity::getId).collect(Collectors.toList());
        List<Integer> uploadFiles = appendixService.lambdaQuery().eq(AppendixEntity::getType, AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                .in(AppendixEntity::getRelateId, materialIds)
                .list().stream()
                .map(AppendixEntity::getRelateId).distinct().map(Integer::valueOf).collect(Collectors.toList());
        for (MaterialEntity entity : list) {
            entity.setCreateByNickname(nickNames.get(entity.getCreateBy()));
            entity.setUpdateByName(nickNames.get(entity.getUpdateBy()));
            entity.setApproverName(nickNames.get(entity.getApprover()));
            entity.setActualApproverName(nickNames.get(entity.getActualApprover()));
            entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
            // 获取包装方案
            List<PackageSchemeEntity> packageSchemeEntities = packageMaps.getOrDefault(entity.getPackageSchemeCode(), new ArrayList<>());
            entity.setPackageSchemeEntities(packageSchemeEntities);
            // 获取物料属性清单
            List<MaterialAttributeListEntity> materialAttributeListEntities = attributeListMap.getOrDefault(entity.getCode(), new ArrayList<>());
            entity.setMaterialAttributeListEntities(materialAttributeListEntities);
            // 查询关联的特征参数
            entity.setAuxiliaryAttrEntities(codeAttrs.getOrDefault(entity.getCode(), new ArrayList<>()));
            MaterialEntity entity1 = JSONObject.parseObject(JSONObject.toJSONString(entity), MaterialEntity.class);
            entity.setMaterialFields(entity1);
            // 设置拓展字段中文名
            setMaterialExtendFieldName(fieldRuleConfMap, entity);
            // 获取二维码信息
            FullPathCodeDTO dto = FullPathCodeDTO.builder()
                    .fullPathCode(ConfigConstant.MATERIAL_QR_CODE_CONF).build();
            MaterialQRCodeConfDTO config = businessConfigService.getValueDto(dto, MaterialQRCodeConfDTO.class);
            String qrCode = config.getQrInfo().stream().map(o -> ColumnUtil.getFieldValue(entity, o)).collect(Collectors.joining(config.getSplitJoint()));
            entity.setQrCode(qrCode);
            // 工装参数
            entity.setProcessAssemblyEntity(processAssemblyMap.get(entity.getCode()));
            // 是否存在附件
            entity.setUploadFile(uploadFiles.contains(entity.getId()));
        }
        // 获取物料类型名称
        setMaterialTypeName(list);
    }


    /**
     * 设置拓展字段中文名
     */
    private void setMaterialExtendFieldName(Map<String, String> fieldRuleConfMap, MaterialEntity entity) {
        entity.setStandard(fieldRuleConfMap.getOrDefault("standard" + entity.getStandard(), entity.getStandard()));
        entity.setRemark(fieldRuleConfMap.getOrDefault("remark" + entity.getRemark(), entity.getRemark()));
        entity.setDrawingNumber(fieldRuleConfMap.getOrDefault("drawingNumber" + entity.getDrawingNumber(), entity.getDrawingNumber()));
//        entity.setMaterialPrice(Double.valueOf(fieldRuleConfMap.getOrDefault("materialPrice" + entity.getMaterialPrice(), String.valueOf(entity.getMaterialPrice()))));
//        entity.setLoseRate(Double.valueOf(fieldRuleConfMap.getOrDefault("loseRate" + entity.getLoseRate(), String.valueOf(entity.getLoseRate()))));
        entity.setRawMaterial(fieldRuleConfMap.getOrDefault("rawMaterial" + entity.getRawMaterial(), entity.getRawMaterial()));
        entity.setNameEnglish(fieldRuleConfMap.getOrDefault("nameEnglish" + entity.getNameEnglish(), entity.getNameEnglish()));
//        entity.setMinimumProductionLot(Double.valueOf(fieldRuleConfMap.getOrDefault("minimumProductionLot" + entity.getMinimumProductionLot(), String.valueOf(entity.getMinimumProductionLot()))));
        entity.setFactoryModel(fieldRuleConfMap.getOrDefault("factoryModel" + entity.getFactoryModel(), entity.getFactoryModel()));
        entity.setCustomFieldOneName(fieldRuleConfMap.getOrDefault("customFieldOneName" + entity.getCustomFieldOne(), entity.getCustomFieldOne()));
        entity.setCustomFieldTwoName(fieldRuleConfMap.getOrDefault("customFieldTwoName" + entity.getCustomFieldTwo(), entity.getCustomFieldTwo()));
        entity.setCustomFieldThreeName(fieldRuleConfMap.getOrDefault("customFieldThreeName" + entity.getCustomFieldThree(), entity.getCustomFieldThree()));
        entity.setCustomFieldFourName(fieldRuleConfMap.getOrDefault("customFieldFourName" + entity.getCustomFieldFour(), entity.getCustomFieldFour()));
        entity.setCustomFieldFiveName(fieldRuleConfMap.getOrDefault("customFieldFiveName" + entity.getCustomFieldFive(), entity.getCustomFieldFive()));
        entity.setCustomFieldSixName(fieldRuleConfMap.getOrDefault("customFieldSixName" + entity.getCustomFieldSix(), entity.getCustomFieldSix()));
        entity.setCustomFieldSevenName(fieldRuleConfMap.getOrDefault("customFieldSevenName" + entity.getCustomFieldSeven(), entity.getCustomFieldSeven()));
        entity.setCustomFieldEightName(fieldRuleConfMap.getOrDefault("customFieldEightName" + entity.getCustomFieldEight(), entity.getCustomFieldEight()));
        entity.setCustomFieldNineName(fieldRuleConfMap.getOrDefault("customFieldNineName" + entity.getCustomFieldNine(), entity.getCustomFieldNine()));
        entity.setCustomFieldTenName(fieldRuleConfMap.getOrDefault("customFieldTenName" + entity.getCustomFieldTen(), entity.getCustomFieldTen()));
        entity.setCustomFieldElevenName(fieldRuleConfMap.getOrDefault("customFieldElevenName" + entity.getCustomFieldEleven(), entity.getCustomFieldEleven()));
    }

    /**
     * 物料展示是否有bom
     */
    private void showBom(List<MaterialEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> materialCodes = list.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
        Map<String, List<BomEntity>> map = bomService.lambdaQuery()
                .in(BomEntity::getCode, materialCodes)
                .eq(BomEntity::getState, BomStateEnum.RELEASED.getCode())
                .eq(BomEntity::getIsTemplate, false)
                .list().stream()
                .collect(Collectors.groupingBy(BomEntity::getCode));
        list.forEach(entity -> entity.setHaveBom(map.containsKey(entity.getCode())));
    }

    /**
     * 展示bom编码和bom版本信息
     */
    private void showBom(List<MaterialEntity> list, Integer skuId) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (MaterialEntity entity : list) {
            // 查询bom信息
            entity.setHaveBom(false);
            // 根据code和skuId精确查询bom信息
            BomEntity latestBom = bomService.getLatestSimpleBom(entity.getCode(), skuId);
            if (latestBom != null) {
                entity.setBomNum(latestBom.getBomNum());
                entity.setBomVersion(latestBom.getVersion());
                entity.setHaveBom(true);
            }
        }
    }

    /**
     * 展示工艺信息
     */
    private void showCraft(List<MaterialEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 查询是否存在生效态的工艺
        List<String> materialCodes = list.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
        Map<String, List<CraftEntity>> map = craftService.lambdaQuery()
                .in(CraftEntity::getMaterialCode, materialCodes)
                .eq(CraftEntity::getState, CraftStateEnum.RELEASED.getCode())
                .eq(CraftEntity::getIsTemplate, false)
                .list().stream()
                .collect(Collectors.groupingBy(CraftEntity::getMaterialCode));
        list.forEach(entity -> entity.setHaveCraft(map.containsKey(entity.getCode())));
    }

    /**
     * 设置物料当前库存数量
     */
    private void setMaterialStockQuantity(List<MaterialEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Integer skuId = Constants.SKU_ID_DEFAULT_VAL;
        List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
        list.forEach(o -> materialDetails.add(StockMaterialDetailEntity.builder().productCode(o.getCode()).skuId(skuId).build()));

        // 查询物料当前库存数量
        Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(stockInventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new TypeReference<Map<String, BigDecimal>>() {
        }, null);
        materialCodeStockMap = materialCodeStockMap == null ? new HashMap<>(16) : materialCodeStockMap;
        for (MaterialEntity materialEntity : list) {
            BigDecimal stockQuantity = materialCodeStockMap.get(ColumnUtil.getMaterialSku(materialEntity.getCode(), skuId));
            materialEntity.setStockQuantity(stockQuantity == null ? null : stockQuantity.doubleValue());
        }
    }

    /**
     * 判断物料编号是否重复
     *
     * @param
     * @return
     */
    private void notRepeat(MaterialEntity materialEntity) {
        LambdaQueryWrapper<MaterialEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(MaterialEntity::getCode, materialEntity.getCode());
        if (materialMapper.selectCount(qw) > 0) {
            throw new ResponseException(RespCodeEnum.MATERIAL_FAIL2SAME_CODE);
        }
    }

    /**
     * 通过物料Code获取物料信息
     *
     * @param codes 物料code
     * @return
     */
    @Override
    public List<MaterialEntity> getMaterialsByCodes(Collection<String> codes) {
        List<MaterialEntity> materialEntities = new ArrayList<>();
        if (CollectionUtils.isEmpty(codes)) {
            return materialEntities;
        }
        LambdaQueryWrapper<MaterialEntity> qw = new LambdaQueryWrapper<>();
        qw.in(MaterialEntity::getCode, codes);
        materialEntities = this.list(qw);
        //获取类型名称
        showName(materialEntities);
        return materialEntities;
    }

    /**
     * 设置物料类型名称
     */
    @Override
    public void setMaterialTypeName(List<MaterialEntity> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<Integer> types = list.stream().map(MaterialEntity::getType).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Integer, DictEntity> typeMap = new HashMap<>(16);
            if (!CollectionUtils.isEmpty(types)) {
                typeMap = dictService.listByIds(types).stream().collect(Collectors.toMap(DictEntity::getId, v -> v));
            }
            for (MaterialEntity entity : list) {
                // 获取分类名称
                entity.setSortName(MaterialSortEnum.getNameByCode(entity.getSort()));
                // 关联状态名
                entity.setStateName(MaterialStateEnum.getNameByCode(entity.getState()));
                // 展示审核状态名称
                entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
                DictEntity dictEntity = typeMap.get(entity.getType());
                if (dictEntity != null) {
                    entity.setTypeName(dictEntity.getName());
                    entity.setIsProcessAssembly(dictEntity.getIsProcessAssembly());
                }
            }
        }
    }


    @Override
    public List<MaterialRelateDataDTO> getRelateData(String code) {
        // 获取关联物料的bom列表
        List<MaterialRelateDataDTO> bomList = materialMapper.getRelateBomData(code);
        bomList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(BomStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        List<MaterialRelateDataDTO> list = new ArrayList<>(bomList);
        // 获取关联物料的采购需求单列表
        MaterialEntityOpenSelectDTO materialEntityOpenSelectDTO = MaterialEntityOpenSelectDTO.builder().code(code).build();
        PurchaseRequestOpenSelectDTO selectDTO = PurchaseRequestOpenSelectDTO.builder().materialFields(materialEntityOpenSelectDTO).build();
        PageResult<PurchaseRequestEntity> requestInterfacePage = extPurchaseRequestInterface.getPage(selectDTO);
        if (!CollectionUtils.isEmpty(requestInterfacePage.getRecords())) {
            requestInterfacePage.getRecords().forEach(purchaseRequestEntity ->
                    list.add(MaterialRelateDataDTO.builder().number(purchaseRequestEntity.getRequestNum()).state(purchaseRequestEntity.getState()).stateName(RequestStateEnum.getNameByCode(purchaseRequestEntity.getState())).type("采购需求单").build()));
        }
        MaterialEntityOpenSelectDTO materialEntitySelectDTO = MaterialEntityOpenSelectDTO.builder().code(code).build();
        PageResult<PurchaseEntity> purchasePageResult = extPurchaseInterface.getPage(PurchaseOpenSelectDTO.builder().materialFields(materialEntitySelectDTO).build());
        if (!CollectionUtils.isEmpty(purchasePageResult.getRecords())) {
            purchasePageResult.getRecords().forEach(purchaseEntity -> list.add(MaterialRelateDataDTO.builder().number(purchaseEntity.getPurchaseCode()).state(purchaseEntity.getState()).stateName(PurchaseStateEnum.getNameByCode(purchaseEntity.getState())).type("采购订单").build()));
        }
        // 获取关联物料的领料单
        List<MaterialRelateDataDTO> takeOutApplicationList = materialMapper.getRelateTakeOutApplicationData(code);
        takeOutApplicationList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(TakeOutApplicationStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeOutApplicationList);
        // 获取关联物料的来料检验清单
        List<IncomingInspectionEntity> incomingInspections = JacksonUtil.getResponseArray(incomingInspectionInterface.listIncomingInspectionByMaterialCode(code), IncomingInspectionEntity.class);
        if (!CollectionUtils.isEmpty(incomingInspections)) {
            for (IncomingInspectionEntity entity : incomingInspections) {
                list.add(MaterialRelateDataDTO.builder().number(entity.getOrderNumber()).state(entity.getState()).stateName(IncomingInspectionStateEnum.getNameByCode(entity.getState())).type("来料检验").build());
            }
        }
        /*入库*/
        // 获取关联物料的采购入库单
        List<StockInAndOutEntity> inAndOutPurchaseList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.INPUT_PURCHASE.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByPurchaseList = inAndOutPurchaseList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("采购入库").build()).collect(Collectors.toList());
        takeInAndOutDataByPurchaseList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(OrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByPurchaseList);
        // 获取关联物料的销售退库单
        List<StockInAndOutEntity> inAndOutSaleReturnList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.INPUT_SALE_RETURN.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataBySaleReturnList = inAndOutSaleReturnList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("销售退库").build()).collect(Collectors.toList());
        takeInAndOutDataBySaleReturnList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataBySaleReturnList);
        // 获取关联物料的生产入库单
        List<StockInAndOutEntity> inAndOutWorkOrderCompleteList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.INPUT_WORK_ORDER_COMPLETE.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByWorkOrderCompleteList = inAndOutWorkOrderCompleteList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("生产入库").build()).collect(Collectors.toList());
        takeInAndOutDataByWorkOrderCompleteList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByWorkOrderCompleteList);
        // 获取关联物料的生产退料入库单
        List<StockInAndOutEntity> inAndOutApplicationReturnList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.INPUT_WORK_ORDER_RETURN.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByApplicationReturnList = inAndOutApplicationReturnList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("生产退料入库").build()).collect(Collectors.toList());
        takeInAndOutDataByApplicationReturnList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByApplicationReturnList);
        // 获取关联物料的其它退库单
        List<StockInAndOutEntity> inAndOutDevelopReturnList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.INPUT_DEVELOP_RETURN.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByDevelopReturnList = inAndOutDevelopReturnList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("其它入库").build()).collect(Collectors.toList());
        takeInAndOutDataByDevelopReturnList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByDevelopReturnList);

        /*出库*/
        // 获取关联物料的交付出库单
        List<StockInAndOutEntity> inAndOutDeliveryList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.OUTPUT_DELIVERY.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByDeliveryList = inAndOutDeliveryList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("交付出库").build()).collect(Collectors.toList());
        takeInAndOutDataByDeliveryList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByDeliveryList);
        // 获取关联物料的生产领料出库单
        List<StockInAndOutEntity> inAndOutWorkOrderTakeOutList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.OUTPUT_WORK_ORDER_TAKE_OUT.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByWorkOrderTakeOutList = inAndOutWorkOrderTakeOutList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("生产领料出库").build()).collect(Collectors.toList());
        takeInAndOutDataByWorkOrderTakeOutList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByWorkOrderTakeOutList);
        // 获取关联物料的生产补料出库单
        List<StockInAndOutEntity> inAndOutWorkOrderSupplementList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.OUTPUT_WORK_ORDER_SUPPLEMENT.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByWorkOrderSupplementList = inAndOutWorkOrderSupplementList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("生产补料出库").build()).collect(Collectors.toList());
        takeInAndOutDataByWorkOrderSupplementList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByWorkOrderSupplementList);
        // 获取关联物料的其它领用单
        List<StockInAndOutEntity> inAndOutDevelopTakeOutList = JacksonUtil.getResponseArray(stockInAndOutInterface.listByMaterialCode(code, StockInputOrOutputTypeEnum.OUTPUT_DEVELOP_TAKE_OUT.getTypeCode()), StockInAndOutEntity.class);
        List<MaterialRelateDataDTO> takeInAndOutDataByDevelopTakeOutList = inAndOutDevelopTakeOutList.stream()
                .map(inAndOutEntity -> MaterialRelateDataDTO.builder().number(inAndOutEntity.getOrderNumber()).state(inAndOutEntity.getState()).type("其它出库").build()).collect(Collectors.toList());
        takeInAndOutDataByDevelopTakeOutList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(StockOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(takeInAndOutDataByDevelopTakeOutList);

        // 获取关联物料的销售订单
        CommMaterialEntitySelectDTO materialSelectDto = new CommMaterialEntitySelectDTO();
        materialSelectDto.setCode(code);
        SaleOrderSelectOpenDTO build = SaleOrderSelectOpenDTO.builder().materialFields(materialSelectDto).showType(OrderShowTypeEnum.ORDER.getType()).build();
        PageResult<SaleOrderVO> responsePage = extSaleOrderInterface.getPage(build);

        if (!CollectionUtils.isEmpty(responsePage.getRecords())) {
            List<MaterialRelateDataDTO> orderList = responsePage.getRecords().stream()
                    .map(sale -> MaterialRelateDataDTO.builder().number(sale.getSaleOrderNumber()).state(sale.getState()).type("销售订单").build()).collect(Collectors.toList());
            orderList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(OrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
            list.addAll(orderList);
        }
        // 获取关联物料的工单
        List<MaterialRelateDataDTO> workOrderList = materialMapper.getRelateWorkOrderData(code);
        workOrderList.forEach(materialRelateDataDTO -> materialRelateDataDTO.setStateName(WorkOrderStateEnum.getNameByCode(materialRelateDataDTO.getState())));
        list.addAll(workOrderList);
        return list;
    }

    @Override
    public List<DictEntity> getMaterialType() {
        LambdaQueryWrapper<DictEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(DictEntity::getType, Constant.MATERIAL_TYPE);
        return dictService.list(qw);
    }

    @Override
    public DictEntity getMaterialTypeByName(String name) {
        return dictService.lambdaQuery()
                .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                .eq(DictEntity::getName, name).one();
    }

    /**
     * 获取系统单位
     *
     * @return
     */
    private List<String> getUnitList() {
        LambdaQueryWrapper<DictEntity> qw = new LambdaQueryWrapper<>();
        qw.select(DictEntity::getName)
                .eq(DictEntity::getType, DictTypeEnum.UNIT.getType());
        List<DictEntity> list = dictService.list(qw);
        return list.stream().map(DictEntity::getName).collect(Collectors.toList());
    }

    @Override
    public MaterialEntity getMaterialEntityByCode(String code) {
        MaterialEntity entity = this.getSimpleMaterialByCode(code);
        if (Objects.isNull(entity)) {
            return null;
        }
        DictEntity dictEntity = dictService.getById(entity.getType());
        if (dictEntity != null) {
            entity.setTypeName(dictEntity.getName());
        }
        entity.setStateName(MaterialStateEnum.getNameByCode(entity.getState()));
        entity.setSortName(MaterialSortEnum.getNameByCode(entity.getSort()));
        // 查询关联的特征参数
        entity.setAuxiliaryAttrEntities(materialAuxiliaryAttrService.lambdaQuery().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, entity.getCode()).list());
        return entity;
    }

    @Override
    public List<MaterialEntity> getMaterialEntityByCodes(Collection<String> codes) {
        List<MaterialEntity> materials = CollectionUtils.isEmpty(codes) ? Collections.emptyList() : this.lambdaQuery().in(MaterialEntity::getCode, codes).list();
        if (CollectionUtils.isEmpty(materials)) {
            return materials;
        }
        List<Integer> types = materials.stream().map(MaterialEntity::getType).distinct().collect(Collectors.toList());
        Map<Integer, String> typeNameMap = dictService.listByIds(types).stream().collect(Collectors.toMap(DictEntity::getId, DictEntity::getName));
        // 特征参数
        List<MaterialAuxiliaryAttrEntity> allMaterialAuxiliaryAttrs = materialAuxiliaryAttrService.lambdaQuery().in(MaterialAuxiliaryAttrEntity::getMaterialCode, codes).list();
        Map<String, List<MaterialAuxiliaryAttrEntity>> codeAuxiliaryAttrsMap = allMaterialAuxiliaryAttrs.stream().collect(Collectors.groupingBy(MaterialAuxiliaryAttrEntity::getMaterialCode));

        for (MaterialEntity material : materials) {
            material.setTypeName(typeNameMap.get(material.getType()));
            material.setAuxiliaryAttrEntities(codeAuxiliaryAttrsMap.getOrDefault(material.getCode(), Collections.emptyList()));
            // 其他的
            material.setStateName(MaterialStateEnum.getNameByCode(material.getState()));
            material.setSortName(MaterialSortEnum.getNameByCode(material.getSort()));
        }
        return materials;
    }

    @Override
    public MaterialEntity getEntityByCodeAndSkuIdAndShowBom(String code, Integer skuId) {
        MaterialEntity materialEntity = getEntityByCodeAndSkuId(code, skuId);
        if (Objects.isNull(materialEntity)) {
            return null;
        }
        // 展示bom编码和bom版本信息
        showBom(Stream.of(materialEntity).collect(Collectors.toList()), skuId);
        return materialEntity;
    }

    @Override
    public MaterialEntity getEntityByCodeAndSkuId(String code, Integer skuId) {
        MaterialEntity materialEntity = this.getMaterialEntityByCode(code);
        if (materialEntity != null) {
            if (skuId == null) {
                skuId = Constants.SKU_ID_DEFAULT_VAL;
            }
            SkuEntity skuEntity = skuService.getById(skuId);
            materialEntity.setSkuEntity(skuEntity);
        }
        return materialEntity;
    }

    @Override
    public List<MaterialEntity> listSimpleMaterialByCodesAndSkuIds(List<MaterialCodeAndSkuIdSelectDTO> originCodeAndSkuIds) {
        // 重写了equal, 用于去重, 避免重复操作
        List<MaterialCodeAndSkuIdSelectDTO> codeAndSkuIds = originCodeAndSkuIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codeAndSkuIds)) {
            return new ArrayList<>();
        }
        Set<String> codes = codeAndSkuIds.stream().map(MaterialCodeAndSkuIdSelectDTO::getMaterialCode).collect(Collectors.toSet());
        Set<Integer> skuIds = codeAndSkuIds.stream().map(MaterialCodeAndSkuIdSelectDTO::getSkuId).collect(Collectors.toSet());

        // 包含物料特征参数
        List<MaterialEntity> originMaterials = getMaterialEntityByCodes(codes);
        List<MaterialEntity> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(originMaterials) || CollectionUtils.isEmpty(skuIds)) {
            return result;
        }
        // 物料sku字段
        Map<Integer, SkuEntity> skuIdEntityMap = skuService.getSkusByIds(skuIds).stream().collect(Collectors.toMap(SkuEntity::getSkuId, v -> v));
        Map<String, MaterialEntity> codeMaterialMap = originMaterials.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        for (MaterialCodeAndSkuIdSelectDTO codeAndSkuId : codeAndSkuIds) {
            MaterialEntity originMaterial = codeMaterialMap.get(codeAndSkuId.getMaterialCode());
            if (Objects.isNull(originMaterial)) {
                continue;
            }
            // 拷贝一份
            MaterialEntity material = BeanUtil.copyProperties(originMaterial, MaterialEntity.class);
            Integer skuId = codeAndSkuId.getSkuId();
            material.setSkuEntity(skuIdEntityMap.get(skuId));
            // 展示bom编码和bom版本信息
            showBom(Stream.of(material).collect(Collectors.toList()), skuId);
            result.add(material);
        }
        // 展示名称
        showName(result);
        return result;
    }

    @Override
    public MaterialEntity getMaterialByCodeInCache(Map<String, MaterialEntity> materialMap, String code) {
        MaterialEntity materialEntity = materialMap.get(code);
        if (materialEntity == null) {
            materialEntity = this.getMaterialEntityByCode(code);
            materialMap.put(code, materialEntity);
        } else {
            materialEntity = materialMap.get(code);
        }
        return materialEntity;
    }

    @Override
    public MaterialEntity getMaterialBySkuInCache(Map<String, MaterialEntity> materialMap, String code, Integer skuId) {
        MaterialEntity materialEntity = materialMap.get(code);
        if (materialEntity == null) {
            materialEntity = this.getEntityByCodeAndSkuId(code, skuId);
            materialMap.put(code, materialEntity);
        }
        return materialEntity;
    }

    @Override
    public void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username) {
        MaterialEntity entity = this.getById(id);
        if (entity == null || entity.getState() == null) {
            return;
        }
        Integer preApprovalStatus = entity.getApprovalStatus();
        if (preApprovalStatus == null) {
            preApprovalStatus = entity.getState().equals(MaterialStateEnum.CREATE.getCode()) ?
                    ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.REJECTED.getCode();
        }
        boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();

        boolean toReject = approvalStatus == ApprovalStatusEnum.REJECTED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        if (toApproved || toReject) {
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(approvalSuggestion);
            entity.setActualApprover(username);
            entity.setApprovalTime(new Date());
            if (toApproved) {
                entity.setState(MaterialStateEnum.RELEASE.getCode());
            }
            this.updateById(entity);
            // 推送精制通知
            sendApproveMsg(entity);
        }
    }


    @Override
    public void approveBatch(ApproveBatchDTO dto) {
        List<Integer> ids = dto.getIds();
        Integer approvalStatus = dto.getApprovalStatus();
        if (CollectionUtils.isEmpty(ids) || approvalStatus == null) {
            return;
        }
        List<MaterialEntity> entities = this.listByIds(ids);
        List<String> approvers = entities.stream().map(MaterialEntity::getApprover).distinct().collect(Collectors.toList());
        approvers.remove(dto.getActualApprover());
        if (!CollectionUtils.isEmpty(approvers)) {
            throw new ResponseException(RespCodeEnum.CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN);
        }
        List<MaterialEntity> list = entities.stream().filter(o -> WorkOrderStateEnum.CREATED.getCode().equals(o.getState()))
                .peek(o -> {
                    if (o.getApprovalStatus() == null) {
                        o.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    }
                })
                .filter(o -> o.getApprovalStatus().equals(ApprovalStatusEnum.TO_BE_APPROVAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Date date = new Date();
        for (MaterialEntity entity : list) {
            Integer preApprovalStatus = entity.getApprovalStatus();
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(dto.getApprovalSuggestion());
            entity.setActualApprover(dto.getActualApprover());
            entity.setApprovalTime(date);

            boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                    && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
            if (toApproved) {
                entity.setState(MaterialStateEnum.RELEASE.getCode());
            }
            // 推送精制通知
            sendApproveMsg(entity);
        }
        this.updateBatchById(list);
    }

    private void sendApproveMsg(MaterialEntity entity) {
        NoticeConfigBuilder noticeConfigBuilder = NoticeConfigBuilder.builder()
                .sendUsername(entity.getActualApprover())
                .materialCode(entity.getCode())
                .materialName(entity.getName())
                .approvalTime(DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT))
                .approvalStateName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()))
                .noticeType(NoticeTypeEnum.APPROVE_MATERIAL_NOTICE.getCode())
                .redisKey(NoticeTypeEnum.APPROVE_MATERIAL_NOTICE.getCode() + entity.getCode() + DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT))
                .build();
        infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
    }


    @Override
    public Double getScaleFactorByCode(String materialCode) {
        LambdaQueryWrapper<MaterialEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(MaterialEntity::getUnitNumerator, MaterialEntity::getUnitDenominator)
                .eq(MaterialEntity::getCode, materialCode);
        MaterialEntity entity = this.getOne(wrapper);
        if (entity != null) {
            Rational rational = new Rational(entity.getUnitNumerator(), entity.getUnitDenominator());
            return rational.doubleValue();
        }
        return null;
    }

    @Override
    public PreviewTemplateEntity getFilesNameOrPath(MultipartFile file) throws Exception {
        //解压文件
        List<FileModel> unzip = ZipAndRarUtil.extractFile(file);
        if (CollectionUtils.isEmpty(unzip)) {
            return PreviewTemplateEntity.builder().build();
        }
        //判断文件是否包含一个excel和一个或多个pdf
        List<MaterialExcelImportDTO> materialExcelImportDTOList = filterMaterialFileList(unzip);
        //判断excel文件的内容是否为空
        if (CollectionUtils.isEmpty(materialExcelImportDTOList)) {
            return PreviewTemplateEntity.builder().build();
        }
        //根据excel文件生成的结果数量查找对应的文件
        //1.判断物料是否存在
        //2.过滤出①文件类型合法②文件名称匹配
        List<FileModel> filterFile;
        for (MaterialExcelImportDTO materialExcelImportDTO : materialExcelImportDTOList) {
            //查找该物料是否存在(物料太多，不能使用list查询，耗费性能)
            MaterialEntity materialEntity = this.getSimpleMaterialByCode(materialExcelImportDTO.getCode().trim());
            if (materialEntity == null) {
                //物料信息在系统内未找到
                materialExcelImportDTO.setReason(RespCodeEnum.MATERIAL_MESSAGE_NOT_FIND.getMsgDes());
                continue;
            }

            //查找物料对应的文件是否存在:fileModel.getFileName()是不是.pdf结尾而且文件名称是否一致(判断条件时，使用stream流进行过滤)
            filterFile = unzip.stream().filter(fileModel -> FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.PDF)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.MP4)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.TXT)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.MOV)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.JPG)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.PNG)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.WMV)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.RMVB))
                    .filter(fileModel -> cutFileSuffix(fileModel.getFileName()).equals(cutFileSuffix(materialExcelImportDTO.getFileName())))
                    .collect(Collectors.toList());
            if (filterFile.isEmpty()) {
                //物料信息未匹配上附件
                materialExcelImportDTO.setReason(RespCodeEnum.MATERIAL_MESSAGE_NOT_MATCH_FILE.getMsgDes());
            }
            // 检验是否存在重复导入的附件
            Long count = appendixService.lambdaQuery()
                    .eq(AppendixEntity::getType, AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                    .eq(AppendixEntity::getRelateId, materialEntity.getId())
                    .eq(AppendixEntity::getRelateName, materialExcelImportDTO.getName())
                    .eq(AppendixEntity::getFileName, materialExcelImportDTO.getFileName())
                    .count();
            if (count > 0) {
                materialExcelImportDTO.setReason(RespCodeEnum.MATERIAL_HAS_SAME_FILE_NAME.getMsgDes());
            }
        }


        return PreviewTemplateEntity.builder()
                .wrongResult(materialExcelImportDTOList.stream().filter(materialExcelImportDTO -> StringUtils.isNotBlank(materialExcelImportDTO.getReason()))
                        .collect(Collectors.toList()))
                .importResult(materialExcelImportDTOList.stream().filter(materialExcelImportDTO -> StringUtils.isBlank(materialExcelImportDTO.getReason()))
                        .collect(Collectors.toList()))
                .build();
    }

    /**
     * 截取文件名
     *
     * @param fileName
     * @return
     */
    private String cutFileSuffix(String fileName) {
        if (fileName.contains(Constants.SPOT)) {
            //以最后一个.的位置往前截取
            int i = fileName.lastIndexOf(".");
            return fileName.substring(0, i);
        }
        return fileName;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveMaterialImport(MultipartFile file, String username) throws Exception {
        //解压文件
        List<FileModel> unzip = ZipAndRarUtil.extractFile(file);
        List<MaterialExcelImportDTO> materialExcelImportDTOList = filterMaterialFileList(unzip);
        //判断excel文件的内容是否为空
        if (CollectionUtils.isEmpty(materialExcelImportDTOList)) {
            return false;
        }
        //根据excel文件生成的结果数量查找对应的文件
        List<FileModel> filterFile;
        for (MaterialExcelImportDTO materialExcelImportDTO : materialExcelImportDTOList) {
            //查找该物料是否存在
            if (this.getSimpleMaterialByCode(materialExcelImportDTO.getCode().trim()) == null) {
                continue;
            }
            //查找物料对应的文件是否存在
            filterFile = unzip.stream().filter(fileModel -> cutFileSuffix(fileModel.getFileName())
                    .equals(cutFileSuffix(materialExcelImportDTO.getFileName()))).collect(Collectors.toList());
            if (filterFile.isEmpty()) {
                continue;
            }
            //查找物料对应的文件是否存在:fileModel.getFileName()是不是.pdf结尾而且文件名称是否一致
            filterFile = unzip.stream().filter(fileModel -> FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.PDF)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.MP4)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.TXT)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.MOV)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.JPG)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.PNG)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.WMV)
                            || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.RMVB))
                    .filter(fileModel -> cutFileSuffix(fileModel.getFileName()).equals(cutFileSuffix(materialExcelImportDTO.getFileName())))
                    .collect(Collectors.toList());
            if (filterFile.isEmpty()) {
                //物料信息未匹配上附件
                continue;
            }
            //查询对应的物料对象
            QueryWrapper<MaterialEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(MaterialEntity::getCode, materialExcelImportDTO.getCode());
            MaterialEntity materialEntity = this.getOne(queryWrapper);
            // 查询附件表是否之前上传过相同的文件
            AppendixEntity appendixEntity = appendixService.get(materialExcelImportDTO.getFileName(), materialEntity.getId(), AppendixTypeEnum.MATERIAL_APPENDIX.getCode());
            UploadEntity uploadEntity = fastDfsClientService.uploadFile(filterFile.get(0).getFile(), username);
            if (appendixEntity != null) {
                if (uploadEntity != null) {
                    appendixEntity.setFilePath(uploadEntity.getUrl());
                    appendixEntity.setUpdateUser(username);
                    appendixEntity.setUpdateTime(new Date());
                    appendixService.updateById(appendixEntity);
                }
            } else {
                // 获取上传文件信息
                AppendixEntity entity = AppendixEntity.builder()
                        .createTime(new Date())
                        .createUser(username)
                        .isUsed(true)
                        .fileName(materialExcelImportDTO.getFileName())
                        .filePath(uploadEntity.getUrl())
                        .fileSize(uploadEntity.getSize())
                        .relateId(materialEntity.getId().toString())
                        .relateName(materialEntity.getName())
                        .type(AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                        .build();
                appendixService.save(entity);
            }
        }
        kafkaWebSocketPublisher.sendMessage(TopicEnum.MATERIAL_APPENDIX_TOPIC.getTopic(), MessageContent.builder()
                .time(new Date())
                .message("物料附件批量导入, 请知悉")
                .build());
        return true;
    }

    /**
     * 解析导入的文件夹 过滤不可导入的文件
     *
     * @param unzip
     * @return
     */
    private List<MaterialExcelImportDTO> filterMaterialFileList(List<FileModel> unzip) {
        //判断文件是否包含一个excel和一个或多个pdf
        List<FileModel> excelList = unzip.stream().filter(fileModel -> ExcelUtil.isExcelFile(fileModel.getFileName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(excelList)) {
            throw new ResponseException(RespCodeEnum.IMPORT_FILE_IS_NULL);
        }
        List<FileModel> excelLists = unzip.stream().filter(fileModel -> FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.PDF)
                || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.MP4)
                || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.TXT)
                || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.MOV)
                || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.JPG)
                || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.PNG)
                || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.WMV)
                || FileUtil.getSuffixLowercase(fileModel.getFileName()).endsWith(Constants.RMVB)
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(excelLists)) {
            throw new ResponseException(RespCodeEnum.IMPORT_FILE_AT_LEAST_ONE_PDF);
        }
        //导入的文件目录表格只能有一个
        if (excelList.size() != 1) {
            throw new ResponseException(RespCodeEnum.IMPORT_FILE_AT_LEAST_ONE_SHEET);
        }
        FileModel excelFile = excelList.get(0);
        //读取excel文件的内容(返回不为空的和去重后的数据)
        List<MaterialExcelImportDTO> materialExcelImportDTOList = EasyExcelUtil.analysisExcel(excelFile.getFile(), MaterialExcelImportDTO.class);
        if (!CollectionUtils.isEmpty(materialExcelImportDTOList)) {
            return materialExcelImportDTOList.stream().filter(o -> StringUtils.isNotBlank(o.getCode())
                    && StringUtils.isNotBlank(o.getName())
                    && StringUtils.isNotBlank(o.getFileName())).distinct().collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public Set<MaterialEntity> getSemiProducts(String materialCode) {
        Set<MaterialEntity> set = new HashSet<>();
        MaterialEntity entity = this.getSimpleMaterialByCode(materialCode);
        if (!ObjectUtils.isEmpty(entity)) {
            set.add(entity);
        }
        // 油脂厂场景：bom下边只有一层半成品，只需要一层循环
        // 根据工单material_code获取生效状态的bom
        List<BomEntity> bomEntityList = bomService.getListByMaterialCode(materialCode);
        if (!CollectionUtils.isEmpty(bomEntityList)) {
            List<Integer> ids = bomEntityList.stream().map(BomEntity::getId).collect(Collectors.toList());
            // 根据关联表进行查询物料-原料关联表
            QueryWrapper<BomRawMaterialEntity> query = new QueryWrapper<>();
            query.lambda().in(BomRawMaterialEntity::getBomId, ids);
            List<BomRawMaterialEntity> bomRawMaterialEntity = bomRawMaterialService.list(query);
            if (!CollectionUtils.isEmpty(bomRawMaterialEntity)) {
                for (BomRawMaterialEntity rawMaterialEntity : bomRawMaterialEntity) {
                    // 根据code进行查询物料表判断是否是生产品，如果是生产品则是半成品取物料
                    MaterialEntity materialEntity = this.getSimpleMaterialByCode(rawMaterialEntity.getCode());
                    ;
                    set.add(materialEntity);
                }
            }
        }
        return set;
    }

    @Override
    public List<String> selectNameByProcedure(Integer procedureId) {
        //获取工单列表
        List<WorkOrderEntity> workOrderEntities = workOrderProcedureRelationService.getWorkOrderListByProcedureId(procedureId);
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return new ArrayList<>();
        }
        return getMaterialNameByWorkOrders(workOrderEntities);
    }

    private List<String> getMaterialNameByWorkOrders(List<WorkOrderEntity> workOrderEntities) {
        List<String> materialNameList = new ArrayList<>();
        List<String> codeCollect = workOrderEntities.stream().map(WorkOrderEntity::getMaterialCode).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(codeCollect)) {
            materialNameList = this.lambdaQuery()
                    .select(MaterialEntity::getName)
                    .in(MaterialEntity::getCode, codeCollect)
                    .list().stream().map(MaterialEntity::getName).distinct().collect(Collectors.toList());
        }
        return materialNameList;
    }

    @Override
    public List<String> selectCodeByProcedure(Integer procedureId) {
        //获取工单列表
        List<WorkOrderEntity> workOrderEntities = workOrderProcedureRelationService.getWorkOrderListByProcedureId(procedureId);
        if (workOrderEntities != null) {
            return workOrderEntities.stream().map(WorkOrderEntity::getMaterialCode).distinct().collect(Collectors.toList());
        } else {
            return null;
        }
    }

    /**
     * 校验数据,返回可以操作的数据
     */
    private List<MaterialImportDTO> verifyFormat(List<MaterialImportDTO> imports) {
        log.info("校验数据：{}", JSONObject.toJSONString(imports));
        // 查询所有物料类型
        List<DictEntity> allMaterialTypes = getMaterialType();
        // 系统中所有单位
        List<String> allUnitList = getUnitList();
        // 是否开启审批功能
        // 如果需要审批，则缺省为待审核状态
        Boolean isApprove = approveConfigService.getConfigByCode(ApproveModuleEnum.MATERIAL.getCode());
        Set<String> editors = imports.stream().map(MaterialImportDTO::getEditor).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, String> userNameNickMap = new HashMap<>(4);
        if (!CollectionUtils.isEmpty(editors)) {
            userNameNickMap = sysUserService.getUserNameNickMap(new ArrayList<>(editors));
        }
        Map<Integer, String> qualityLevelMap = allMaterialTypes.stream().filter(o -> StringUtils.isNotBlank(o.getQualityLevel()))
                .collect(Collectors.toMap(DictEntity::getId, DictEntity::getQualityLevel));
        List<MaterialImportDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (MaterialImportDTO materialImportDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;
            if (StringUtils.isBlank(materialImportDTO.getTypeName())) {
                importResult.append("物料类型不能为空；");
                canImport = false;
            } else {
                List<DictEntity> materialTypes = allMaterialTypes.stream().filter(res -> materialImportDTO.getTypeName().equals(res.getName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(materialTypes)) {
                    importResult.append("物料类型必须在系统中存在；");
                    canImport = false;
                } else if (materialTypes.size() >= 2) {
                    importResult.append("系统中").append(materialImportDTO.getTypeName()).append("物料类型重复；");
                    canImport = false;
                } else {
                    materialImportDTO.setType(materialTypes.get(0).getId());
                    materialImportDTO.setTypeCode(materialTypes.get(0).getCode());
                }
            }
            // 如果物料编码为空，则调用编码规则，如果存在人工输入部分，则报错
            if (StringUtils.isBlank(materialImportDTO.getCode())) {
                Map<String, String> numberMap = new HashMap<>(8);
                numberMap.put(RulePrefixEnum.MATERIAL_TYPE_CODE.getCode(), materialImportDTO.getTypeCode());
                Map<String, String> relatedMap = new HashMap<>(8);
                relatedMap.put(AutoIncrementConfigureTypeEnum.MATERIAL_TYPE_CODE.getCode(), materialImportDTO.getTypeCode());
                GenerateCodeDTO build = GenerateCodeDTO.builder()
                        .type("82")
                        .numberMap(numberMap)
                        .relatedMap(relatedMap)
                        .build();
                String materialCode = numberRuleService.generateCodeByType(build);
                if (StringUtils.isBlank(materialCode)) {
                    importResult.append("自动生成的物料编码为空，请检查编码规则配置(编码规则中必须存在自动生成序号)；");
                    canImport = false;
                }
                materialImportDTO.setCode(materialCode);
            } else {
//                if (!materialImportDTO.getCode().matches(Constant.CODE_REX)) {
//                    importResult.append("物料编码不合法；");
//                    canImport = false;
//                } else {
                    MaterialEntity one = this.lambdaQuery().eq(MaterialEntity::getCode, materialImportDTO.getCode()).one();
                    if (Objects.nonNull(one)) {
                        // 物料在系统中存在
                        if (Objects.isNull(materialImportDTO.getUpdateTime())) {
                            importResult.append("更新操作,更新时间不能填空；");
                            canImport = false;
                        }
                        if (MaterialStateEnum.CREATE.getCode() != one.getState()
                                && MaterialStateEnum.RELEASE.getCode() != one.getState()) {
                            importResult.append("更新操作,系统中的物料必须是创建状态或生效状态；");
                            canImport = false;
                        }
                        if (MaterialStateEnum.RELEASE.getCode() == one.getState()
                                && MaterialStateEnum.CREATE.getName().equals(materialImportDTO.getStateName())) {
                            // 创建态不能覆盖生效态
                            importResult.append("更新操作,创建状态不能覆盖生效状态；");
                            canImport = false;
                        }
                        Date temDate = Objects.isNull(one.getUpdateTime()) ? one.getCreateTime() : one.getUpdateTime();
                        if (Objects.nonNull(materialImportDTO.getUpdateTime())
                                && materialImportDTO.getUpdateTime().compareTo(temDate) < 0) {
                            importResult.append("更新操作,更新时间必须比系统中该物料的更新时间大；");
                            canImport = false;
                        }
                    }
                    //判断导入的数据是否重复
                    long count = canImports.stream()
                            .filter(res -> res.getCode().equals(materialImportDTO.getCode()))
                            .count();
                    if (count >= 1) {
                        importResult.append("该物料编码对应的记录不能重复导入；");
                        canImport = false;
                    }
                    if (Objects.isNull(one)) {
                        // 物料在系统中不存在,就做新增操作
                        // 新增操作, updateTime字段要为null, 后面是通过这个字段判断要新增哪些,更新哪些
                        materialImportDTO.setUpdateTime(null);
                    }
                }
//            }
            if (StringUtils.isBlank(materialImportDTO.getName())) {
                importResult.append("物料名称不能为空；");
                canImport = false;
            }

            if (StringUtils.isBlank(materialImportDTO.getStateName())) {
                importResult.append("状态不能为空；");
                canImport = false;
            } else {
                Integer state = MaterialStateEnum.getCodeByName(materialImportDTO.getStateName());
                if (Objects.isNull(state) || MaterialStateEnum.CREATE.getCode() != state && MaterialStateEnum.RELEASE.getCode() != state) {
                    importResult.append("状态只能为创建和生效状态；");
                    canImport = false;
                } else {
                    materialImportDTO.setState(state);
                }
                materialImportDTO.setIsApproval(isApprove);

                if (isApprove && Objects.nonNull(state) && Objects.isNull(materialImportDTO.getUpdateTime())
                        && MaterialStateEnum.CREATE.getCode() != state) {
                    importResult.append("创建操作,开启了审批功能,导入数据状态只能为创建状态；");
                    canImport = false;
                }
            }

            if (StringUtils.isBlank(materialImportDTO.getSortName())) {
                importResult.append("物料分类不能为空；");
                canImport = false;
            } else {
                String sortCode = MaterialSortEnum.getCodeByName(materialImportDTO.getSortName());
                if (Objects.isNull(sortCode)) {
                    importResult.append("物料分类只能是采购品/生产品/委外品；");
                    canImport = false;
                } else {
                    materialImportDTO.setSort(sortCode);
                }
            }

            if (StringUtils.isBlank(materialImportDTO.getComp())) {
                importResult.append("单位不能为空；");
                canImport = false;
            } else {
                if (!allUnitList.contains(materialImportDTO.getComp())) {
                    importResult.append("单位必须在系统中存在；");
                    canImport = false;
                }
            }

            if (StringUtils.isBlank(materialImportDTO.getIsDoubleUnitName())) {
                importResult.append("是否为双单位不能为空；");
                canImport = false;
            } else {
                if (materialImportDTO.getIsDoubleUnitName().equals(YES)) {
                    materialImportDTO.setIsDoubleUnit(true);
                } else if (materialImportDTO.getIsDoubleUnitName().equals(NO)) {
                    materialImportDTO.setIsDoubleUnit(false);
                } else {
                    importResult.append("是否为双单位只能填是或否；");
                    canImport = false;
                }
                if (materialImportDTO.getIsDoubleUnit()) {
                    // 双单位中，计量相关字段需要必填
                    if (Objects.isNull(materialImportDTO.getUnitNumerator())
                            || Objects.isNull(materialImportDTO.getUnitDenominator())
                            || StringUtils.isBlank(materialImportDTO.getUnit())
                            || !allUnitList.contains(materialImportDTO.getUnit())
                            || materialImportDTO.getUnit().equals(materialImportDTO.getComp())) {
                        importResult.append("是双单位,计量系数分子/分母和计量单位必填,且计量单位必须在系统存在,且不能和第一个单位相同");
                        canImport = false;
                    }
                    if (Objects.nonNull(materialImportDTO.getUnitNumerator()) && materialImportDTO.getUnitNumerator() <= 0) {
                        importResult.append("计量系数分子必须大于0;");
                        canImport = false;
                    }
                    if (Objects.nonNull(materialImportDTO.getUnitDenominator()) && materialImportDTO.getUnitDenominator() <= 0) {
                        importResult.append("计量系数分母必须大于0;");
                        canImport = false;
                    }
                }
            }

            if (StringUtils.isBlank(materialImportDTO.getIsBatchMagName())) {
                importResult.append("是否按批次管理不能为空；");
                canImport = false;
            } else {
                if (materialImportDTO.getIsBatchMagName().equals(YES)) {
                    materialImportDTO.setIsBatchMag(true);
                } else if (materialImportDTO.getIsBatchMagName().equals(NO)) {
                    materialImportDTO.setIsBatchMag(false);
                } else {
                    importResult.append("是否按批次管理只能填是或否；");
                    canImport = false;
                }
            }

//            if (StringUtils.isBlank(materialImportDTO.getIsAuxiliaryMaterialName())) {
//                importResult.append("是否辅料不能为空；");
//                canImport = false;
//            } else {
            if (StringUtils.isNotBlank(materialImportDTO.getIsAuxiliaryMaterialName())) {
                if (materialImportDTO.getIsAuxiliaryMaterialName().equals(YES)) {
                    materialImportDTO.setIsAuxiliaryMaterial(true);
                } else if (materialImportDTO.getIsAuxiliaryMaterialName().equals(NO)) {
                    materialImportDTO.setIsAuxiliaryMaterial(false);
                } else {
                    importResult.append("是否辅料只能填是或否；");
                    canImport = false;
                }
            }
//            }

            if (StringUtils.isBlank(materialImportDTO.getIsSupportCodeManageName())) {
                importResult.append("是否按流水码管理不能为空；");
                canImport = false;
            } else {
                if (materialImportDTO.getIsSupportCodeManageName().equals(YES)) {
                    materialImportDTO.setIsSupportCodeManage(true);
                } else if (materialImportDTO.getIsSupportCodeManageName().equals(NO)) {
                    materialImportDTO.setIsSupportCodeManage(false);
                } else {
                    importResult.append("是否按流水码管理只能填是或否；");
                    canImport = false;
                }
            }

            if (StringUtils.isNotBlank(materialImportDTO.getIsEnableShelfLifeName())) {
                if (materialImportDTO.getIsEnableShelfLifeName().equals(YES)) {
                    materialImportDTO.setIsEnableShelfLife(true);
                } else if (materialImportDTO.getIsEnableShelfLifeName().equals(NO)) {
                    materialImportDTO.setIsEnableShelfLife(false);
                } else {
                    importResult.append("是否启用保质期只能填是或否；");
                    canImport = false;
                }
            }
            if (StringUtils.isNotBlank(materialImportDTO.getShelfLifeUnitName())) {
                String code = ShelfLifeUnitEnum.getCodeByName(materialImportDTO.getShelfLifeUnitName());
                if (StringUtils.isBlank(code)) {
                    importResult.append("保质期单位只能为 年/个月/天/小时；");
                    canImport = false;
                } else {
                    materialImportDTO.setShelfLifeUnit(code);
                }
            }

            if (Objects.nonNull(materialImportDTO.getMaterialPrice()) && materialImportDTO.getMaterialPrice() < 0) {
                importResult.append("物料单价不能为负数；");
                canImport = false;
            }
            if (Objects.nonNull(materialImportDTO.getMinimumProductionLot()) && materialImportDTO.getMinimumProductionLot() < 0) {
                importResult.append("最小生产批量不能为负数；");
                canImport = false;
            }
            if (StringUtils.isNotBlank(materialImportDTO.getEditor()) && StringUtils.isBlank(userNameNickMap.get(materialImportDTO.getEditor()))) {
                importResult.append("编制人在系统中不存在；");
                canImport = false;
            } else {
                if (StringUtils.isNotBlank(materialImportDTO.getEditor())) {
                    materialImportDTO.setEditorName(userNameNickMap.get(materialImportDTO.getEditor()));
                }
            }

            // 质量等级未填时默认从物料类型上取
            if (StringUtils.isBlank(materialImportDTO.getQualityLevel())) {
                materialImportDTO.setQualityLevel(qualityLevelMap.get(materialImportDTO.getType()));
            }

            //物料特征参数
            if (StringUtils.isNotBlank(materialImportDTO.getMaterialAuxiliary())) {
                List<MaterialAuxiliaryAttrEntity> materialAuxiliaryAttrEntityList = new ArrayList<>();
                for (String materialAuxiliaryName : materialImportDTO.getMaterialAuxiliary().split(Constant.SEP)) {
                    AuxiliaryAttrEntity auxiliaryAttrEntity = auxiliaryAttrService.lambdaQuery().eq(AuxiliaryAttrEntity::getAuxiliaryAttrName, materialAuxiliaryName).last("limit 1").one();
                    if (auxiliaryAttrEntity == null) {
                        importResult.append("特征参数在系统中不存在；");
                        canImport = false;
                    } else {
                        MaterialAuxiliaryAttrEntity materialAuxiliaryAttrEntity = MaterialAuxiliaryAttrEntity.builder()
                                .auxiliaryAttrCode(auxiliaryAttrEntity.getAuxiliaryAttrCode())
                                .auxiliaryAttrName(auxiliaryAttrEntity.getAuxiliaryAttrName())
                                .build();
                        materialAuxiliaryAttrEntityList.add(materialAuxiliaryAttrEntity);
                    }
                }
                materialImportDTO.setMaterialAuxiliaryAttrEntityList(materialAuxiliaryAttrEntityList);
            }
            materialImportDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(materialImportDTO.getVerifyPass())) {
                canImports.add(materialImportDTO);
                materialImportDTO.setImportResult("数据校验通过");
            } else {
                materialImportDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    /**
     * 获取物料导入模板, 如果没有导入自定义模板,则取默认的
     */
    private InputStream getTransferTemplate() throws IOException {
        InputStream inputStream;
        byte[] download = uploadService.download(UploadFileCodeEnum.MATERIAL_TEMPLATE_TEMPLATE.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            inputStream = new ByteArrayInputStream(download);
        } else {
            //默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template/materialTemplate.xlsx");
            inputStream = resource.getInputStream();
        }
        return inputStream;
    }

    @Override
    public Double calCountByExtend(Double count, WorkOrderEntity workOrder) {
        MaterialEntity materialEntity = getSimpleMaterialByCode(workOrder.getMaterialCode());
        //物料扩展字段
        CommonExtendDTO commonExtendDTO = JSONObject.parseObject(materialEntity.getExtend(), CommonExtendDTO.class);
        if (commonExtendDTO != null && commonExtendDTO.getMakeUpQuantity() != null) {
            //总数=计数器×面板数
            log.info("工单：{}，投入计数器计数:{}，面数:{}", workOrder.getWorkOrderNumber(), count, commonExtendDTO.getMakeUpQuantity());
            count = count * commonExtendDTO.getMakeUpQuantity();
        }
        log.info("工单：{}，投入计数器计数总数:{}", workOrder.getWorkOrderNumber(), count);
        return count;
    }

    @Override
    public List<String> selectNameByWorkCenterId(Integer workCenterId) {
        LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderEntity::getWorkCenterId, workCenterId);
        List<WorkOrderEntity> workOrderEntities = workOrderMapper.selectList(queryWrapper);
        //获取工单列表
        return getMaterialNameByWorkOrders(workOrderEntities);
    }

    @Override
    public List<String> selectCodeByWorkCenterId(Integer workCenterId) {
        LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderEntity::getWorkCenterId, workCenterId);
        List<WorkOrderEntity> workOrderEntities = workOrderMapper.selectList(queryWrapper);
        if (workOrderEntities != null) {
            return workOrderEntities.stream().map(WorkOrderEntity::getMaterialCode).distinct().collect(Collectors.toList());
        } else {
            return null;
        }
    }

    @Override
    public List<MaterialEntity> getList(List<String> materialCodes, String materialStandard) {
        return this.lambdaQuery()
                .in(!CollectionUtils.isEmpty(materialCodes), MaterialEntity::getCode, materialCodes)
                .like(StringUtils.isNotBlank(materialStandard), MaterialEntity::getStandard, materialStandard)
                .list();
    }

    @Override
    public List<String> materialCodesByMaterialFields(MaterialEntitySelectDTO materialEntity) {
        if (Objects.isNull(materialEntity)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MaterialEntity> lambda = new LambdaQueryWrapper<>();
        lambda.select(MaterialEntity::getCode)
                .eq(materialEntity.getId() != null, MaterialEntity::getId, materialEntity.getId())
                .like(StringUtils.isNotEmpty(materialEntity.getStandard()), MaterialEntity::getStandard, materialEntity.getStandard())
                .like(StringUtils.isNotEmpty(materialEntity.getDrawingNumber()), MaterialEntity::getDrawingNumber, materialEntity.getDrawingNumber())
                .like(StringUtils.isNotEmpty(materialEntity.getRawMaterial()), MaterialEntity::getRawMaterial, materialEntity.getRawMaterial())
                .like(StringUtils.isNotEmpty(materialEntity.getNameEnglish()), MaterialEntity::getNameEnglish, materialEntity.getNameEnglish())
                .like(StringUtils.isNotEmpty(materialEntity.getRemark()), MaterialEntity::getRemark, materialEntity.getRemark())
                .like(StringUtils.isNotEmpty(materialEntity.getComp()), MaterialEntity::getComp, materialEntity.getComp())
                .like(StringUtils.isNotEmpty(materialEntity.getVersion()), MaterialEntity::getVersion, materialEntity.getVersion())
                .like(StringUtils.isNotEmpty(materialEntity.getFactoryModel()), MaterialEntity::getFactoryModel, materialEntity.getFactoryModel())
                .eq(!Objects.isNull(materialEntity.getLoseRate()), MaterialEntity::getLoseRate, materialEntity.getLoseRate())
                .eq(materialEntity.getType() != null, MaterialEntity::getType, materialEntity.getType())
                .eq(StringUtils.isNotEmpty(materialEntity.getSort()), MaterialEntity::getSort, materialEntity.getSort())
                .eq(materialEntity.getState() != null, MaterialEntity::getState, materialEntity.getState())
                .eq(materialEntity.getIsBatchMag() != null, MaterialEntity::getIsBatchMag, materialEntity.getIsBatchMag())
                .like(StringUtils.isNotBlank(materialEntity.getTypeName()), MaterialEntity::getTypeName, materialEntity.getTypeName())
                .eq(!Objects.isNull(materialEntity.getMaterialPrice()), MaterialEntity::getMaterialPrice, materialEntity.getMaterialPrice())
                .eq(!Objects.isNull(materialEntity.getHaveBom()), MaterialEntity::getHaveBom, materialEntity.getHaveBom())
                .eq(!Objects.isNull(materialEntity.getHaveCraft()), MaterialEntity::getHaveCraft, materialEntity.getHaveCraft())
                .eq(!Objects.isNull(materialEntity.getMinimumProductionLot()), MaterialEntity::getMinimumProductionLot, materialEntity.getMinimumProductionLot())
                .like(StringUtils.isNotEmpty(materialEntity.getUnit()), MaterialEntity::getUnit, escapeSpecialCharacters(materialEntity.getUnit()))
                .like(StringUtils.isNotEmpty(materialEntity.getLevel()), MaterialEntity::getLevel, materialEntity.getLevel())
                .like(StringUtils.isNotEmpty(materialEntity.getCreateBy()), MaterialEntity::getCreateBy, materialEntity.getCreateBy())
                // 物料拓展字段查询
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldOne()), MaterialEntity::getCustomFieldOne, materialEntity.getCustomFieldOne())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldTwo()), MaterialEntity::getCustomFieldTwo, materialEntity.getCustomFieldTwo())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldThree()), MaterialEntity::getCustomFieldThree, materialEntity.getCustomFieldThree())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldFour()), MaterialEntity::getCustomFieldFour, materialEntity.getCustomFieldFour())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldFive()), MaterialEntity::getCustomFieldFive, materialEntity.getCustomFieldFive())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldSix()), MaterialEntity::getCustomFieldSix, materialEntity.getCustomFieldSix())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldSeven()), MaterialEntity::getCustomFieldSeven, materialEntity.getCustomFieldSeven())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldEight()), MaterialEntity::getCustomFieldEight, materialEntity.getCustomFieldEight())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldNine()), MaterialEntity::getCustomFieldNine, materialEntity.getCustomFieldNine())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldTen()), MaterialEntity::getCustomFieldTen, materialEntity.getCustomFieldTen())
                .like(!ObjectUtils.isEmpty(materialEntity.getCustomFieldEleven()), MaterialEntity::getCustomFieldEleven, materialEntity.getCustomFieldEleven())
        ;
        if (StringUtils.isNotEmpty(materialEntity.getStates())) {
            List<Integer> states = Arrays.stream(materialEntity.getStates().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            lambda.in(MaterialEntity::getState, states);
        }
        if (StringUtils.isNotEmpty(materialEntity.getTypes())) {
            List<Integer> types = Arrays.stream(materialEntity.getTypes().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            lambda.in(MaterialEntity::getType, types);
        }
        if (StringUtils.isNotEmpty(materialEntity.getTypeNames())) {
            List<String> typeNames = Arrays.asList(materialEntity.getTypeNames().split(Constant.SEP));
            lambda.in(MaterialEntity::getTypeName, typeNames);
        }
        if (StringUtils.isNotEmpty(materialEntity.getSorts())) {
            List<String> sorts = Arrays.asList(materialEntity.getSorts().split(Constant.SEP));
            lambda.in(MaterialEntity::getSort, sorts);
        }
        if (StringUtils.isNotEmpty(materialEntity.getNames())) {
            List<String> names = Arrays.asList(materialEntity.getNames().split(Constant.SEP));
            lambda.in(MaterialEntity::getName, names);
        }
        List<String> codes = materialEntity.getCodeList();
        if (CollectionUtils.isEmpty(codes) && StringUtils.isNotEmpty(materialEntity.getCodes())) {
            codes = Arrays.asList(materialEntity.getCodes().split(Constant.SEP));
        }
        lambda.in(!CollectionUtils.isEmpty(codes), MaterialEntity::getCode, codes);
        MaterialQueryConfigDTO queryConf = materialExtendService.queryConf();
        // 禁用缓存
        if(materialCache.isDisableCache()) {
            lambda.like(StringUtils.isNotEmpty(materialEntity.getCode()) && queryConf.toCodeMatchMethod() == MatchMethodEnum.LIKE, MaterialEntity::getCode, materialEntity.getCode())
                    .likeRight(StringUtils.isNotEmpty(materialEntity.getCode()) && queryConf.toCodeMatchMethod() == MatchMethodEnum.LIKE_RIGHT, MaterialEntity::getCode, materialEntity.getCode())
                    .like(StringUtils.isNotEmpty(materialEntity.getName()) && queryConf.toNameMatchMethod() == MatchMethodEnum.LIKE, MaterialEntity::getName, materialEntity.getName())
                    .likeRight(StringUtils.isNotEmpty(materialEntity.getName()) && queryConf.toNameMatchMethod() == MatchMethodEnum.LIKE_RIGHT, MaterialEntity::getName, materialEntity.getName())
            ;
        } else {
            MaterialCacheSelectDTO cacheDTO = MaterialCacheSelectDTO.builder()
                    .likeCode(materialEntity.getCode())
                    .likeName(materialEntity.getName())
                    .queryConfig(queryConf)
                    .build();
            // 没有其他查询条件
            if(lambda.getExpression().getNormal().isEmpty()) {
                return materialCache.query(cacheDTO);
            }else {
                // 将缓存的物料编码与其他条件组合
                if(StringUtils.isNotEmpty(materialEntity.getCode()) || StringUtils.isNotEmpty(materialEntity.getName())) {
                    List<String> materialCodes = materialCache.query(cacheDTO);
                    if(CollectionUtils.isEmpty(materialCodes)) {
                        lambda.eq(MaterialEntity::getId, -1);
                    }else {
                        lambda.in(MaterialEntity::getCode, materialCodes);
                    }
                }
            }
        }
        return this.list(lambda).stream().map(MaterialEntity::getCode).collect(Collectors.toList());
    }

    /**
     * 参数情况：
     * 1、物料和特征参数sku都不传
     * 2、有传物料，查询物料：
     * 2.1、没结果，直接返回
     * 2.2、有结果：
     * 2.2.1、没传特征参数sku，直接返回
     * 2.2.2、有传特征参数sku，查询sku后返回
     * 3、没传物料，只传特征参数sku，查询sku后返回
     *
     * @param selectDTO
     * @return
     */
    @Override
    public MaterialSkuDTO codesAndSkuIdsByMaterialFields(MaterialSkuEntitySelectDTO selectDTO) {
        List<String> materialCodes = new ArrayList<>();
        List<Integer> skuIds = new ArrayList<>();
        MaterialSkuDTO skuDTO = MaterialSkuDTO.builder().materialCodes(materialCodes).skuIds(skuIds).build();
        // 1、物料和特征参数sku都不传
        if (selectDTO == null) {
            skuDTO.setUseConditions(false);
            return skuDTO;
        }
        MaterialEntitySelectDTO materialFields = selectDTO.getMaterialFields();

        //按物料字段查询物料表
        boolean useMaterialConditions = FieldUtil.objectIsNotNull(materialFields);
        boolean useSkuConditions = false;
        //判断特征参数sku是否有传查询条件
        List<MaterialSkuSelectDTO> auxiliaryAttrs = selectDTO.getMaterialSkus();
        Integer skuConditionSize = 0;
        if (!CollectionUtils.isEmpty(auxiliaryAttrs)) {
            for (MaterialSkuSelectDTO o : auxiliaryAttrs) {
                if (o != null && StringUtils.isNotBlank(o.getValueName())) {
                    skuConditionSize++;
                }
            }
        }
        if (skuConditionSize > 0) {
            useSkuConditions = true;
        }
        // 1、物料和特征参数sku都不传
        if (!useMaterialConditions && !useSkuConditions) {
            skuDTO.setUseConditions(false);
            return skuDTO;
        }
        // 2、有传物料
        if (useMaterialConditions) {
            materialCodes = this.materialCodesByMaterialFields(materialFields);
            skuDTO.setMaterialCodes(materialCodes);
            // 2.1、没结果，直接返回。2.2.1、有结果，没传特征参数sku，直接返回
            if (CollectionUtils.isEmpty(materialCodes) || !useSkuConditions) {
                skuDTO.setUseConditions(true);
                return skuDTO;
            }
        }
        // 2.2.2、物料有结果，有传特征参数sku，查询sku后返回。3、没传物料，只传特征参数sku，查询sku后返回
        List<MaterialAuxiliaryAttrSkuEntity> skuEntities = materialSkuService.selectSkuList(materialCodes, auxiliaryAttrs, skuConditionSize);
        materialCodes = skuEntities.stream().map(MaterialAuxiliaryAttrSkuEntity::getMaterialCode).distinct().collect(Collectors.toList());
        skuIds = skuEntities.stream().map(MaterialAuxiliaryAttrSkuEntity::getSkuId).distinct().collect(Collectors.toList());
        skuDTO.setMaterialCodes(materialCodes);
        skuDTO.setSkuIds(skuIds);
        skuDTO.setUseConditions(true);
        return skuDTO;
    }

    /**
     * 获取物料所对应的BOM物料
     *
     * @param materialCode
     * @return
     */
    @Override
    public List<MaterialEntity> getBomMaterialsByMaterialCode(String materialCode) {
        return this.baseMapper.getBomMaterialsByMaterialCode(materialCode, BomStateEnum.RELEASED.getCode());
    }

    @Override
    public PrintDTO print(MaterialsSelectDTO selectDTO) {
        List<PrintDTO.ParamDto> printDTOS = new ArrayList<>();
        selectDTO.setIsShowSimpleInfo(true);
        // 必填项如果未填，则直接返回，防止前端误传导致CPU飙高、以及未打印的数据显示已打印
        boolean pageParams = Objects.isNull(selectDTO.getCurrent()) || Objects.isNull(selectDTO.getSize());
        if (StringUtils.isBlank(selectDTO.getCode()) && pageParams) {
            throw new ResponseException(RespCodeEnum.PRINT_IS_REQUIRE);
        }
        List<MaterialEntity> materialList = getMaterialEntities(selectDTO);

        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, selectDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        LabelTypeConfigEntity labelTypeConfigEntity = labelTypeConfigService.detail(labelEntity.getCodeType());
        BarCodeAnalysisService barCodeAnalysisService = SpringUtil.getBean(BarCodeAnalysisService.class);
        // 每次打印，序号初始化为1
        int i = 1;
        // 获取打印模板, 替换打印模板里${ }的数据
        for (MaterialEntity materialEntity : materialList) {
            PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
            List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
            for (PrintDTO.ParamDto.Content content : printElements) {
                String data = content.getOptions().getTitle();
                if (StringUtils.isBlank(data)) {
                    continue;
                }
                // 替换占位符, 替换值为查询对象的某个属性值
                Matcher matcher = PATTERN.matcher(data);
                while (matcher.find()) {
                    String placeholder = matcher.group();
                    // 序号加1
                    if (placeholder.equals(LabelInfoEnum.SERIAL_NO.getPlaceholder())) {
                        data = String.valueOf(i);
                        i++;
                        continue;
                    }
                    CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                            .ruleType(labelEntity.getCodeType())
                            .relateType(labelTypeConfigEntity.getRelateType())
                            .materialCode(materialEntity.getCode())
                            .placeholder(placeholder)
                            .sliceDigits(content.getOptions().getSliceDigits())
                            .build();
                    // 判断该标签是否存在公式，如果存在运算公式，将对数据进行四则运算
                    // 如果公式为空，则直接进行真实值替换占位符的逻辑
                    String placeholderValue = barCodeAnalysisService.replacePlaceholder(content, build);
                    data = data.replace(placeholder, placeholderValue);
                }
                content.getOptions().setTitle(data);
                // 字体大小如果为空,则设置默认值
                if (content.getOptions().getFontSize() == null) {
                    content.getOptions().setFontSize(9.0);
                }
            }
            printDTOS.addAll(printDTO.getPanels());
        }
        PrintDTO printDTO = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTOS).build();
        PrintDTO dto = labelService.getPrintDtoByOpenApi(printDTO);
        if (Objects.nonNull(selectDTO.getIsPreview()) && selectDTO.getIsPreview()) {
            return dto;
        }
        // 标记为已打印
        List<String> printMaterialCodes = materialList.stream().map(MaterialEntity::getCode).collect(Collectors.toList());
        this.lambdaUpdate().in(MaterialEntity::getCode, printMaterialCodes).set(MaterialEntity::getIsPrint, true).update();
        // 记录打印的编号
        dto.setPrintCodes(StringUtils.join(printMaterialCodes, Constant.SEP));
        return dto;
    }

    @Override
    public Boolean batchUpdateState(BatchChangeStateDTO batchApprovalDTO, String username) {
        List<MaterialEntity> list = this.lambdaQuery().in(MaterialEntity::getId, batchApprovalDTO.getIds()).list();
        // 改状态时，要求状态都是一样的才能改
        long stateCount = list.stream().map(MaterialEntity::getState).distinct().count();
        if (stateCount > 1) {
            throw new ResponseException(RespCodeEnum.MUST_BE_IN_THE_SAME_STATE_TO_MODIFY);
        }
        Integer integer = list.stream().map(MaterialEntity::getState).distinct().collect(Collectors.toList()).get(0);
        if (Objects.equals(integer, batchApprovalDTO.getState())) {
            return true;
        }
        //开启审批后不可直接将创建状态改为生效
        Boolean responseObject = approveConfigService.getConfigByCode(ApproveModuleEnum.MATERIAL.getCode());
        if (MaterialStateEnum.CREATE.getCode() == integer && responseObject) {
            throw new ResponseException(RespCodeEnum.APPROVE_IS_USER_UPDATE_STATE_IS_EFFECT);
        }
        return this.lambdaUpdate().in(MaterialEntity::getId, batchApprovalDTO.getIds())
                .set(MaterialEntity::getState, batchApprovalDTO.getState())
                .set(MaterialEntity::getUpdateTime, new Date())
                .set(MaterialEntity::getUpdateBy, username)
                .set(Objects.equals(MaterialStateEnum.RELEASE.getCode(), integer), MaterialEntity::getReleaseTime, new Date())
                .update();
    }

    @Override
    public void judgeBeforePrint(MaterialsSelectDTO selectDTO) {
        long count;
        if (StringUtils.isNotBlank(selectDTO.getCode())) {
            List<String> materialCodes = Arrays.asList(selectDTO.getCode().split(Constant.SEP));
            count = this.lambdaQuery().in(MaterialEntity::getCode, materialCodes).eq(MaterialEntity::getIsPrint, true).count();
        } else {
            LambdaQueryWrapper<MaterialEntity> wrapper = conditionQuery(selectDTO);
            // 如果存在已打印的数据，抛出异常
            wrapper.eq(MaterialEntity::getIsPrint, true);
            count = this.count(wrapper);
        }
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.FOUND_PRINTED_DATA);
        }
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importMaterialInspectExcel(String filename, InputStream inputStream, String username) {
        final String lockKey = RedisKeyPrefix.MATERIAL_INSPECT_LOCK, importProgressKey = RedisKeyPrefix.MATERIAL_INSPECT_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第一个sheet,从第3行开始读的数据
            List<MaterialInspectExcelDTO> totalImportRecords = EasyExcelUtil.read(inputStream, MaterialInspectExcelDTO.class, 0, 2);
            //2、校验数据
            importProgressService.updateProgress(importProgressKey, "校验数据中...", 0.1, false, null);
            List<MaterialInspectExcelDTO> canImportRecords = verifyFormatMaterialInspect(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, MaterialInspectExcelDTO.class, username);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(MaterialInspectExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(filename)
                    .importType(ImportTypeEnum.MATERIAL_INSPECT_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.MATERIAL_INSPECT_IMPORT.getTypeName())
                    .createBy(username)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(username).build());
            int successCount = canImportRecords.size(), failCount = totalImportRecords.size() - canImportRecords.size();

            importProgressService.updateProgress(importProgressKey, "保存数据中...", 0.5, false, null);
            saveMaterialInspects(canImportRecords);
            // 完成
            importProgressService.updateProgress(importProgressKey, "成功" + successCount + "条，" + "失败" + failCount + "条。", 1.0, true, importUrl);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.warn("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    /**
     * 校验物料检验
     */
    private List<MaterialInspectExcelDTO> verifyFormatMaterialInspect(List<MaterialInspectExcelDTO> imports) {
        Map<String, String> inspectMethodMap = new HashMap<>();
        Map<String, String> inspectTriggerMap = new HashMap<>();
        for (MaterialInspectMethodEnum anEnum : MaterialInspectMethodEnum.values()) {
            inspectMethodMap.put(anEnum.getName(), anEnum.getCode());
        }
        for (InspectTriggerConditionEnum anEnum : InspectTriggerConditionEnum.values()) {
            inspectTriggerMap.put(anEnum.getName(), anEnum.getCode());
        }

        List<MaterialInspectExcelDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (MaterialInspectExcelDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            if (StringUtils.isBlank(importDTO.getMaterialCode())) {
                importResult.append("物料编号不能为空；");
                canImport = false;
            } else {
                MaterialEntity material = this.getSimpleMaterialByCode(importDTO.getMaterialCode());
                if (Objects.isNull(material)) {
                    importResult.append("物料编号必须在系统中存在；");
                    canImport = false;
                } else {
                    importDTO.setMaterialId(material.getId());
                }
            }

            if (StringUtils.isBlank(importDTO.getInspectTypeName())) {
                importResult.append("检验类型不能为空；");
                canImport = false;
            } else {
                Integer inspectType = InspectionsSchemeTypeEnum.getCodeByName(importDTO.getInspectTypeName());
                if (Objects.isNull(inspectType)) {
                    importResult.append("未找到对应的检验类型；");
                    canImport = false;
                }
            }

            // 获取质检方案
            if (StringUtils.isNotBlank(importDTO.getInspectSchemeNames())) {
                List<String> importInspectSchemeNames = Arrays.asList(importDTO.getInspectSchemeNames().split(Constant.SEP));
                List<QualityInspectionSchemeEntity> schemes = JacksonUtil.getResponseArray(inspectionSchemeInterface.getReleasedAndCreateSchemeList(importDTO.getInspectSchemeNames()), QualityInspectionSchemeEntity.class);
                if (importInspectSchemeNames.size() != schemes.size()) {
                    importResult.append("未找到对应的检验方案，请检查检验方案输入是否正确；");
                    canImport = false;
                } else {
                    importDTO.setInspectSchemeIds(schemes.stream().map(QualityInspectionSchemeEntity::getQualityInspectionSchemeId).collect(Collectors.toList()));
                }
                // 检验方案需要和检验类型匹配
                for (QualityInspectionSchemeEntity inspectionSchemeEntity : schemes) {
                    if (!inspectionSchemeEntity.getTypeName().equals(importDTO.getInspectTypeName())) {
                        importResult.append("检验方案(").append(inspectionSchemeEntity.getQualityInspectionSchemeName()).append(")的检验类型需要与导入的检验类型匹配；");
                        canImport = false;
                    }
                }
            }
            // 控制方式
            if (StringUtils.isNotBlank(importDTO.getInspectMethodNames())) {
                List<String> inspectMethodNames = Arrays.asList(importDTO.getInspectMethodNames().split(Constant.SEP));
                if (!inspectMethodMap.keySet().containsAll(inspectMethodNames)) {
                    importResult.append("未找到对应的检验方式；");
                    canImport = false;
                }
                // 如果不为空，则检验方案必须要有值
                if (StringUtils.isBlank(importDTO.getInspectSchemeNames())) {
                    importResult.append("如果校验方式不为空，则检验方案必填；");
                    canImport = false;
                }
                // 检验方式需要和检验类型匹配
                for (String inspectMethodName : inspectMethodNames) {
                    boolean exists = dictService.lambdaQuery().eq(DictEntity::getName, inspectMethodName)
                            .eq(DictEntity::getType, importDTO.getInspectTypeName()).exists();
                    if (!exists) {
                        importResult.append("检验方式(").append(inspectMethodName).append(")需要和检验类型匹配");
                        canImport = false;
                    }
                }
            }
            // 检验触发条件
            if (StringUtils.isNotBlank(importDTO.getInspectTriggerConditionNames())) {
                List<String> inspectTriggerConditionNames = Arrays.asList(importDTO.getInspectTriggerConditionNames().split(Constant.SEP));
                if (!inspectTriggerMap.keySet().containsAll(inspectTriggerConditionNames)) {
                    importResult.append("未找到对应的检验触发条件；");
                    canImport = false;
                }
                // 检验方式需要和检验类型匹配
                for (String inspectTriggerName : inspectTriggerConditionNames) {
                    boolean exists = dictService.lambdaQuery().eq(DictEntity::getName, inspectTriggerName)
                            .eq(DictEntity::getType, importDTO.getInspectTypeName()).exists();
                    if (!exists) {
                        importResult.append("检验触发条件(").append(inspectTriggerName).append(")需要和检验类型匹配");
                        canImport = false;
                    }
                }
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    /**
     * 保存物料检验数据
     */
    private void saveMaterialInspects(List<MaterialInspectExcelDTO> materialExcelDTOS) {
        Map<String, List<MaterialInspectExcelDTO>> inspectMap = materialExcelDTOS.stream().filter(o -> StringUtils.isNotBlank(o.getInspectSchemeNames())).collect(Collectors.groupingBy(MaterialInspectExcelDTO::getMaterialCode));
        for (Map.Entry<String, List<MaterialInspectExcelDTO>> entry : inspectMap.entrySet()) {
            Set<Integer> ids = new HashSet<>();
            for (MaterialInspectExcelDTO dto : entry.getValue()) {
                ids.addAll(dto.getInspectSchemeIds());
            }
            QualityInspectionSchemeEntity inspectionSchemeEntity = new QualityInspectionSchemeEntity();
            inspectionSchemeEntity.setQualityInspectionSchemeIds(new ArrayList<>(ids));
            inspectionSchemeInterface.updateByMaterial(inspectionSchemeEntity, entry.getValue().get(0).getMaterialId());
        }
        for (MaterialInspectExcelDTO dto : materialExcelDTOS) {
            // 刷新校验方式控制
            materialInspectMethodService.lambdaUpdate()
                    .eq(MaterialInspectMethodEntity::getMaterialCode, dto.getMaterialCode())
                    .eq(MaterialInspectMethodEntity::getInspectTypeName, dto.getInspectTypeName()).remove();
            if (StringUtils.isNotBlank(dto.getInspectMethodNames())) {
                String[] methodNames = dto.getInspectMethodNames().split(Constant.SEP);
                List<MaterialInspectMethodEntity> inspectSchemeMethodEntities = new ArrayList<>();
                for (String methodName : methodNames) {
                    inspectSchemeMethodEntities.add(MaterialInspectMethodEntity.builder()
                            .materialCode(dto.getMaterialCode())
                            .inspectTypeCode(InspectionsSchemeTypeEnum.getCodeByName(dto.getInspectTypeName()))
                            .inspectTypeName(dto.getInspectTypeName())
                            .inspectMethodCode(MaterialInspectMethodEnum.getCodeByName(methodName))
                            .inspectMethodName(methodName)
                            .build());
                }
                materialInspectMethodService.saveBatch(inspectSchemeMethodEntities);
            }
            // 刷新检验触发条件
            if (StringUtils.isNotBlank(dto.getInspectTriggerConditionNames())) {
                String[] triggerNames = dto.getInspectTriggerConditionNames().split(Constant.SEP);
                List<MaterialInspectTriggerConditionEntity> inspectTriggerConditionEntities = new ArrayList<>();
                for (String triggerName : triggerNames) {
                    inspectTriggerConditionEntities.add(MaterialInspectTriggerConditionEntity.builder()
                            .materialCode(dto.getMaterialCode())
                            .inspectTypeCode(InspectionsSchemeTypeEnum.getCodeByName(dto.getInspectTypeName()))
                            .inspectTypeName(dto.getInspectTypeName())
                            .inspectTriggerConditionCode(InspectTriggerConditionEnum.getCodeByName(triggerName))
                            .inspectTriggerConditionName(triggerName)
                            .build()
                    );
                }
                materialInspectTriggerConditionService.saveBatch(inspectTriggerConditionEntities);
            }
        }
    }

    /**
     * 获取物料列表
     */
    private List<MaterialEntity> getMaterialEntities(MaterialsSelectDTO selectDTO) {
        List<MaterialEntity> materialList;
        List<String> materialCodes;
        if (Objects.nonNull(selectDTO.getIsSelectAllPrint()) && selectDTO.getIsSelectAllPrint()) {
            Page<MaterialEntity> page = this.getList(selectDTO);
            materialList = page.getRecords();
        } else {
            materialCodes = Arrays.asList(selectDTO.getCode().split(Constant.SEP));
            materialList = this.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list();
        }
        if (CollectionUtils.isEmpty(materialList)) {
            throw new ResponseException(RespCodeEnum.PRINT_DATA_IS_LOSE);
        }
        return materialList;
    }


    @Override
    public void uploadCustomTemplate(MultipartFile file, String operationUsername) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        // 上传
        uploadService.uploadReferencedFile(UploadFileCodeEnum.MATERIAL_TEMPLATE_TEMPLATE.getCode(), operationUsername, file);
    }

    @Override
    public byte[] downloadImportTemplate() throws IOException {
        Workbook templateWorkbook = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        InputStream inputStream = null;
        try {
            inputStream = getTransferTemplate();
            templateWorkbook = WorkbookFactory.create(inputStream);
            //删除说明sheet和目标数据sheet
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } finally {
            ExcelUtil.closeWorkBook(templateWorkbook);
            IOUtils.closeQuietly(byteArrayOutputStream);
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public InputStream downloadCustomImportTemplate() throws IOException {
        byte[] download = uploadService.download(UploadFileCodeEnum.MATERIAL_TEMPLATE_TEMPLATE.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            return new ByteArrayInputStream(download);
        }
        throw new ResponseException("未配置自定义转换模板");
    }

    @Async
    @Override
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        final String lockKey = RedisKeyPrefix.MATERIAL_IMPORT_LOCK;
        importProgressKey = RedisKeyPrefix.MATERIAL_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        InputStream templateInputStream = null;
        File templateFile = null;
        try {
            //1、获取模板文件 ： 用户自定义/默认模板,转成文件
            templateInputStream = getTransferTemplate();
            // 创建一个空的临时文件
            templateFile = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!templateFile.getParentFile().exists()) {
                templateFile.getParentFile().mkdirs();
            }
            FileUtils.copyInputStreamToFile(templateInputStream, templateFile);
            //2、读取模板配置说明
//            ExcelTemplateSetDTO excelTemplateSetDTO = EasyExcelUtil.read(new FileInputStream(templateFile), ExcelTemplateSetDTO.class, 0, 3).get(0);
            ExcelTemplateSetDTO excelTemplateSetDTO = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ExcelTemplateSetDTO.class, 0, 3).get(0);
            //3、通过配置将数据转换并复制到模板原始数据
            List<MaterialImportDTO> totalImportRecords = ExcelUtil.executeDataToTarget(WorkbookFactory.create(inputStream), templateFile, excelTemplateSetDTO, MaterialImportDTO.class);
            //4、校验并转换数据
            List<MaterialImportDTO> canImportRecords = verifyFormat(totalImportRecords);
            // 需要更新的
            List<MaterialImportDTO> updateImportRecords = canImportRecords.stream().filter(res -> Objects.nonNull(res.getUpdateTime())).collect(Collectors.toList());
            // 需要新增的
            List<MaterialImportDTO> insertImportRecords = canImportRecords.stream().filter(res -> Objects.isNull(res.getUpdateTime())).collect(Collectors.toList());

            //5、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, MaterialImportDTO.class, operationUsername);
            //6、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(MaterialImportDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.MATERIAL_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.MATERIAL_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //7、保存数据
            int totalSize = totalImportRecords.size();
            int rowTotal = canImportRecords.size();
            int successCount = 0, failCount = totalSize - rowTotal;
            // 更新
            for (MaterialImportDTO updateImport : updateImportRecords) {
                importUpdate(updateImport, operationUsername);
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalSize, successCount, failCount);
            }
            // 新增
            for (MaterialImportDTO insertImport : insertImportRecords) {
                importInsert(insertImport, operationUsername);
                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalSize, successCount, failCount);
            }
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalSize, successCount, failCount);
            if(successCount > 0) {
                // 刷新缓存
                materialCache.noticeInstanceRefreshCache();
            }
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.error("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(templateInputStream);
            IOUtils.closeQuietly(inputStream);
            FileUtils.deleteQuietly(templateFile);
            importProgressService.releaseLock(lockKey);
        }
    }

    /**
     * 导出物料时的更新操作
     */
    private void importUpdate(MaterialImportDTO updateImport, String username) {
        // 创建状态:待审批, 生效状态: 已审批
        int approvalStatus = MaterialStateEnum.CREATE.getCode() == updateImport.getState() ? ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.APPROVED.getCode();
        // 原换算系数 = 计量系数分子 / 计量系数分母
        Double scaleFactor = null;
        if (updateImport.getIsDoubleUnit()) {
            scaleFactor = updateImport.getUnitNumerator() != null && updateImport.getUnitDenominator() != null ?
                    new Rational(updateImport.getUnitNumerator(), updateImport.getUnitDenominator()).doubleValue()
                    : updateImport.getScaleFactor();
        }

        this.lambdaUpdate()
                .eq(MaterialEntity::getCode, updateImport.getCode())
                .set(MaterialEntity::getNameEnglish, updateImport.getEnglishName())
                .set(MaterialEntity::getName, updateImport.getName())
                .set(MaterialEntity::getState, updateImport.getState())
                .set(MaterialEntity::getSort, updateImport.getSort())
                .set(MaterialEntity::getType, updateImport.getType())
                .set(MaterialEntity::getTypeName, updateImport.getTypeName())
                .set(MaterialEntity::getStandard, updateImport.getStandard())
                .set(MaterialEntity::getComp, updateImport.getComp())
                .set(MaterialEntity::getVersion, updateImport.getVersion())
                .set(MaterialEntity::getIsDoubleUnit, updateImport.getIsDoubleUnit())
                .set(MaterialEntity::getScaleFactor, Objects.nonNull(scaleFactor) ? scaleFactor : 1)
                .set(MaterialEntity::getUnitNumerator, updateImport.getIsDoubleUnit() ? updateImport.getUnitNumerator() : 1)
                .set(MaterialEntity::getUnitDenominator, updateImport.getIsDoubleUnit() ? updateImport.getUnitDenominator() : 1)
                .set(MaterialEntity::getUnit, updateImport.getIsDoubleUnit() ? updateImport.getUnit() : null)
                .set(MaterialEntity::getLevel, updateImport.getLevel())
                .set(MaterialEntity::getIsBatchMag, updateImport.getIsBatchMag())
                .set(MaterialEntity::getIsAuxiliaryMaterial, updateImport.getIsAuxiliaryMaterial())
                .set(MaterialEntity::getIsSupportCodeManage, updateImport.getIsSupportCodeManage())
                .set(MaterialEntity::getMaterialPrice, updateImport.getMaterialPrice())
                .set(MaterialEntity::getDrawingNumber, updateImport.getDrawingNumber())
                .set(MaterialEntity::getRawMaterial, updateImport.getRawMaterial())
                .set(MaterialEntity::getLoseRate, Objects.isNull(updateImport.getLoseRate()) ? 0 : updateImport.getLoseRate())
                .set(Objects.nonNull(updateImport.getMinimumProductionLot()), MaterialEntity::getMinimumProductionLot, updateImport.getMinimumProductionLot())
                .set(MaterialEntity::getFactoryModel, updateImport.getFactoryModel())
                .set(MaterialEntity::getRemark, updateImport.getRemark())
                .set(MaterialEntity::getUpdateTime, updateImport.getUpdateTime())
                .set(MaterialEntity::getUpdateBy, username)
                .set(MaterialEntity::getApprovalStatus, updateImport.getIsApproval() ? approvalStatus : null)
                .set(MaterialEntity::getApprover, updateImport.getIsApproval() ? username : null)
                .set(MaterialEntity::getActualApprover, updateImport.getIsApproval() && ApprovalStatusEnum.APPROVED.getCode() == approvalStatus ? username : null)
                .set(MaterialEntity::getApprovalTime, updateImport.getIsApproval() && ApprovalStatusEnum.APPROVED.getCode() == approvalStatus ? new Date() : null)
                .set(MaterialEntity::getCustomFieldOne, updateImport.getCustomFieldOne())
                .set(MaterialEntity::getCustomFieldTwo, updateImport.getCustomFieldTwo())
                .set(MaterialEntity::getCustomFieldThree, updateImport.getCustomFieldThree())
                .set(MaterialEntity::getCustomFieldFour, updateImport.getCustomFieldFour())
                .set(MaterialEntity::getCustomFieldFive, updateImport.getCustomFieldFive())
                .set(MaterialEntity::getCustomFieldSix, updateImport.getCustomFieldSix())
                .set(MaterialEntity::getCustomFieldSeven, updateImport.getCustomFieldSeven())
                .set(MaterialEntity::getCustomFieldEight, updateImport.getCustomFieldEight())
                .set(MaterialEntity::getCustomFieldNine, updateImport.getCustomFieldNine())
                .set(MaterialEntity::getCustomFieldTen, updateImport.getCustomFieldTen())
                .set(MaterialEntity::getCustomFieldEleven, updateImport.getCustomFieldEleven())
                .set(MaterialEntity::getEditor, updateImport.getEditor())
                .set(MaterialEntity::getEditorName, updateImport.getEditorName())
                .set(MaterialEntity::getQualityLevel, updateImport.getQualityLevel())
                .set(MaterialEntity::getIsEnableShelfLife, updateImport.getIsEnableShelfLife())
                .set(MaterialEntity::getShelfLifeTime, updateImport.getShelfLifeTime())
                .set(MaterialEntity::getShelfLifeUnit, updateImport.getShelfLifeUnit())
                .set(MaterialEntity::getStorageConditions, updateImport.getStorageConditions())
                .update();
        //特征参数重置
        MaterialEntity materialEntity = MaterialEntity.builder()
                .code(updateImport.getCode())
                .createBy(username)
                .updateBy(username)
                .auxiliaryAttrEntities(updateImport.getMaterialAuxiliaryAttrEntityList()).build();
        bindAuxiliaryAttr(materialEntity);
        MaterialProcessAssemblyEntity processAssemblyEntity = MaterialProcessAssemblyEntity.builder()
                .theoryLife(updateImport.getTheoryLife())
                .lifeUnit(updateImport.getLifeUnit())
                .lifeNoticeUp(updateImport.getLifeNoticeUp())
                .build();
        materialEntity.setProcessAssemblyEntity(processAssemblyEntity);
        // 保存工装参数
        materialProcessAssemblyService.save(materialEntity);
    }

    /**
     * 导入物料时的插入操作
     */
    private void importInsert(MaterialImportDTO insertImport, String username) {
        Date nowDate = new Date();
        // 创建状态:待审批, 生效状态: 已审批
        int approvalStatus = MaterialStateEnum.CREATE.getCode() == insertImport.getState() ? ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.APPROVED.getCode();
        // 原换算系数 = 计量系数分子 / 计量系数分母
        Double scaleFactor = null;
        if (insertImport.getIsDoubleUnit()) {
            scaleFactor = insertImport.getUnitNumerator() != null && insertImport.getUnitDenominator() != null ?
                    new Rational(insertImport.getUnitNumerator(), insertImport.getUnitDenominator()).doubleValue()
                    : insertImport.getScaleFactor();
        }

        this.save(MaterialEntity.builder()
                .code(insertImport.getCode())
                .nameEnglish(insertImport.getEnglishName())
                .name(insertImport.getName())
                .state(insertImport.getState())
                .sort(insertImport.getSort())
                .type(insertImport.getType())
                .typeName(insertImport.getTypeName())
                .standard(insertImport.getStandard())
                .comp(insertImport.getComp())
                .version(insertImport.getVersion())
                .isDoubleUnit(insertImport.getIsDoubleUnit())
                .scaleFactor(scaleFactor)
                .unitNumerator(insertImport.getIsDoubleUnit() ? insertImport.getUnitNumerator() : null)
                .unitDenominator(insertImport.getIsDoubleUnit() ? insertImport.getUnitDenominator() : null)
                .unit(insertImport.getIsDoubleUnit() ? insertImport.getUnit() : null)
                .level(insertImport.getLevel())
                .isBatchMag(insertImport.getIsBatchMag())
                .isAuxiliaryMaterial(insertImport.getIsAuxiliaryMaterial())
                .isSupportCodeManage(insertImport.getIsSupportCodeManage())
                .materialPrice(insertImport.getMaterialPrice())
                .drawingNumber(insertImport.getDrawingNumber())
                .rawMaterial(insertImport.getRawMaterial())
                .loseRate(Objects.isNull(insertImport.getLoseRate()) ? 0 : insertImport.getLoseRate())
                .factoryModel(insertImport.getFactoryModel())
                .remark(insertImport.getRemark())
                .minimumProductionLot(insertImport.getMinimumProductionLot())
                .createTime(Objects.nonNull(insertImport.getCreateTime()) ? insertImport.getCreateTime() : nowDate)
                .updateTime(nowDate)
                .createBy(username)
                .updateBy(username)
                .approvalStatus(insertImport.getIsApproval() ? approvalStatus : null)
                .approver(insertImport.getIsApproval() ? username : null)
                .actualApprover(insertImport.getIsApproval() && ApprovalStatusEnum.APPROVED.getCode() == approvalStatus ? username : null)
                .approvalTime(insertImport.getIsApproval() && ApprovalStatusEnum.APPROVED.getCode() == approvalStatus ? nowDate : null)
                .customFieldOne(insertImport.getCustomFieldOne())
                .customFieldTwo(insertImport.getCustomFieldTwo())
                .customFieldThree(insertImport.getCustomFieldThree())
                .customFieldFour(insertImport.getCustomFieldFour())
                .customFieldFive(insertImport.getCustomFieldFive())
                .customFieldSix(insertImport.getCustomFieldSix())
                .customFieldSeven(insertImport.getCustomFieldSeven())
                .customFieldEight(insertImport.getCustomFieldEight())
                .customFieldNine(insertImport.getCustomFieldNine())
                .customFieldTen(insertImport.getCustomFieldTen())
                .customFieldEleven(insertImport.getCustomFieldEleven())
                .editor(insertImport.getEditor())
                .editorName(insertImport.getEditorName())
                .qualityLevel(insertImport.getQualityLevel())
                .isEnableShelfLife(insertImport.getIsEnableShelfLife())
                .shelfLifeTime(insertImport.getShelfLifeTime())
                .shelfLifeUnit(insertImport.getShelfLifeUnit())
                .storageConditions(insertImport.getStorageConditions())
                .build());
        //特征参数重置
        MaterialEntity materialEntity = MaterialEntity.builder()
                .code(insertImport.getCode())
                .createBy(username)
                .updateBy(username)
                .auxiliaryAttrEntities(insertImport.getMaterialAuxiliaryAttrEntityList()).build();
        bindAuxiliaryAttr(materialEntity);
        MaterialProcessAssemblyEntity processAssemblyEntity = MaterialProcessAssemblyEntity.builder()
                .theoryLife(insertImport.getTheoryLife())
                .lifeUnit(insertImport.getLifeUnit())
                .lifeNoticeUp(insertImport.getLifeNoticeUp())
                .build();
        materialEntity.setProcessAssemblyEntity(processAssemblyEntity);
        // 保存工装参数
        materialProcessAssemblyService.save(materialEntity);
        // 检验是否开启物料检验配置，开启则绑定 对应类型的检验要求
        bindMaterialInspect(insertImport.getCode());
    }

    @Override
    public ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.MATERIAL_IMPORT_PROGRESS);
    }

    @Override
    public Map<String, String> getMaterialCodeNameMap(Collection<String> materialCodes) {
        List<MaterialEntity> material = CollUtil.isNotEmpty(materialCodes) ?
                lambdaQuery().select(MaterialEntity::getCode, MaterialEntity::getName).in(MaterialEntity::getCode, materialCodes).list()
                : Collections.emptyList();
        return material.stream().collect(Collectors.toMap(MaterialEntity::getCode, MaterialEntity::getName));
    }

    @Override
    public List<MaterialExcelDTO> convertToExportDTO(List<MaterialEntity> records) {
        List<MaterialExcelDTO> list = new ArrayList<>();
        for (MaterialEntity record : records) {
            MaterialExcelDTO excelDTO = MaterialExcelDTO.convertDTO(record);
            list.add(excelDTO);
        }
        return list;
    }

    @Override
    public void uploadListExportTemplate(MultipartFile file, String username) throws IOException {
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(
                file.getName(),
                file.getOriginalFilename(),
                file.getContentType(),
                EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile, ModelUploadFileEnum.MATERIAL_REPORT.getCode(), username);
    }

    @Override
    public ExcelTask taskById(Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.MATERIAL_REPORT.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        return records.get(0);
    }

    @Override
    @Async
    public void batchUpdateInspectDemand(MaterialBatchUpdateInspectDemandDTO updateDto) {
        // 给用户加锁，防止重复点击
        String progressKey = RedisKeyPrefix.MATERIAL_BATCH_UPDATE_INSPECT_PROGRESS + updateDto.getUsername();
        String lockKey = RedisKeyPrefix.MATERIAL_BATCH_UPDATE_INSPECT_LOCK + updateDto.getUsername();
        redisService.initProgress(progressKey, lockKey);
        try {
            List<MaterialEntity> materialEntities = this.listByIds(updateDto.getMaterialIds());
            int rowTotal = materialEntities.size();
            for (int i = 0; i < rowTotal; i++) {
                MaterialEntity materialEntity = materialEntities.get(i);
                // 更新检测方案
                QualityInspectionSchemeEntity updateScheme = Objects.isNull(updateDto.getQualityInspectionSchemeEntity()) ? new QualityInspectionSchemeEntity() : updateDto.getQualityInspectionSchemeEntity();
                inspectionSchemeInterface.updateByMaterial(updateScheme, materialEntity.getId());
                // 刷新校验方式控制
                materialEntity.setInspectMethodMap(updateDto.getInspectMethodMap());
                materialInspectMethodService.lambdaUpdate()
                        .eq(MaterialInspectMethodEntity::getMaterialCode, materialEntity.getCode())
                        .eq(MaterialInspectMethodEntity::getInspectTypeName, updateDto.getInspectTypeName())
                        .remove();
                saveOrUpdateInspectSchemeMethod(materialEntity);
                // 刷新检验触发条件
                materialEntity.setInspectTriggerConditionMap(updateDto.getInspectTriggerConditionMap());
                materialInspectTriggerConditionService.lambdaUpdate()
                        .eq(MaterialInspectTriggerConditionEntity::getMaterialCode, materialEntity.getCode())
                        .eq(MaterialInspectTriggerConditionEntity::getInspectTypeName, updateDto.getInspectTypeName())
                        .remove();
                saveOrUpdateInspectTriggerCondition(materialEntity);
                // 设置更新进度
                Double processPercent = MathUtil.divideDouble(i + 1, rowTotal, 2);
                boolean finish = processPercent.compareTo(1.0) == 0;
                ImportProgressDTO build = ImportProgressDTO.builder()
                        .code(Result.SUCCESS_CODE)
                        .progress(processPercent)
                        .executionDescription(finish ? "处理完成!" : "正在处理中，请耐心等候...")
                        .executionStatus(finish)
                        .importUrl(null)
                        .build();
                redisTemplate.opsForValue().set(progressKey, JSONObject.toJSONString(build), 2, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            CommonService commonService = SpringUtil.getBean(CommonService.class);
            ImportProgressDTO build = commonService.getImportProgressDTO(progressKey, e);
            redisTemplate.opsForValue().set(progressKey, JSONObject.toJSONString(build), 2, TimeUnit.HOURS);
            // 手动触发事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } finally {
            redisService.releaseLock(lockKey);
        }
    }

    private static final String MATERIAL_ID = "material_id";
    private static final String MATERIAL_CODE = "material_code";
    private static final String MATERIAL_CODE2 = "code";
    private static final String MATERIAL_CODE3 = "product_code";

    private List<DataDelSchema> getCheckSchemas() {
        return Arrays.asList(
                DataDelSchema.builder().tableName("dfs_bom").originColumnName(MATERIAL_CODE2).tableNickName("BOM").build(),
                DataDelSchema.builder().tableName("dfs_bom_raw_material").originColumnName(MATERIAL_CODE2).tableNickName("BOM物料清单").build(),
                DataDelSchema.builder().tableName("dfs_supplier_material").originColumnName(MATERIAL_ID).tableNickName("供应商物料清单").build(),
                DataDelSchema.builder().tableName("dfs_craft").originColumnName(MATERIAL_CODE).tableNickName("工艺").build(),
                DataDelSchema.builder().tableName("dfs_work_order").originColumnName(MATERIAL_CODE).tableNickName("工单").build(),
                DataDelSchema.builder().tableName("dfs_capacity").originColumnName(MATERIAL_ID).tableNickName("默认产能").build(),
                DataDelSchema.builder().tableName("dfs_bar_code").originColumnName(MATERIAL_CODE).tableNickName("批次").build(),
                DataDelSchema.builder().tableName("dfs_procedure_material").originColumnName(MATERIAL_CODE).tableNickName("工序用料").build(),
                DataDelSchema.builder().tableName("dfs_procedure_materials").originColumnName(MATERIAL_CODE).tableNickName("工序物料").build(),
                DataDelSchema.builder().tableName("dfs_customer_material_list").originColumnName(MATERIAL_CODE).tableNickName("客户物料清单").build(),
                DataDelSchema.builder().tableName("dfs_delivery_application_material").originColumnName(MATERIAL_CODE).tableNickName("出货申请单").build(),
                DataDelSchema.builder().tableName("dfs_feed_back_definition").originColumnName(MATERIAL_CODE).tableNickName("品质反馈定义").build(),
                DataDelSchema.builder().tableName("dfs_feed_record").originColumnName(MATERIAL_CODE).tableNickName("上料记录").build(),
                DataDelSchema.builder().tableName("dfs_formula_detail").originColumnName(MATERIAL_CODE).tableNickName("配方明细").build(),
                DataDelSchema.builder().tableName("dfs_maintain_material_record").originColumnName(MATERIAL_CODE).tableNickName("质检材料说明记录").build(),
                DataDelSchema.builder().tableName("dfs_material_attribute_list").originColumnName(MATERIAL_CODE).tableNickName("物料属性清单").build(),
                DataDelSchema.builder().tableName("dfs_material_auxiliary_attr_sku").originColumnName(MATERIAL_CODE).tableNickName("物料sku").build(),
                DataDelSchema.builder().tableName("dfs_material_inspect_method").originColumnName(MATERIAL_CODE).tableNickName("物料检验控制方式").build(),
                DataDelSchema.builder().tableName("dfs_material_inspect_trigger_condition").originColumnName(MATERIAL_CODE).tableNickName("物料检验控制触发条件").build(),
                DataDelSchema.builder().tableName("dfs_material_replace").originColumnName(MATERIAL_CODE).tableNickName("物料替换表").build(),
                DataDelSchema.builder().tableName("dfs_node_configure").originColumnName(MATERIAL_CODE).tableNickName("项目节点配置").build(),
                DataDelSchema.builder().tableName("dfs_node_process_report_record").originColumnName(MATERIAL_CODE).tableNickName("进度上报记录").build(),
                DataDelSchema.builder().tableName("dfs_package_level").originColumnName(MATERIAL_CODE).tableNickName("包装层级").build(),
                DataDelSchema.builder().tableName("dfs_package_record").originColumnName(MATERIAL_CODE).tableNickName("包装记录").build(),
                DataDelSchema.builder().tableName("dfs_package_scheme_material").originColumnName(MATERIAL_CODE).tableNickName("包装方案物料关联表").build(),
                DataDelSchema.builder().tableName("dfs_valuation_config").originColumnName(MATERIAL_CODE).tableNickName("工资配置").build(),
                DataDelSchema.builder().tableName("dfs_procedure_inspect_record").originColumnName(MATERIAL_CODE).tableNickName("工序检验记录").build(),
                DataDelSchema.builder().tableName("dfs_config_reverse_material").originColumnName(MATERIAL_CODE).tableNickName("倒排物料配置").build(),
                DataDelSchema.builder().tableName("dfs_config_schedule_reverse_material").originColumnName(MATERIAL_CODE).tableNickName("工单排程-倒排物料配置").build(),
                DataDelSchema.builder().tableName("dfs_record_device_resume_daily").originColumnName(MATERIAL_CODE).tableNickName("设备每日状态记录").build(),
                DataDelSchema.builder().tableName("dfs_product_flow_code").originColumnName(MATERIAL_CODE).tableNickName("流水码").build(),
                DataDelSchema.builder().tableName("dfs_work_order_material_list_material").originColumnName(MATERIAL_CODE).tableNickName("生产工单用料清单物料").build(),
                DataDelSchema.builder().tableName("dfs_work_order_material_check_material").originColumnName(MATERIAL_CODE).tableNickName("上料防错物料").build(),
                DataDelSchema.builder().tableName("dfs_package_order").originColumnName(MATERIAL_CODE).tableNickName("包装工单").build(),
                DataDelSchema.builder().tableName("dfs_procedure_def_material").originColumnName(MATERIAL_CODE).tableNickName("工序定义用料").build(),

                // AMS 的表
                DataDelSchema.builder().tableSchema("ams").tableName("dfs_sale_order_material").originColumnName(MATERIAL_CODE).tableNickName("ams.销售订单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("dfs_product_order_material").originColumnName(MATERIAL_CODE).tableNickName("ams.生产订单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_product_order_material_list_material").originColumnName(MATERIAL_CODE).tableNickName("ams.生产订单用料清单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_purchase_request_material").originColumnName(MATERIAL_CODE2).tableNickName("ams.采购需求单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_purchase_material").originColumnName(MATERIAL_CODE2).tableNickName("ams.采购单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_purchase_return_application_material").originColumnName(MATERIAL_CODE).tableNickName("ams.采购退料申请单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_purchase_return_material").originColumnName(MATERIAL_CODE).tableNickName("ams.采购退料单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_subcontract_order_material_line").originColumnName(MATERIAL_CODE).tableNickName("ams.委外订单用料清单行").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_subcontract_order_material_line_detail").originColumnName(MATERIAL_CODE).tableNickName("ams.委外订单用料清单明细").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_subcontract_order_detail").originColumnName(MATERIAL_CODE).tableNickName("ams.委外订单").build(),
                DataDelSchema.builder().tableSchema("ams").tableName("ams_subcontract_receipt_detail").originColumnName(MATERIAL_CODE).tableNickName("ams.委外订单收货单明细").build(),

                // QMS的表
                DataDelSchema.builder().tableSchema("qms").tableName("qms_inspection_sheet_material").originColumnName(MATERIAL_CODE).tableNickName("qms.产品检验单物料关联单").build(),
                DataDelSchema.builder().tableSchema("qms").tableName("qms_incoming_inspection_material").originColumnName(MATERIAL_CODE).tableNickName("qms.来料检验物料关联单").build(),
                DataDelSchema.builder().tableSchema("qms").tableName("qms_quality_inspection_scheme_material").originColumnName(MATERIAL_ID).tableNickName("qms.检验方案与物料关联").build(),

                // PMS的表
                DataDelSchema.builder().tableSchema("pms").tableName("pms_contract_material").originColumnName(MATERIAL_CODE).tableNickName("pms.合同和物料关系").build(),
                DataDelSchema.builder().tableSchema("pms").tableName("pms_project_material").originColumnName(MATERIAL_CODE).tableNickName("pms.项目物料").build(),
                DataDelSchema.builder().tableSchema("pms").tableName("pms_project_node").originColumnName(MATERIAL_CODE).tableNickName("pms.项目节点").build(),
                DataDelSchema.builder().tableSchema("pms").tableName("pms_project_product").originColumnName(MATERIAL_CODE).tableNickName("pms.项目产品").build(),

                // WMS的表
                DataDelSchema.builder().tableSchema("wms").tableName("wms_bar_code").originColumnName(MATERIAL_CODE).tableNickName("wms.批次表").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_material_bar_code").originColumnName(MATERIAL_CODE).tableNickName("wms.物料批次绑定").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_balance").originColumnName(MATERIAL_CODE).tableNickName("wms.库存结余").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_check_materiel").originColumnName(MATERIAL_CODE).tableNickName("wms.盘点明细").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_location_default_material").originColumnName(MATERIAL_CODE).tableNickName("wms.库位默认物料").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_warehouse_default_material").originColumnName(MATERIAL_CODE).tableNickName("wms.仓库默认物料").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_material_order_detail").originColumnName(MATERIAL_CODE).tableNickName("wms.领料申请单详情").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_sale_apply_detail").originColumnName(MATERIAL_CODE).tableNickName("wms.销售出货申请单订单物料信息").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_sale_material").originColumnName(MATERIAL_CODE2).tableNickName("wms.销售订单-物料").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_os_order_material").originColumnName(MATERIAL_CODE2).tableNickName("wms.委外订单-物料").build(),
                DataDelSchema.builder().tableSchema("wms").tableName("wms_stock_material_detail").originColumnName(MATERIAL_CODE3).tableNickName("wms.物料明细记录表").build()
        );
    }

    private List<DataDelSchema> getDeleteSchemas() {
        return Arrays.asList(
                DataDelSchema.builder().tableName("dfs_material").originColumnName(MATERIAL_CODE2).build(),
                DataDelSchema.builder().tableName("dfs_material_auxiliary_attr").originColumnName(MATERIAL_CODE).build(),
                DataDelSchema.builder().tableName("dfs_material_process_assembly").originColumnName(MATERIAL_CODE).tableNickName("物料工装参数").build()
         );
    }

    @Override
    public String deleteCheck(Integer materialId) {
        MaterialEntity materialEntity = this.getById(materialId);
        // 赋予输入值
        List<DataDelSchema> dataDelSchemas = getCheckSchemas();
        preHandleData(materialEntity, dataDelSchemas);
        // 关联数据校验
        commonDeleteHandler.doCheck(dataDelSchemas);
        return commonDeleteHandler.getRedisKey(dataDelSchemas);
    }

    /**
     * 需要调用其他第三方服务来获取是否关联物料数据
     */
    private void checkIsRelatedMaterialByThirdPartyService(String redisKey, MaterialEntity materialEntity) {
        // ams
        try {
            OpenApiDTO openApiDTO = OpenApiDTO.builder()
                    .sendReqObject(RelatedMaterialRequestParam.builder().materialCode(materialEntity.getCode()).build())
                    .service(OpenApiEnum.AMS_RELATED_MATERIAL_DATA.getService())
                    .modelCode(OpenApiEnum.AMS_RELATED_MATERIAL_DATA.getModelCode())
                    .interfaceCode(OpenApiEnum.AMS_RELATED_MATERIAL_DATA.getInterfaceCode())
                    .build();
            RelatedMaterialResponseDTO responseDTO = apiConfigService.callOpenUrlToGetObject(openApiDTO, RelatedMaterialResponseDTO.class);
            if (responseDTO.getIsExistRelatedData()) {
                String err = String.format("存在关联数据（%s）", responseDTO.getRelatedTableNickName());
                redisTemplate.opsForValue().set(redisKey, err, 60, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        // qms
        try {
            OpenApiDTO openApiDTO = OpenApiDTO.builder()
                    .sendReqObject(RelatedMaterialRequestParam.builder().materialCode(materialEntity.getCode()).build())
                    .service(OpenApiEnum.QMS_RELATED_MATERIAL_DATA.getService())
                    .modelCode(OpenApiEnum.QMS_RELATED_MATERIAL_DATA.getModelCode())
                    .interfaceCode(OpenApiEnum.QMS_RELATED_MATERIAL_DATA.getInterfaceCode())
                    .build();
            RelatedMaterialResponseDTO responseDTO = apiConfigService.callOpenUrlToGetObject(openApiDTO, RelatedMaterialResponseDTO.class);
            setErrorInfo(responseDTO, redisKey);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        // wms
        try {
            OpenApiDTO openApiDTO = OpenApiDTO.builder()
                    .sendReqObject(RelatedMaterialRequestParam.builder().materialCode(materialEntity.getCode()).build())
                    .service(OpenApiEnum.WMS_RELATED_MATERIAL_DATA.getService())
                    .modelCode(OpenApiEnum.WMS_RELATED_MATERIAL_DATA.getModelCode())
                    .interfaceCode(OpenApiEnum.WMS_RELATED_MATERIAL_DATA.getInterfaceCode())
                    .build();
            RelatedMaterialResponseDTO responseDTO = apiConfigService.callOpenUrlToGetObject(openApiDTO, RelatedMaterialResponseDTO.class);
            setErrorInfo(responseDTO, redisKey);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private void setErrorInfo(RelatedMaterialResponseDTO responseDTO, String redisKey) {
        if (responseDTO.getIsExistRelatedData()) {
            String err = String.format("第三方服务存在关联物料的数据, 无法变更为废弃态！");
            log.error(err);
            redisTemplate.opsForValue().set(redisKey, err, 60, TimeUnit.MINUTES);
        }
    }

    private void preHandleData(MaterialEntity materialEntity, List<DataDelSchema> dataDelSchemas) {
        for (DataDelSchema schema : dataDelSchemas) {
            if (MATERIAL_ID.equals(schema.getOriginColumnName())) {
                schema.setValues(Collections.singletonList(materialEntity.getId()));
            }
            if (MATERIAL_CODE.equals(schema.getOriginColumnName())) {
                schema.setValues(Collections.singletonList(materialEntity.getCode()));
            }
            if (MATERIAL_CODE2.equals(schema.getOriginColumnName())) {
                schema.setValues(Collections.singletonList(materialEntity.getCode()));
            }
            if (MATERIAL_CODE3.equals(schema.getOriginColumnName())) {
                schema.setValues(Collections.singletonList(materialEntity.getCode()));
            }
        }
    }


    @Override
    public Object deleteCheckProgress(String key) {
        return commonDeleteHandler.checkProgress(key);
    }


    @Override
    public final Map<String, MaterialEntity> listByCodes(Set<String> materialCodes, SFunction<MaterialEntity, ?>... columns) {
        return this.lambdaQuery()
                .select(columns)
                .in(MaterialEntity::getCode, materialCodes)
                .list()
                .stream().collect(Collectors.toMap(MaterialEntity::getCode, o -> o));
    }

    @Override
    public List<CommonType> getShelfLifeUnit() {
        return Stream.of(ShelfLifeUnitEnum.values())
                .map(unitEnum -> CommonType.builder().code(unitEnum.getCode()).name(unitEnum.getName()).build())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialEntity upsert(UpdateInsertMaterialDTO materialDTO) {
        MaterialEntity entity = JacksonUtil.convertObject(materialDTO, MaterialEntity.class);

        Date now = new Date();
        //查询物料类型是否存在，不存在则创建
        addMaterialTypeIfNotExist(entity, materialDTO.getIsCreateMaterialType());
        //查询物料单位是否存在，不存在则创建
        addUnitIfNotExist(entity.getComp(), materialDTO.getIsCreateUnit());
        addUnitIfNotExist(entity.getUnit(), materialDTO.getIsCreateUnit());
        MaterialEntity materialDB = getSimpleMaterialByCode(entity.getCode());
        if (materialDB == null) {
            //新增
            entity.setUpdateTime(entity.getUpdateTime() == null ? now : entity.getUpdateTime());
            entity.setCreateTime(entity.getCreateTime() == null ? now : entity.getCreateTime());
            if (entity.getState() != null && MaterialStateEnum.CREATE.getCode() == entity.getState()) {
                saveEntity(entity);
            } else {
                saveReleasedEntity(entity);
            }
        } else {
            //更新
            FieldUtil.copyValueTo(materialDB, entity);
            updateEntity(entity);
        }
        return entity;
    }

    private void addUnitIfNotExist(String unit, Boolean isCreateUnit) {
        if (StringUtils.isBlank(unit)) {
            return;
        }
        QueryWrapper<DictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("BINARY name", unit);
        queryWrapper.lambda().eq(DictEntity::getType, DictTypeEnum.UNIT.getType());
        DictEntity entity = dictService.getOne(queryWrapper);
        if (entity != null) {
            return;
        }
        //不自动创建则报错
        if (!isCreateUnit) {
            throw new ResponseException(RespCodeEnum.UNIT_NOT_FOUND);
        }
        Date now = new Date();
        entity = DictEntity.builder()
                .name(unit)
                .createTime(now)
                .updateTime(now)
                .build();
        dictService.addUnits(entity);
    }

    private void addMaterialTypeIfNotExist(MaterialEntity entity, Boolean isCreateMaterialType) {
        if (entity.getType() == null && StringUtils.isBlank(entity.getTypeName())) {
            throw new ResponseException(RespCodeEnum.MATERIAL_TYPE_ID_NAME_IS_NULL);
        }
        DictEntity materialType;
        if (entity.getType() != null) {
            //通过id查询物料类型，不存在则报错
            materialType = dictService.lambdaQuery()
                    .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType()).eq(DictEntity::getId, entity.getType()).one();
            if (materialType == null) {
                throw new ResponseException(RespCodeEnum.MATERIAL_TYPE_NOT_FOUND);
            }
        } else {
            //通过物料类型名称查询物料类型，不存在则创建
            materialType = getMaterialTypeByName(entity.getTypeName());
            if (materialType == null) {
                //物料类型不存在且不自动创建则报错
                if (!isCreateMaterialType) {
                    throw new ResponseException(RespCodeEnum.MATERIAL_TYPE_NOT_FOUND);
                }
                //添加类型
                this.addMaterialTypeWithDefaultConfig(
                        MaterialTypeInsertDTO.builder()
                                .name(entity.getTypeName())
                                .build()
                );
                materialType = getMaterialTypeByName(entity.getTypeName());
            }
            //设置类型id
            entity.setType(materialType.getId());
        }
    }

    @Override
    public void addMaterialTypeWithDefaultConfig(MaterialTypeInsertDTO dto) {
        Date now = new Date();
        DictEntity dictEntity = JacksonUtil.convertObject(dto, DictEntity.class);
        dictEntity.setCreateTime(now);
        dictEntity.setUpdateTime(now);

        MaterialTypeAddDTO build = MaterialTypeAddDTO.builder().dictEntity(dictEntity).build();
        dictService.addMaterialType(build);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsertMaterialType(MaterialTypeInsertDTO dto) {
        DictEntity one = dictService.lambdaQuery()
                .eq(DictEntity::getName, dto.getName())
                .eq(DictEntity::getType, Constant.MATERIAL_TYPE).one();
        if (one == null) {
            //新增
            addMaterialTypeWithDefaultConfig(dto);
            return;
        }
        //更新
        DictEntity dictEntity = JacksonUtil.convertObject(dto, DictEntity.class);

        //数据库的复制到传入的对象，有值不覆盖
        FieldUtil.copyValueTo(one, dictEntity);

        MaterialTypeAddDTO build = MaterialTypeAddDTO.builder()
                .dictEntity(dictEntity)
                .build();
        dictService.updateMaterialType(build);
    }

    @Override
    public Map<String, MaterialEntity> getMaterialEntity(List<MaterialCodeAndSkuIdSelectDTO> codeSkuIds) {
        // 过滤掉物料编码和skuId都为空的
        codeSkuIds = codeSkuIds.stream().filter(res -> !(org.springframework.util.StringUtils.isEmpty(res.getMaterialCode()) && Objects.isNull(res.getSkuId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codeSkuIds)) {
            return new HashMap<>();
        }
        //获取没有sku的物料编码
        List<String> materielCodes = codeSkuIds.stream()
                .filter(codeSkuId -> Objects.isNull(codeSkuId.getSkuId()) || Constants.SKU_ID_DEFAULT_VAL.equals(codeSkuId.getSkuId()))
                .map(MaterialCodeAndSkuIdSelectDTO::getMaterialCode).distinct().collect(Collectors.toList());
        //获取没有sku的物料编码对应物料转成结果map
        List<MaterialEntity> materialEntities = CollectionUtils.isEmpty(materielCodes)? Collections.emptyList() : this.lambdaQuery().in(MaterialEntity::getCode, materielCodes).list();
        Map<String, MaterialEntity> materielCodeMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));

        //定义一个结果存储map
        Map<String, MaterialEntity> materialMap = new HashMap<>(codeSkuIds.size());

        for (MaterialCodeAndSkuIdSelectDTO codeSkuId : codeSkuIds) {
            //不管存在不存在sku都会生成一个唯一的key
            String key = ColumnUtil.getMaterialSku(codeSkuId.getMaterialCode(), codeSkuId.getSkuId());
            //如果sku不存在直接叫前面查询到的数据设置到结果map
            if (Objects.isNull(codeSkuId.getSkuId()) || Constants.SKU_ID_DEFAULT_VAL.equals(codeSkuId.getSkuId())) {
                materialMap.put(key, materielCodeMap.get(codeSkuId.getMaterialCode()));
            } else {
                materialMap.computeIfAbsent(key, s -> {
                    //如果存在sku、那么去判断结果集合中是否已经存在、不存在则添加
                    MaterialEntity materialEntity = getEntityByCodeAndSkuIdAndShowBom(codeSkuId.getMaterialCode(), codeSkuId.getSkuId());
                    materialEntity.setSortName(com.yelink.dfscommon.constant.MaterialSortEnum.getNameByCode(materialEntity.getSort()));
                    return materialEntity;
                });
            }
        }
        return materialMap;
    }

    @Override
    public Page<MaterialEntity> getList(MaterialQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<MaterialEntity> page = this.baseMapper.getList(sql, dto.getPage());
        this.showName(page.getRecords());
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialSyncResultVO sync(List<UpdateInsertMaterialDTO> dtos) {
        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialSyncResultVO.MaterialSyncFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        List<String> insertedCodes = new ArrayList<>();
        List<String> updatedCodes = new ArrayList<>();

        // 获取所有物料编码，用于批量查询已存在的物料
        List<String> allCodes = dtos.stream().map(UpdateInsertMaterialDTO::getCode).collect(Collectors.toList());
        // 批量查询已存在的物料编码
        Set<String> existingCodes = this.lambdaQuery()
                .select(MaterialEntity::getCode).in(MaterialEntity::getCode, allCodes)
                .list().stream().map(MaterialEntity::getCode).collect(Collectors.toSet());
        // 处理每个物料
        for (UpdateInsertMaterialDTO dto : dtos) {
            String materialCode = dto.getCode();
            String operationType = existingCodes.contains(materialCode) ? "UPDATE" : "INSERT";
            try {
                // 执行同步操作（新增或更新）
                upsert(dto);
                // 记录成功操作
                resultBuilder.addSuccess(materialCode);
                if ("INSERT".equals(operationType)) {
                    insertedCodes.add(materialCode);
                } else {
                    updatedCodes.add(materialCode);
                }
            } catch (Exception e) {
                log.error("同步物料失败，物料编码：{}，操作类型：{}，错误信息：{}", materialCode, operationType, e.getMessage(), e);
                resultBuilder.addFailure(MaterialSyncResultVO.MaterialSyncFailureVO.builder()
                        .materialCode(materialCode)
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.BUSINESS_ERROR)
                        .build());
            }
        }

        MaterialSyncResultVO resultVO = JacksonUtil.convertObject(resultBuilder, MaterialSyncResultVO.class);
        resultVO.setInsertedCodes(insertedCodes);
        resultVO.setUpdatedCodes(updatedCodes);
        return resultVO;
    }

    @Override
    public MaterialFieldDetailVO detail(MaterialDetailQueryDTO dto) {
        MaterialEntity materialEntity = this.lambdaQuery().eq(MaterialEntity::getCode, dto.getMaterialCode()).one();
        if (Objects.isNull(materialEntity)) {
            materialEntity = this.getById(dto.getMaterialId());
            if (Objects.isNull(materialEntity)) {
                return null;
            }
        }
        // 展示必要信息
        showName(Collections.singletonList(materialEntity));
        // 数据转换
        return JacksonUtil.convertObject(materialEntity, MaterialFieldDetailVO.class);
    }

    @Override
    public void syncMaterialType(List<MaterialTypeInsert2DTO> dtos) {
        for (MaterialTypeInsert2DTO dto : dtos) {
            upsertMaterialType2(dto);
        }
    }

    @Override
    public void upsertMaterialType2(MaterialTypeInsert2DTO dto) {
        DictEntity one = dictService.lambdaQuery()
                .eq(DictEntity::getCode, dto.getCode())
                .eq(DictEntity::getType, Constant.MATERIAL_TYPE).one();
        if (one == null) {
            //新增
            Date now = new Date();
            DictEntity dictEntity = JacksonUtil.convertObject(dto, DictEntity.class);
            dictEntity.setCreateTime(now);
            dictEntity.setUpdateTime(now);

            MaterialTypeAddDTO build = MaterialTypeAddDTO.builder().dictEntity(dictEntity).build();
            dictService.addMaterialType(build);
            return;
        }
        //更新
        DictEntity dictEntity = JacksonUtil.convertObject(dto, DictEntity.class);

        //数据库的复制到传入的对象，有值不覆盖
        FieldUtil.copyValueTo(one, dictEntity);

        MaterialTypeAddDTO build = MaterialTypeAddDTO.builder()
                .dictEntity(dictEntity)
                .build();
        dictService.updateMaterialType(build);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialBatchInsertResultVO batchInsert(MaterialBatchInsertDTO dto) {
        List<UpdateInsertMaterialDTO> materials = dto.getMaterials();
        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialBatchInsertResultVO.MaterialBatchInsertFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 获取所有物料编码，用于批量查询已存在的物料
        List<String> allCodes = materials.stream().map(UpdateInsertMaterialDTO::getCode).collect(Collectors.toList());
        // 批量查询已存在的物料编码
        Set<String> existingCodes = this.lambdaQuery()
                .select(MaterialEntity::getCode).in(MaterialEntity::getCode, allCodes)
                .list().stream().map(MaterialEntity::getCode).collect(Collectors.toSet());

        // 第一阶段：验证所有数据，收集失败项
        List<UpdateInsertMaterialDTO> validMaterials = new ArrayList<>();
        for (UpdateInsertMaterialDTO material : materials) {
            String materialCode = material.getCode();
            try {
                // 检查物料是否已存在
                if (existingCodes.contains(materialCode)) {
                    resultBuilder.addFailure(MaterialBatchInsertResultVO.MaterialBatchInsertFailureVO.builder()
                            .materialCode(materialCode)
                            .failureReason("物料编码已存在")
                            .errorType(BatchOperationErrorType.DATA_ALREADY_EXISTS)
                            .build());
                    continue;
                }
                validMaterials.add(material);
                resultBuilder.addSuccess(materialCode);
            } catch (Exception e) {
                log.error("批量新增物料验证失败，物料编码：{}，错误信息：{}", materialCode, e.getMessage(), e);
                resultBuilder.addFailure(MaterialBatchInsertResultVO.MaterialBatchInsertFailureVO.builder()
                        .materialCode(materialCode)
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            MaterialBatchInsertResultVO result = JacksonUtil.convertObject(resultBuilder, MaterialBatchInsertResultVO.class);
            throw new BatchOperationException("批量新增物料操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有操作
        for (UpdateInsertMaterialDTO material : validMaterials) {
            try {
                // 防止新增的物料可能存在相同的，这里调用upsert方法
                upsert(material);
            } catch (Exception e) {
                log.error("批量新增物料执行失败，物料编码：{}，错误信息：{}", material.getCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量新增物料执行失败，物料编码：" + material.getCode() + "，错误：" + e.getMessage(), e);
            }
        }

        return JacksonUtil.convertObject(resultBuilder, MaterialBatchInsertResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialBatchUpdateResultVO batchUpdate(MaterialBatchUpdateDTO dto) {
        List<UpdateInsertMaterialDTO> materials = dto.getMaterials();

        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialBatchUpdateResultVO.MaterialBatchUpdateFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 获取所有物料编码，用于批量查询已存在的物料
        List<String> allCodes = materials.stream()
                .map(UpdateInsertMaterialDTO::getCode)
                .collect(Collectors.toList());

        // 批量查询已存在的物料
        Map<String, MaterialEntity> existingMaterialsMap = this.lambdaQuery()
                .in(MaterialEntity::getCode, allCodes)
                .list().stream()
                .collect(Collectors.toMap(MaterialEntity::getCode, entity -> entity));

        // 第一阶段：验证所有数据，收集失败项
        List<UpdateInsertMaterialDTO> validMaterials = new ArrayList<>();
        for (UpdateInsertMaterialDTO material : materials) {
            String materialCode = material.getCode();
            try {
                // 检查物料是否存在
                MaterialEntity existingMaterial = existingMaterialsMap.get(materialCode);
                if (existingMaterial == null) {
                    resultBuilder.addFailure(MaterialBatchUpdateResultVO.MaterialBatchUpdateFailureVO.builder()
                            .materialCode(materialCode)
                            .failureReason("物料编码不存在")
                            .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                            .build());
                    continue;
                }
                validMaterials.add(material);
                resultBuilder.addSuccess(materialCode);
            } catch (Exception e) {
                log.error("批量更新物料验证失败，物料编码：{}，错误信息：{}", materialCode, e.getMessage(), e);
                resultBuilder.addFailure(MaterialBatchUpdateResultVO.MaterialBatchUpdateFailureVO.builder()
                        .materialCode(materialCode)
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            MaterialBatchUpdateResultVO result = JacksonUtil.convertObject(resultBuilder, MaterialBatchUpdateResultVO.class);
            throw new BatchOperationException("批量更新物料操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有操作
        for (UpdateInsertMaterialDTO material : validMaterials) {
            try {
                // 执行更新操作
                upsert(material);
            } catch (Exception e) {
                log.error("批量更新物料执行失败，物料编码：{}，错误信息：{}", material.getCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量更新物料执行失败，物料编码：" + material.getCode() + "，错误：" + e.getMessage(), e);
            }
        }

        return JacksonUtil.convertObject(resultBuilder, MaterialBatchUpdateResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialBatchDeleteResultVO batchDelete(MaterialBatchDeleteDTO dto) {
        List<String> materialCodes = dto.getMaterialCodes();
        // 使用工具类构建结果
        BatchOperationResultBuilder<MaterialBatchDeleteResultVO.MaterialBatchDeleteFailureVO> resultBuilder =
                BatchOperationResultBuilder.create();

        // 批量查询已存在的物料
        Map<String, MaterialEntity> existingMaterialsMap = this.lambdaQuery()
                .in(MaterialEntity::getCode, materialCodes)
                .list().stream()
                .collect(Collectors.toMap(MaterialEntity::getCode, entity -> entity));

        // 第一阶段：验证所有数据，收集失败项和有效的删除目标
        List<MaterialEntity> validEntities = new ArrayList<>();
        for (String materialCode : materialCodes) {
            try {
                // 检查物料是否存在
                MaterialEntity existingMaterial = existingMaterialsMap.get(materialCode);
                if (existingMaterial == null) {
                    resultBuilder.addFailure(MaterialBatchDeleteResultVO.MaterialBatchDeleteFailureVO.builder()
                            .materialCode(materialCode)
                            .failureReason("物料编码不存在")
                            .errorType(BatchOperationErrorType.DATA_NOT_FOUND)
                            .build());
                    continue;
                }

                // 使用deleteCheck方法检查物料是否可以删除
                try {
                    deleteCheck(existingMaterial.getId());
                } catch (Exception e) {
                    resultBuilder.addFailure(MaterialBatchDeleteResultVO.MaterialBatchDeleteFailureVO.builder()
                            .materialCode(materialCode)
                            .failureReason("物料存在关联数据，无法删除：" + e.getMessage())
                            .errorType(BatchOperationErrorType.INVALID_STATE)
                            .build());
                    continue;
                }

                validEntities.add(existingMaterial);
                resultBuilder.addSuccess(materialCode);
            } catch (Exception e) {
                log.error("批量删除物料验证失败，物料编码：{}，错误信息：{}", materialCode, e.getMessage(), e);
                resultBuilder.addFailure(MaterialBatchDeleteResultVO.MaterialBatchDeleteFailureVO.builder()
                        .materialCode(materialCode)
                        .failureReason(e.getMessage())
                        .errorType(BatchOperationErrorType.SYSTEM_ERROR)
                        .build());
            }
        }

        // 如果有失败项，抛出异常触发事务回滚
        if (resultBuilder.getFailedCount() > 0) {
            MaterialBatchDeleteResultVO result = JacksonUtil.convertObject(resultBuilder, MaterialBatchDeleteResultVO.class);
            throw new BatchOperationException("批量删除物料操作失败，存在" + resultBuilder.getFailedCount() + "个失败项", result);
        }

        // 第二阶段：执行所有删除操作
        for (MaterialEntity entity : validEntities) {
            try {
                // 执行删除操作
                removeEntityById(entity.getId());
            } catch (Exception e) {
                log.error("批量删除物料执行失败，物料编码：{}，错误信息：{}", entity.getCode(), e.getMessage(), e);
                // 执行阶段出现异常，直接抛出触发回滚
                throw new RuntimeException("批量删除物料执行失败，物料编码：" + entity.getCode() + "，错误：" + e.getMessage(), e);
            }
        }

        return JacksonUtil.convertObject(resultBuilder, MaterialBatchDeleteResultVO.class);
    }

}
