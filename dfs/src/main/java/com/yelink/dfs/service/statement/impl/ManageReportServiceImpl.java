package com.yelink.dfs.service.statement.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.dto.FormFieldNameDTO;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.DynamicExcelPropertyHandler;
import com.asyncexcel.core.exporter.ExportHandler;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.constant.defect.DefectTypeDictEnum;
import com.yelink.dfs.constant.device.DeviceTypeEnum;
import com.yelink.dfs.constant.maintain.MaintainTypeDictEnum;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.constant.order.AssignmentStateEnum;
import com.yelink.dfs.constant.statement.ExecutionFrequencyEnum;
import com.yelink.dfs.constant.statement.ReportDateRageTypeEnum;
import com.yelink.dfs.constant.statement.ReportDefaultTemplateEnum;
import com.yelink.dfs.constant.statement.ReportModelEnum;
import com.yelink.dfs.constant.statement.ReportStateEnum;
import com.yelink.dfs.constant.statement.ReportTypeEnum;
import com.yelink.dfs.constant.statement.ScriptTypeEnum;
import com.yelink.dfs.constant.statement.SourceTypeEnum;
import com.yelink.dfs.constant.sys.TableFieldTypeEnum;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.common.UploadEntity;
import com.yelink.dfs.entity.defect.dto.TypeEnumDTO;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.shift.ShiftEntity;
import com.yelink.dfs.entity.statement.ManageReportEntity;
import com.yelink.dfs.entity.statement.ManageReportTaskEntity;
import com.yelink.dfs.entity.statement.ManageReportTypeEntity;
import com.yelink.dfs.entity.statement.ManageReportTypeRelationEntity;
import com.yelink.dfs.entity.statement.ManageSourceEntity;
import com.yelink.dfs.entity.statement.ManageSourceFieldEntity;
import com.yelink.dfs.entity.statement.ManageSourceParamEntity;
import com.yelink.dfs.entity.statement.dto.AppDTO;
import com.yelink.dfs.entity.statement.dto.CodeNameDTO;
import com.yelink.dfs.entity.statement.dto.CreateShortcutParamDTO;
import com.yelink.dfs.entity.statement.dto.DataSourceTypeConfigDTO;
import com.yelink.dfs.entity.statement.dto.ManageReportDataSourceConfigDTO;
import com.yelink.dfs.entity.statement.dto.ManageReportSelectDTO;
import com.yelink.dfs.entity.statement.dto.ReportDownloadFilterFieldDTO;
import com.yelink.dfs.entity.statement.dto.TableOrApiExportHandlerDTO;
import com.yelink.dfs.entity.statement.dto.TaskResultSelectDTO;
import com.yelink.dfs.entity.statement.dto.TaskSelectDTO;
import com.yelink.dfs.entity.statement.vo.TaskExecuteResultVO;
import com.yelink.dfs.mapper.statement.ManageReportMapper;
import com.yelink.dfs.open.v1.aksk.dto.OpenApiDTO;
import com.yelink.dfs.open.v1.statement.dto.AddDefaultReportSourceDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.FdfsClientWrapper;
import com.yelink.dfs.service.app.JingZhiService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.service.common.config.OrderTypeConfigService;
import com.yelink.dfs.service.defect.BaseTypeService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.open.OpenApiConfigService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.shift.ShiftService;
import com.yelink.dfs.service.statement.ManageReportService;
import com.yelink.dfs.service.statement.ManageReportTaskService;
import com.yelink.dfs.service.statement.ManageReportTypeRelationService;
import com.yelink.dfs.service.statement.ManageReportTypeService;
import com.yelink.dfs.service.statement.ManageSourceFieldService;
import com.yelink.dfs.service.statement.ManageSourceParamService;
import com.yelink.dfs.service.statement.ManageSourceService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.user.TeamTypeDefService;
import com.yelink.dfs.utils.ExtApiUtil;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.CategoryTypeEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.InputTypeEnum;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.OpenApiEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.order.OrderStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderTypeEnum;
import com.yelink.dfscommon.dto.common.config.BusinessTypeListVO;
import com.yelink.dfscommon.dto.common.config.FullRouteCodeDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeSelectDTO;
import com.yelink.dfscommon.dto.dfs.TargetFieldOptionDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.inner.utils.FieldUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 报表管理(DfsManageReport)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-04 10:39:42
 */
@Slf4j
@Service
public class ManageReportServiceImpl extends ServiceImpl<ManageReportMapper, ManageReportEntity> implements ManageReportService {
    @Resource
    private ManageReportTypeRelationService manageReportTypeRelationService;
    @Resource
    private ManageSourceService manageSourceService;
    @Resource
    private ManageSourceFieldService manageSourceFieldService;
    @Resource
    private ManageSourceParamService manageSourceParamService;
    @Resource
    private ManageReportTaskService manageReportTaskService;
    @Resource
    private UserAuthenService userAuthenService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private UploadService uploadService;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Lazy
    @Resource
    private ProductionLineService productionLineService;
    @Resource
    private OrderTypeConfigService orderTypeConfigService;
    @Resource
    private ModelService modelService;
    @Resource
    private GridService gridService;
    @Resource
    private WorkCenterService workCenterService;
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    private ExcelService excelService;
    @Resource
    private DynamicExcelPropertyHandler excelPropertyHandler;
    @Resource
    private FormFieldRuleConfigService formFieldRuleConfigService;
    @Resource
    private CustomerService customerService;
    @Resource
    private BaseTypeService baseTypeService;
    @Lazy
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private ReportLineService reportLineService;
    @Resource
    private ShiftService shiftService;
    @Resource
    private TeamTypeDefService teamTypeDefService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private SysUserService userService;
    @Resource
    private FdfsClientWrapper fdfsClientWrapper;
    @Resource
    private OpenApiConfigService openApiConfigService;
    @Resource
    private JingZhiService jingZhiService;

    @Override
    public ModelUploadFileEntity applyInnerDefaultTemplate(ReportDefaultTemplateEnum defaultTemplate) {
        List<ModelUploadFileEntity> list = modelUploadFileService.lambdaQuery()
                .eq(ModelUploadFileEntity::getModelType, defaultTemplate.getModelType())
                .list();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        String fileName = defaultTemplate.getDefaultTemplateName() + Constant.XLSX, url;
        String username = userAuthenService.getUsername();
        if (StringUtils.isEmpty(username)) {
            username = Constant.ADMIN;
        }
        try {
            // 下载默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template" + File.separator + defaultTemplate.getDefaultTemplateCode() + Constant.XLSX);
            if (!resource.exists()) {
                throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
            }
            //存入fast-dfs服务器
            MultipartFile file = new MockMultipartFile(fileName, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", resource.getInputStream());
            UploadEntity uploadEntity = uploadService.uploadReferencedFile(file, username);
            url = uploadEntity.getUrl();
        } catch (IOException e) {
            log.error("上传文件失败", e);
            throw new ResponseException("上传文件失败");
        }
        ModelUploadFileEntity uploadFile = ModelUploadFileEntity.builder()
                .modelType(defaultTemplate.getModelType())
                .fileName(fileName)
                .fileAddress(url)
                .uploadTime(new Date())
                .uploadPerson(username)
                .build();
        modelUploadFileService.save(uploadFile);
        return uploadFile;
    }

    @Override
    public Page<ManageReportEntity> pageReport(ManageReportSelectDTO selectDTO) {
        // 模块只能展示当前模块创建的,如果是报表中心,则查全部
        if (StringUtils.isNotBlank(selectDTO.getModel()) && ReportModelEnum.STATEMENT_CENTER.getCode().equals(selectDTO.getModel())) {
            selectDTO.setModel(null);
        }
        QueryWrapper<ManageReportEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(selectDTO.getReportTypeIds())) {
            List<Integer> reportTypeIds = Arrays.stream(selectDTO.getReportTypeIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            List<Integer> reportIds = manageReportTypeRelationService.lambdaQuery()
                    .select(ManageReportTypeRelationEntity::getReportId)
                    .in(ManageReportTypeRelationEntity::getTypeId, reportTypeIds)
                    .list().stream().map(ManageReportTypeRelationEntity::getReportId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(reportIds)) {
                return new Page<>();
            }
            queryWrapper.lambda().in(ManageReportEntity::getReportId, reportIds);
        }
        queryWrapper.lambda()
                .eq(StringUtils.isNotBlank(selectDTO.getModel()), ManageReportEntity::getModel, selectDTO.getModel())
                .like(StringUtils.isNotBlank(selectDTO.getReportName()), ManageReportEntity::getReportName, selectDTO.getReportName())
                .in(StringUtils.isNotBlank(selectDTO.getStates()), ManageReportEntity::getState, StringUtils.isNotBlank(selectDTO.getStates()) ? Arrays.stream(selectDTO.getStates().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList()) : null)
                .between(StringUtils.isNoneBlank(selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime()),
                        ManageReportEntity::getCreateTime, selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime())
                .orderByDesc(ManageReportEntity::getReportId);
        Page<ManageReportEntity> page = this.page(new Page<>(selectDTO.getCurrent(), selectDTO.getSize()), queryWrapper);
        // 设置状态名称和模板名称
        setStateNameForList(page.getRecords());
        // 设置数据集名称
        setDataSourcesNameForList(page.getRecords());
        return page;
    }

    @Override
    public List<ManageReportEntity> listManageReport(Collection<Integer> reportIds, Collection<Integer> notReportIds) {
        return this.lambdaQuery()
                //data_source_config字段数据量大,不显示data_source_config字段
                .select(ManageReportEntity::getReportId, ManageReportEntity::getReportName,
                        ManageReportEntity::getSubhead, ManageReportEntity::getState, ManageReportEntity::getDefaultInner,
                        ManageReportEntity::getState, ManageReportEntity::getModel,
                        ManageReportEntity::getDataSources, ManageReportEntity::getTemplateId,
                        ManageReportEntity::getCreateTime, ManageReportEntity::getCreateBy, ManageReportEntity::getUpdateTime, ManageReportEntity::getUpdateBy)
                .in(!CollectionUtils.isEmpty(reportIds), ManageReportEntity::getReportId, reportIds)
                .notIn(!CollectionUtils.isEmpty(notReportIds), ManageReportEntity::getReportId, notReportIds)
                .list();
    }

    @Override
    public ManageReportEntity detail(Integer reportId) {
        ManageReportEntity byId = this.getById(reportId);
        if (Objects.isNull(byId)) {
            throw new ResponseException("报表不存在");
        }
        // 设置状态名称和模板名称
        setStateNameForList(Stream.of(byId).collect(Collectors.toList()));
        return byId;
    }

    @Deprecated
    @Override
    public List<DataSourceTypeConfigDTO> getDataSourceConfig(String model) {
        List<ManageReportDataSourceConfigDTO> configs = new ArrayList<>();

        for (ReportTypeEnum reportType : ReportTypeEnum.values()) {
            List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = getFieldConfig(reportType);

            ManageReportDataSourceConfigDTO build = ManageReportDataSourceConfigDTO.builder()
                    .dataSourceCode(reportType.getTypeCode())
                    .dataSourceCodeName(reportType.getTypeName())
                    .fieldConfigs(fieldConfigs)
                    .build();
            configs.add(build);
        }
        // 具体字段信息
        for (ManageReportDataSourceConfigDTO config : configs) {
            ReportTypeEnum typeEnum = ReportTypeEnum.getByCode(config.getDataSourceCode());
            assert typeEnum != null;
            // 获取 @ExcelProperty 注解标注的属性和对应名称
            List<Field> excelFields = Arrays.stream(typeEnum.getExportClass().getDeclaredFields())
                    .filter(field -> !Objects.isNull(field.getAnnotation(ExcelProperty.class)))
                    .collect(Collectors.toList());
            List<CodeNameDTO> fields = new ArrayList<>();
            for (Field declaredField : excelFields) {
                ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);
                fields.add(CodeNameDTO.builder().code(declaredField.getName()).name(annotation.value()[0]).build());
            }
            config.setFields(fields);
        }
        // 分类
        List<DataSourceTypeConfigDTO> dataSourceConfigs = new ArrayList<>();
        Map<String, List<ManageReportDataSourceConfigDTO>> dataSourceTypeConfigsMap = configs.stream().collect(Collectors.groupingBy(res -> ReportTypeEnum.getByCode(res.getDataSourceCode()).getDataSourceType()));
        for (Map.Entry<String, List<ManageReportDataSourceConfigDTO>> entry : dataSourceTypeConfigsMap.entrySet()) {
            String dataSourceType = entry.getKey();
            dataSourceConfigs.add(DataSourceTypeConfigDTO.builder()
                    .dataSourceType(dataSourceType)
                    .dataSourceTypeName(ReportTypeEnum.getDataSourceTypeName(dataSourceType))
                    .dataSources(entry.getValue())
                    .build());
        }
        return dataSourceConfigs;
    }

    @Override
    public List<DataSourceTypeConfigDTO> getDataSourceConfigV2(String model) {
        List<ManageReportDataSourceConfigDTO> configs = new ArrayList<>();

        for (ReportTypeEnum reportType : ReportTypeEnum.values()) {
            List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = getFieldConfig(reportType);

            ManageReportDataSourceConfigDTO build = ManageReportDataSourceConfigDTO.builder()
                    .dataSourceCode(reportType.getTypeCode())
                    .dataSourceCodeName(reportType.getTypeName())
                    .fieldConfigs(fieldConfigs)
                    .build();
            configs.add(build);
        }

        // 计划调度、生产作业、质量管理、报表中心,不同路径下面展示不同的数据源
        //计划调度-销售订单+生产订单+生产工单
        //生产作业-生产工单+生产工单绩效
        //质量管理-质检明细表
        //报表中心-全部展示
        if (ReportModelEnum.ORDER_MODEL.getCode().equals(model)) {
            //计划调度-销售订单+生产订单+生产工单
            configs = configs.stream().filter(res -> ReportTypeEnum.SALE_ORDER.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PRODUCT_ORDER.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.WORK_ORDER.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PRODUCT_ORDER_PROCEDURE.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.WORK_ORDER_STATISTICS.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.SALE_ORDER_PROGRESS.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.SALE_ORDER_PROGRESS_DAILY.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.SALE_ORDER_MONTH.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PRODUCT_ORDER_PROGRESS.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PRODUCT_ORDER_PROGRESS_DAILY.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PRODUCT_ORDER_MONTH_PROGRESS.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PRODUCT_ORDER_MONTH.getTypeCode().equals(res.getDataSourceCode()))
                    .collect(Collectors.toList());
        } else if (ReportModelEnum.WORK_ORDER_MODEL.getCode().equals(model)) {
            // 生产作业-生产工单+生产工单绩效
            configs = configs.stream().filter(res -> ReportTypeEnum.WORK_ORDER.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.WORK_ORDER_EFFICIENCY.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.REPORTER_RECORD.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.ATTENDANCE.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.WORK_ORDER_DAILY_PROGRESS.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.WORK_ORDER_HOURLY_MONITOR.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.WORK_ORDER_STATISTICS.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.MATERIAL_PRODUCT_DAILY.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PERSONNEL_PRODUCTION.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.PROCESS_PROGRESS_HOURLY_MONITOR.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.WORK_ORDER_EVERY_TEN_MIN.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.FLOW_CODE_RECORD.getTypeCode().equals(res.getDataSourceCode()))
                    .collect(Collectors.toList());
        } else if (ReportModelEnum.QUALITIES_MANAGE.getCode().equals(model)) {
            //质量管理-质检明细表
            configs = configs.stream().filter(res -> ReportTypeEnum.DEFECT_STATEMENT.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.MAINTAIN.getTypeCode().equals(res.getDataSourceCode())
                            || ReportTypeEnum.LINE_DEFECT.getTypeCode().equals(res.getDataSourceCode()))
                    .collect(Collectors.toList());
        }
        // 具体字段信息
        for (ManageReportDataSourceConfigDTO config : configs) {
            ReportTypeEnum typeEnum = ReportTypeEnum.getByCode(config.getDataSourceCode());
            assert typeEnum != null;
            if (Objects.nonNull(typeEnum.getExportForm())) {
                // 导出实体类使用的是@DynamicExcelProperty注解
                List<FormFieldNameDTO> formFields = JacksonUtil.convertArray(formFieldRuleConfigService.detailRuleByFullPathCode(FullRouteCodeDTO.builder().fullPathCode(typeEnum.getExportForm().getFullPathCode()).build()), FormFieldNameDTO.class);
                excelPropertyHandler.processDynamicExcelProperties(typeEnum.getExportClass(), formFields);
            }
            // 获取 @ExcelProperty 注解标注的属性和对应名称
            List<Field> excelFields = Arrays.stream(typeEnum.getExportClass().getDeclaredFields())
                    .filter(field -> !Objects.isNull(field.getAnnotation(ExcelProperty.class)))
                    .collect(Collectors.toList());
            List<CodeNameDTO> fields = new ArrayList<>();
            for (Field declaredField : excelFields) {
                ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);
                fields.add(CodeNameDTO.builder().code(declaredField.getName()).name(annotation.value()[0]).build());
            }
            config.setFields(fields);
        }
        // 分类
        List<DataSourceTypeConfigDTO> dataSourceConfigs = new ArrayList<>();
        Map<String, List<ManageReportDataSourceConfigDTO>> dataSourceTypeConfigsMap = configs.stream().collect(Collectors.groupingBy(res -> ReportTypeEnum.getByCode(res.getDataSourceCode()).getDataSourceType()));
        for (Map.Entry<String, List<ManageReportDataSourceConfigDTO>> entry : dataSourceTypeConfigsMap.entrySet()) {
            String dataSourceType = entry.getKey();
            dataSourceConfigs.add(DataSourceTypeConfigDTO.builder()
                    .dataSourceType(dataSourceType)
                    .dataSourceTypeName(ReportTypeEnum.getDataSourceTypeName(dataSourceType))
                    .dataSources(entry.getValue())
                    .build());
        }
        //自定义指标数据, 自定义数据源
        dataSourceConfigs.add(getSelfDataSourceConfig());
        return dataSourceConfigs;
    }

    private DataSourceTypeConfigDTO getSelfDataSourceConfig() {
        // 自定义指标数据, 自定义数据源
        List<ManageSourceEntity> list = listSelfManageSource();
        List<ManageReportDataSourceConfigDTO> selfDataSources = new ArrayList<>();
        for (ManageSourceEntity manageSource : list) {
            List<CodeNameDTO> fields = new ArrayList<>();
            List<ManageReportDataSourceConfigDTO.FieldConfigDTO> filterFieldConfigs = new ArrayList<>();
            if (SourceTypeEnum.API.getCode().equals(manageSource.getSourceType())) {
                // api
                List<ManageSourceFieldEntity> tableFields = manageSourceFieldService.listTableFields(manageSource.getSourceCode());
                for (ManageSourceFieldEntity fieldConfig : tableFields) {
                    fields.add(CodeNameDTO.builder()
                            .code(fieldConfig.getFieldCode())
                            .name(fieldConfig.getFieldName())
                            .build());
                }
                // api的筛选字段
                List<ManageSourceParamEntity> paramFields = manageSourceParamService.listParamFields(manageSource.getSourceCode());
                for (ManageSourceParamEntity paramField : paramFields) {
                    String paramCode = paramField.getParamCode();
                    String paramName = paramField.getParamName();
                    String paramType = paramField.getParamType();
                    if (TableFieldTypeEnum.DATETIME.getType().equals(paramType) || TableFieldTypeEnum.DATE.getType().equals(paramType)) {
                        filterFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                                .type("date-rage").field(paramCode).fieldName(paramName)
                                .filterFields(Stream.of(paramField.getParamStartTimeField(), paramField.getParamEndTimeField()).collect(Collectors.toList()))
                                .build());
                    } else {
                        if ("apiSource".equals(paramField.getOptionValueType())) {
                            // api数据源
                            filterFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                                    .type("select").field(paramCode).fieldName(paramName)
                                    .filterFields(Stream.of(paramCode).collect(Collectors.toList()))
                                    .optionValueType(paramField.getOptionValueType())
                                    .optionValue(paramField.getOptionValue())
                                    .build());
                        } else {
                            filterFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                                    .type("input").field(paramCode).fieldName(paramName)
                                    .filterFields(Stream.of(paramCode).collect(Collectors.toList()))
                                    .build());
                        }
                    }
                }
            } else if (SourceTypeEnum.DFS.getCode().equals(manageSource.getSourceType())) {
                if (StringUtils.isBlank(manageSource.getTableSchema()) || StringUtils.isBlank(manageSource.getTableName())) {
                    continue;
                }
                List<ManageSourceFieldEntity> tableFields = manageSourceFieldService.listTableFields(manageSource.getTableSchema(), manageSource.getTableName());
                for (ManageSourceFieldEntity fieldConfig : tableFields) {
                    fields.add(CodeNameDTO.builder()
                            .code(fieldConfig.getFieldCode())
                            .name(fieldConfig.getFieldName())
                            .build());
                    ManageReportDataSourceConfigDTO.FieldConfigDTO fieldConfigDTO = null;
                    // 下划线 转 驼峰
                    String humpFieldCode = FieldUtil.underlineToCamel(fieldConfig.getFieldCode());
                    if (TableFieldTypeEnum.DATETIME.getType().equals(fieldConfig.getFieldType()) || TableFieldTypeEnum.DATE.getType().equals(fieldConfig.getFieldType())) {
                        fieldConfigDTO = ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                                .type("date-rage").field(humpFieldCode).fieldName(fieldConfig.getFieldName())
                                .filterFields(Stream.of(humpFieldCode + "StartTime", humpFieldCode + "EndTime").collect(Collectors.toList()))
                                .build();
                    } else if (TableFieldTypeEnum.VARCHAR.getType().equals(fieldConfig.getFieldType())) {
                        fieldConfigDTO = ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                                .type("input").field(humpFieldCode).fieldName(fieldConfig.getFieldName())
                                .filterFields(Stream.of(humpFieldCode).collect(Collectors.toList()))
                                .build();
                    }
                    // 如果是枚举，直接就是下拉选项
                    if (InputTypeEnum.SELECT.getType().equals(fieldConfig.getInputType())
                            && Constants.TABLE.equals(fieldConfig.getOptionValuesType())) {
                        List<TargetFieldOptionDTO.EnumDTO> enums = JSON.parseArray(fieldConfig.getAllOption(), TargetFieldOptionDTO.EnumDTO.class);
                        List<CodeNameDTO> enumList = enums.stream().map(res -> CodeNameDTO.builder().code(res.getValue()).name(res.getLabel()).build()).collect(Collectors.toList());
                        fieldConfigDTO = ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                                .type("select").field(humpFieldCode).fieldName(fieldConfig.getFieldName())
                                .filterFields(Stream.of(humpFieldCode).collect(Collectors.toList()))
                                .optionValues(enumList)
                                .build();
                    }

                    if (Objects.nonNull(fieldConfigDTO)) {
                        filterFieldConfigs.add(fieldConfigDTO);
                    }
                }
            } else {
                continue;
            }
            selfDataSources.add(ManageReportDataSourceConfigDTO.builder()
                    .dataSourceCode(manageSource.getSourceCode())
                    .dataSourceCodeName(manageSource.getSourceName())
                    .fields(fields)
                    .fieldConfigs(filterFieldConfigs)
                    .build());
        }
        return DataSourceTypeConfigDTO.builder()
                .dataSourceType("selfIndexData")
                .dataSourceTypeName("自定义指标数据")
                .dataSources(selfDataSources)
                .build();
    }

    /**
     * 自定义手动创建的数据集
     */
    private List<ManageSourceEntity> listSelfManageSource() {
        return manageSourceService.lambdaQuery()
                .in(ManageSourceEntity::getSourceType, Stream.of(SourceTypeEnum.DFS.getCode(), SourceTypeEnum.API.getCode()).collect(Collectors.toList()))
                .list();
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getFieldConfig(ReportTypeEnum reportType) {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        if (ReportTypeEnum.SALE_ORDER.equals(reportType)) {
            fieldConfigs = getSaleOrderFieldConfigs();
        } else if (ReportTypeEnum.PRODUCT_ORDER.equals(reportType)) {
            fieldConfigs = getProductOrderFieldConfigs();
        } else if (ReportTypeEnum.WORK_ORDER.equals(reportType)) {
            fieldConfigs = getWorkOrderFieldConfigs();
        } else if (ReportTypeEnum.DEFECT_STATEMENT.equals(reportType)) {
            fieldConfigs = getDefectStatementFieldConfigs();
        } else if (ReportTypeEnum.WORK_ORDER_EFFICIENCY.equals(reportType)) {
            fieldConfigs = getWorkOrderEfficiencyFieldConfigs();
        } else if (ReportTypeEnum.CUSTOMER.equals(reportType)) {
            fieldConfigs = getCustomerFieldConfigs();
        } else if (ReportTypeEnum.MAINTAIN.equals(reportType)) {
            fieldConfigs = getMaintainFieldConfigs();
        } else if (ReportTypeEnum.REPORTER_RECORD.equals(reportType)) {
            fieldConfigs = getReporterRecordFieldConfigs();
        } else if (ReportTypeEnum.ATTENDANCE.equals(reportType)) {
            fieldConfigs = getAttendanceFieldConfigs();
        } else if (ReportTypeEnum.USER_MANAGER.equals(reportType)) {
            fieldConfigs = getUserManagerFieldConfigs();
        } else if (ReportTypeEnum.ROLE_PERMISSION.equals(reportType)) {
            fieldConfigs = getRolePermissionFieldConfigs();
        } else if (ReportTypeEnum.GRID_LIST.equals(reportType)) {
            fieldConfigs = getGridListFieldConfigs();
        } else if (ReportTypeEnum.WORK_CENTER.equals(reportType)) {
            fieldConfigs = getWorkCenterFieldConfigs();
        } else if (ReportTypeEnum.PRODUCTION_LINE.equals(reportType)) {
            fieldConfigs = getProductionLineFieldConfigs();
        } else if (ReportTypeEnum.FAC_LIST.equals(reportType)) {
            fieldConfigs = getFacListFieldConfigs();
        } else if (ReportTypeEnum.TEAM_LIST.equals(reportType)) {
            fieldConfigs = getTeamListFieldConfigs();
        } else if (ReportTypeEnum.DEVICE_LEDGER.equals(reportType)) {
            fieldConfigs = getDeviceLedgerFieldConfigs();
        } else if (ReportTypeEnum.PRODUCT_ORDER_PROCEDURE.equals(reportType)) {
            fieldConfigs = getProductOrderProcedureFieldConfigs();
        } else if (ReportTypeEnum.LINE_DEFECT.equals(reportType)) {
            fieldConfigs = getLineDefectFieldConfigs();
        } else if (ReportTypeEnum.WORK_ORDER_DAILY_PROGRESS.equals(reportType)) {
            fieldConfigs = getWorkOrderDailyProgressFieldConfigs();
        } else if (ReportTypeEnum.WORK_ORDER_HOURLY_MONITOR.equals(reportType)) {
            fieldConfigs = getWorkOrderHourlyMonitorFieldConfigs();
        } else if (ReportTypeEnum.WORK_ORDER_STATISTICS.equals(reportType)) {
            fieldConfigs = getWorkOrderStatisticsFieldConfigs();
        } else if (ReportTypeEnum.MATERIAL_PRODUCT_DAILY.equals(reportType)) {
            fieldConfigs = getMaterialProductDailyFieldConfigs();
        } else if (ReportTypeEnum.SALE_ORDER_PROGRESS.equals(reportType)) {
            fieldConfigs = getSaleOrderProgressFieldConfigs();
        } else if (ReportTypeEnum.SALE_ORDER_PROGRESS_DAILY.equals(reportType)) {
            fieldConfigs = getSaleOrderProgressDailyFieldConfigs();
        } else if (ReportTypeEnum.SALE_ORDER_MONTH.equals(reportType)) {
            fieldConfigs = getSaleOrderMonthFieldConfigs();
        } else if (ReportTypeEnum.PRODUCT_ORDER_PROGRESS.equals(reportType)) {
            fieldConfigs = getProductOrderProgressFieldConfigs();
        } else if (ReportTypeEnum.PRODUCT_ORDER_PROGRESS_DAILY.equals(reportType)) {
            fieldConfigs = getProductOrderProgressDailyFieldConfigs();
        } else if (ReportTypeEnum.PRODUCT_ORDER_MONTH_PROGRESS.equals(reportType)) {
            fieldConfigs = getProductOrderMonthProgressFieldConfigs();
        } else if (ReportTypeEnum.PRODUCT_ORDER_MONTH.equals(reportType)) {
            fieldConfigs = getProductOrderMonthFieldConfigs();
        } else if (ReportTypeEnum.PERSONNEL_PRODUCTION.equals(reportType)) {
            fieldConfigs = getPersonnelProductionFieldConfigs();
        } else if (ReportTypeEnum.PROCESS_PROGRESS_HOURLY_MONITOR.equals(reportType)) {
            fieldConfigs = getProcessProgressHourlyMonitorFieldConfigs();
        } else if (ReportTypeEnum.WORK_ORDER_EVERY_TEN_MIN.equals(reportType)) {
            fieldConfigs = getWorkOrderEveryTenMinFieldConfigs();
        } else if (ReportTypeEnum.FLOW_CODE_RECORD.equals(reportType)) {
            fieldConfigs = getFlowCodeRecordFieldConfigs();
        }
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getSaleOrderFieldConfigs() {
        //Class<SaleOrderSelectOpenDTO> selectClass = SaleOrderSelectOpenDTO.class;
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("requireGoodsDate").fieldName("要货日期")
                .filterFields(Stream.of("planDeliveryStartTime", "planDeliveryEndTime").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("orderDate").fieldName("下单日期")
                .filterFields(Stream.of("orderStartTime", "orderEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("deliveryDate").fieldName("发货日期")
                .filterFields(Stream.of("actualDeliveryStartTime", "actualDeliveryEndTime").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("createTime").fieldName("创建时间")
                .filterFields(Stream.of("createStartTime", "createEndTime").collect(Collectors.toList()))
                .build());
        List<CodeNameDTO> stateList = new ArrayList<>();
        for (OrderStateEnum value : OrderStateEnum.values()) {
            stateList.add(CodeNameDTO.builder()
                    .code(value.getCode())
                    .name(value.getName())
                    .build());
        }
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("states").fieldName("状态")
                .filterFields(Stream.of("states").collect(Collectors.toList()))
                .optionValues(stateList)
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("customerCodes").fieldName("客户编码")
                .filterFields(Stream.of("customerCodes").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("customerName").fieldName("客户名称")
                .filterFields(Stream.of("customerName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("salesmanCode").fieldName("销售员编号")
                .filterFields(Stream.of("salesmanCode").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("salesmanName").fieldName("销售员名称")
                .filterFields(Stream.of("salesmanName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("code").fieldName("物料编码")
                .outerField("materialFields")
                .filterFields(Stream.of("code").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("name").fieldName("物料名称")
                .outerField("materialFields")
                .filterFields(Stream.of("name").collect(Collectors.toList()))
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProductOrderFieldConfigs() {
        //Class<ProductOrderSelectOpenDTO> selectClass = ProductOrderSelectOpenDTO.class;
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> productOrderFieldConfigs = new ArrayList<>();
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("planProductStartTime").fieldName("计划生产开始时间")
                .filterFields(Stream.of("plannedStartTime", "plannedEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("planProductEndTime").fieldName("计划生产完成时间")
                .filterFields(Stream.of("plannedCompletionStartTime", "plannedCompletionEndTime").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("requireGoodsDate").fieldName("要货日期")
                .filterFields(Stream.of("planDeliveryStartTime", "planDeliveryEndTime").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("orderDate").fieldName("下单日期")
                .filterFields(Stream.of("orderStartTime", "orderEndTime").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("actualProductStartTime").fieldName("实际开始生产时间")
                .filterFields(Stream.of("actualProductStartTimeBegin", "actualProductStartTimeEnd").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("actualProductEndTime").fieldName("实际生产完成时间")
                .filterFields(Stream.of("actualProductStartTime", "actualProductEndTime").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("createTime").fieldName("订单创建时间")
                .filterFields(Stream.of("createStartTime", "createEndTime").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("customerCode").fieldName("客户编码")
                .filterFields(Stream.of("customerCode").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .show(true)
                .build());
        List<CodeNameDTO> stateList = new ArrayList<>();
        for (OrderStateEnum value : OrderStateEnum.values()) {
            stateList.add(CodeNameDTO.builder()
                    .code(value.getCode())
                    .name(value.getName())
                    .build());
        }
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("states").fieldName("状态")
                .filterFields(Stream.of("states").collect(Collectors.toList()))
                .optionValues(stateList)
                .show(true)
                .build());
        List<CodeNameDTO> grids = gridService.lambdaQuery()
                .select(GridEntity::getGcode, GridEntity::getGname).list()
                .stream().map(res -> CodeNameDTO.builder().code(res.getGcode()).name(res.getGname()).build())
                .collect(Collectors.toList());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("gridCodes").fieldName("车间")
                .filterFields(Stream.of("gridCodes").collect(Collectors.toList()))
                .optionValues(grids)
                .build());
        return productOrderFieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getWorkOrderFieldConfigs() {
        //Class<WorkOrderSelectDTO> selectClass = WorkOrderSelectDTO.class;

        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> productOrderFieldConfigs = new ArrayList<>();
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("startDate").fieldName("计划开始时间")
                .filterFields(Stream.of("planStartTime", "planEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("endDate").fieldName("计划完成时间")
                .filterFields(Stream.of("planCompleteStartTime", "planCompleteEndTime").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("actualStartDate").fieldName("实际开始时间")
                .filterFields(Stream.of("actualStartTime", "actualEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("actualEndDate").fieldName("实际完成时间")
                .filterFields(Stream.of("actualCompleteStartTime", "actualCompleteEndTime").collect(Collectors.toList()))
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("createDate").fieldName("工单创建时间")
                .filterFields(Stream.of("startTime", "endTime").collect(Collectors.toList()))
                .build());
        List<CodeNameDTO> stateList = new ArrayList<>();
        for (WorkOrderStateEnum value : WorkOrderStateEnum.values()) {
            stateList.add(CodeNameDTO.builder()
                    .code(value.getCode())
                    .name(value.getName())
                    .build());
        }
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("states").fieldName("工单状态")
                .filterFields(Stream.of("states").collect(Collectors.toList()))
                .optionValues(stateList)
                .show(true)
                .build());
        List<CodeNameDTO> assignmentStates = new ArrayList<>();
        for (AssignmentStateEnum value : AssignmentStateEnum.values()) {
            assignmentStates.add(CodeNameDTO.builder()
                    .code(value.getType())
                    .name(value.getTypeName())
                    .build());
        }
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("assignmentState").fieldName("工单派工状态")
                .filterFields(Stream.of("assignmentState").collect(Collectors.toList()))
                .optionValues(assignmentStates)
                .show(true)
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("code").fieldName("物料编码")
                .outerField("materialFields")
                .filterFields(Stream.of("code").collect(Collectors.toList()))
                .show(true)
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("name").fieldName("物料名称")
                .outerField("materialFields")
                .filterFields(Stream.of("name").collect(Collectors.toList()))
                .show(true)
                .build());

        // 获取工单的单据类型
        List<CodeNameDTO> workOrderTypes = new ArrayList<>();
        try {
            List<BusinessTypeListVO> vos = orderTypeConfigService.getOrderTypeListByOrderCategory(OrderTypeSelectDTO.builder().categoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode()).build());
            workOrderTypes = vos.stream().flatMap(vo -> vo.getOrderTypeListVOList().stream())
                    .map(vo -> CodeNameDTO.builder()
                            .code(vo.getOrderType())
                            .name(vo.getOrderTypeName())
                            .build())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            // 如果部署报错则使用原来的枚举进行数据填充
            for (WorkOrderTypeEnum value : WorkOrderTypeEnum.values()) {
                workOrderTypes.add(CodeNameDTO.builder()
                        .code(value.getCode())
                        .name(value.getName())
                        .build());
            }
        }
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("orderType").fieldName("单据类型")
                .filterFields(Stream.of("orderType").collect(Collectors.toList()))
                .optionValues(workOrderTypes)
                .build());

        List<ProductionLineEntity> lines = productionLineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)
                .list();
        List<CodeNameDTO> lineList = new ArrayList<>();
        for (ProductionLineEntity line : lines) {
            lineList.add(CodeNameDTO.builder()
                    .code(line.getProductionLineId())
                    .name(line.getName())
                    .build());
        }
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("lineId").fieldName("制造单元")
                .filterFields(Stream.of("lineId").collect(Collectors.toList()))
                .optionValues(lineList)
                .show(true)
                .build());
        List<CodeNameDTO> lineModelList = modelService.lambdaQuery()
                .select(ModelEntity::getId, ModelEntity::getName)
                .eq(ModelEntity::getType, ModelEnum.LINE.getType())
                .list().stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build()).collect(Collectors.toList());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("lineModelId").fieldName("制造单元类型")
                .filterFields(Stream.of("lineModelId").collect(Collectors.toList()))
                .optionValues(lineModelList)
                .build());
        List<WorkCenterEntity> workCenters = workCenterService.lambdaQuery()
                .select(WorkCenterEntity::getId, WorkCenterEntity::getName)
                .list();
        List<CodeNameDTO> workCenterList = workCenters.stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build()).collect(Collectors.toList());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("workCenterId").fieldName("工作中心")
                .filterFields(Stream.of("workCenterId").collect(Collectors.toList()))
                .optionValues(workCenterList)
                .build());
        productOrderFieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("procedureName").fieldName("工序名称")
                .filterFields(Stream.of("procedureName").collect(Collectors.toList()))
                .show(true)
                .build());
        return productOrderFieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getDefectStatementFieldConfigs() {
        //Class<DefectRecordQueryDTO> selectClass = DefectRecordQueryDTO.class;
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("abnormalName").fieldName("不良项目")
                .filterFields(Stream.of("abnormalName").collect(Collectors.toList()))
                .show(true)
                .build());
        List<TypeEnumDTO> defectTypeList = baseTypeService.getTypeList(DefectTypeDictEnum.DEFECT_TYPE.getCode(), null);
        List<CodeNameDTO> defectTypes = defectTypeList.stream().map(res -> CodeNameDTO.builder().code(res.getCode()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("defectType").fieldName("不良类型")
                .filterFields(Stream.of("defectType").collect(Collectors.toList()))
                .optionValues(defectTypes)
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrder").fieldName("生产工单号")
                .filterFields(Stream.of("workOrder").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("关联生产订单")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("关联销售订单")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("关联销售订单")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .build());
        List<WorkCenterEntity> workCenters = workCenterService.lambdaQuery()
                .select(WorkCenterEntity::getId, WorkCenterEntity::getName)
                .list();
        List<CodeNameDTO> workCenterList = workCenters.stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("workCenterId").fieldName("工作中心")
                .filterFields(Stream.of("workCenterId").collect(Collectors.toList()))
                .optionValues(workCenterList)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productionBaseUnitName").fieldName("生产基本单元名称")
                .filterFields(Stream.of("productionBaseUnitName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("物料名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("createByName").fieldName("质检员")
                .filterFields(Stream.of("createByName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("createDate").fieldName("质检时间")
                .filterFields(Stream.of("start", "end").collect(Collectors.toList()))
                .show(true)
                .build());

        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getWorkOrderEfficiencyFieldConfigs() {
        //Class<StatementProductionDTO.WorkOrderEfficiencySelectDTO> selectClass = StatementProductionDTO.WorkOrderEfficiencySelectDTO.class;

        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("timeRange").fieldName("日期")
                .must(true)
                .outerField("timeRange")
                .filterFields(Stream.of("startTime", "endTime").collect(Collectors.toList()))
                .show(true)
                .build());

        List<CodeNameDTO> stateList = new ArrayList<>();
        stateList.add(CodeNameDTO.builder()
                .code(WorkOrderStateEnum.RELEASED.getCode())
                .name(WorkOrderStateEnum.RELEASED.getName())
                .build());
        stateList.add(CodeNameDTO.builder()
                .code(WorkOrderStateEnum.INVESTMENT.getCode())
                .name(WorkOrderStateEnum.INVESTMENT.getName())
                .build());
        stateList.add(CodeNameDTO.builder()
                .code(WorkOrderStateEnum.HANG_UP.getCode())
                .name(WorkOrderStateEnum.HANG_UP.getName())
                .build());
        stateList.add(CodeNameDTO.builder()
                .code(WorkOrderStateEnum.FINISHED.getCode())
                .name(WorkOrderStateEnum.FINISHED.getName())
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("states").fieldName("状态")
                .filterFields(Stream.of("states").collect(Collectors.toList()))
                .optionValues(stateList)
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("物料名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .show(true)
                .build());
        List<WorkCenterEntity> workCenters = workCenterService.lambdaQuery()
                .select(WorkCenterEntity::getId, WorkCenterEntity::getName)
                .list();
        List<CodeNameDTO> workCenterList = workCenters.stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("workCenterIds").fieldName("工作中心")
                .filterFields(Stream.of("workCenterIds").collect(Collectors.toList()))
                .optionValues(workCenterList)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("lineName").fieldName("产线")
                .filterFields(Stream.of("lineName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("teamName").fieldName("班组")
                .filterFields(Stream.of("teamName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("deviceName").fieldName("设备")
                .filterFields(Stream.of("deviceName").collect(Collectors.toList()))
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getCustomerFieldConfigs() {
        //Class<CustomerSelectDTO> selectClass = CustomerSelectDTO.class;
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("customerCode").fieldName("客户编码")
                .filterFields(Stream.of("customerCode").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("customerName").fieldName("客户名称")
                .filterFields(Stream.of("customerName").collect(Collectors.toList()))
                .show(true)
                .build());
        List<CommonType> allState = customerService.getAllState();
        List<CodeNameDTO> states = new ArrayList<>();
        for (CommonType commonType : allState) {
            states.add(CodeNameDTO.builder()
                    .code(commonType.getCode())
                    .name(commonType.getName())
                    .build());
        }
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("cusState").fieldName("状态")
                .filterFields(Stream.of("cusState").collect(Collectors.toList()))
                .optionValues(states)
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getMaintainFieldConfigs() {
        //Class<MaintainRecordQueryDTO> selectClass = MaintainRecordQueryDTO.class;
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("maintainName").fieldName("维修项目")
                .filterFields(Stream.of("maintainName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrder").fieldName("生产工单号")
                .filterFields(Stream.of("workOrder").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("关联生产订单")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("关联销售订单")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("关联销售订单")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编号")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("物料名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("createByName").fieldName("操作员")
                .filterFields(Stream.of("createByName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("createTime").fieldName("维修时间")
                .filterFields(Stream.of("start", "end").collect(Collectors.toList()))
                .show(true)
                .build());
        List<TypeEnumDTO> list = baseTypeService.getTypeList(MaintainTypeDictEnum.MAINTAIN_TYPE.getCode(), null);
        List<CodeNameDTO> maintainTypes = list.stream().map(res -> CodeNameDTO.builder().code(res.getCode()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("maintainType").fieldName("维修类型")
                .filterFields(Stream.of("maintainType").collect(Collectors.toList()))
                .optionValues(maintainTypes)
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getReporterRecordFieldConfigs() {
        //Class<ReporterRecordDTO> selectClass = ReporterRecordDTO.class;
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrderNumber").fieldName("生产工单号")
                .filterFields(Stream.of("workOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        List<CommonState> list = workOrderService.getWorkOrderState();
        List<CodeNameDTO> states = list.stream().map(res -> CodeNameDTO.builder().code(res.getCode()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("state").fieldName("状态")
                .filterFields(Stream.of("state").collect(Collectors.toList()))
                .optionValues(states)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("产品编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("产品名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("craftName").fieldName("工序名称")
                .filterFields(Stream.of("craftName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("lineName").fieldName("制造单元名称")
                .filterFields(Stream.of("lineName").collect(Collectors.toList()))
                .build());
        List<WorkCenterEntity> workCenters = workCenterService.lambdaQuery()
                .select(WorkCenterEntity::getId, WorkCenterEntity::getName)
                .list();
        List<CodeNameDTO> workCenterList = workCenters.stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("workCenterId").fieldName("制造单元类型")
                .filterFields(Stream.of("workCenterId").collect(Collectors.toList()))
                .optionValues(workCenterList)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("startDate").fieldName("计划开始时间")
                .filterFields(Stream.of("startDateStart", "startDateEnd").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("endDate").fieldName("计划完成时间")
                .filterFields(Stream.of("endDateStart", "endDateEnd").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("actualStartDate").fieldName("实际开始时间")
                .filterFields(Stream.of("actualStartDateStart", "actualStartDateEnd").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("actualEndDate").fieldName("实际完成时间")
                .filterFields(Stream.of("actualEndDateStart", "actualEndDateEnd").collect(Collectors.toList()))
                .build());
        List<CommonType> allReportType = reportLineService.getAllReportType();
        List<CodeNameDTO> reportTypes = new ArrayList<>();
        for (CommonType commonType : allReportType) {
            reportTypes.add(CodeNameDTO.builder()
                    .code(commonType.getType())
                    .name(commonType.getName())
                    .build());
        }
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("type").fieldName("上报方式")
                .filterFields(Stream.of("type").collect(Collectors.toList()))
                .optionValues(reportTypes)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("createTime").fieldName("上报时间")
                .filterFields(Stream.of("createTimeStartDate", "createTimeEndDate").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("resourceTeamName").fieldName("班组名称")
                .filterFields(Stream.of("resourceTeamName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("resourceDeviceName").fieldName("设备名称")
                .filterFields(Stream.of("resourceDeviceName").collect(Collectors.toList()))
                .build());
        List<CodeNameDTO> shifts = shiftService.lambdaQuery()
                .select(ShiftEntity::getId, ShiftEntity::getShiftType)
                .list().stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getShiftType()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("shiftIds").fieldName("班次")
                .filterFields(Stream.of("shiftIds").collect(Collectors.toList()))
                .optionValues(shifts)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getAttendanceFieldConfigs() {
        //Class<CustomerSelectDTO> selectClass = CustomerSelectDTO.class;
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("userName").fieldName("用户名")
                .filterFields(Stream.of("userName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("nickName").fieldName("姓名")
                .filterFields(Stream.of("nickName").collect(Collectors.toList()))
                .build());

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("submitTime").fieldName("提交时间")
                .filterFields(Stream.of("submitStartTime", "submitEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrderNumber").fieldName("生产工单号")
                .filterFields(Stream.of("workOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("生产订单号")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("销售订单号")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("物料名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("craftProcedureName").fieldName("工序名称")
                .filterFields(Stream.of("craftProcedureName").collect(Collectors.toList()))
                .build());
        List<WorkCenterEntity> workCenters = workCenterService.lambdaQuery()
                .select(WorkCenterEntity::getId, WorkCenterEntity::getName)
                .list();
        List<CodeNameDTO> workCenterList = workCenters.stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("workCenterId").fieldName("工作中心")
                .filterFields(Stream.of("workCenterId").collect(Collectors.toList()))
                .optionValues(workCenterList)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productionBasicUnitName").fieldName("基本生产单元")
                .filterFields(Stream.of("productionBasicUnitName").collect(Collectors.toList()))
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getUserManagerFieldConfigs() {
        // UserSelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("nickname").fieldName("姓名")
                .filterFields(Stream.of("nickname").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("jobNumber").fieldName("工号")
                .filterFields(Stream.of("jobNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("departmentName").fieldName("部门")
                .filterFields(Stream.of("departmentName").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getRolePermissionFieldConfigs() {
        // UserSelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("name").fieldName("角色名称")
                .filterFields(Stream.of("name").collect(Collectors.toList()))
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getGridListFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("gname").fieldName("车间名称")
                .filterFields(Stream.of("gname").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getWorkCenterFieldConfigs() {
        // WorkCenterSelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("name").fieldName("工作中心名称")
                .filterFields(Stream.of("name").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productionBasicUnit").fieldName("生产基本单元")
                .filterFields(Stream.of("productionBasicUnit").collect(Collectors.toList()))
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProductionLineFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("name").fieldName("制造单元名称")
                .filterFields(Stream.of("name").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("modelName").fieldName("制造单元类型")
                .filterFields(Stream.of("modelName").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getFacListFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("name").fieldName("工位名称")
                .filterFields(Stream.of("name").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("modelName").fieldName("工位类型")
                .filterFields(Stream.of("modelName").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getTeamListFieldConfigs() {
        //SysTeamPageDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        List<CodeNameDTO> teamTypes = teamTypeDefService.list().stream()
                .map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getTypeDefName()).build())
                .collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("teamTypes").fieldName("班组类型")
                .filterFields(Stream.of("teamTypes").collect(Collectors.toList()))
                .optionValues(teamTypes)
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("teamName").fieldName("班组名称")
                .filterFields(Stream.of("teamName").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getDeviceLedgerFieldConfigs() {
        //DeviceSelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("deviceCode").fieldName("设备编码")
                .filterFields(Stream.of("deviceCode").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("deviceName").fieldName("设备名称")
                .filterFields(Stream.of("deviceName").collect(Collectors.toList()))
                .build());
        List<CodeNameDTO> deviceTypeCodes = Arrays.stream(DeviceTypeEnum.values())
                .map(res -> CodeNameDTO.builder().code(res.getCode()).name(res.getType()).build())
                .collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("deviceTypeCodes").fieldName("设备分类")
                .filterFields(Stream.of("deviceTypeCodes").collect(Collectors.toList()))
                .optionValues(deviceTypeCodes)
                .show(true)
                .build());
        List<CodeNameDTO> deviceModels = modelService.lambdaQuery()
                .select(ModelEntity::getId, ModelEntity::getName)
                .eq(ModelEntity::getType, ModelEnum.DEVICE.getType()).list()
                .stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build())
                .collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("deviceModelIds").fieldName("设备类型")
                .filterFields(Stream.of("deviceModelIds").collect(Collectors.toList()))
                .optionValues(deviceModels)
                .show(true)
                .build());
        List<CodeNameDTO> grids = gridService.lambdaQuery()
                .select(GridEntity::getGid, GridEntity::getGcode, GridEntity::getGname).list()
                .stream().map(res -> CodeNameDTO.builder().code(res.getGid()).name(res.getGname()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("gridIds").fieldName("车间")
                .filterFields(Stream.of("gridIds").collect(Collectors.toList()))
                .optionValues(grids)
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("facName").fieldName("工位")
                .filterFields(Stream.of("facName").collect(Collectors.toList()))
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProductOrderProcedureFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("生产订单号")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("procedureCode").fieldName("工序编码")
                .filterFields(Stream.of("procedureCode").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("procedureName").fieldName("工序名称")
                .filterFields(Stream.of("procedureName").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("time").fieldName("统计时间")
                .filterFields(Stream.of("startTime", "endTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getLineDefectFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("lineCodes").fieldName("产线编码")
                .filterFields(Stream.of("lineCodes").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("lineName").fieldName("产线名称")
                .filterFields(Stream.of("lineName").collect(Collectors.toList()))
                .show(true)
                .build());
        List<TypeEnumDTO> defectTypeList = baseTypeService.getTypeList(DefectTypeDictEnum.DEFECT_TYPE.getCode(), null);
        List<CodeNameDTO> defectTypes = defectTypeList.stream().map(res -> CodeNameDTO.builder().code(res.getCode()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("defectTypeCodes").fieldName("不良类型")
                .filterFields(Stream.of("defectTypeCodes").collect(Collectors.toList()))
                .optionValues(defectTypes)
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("defectName").fieldName("不良项")
                .filterFields(Stream.of("defectName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordDate").fieldName("记录日期")
                .filterFields(Stream.of("recordDateStartTime", "recordDateEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getWorkOrderDailyProgressFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrderNumber").fieldName("工单编号")
                .filterFields(Stream.of("workOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordDate").fieldName("记录日期")
                .filterFields(Stream.of("recordDateStartTime", "recordDateEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getWorkOrderHourlyMonitorFieldConfigs() {
        // MetricsWorkOrderHourlySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrderNumber").fieldName("工单编号")
                .filterFields(Stream.of("workOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordTime").fieldName("记录日期")
                .filterFields(Stream.of("recordTimeStartTime", "recordTimeEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getWorkOrderStatisticsFieldConfigs() {
        // MetricsWorkOrderSelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrderNumber").fieldName("工单编号")
                .filterFields(Stream.of("workOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("time").fieldName("最新统计时间")
                .filterFields(Stream.of("timeStartTime", "timeEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getMaterialProductDailyFieldConfigs() {
        // MetricsMaterialDailySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordDate").fieldName("记录日期")
                .filterFields(Stream.of("recordDateStartTime", "recordDateEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getSaleOrderProgressFieldConfigs() {
        // MetricsSaleOrderMaterialSelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("销售订单号")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("物料名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getSaleOrderProgressDailyFieldConfigs() {
        //MetricsSaleOrderMaterialDailySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordDate").fieldName("记录日期")
                .filterFields(Stream.of("recordDateStartTime", "recordDateEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("销售订单号")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("物料名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getSaleOrderMonthFieldConfigs() {
        //MetricsSaleOrderSummaryMonthlySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordMonth").fieldName("月份")
                .filterFields(Stream.of("recordMonthStartTime", "recordMonthEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProductOrderProgressFieldConfigs() {
        //MetricsSaleOrderMaterialDailySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("生产订单号")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProductOrderProgressDailyFieldConfigs() {
        //MetricsSaleOrderMaterialDailySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordDate").fieldName("记录日期")
                .filterFields(Stream.of("recordDateStartTime", "recordDateEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("生产订单号")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProductOrderMonthProgressFieldConfigs() {
        //MetricsProductOrderMonthlySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordMonth").fieldName("月份")
                .filterFields(Stream.of("recordMonthStartTime", "recordMonthEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("生产订单号")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProductOrderMonthFieldConfigs() {
        //MetricsProductOrderSummaryMonthlySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordMonth").fieldName("月份")
                .filterFields(Stream.of("recordMonthStartTime", "recordMonthEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getPersonnelProductionFieldConfigs() {
        //MetricsStaffDailySelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("usernameNick").fieldName("人员姓名")
                .filterFields(Stream.of("usernameNick").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordDate").fieldName("记录日期")
                .filterFields(Stream.of("recordDateStartTime", "recordDateEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getProcessProgressHourlyMonitorFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("procedureName").fieldName("工序名")
                .filterFields(Stream.of("procedureName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordTime").fieldName("记录日期")
                .filterFields(Stream.of("recordTimeStartTime", "recordTimeEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getWorkOrderEveryTenMinFieldConfigs() {
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("workOrderNumber").fieldName("工单编号")
                .filterFields(Stream.of("workOrderNumber").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("recordTime").fieldName("记录日期")
                .filterFields(Stream.of("recordTimeStartTime", "recordTimeEndTime").collect(Collectors.toList()))
                .show(true)
                .build());
        return fieldConfigs;
    }

    private List<ManageReportDataSourceConfigDTO.FieldConfigDTO> getFlowCodeRecordFieldConfigs() {
        // CodeRecordSelectDTO
        List<ManageReportDataSourceConfigDTO.FieldConfigDTO> fieldConfigs = new ArrayList<>();

        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("relationNumber").fieldName("生产工单号")
                .filterFields(Stream.of("relationNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("productOrderNumber").fieldName("生产订单编号")
                .filterFields(Stream.of("productOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("saleOrderNumber").fieldName("销售订单编号")
                .filterFields(Stream.of("saleOrderNumber").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialCode").fieldName("物料编码")
                .filterFields(Stream.of("materialCode").collect(Collectors.toList()))
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("materialName").fieldName("物料名称")
                .filterFields(Stream.of("materialName").collect(Collectors.toList()))
                .build());
        List<ProductionLineEntity> lines = productionLineService.lambdaQuery()
                .select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName)
                .list();
        List<CodeNameDTO> lineList = lines.stream().map(line -> CodeNameDTO.builder().code(line.getProductionLineId()).name(line.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("lineIds").fieldName("制造单元名称")
                .filterFields(Stream.of("lineIds").collect(Collectors.toList()))
                .optionValues(lineList)
                .show(true)
                .build());
        List<FacilitiesEntity> allFac = facilitiesService.lambdaQuery()
                .select(FacilitiesEntity::getFid, FacilitiesEntity::getFname)
                .eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode())
                .list();
        List<CodeNameDTO> facList = allFac.stream().map(fac -> CodeNameDTO.builder().code(fac.getFid()).name(fac.getFname()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("facIds").fieldName("工位名称")
                .filterFields(Stream.of("facIds").collect(Collectors.toList()))
                .optionValues(facList)
                .show(true)
                .build());
        List<WorkCenterEntity> workCenters = workCenterService.lambdaQuery()
                .select(WorkCenterEntity::getId, WorkCenterEntity::getName)
                .list();
        List<CodeNameDTO> workCenterList = workCenters.stream().map(res -> CodeNameDTO.builder().code(res.getId()).name(res.getName()).build()).collect(Collectors.toList());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("select").field("workCenterId").fieldName("工作中心")
                .filterFields(Stream.of("workCenterId").collect(Collectors.toList()))
                .optionValues(workCenterList)
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("input").field("reportByName").fieldName("操作人")
                .filterFields(Stream.of("reportByName").collect(Collectors.toList()))
                .show(true)
                .build());
        fieldConfigs.add(ManageReportDataSourceConfigDTO.FieldConfigDTO.builder()
                .type("date-rage").field("reportTime").fieldName("上报时间")
                .filterFields(Stream.of("startTime", "endTime").collect(Collectors.toList()))
                .build());
        return fieldConfigs;
    }

    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ManageReportEntity addReport(ManageReportEntity manageReport) {
        String username = userAuthenService.getUsername();
        if (StringUtils.isEmpty(username)) {
            username = Constant.ADMIN;
        }
        manageReport.setState(ReportStateEnum.RELEASED.getCode());
        manageReport.setCreateBy(username);
        manageReport.setCreateTime(new Date());
        this.save(manageReport);

        // 关联报表类型
        saveReportRelateReportType(manageReport.getReportId(), manageReport.getRelateReportTypeIds());
        return manageReport;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ManageReportEntity addReportV2(ManageReportEntity manageReport) {
        String username = userAuthenService.getUsername();
        if (StringUtils.isEmpty(username)) {
            username = Constant.ADMIN;
        }
        manageReport.setState(ReportStateEnum.RELEASED.getCode());
        manageReport.setCreateBy(username);
        manageReport.setCreateTime(new Date());
        this.save(manageReport);

        if (Objects.nonNull(manageReport.getProgramShortcutFlag()) && manageReport.getProgramShortcutFlag()) {
            // 创建小程序快捷方式
            createProgramShortcut(manageReport);
        }
        // 关联报表类型
        saveReportRelateReportType(manageReport.getReportId(), manageReport.getRelateReportTypeIds());
        return manageReport;
    }

    private void createProgramShortcut(ManageReportEntity manageReport) {
        // 小程序-“报表中心”
        final String APPLICATION_ID = "yelink.reportForm.com";
        String redisKey = RedisKeyPrefix.APP_ID + APPLICATION_ID;
        String appId = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isEmpty(appId)) {
            appId = jingZhiService.getAppid(APPLICATION_ID);
            redisTemplate.opsForValue().set(redisKey, appId, 7, TimeUnit.DAYS);
        }
        if (StringUtils.isBlank(appId)) {
            throw new ResponseException("未添加报表中心小程序：" + APPLICATION_ID);
        }
        CreateShortcutParamDTO build = CreateShortcutParamDTO.builder()
                .title(manageReport.getProgramShortcutName())
                .weight(manageReport.getProgramShortcutWeight())
                .categoryId(manageReport.getProgramShortcutCategoryId())
                .label(manageReport.getProgramShortcutLabel())
                .parameter("?reportId=" + manageReport.getReportId())
                .shortDescription(manageReport.getProgramShortcutRemark())
                .iconUrl(manageReport.getProgramShortcutIcon())
                .appId(appId)
                .build();
        String shortcutAppId = createProgramShortcut(build);
        manageReport.setProgramShortcutParam(build.getParameter());
        manageReport.setProgramShortcutAppId(shortcutAppId);
        this.updateById(manageReport);
    }

    private String createProgramShortcut(CreateShortcutParamDTO param) {
        // 调用精致接口创建小程序快捷方式
        // http://**************:8301/jingzhi/shortcut
        MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("title", param.getTitle());
        requestBody.add("weight", param.getWeight());
        requestBody.add("categoryId", param.getCategoryId());
        requestBody.add("label", param.getLabel());
        requestBody.add("parameter", param.getParameter());
        requestBody.add("shortDescription", param.getShortDescription());
        requestBody.add("appId", param.getAppId());
        if (StringUtils.isNotBlank(param.getIconUrl())) {
            UploadEntity one = uploadService.lambdaQuery()
                    .eq(UploadEntity::getUrl, param.getIconUrl())
                    .last("limit 1").one();
            byte[] bytes = fdfsClientWrapper.download(param.getIconUrl());
            ByteArrayResource icon = new ByteArrayResource(bytes) {
                @Override
                public String getFilename() {
                    return one.getFileName();
                }
            };
            requestBody.add("icon", icon);
        }
        OpenApiDTO openApiDTO = OpenApiDTO.builder()
                .sendReqObject(requestBody)
                .modelCode(OpenApiEnum.CREATE_SHORTCUT.getModelCode())
                .interfaceCode(OpenApiEnum.CREATE_SHORTCUT.getInterfaceCode())
                .interfaceCode(OpenApiEnum.CREATE_SHORTCUT.getInterfaceCode())
                .mediaType(MediaType.MULTIPART_FORM_DATA)
                .build();
        JSONObject res = openApiConfigService.callOpenUrl(openApiDTO);
        ResponseData appDetail = JacksonUtil.convertObject(res, ResponseData.class);
        if (appDetail == null) {
            throw new ResponseException(RespCodeEnum.SERVICE_NOT_USE.getMsgDes());
        }
        if (ResponseData.JZ_SUCCESS_CODE.equals(appDetail.getCode())) {
            //查询刚才创建的小程序快捷方式
            // /jingzhi/app/v2?size=10&page=1&title=xxx&isShortcut=1&categoryId=4&label=xx
            HashMap<String, String> selectParam = new HashMap<>();
            selectParam.put("title", param.getTitle());
            selectParam.put("isShortcut", "1");
            selectParam.put("categoryId", String.valueOf(param.getCategoryId()));
            selectParam.put("label", param.getLabel());
            //获取边端本地小程序
            OpenApiDTO openApiDTO2 = OpenApiDTO.builder()
                    .modelCode(OpenApiEnum.GET_JZ_APP_V2.getModelCode())
                    .interfaceCode(OpenApiEnum.GET_JZ_APP_V2.getInterfaceCode())
                    .sendReqParam(selectParam)
                    .token(ExtApiUtil.getTokenBasedOnContext())
                    .build();
            JSONObject res2 = openApiConfigService.callOpenUrl(openApiDTO2);
            List<AppDTO> shortcutApps = JacksonUtil.parseArray(res2.getJSONObject("data").getJSONArray("items").toJSONString(), AppDTO.class);
            // 按最新排序
            assert shortcutApps != null;
            shortcutApps = shortcutApps.stream()
                    .filter(shortcut -> Objects.equals(param.getTitle(), shortcut.getTitle()) && Objects.equals(param.getLabel(), shortcut.getLabel()))
                    .sorted((o1, o2) -> o2.getBindDate().compareTo(o1.getBindDate()))
                    .collect(Collectors.toList());
            return shortcutApps.get(0).getAppId();
        } else {
            throw new ResponseException(RespCodeEnum.SERVICE_NOT_USE.getMsgDes());
        }
    }


    /**
     * 删除快捷小程序快捷方式
     */
    private void deleteProgramShortcut(String shortcutAppId) {
        if (StringUtils.isEmpty(shortcutAppId)) {
            return;
        }
        //DELETE：http://**************:8301/jingzhi/shortcut/ff80818195ccdcd60195d541f0480022
        OpenApiDTO openApiDTO = OpenApiDTO.builder()
                .sendReqUrlParam(shortcutAppId)
                .modelCode(OpenApiEnum.DELETE_SHORTCUT.getModelCode())
                .interfaceCode(OpenApiEnum.DELETE_SHORTCUT.getInterfaceCode())
                .interfaceCode(OpenApiEnum.DELETE_SHORTCUT.getInterfaceCode())
                .build();
        openApiConfigService.callOpenUrl(openApiDTO);
    }

    /**
     * 保存报表关联类型
     */
    private void saveReportRelateReportType(Integer reportId, List<Integer> relateReportTypeIds) {
        if (CollectionUtils.isNotEmpty(relateReportTypeIds)) {
            List<ManageReportTypeRelationEntity> reportTypeRelations = new ArrayList<>();
            for (Integer relateReportTypeId : relateReportTypeIds) {
                reportTypeRelations.add(ManageReportTypeRelationEntity.builder()
                        .reportId(reportId)
                        .typeId(relateReportTypeId)
                        .build());
            }
            manageReportTypeRelationService.reportTypeRelate(reportTypeRelations);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ManageReportEntity updateReport(ManageReportEntity manageReport) {
        Integer reportId = manageReport.getReportId();
        ManageReportEntity preManageReport = this.getById(reportId);
        if (Objects.isNull(preManageReport)) {
            return manageReport;
        }
        manageReport.setUpdateBy(userAuthenService.getUsername());
        manageReport.setUpdateTime(new Date());
        if (Objects.isNull(manageReport.getTemplateId())) {
            // 没传模板,设置为null
            manageReport.setTemplateId(null);
        }
        this.updateById(manageReport);

        if (preManageReport.getProgramShortcutFlag() && !manageReport.getProgramShortcutFlag()) {
            // 如果之前有，更新成没有。删除小程序快捷方式
            deleteProgramShortcut(preManageReport.getProgramShortcutAppId());
        }
        if (manageReport.getProgramShortcutFlag() && isUpdateProgramShortcut(preManageReport, manageReport)) {
            // 更新小程序快捷方式
            // 判断是否变更信息,如何有变更这更新（先删除、再新增）
            deleteProgramShortcut(preManageReport.getProgramShortcutAppId());
            createProgramShortcut(manageReport);
        }

        // 关联报表类型
        manageReportTypeRelationService.removeReportTypeRelate(reportId);
        saveReportRelateReportType(reportId, manageReport.getRelateReportTypeIds());
        return manageReport;
    }

    private boolean isUpdateProgramShortcut(ManageReportEntity preManageReport, ManageReportEntity manageReport) {
        return !Objects.equals(manageReport.getProgramShortcutName(), preManageReport.getProgramShortcutName())
                || !Objects.equals(manageReport.getProgramShortcutWeight(), preManageReport.getProgramShortcutWeight())
                || !Objects.equals(manageReport.getProgramShortcutCategoryId(), preManageReport.getProgramShortcutCategoryId())
                || !Objects.equals(manageReport.getProgramShortcutLabel(), preManageReport.getProgramShortcutLabel())
                || !Objects.equals(manageReport.getProgramShortcutIcon(), preManageReport.getProgramShortcutIcon())
                || !Objects.equals(manageReport.getProgramShortcutRemark(), preManageReport.getProgramShortcutRemark());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ManageReportEntity deleteReport(Integer reportId) {
        ManageReportEntity byId = this.getById(reportId);
        if (Objects.isNull(byId)) {
            return null;
        }
        this.removeById(reportId);
        if (Objects.nonNull(byId.getTemplateId())) {
            // 删除上传的模板
            modelUploadFileService.removeFileById(byId.getTemplateId());
        }
        // 删除定时任务
        manageReportTaskService.removeByReportId(reportId);
        // 删除关联报表类型
        manageReportTypeRelationService.removeReportTypeRelate(reportId);
        if (Objects.nonNull(byId.getProgramShortcutFlag()) && byId.getProgramShortcutFlag()) {
            // 删除对应快捷方式
            deleteProgramShortcut(byId.getProgramShortcutAppId());
        }
        return byId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ManageReportEntity updateReportState(Integer reportId, Integer state) {
        ManageReportEntity byId = this.getById(reportId);
        if (Objects.isNull(byId)) {
            return null;
        }
        Integer oldState = byId.getState();

        String username = userAuthenService.getUsername();
        ManageReportEntity build = ManageReportEntity.builder()
                .reportId(reportId)
                .state(state)
                .updateBy(username)
                .updateTime(new Date())
                .build();
        this.updateById(build);
        // 报表失效，定时任务也失效
        if (!oldState.equals(state) && ReportStateEnum.CLOSED.getCode() == state) {
            manageReportTaskService.lambdaUpdate()
                    .eq(ManageReportTaskEntity::getReportId, reportId)
                    .set(ManageReportTaskEntity::getState, ReportStateEnum.CLOSED.getCode())
                    .set(ManageReportTaskEntity::getUpdateBy, username)
                    .set(ManageReportTaskEntity::getUpdateTime, new Date())
                    .update();
        }
        return byId;
    }

    @Override
    public InputStream getDefaultTemplateInputStream(String dataSources) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(os).build();
        for (String sourceCode : dataSources.split(Constants.SEP)) {
            ReportTypeEnum typeEnum = ReportTypeEnum.getByCode(sourceCode);
            if (Objects.nonNull(typeEnum)) {
                if (Objects.nonNull(typeEnum.getExportForm())) {
                    // 导出实体类使用的是@DynamicExcelProperty注解
                    List<FormFieldNameDTO> formFields = JacksonUtil.convertArray(formFieldRuleConfigService.detailRuleByFullPathCode(FullRouteCodeDTO.builder().fullPathCode(typeEnum.getExportForm().getFullPathCode()).build()), FormFieldNameDTO.class);
                    excelPropertyHandler.processDynamicExcelProperties(typeEnum.getExportClass(), formFields);
                }
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(typeEnum.getTypeName())
                        .head(typeEnum.getExportClass())
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .build();
                excelWriter.write(new ArrayList<>(), writeSheet);
            } else {
                // 自定义数据源
                ManageSourceEntity manageSource = manageSourceService.lambdaQuery().eq(ManageSourceEntity::getSourceCode, sourceCode).one();
                if (Objects.isNull(manageSource)) {
                    continue;
                }
                List<ManageSourceFieldEntity> fields = manageSourceFieldService.listTableFields(manageSource.getSourceCode());
                if (CollectionUtils.isEmpty(fields)) {
                    continue;
                }
                List<List<String>> headList = new ArrayList<>();
                for (ManageSourceFieldEntity fieldConfig : fields) {
                    List<String> head = new ArrayList<>();
                    head.add(fieldConfig.getFieldName());
                    headList.add(head);
                }
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(manageSource.getSourceName())
                        .head(headList)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .build();
                excelWriter.write(new ArrayList<>(), writeSheet);
            }
        }
        excelWriter.finish();
        excelWriter.close();
        return new ByteArrayInputStream(os.toByteArray());
    }

    @Override
    public long asyncDownloadExport(ReportDownloadFilterFieldDTO selectDTO) {
        ManageReportEntity byId = this.getById(selectDTO.getReportId());
        // 根据数据源,筛选数据
        if (StringUtils.isBlank(byId.getDataSources())) {
            log.error("未配置数据源,下载失败, {}", selectDTO.getReportId());
            throw new ResponseException("未配置数据源,下载失败");
        }
        if (CollectionUtils.isEmpty(selectDTO.getFilterFields())) {
            // 构造空的条件
            List<ReportDownloadFilterFieldDTO.ReportFilterFieldDTO> emptyFilterFields = new ArrayList<>();
            for (String dataSourceCode : byId.getDataSources().split(Constants.SEP)) {
                emptyFilterFields.add(ReportDownloadFilterFieldDTO.ReportFilterFieldDTO.builder()
                        .dataSourceCode(dataSourceCode)
                        .filterFieldValue("{}")
                        .build());
            }
            selectDTO.setFilterFields(emptyFilterFields);
        }
        if (ReportStateEnum.CLOSED.getCode() == byId.getState()) {
            log.error("关闭状态,下载失败, {}", selectDTO.getReportId());
            throw new ResponseException("关闭状态,下载失败");
        }
        // 没有模板,使用默认模板, ExportHandler中直接会生成
        Integer templateId = byId.getTemplateId();
        String templateFileUrl = selectDTO.getTemplateFileUrl();
        // templateFileUrl 判断文件是否存在
        if (StringUtils.isNotBlank(templateFileUrl)) {
            UploadEntity one = uploadService.lambdaQuery()
                    .eq(UploadEntity::getUrl, templateFileUrl).last("limit 1").one();
            if (Objects.isNull(one) || (Objects.nonNull(one.getIsDelete()) && one.getIsDelete())) {
                //templateFileUrl 对应文件已经被删除
                templateFileUrl = null;
            }
        }
        String authorization = null;
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            authorization = attributes.getRequest().getHeader("Authorization");
        }

        String username = userAuthenService.getUsername();

        DataExportParam dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(getManageReportExportFileName(byId, selectDTO.getFilterFields()));
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.MANAGE_REPORT.name());
        dataExportParam.setCreateUserCode(username);
        dataExportParam.setParameters(new HashMap<>(8));
        dataExportParam.getParameters().put("templateId", templateId);
        dataExportParam.getParameters().put("templateFileUrl", templateFileUrl);

        List<Class<? extends ExportHandler>> handles = new ArrayList<>();
        List<String> cleanSheetNames = new ArrayList<>();
        for (String sourceCode : byId.getDataSources().split(Constants.SEP)) {
            // 查询条件
            List<ReportDownloadFilterFieldDTO.ReportFilterFieldDTO> filterFieldValues = selectDTO.getFilterFields().stream().filter(res -> sourceCode.equals(res.getDataSourceCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterFieldValues)) {
                continue;
            }
            ReportDownloadFilterFieldDTO.ReportFilterFieldDTO filterFieldValue = filterFieldValues.get(0);
            if (!filterFieldValue.getSelectChange()) {
                // 查询条件没有改变，只需要重新导出改了查询条件的对应sheet
                continue;
            }
            ReportTypeEnum typeEnum = ReportTypeEnum.getByCode(sourceCode);
            if (Objects.nonNull(typeEnum)) {
                Class<? extends ExportHandler> handler = typeEnum.getExportHandler();
                dataExportParam.getParameters().put(typeEnum.getTypeCode(), typeEnum.getTypeName());
                dataExportParam.getParameters().put(handler.getName(), filterFieldValue.getFilterFieldValue());
                handles.add(handler);

                cleanSheetNames.add(typeEnum.getTypeName());
            } else {
                //自定义数据源 导出
                ManageSourceEntity manageSource = manageSourceService.lambdaQuery().eq(ManageSourceEntity::getSourceCode, sourceCode).one();
                if (Objects.isNull(manageSource)) {
                    continue;
                }
                List<ManageSourceFieldEntity> fields = manageSourceFieldService.listTableFields(manageSource.getSourceCode());
                if (CollectionUtils.isEmpty(fields)) {
                    continue;
                }
                List<List<String>> headList = new ArrayList<>();
                for (ManageSourceFieldEntity fieldConfig : fields) {
                    List<String> head = new ArrayList<>();
                    head.add(fieldConfig.getFieldName());
                    headList.add(head);
                }
                if (SourceTypeEnum.DFS.getCode().equals(manageSource.getSourceType())) {
                    // 自定义数据源导出相关信息
                    TableOrApiExportHandlerDTO build = TableOrApiExportHandlerDTO.builder()
                            .templateSheetName(manageSource.getSourceName())
                            .head(headList)
                            .tableSchema(manageSource.getTableSchema())
                            .tableName(manageSource.getTableName())
                            .exportSelect(filterFieldValue.getFilterFieldValue())
                            .build();
                    // 先使用redis缓存,在TableExportHandler中再取出使用
                    redisTemplate.opsForList().rightPush(TableExportHandler.REDIS_KEY, build);
                    redisTemplate.expire(TableExportHandler.REDIS_KEY, 10, TimeUnit.MINUTES);
                    handles.add(TableExportHandler.class);

                    cleanSheetNames.add(manageSource.getSourceName());
                } else if (SourceTypeEnum.API.getCode().equals(manageSource.getSourceType())) {
                    // Api自定义数据
                    TableOrApiExportHandlerDTO build = TableOrApiExportHandlerDTO.builder()
                            .templateSheetName(manageSource.getSourceName())
                            .head(headList)
                            .sourceCode(manageSource.getSourceCode())
                            .authorization(authorization)
                            .exportSelect(filterFieldValue.getFilterFieldValue())
                            .build();
                    // 先使用redis缓存,在TableExportHandler中再取出使用
                    redisTemplate.opsForList().rightPush(ApiExportHandler.REDIS_KEY, build);
                    redisTemplate.expire(ApiExportHandler.REDIS_KEY, 10, TimeUnit.MINUTES);
                    handles.add(ApiExportHandler.class);

                    cleanSheetNames.add(manageSource.getSourceName());
                }
            }
        }
        // 需要清除的sheet名称,如果有就清除
        dataExportParam.getParameters().put("cleanSheetNames", String.join(Constants.SEP, cleanSheetNames));
        handles = handles.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(handles)) {
            log.error("导出数据不存在, {}", selectDTO);
            throw new ResponseException("导出数据不存在");
        }
        return excelService.doExport(dataExportParam, handles.toArray(new Class[0]));
    }

    private String getManageReportExportFileName(ManageReportEntity byId, List<ReportDownloadFilterFieldDTO.ReportFilterFieldDTO> filterFields) {
        // 导出报表名称
        String exportFileName = byId.getReportName();
        // 副标题名称
        if (StringUtils.isNotBlank(byId.getSubhead())) {
            // subhead：数据源名称-筛选字段名称-筛选字段code
            String subhead = byId.getSubhead();

            int i = subhead.lastIndexOf("-");
            String manageSourceFieldCode = subhead.substring(i + 1);
            String manageSourceName = subhead.substring(0, subhead.substring(0, i).lastIndexOf("-"));

            // 获取报表数据源code
            String sourceCode;
            ReportTypeEnum typeEnum = ReportTypeEnum.getByName(manageSourceName);
            if (Objects.isNull(typeEnum)) {
                ManageSourceEntity manageSource = manageSourceService.lambdaQuery().eq(ManageSourceEntity::getSourceName, manageSourceName).last("limit 1").one();
                sourceCode = Objects.nonNull(manageSource) ? manageSource.getSourceCode() : "";
            } else {
                sourceCode = typeEnum.getTypeCode();
            }
            if (StringUtils.isEmpty(sourceCode)) {
                return exportFileName;
            }

            List<ReportDownloadFilterFieldDTO.ReportFilterFieldDTO> filterFieldValues = filterFields.stream()
                    .filter(res -> sourceCode.equals(res.getDataSourceCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterFieldValues)) {
                return exportFileName;
            }
            // 获取前端传的筛选条件中的值
            JsonNode filterFieldValueNode = JacksonUtil.stringToNode(filterFieldValues.get(0).getFilterFieldValue());
            StringBuilder subheadValue = new StringBuilder();
            String[] manageSourceFieldCodes = manageSourceFieldCode.split(Constants.SEP);
            if (manageSourceFieldCodes.length == 1) {
                JsonNode nodeValue = getNodeValue(filterFieldValueNode, manageSourceFieldCodes[0]);
                if (Objects.nonNull(nodeValue)) {
                    subheadValue.append(nodeValue.textValue());
                }
            } else if (manageSourceFieldCodes.length == 2) {
                // 日期
                JsonNode nodeValue1 = getNodeValue(filterFieldValueNode, manageSourceFieldCodes[0]);
                JsonNode nodeValue2 = getNodeValue(filterFieldValueNode, manageSourceFieldCodes[1]);
                if (Objects.nonNull(nodeValue1) && StringUtils.isNotBlank(nodeValue1.textValue())) {
                    // 不要时分秒, 日期和时分秒是使用空格分开的
                    subheadValue.append(nodeValue1.textValue().split(" ")[0]);
                }
                if (Objects.nonNull(nodeValue2) && StringUtils.isNotBlank(nodeValue2.textValue())) {
                    subheadValue.append("-").append(nodeValue2.textValue().split(" ")[0]);
                }
            }
            if (StringUtils.isNotBlank(subheadValue)) {
                exportFileName = exportFileName + "(" + subheadValue + ")";
            }
        }
        return exportFileName;
    }

    /**
     * 获取对应节点的值
     * 比如：{"orderNumber":"001","materialFields":{"code":"test"}} 获取orderNumber中的001或者materialFields.code中的test
     */
    public static JsonNode getNodeValue(JsonNode jsonNode, String paramCode) {
        String[] paramCodes = paramCode.split("\\.");
        JsonNode tempJsonNode = jsonNode;
        for (int i = 0; i < paramCodes.length; i++) {
            if (i == paramCodes.length - 1) {
                if (Objects.nonNull(tempJsonNode)) {
                    return tempJsonNode.get(paramCodes[paramCodes.length - 1]);
                }
            } else {
                tempJsonNode = tempJsonNode.get(paramCodes[i]);
            }
        }
        return null;
    }

    @Override
    public Long downloadExport(ReportDownloadFilterFieldDTO selectDTO) {
        Long excelId = asyncDownloadExport(selectDTO);

        if (StringUtils.isNotBlank(selectDTO.getTemplateFileUrl())) {
            redisTemplate.opsForValue().set("ManageReport:downloadExport:" + excelId, selectDTO.getTemplateFileUrl(), 1, TimeUnit.HOURS);
        }
        return excelId;
    }

    @Override
    public UploadEntity downloadFile(Integer excelId) {
        ExcelTask select = new ExcelTask();
        select.setId(Long.valueOf(excelId));
        ExcelTask excelTask = excelService.listPage(select, 1, 1).getRecords().get(0);
        // 0-初始,1-进行中,2-完成,3-失败
        Integer excelStatus = excelTask.getStatus();

        String username = userAuthenService.getUsername();
        UploadEntity uploadEntity = null;
        if (excelStatus == 2) {
            if (excelTask.getTotalCount() == 0 && StringUtils.isEmpty(excelTask.getFileUrl())) {
                throw new ResponseException("查询数据为空");
            }
            //保存上传记录
            uploadEntity = UploadEntity.builder()
                    .isUsed(false)
                    .url(excelTask.getFileUrl())
                    .fileName(excelTask.getFileName())
                    .createBy(username)
                    .createTime(new Date())
                    .remark("tableName：excel_task，tableId：" + excelId)
                    .build();
            uploadService.save(uploadEntity);

            String templateFileUrl = (String) redisTemplate.opsForValue().get("ManageReport:downloadExport:" + excelId);
            if (StringUtils.isNotBlank(templateFileUrl)) {
                // 清理上一次查询的文件
                boolean result = fastDfsClientService.deleteFile(templateFileUrl);
                if (result) {
                    uploadService.lambdaUpdate()
                            .eq(UploadEntity::getUrl, templateFileUrl)
                            .set(UploadEntity::getIsDelete, true)
                            .set(UploadEntity::getDeleteTime, new Date()).update();
                }
            }
        } else if (excelStatus == 3) {
            throw new ResponseException("查询导出失败");
        }
        return uploadEntity;
    }


    @Override
    public ManageReportTaskEntity addReportTask(ManageReportTaskEntity task) {
        Integer reportId = task.getReportId();
        ManageReportEntity byId = this.getById(reportId);
        if (Objects.isNull(byId) || ReportStateEnum.CLOSED.getCode() == byId.getState()) {
            log.error("报表失效,不可以新建定时任务, {}", reportId);
            throw new ResponseException("报表失效,不可以新建定时任务");
        }
        String username = userAuthenService.getUsername();
        task.setState(ReportStateEnum.RELEASED.getCode());
        task.setCreateBy(username);
        task.setCreateTime(new Date());
        manageReportTaskService.save(task);
        return task;
    }

    @Override
    public Page<ManageReportTaskEntity> listReportTask(TaskSelectDTO selectDTO) {
        // 模块只能展示当前模块创建的,如果是报表中心,则查全部
        if (StringUtils.isNotBlank(selectDTO.getModel()) && ReportModelEnum.STATEMENT_CENTER.getCode().equals(selectDTO.getModel())) {
            selectDTO.setModel(null);
        }
        List<Integer> reportIds = this.lambdaQuery()
                .select(ManageReportEntity::getReportId)
                .eq(StringUtils.isNotBlank(selectDTO.getModel()), ManageReportEntity::getModel, selectDTO.getModel())
                .eq(Objects.nonNull(selectDTO.getReportId()), ManageReportEntity::getReportId, selectDTO.getReportId())
                .list().stream().map(ManageReportEntity::getReportId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(reportIds)) {
            return new Page<>();
        }
        // 查询任务列表
        Page<ManageReportTaskEntity> pageData = manageReportTaskService.lambdaQuery()
                .in(ManageReportTaskEntity::getReportId, reportIds)
                .like(StringUtils.isNotBlank(selectDTO.getTaskCode()), ManageReportTaskEntity::getTaskCode, selectDTO.getTaskCode())
                .in(StringUtils.isNotBlank(selectDTO.getStates()), ManageReportTaskEntity::getState, StringUtils.isNotBlank(selectDTO.getStates()) ? Arrays.stream(selectDTO.getStates().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList()) : null)
                .between(StringUtils.isNoneBlank(selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime()),
                        ManageReportTaskEntity::getCreateTime, selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime())
                .orderByDesc(ManageReportTaskEntity::getTaskId)
                .page(new Page<>(selectDTO.getCurrent(), selectDTO.getSize()));
        setSateNameForList(pageData.getRecords());
        return pageData;
    }

    private void setSateNameForList(List<ManageReportTaskEntity> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        Set<String> usernames = tasks.stream().map(ManageReportTaskEntity::getCreateBy).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        for (ManageReportTaskEntity task : tasks) {
            if (StringUtils.isNotBlank(task.getReceiver())) {
                usernames.addAll(Arrays.stream(task.getReceiver().split(Constants.SEP)).collect(Collectors.toList()));
            }
        }
        Map<String, String> userNameNickMap = userService.getUserNameNickMap(usernames);

        Set<Integer> reportIds = tasks.stream().map(ManageReportTaskEntity::getReportId).collect(Collectors.toSet());
        Map<Integer, String> reportIdNameMap = this.lambdaQuery()
                .select(ManageReportEntity::getReportId, ManageReportEntity::getReportName)
                .in(ManageReportEntity::getReportId, reportIds)
                .list().stream().collect(Collectors.toMap(ManageReportEntity::getReportId, ManageReportEntity::getReportName));
        for (ManageReportTaskEntity record : tasks) {
            record.setStateName(ReportStateEnum.getNameByCode(record.getState()));
            record.setReportName(reportIdNameMap.get(record.getReportId()));
            if (StringUtils.isNotBlank(record.getReceiver())) {
                List<String> userNicks = Arrays.stream(record.getReceiver().split(Constants.SEP)).map(userNameNickMap::get).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                record.setReceiverName(userNicks);
            }
            record.setCreateByName(userNameNickMap.get(record.getCreateBy()));
        }
    }

    @Override
    public void updateTaskState(Integer taskId, Integer state) {
        ManageReportTaskEntity task = manageReportTaskService.getById(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        ManageReportEntity byId = this.getById(task.getReportId());
        if (Objects.isNull(byId) || (ReportStateEnum.CLOSED.getCode() == byId.getState() && ReportStateEnum.RELEASED.getCode() == state)) {
            log.error("报表失效,不可以修改定时任务为生效状态, taskId:{}", taskId);
            throw new ResponseException("报表失效,不可以修改定时任务为生效状态");
        }
        if (task.getAutoInvalid() && ReportStateEnum.RELEASED.getCode() == state) {
            log.error("该任务已结束,任务到期后自动失效,不可再次生效, taskId:{}", taskId);
            throw new ResponseException("该任务已结束");
        }
        String username = userAuthenService.getUsername();
        ManageReportTaskEntity updateBuild = ManageReportTaskEntity.builder()
                .taskId(taskId)
                .state(state)
                .updateBy(username)
                .updateTime(new Date())
                .build();
        manageReportTaskService.updateById(updateBuild);
    }

    @Override
    public void executionManageReportTask(ManageReportTaskEntity task, Date nowDate, Date preDate, Date afterDate) {
        Date executionTime = task.getExecutionTime();
        if (Objects.nonNull(task.getLastExecutionTime())) {
            // 执行频率
            String executionFrequency = task.getExecutionFrequency();
            Date lastExecutionTime = task.getLastExecutionTime();
            if (ChronoUnit.DAYS.toString().equals(executionFrequency)) {
                // 每天Days
                executionTime = DateUtil.addDay(lastExecutionTime, 1);
            } else if (ChronoUnit.WEEKS.toString().equals(executionFrequency)) {
                // 每周Weeks、每月Months、每年Years
                executionTime = DateUtil.addWeeks(lastExecutionTime, 1);
            } else if (ChronoUnit.MONTHS.toString().equals(executionFrequency)) {
                // 每月Months
                executionTime = DateUtil.addMonths(lastExecutionTime, 1);
            } else if (ChronoUnit.YEARS.toString().equals(executionFrequency)) {
                // 每年Years
                executionTime = DateUtil.addYears(lastExecutionTime, 1);
            }
        }
        if (executionTime.compareTo(preDate) >= 0 && executionTime.compareTo(afterDate) <= 0) {
            String taskKey = RedisKeyPrefix.DFS_SCHEDULE_TASK + "manageReportTask:" + task.getTaskId();
            long increment = redisTemplate.opsForValue().increment(taskKey, 1);
            if (increment != 1) {
                // 不执行
                return;
            }
            redisTemplate.expire(taskKey, 1, TimeUnit.HOURS);
            ManageReportEntity manageReport = this.getById(task.getReportId());
            try {
                // 执行任务
                ReportDownloadFilterFieldDTO build = JSON.parseObject(task.getFilterConfig(), ReportDownloadFilterFieldDTO.class);
                log.info("执行报表管理的定时任务开始,taskId:{}", task.getTaskId());
                // 替换时间段,今天、昨天、本周、本月、本季度、今年、过去7天、过去30天、上周、上个月、上个季度、去年
                if (CollectionUtils.isNotEmpty(build.getFilterRangeTime())) {
                    replaceRangeTimeField(build);
                }
                long excelTaskId = this.asyncDownloadExport(build);
                // 更新时间，保存下载历史记录
                manageReportTaskService.saveExecuteRecord(manageReport, task, excelTaskId, nowDate);
                log.info("执行报表管理的定时任务成功,{},excelTaskId:{}", task, excelTaskId);
                // 推送精制通知
                if (StringUtils.isNotBlank(task.getReceiver())) {
                    manageReportTaskService.pushJingZhiMessage(task, excelTaskId);
                }
            } catch (Exception e) {
                log.error("执行报表管理的定时任务失败,", e);
                // 更新时间
                manageReportTaskService.saveExecuteRecord(manageReport, task, null, nowDate);
            }
        }
    }

    /**
     * 替换时间段,今天、昨天、本周、本月、本季度、今年、过去7天、过去30天、上周、上个月、上个季度、去年
     */
    private void replaceRangeTimeField(ReportDownloadFilterFieldDTO build) {
        if (CollectionUtils.isEmpty(build.getFilterRangeTime())) {
            return;
        }
        List<ReportDownloadFilterFieldDTO.ReportTaskFilterRangeTimeDTO> filterRangeTimes = build.getFilterRangeTime().stream().filter(res -> Objects.nonNull(res.getRangeFieldTypeMap())).collect(Collectors.toList());

        for (ReportDownloadFilterFieldDTO.ReportTaskFilterRangeTimeDTO rangeTime : filterRangeTimes) {
            String dataSourceCode = rangeTime.getDataSourceCode();
            List<Map.Entry<String, String>> timeFields = rangeTime.getRangeFieldTypeMap().entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getValue())).collect(Collectors.toList());
            for (Map.Entry<String, String> entry : timeFields) {
                // 指定时间段的字段替换
                List<ManageReportDataSourceConfigDTO.FieldConfigDTO> dataSourceCodeFilterFields = getFieldConfig(ReportTypeEnum.getByCode(dataSourceCode));
                if (CollectionUtils.isEmpty(dataSourceCodeFilterFields)) {
                    // 如果不是内置数据集，从自定义数据源中找
                    DataSourceTypeConfigDTO selfDataSourceConfig = getSelfDataSourceConfig();
                    if (Objects.nonNull(selfDataSourceConfig) && CollectionUtils.isNotEmpty(selfDataSourceConfig.getDataSources())) {
                        for (ManageReportDataSourceConfigDTO dataSource : selfDataSourceConfig.getDataSources()) {
                            if (dataSource.getDataSourceCode().equals(dataSourceCode)) {
                                dataSourceCodeFilterFields = dataSource.getFieldConfigs();
                                break;
                            }
                        }
                    }
                }
                // 指定是这个字段
                dataSourceCodeFilterFields = dataSourceCodeFilterFields.stream().filter(res ->
                                "date-rage".equals(res.getType()) && res.getFilterFields().get(0).equals(entry.getKey()))
                        .collect(Collectors.toList());
                for (ManageReportDataSourceConfigDTO.FieldConfigDTO dataSourceTypeConfig : dataSourceCodeFilterFields) {
                    List<String> filterFields = dataSourceTypeConfig.getFilterFields();
                    for (ReportDownloadFilterFieldDTO.ReportFilterFieldDTO reportFilterFieldDTO : build.getFilterFields()) {
                        if (!reportFilterFieldDTO.getDataSourceCode().equals(dataSourceCode)) {
                            continue;
                        }
                        List<LocalDateTime> replaceRangeTime = getRangeTime(entry.getValue());
                        if (CollectionUtils.isNotEmpty(replaceRangeTime)) {
                            // 替换值
                            JSONObject jsonObject = JSON.parseObject(reportFilterFieldDTO.getFilterFieldValue());
                            jsonObject.put(filterFields.get(0), DateUtil.format(DateUtil.parseDate(replaceRangeTime.get(0)), DateUtil.DATETIME_FORMAT));
                            jsonObject.put(filterFields.get(1), DateUtil.format(DateUtil.parseDate(replaceRangeTime.get(1)), DateUtil.DATETIME_FORMAT));

                            reportFilterFieldDTO.setFilterFieldValue(JSON.toJSONString(jsonObject));
                        }
                    }
                }
            }
        }
    }

    private static List<LocalDateTime> getRangeTime(String type) {
        List<LocalDateTime> rangeTimeList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        // 计算今天的起始时间和结束时间
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.from(now), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.from(now), LocalTime.MAX);
        if (ReportDateRageTypeEnum.TODAY.getCode().equals(type)) {
            rangeTimeList.add(todayStart);
            rangeTimeList.add(todayEnd);
            return rangeTimeList;
        }

        // 计算昨天的起始时间和结束时间
        LocalDateTime yesterdayStart = todayStart.minusDays(1);
        LocalDateTime yesterdayEnd = todayEnd.minusDays(1);
        if (ReportDateRageTypeEnum.YESTERDAY.getCode().equals(type)) {
            rangeTimeList.add(yesterdayStart);
            rangeTimeList.add(yesterdayEnd);
            return rangeTimeList;
        }

        // 计算本周的起始时间和结束时间
        LocalDateTime weekStart = todayStart.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDateTime weekEnd = todayEnd.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        if (ReportDateRageTypeEnum.THIS_WEEK.getCode().equals(type)) {
            rangeTimeList.add(weekStart);
            rangeTimeList.add(weekEnd);
            return rangeTimeList;
        }

        // 计算本月的起始时间和结束时间
        LocalDateTime monthStart = LocalDateTime.of(now.getYear(), now.getMonth(), 1, 0, 0, 0);
        LocalDateTime monthEnd = LocalDateTime.of(now.getYear(), now.getMonth(), Month.of(now.getMonthValue()).maxLength(), 23, 59, 59);
        if (ReportDateRageTypeEnum.THIS_MONTH.getCode().equals(type)) {
            rangeTimeList.add(monthStart);
            rangeTimeList.add(monthEnd);
            return rangeTimeList;
        }

        // 计算本季度的起始时间和结束时间
        int quarter = (now.getMonthValue() - 1) / 3 + 1;
        LocalDateTime quarterStart = LocalDateTime.of(now.getYear(), Month.of((quarter - 1) * 3 + 1), 1, 0, 0, 0);
        LocalDateTime quarterEnd = LocalDateTime.of(now.getYear(), Month.of((quarter - 1) * 3 + 3), Month.of((quarter - 1) * 3 + 3).maxLength(), 23, 59, 59);
        if (ReportDateRageTypeEnum.THIS_QUARTER.getCode().equals(type)) {
            rangeTimeList.add(quarterStart);
            rangeTimeList.add(quarterEnd);
            return rangeTimeList;
        }

        // 计算今年的起始时间和结束时间
        LocalDateTime yearStart = LocalDateTime.of(now.getYear(), 1, 1, 0, 0, 0);
        LocalDateTime yearEnd = LocalDateTime.of(now.getYear(), 12, 31, 23, 59, 59);
        if (ReportDateRageTypeEnum.THIS_YEAR.getCode().equals(type)) {
            rangeTimeList.add(yearStart);
            rangeTimeList.add(yearEnd);
            return rangeTimeList;
        }

        // 计算过去7天的起始时间和结束时间, 不包含今天
        LocalDateTime past7DaysStart = todayStart.minusDays(7);
        LocalDateTime past7DaysEnd = todayEnd.minusDays(1);
        if (ReportDateRageTypeEnum.LAST_7_DAYS.getCode().equals(type)) {
            rangeTimeList.add(past7DaysStart);
            rangeTimeList.add(past7DaysEnd);
            return rangeTimeList;
        }

        // 计算过去30天的起始时间和结束时间, 不包含今天
        LocalDateTime past30DaysStart = todayStart.minusDays(30);
        LocalDateTime past30DaysEnd = todayEnd.minusDays(1);
        if (ReportDateRageTypeEnum.LAST_30_DAYS.getCode().equals(type)) {
            rangeTimeList.add(past30DaysStart);
            rangeTimeList.add(past30DaysEnd);
            return rangeTimeList;
        }

        // 计算上周的起始时间和结束时间
        LocalDateTime lastWeekStart = weekStart.minusDays(7);
        LocalDateTime lastWeekEnd = weekEnd.minusDays(7);
        if (ReportDateRageTypeEnum.LAST_WEEK.getCode().equals(type)) {
            rangeTimeList.add(lastWeekStart);
            rangeTimeList.add(lastWeekEnd);
            return rangeTimeList;
        }

        // 计算上个月的起始时间和结束时间
        LocalDateTime lastMonthStart = monthStart.minusMonths(1);
        LocalDateTime lastMonthEnd = monthEnd.minusMonths(1);
        if (ReportDateRageTypeEnum.LAST_MONTH.getCode().equals(type)) {
            rangeTimeList.add(lastMonthStart);
            rangeTimeList.add(lastMonthEnd);
            return rangeTimeList;
        }

        // 计算上个季度的起始时间和结束时间
        LocalDateTime lastQuarterStart = quarterStart.minusMonths(3);
        LocalDateTime lastQuarterEnd = quarterEnd.minusMonths(3);
        if (ReportDateRageTypeEnum.LAST_QUARTER.getCode().equals(type)) {
            rangeTimeList.add(lastQuarterStart);
            rangeTimeList.add(lastQuarterEnd);
            return rangeTimeList;
        }

        // 计算去年的起始时间和结束时间
        LocalDateTime lastYearStart = LocalDateTime.of(now.getYear() - 1, 1, 1, 0, 0, 0);
        LocalDateTime lastYearEnd = LocalDateTime.of(now.getYear() - 1, 12, 31, 23, 59, 59);
        if (ReportDateRageTypeEnum.LAST_YEAR.getCode().equals(type)) {
            rangeTimeList.add(lastYearStart);
            rangeTimeList.add(lastYearEnd);
            return rangeTimeList;
        }
        return rangeTimeList;
    }

    @Override
    public Page<TaskExecuteResultVO> pageTaskExecuteResult(TaskResultSelectDTO selectDTO) {
        // 只能展示当前模块创建的,如果是报表中心,则查全部
        if (StringUtils.isNotBlank(selectDTO.getModel()) && ReportModelEnum.STATEMENT_CENTER.getCode().equals(selectDTO.getModel())) {
            selectDTO.setModel(null);
        }
        Page<TaskExecuteResultVO> pageData = this.getBaseMapper().pageTaskExecuteResult(selectDTO, new Page<>(selectDTO.getCurrent(), selectDTO.getSize()));
        if (CollectionUtils.isNotEmpty(pageData.getRecords())) {
            for (TaskExecuteResultVO record : pageData.getRecords()) {
                // 手动生成的报表的命名
                StringBuilder taskCreateReportName = new StringBuilder();
                taskCreateReportName.append(record.getReportName()).append(Constants.UNDER_LINE);
                if (ExecutionFrequencyEnum.DAYS.getCode().equals(record.getExecutionFrequency())) {
                    taskCreateReportName.append("日报").append(Constants.UNDER_LINE);
                } else if (ExecutionFrequencyEnum.WEEKS.getCode().equals(record.getExecutionFrequency())) {
                    taskCreateReportName.append("周报").append(Constants.UNDER_LINE);
                } else if (ExecutionFrequencyEnum.MONTHS.getCode().equals(record.getExecutionFrequency())) {
                    taskCreateReportName.append("月报").append(Constants.UNDER_LINE);
                } else if (ExecutionFrequencyEnum.YEARS.getCode().equals(record.getExecutionFrequency())) {
                    taskCreateReportName.append("年报").append(Constants.UNDER_LINE);
                }
                taskCreateReportName.append(DateUtil.format(record.getExecuteTime(), DateUtil.DATE_FORMAT));
                // 报表名称+执行频率（月报/周报/日报）+生成日期（年-月-日）
                record.setTaskCreateReportName(taskCreateReportName.toString());
                record.setExecutionFrequencyName(ExecutionFrequencyEnum.getNameByCode(record.getExecutionFrequency()));
            }
        }
        return pageData;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ModelUploadFileEntity importReportTemplate(MultipartFile file, Integer reportId) {
        if (Objects.isNull(file)) {
            throw new ResponseException(RespCodeEnum.FILE_NOT_EMPTY);
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        ManageReportEntity one = this.lambdaQuery()
                .select(ManageReportEntity::getReportId, ManageReportEntity::getTemplateId)
                .eq(ManageReportEntity::getReportId, reportId)
                .one();
        if (Objects.isNull(one)) {
            throw new ResponseException("报表不存在");
        }
        // 之前使用的模板
        Integer preTemplateId = one.getTemplateId();
        String username = userAuthenService.getUsername();
        // 上传
        ModelUploadFileEntity uploadFile = modelUploadFileService.uploadFile(file, ModelUploadFileEnum.MANAGE_REPORT.getCode(), username);
        Integer templateId = uploadFile.getId();
        // 模板保存到报表上
        ManageReportEntity build = ManageReportEntity.builder()
                .reportId(reportId)
                .templateId(templateId)
                .build();
        this.updateById(build);
        if (Objects.nonNull(preTemplateId)) {
            // 如果之前使用的模板没有被其他报表使用,则删除该模板
            Long count = this.lambdaQuery().eq(ManageReportEntity::getTemplateId, preTemplateId).count();
            if (count == 0) {
                modelUploadFileService.removeFileById(preTemplateId);
            }
        }
        return uploadFile;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteReportTemplate(Integer templateId) {
        Long count = this.lambdaQuery()
                .eq(ManageReportEntity::getTemplateId, templateId)
                .count();
        if (count >= 2) {
            throw new ResponseException("该模板被其他报表使用,不能直接删除");
        }
        this.lambdaUpdate()
                .eq(ManageReportEntity::getTemplateId, templateId)
                .set(ManageReportEntity::getTemplateId, null)
                .update();
        modelUploadFileService.removeFileById(templateId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAddDefaultReport(List<AddDefaultReportSourceDTO> defaultReportSources) {
        if (CollectionUtils.isEmpty(defaultReportSources)) {
            return;
        }
        // 校验数据
        for (AddDefaultReportSourceDTO defaultReportSource : defaultReportSources) {
            if (StringUtils.isEmpty(defaultReportSource.getSourceType()) || StringUtils.isEmpty(defaultReportSource.getSourceCode()) || StringUtils.isEmpty(defaultReportSource.getSourceName())) {
                throw new ResponseException(RespCodeEnum.PARAM_EXCEPTION);
            }
            if (!(SourceTypeEnum.DFS.getCode().equals(defaultReportSource.getSourceType()) || SourceTypeEnum.API.getCode().equals(defaultReportSource.getSourceType()))) {
                throw new ResponseException(RespCodeEnum.PARAM_EXCEPTION);
            }
            if (SourceTypeEnum.API.getCode().equals(defaultReportSource.getSourceType())) {
                if (StringUtils.isEmpty(defaultReportSource.getBody())) {
                    defaultReportSource.setBody("{}");
                }
                if (StringUtils.isEmpty(defaultReportSource.getScriptType()) && StringUtils.isEmpty(defaultReportSource.getTransScript())) {
                    defaultReportSource.setScriptType(ScriptTypeEnum.JS.getCode());
                    defaultReportSource.setTransScript("function dataTransform(data){\n" +
                            "\t//自定义脚本内容\n" +
                            "\treturn data;\n" +
                            "}");
                }
                if (StringUtils.isEmpty(defaultReportSource.getApiUrl())
                        || StringUtils.isEmpty(defaultReportSource.getMethod())
                        || StringUtils.isEmpty(defaultReportSource.getHeader())
                        || CollectionUtils.isEmpty(defaultReportSource.getRespFields())) {
                    throw new ResponseException(RespCodeEnum.PARAM_EXCEPTION);
                }
            }
        }
        List<ManageSourceEntity> sources = JacksonUtil.convertArray(defaultReportSources, ManageSourceEntity.class);
        manageSourceService.addDataSource2(sources);

        List<DataSourceTypeConfigDTO> dataSourceConfig = this.getDataSourceConfigV2(ReportModelEnum.STATEMENT_CENTER.getCode());
        for (ManageSourceEntity source : sources) {
            ManageReportEntity build = ManageReportEntity.builder()
                    .reportName(source.getSourceName())
                    .defaultInner(true)
                    .model(ReportModelEnum.STATEMENT_CENTER.getCode())
                    .dataSources(source.getSourceCode())
                    .dataSourceConfig(this.getDataSourceConfig(dataSourceConfig, source.getSourceCode()))
                    .build();
            this.addReportV2(build);
        }
    }

    /**
     * 设置状态名称和模板名称
     */
    @Override
    public void setStateNameForList(List<ManageReportEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 应用的模板
        Set<Integer> templateIds = records.stream().map(ManageReportEntity::getTemplateId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, ModelUploadFileEntity> templateIdNameMap = new HashMap<>(templateIds.size());
        if (CollectionUtils.isNotEmpty(templateIds)) {
            List<ModelUploadFileEntity> uploadFiles = modelUploadFileService.listByIds(templateIds);
            templateIdNameMap = uploadFiles.stream().collect(Collectors.toMap(ModelUploadFileEntity::getId, v -> v));
        }
        List<String> usernames = records.stream().map(ManageReportEntity::getCreateBy).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, String> userNameNickMap = userService.getUserNameNickMap(usernames);

        List<Integer> reportIds = records.stream().map(ManageReportEntity::getReportId).collect(Collectors.toList());
        List<ManageReportTypeRelationEntity> relates = manageReportTypeRelationService.lambdaQuery()
                .in(ManageReportTypeRelationEntity::getReportId, reportIds)
                .list();
        Map<Integer, List<ManageReportTypeRelationEntity>> reportIdTypesMap = relates.stream().collect(Collectors.groupingBy(ManageReportTypeRelationEntity::getReportId));
        Map<Integer, String> typeIdNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(relates)) {
            Set<Integer> reportTypeIds = relates.stream().map(ManageReportTypeRelationEntity::getTypeId).collect(Collectors.toSet());
            ManageReportTypeService manageReportTypeService = SpringUtil.getBean(ManageReportTypeService.class);
            typeIdNameMap = manageReportTypeService.lambdaQuery()
                    .in(ManageReportTypeEntity::getTypeId, reportTypeIds)
                    .list().stream().collect(Collectors.toMap(ManageReportTypeEntity::getTypeId, ManageReportTypeEntity::getTypeName));
        }
        for (ManageReportEntity record : records) {
            record.setStateName(ReportStateEnum.getNameByCode(record.getState()));
            record.setCreateByName(userNameNickMap.get(record.getCreateBy()));
            ModelUploadFileEntity templateFileEntity = templateIdNameMap.get(record.getTemplateId());
            if (Objects.nonNull(templateFileEntity)) {
                record.setTemplateName(templateFileEntity.getFileName());
                record.setTemplateFile(templateFileEntity);
            }
            // 关联报表类型
            List<ManageReportTypeRelationEntity> relateTypes = reportIdTypesMap.getOrDefault(record.getReportId(), new ArrayList<>());
            record.setRelateReportTypeIds(relateTypes.stream().map(ManageReportTypeRelationEntity::getTypeId).collect(Collectors.toList()));
            List<String> reportTypeNames = new ArrayList<>(relateTypes.size());
            for (ManageReportTypeRelationEntity relateType : relateTypes) {
                reportTypeNames.add(typeIdNameMap.get(relateType.getTypeId()));
            }
            record.setRelateReportTypeNames(String.join(Constants.SEP, reportTypeNames));

            // 内置的报表,可能存在配置为空
            if ("[]".equals(record.getDataSourceConfig()) && Constant.ADMIN.equals(record.getCreateBy())
                    && record.getDataSources().split(Constants.SEP).length > 0) {
                // [{"code":"sale_order_list","name":"销售订单列表","fieldConfigs":[]}]
                List<ManageSourceEntity> sources = manageSourceService.lambdaQuery()
                        .in(ManageSourceEntity::getSourceCode, Arrays.stream(record.getDataSources().split(Constants.SEP)).collect(Collectors.toList()))
                        .list();
                List<JsonNode> addSourceNode = new ArrayList<>();
                for (ManageSourceEntity manageSource : sources) {
                    ObjectNode jsonNode = new ObjectNode(JsonNodeFactory.instance);
                    jsonNode.put("code", manageSource.getSourceCode());
                    jsonNode.put("name", manageSource.getSourceName());
                    addSourceNode.add(jsonNode);
                }
                record.setDataSourceConfig(JacksonUtil.toJSONString(addSourceNode));
            }
        }
    }

    /**
     * 设置数据集名称
     */
    private void setDataSourcesNameForList(List<ManageReportEntity> records) {
        // 查询所有数据集的名称
        Map<String, String> dataSourceNameMap = new HashMap<>();
        for (ReportTypeEnum value : ReportTypeEnum.values()) {
            dataSourceNameMap.put(value.getTypeCode(), value.getTypeName());
        }
        List<ManageSourceEntity> selfSources = listSelfManageSource();
        for (ManageSourceEntity selfSource : selfSources) {
            dataSourceNameMap.put(selfSource.getSourceCode(), selfSource.getSourceName());
        }

        for (ManageReportEntity record : records) {
            String[] dataSources = StringUtils.isNotBlank(record.getDataSources()) ? record.getDataSources().split(Constants.SEP) : new String[]{};
            List<String> dataSourceNames = new ArrayList<>();
            for (String dataSource : dataSources) {
                if (dataSourceNameMap.get(dataSource) != null) {
                    dataSourceNames.add(dataSourceNameMap.get(dataSource));
                }
            }
            record.setDataSourcesName(String.join(Constants.SEP, dataSourceNames));
        }
    }


    @Override
    public String getDataSourceConfig(List<DataSourceTypeConfigDTO> dataSourceConfigs, String sourceCode) {
        // 不要大分类
        List<ManageReportDataSourceConfigDTO> dataSources = new ArrayList<>();
        for (DataSourceTypeConfigDTO dataSourceConfig : dataSourceConfigs) {
            dataSources.addAll(dataSourceConfig.getDataSources());
        }
        JsonNode allDataSourceNode = JacksonUtil.valueToNode(dataSources);
        List<JsonNode> addSourceNode = new ArrayList<>();
        for (JsonNode jsonNode : allDataSourceNode) {
            if (sourceCode.equals(jsonNode.get("dataSourceCode").textValue())) {
                // 需要的数据源
                // 将结点的key替换
                replaceNode(jsonNode, "dataSourceCode", "code");
                replaceNode(jsonNode, "dataSourceCodeName", "name");
                // 移除节点
                ((ObjectNode) jsonNode).remove("fields");
                addSourceNode.add(jsonNode);
                break;
            }
        }
        return JacksonUtil.toJSONString(addSourceNode);
    }

    private void replaceNode(JsonNode jsonNode, String oldCode, String newCode) {
        JsonNode nodeValue = jsonNode.get(oldCode);
        if (nodeValue != null) {
            ((ObjectNode) jsonNode).remove(oldCode);
            ((ObjectNode) jsonNode).put(newCode, nodeValue);
        }
    }

}

