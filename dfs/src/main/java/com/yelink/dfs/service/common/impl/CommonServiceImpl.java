package com.yelink.dfs.service.common.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.ExceptionUtil;
import com.asyncexcel.core.dto.FormFieldNameDTO;
import com.asyncexcel.core.exporter.DynamicExcelPropertyHandler;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.service.IStorageService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.capacity.CapacityUnitTypeEnum;
import com.yelink.dfs.constant.model.FacilitiesCountTypeEnum;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.constant.sensor.SensorParamEnum;
import com.yelink.dfs.constant.sensor.SensorTypeCodeEnum;
import com.yelink.dfs.entity.capacity.CapacityEntity;
import com.yelink.dfs.entity.common.DeleteReturnException;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.device.dto.IndicatorEntityDTO;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacSensorEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.order.EuiMarkEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.target.CombineTargetEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.record.RecordLineDayUnionEntity;
import com.yelink.dfs.entity.target.record.dto.LineOeeDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.capacity.CapacityMapper;
import com.yelink.dfs.mapper.device.DeviceMapper;
import com.yelink.dfs.mapper.device.DeviceSensorMapper;
import com.yelink.dfs.mapper.device.IndicatorMapper;
import com.yelink.dfs.mapper.model.FacSensorMapper;
import com.yelink.dfs.mapper.model.FacilitiesMapper;
import com.yelink.dfs.provider.FdfsClientWrapper;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.common.RedisService;
import com.yelink.dfs.service.common.TargetThresholdCommonService;
import com.yelink.dfs.service.device.IndicatorService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.EuiMarkService;
import com.yelink.dfs.service.order.OrderExecuteSeqService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.target.CombineTargetService;
import com.yelink.dfs.service.target.CountService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.target.record.RecordLineDayUnionService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.work.calendar.WorkCalendarService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.api.dfs.FormFieldInterface;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.OperationDayCountDTO;
import com.yelink.dfscommon.dto.common.config.FullRouteCodeDTO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.Rational;
import com.yelink.dfscommon.utils.converter.AdaptiveColumnWidthStrategy;
import com.yelink.inner.data.access.JsonObjectEntity;
import com.yelink.inner.data.access.SensorValueTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PipedInputStream;
import java.io.PipedOutputStream;
import java.text.ParseException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 公共处理方法
 *
 * <AUTHOR>
 * @Date 2021-03-03 15:47
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {
    @Resource
    private ModelService modelService;
    @Resource
    private FacilitiesMapper facilitiesMapper;
    @Resource
    private IndicatorService indicatorService;
    @Resource
    private TargetThresholdCommonService targetThresholdCommonService;
    @Resource
    private TargetModelService targetModelService;
    @Resource
    private RedisService redisService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private DeviceSensorMapper deviceSensorMapper;
    @Resource
    private FacSensorMapper facSensorMapper;
    @Resource
    private IndicatorMapper indicatorMapper;
    @Resource
    private DictService dictService;
    @Resource
    private RecordWorkOrderDayCountService workOrderDayCountService;
    @Resource
    private CapacityMapper capacityMapper;
    @Resource
    private WorkOrderPlanService workOrderPlanService;
    @Lazy
    @Resource
    private MaterialService materialService;
    @Resource
    private WorkCalendarService workCalendarService;
    @Resource
    private EuiMarkService euiMarkService;
    @Resource
    private CountService countService;
    @Resource
    private CombineTargetService combineTargetService;
    @Resource
    private AssignmentInterface assignmentInterface;
    @Resource
    private WorkCenterService workCenterService;
    @Resource
    private OrderExecuteSeqService orderExecuteSeqService;
    @Resource
    private IStorageService storageService;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private FdfsClientWrapper fdfsClientWrapper;
    @Resource
    private RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService;
    @Resource
    @Lazy
    private SysUserService userService;
    @Resource
    @Lazy
    private OperationLogService operationLogService;
    @Autowired
    private DynamicExcelPropertyHandler excelPropertyHandler;

    /**
     * 更新dfs_record_facility_day_union表记录
     * dfs_record_facility_day_union表，以产线和时间（天）为维度
     * 一个工位、一天只有一条记录，方便后续统计
     *
     * @param dayUnionEntity
     */
    /*@Override
    public void updateFacDayUnion(RecordFacilityDayUnionEntity dayUnionEntity) {
        //更新今天的记录
        Date recordDate = dictService.getRecordDate(new Date());
        UpdateWrapper<RecordFacilityDayUnionEntity> dayUnionQw = new UpdateWrapper<>();
        dayUnionQw.lambda().eq(RecordFacilityDayUnionEntity::getFid, dayUnionEntity.getFid())
                .eq(RecordFacilityDayUnionEntity::getTime, recordDate);
        dayUnionEntity.setTime(recordDate);
        recordFacilityDayUnionService.saveOrUpdate(dayUnionEntity, dayUnionQw);
    }*/

    /**
     * 更新dfs_record_line_day_union表记录
     * dfs_record_line_day_union表，以产线和时间（天）为维度
     * 一条产线、一天只有一条记录，方便后续统计
     *
     * @param dayUnionEntity
     */
    @Override
    public void updateLineDayUnion(RecordLineDayUnionEntity dayUnionEntity) {
        RecordLineDayUnionService recordLineDayUnionService = SpringUtil.getBean(RecordLineDayUnionService.class);
        //更新今天的记录
        Date recordDate = dictService.getRecordDate(new Date());
        UpdateWrapper<RecordLineDayUnionEntity> dayUnionQw = new UpdateWrapper<>();
        dayUnionQw.lambda().eq(RecordLineDayUnionEntity::getProductionLineId, dayUnionEntity.getProductionLineId())
                .eq(RecordLineDayUnionEntity::getTime, recordDate);
        dayUnionEntity.setTime(recordDate);
        recordLineDayUnionService.saveOrUpdate(dayUnionEntity, dayUnionQw);
    }

    /**
     * 更新dfs_record_line_day_union表记录
     * dfs_record_line_day_union表，以产线和时间（天）为维度
     * 一条产线、一天只有一条记录，方便后续统计
     *
     * @param dayUnionEntity
     */
    @Override
    public void updateLineDayUnionByRecordDate(RecordLineDayUnionEntity dayUnionEntity, Date recordDate) {
        RecordLineDayUnionService recordLineDayUnionService = SpringUtil.getBean(RecordLineDayUnionService.class);
        //更新今天的记录
        UpdateWrapper<RecordLineDayUnionEntity> dayUnionQw = new UpdateWrapper<>();
        dayUnionQw.lambda().eq(RecordLineDayUnionEntity::getProductionLineId, dayUnionEntity.getProductionLineId())
                .eq(RecordLineDayUnionEntity::getTime, recordDate);
        recordLineDayUnionService.saveOrUpdate(dayUnionEntity, dayUnionQw);
    }

    @Override
    public List<FacilitiesEntity> getLastFacilities(ProductionLineEntity lineEntity) {
        if (lineEntity == null) {
            return new ArrayList<>();
        }
        //优先查询已作标记工位
        QueryWrapper<FacilitiesEntity> qw = new QueryWrapper<>();
        qw.lambda()
                .eq(FacilitiesEntity::getProductionLineId, lineEntity.getProductionLineId())
                .eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode())
                .in(FacilitiesEntity::getIsCheck, FacilitiesCountTypeEnum.reportCode);
        List<FacilitiesEntity> facilitiesEntities = facilitiesMapper.selectList(qw);
        return facilitiesEntities;
    }

    @Override
    public Set<Integer> getOtherLineId(Integer lineId) {
        LambdaQueryWrapper<FacilitiesEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FacilitiesEntity::getProductionLineId, lineId);
        queryWrapper.in(FacilitiesEntity::getIsCheck, FacilitiesCountTypeEnum.reportCode);
        List<FacilitiesEntity> facilitiesEntities = facilitiesMapper.selectList(queryWrapper);
        List<String> euis = new ArrayList<>();
        Set<Integer> fids = new HashSet<>();
        if (CollectionUtils.isEmpty(facilitiesEntities)) {
            return new HashSet<>();
        }
        for (FacilitiesEntity fac : facilitiesEntities) {
            FacSensorEntity counterFacSensor = getCounterFacSensor(fac.getFid(), Constant.REPORT_LINE);
            if (counterFacSensor != null) {
                euis.add(counterFacSensor.getEui());
                fids.add(counterFacSensor.getFid());
            }
        }
        if (CollectionUtils.isEmpty(euis)) {
            return new HashSet<>();
        }
        //获取所有采集设备对应的工位（可能包含其他产线的工位）
        LambdaQueryWrapper<FacSensorEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(FacSensorEntity::getEui, euis);
        List<FacSensorEntity> facSensorEntities = facSensorMapper.selectList(lambdaQueryWrapper);
        Set<Integer> allfids = facSensorEntities.stream().map(FacSensorEntity::getFid).collect(Collectors.toSet());

        //得到不属于该产线的工位，再获取这些工位对应的产线
        allfids.removeAll(fids);
        if (CollectionUtils.isEmpty(allfids)) {
            return new HashSet<>();
        }
        LambdaQueryWrapper<FacilitiesEntity> facilitiesQueryWrapper = new LambdaQueryWrapper<>();
        facilitiesQueryWrapper.in(FacilitiesEntity::getFid, allfids);
        List<FacilitiesEntity> list = facilitiesMapper.selectList(facilitiesQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new HashSet<>();
        }
        //过滤掉产线相同的，即得到不同产线绑定同一个采集器的情况
        return list.stream().filter(o -> !o.getProductionLineId().equals(lineId)).map(FacilitiesEntity::getProductionLineId).collect(Collectors.toSet());
    }

    @Async
    @Override
    public void dealTarget(DeviceEntity entity, String key) {
        if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
            return;
        }
        // 从Redis中获取采集数据
        List<String> sensorValueList = redisService.popAllData(key);
        if (CollectionUtils.isEmpty(sensorValueList)) {
            return;
        }
        Map<String, SensorValueTemplate> map = new HashMap<>(8);
        for (String sensorValue : sensorValueList) {
            List<SensorValueTemplate> sensorValueTemplates = JSON.parseArray(sensorValue, SensorValueTemplate.class);
            for (SensorValueTemplate sensorValueTemplate : sensorValueTemplates) {
                map.put(sensorValueTemplate.getEname(), sensorValueTemplate);
            }
        }

        //正常处理指标数据
        dealTargetData(new ArrayList<>(map.values()), entity, null);
    }

    @Async
    @Override
    public void dealCommonTarget(DeviceEntity entity, String key) {
        if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
            return;
        }
        // 从Redis中获取采集数据
        String sensorValue = (String) redisTemplate.opsForValue().get(key);
        redisTemplate.delete(key);
        if (StringUtils.isBlank(sensorValue)) {
            return;
        }

        SensorValueTemplate sensorValueTemplate = JacksonUtil.parseObject(sensorValue, SensorValueTemplate.class);
        List<SensorValueTemplate> sensorValueTemplates = Collections.singletonList(sensorValueTemplate);

        //正常处理指标数据
        dealTargetData(sensorValueTemplates, entity, null);
    }


    /**
     * 正常处理指标数据
     *
     * @param sensorValueTemplates
     * @param entity
     * @param iotTime
     */
    @Async
    @Override
    public void dealTargetData(List<SensorValueTemplate> sensorValueTemplates, DeviceEntity entity, Date iotTime) {
        //找到采集器是否有这个数据
        String batch = getBatch(entity);
        Date time = iotTime;
        if (time == null) {
            time = new Date();
        }

        //判断是否在工作时间段内，不在时间段内不产生告警
        boolean inWorkTime = workCalendarService.inWorkTime(entity.getDeviceId(), ModelEnum.DEVICE.getType(), new Date());
        List<Map.Entry<Object, Object>> toRedisList = new ArrayList<>();

        for (SensorValueTemplate sensorValueTemplate : sensorValueTemplates) {
            TargetModelEntity targetModelEntity = targetModelService.getByTargetModelMap(sensorValueTemplate.getEname(), entity.getModelId());
            //找到该指标
            if (targetModelEntity == null) {
                continue;
            }
            String dataVal = sensorValueTemplate.getValue();
            try {
                if (dataVal != null) {
                    String eName = sensorValueTemplate.getEname();
                    IndicatorEntityDTO dto = indicatorService.getFieldNameAndTableName(eName);
                    String fieldName = dto.getFieldName();
                    String tableName = dto.getTableName();
                    // 插入数据
                    indicatorService.addIndicator(tableName, entity.getDeviceId(), dataVal, time, batch);
                    //插入缓存
                    IndicatorEntityDTO build = IndicatorEntityDTO.builder().deviceId(entity.getDeviceId()).dataVal(dataVal).time(DateUtil.dateTimeStr(time)).batch(null).build();
                    toRedisList.add(
                            new AbstractMap.SimpleEntry<>(
                                    RedisKeyPrefix.getDeviceTargetKey(entity.getDeviceId(), eName),
                                    JacksonUtil.toJSONString(build)
                            )
                    );
//                    redisTemplate.opsForValue().set(RedisKeyPrefix.getDeviceTargetKey(entity.getDeviceId(), eName), JSON.toJSONString(build));
                    //指标聚合
//                    indicatorService.aggregation(tableName, fieldName, entity.getDeviceId(), dataVal, time, null);

                    // 阈值判断
                    if (inWorkTime) {
                        targetThresholdCommonService.judgeThreshold(targetModelEntity, dataVal, entity.getDeviceId(), entity.getDeviceCode());
                    }
                    // 处理组合指标
                    dealCombineTarget(entity, iotTime, targetModelEntity.getTargetName(), dataVal);
                }
            } catch (Exception e) {
                log.error("插入指标值失败", e);
            }
        }
        //批量设置reidis缓存
        redisTemplate.executePipelined(new SessionCallback() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                for (Map.Entry<Object, Object> entry : toRedisList) {
                    redisTemplate.opsForValue().set(entry.getKey(), entry.getValue());
                }
                return null;
            }
        });
    }

    /**
     * 处理组合指标
     */
    private void dealCombineTarget(DeviceEntity entity, Date iotTime, String keyTargetName, String dataVal) throws ParseException {
        if (iotTime == null) {
            return;
        }
        // 判断是否为组合指标的主指标，如果是，则设置缓存时间
        LambdaQueryWrapper<CombineTargetEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(CombineTargetEntity::getKeyTarget, keyTargetName)
                .eq(CombineTargetEntity::getModelId, entity.getModelId());
        List<CombineTargetEntity> combineTargetEntities = combineTargetService.list(qw);
        if (CollectionUtils.isEmpty(combineTargetEntities)) {
            return;
        }
        for (CombineTargetEntity combineTargetEntity : combineTargetEntities) {
            // 如果主指标先后顺序为后到，则直接绑定关联指标，否则设置缓存过期时间，待过期才进行绑定
            if (combineTargetEntity.getFirst()) {
                Map<String, Object> map = new HashMap<>();
                map.put(Constant.TIME_KEY, iotTime.getTime());
                map.put(Constant.DATA_KEY, dataVal);
                TimeUnit timeUnit = null;
                switch (combineTargetEntity.getUnit()) {
                    case Constant.HOUR:
                        timeUnit = TimeUnit.HOURS;
                        break;
                    case Constant.MINUTE:
                        timeUnit = TimeUnit.MINUTES;
                        break;
                    case Constant.SECOND:
                        timeUnit = TimeUnit.SECONDS;
                        break;
                    default:
                        break;
                }
                // key : 组合指标ID + 设备ID + 时间戳
                redisTemplate.opsForValue().set(RedisKeyPrefix.COMBINE_TARGET_ITEM + combineTargetEntity.getId() + Constant.UNDERLINE + entity.getDeviceId() + Constant.UNDERLINE + System.currentTimeMillis(), JSON.toJSONString(map), combineTargetEntity.getEnd(), timeUnit);
                redisTemplate.opsForValue().set(RedisKeyPrefix.COMBINE_TARGET_VALUE + combineTargetEntity.getId() + Constant.UNDERLINE + entity.getDeviceId() + Constant.UNDERLINE + System.currentTimeMillis(), JSON.toJSONString(map));
            } else {
                combineTargetService.bindRelatedTarget(iotTime.getTime(), dataVal, combineTargetEntity, entity.getDeviceId());
            }
        }
    }

    /**
     * 获取批次号
     *
     * @param entity
     * @return
     */
    private String getBatch(DeviceEntity entity) {
        /*FacilitiesEntity facilitiesEntity = facilitiesMapper.selectById(entity.getFid());
        if (facilitiesEntity == null) {
            return null;
        }
        return orderExecuteSeqService.getOccupyOrderByLineId(facilitiesEntity.getProductionLineId());*/

        /*LambdaQueryWrapper<ReportCountEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReportCountEntity::getLineId, facilitiesEntity.getProductionLineId())
                .orderByDesc(ReportCountEntity::getCreateTime).orderByDesc(ReportCountEntity::getId).last("limit 1");
        ReportCountEntity reportCountEntity = reportCountMapper.selectOne(wrapper);
        if (reportCountEntity == null) {
            return null;
        }
        if (ReportType.activeTypeList.contains(reportCountEntity.getType())) {
            return reportCountEntity.getWorkOrder();
        }
        return null;*/
        String workOrderNumber;
        try {
            workOrderNumber = orderExecuteSeqService.getCalOrderByLineId(entity.getDeviceId());
            if (StringUtils.isBlank(workOrderNumber)) {
                workOrderNumber = orderExecuteSeqService.getCalOrderByLineId(entity.getProductionLineId());
            }
        } catch (Exception e) {
            return null;
        }
        return workOrderNumber;
    }

    @Override
    public FacSensorEntity getCounterFacSensor(Integer fid, String type) {
        //查询出绑定的采集器
        FacSensorEntity facSensorEntity = facSensorMapper.getCounterEui(fid, SensorTypeCodeEnum.COUNTER_SENSOR.getCode());
        if (facSensorEntity != null) {
            facSensorEntity.setCountTarget(SensorParamEnum.COUNTER_COUNT.getEname());
            return facSensorEntity;
        }

        //工位 -> 生产设备 -> 计数器
        String eui = deviceMapper.getDeviceWithCounter(fid, SensorTypeCodeEnum.COUNTER_SENSOR.getCode());
        if (StringUtils.isNotBlank(eui)) {
            return FacSensorEntity.builder().fid(fid).eui(eui).countTarget(SensorParamEnum.COUNTER_COUNT.getEname()).build();
        }

        //查询出绑定了计数设备的生产设备
        FacSensorEntity entity = deviceMapper.getCountDeviceAndTarget(fid, type);
        if (entity == null) {
            return null;
        }
        eui = deviceSensorMapper.getEuiByDeviceIdAndTarget(entity.getDeviceId(), entity.getCountTarget());
        return FacSensorEntity.builder().eui(eui).countTarget(entity.getCountTarget()).build();
    }

    @Override
    public void genRecordAndAlarm(DeviceEntity entity, TargetModelEntity targetModel, Integer deviceId, Map<String, String> map) {

        //判断是否在工作时间段内，不在时间段内不产生告警
        boolean inWorkTime = workCalendarService.inWorkTime(deviceId, ModelEnum.DEVICE.getType(), new Date());

        for (Map.Entry<String, String> entry : map.entrySet()) {
            IndicatorEntityDTO dto = indicatorService.getFieldNameAndTableName(entry.getKey());
            String fieldName = dto.getFieldName();
            String tableName = dto.getTableName();
            String value = entry.getValue();
            if (StringUtils.isNotBlank(value)) {
                indicatorService.addIndicator(tableName, deviceId, value, new Date(), null);

                // 阈值判断
                if (!inWorkTime) {
                    continue;
                }
                QueryWrapper<TargetModelEntity> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(TargetModelEntity::getModelId, targetModel.getModelId())
                        .eq(TargetModelEntity::getTargetName, entry.getKey());
                TargetModelEntity modelEntity = targetModelService.getOne(wrapper);
                targetThresholdCommonService.judgeThreshold(modelEntity, value, entity.getDeviceId(), entity.getDeviceCode());
            }
        }
    }

    /*@Override
    public Double getUnqualifiedByCounter(TargetModelEntity targetModel, FacSensorEntity facSensor, Integer lineId,
                                          Double count, Date startDate, Date endDate) {
        //前后计数器相减算法
        //当计数器绑在出口位置，不合格量=前一个工位出口位置计数值-当前工位计数值
        //当计数器绑在入口位置，不合格量=当前工位计数值-下一个工位入口位置计数值
        Integer position = facSensor.getPosition();
        if (facSensor.getPosition() == SensorPositionEnum.OUTPUT.getCode()) {
            Double preCount = getNextFacCount(targetModel, lineId, -1, position, startDate, endDate);
            return preCount - count;
        } else {
            Double nextCount = getNextFacCount(targetModel, lineId, 1, position, startDate, endDate);
            return count - nextCount;
        }
    }

    private Double getNextFacCount(TargetModelEntity targetModel, Integer lineId, Integer offset,
                                   Integer sensorPosition, Date startDate, Date endDate) {
        Double count = 0.0;
        ModelEntity facModel = modelService.getNextFacModel(targetModel.getModelId(), offset);
        if (facModel != null) {
            LambdaQueryWrapper<FacilitiesEntity> facQw = new LambdaQueryWrapper<>();
            facQw.eq(FacilitiesEntity::getProductionLineId, lineId)
                    .eq(FacilitiesEntity::getModelId, facModel.getId());
            List<FacilitiesEntity> facilitiesEntities = facilitiesMapper.selectList(facQw);
            if (!CollectionUtils.isEmpty(facilitiesEntities)) {
                FacSensorEntity facSensorEntity = getCounterFacSensor(facilitiesEntities.get(0).getFid());
                //位置要相对应才能进行运算，出口对出口，入口对入口
                if (facSensorEntity != null && sensorPosition.equals(facSensorEntity.getPosition())) {
                    count = countService.getCount(facSensorEntity.getEui(), facSensorEntity.getCountTarget(), startDate, endDate);
                }
            }
        }
        return count;
    }*/

    /**
     * 获取产线特定检测工位
     * 若没标记则获取所有工位
     *
     * @return
     */
    @Override
    public List<FacilitiesEntity> getDetection(Integer productionLineId) {
        LambdaQueryWrapper<FacilitiesEntity> facilitiesWrapper = new LambdaQueryWrapper<>();
        facilitiesWrapper.eq(FacilitiesEntity::getProductionLineId, productionLineId);
        facilitiesWrapper.eq(FacilitiesEntity::getIsDefective, true);
        List<FacilitiesEntity> list = facilitiesMapper.selectList(facilitiesWrapper);
        if (CollectionUtils.isEmpty(list)) {
            //获取该产线下所有工位
            LambdaQueryWrapper<FacilitiesEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FacilitiesEntity::getProductionLineId, productionLineId);
            list = facilitiesMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(list)) {
                return new ArrayList<>();
            }
        }
        return list;
    }

    @Override
    public Double getAutoCountByLine(ProductionLineEntity line, Date start, Date end) {
        Double total = 0.0;
        List<FacilitiesEntity> facilitiesEntities = getLastFacilities(line);
        for (FacilitiesEntity facility : facilitiesEntities) {
            FacSensorEntity counterFacSensor = getCounterFacSensor(facility.getFid(), Constant.REPORT_LINE);
            if (counterFacSensor != null) {
                Double count = countService.getCount(counterFacSensor.getEui(), counterFacSensor.getCountTarget(), start, end);
                total += count;
            }
        }
        return total;
    }

    @Override
    public Double getAutoCountByLineAccumulation(ProductionLineEntity line, Date start, Date end, Integer recordId, String type) {
        List<EuiMarkEntity> markList = euiMarkService.getEuiMarkByIdAndType(recordId, type);
        Map<String, EuiMarkEntity> euiMarkMap = markList.stream().collect(groupingBy(EuiMarkEntity::getEui, Collectors.collectingAndThen(Collectors.toList(), o -> o.get(0))));

        List<FacilitiesEntity> facilitiesEntities = getLastFacilities(line);
        return getAutoCountByFacilities(start, end, recordId, type, euiMarkMap, facilitiesEntities);
    }


    @Override
    public Double getInputAutoCountByLineAccumulation(Integer lineId, Date start, Date end, Integer recordId, String type) {
        List<EuiMarkEntity> markList = euiMarkService.getEuiMarkByIdAndType(recordId, type);
        Map<String, EuiMarkEntity> euiMarkMap = markList.stream().collect(groupingBy(EuiMarkEntity::getEui, Collectors.collectingAndThen(Collectors.toList(), o -> o.get(0))));

        QueryWrapper<FacilitiesEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FacilitiesEntity::getIsInput, true).eq(FacilitiesEntity::getProductionLineId, lineId);
        List<FacilitiesEntity> facilitiesEntities = facilitiesMapper.selectList(queryWrapper);

        return getAutoCountByFacilities(start, end, recordId, type, euiMarkMap, facilitiesEntities);

    }

    private Double getAutoCountByFacilities(Date start, Date end, Integer recordId, String type, Map<String, EuiMarkEntity> euiMarkMap, List<FacilitiesEntity> facilitiesEntities) {
        List<EuiMarkEntity> list = new ArrayList<>();
        Double count = 0.0;
        for (FacilitiesEntity facility : facilitiesEntities) {
            FacSensorEntity counterFacSensor = getCounterFacSensor(facility.getFid(), Constant.REPORT_LINE);
            if (counterFacSensor != null) {
                EuiMarkEntity entity = euiMarkMap.get(counterFacSensor.getEui());
                Integer countId = null;
                Date date = start;
                if (entity != null) {
                    countId = entity.getCountId();
                    date = entity.getCountRecordTime();
                }
                EuiMarkEntity mark = countService.getCountAndRecordDate(counterFacSensor.getEui(), countId, date, end);
                list.add(mark);
                count += mark.getCount();
            }
        }

        //记录countId和时间
        for (EuiMarkEntity entity : list) {
            entity.setRecordId(recordId);
            entity.setType(type);
            LambdaUpdateWrapper<EuiMarkEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(EuiMarkEntity::getRecordId, recordId).eq(EuiMarkEntity::getType, type);
            euiMarkService.saveOrUpdate(entity, wrapper);
        }
        return count;
    }

    @Override
    public List<LineOeeDTO> getLineOee(List<ProductionLineEntity> lines, Date date) {
        if (CollectionUtils.isEmpty(lines)) {
            log.debug("计算产线OEE===产线为空,返回空集合");
            return new ArrayList<>();
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        Date begin = dictService.getDayOutputBeginTime(date);
        Date recordDate = DateUtil.formatToDate(begin, DateUtil.DATETIME_FORMAT_ZERO);
        //当天工作的小时数
        double workTimes = DateUtil.getTime(begin, date).doubleValue();
        double workHours = MathUtil.divideDouble(workTimes, 1000 * 60 * 60, 2);
        log.info("计算产线OEE===当前时间为:{},开工时间为:{}",
                DateUtil.format(date, DateUtil.DATETIME_FORMAT), DateUtil.format(begin, DateUtil.DATETIME_FORMAT));

        List<LineOeeDTO> lineOees = new ArrayList<>();
        WorkOrderEntity workOrderEntity;
        for (ProductionLineEntity lineEntity : lines) {
            if (!lineEntity.getIsCalOee()) {
                continue;
            }
            Integer lineId = lineEntity.getProductionLineId();
            String lineName = lineEntity.getName();
            log.info("计算产线OEE===产线ID:{},产线名称:{}", lineId, lineName);
            //获取日历时间
            Double workDuration = workCalendarService.getWorkDurationUpToNow(lineId, ModelEnum.LINE.getType(), date);
            /*if (workDuration != null) {
                workHours = workDuration;
            }*/
            //产线的理论工作时间
            double lineWorkTime = 0.0;
            Double convertCount = null;
            Double convertPlanQuantity = null;
            //产线开工时间
            Double productLineWorkTime = 0.0;
            //找到产线下dfs_record_work_order_line_day_count的所有工单记录
            List<RecordWorkOrderLineDayCountEntity> workOrderDayCountList;
            if (workCenterService.isOperationByLineId(lineId)) {
                //作业工单模式查找dayCount
                workOrderDayCountList = getWorkOrderDayCountListByOperation(recordDate, lineId);
            } else {
                LambdaQueryWrapper<RecordWorkOrderLineDayCountEntity> recordWorkOrderDayCountWrapper = new LambdaQueryWrapper<>();
                recordWorkOrderDayCountWrapper.eq(RecordWorkOrderLineDayCountEntity::getLineId, lineId)
                        .eq(RecordWorkOrderLineDayCountEntity::getTime, recordDate);
                workOrderDayCountList = recordWorkOrderLineDayCountService.list(recordWorkOrderDayCountWrapper);
            }

            double unqualified = workOrderDayCountList.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getUnqualified).sum();
            double count = workOrderDayCountList.stream().mapToDouble(RecordWorkOrderLineDayCountEntity::getCount).sum();
            if (count == 0) {
                if (workDuration == 0) {
                    log.info("计算产线OEE==={}产线无产量且日历配置为非工作日", lineName);
                    continue;
                }
                log.info("计算产线OEE==={}产线当天工单记录为空,遍历下一产线", lineName);
                lineOees.add(LineOeeDTO.builder().lineId(lineId).lineName(lineName)
                        .oee(0.0).capacity(0.0).count(0.0).unqualified(0.0).time(recordDate).build());
                continue;
            }

            if (workDuration == 0) {
                //当天有产量，但日历为非工作日，则取昨日或之前的日历
                workDuration = workCalendarService.getPreviousWorkCalendarDuration(lineId, ModelEnum.LINE.getType(), date);
                if (workDuration != 0) {
                    workHours = workDuration;
                }
            } else {
                workHours = workDuration;
            }

            //当日产能
            double capacity = workHours > 0.0 ? MathUtil.divideDouble(count, workHours, 2) : 0.0;
            //获取计划量
            Double planQuantity = workOrderPlanService.getPlanQuantityByOrders(workOrderDayCountList.stream().map(RecordWorkOrderLineDayCountEntity::getWorkOrderNumber).collect(Collectors.toList()), recordDate);

            for (RecordWorkOrderLineDayCountEntity countEntity : workOrderDayCountList) {
                String workOrderNumber = countEntity.getWorkOrderNumber();
                String materialCode = countEntity.getMaterialCode();
                Double singleCount = countEntity.getCount();
                workOrderEntity = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).one();
                // 找到对应产线下对应物料的产能
                WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getWorkCenterId());
                Double standardCapacity = getStandardCapacity(workCenterEntity.getType(), workOrderEntity.getProductionBasicUnitId(), materialCode);
                //求出工单的理论运行时间
                Double workOrderTime = standardCapacity == null ? 0 : MathUtil.divideDouble(singleCount, standardCapacity, 5);
                lineWorkTime += workOrderTime;

                Double scaleFactor = materialService.getScaleFactorByCode(materialCode);
                Double singleConvertCount = scaleFactor == null ? null : singleCount * scaleFactor;
                //获取计划量
                Double singlePlanQuantity = workOrderPlanService.getPlanQuantityByOrder(countEntity.getWorkOrderNumber(), recordDate);
                Double singleConvertPlanQuantity = scaleFactor == null ? null : singlePlanQuantity * scaleFactor;
                //当天所有工单开始时间与结束时间相加
                if (workOrderEntity != null) {
                    productLineWorkTime = productLineWorkTime + DateUtil.timeDifferenceHour(workOrderEntity.getActualStartDate(), workOrderEntity.getActualEndDate());
                }
                if (singleConvertCount != null) {
                    convertCount = convertCount == null ? 0 : convertCount;
                    convertCount += singleConvertCount;
                }
                if (singleConvertPlanQuantity != null) {
                    convertPlanQuantity = convertPlanQuantity == null ? 0 : convertPlanQuantity;
                    convertPlanQuantity += singleConvertPlanQuantity;
                }

                log.info("计算产线OEE==={}产线当天记录的工单号:{},物料编号:{},产量:{},不合格量:{},计算算得出=>转换产量:{},理论产能:{},理论运行时间:{}",
                        lineName, workOrderNumber, materialCode, singleCount, countEntity.getUnqualified(), singleConvertCount, standardCapacity, workOrderTime);
            }
            //double oee = workHours > 0.0 ? MathUtil.divideDouble(lineWorkTime, workHours, 4) * 100 : 0.0;
            //oee = oee > 0.0 ? MathUtil.round(oee, 2) : oee;
            double oee = workHours > 0.0 ? MathUtil.divideDouble(lineWorkTime, workHours, 4) : 0.0;
            log.info("计算产线OEE===计算出{}产线当天总产量:{},总不合格量:{},总转换产量:{},当天产能:{},理论工作时间总和:{},当天工作时长:{},OEE:{}",
                    lineName, count, unqualified, convertCount, capacity, lineWorkTime, workHours, oee);
            LineOeeDTO lineOeeDTO = LineOeeDTO.builder()
                    .lineId(lineId)
                    .lineName(lineName)
                    .count(count)
                    .unqualified(unqualified)
                    .convertCount(convertCount)
                    .oee(oee > 1 ? 1 : oee)
                    .capacity(capacity)
                    .convertPlanQuantity(convertPlanQuantity)
                    .planQuantity(planQuantity)
                    .productLineWorkTime(productLineWorkTime)
                    .time(recordDate)
                    .build();
            lineOees.add(lineOeeDTO);
        }
        return lineOees;
    }

    /**
     * 通过作业工单获取指定日期产量
     *
     * @param recordDate
     * @param lineId
     * @return
     */
    @Override
    public List<RecordWorkOrderLineDayCountEntity> getWorkOrderDayCountListByOperation(Date recordDate, Integer lineId) {
        List<RecordWorkOrderLineDayCountEntity> workOrderDayCountList = new ArrayList<>();
        try {
            ResponseData responseData = assignmentInterface.getOperationOrderDayCount(OperationDayCountDTO.builder().lineId(lineId).time(recordDate).build());
            if (responseData.getCode().equals(ResponseData.SUCCESS_CODE)) {
                List<OperationDayCountDTO> dto = JSONArray.parseArray(JSON.toJSONString(responseData.getData()), OperationDayCountDTO.class);
                workOrderDayCountList = dto.stream().map(
                        o -> RecordWorkOrderLineDayCountEntity.builder()
                                .workOrderNumber(o.getOperationNumber())
                                .count(o.getCount())
                                .unqualified(o.getUnqualified())
                                .lineId(o.getLineId())
                                .time(o.getTime())
                                .materialCode(o.getMaterialCode())
                                .build()
                ).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("请求作业工单获取每日产量异常", e);
        }
        return workOrderDayCountList;
    }


    /**
     * 获取产能
     *
     * @param materialCode
     * @return
     */
    @Override
    public Double getStandardCapacity(String workCenterType, Integer productionBasicUnitId, String materialCode) {
        LambdaQueryWrapper<CapacityEntity> capacityWrapper = new LambdaQueryWrapper<>();
        capacityWrapper.eq(CapacityEntity::getProductionBasicUnitId, productionBasicUnitId)
                .eq(CapacityEntity::getWorkCenterType, workCenterType)
                .eq(CapacityEntity::getMaterialCode, materialCode);
        CapacityEntity capacityEntity = capacityMapper.selectOne(capacityWrapper);
        if (capacityEntity != null) {
            MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
            //匹配第一单位
            if (CapacityUnitTypeEnum.FIRST.getCode().equals(capacityEntity.getUnitType())) {
                return capacityEntity.getCapacity();
            }
            //匹配第二单位，通过换算系数转单位
            if (CapacityUnitTypeEnum.SECOND.getCode().equals(capacityEntity.getUnitType())) {
                Rational rational = new Rational(materialEntity.getUnitNumerator(), materialEntity.getUnitDenominator());
//                Double scaleFactor = materialEntity.getScaleFactor();
                double scaleFactor = rational.doubleValue();
                return MathUtil.divideDouble(capacityEntity.getCapacity(), scaleFactor, 5);
            }
        }
        //查询不到产线、产品产能，则查询产线产能
        if (workCenterType.equals(WorkCenterTypeEnum.LINE.getCode())) {
            LambdaQueryWrapper<DictEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DictEntity::getType, Constant.LINE_CAPACITY).eq(DictEntity::getDes, productionBasicUnitId);
            DictEntity lineCapacity = dictService.getOne(wrapper);
            if (lineCapacity != null && StringUtils.isNotBlank(lineCapacity.getCode())) {
                return new Double(lineCapacity.getCode());
            }
        }
        //查询工厂产能
        LambdaQueryWrapper<DictEntity> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(DictEntity::getType, Constant.FACTORY_CAPACITY);
        DictEntity factoryCapacity = dictService.getOne(wrapper2);
        if (factoryCapacity != null && StringUtils.isNotBlank(factoryCapacity.getCode())) {
            return new Double(factoryCapacity.getCode());
        }
        return null;
    }

    /**
     * 获取工位
     */
    @Override
    public Integer getFidWhichBindSensor(JsonObjectEntity jsonDataEntity, String workOrderNum) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        Integer fid = null;
        // 查询采集器所关联的工位(如果存在多个工位，则只取第一个工位，如果找不到就找工单绑定的工位)
        LambdaQueryWrapper<FacSensorEntity> facSensorWrapper = new LambdaQueryWrapper<>();
        facSensorWrapper.eq(FacSensorEntity::getEui, jsonDataEntity.getDevName())
                .orderByDesc(FacSensorEntity::getCreateDate);
        List<FacSensorEntity> sensorEntities = facSensorMapper.selectList(facSensorWrapper);
        if (CollectionUtils.isEmpty(sensorEntities)) {
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNum);
            if (workOrderEntity != null) {
                // 如果不为空，获取工单绑定的工位，如果为多个，则取第一个工位
                LambdaQueryWrapper<FacilitiesEntity> facWrapper = new LambdaQueryWrapper<>();
                facWrapper.eq(FacilitiesEntity::getProductionLineId, workOrderEntity.getLineId());
                List<FacilitiesEntity> facilitiesEntities = facilitiesMapper.selectList(facWrapper);
                if (!CollectionUtils.isEmpty(facilitiesEntities)) {
                    fid = facilitiesEntities.get(0).getFid();
                }
            }
        } else {
            fid = sensorEntities.get(0).getFid();
        }
        return fid;
    }

    @Override
    public IndicatorEntityDTO getLatestTargetValue(Integer deviceId, String targetEname) {
        String dtoJson = (String) redisTemplate.opsForValue().get(RedisKeyPrefix.getDeviceTargetKey(deviceId, targetEname));
        IndicatorEntityDTO dto;
        if (StringUtils.isNotBlank(dtoJson)) {
            if (dtoJson.equals(Constant.NULL)) {
                return null;
            } else {
                dto = JSON.parseObject(dtoJson, IndicatorEntityDTO.class);
            }
        } else {
            IndicatorEntityDTO nameDTO = indicatorService.getFieldNameAndTableName(targetEname);
            dto = indicatorService.getNewVal(nameDTO.getTableName(), nameDTO.getFieldName(), deviceId);
            redisTemplate.opsForValue().set(RedisKeyPrefix.getDeviceTargetKey(deviceId, targetEname), JSON.toJSONString(dto));
        }
        return dto;
    }

    /**
     * 该方法供报工时更新历史oee使用
     * 代码重复，不方便抽取，注意LineOeeImpl方法的修改
     *
     * @param lineOees
     * @param begin
     * @param end
     * @param lineMap
     */
    @Override
    public void updateOee(List<LineOeeDTO> lineOees, Date begin, Date end, Map<Integer, Integer> lineMap) {

        for (LineOeeDTO oeeDTO : lineOees) {
            Integer lineId = oeeDTO.getLineId();
            Double count = oeeDTO.getCount();
            Double unqualified = oeeDTO.getUnqualified();
            Double oee = oeeDTO.getOee();
            Double capacity = oeeDTO.getCapacity();

            //获取计划量
            double planQuantity = oeeDTO.getPlanQuantity() == null ? 0 : oeeDTO.getPlanQuantity();
            //planQuantity不为null,方法会返回0
            double outputCompletionRate = planQuantity == 0 ? 0 : MathUtil.divideDouble(count, planQuantity, 2);
            //当天工作的小时数
            double workTimes = DateUtil.getTime(begin, end).doubleValue();
            double workHours = MathUtil.divideDouble(workTimes, 1000 * 60 * 60, 2);
            ModelEntity modelEntity = modelService.getById(lineMap.get(lineId));

            Double theoryEfficiency = getStandardCapacity(WorkCenterTypeEnum.LINE.getCode(), lineId, null);
            //当天工作效率=当天已加工数/当天实际产出产品的总时间
            double efficiency = workHours == 0 ? 0 : MathUtil.divideDouble(count, workHours, 3);
            //良品率=(已加工数量-不合格量）/已加工数量
            double yield = count == 0 ? 0 : MathUtil.divideDouble(count, (count + unqualified), 2);
            //性能利用率=当天工作效率/理论工作效率
            double performance = theoryEfficiency == null ? 0 : MathUtil.divideDouble(efficiency, theoryEfficiency, 2);
            //时间利用率(时间开动率)=运行时间/理论每天产线工作时长
            Integer theoryWorkingHours = modelEntity.getWorkingHours();
            double runRate = theoryWorkingHours == 0 ? 0 : MathUtil.divideDouble(workHours, theoryWorkingHours, 2);


            //插入或更新产线每天记录
            RecordLineDayUnionEntity dayUnionEntity = RecordLineDayUnionEntity.builder()
                    .productionLineId(lineId)
                    .count(count)
                    .unqualified(unqualified)
                    .convertCount(oeeDTO.getConvertCount())
                    .capacity(capacity)
                    .timeEfficiency(runRate)
                    .performance(performance)
                    .yield(yield)
                    .oee(oee)
                    .outputCompletionRate(outputCompletionRate)
                    .planQuantity(planQuantity)
                    .convertPlanQuantity(oeeDTO.getConvertPlanQuantity())
                    .productLineWorkTime(oeeDTO.getProductLineWorkTime())
                    .time(oeeDTO.getTime())
                    .build();
            updateLineDayUnionByRecordDate(dayUnionEntity, oeeDTO.getTime());
        }
    }

    /**
     * 增加工单投入数
     *
     * @param eui
     * @param currentCountId
     * @param count
     * @param workOrderNumber
     * @param inputRecordId
     */
    @Override
    public void dealInput(String eui, Integer currentCountId, double count, String workOrderNumber, Integer inputRecordId) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        Double inputTotal = workOrder.getInputTotal();
        inputTotal = inputTotal == null ? 0 : inputTotal;

        WorkOrderEntity build = WorkOrderEntity.builder().workOrderId(workOrder.getWorkOrderId()).inputTotal(inputTotal + count).build();
        workOrderService.updateById(build);

        workOrder.setInputTotal(build.getInputTotal());
        workOrderDayCountService.updateWorkOrderDayInput(workOrder);
    }

    @Override
    public Double getLastRecordBeforeToday(Date todayBeginTime, Integer deviceId, String fieldName) {
        String lastTargetKey = RedisKeyPrefix.getDeviceYesterdayLastTargetKey(deviceId, fieldName, DateUtil.dateStr(todayBeginTime));
        IndicatorEntityDTO indicatorEntityDTO = JSON.parseObject((String) redisTemplate.opsForValue().get(lastTargetKey), IndicatorEntityDTO.class);

        if (indicatorEntityDTO != null) {
            return Double.valueOf(indicatorEntityDTO.getDataVal());
        }

        indicatorEntityDTO = indicatorMapper.getNewValByTime(Constant.DFS_RECORD_DEVICE_ + fieldName, fieldName, deviceId, todayBeginTime);

        if (indicatorEntityDTO != null) {
            redisTemplate.opsForValue().set(lastTargetKey, JSON.toJSONString(indicatorEntityDTO), 24, TimeUnit.HOURS);
            return Double.valueOf(indicatorEntityDTO.getDataVal());
        }
        indicatorEntityDTO = IndicatorEntityDTO.builder().deviceId(deviceId).targetName(fieldName).dataVal("0.0").build();
        redisTemplate.opsForValue().set(lastTargetKey, JSON.toJSONString(indicatorEntityDTO), 24, TimeUnit.HOURS);
        return 0.0;
    }

    @Override
    public void initExcelContext(Integer templateId, String templateSheetName, String cleanSheetNames, ExportContext context, Class<?> clazz, String fullPathCode) {
        String templateFileUrl = null;
        if (Objects.nonNull(templateId)) {
            ModelUploadFileEntity uploadFile = modelUploadFileService.getById(templateId);
            templateFileUrl = uploadFile.getFileAddress();
        }
        initExcelContext(templateFileUrl, templateSheetName, cleanSheetNames, context, clazz, fullPathCode);
    }

    @Override
    public void initExcelContext(Integer templateId, String templateSheetName, String cleanSheetNames, ExportContext context, List<List<String>> head, String fullPathCode) {
        String templateFileUrl = null;
        if (Objects.nonNull(templateId)) {
            ModelUploadFileEntity uploadFile = modelUploadFileService.getById(templateId);
            templateFileUrl = uploadFile.getFileAddress();
        }
        initExcelContext(templateFileUrl, templateSheetName, cleanSheetNames, context, head, fullPathCode);
    }

    @Override
    public void initExcelContext(String templateFileUrl, String templateSheetName, String cleanSheetNames, ExportContext context, Class<?> clazz, String fullPathCode) {
        initExcelContext(templateFileUrl, templateSheetName, cleanSheetNames, context, clazz, null, fullPathCode);
    }

    @Override
    public void initExcelContext(String templateFileUrl, String templateSheetName, String cleanSheetNames, ExportContext context, List<List<String>> head, String fullPathCode) {
        initExcelContext(templateFileUrl, templateSheetName, cleanSheetNames, context, null, head, fullPathCode);
    }

    private void initExcelContext(String templateFileUrl, String templateSheetName, String cleanSheetNames, ExportContext context, Class<?> clazz, List<List<String>> head, String fullPathCode) {
        if (StringUtils.isNotBlank(templateFileUrl)) {
            //保存通道
            if (context.getOutputStream() == null) {
                PipedOutputStream pos = new PipedOutputStream();
                try {
                    PipedInputStream pis = new PipedInputStream(pos);
                    context.setInputStream(pis);
                    //此处单独起线程避免线程互相等待死锁
                    FutureTask<String> futureTask = new FutureTask<>(
                            () -> storageService.write(context.getFileName(), context.getInputStream()));
                    new Thread(futureTask).start();
                    context.setFuture(futureTask);
                } catch (IOException e) {
                    ExceptionUtil.wrap2Runtime(e);
                }
                context.setOutputStream(pos);
            }
            if (context.getExcelWriter() == null) {
                //获取模板流
                byte[] bytes = fdfsClientWrapper.download(templateFileUrl);
                InputStream inputStream = new ByteArrayInputStream(bytes);

                // 1.如果已经存在该 sheet,需要清空
                InputStream temInputStream = ExcelUtil.clearSheet(inputStream, cleanSheetNames);

                //根据模板写入数据表格
                ExcelWriter excelWriter = EasyExcelFactory
                        .write(context.getOutputStream())
                        .excelType(ExcelTypeEnum.XLSX)
                        .autoCloseStream(false)
                        .withTemplate(temInputStream)
                        .registerWriteHandler(new AdaptiveColumnWidthStrategy())
                        .build();
                context.setExcelWriter(excelWriter);
            }
        }
        // 2.如果已经存在该 sheet,需要清空
        ExcelUtil.cleanSheet(context.getExcelWriter(), templateSheetName);
        WriteSheet sheet = null;
        if (!CollectionUtils.isEmpty(head)) {
            sheet = EasyExcelFactory.writerSheet(templateSheetName).head(head).build();
        } else {
            // 初始化前读取表单配置的自定义名称，重写到ExcelProperty注解里
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(fullPathCode)) {
                FormFieldInterface formFieldInterface = SpringUtil.getBean(FormFieldInterface.class);
                List<FormFieldNameDTO> dtos = JacksonUtil.getResponseArray(formFieldInterface.detailRuleByFullPathCode(FullRouteCodeDTO.builder().fullPathCode(fullPathCode).build()), FormFieldNameDTO.class);
                excelPropertyHandler.processDynamicExcelProperties(clazz, dtos);
            }
            sheet = EasyExcelFactory.writerSheet(templateSheetName).head(clazz).build();
        }
        if (StringUtils.isNotBlank(templateFileUrl)) {
            // 如果不存在该 sheet，则需要新增
            ExcelUtil.addSheet(context.getExcelWriter(), templateSheetName, head, clazz);
        }
        context.setWriteSheet(sheet);
    }

    @Override
    public ImportProgressDTO getImportProgressDTO(String redisKey, Exception e) {
        ImportProgressDTO build;
        Object obj = redisTemplate.opsForValue().get(redisKey);
        if (obj != null) {
            build = JSONObject.parseObject((String) obj, ImportProgressDTO.class);
        } else {
            build = ImportProgressDTO.builder().progress(0.0).build();
        }
        build.setExecutionStatus(false);
        if (e instanceof ResponseException) {
            build.setExecutionDescription(e.getMessage());
        } else if (e instanceof DeleteReturnException) {
            build.setExecutionDescription(e.getMessage());
            build.setImportUrl(((DeleteReturnException) e).getDetail());
        } else {
            build.setExecutionDescription("程序发生错误，请联系开发人员解决");
        }
        build.setCode(Result.FAIL_CODE);
        return build;
    }

    @Override
    public void updateProgress(String progressKey, Double process, String message) {
        boolean executionStatus = process.compareTo(1.0) == 0;
        String execInfo = executionStatus ? "操作执行完成" : "操作执行中";
        ImportProgressDTO build = ImportProgressDTO.builder()
                .progress(process)
                .executionDescription(StringUtils.isNotBlank(message) ? message : execInfo)
                .executionStatus(executionStatus)
                .build();
        redisTemplate.opsForValue().set(progressKey, JSON.toJSONString(build), 1, TimeUnit.HOURS);
    }

    @Override
    public void importProgressException(String importProcessKey, Exception e) {
        Object importObj = redisTemplate.opsForValue().get(importProcessKey);
        ImportProgressDTO build;
        if (importObj != null) {
            build = JSON.parseObject((String) importObj, ImportProgressDTO.class);
        } else {
            build = ImportProgressDTO.builder()
                    .progress(0.0).build();
        }
        build.setExecutionStatus(true);
        build.setExecutionDescription(StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : e.toString());
        redisTemplate.opsForValue().set(importProcessKey, JSON.toJSONString(build), 1, TimeUnit.HOURS);
    }


    @Override
    public ImportProgressDTO getProgress(String importProgressKey) {
        Object obj = redisTemplate.opsForValue().get(importProgressKey);
        return JSONObject.parseObject((String) obj, ImportProgressDTO.class);
    }

    /**
     * 记录日志
     */
    @Override
    public void recordLog(String username, String logMsg) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, username).one();
        OperationLogEntity productOrderLogEntity = OperationLogEntity.builder()
                .module("计划调度")
                .type(OperationType.ADD)
                .des(logMsg)
                .username(StringUtils.isNotBlank(username) ? username : "admin")
                .nickname(Objects.nonNull(userEntity) ? userEntity.getNickname() : "管理员")
                .createTime(new Date())
                .build();
        operationLogService.save(productOrderLogEntity);
    }

    @Override
    public void releaseLock(List<String> pushOrderNumbers, String lockKeyPrefix, String username) {
        for (String number : pushOrderNumbers) {
            redisTemplate.delete(lockKeyPrefix + number);
        }
        redisTemplate.delete(lockKeyPrefix + username);
    }

    @Override
    public void initLockProgress(String progressKey, List<String> orderNumbers, String lockKeyPrefix, int timeout, TimeUnit timeUnit, String username) {
        // 给用户加锁
        Boolean usernameLockStatus = redisTemplate.opsForValue().setIfAbsent(lockKeyPrefix + username, new Date(), 1, TimeUnit.HOURS);
        if (usernameLockStatus == null || !usernameLockStatus) {
            ImportProgressDTO build = ImportProgressDTO.builder()
                    .progress(Double.valueOf(com.yelink.dfscommon.constant.Constant.ZERO_VALUE))
                    .executionDescription(RespCodeEnum.SOMEONE_IS_PERFORMING_THE_OPERATION.getMsgDes())
                    .executionStatus(true)
                    .build();
            redisTemplate.opsForValue().set(progressKey, JSON.toJSONString(build), 1, TimeUnit.HOURS);
            // 抛出异常,不在继续执行下去
            throw new ResponseException(RespCodeEnum.SOMEONE_IS_PERFORMING_THE_OPERATION);
        }
        // 给单加锁
        synchronized (this) {
            List<String> cacheNumbers = new ArrayList<>();
            boolean exec = true;
            for (String number : orderNumbers) {
                cacheNumbers.add(number);
                Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(lockKeyPrefix + number, new Date(), timeout, timeUnit);
                if (lockStatus == null || !lockStatus) {
                    exec = false;
                    break;
                }
            }
            if (!exec) {
                // 最后这个订单正在执行操作，请勿重复操作
                String number = cacheNumbers.get(cacheNumbers.size() - 1);
                // 需要删除没有执行操作,但刚才校验的时候加到redis的单据
                for (int i = 0; i < cacheNumbers.size() - 1; i++) {
                    redisTemplate.delete(lockKeyPrefix + cacheNumbers.get(i));
                }

                ImportProgressDTO build = ImportProgressDTO.builder()
                        .progress(Double.valueOf(com.yelink.dfscommon.constant.Constant.ZERO_VALUE))
                        .executionDescription(number + "正在被执行操作，请勿重复操作")
                        .executionStatus(true)
                        .build();
                redisTemplate.opsForValue().set(progressKey, JSON.toJSONString(build), 1, TimeUnit.HOURS);
                // 抛出异常,不在继续执行下去
                throw new ResponseException(number + "正在被执行操作，请勿重复操作");
            }
        }
        ImportProgressDTO build = ImportProgressDTO.builder()
                .progress(Double.valueOf(com.yelink.dfscommon.constant.Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .build();
        redisTemplate.opsForValue().set(progressKey, JSON.toJSONString(build), 1, TimeUnit.HOURS);
    }

}
