package com.yelink.dfs.service.impl.product;

import com.yelink.dfs.constant.DictTypeEnum;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.product.dto.MaterialTypeImportDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.product.MaterialTypeService;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 物料类型业务实现类
 * @author: shuang
 * @time: 2023/2/23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MaterialTypeServiceImpl implements MaterialTypeService {

    private final ImportProgressService importProgressService;
    private final ImportDataRecordService importDataRecordService;
    private final DictService dictService;

    @Async
    @Override
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        importProgressService.initLockProgress(RedisKeyPrefix.MATERIAL_TYPE_LOCK, RedisKeyPrefix.MATERIAL_TYPE_PROGRESS,"物料类型数据");
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<MaterialTypeImportDTO> materialTypeImportDTOS = EasyExcelUtil.read(inputStream, MaterialTypeImportDTO.class, 0, 2);
            List<DictEntity> dictEntities = this.verifyFormat(materialTypeImportDTOS);
            String importUrl = importProgressService.verifyResultToExcelUploadTo(materialTypeImportDTOS, MaterialTypeImportDTO.class, operationUsername);
            List<MaterialTypeImportDTO> materialTypeImportDTOList = materialTypeImportDTOS.stream().filter(MaterialTypeImportDTO::getVerifyPass).collect(Collectors.toList());
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.MATERIAL_TYPE_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.MATERIAL_TYPE_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(materialTypeImportDTOS.size() - materialTypeImportDTOList.size())
                    .successNumber(materialTypeImportDTOList.size())
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            int rowTotal = dictEntities.size();
            if (rowTotal > 0) {
                for (int i = 0; i < rowTotal; i++) {
                    DictEntity dictEntity = dictEntities.get(i);
                    dictEntity.setCreateBy(operationUsername);
                    dictEntity.setCreateTime(Objects.nonNull(dictEntity.getCreateTime()) ? dictEntity.getCreateTime() : new Date());
                    dictEntity.setType(DictTypeEnum.MATERIAL_TYPE.getType());
                    dictService.save(dictEntity);
                    importProgressService.updateProgress(RedisKeyPrefix.MATERIAL_TYPE_PROGRESS, importUrl, materialTypeImportDTOS.size(), rowTotal, i + 1,"物料类型数据");
                }
            } else {
                importProgressService.updateProgress(RedisKeyPrefix.MATERIAL_TYPE_PROGRESS, importUrl, materialTypeImportDTOS.size(), 0, 0,"物料类型数据");
            }
        }catch (Exception e){
                importProgressService.importProgressException(RedisKeyPrefix.MATERIAL_TYPE_PROGRESS,e,"物料类型数据");
                log.error("excel导入数据错误", e);
        }finally {
            // 释放锁
            importProgressService.releaseLock(RedisKeyPrefix.MATERIAL_TYPE_LOCK);
            IOUtils.closeQuietly(inputStream);
        }
    }


    /**
     * 校验数据
     * @param materialTypeImportDTOS
     * @return
     */
    private List<DictEntity> verifyFormat(List<MaterialTypeImportDTO> materialTypeImportDTOS) {
        List<DictEntity> list = new ArrayList<>();
        boolean temp;
        StringBuilder verifyInfo;
        for (MaterialTypeImportDTO materialTypeImportDTO : materialTypeImportDTOS) {
            temp = true;verifyInfo = new StringBuilder();
            if (StringUtils.isBlank(materialTypeImportDTO.getName())) {
                verifyInfo.append("物料类型名称不能为空；");
                temp = false;
            }else if(!materialTypeImportDTO.getName().matches(Constant.NAME_REX_FIFTY)){
                verifyInfo.append("物料类型名称只能包含汉字、英文字母、空格、英文点号、中划线、下划线、左右括号、井号、星号和斜杠、长度最大为120，不分顺序；");
                temp = false;
            }else{
                Long count = dictService.lambdaQuery()
                        .eq(DictEntity::getType, DictTypeEnum.MATERIAL_TYPE.getType())
                        .eq(DictEntity::getName,materialTypeImportDTO.getName())
                        .count();
                boolean nameReStatus = list.stream().anyMatch(dictEntity -> dictEntity.getName().equals(materialTypeImportDTO.getName()));
                if(count > 0 || nameReStatus ){
                    verifyInfo.append("物料类型名称在系统中已存在；");
                    temp = false;
                }
            }
            if(StringUtils.isNotBlank(materialTypeImportDTO.getDesc()) && materialTypeImportDTO.getDesc().length() > 100){
                verifyInfo.append("物料类型描述不能超过100");
                temp = false;
            }
            materialTypeImportDTO.setVerifyPass(temp);
            if(temp){
                list.add(DictEntity.builder()
                        .name(materialTypeImportDTO.getName())
                        .des(materialTypeImportDTO.getDesc())
                        .createTime(Objects.nonNull(materialTypeImportDTO.getCreateTime()) ? materialTypeImportDTO.getCreateTime() : new Date())
                        .build());
                materialTypeImportDTO.setImportResult("数据校验通过");
            }else{
                materialTypeImportDTO.setImportResult(verifyInfo.toString());
            }
        }
        return list;
    }

    @Override
    public ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.MATERIAL_TYPE_PROGRESS);
    }
}
