package com.yelink.dfs.service.impl.product;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.product.dto.MaterialAttributeTypeImportDTO;
import com.yelink.dfs.entity.product.dto.MaterialAttributeTypeSelectDTO;
import com.yelink.dfs.mapper.product.MaterialAttributeMapper;
import com.yelink.dfs.mapper.product.MaterialAttributeTypeMapper;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.product.MaterialAttributeTypeService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAttributeTypeEntity;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-04-14 14:20
 */
@Slf4j
@Service
@AllArgsConstructor
public class MaterialAttributeTypeServiceImpl extends ServiceImpl<MaterialAttributeTypeMapper, MaterialAttributeTypeEntity> implements MaterialAttributeTypeService {

    private SysUserService sysUserService;
    private MaterialAttributeMapper materialAttributeMapper;
    private ImportProgressService importProgressService;
    private ImportDataRecordService importDataRecordService;

    @Override
    public List<MaterialAttributeTypeEntity> getTree() {
        List<MaterialAttributeTypeEntity> list = this.lambdaQuery().list();
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        // 根据父级编码进行分组
        Map<String, List<MaterialAttributeTypeEntity>> childrenMap = list.stream().collect(Collectors.groupingBy(MaterialAttributeTypeEntity::getParentCode));
        List<MaterialAttributeTypeEntity> resultList = new ArrayList<>();
        // 添加根分类
        List<MaterialAttributeTypeEntity> rootTypeEntities = childrenMap.get("");
        if (CollectionUtils.isEmpty(rootTypeEntities)) {
            return resultList;
        }
        rootTypeEntities.sort(Comparator.comparing(MaterialAttributeTypeEntity::getSeq));
        resultList.addAll(rootTypeEntities);
        for (MaterialAttributeTypeEntity entity : resultList) {
            // 递归查找该节点的子节点并添加到当前节点的 children 中
            setChildren(entity, childrenMap);
        }
        return resultList;
    }

    private void setChildren(MaterialAttributeTypeEntity entity, Map<String, List<MaterialAttributeTypeEntity>> childrenMap) {
        if (entity == null) {
            return;
        }
        List<MaterialAttributeTypeEntity> children = childrenMap.get(entity.getAttributeTypeCode());
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        children.sort(Comparator.comparing(MaterialAttributeTypeEntity::getSeq));
        entity.setChildren(children);
        for (MaterialAttributeTypeEntity childrenEntity : children) {
            setChildren(childrenEntity, childrenMap);
        }
    }

    @Override
    public Page<MaterialAttributeTypeEntity> getList(MaterialAttributeTypeSelectDTO selectDTO) {
        int current = selectDTO.getCurrent() == null ? 1 : selectDTO.getCurrent();
        int size = selectDTO.getSize() == null ? Integer.MAX_VALUE : selectDTO.getSize();
        LambdaQueryWrapper<MaterialAttributeTypeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(selectDTO.getAttributeTypeCode()), MaterialAttributeTypeEntity::getAttributeTypeCode, selectDTO.getAttributeTypeCode())
                .like(StringUtils.isNotBlank(selectDTO.getAttributeTypeName()), MaterialAttributeTypeEntity::getAttributeTypeName, selectDTO.getAttributeTypeName())
        ;
        // 父级分类名称
        if (StringUtils.isNotBlank(selectDTO.getParentName())) {
            List<String> parentCodes = this.lambdaQuery().select(MaterialAttributeTypeEntity::getAttributeTypeCode)
                    .like(MaterialAttributeTypeEntity::getAttributeTypeName, selectDTO.getParentName()).list()
                    .stream().map(MaterialAttributeTypeEntity::getAttributeTypeCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(parentCodes)) {
                return new Page<>();
            }
            wrapper.in(MaterialAttributeTypeEntity::getParentCode, parentCodes);
        }
        // 分类编码
        if (StringUtils.isNotBlank(selectDTO.getAttributeTypeCodes())) {
            List<String> attributeTypeCodes = Arrays.stream(selectDTO.getAttributeTypeCodes().split(Constant.SEP)).collect(Collectors.toList());
            wrapper.in(MaterialAttributeTypeEntity::getAttributeTypeCode, attributeTypeCodes);
        }
        Page<MaterialAttributeTypeEntity> page = this.page(new Page<>(current, size), wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new Page<>();
        }
        // 展示名称
        showName(page.getRecords());
        return page;
    }

    private void showName(List<MaterialAttributeTypeEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> usernameSet = list.stream().map(MaterialAttributeTypeEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toSet());
        usernameSet.addAll(list.stream().map(MaterialAttributeTypeEntity::getUpdateBy).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String, String> nickNames = sysUserService.getUserNameNickMap(new ArrayList<>(usernameSet));
        Map<String, String> typeNameMap = this.getTypeNameMap(null);
        for (MaterialAttributeTypeEntity entity : list) {
            entity.setCreateByName(nickNames.get(entity.getCreateBy()));
            entity.setUpdateByName(nickNames.get(entity.getUpdateBy()));
            // 父级分类名称
            entity.setParentName(typeNameMap.get(entity.getParentCode()));
        }
    }

    private Map<String, String> getTypeNameMap(List<String> typeCodes) {
        return this.lambdaQuery().in(CollectionUtils.isNotEmpty(typeCodes), MaterialAttributeTypeEntity::getAttributeTypeCode, typeCodes).list()
                .stream().collect(Collectors.toMap(MaterialAttributeTypeEntity::getAttributeTypeCode, MaterialAttributeTypeEntity::getAttributeTypeName));
    }

    @Override
    public void delete(Integer id) {
        MaterialAttributeTypeEntity entity = this.getById(id);
        String attributeTypeCode = entity.getAttributeTypeCode();
        Long count = this.lambdaQuery().eq(MaterialAttributeTypeEntity::getParentCode, attributeTypeCode).count();
        if (count > 0) {
            throw new ResponseException("已绑定子分类，不能删除");
        }
        LambdaQueryWrapper<MaterialAttributeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialAttributeEntity::getParentCode, attributeTypeCode);
        Long count1 = materialAttributeMapper.selectCount(wrapper);
        if (count1 > 0) {
            throw new ResponseException("已绑定物料属性，不能删除");
        }
        this.removeById(id);
    }

    @Override
    public MaterialAttributeTypeEntity detail(Integer id) {
        MaterialAttributeTypeEntity entity = this.getById(id);
        showName(Stream.of(entity).collect(Collectors.toList()));
        return entity;
    }

    @Override
    public void saveEntity(MaterialAttributeTypeEntity entity) {
        Long count = this.lambdaQuery().eq(MaterialAttributeTypeEntity::getAttributeTypeCode, entity.getAttributeTypeCode()).count();
        if (count > 0) {
            throw new ResponseException("分类编码重复");
        }
        Long count1 = this.lambdaQuery().eq(MaterialAttributeTypeEntity::getAttributeTypeName, entity.getAttributeTypeName()).count();
        if (count1 > 0) {
            throw new ResponseException("分类名称重复");
        }
        // 检查和设值
        this.checkAndSetData(entity);
        this.save(entity);
    }

    @Override
    public void updateEntity(MaterialAttributeTypeEntity entity) {
        MaterialAttributeTypeEntity oldEntity = this.getById(entity.getId());
        String newParentCode = entity.getParentCode();
        // 上级变更
        if (!oldEntity.getParentCode().equals(newParentCode)) {
            // 检查和设值
            this.checkAndSetData(entity);
            // 更新子级的全路径分类编码
            this.baseMapper.updateSonFullCode(oldEntity.getFullAttributeTypeCode() + Constants.SPOT,
                    entity.getFullAttributeTypeCode() + Constants.SPOT);
        }
        this.updateById(entity);
    }

    /**
     * 检查和设值
     *
     * @param entity
     */
    private void checkAndSetData(MaterialAttributeTypeEntity entity) {
        String attributeTypeCode = entity.getAttributeTypeCode();
        String parentCode = entity.getParentCode();
        if (attributeTypeCode.equals(parentCode)) {
            throw new ResponseException("上级分类不能为本分类");
        }
        // 环判断，父级分类的全路径编码包含本级编码，则存在环
        String parentFullAttributeTypeCode = this.getParentFullAttributeTypeCode(parentCode);
        List<String> typeCodes = Arrays.stream(parentFullAttributeTypeCode.split(Constants.SPOT_SPLIT)).collect(Collectors.toList());
        if (typeCodes.contains(attributeTypeCode)) {
            throw new ResponseException("分类循环");
        }
        // seq调整
        Long seq = this.lambdaQuery().eq(MaterialAttributeTypeEntity::getParentCode, parentCode).count();
        entity.setSeq(seq.intValue() + 1);
        String fullAttributeTypeCode;
        // 全路径编码调整
        if (StringUtils.isNotBlank(parentFullAttributeTypeCode)) {
            fullAttributeTypeCode = parentFullAttributeTypeCode + Constants.SPOT + attributeTypeCode;
        } else {
            fullAttributeTypeCode = attributeTypeCode;
        }
        entity.setFullAttributeTypeCode(fullAttributeTypeCode);
    }

    private String getParentFullAttributeTypeCode(String parentCode) {
        MaterialAttributeTypeEntity parentEntity = this.lambdaQuery().select(MaterialAttributeTypeEntity::getFullAttributeTypeCode)
                .eq(MaterialAttributeTypeEntity::getAttributeTypeCode, parentCode).one();
        if (parentEntity == null) {
            return "";
        }
        return parentEntity.getFullAttributeTypeCode();
    }

    @Override
    public void seqAdjust(List<MaterialAttributeTypeEntity> list) {
        List<MaterialAttributeTypeEntity> seqList = new ArrayList<>();
        int i = 1;
        for (MaterialAttributeTypeEntity entity : list) {
            seqList.add(MaterialAttributeTypeEntity.builder().id(entity.getId()).seq(i++).build());
            setChildrenSeq(seqList, entity.getChildren());
        }
        this.updateBatchById(seqList);
    }

    private void setChildrenSeq(List<MaterialAttributeTypeEntity> seqList, List<MaterialAttributeTypeEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int i = 1;
        for (MaterialAttributeTypeEntity entity : list) {
            seqList.add(MaterialAttributeTypeEntity.builder().id(entity.getId()).seq(i++).build());
            setChildrenSeq(seqList, entity.getChildren());
        }
    }

    @Async
    @Override
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        final String lockKey = RedisKeyPrefix.MATERIAL_ATTRIBUTE_TYPE_IMPORT_LOCK;
        importProgressKey = RedisKeyPrefix.MATERIAL_ATTRIBUTE_TYPE_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        try {
            //1、读取excel第二个sheet,从第3行开始读的数据
            List<MaterialAttributeTypeImportDTO> totalImportRecords = EasyExcelUtil.read(inputStream, MaterialAttributeTypeImportDTO.class, 1, 2);
            //2、校验数据
            List<MaterialAttributeTypeEntity> canImportRecords = verifyFormat(totalImportRecords);

            //3、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, MaterialAttributeTypeImportDTO.class, operationUsername);
            //4、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(MaterialAttributeTypeImportDTO::getVerifyPass).count();
            Date nowDate = new Date();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.MATERIAL_ATTRIBUTE_TYPE_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.MATERIAL_ATTRIBUTE_TYPE_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(nowDate)
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(nowDate)
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //5、保存数据
            int successCount = canImportRecords.size(), failCount = totalImportRecords.size() - canImportRecords.size();
            // 新增
            for (MaterialAttributeTypeEntity entity : canImportRecords) {
                entity.setCreateBy(operationUsername);
                entity.setUpdateBy(operationUsername);
                entity.setCreateTime(Objects.nonNull(entity.getCreateTime()) ? entity.getCreateTime() : new Date());
                entity.setUpdateTime(nowDate);
            }
            this.saveBatch(canImportRecords);
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.error("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(inputStream);
            importProgressService.releaseLock(lockKey);
        }
    }

    private List<MaterialAttributeTypeEntity> verifyFormat(List<MaterialAttributeTypeImportDTO> imports) {
        log.info("校验属性分类导入数据：{}", JSONObject.toJSONString(imports));

        List<MaterialAttributeTypeEntity> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (MaterialAttributeTypeImportDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            // 属性分类编号
            if (StringUtils.isBlank(importDTO.getAttributeTypeCode())) {
                importResult.append("分类编码不能为空；");
                canImport = false;
            } else {
                Long count = this.lambdaQuery().eq(MaterialAttributeTypeEntity::getAttributeTypeCode, importDTO.getAttributeTypeCode()).count();
                if (count > 0) {
                    importResult.append("分类编码已存在；");
                    canImport = false;
                }
            }
            // 分类名称
            if (StringUtils.isBlank(importDTO.getAttributeTypeName())) {
                importResult.append("分类名称不能为空；");
                canImport = false;
            } else {
                Long count = this.lambdaQuery().eq(MaterialAttributeTypeEntity::getAttributeTypeName, importDTO.getAttributeTypeName()).count();
                if (count > 0) {
                    importResult.append("分类名称已存在；");
                    canImport = false;
                }
            }
            // 上级分类编码
            if (StringUtils.isNotBlank(importDTO.getParentCode())) {
                Long count = this.lambdaQuery().eq(MaterialAttributeTypeEntity::getAttributeTypeCode, importDTO.getParentCode()).count();
                if (count == 0) {
                    importResult.append("上级分类编码不存在；");
                    canImport = false;
                }
            }
            MaterialAttributeTypeEntity typeEntity = JacksonUtil.convertObject(importDTO, MaterialAttributeTypeEntity.class);
            try {
                this.checkAndSetData(typeEntity);
            } catch (Exception e) {
                importResult.append(e.getMessage());
                canImport = false;
            }

            importDTO.setVerifyPass(canImport);
            if (Boolean.TRUE.equals(importDTO.getVerifyPass())) {
                canImports.add(typeEntity);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    @Override
    public ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.MATERIAL_ATTRIBUTE_TYPE_IMPORT_PROGRESS);
    }
}
