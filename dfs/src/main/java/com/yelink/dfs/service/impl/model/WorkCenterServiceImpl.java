package com.yelink.dfs.service.impl.model;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.device.DevicesUseStateEnum;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitTypeDTO;
import com.yelink.dfs.entity.common.config.dto.FilterOperatorConfigDTO;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.model.LineEmployeeEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceRelevanceEntity;
import com.yelink.dfs.entity.model.WorkCenterLineRelevanceEntity;
import com.yelink.dfs.entity.model.WorkCenterTeamEntity;
import com.yelink.dfs.entity.model.WorkCenterTeamRelevanceEntity;
import com.yelink.dfs.entity.model.dto.ExcelParseWorkCenterDTO;
import com.yelink.dfs.entity.model.dto.ExcelWorkCenterDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterDeviceDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterEditDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterLineDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterRelevanceDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterRelevanceLineDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterSelectDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterTeamDTO;
import com.yelink.dfs.entity.model.vo.ExcelLogVO;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.ProcedureDeviceTypeEntity;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.user.MyAuthenticationUserDetail;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.model.GridMapper;
import com.yelink.dfs.mapper.model.WorkCenterMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.product.ProcedureMapper;
import com.yelink.dfs.open.v2.model.dto.WorkCenterDetailQueryDTO;
import com.yelink.dfs.open.v2.model.dto.WorkCenterQueryDTO;
import com.yelink.dfs.open.v2.model.vo.WorkCenterDetailVO;
import com.yelink.dfs.open.v2.model.vo.WorkCenterProductResourceVO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderDetailQueryDTO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderDetailVO;
import com.yelink.dfs.open.v2.supplier.vo.SupplierVO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.AreaService;
import com.yelink.dfs.service.model.LineEmployeeService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterDeviceRelevanceService;
import com.yelink.dfs.service.model.WorkCenterDeviceService;
import com.yelink.dfs.service.model.WorkCenterLineRelevanceService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.model.WorkCenterTeamRelevanceService;
import com.yelink.dfs.service.model.WorkCenterTeamService;
import com.yelink.dfs.service.order.IsolationService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.ProcedureDeviceTypeService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfs.service.user.SysTeamUserService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.user.TeamTypeDefService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfs.utils.SqlUtil;
import com.yelink.dfscommon.cache.RedisCache;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.IsolationDTO;
import com.yelink.dfscommon.dto.ProductionBasicUnitDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.ProcedureDeviceVO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamUserEntity;
import com.yelink.dfscommon.entity.dfs.TeamTypeDefEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.ValidateUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工作中心
 * Service
 *
 * <AUTHOR>
 * @Date 2020-12-03 11:14
 */
@Slf4j
@Service
public class WorkCenterServiceImpl extends ServiceImpl<WorkCenterMapper, WorkCenterEntity> implements WorkCenterService {
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    @Lazy
    private ProductionLineService productionLineService;
    @Autowired
    private SysTeamService teamService;
    @Autowired
    private WorkCenterTeamRelevanceService workCenterTeamRelevanceService;
    @Autowired
    private WorkCenterDeviceRelevanceService workCenterDeviceRelevanceService;
    @Autowired
    private WorkCenterLineRelevanceService workCenterLineRelevanceService;
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private ModelService modelService;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private WorkCenterTeamService workCenterTeamService;
    @Autowired
    private WorkCenterDeviceService workCenterDeviceService;
    @Autowired
    private AreaService areaService;
    @Resource
    private SysUserService sysUserService;
    @Autowired
    private GridMapper gridMapper;
    @Autowired
    private IsolationService isolationService;
    @Autowired
    private MessagePushToKafkaService messagePushToKafkaService;
    @Autowired
    private ProcedureMapper procedureMapper;
    @Autowired
    private TeamTypeDefService teamTypeDefService;
    @Autowired
    private BusinessConfigService businessConfigService;
    @Autowired
    private LineEmployeeService lineEmployeeService;
    @Autowired
    private SysTeamUserService teamUserService;
    @Resource
    private UserAuthenService userAuthenService;
    @Resource
    private ProcedureDeviceTypeService procedureDeviceTypeService;
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;
    @Resource
    private CraftProcedureService craftProcedureService;

    /**
     * 获取工作中心列表
     *
     * @return
     */
    @Override
    public Page<WorkCenterEntity> list(WorkCenterSelectDTO dto) {
        Integer currentPage = dto.getCurrentPage();
        Integer pageSize = dto.getPageSize();
        Boolean isIsolation = dto.getIsIsolation();
        String username = dto.getUsername();
        Page<WorkCenterEntity> page = new Page<>();

        //生产基本单元名称 过滤
        List<Integer> filterWorkCenterIds = null;
        if (!StringUtils.isEmpty(dto.getProductionBasicUnit())) {
            filterWorkCenterIds = conditionProductionBasicUnit(dto.getProductionBasicUnit());
            if (CollectionUtils.isEmpty(filterWorkCenterIds)) {
                return new Page<>();
            }
        }
        QueryWrapper<WorkCenterEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<WorkCenterEntity> labda = queryWrapper.lambda();
        labda.in(CollectionUtils.isNotEmpty(filterWorkCenterIds), WorkCenterEntity::getId, filterWorkCenterIds);
        labda.in(CollectionUtils.isNotEmpty(dto.getWorkCenterIds()), WorkCenterEntity::getId, dto.getWorkCenterIds());
        labda.in(CollectionUtils.isNotEmpty(dto.getAids()), WorkCenterEntity::getAid, dto.getAids());
        WrapperUtil.eq(labda, WorkCenterEntity::getAid, dto.getId());
        WrapperUtil.like(labda, WorkCenterEntity::getCode, dto.getCode());
        WrapperUtil.like(labda, WorkCenterEntity::getName, dto.getName());
        WrapperUtil.eq(labda, WorkCenterEntity::getType, dto.getType());
        WrapperUtil.eq(labda, WorkCenterEntity::getIsOperation, dto.getIsOperation());
        // 过滤生产基本单元类型id对应的工作中心
        filterProductBasicUnitType(dto, labda);
        if (StringUtils.isNotBlank(dto.getLineModelId())) {
            List<Integer> workCenterIds = new ArrayList<>();
            List<Integer> selectLineModelIds = Arrays.stream(dto.getLineModelId().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            List<WorkCenterEntity> workCenterEntities = this.lambdaQuery()
                    .eq(WorkCenterEntity::getType, WorkCenterTypeEnum.LINE.getCode())
                    .select(WorkCenterEntity::getId, WorkCenterEntity::getLineModelId).list();
            for (WorkCenterEntity workCenterEntity : workCenterEntities) {
                if (StringUtils.isBlank(workCenterEntity.getLineModelId())) {
                    continue;
                }
                List<Integer> list = Arrays.stream(workCenterEntity.getLineModelId().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
                for (Integer lineModelId : list) {
                    if (selectLineModelIds.contains(lineModelId)) {
                        workCenterIds.add(workCenterEntity.getId());
                    }
                }
            }
            if (CollectionUtils.isEmpty(workCenterIds)) {
                return page;
            } else {
                labda.in(WorkCenterEntity::getId, workCenterIds);
            }
        }
        labda.orderByDesc(WorkCenterEntity::getCreateTime);
        labda.orderByDesc(WorkCenterEntity::getId);
        labda.in(CollectionUtils.isNotEmpty(dto.getWorkCenterCodes()), WorkCenterEntity::getCode, dto.getWorkCenterCodes());

        //数据隔离(小程序)
        if (isIsolation != null && isIsolation) {
            List<Integer> workCenterIds = isolationService.getIsolationWorkCenterIds(username);
            if (CollectionUtils.isEmpty(workCenterIds)) {
                return page;
            }
            labda.in(WorkCenterEntity::getId, workCenterIds);
        }

        if (currentPage == null || pageSize == null) {
            //查询全部工作中心
            List<WorkCenterEntity> list = this.list(queryWrapper);
            page = new Page<>(1, list.size(), list.size());
            page.setRecords(list);
        } else {
            page = this.page(new Page<>(currentPage, pageSize), queryWrapper);
        }
        for (WorkCenterEntity workCenterEntity : page.getRecords()) {
            //展示所属产区
            AreaEntity areaEntity = areaService.getById(workCenterEntity.getAid());
            workCenterEntity.setAname(areaEntity == null ? null : areaEntity.getAname());
            List<ProductionBasicUnitDTO> productionBasicUnitDTOList = new ArrayList<>();
            //展示列表生产基本单元
            if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterEntity.getType())) {
                List<ProductionLineEntity> lineEntityList = this.listLineByCenterId(workCenterEntity.getId(), null, null, null);
                if (CollectionUtils.isNotEmpty(lineEntityList)) {
                    workCenterEntity.setProductionBasicUnit(lineEntityList.stream().map(ProductionLineEntity::getName).collect(Collectors.joining(Constant.SEP)));
                    lineEntityList.forEach(o -> {
                        ProductionBasicUnitDTO unitDTO = ProductionBasicUnitDTO.builder().id(o.getProductionLineId()).code(o.getProductionLineCode())
                                .name(o.getName()).type(workCenterEntity.getType()).build();
                        productionBasicUnitDTOList.add(unitDTO);
                    });
                }
            } else if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getType())) {
                List<SysTeamEntity> teamEntityList = this.listTeamByCenterId(workCenterEntity.getId(), null, null);
                if (CollectionUtils.isNotEmpty(teamEntityList)) {
                    workCenterEntity.setProductionBasicUnit(teamEntityList.stream().map(SysTeamEntity::getTeamName).collect(Collectors.joining(Constant.SEP)));
                    teamEntityList.forEach(o -> {
                        ProductionBasicUnitDTO unitDTO = ProductionBasicUnitDTO.builder().id(o.getId()).code(o.getTeamCode())
                                .name(o.getTeamName()).type(workCenterEntity.getType()).build();
                        productionBasicUnitDTOList.add(unitDTO);
                    });
                }
            } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterEntity.getType())) {
                List<DeviceEntity> deviceEntityList = this.listDeviceByCenterId(workCenterEntity.getId(), null, null, null);
                if (CollectionUtils.isNotEmpty(deviceEntityList)) {
                    workCenterEntity.setProductionBasicUnit(deviceEntityList.stream().map(DeviceEntity::getDeviceName).collect(Collectors.joining(Constant.SEP)));
                    deviceEntityList.forEach(o -> {
                        ProductionBasicUnitDTO unitDTO = ProductionBasicUnitDTO.builder().id(o.getDeviceId()).code(o.getDeviceCode())
                                .name(o.getDeviceName()).type(workCenterEntity.getType()).build();
                        productionBasicUnitDTOList.add(unitDTO);
                    });
                }
            }
            //展示列表生产基本单元列表
            workCenterEntity.setProductionBasicUnitDTOList(productionBasicUnitDTOList);
            //展示列表关联资源
            if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterEntity.getRelevanceType())) {
                List<ProductionLineEntity> relevanceLine = getRelevanceLines(workCenterEntity.getId());
                if (CollectionUtils.isNotEmpty(relevanceLine)) {
                    workCenterEntity.setProductionResources(relevanceLine.stream().map(ProductionLineEntity::getName).collect(Collectors.joining(Constant.SEP)));
                }
            } else if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getRelevanceType())) {
                List<SysTeamEntity> relevanceTeam = getRelevanceTeams(workCenterEntity.getId());
                if (CollectionUtils.isNotEmpty(relevanceTeam)) {
                    workCenterEntity.setProductionResources(relevanceTeam.stream().map(SysTeamEntity::getTeamName).collect(Collectors.joining(Constant.SEP)));
                }
            } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterEntity.getRelevanceType())) {
                List<DeviceEntity> relevanceDevice = getRelevanceDevice(workCenterEntity.getId());
                if (CollectionUtils.isNotEmpty(relevanceDevice)) {
                    workCenterEntity.setProductionResources(relevanceDevice.stream().map(DeviceEntity::getDeviceName).collect(Collectors.joining(Constant.SEP)));
                }
            }

        }
        return page;
    }

    private void filterProductBasicUnitType(WorkCenterSelectDTO dto, LambdaQueryWrapper<WorkCenterEntity> labda) {
        List<ProductionBasicUnitTypeDTO> basicUnitTypeDTOS = dto.getProductionBasicUnitTypeDTOS();
        if (CollectionUtils.isEmpty(basicUnitTypeDTOS)) {
            return;
        }
        List<Integer> deviceModelIds = basicUnitTypeDTOS.stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.DEVICE.getCode()))
                .map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId)
                .collect(Collectors.toList());
        List<Integer> lineModelIds = basicUnitTypeDTOS.stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode()))
                .map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId)
                .collect(Collectors.toList());
        List<Integer> teamModelIds = basicUnitTypeDTOS.stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.TEAM.getCode()))
                .map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId)
                .collect(Collectors.toList());
        // 查询设备
        if (CollectionUtils.isNotEmpty(deviceModelIds)) {
            List<Integer> deviceIds = deviceService.lambdaQuery().in(DeviceEntity::getModelId, deviceModelIds).list().stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deviceIds)) {
                labda.isNull(WorkCenterEntity::getId);
                return;
            }
            List<Integer> workCenterIds = workCenterDeviceService.lambdaQuery().in(WorkCenterDeviceEntity::getDeviceId, deviceIds).list().stream().map(WorkCenterDeviceEntity::getWorkCenterId).collect(Collectors.toList());
            workCenterIds.addAll(workCenterDeviceRelevanceService.lambdaQuery().in(WorkCenterDeviceRelevanceEntity::getDeviceId, deviceIds).list().stream().map(WorkCenterDeviceRelevanceEntity::getWorkCenterId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(workCenterIds)) {
                labda.isNull(WorkCenterEntity::getId);
                return;
            }
            labda.in(WorkCenterEntity::getId, workCenterIds);
            return;
        }
        // 查询制造单元
        if (CollectionUtils.isNotEmpty(lineModelIds)) {
            List<ProductionLineEntity> lineEntities = productionLineService.lambdaQuery().in(ProductionLineEntity::getModelId, lineModelIds).list();
            if (CollectionUtils.isEmpty(lineEntities)) {
                labda.isNull(WorkCenterEntity::getId);
                return;
            }
            List<Integer> workCenterIds = lineEntities.stream().map(ProductionLineEntity::getWorkCenterId).filter(Objects::nonNull).collect(Collectors.toList());
            List<Integer> lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            workCenterIds.addAll(workCenterLineRelevanceService.lambdaQuery().in(WorkCenterLineRelevanceEntity::getLineId, lineIds).list().stream().map(WorkCenterLineRelevanceEntity::getWorkCenterId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(workCenterIds)) {
                labda.isNull(WorkCenterEntity::getId);
                return;
            }
            labda.in(WorkCenterEntity::getId, workCenterIds);
            return;
        }
        // 查询班组
        if (CollectionUtils.isNotEmpty(teamModelIds)) {
            List<Integer> teamIds = teamService.lambdaQuery().in(SysTeamEntity::getTeamType, teamModelIds).list().stream().map(SysTeamEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(teamIds)) {
                labda.isNull(WorkCenterEntity::getId);
                return;
            }
            List<Integer> workCenterIds = workCenterTeamService.lambdaQuery().in(WorkCenterTeamEntity::getTeamId, teamIds).list().stream().map(WorkCenterTeamEntity::getWorkCenterId).collect(Collectors.toList());
            workCenterIds.addAll(workCenterTeamRelevanceService.lambdaQuery().in(WorkCenterTeamRelevanceEntity::getTeamId, teamIds).list().stream().map(WorkCenterTeamRelevanceEntity::getWorkCenterId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(workCenterIds)) {
                labda.isNull(WorkCenterEntity::getId);
                return;
            }
            labda.in(WorkCenterEntity::getId, workCenterIds);
        }
    }

    /**
     * 生产基本单元名称 过滤
     */
    private List<Integer> conditionProductionBasicUnit(String productionBasicUnit) {
        List<Integer> workCenterIds = new ArrayList<>();

        List<Integer> lineWorkCenterIds = productionLineService.lambdaQuery()
                .like(ProductionLineEntity::getName, productionBasicUnit)
                .list().stream().map(ProductionLineEntity::getWorkCenterId).collect(Collectors.toList());
        workCenterIds.addAll(lineWorkCenterIds);

        List<Integer> teamIds = teamService.lambdaQuery()
                .like(SysTeamEntity::getTeamName, productionBasicUnit)
                .list().stream().map(SysTeamEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(teamIds)) {
            List<Integer> teamWorkCenterIds = workCenterTeamService.lambdaQuery()
                    .in(WorkCenterTeamEntity::getTeamId, teamIds)
                    .list().stream().map(WorkCenterTeamEntity::getWorkCenterId).collect(Collectors.toList());
            workCenterIds.addAll(teamWorkCenterIds);
        }

        List<Integer> deviceIds = deviceService.lambdaQuery()
                .like(DeviceEntity::getDeviceName, productionBasicUnit)
                .list().stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            List<Integer> deviceWorkCenterIds = workCenterDeviceService.lambdaQuery()
                    .in(WorkCenterDeviceEntity::getDeviceId, deviceIds)
                    .list().stream().map(WorkCenterDeviceEntity::getWorkCenterId).collect(Collectors.toList());
            workCenterIds.addAll(deviceWorkCenterIds);
        }
        return workCenterIds;
    }

    /**
     * 根据ids获取工作中心列表
     *
     * @return
     */
    @Override
    public List<WorkCenterEntity> listByIds(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        String[] split = ids.split(Constant.SEP);
        return this.listByIds(Arrays.asList(split));

    }

    /**
     * 拿到工作中心绑定的制造单元列表
     *
     * @param id
     * @param isIsolation
     * @param username
     */
    @Override
    public List<ProductionLineEntity> listLineByCenterId(Integer id, Boolean isIsolation, String username, String craftProcedureIds) {
        LambdaQueryWrapper<ProductionLineEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductionLineEntity::getWorkCenterId, id);
        // 查询用户绑定的产线信息
        List<Integer> lineIds = lineEmployeeService.lambdaQuery().eq(LineEmployeeEntity::getUserName, username)
                .list().stream().map(LineEmployeeEntity::getLineId).collect(Collectors.toList());

        List<ProductionLineEntity> productionLineEntityList = productionLineService.list(lambdaQueryWrapper);

        // 如果传入工艺工序参数，根据工序的制造单元类型进行过滤
        if (StringUtils.isNotBlank(craftProcedureIds)) {
            productionLineEntityList = filterLinesByProcedures(productionLineEntityList, craftProcedureIds);
        }
        // 数据隔离
        if (Boolean.TRUE.equals(isIsolation)) {
            IsolationDTO isolationDTO = isolationService.getIsolationDTO(username, WorkCenterTypeEnum.LINE.getCode(), id);
            if (!isolationDTO.getAllWorkCenter()) {
                productionLineEntityList = productionLineEntityList.stream().filter(e -> isolationDTO.getIds().contains(e.getProductionLineId())).collect(Collectors.toList());
            }
        }
        for (ProductionLineEntity entity : productionLineEntityList) {
            if (entity.getGid() != null) {
                GridEntity gridEntity = gridMapper.selectById(entity.getGid());
                entity.setGname(gridEntity == null ? null : gridEntity.getGname());
            }
            // 绑定班组则为true，用户前端自动选择该数据
            if (lineIds.contains(entity.getProductionLineId())) {
                entity.setIsIsolateProductBasicUnit(true);
            }
        }
        return productionLineEntityList;
    }

    /**
     * 拿到工作中心绑定的班组列表
     *
     * @param id
     * @param isIsolation
     * @param username
     */
    @Override
    public List<SysTeamEntity> listTeamByCenterId(Integer id, Boolean isIsolation, String username) {
        LambdaQueryWrapper<WorkCenterTeamEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkCenterTeamEntity::getWorkCenterId, id);
        List<WorkCenterTeamEntity> workCenterTeamEntityList = workCenterTeamService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(workCenterTeamEntityList)) {
            return null;
        }
        List<Integer> teamIds = workCenterTeamEntityList.stream().map(WorkCenterTeamEntity::getTeamId).collect(Collectors.toList());
        // 数据隔离
        if (Boolean.TRUE.equals(isIsolation)) {
            IsolationDTO isolationDTO = isolationService.getIsolationDTO(username, WorkCenterTypeEnum.TEAM.getCode(), id);
            if (!isolationDTO.getAllWorkCenter()) {
                teamIds = teamIds.stream().filter(e -> isolationDTO.getIds().contains(e)).collect(Collectors.toList());
            }
        }
        LambdaQueryWrapper<SysTeamEntity> sysTeamEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysTeamEntityLambdaQueryWrapper.in(SysTeamEntity::getId, teamIds);
        // 查询用户绑定的班组信息
        List<Integer> teamsIds = teamUserService.lambdaQuery().eq(SysTeamUserEntity::getUserName, username)
                .list().stream()
                .map(SysTeamUserEntity::getTeamId).collect(Collectors.toList());
        List<SysTeamEntity> sysTeamEntityList = teamService.list(sysTeamEntityLambdaQueryWrapper);
        for (SysTeamEntity teamEntity : sysTeamEntityList) {
            // 绑定班组则为true，用户前端自动选择该数据
            if (teamsIds.contains(teamEntity.getId())) {
                teamEntity.setIsIsolateProductBasicUnit(true);
            }
        }
        teamService.showName(sysTeamEntityList);
        return sysTeamEntityList;
    }

    /**
     * 拿到工作中心绑定的设备列表
     *
     * @param id
     * @param isIsolation
     * @param username
     */
    @Override
    public List<DeviceEntity> listDeviceByCenterId(Integer id, Boolean isIsolation, String username, String craftProcedureIds) {
        // 优先取工艺工序绑定的工序设备类型下的设备，没有则取工作中心下的设备
        List<DeviceEntity> deviceEntities = getDeviceEntitiesByProcedureDevice(id, isIsolation, username, craftProcedureIds);
        if (CollectionUtils.isNotEmpty(deviceEntities)) {
            return deviceEntities;
        }
        List<WorkCenterDeviceEntity> workCenterDevices = workCenterDeviceService.lambdaQuery()
                .eq(WorkCenterDeviceEntity::getWorkCenterId, id).list();
        if (CollectionUtils.isEmpty(workCenterDevices)) {
            return new ArrayList<>();
        }
        List<Integer> deviceIds = workCenterDevices.stream().map(WorkCenterDeviceEntity::getDeviceId).collect(Collectors.toList());
        // 数据隔离
        if (Boolean.TRUE.equals(isIsolation)) {
            IsolationDTO isolationDTO = isolationService.getIsolationDTO(username, WorkCenterTypeEnum.DEVICE.getCode(), id);
            if (!isolationDTO.getAllWorkCenter()) {
                deviceIds = deviceIds.stream().filter(e -> isolationDTO.getIds().contains(e)).collect(Collectors.toList());
            }
        }
        deviceEntities = deviceService.lambdaQuery().in(DeviceEntity::getDeviceId, deviceIds).list();
        List<Integer> deviceModelIds = deviceEntities.stream().map(DeviceEntity::getModelId).collect(Collectors.toList());
        Map<Integer, String> modelMap = modelService.listByIds(deviceModelIds).stream().collect(Collectors.toMap(ModelEntity::getId, ModelEntity::getName));
        for (DeviceEntity deviceEntity : deviceEntities) {
            // 获取模型名称
            deviceEntity.setModelName(modelMap.get(deviceEntity.getModelId()));
        }
        return deviceEntities;
    }

    private List<DeviceEntity> getDeviceEntitiesByProcedureDevice(Integer id, Boolean isIsolation, String username, String craftProcedureIds) {
        if (StringUtils.isBlank(craftProcedureIds)) {
            return new ArrayList<>();
        }
        List<Integer> procedureIds = Arrays.stream(craftProcedureIds.split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toList());
        List<Integer> procedureDeviceModelIds = procedureDeviceTypeService.lambdaQuery().in(ProcedureDeviceTypeEntity::getProcedureId, procedureIds)
                .list().stream()
                .map(ProcedureDeviceTypeEntity::getDeviceModelId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(procedureDeviceModelIds)) {
            return new ArrayList<>();
        }
        Map<Integer, String> modelMap = modelService.listByIds(procedureDeviceModelIds).stream().collect(Collectors.toMap(ModelEntity::getId, ModelEntity::getName));
        List<DeviceEntity> deviceEntities = deviceService.lambdaQuery().in(DeviceEntity::getModelId, procedureDeviceModelIds).list();
        // 数据隔离
        if (Boolean.TRUE.equals(isIsolation)) {
            IsolationDTO isolationDTO = isolationService.getIsolationDTO(username, WorkCenterTypeEnum.DEVICE.getCode(), id);
            if (!isolationDTO.getAllWorkCenter()) {
                deviceEntities = deviceEntities.stream().filter(e -> isolationDTO.getIds().contains(e.getDeviceId())).collect(Collectors.toList());
            }
        }
        // 数据隔离后依旧不为空，则直接返回，如果为空，走工作中心的逻辑
        if (CollectionUtils.isEmpty(deviceEntities)) {
            return new ArrayList<>();
        }
        // 获取模型名称
        for (DeviceEntity deviceEntity : deviceEntities) {
            deviceEntity.setModelName(modelMap.get(deviceEntity.getModelId()));
        }
        return deviceEntities;
    }

    /**
     * 新增工作中心
     *
     * @param workCenterEntity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(WorkCenterEntity workCenterEntity) {
        boolean existName = this.lambdaQuery().eq(WorkCenterEntity::getName, workCenterEntity.getName()).exists();
        if (existName) {
            throw new ResponseException("所填写工作中心名称已存在");
        }
        if (StringUtils.isBlank(workCenterEntity.getCode())) {
            workCenterEntity.setCode(RandomUtil.randomString(10));
        }
        boolean existCode = this.lambdaQuery().eq(WorkCenterEntity::getCode, workCenterEntity.getCode()).exists();
        if (existCode) {
            throw new ResponseException("所填写工作中心编码已存在");
        }
        save(workCenterEntity);
        // 重新绑定生产基本单元
        rebindProductBasicUnit(workCenterEntity);
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.INSTANCE_TREE + "*"));
        //推送消息
        messagePushToKafkaService.pushNewMessage(workCenterEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_CENTER_ADD_MESSAGE);
    }

    /**
     * 重新绑定生产基本单元
     */
    private void rebindProductBasicUnit(WorkCenterEntity workCenterEntity) {
        //工作中心关联主资源
        relevanceLine(workCenterEntity);
        relevanceTeam(workCenterEntity);
        relevanceDevice(workCenterEntity);
        //工作中心关联关联资源
        relevanceTeamRelevance(workCenterEntity);
        relevanceDeviceRelevance(workCenterEntity);
        relevanceLineRelevance(workCenterEntity);
    }


    /**
     * 修改工作中心
     *
     * @param workCenterEditDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(WorkCenterEditDTO workCenterEditDTO) {
        WorkCenterEntity workCenterEntity = workCenterEditDTO.getWorkCenterEntity();
        updateById(workCenterEntity);
        // 解绑数据时的数据检查
        judgeWhenUnbindData(workCenterEditDTO, workCenterEntity);
        // 重新绑定生产基本单元
        rebindProductBasicUnit(workCenterEntity);
        //推送消息
        messagePushToKafkaService.pushNewMessage(workCenterEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_CENTER_UPDATE_MESSAGE);
        redisTemplate.delete(RedisKeyPrefix.getWorkCenterDetail(workCenterEntity.getId()));
    }

    /**
     * 解绑数据时的数据检查
     */
    private void judgeWhenUnbindData(WorkCenterEditDTO workCenterEditDTO, WorkCenterEntity workCenterEntity) {
        CraftProcedureService craftProcedureService = SpringUtil.getBean(CraftProcedureService.class);
        CraftService craftService = SpringUtil.getBean(CraftService.class);
        List<CraftProcedureEntity> craftProcedureEntities = new ArrayList<>();
        // 取消关联制造单元
        if (CollectionUtils.isNotEmpty(workCenterEditDTO.getDeleteLineIds())) {
            List<Integer> lineModelIds = new ArrayList<>();
            for (Integer lineId : workCenterEditDTO.getDeleteLineIds()) {
                ProductionLineEntity productionLineEntity = productionLineService.getById(lineId);
                Long count = workOrderBasicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.LINE.getCode())
                        .eq(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, lineId)
                        .count();
                if (count > 0) {
                    throw new ResponseException("制造单元" + productionLineEntity.getName() + "已绑定工单，无法取消绑定");
                }
                lineModelIds.add(productionLineEntity.getModelId());
            }
            // 查询解绑的制造单元实例中，是否绑定过工艺工序
            craftProcedureEntities = craftProcedureService.lambdaQuery()
                    .eq(CraftProcedureEntity::getType, WorkCenterTypeEnum.LINE.getCode())
                    .in(CraftProcedureEntity::getLineModelId, lineModelIds).list();
            List<CraftProcedureEntity> existList = craftProcedureEntities.stream().filter(o -> {
                List<String> relatedWorkCenterIds = Arrays.stream(o.getWorkCenterIds().split(Constants.SEP)).collect(Collectors.toList());
                return relatedWorkCenterIds.contains(String.valueOf(workCenterEntity.getId()));
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existList)) {
                CraftEntity craftEntity = craftService.lambdaQuery().eq(CraftEntity::getCraftId, existList.get(0).getCraftId()).one();
                throw new ResponseException("工艺/工艺模板编码" + craftEntity.getCraftCode() + "已绑定工序" + existList.get(0).getProcedureName() + "，无法取消绑定，如需解绑请先修改工艺工序");
            }
        }
        // 取消关联班组
        if (CollectionUtils.isNotEmpty(workCenterEditDTO.getDeleteTeamIds())) {
            for (Integer teamId : workCenterEditDTO.getDeleteTeamIds()) {
                SysTeamEntity sysTeamEntity = teamService.getById(teamId);
                Long count = workOrderBasicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.TEAM.getCode())
                        .eq(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, teamId)
                        .count();
                if (count > 0) {
                    throw new ResponseException("班组" + sysTeamEntity.getTeamName() + "已绑定工单，无法取消绑定");
                }
            }
            // 查询工作中心换绑的工作中心类型是否绑定过工艺工序，绑定则不能换绑类型
            craftProcedureEntities = craftProcedureService.lambdaQuery().eq(CraftProcedureEntity::getType, WorkCenterTypeEnum.TEAM.getCode()).list();
        }
        // 取消关联设备
        if (CollectionUtils.isNotEmpty(workCenterEditDTO.getDeleteDeviceIds())) {
            for (Integer deviceId : workCenterEditDTO.getDeleteDeviceIds()) {
                DeviceEntity deviceEntity = deviceService.getById(deviceId);
                Long count = workOrderBasicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.DEVICE.getCode())
                        .eq(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, deviceId)
                        .count();
                if (count > 0) {
                    throw new ResponseException("设备" + deviceEntity.getDeviceName() + "已绑定工单，无法取消绑定");
                }
            }
            craftProcedureEntities = craftProcedureService.lambdaQuery().eq(CraftProcedureEntity::getType, WorkCenterTypeEnum.DEVICE.getCode()).list();
        }
        // 查询工作中心换绑的工作中心类型是否绑定过工艺工序，绑定则不能换绑类型
        List<CraftProcedureEntity> existList = craftProcedureEntities.stream().filter(o -> {
            List<String> relatedWorkCenterIds = Arrays.stream(o.getWorkCenterIds().split(Constants.SEP)).collect(Collectors.toList());
            return relatedWorkCenterIds.contains(String.valueOf(workCenterEntity.getId())) && !o.getType().equals(workCenterEntity.getType());
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existList)) {
            CraftEntity craftEntity = craftService.lambdaQuery().eq(CraftEntity::getCraftId, existList.get(0).getCraftId()).one();
            throw new ResponseException("工艺/工艺模板编码" + craftEntity.getCraftCode() + "已绑定工序" + existList.get(0).getProcedureName() + "，无法修改工作中心类型，如需更改请先修改工艺工序");
        }
    }

    /**
     * 删除工作中心
     * 1. 制造单元 dfs_production_line.work_center_id, dfs_work_center_line_relevance (解绑)
     * 2. 设备 dfs_work_center_device, dfs_work_center_device_relevance (解绑)
     * 3. 班组 dfs_work_center_team, dfs_work_center_team_relevance (解绑)
     * 4. 工单 dfs_work_order.work_center_id
     * 5. 工序 dfs_procedure。work_center_ids
     *
     * @param id 工作中心id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        WorkCenterEntity workCenterEntity = this.getById(id);
        //已有工单不让删除
        LambdaQueryWrapper<WorkOrderEntity> lambdaQueryWrapper5 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper5.eq(WorkOrderEntity::getWorkCenterId, id);
        Long workOrderCount = workOrderMapper.selectCount(lambdaQueryWrapper5);
        if (workOrderCount > 0) {
            throw new ResponseException("已有关联生产工单，无法删除");
        }
        //已有工序不让删除
        Long procedureCount = procedureMapper.countByWorkCenterId(id);
        if (procedureCount > 0) {
            throw new ResponseException("已有关联工序定义，无法删除");
        }
        // 如果已绑定制造单元类型的工艺工序，则不能删除
        CraftProcedureService craftProcedureService = SpringUtil.getBean(CraftProcedureService.class);
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.lambdaQuery()
                .eq(CraftProcedureEntity::getType, WorkCenterTypeEnum.LINE.getCode()).list();
        long craftProcedureCount = craftProcedureEntities.stream().filter(o -> {
            List<String> relatedWorkCenterIds = Arrays.stream(o.getWorkCenterIds().split(Constants.SEP)).collect(Collectors.toList());
            return relatedWorkCenterIds.contains(String.valueOf(workCenterEntity.getId()));
        }).count();
        if (craftProcedureCount > 0) {
            throw new ResponseException("已关联工艺/工艺模板下的工艺工序，无法删除");
        }

        // 解绑产线
        productionLineService.lambdaUpdate().eq(ProductionLineEntity::getWorkCenterId, id)
                .set(ProductionLineEntity::getWorkCenterId, null)
                .update();
        workCenterLineRelevanceService.lambdaUpdate().eq(WorkCenterLineRelevanceEntity::getWorkCenterId, id).remove();

        // 解绑设备
        workCenterDeviceService.lambdaUpdate().eq(WorkCenterDeviceEntity::getWorkCenterId, id).remove();
        workCenterDeviceRelevanceService.lambdaUpdate().eq(WorkCenterDeviceRelevanceEntity::getWorkCenterId, id).remove();

        // 解绑班组
        workCenterTeamService.lambdaUpdate().eq(WorkCenterTeamEntity::getWorkCenterId, id).remove();
        workCenterTeamRelevanceService.lambdaUpdate().eq(WorkCenterTeamRelevanceEntity::getWorkCenterId, id).remove();

        //删除
        removeById(id);
        //推送消息
        messagePushToKafkaService.pushNewMessage(workCenterEntity, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.WORK_CENTER_DELETE_MESSAGE);
        redisTemplate.delete(RedisKeyPrefix.getWorkCenterDetail(workCenterEntity.getId()));
    }

    /**
     * 工作中心主资源为制造单元
     *
     * @param workCenterEntity
     */
    private void relevanceLine(WorkCenterEntity workCenterEntity) {
        productionLineService.lambdaUpdate().eq(ProductionLineEntity::getWorkCenterId, workCenterEntity.getId()).set(ProductionLineEntity::getWorkCenterId, null).update();
        if (!workCenterEntity.getType().equals(WorkCenterTypeEnum.LINE.getCode())) {
            return;
        }
        if (CollectionUtils.isEmpty(workCenterEntity.getProductionLineIds())) {
            throw new ResponseException(RespCodeEnum.WORK_CENTER_NEED_MAIN_RESOURCE);
        }
        List<ProductionLineEntity> lineEntities = workCenterEntity.getProductionLineIds().stream().map(o -> ProductionLineEntity.builder()
                        .productionLineId(o)
                        .workCenterId(workCenterEntity.getId())
                        .build())
                .collect(Collectors.toList());
        productionLineService.updateBatchById(lineEntities);
        List<ProductionLineEntity> productionLineEntities = productionLineService.listByIds(workCenterEntity.getProductionLineIds());
        // 推送制造单元修改字段的消息
        messagePushToKafkaService.pushNewMessage(productionLineEntities, Constants.KAFKA_MAIN_DATA_TOPIC, KafkaMessageTypeEnum.PRODUCTION_LINE_UPDATE_MESSAGE);
        // 刷新工作中心的模型id字段
        List<Integer> modelIds = productionLineEntities.stream().map(ProductionLineEntity::getModelId).collect(Collectors.toList());
        List<ModelEntity> modelEntities = modelService.listByIds(modelIds);
        this.lambdaUpdate().eq(WorkCenterEntity::getId, workCenterEntity.getId())
                .set(WorkCenterEntity::getLineModelId, modelEntities.stream().map(ModelEntity::getId).map(String::valueOf).collect(Collectors.joining(Constants.SEP)))
                .set(WorkCenterEntity::getLineModelName, modelEntities.stream().map(ModelEntity::getName).collect(Collectors.joining(Constants.SEP)))
                .set(WorkCenterEntity::getDeviceModelId, null)
                .update();
    }


    /**
     * 工作中心主资源为班组
     *
     * @param workCenterEntity
     */
    private void relevanceTeam(WorkCenterEntity workCenterEntity) {
        workCenterTeamService.lambdaUpdate().eq(WorkCenterTeamEntity::getWorkCenterId, workCenterEntity.getId()).remove();
        if (!workCenterEntity.getType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
            return;
        }
        if (CollectionUtils.isEmpty(workCenterEntity.getSysTeamIds())) {
            throw new ResponseException(RespCodeEnum.WORK_CENTER_NEED_MAIN_RESOURCE);
        }
        List<WorkCenterTeamEntity> list = workCenterEntity.getSysTeamIds().stream().map(o -> WorkCenterTeamEntity.builder()
                .workCenterId(workCenterEntity.getId())
                .teamId(o)
                .build()).collect(Collectors.toList());
        workCenterTeamService.saveBatch(list);
    }


    /**
     * 工作中心主资源为设备
     *
     * @param workCenterEntity
     */
    private void relevanceDevice(WorkCenterEntity workCenterEntity) {
        workCenterDeviceService.lambdaUpdate().eq(WorkCenterDeviceEntity::getWorkCenterId, workCenterEntity.getId()).remove();
        if (!workCenterEntity.getType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
            return;
        }
        if (CollectionUtils.isEmpty(workCenterEntity.getDeviceIds())) {
            throw new ResponseException(RespCodeEnum.WORK_CENTER_NEED_MAIN_RESOURCE);
        }
        List<DeviceEntity> deviceEntities = deviceService.listByIds(workCenterEntity.getDeviceIds());
        List<WorkCenterDeviceEntity> entities = deviceEntities.stream().map(o -> WorkCenterDeviceEntity.builder()
                .workCenterId(workCenterEntity.getId())
                .deviceId(o.getDeviceId())
                .build()).collect(Collectors.toList());
        workCenterDeviceService.saveBatch(entities);
        // 刷新工作中心的模型id字段
        String modelIds = deviceEntities.stream().map(DeviceEntity::getModelId).map(String::valueOf).collect(Collectors.joining(Constants.SEP));
        this.lambdaUpdate().eq(WorkCenterEntity::getId, workCenterEntity.getId())
                .set(WorkCenterEntity::getDeviceModelId, modelIds)
                .set(WorkCenterEntity::getLineModelId, null)
                .set(WorkCenterEntity::getLineModelName, null)
                .update();
    }

    /**
     * 配置关联资源班组
     *
     * @param workCenterEntity
     */
    private void relevanceTeamRelevance(WorkCenterEntity workCenterEntity) {
        workCenterTeamRelevanceService.lambdaUpdate().eq(WorkCenterTeamRelevanceEntity::getWorkCenterId, workCenterEntity.getId()).remove();
        if (CollectionUtils.isEmpty(workCenterEntity.getTeamRelevanceIds())) {
            return;
        }
        List<WorkCenterTeamRelevanceEntity> collect = workCenterEntity.getTeamRelevanceIds().stream().map(o -> WorkCenterTeamRelevanceEntity.builder()
                .workCenterId(workCenterEntity.getId()).teamId(o).createBy(workCenterEntity.getUpdateBy())
                .createTime(new Date()).build()).collect(Collectors.toList());
        workCenterTeamRelevanceService.saveBatch(collect);
    }

    /**
     * 工作中心关联资源为设备
     *
     * @param workCenterEntity
     */
    private void relevanceDeviceRelevance(WorkCenterEntity workCenterEntity) {
        workCenterDeviceRelevanceService.lambdaUpdate().eq(WorkCenterDeviceRelevanceEntity::getWorkCenterId, workCenterEntity.getId()).remove();
        if (CollectionUtils.isEmpty(workCenterEntity.getDeviceRelevanceIds())) {
            return;
        }
        List<WorkCenterDeviceRelevanceEntity> collect = workCenterEntity.getDeviceRelevanceIds().stream().map(o -> WorkCenterDeviceRelevanceEntity.builder()
                .workCenterId(workCenterEntity.getId()).deviceId(o).createBy(workCenterEntity.getUpdateBy())
                .createTime(new Date()).build()).collect(Collectors.toList());
        workCenterDeviceRelevanceService.saveBatch(collect);
    }

    /**
     * 工作中心关联资源为制造单元
     *
     * @param workCenterEntity
     */
    private void relevanceLineRelevance(WorkCenterEntity workCenterEntity) {
        workCenterLineRelevanceService.lambdaUpdate().eq(WorkCenterLineRelevanceEntity::getWorkCenterId, workCenterEntity.getId()).remove();
        if (CollectionUtils.isEmpty(workCenterEntity.getLineRelevanceIds())) {
            return;
        }
        List<WorkCenterLineRelevanceEntity> collect = workCenterEntity.getLineRelevanceIds().stream().map(o -> WorkCenterLineRelevanceEntity.builder()
                .workCenterId(workCenterEntity.getId()).lineId(o).createBy(workCenterEntity.getUpdateBy())
                .createTime(new Date()).build()).collect(Collectors.toList());
        workCenterLineRelevanceService.saveBatch(collect);
    }

    /**
     * 获取工作中心可选产线列表
     *
     * @param id
     * @return
     */
    @Override
    public List<WorkCenterLineDTO> listProductLine(Integer id) {
        List<ModelEntity> modelEntityList = modelService.getListByType(ModelEnum.LINE);
        List<WorkCenterLineDTO> result = new ArrayList<>();
        for (ModelEntity modelEntity : modelEntityList) {
            LambdaQueryWrapper<ProductionLineEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductionLineEntity::getModelId, modelEntity.getId())
                    .and(i -> i.isNull(ProductionLineEntity::getWorkCenterId).or(o -> o.eq(ProductionLineEntity::getWorkCenterId, id)));
            List<ProductionLineEntity> productionLineEntityList = productionLineService.list(lambdaQueryWrapper);
            if (CollectionUtils.isEmpty(productionLineEntityList)) {
                continue;
            }
            WorkCenterLineDTO workCenterLineDTO = WorkCenterLineDTO.builder()
                    .id(modelEntity.getId()).name(modelEntity.getName())
                    .productionLineEntityList(productionLineEntityList).build();
            result.add(workCenterLineDTO);
        }
        return result;
    }


    /**
     * 获取工作中心详情
     *
     * @param id
     * @return
     */
    @Override
    public WorkCenterEntity detail(Integer id) {
        WorkCenterEntity workCenterEntity = this.getById(id);
        //获取已关联制造单元
        List<ProductionLineEntity> productionLineEntityList = listLineByCenterId(id, null, null, null);
        if (CollectionUtils.isNotEmpty(productionLineEntityList)) {
            workCenterEntity.setProductionLineIds(productionLineEntityList.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList()));
            workCenterEntity.setProductionLineTypeIds(productionLineEntityList.stream().map(ProductionLineEntity::getModelId).distinct().collect(Collectors.toList()));
        }
        //获取已关联班组
        List<SysTeamEntity> sysTeamEntityList = listTeamByCenterId(id, null, null);
        if (CollectionUtils.isNotEmpty(sysTeamEntityList)) {
            workCenterEntity.setSysTeamIds(sysTeamEntityList.stream().map(SysTeamEntity::getId).collect(Collectors.toList()));
        }
        //获取已关联设备
        List<DeviceEntity> deviceEntityList = listDeviceByCenterId(id, null, null, null);
        if (CollectionUtils.isNotEmpty(deviceEntityList)) {
            workCenterEntity.setDeviceIds(deviceEntityList.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList()));
        }

        //获取已关联设备id
        List<DeviceEntity> relevanceDeviceEntityList = getRelevanceDevice(id);
        if (CollectionUtils.isNotEmpty(relevanceDeviceEntityList)) {
            workCenterEntity.setDeviceRelevanceIds(relevanceDeviceEntityList.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList()));
            workCenterEntity.setDeviceRelevanceTypeIds(relevanceDeviceEntityList.stream().map(DeviceEntity::getModelId).distinct().collect(Collectors.toList()));
        }
        //获取已关联的班组关联资源
        List<SysTeamEntity> sysRelevanceTeamEntities = getRelevanceTeams(id);
        if (CollectionUtils.isNotEmpty(sysRelevanceTeamEntities)) {
            workCenterEntity.setTeamRelevanceIds(sysRelevanceTeamEntities.stream().map(SysTeamEntity::getId).collect(Collectors.toList()));
        }
        //获取已关联制造单元id
        List<ProductionLineEntity> lineEntityList = getRelevanceLine(id);
        if (CollectionUtils.isNotEmpty(lineEntityList)) {
            workCenterEntity.setLineRelevanceIds(lineEntityList.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList()));
            workCenterEntity.setLineRelevanceTypeIds(lineEntityList.stream().map(ProductionLineEntity::getModelId).distinct().collect(Collectors.toList()));
        }
        return workCenterEntity;
    }

    @Override
    public WorkCenterEntity detailByRedis(Integer id) {
        String workCenterDetailKey = RedisKeyPrefix.getWorkCenterDetail(id);
        String json = (String) redisTemplate.opsForValue().get(workCenterDetailKey);
        WorkCenterEntity detail;
        if (StringUtils.isBlank(json)) {
            detail = detail(id);
            redisTemplate.opsForValue().set(workCenterDetailKey, JacksonUtil.toJSONString(detail));
        } else {
            detail = JacksonUtil.parseObject(json, WorkCenterEntity.class);
        }
        return detail;
    }

    /**
     * 获取工作中心可选的设备列表
     *
     * @return
     */
    @Override
    public List<WorkCenterDeviceDTO> listDevice(Integer workCenterId) {
        List<ModelEntity> modelEntityList = modelService.getListByType(ModelEnum.DEVICE);
        List<WorkCenterDeviceDTO> result = new ArrayList<>();
        for (ModelEntity modelEntity : modelEntityList) {
            List<DeviceEntity> deviceEntities = deviceService.lambdaQuery()
                    .eq(DeviceEntity::getModelId, modelEntity.getId())
                    .list();
            if (CollectionUtils.isEmpty(deviceEntities)) {
                continue;
            }
            // 工作中心已经绑定过的设备不能再被新的工作中心关联
            List<Integer> allDeviceIds = workCenterDeviceService.list().stream()
                    .map(WorkCenterDeviceEntity::getDeviceId).distinct()
                    .collect(Collectors.toList());
            List<Integer> bindDeviceIds = workCenterDeviceService.lambdaQuery().eq(WorkCenterDeviceEntity::getWorkCenterId, workCenterId)
                    .list().stream()
                    .map(WorkCenterDeviceEntity::getDeviceId).collect(Collectors.toList());
            deviceEntities = deviceEntities.stream().filter(o -> !allDeviceIds.contains(o.getDeviceId()) || bindDeviceIds.contains(o.getDeviceId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deviceEntities)) {
                continue;
            }
            WorkCenterDeviceDTO workCenterDeviceDTO = WorkCenterDeviceDTO.builder()
                    .id(modelEntity.getId()).name(modelEntity.getName())
                    .deviceEntityList(deviceEntities).build();
            result.add(workCenterDeviceDTO);
        }
        return result;
    }


    /**
     * 获取工作中心可选关联产线列表
     *
     * @return
     */
    @Override
    public List<WorkCenterRelevanceLineDTO> listRelevanceLine() {
        List<ModelEntity> modelEntityList = modelService.getListByType(ModelEnum.LINE);
        List<WorkCenterRelevanceLineDTO> result = new ArrayList<>();
        for (ModelEntity modelEntity : modelEntityList) {
            LambdaQueryWrapper<ProductionLineEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductionLineEntity::getModelId, modelEntity.getId());
            List<ProductionLineEntity> lineEntityList = productionLineService.list(lambdaQueryWrapper);
            WorkCenterRelevanceLineDTO workCenterRelevanceLineDTO = WorkCenterRelevanceLineDTO.builder()
                    .id(modelEntity.getId()).name(modelEntity.getName())
                    .productionLineEntityList(lineEntityList).build();
            result.add(workCenterRelevanceLineDTO);
        }
        return result;
    }


    /**
     * 获取已选关联设备id
     *
     * @param id
     * @return
     */
    @Override
    public List<DeviceEntity> getRelevanceDevice(Integer id) {
        LambdaQueryWrapper<WorkCenterDeviceRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkCenterDeviceRelevanceEntity::getWorkCenterId, id);
        List<WorkCenterDeviceRelevanceEntity> relevanceDevices = workCenterDeviceRelevanceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(relevanceDevices)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DeviceEntity> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.in(DeviceEntity::getDeviceId, relevanceDevices.stream().map(WorkCenterDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList()));
        return deviceService.list(deviceQueryWrapper);
    }


    /**
     * 获取已选关联制造单元id
     *
     * @param id
     * @return
     */
    public List<ProductionLineEntity> getRelevanceLine(Integer id) {
        LambdaQueryWrapper<WorkCenterLineRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkCenterLineRelevanceEntity::getWorkCenterId, id);
        List<WorkCenterLineRelevanceEntity> relevanceLines = workCenterLineRelevanceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(relevanceLines)) {
            return null;
        }
        LambdaQueryWrapper<ProductionLineEntity> lineQueryWrapper = new LambdaQueryWrapper<>();
        lineQueryWrapper.in(ProductionLineEntity::getProductionLineId, relevanceLines.stream().map(WorkCenterLineRelevanceEntity::getLineId).collect(Collectors.toList()));
        return productionLineService.list(lineQueryWrapper);
    }

    /**
     * 获取工单可选关联设备资源
     *
     * @param id
     * @return
     */
    @Override
    public List<DeviceEntity> getRelevanceDeviceForWorkOrder(Integer id, String craftProcedureIds) {
        List<DeviceEntity> deviceEntities = this.getRelevanceDevice(id);
        if (CollectionUtils.isNotEmpty(deviceEntities)) {
            List<Integer> deviceModelIds = deviceEntities.stream().map(DeviceEntity::getModelId).collect(Collectors.toList());
            Map<Integer, String> modelMap = modelService.listByIds(deviceModelIds).stream().collect(Collectors.toMap(ModelEntity::getId, ModelEntity::getName));
            for (DeviceEntity deviceEntity : deviceEntities) {
                // 获取模型名称
                deviceEntity.setModelName(modelMap.get(deviceEntity.getModelId()));
            }
            // 获取工艺工序绑定的工序设备类型下的设备
            if (StringUtils.isNotBlank(craftProcedureIds)) {
                List<Integer> procedureIds = Arrays.stream(craftProcedureIds.split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toList());
                List<Integer> procedureDeviceModelIds = procedureDeviceTypeService.lambdaQuery().in(ProcedureDeviceTypeEntity::getProcedureId, procedureIds)
                        .list().stream()
                        .map(ProcedureDeviceTypeEntity::getDeviceModelId)
                        .collect(Collectors.toList());
                // 如果不为空，则只获取工序设备类型下的设备，为空则走工作中心->关联设备的默认逻辑
                if (CollectionUtils.isNotEmpty(procedureDeviceModelIds)) {
                    deviceEntities = deviceEntities.stream().filter(deviceEntity -> procedureDeviceModelIds.contains(deviceEntity.getModelId()))
                            .collect(Collectors.toList());
                }
            }
            return deviceEntities;
        }
        // 如果为空则查询所有设备（维持晓成之前的逻辑）
        return deviceService.list();
    }

    /**
     * 获取工作中心id可选关联制造单元资源
     *
     * @param id
     * @return
     */
    @Override
    public List<ProductionLineEntity> getRelevanceLineForWorkOrder(Integer id, String craftProcedureIds) {
        List<ProductionLineEntity> lineEntityList = this.getRelevanceLine(id);
        if (CollectionUtils.isNotEmpty(lineEntityList)) {
            // 如果传入工艺工序参数，根据工序的制造单元类型进行过滤
            if (StringUtils.isNotBlank(craftProcedureIds)) {
                lineEntityList = filterLinesByProcedures(lineEntityList, craftProcedureIds);
            }
            return lineEntityList;
        }
        List<ProductionLineEntity> productionLineEntityList = productionLineService.list();
        // 如果传入工艺工序参数，根据工序的制造单元类型进行过滤
        if (StringUtils.isNotBlank(craftProcedureIds)) {
            productionLineEntityList = filterLinesByProcedures(productionLineEntityList, craftProcedureIds);
        }
        for (ProductionLineEntity productionLineEntity : productionLineEntityList) {
            GridEntity gridEntity = gridMapper.selectById(productionLineEntity.getGid());
            if (gridEntity != null) {
                productionLineEntity.setGname(gridEntity.getGname());
            }
        }
        return productionLineEntityList;
    }

    /**
     * 根据工艺工序过滤制造单元列表
     *
     * @param lineEntityList 制造单元列表
     * @param craftProcedureIds 工艺工序ID字符串
     * @return 过滤后的制造单元列表
     */
    private List<ProductionLineEntity> filterLinesByProcedures(List<ProductionLineEntity> lineEntityList, String craftProcedureIds) {
        // 参数校验
        if (CollectionUtils.isEmpty(lineEntityList) || StringUtils.isBlank(craftProcedureIds)) {
            return new ArrayList<>();
        }
        // 将工艺工序ID按逗号分隔成数组
        List<Integer> procedureIds = Arrays.stream(craftProcedureIds.split(Constants.SEP))
                .filter(StringUtils::isNotBlank) // 过滤掉空字符串
                .map(String::trim) // 去除前后空格
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(procedureIds)) {
            return new ArrayList<>();
        }
        // 查询工艺工序关联的制造单元模型ID
        List<CraftProcedureEntity> craftProcedures = craftProcedureService.lambdaQuery()
                .in(CraftProcedureEntity::getId, procedureIds)
                .select(CraftProcedureEntity::getLineModelId)
                .list();
        // 获取工艺工序关联的制造单元模型ID集合
        Set<Integer> lineModelIds = craftProcedures.stream()
                .filter(Objects::nonNull) // 过滤掉null的CraftProcedureEntity对象
                .map(CraftProcedureEntity::getLineModelId)
                .filter(Objects::nonNull) // 过滤掉null的lineModelId
                .collect(Collectors.toSet());
        // 根据制造单元模型ID过滤制造单元列表
        if (CollectionUtils.isNotEmpty(lineModelIds)) {
            return lineEntityList.stream()
                    .filter(Objects::nonNull) // 过滤掉null的ProductionLineEntity对象
                    .filter(line -> lineModelIds.contains(line.getModelId()))
                    .collect(Collectors.toList());
        } else {
            // 如果工艺工序没有关联制造单元模型，返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * 获取工单可选关联班组资源
     *
     * @param id
     * @return
     */
    @Override
    public List<SysTeamEntity> getRelevanceTeamForWorkOrder(Integer id) {
        List<SysTeamEntity> teamEntityList = this.getRelevanceTeams(id);
        if (CollectionUtils.isNotEmpty(teamEntityList)) {
            return teamEntityList;
        }
        return teamService.getList(3, null);
    }


    /**
     * 获取已选关联班组id
     *
     * @param id
     * @return
     */
    @Override
    public List<SysTeamEntity> getRelevanceTeams(Integer id) {
        LambdaQueryWrapper<WorkCenterTeamRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkCenterTeamRelevanceEntity::getWorkCenterId, id).select(WorkCenterTeamRelevanceEntity::getTeamId);
        List<WorkCenterTeamRelevanceEntity> relevanceTeams = workCenterTeamRelevanceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(relevanceTeams)) {
            return null;
        }
        LambdaQueryWrapper<SysTeamEntity> teamQueryWrapper = new LambdaQueryWrapper<>();
        teamQueryWrapper.in(SysTeamEntity::getId, relevanceTeams.stream()
                .map(WorkCenterTeamRelevanceEntity::getTeamId).collect(Collectors.toList()));
        List<SysTeamEntity> sysTeamEntityList = teamService.list(teamQueryWrapper);
        for (SysTeamEntity sysTeamEntity : sysTeamEntityList) {
            //展示班组类型名称
            TeamTypeDefEntity teamTypeDefEntity = teamTypeDefService.lambdaQuery().eq(TeamTypeDefEntity::getId, sysTeamEntity.getTeamType()).one();
            sysTeamEntity.setTeamTypeName(teamTypeDefEntity.getTypeDefName());
        }
        return sysTeamEntityList;
    }

    /**
     * 获取已选关联制造单元id
     *
     * @param id
     * @return
     */
    @Override
    public List<ProductionLineEntity> getRelevanceLines(Integer id) {
        LambdaQueryWrapper<WorkCenterLineRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkCenterLineRelevanceEntity::getWorkCenterId, id).select(WorkCenterLineRelevanceEntity::getLineId);
        List<WorkCenterLineRelevanceEntity> relevanceLines = workCenterLineRelevanceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(relevanceLines)) {
            return null;
        }
        LambdaQueryWrapper<ProductionLineEntity> lineQueryWrapper = new LambdaQueryWrapper<>();
        lineQueryWrapper.in(ProductionLineEntity::getProductionLineId, relevanceLines.stream().map(WorkCenterLineRelevanceEntity::getLineId).collect(Collectors.toList()));
        return productionLineService.list(lineQueryWrapper);
    }

    /**
     * 根据id获取工作中心名称
     *
     * @param id
     * @return
     */
    @Override
    public String getNameById(Integer id) {
        WorkCenterEntity workCenterEntity = this.getById(id);
        return workCenterEntity == null ? null : workCenterEntity.getName();
    }

    /**
     * 根据编码获取工作中心
     *
     * @param code
     * @return
     */
    @Override
    public WorkCenterEntity getCenterByCode(String code) {
        LambdaQueryWrapper<WorkCenterEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkCenterEntity::getCode, code);
        return getOne(lambdaQueryWrapper);
    }

    /**
     * 根据名称获取工作中心
     *
     * @param name
     * @return
     */
    @Override
    public WorkCenterEntity getCenterByName(String name) {
        LambdaQueryWrapper<WorkCenterEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkCenterEntity::getName, name);
        return getOne(lambdaQueryWrapper);
    }

    /**
     * 判断产线是否开启作业工单
     *
     * @param lineId
     * @return
     */
    @Override
    public Boolean isOperationByLineId(Integer lineId) {
        ProductionLineEntity productionLineEntity = productionLineService.getById(lineId);
        if (productionLineEntity == null) {
            return false;
        }
        return isOperationWorkCenter(productionLineEntity.getWorkCenterId());
    }

    /**
     * 判断产线是否开启作业工单
     *
     * @param deviceId
     * @return
     */
    @Override
    public Boolean isOperationByDeviceId(Integer deviceId) {
        WorkCenterDeviceEntity one = workCenterDeviceService.lambdaQuery()
                .select(WorkCenterDeviceEntity::getWorkCenterId)
                .eq(WorkCenterDeviceEntity::getDeviceId, deviceId).last("limit 1").one();
        if (one != null) {
            return isOperationWorkCenter(one.getWorkCenterId());
        }

        WorkCenterDeviceRelevanceEntity entity = workCenterDeviceRelevanceService.lambdaQuery()
                .select(WorkCenterDeviceRelevanceEntity::getWorkCenterId)
                .eq(WorkCenterDeviceRelevanceEntity::getDeviceId, deviceId).last("limit 1").one();
        if (entity == null) {
            return false;
        }
        return isOperationWorkCenter(entity.getWorkCenterId());
    }

    /**
     * 判断工单是否开启作业工单
     *
     * @param WorkOrderId
     * @return
     */
    @Override
    public Boolean isOperationWorkOrder(Integer WorkOrderId) {
        WorkOrderEntity workOrderEntity = workOrderMapper.selectById(WorkOrderId);
        if (workOrderEntity == null) {
            return false;
        }
        return isOperationWorkCenter(workOrderEntity.getWorkCenterId());
    }

    /**
     * 判断工作中心是否开启作业工单
     *
     * @param WorkCenterId
     * @return
     */
    @Override
    public Boolean isOperationWorkCenter(Integer WorkCenterId) {
        WorkCenterEntity workCenterEntity = this.getById(WorkCenterId);
        if (workCenterEntity == null) {
            return false;
        }
        return workCenterEntity.getIsOperation() != null ? workCenterEntity.getIsOperation() : false;
    }

    @Override
    public List<SysUserEntity> getUserListByWorkCenter(Integer workOrderId, String nickname, Integer teamId) {
        // 默认返回所有启用的用户
        List<SysUserEntity> userEntities = sysUserService.lambdaQuery().eq(SysUserEntity::getEnabled, Constant.ENABLE)
                .eq(StringUtils.isNotBlank(nickname), SysUserEntity::getNickname, nickname)
                .list();
        List<SysTeamEntity> teamEntities = new ArrayList<>();
        WorkOrderEntity workOrderEntity = workOrderMapper.selectById(workOrderId);
        if (workOrderEntity == null) {
            return userEntities;
        }
        WorkCenterEntity workCenterEntity = this.getById(workOrderEntity.getWorkCenterId());
        if (workCenterEntity == null) {
            return userEntities;
        }
        // 获取工单关联的生产基本单元
        List<WorkOrderBasicUnitRelationEntity> relationEntities = workOrderBasicUnitRelationService.getByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
        if (CollectionUtils.isEmpty(relationEntities)) {
            return userEntities;
        }
        // 工作中心直接关联班组
        if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getType())) {
            // 获取班组对应的成员列表
            List<Integer> teamIds = relationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
            List<SysUserEntity> teamUsers = teamService.getMemberByTeams(teamIds);
            if (CollectionUtils.isNotEmpty(teamUsers)) {
                // 检验用户是否班组成员
                return checkTeamUser(nickname, teamUsers, teamEntities);
            }
            // 工作中心没有关联班组，返回整个用户列表
            return checkTeamUser(nickname, userEntities, teamEntities);
        } else if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterEntity.getType())) {
            // 根据业务配置获取产线成员
            FullPathCodeDTO dto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.FILTER_OPERATOR_CONF).build();
            FilterOperatorConfigDTO config = businessConfigService.getValueDto(dto, FilterOperatorConfigDTO.class);
            List<Integer> lineIds = relationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
            // 未启用配置或者配置产线为空或者配置产线不包含在该工单的产线中，三种情况都返回整个用户列表
            boolean match = lineIds.stream().anyMatch(x -> config.getEnableLine().stream().anyMatch(y -> Objects.equals(y, x)));
            if (!config.getEnable() || CollectionUtils.isEmpty(config.getEnableLine()) || !match) {
                return userEntities;
            }
            List<String> lineUsers = lineEmployeeService.lambdaQuery().in(LineEmployeeEntity::getLineId, lineIds)
                    .list().stream()
                    .map(LineEmployeeEntity::getUserName)
                    .collect(Collectors.toList());
            // 用户需要包含产线的负责人
            List<ProductionLineEntity> lineEntities = productionLineService.listByIds(lineIds);
            List<String> magNames = lineEntities.stream().map(ProductionLineEntity::getMagName).collect(Collectors.toList());
            lineUsers.addAll(magNames);
            List<SysUserEntity> sysUserEntities = sysUserService.lambdaQuery().in(SysUserEntity::getUsername, lineUsers).list();
            List<String> listUserNickNames = sysUserEntities.stream().map(SysUserEntity::getNickname).collect(Collectors.toList());
            if (StringUtils.isNotBlank(nickname)) {
                if (!listUserNickNames.contains(nickname)) {
                    String lineNames = lineEntities.stream().map(ProductionLineEntity::getName).collect(Collectors.joining(Constants.SEP));
                    throw new ResponseException(nickname + "不是" + lineNames + "的成员，请确认");
                }
                return sysUserService.lambdaQuery().eq(SysUserEntity::getNickname, nickname).list();
            } else {
                return sysUserEntities;
            }
        }
        return userEntities;
    }

    /**
     * 检验用户是否班组成员
     *
     * @param nickname     用户名
     * @param userEntities 用户列表
     * @param teamEntities 班组列表
     */
    private List<SysUserEntity> checkTeamUser(String nickname, List<SysUserEntity> userEntities, List<SysTeamEntity> teamEntities) {
        if (CollectionUtils.isNotEmpty(userEntities) && !StringUtils.isEmpty(nickname)) {
            userEntities = userEntities.stream().filter(o -> o.getNickname().equals(nickname)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userEntities)) {
                if (CollectionUtils.isEmpty(teamEntities)) {
                    throw new ResponseException(" 未找到该用户: " + nickname + " 请确认");
                } else {
                    String teamNames = teamEntities.stream().map(SysTeamEntity::getTeamName).collect(Collectors.joining("，"));
                    throw new ResponseException(nickname + " 不是 " + teamNames + " 班组成员，请确认");
                }
            }
        }
        return userEntities;
    }

    @Override
    public WorkCenterEntity getWithBasicUnits(Integer workCenterId) {
        WorkCenterEntity workCenter = this.getById(workCenterId);
        List<ProductionBasicUnitDTO> basicUnits = new ArrayList<>();
        if (workCenter != null) {
            if (WorkCenterTypeEnum.LINE.getCode().equals(workCenter.getType())) {
                List<ProductionLineEntity> originLines = productionLineService.lambdaQuery().eq(ProductionLineEntity::getWorkCenterId, workCenter.getId()).list();
                List<ProductionBasicUnitDTO> lines = originLines.stream().map(line -> ProductionBasicUnitDTO.builder()
                        .id(line.getProductionLineId())
                        .code(line.getProductionLineCode())
                        .name(line.getName())
                        .type(workCenter.getType())
                        .build()
                ).collect(Collectors.toList());
                basicUnits.addAll(lines);
            } else if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenter.getType())) {
                List<WorkCenterTeamEntity> workCenterTeams = workCenterTeamService.lambdaQuery().eq(WorkCenterTeamEntity::getWorkCenterId, workCenterId).list();
                List<Integer> teamIds = workCenterTeams.stream().map(WorkCenterTeamEntity::getTeamId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(teamIds)) {
                    List<ProductionBasicUnitDTO> teams = teamService.listByIds(teamIds)
                            .stream().map(team -> ProductionBasicUnitDTO.builder()
                                    .id(team.getId())
                                    .code(team.getTeamCode())
                                    .name(team.getTeamName())
                                    .type(workCenter.getType())
                                    .build()
                            ).collect(Collectors.toList());
                    basicUnits.addAll(teams);
                }
            } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenter.getType())) {
                List<WorkCenterDeviceEntity> workCenterDevices = workCenterDeviceService.lambdaQuery().eq(WorkCenterDeviceEntity::getWorkCenterId, workCenterId).list();
                List<Integer> deviceIds = workCenterDevices.stream().map(WorkCenterDeviceEntity::getDeviceId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deviceIds)) {
                    List<ProductionBasicUnitDTO> devices = deviceService.listByIds(deviceIds)
                            .stream().map(device -> ProductionBasicUnitDTO.builder()
                                    .id(device.getDeviceId())
                                    .code(device.getDeviceCode())
                                    .name(device.getDeviceName())
                                    .type(workCenter.getType())
                                    .state(device.getState())
                                    .stateName(DevicesStateEnum.getNameByCode(device.getState()))
                                    .build()
                            ).collect(Collectors.toList());
                    basicUnits.addAll(devices);
                }
            }
            workCenter.setProductionBasicUnitDTOList(basicUnits);
        }
        return workCenter;
    }

    @Override
    public List<CommonType> getWorkCenterTree() {
        // 获取工作中心类型及该类型下的基本生产单元
        List<CommonType> list = new ArrayList<>();
        // 获取产线类型
        List<CommonType> lineTypes = modelService.lambdaQuery().eq(ModelEntity::getType, ModelEnum.LINE.getType())
                .list().stream()
                .map(o -> CommonType.builder().id(o.getId()).type(o.getType()).name(o.getName()).build())
                .collect(Collectors.toList());
        // 获取产线实例并放置到对应类型下
        Map<Object, List<CommonType>> lineMap = productionLineService.lambdaQuery().list().stream()
                .map(o -> CommonType.builder().id(o.getProductionLineId()).type(o.getModelId()).code(o.getProductionLineCode()).name(o.getName()).build())
                .collect(Collectors.groupingBy(CommonType::getType));
        for (CommonType lineType : lineTypes) {
            lineType.setList(lineMap.get(lineType.getId()));
        }
        CommonType lineDto = CommonType.builder().code(WorkCenterTypeEnum.LINE.getCode()).name(WorkCenterTypeEnum.LINE.getName()).list(lineTypes).build();

        // 获取设备类型
        List<CommonType> deviceTypes = modelService.lambdaQuery().eq(ModelEntity::getType, ModelEnum.DEVICE.getType())
                .list().stream()
                .map(o -> CommonType.builder().id(o.getId()).type(o.getType()).name(o.getName()).build())
                .collect(Collectors.toList());
        // 获取设备实例
        Map<Object, List<CommonType>> deviceMap = deviceService.lambdaQuery().list().stream()
                .map(o -> CommonType.builder().id(o.getDeviceId()).type(o.getModelId()).code(o.getDeviceCode()).name(o.getDeviceName()).build())
                .collect(Collectors.groupingBy(CommonType::getType));
        for (CommonType deviceType : deviceTypes) {
            deviceType.setList(deviceMap.get(deviceType.getId()));
        }
        CommonType deviceDto = CommonType.builder().code(WorkCenterTypeEnum.DEVICE.getCode()).name(WorkCenterTypeEnum.DEVICE.getName()).list(deviceTypes).build();

        // 获取班组类型
        List<CommonType> teamTypes = teamTypeDefService.list().stream().map(o ->
                        CommonType.builder().id(o.getId()).type(o.getTypeDefCode()).name(o.getTypeDefName()).build())
                .collect(Collectors.toList());
        // 获取班组实例
        Map<Object, List<CommonType>> teamMap = teamService.lambdaQuery().list().stream()
                .map(o -> CommonType.builder().id(o.getId()).type(o.getTeamType()).code(o.getTeamCode()).name(o.getTeamName()).build())
                .collect(Collectors.groupingBy(CommonType::getType));
        for (CommonType teamType : teamTypes) {
            teamType.setList(teamMap.get(teamType.getId()));
        }
        CommonType teamDto = CommonType.builder().code(WorkCenterTypeEnum.TEAM.getCode()).name(WorkCenterTypeEnum.TEAM.getName()).list(teamTypes).build();
        list.add(lineDto);
        list.add(deviceDto);
        list.add(teamDto);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExcelLogVO importData(MultipartFile file) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            List<ExcelWorkCenterDTO> excels = EasyExcelUtil.dynamicColumnRead(file.getInputStream(), ExcelWorkCenterDTO.class, 0, 1);
            ExcelLogVO vo = ExcelLogVO.init();
            vo.setImportData(excels);
            for (int i = 0; i < excels.size(); i++) {
                ExcelWorkCenterDTO dto = excels.get(i);
                try {
                    ExcelParseWorkCenterDTO parseDTO = parseDto(dto);
                    WorkCenterEntity workCenter = parseDTO.getWorkCenter();
                    add(workCenter);
                    vo.addSuccess();
                    dto.setImportResult("数据校验通过");
                } catch (Exception e) {
                    dto.setImportResult(e.getMessage());
                    vo.appendErr("第" + (i + 1) + "个记录异常, msg:" + e.getMessage() + ";\n");
                    vo.addFail();
                }
            }
            return vo;
        } catch (IOException e) {
            throw new ResponseException(String.format("excel导入数据错误: %s", e.getMessage()));
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public List<ProcedureDeviceVO> getDeviceModelListByWorkCenter(String workCenterIds) {
        List<ProcedureDeviceVO> procedureDeviceVOS = new ArrayList<>();
        if (StringUtils.isBlank(workCenterIds)) {
            return procedureDeviceVOS;
        }
        String[] ids = workCenterIds.split(Constants.SEP);
        for (String id : ids) {
            WorkCenterEntity workCenterEntity = getById(id);
            if (Objects.isNull(workCenterEntity)) {
                continue;
            }
            // 如果类型为班组，则查询关联资源为设备类型的数据
            if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getType())) {
                List<WorkCenterDeviceRelevanceEntity> relevanceEntities = workCenterDeviceRelevanceService.lambdaQuery().eq(WorkCenterDeviceRelevanceEntity::getWorkCenterId, id).list();
                List<Integer> deviceIds = relevanceEntities.stream().map(WorkCenterDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(deviceIds)) {
                    continue;
                }
                List<Integer> deviceModelIds = deviceService.listByIds(deviceIds).stream().map(DeviceEntity::getModelId).collect(Collectors.toList());
                List<ProcedureDeviceVO> collect = modelService.lambdaQuery().eq(ModelEntity::getType, ModelEnum.DEVICE.getType()).in(ModelEntity::getId, deviceModelIds)
                        .list().stream().map(o ->
                                ProcedureDeviceVO.builder()
                                        .deviceTypeName(o.getName())
                                        .deviceModelId(o.getId())
                                        .build())
                        .collect(Collectors.toList());
                procedureDeviceVOS.addAll(collect);
            }
            // 如果类型为设备，则直接查询关联的设备类型
            if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterEntity.getType())) {
                List<WorkCenterDeviceEntity> relevanceEntities = workCenterDeviceService.lambdaQuery().eq(WorkCenterDeviceEntity::getWorkCenterId, id).list();
                List<Integer> deviceIds = relevanceEntities.stream().map(WorkCenterDeviceEntity::getDeviceId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(deviceIds)) {
                    continue;
                }
                List<Integer> deviceModelIds = deviceService.listByIds(deviceIds).stream().map(DeviceEntity::getModelId).collect(Collectors.toList());
                List<ProcedureDeviceVO> collect = modelService.lambdaQuery().eq(ModelEntity::getType, ModelEnum.DEVICE.getType()).in(ModelEntity::getId, deviceModelIds)
                        .list().stream().map(o ->
                                ProcedureDeviceVO.builder()
                                        .deviceTypeName(o.getName())
                                        .deviceModelId(o.getId())
                                        .build())
                        .collect(Collectors.toList());
                procedureDeviceVOS.addAll(collect);
            }
        }
        // 如果未关联设备类型，则系统会找所有的设备类型
        if (CollectionUtils.isEmpty(procedureDeviceVOS)) {
            return modelService.lambdaQuery().eq(ModelEntity::getType, ModelEnum.DEVICE.getType())
                    .list().stream().map(o ->
                            ProcedureDeviceVO.builder()
                                    .deviceTypeName(o.getName())
                                    .deviceModelId(o.getId())
                                    .build())
                    .collect(Collectors.toList());
        }
        return procedureDeviceVOS;
    }

    @Override
    public List<WorkCenterTeamDTO> listTeam(Integer workCenterId) {
        List<TeamTypeDefEntity> teamTypeDefEntities = teamTypeDefService.list();
        Map<Integer, List<SysTeamEntity>> map = teamService.list().stream().collect(Collectors.groupingBy(SysTeamEntity::getTeamType));
        List<WorkCenterTeamDTO> result = new ArrayList<>();
        for (TeamTypeDefEntity typeDefEntity : teamTypeDefEntities) {
            List<SysTeamEntity> sysTeamEntities = map.get(typeDefEntity.getId());
            if (CollectionUtils.isEmpty(sysTeamEntities)) {
                continue;
            }
            // 工作中心已经绑定过的班组不能再被新的工作中心关联
            List<Integer> allTeamIds = workCenterTeamService.list().stream()
                    .map(WorkCenterTeamEntity::getTeamId).distinct()
                    .collect(Collectors.toList());
            List<Integer> bindTeamIds = workCenterTeamService.lambdaQuery().eq(WorkCenterTeamEntity::getWorkCenterId, workCenterId)
                    .list().stream()
                    .map(WorkCenterTeamEntity::getTeamId).collect(Collectors.toList());
            sysTeamEntities = sysTeamEntities.stream().filter(o -> !allTeamIds.contains(o.getId()) || bindTeamIds.contains(o.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sysTeamEntities)) {
                continue;
            }
            WorkCenterTeamDTO teamDTO = WorkCenterTeamDTO.builder()
                    .id(typeDefEntity.getId())
                    .name(typeDefEntity.getTypeDefName())
                    .teamEntities(sysTeamEntities).build();
            result.add(teamDTO);
        }
        return result;
    }

    @Override
    public List<WorkCenterRelevanceDTO> listRelevanceResource(String relevanceType) {
        if (WorkCenterTypeEnum.LINE.getCode().equals(relevanceType)) {
            List<WorkCenterRelevanceDTO.ProductBasicUnitDTO> collect = productionLineService.list().stream()
                    .map(o -> WorkCenterRelevanceDTO.ProductBasicUnitDTO.builder()
                            .id(o.getProductionLineId())
                            .name(o.getName())
                            .modelId(o.getModelId())
                            .build()
                    ).collect(Collectors.toList());
            Map<Integer, List<WorkCenterRelevanceDTO.ProductBasicUnitDTO>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterRelevanceDTO.ProductBasicUnitDTO::getModelId));
            List<WorkCenterRelevanceDTO> dtos = modelService.lambdaQuery()
                    .eq(ModelEntity::getType, ModelEnum.LINE.getType()).list().stream()
                    .map(o -> WorkCenterRelevanceDTO.builder()
                            .id(o.getId())
                            .name(o.getName())
                            .productBasicUnitList(map.get(o.getId()))
                            .build())
                    .collect(Collectors.toList());
            dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getProductBasicUnitList())).collect(Collectors.toList());
            return dtos;
        } else if (WorkCenterTypeEnum.TEAM.getCode().equals(relevanceType)) {
            List<WorkCenterRelevanceDTO.ProductBasicUnitDTO> collect = teamService.list().stream()
                    .map(o -> WorkCenterRelevanceDTO.ProductBasicUnitDTO.builder()
                            .id(o.getId())
                            .name(o.getTeamName())
                            .modelId(o.getTeamType())
                            .build()
                    ).collect(Collectors.toList());
            Map<Integer, List<WorkCenterRelevanceDTO.ProductBasicUnitDTO>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterRelevanceDTO.ProductBasicUnitDTO::getModelId));
            List<WorkCenterRelevanceDTO> dtos = teamTypeDefService.list().stream().map(o -> WorkCenterRelevanceDTO.builder()
                            .id(o.getId())
                            .name(o.getTypeDefName())
                            .productBasicUnitList(map.get(o.getId()))
                            .build())
                    .collect(Collectors.toList());
            dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getProductBasicUnitList())).collect(Collectors.toList());
            return dtos;
        } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(relevanceType)) {
            List<WorkCenterRelevanceDTO.ProductBasicUnitDTO> collect = deviceService.list().stream()
                    .map(o -> WorkCenterRelevanceDTO.ProductBasicUnitDTO.builder()
                            .id(o.getDeviceId())
                            .name(o.getDeviceName())
                            .modelId(o.getModelId())
                            .build()
                    ).collect(Collectors.toList());
            Map<Integer, List<WorkCenterRelevanceDTO.ProductBasicUnitDTO>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterRelevanceDTO.ProductBasicUnitDTO::getModelId));
            List<WorkCenterRelevanceDTO> dtos = modelService.lambdaQuery()
                    .eq(ModelEntity::getType, ModelEnum.DEVICE.getType()).list().stream()
                    .map(o -> WorkCenterRelevanceDTO.builder()
                            .id(o.getId())
                            .name(o.getName())
                            .productBasicUnitList(map.get(o.getId()))
                            .build())
                    .collect(Collectors.toList());
            dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getProductBasicUnitList())).collect(Collectors.toList());
            return dtos;
        }
        return new ArrayList<>();
    }

    @Override
    public WorkCenterDetailVO detail(WorkCenterDetailQueryDTO dto) {
        WorkCenterEntity workCenterEntity = this.lambdaQuery().eq(WorkCenterEntity::getCode, dto.getWorkCenterCode()).one();
        if (Objects.isNull(workCenterEntity)) {
            workCenterEntity = this.getById(dto.getWorkCenterId());
            if (Objects.isNull(workCenterEntity)) {
                return null;
            }
        }
        // 获取工作中心的主资源
        List<WorkCenterProductResourceVO.InstanceType> mainResource = getMainResource(workCenterEntity);
        // 获取工作中心的关联资源
        List<WorkCenterProductResourceVO.InstanceType> relatedResource = getRelatedResource(workCenterEntity);
        return WorkCenterDetailVO.builder()
                .areaId(workCenterEntity.getAid())
                .id(workCenterEntity.getId())
                .code(workCenterEntity.getCode())
                .name(workCenterEntity.getName())
                .magName(workCenterEntity.getMagName())
                .mainResource(WorkCenterProductResourceVO.builder()
                        .type(workCenterEntity.getType())
                        .instanceTypes(mainResource)
                        .build())
                .relatedResource(StringUtils.isNotBlank(workCenterEntity.getRelevanceType()) ? WorkCenterProductResourceVO.builder()
                        .type(workCenterEntity.getRelevanceType())
                        .instanceTypes(relatedResource)
                        .build() : null)
                .build();
    }

    /**
     * 获取工作中心的主资源
     */
    private List<WorkCenterProductResourceVO.InstanceType> getMainResource(WorkCenterEntity workCenterEntity) {
        if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterEntity.getType())) {
            List<WorkCenterProductResourceVO.Instance> collect = productionLineService.lambdaQuery()
                    .eq(ProductionLineEntity::getWorkCenterId, workCenterEntity.getId())
                    .list().stream()
                    .map(o -> WorkCenterProductResourceVO.Instance.builder()
                            .id(o.getProductionLineId())
                            .code(o.getProductionLineCode())
                            .name(o.getName())
                            .modelId(o.getModelId())
                            .build()
                    ).collect(Collectors.toList());
            Map<Integer, List<WorkCenterProductResourceVO.Instance>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterProductResourceVO.Instance::getModelId));
            List<WorkCenterProductResourceVO.InstanceType> dtos = modelService.lambdaQuery()
                    .eq(ModelEntity::getType, ModelEnum.LINE.getType()).list().stream()
                    .map(o -> WorkCenterProductResourceVO.InstanceType.builder()
                            .modelId(o.getId())
                            .modelName(o.getName())
                            .instances(map.get(o.getId()))
                            .build())
                    .collect(Collectors.toList());
            dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getInstances())).collect(Collectors.toList());
            return dtos;
        } else if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getType())) {
            List<Integer> teamIds = workCenterTeamService.lambdaQuery().eq(WorkCenterTeamEntity::getWorkCenterId, workCenterEntity.getId())
                    .list().stream().map(WorkCenterTeamEntity::getTeamId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(teamIds)) {
                List<WorkCenterProductResourceVO.Instance> collect = teamService.lambdaQuery().in(SysTeamEntity::getId, teamIds)
                        .list().stream()
                        .map(o -> WorkCenterProductResourceVO.Instance.builder()
                                .id(o.getId())
                                .code(o.getTeamCode())
                                .name(o.getTeamName())
                                .modelId(o.getTeamType())
                                .build()
                        ).collect(Collectors.toList());
                Map<Integer, List<WorkCenterProductResourceVO.Instance>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterProductResourceVO.Instance::getModelId));
                List<WorkCenterProductResourceVO.InstanceType> dtos = teamTypeDefService.list().stream().map(o -> WorkCenterProductResourceVO.InstanceType.builder()
                                .modelId(o.getId())
                                .modelName(o.getTypeDefName())
                                .instances(map.get(o.getId()))
                                .build())
                        .collect(Collectors.toList());
                dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getInstances())).collect(Collectors.toList());
                return dtos;
            }
        } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterEntity.getType())) {
            List<Integer> deviceIds = workCenterDeviceService.lambdaQuery().eq(WorkCenterDeviceEntity::getWorkCenterId, workCenterEntity.getId())
                    .list().stream().map(WorkCenterDeviceEntity::getDeviceId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                List<WorkCenterProductResourceVO.Instance> collect = deviceService.lambdaQuery()
                        .in(DeviceEntity::getDeviceId, deviceIds)
                        .list().stream()
                        .map(o -> WorkCenterProductResourceVO.Instance.builder()
                                .id(o.getDeviceId())
                                .code(o.getDeviceCode())
                                .name(o.getDeviceName())
                                .stateName(DevicesUseStateEnum.getNameByCode(o.getUseState()))
                                .modelId(o.getModelId())
                                .build()
                        ).collect(Collectors.toList());
                Map<Integer, List<WorkCenterProductResourceVO.Instance>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterProductResourceVO.Instance::getModelId));
                List<WorkCenterProductResourceVO.InstanceType> dtos = modelService.lambdaQuery()
                        .eq(ModelEntity::getType, ModelEnum.DEVICE.getType()).list().stream()
                        .map(o -> WorkCenterProductResourceVO.InstanceType.builder()
                                .modelId(o.getId())
                                .modelName(o.getName())
                                .instances(map.get(o.getId()))
                                .build())
                        .collect(Collectors.toList());
                dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getInstances())).collect(Collectors.toList());
                return dtos;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取工作中心的关联资源
     */
    private List<WorkCenterProductResourceVO.InstanceType> getRelatedResource(WorkCenterEntity workCenterEntity) {
        if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterEntity.getRelevanceType())) {
            List<Integer> lineIds = workCenterLineRelevanceService.lambdaQuery().eq(WorkCenterLineRelevanceEntity::getWorkCenterId, workCenterEntity.getId())
                    .list().stream()
                    .map(WorkCenterLineRelevanceEntity::getLineId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lineIds)) {
                List<WorkCenterProductResourceVO.Instance> collect = productionLineService.lambdaQuery()
                        .in(ProductionLineEntity::getProductionLineId, lineIds)
                        .list().stream()
                        .map(o -> WorkCenterProductResourceVO.Instance.builder()
                                .id(o.getProductionLineId())
                                .code(o.getProductionLineCode())
                                .name(o.getName())
                                .modelId(o.getModelId())
                                .build()
                        ).collect(Collectors.toList());
                Map<Integer, List<WorkCenterProductResourceVO.Instance>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterProductResourceVO.Instance::getModelId));
                List<WorkCenterProductResourceVO.InstanceType> dtos = modelService.lambdaQuery()
                        .eq(ModelEntity::getType, ModelEnum.LINE.getType()).list().stream()
                        .map(o -> WorkCenterProductResourceVO.InstanceType.builder()
                                .modelId(o.getId())
                                .modelName(o.getName())
                                .instances(map.get(o.getId()))
                                .build())
                        .collect(Collectors.toList());
                dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getInstances())).collect(Collectors.toList());
                return dtos;
            }

        } else if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getRelevanceType())) {
            List<Integer> teamIds = workCenterTeamRelevanceService.lambdaQuery().eq(WorkCenterTeamRelevanceEntity::getWorkCenterId, workCenterEntity.getId())
                    .list().stream().map(WorkCenterTeamRelevanceEntity::getTeamId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(teamIds)) {
                List<WorkCenterProductResourceVO.Instance> collect = teamService.lambdaQuery().in(SysTeamEntity::getId, teamIds)
                        .list().stream()
                        .map(o -> WorkCenterProductResourceVO.Instance.builder()
                                .id(o.getId())
                                .code(o.getTeamCode())
                                .name(o.getTeamName())
                                .modelId(o.getTeamType())
                                .build()
                        ).collect(Collectors.toList());
                Map<Integer, List<WorkCenterProductResourceVO.Instance>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterProductResourceVO.Instance::getModelId));
                List<WorkCenterProductResourceVO.InstanceType> dtos = teamTypeDefService.list().stream().map(o -> WorkCenterProductResourceVO.InstanceType.builder()
                                .modelId(o.getId())
                                .modelName(o.getTypeDefName())
                                .instances(map.get(o.getId()))
                                .build())
                        .collect(Collectors.toList());
                dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getInstances())).collect(Collectors.toList());
                return dtos;
            }
        } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterEntity.getRelevanceType())) {
            List<Integer> deviceIds = workCenterDeviceRelevanceService.lambdaQuery().eq(WorkCenterDeviceRelevanceEntity::getWorkCenterId, workCenterEntity.getId())
                    .list().stream().map(WorkCenterDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                List<WorkCenterProductResourceVO.Instance> collect = deviceService.lambdaQuery()
                        .in(DeviceEntity::getDeviceId, deviceIds)
                        .list().stream()
                        .map(o -> WorkCenterProductResourceVO.Instance.builder()
                                .id(o.getDeviceId())
                                .code(o.getDeviceCode())
                                .name(o.getDeviceName())
                                .modelId(o.getModelId())
                                .build()
                        ).collect(Collectors.toList());
                Map<Integer, List<WorkCenterProductResourceVO.Instance>> map = collect.stream().collect(Collectors.groupingBy(WorkCenterProductResourceVO.Instance::getModelId));
                List<WorkCenterProductResourceVO.InstanceType> dtos = modelService.lambdaQuery()
                        .eq(ModelEntity::getType, ModelEnum.DEVICE.getType()).list().stream()
                        .map(o -> WorkCenterProductResourceVO.InstanceType.builder()
                                .modelId(o.getId())
                                .modelName(o.getName())
                                .instances(map.get(o.getId()))
                                .build())
                        .collect(Collectors.toList());
                dtos = dtos.stream().filter(o -> CollectionUtils.isNotEmpty(o.getInstances())).collect(Collectors.toList());
                return dtos;
            }
        }
        return new ArrayList<>();
    }

    private ExcelParseWorkCenterDTO parseDto(ExcelWorkCenterDTO dto) {
        WorkCenterEntity workCenter = JacksonUtil.convertObject(dto, WorkCenterEntity.class);
        ExcelParseWorkCenterDTO result = ExcelParseWorkCenterDTO.builder().workCenter(workCenter).build();

        // 制造单元类型
        WorkCenterTypeEnum workCenterType = WorkCenterTypeEnum.getByName(dto.getBasicUnitTypeName());
        if (workCenterType == null) {
            throw new ResponseException("生产基本单元类型有误, 只限[制造单元,班组,设备]");
        }
        workCenter.setType(workCenterType.getCode());

        if(StringUtils.isBlank(dto.getBasicUnitStr())) {
            throw new ResponseException("生产基本单元为空");
        }
        List<String> basicUnitNames = Arrays.asList(dto.getBasicUnitStr().split(Constants.SEP));
        // 制造单元
        if (workCenterType == WorkCenterTypeEnum.LINE) {
            // 校验
            List<ProductionLineEntity> lines = checkLine(basicUnitNames, true);
            workCenter.setProductionLineIds(lines.stream().map(ProductionLineEntity::getProductionLineId).distinct().collect(Collectors.toList()));
        }
        // 设备
        if (workCenterType == WorkCenterTypeEnum.DEVICE) {
            // 校验
            List<DeviceEntity> devices = checkDevice(basicUnitNames);
            // 查是否绑定工作中心
            List<Integer> deviceIds = devices.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
            workCenter.setDeviceIds(deviceIds);
        }
        // 班组
        if (workCenterType == WorkCenterTypeEnum.TEAM) {
            List<SysTeamEntity> teams = checkTeam(basicUnitNames);
            // 查是否绑定工作中心
            List<Integer> teamIds = teams.stream().map(SysTeamEntity::getId).collect(Collectors.toList());
            workCenter.setSysTeamIds(teamIds);
        }
        // 关联资源
        if(StringUtils.isNotBlank(dto.getRelevanceTypeName())) {
            WorkCenterTypeEnum relevanceType = WorkCenterTypeEnum.getByName(dto.getRelevanceTypeName());
            if(relevanceType == null) {
                throw new ResponseException("关联资源类型有误, 只限[制造单元,班组,设备]");
            }
            if(relevanceType == workCenterType) {
                throw new ResponseException("关联资源类型不能与生产基本单元类型一样");
            }
            workCenter.setRelevanceType(relevanceType.getCode());
            List<String> relevanceNames = Arrays.asList(dto.getRelevanceStr().split(Constants.SEP));
            if(relevanceType == WorkCenterTypeEnum.LINE) {
                // 校验
                List<ProductionLineEntity> lines = checkLine(relevanceNames, false);
                workCenter.setLineRelevanceIds(lines.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList()));
            }
            if(relevanceType == WorkCenterTypeEnum.DEVICE) {
                // 校验
                List<DeviceEntity> devices = checkDevice(relevanceNames);
                workCenter.setDeviceRelevanceIds(devices.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList()));
            }
            if(relevanceType == WorkCenterTypeEnum.TEAM) {
                // 校验
                List<SysTeamEntity> teams = checkTeam(relevanceNames);
                workCenter.setTeamRelevanceIds(teams.stream().map(SysTeamEntity::getId).collect(Collectors.toList()));
            }
        }


        // 用户名
        if (!StringUtils.isEmpty(dto.getMagNickname())) {
            SysUserEntity user = sysUserService.getOneByNickname(dto.getMagNickname());
            if (user == null) {
                throw new ResponseException("用户名:" + dto.getMagNickname() + "未找到");
            }
            workCenter.setMagName(user.getUsername());
            workCenter.setMagPhone(user.getMobile());
        }
        // cid
        MyAuthenticationUserDetail userInfo = userAuthenService.getUserInfo();
        if (userInfo != null) {
            workCenter.setCid(userInfo.getCompId());
        }
        // 厂区
        AreaEntity area = areaService.getByName(dto.getAname());
        if (area == null) {
            throw new ResponseException("厂区:" + dto.getAname() + "未找到");
        }
        workCenter.setAid(area.getAid());

        // 校验
        ValidateUtil.valid(workCenter, null);
        return result;
    }


    private List<ProductionLineEntity> checkLine(List<String> lineNames, boolean checkBind) {
        List<ProductionLineEntity> lines = productionLineService.lambdaQuery().in(ProductionLineEntity::getName, lineNames).list();
        Map<String, ProductionLineEntity> lineNameMap = lines.stream().collect(Collectors.toMap(ProductionLineEntity::getName, Function.identity()));
        // 逐个校验
        for (String lineName : lineNames) {
            ProductionLineEntity line = lineNameMap.get(lineName);
            if(line == null) {
                throw new ResponseException("制造单元:" + lineName + "未找到");
            }
            if(checkBind) {
                if(line.getWorkCenterId() != null) {
                    throw new ResponseException("制造单元:" + lineName + "已绑定其他工作中心");
                }
            }
        }
        return lines;
    }
    private List<DeviceEntity> checkDevice(List<String> deviceNames) {
        List<DeviceEntity> devices = deviceService.lambdaQuery().in(DeviceEntity::getDeviceName, deviceNames).list();
        Map<String, Long> deviceNameGroupCount = devices.stream().collect(Collectors.groupingBy(DeviceEntity::getDeviceName, Collectors.counting()));
        for (Map.Entry<String, Long> entry : deviceNameGroupCount.entrySet()) {
            if(entry.getValue() <= 1 ) {
                continue;
            }else {
                throw new ResponseException("设备名称:" + entry.getKey() + "存在多个");
            }
        }
        Map<String, DeviceEntity> deviceNameMap = devices.stream().collect(Collectors.toMap(DeviceEntity::getDeviceName, Function.identity()));
        // 逐个校验
        for (String deviceName : deviceNames) {
            DeviceEntity device = deviceNameMap.get(deviceName);
            if(device == null) {
                throw new ResponseException("设备:" + deviceName + "未找到");
            }
        }
        return devices;
    }

    private List<SysTeamEntity> checkTeam(List<String> teamNames) {
        List<SysTeamEntity> teams = teamService.lambdaQuery().in(SysTeamEntity::getTeamName, teamNames).list();
        Map<String, SysTeamEntity> teamNameMap = teams.stream().collect(Collectors.toMap(SysTeamEntity::getTeamName, Function.identity()));
        // 逐个校验
        for (String teamName : teamNames) {
            SysTeamEntity team = teamNameMap.get(teamName);
            if(team == null) {
                throw new ResponseException("班组:" + teamName + "未找到");
            }
        }
        return teams;
    }

    @Override
    @RedisCache(timeOut = 60 * 3)
    public WorkCenterEntity getByIdCache(Serializable id) {
        return this.getById(id);
    }

    @Override
    public Page<WorkCenterDetailVO> getList2(WorkCenterQueryDTO dto) {
        String sql = SqlUtil.getSql(dto);
        Page<WorkCenterEntity> workCenterPage = this.baseMapper.getList(sql, dto.getPage());
        // 转换成VO
        List<WorkCenterDetailVO> detailVOS = workCenterPage.getRecords().stream().map(workCenter ->
                detail(WorkCenterDetailQueryDTO
                        .builder()
                        .workCenterCode(workCenter.getCode())
                        .build())).collect(Collectors.toList());
        Page<WorkCenterDetailVO> page = new Page<>(dto.getCurrent(), dto.getSize());
        page.setRecords(detailVOS);
        page.setTotal(workCenterPage.getTotal());
        return page;
    }
    @Override
    public Map<Integer, AreaEntity> areaWorkCenterIdMap(Collection<Integer> workCenterIds) {
        if(CollectionUtils.isEmpty(workCenterIds)) {
            return Collections.emptyMap();
        }
        List<WorkCenterEntity> workCenters = this.listByIds(workCenterIds);
        Map<Integer, Integer> workCenterIdAIdMap = workCenters.stream().filter(e -> e.getAid() != null).collect(Collectors.toMap(WorkCenterEntity::getId, WorkCenterEntity::getAid));
        Map<Integer, AreaEntity> aIdMap = areaService.getMapByIds(new HashSet<>(workCenterIdAIdMap.values()));
        return workCenterIdAIdMap.entrySet().stream()
                .filter(entry -> aIdMap.containsKey(entry.getValue()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> aIdMap.get(entry.getValue())

                ));
    }

    @Override
    public Map<Integer, WorkCenterEntity> deviceIdWorkCenterMap(Collection<Integer> deviceIds) {
        if(CollectionUtils.isEmpty(deviceIds)) {
            return Collections.emptyMap();
        }
        List<WorkCenterDeviceEntity> workCenterDevices = workCenterDeviceService.lambdaQuery().in(WorkCenterDeviceEntity::getDeviceId, deviceIds).list();
        Map<Integer, Integer> deviceIdCenterIdMap = workCenterDevices.stream().collect(Collectors.toMap(WorkCenterDeviceEntity::getDeviceId, WorkCenterDeviceEntity::getWorkCenterId, (v1, v2) -> v1));
        List<WorkCenterEntity> workCenters = this.listByIds(deviceIdCenterIdMap.values());
        Map<Integer, WorkCenterEntity> workCenterIdMap = workCenters.stream().collect(Collectors.toMap(WorkCenterEntity::getId, Function.identity()));
        return deviceIdCenterIdMap.entrySet().stream()
                .filter(e -> workCenterIdMap.containsKey(e.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, e -> workCenterIdMap.get(e.getValue())));
    }
}
